{"name": "core-parser-app", "version": "1.0.0", "description": "Core文件解析网站", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["core", "parser", "file-upload", "react"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}