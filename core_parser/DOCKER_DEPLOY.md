# Docker 部署指南

## 文件说明

- `Dockerfile` - 开发环境Docker配置
- `Dockerfile.prod` - 生产环境Docker配置  
- `docker-compose.yml` - Docker Compose配置
- `.dockerignore` - Docker构建忽略文件

## 快速开始

### 开发环境部署

```bash
# 使用docker-compose启动开发环境
docker-compose up core-parser-dev

# 或者直接使用Docker
docker build -t core-parser:dev .
docker run -p 3000:3000 -p 3001:3001 core-parser:dev
```

访问地址：
- 前端：http://localhost:3000
- 后端：http://localhost:3001

### 生产环境部署

```bash
# 使用docker-compose启动生产环境
docker-compose up -d core-parser-prod

# 或者直接使用Docker
docker build -f Dockerfile.prod -t core-parser:prod .
docker run -d -p 3001:3001 core-parser:prod
```

访问地址：
- 应用：http://localhost:3001

## 构建优化

### 多阶段构建（可选优化）

如果需要更小的生产镜像，可以考虑多阶段构建：

```dockerfile
# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
COPY client/package*.json ./client/
COPY server/package*.json ./server/
RUN npm install && cd server && npm install && cd ../client && npm install
COPY . .
RUN cd client && npm run build

# 生产阶段
FROM artifactory.ep.chehejia.com/lios-docker-release-local/ubuntu20.04-python3.11:0.0.6
RUN apt-get update && apt-get install -y nodejs npm && rm -rf /var/lib/apt/lists/*
WORKDIR /opt/workspace
COPY --from=builder /app/server ./server
COPY --from=builder /app/client/build ./client/build
COPY --from=builder /app/package*.json ./
RUN cd server && npm ci --only=production
EXPOSE 3001
CMD ["npm", "run", "start"]
```

## 常用命令

```bash
# 查看运行中的容器
docker ps

# 查看日志
docker-compose logs core-parser-dev
docker-compose logs core-parser-prod

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build

# 进入容器调试
docker exec -it <container_id> /bin/bash
```

## 注意事项

1. **端口配置**：确保宿主机端口3000和3001未被占用
2. **依赖安装**：Docker会自动安装所有Node.js依赖
3. **Python脚本**：确保`server/scripts/core_parse.py`存在
4. **数据持久化**：如需持久化数据，请添加volume映射
5. **环境变量**：可在docker-compose.yml中添加更多环境变量配置

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3000
   lsof -i :3001
   ```

2. **依赖安装失败**
   ```bash
   # 清理Docker缓存重新构建
   docker system prune -a
   docker-compose build --no-cache
   ```

3. **Python脚本找不到**
   ```bash
   # 检查文件是否存在
   docker exec -it <container_id> ls -la /opt/workspace/server/scripts/
   ```
