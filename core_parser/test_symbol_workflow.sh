#!/bin/bash

# 测试新的符号表工作流程

echo "=== 测试符号表工作流程 ==="

# 创建测试目录结构
mkdir -p test_symbol_dir/subdir1
mkdir -p test_symbol_dir/subdir2

# 创建模拟符号表文件
echo "符号表文件1" > test_symbol_dir/symbol1.sym
echo "符号表文件2" > test_symbol_dir/symbol2.sym
echo "子目录符号表1" > test_symbol_dir/subdir1/symbol3.sym
echo "子目录符号表2" > test_symbol_dir/subdir2/symbol4.sym

echo "✅ 创建了测试符号表目录结构:"
find test_symbol_dir -type f

echo ""
echo "新的工作流程说明:"
echo "1. 用户上传符号表目录 → 存储到 server/user_symbols/"
echo "2. Python脚本执行时会删除并重建 server/symbols/"
echo "3. Python脚本执行完成后，自动复制 user_symbols/ → symbols/"
echo "4. 这样既保护了用户上传的符号表，又满足了Python脚本的需求"

echo ""
echo "测试步骤:"
echo "1. 启动服务器: ./start.sh"
echo "2. 访问前端: http://localhost:3000"
echo "3. 上传core文件"
echo "4. 点击'选择目录'上传 test_symbol_dir 目录"
echo "5. 点击开始解析"
echo "6. 检查以下目录:"
echo "   - server/user_symbols/ (用户上传的符号表，不会被删除)"
echo "   - server/symbols/ (Python脚本使用的符号表，解析后包含用户符号表)"

echo ""
echo "预期结果:"
echo "- 用户符号表安全存储在 user_symbols/ 目录"
echo "- Python脚本正常工作，不会影响用户上传的符号表"
echo "- 解析时能够使用用户提供的符号表文件"

# 清理测试目录
# rm -rf test_symbol_dir
