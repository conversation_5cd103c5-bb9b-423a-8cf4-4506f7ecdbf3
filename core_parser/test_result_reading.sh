#!/bin/bash

# 测试解析结果读取功能

echo "=== 测试解析结果读取功能 ==="

# 创建测试目录
mkdir -p server/uploads
mkdir -p server/results

# 创建一个模拟的core文件
echo "模拟core文件内容" > server/uploads/test_core.dump

# 模拟Python脚本生成info.log文件的行为
echo "=== 模拟解析结果 ===" > test_core.dump_info.log
echo "调用栈信息:" >> test_core.dump_info.log
echo "#0  0x7fff12345678 in main_function ()" >> test_core.dump_info.log
echo "#1  0x7fff12345600 in process_data ()" >> test_core.dump_info.log
echo "内存信息:" >> test_core.dump_info.log
echo "堆内存使用: 256MB" >> test_core.dump_info.log
echo "解析完成" >> test_core.dump_info.log

echo "✅ 创建了模拟的解析结果文件: test_core.dump_info.log"

# 测试后端是否能正确读取info.log文件
echo "现在可以测试后端功能:"
echo "1. 启动服务器: ./start.sh"
echo "2. 访问前端: http://localhost:3000"
echo "3. 上传 server/uploads/test_core.dump 文件"
echo "4. 点击开始解析"
echo "5. 检查结果是否包含info.log文件的内容"

echo ""
echo "预期结果应该包含:"
echo "- 只显示Core文件解析结果（来自info.log文件）"
echo "- 不显示Python脚本的执行日志"
echo ""
echo "Python脚本的执行日志将只在服务器控制台显示，前端只显示纯净的解析结果。"
