FROM artifactory.ep.chehejia.com/lios-docker-release-local/ubuntu20.04-python3.11:0.0.6

# 安装Node.js和npm
RUN apt-get update && \
    apt-get install -y curl && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 验证安装
RUN node --version && npm --version

WORKDIR /opt/workspace/

# 复制package.json文件先安装依赖（利用Docker缓存）
COPY package*.json ./
COPY server/package*.json ./server/
COPY client/package*.json ./client/

# 安装所有依赖
RUN npm install && \
    cd server && npm install && \
    cd ../client && npm install

# 复制其余文件
COPY . /opt/workspace/

# 暴露端口
EXPOSE 3000 3001

# 设置启动命令
CMD ["npm", "run", "dev"]