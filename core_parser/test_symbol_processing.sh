#!/bin/bash

# 测试符号表文件处理功能

echo "=== 测试符号表文件处理功能 ==="

# 创建测试目录
mkdir -p server/uploads
mkdir -p server/symbols
mkdir -p test_symbol_content

# 创建模拟的符号表内容
echo "模拟符号表文件1" > test_symbol_content/symbol1.sym
echo "模拟符号表文件2" > test_symbol_content/symbol2.sym
mkdir -p test_symbol_content/subdir
echo "子目录符号表文件" > test_symbol_content/subdir/symbol3.sym

# 创建tar.gz测试文件
cd test_symbol_content
tar -czf ../server/uploads/test_symbols.tar.gz *
cd ..

echo "✅ 创建了测试符号表文件: server/uploads/test_symbols.tar.gz"

# 显示测试文件内容
echo ""
echo "测试文件内容:"
tar -tzf server/uploads/test_symbols.tar.gz

echo ""
echo "现在可以测试符号表处理功能:"
echo "1. 启动服务器: ./start.sh"
echo "2. 访问前端: http://localhost:3000"
echo "3. 上传core文件"
echo "4. 上传符号表文件: server/uploads/test_symbols.tar.gz"
echo "5. 点击开始解析"
echo "6. 检查server/symbols/目录是否包含解压后的文件"

echo ""
echo "预期结果:"
echo "- tar.gz文件会被自动解压"
echo "- 解压后的内容会复制到server/symbols/目录"
echo "- Python脚本可以使用symbols目录中的符号表文件"

# 清理测试内容目录
rm -rf test_symbol_content
