#!/bin/bash

# Core文件解析器启动脚本

echo "=== Core文件解析器启动脚本 ==="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    echo "Ubuntu/Debian: sudo apt install nodejs npm"
    echo "CentOS/RHEL: sudo yum install nodejs npm"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm，请先安装npm"
    exit 1
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 检查是否已安装依赖
if [ ! -d "node_modules" ] || [ ! -d "server/node_modules" ] || [ ! -d "client/node_modules" ]; then
    echo "正在安装依赖..."
    npm run install-all
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    echo "Ubuntu/Debian: sudo apt install python3"
    exit 1
fi

echo "Python3版本: $(python3 --version)"

# 检查Python解析脚本是否存在
if [ ! -f "server/scripts/core_parse.py" ]; then
    echo "错误: 未找到core_parse.py脚本，请确保脚本已放置在server/scripts/目录下"
    exit 1
fi

echo "正在启动应用..."
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:3001"
echo "按 Ctrl+C 停止服务"

# 启动应用
npm run dev
