# Core文件解析器

一个基于React和Node.js的Web应用程序，用于自动解析core文件，支持单文件解析和双文件解析模式。

## 功能特性

- 🔧 **单文件模式**: 仅上传core文件进行解析
- 🔧 **双文件模式**: 上传core文件和符号表文件进行更详细的解析
- 📦 **智能解压**: 自动识别并解压tar.gz格式的符号表文件
- 📁 **文件上传**: 支持拖拽和点击上传
- 📊 **实时解析**: 显示解析进度和结果
- 🧹 **一键清空**: 快速清理所有上传文件和结果
- 📱 **响应式设计**: 支持桌面和移动设备

## 项目结构

```
core_parser/
├── client/                 # React前端应用
│   ├── public/
│   ├── src/
│   │   ├── App.js          # 主应用组件
│   │   ├── App.css         # 样式文件
│   │   ├── index.js        # 入口文件
│   │   └── index.css       # 全局样式
│   └── package.json
├── server/                 # Node.js后端服务
│   ├── scripts/
│   │   └── parse_core.sh   # Core文件解析脚本
│   ├── uploads/            # 上传文件目录
│   ├── results/            # 解析结果目录
│   ├── symbols/            # 符号表文件目录
│   ├── index.js            # 服务器主文件
│   └── package.json
└── package.json            # 根目录配置
```

## 安装要求

- Node.js (版本 14 或更高)
- npm 或 yarn
- Python 3.x
- Linux/Unix 系统

## 安装步骤

1. 克隆项目到本地:
```bash
git clone <repository-url>
cd core_parser
```

2. 安装所有依赖:
```bash
npm run install-all
```

## 运行应用

### 开发模式
同时启动前端和后端开发服务器:
```bash
npm run dev
```

- 前端服务器: http://localhost:3000
- 后端服务器: http://localhost:5000

### 生产模式
1. 构建前端应用:
```bash
npm run build
```

2. 启动后端服务器:
```bash
npm start
```

## 使用说明

1. **上传文件**:
   - 点击"选择文件"按钮选择core文件（必需）
   - 可选择上传符号表目录以获得更详细的解析结果
   - 符号表支持：
     - 点击"选择目录"上传整个符号表目录
     - 保持完整的目录结构和文件层次

2. **开始解析**:
   - 点击"开始解析"按钮启动解析过程
   - 系统会显示解析进度和状态

3. **查看结果**:
   - 解析完成后，结果会显示在下方的结果区域
   - 结果包含调用栈、内存信息、符号解析等详细信息

4. **清空重置**:
   - 点击"清空"按钮可以清除所有上传文件和解析结果

## API接口

### 文件上传
- **POST** `/api/upload`
- 支持multipart/form-data格式
- 字段: `coreFile` (必需), `symbolFile` (可选)

### 开始解析
- **POST** `/api/parse`
- Body: `{ coreFileName: string, symbolFileName?: string }`

### 获取结果
- **GET** `/api/result/:filename`
- 返回解析结果内容

### 清空文件
- **DELETE** `/api/clear`
- 清除所有上传文件和结果

## 自定义解析脚本

项目使用 `server/scripts/core_parse.py` Python脚本进行core文件解析。脚本调用格式：

```bash
python3 server/scripts/core_parse.py -u huatong -p "ht,.062696" -f {core文件路径}
```

参数说明:
- `-u`: 用户名参数
- `-p`: 密码参数
- `-f`: core文件路径

### 符号表处理流程

为了避免Python脚本删除用户上传的符号表，系统采用了智能的符号表管理机制：

1. **用户上传**: 符号表目录上传到 `server/user_symbols/` 目录
2. **脚本执行**: Python脚本正常执行，会删除并重建 `server/symbols/` 目录
3. **自动复制**: 脚本执行完成后，自动将用户符号表复制到 `server/symbols/` 目录
4. **安全保护**: 用户上传的符号表始终保存在 `user_symbols/` 目录中，不会被删除

### 解析结果处理

Python脚本会生成两部分内容：
1. **执行日志**: 脚本运行过程中的打印输出（仅在服务器控制台显示）
2. **解析结果**: 写入到 `{core文件名}_info.log` 文件中的详细解析内容

前端只显示解析结果文件的内容，脚本执行日志会记录在服务器控制台中。

您可以根据实际需求修改Python脚本的逻辑。

## 故障排除

1. **端口冲突**: 如果端口被占用，可以修改 `server/index.js` 中的PORT变量
2. **权限问题**: 确保解析脚本有执行权限: `chmod +x server/scripts/parse_core.sh`
3. **文件上传失败**: 检查服务器磁盘空间和文件大小限制

## 开发说明

- 前端使用React 18和现代JavaScript特性
- 后端使用Express.js和multer处理文件上传
- 支持最大100MB的文件上传
- 自动创建必要的目录结构

## 许可证

MIT License
