/* 页面整体布局 */
.App {
  text-align: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* .App-header 通常是顶部导航或标题区 黑灰色背景 + 白色文字 + padding 内部的 h1 标题没有额外外边距 */
.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  margin-bottom: 30px;
}

.App-header h1 {
  margin: 0;
  font-size: 2rem;
  /* LiOS Minidump解析 */
}

/* .container 用来限制内容宽度（最大 1200px） margin: 0 auto 水平居中 给左右加 20 像素内边距 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 文件上传区域 白色背景 + 圆角 + 内边框 + 阴影 */
.upload-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-section h2 {
  margin-top: 0;
  margin-bottom: 25px;
  color: #333;
  font-size: 1.5rem;
}

/* .file-row 是每一行上传相关的内容，采用 Flex 布局，子元素水平排列 */
.file-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
  /* 15px 元素之间的间距 */
}

/* .file-label 用来显示“文件名”或者“选择文件”的文字提示 */
.file-label {
  min-width: 120px;
  text-align: left;
  font-weight: 500;
  color: #555;
}

/* .file-path 是输入框或显示已选文件路径的区域 */
.file-path {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #f9f9f9;
  color: #666;
}

.file-path:focus {
  outline: none;
  border-color: #007bff;
}

/* .file-path.uploaded 表示已经上传完成的状态（绿色边框+淡绿色背景）*/
.file-path.uploaded {
  border-color: #28a745;
  background-color: #f8fff9;
}

/* 上传按钮，蓝色背景，圆角，可 hover 改变颜色 */
.upload-btn {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s;
  min-width: 80px;
}

.upload-btn:hover {
  background-color: #0056b3;
}

.upload-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 操作按钮 */
/* .action-buttons：按钮容器，按钮左右有间距，居中 */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

/* .action-btn 是所有操作按钮的基础样式 */
.action-btn {
  padding: 12px 30px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.parse-btn {
  /* 解析”按钮，绿色 */
  background-color: #28a745;
  color: white;
}

.parse-btn:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.parse-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background-color: #dc3545;
  color: white;
}

.clear-btn:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-1px);
}

.clear-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 结果展示区域 */
.result-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.result-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 20px;
  min-height: 300px;
  text-align: left;
  overflow-x: auto;
}

.result-content pre {
  margin: 0;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式设计 */
/*
当屏幕宽度 ≤ 768px（手机/平板）时：
.file-row 改竖直排列（方便在小屏上显示）
按钮和标签自动调整大小
.action-buttons 垂直布局
*/
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .file-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .file-label {
    min-width: auto;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 200px;
  }
}