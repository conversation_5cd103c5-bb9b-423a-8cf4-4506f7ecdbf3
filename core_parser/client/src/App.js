import React, { useState } from 'react';
import axios from 'axios';
import './App.css';

function App() {
  const [coreFile, setCoreFile] = useState(null);
  const [symbolFile, setSymbolFile] = useState(null);
  const [coreFilePath, setCoreFilePath] = useState('');
  const [symbolFilePath, setSymbolFilePath] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [parseResult, setParseResult] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState({});



  // 处理core文件选择并自动上传
  const handleCoreFileChange = async (event) => {
    const file = event.target.files[0];
    if (file) {
      setCoreFile(file);
      setCoreFilePath(file.name);

      // 自动上传core文件
      await uploadSingleFile(file, 'coreFile');
    }
  };

  // 处理符号表文件选择并自动上传
  const handleSymbolFileChange = async (event) => {
    console.log('=== handleSymbolFileChange 被调用 ===');
    const files = event.target.files;
    console.log('选择的文件数量:', files ? files.length : 0);

    if (files && files.length > 0) {
      const fileList = Array.from(files);
      const firstFile = fileList[0];

      console.log('第一个文件信息:', {
        name: firstFile.name,
        size: firstFile.size,
        webkitRelativePath: firstFile.webkitRelativePath,
        type: firstFile.type
      });

      // 检查是否是目录选择（通过webkitRelativePath判断）
      if (firstFile.webkitRelativePath && firstFile.webkitRelativePath.includes('/')) {
        // 目录选择
        console.log('检测到目录选择，文件数量:', fileList.length);
        console.log('第一个文件的相对路径:', firstFile.webkitRelativePath);

        setSymbolFile(firstFile); // 设置第一个文件作为代表
        setSymbolFilePath(`目录: ${firstFile.webkitRelativePath.split('/')[0]} (${fileList.length}个文件)`);

        // 上传所有文件
        console.log('开始上传目录文件...');
        await uploadDirectoryFiles(fileList);
      } else {
        // 单个文件选择
        console.log('检测到单个文件选择:', firstFile.name);
        setSymbolFile(firstFile);
        setSymbolFilePath(firstFile.name);
        await uploadSingleFile(firstFile, 'symbolFile');
      }
    } else {
      console.log('没有选择任何文件');
    }
  };

  // 上传单个文件
  const uploadSingleFile = async (file, fileType) => {
    setIsUploading(true);
    const formData = new FormData();
    formData.append(fileType, file);

    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setUploadedFiles(prev => ({
          ...prev,
          ...response.data.files
        }));
        console.log(`${fileType}上传成功:`, response.data.files);
      }
    } catch (error) {
      console.error('上传失败:', error);
      alert(`${fileType === 'coreFile' ? 'Core文件' : '符号表文件'}上传失败`);
    } finally {
      setIsUploading(false);
    }
  };

  // 上传目录文件
  const uploadDirectoryFiles = async (fileList) => {
    console.log('=== uploadDirectoryFiles 开始执行 ===');
    console.log('文件列表长度:', fileList.length);

    setIsUploading(true);
    const formData = new FormData();

    // 将所有文件添加到FormData中
    fileList.forEach((file, index) => {
      console.log(`添加文件 ${index}:`, {
        name: file.name,
        path: file.webkitRelativePath,
        size: file.size
      });
      formData.append(`symbolFile_${index}`, file);
      formData.append(`symbolFile_${index}_path`, file.webkitRelativePath);
    });

    formData.append('isDirectory', 'true');
    formData.append('fileCount', fileList.length.toString());

    console.log('FormData 准备完成，开始发送请求...');

    try {
      const response = await axios.post('/api/upload-directory', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('服务器响应:', response.data);

      if (response.data.success) {
        setUploadedFiles(prev => ({
          ...prev,
          symbolFile: response.data.directoryName
        }));
        console.log('目录上传成功:', response.data);
        alert('符号表目录上传成功！');
      } else {
        console.error('服务器返回失败:', response.data);
        alert('符号表目录上传失败: ' + (response.data.error || '未知错误'));
      }
    } catch (error) {
      console.error('目录上传失败:', error);
      console.error('错误详情:', error.response?.data);
      alert('符号表目录上传失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setIsUploading(false);
    }
  };

  // 批量上传文件（保留原有功能，但现在主要用于重新上传）
  const uploadFiles = async () => {
    if (!coreFile) {
      alert('请选择core文件');
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append('coreFile', coreFile);
    if (symbolFile) {
      formData.append('symbolFile', symbolFile);
    }

    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setUploadedFiles(response.data.files);
        alert('文件上传成功！');
      }
    } catch (error) {
      console.error('上传失败:', error);
      alert('文件上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  // 开始解析
  const startParsing = async () => {
    if (!uploadedFiles.coreFile) {
      alert('请先上传文件');
      return;
    }



    setIsParsing(true);
    setParseResult('正在解析中...');

    try {
      const response = await axios.post('/api/parse', {
        coreFileName: uploadedFiles.coreFile,
        symbolFileName: uploadedFiles.symbolFile,
      });

      if (response.data.success) {
        // 获取解析结果
        const resultResponse = await axios.get(`/api/result/${response.data.resultFile}`);
        if (resultResponse.data.success) {
          setParseResult(resultResponse.data.content);
        }
      }
    } catch (error) {
      console.error('解析失败:', error);
      setParseResult('解析失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setIsParsing(false);
    }
  };

  // 清空所有内容
  const clearAll = async () => {
    try {
      await axios.delete('/api/clear');
      setCoreFile(null);
      setSymbolFile(null);
      setCoreFilePath('');
      setSymbolFilePath('');
      setParseResult('');
      setUploadedFiles({});

      // 清空文件输入框
      document.getElementById('coreFileInput').value = '';
      document.getElementById('symbolFileInput').value = '';

      alert('已清空所有内容');
    } catch (error) {
      console.error('清空失败:', error);
      alert('清空失败');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>LiOS Minidump解析</h1>
      </header>

      <div className="container">
        {/* 文件上传区域 */}
        <div className="upload-section">
          <h2>文件上传</h2>

          {/* Core文件行 */}
          <div className="file-row">
            <label className="file-label">core文件:</label>
            <input
              type="text"
              className="file-path"
              value={coreFilePath || '请选择core文件'}
              readOnly
              placeholder="请选择core文件"
              style={{
                color: uploadedFiles.coreFile ? '#28a745' : '#666',
                fontWeight: uploadedFiles.coreFile ? 'bold' : 'normal'
              }}
            />
            <input
              id="coreFileInput"
              type="file"
              onChange={handleCoreFileChange}
              style={{ display: 'none' }}
              accept=".core,.*"
            />
            <button
              className="upload-btn"
              onClick={() => document.getElementById('coreFileInput').click()}
              disabled={isUploading}
            >
              {isUploading ? '上传中...' : (uploadedFiles.coreFile ? '重新选择' : '选择文件')}
            </button>
          </div>

          {/* 符号表文件行 */}
          <div className="file-row">
            <label className="file-label">小包符号表:</label>
            <input
              type="text"
              className="file-path"
              value={symbolFilePath || '请选择符号表目录（可选）'}
              readOnly
              placeholder="请选择符号表目录（可选）"
              style={{
                color: uploadedFiles.symbolFile ? '#28a745' : '#666',
                fontWeight: uploadedFiles.symbolFile ? 'bold' : 'normal'
              }}
            />
            <input
              id="symbolDirInput"
              type="file"
              onChange={handleSymbolFileChange}
              style={{ display: 'none' }}
              webkitdirectory=""
              directory=""
              multiple
            />
            <button
              className="upload-btn"
              onClick={() => document.getElementById('symbolDirInput').click()}
              disabled={isUploading}
            >
              {isUploading ? '上传中...' : (uploadedFiles.symbolFile ? '重新选择' : '选择目录')}
            </button>
          </div>

          {/* 操作按钮 */}
          <div className="action-buttons">
            <button
              className="action-btn parse-btn"
              onClick={startParsing}
              disabled={isParsing || !uploadedFiles.coreFile || isUploading}
            >
              {isParsing ? '解析中...' : '开始解析'}
            </button>
            <button
              className="action-btn clear-btn"
              onClick={clearAll}
              disabled={isUploading || isParsing}
            >
              清空
            </button>
          </div>
        </div>

        {/* 结果展示区域 */}
        <div className="result-section">
          <h2>解析结果</h2>
          <div className="result-content">
            <pre>{parseResult || '等待解析...'}</pre>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
