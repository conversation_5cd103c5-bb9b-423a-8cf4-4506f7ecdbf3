# 安装指南

## 系统要求

- Ubuntu 18.04+ / CentOS 7+ / macOS 10.14+
- Node.js 14.0+ 
- npm 6.0+
- Bash shell

## 快速安装

### 1. 安装Node.js和npm

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install nodejs npm
```

#### CentOS/RHEL:
```bash
sudo yum install nodejs npm
```

#### macOS (使用Homebrew):
```bash
brew install node
```

### 2. 验证安装
```bash
node --version  # 应该显示 v14.0.0 或更高版本
npm --version   # 应该显示 6.0.0 或更高版本
```

### 3. 克隆并启动项目
```bash
# 进入项目目录
cd core_parser

# 使用快速启动脚本
./start.sh
```

## 手动安装步骤

如果快速启动脚本失败，可以按以下步骤手动安装：

### 1. 安装根目录依赖
```bash
npm install
```

### 2. 安装后端依赖
```bash
cd server
npm install
cd ..
```

### 3. 安装前端依赖
```bash
cd client
npm install
cd ..
```

### 4. 设置脚本权限
```bash
chmod +x server/scripts/parse_core.sh
chmod +x start.sh
```

### 5. 启动开发服务器
```bash
npm run dev
```

## 生产环境部署

### 1. 构建前端应用
```bash
cd client
npm run build
cd ..
```

### 2. 启动生产服务器
```bash
cd server
npm start
```

### 3. 使用PM2管理进程（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server/index.js --name "core-parser"

# 查看状态
pm2 status

# 查看日志
pm2 logs core-parser
```

## 故障排除

### 端口被占用
如果遇到端口冲突，修改以下文件：
- 后端端口：编辑 `server/index.js` 中的 `PORT` 变量
- 前端端口：在 `client` 目录下创建 `.env` 文件，添加 `PORT=3001`

### 权限问题
```bash
# 确保脚本有执行权限
chmod +x server/scripts/parse_core.sh
chmod +x start.sh

# 确保目录可写
chmod 755 server/uploads server/results server/symbols
```

### 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules server/node_modules client/node_modules
npm run install-all
```

## 验证安装

访问 http://localhost:3000，您应该看到Core文件解析器的界面。

尝试上传一个测试文件并点击"开始解析"，如果看到解析结果，说明安装成功。
