const express = require('express');
const multer = require('multer');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');
const tar = require('tar');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const resultsDir = path.join(__dirname, 'results');
const symbolsDir = path.join(__dirname, 'symbols');

fs.ensureDirSync(uploadsDir);
fs.ensureDirSync(resultsDir);
fs.ensureDirSync(symbolsDir);

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // 保持原始文件名
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB限制
  }
});

// 为解析任务创建唯一的工作目录
function createUniqueWorkDir() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 8);
  return `work_${timestamp}_${randomId}`;
}

// 准备解析工作目录
async function prepareWorkDirectory(workDirName, hasUserSymbols = false) {
  const workDir = path.join(__dirname, 'work_dirs', workDirName);
  const workSymbolsDir = path.join(workDir, 'symbols');
  const resultsDir = path.join(workDir, 'results');

  try {
    // 创建工作目录结构
    await fs.ensureDir(workDir);
    await fs.ensureDir(workSymbolsDir);
    await fs.ensureDir(resultsDir);

    console.log(`创建工作目录: ${workDirName}`);

    // 如果有用户符号表，复制到工作目录的symbols中
    if (hasUserSymbols) {
      const mainSymbolsDir = path.join(__dirname, 'symbols');

      if (await fs.pathExists(mainSymbolsDir)) {
        console.log('开始移动处理后的符号表到工作目录...');

        const items = await fs.readdir(mainSymbolsDir);
        for (const item of items) {
          const sourcePath = path.join(mainSymbolsDir, item);
          const targetPath = path.join(workSymbolsDir, item);

          if (await fs.pathExists(sourcePath)) {
            await fs.move(sourcePath, targetPath, { overwrite: true });
            console.log(`已移动符号表: ${item} -> ${workDirName}/symbols/`);
          }
        }

        console.log('符号表移动完成');
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('准备工作目录失败:', error);
    throw error;
  }
}

// 清理每个用户的工作目录
async function cleanupWorkDirectory(workDirName) {
  const workDir = path.join(__dirname, 'work_dirs', workDirName);

  try {
    if (await fs.pathExists(workDir)) {
      await fs.remove(workDir);
      console.log(`已清理工作目录: ${workDirName}`);
    }
  } catch (error) {
    console.error('清理工作目录失败:', error);
  }
}



// 处理符号表文件的函数
async function processSymbolFile(symbolFilePath, symbolFileName) {
  // 使用用户上传的符号表目录，避免与Python脚本的symbols目录冲突
  const symbolsTargetDir = path.join(__dirname, 'user_symbols');

  // 确保user_symbols目录存在
  await fs.ensureDir(symbolsTargetDir);

  // 检查文件扩展名
  const fileExt = path.extname(symbolFileName).toLowerCase();

  if (fileExt === '.gz' && symbolFileName.includes('.tar.gz')) {
    // 处理tar.gz文件
    console.log(`开始解压符号表文件: ${symbolFileName}`);

    try {
      // 创建临时解压目录
      const tempExtractDir = path.join(__dirname, 'temp_extract');
      await fs.ensureDir(tempExtractDir);

      // 解压tar.gz文件
      await tar.extract({
        file: symbolFilePath,
        cwd: tempExtractDir
      });

      console.log(`符号表文件解压完成: ${symbolFileName}`);

      // 将解压后的内容复制到symbols目录
      const extractedItems = await fs.readdir(tempExtractDir);

      for (const item of extractedItems) {
        const sourcePath = path.join(tempExtractDir, item);
        const targetPath = path.join(symbolsTargetDir, item);

        // 如果目标已存在，先删除
        if (await fs.pathExists(targetPath)) {
          await fs.remove(targetPath);
        }

        await fs.copy(sourcePath, targetPath);
        console.log(`已复制符号表内容: ${item} -> symbols/`);
      }

      // 清理临时目录
      await fs.remove(tempExtractDir);

      return { success: true, message: `符号表文件 ${symbolFileName} 解压并复制成功` };

    } catch (error) {
      console.error('处理符号表文件失败:', error);
      return { success: false, error: `解压符号表文件失败: ${error.message}` };
    }

  } else {
    // 处理非压缩文件或目录
    const targetPath = path.join(symbolsTargetDir, symbolFileName);

    try {
      // 如果目标已存在，先删除
      if (await fs.pathExists(targetPath)) {
        await fs.remove(targetPath);
      }

      await fs.copy(symbolFilePath, targetPath);
      console.log(`已复制符号表文件: ${symbolFileName} -> symbols/`);

      return { success: true, message: `符号表文件 ${symbolFileName} 复制成功` };

    } catch (error) {
      console.error('复制符号表文件失败:', error);
      return { success: false, error: `复制符号表文件失败: ${error.message}` };
    }
  }
}

// 文件上传接口
app.post('/api/upload', upload.fields([
  { name: 'coreFile', maxCount: 1 },
  { name: 'symbolFile', maxCount: 1 }
]), async (req, res) => {
  try {
    const { coreFile, symbolFile } = req.files;

    if (!coreFile || !coreFile[0]) {
      return res.status(400).json({ error: 'Core文件是必需的' });
    }

    const coreFilePath = coreFile[0].path;
    // const hasSymbolFile = symbolFile && symbolFile[0];

    // let symbolFilePath = null;
    // if (hasSymbolFile) {
    //   symbolFilePath = symbolFile[0].path;
    // }

    console.log('=== 文件上传调试信息 ===');
    console.log('coreFile:', coreFile);
    // console.log('symbolFile:', symbolFile);
    console.log('coreFilePath:', coreFilePath);
    // console.log('symbolFile[0]:', symbolFile[0]);
    // console.log('symbolFilePath:', symbolFilePath);

    res.json({
      success: true,
      message: '文件上传成功',
      files: {
        coreFile: coreFile[0].filename
        // symbolFile: hasSymbolFile ? symbolFile[0].filename : null
      }
    });
  } catch (error) {
    console.error('文件上传错误:', error);
    res.status(500).json({ error: '文件上传失败' });
  }
});

// 简单处理符号表目录：将上传目录下的所有子目录移动到symbols目录
async function processSymbolDirectories(userSymbolsDir) {
  try {
    const symbolsDir = path.join(__dirname, 'symbols');
    await fs.ensureDir(symbolsDir);

    console.log('开始处理符号表目录结构...');

    // 扫描user_symbols目录
    const items = await fs.readdir(userSymbolsDir);

    for (const item of items) {
      const itemPath = path.join(userSymbolsDir, item);
      const stat = await fs.stat(itemPath);

      if (stat.isDirectory()) {
        // 扫描这个目录下的所有子目录
        const subItems = await fs.readdir(itemPath);

        for (const subItem of subItems) {
          const subItemPath = path.join(itemPath, subItem);
          const subStat = await fs.stat(subItemPath);

          if (subStat.isDirectory()) {
            // 将子目录移动到symbols目录
            const targetPath = path.join(symbolsDir, subItem);
            await fs.move(subItemPath, targetPath, { overwrite: true });
            console.log(`移动子目录: ${subItem} -> symbols/`);
          }
        }
      }
    }

    console.log('符号表目录结构处理完成');
    return true;
  } catch (error) {
    console.error('处理符号表目录结构失败:', error);
    throw error;
  }
}

// 目录上传接口
app.post('/api/upload-directory', upload.any(), async (req, res) => {
  try {
    const files = req.files;
    const { isDirectory, fileCount } = req.body;

    console.log('=== 目录上传调试信息 ===');
    console.log('isDirectory:', isDirectory);
    console.log('fileCount:', fileCount);
    console.log('files数量:', files ? files.length : 0);
    console.log('req.body keys:', Object.keys(req.body));

    if (!isDirectory || !files || files.length === 0) {
      console.error('目录上传验证失败:', { isDirectory, filesLength: files?.length });
      return res.status(400).json({ error: '无效的目录上传请求' });
    }

    console.log(`接收到目录上传请求，包含 ${files.length} 个文件`);

    // 创建目录结构并复制文件到用户符号表目录
    const userSymbolsDir = path.join(__dirname, 'user_symbols');
    await fs.ensureDir(userSymbolsDir);

    // 获取目录名（从第一个文件的路径中提取）
    const firstFile = files[0];
    const relativePath = req.body[`symbolFile_0_path`] || firstFile.originalname;
    const directoryName = relativePath.split('/')[0];

    console.log('第一个文件信息:', {
      originalname: firstFile.originalname,
      relativePath: relativePath,
      directoryName: directoryName
    });

    // 处理每个文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileRelativePath = req.body[`symbolFile_${i}_path`] || file.originalname;

      console.log(`处理文件 ${i + 1}/${files.length}:`, {
        originalname: file.originalname,
        fileRelativePath: fileRelativePath,
        size: file.size
      });

      // 构建目标路径
      const targetFilePath = path.join(userSymbolsDir, fileRelativePath);
      const targetDir = path.dirname(targetFilePath);

      // 确保目标目录存在
      await fs.ensureDir(targetDir);

      // 复制文件
      await fs.copy(file.path, targetFilePath);
      console.log(`文件复制成功: ${file.originalname} -> ${targetFilePath}`);

      // 删除临时文件
      await fs.remove(file.path);
    }

    console.log(`目录 ${directoryName} 上传并复制到 user_symbols/ 目录成功`);

    // 智能处理符号表目录结构
    await processSymbolDirectories(userSymbolsDir);

    res.json({
      success: true,
      message: '目录上传成功，符号表已处理',
      directoryName: directoryName,
      fileCount: files.length
    });
  } catch (error) {
    console.error('目录上传错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ error: '目录上传失败: ' + error.message });
  }
});

// 解析接口
app.post('/api/parse', async (req, res) => {
  try {
    const { coreFileName, symbolFileName } = req.body;

    console.log('=== 解析接口调试信息 ===');
    console.log('coreFileName:', coreFileName);
    console.log('symbolFileName:', symbolFileName);

    if (!coreFileName) {
      return res.status(400).json({ error: 'Core文件名是必需的' });
    }

    const coreFilePath = path.join(uploadsDir, coreFileName);

    // 检查core文件是否存在
    if (!fs.existsSync(coreFilePath)) {
      return res.status(404).json({ error: 'Core文件不存在' });
    }

    // 创建唯一的工作目录
    const workDirName = createUniqueWorkDir();
    const workDir = path.join(__dirname, 'work_dirs', workDirName);
    const workSymbolsDir = path.join(workDir, 'symbols');
    const workResultsDir = path.join(workDir, 'results');

    console.log('workDir:', workDir);


    console.log(`开始解析任务，工作目录: ${workDirName}`);



    try {
      // 准备工作目录，如果有用户符号表则复制
      const hasUserSymbols = symbolFileName && await fs.pathExists(path.join(__dirname, 'symbols'));
      await prepareWorkDirectory(workDirName, hasUserSymbols);

      // 构建Python解析命令，使用工作目录的symbols
      const pythonScriptPath = path.join(__dirname, 'scripts', 'core_parse.py');
      const command = `python3 "${pythonScriptPath}" -u huatong -p "ht,.062696" -f "${coreFilePath}" -s "${workSymbolsDir}"`;

      console.log(`执行解析命令: ${command}`);

      // 执行解析命令
      exec(command, async (error, stdout, stderr) => {
        try {
          if (error) {
            console.error('解析错误:', error);
            await cleanupWorkDirectory(workDirName);
            return res.status(500).json({ error: '解析失败', details: error.message });
          }

          // Python脚本执行完成后，查找生成的info.log文件
          const coreBaseName = path.basename(coreFilePath);
          const infoLogFileName = `${coreBaseName}_info.log`;
          const infoLogPath = path.join(__dirname, infoLogFileName);

          // 生成结果文件名，保存到工作目录的results中
          const resultFileName = `result_${workDirName}.txt`;
          const workResultsDir = path.join(workDir, 'results');
          const resultFilePath = path.join(workResultsDir, resultFileName);

          // 检查info.log文件是否存在
          if (fs.existsSync(infoLogPath)) {
            // 读取info.log文件内容
            const infoLogContent = await fs.readFile(infoLogPath, 'utf8');

            // 保存解析结果文件内容
            await fs.writeFile(resultFilePath, infoLogContent);

            // 删除临时的info.log文件
            await fs.remove(infoLogPath);

            console.log(`解析完成，结果文件: ${resultFileName}`);
            console.log(`Python脚本执行日志: ${stdout}`);
            if (stderr) {
              console.log(`Python脚本错误信息: ${stderr}`);
            }
          } else {
            // 如果没有找到info.log文件，显示错误信息
            const errorContent = `解析失败: 未找到解析结果文件 ${infoLogFileName}\n\n脚本执行输出:\n${stdout}\n\n错误信息:\n${stderr}`;
            await fs.writeFile(resultFilePath, errorContent);

            console.log(`警告: 未找到解析结果文件 ${infoLogFileName}`);
          }

          // 不立即清理工作目录，保留结果文件供后续读取
          // await cleanupWorkDirectory(workDirName);

          res.json({
            success: true,
            message: '解析完成',
            resultFile: resultFileName,
            workDir: workDirName
          });
        } catch (fileError) {
          console.error('读取解析结果文件错误:', fileError);
          await cleanupWorkDirectory(workDirName);
          res.status(500).json({ error: '读取解析结果失败', details: fileError.message });
        }
      });

    } catch (prepError) {
      console.error('准备工作目录失败:', prepError);
      await cleanupWorkDirectory(workDirName);
      res.status(500).json({ error: '准备工作环境失败', details: prepError.message });
    }

  } catch (error) {
    console.error('解析处理错误:', error);
    res.status(500).json({ error: '解析处理失败' });
  }
});

// 获取解析结果
app.get('/api/result/:filename', async (req, res) => {
  try {
    const { filename } = req.params;

    // 从文件名中提取工作目录名
    const workDirName = filename.replace('result_', '').replace('.txt', '');
    const workDir = path.join(__dirname, 'work_dirs', workDirName);
    const workResultsDir = path.join(workDir, 'results');
    const resultFilePath = path.join(workResultsDir, filename);

    console.log('查找结果文件:', resultFilePath);

    if (!fs.existsSync(resultFilePath)) {
      console.error('结果文件不存在:', resultFilePath);
      return res.status(404).json({ error: '结果文件不存在' });
    }

    const content = await fs.readFile(resultFilePath, 'utf8');
    res.json({
      success: true,
      content: content
    });
  } catch (error) {
    console.error('读取结果错误:', error);
    res.status(500).json({ error: '读取结果失败' });
  }
});

// 清空上传文件
app.delete('/api/clear', async (req, res) => {
  try {
    console.log('开始清空所有文件和目录...');

    // 清空上传目录
    await fs.emptyDir(uploadsDir);
    console.log('已清空uploads目录');

    // 不再需要清空server根目录下的results目录，因为结果现在保存在工作目录中

    // 清空旧的symbols目录（如果存在）
    if (await fs.pathExists(symbolsDir)) {
      await fs.emptyDir(symbolsDir);
      console.log('已清空symbols目录');
    }

    // 清空用户符号表目录
    const userSymbolsDir = path.join(__dirname, 'user_symbols');
    if (await fs.pathExists(userSymbolsDir)) {
      await fs.emptyDir(userSymbolsDir);
      console.log('已清空user_symbols目录');
    }

    // 清空所有工作目录
    const workDirsPath = path.join(__dirname, 'work_dirs');
    if (await fs.pathExists(workDirsPath)) {
      await fs.remove(workDirsPath);
      console.log('已清空work_dirs目录');
    }

    // 清理可能残留的info.log文件
    const files = await fs.readdir(__dirname);
    for (const file of files) {
      if (file.endsWith('_info.log')) {
        await fs.remove(path.join(__dirname, file));
        console.log(`已删除残留文件: ${file}`);
      }
    }

    console.log('所有文件和目录清空完成');

    res.json({
      success: true,
      message: '所有文件和目录已清空'
    });
  } catch (error) {
    console.error('清空文件错误:', error);
    res.status(500).json({ error: '清空文件失败: ' + error.message });
  }
});

app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
});
