import re
import argparse
import requests
import os
from requests.auth import HTTPBasicAuth
import subprocess
import tarfile

# TL;DR
# 本地解析单个core文件: python3 core_parse.py -u {域账号} -p {密码} -f {core文件}
# 本地,云端解析core目录下文件: python3 core_parse.py -u {域账号} -p {密码} -d {解压后的core文件目录}


class MyPath:
    @staticmethod
    def find_files(path, name):
        matched_files = []

        for root, dirs, files in os.walk(path):
            for file in files:
                if name == file:
                    matched_files.append(os.path.join(root, file))

        return matched_files
    
    @staticmethod
    def create_path(path):
        """目录存在时删除重建"""
        if os.path.exists(path):
            try:
                os.rmdir(path)
            except OSError:
                import shutil
                shutil.rmtree(path)
            print(f"已删除现有目录: {path}")

        os.makedirs(path, exist_ok=True)
        print(f"已创建目录: {path}")
    
    @staticmethod
    def get_curr_script_dir():
        script_path = os.path.abspath(__file__)
        return os.path.dirname(script_path)


class SymbolFileDownloader():
    def __init__(self, user, password, symbols_path="./symbols/") -> None:
        self.user = user
        self.password = password
        self.auth = HTTPBasicAuth(self.user, self.password)
        self.output_path = symbols_path
        self.output_file = os.path.join(symbols_path, "sym.tar.gz")
        # self.core_file = core_file
        # self.core_file_dir = core_file_dir

        # 确保symbols目录存在，但不删除现有内容
        os.makedirs(self.output_path, exist_ok=True)
        print(f"使用符号表目录: {self.output_path}")

    def parse_version(self, core_file_path):
        cmd = f"{MyPath.get_curr_script_dir()}/bin/minidump_dump -v {core_file_path} 2>/dev/null | grep -w \"version:\""
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        version = result.stdout

        sub_version=""
        match = re.search(r'ota.(.*)', version)
        if match:
            sub_version = match.group(1)
            self.output_file = os.path.join(self.output_path, f"{sub_version}.tar.gz")
        else:
            print(f"version={version}")
            print("解析版本号失败, 这个core不是使用最新的Minidump可执行文件生成的")
            exit(1)
        return sub_version
        

    """ 下载对应版本的符号表包 """
    def download(self, core_file_path) -> bool:
        version = self.parse_version(core_file_path)
        url = f"https://artifactory.ep.chehejia.com:443/artifactory/ad-x-platform-local/release/max/Sym/Sym_{version}.tar.gz"
        print(f"download symbol package from: {url}")

        tar_gz_path = os.path.join(self.output_path, version + ".tar.gz")
        if os.path.exists(tar_gz_path):
            print(f"这个版本的符号表包已经下载过,无需重复下载")
            return True
        try:
            response = requests.get(url, auth=self.auth, stream=True)

            # download success
            if response.status_code == 200:
                with open(self.output_file, 'wb') as file:
                    for chunk in response.iter_content(1024):
                        if chunk:  # 过滤掉keep-alive new chunks
                            file.write(chunk)
                with tarfile.open(self.output_file, 'r:gz') as tar:
                    tar.extractall(path=self.output_path)
                print(f"符号表包已成功下载到: {self.output_file}")
                print("\n")
                return True
            print(f"下载失败，状态码: {response.status_code} url:{url}")
            exit(1)

        except requests.exceptions.HTTPError as err:
            print(f"HTTP 错误发生: {err}")
        except Exception as err:
            print(f"发生其他错误: {err}")

        return False
    
    def parse_core_content(self, core_file_path):
        base_name = os.path.basename(core_file_path)
        cmd = f"{MyPath.get_curr_script_dir()}/bin/minidump_stackwalk {core_file_path} {self.output_path} > {base_name}_info.log"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        # print("===================================================================")
        print(f"解析core文件{base_name}成功")
        print("===================================================================")


def parse_args():
    """ 解析转换命令行参数 """
    parser = argparse.ArgumentParser(description="大包版本core文件解析(带行号)")

    """ 本地解析用 """
    parser.add_argument('--core_file', '-f',
                        metavar="file",
                        type=str,
                        default="",
                        help="core文件")

    parser.add_argument('--core_file_dir', '-d',
                        type=str,
                        default="",
                        help="core文件目录")

    """ 符号表目录 """
    parser.add_argument('--symbols_dir', '-s',
                        type=str,
                        default="./symbols/",
                        help="符号表目录路径")

    """ 制品库的账号 """
    parser.add_argument('--user', '-u',
                        default="",
                        help="可以访问制品库的账号")

    """ 制品库的密码 """
    parser.add_argument('--password', '-p',
                        default="",
                        help="账号密码")

    args = parser.parse_args()
    return args


def main():
    args = parse_args()

    if not args.user or not args.password:
        print("请提供制品库用户名、密码和core文件!!")
        exit(1)

    if not args.core_file and not args.core_file_dir:
        print("请提供core文件或core文件目录")
        exit(1)

    user = args.user
    password = args.password
    core_file = args.core_file
    core_file_dir = args.core_file_dir
    symbols_dir = args.symbols_dir

    print(f"使用符号表目录: {symbols_dir}")

    if args.core_file:
        downloader = SymbolFileDownloader(user, password, symbols_dir)
        downloader.download(args.core_file)

        # 解析core
        downloader.parse_core_content(args.core_file)

    if args.core_file_dir:
        downloader = SymbolFileDownloader(user, password, symbols_dir)

        for root, dirs, files in os.walk(args.core_file_dir):
            for file_name in files:
                core_file_path = os.path.join(root, file_name)
                downloader.download(core_file_path)

                downloader.parse_core_content(core_file_path)


if __name__ == '__main__':
    main()   