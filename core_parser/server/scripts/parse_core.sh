#!/bin/bash

# Core文件解析脚本示例
# 参数: $1 = core文件路径, $2 = 输出文件路径, $3 = 符号表文件路径(可选)

CORE_FILE="$1"
OUTPUT_FILE="$2"
SYMBOL_FILE="$3"

echo "=== Core文件解析结果 ===" > "$OUTPUT_FILE"
echo "解析时间: $(date)" >> "$OUTPUT_FILE"
echo "Core文件: $CORE_FILE" >> "$OUTPUT_FILE"

if [ -n "$SYMBOL_FILE" ]; then
    echo "符号表文件: $SYMBOL_FILE" >> "$OUTPUT_FILE"
    echo "解析模式: 双文件模式" >> "$OUTPUT_FILE"
else
    echo "解析模式: 单文件模式" >> "$OUTPUT_FILE"
fi

echo "" >> "$OUTPUT_FILE"
echo "=== 文件信息 ===" >> "$OUTPUT_FILE"

if [ -f "$CORE_FILE" ]; then
    echo "Core文件大小: $(stat -c%s "$CORE_FILE") bytes" >> "$OUTPUT_FILE"
    echo "Core文件类型: $(file "$CORE_FILE")" >> "$OUTPUT_FILE"
else
    echo "错误: Core文件不存在" >> "$OUTPUT_FILE"
    exit 1
fi

if [ -n "$SYMBOL_FILE" ] && [ -f "$SYMBOL_FILE" ]; then
    echo "符号表文件大小: $(stat -c%s "$SYMBOL_FILE") bytes" >> "$OUTPUT_FILE"
    echo "符号表文件类型: $(file "$SYMBOL_FILE")" >> "$OUTPUT_FILE"
fi

echo "" >> "$OUTPUT_FILE"
echo "=== 解析过程 ===" >> "$OUTPUT_FILE"
echo "1. 正在读取core文件头部信息..." >> "$OUTPUT_FILE"
echo "2. 正在分析内存映射..." >> "$OUTPUT_FILE"
echo "3. 正在提取调用栈信息..." >> "$OUTPUT_FILE"

if [ -n "$SYMBOL_FILE" ]; then
    echo "4. 正在加载符号表..." >> "$OUTPUT_FILE"
    echo "5. 正在解析符号信息..." >> "$OUTPUT_FILE"
fi

echo "6. 正在生成解析报告..." >> "$OUTPUT_FILE"

echo "" >> "$OUTPUT_FILE"
echo "=== 解析结果 ===" >> "$OUTPUT_FILE"
echo "程序崩溃位置: 0x7fff12345678" >> "$OUTPUT_FILE"
echo "崩溃原因: 段错误 (SIGSEGV)" >> "$OUTPUT_FILE"
echo "调用栈深度: 5" >> "$OUTPUT_FILE"

echo "" >> "$OUTPUT_FILE"
echo "=== 调用栈信息 ===" >> "$OUTPUT_FILE"
echo "#0  0x7fff12345678 in main_function ()" >> "$OUTPUT_FILE"
echo "#1  0x7fff12345600 in process_data ()" >> "$OUTPUT_FILE"
echo "#2  0x7fff12345550 in handle_request ()" >> "$OUTPUT_FILE"
echo "#3  0x7fff12345500 in worker_thread ()" >> "$OUTPUT_FILE"
echo "#4  0x7fff12345450 in thread_start ()" >> "$OUTPUT_FILE"

if [ -n "$SYMBOL_FILE" ]; then
    echo "" >> "$OUTPUT_FILE"
    echo "=== 符号解析信息 ===" >> "$OUTPUT_FILE"
    echo "已解析符号数量: 1234" >> "$OUTPUT_FILE"
    echo "未解析符号数量: 56" >> "$OUTPUT_FILE"
    echo "符号解析成功率: 95.6%" >> "$OUTPUT_FILE"
fi

echo "" >> "$OUTPUT_FILE"
echo "=== 内存信息 ===" >> "$OUTPUT_FILE"
echo "堆内存使用: 256MB" >> "$OUTPUT_FILE"
echo "栈内存使用: 8MB" >> "$OUTPUT_FILE"
echo "共享库数量: 42" >> "$OUTPUT_FILE"

echo "" >> "$OUTPUT_FILE"
echo "解析完成时间: $(date)" >> "$OUTPUT_FILE"
echo "解析状态: 成功" >> "$OUTPUT_FILE"

echo "Core文件解析完成"
