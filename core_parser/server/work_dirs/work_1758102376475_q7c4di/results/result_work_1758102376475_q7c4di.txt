Operating system: Linux
                  0.0.0 Linux 5.15.168-rt-tegra #1 SMP PREEMPT_RT Fri Sep 5 23:09:06 CST 2025 aarch64
CPU: arm64
     12 CPUs

GPU: UNKNOWN

Crash reason:  SIGABRT
Crash address: 0x0
Process uptime: not available

Thread 0 (crashed)
 0  libc.so.6 + 0x33d88
     x0 = 0x0000000000000000    x1 = 0x0000fffef39c3ce8
     x2 = 0x0000000000000000    x3 = 0x0000000000000008
     x4 = 0x0000000000000000    x5 = 0x0000ffff86c5e000
     x6 = 0xffffffffffffffff    x7 = 0xffffffffffffffff
     x8 = 0x0000000000000087    x9 = 0x0000fffef39c3ec0
    x10 = 0xffffffffffffffff   x11 = 0xffffffffffffffff
    x12 = 0xffffffffffffffff   x13 = 0xffffffffffffffff
    x14 = 0xffffffffffffffff   x15 = 0xffffffffffffffff
    x16 = 0xffffffffffffffff   x17 = 0xffffffffffffffff
    x18 = 0xffffffffffffffff   x19 = 0xffffffffffffffff
    x20 = 0x0000fffef39c3ea0   x21 = 0x0000ffff86c60ac0
    x22 = 0x0000ffff86c60660   x23 = 0x0000fffef39c42e8
    x24 = 0x000000031c04a000   x25 = 0x000000031bf4c000
    x26 = 0x0000000000000000   x27 = 0x0000fffd3db97490
    x28 = 0x0000fffd3db97358    fp = 0x0000fffef39c3cc0
     lr = 0x0000fffef39c3ce8    sp = 0x0000fffef39c3cc0
     pc = 0x0000ffff86b24d88
    Found by: given as instruction pointer in context
 1  0xfffef39c3ce4
     fp = 0x0000fffef39c3df0    lr = 0x0000ffff86b11b64
     sp = 0x0000fffef39c3cd0    pc = 0x0000fffef39c3ce8
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x20b60
     fp = 0x0000fffef39c3f40    lr = 0x0000ffff86d298bc
     sp = 0x0000fffef39c3e00    pc = 0x0000ffff86b11b64
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!__gnu_cxx::__verbose_terminate_handler() + 0x198
     fp = 0x0000fffef39c3f80    lr = 0x0000ffff86d2720c
     sp = 0x0000fffef39c3f50    pc = 0x0000ffff86d298bc
    Found by: previous frame's frame pointer
 4  0xffff86e5ef94
    x19 = 0x0000ffff86d3cff0   x20 = 0x0000000000000000
    x21 = 0x0000000000000000   x22 = 0x89a3e21ead3e1d00
     fp = 0x0000fffeef71a6a0    sp = 0x0000fffef39c3f90
     pc = 0x0000ffff86e5ef98
    Found by: call frame info
 5  libstdc++.so.6!__cxa_throw + 0x60
     sp = 0x0000fffef39c3fa0    pc = 0x0000ffff86d27564
    Found by: stack scanning
 6  libcognition_vlm_gpu_platform_mlc_model_runtime.so + 0x5ba8c
    x19 = 0x0000fffeef71a6c0   x20 = 0x0000ffff1662444f
    x21 = 0x0000fffd381a26a8    fp = 0x0000fffef39c3fd0
     sp = 0x0000fffef39c3fd0    pc = 0x0000ffff16603a90
    Found by: call frame info

Thread 1
 0  libc.so.6 + 0x9e60c
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000ffff7dffd760    x3 = 0x0000ffff7dffd760
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000001db    x7 = 0x000000000003be34
     x8 = 0x0000000000000073    x9 = 0x000000000003be34
    x10 = 0x0000000000000017   x11 = 0x0014d84314c3feb4
    x12 = 0x0000000378546f34   x13 = 0x000000007fffffff
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86b94f78
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff7dffdf40   x21 = 0x0000ffff86c5e000
    x22 = 0x0000000000000000   x23 = 0x0000000000000000
    x24 = 0x0000000000000001   x25 = 0x0000ffff86fc99a0
    x26 = 0x0000ffff868ddc40   x27 = 0x0000ffff868dfa40
    x28 = 0x0000ffff868ddc40    fp = 0x0000ffff7dffd690
     lr = 0x0000ffff86b8f5f0    sp = 0x0000ffff7dffd690
     pc = 0x0000ffff86b8f60c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x9e5ec
     fp = 0x0000ffff7dffd720    lr = 0x0000ffff86b94f94
     sp = 0x0000ffff7dffd6a0    pc = 0x0000ffff86b8f5f0
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0xa3f90
     fp = 0x0000ffff7dffd730    lr = 0x0000ffff868c3950
     sp = 0x0000ffff7dffd730    pc = 0x0000ffff86b94f94
    Found by: previous frame's frame pointer
 3  libtimer.so.3!lios::timer::TimeRate::Sleep(long) + 0x94
     fp = 0x0000ffff7dffd770    lr = 0x0000ffff868c4660
     sp = 0x0000ffff7dffd740    pc = 0x0000ffff868c3950
    Found by: previous frame's frame pointer
 4  0xffff868ddc3c
    x19 = 0x0000ffff7dffd770   x20 = 0x0000ffff868c4650
     fp = 0x0000000000000040    sp = 0x0000ffff7dffd780
     pc = 0x0000ffff868ddc40
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7dffd7b0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0xf99c
     sp = 0x0000ffff7dffd7e0    pc = 0x0000ffff86fc99a0
    Found by: stack scanning
 7  libpthread.so.0 + 0x7620
     sp = 0x0000ffff7dffd810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 8  libc.so.6 + 0xd1668
     sp = 0x0000ffff7dffd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7dffd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 2
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b86448    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86448
     x6 = 0x0000000000000000    x7 = 0x0000fffef61c9748
     x8 = 0x0000000000000062    x9 = 0x00000000000044f0
    x10 = 0x0000ffff7e222840   x11 = 0x000affff8082d781
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf4e98   x21 = 0x0000ffff72b86430
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff72b86448
    x28 = 0x0000ffff72b86420    fp = 0x0000fffef61c96c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef61c96c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef61c9790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef61c96d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef61c97a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef61c97a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef61c9800    sp = 0x0000fffef61c97b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1fb60    sp = 0x0000fffef61c97d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef61c97e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef61c9810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef61c9830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef61c9890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 3
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b8659c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86598
     x6 = 0x0000000000000000    x7 = 0x0000fffef59c8748
     x8 = 0x0000000000000062    x9 = 0x000000000003a47f
    x10 = 0x0000ffff7e3d24b8   x11 = 0x000affff73a40181
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000086ac68   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf5198   x21 = 0x0000ffff72b86584
    x22 = 0x000000000000001f   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000ffff72b8659c
    x28 = 0x0000ffff72b86570    fp = 0x0000fffef59c86c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef59c86c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef59c8790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef59c86d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef59c87a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef59c87a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef59c8800    sp = 0x0000fffef59c87b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1fba0    sp = 0x0000fffef59c87d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef59c87e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef59c8810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef59c8830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef59c8890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 4
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85b15a28    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85b15a28
     x6 = 0x0000000000000000    x7 = 0x0000fffeffb6c748
     x8 = 0x0000000000000062    x9 = 0x000000000002f085
    x10 = 0x0000fffeebf784e8   x11 = 0x000affff85c34b81
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000055b2f0   x19 = 0x0000000000000000
    x20 = 0x0000ffff74423b98   x21 = 0x0000ffff85b15a10
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff85b15a28
    x28 = 0x0000ffff85b15a00    fp = 0x0000fffeffb6c6c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffeffb6c6c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffeffb6c790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffeffb6c6d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffeffb6c7a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffeffb6c7a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffeffb6c800    sp = 0x0000fffeffb6c7b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1f920    sp = 0x0000fffeffb6c7d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeffb6c7e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libstdc++.so.6!std::_V2::generic_category() + 0x6d
     sp = 0x0000fffeffb6c800    pc = 0x0000ffff86d54f01
    Found by: stack scanning
 7  libpthread.so.0 + 0x7620
    x19 = 0x0000fffeffb6d45c    fp = 0x0000fffeffb6c820
     sp = 0x0000fffeffb6c820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 5
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd418011cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd418011c8
     x6 = 0x0000000000000000    x7 = 0x0000fffe7c9a26d8
     x8 = 0x0000000000000062    x9 = 0x0000000000035aec
    x10 = 0x0000fffd3f1ad820   x11 = 0x000affff7568ec01
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000200e0   x19 = 0x0000000000000000
    x20 = 0x0000fffd41801140   x21 = 0x0000fffd418011b4
    x22 = 0x000000000000001f   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000fffd418011cc
    x28 = 0x0000fffd418011a0    fp = 0x0000fffe7c9a2650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffe7c9a2650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffe7c9a2720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffe7c9a2660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffe7c9a2730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffe7c9a2730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffe7c9a2800    sp = 0x0000fffe7c9a2740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffefbb643bc
    x19 = 0x0000fffefbb643be   x20 = 0x0000ffff86fe5000
     fp = 0x0000fffd35be6620    sp = 0x0000fffe7c9a2760
     pc = 0x0000fffefbb643c0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe7c9a2770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x73bc
     sp = 0x0000fffe7c9a27a0    pc = 0x0000ffff86fc13c0
    Found by: stack scanning
 7  libpthread.so.0 + 0x74fd
     sp = 0x0000fffe7c9a27d0    pc = 0x0000ffff86fc1501
    Found by: stack scanning
 8  libc.so.6 + 0xefa38
     sp = 0x0000fffe7c9a27e0    pc = 0x0000ffff86be0a3c
    Found by: stack scanning
 9  libsodium.so.23!sodium_bin2hex + 0x1bb
     sp = 0x0000fffe7c9a2800    pc = 0x0000ffff8600305f
    Found by: stack scanning
10  libpthread.so.0 + 0x7620
     sp = 0x0000fffe7c9a2810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
11  libc.so.6 + 0xd1668
     sp = 0x0000fffe7c9a2830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
12  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe7c9a2890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 6
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b86328    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86328
     x6 = 0x0000000000000000    x7 = 0x0000fffef51c7748
     x8 = 0x0000000000000062    x9 = 0x000000000002f085
    x10 = 0x0000fffeebf784e8   x11 = 0x000affff85c34b81
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000b04a8   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf5498   x21 = 0x0000ffff72b86310
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff72b86328
    x28 = 0x0000ffff72b86300    fp = 0x0000fffef51c76c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef51c76c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef51c7790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef51c76d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef51c77a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef51c77a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef51c7800    sp = 0x0000fffef51c77b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1fb90    sp = 0x0000fffef51c77d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef51c77e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef51c7810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef51c7830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef51c7890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 7
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b86568    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86568
     x6 = 0x0000000000000000    x7 = 0x0000fffef41c5748
     x8 = 0x0000000000000062    x9 = 0x000000000002f085
    x10 = 0x0000fffeebf784e8   x11 = 0x000affff85c34b81
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000282f80   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf5a98   x21 = 0x0000ffff72b86550
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff72b86568
    x28 = 0x0000ffff72b86540    fp = 0x0000fffef41c56c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef41c56c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef41c5790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef41c56d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef41c57a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef41c57a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef41c5800    sp = 0x0000fffef41c57b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85a23420    sp = 0x0000fffef41c57d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef41c57e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef41c5810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef41c5830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef41c5890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 8
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd418011f8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd418011f8
     x6 = 0x0000000000000000    x7 = 0x0000fffc6d0b76d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffc6d0b8750
    x12 = 0x0000000000000030   x13 = 0x000000000049b6e0
    x14 = 0x00000000004a2938   x15 = 0x000000000049b710
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000fffd41801170   x21 = 0x0000fffd418011e0
    x22 = 0x000000000000003c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000001e   x27 = 0x0000fffd418011f8
    x28 = 0x0000fffd418011d0    fp = 0x0000fffc6d0b7650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffc6d0b7650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffc6d0b7720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffc6d0b7660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffc6d0b7730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffc6d0b7730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffc6d0b7800    sp = 0x0000fffc6d0b7740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffefbb643bc
    x19 = 0x0000fffefbb643be   x20 = 0x0000ffff86fe5000
     fp = 0x0000fffd35be6640    sp = 0x0000fffc6d0b7760
     pc = 0x0000fffefbb643c0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffc6d0b7770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffc6d0b7810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffc6d0b7830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffc6d0b7890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 9
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd35dc0460    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffc6c8b67f0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffc6c8b6728
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x00028fd0d5a007f8
    x12 = 0x000000037756c1cc   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffefbb65a90   x19 = 0x0000000000000000
    x20 = 0x0000fffd35dc0460   x21 = 0x0000fffd35dc0070
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000002
    x24 = 0x0000fffc6c8b67f0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000001
    x28 = 0x0000fffd35dc0438    fp = 0x0000fffc6c8b6680
     lr = 0x0000ffff86fc8768    sp = 0x0000fffc6c8b6680
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffc6c8b6770    lr = 0x0000ffff8684a16c
     sp = 0x0000fffc6c8b6690    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xf8
     fp = 0x0000fffc6c8b6800    lr = 0x0000ffff86d54f9c
     sp = 0x0000fffc6c8b6780    pc = 0x0000ffff8684a16c
    Found by: previous frame's frame pointer
 3  0xfffefbb643bc
    x19 = 0x0000fffefbb643be   x20 = 0x0000ffff86fe5000
    x21 = 0x0000fffefbb643bf   x22 = 0x0000ffff86d54f80
    x23 = 0x0000fffc6c8b7740   x24 = 0x0000fffc6c8b7040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000fffc6c8b7040
    x27 = 0x0000000000000000   x28 = 0x0000ffff86fc99a0
     fp = 0x0000fffd4bb35950    sp = 0x0000fffc6c8b6810
     pc = 0x0000fffefbb643c0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000fffc6c8b6830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffc6c8b6890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 10
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b86118    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86118
     x6 = 0x0000000000000000    x7 = 0x0000fffefbb64748
     x8 = 0x0000000000000062    x9 = 0x0000000000004126
    x10 = 0x0000ffff7e2209f0   x11 = 0x000affff85c32701
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000021408   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf3098   x21 = 0x0000ffff72b86100
    x22 = 0x0000000000000020   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000010   x27 = 0x0000ffff72b86118
    x28 = 0x0000ffff72b860f0    fp = 0x0000fffefbb646c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefbb646c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefbb64790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefbb646d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefbb647a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffefbb647a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefbb64800    sp = 0x0000fffefbb647b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85a23310    sp = 0x0000fffefbb647d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefbb647e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefbb64810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefbb64830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefbb64890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 11
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff85a361c0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000ffff855fd7f0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000ffff855fd738
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000ac9876d9991eb
    x12 = 0x0000000377da5d34   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a361c0   x21 = 0x0000ffff85a36168
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000016
    x24 = 0x0000ffff855fd7f0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x000000000000000b
    x28 = 0x0000ffff85a36198    fp = 0x0000ffff855fd690
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff855fd690
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff855fd780    lr = 0x0000ffff86e7bc74
     sp = 0x0000ffff855fd6a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  liblog.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::log::details::PeriodicFlusher::PeriodicFlusher(std::function<void ()> const&)::{lambda()#1}> > >::_M_run() + 0xc0
     fp = 0x0000ffff855fd800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff855fd790    pc = 0x0000ffff86e7bc74
    Found by: previous frame's frame pointer
 3  0xffffd14e67cc
    x19 = 0x0000ffffd14e67ce   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e67cf   x22 = 0x0000ffff86d54f80
    x23 = 0x0000ffff855fe740   x24 = 0x0000ffff855fe040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000ffff855fe040
    x27 = 0x0000ffff86fc99a0   x28 = 0x0000ffff855fd7f0
     fp = 0x0000ffff85a25d40    sp = 0x0000ffff855fd810
     pc = 0x0000ffffd14e67d0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff855fd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff855fd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 12
 0  libjemalloc.so.2!operator new(unsigned long) [cache_bin.h : 369 + 0x0]
     x0 = 0x0000fffd27241530    x1 = 0x0000fffd79780750
     x2 = 0x0000fffd797807f8    x3 = 0x0000000000000068
     x4 = 0x0000000000299980    x5 = 0x0000fffeef06ff10
     x6 = 0x000000000000ffc8    x7 = 0x0000fffeef06ff18
     x8 = 0x0000000000000000    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000003
    x12 = 0x0000000000000000   x13 = 0x0000000000000003
    x14 = 0x0000000000000000   x15 = 0x0000006900000000
    x16 = 0x0000ffff8827a198   x17 = 0x0000ffff883dd090
    x18 = 0x0000000000000000   x19 = 0x0000fffd2723d8f0
    x20 = 0x000000000000000d   x21 = 0x0000fffd7977e308
    x22 = 0x0000000000000068   x23 = 0x0000fffd2723d930
    x24 = 0x0000fffd2723d948   x25 = 0x0000000000000000
    x26 = 0x0000fffd6f60ae00   x27 = 0x0000000000000000
    x28 = 0x0000fffd7977e600    fp = 0x0000fffd7977e280
     lr = 0x0000ffff8821e5d4    sp = 0x0000fffd7977e280
     pc = 0x0000ffff883dd0f8
    Found by: given as instruction pointer in context
 1  libscheduling.so.3!std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<lios::node::DagBuffer, std::allocator<lios::node::DagBuffer> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<lios::node::DagBuffer, std::allocator<lios::node::DagBuffer> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&) + 0x38
    x19 = 0x0000fffd2723d8f0   x20 = 0x000000000000000d
    x21 = 0x0000fffd7977e308   x22 = 0x0000000000000068
    x23 = 0x0000fffd2723d930   x24 = 0x0000fffd2723d948
    x25 = 0x0000000000000000   x26 = 0x0000fffd6f60ae00
    x27 = 0x0000000000000000   x28 = 0x0000fffd7977e600
     fp = 0x0000fffd7977e280    sp = 0x0000fffd7977e280
     pc = 0x0000ffff8821e5d4
    Found by: call frame info
 2  libscheduling.so.3!std::__detail::_Map_base<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<lios::node::DagBuffer, std::allocator<lios::node::DagBuffer> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<lios::node::DagBuffer, std::allocator<lios::node::DagBuffer> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true>, true>::operator[](std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) + 0x100
    x19 = 0x0000fffd2723d958   x20 = 0x0000fffd2723d8f0
    x21 = 0x0000000000000013   x22 = 0xbdc36392e6f2f85d
    x23 = 0x0000fffd2723d930   x24 = 0x0000fffd2723d948
    x25 = 0x0000000000000000   x26 = 0x0000fffd6f60ae00
    x27 = 0x0000000000000000   x28 = 0x0000fffd7977e600
     fp = 0x0000fffd7977e2b0    sp = 0x0000fffd7977e2b0
     pc = 0x0000ffff8821e7c4
    Found by: call frame info
 3  libscheduling.so.3!lios::scheduling::FifoBuffer::TryFetch(std::shared_ptr<lios::node::ItcHeader> const&, std::shared_ptr<lios::node::DagMessage>) + 0x110
    x19 = 0x0000ffff88279000   x20 = 0x0000fffd26c48180
    x21 = 0x0000fffd26c48160   x22 = 0x0000ffff03f2e9b8
    x23 = 0x0000fffd346b6140   x24 = 0x0000fffd2723d8f0
    x25 = 0x0000ffff03f2e918   x26 = 0x0000fffd6f60ae00
    x27 = 0x0000000000000000   x28 = 0x0000fffd7977e600
     fp = 0x0000fffd7977e310    sp = 0x0000fffd7977e310
     pc = 0x0000ffff8821aa8c
    Found by: call frame info
 4  libscheduling.so.3!lios::scheduling::DagGraph::TriggerTask(std::shared_ptr<lios::scheduling::CallbackHandle> const&, std::shared_ptr<void> const&, std::shared_ptr<lios::node::ItcHeader> const&) + 0x1b4
    x19 = 0x0000ffff85bc6100   x20 = 0x0000ffff85bc6108
    x21 = 0x0000fffd6f60ae00   x22 = 0x0000ffff88279000
    x23 = 0x0000fffd7977e7e0   x24 = 0x0000ffff8827a000
    x25 = 0x0000fffd2723d8e0   x26 = 0x0000fffd2723d8f0
    x27 = 0x0000000000000000   x28 = 0x0000fffd7977e600
     fp = 0x0000fffd7977e410    sp = 0x0000fffd7977e410
     pc = 0x0000ffff88222a78
    Found by: call frame info
 5  libscheduling.so.3!lios::scheduling::DagGraph::TopicDispatcher(std::shared_ptr<void> const&, std::shared_ptr<lios::node::ItcHeader> const&) + 0x1d8
    x19 = 0x0000ffff03f1f9f0   x20 = 0x0000ffff85beef10
    x21 = 0x0000ffff85b2b700   x22 = 0x0000fffd7977e7e0
    x23 = 0x0000fffd7977e840   x24 = 0x0000ffff03f1f9f0
    x25 = 0x0000ffff8827af58   x26 = 0x0000ffff88255d58
    x27 = 0x0000ffff88255f00   x28 = 0x0000fffd7977e8f8
     fp = 0x0000fffd7977e660    sp = 0x0000fffd7977e660
     pc = 0x0000ffff8822398c
    Found by: call frame info
 6  libscheduling.so.3!lios::scheduling::DagGraph::OnExternMessage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<void> const&, std::shared_ptr<lios::node::ItcHeader> const&) + 0xf4
    x19 = 0x0000ffff72bd6920   x20 = 0x0000ffff72bd6920
    x21 = 0x0000fffd7977e840   x22 = 0x0000fffd7977e7e0
    x23 = 0xbdc36392e6f2f85d   x24 = 0x0000fffd6f568150
    x25 = 0x0000000000000009   x26 = 0x0000fffd26c481b8
    x27 = 0x0000fffd26c481e8   x28 = 0x0000fffd7977e8f8
     fp = 0x0000fffd7977e6e0    sp = 0x0000fffd7977e6e0
     pc = 0x0000ffff88220c90
    Found by: call frame info
 7  libscheduling.so.3!lios::scheduling::DagScheduler::DagSchedulerImpl::OnExternMessage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<void> const&, std::shared_ptr<lios::node::ItcHeader> const&) + 0x48
    x19 = 0x0000ffff72c79c40   x20 = 0x0000fffd6f568150
    x21 = 0x0000fffd7977e840   x22 = 0x0000fffd7977e7e0
    x23 = 0x0000fffd6f568150   x24 = 0x0000fffd6f568170
    x25 = 0x0000fffd7977e840   x26 = 0x0000fffd26c481b8
    x27 = 0x0000fffd26c481e8   x28 = 0x0000fffd7977e8f8
     fp = 0x0000fffd7977e730    sp = 0x0000fffd7977e730
     pc = 0x0000ffff88220cec
    Found by: call frame info
 8  libnode.so.3!lios::node::OnExternMessage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<void> const&, lios::node::ItcHeader const&, lios::type::TypeTraits const&) + 0x218
    x19 = 0x0000ffff8834e7a8   x20 = 0x0000ffff881e4000
    x21 = 0x0000000000000000   x22 = 0x0000fffd26c48180
    x23 = 0x0000fffd6f568150   x24 = 0x0000fffd6f568170
    x25 = 0x0000fffd7977e840   x26 = 0x0000fffd26c481b8
    x27 = 0x0000fffd26c481e8   x28 = 0x0000fffd7977e8f8
     fp = 0x0000fffd7977e760    sp = 0x0000fffd7977e760
     pc = 0x0000ffff881b4eec
    Found by: call frame info
 9  libe2e_nodes.so + 0x4d54ec
    x19 = 0x0000fffd7977e860   x20 = 0x0000fffd346b6140
    x21 = 0x0000ffff86fc99a0   x22 = 0x0000fffd7977e8d8
    x23 = 0x0000fffd7977e898   x24 = 0x0000fffd7977e8f8
    x25 = 0x0000fffd7977e8b8   x26 = 0x0000fffd3d87e190
    x27 = 0x0000fffd6f60ae00   x28 = 0x0000fffd7977eb00
     fp = 0x0000fffd7977e7f0    sp = 0x0000fffd7977e7f0
     pc = 0x0000ffff38c384f0
    Found by: call frame info

Thread 13
 0  libc.so.6 + 0xcda94
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000000080000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85f30750
     x6 = 0x0000ffff85a00f48    x7 = 0x0000ffff85a00f48
     x8 = 0x0000000000000062    x9 = 0x0000000000005bcb
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000ffff86af4e48   x13 = 0x0000000000000000
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000ffff86bbea70   x17 = 0x0000ffff86e680f8
    x18 = 0x000000000092ca00   x19 = 0x0000ffff85a26180
    x20 = 0x0000ffff85a26170   x21 = 0x0000000080000000
    x22 = 0x0000000000000001   x23 = 0x0000ffff85a87600
    x24 = 0x0000ffff88335510   x25 = 0x0000ffff85a87608
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000000000000000    fp = 0x0000ffffd14e65e0
     lr = 0x0000ffff86d52ae0    sp = 0x0000ffffd14e65e0
     pc = 0x0000ffff86bbea94
    Found by: given as instruction pointer in context
 1  libstdc++.so.6!std::__atomic_futex_unsigned_base::_M_futex_wait_until(unsigned int*, unsigned int, bool, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<1l, 1000000000l> >) + 0xfc
     fp = 0x0000ffffd14e6650    lr = 0x0000ffff88316028
     sp = 0x0000ffffd14e65f0    pc = 0x0000ffff86d52ae0
    Found by: previous frame's frame pointer

Thread 14
 0  libc.so.6 + 0xd1794
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff81bfb770
     x2 = 0x0000000000000100    x3 = 0xffffffffffffffff
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x0000000000000000
     x8 = 0x0000000000000016    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000ffff86af4e48   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86131d30
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000007
    x20 = 0x0000000000000000   x21 = 0x0000ffff81bfb770
    x22 = 0x0000ffff81bfd740   x23 = 0x0000ffff81bfb770
    x24 = 0x0000ffff860ef4e0   x25 = 0x0000ffff860efd98
    x26 = 0x0000ffff81bfd040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff81bfd040    fp = 0x0000ffff81bfb6e0
     lr = 0x0000ffff86bc2774    sp = 0x0000ffff81bfb6e0
     pc = 0x0000ffff86bc2794
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xd1770
     fp = 0x0000ffff81bfb720    lr = 0x0000ffff8608cafc
     sp = 0x0000ffff81bfb6f0    pc = 0x0000ffff86bc2774
    Found by: previous frame's frame pointer
 2  libzmq.so.5!zmq::epoll_t::loop() + 0x88
     fp = 0x0000ffff81bfc770    lr = 0x0000ffff860bffbc
     sp = 0x0000ffff81bfb730    pc = 0x0000ffff8608cafc
    Found by: previous frame's frame pointer
 3  0xffff81bfc79c
    x19 = 0x0000ffffd14e5c8e   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5c8f   x22 = 0x0000ffff860bff70
    x23 = 0x0000ffff81bfd740   x24 = 0x0000000000000000
    x25 = 0x0000000000000000    fp = 0x0000ffff85a60318
     sp = 0x0000ffff81bfc780    pc = 0x0000ffff81bfc7a0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff81bfc830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libzmq.so.5!zmq::thread_t::applyThreadName() + 0xc
     sp = 0x0000ffff81bfc890    pc = 0x0000ffff860bff70
    Found by: stack scanning

Thread 15
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85a4276c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85a42768
     x6 = 0x0000000000000000    x7 = 0x0000ffff831fd5b8
     x8 = 0x0000000000000062    x9 = 0x6320464e4920726f
    x10 = 0x20736920746e756f   x11 = 0x0a21303438393532
    x12 = 0x525245204543545b   x13 = 0x6e6554203a5d524f
    x14 = 0x736e6554203a5d52   x15 = 0x645f616c7620726f
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a42710   x21 = 0x0000ffff85a42754
    x22 = 0x0000000000001edf   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000f6f   x27 = 0x0000ffff85a4276c
    x28 = 0x0000ffff85a42740    fp = 0x0000ffff831fd530
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff831fd530
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff831fd600    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff831fd540    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff831fd610    lr = 0x0000ffff86e7a884
     sp = 0x0000ffff831fd610    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  liblog.so.3!lios::log::details::AsyncThreadPool::WorkerLoop() + 0x14
     fp = 0x0000ffff831fd7e0    sp = 0x0000ffff831fd620
     pc = 0x0000ffff86e7aae0
    Found by: call frame info
 4  0xffffd14e659c
    x19 = 0x0000ffffd14e659e    fp = 0x0000ffff85a42710
     sp = 0x0000ffff831fd640    pc = 0x0000ffffd14e65a0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff831fd650    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 16
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff868dfb80    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000ffff7f1fd7f0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5146    x7 = 0x0000ffff7f1fd738
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000f1429257f7786
    x12 = 0x0000000376423104   x13 = 0x000000007fffffff
    x14 = 0x00000000040a0978   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000000064   x19 = 0x0000000000000000
    x20 = 0x0000ffff868dfb80   x21 = 0x0000ffff868dfb28
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000000a
    x24 = 0x0000ffff7f1fd7f0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000005
    x28 = 0x0000ffff868dfb58    fp = 0x0000ffff7f1fd690
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff7f1fd690
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff7f1fd780    lr = 0x0000ffff868c6c78
     sp = 0x0000ffff7f1fd6a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libtimer.so.3!lios::timer::TimerMonitorManager::PeriodicMonitor(long) + 0xec
     fp = 0x0000ffff7f1fd800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff7f1fd790    pc = 0x0000ffff868c6c78
    Found by: previous frame's frame pointer
 3  0xffffd14e5c4c
    x19 = 0x0000ffffd14e5c4e   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5c4f   x22 = 0x0000ffff86d54f80
    x23 = 0x0000ffff7f1fe740   x24 = 0x0000ffff7f1fe040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000ffff7f1fe040
    x27 = 0x4010040140100401   x28 = 0x0000000077359400
     fp = 0x0000ffff85ad1580    sp = 0x0000ffff7f1fd810
     pc = 0x0000ffffd14e5c50
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff7f1fd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7f1fd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 17
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b865f8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b865f8
     x6 = 0x0000000000000000    x7 = 0x0000fffef49c6748
     x8 = 0x0000000000000062    x9 = 0x000000000002f085
    x10 = 0x0000fffeebf784e8   x11 = 0x000affff85c34b81
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf5798   x21 = 0x0000ffff72b865e0
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff72b865f8
    x28 = 0x0000ffff72b865d0    fp = 0x0000fffef49c66c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef49c66c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef49c6790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef49c66d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef49c67a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef49c67a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef49c6800    sp = 0x0000fffef49c67b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1fc10    sp = 0x0000fffef49c67d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef49c67e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef49c6810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef49c6830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef49c6890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 18
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85b299c4    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85b299c0
     x6 = 0x0000000000000000    x7 = 0x0000ffff7bdfb6f8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000000800000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x000000000000002b   x15 = 0x0000000000000017
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff7bdfca98   x19 = 0x0000000000000000
    x20 = 0x0000ffff85b29968   x21 = 0x0000ffff85b299ac
    x22 = 0x0000000000000017   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000b   x27 = 0x0000ffff85b299c4
    x28 = 0x0000ffff85b29998    fp = 0x0000ffff7bdfb670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff7bdfb670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff7bdfb740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff7bdfb680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff7bdfb750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff7bdfb750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff7bdfb800    sp = 0x0000ffff7bdfb760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5d4c
    x19 = 0x0000ffffd14e5d4e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85a52dc0    sp = 0x0000ffff7bdfb780
     pc = 0x0000ffffd14e5d50
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7bdfb790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000ffff7bdfb7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffffd14e5cfd
    x19 = 0x0000fffd2552f000    fp = 0x0000ffff85b29968
     sp = 0x0000ffff7bdfb7d0    pc = 0x0000ffffd14e5d01
    Found by: call frame info
 8  libipc.so.3!std::_Function_handler<void (std::shared_ptr<lios::ipc::MessageEnvelope> const&), lios::ipc::IpcSubscriberImpl::IpcSubscriberImpl(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::type::TypeTraits const&, std::function<void (std::shared_ptr<std::vector<char, std::allocator<char> > > const&, lios::com::MessageInfo const*)>&&)::{lambda(std::shared_ptr<lios::ipc::MessageEnvelope> const&)#1}>::_M_invoke(std::_Any_data const&, std::shared_ptr<lios::ipc::MessageEnvelope> const&) + 0x49c
     sp = 0x0000ffff7bdfb7e0    pc = 0x0000ffff869c1ad8
    Found by: stack scanning

Thread 19
 0  libc.so.6 + 0x9e60c
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000ffff823fd7f0    x3 = 0x0000ffff823fd7f0
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000000000ad8    x7 = 0x0000000000000000
     x8 = 0x0000000000000073    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000ffff86af4e48   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff8834e488
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff823fdf40   x21 = 0x0000ffff86c5e000
    x22 = 0x0000000000000000   x23 = 0x0000000000000000
    x24 = 0x0000000000000001   x25 = 0x0000ffff823fe740
    x26 = 0x0000ffff823fe040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff823fe040    fp = 0x0000ffff823fd720
     lr = 0x0000ffff86b8f5f0    sp = 0x0000ffff823fd720
     pc = 0x0000ffff86b8f60c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x9e5ec
     fp = 0x0000ffff823fd7b0    lr = 0x0000ffff86b94f94
     sp = 0x0000ffff823fd730    pc = 0x0000ffff86b8f5f0
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0xa3f90
     fp = 0x0000ffff823fd7c0    lr = 0x0000ffff88315d2c
     sp = 0x0000ffff823fd7c0    pc = 0x0000ffff86b94f94
    Found by: previous frame's frame pointer
 3  libapp.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::app::AppContainer::Run()::{lambda()#1}> > >::_M_run() + 0x90
     fp = 0x0000ffff823fd800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff823fd7d0    pc = 0x0000ffff88315d2c
    Found by: previous frame's frame pointer
 4  0xffffd14e662c
    x19 = 0x0000ffffd14e662e   x20 = 0x0000ffff86fe5000
    x21 = 0x0000000000000030   x22 = 0x000000002bf14b36
     fp = 0x0000ffff85a8b380    sp = 0x0000ffff823fd810
     pc = 0x0000ffffd14e6630
    Found by: call frame info
 5  libc.so.6 + 0xd1668
     sp = 0x0000ffff823fd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff823fd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 20
 0  libc.so.6 + 0xcda90
     x0 = 0x0000ffff795fd028    x1 = 0x000000000000000b
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000ffff795fd020    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000000
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x00000000003d0f00
    x12 = 0x0000ffff868e60d0   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000ffff86a31b68   x17 = 0x0000ffff86bbea70
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff795fd010   x21 = 0x0000ffff795fd020
    x22 = 0x0000ffff795fd028   x23 = 0x0000ffff795fd010
    x24 = 0x0000ffff795fb740   x25 = 0x0000ffff795fd028
    x26 = 0x0000ffff795fa7bf   x27 = 0x0000ffff86fe6000
    x28 = 0x0000000000000000    fp = 0x0000ffff795fa6c0
     lr = 0x0000ffff869564e0    sp = 0x0000ffff795fa6c0
     pc = 0x0000ffff86bbea90
    Found by: given as instruction pointer in context
 1  libipc.so.3!a0_cnd_timedwait + 0x1f4
     fp = 0x0000ffff795fa740    lr = 0x0000ffff8697b398
     sp = 0x0000ffff795fa6d0    pc = 0x0000ffff869564e0
    Found by: previous frame's frame pointer
 2  0xffff85a48124
    x19 = 0x0000ffff8697a9b8   x20 = 0x0000ffff795fa810
    x21 = 0x8020000000000000   x22 = 0x8020080280200802
     fp = 0x0000000000000000    sp = 0x0000ffff795fa750
     pc = 0x0000ffff85a48128
    Found by: call frame info

Thread 21
 0  libc.so.6 + 0xd1794
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff78df8770
     x2 = 0x0000000000000100    x3 = 0xffffffffffffffff
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x0000000000000000
     x8 = 0x0000000000000016    x9 = 0x0000000000000000
    x10 = 0x0000ffff86fc14a0   x11 = 0x00000000003d0f00
    x12 = 0x0000000000001050   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bc28c8
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000011
    x20 = 0x0000000000000000   x21 = 0x0000ffff78df8770
    x22 = 0x0000ffff78dfa740   x23 = 0x0000ffff78df8770
    x24 = 0x0000ffff860ef4e0   x25 = 0x0000ffff860efd98
    x26 = 0x0000ffff78dfa040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff78dfa040    fp = 0x0000ffff78df86e0
     lr = 0x0000ffff86bc2774    sp = 0x0000ffff78df86e0
     pc = 0x0000ffff86bc2794
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xd1770
     fp = 0x0000ffff78df8720    lr = 0x0000ffff8608cafc
     sp = 0x0000ffff78df86f0    pc = 0x0000ffff86bc2774
    Found by: previous frame's frame pointer
 2  libzmq.so.5!zmq::epoll_t::loop() + 0x88
     fp = 0x0000ffff78df9770    lr = 0x0000ffff860bffbc
     sp = 0x0000ffff78df8730    pc = 0x0000ffff8608cafc
    Found by: previous frame's frame pointer
 3  0xffff78df979c
    x19 = 0x0000ffffd14e5dbe   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5dbf   x22 = 0x0000ffff860bff70
    x23 = 0x0000ffff78dfa740   x24 = 0x0000000000000000
    x25 = 0x0000000000000000    fp = 0x0000ffff85a5d9f8
     sp = 0x0000ffff78df9780    pc = 0x0000ffff78df97a0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff78df9830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libzmq.so.5!zmq::thread_t::applyThreadName() + 0xc
     sp = 0x0000ffff78df9890    pc = 0x0000ffff860bff70
    Found by: stack scanning

Thread 22
 0  libc.so.6 + 0xd1794
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff813fa770
     x2 = 0x0000000000000100    x3 = 0xffffffffffffffff
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x746e6576652d6370
     x8 = 0x0000000000000016    x9 = 0x0000000000000010
    x10 = 0x6f682c32726e686b   x11 = 0x7f7f7f7f7f7f7f7f
    x12 = 0x0101010101010101   x13 = 0x0000000000111d40
    x14 = 0xffffffffffffffff   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bc28c8
    x18 = 0x0000ffff85b8b5b8   x19 = 0x0000000000000009
    x20 = 0x0000000000000000   x21 = 0x0000ffff813fa770
    x22 = 0x0000ffff813fc740   x23 = 0x0000ffff813fa770
    x24 = 0x0000ffff860ef4e0   x25 = 0x0000ffff860efd98
    x26 = 0x0000ffff813fc040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff813fc040    fp = 0x0000ffff813fa6e0
     lr = 0x0000ffff86bc2774    sp = 0x0000ffff813fa6e0
     pc = 0x0000ffff86bc2794
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xd1770
     fp = 0x0000ffff813fa720    lr = 0x0000ffff8608cafc
     sp = 0x0000ffff813fa6f0    pc = 0x0000ffff86bc2774
    Found by: previous frame's frame pointer
 2  libzmq.so.5!zmq::epoll_t::loop() + 0x88
     fp = 0x0000ffff813fb770    lr = 0x0000ffff860bffbc
     sp = 0x0000ffff813fa730    pc = 0x0000ffff8608cafc
    Found by: previous frame's frame pointer
 3  0xffff813fb79c
    x19 = 0x0000ffffd14e5c5e   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5c5f   x22 = 0x0000ffff860bff70
    x23 = 0x0000ffff813fc740   x24 = 0x0000000000000000
    x25 = 0x0000000000000001    fp = 0x0000ffff85a603f8
     sp = 0x0000ffff813fb780    pc = 0x0000ffff813fb7a0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff813fb830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libzmq.so.5!zmq::thread_t::applyThreadName() + 0xc
     sp = 0x0000ffff813fb890    pc = 0x0000ffff860bff70
    Found by: stack scanning

Thread 23
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff85a70ce8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000ffff777fd7a0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000ffff777fd6f8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000d68ea756879bd
    x12 = 0x0000000378045364   x13 = 0x000000007fffffff
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000078da0   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a70ce8   x21 = 0x0000ffff85a70c90
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000016
    x24 = 0x0000ffff777fd7a0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x000000000000000b
    x28 = 0x0000ffff85a70cc0    fp = 0x0000ffff777fd650
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff777fd650
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff777fd740    lr = 0x0000ffff8683ebbc
     sp = 0x0000ffff777fd660    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::RepeatedAndTriggerableTaskRunner::RepeatedAndTriggerableTaskRunner(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::variant<std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000l> >, std::chrono::duration<long, std::ratio<1l, 1000l> >, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<60l, 1l> >, std::chrono::duration<long, std::ratio<3600l, 1l> > > const&, std::function<void ()>&&)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::RepeatedAndTriggerableTaskRunner::RepeatedAndTriggerableTaskRunner(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::variant<std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000l> >, std::chrono::duration<long, std::ratio<1l, 1000l> >, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<60l, 1l> >, std::chrono::duration<long, std::ratio<3600l, 1l> > > const&, std::function<void ()>&&)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x138
     fp = 0x0000ffff777fd800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff777fd750    pc = 0x0000ffff8683ebbc
    Found by: previous frame's frame pointer
 3  0xffffd14e5fbc
    x19 = 0x0000ffffd14e5fbe   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5fbf   x22 = 0x0000ffff86d54f80
    x23 = 0x0000ffff777fe740   x24 = 0x0000ffff777fe040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000ffff777fe040
    x27 = 0x0000000068ca5148   x28 = 0x000000001ae2b42b
     fp = 0x0000ffff85a65100    sp = 0x0000ffff777fd810
     pc = 0x0000ffffd14e5fc0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff777fd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff777fd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 24
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85a277c8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85a277c8
     x6 = 0x0000000000000000    x7 = 0x0000ffff7fdfc6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003ce05
    x10 = 0x0000ffff7e1e70e8   x11 = 0x0003ffff7d216781
    x12 = 0x0000000000000003   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000003   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000010fd30   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a27770   x21 = 0x0000ffff85a277b0
    x22 = 0x0000000000000018   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000c   x27 = 0x0000ffff85a277c8
    x28 = 0x0000ffff85a277a0    fp = 0x0000ffff7fdfc670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff7fdfc670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff7fdfc740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff7fdfc680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff7fdfc750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff7fdfc750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff7fdfc800    sp = 0x0000ffff7fdfc760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5c3c
    x19 = 0x0000ffffd14e5c3e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85ad1540    sp = 0x0000ffff7fdfc780
     pc = 0x0000ffffd14e5c40
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7fdfc790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libstdc++.so.6!std::chrono::_V2::system_clock::now() + 0x24
     sp = 0x0000ffff7fdfc7d0    pc = 0x0000ffff86d4b108
    Found by: stack scanning
 7  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     fp = 0x0000000000000000    sp = 0x0000ffff7fdfc800
     pc = 0x0000ffff868c4900
    Found by: call frame info

Thread 25
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85b294c4    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85b294c0
     x6 = 0x0000000000000000    x7 = 0x0000ffff7c5fc6f8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000000800000   x11 = 0x0000000100000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000001
    x14 = 0x000000000000002a   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff7c5fda98   x19 = 0x0000000000000000
    x20 = 0x0000ffff85b29468   x21 = 0x0000ffff85b294ac
    x22 = 0x0000000000000017   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000b   x27 = 0x0000ffff85b294c4
    x28 = 0x0000ffff85b29498    fp = 0x0000ffff7c5fc670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff7c5fc670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff7c5fc740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff7c5fc680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff7c5fc750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff7c5fc750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff7c5fc800    sp = 0x0000ffff7c5fc760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5e0c
    x19 = 0x0000ffffd14e5e0e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85a52d40    sp = 0x0000ffff7c5fc780
     pc = 0x0000ffffd14e5e10
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7c5fc790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000ffff7c5fc7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffffd14e5dfd
    x19 = 0x0000fffd2552f200    fp = 0x0000ffff85b29468
     sp = 0x0000ffff7c5fc7d0    pc = 0x0000ffffd14e5e01
    Found by: call frame info
 8  libipc.so.3!std::_Function_handler<void (std::shared_ptr<lios::ipc::MessageEnvelope> const&), lios::ipc::IpcSubscriberImpl::IpcSubscriberImpl(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::type::TypeTraits const&, std::function<void (std::shared_ptr<std::vector<char, std::allocator<char> > > const&, lios::com::MessageInfo const*)>&&)::{lambda(std::shared_ptr<lios::ipc::MessageEnvelope> const&)#1}>::_M_invoke(std::_Any_data const&, std::shared_ptr<lios::ipc::MessageEnvelope> const&) + 0x49c
     sp = 0x0000ffff7c5fc7e0    pc = 0x0000ffff869c1ad8
    Found by: stack scanning

Thread 26
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007a5c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007a58
     x6 = 0x0000000000000000    x7 = 0x0000ffff023716d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000ffff02372750
    x12 = 0x0000000000000030   x13 = 0x00000000000068d0
    x14 = 0x0000000000010000   x15 = 0x0000000000006900
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007530   x21 = 0x0000ffff75007a44
    x22 = 0x000000000000001f   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000ffff75007a5c
    x28 = 0x0000ffff75007a30    fp = 0x0000ffff02371650
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff02371650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff02371720    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff02371660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff02371730    lr = 0x0000ffff8684b6e8
     sp = 0x0000ffff02371730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff02371800    sp = 0x0000ffff02371740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bcbaa0    sp = 0x0000ffff02371760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff02371770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000ffff02371810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000ffff02371830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff02371890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 27
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff75007470    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000ffff01b707f0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5146    x7 = 0x0000ffff01b70728
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x001a2a4bc3c5ef97
    x12 = 0x0000000376f3932c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007470   x21 = 0x0000ffff75007080
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000012
    x24 = 0x0000ffff01b707f0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000009
    x28 = 0x0000ffff75007448    fp = 0x0000ffff01b70680
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff01b70680
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff01b70770    lr = 0x0000ffff8684a16c
     sp = 0x0000ffff01b70690    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xf8
     fp = 0x0000ffff01b70800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff01b70780    pc = 0x0000ffff8684a16c
    Found by: previous frame's frame pointer
 3  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5fef   x22 = 0x0000ffff86d54f80
    x23 = 0x0000ffff01b71740   x24 = 0x0000ffff01b71040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000ffff01b71040
    x27 = 0x0000000000000000   x28 = 0x0000ffff86fc99a0
     fp = 0x0000ffff85a23440    sp = 0x0000ffff01b70810
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff01b70830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff01b70890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 28
 0  libc.so.6 + 0xd1794
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff785f7770
     x2 = 0x0000000000000100    x3 = 0xffffffffffffffff
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x0000000000000016    x9 = 0x0000ffff85bb42c8
    x10 = 0x0000ffff85bb42c8   x11 = 0x0000ffff785f9760
    x12 = 0x0000ffff785f9a88   x13 = 0x0000ffff785f9758
    x14 = 0x0000ffff785f9a90   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bc28c8
    x18 = 0x0000000000000064   x19 = 0x0000000000000013
    x20 = 0x0000000000000000   x21 = 0x0000ffff785f7770
    x22 = 0x0000ffff785f9740   x23 = 0x0000ffff785f7770
    x24 = 0x0000ffff860ef4e0   x25 = 0x0000ffff860efd98
    x26 = 0x0000ffff785f9040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff785f9040    fp = 0x0000ffff785f76e0
     lr = 0x0000ffff86bc2774    sp = 0x0000ffff785f76e0
     pc = 0x0000ffff86bc2794
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xd1770
     fp = 0x0000ffff785f7720    lr = 0x0000ffff8608cafc
     sp = 0x0000ffff785f76f0    pc = 0x0000ffff86bc2774
    Found by: previous frame's frame pointer
 2  libzmq.so.5!zmq::epoll_t::loop() + 0x88
     fp = 0x0000ffff785f8770    lr = 0x0000ffff860bffbc
     sp = 0x0000ffff785f7730    pc = 0x0000ffff8608cafc
    Found by: previous frame's frame pointer
 3  0xffff785f879c
    x19 = 0x0000ffffd14e5d8e   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e5d8f   x22 = 0x0000ffff860bff70
    x23 = 0x0000ffff785f9740   x24 = 0x0000000000000000
    x25 = 0x0000000000000001    fp = 0x0000ffff85a5e7f8
     sp = 0x0000ffff785f8780    pc = 0x0000ffff785f87a0
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff785f8830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libzmq.so.5!zmq::thread_t::applyThreadName() + 0xc
     sp = 0x0000ffff785f8890    pc = 0x0000ffff860bff70
    Found by: stack scanning

Thread 29
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7505c1cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7505c1c8
     x6 = 0x0000000000000000    x7 = 0x0000fffeff36b6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b49c
    x10 = 0x0000000000000017   x11 = 0x001991b4d229ca48
    x12 = 0x000000036f76b084   x13 = 0x000000007fffffff
    x14 = 0x00000000840000e8   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff761fda90   x19 = 0x0000000000000000
    x20 = 0x0000ffff7505c170   x21 = 0x0000ffff7505c1b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff7505c1cc
    x28 = 0x0000ffff7505c1a0    fp = 0x0000fffeff36b670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffeff36b670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffeff36b740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffeff36b680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffeff36b750    lr = 0x0000ffff868507ac
     sp = 0x0000fffeff36b750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffeff36b800    sp = 0x0000fffeff36b760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff761fba2c
    x19 = 0x0000ffff761fba2e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7501be40    sp = 0x0000fffeff36b780
     pc = 0x0000ffff761fba30
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeff36b790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffeff36b7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff761fb9fd
    x19 = 0x0000ffff7506e0e0    fp = 0x0000ffff7505c170
     sp = 0x0000fffeff36b7d0    pc = 0x0000ffff761fba01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffeff36b7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffeff36b810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffeff36b830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeff36b890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 30
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007b7c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007b78
     x6 = 0x0000000000000000    x7 = 0x0000fffefcb666d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefcb67750
    x12 = 0x0000000000000030   x13 = 0x000000000005c170
    x14 = 0x0000000000065810   x15 = 0x000000000005c1a0
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000a2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007650   x21 = 0x0000ffff75007b64
    x22 = 0x0000000000000023   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000011   x27 = 0x0000ffff75007b7c
    x28 = 0x0000ffff75007b50    fp = 0x0000fffefcb66650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefcb66650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefcb66720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefcb66660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefcb66730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefcb66730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefcb66800    sp = 0x0000fffefcb66740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f32be0    sp = 0x0000fffefcb66760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefcb66770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefcb66810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefcb66830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefcb66890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 31
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b860bc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b860b8
     x6 = 0x0000000000000000    x7 = 0x0000fffefb363748
     x8 = 0x0000000000000062    x9 = 0x000000000000257d
    x10 = 0x0000ffff85612ca8   x11 = 0x000affff8261a901
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf3398   x21 = 0x0000ffff72b860a4
    x22 = 0x0000000000000027   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000013   x27 = 0x0000ffff72b860bc
    x28 = 0x0000ffff72b86090    fp = 0x0000fffefb3636c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefb3636c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefb363790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefb3636d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefb3637a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffefb3637a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefb363800    sp = 0x0000fffefb3637b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1fa20    sp = 0x0000fffefb3637d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefb3637e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefb363810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefb363830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefb363890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 32
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007c08    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007c08
     x6 = 0x0000000000000000    x7 = 0x0000fffefa3616d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefa362750
    x12 = 0x0000000000000030   x13 = 0x00000000003505b8
    x14 = 0x0000000000360328   x15 = 0x00000000003505e8
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000062c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff750076e0   x21 = 0x0000ffff75007bf0
    x22 = 0x0000000000000044   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000022   x27 = 0x0000ffff75007c08
    x28 = 0x0000ffff75007be0    fp = 0x0000fffefa361650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefa361650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefa361720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefa361660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefa361730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefa361730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefa361800    sp = 0x0000fffefa361740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf62c0    sp = 0x0000fffefa361760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefa361770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefa361810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefa361830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefa361890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 33
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff7d013000    x1 = 0x000000000000000a
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000000000002    x7 = 0x0000000000000003
     x8 = 0x0000000000000049    x9 = 0x0000000000000005
    x10 = 0x0000ffff3acb8750   x11 = 0x0000000000000050
    x12 = 0x00000000000001d8   x13 = 0x0000000000010000
    x14 = 0x0000000000000228   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000ffff3c719028   x19 = 0x0000ffff7d013000
    x20 = 0x0000ffff86c5e000   x21 = 0x0000ffff3acb8740
    x22 = 0x0000ffff70828050   x23 = 0x0000ffff3fe15520
    x24 = 0x0000000000000004   x25 = 0x000000000000000a
    x26 = 0x000000000000000a   x27 = 0x0000ffff3acb773f
    x28 = 0x0000ffff3acb7750    fp = 0x0000ffff3acb7630
     lr = 0x0000ffff86bb90c0    sp = 0x0000ffff3acb7630
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000ffff3acb7820    lr = 0x0000ffff6efac4fc
     sp = 0x0000ffff3acb7640    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libcuda.so.1 + 0x2df4f8
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000ffff3acb7830    pc = 0x0000ffff6efac4fc
    Found by: previous frame's frame pointer

Thread 34
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007c98    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007c98
     x6 = 0x0000000000000000    x7 = 0x0000fffef8a966d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffef8a97750
    x12 = 0x0000000000000030   x13 = 0x0000000000020ed0
    x14 = 0x00000000000302a0   x15 = 0x0000000000020f00
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007770   x21 = 0x0000ffff75007c80
    x22 = 0x0000000000000148   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x00000000000000a4   x27 = 0x0000ffff75007c98
    x28 = 0x0000ffff75007c70    fp = 0x0000fffef8a96650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef8a96650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef8a96720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef8a96660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef8a96730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffef8a96730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef8a96800    sp = 0x0000fffef8a96740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf6860    sp = 0x0000fffef8a96760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef8a96770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef8a96810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef8a96830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef8a96890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 35
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff72bd4460    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff6c9fc4e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001da    x7 = 0x0000ffff6c9fc408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x001cb66bd499dbf7
    x12 = 0x0000000376f57b74   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff6c9fda90   x19 = 0x0000000000000000
    x20 = 0x0000ffff72bd4460   x21 = 0x0000ffff72bd4408
    x22 = 0x0000ffff86fe5000   x23 = 0x00000000000000ac
    x24 = 0x0000ffff6c9fc4e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000056
    x28 = 0x0000ffff72bd4438    fp = 0x0000ffff6c9fc360
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff6c9fc360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff6c9fc460    lr = 0x0000ffff874b37f0
     sp = 0x0000ffff6c9fc370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000ffff6c9fc580    lr = 0x0000ffff873a7464
     sp = 0x0000ffff6c9fc470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000ffff6c9fc650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff85be5d00   x22 = 0x0000ffff72bf0400
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000ffff72bf03c0   x26 = 0x0000ffff874b0204
    x27 = 0x0000ffff6c9fd740   x28 = 0x0000ffff6c9fd040
     fp = 0x0000ffff6c9fc590    sp = 0x0000ffff6c9fc6a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 36
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85a27acc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85a27ac8
     x6 = 0x0000000000000000    x7 = 0x0000fffef79cc6f8
     x8 = 0x0000000000000062    x9 = 0x0000000000024852
    x10 = 0x0000fffd3f124350   x11 = 0x0003ffff7522b801
    x12 = 0x0000000000000003   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000003   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010200   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a27a70   x21 = 0x0000ffff85a27ab4
    x22 = 0x0000000000000027   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000013   x27 = 0x0000ffff85a27acc
    x28 = 0x0000ffff85a27aa0    fp = 0x0000fffef79cc670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef79cc670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef79cc740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef79cc680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef79cc750    lr = 0x0000ffff868507ac
     sp = 0x0000fffef79cc750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef79cc800    sp = 0x0000fffef79cc760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5eac
    x19 = 0x0000ffffd14e5eae   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bfd880    sp = 0x0000fffef79cc780
     pc = 0x0000ffffd14e5eb0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef79cc790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffef79cc7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffffd14e5dfd
    x19 = 0x0000ffff85bc71a0    fp = 0x0000ffff85a27a70
     sp = 0x0000fffef79cc7d0    pc = 0x0000ffffd14e5e01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffef79cc7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libstdc++.so.6!std::chrono::_V2::system_clock::now() + 0x24
     sp = 0x0000fffef79cc7f0    pc = 0x0000ffff86d4b108
    Found by: stack scanning
10  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     fp = 0x0000000000000000    sp = 0x0000fffef79cc820
     pc = 0x0000ffff868c4900
    Found by: call frame info

Thread 37
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff03f108b8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff03f108b8
     x6 = 0x0000000000000000    x7 = 0x0000ffff03b746f8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000002   x11 = 0x0000ffff03b75750
    x12 = 0x0000000000000020   x13 = 0x0000000000001e40
    x14 = 0x0000000000010000   x15 = 0x0000000000001e60
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000001c88   x19 = 0x0000000000000000
    x20 = 0x0000ffff03f10860   x21 = 0x0000ffff03f108a0
    x22 = 0x0000000000000038   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000001c   x27 = 0x0000ffff03f108b8
    x28 = 0x0000ffff03f10890    fp = 0x0000ffff03b74670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff03b74670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff03b74740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff03b74680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff03b74750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff03b74750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff03b74800    sp = 0x0000ffff03b74760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff735fcc2c
    x19 = 0x0000ffff735fcc2e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff72c79c00    sp = 0x0000ffff03b74780
     pc = 0x0000ffff735fcc30
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff03b74790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000ffff03b747b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff735fcbfd
    x19 = 0x0000ffff7e854c60    fp = 0x0000ffff03f10860
     sp = 0x0000ffff03b747d0    pc = 0x0000ffff735fcc01
    Found by: call frame info
 8  libnode.so.3!cereal::Exception::~Exception() + 0x34
     sp = 0x0000ffff03b747e0    pc = 0x0000ffff8819f238
    Found by: stack scanning
 9  libnode.so.3!cereal::Exception::~Exception() + 0x34
     sp = 0x0000ffff03b74800    pc = 0x0000ffff8819f238
    Found by: stack scanning
10  libpthread.so.0 + 0x7620
     sp = 0x0000ffff03b74810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
11  libc.so.6 + 0xd1668
     sp = 0x0000ffff03b74830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
12  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff03b74890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 38
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007ccc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007cc8
     x6 = 0x0000000000000000    x7 = 0x0000fffef71cb6d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffef71cc750
    x12 = 0x0000000000000030   x13 = 0x0000000000005850
    x14 = 0x0000000000010000   x15 = 0x0000000000005880
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000032c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff750077a0   x21 = 0x0000ffff75007cb4
    x22 = 0x0000000000000007   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000003   x27 = 0x0000ffff75007ccc
    x28 = 0x0000ffff75007ca0    fp = 0x0000fffef71cb650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef71cb650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef71cb720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef71cb660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef71cb730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffef71cb730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef71cb800    sp = 0x0000fffef71cb740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf6b40    sp = 0x0000fffef71cb760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef71cb770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef71cb810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef71cb830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef71cb890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 39
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007ae8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007ae8
     x6 = 0x0000000000000000    x7 = 0x0000fffefeb6a6d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefeb6b750
    x12 = 0x0000000000000030   x13 = 0x0000000000000640
    x14 = 0x0000000000010000   x15 = 0x0000000000000670
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000072c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff750075c0   x21 = 0x0000ffff75007ad0
    x22 = 0x0000000000000004   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000ffff75007ae8
    x28 = 0x0000ffff75007ac0    fp = 0x0000fffefeb6a650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefeb6a650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefeb6a720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefeb6a660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefeb6a730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefeb6a730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefeb6a800    sp = 0x0000fffefeb6a740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff72baf1c0    sp = 0x0000fffefeb6a760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefeb6a770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefeb6a810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefeb6a830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefeb6a890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 40
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff03f390cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff03f390c8
     x6 = 0x0000000000000000    x7 = 0x0000fffec0bf36f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b76e
    x10 = 0x0000000000000017   x11 = 0x0007b011bcfbc5cf
    x12 = 0x00000003721242f4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffecb1f7a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff03f39070   x21 = 0x0000ffff03f390b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff03f390cc
    x28 = 0x0000ffff03f390a0    fp = 0x0000fffec0bf3670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffec0bf3670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffec0bf3740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffec0bf3680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffec0bf3750    lr = 0x0000ffff868507ac
     sp = 0x0000fffec0bf3750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffec0bf3800    sp = 0x0000fffec0bf3760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffecb1f5fbc
    x19 = 0x0000fffecb1f5fbe   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff72baeec0    sp = 0x0000fffec0bf3780
     pc = 0x0000fffecb1f5fc0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec0bf3790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffec0bf37b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffecb1f5efd
    x19 = 0x0000ffff72d952c0    fp = 0x0000ffff03f39070
     sp = 0x0000fffec0bf37d0    pc = 0x0000fffecb1f5f01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffec0bf37e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffec0bf3810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffec0bf3830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec0bf3890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 41
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007b18    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007b18
     x6 = 0x0000000000000000    x7 = 0x0000fffefe3696d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefe36a750
    x12 = 0x0000000000000030   x13 = 0x000000000001c360
    x14 = 0x00000000000202a0   x15 = 0x000000000001c390
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000e2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff750075f0   x21 = 0x0000ffff75007b00
    x22 = 0x0000000000000148   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x00000000000000a4   x27 = 0x0000ffff75007b18
    x28 = 0x0000ffff75007af0    fp = 0x0000fffefe369650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefe369650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefe369720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefe369660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefe369730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefe369730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefe369800    sp = 0x0000fffefe369740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bcbe00    sp = 0x0000fffefe369760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefe369770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefe369810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefe369830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefe369890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 42
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff3b2d2100    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffebf8f1678
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffebf8f15e8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x001321c2865fc692
    x12 = 0x00000003785fe0e4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000006900000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000000000   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2d2100   x21 = 0x0000ffff3b2d1038
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000218
    x24 = 0x0000fffebf8f1678   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x000000000000010c
    x28 = 0x0000ffff3b2d20d8    fp = 0x0000fffebf8f1540
     lr = 0x0000ffff86fc8768    sp = 0x0000fffebf8f1540
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffebf8f1780    lr = 0x0000ffff828e7808
     sp = 0x0000fffebf8f1550    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnvscistream.so.1 + 0x2e804
     fp = 0x0000fffebf8f1790    lr = 0x0000ffff84d095a8
     sp = 0x0000fffebf8f1790    pc = 0x0000ffff828e7808
    Found by: previous frame's frame pointer
 3  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x24
     fp = 0x0000fffebf8f17d0    lr = 0x0000ffff84d09950
     sp = 0x0000fffebf8f17a0    pc = 0x0000ffff84d095a8
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamProducer::HandleSetupComplete() + 0xec
    x19 = 0x0000ffff75067618   x20 = 0x4010040140100401
     fp = 0x0000ffff75019fa0    sp = 0x0000fffebf8f17e0
     pc = 0x0000ffff84d0d000
    Found by: call frame info
 5  0xfffecc1f853c
    x19 = 0x0000fffecc1f853e   x20 = 0x0000000000000000
    x21 = 0x0000fffebf8f1820   x22 = 0x0000ffff86fc1624
    x23 = 0x0000fffebf8f245c   x24 = 0x0000fffecc1f8540
    x25 = 0x0000000000000000   x26 = 0x0000ffff86bc266c
    x27 = 0x0000fffebf8f2040   x28 = 0x0000fffecc1f8540
     fp = 0x0000ffff75019fa0    sp = 0x0000fffebf8f1850
     pc = 0x0000fffecc1f8540
    Found by: call frame info
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffebf8f1890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 43
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007b48    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007b48
     x6 = 0x0000000000000000    x7 = 0x0000fffefd3676d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefd368750
    x12 = 0x0000000000000030   x13 = 0x000000000008a550
    x14 = 0x0000000000092ec0   x15 = 0x000000000008a580
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000002c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007620   x21 = 0x0000ffff75007b30
    x22 = 0x0000000000000148   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x00000000000000a4   x27 = 0x0000ffff75007b48
    x28 = 0x0000ffff75007b20    fp = 0x0000fffefd367650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefd367650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefd367720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefd367660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefd367730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefd367730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefd367800    sp = 0x0000fffefd367740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff72baf600    sp = 0x0000fffefd367760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefd367770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefd367810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefd367830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefd367890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 44
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff88162468    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff88162468
     x6 = 0x0000000000000000    x7 = 0x0000fffe9b42a6f8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000008000000   x11 = 0x0000006900000000
    x12 = 0x0000000000000001   x13 = 0x0000000000000029
    x14 = 0x00000000000011dc   x15 = 0x000000000000086a
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffe9b42ba98   x19 = 0x0000000000000000
    x20 = 0x0000ffff88162410   x21 = 0x0000ffff88162450
    x22 = 0x000000000000002c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000016   x27 = 0x0000ffff88162468
    x28 = 0x0000ffff88162440    fp = 0x0000fffe9b42a670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffe9b42a670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffe9b42a740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffe9b42a680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffe9b42a750    lr = 0x0000ffff868507ac
     sp = 0x0000fffe9b42a750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffe9b42a800    sp = 0x0000fffe9b42a760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff6c9fa2cc
    x19 = 0x0000ffff6c9fa2ce   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7783a540    sp = 0x0000fffe9b42a780
     pc = 0x0000ffff6c9fa2d0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe9b42a790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffe9b42a7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff6c9fa1fd
    x19 = 0x0000fffd247e0d20    fp = 0x0000ffff88162410
     sp = 0x0000fffe9b42a7d0    pc = 0x0000ffff6c9fa201
    Found by: call frame info
 8  libcom.so.3!cereal::detail::PolymorphicCasters::~PolymorphicCasters() + 0x12c
     sp = 0x0000fffe9b42a7e0    pc = 0x0000ffff88149d90
    Found by: stack scanning
 9  libcom.so.3!cereal::detail::PolymorphicCasters::~PolymorphicCasters() + 0x12c
     sp = 0x0000fffe9b42a800    pc = 0x0000ffff88149d90
    Found by: stack scanning
10  libpthread.so.0 + 0x7620
     sp = 0x0000fffe9b42a810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
11  libc.so.6 + 0xd1668
     sp = 0x0000fffe9b42a830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
12  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe9b42a890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 45
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007bd8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007bd8
     x6 = 0x0000000000000000    x7 = 0x0000fffefab626d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefab63750
    x12 = 0x0000000000000030   x13 = 0x000000000007b998
    x14 = 0x0000000000085938   x15 = 0x000000000007b9c8
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000032c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff750076b0   x21 = 0x0000ffff75007bc0
    x22 = 0x0000000000000024   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000012   x27 = 0x0000ffff75007bd8
    x28 = 0x0000ffff75007bb0    fp = 0x0000fffefab62650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefab62650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefab62720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefab62660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefab62730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefab62730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefab62800    sp = 0x0000fffefab62740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf60e0    sp = 0x0000fffefab62760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefab62770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefab62810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefab62830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefab62890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 46
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007c38    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007c38
     x6 = 0x0000000000000000    x7 = 0x0000fffef9a986d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffef9a99750
    x12 = 0x0000000000000030   x13 = 0x0000000000000d40
    x14 = 0x0000000000010000   x15 = 0x0000000000000d70
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000e2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007710   x21 = 0x0000ffff75007c20
    x22 = 0x0000000000000004   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000ffff75007c38
    x28 = 0x0000ffff75007c10    fp = 0x0000fffef9a98650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef9a98650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef9a98720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef9a98660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef9a98730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffef9a98730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef9a98800    sp = 0x0000fffef9a98740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf64a0    sp = 0x0000fffef9a98760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef9a98770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef9a98810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef9a98830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef9a98890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 47
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffec0170d08    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0xffffffffffffffff    x7 = 0x0000fffec017d080
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000000000072f98   x19 = 0x0000fffec0170d08
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffec00f3740
    x22 = 0x0000fffec00f27d0   x23 = 0x0000fffec00f2710
    x24 = 0x0000fffec00f26e8   x25 = 0x0000fffec00f27d8
    x26 = 0x0000000000000003   x27 = 0x0000000000000000
    x28 = 0x0000fffec00f3040    fp = 0x0000fffec00f25d0
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffec00f25d0
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffec00f2800    lr = 0x0000ffff80b9e660
     sp = 0x0000fffec00f25e0    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x165c
     fp = 0x0000fffec00f2820    lr = 0x0000ffff86fc1624
     sp = 0x0000fffec00f2810    pc = 0x0000ffff80b9e660
    Found by: previous frame's frame pointer
 3  libpthread.so.0 + 0x7620
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffec00f2830    pc = 0x0000ffff86fc1624
    Found by: previous frame's frame pointer

Thread 48
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007c6c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007c68
     x6 = 0x0000000000000000    x7 = 0x0000fffef92976d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffef9298750
    x12 = 0x0000000000000030   x13 = 0x00000000001b3110
    x14 = 0x00000000001b8340   x15 = 0x00000000001b3140
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007740   x21 = 0x0000ffff75007c54
    x22 = 0x00000000000000a7   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000053   x27 = 0x0000ffff75007c6c
    x28 = 0x0000ffff75007c40    fp = 0x0000fffef9297650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef9297650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef9297720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef9297660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef9297730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffef9297730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef9297800    sp = 0x0000fffef9297740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf6620    sp = 0x0000fffef9297760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef9297770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef9297810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef9297830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef9297890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 49
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7505c2cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7505c2c8
     x6 = 0x0000000000000000    x7 = 0x0000fffede9f96f8
     x8 = 0x0000000000000062    x9 = 0x00000000000336b7
    x10 = 0x0000ffff7e19b678   x11 = 0x0002ffff73817781
    x12 = 0x0000000000000002   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000032578   x19 = 0x0000000000000000
    x20 = 0x0000ffff7505c270   x21 = 0x0000ffff7505c2b4
    x22 = 0x0000000000000013   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000009   x27 = 0x0000ffff7505c2cc
    x28 = 0x0000ffff7505c2a0    fp = 0x0000fffede9f9670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffede9f9670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffede9f9740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffede9f9680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffede9f9750    lr = 0x0000ffff868507ac
     sp = 0x0000fffede9f9750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffede9f9800    sp = 0x0000fffede9f9760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff761fb9ec
    x19 = 0x0000ffff761fb9ee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff75058600    sp = 0x0000fffede9f9780
     pc = 0x0000ffff761fb9f0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffede9f9790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffede9f97b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff761fb8fd
    x19 = 0x0000ffff7506e1c0    fp = 0x0000ffff7505c270
     sp = 0x0000fffede9f97d0    pc = 0x0000ffff761fb901
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffede9f97e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libstdc++.so.6!std::chrono::_V2::system_clock::now() + 0x24
     sp = 0x0000fffede9f97f0    pc = 0x0000ffff86d4b108
    Found by: stack scanning
10  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     fp = 0x0000000000000000    sp = 0x0000fffede9f9820
     pc = 0x0000ffff868c4900
    Found by: call frame info

Thread 50
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff76a98160    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffde277d4e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001db    x7 = 0x0000fffde277d408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0011a0498b41f188
    x12 = 0x000000037820ef9c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffde277ea90   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a98160   x21 = 0x0000ffff76a98108
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000228
    x24 = 0x0000fffde277d4e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000114
    x28 = 0x0000ffff76a98138    fp = 0x0000fffde277d360
     lr = 0x0000ffff86fc8768    sp = 0x0000fffde277d360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffde277d460    lr = 0x0000ffff874b37f0
     sp = 0x0000fffde277d370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffde277d580    lr = 0x0000ffff873a7464
     sp = 0x0000fffde277d470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000fffde277d650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff76a0d8a0   x22 = 0x0000ffff76ab94c0
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000ffff76ab9480   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffde277e740   x28 = 0x0000fffde277e040
     fp = 0x0000fffde277d590    sp = 0x0000fffde277d6a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 51
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff043d8b74    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff043d8b70
     x6 = 0x0000000000000000    x7 = 0x0000fffee31fa748
     x8 = 0x0000000000000062    x9 = 0x0000ffff076ba708
    x10 = 0x0000ffff076ba6f8   x11 = 0x0000ffff076ba748
    x12 = 0x0000ffff076ba738   x13 = 0x0000fffee31fb758
    x14 = 0x0000fffee31fba90   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000000064   x19 = 0x0000000000000000
    x20 = 0x0000ffff043d8b10   x21 = 0x0000ffff043d8b5c
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff043d8b74
    x28 = 0x0000ffff043d8b48    fp = 0x0000fffee31fa6c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffee31fa6c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffee31fa820    lr = 0x0000ffff337b2118
     sp = 0x0000fffee31fa6d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libopencv_core.so.4.3!cv::WorkerThread::thread_body() + 0x114
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffee31fa830    pc = 0x0000ffff337b2118
    Found by: previous frame's frame pointer
 3  0xfffef19bef0b
    x19 = 0x0000fffee31fb040   x20 = 0x0000fffef19bef10
    x21 = 0x0000fffef19bef0e   x22 = 0x0000000000000000
    x23 = 0x0000000000000000   x24 = 0x0000fffee31fb040
    x25 = 0x0000fffee31fb45c   x26 = 0x0000fffef19bef10
    x27 = 0x0000fffef19bef0e   x28 = 0x0000ffff86fe5000
     fp = 0x0000000000000000    sp = 0x0000fffee31fa8b0
     pc = 0x0000fffef19bef0f
    Found by: call frame info

Thread 52
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff76ae5be0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffef19c04e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001db    x7 = 0x0000fffef19c0408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x00136a0d108c294c
    x12 = 0x00000003783d8bd4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffef19c1a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff76ae5be0   x21 = 0x0000ffff76ae5b88
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000062
    x24 = 0x0000fffef19c04e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000031
    x28 = 0x0000ffff76ae5bb8    fp = 0x0000fffef19c0360
     lr = 0x0000ffff86fc8768    sp = 0x0000fffef19c0360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffef19c0460    lr = 0x0000ffff874b37f0
     sp = 0x0000fffef19c0370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffef19c0580    lr = 0x0000ffff873a7464
     sp = 0x0000fffef19c0470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000fffef19c0650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff76a0f8b0   x22 = 0x0000fffde29c3380
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000fffde29c3340   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffef19c1740   x28 = 0x0000fffef19c1040
     fp = 0x0000fffef19c0590    sp = 0x0000fffef19c06a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 53
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff03fdf100    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffeb9ec3678
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffeb9ec35e8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0013404706b96bca
    x12 = 0x000000037861c92c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000084860   x19 = 0x0000000000000000
    x20 = 0x0000ffff03fdf100   x21 = 0x0000ffff03fde038
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000224
    x24 = 0x0000fffeb9ec3678   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000112
    x28 = 0x0000ffff03fdf0d8    fp = 0x0000fffeb9ec3540
     lr = 0x0000ffff86fc8768    sp = 0x0000fffeb9ec3540
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffeb9ec3780    lr = 0x0000ffff828e7808
     sp = 0x0000fffeb9ec3550    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnvscistream.so.1 + 0x2e804
     fp = 0x0000fffeb9ec3790    lr = 0x0000ffff84d095a8
     sp = 0x0000fffeb9ec3790    pc = 0x0000ffff828e7808
    Found by: previous frame's frame pointer
 3  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x24
     fp = 0x0000fffeb9ec37d0    lr = 0x0000ffff84d09950
     sp = 0x0000fffeb9ec37a0    pc = 0x0000ffff84d095a8
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamProducer::HandleSetupComplete() + 0xec
    x19 = 0x0000ffff7661b618   x20 = 0x0000000000000009
     fp = 0x0000ffff766210c0    sp = 0x0000fffeb9ec37e0
     pc = 0x0000ffff84d0d000
    Found by: call frame info
 5  0xfffecb1f653c
    x19 = 0x0000fffecb1f653e   x20 = 0x0000000000000000
    x21 = 0x0000fffeb9ec3820   x22 = 0x0000ffff86fc1624
    x23 = 0x0000fffeb9ec445c   x24 = 0x0000fffecb1f6540
    x25 = 0x0000000000000000   x26 = 0x0000ffff86bc266c
    x27 = 0x0000fffeb9ec4040   x28 = 0x0000fffecb1f6540
     fp = 0x0000ffff766210c0    sp = 0x0000fffeb9ec3850
     pc = 0x0000fffecb1f6540
    Found by: call frame info
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeb9ec3890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 54
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7505c3c8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7505c3c8
     x6 = 0x0000000000000000    x7 = 0x0000fffecb1f66f8
     x8 = 0x0000000000000062    x9 = 0x0000000000036a37
    x10 = 0x0000ffff7e1b5278   x11 = 0x0002ffff76c16e81
    x12 = 0x0000000000000002   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000215e0   x19 = 0x0000000000000000
    x20 = 0x0000ffff7505c370   x21 = 0x0000ffff7505c3b0
    x22 = 0x0000000000000010   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000ffff7505c3c8
    x28 = 0x0000ffff7505c3a0    fp = 0x0000fffecb1f6670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffecb1f6670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffecb1f6740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffecb1f6680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffecb1f6750    lr = 0x0000ffff868507ac
     sp = 0x0000fffecb1f6750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffecb1f6800    sp = 0x0000fffecb1f6760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff761fb9ec
    x19 = 0x0000ffff761fb9ee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff75058740    sp = 0x0000fffecb1f6780
     pc = 0x0000ffff761fb9f0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffecb1f6790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffecb1f67b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff761fb8fd
    x19 = 0x0000ffff7506e2a0    fp = 0x0000ffff7505c370
     sp = 0x0000fffecb1f67d0    pc = 0x0000ffff761fb901
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffecb1f67e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libstdc++.so.6!std::chrono::_V2::system_clock::now() + 0x24
     sp = 0x0000fffecb1f67f0    pc = 0x0000ffff86d4b108
    Found by: stack scanning
10  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     fp = 0x0000000000000000    sp = 0x0000fffecb1f6820
     pc = 0x0000ffff868c4900
    Found by: call frame info

Thread 55
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000b6    x1 = 0x0000fffdaa92eae0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffde06885a0    x5 = 0x0000fffd786b53c4
     x6 = 0x0000000000000000    x7 = 0x0000000000000964
     x8 = 0x00000000000000cf    x9 = 0x000000000003be44
    x10 = 0x0000000000000017   x11 = 0x0015cc6717931102
    x12 = 0x000000037863b174   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000000010000   x19 = 0x00000000000000b6
    x20 = 0x0000fffd786b53c4   x21 = 0x0000fffde06885a0
    x22 = 0x0000000000010000   x23 = 0x0000fffdaa92eae0
    x24 = 0x0000fffd786b6740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffdcb766bc0
    x28 = 0x0000fffd786b6040    fp = 0x0000fffd786b5350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd786b5350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd786b53a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd786b5360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd786b5400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd786b53b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff04122720   x20 = 0x0000fffd786b5608
     fp = 0x0000fffd786b5520    sp = 0x0000fffd786b5430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffde29a59c0   x20 = 0x0000ffff76b10400
    x21 = 0x0000fffdaa89c280   x22 = 0x0000ffff04122600
    x23 = 0x0000ffff04125900   x24 = 0x0000fffd786b5608
    x25 = 0x0000fffd786b5618   x26 = 0x0000fffdcc583b50
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd786b6040
     fp = 0x0000fffd786b55a0    sp = 0x0000fffd786b55a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76a0f7c0   x20 = 0x0000fffdaa89c2c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffde29a59c0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd786b6740   x26 = 0x0000fffd786b6040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd786b6040
     fp = 0x0000fffd786b5650    sp = 0x0000fffd786b5640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd786b6458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a25fe   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a25ff   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd786b6740   x26 = 0x0000fffd786b6040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd786b6040
     fp = 0x0000fffd786b5820    sp = 0x0000fffd786b5820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 56
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff3b2c03cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff3b2c03c8
     x6 = 0x0000000000000000    x7 = 0x0000fffef11bf6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b7a6
    x10 = 0x0000000000000017   x11 = 0x000ae80b3e428da2
    x12 = 0x000000037245c28c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000000a   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2c0370   x21 = 0x0000ffff3b2c03b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff3b2c03cc
    x28 = 0x0000ffff3b2c03a0    fp = 0x0000fffef11bf670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef11bf670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef11bf740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef11bf680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef11bf750    lr = 0x0000ffff868507ac
     sp = 0x0000fffef11bf750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef11bf800    sp = 0x0000fffef11bf760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffef19bffbc
    x19 = 0x0000fffef19bffbe   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7505b940    sp = 0x0000fffef11bf780
     pc = 0x0000fffef19bffc0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef11bf790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffef11bf7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffef19bfefd
    x19 = 0x0000ffff751f00c0    fp = 0x0000ffff3b2c0370
     sp = 0x0000fffef11bf7d0    pc = 0x0000fffef19bff01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffef11bf7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7568
     sp = 0x0000fffef11bf7f0    pc = 0x0000ffff86fc156c
    Found by: stack scanning
10  libc.so.6 + 0xefa38
     sp = 0x0000fffef11bf800    pc = 0x0000ffff86be0a3c
    Found by: stack scanning
11  libpthread.so.0 + 0x7620
     sp = 0x0000fffef11bf810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
12  libc.so.6 + 0xd1668
     sp = 0x0000fffef11bf830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
13  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef11bf890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 57
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7cfec0cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7cfec0c8
     x6 = 0x0000000000000000    x7 = 0x0000fffef09be6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b7a6
    x10 = 0x0000000000000017   x11 = 0x000ae80b3e428da2
    x12 = 0x000000037245c28c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffebbf45a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff7cfec070   x21 = 0x0000ffff7cfec0b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff7cfec0cc
    x28 = 0x0000ffff7cfec0a0    fp = 0x0000fffef09be670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef09be670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef09be740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef09be680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef09be750    lr = 0x0000ffff868507ac
     sp = 0x0000fffef09be750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef09be800    sp = 0x0000fffef09be760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffebbf43fbc
    x19 = 0x0000fffebbf43fbe   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7cea0240    sp = 0x0000fffef09be780
     pc = 0x0000fffebbf43fc0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef09be790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffef09be7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffebbf43efd
    x19 = 0x0000ffff7ceb1780    fp = 0x0000ffff7cfec070
     sp = 0x0000fffef09be7d0    pc = 0x0000fffebbf43f01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffef09be7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffef09be810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffef09be830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef09be890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 58
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffd00242c88    x1 = 0x0000000000000008
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fffd0024fc40    x7 = 0x0000000000000000
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000076fe98   x19 = 0x0000fffd00242c88
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffd68e10740
    x22 = 0x0000000000000007   x23 = 0x0000fffd68e0f710
    x24 = 0x0000fffd68e0f6e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000007   x27 = 0x0000000000000001
    x28 = 0x0000000000001400    fp = 0x0000fffd68e0f5d0
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffd68e0f5d0
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffd68e0f800    lr = 0x0000ffff80b9e660
     sp = 0x0000fffd68e0f5e0    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x165c
     fp = 0x0000fffd68e0f820    lr = 0x0000ffff86fc1624
     sp = 0x0000fffd68e0f810    pc = 0x0000ffff80b9e660
    Found by: previous frame's frame pointer
 3  libpthread.so.0 + 0x7620
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffd68e0f830    pc = 0x0000ffff86fc1624
    Found by: previous frame's frame pointer

Thread 59
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7cfec3cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7cfec3c8
     x6 = 0x0000000000000000    x7 = 0x0000fffecc1f86f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b7a6
    x10 = 0x0000000000000017   x11 = 0x000ae80b3e428da2
    x12 = 0x000000037245c28c   x13 = 0x000000007fffffff
    x14 = 0x00000000b71d5768   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffebbf45a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff7cfec370   x21 = 0x0000ffff7cfec3b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff7cfec3cc
    x28 = 0x0000ffff7cfec3a0    fp = 0x0000fffecc1f8670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffecc1f8670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffecc1f8740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffecc1f8680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffecc1f8750    lr = 0x0000ffff868507ac
     sp = 0x0000fffecc1f8750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffecc1f8800    sp = 0x0000fffecc1f8760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffebbf43d0c
    x19 = 0x0000fffebbf43d0e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7cea0340    sp = 0x0000fffecc1f8780
     pc = 0x0000fffebbf43d10
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffecc1f8790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffecc1f87b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffebbf43cfd
    x19 = 0x0000ffff7ceb1860    fp = 0x0000ffff7cfec370
     sp = 0x0000fffecc1f87d0    pc = 0x0000fffebbf43d01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffecc1f87e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffecc1f8810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffecc1f8830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffecc1f8890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 60
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffeeff5f408    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0xffffffffffffffff    x7 = 0x0000fffeeff6c840
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000000000071b78   x19 = 0x0000fffeeff5f408
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffe9566a740
    x22 = 0x0000fffe956697d0   x23 = 0x0000fffe95669710
    x24 = 0x0000fffe956696e8   x25 = 0x0000fffe956697d8
    x26 = 0x0000000000000003   x27 = 0x0000000000000000
    x28 = 0x0000fffe9566a040    fp = 0x0000fffe956695d0
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffe956695d0
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffe95669800    lr = 0x0000ffff80b9e660
     sp = 0x0000fffe956695e0    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x165c
     fp = 0x0000fffe95669820    lr = 0x0000ffff86fc1624
     sp = 0x0000fffe95669810    pc = 0x0000ffff80b9e660
    Found by: previous frame's frame pointer
 3  libpthread.so.0 + 0x7620
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffe95669830    pc = 0x0000ffff86fc1624
    Found by: previous frame's frame pointer

Thread 61
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff074416e0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffe94e684e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001db    x7 = 0x0000fffe94e68408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x00136a0d108c294c
    x12 = 0x00000003783d8bd4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffe94e69a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff074416e0   x21 = 0x0000ffff07441688
    x22 = 0x0000ffff86fe5000   x23 = 0x00000000000003d0
    x24 = 0x0000fffe94e684e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x00000000000001e8
    x28 = 0x0000ffff074416b8    fp = 0x0000fffe94e68360
     lr = 0x0000ffff86fc8768    sp = 0x0000fffe94e68360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffe94e68460    lr = 0x0000ffff874b37f0
     sp = 0x0000fffe94e68370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffe94e68580    lr = 0x0000ffff873a7464
     sp = 0x0000fffe94e68470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000fffe94e68650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff3c1ae040   x22 = 0x0000fffeefc19a00
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000fffeefc199c0   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffe94e69740   x28 = 0x0000fffe94e69040
     fp = 0x0000fffe94e68590    sp = 0x0000fffe94e686a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 62
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000b5    x1 = 0x0000fffdaa8ce4e0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffde0688520    x5 = 0x0000fffd7a0483c4
     x6 = 0x0000000000000000    x7 = 0x0000ffff873a8468
     x8 = 0x00000000000000cf    x9 = 0x000000000003ba14
    x10 = 0x0000000000000017   x11 = 0x00136a0c784842db
    x12 = 0x000000037483df34   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000ffff03fb5528   x19 = 0x00000000000000b5
    x20 = 0x0000fffd7a0483c4   x21 = 0x0000fffde0688520
    x22 = 0x0000000000010000   x23 = 0x0000fffdaa8ce4e0
    x24 = 0x0000fffd7a049740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffdcb766a70
    x28 = 0x0000fffd7a049040    fp = 0x0000fffd7a048350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd7a048350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd7a0483a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd7a048360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd7a048400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd7a0483b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff750155a0   x20 = 0x0000fffd7a048608
     fp = 0x0000fffd7a048520    sp = 0x0000fffd7a048430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffde29a57e0   x20 = 0x0000ffff76b10400
    x21 = 0x0000fffdaa89c0c0   x22 = 0x0000ffff750153c0
    x23 = 0x0000ffff750120c0   x24 = 0x0000fffd7a048608
    x25 = 0x0000fffd7a048618   x26 = 0x0000ffff76b1ae18
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd7a049040
     fp = 0x0000fffd7a0485a0    sp = 0x0000fffd7a0485a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29a57b0   x20 = 0x0000fffdaa89c0a0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffde29a57e0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd7a049740   x26 = 0x0000fffd7a049040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd7a049040
     fp = 0x0000fffd7a048650    sp = 0x0000fffd7a048640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd7a049458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a25fe   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a25ff   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd7a049740   x26 = 0x0000fffd7a049040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd7a049040
     fp = 0x0000fffd7a048820    sp = 0x0000fffd7a048820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 63
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd78f7e370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x000000000009f308
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000fffffd0c
    x10 = 0x000000000009f308   x11 = 0x0000000000000035
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x000000000000b400   x19 = 0x0000fffd78f7e4a4
    x20 = 0x0000ffff76b0ca18   x21 = 0x0000000000000001
    x22 = 0x0000fffd78f7e370   x23 = 0x0000000000000001
    x24 = 0x0000fffd78f7f040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd78f7e424   x27 = 0x0000fffdcb766b50
    x28 = 0x0000000000000000    fp = 0x0000fffd78f7e320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd78f7e310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd78f7e400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd78f7e330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000fffd78f7e608
    x21 = 0x0202800178f7e4e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd78f7e520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7361d660   x26 = 0x0000fffd78f7e608
     fp = 0x0000fffd78f7e430    sp = 0x0000fffd78f7e4e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7361d660   x20 = 0x0000fffd78f7e608
    x21 = 0x0000fffdcb766450   x22 = 0x0000fffd78f7e618
    x23 = 0x0000fffdcb766b50   x24 = 0x0000000000000000
    x25 = 0x0000ffff76b1aea8   x26 = 0x0000ffff7361d698
    x27 = 0x00000000000001eb   x28 = 0x0000fffd78f7f040
     fp = 0x0000fffd78f7e520    sp = 0x0000fffd78f7e520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffde29a5930   x20 = 0x0000ffff76b10400
    x21 = 0x0000fffdaa89c240   x22 = 0x0000ffff7361d540
    x23 = 0x0000ffff7362f600   x24 = 0x0000fffd78f7e608
    x25 = 0x0000fffd78f7e618   x26 = 0x0000ffff76b1aea8
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd78f7f040
     fp = 0x0000fffd78f7e5a0    sp = 0x0000fffd78f7e5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29a5960   x20 = 0x0000fffdaa89c260
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffde29a5930   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd78f7f740   x26 = 0x0000fffd78f7f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd78f7f040
     fp = 0x0000fffd78f7e650    sp = 0x0000fffd78f7e640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd78f7f458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a25fe   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a25ff   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd78f7f740   x26 = 0x0000fffd78f7f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd78f7f040
     fp = 0x0000fffd78f7e820    sp = 0x0000fffd78f7e820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 64
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd6f633264    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd579c54e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001da    x7 = 0x0000fffd579c5408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x00184d45487307b2
    x12 = 0x0000000376aee90c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffd579c6a90   x19 = 0x0000000000000000
    x20 = 0x0000fffd6f633264   x21 = 0x0000fffd6f633208
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000006d
    x24 = 0x0000fffd579c54e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000000000000036
    x28 = 0x0000fffd6f633238    fp = 0x0000fffd579c5360
     lr = 0x0000ffff86fc8768    sp = 0x0000fffd579c5360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffd579c5460    lr = 0x0000ffff874b37f0
     sp = 0x0000fffd579c5370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffd579c5580    lr = 0x0000ffff873a7464
     sp = 0x0000fffd579c5470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000fffd579c5650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff074e1ec0   x22 = 0x0000fffd6f804940
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000fffd6f804900   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffd579c6740   x28 = 0x0000fffd579c6040
     fp = 0x0000fffd579c5590    sp = 0x0000fffd579c56a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 65
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd55fc3370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000369acadfc   x13 = 0x000000007fffffff
    x14 = 0x000000000000000b   x15 = 0x0000fffd55fc4a88
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000fffd55fc4a90   x19 = 0x0000fffd55fc34a4
    x20 = 0x0000fffdc53ba958   x21 = 0x0000000000000001
    x22 = 0x0000fffd55fc3370   x23 = 0x0000000000000001
    x24 = 0x0000fffd55fc4040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd55fc3424   x27 = 0x0000fffd6f983bd0
    x28 = 0x0000000000000000    fp = 0x0000fffd55fc3320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd55fc3310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd55fc3400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd55fc3330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000fffd55fc3608
    x21 = 0x0202800155fc34e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd55fc3520   x24 = 0x0000ffff873903cc
    x25 = 0x0000fffd773b4720   x26 = 0x0000fffd55fc3608
     fp = 0x0000fffd55fc3430    sp = 0x0000fffd55fc34e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd773b4720   x20 = 0x0000fffd55fc3608
    x21 = 0x0000fffd6f983620   x22 = 0x0000fffd55fc3618
    x23 = 0x0000fffd6f983bd0   x24 = 0x0000000000000000
    x25 = 0x0000fffd6f6dbfd0   x26 = 0x0000fffd773b4758
    x27 = 0x0000000000000263   x28 = 0x0000fffd55fc4040
     fp = 0x0000fffd55fc3520    sp = 0x0000fffd55fc3520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffeefbf7eb0   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd6f8cd860   x22 = 0x0000fffd773b4600
    x23 = 0x0000fffd774fa840   x24 = 0x0000fffd55fc3608
    x25 = 0x0000fffd55fc3618   x26 = 0x0000fffd6f6dbfd0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd55fc4040
     fp = 0x0000fffd55fc35a0    sp = 0x0000fffd55fc35a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeefbf7e80   x20 = 0x0000fffd6f8cd840
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffeefbf7eb0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd55fc4740   x26 = 0x0000fffd55fc4040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd55fc4040
     fp = 0x0000fffd55fc3650    sp = 0x0000fffd55fc3640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd55fc4458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16be   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16bf   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd55fc4740   x26 = 0x0000fffd55fc4040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd55fc4040
     fp = 0x0000fffd55fc3820    sp = 0x0000fffd55fc3820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 66
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd3818c8c8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd3818c8c8
     x6 = 0x0000000000000000    x7 = 0x0000fffe7d9a46f8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000ffff86fc14a0   x11 = 0x00000000003d0f00
    x12 = 0x0000fffe7d9a5040   x13 = 0xffffffffffffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000070c24da0   x19 = 0x0000000000000000
    x20 = 0x0000fffd3818c870   x21 = 0x0000fffd3818c8b0
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd3818c8c8
    x28 = 0x0000fffd3818c8a0    fp = 0x0000fffe7d9a4670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffe7d9a4670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffe7d9a4740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffe7d9a4680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffe7d9a4750    lr = 0x0000ffff868507ac
     sp = 0x0000fffe7d9a4750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffe7d9a4800    sp = 0x0000fffe7d9a4760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffe7c9a142c
    x19 = 0x0000fffe7c9a142e   x20 = 0x0000ffff86fe5000
     fp = 0x0000fffd381a4680    sp = 0x0000fffe7d9a4780
     pc = 0x0000fffe7c9a1430
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe7d9a4790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffe7d9a47b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffe7c9a13fd
    x19 = 0x0000fffe7d9a47d0    fp = 0x0000fffd3818c870
     sp = 0x0000fffe7d9a47d0    pc = 0x0000fffe7c9a1401
    Found by: call frame info
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     sp = 0x0000fffe7d9a47f0    pc = 0x0000ffff86d54f9c
    Found by: stack scanning
 9  libpthread.so.0 + 0x776c
    x19 = 0x0000fffe7d9a4820   x20 = 0x0000ffff86fc1624
     fp = 0x0000fffd381a4680    sp = 0x0000fffe7d9a4810
     pc = 0x0000ffff86fc1770
    Found by: call frame info
10  libc.so.6 + 0xd1668
     sp = 0x0000fffe7d9a4830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe7d9a4890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 67
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd292ea100    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffcffd50678
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffcffd505e8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0013404706b96bca
    x12 = 0x000000037861c92c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000002   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x00000000001a5538   x19 = 0x0000000000000000
    x20 = 0x0000fffd292ea100   x21 = 0x0000fffd292e9038
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000068
    x24 = 0x0000fffcffd50678   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000034
    x28 = 0x0000fffd292ea0d8    fp = 0x0000fffcffd50540
     lr = 0x0000ffff86fc8768    sp = 0x0000fffcffd50540
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffcffd50780    lr = 0x0000ffff828e7808
     sp = 0x0000fffcffd50550    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnvscistream.so.1 + 0x2e804
     fp = 0x0000fffcffd50790    lr = 0x0000ffff84d095a8
     sp = 0x0000fffcffd50790    pc = 0x0000ffff828e7808
    Found by: previous frame's frame pointer
 3  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x24
     fp = 0x0000fffcffd507d0    lr = 0x0000ffff84d09950
     sp = 0x0000fffcffd507a0    pc = 0x0000ffff84d095a8
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamProducer::HandleSetupComplete() + 0xec
    x19 = 0x0000fffd29292618   x20 = 0x000000000000000f
     fp = 0x0000fffd384c0100    sp = 0x0000fffcffd507e0
     pc = 0x0000ffff84d0d000
    Found by: call frame info
 5  0xfffe7c9a199c
    x19 = 0x0000fffe7c9a199e   x20 = 0x0000000000000000
    x21 = 0x0000fffcffd50820   x22 = 0x0000ffff86fc1624
    x23 = 0x0000fffcffd5145c   x24 = 0x0000fffe7c9a19a0
    x25 = 0x0000000000000000   x26 = 0x0000ffff86bc266c
    x27 = 0x0000fffcffd51040   x28 = 0x0000fffe7c9a19a0
     fp = 0x0000fffd384c0100    sp = 0x0000fffcffd50850
     pc = 0x0000fffe7c9a19a0
    Found by: call frame info
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffcffd50890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 68
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff7e801008    x1 = 0x0000000000000001
     x2 = 0x0000ffff805fd398    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x000000000003be3e
     x8 = 0x0000000000000049    x9 = 0x000000000003be3e
    x10 = 0x0000000000000017   x11 = 0x001570d99686215a
    x12 = 0x00000003785df89c   x13 = 0x000000007fffffff
    x14 = 0x00000000003309a8   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000ffff85b9b610   x19 = 0x0000ffff7e801008
    x20 = 0x0000ffff86c5e000   x21 = 0x0000ffff805fe740
    x22 = 0x0000ffff7e7ff060   x23 = 0x0000000000000001
    x24 = 0x0000ffff805fd430   x25 = 0x0000ffff805fd41f
    x26 = 0x0000ffff860ef4e0   x27 = 0x0000ffff860f3a50
    x28 = 0x0000ffff85a3b7d0    fp = 0x0000ffff805fd350
     lr = 0x0000ffff86bb90c0    sp = 0x0000ffff805fd350
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000ffff805fd3b0    lr = 0x0000ffff860ccddc
     sp = 0x0000ffff805fd360    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libzmq.so.5!zmq::socket_poller_t::wait(zmq_poller_event_t*, int, long) + 0x118
     fp = 0x0000ffff805fd440    lr = 0x0000ffff860cb5f0
     sp = 0x0000ffff805fd3c0    pc = 0x0000ffff860ccddc
    Found by: previous frame's frame pointer
 3  0xffff7e7ff05c
    x19 = 0x0000ffff85a3b7d0   x20 = 0x0000ffff85a3b7d0
    x21 = 0x0000000000000001   x22 = 0x0000000000000001
    x23 = 0x0000ffff85a3b7c0   x24 = 0x0000000000000000
    x25 = 0x0000ffff805fd4f8   x26 = 0x0000ffff85a3b7d0
    x27 = 0x0000ffff805fd440   x28 = 0x0000ffff860cb5f0
     fp = 0x0000000000000000    sp = 0x0000ffff805fd450
     pc = 0x0000ffff7e7ff060
    Found by: call frame info

Thread 69
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85a97640    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85a97640
     x6 = 0x0000000000000000    x7 = 0x0000ffff7cdfd6f8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000ffff86fc14a0   x11 = 0x00000000003d0f00
    x12 = 0x0000ffff7cdfe040   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff85a975e8   x21 = 0x0000ffff85a97628
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff85a97640
    x28 = 0x0000ffff85a97618    fp = 0x0000ffff7cdfd670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff7cdfd670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff7cdfd740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff7cdfd680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff7cdfd750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff7cdfd750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff7cdfd800    sp = 0x0000ffff7cdfd760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5e2c
    x19 = 0x0000ffffd14e5e2e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85a52cc0    sp = 0x0000ffff7cdfd780
     pc = 0x0000ffffd14e5e30
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff7cdfd790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000ffff7cdfd7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffffd14e5dfd
    x19 = 0x0000ffff7cdfd7d0    fp = 0x0000ffff85a975e8
     sp = 0x0000ffff7cdfd7d0    pc = 0x0000ffffd14e5e01
    Found by: call frame info
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     sp = 0x0000ffff7cdfd7f0    pc = 0x0000ffff86d54f9c
    Found by: stack scanning

Thread 70
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff5ad4b5e4    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff53ec44e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001db    x7 = 0x0000ffff53ec4408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x00130e7f8f7f7f64
    x12 = 0x000000037837d2fc   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff53ec5a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff5ad4b5e4   x21 = 0x0000ffff5ad4b588
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000026f
    x24 = 0x0000ffff53ec44e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000000000000137
    x28 = 0x0000ffff5ad4b5b8    fp = 0x0000ffff53ec4360
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff53ec4360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff53ec4460    lr = 0x0000ffff874b37f0
     sp = 0x0000ffff53ec4370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000ffff53ec4580    lr = 0x0000ffff873a7464
     sp = 0x0000ffff53ec4470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000ffff53ec4650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff7461d630   x22 = 0x0000ffff5adc7b80
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000ffff5adc7580   x26 = 0x0000ffff874b0204
    x27 = 0x0000ffff53ec5740   x28 = 0x0000ffff53ec5040
     fp = 0x0000ffff53ec4590    sp = 0x0000ffff53ec46a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 71
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff75409470    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000ffff735fd7f0
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5146    x7 = 0x0000ffff735fd728
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0019cebe42c84253
    x12 = 0x0000000376edda54   x13 = 0x000000007fffffff
    x14 = 0x0000ffff735feaa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x000000000000fff0   x19 = 0x0000000000000000
    x20 = 0x0000ffff75409470   x21 = 0x0000ffff75409080
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000012
    x24 = 0x0000ffff735fd7f0   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000009
    x28 = 0x0000ffff75409448    fp = 0x0000ffff735fd680
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff735fd680
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff735fd770    lr = 0x0000ffff8684a16c
     sp = 0x0000ffff735fd690    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xf8
     fp = 0x0000ffff735fd800    lr = 0x0000ffff86d54f9c
     sp = 0x0000ffff735fd780    pc = 0x0000ffff8684a16c
    Found by: previous frame's frame pointer
 3  0xffffd14e62fc
    x19 = 0x0000ffffd14e62fe   x20 = 0x0000ffff86fe5000
    x21 = 0x0000ffffd14e62ff   x22 = 0x0000ffff86d54f80
    x23 = 0x0000ffff735fe740   x24 = 0x0000ffff735fe040
    x25 = 0x0000ffff86fe6000   x26 = 0x0000ffff735fe040
    x27 = 0x0000ffff735fd7f0   x28 = 0x0000ffff86fc99a0
     fp = 0x0000ffff85a23380    sp = 0x0000ffff735fd810
     pc = 0x0000ffffd14e6300
    Found by: call frame info
 4  libc.so.6 + 0xd1668
     sp = 0x0000ffff735fd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff735fd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 72
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007a28    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007a28
     x6 = 0x0000000000000000    x7 = 0x0000ffff02b726d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000ffff02b73750
    x12 = 0x0000000000000030   x13 = 0x0000000000014ec0
    x14 = 0x00000000000200f0   x15 = 0x0000000000014ef0
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x00000000000032c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007500   x21 = 0x0000ffff75007a10
    x22 = 0x000000000000001c   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000ffff75007a28
    x28 = 0x0000ffff75007a00    fp = 0x0000ffff02b72650
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff02b72650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff02b72720    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff02b72660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff02b72730    lr = 0x0000ffff8684b6e8
     sp = 0x0000ffff02b72730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff02b72800    sp = 0x0000ffff02b72740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bcb7e0    sp = 0x0000ffff02b72760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff02b72770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libsodium.so.23!sodium_bin2hex + 0x1bb
     sp = 0x0000ffff02b72800    pc = 0x0000ffff8600305f
    Found by: stack scanning
 7  libpthread.so.0 + 0x7620
     sp = 0x0000ffff02b72810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 8  libc.so.6 + 0xd1668
     sp = 0x0000ffff02b72830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff02b72890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 73
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007a8c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007a88
     x6 = 0x0000000000000000    x7 = 0x0000ffff0136f6d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000ffff01370750
    x12 = 0x0000000000000030   x13 = 0x0000000000006de0
    x14 = 0x0000000000010000   x15 = 0x0000000000006e10
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007560   x21 = 0x0000ffff75007a74
    x22 = 0x000000000000001f   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000ffff75007a8c
    x28 = 0x0000ffff75007a60    fp = 0x0000ffff0136f650
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff0136f650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff0136f720    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff0136f660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff0136f730    lr = 0x0000ffff8684b6e8
     sp = 0x0000ffff0136f730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff0136f800    sp = 0x0000ffff0136f740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bcbb80    sp = 0x0000ffff0136f760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff0136f770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000ffff0136f810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000ffff0136f830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff0136f890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 74
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007ab8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007ab8
     x6 = 0x0000000000000000    x7 = 0x0000ffff00b6e6d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000ffff00b6f750
    x12 = 0x0000000000000030   x13 = 0x0000000000051850
    x14 = 0x0000000000060c90   x15 = 0x0000000000051880
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007590   x21 = 0x0000ffff75007aa0
    x22 = 0x0000000000000020   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000010   x27 = 0x0000ffff75007ab8
    x28 = 0x0000ffff75007a90    fp = 0x0000ffff00b6e650
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff00b6e650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff00b6e720    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff00b6e660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff00b6e730    lr = 0x0000ffff8684b6e8
     sp = 0x0000ffff00b6e730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff00b6e800    sp = 0x0000ffff00b6e740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bcbd60    sp = 0x0000ffff00b6e760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff00b6e770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000ffff00b6e810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000ffff00b6e830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff00b6e890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 75
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff7505c0cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff7505c0c8
     x6 = 0x0000000000000000    x7 = 0x0000ffff0036d6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b49c
    x10 = 0x0000000000000017   x11 = 0x001991b4d229ca48
    x12 = 0x000000036f76b084   x13 = 0x000000007fffffff
    x14 = 0x0000000082ffe260   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff761fda90   x19 = 0x0000000000000000
    x20 = 0x0000ffff7505c070   x21 = 0x0000ffff7505c0b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff7505c0cc
    x28 = 0x0000ffff7505c0a0    fp = 0x0000ffff0036d670
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff0036d670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff0036d740    lr = 0x0000ffff86d4e660
     sp = 0x0000ffff0036d680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000ffff0036d750    lr = 0x0000ffff868507ac
     sp = 0x0000ffff0036d750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000ffff0036d800    sp = 0x0000ffff0036d760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffff761fba2c
    x19 = 0x0000ffff761fba2e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff750180c0    sp = 0x0000ffff0036d780
     pc = 0x0000ffff761fba30
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff0036d790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000ffff0036d7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xffff761fb9fd
    x19 = 0x0000ffff7506e000    fp = 0x0000ffff7505c070
     sp = 0x0000ffff0036d7d0    pc = 0x0000ffff761fba01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000ffff0036d7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000ffff0036d810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000ffff0036d830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000ffff0036d890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 76
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff85b15998    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff85b15998
     x6 = 0x0000000000000000    x7 = 0x0000fffefdb68748
     x8 = 0x0000000000000062    x9 = 0x000000000003e8c0
    x10 = 0x0000ffff7e1f46c0   x11 = 0x000affff7f218501
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff74424498   x21 = 0x0000ffff85b15980
    x22 = 0x0000000000000024   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000012   x27 = 0x0000ffff85b15998
    x28 = 0x0000ffff85b15970    fp = 0x0000fffefdb686c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefdb686c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefdb68790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefdb686d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefdb687a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffefdb687a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefdb68800    sp = 0x0000fffefdb687b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1f980    sp = 0x0000fffefdb687d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefdb687e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefdb68810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefdb68830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefdb68890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 77
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007ba8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007ba8
     x6 = 0x0000000000000000    x7 = 0x0000fffefc3656d8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000003   x11 = 0x0000fffefc366750
    x12 = 0x0000000000000030   x13 = 0x00000000000478a0
    x14 = 0x0000000000050f70   x15 = 0x00000000000478d0
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000b2c8   x19 = 0x0000000000000000
    x20 = 0x0000ffff75007680   x21 = 0x0000ffff75007b90
    x22 = 0x0000000000000020   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000010   x27 = 0x0000ffff75007ba8
    x28 = 0x0000ffff75007b80    fp = 0x0000fffefc365650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffefc365650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffefc365720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffefc365660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffefc365730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffefc365730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffefc365800    sp = 0x0000fffefc365740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f32c60    sp = 0x0000fffefc365760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefc365770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffefc365810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffefc365830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffefc365890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 78
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff72b8635c    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff72b86358
     x6 = 0x0000000000000000    x7 = 0x0000fffef8295748
     x8 = 0x0000000000000062    x9 = 0x0000000000024856
    x10 = 0x0000fffd3f124370   x11 = 0x000affff7522b881
    x12 = 0x000000000000000a   x13 = 0x0000ffff8840a000
    x14 = 0x000000000000000a   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000010000   x19 = 0x0000000000000000
    x20 = 0x0000ffff85bf4598   x21 = 0x0000ffff72b86344
    x22 = 0x0000000000000007   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000003   x27 = 0x0000ffff72b8635c
    x28 = 0x0000ffff72b86330    fp = 0x0000fffef82956c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef82956c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef8295790    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef82956d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef82957a0    lr = 0x0000ffff868410d4
     sp = 0x0000fffef82957a0    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef8295800    sp = 0x0000fffef82957b0
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5ecc
    x19 = 0x0000ffffd14e5ece   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff03f1faf0    sp = 0x0000fffef82957d0
     pc = 0x0000ffffd14e5ed0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef82957e0    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef8295810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef8295830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef8295890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 79
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff75007cf8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff75007cf8
     x6 = 0x0000000000000000    x7 = 0x0000fffef69ca6d8
     x8 = 0x0000000000000062    x9 = 0xffffff80ffffffc8
    x10 = 0x0000000000000000   x11 = 0x0000fffef69ca1e8
    x12 = 0x0000000000000002   x13 = 0x000000000000000f
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff85f30a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff750077d0   x21 = 0x0000ffff75007ce0
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff75007cf8
    x28 = 0x0000ffff75007cd0    fp = 0x0000fffef69ca650
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef69ca650
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef69ca720    lr = 0x0000ffff86d4e660
     sp = 0x0000fffef69ca660    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffef69ca730    lr = 0x0000ffff8684b6e8
     sp = 0x0000fffef69ca730    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffef69ca800    sp = 0x0000fffef69ca740
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xffffd14e5fec
    x19 = 0x0000ffffd14e5fee   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff85bf6c00    sp = 0x0000fffef69ca760
     pc = 0x0000ffffd14e5ff0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef69ca770    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0x7620
     sp = 0x0000fffef69ca810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
 7  libc.so.6 + 0xd1668
     sp = 0x0000fffef69ca830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
 8  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffef69ca890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 80
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff043d8cb0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff043d8cb0
     x6 = 0x0000000000000000    x7 = 0x0000fffecb9f7748
     x8 = 0x0000000000000062    x9 = 0x0000ffff076ba708
    x10 = 0x0000ffff076ba6f8   x11 = 0x0000ffff076ba748
    x12 = 0x0000ffff076ba738   x13 = 0x0000fffecb9f8758
    x14 = 0x0000fffecb9f8a90   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000000064   x19 = 0x0000000000000000
    x20 = 0x0000ffff043d8c50   x21 = 0x0000ffff043d8c98
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff043d8cb0
    x28 = 0x0000ffff043d8c88    fp = 0x0000fffecb9f76c0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffecb9f76c0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffecb9f7820    lr = 0x0000ffff337b2118
     sp = 0x0000fffecb9f76d0    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libopencv_core.so.4.3!cv::WorkerThread::thread_body() + 0x114
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffecb9f7830    pc = 0x0000ffff337b2118
    Found by: previous frame's frame pointer
 3  0xfffef19bef0b
    x19 = 0x0000fffecb9f8040   x20 = 0x0000fffef19bef10
    x21 = 0x0000fffef19bef0e   x22 = 0x0000000000000000
    x23 = 0x0000000000000000   x24 = 0x0000fffecb9f8040
    x25 = 0x0000fffecb9f845c   x26 = 0x0000fffef19bef10
    x27 = 0x0000fffef19bef0e   x28 = 0x0000ffff86fe5000
     fp = 0x0000000000000000    sp = 0x0000fffecb9f78b0
     pc = 0x0000fffef19bef0f
    Found by: call frame info

Thread 81
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff3b2c00cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff3b2c00c8
     x6 = 0x0000000000000000    x7 = 0x0000fffec1bf56f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b758
    x10 = 0x0000000000000017   x11 = 0x000660603cc474ec
    x12 = 0x0000000371fd47dc   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffecc1f9a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2c0070   x21 = 0x0000ffff3b2c00b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff3b2c00cc
    x28 = 0x0000ffff3b2c00a0    fp = 0x0000fffec1bf5670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffec1bf5670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffec1bf5740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffec1bf5680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffec1bf5750    lr = 0x0000ffff868507ac
     sp = 0x0000fffec1bf5750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffec1bf5800    sp = 0x0000fffec1bf5760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffecc1f7fbc
    x19 = 0x0000fffecc1f7fbe   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7505b240    sp = 0x0000fffec1bf5780
     pc = 0x0000fffecc1f7fc0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec1bf5790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffec1bf57b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffecc1f7efd
    x19 = 0x0000ffff751eff00    fp = 0x0000ffff3b2c0070
     sp = 0x0000fffec1bf57d0    pc = 0x0000fffecc1f7f01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffec1bf57e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffec1bf5810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffec1bf5830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec1bf5890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 82
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff3b2c02cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff3b2c02c8
     x6 = 0x0000000000000000    x7 = 0x0000fffec13f46f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b6d2
    x10 = 0x0000000000000017   x11 = 0x001c6e20bc6f89c3
    x12 = 0x0000000371814d94   x13 = 0x000000007fffffff
    x14 = 0x00000000c1fc5d18   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffecc1f9a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2c0270   x21 = 0x0000ffff3b2c02b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff3b2c02cc
    x28 = 0x0000ffff3b2c02a0    fp = 0x0000fffec13f4670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffec13f4670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffec13f4740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffec13f4680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffec13f4750    lr = 0x0000ffff868507ac
     sp = 0x0000fffec13f4750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffec13f4800    sp = 0x0000fffec13f4760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffecc1f7d0c
    x19 = 0x0000fffecc1f7d0e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7505b380    sp = 0x0000fffec13f4780
     pc = 0x0000fffecc1f7d10
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec13f4790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffec13f47b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffecc1f7cfd
    x19 = 0x0000ffff751effe0    fp = 0x0000ffff3b2c0270
     sp = 0x0000fffec13f47d0    pc = 0x0000fffecc1f7d01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffec13f47e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffec13f4810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffec13f4830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffec13f4890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 83
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff03f392cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff03f392c8
     x6 = 0x0000000000000000    x7 = 0x0000fffebe7c66f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b76e
    x10 = 0x0000000000000017   x11 = 0x0007b011bcfbc5cf
    x12 = 0x00000003721242f4   x13 = 0x000000007fffffff
    x14 = 0x00000000c4c01ae0   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffecb1f7a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff03f39270   x21 = 0x0000ffff03f392b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff03f392cc
    x28 = 0x0000ffff03f392a0    fp = 0x0000fffebe7c6670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffebe7c6670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffebe7c6740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffebe7c6680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffebe7c6750    lr = 0x0000ffff868507ac
     sp = 0x0000fffebe7c6750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffebe7c6800    sp = 0x0000fffebe7c6760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffecb1f5d0c
    x19 = 0x0000fffecb1f5d0e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff72baefc0    sp = 0x0000fffebe7c6780
     pc = 0x0000fffecb1f5d10
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffebe7c6790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffebe7c67b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffecb1f5cfd
    x19 = 0x0000ffff72d953a0    fp = 0x0000ffff03f39270
     sp = 0x0000fffebe7c67d0    pc = 0x0000fffecb1f5d01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffebe7c67e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffebe7c6810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffebe7c6830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffebe7c6890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 84
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff3b384588    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0xffffffffffffffff    x7 = 0x0000ffff3b3919c0
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000000000071b78   x19 = 0x0000ffff3b384588
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffee79fd740
    x22 = 0x0000fffee79fc7d0   x23 = 0x0000fffee79fc710
    x24 = 0x0000fffee79fc6e8   x25 = 0x0000fffee79fc7d8
    x26 = 0x0000000000000003   x27 = 0x0000000000000000
    x28 = 0x0000fffee79fd040    fp = 0x0000fffee79fc5d0
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffee79fc5d0
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffee79fc800    lr = 0x0000ffff80b9e660
     sp = 0x0000fffee79fc5e0    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x165c
     fp = 0x0000fffee79fc820    lr = 0x0000ffff86fc1624
     sp = 0x0000fffee79fc810    pc = 0x0000ffff80b9e660
    Found by: previous frame's frame pointer
 3  libpthread.so.0 + 0x7620
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffee79fc830    pc = 0x0000ffff86fc1624
    Found by: previous frame's frame pointer

Thread 85
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff3b2c05cc    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff3b2c05c8
     x6 = 0x0000000000000000    x7 = 0x0000fffeebdfd6f8
     x8 = 0x0000000000000062    x9 = 0x000000000003b7a6
    x10 = 0x0000000000000017   x11 = 0x000ae80b3e428da2
    x12 = 0x000000037245c28c   x13 = 0x000000007fffffff
    x14 = 0x00000000975d05f0   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000fffef19c1a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2c0570   x21 = 0x0000ffff3b2c05b4
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff3b2c05cc
    x28 = 0x0000ffff3b2c05a0    fp = 0x0000fffeebdfd670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffeebdfd670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffeebdfd740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffeebdfd680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffeebdfd750    lr = 0x0000ffff868507ac
     sp = 0x0000fffeebdfd750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffeebdfd800    sp = 0x0000fffeebdfd760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffef19bfd0c
    x19 = 0x0000fffef19bfd0e   x20 = 0x0000ffff86fe5000
     fp = 0x0000ffff7505ba40    sp = 0x0000fffeebdfd780
     pc = 0x0000fffef19bfd10
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeebdfd790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffeebdfd7b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffef19bfcfd
    x19 = 0x0000ffff751f01a0    fp = 0x0000ffff3b2c0570
     sp = 0x0000fffeebdfd7d0    pc = 0x0000fffef19bfd01
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffeebdfd7e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libpthread.so.0 + 0x7620
     sp = 0x0000fffeebdfd810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
10  libc.so.6 + 0xd1668
     sp = 0x0000fffeebdfd830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffeebdfd890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 86
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff0405e408    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0xffffffffffffffff    x7 = 0x0000ffff0406b100
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x0000000000072f98   x19 = 0x0000ffff0405e408
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffebdfc6740
    x22 = 0x0000fffebdfc57d0   x23 = 0x0000fffebdfc5710
    x24 = 0x0000fffebdfc56e8   x25 = 0x0000fffebdfc57d8
    x26 = 0x0000000000000003   x27 = 0x0000000000000000
    x28 = 0x0000fffebdfc6040    fp = 0x0000fffebdfc55d0
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffebdfc55d0
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffebdfc5800    lr = 0x0000ffff80b9e660
     sp = 0x0000fffebdfc55e0    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x165c
     fp = 0x0000fffebdfc5820    lr = 0x0000ffff86fc1624
     sp = 0x0000fffebdfc5810    pc = 0x0000ffff80b9e660
    Found by: previous frame's frame pointer
 3  libpthread.so.0 + 0x7620
     fp = 0x0000000000000000    lr = 0x0000ffff86bc266c
     sp = 0x0000fffebdfc5830    pc = 0x0000ffff86fc1624
    Found by: previous frame's frame pointer

Thread 87
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff7cffd104    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffe93e66678
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffe93e665e8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x001321c2865fc692
    x12 = 0x00000003785fe0e4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000006900000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000000000   x19 = 0x0000000000000000
    x20 = 0x0000ffff7cffd104   x21 = 0x0000ffff7cffc038
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000020f
    x24 = 0x0000fffe93e66678   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000000000000107
    x28 = 0x0000ffff7cffd0d8    fp = 0x0000fffe93e66540
     lr = 0x0000ffff86fc8768    sp = 0x0000fffe93e66540
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffe93e66780    lr = 0x0000ffff828e7808
     sp = 0x0000fffe93e66550    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnvscistream.so.1 + 0x2e804
     fp = 0x0000fffe93e66790    lr = 0x0000ffff84d095a8
     sp = 0x0000fffe93e66790    pc = 0x0000ffff828e7808
    Found by: previous frame's frame pointer
 3  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x24
     fp = 0x0000fffe93e667d0    lr = 0x0000ffff84d09950
     sp = 0x0000fffe93e667a0    pc = 0x0000ffff84d095a8
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamProducer::HandleSetupComplete() + 0xec
    x19 = 0x0000ffff7cfee318   x20 = 0x0000000000000009
     fp = 0x0000ffff7cfc2310    sp = 0x0000fffe93e667e0
     pc = 0x0000ffff84d0d000
    Found by: call frame info
 5  0xfffebbf4453c
    x19 = 0x0000fffebbf4453e   x20 = 0x0000000000000000
    x21 = 0x0000fffe93e66820   x22 = 0x0000ffff86fc1624
    x23 = 0x0000fffe93e6745c   x24 = 0x0000fffebbf44540
    x25 = 0x0000000000000000   x26 = 0x0000ffff86bc266c
    x27 = 0x0000fffe93e67040   x28 = 0x0000fffebbf44540
     fp = 0x0000ffff7cfc2310    sp = 0x0000fffe93e66850
     pc = 0x0000fffebbf44540
    Found by: call frame info
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe93e66890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 88
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff3b2d8104    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000fffe93665678
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000068ca5147    x7 = 0x0000fffe936655e8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0013404706b96bca
    x12 = 0x000000037861c92c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000000000084990   x19 = 0x0000000000000000
    x20 = 0x0000ffff3b2d8104   x21 = 0x0000ffff3b2d7038
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000217
    x24 = 0x0000fffe93665678   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x000000000000010b
    x28 = 0x0000ffff3b2d80d8    fp = 0x0000fffe93665540
     lr = 0x0000ffff86fc8768    sp = 0x0000fffe93665540
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffe93665780    lr = 0x0000ffff828e7808
     sp = 0x0000fffe93665550    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnvscistream.so.1 + 0x2e804
     fp = 0x0000fffe93665790    lr = 0x0000ffff84d095a8
     sp = 0x0000fffe93665790    pc = 0x0000ffff828e7808
    Found by: previous frame's frame pointer
 3  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x24
     fp = 0x0000fffe936657d0    lr = 0x0000ffff84d09950
     sp = 0x0000fffe936657a0    pc = 0x0000ffff84d095a8
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamProducer::HandleSetupComplete() + 0xec
    x19 = 0x0000ffff75068218   x20 = 0x0000000000000009
     fp = 0x0000ffff3b286290    sp = 0x0000fffe936657e0
     pc = 0x0000ffff84d0d000
    Found by: call frame info
 5  0xfffef19c053c
    x19 = 0x0000fffef19c053e   x20 = 0x0000000000000000
    x21 = 0x0000fffe93665820   x22 = 0x0000ffff86fc1624
    x23 = 0x0000fffe9366645c   x24 = 0x0000fffef19c0540
    x25 = 0x0000000000000000   x26 = 0x0000ffff86bc266c
    x27 = 0x0000fffe93666040   x28 = 0x0000fffef19c0540
     fp = 0x0000ffff3b286290    sp = 0x0000fffe93665850
     pc = 0x0000fffef19c0540
    Found by: call frame info
 6  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe93665890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 89
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000bb    x1 = 0x0000fffd6fc654e0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd6f720b20    x5 = 0x0000fffd557c23c4
     x6 = 0x0000000000000000    x7 = 0x00000000460ea364
     x8 = 0x00000000000000cf    x9 = 0x000000000003bd58
    x10 = 0x0000000000000017   x11 = 0x000816e16f2060a9
    x12 = 0x000000037788591c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000100d0   x19 = 0x00000000000000bb
    x20 = 0x0000fffd557c23c4   x21 = 0x0000fffd6f720b20
    x22 = 0x0000000000010000   x23 = 0x0000fffd6fc654e0
    x24 = 0x0000fffd557c3740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd6f983c40
    x28 = 0x0000fffd557c3040    fp = 0x0000fffd557c2350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd557c2350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd557c23a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd557c2360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd557c2400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd557c23b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7e8b9380   x20 = 0x0000fffd557c2608
     fp = 0x0000fffd557c2520    sp = 0x0000fffd557c2430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffeefbf7e50   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd6f8cd820   x22 = 0x0000ffff7e8b9260
    x23 = 0x0000ffff7e8c9000   x24 = 0x0000fffd557c2608
    x25 = 0x0000fffd557c2618   x26 = 0x0000fffd6f6dc018
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd557c3040
     fp = 0x0000fffd557c25a0    sp = 0x0000fffd557c25a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeefbf7e20   x20 = 0x0000fffd6f8cd800
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffeefbf7e50   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd557c3740   x26 = 0x0000fffd557c3040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd557c3040
     fp = 0x0000fffd557c2650    sp = 0x0000fffd557c2640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd557c3458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd557c3740   x26 = 0x0000fffd557c3040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd557c3040
     fp = 0x0000fffd557c2820    sp = 0x0000fffd557c2820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 90
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000b4    x1 = 0x0000fffdaa8ae360
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffde0688560    x5 = 0x0000fffe946673c4
     x6 = 0x0000000000000000    x7 = 0x0000000045f9e05e
     x8 = 0x00000000000000cf    x9 = 0x000000000003bd58
    x10 = 0x0000000000000017   x11 = 0x000816e16f2060a9
    x12 = 0x000000037788591c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000100d0   x19 = 0x00000000000000b4
    x20 = 0x0000fffe946673c4   x21 = 0x0000fffde0688560
    x22 = 0x0000000000010000   x23 = 0x0000fffdaa8ae360
    x24 = 0x0000fffe94668740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffdcb766a00
    x28 = 0x0000fffe94668040    fp = 0x0000fffe94667350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffe94667350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffe946673a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffe94667360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffe94667400    lr = 0x0000ffff87399c00
     sp = 0x0000fffe946673b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7ce9c460   x20 = 0x0000fffe94667608
     fp = 0x0000fffe94667520    sp = 0x0000fffe94667430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffde29a5840   x20 = 0x0000ffff76b10400
    x21 = 0x0000fffdaa89c100   x22 = 0x0000ffff7ce9c280
    x23 = 0x0000fffeeff85d00   x24 = 0x0000fffe94667608
    x25 = 0x0000fffe94667618   x26 = 0x0000ffff76b1add0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffe94668040
     fp = 0x0000fffe946675a0    sp = 0x0000fffe946675a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29a5810   x20 = 0x0000fffdaa89c0e0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffde29a5840   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffe94668740   x26 = 0x0000fffe94668040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffe94668040
     fp = 0x0000fffe94667650    sp = 0x0000fffe94667640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffe94668458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a260e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a260f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffe94668740   x26 = 0x0000fffe94668040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffe94668040
     fp = 0x0000fffe94667820    sp = 0x0000fffe94667820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 91
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd54ef9370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x000000000007f820
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000ffffffbc
    x10 = 0x000000000007f820   x11 = 0x0000000000000010
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000000000862ef0   x19 = 0x0000fffd54ef94a4
    x20 = 0x0000fffdc53ba898   x21 = 0x0000000000000001
    x22 = 0x0000fffd54ef9370   x23 = 0x0000000000000001
    x24 = 0x0000fffd54efa040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd54ef9424   x27 = 0x0000fffd6f983cb0
    x28 = 0x0000000000000000    fp = 0x0000fffd54ef9320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd54ef9310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd54ef9400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd54ef9330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000fffd54ef9608
    x21 = 0x0202800154ef94e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd54ef9520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7d080e60   x26 = 0x0000fffd54ef9608
     fp = 0x0000fffd54ef9430    sp = 0x0000fffd54ef94e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7d080e60   x20 = 0x0000fffd54ef9608
    x21 = 0x0000fffd6f983620   x22 = 0x0000fffd54ef9618
    x23 = 0x0000fffd6f983cb0   x24 = 0x0000000000000000
    x25 = 0x0000fffd6f6dc060   x26 = 0x0000ffff7d080e98
    x27 = 0x0000000000000263   x28 = 0x0000fffd54efa040
     fp = 0x0000fffd54ef9520    sp = 0x0000fffd54ef9520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffdc57f9070   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd6f8cd7e0   x22 = 0x0000ffff7d080d40
    x23 = 0x0000ffff7d090000   x24 = 0x0000fffd54ef9608
    x25 = 0x0000fffd54ef9618   x26 = 0x0000fffd6f6dc060
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd54efa040
     fp = 0x0000fffd54ef95a0    sp = 0x0000fffd54ef95a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeefbf7f70   x20 = 0x0000fffd6f8cd7c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffdc57f9070   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd54efa740   x26 = 0x0000fffd54efa040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd54efa040
     fp = 0x0000fffd54ef9650    sp = 0x0000fffd54ef9640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd54efa458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd54efa740   x26 = 0x0000fffd54efa040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd54efa040
     fp = 0x0000fffd54ef9820    sp = 0x0000fffd54ef9820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 92
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000bd    x1 = 0x0000fffd6fca5fa0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd6f720b60    x5 = 0x0000fffd546f83c4
     x6 = 0x0000000000000000    x7 = 0x0000000000000018
     x8 = 0x00000000000000cf    x9 = 0x0000000000009988
    x10 = 0x0000fffeeaa79680   x11 = 0x0000fffeeaa79688
    x12 = 0x0000000000000000   x13 = 0x0000000000000112
    x14 = 0x0000000000000003   x15 = 0x0000fffd546f9a88
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000fffd546f9a90   x19 = 0x00000000000000bd
    x20 = 0x0000fffd546f83c4   x21 = 0x0000fffd6f720b60
    x22 = 0x0000000000010000   x23 = 0x0000fffd6fca5fa0
    x24 = 0x0000fffd546f9740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd6f983d20
    x28 = 0x0000fffd546f9040    fp = 0x0000fffd546f8350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd546f8350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd546f83a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd546f8360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd546f8400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd546f83b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffeeffdaf60   x20 = 0x0000fffd546f8608
     fp = 0x0000fffd546f8520    sp = 0x0000fffd546f8430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffeefbf7fa0   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd6f8cd9a0   x22 = 0x0000fffeeffdae40
    x23 = 0x0000fffeeff9d240   x24 = 0x0000fffd546f8608
    x25 = 0x0000fffd546f8618   x26 = 0x0000fffd6f6dc0a8
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd546f9040
     fp = 0x0000fffd546f85a0    sp = 0x0000fffd546f85a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffdc57ed030   x20 = 0x0000fffd6f8cd9c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffeefbf7fa0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd546f9740   x26 = 0x0000fffd546f9040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd546f9040
     fp = 0x0000fffd546f8650    sp = 0x0000fffd546f8640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd546f9458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd546f9740   x26 = 0x0000fffd546f9040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd546f9040
     fp = 0x0000fffd546f8820    sp = 0x0000fffd546f8820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 93
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000bc    x1 = 0x0000fffd6fcc5220
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd6f720ae0    x5 = 0x0000fffd53e2f3c4
     x6 = 0x0000000000000000    x7 = 0x0000fffd53e2eb50
     x8 = 0x00000000000000cf    x9 = 0x00000000c2040000
    x10 = 0x001803065af10b65   x11 = 0xc2040000c7040000
    x12 = 0x0000001100000000   x13 = 0x000000c900000000
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000000c9ccf0   x19 = 0x00000000000000bc
    x20 = 0x0000fffd53e2f3c4   x21 = 0x0000fffd6f720ae0
    x22 = 0x0000000000010000   x23 = 0x0000fffd6fcc5220
    x24 = 0x0000fffd53e30740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd6f983d90
    x28 = 0x0000fffd53e30040    fp = 0x0000fffd53e2f350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd53e2f350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd53e2f3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd53e2f360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd53e2f400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd53e2f3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd773b6ca0   x20 = 0x0000fffd53e2f608
     fp = 0x0000fffd53e2f520    sp = 0x0000fffd53e2f430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffdc57ed060   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd6f8cd9e0   x22 = 0x0000fffd773b6b80
    x23 = 0x0000fffd77327840   x24 = 0x0000fffd53e2f608
    x25 = 0x0000fffd53e2f618   x26 = 0x0000fffd6fac3ad0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd53e30040
     fp = 0x0000fffd53e2f5a0    sp = 0x0000fffd53e2f5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074e1c20   x20 = 0x0000fffd6f8cda20
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffdc57ed060   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd53e30740   x26 = 0x0000fffd53e30040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd53e30040
     fp = 0x0000fffd53e2f650    sp = 0x0000fffd53e2f640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd53e30458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd53e30740   x26 = 0x0000fffd53e30040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd53e30040
     fp = 0x0000fffd53e2f820    sp = 0x0000fffd53e2f820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 94
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000be    x1 = 0x0000fffd534556a0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd533d7020    x5 = 0x0000fffd527d63c4
     x6 = 0x0000000000000000    x7 = 0x000000000003be42
     x8 = 0x00000000000000cf    x9 = 0x000000000003be42
    x10 = 0x0000000000000017   x11 = 0x0013404706b96bca
    x12 = 0x000000037861c92c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000003   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000000f0dfe0   x19 = 0x00000000000000be
    x20 = 0x0000fffd527d63c4   x21 = 0x0000fffd533d7020
    x22 = 0x0000000000010000   x23 = 0x0000fffd534556a0
    x24 = 0x0000fffd527d7740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd6f988ef0
    x28 = 0x0000fffd527d7040    fp = 0x0000fffd527d6350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd527d6350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd527d63a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd527d6360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd527d6400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd527d63b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd77504220   x20 = 0x0000fffd527d6608
     fp = 0x0000fffd527d6520    sp = 0x0000fffd527d6430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffd6f883f40   x20 = 0x0000fffdc5220800
    x21 = 0x0000fffd533dbb20   x22 = 0x0000fffd77504100
    x23 = 0x0000fffd77505840   x24 = 0x0000fffd527d6608
    x25 = 0x0000fffd527d6618   x26 = 0x0000fffd6fac3b18
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd527d7040
     fp = 0x0000fffd527d65a0    sp = 0x0000fffd527d65a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f883f10   x20 = 0x0000fffd533dbb40
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffd6f883f40   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd527d7740   x26 = 0x0000fffd527d7040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd527d7040
     fp = 0x0000fffd527d6650    sp = 0x0000fffd527d6640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd527d7458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a0a2e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a0a2f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd527d7740   x26 = 0x0000fffd527d7040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd527d7040
     fp = 0x0000fffd527d6820    sp = 0x0000fffd527d6820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 95
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd534a0264    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd453444e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001da    x7 = 0x0000fffd45344408
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x001d11f95595a117
    x12 = 0x0000000376fb344c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffd45345a90   x19 = 0x0000000000000000
    x20 = 0x0000fffd534a0264   x21 = 0x0000fffd534a0208
    x22 = 0x0000ffff86fe5000   x23 = 0x00000000000004a9
    x24 = 0x0000fffd453444e8   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000000000000254
    x28 = 0x0000fffd534a0238    fp = 0x0000fffd45344360
     lr = 0x0000ffff86fc8768    sp = 0x0000fffd45344360
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffd45344460    lr = 0x0000ffff874b37f0
     sp = 0x0000fffd45344370    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffd45344580    lr = 0x0000ffff873a7464
     sp = 0x0000fffd45344470    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveGeneratorThread_loop + 0x890
    x19 = 0x0000fffd45344650   x20 = 0x0000ffff874b065c
    x21 = 0x0000fffeefbf7f10   x22 = 0x0000fffd4b331b20
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a8790
    x25 = 0x0000fffd4b331ae0   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffd45345740   x28 = 0x0000fffd45345040
     fp = 0x0000fffd45344590    sp = 0x0000fffd453446a0
     pc = 0x0000ffff873a9024
    Found by: call frame info

Thread 96
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd3818c5c8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd3818c5c8
     x6 = 0x0000000000000000    x7 = 0x0000fffe7d1a36f8
     x8 = 0x0000000000000062    x9 = 0x0000ffff88629e40
    x10 = 0x0000000000000001   x11 = 0x0000fffe7d1a4750
    x12 = 0x0000000000000010   x13 = 0x0000000000005f10
    x14 = 0x0000000000010000   x15 = 0x0000000000005f20
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000000003648   x19 = 0x0000000000000000
    x20 = 0x0000fffd3818c570   x21 = 0x0000fffd3818c5b0
    x22 = 0x0000000000000008   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000004   x27 = 0x0000fffd3818c5c8
    x28 = 0x0000fffd3818c5a0    fp = 0x0000fffe7d1a3670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffe7d1a3670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffe7d1a3740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffe7d1a3680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffe7d1a3750    lr = 0x0000ffff868507ac
     sp = 0x0000fffe7d1a3750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffe7d1a3800    sp = 0x0000fffe7d1a3760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffe7c9a16bc
    x19 = 0x0000fffe7c9a16be   x20 = 0x0000ffff86fe5000
     fp = 0x0000fffd381a45c0    sp = 0x0000fffe7d1a3780
     pc = 0x0000fffe7c9a16c0
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffe7d1a3790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::ThreadPool::ThreadPool(unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x14
     sp = 0x0000fffe7d1a37b0    pc = 0x0000ffff86850870
    Found by: stack scanning
 7  0xfffe7c9a15fd
    x19 = 0x0000fffd047b9c40    fp = 0x0000fffd3818c570
     sp = 0x0000fffe7d1a37d0    pc = 0x0000fffe7c9a1601
    Found by: call frame info
 8  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     sp = 0x0000fffe7d1a37e0    pc = 0x0000ffff868c4900
    Found by: stack scanning
 9  libstdc++.so.6!std::chrono::_V2::system_clock::now() + 0x24
     sp = 0x0000fffe7d1a37f0    pc = 0x0000ffff86d4b108
    Found by: stack scanning
10  libtimer.so.3!lios::timer::TimerBase::IsPeriodic() const + 0xc
     fp = 0x0000000000000000    sp = 0x0000fffe7d1a3820
     pc = 0x0000ffff868c4900
    Found by: call frame info

Thread 97
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd3d8b41d8    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd3d8b41d8
     x6 = 0x0000000000000000    x7 = 0x0000fffee39fb6f8
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000038
    x12 = 0x0000ffff86c90dc0   x13 = 0x0000000000000000
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000000070c5a798   x19 = 0x0000000000000000
    x20 = 0x0000fffd3d8b4180   x21 = 0x0000fffd3d8b41c0
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd3d8b41d8
    x28 = 0x0000fffd3d8b41b0    fp = 0x0000fffee39fb670
     lr = 0x0000ffff86fc8400    sp = 0x0000fffee39fb670
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffee39fb740    lr = 0x0000ffff86d4e660
     sp = 0x0000fffee39fb680    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libstdc++.so.6!std::condition_variable::wait(std::unique_lock<std::mutex>&) + 0xc
     fp = 0x0000fffee39fb750    lr = 0x0000ffff7e65290c
     sp = 0x0000fffee39fb750    pc = 0x0000ffff86d4e660
    Found by: previous frame's frame pointer
 3  libstdc++.so.6!std::error_code::default_error_condition() const + 0x38
     fp = 0x0000fffee39fb800    sp = 0x0000fffee39fb760
     pc = 0x0000ffff86d54f9c
    Found by: call frame info
 4  0xfffe7c9a1d5c
    x19 = 0x0000fffe7c9a1d5e   x20 = 0x0000ffff86fe5000
     fp = 0x0000fffd384c0130    sp = 0x0000fffee39fb780
     pc = 0x0000fffe7c9a1d60
    Found by: call frame info
 5  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffee39fb790    pc = 0x0000ffff86d54f80
    Found by: stack scanning
 6  libpthread.so.0 + 0xf99c
     sp = 0x0000fffee39fb7c8    pc = 0x0000ffff86fc99a0
    Found by: stack scanning
 7  libpthread.so.0 + 0x7568
     sp = 0x0000fffee39fb7d0    pc = 0x0000ffff86fc156c
    Found by: stack scanning
 8  libc.so.6 + 0xef9fd
     sp = 0x0000fffee39fb7e0    pc = 0x0000ffff86be0a01
    Found by: stack scanning
 9  libnvstream_core_pubsub.so!linvs::helper::HelperConsumerConfigBase::GetConsumerConfigBase() const + 0xc
     sp = 0x0000fffee39fb7f8    pc = 0x0000ffff7e64f2d0
    Found by: stack scanning
10  libnvstream_core_pubsub.so!std::_Sp_counted_ptr_inplace<linvs::stream::StreamData<linvs::ps::PacketMsg>, std::allocator<linvs::stream::StreamData<linvs::ps::PacketMsg> >, (__gnu_cxx::_Lock_policy)2>::_M_destroy() + 0xc
     sp = 0x0000fffee39fb800    pc = 0x0000ffff7e64f560
    Found by: stack scanning
11  libpthread.so.0 + 0x7620
     sp = 0x0000fffee39fb810    pc = 0x0000ffff86fc1624
    Found by: stack scanning
12  libc.so.6 + 0xd1668
     sp = 0x0000fffee39fb830    pc = 0x0000ffff86bc266c
    Found by: stack scanning
13  libstdc++.so.6!std::error_code::default_error_condition() const + 0x1c
     sp = 0x0000fffee39fb890    pc = 0x0000ffff86d54f80
    Found by: stack scanning

Thread 98
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4aee0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4aee0
     x6 = 0x0000000000000000    x7 = 0x0000fffd6bdf9478
     x8 = 0x0000000000000062    x9 = 0x0000fffd6bdf9350
    x10 = 0x0000000000000000   x11 = 0x0000fffd6bdf8f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4ae88   x21 = 0x0000fffd77e4aec8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4aee0
    x28 = 0x0000fffd77e4aeb8    fp = 0x0000fffd6bdf93f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd6bdf93f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd6bdf94d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd6bdf9400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd6bdf95f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd6bdf94e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bc0b0   x20 = 0x0000fffdcb630780
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53a30   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6bdfa740   x26 = 0x0000fffd6bdfa040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6bdf9650    sp = 0x0000fffd6bdf9710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd6bdfa458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6bdfa740   x26 = 0x0000fffd6bdfa040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6bdf9820    sp = 0x0000fffd6bdf9820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 99
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4ade0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4ade0
     x6 = 0x0000000000000000    x7 = 0x0000fffd6c5fa478
     x8 = 0x0000000000000062    x9 = 0x0000fffd6c5fa350
    x10 = 0x0000000000000000   x11 = 0x0000fffd6c5f9f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4ad88   x21 = 0x0000fffd77e4adc8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4ade0
    x28 = 0x0000fffd77e4adb8    fp = 0x0000fffd6c5fa3f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd6c5fa3f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd6c5fa4d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd6c5fa400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd6c5fa5f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd6c5fa4e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bbf30   x20 = 0x0000fffd77105280
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e538c0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6c5fb740   x26 = 0x0000fffd6c5fb040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6c5fa650    sp = 0x0000fffd6c5fa710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd6c5fb458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf73e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf73f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6c5fb740   x26 = 0x0000fffd6c5fb040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6c5fa820    sp = 0x0000fffd6c5fa820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 100
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4aae0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4aae0
     x6 = 0x0000000000000000    x7 = 0x0000fffd70d37478
     x8 = 0x0000000000000062    x9 = 0x0000fffd70d37350
    x10 = 0x0000000000000000   x11 = 0x0000fffd70d36f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4aa88   x21 = 0x0000fffd77e4aac8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4aae0
    x28 = 0x0000fffd77e4aab8    fp = 0x0000fffd70d373f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd70d373f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd70d374d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd70d37400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd70d375f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd70d374e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bb750   x20 = 0x0000fffdcb692be0
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53470   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd70d38740   x26 = 0x0000fffd70d38040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd70d37650    sp = 0x0000fffd70d37710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd70d38458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a15ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a15af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd70d38740   x26 = 0x0000fffd70d38040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd70d37820    sp = 0x0000fffd70d37820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 101
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000bf    x1 = 0x0000fffd4b8dc0a0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd5349f4e0    x5 = 0x0000fffd4425e3c4
     x6 = 0x0000000000000000    x7 = 0x0000fffd4425db50
     x8 = 0x00000000000000cf    x9 = 0x00000000c2040000
    x10 = 0x00180306fa4d1d54   x11 = 0xc2040000c7040000
    x12 = 0x0000000100000000   x13 = 0x0000011f00000000
    x14 = 0x0000000000000000   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x000000000059b920   x19 = 0x00000000000000bf
    x20 = 0x0000fffd4425e3c4   x21 = 0x0000fffd5349f4e0
    x22 = 0x0000000000010000   x23 = 0x0000fffd4b8dc0a0
    x24 = 0x0000fffd4425f740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd4b2a13b0
    x28 = 0x0000fffd4425f040    fp = 0x0000fffd4425e350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd4425e350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd4425e3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd4425e360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd4425e400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd4425e3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffeeaa99220   x20 = 0x0000fffd4425e608
     fp = 0x0000fffd4425e520    sp = 0x0000fffd4425e430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffd6f716140   x20 = 0x0000fffd534ebc00
    x21 = 0x0000fffd4b098a00   x22 = 0x0000fffeeaa99100
    x23 = 0x0000fffeeaa9a900   x24 = 0x0000fffd4425e608
    x25 = 0x0000fffd4425e618   x26 = 0x0000fffd532917d0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd4425f040
     fp = 0x0000fffd4425e5a0    sp = 0x0000fffd4425e5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f716110   x20 = 0x0000fffd4b0989e0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffd6f716140   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd4425f740   x26 = 0x0000fffd4425f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd4425f040
     fp = 0x0000fffd4425e650    sp = 0x0000fffd4425e640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd4425f458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16be   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16bf   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd4425f740   x26 = 0x0000fffd4425f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd4425f040
     fp = 0x0000fffd4425e820    sp = 0x0000fffd4425e820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 102
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4a7e0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4a7e0
     x6 = 0x0000000000000000    x7 = 0x0000fffd74393478
     x8 = 0x0000000000000062    x9 = 0x0000fffd74393350
    x10 = 0x0000000000000000   x11 = 0x0000fffd74392f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4a788   x21 = 0x0000fffd77e4a7c8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4a7e0
    x28 = 0x0000fffd77e4a7b8    fp = 0x0000fffd743933f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd743933f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd743934d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd74393400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd743935f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd743934e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76a0da80   x20 = 0x0000fffd77e3c080
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53020   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd74394740   x26 = 0x0000fffd74394040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd74393650    sp = 0x0000fffd74393710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd74394458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd74394740   x26 = 0x0000fffd74394040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd74393820    sp = 0x0000fffd74393820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 103
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd43194370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000040   x15 = 0x0000fffd43195a88
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000fffd43195a90   x19 = 0x0000fffd431944a4
    x20 = 0x0000fffd4b0bc798   x21 = 0x0000000000000001
    x22 = 0x0000fffd43194370   x23 = 0x0000000000000001
    x24 = 0x0000fffd43195040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd43194424   x27 = 0x0000fffd4b2a1490
    x28 = 0x0000000000000000    fp = 0x0000fffd43194320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd43194310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd43194400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd43194330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000fffd43195758
    x21 = 0x02028001431944e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd43194520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7d093960   x26 = 0x0000fffd43194608
     fp = 0x0000fffd43194430    sp = 0x0000fffd431944e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7d093960   x20 = 0x0000fffd43194608
    x21 = 0x0000fffd4b2a0d90   x22 = 0x0000fffd43194618
    x23 = 0x0000fffd4b2a1490   x24 = 0x0000000000000000
    x25 = 0x0000fffd53291860   x26 = 0x0000ffff7d093998
    x27 = 0x00000000000002db   x28 = 0x0000fffd43195040
     fp = 0x0000fffd43194520    sp = 0x0000fffd43194520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffdc5230a50   x20 = 0x0000fffd534ebc00
    x21 = 0x0000fffd4b098980   x22 = 0x0000ffff7d093840
    x23 = 0x0000ffff7d099000   x24 = 0x0000fffd43194608
    x25 = 0x0000fffd43194618   x26 = 0x0000fffd53291860
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd43195040
     fp = 0x0000fffd431945a0    sp = 0x0000fffd431945a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f716200   x20 = 0x0000fffd4b098960
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffdc5230a50   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd43195740   x26 = 0x0000fffd43195040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd43195040
     fp = 0x0000fffd43194650    sp = 0x0000fffd43194640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd43195458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd43195740   x26 = 0x0000fffd43195040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd43195040
     fp = 0x0000fffd43194820    sp = 0x0000fffd43194820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 104
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd73b92370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x00000000000553a0
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000ffffffc0
    x10 = 0x00000000000553a0   x11 = 0x0000000000000015
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000001   x15 = 0x7f343365cb03d68e
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000fffd73b93a90   x19 = 0x0000fffd73b924a4
    x20 = 0x0000ffff07267e98   x21 = 0x0000000000000001
    x22 = 0x0000fffd73b92370   x23 = 0x0000000000000001
    x24 = 0x0000fffd73b93040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd73b92424   x27 = 0x0000fffdc593d510
    x28 = 0x0000000000000000    fp = 0x0000fffd73b92320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd73b92310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd73b92400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd73b92330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000fffd73b92608
    x21 = 0x0202800173b924e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd73b92520   x24 = 0x0000ffff873903cc
    x25 = 0x0000fffd6b2eb300   x26 = 0x0000fffd73b92608
     fp = 0x0000fffd73b92430    sp = 0x0000fffd73b924e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd6b2eb300   x20 = 0x0000fffd73b92608
    x21 = 0x0000ffff074a3cb0   x22 = 0x0000fffd73b92618
    x23 = 0x0000fffdc593d510   x24 = 0x0000000000000000
    x25 = 0x0000fffeefd7b398   x26 = 0x0000fffd6b2eb338
    x27 = 0x0000000000000173   x28 = 0x0000fffd73b93040
     fp = 0x0000fffd73b92520    sp = 0x0000fffd73b92520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffdc57eec20   x20 = 0x0000ffff07488800
    x21 = 0x0000fffdc5110860   x22 = 0x0000fffd6b2eb1e0
    x23 = 0x0000fffd6aa63e80   x24 = 0x0000fffd73b92608
    x25 = 0x0000fffd73b92618   x26 = 0x0000fffeefd7b398
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd73b93040
     fp = 0x0000fffd73b925a0    sp = 0x0000fffd73b925a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeefbf67d0   x20 = 0x0000fffdc5110880
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffdc57eec20   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd73b93740   x26 = 0x0000fffd73b93040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd73b93040
     fp = 0x0000fffd73b92650    sp = 0x0000fffd73b92640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd73b93458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a0a2e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a0a2f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd73b93740   x26 = 0x0000fffd73b93040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd73b93040
     fp = 0x0000fffd73b92820    sp = 0x0000fffd73b92820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 105
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000ab    x1 = 0x0000fffdc98b0660
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff0750f2a0    x5 = 0x0000fffdc825a3c4
     x6 = 0x0000000000000000    x7 = 0x000000004617d47f
     x8 = 0x00000000000000cf    x9 = 0x000000000003bd58
    x10 = 0x0000000000000017   x11 = 0x000816e16f2060a9
    x12 = 0x000000037788591c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000fffeef1a4fb8   x19 = 0x00000000000000ab
    x20 = 0x0000fffdc825a3c4   x21 = 0x0000ffff0750f2a0
    x22 = 0x0000000000010000   x23 = 0x0000fffdc98b0660
    x24 = 0x0000fffdc825b740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff074a42d0
    x28 = 0x0000fffdc825b040    fp = 0x0000fffdc825a350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdc825a350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdc825a3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdc825a360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdc825a400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdc825a3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7d08e3e0   x20 = 0x0000fffdc825a608
     fp = 0x0000fffdc825a520    sp = 0x0000fffdc825a430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff074cc560   x20 = 0x0000ffff07488800
    x21 = 0x0000ffff074788e0   x22 = 0x0000ffff7d08e2c0
    x23 = 0x0000ffff7d096000   x24 = 0x0000fffdc825a608
    x25 = 0x0000fffdc825a618   x26 = 0x0000ffff073cb518
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdc825b040
     fp = 0x0000fffdc825a5a0    sp = 0x0000fffdc825a5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074cc530   x20 = 0x0000ffff074788c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff074cc560   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc825b740   x26 = 0x0000fffdc825b040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc825b040
     fp = 0x0000fffdc825a650    sp = 0x0000fffdc825a640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc825b458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a176e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a176f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc825b740   x26 = 0x0000fffdc825b040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc825b040
     fp = 0x0000fffdc825a820    sp = 0x0000fffdc825a820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 106
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffd44b43610    x1 = 0x0000000000000001
     x2 = 0x0000fffd44b435b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000000000000028
     x8 = 0x0000000000000049    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000005555556   x11 = 0x0000005f00000be0
    x12 = 0x0000fffd44b44768   x13 = 0x0000fffd44b44770
    x14 = 0x0000fffd44b44aa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000fffd44b43610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffd44b44740
    x22 = 0x00000000000000c1   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000fffd44b44740
    x26 = 0x0000fffd44b44040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000fffd44b44040    fp = 0x0000fffd44b43570
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffd44b43570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffd44b435d0    lr = 0x0000ffff874ad15c
     sp = 0x0000fffd44b43580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000fffd44b43620    lr = 0x0000ffff874b0188
     sp = 0x0000fffd44b435e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f7161d0   x20 = 0x0000fffd4b098c40
    x21 = 0x0000000000000000   x22 = 0x0000000000000000
    x23 = 0x0000fffd44b43820    fp = 0x0000fffd44b43650
     sp = 0x0000fffd44b43670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd44b44458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a1bde   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a1bdf    fp = 0x0000fffd44b43820
     sp = 0x0000fffd44b43820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 107
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000c5    x1 = 0x0000fffd4b8fcea0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd5349f520    x5 = 0x0000fffd43a5d3c4
     x6 = 0x0000000000000000    x7 = 0x00000000bbac8b9b
     x8 = 0x00000000000000cf    x9 = 0x000000000003be44
    x10 = 0x0000000000000017   x11 = 0x0015cc6717931102
    x12 = 0x000000037863b174   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000d3f00   x19 = 0x00000000000000c5
    x20 = 0x0000fffd43a5d3c4   x21 = 0x0000fffd5349f520
    x22 = 0x0000000000010000   x23 = 0x0000fffd4b8fcea0
    x24 = 0x0000fffd43a5e740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd4b2a1420
    x28 = 0x0000fffd43a5e040    fp = 0x0000fffd43a5d350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd43a5d350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd43a5d3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd43a5d360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd43a5d400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd43a5d3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd6c7cc060   x20 = 0x0000fffd43a5d608
     fp = 0x0000fffd43a5d520    sp = 0x0000fffd43a5d430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffd6f7160e0   x20 = 0x0000fffd534ebc00
    x21 = 0x0000fffd4b0989c0   x22 = 0x0000ffff82585f40
    x23 = 0x0000fffd6c7cf000   x24 = 0x0000fffd43a5d608
    x25 = 0x0000fffd43a5d618   x26 = 0x0000fffd53291818
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd43a5e040
     fp = 0x0000fffd43a5d5a0    sp = 0x0000fffd43a5d5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f7160b0   x20 = 0x0000fffd4b0989a0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffd6f7160e0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd43a5e740   x26 = 0x0000fffd43a5e040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd43a5e040
     fp = 0x0000fffd43a5d650    sp = 0x0000fffd43a5d640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd43a5e458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd43a5e740   x26 = 0x0000fffd43a5e040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd43a5e040
     fp = 0x0000fffd43a5d820    sp = 0x0000fffd43a5d820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 108
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4ace0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4ace0
     x6 = 0x0000000000000000    x7 = 0x0000fffd6f535478
     x8 = 0x0000000000000062    x9 = 0x0000fffd6f535350
    x10 = 0x0000000000000000   x11 = 0x0000fffd6f534f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4ac88   x21 = 0x0000fffd77e4acc8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4ace0
    x28 = 0x0000fffd77e4acb8    fp = 0x0000fffd6f5353f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd6f5353f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd6f5354d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd6f535400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd6f5355f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd6f5354e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bd2b0   x20 = 0x0000fffdaa8a5f80
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53750   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6f536740   x26 = 0x0000fffd6f536040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6f535650    sp = 0x0000fffd6f535710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd6f536458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a15ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a15af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd6f536740   x26 = 0x0000fffd6f536040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd6f535820    sp = 0x0000fffd6f535820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 109
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77123160    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77123160
     x6 = 0x0000000000000000    x7 = 0x0000fffef29c2478
     x8 = 0x0000000000000062    x9 = 0x0000fffef29c2350
    x10 = 0x0000000000000000   x11 = 0x0000fffef29c1f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77123108   x21 = 0x0000fffd77123148
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77123160
    x28 = 0x0000fffd77123138    fp = 0x0000fffef29c23f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffef29c23f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffef29c24d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffef29c2400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffef29c25f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffef29c24e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bd970   x20 = 0x0000fffd770671c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77836820   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffef29c3740   x26 = 0x0000fffef29c3040
    x27 = 0x0000fffef29c27c0   x28 = 0x0000fffef29c27a0
     fp = 0x0000fffef29c2650    sp = 0x0000fffef29c2710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffef29c3458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a15ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a15af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffef29c3740   x26 = 0x0000fffef29c3040
    x27 = 0x0000fffef29c27c0   x28 = 0x0000fffef29c27a0
     fp = 0x0000fffef29c2820    sp = 0x0000fffef29c2820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 110
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff76a383e0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffef31c3518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d4    x7 = 0x0000fffef31c3438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000c6183712d196d
    x12 = 0x000000036ac3270c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffef31c4a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a383e0   x21 = 0x0000ffff76a38388
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000010
    x24 = 0x0000fffef31c3518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000008
    x28 = 0x0000ffff76a383b8    fp = 0x0000fffef31c3390
     lr = 0x0000ffff86fc8768    sp = 0x0000fffef31c3390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffef31c3490    lr = 0x0000ffff874b37f0
     sp = 0x0000fffef31c33a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffef31c35b0    lr = 0x0000ffff873a7464
     sp = 0x0000fffef31c34a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000fffef31c3650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff76a0d9c0   x22 = 0x0000ffff76a37180
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000ffff76a0d5a0   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffef31c4740   x28 = 0x0000fffef31c4aa0
     fp = 0x0000fffef31c35c0    sp = 0x0000fffef31c36d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 111
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4a8e0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4a8e0
     x6 = 0x0000000000000000    x7 = 0x0000fffd732c9478
     x8 = 0x0000000000000062    x9 = 0x0000fffd732c9350
    x10 = 0x0000000000000000   x11 = 0x0000fffd732c8f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4a888   x21 = 0x0000fffd77e4a8c8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4a8e0
    x28 = 0x0000fffd77e4a8b8    fp = 0x0000fffd732c93f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd732c93f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd732c94d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd732c9400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd732c95f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd732c94e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bba20   x20 = 0x0000fffdcb6393a0
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53190   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd732ca740   x26 = 0x0000fffd732ca040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd732c9650    sp = 0x0000fffd732c9710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd732ca458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd732ca740   x26 = 0x0000fffd732ca040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd732c9820    sp = 0x0000fffd732c9820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 112
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff74ffd370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000040   x15 = 0x0000ffff74ffea88
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000ffff74ffea90   x19 = 0x0000ffff74ffd4a4
    x20 = 0x0000ffff72bf5618   x21 = 0x0000000000000001
    x22 = 0x0000ffff74ffd370   x23 = 0x0000000000000001
    x24 = 0x0000ffff74ffe040   x25 = 0x0000ffff875525a0
    x26 = 0x0000ffff74ffd424   x27 = 0x0000ffff72d876f0
    x28 = 0x0000000000000000    fp = 0x0000ffff74ffd320
     lr = 0x0000ffff874b754c    sp = 0x0000ffff74ffd310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000ffff74ffd400    lr = 0x0000ffff874b7c58
     sp = 0x0000ffff74ffd330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000ffff74ffe758
    x21 = 0x0202800174ffd4e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000ffff74ffd520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7d00a2a0   x26 = 0x0000ffff74ffd608
     fp = 0x0000ffff74ffd430    sp = 0x0000ffff74ffd4e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7d00a2a0   x20 = 0x0000ffff74ffd608
    x21 = 0x0000ffff72d87060   x22 = 0x0000ffff74ffd618
    x23 = 0x0000ffff72d876f0   x24 = 0x0000000000000000
    x25 = 0x0000ffff72dea618   x26 = 0x0000ffff7d00a2d8
    x27 = 0x0000000000000083   x28 = 0x0000ffff74ffe040
     fp = 0x0000ffff74ffd520    sp = 0x0000ffff74ffd520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff72d07780   x20 = 0x0000ffff72bb8800
    x21 = 0x0000ffff72c622a0   x22 = 0x0000ffff7d00a180
    x23 = 0x0000ffff7d004000   x24 = 0x0000ffff74ffd608
    x25 = 0x0000ffff74ffd618   x26 = 0x0000ffff72dea618
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff74ffe040
     fp = 0x0000ffff74ffd5a0    sp = 0x0000ffff74ffd5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff72d07720   x20 = 0x0000ffff72c62280
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff72d07780   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff74ffe740   x26 = 0x0000ffff74ffe040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff74ffe040
     fp = 0x0000ffff74ffd650    sp = 0x0000ffff74ffd640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff74ffe458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fb69e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fb69f   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff74ffe740   x26 = 0x0000ffff74ffe040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff74ffe040
     fp = 0x0000ffff74ffd820    sp = 0x0000ffff74ffd820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 113
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff76a9b9e0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff76a9b9e0
     x6 = 0x0000000000000000    x7 = 0x0000fffdc44c6478
     x8 = 0x0000000000000062    x9 = 0x0000fffdc44c6350
    x10 = 0x0000000000000000   x11 = 0x0000fffdc44c5f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a9b988   x21 = 0x0000ffff76a9b9c8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff76a9b9e0
    x28 = 0x0000ffff76a9b9b8    fp = 0x0000fffdc44c63f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffdc44c63f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffdc44c64d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffdc44c6400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffdc44c65f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffdc44c64e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074e19e0   x20 = 0x0000ffff07440780
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000ffff76b1ef90   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc44c7740   x26 = 0x0000fffdc44c7040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdc44c6650    sp = 0x0000fffdc44c6710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc44c7458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a071e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a071f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc44c7740   x26 = 0x0000fffdc44c7040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdc44c6820    sp = 0x0000fffdc44c6820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 114
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffdcfb27370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000001640
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000ffff84c8
    x10 = 0x0000000000001640   x11 = 0x000000000000000e
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x7f343365cb03d68e
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x00000000002aadc0   x19 = 0x0000fffdcfb274a4
    x20 = 0x0000ffff76ae1ed8   x21 = 0x0000000000000001
    x22 = 0x0000fffdcfb27370   x23 = 0x0000000000000001
    x24 = 0x0000fffdcfb28040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffdcfb27424   x27 = 0x0000fffddae8ff80
    x28 = 0x0000000000000000    fp = 0x0000fffdcfb27320
     lr = 0x0000ffff874b754c    sp = 0x0000fffdcfb27310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffdcfb27400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffdcfb27330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000fffdcfb27608
    x21 = 0x02028001cfb274e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffdcfb27520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7367c220   x26 = 0x0000fffdcfb27608
     fp = 0x0000fffdcfb27430    sp = 0x0000fffdcfb274e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7367c220   x20 = 0x0000fffdcfb27608
    x21 = 0x0000ffff76a02460   x22 = 0x0000fffdcfb27618
    x23 = 0x0000fffddae8ff80   x24 = 0x0000000000000000
    x25 = 0x0000ffff76ab3dd0   x26 = 0x0000ffff7367c258
    x27 = 0x00000000000000fb   x28 = 0x0000fffdcfb28040
     fp = 0x0000fffdcfb27520    sp = 0x0000fffdcfb27520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff76ad5130   x20 = 0x0000ffff76b0d400
    x21 = 0x0000fffddaed68a0   x22 = 0x0000ffff7367c100
    x23 = 0x0000ffff737b8600   x24 = 0x0000fffdcfb27608
    x25 = 0x0000fffdcfb27618   x26 = 0x0000ffff76ab3dd0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdcfb28040
     fp = 0x0000fffdcfb275a0    sp = 0x0000fffdcfb275a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76a0d7b0   x20 = 0x0000fffddaed68e0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff76ad5130   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdcfb28740   x26 = 0x0000fffdcfb28040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdcfb28040
     fp = 0x0000fffdcfb27650    sp = 0x0000fffdcfb27640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdcfb28458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c077e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c077f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdcfb28740   x26 = 0x0000fffdcfb28040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdcfb28040
     fp = 0x0000fffdcfb27820    sp = 0x0000fffdcfb27820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 115
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff76a9bae0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff76a9bae0
     x6 = 0x0000000000000000    x7 = 0x0000fffdc3bfd478
     x8 = 0x0000000000000062    x9 = 0x0000fffdc3bfd350
    x10 = 0x0000000000000000   x11 = 0x0000fffdc3bfcf88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a9ba88   x21 = 0x0000ffff76a9bac8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff76a9bae0
    x28 = 0x0000ffff76a9bab8    fp = 0x0000fffdc3bfd3f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffdc3bfd3f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffdc3bfd4d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffdc3bfd400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffdc3bfd5f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffdc3bfd4e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeeffb4060   x20 = 0x0000fffef0003520
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000ffff76b1f100   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc3bfe740   x26 = 0x0000fffdc3bfe040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdc3bfd650    sp = 0x0000fffdc3bfd710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc3bfe458   x20 = 0x0000000000000000
    x21 = 0x0000fffef21be76e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef21be76f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc3bfe740   x26 = 0x0000fffdc3bfe040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdc3bfd820    sp = 0x0000fffdc3bfd820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 116
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd4b3334e0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd45b45518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d5    x7 = 0x0000fffd45b45438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0002f69fc277b0a1
    x12 = 0x000000036c094f24   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffd45b46a90   x19 = 0x0000000000000000
    x20 = 0x0000fffd4b3334e0   x21 = 0x0000fffd4b333488
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000048
    x24 = 0x0000fffd45b45518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000024
    x28 = 0x0000fffd4b3334b8    fp = 0x0000fffd45b45390
     lr = 0x0000ffff86fc8768    sp = 0x0000fffd45b45390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffd45b45490    lr = 0x0000ffff874b37f0
     sp = 0x0000fffd45b453a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffd45b455b0    lr = 0x0000ffff873a7464
     sp = 0x0000fffd45b454a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000fffd45b45650   x20 = 0x0000ffff874b065c
    x21 = 0x0000fffd6f715d50   x22 = 0x0000fffd4b3317e0
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000fffdc5230210   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffd45b46740   x28 = 0x0000fffd45b46aa0
     fp = 0x0000fffd45b455c0    sp = 0x0000fffd45b456d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 117
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4abe0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4abe0
     x6 = 0x0000000000000000    x7 = 0x0000fffd70536478
     x8 = 0x0000000000000062    x9 = 0x0000fffd70536350
    x10 = 0x0000000000000000   x11 = 0x0000fffd70535f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4ab88   x21 = 0x0000fffd77e4abc8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4abe0
    x28 = 0x0000fffd77e4abb8    fp = 0x0000fffd705363f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd705363f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd705364d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd70536400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd705365f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd705364e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bcbc0   x20 = 0x0000fffdcb6b7c40
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e535e0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd70537740   x26 = 0x0000fffd70537040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd70536650    sp = 0x0000fffd70536710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd70537458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a15ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a15af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd70537740   x26 = 0x0000fffd70537040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd70536820    sp = 0x0000fffd70536820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 118
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff76a9bbe0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff76a9bbe0
     x6 = 0x0000000000000000    x7 = 0x0000fffd75dec478
     x8 = 0x0000000000000062    x9 = 0x0000fffd75dec350
    x10 = 0x0000000000000000   x11 = 0x0000fffd75debf88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a9bb88   x21 = 0x0000ffff76a9bbc8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff76a9bbe0
    x28 = 0x0000ffff76a9bbb8    fp = 0x0000fffd75dec3f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd75dec3f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd75dec4d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd75dec400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd75dec5f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd75dec4e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffddb1cf770   x20 = 0x0000fffde29bea80
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000ffff76b1f270   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd75ded740   x26 = 0x0000fffd75ded040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd75dec650    sp = 0x0000fffd75dec710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd75ded458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd75ded740   x26 = 0x0000fffd75ded040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd75dec820    sp = 0x0000fffd75dec820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 119
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffd42993370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x00000000000b8490
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000fffffdd8
    x10 = 0x00000000000b8490   x11 = 0x0000000000000038
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0xd3db42c987cf341e
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x00000000000a0340   x19 = 0x0000fffd429934a4
    x20 = 0x0000fffd4b0bc6d8   x21 = 0x0000000000000001
    x22 = 0x0000fffd42993370   x23 = 0x0000000000000001
    x24 = 0x0000fffd42994040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffd42993424   x27 = 0x0000fffd4b2a1500
    x28 = 0x0000000000000000    fp = 0x0000fffd42993320
     lr = 0x0000ffff874b754c    sp = 0x0000fffd42993310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffd42993400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffd42993330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000fffd42993608
    x21 = 0x02028001429934e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffd42993520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff04547ac0   x26 = 0x0000fffd42993608
     fp = 0x0000fffd42993430    sp = 0x0000fffd429934e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff04547ac0   x20 = 0x0000fffd42993608
    x21 = 0x0000fffd4b2a0d90   x22 = 0x0000fffd42993618
    x23 = 0x0000fffd4b2a1500   x24 = 0x0000000000000000
    x25 = 0x0000fffd532918a8   x26 = 0x0000ffff04547af8
    x27 = 0x00000000000002db   x28 = 0x0000fffd42994040
     fp = 0x0000fffd42993520    sp = 0x0000fffd42993520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffd6f716290   x20 = 0x0000fffd534ebc00
    x21 = 0x0000fffd4b098c60   x22 = 0x0000ffff045479a0
    x23 = 0x0000ffff04549200   x24 = 0x0000fffd42993608
    x25 = 0x0000fffd42993618   x26 = 0x0000fffd532918a8
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd42994040
     fp = 0x0000fffd429935a0    sp = 0x0000fffd429935a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffd6f7162c0   x20 = 0x0000fffd4b098c80
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffd6f716290   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd42994740   x26 = 0x0000fffd42994040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd42994040
     fp = 0x0000fffd42993650    sp = 0x0000fffd42993640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd42994458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd42994740   x26 = 0x0000fffd42994040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd42994040
     fp = 0x0000fffd42993820    sp = 0x0000fffd42993820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 120
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff54bf9ce4    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff54bf9ce0
     x6 = 0x0000000000000000    x7 = 0x0000ffff3d1fd478
     x8 = 0x0000000000000062    x9 = 0x000000000003a864
    x10 = 0x0000000000000000   x11 = 0x001a170949e02426
    x12 = 0x0000ffff87020ac0   x13 = 0x0000000000000000
    x14 = 0x0000000000024d60   x15 = 0x0000ffff3d1fea88
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x0000ffff3d1fea90   x19 = 0x0000000000000000
    x20 = 0x0000ffff54bf9c88   x21 = 0x0000ffff54bf9ccc
    x22 = 0x0000000000000003   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000ffff54bf9ce4
    x28 = 0x0000ffff54bf9cb8    fp = 0x0000ffff3d1fd3f0
     lr = 0x0000ffff86fc8400    sp = 0x0000ffff3d1fd3f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000ffff3d1fd4d0    lr = 0x0000ffff874b3688
     sp = 0x0000ffff3d1fd400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000ffff3d1fd5f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000ffff3d1fd4e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff7461f1f0   x20 = 0x0000ffff3fde6740
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000ffff54cc2c20   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff3d1fe740   x26 = 0x0000ffff3d1fe040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffff3d1fd650    sp = 0x0000ffff3d1fd710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff3d1fe458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ff8dae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ff8daf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff3d1fe740   x26 = 0x0000ffff3d1fe040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffff3d1fd820    sp = 0x0000ffff3d1fd820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 121
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff76aebe60    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffdc29fc518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d4    x7 = 0x0000fffdc29fc438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0019046054351732
    x12 = 0x000000036b8d54dc   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffdc29fda90   x19 = 0x0000000000000000
    x20 = 0x0000ffff76aebe60   x21 = 0x0000ffff76aebe08
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000004
    x24 = 0x0000fffdc29fc518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000002
    x28 = 0x0000ffff76aebe38    fp = 0x0000fffdc29fc390
     lr = 0x0000ffff86fc8768    sp = 0x0000fffdc29fc390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffdc29fc490    lr = 0x0000ffff874b37f0
     sp = 0x0000fffdc29fc3a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffdc29fc5b0    lr = 0x0000ffff873a7464
     sp = 0x0000fffdc29fc4a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000fffdc29fc650   x20 = 0x0000ffff874b065c
    x21 = 0x0000fffde06b0e40   x22 = 0x0000fffde29c3040
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000fffde06b0bd0   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffdc29fd740   x28 = 0x0000fffdc29fdaa0
     fp = 0x0000fffdc29fc5c0    sp = 0x0000fffdc29fc6d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 122
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000fffd77e4a9e0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000fffd77e4a9e0
     x6 = 0x0000000000000000    x7 = 0x0000fffd72ac8478
     x8 = 0x0000000000000062    x9 = 0x0000fffd72ac8350
    x10 = 0x0000000000000000   x11 = 0x0000fffd72ac7f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000fffd77e4a988   x21 = 0x0000fffd77e4a9c8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000fffd77e4a9e0
    x28 = 0x0000fffd77e4a9b8    fp = 0x0000fffd72ac83f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffd72ac83f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffd72ac84d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffd72ac8400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffd72ac85f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffd72ac84e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29bbcf0   x20 = 0x0000fffdcb692120
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000fffd77e53300   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd72ac9740   x26 = 0x0000fffd72ac9040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd72ac8650    sp = 0x0000fffd72ac8710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd72ac9458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd72ac9740   x26 = 0x0000fffd72ac9040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffd72ac8820    sp = 0x0000fffd72ac8820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 123
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000ac    x1 = 0x0000fffdc98d03e0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff0750f260    x5 = 0x0000fffdc79913c4
     x6 = 0x0000000000000000    x7 = 0x0000fffdc7990b50
     x8 = 0x00000000000000cf    x9 = 0x00000000c2040000
    x10 = 0x00180306a30b5ddc   x11 = 0xc2040000c7040000
    x12 = 0x0000000200000000   x13 = 0x000000d200000000
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000fffeef1a18a8   x19 = 0x00000000000000ac
    x20 = 0x0000fffdc79913c4   x21 = 0x0000ffff0750f260
    x22 = 0x0000000000010000   x23 = 0x0000fffdc98d03e0
    x24 = 0x0000fffdc7992740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff074a4340
    x28 = 0x0000fffdc7992040    fp = 0x0000fffdc7991350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdc7991350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdc79913a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdc7991360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdc7991400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdc79913b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7e8c6900   x20 = 0x0000fffdc7991608
     fp = 0x0000fffdc7991520    sp = 0x0000fffdc7991430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff3c1adce0   x20 = 0x0000ffff07488800
    x21 = 0x0000ffff074788a0   x22 = 0x0000ffff7e8c67e0
    x23 = 0x0000ffff7e8cc000   x24 = 0x0000fffdc7991608
    x25 = 0x0000fffdc7991618   x26 = 0x0000ffff073cb560
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdc7992040
     fp = 0x0000fffdc79915a0    sp = 0x0000fffdc79915a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074cc7a0   x20 = 0x0000ffff07478880
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff3c1adce0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc7992740   x26 = 0x0000fffdc7992040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc7992040
     fp = 0x0000fffdc7991650    sp = 0x0000fffdc7991640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc7992458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a176e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a176f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc7992740   x26 = 0x0000fffdc7992040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc7992040
     fp = 0x0000fffdc7991820    sp = 0x0000fffdc7991820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 124
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000a6    x1 = 0x0000fffddae685e0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff76ad6e60    x5 = 0x0000fffdd20703c4
     x6 = 0x0000000000000000    x7 = 0x000000005aa28242
     x8 = 0x00000000000000cf    x9 = 0x000000000003bd80
    x10 = 0x0000000000000017   x11 = 0x000a793b75fef504
    x12 = 0x0000000377ae7ebc   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000fffdd2071a90   x19 = 0x00000000000000a6
    x20 = 0x0000fffdd20703c4   x21 = 0x0000ffff76ad6e60
    x22 = 0x0000000000010000   x23 = 0x0000fffddae685e0
    x24 = 0x0000fffdd2071740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff76a08350
    x28 = 0x0000fffdd2071040    fp = 0x0000fffdd2070350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdd2070350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdd20703a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdd2070360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdd2070400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdd20703b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffd5701d000   x20 = 0x0000fffdd2070608
     fp = 0x0000fffdd2070520    sp = 0x0000fffdd2070430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff76ad50d0   x20 = 0x0000ffff76b0d400
    x21 = 0x0000ffff76acc600   x22 = 0x0000fffd56e6eee0
    x23 = 0x0000fffd56fa7f40   x24 = 0x0000fffdd2070608
    x25 = 0x0000fffdd2070618   x26 = 0x0000ffff76b1a128
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdd2071040
     fp = 0x0000fffdd20705a0    sp = 0x0000fffdd20705a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76ad5100   x20 = 0x0000ffff76acc620
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff76ad50d0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd2071740   x26 = 0x0000fffdd2071040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd2071040
     fp = 0x0000fffdd2070650    sp = 0x0000fffdd2070640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdd2071458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c077e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c077f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd2071740   x26 = 0x0000fffdd2071040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd2071040
     fp = 0x0000fffdd2070820    sp = 0x0000fffdd2070820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 125
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff3c82a6e0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff6bd11518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d3    x7 = 0x0000ffff6bd11438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000c05f62db335df
    x12 = 0x0000000368e097e4   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff6bd12a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff3c82a6e0   x21 = 0x0000ffff3c82a688
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000000c
    x24 = 0x0000ffff6bd11518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000006
    x28 = 0x0000ffff3c82a6b8    fp = 0x0000ffff6bd11390
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff6bd11390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff6bd11490    lr = 0x0000ffff874b37f0
     sp = 0x0000ffff6bd113a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000ffff6bd115b0    lr = 0x0000ffff873a7464
     sp = 0x0000ffff6bd114a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000ffff6bd11650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff72d07e70   x22 = 0x0000ffff72bf00c0
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000ffff72d084d0   x26 = 0x0000ffff874b0204
    x27 = 0x0000ffff6bd12740   x28 = 0x0000ffff6bd12aa0
     fp = 0x0000ffff6bd115c0    sp = 0x0000ffff6bd116d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 126
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000a8    x1 = 0x0000fffde29d66e0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff76ad6e20    x5 = 0x0000fffddadfb3c4
     x6 = 0x0000000000000000    x7 = 0x0000fffddadfab50
     x8 = 0x00000000000000cf    x9 = 0x00000000c2030000
    x10 = 0x001803062b570851   x11 = 0xc2030000c7030000
    x12 = 0x0000002e00000000   x13 = 0x0000005300000000
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000000456d30   x19 = 0x00000000000000a8
    x20 = 0x0000fffddadfb3c4   x21 = 0x0000ffff76ad6e20
    x22 = 0x0000000000010000   x23 = 0x0000fffde29d66e0
    x24 = 0x0000fffddadfc740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff76a08200
    x28 = 0x0000fffddadfc040    fp = 0x0000fffddadfb350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffddadfb350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffddadfb3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffddadfb360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffddadfb400    lr = 0x0000ffff87399c00
     sp = 0x0000fffddadfb3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff3a479340   x20 = 0x0000fffddadfb608
     fp = 0x0000fffddadfb520    sp = 0x0000fffddadfb430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff76ad5010   x20 = 0x0000ffff76b0d400
    x21 = 0x0000ffff76acc3a0   x22 = 0x0000ffff3a479220
    x23 = 0x0000ffff3a47f6c0   x24 = 0x0000fffddadfb608
    x25 = 0x0000fffddadfb618   x26 = 0x0000ffff76b1a050
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffddadfc040
     fp = 0x0000fffddadfb5a0    sp = 0x0000fffddadfb5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76ad4fe0   x20 = 0x0000ffff76acc380
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff76ad5010   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffddadfc740   x26 = 0x0000fffddadfc040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffddadfc040
     fp = 0x0000fffddadfb650    sp = 0x0000fffddadfb640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffddadfc458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c078e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c078f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffddadfc740   x26 = 0x0000fffddadfc040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffddadfc040
     fp = 0x0000fffddadfb820    sp = 0x0000fffddadfb820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 127
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000fffd6f7184e0    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd581c6518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d5    x7 = 0x0000fffd581c6438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0018a8d2950c671b
    x12 = 0x000000036d647254   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffd581c7a90   x19 = 0x0000000000000000
    x20 = 0x0000fffd6f7184e0   x21 = 0x0000fffd6f718488
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000004
    x24 = 0x0000fffd581c6518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000002
    x28 = 0x0000fffd6f7184b8    fp = 0x0000fffd581c6390
     lr = 0x0000ffff86fc8768    sp = 0x0000fffd581c6390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffd581c6490    lr = 0x0000ffff874b37f0
     sp = 0x0000fffd581c63a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffd581c65b0    lr = 0x0000ffff873a7464
     sp = 0x0000fffd581c64a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000fffd581c6650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff074f6a80   x22 = 0x0000fffd6f8041c0
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000fffeefbf7fd0   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffd581c7740   x28 = 0x0000fffd581c7aa0
     fp = 0x0000fffd581c65c0    sp = 0x0000fffd581c66d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 128
 0  libpthread.so.0 + 0x129c8
     x0 = 0x000000000000006c    x1 = 0x0000ffff762d9120
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff72bda820    x5 = 0x0000ffff6dcc83c4
     x6 = 0x0000000000000000    x7 = 0x0000000064e2e32c
     x8 = 0x00000000000000cf    x9 = 0x000000000003bd96
    x10 = 0x0000000000000017   x11 = 0x000baa687982e99a
    x12 = 0x0000000377c1918c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000ffff6dcc9a90   x19 = 0x000000000000006c
    x20 = 0x0000ffff6dcc83c4   x21 = 0x0000ffff72bda820
    x22 = 0x0000000000010000   x23 = 0x0000ffff762d9120
    x24 = 0x0000ffff6dcc9740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff72d87680
    x28 = 0x0000ffff6dcc9040    fp = 0x0000ffff6dcc8350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff6dcc8350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff6dcc83a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff6dcc8360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff6dcc8400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff6dcc83b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7ce95780   x20 = 0x0000ffff6dcc8608
     fp = 0x0000ffff6dcc8520    sp = 0x0000ffff6dcc8430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff72d07810   x20 = 0x0000ffff72bb8800
    x21 = 0x0000ffff72c622e0   x22 = 0x0000ffff7ce95660
    x23 = 0x0000ffff7ce9d000   x24 = 0x0000ffff6dcc8608
    x25 = 0x0000ffff6dcc8618   x26 = 0x0000ffff72dea5d0
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff6dcc9040
     fp = 0x0000ffff6dcc85a0    sp = 0x0000ffff6dcc85a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff72d077e0   x20 = 0x0000ffff72c622c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff72d07810   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6dcc9740   x26 = 0x0000ffff6dcc9040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6dcc9040
     fp = 0x0000ffff6dcc8650    sp = 0x0000ffff6dcc8640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff6dcc9458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fb6ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fb6af   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6dcc9740   x26 = 0x0000ffff6dcc9040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6dcc9040
     fp = 0x0000ffff6dcc8820    sp = 0x0000ffff6dcc8820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 129
 0  libpthread.so.0 + 0x129c8
     x0 = 0x0000000000000020    x1 = 0x0000ffff5498ae20
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff54943120    x5 = 0x0000ffff51ac03c4
     x6 = 0x0000000000000000    x7 = 0x0000000000000018
     x8 = 0x00000000000000cf    x9 = 0x000000000000b988
    x10 = 0x0000ffff72dcb680   x11 = 0x0000ffff72dcb688
    x12 = 0x0000000000000000   x13 = 0x0000000000000000
    x14 = 0x0000000000000040   x15 = 0x0000ffff51ac1a88
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000ffff51ac1a90   x19 = 0x0000000000000020
    x20 = 0x0000ffff51ac03c4   x21 = 0x0000ffff54943120
    x22 = 0x0000000000010000   x23 = 0x0000ffff5498ae20
    x24 = 0x0000ffff51ac1740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff54d05a10
    x28 = 0x0000ffff51ac1040    fp = 0x0000ffff51ac0350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff51ac0350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff51ac03a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff51ac0360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff51ac0400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff51ac03b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff824236a0   x20 = 0x0000ffff51ac0608
     fp = 0x0000ffff51ac0520    sp = 0x0000ffff51ac0430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff5aea82f0   x20 = 0x0000ffff747da400
    x21 = 0x0000ffff5493e5e0   x22 = 0x0000ffff82423580
    x23 = 0x0000ffff8242b000   x24 = 0x0000ffff51ac0608
    x25 = 0x0000ffff51ac0618   x26 = 0x0000ffff54b67d60
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff51ac1040
     fp = 0x0000ffff51ac05a0    sp = 0x0000ffff51ac05a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff5ada29d0   x20 = 0x0000ffff5493e5c0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff5aea82f0   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff51ac1740   x26 = 0x0000ffff51ac1040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff51ac1040
     fp = 0x0000ffff51ac0650    sp = 0x0000ffff51ac0640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff51ac1458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffaeae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffaeaf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff51ac1740   x26 = 0x0000ffff51ac1040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff51ac1040
     fp = 0x0000ffff51ac0820    sp = 0x0000ffff51ac0820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 130
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff522c1370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x000000000008f7c8
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000fffffd7c
    x10 = 0x000000000008f7c8   x11 = 0x0000000000000009
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x00000000000d6580   x19 = 0x0000ffff522c14a4
    x20 = 0x0000ffff5aeb5998   x21 = 0x0000000000000001
    x22 = 0x0000ffff522c1370   x23 = 0x0000000000000001
    x24 = 0x0000ffff522c2040   x25 = 0x0000ffff875525a0
    x26 = 0x0000ffff522c1424   x27 = 0x0000ffff54d059a0
    x28 = 0x0000000000000000    fp = 0x0000ffff522c1320
     lr = 0x0000ffff874b754c    sp = 0x0000ffff522c1310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000ffff522c1400    lr = 0x0000ffff874b7c58
     sp = 0x0000ffff522c1330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000ffff522c1608
    x21 = 0x02028001522c14e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000ffff522c1520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff7361f6a0   x26 = 0x0000ffff522c1608
     fp = 0x0000ffff522c1430    sp = 0x0000ffff522c14e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7361f6a0   x20 = 0x0000ffff522c1608
    x21 = 0x0000ffff54d052a0   x22 = 0x0000ffff522c1618
    x23 = 0x0000ffff54d059a0   x24 = 0x0000000000000000
    x25 = 0x0000ffff54b67d18   x26 = 0x0000ffff7361f6d8
    x27 = 0x000000000000000b   x28 = 0x0000ffff522c2040
     fp = 0x0000ffff522c1520    sp = 0x0000ffff522c1520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff5ada28e0   x20 = 0x0000ffff747da400
    x21 = 0x0000ffff5493e620   x22 = 0x0000ffff7361f580
    x23 = 0x0000ffff7367d000   x24 = 0x0000ffff522c1608
    x25 = 0x0000ffff522c1618   x26 = 0x0000ffff54b67d18
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff522c2040
     fp = 0x0000ffff522c15a0    sp = 0x0000ffff522c15a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff5ada28b0   x20 = 0x0000ffff5493e600
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff5ada28e0   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff522c2740   x26 = 0x0000ffff522c2040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff522c2040
     fp = 0x0000ffff522c1650    sp = 0x0000ffff522c1640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff522c2458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffaeae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffaeaf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff522c2740   x26 = 0x0000ffff522c2040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff522c2040
     fp = 0x0000ffff522c1820    sp = 0x0000ffff522c1820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 131
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff54caf860    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff546c5518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d4    x7 = 0x0000ffff546c5438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x000722bd7d1cf1dd
    x12 = 0x000000036a6f3aac   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000ffff546c6a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff54caf860   x21 = 0x0000ffff54caf808
    x22 = 0x0000ffff86fe5000   x23 = 0x0000000000000050
    x24 = 0x0000ffff546c5518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000028
    x28 = 0x0000ffff54caf838    fp = 0x0000ffff546c5390
     lr = 0x0000ffff86fc8768    sp = 0x0000ffff546c5390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000ffff546c5490    lr = 0x0000ffff874b37f0
     sp = 0x0000ffff546c53a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000ffff546c55b0    lr = 0x0000ffff873a7464
     sp = 0x0000ffff546c54a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000ffff546c5650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff5aea7300   x22 = 0x0000ffff5ad91420
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000ffff5ada2d60   x26 = 0x0000ffff874b0204
    x27 = 0x0000ffff546c6740   x28 = 0x0000ffff546c6aa0
     fp = 0x0000ffff546c55c0    sp = 0x0000ffff546c56d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 132
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000b7    x1 = 0x0000fffdc9910920
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffdc57dc7a0    x5 = 0x0000fffdc68c73c4
     x6 = 0x0000000000000000    x7 = 0x00000000000010d8
     x8 = 0x00000000000000cf    x9 = 0x000000000003be44
    x10 = 0x0000000000000017   x11 = 0x0015cc6717931102
    x12 = 0x000000037863b174   x13 = 0x000000007fffffff
    x14 = 0x0000000000000004   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000001130c90   x19 = 0x00000000000000b7
    x20 = 0x0000fffdc68c73c4   x21 = 0x0000fffdc57dc7a0
    x22 = 0x0000000000010000   x23 = 0x0000fffdc9910920
    x24 = 0x0000fffdc68c8740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff074a4420
    x28 = 0x0000fffdc68c8040    fp = 0x0000fffdc68c7350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdc68c7350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdc68c73a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdc68c7360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdc68c7400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdc68c73b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000fffef00e04e0   x20 = 0x0000fffdc68c7608
     fp = 0x0000fffdc68c7520    sp = 0x0000fffdc68c7430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff074cc860   x20 = 0x0000ffff07488800
    x21 = 0x0000ffff07478aa0   x22 = 0x0000fffef00e03c0
    x23 = 0x0000fffef00e3240   x24 = 0x0000fffdc68c7608
    x25 = 0x0000fffdc68c7618   x26 = 0x0000fffeefd7b350
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdc68c8040
     fp = 0x0000fffdc68c75a0    sp = 0x0000fffdc68c75a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff3c1adf50   x20 = 0x0000ffff07478880
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff074cc860   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc68c8740   x26 = 0x0000fffdc68c8040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc68c8040
     fp = 0x0000fffdc68c7650    sp = 0x0000fffdc68c7640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc68c8458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a176e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a176f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc68c8740   x26 = 0x0000fffdc68c8040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc68c8040
     fp = 0x0000fffdc68c7820    sp = 0x0000fffdc68c7820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 133
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff532c3610    x1 = 0x0000000000000001
     x2 = 0x0000ffff532c35b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000ffff5ada0000
     x8 = 0x0000000000000049    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000ffff86af4e48   x13 = 0x0000000000000000
    x14 = 0x0000ffff532c4aa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000ffff532c3610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000ffff532c4740
    x22 = 0x000000000000001c   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000ffff532c4740
    x26 = 0x0000ffff532c4040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff532c4040    fp = 0x0000ffff532c3570
     lr = 0x0000ffff86bb90c0    sp = 0x0000ffff532c3570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000ffff532c35d0    lr = 0x0000ffff874ad15c
     sp = 0x0000ffff532c3580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000ffff532c3620    lr = 0x0000ffff874b0188
     sp = 0x0000ffff532c35e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff5ada29a0   x20 = 0x0000ffff5493e780
    x21 = 0x0000000000000000   x22 = 0x0000000000000000
    x23 = 0x0000ffff532c3820    fp = 0x0000ffff532c3650
     sp = 0x0000ffff532c3670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff532c4458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffb3de   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffb3df    fp = 0x0000ffff532c3820
     sp = 0x0000ffff532c3820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 134
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffebbf44610    x1 = 0x0000000000000001
     x2 = 0x0000fffebbf445b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000fffde29a5000
     x8 = 0x0000000000000049    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000000000001   x11 = 0x0000000000000000
    x12 = 0x0000fffebbf45768   x13 = 0x0000fffebbf45770
    x14 = 0x0000fffebbf45aa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000fffebbf44610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffebbf45740
    x22 = 0x00000000000000b2   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000fffebbf45740
    x26 = 0x0000fffebbf45040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000fffebbf45040    fp = 0x0000fffebbf44570
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffebbf44570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffebbf445d0    lr = 0x0000ffff874ad15c
     sp = 0x0000fffebbf44580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000fffebbf44620    lr = 0x0000ffff874b0188
     sp = 0x0000fffebbf445e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffde29a58d0   x20 = 0x0000fffdaa89c220
    x21 = 0x0000fffebbf446c0   x22 = 0x0000ffff8837817c
    x23 = 0x0000fffebbf44820    fp = 0x0000fffebbf44650
     sp = 0x0000fffebbf44670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffebbf45458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d9a2b2e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d9a2b2f    fp = 0x0000fffebbf44820
     sp = 0x0000fffebbf44820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 135
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000ffff6d1fd610    x1 = 0x0000000000000001
     x2 = 0x0000ffff6d1fd5b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000000000000028
     x8 = 0x0000000000000049    x9 = 0x0000ffff88626fb8
    x10 = 0x0000000005555556   x11 = 0x0000002d000005a0
    x12 = 0x0000ffff6d1fe768   x13 = 0x0000ffff6d1fe770
    x14 = 0x0000ffff6d1feaa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000ffff6d1fd610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000ffff6d1fe740
    x22 = 0x000000000000006a   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000ffff6d1fe740
    x26 = 0x0000ffff6d1fe040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000ffff6d1fe040    fp = 0x0000ffff6d1fd570
     lr = 0x0000ffff86bb90c0    sp = 0x0000ffff6d1fd570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000ffff6d1fd5d0    lr = 0x0000ffff874ad15c
     sp = 0x0000ffff6d1fd580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000ffff6d1fd620    lr = 0x0000ffff874b0188
     sp = 0x0000ffff6d1fd5e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff72d07870   x20 = 0x0000ffff72c62520
    x21 = 0x0000000000000000   x22 = 0x3335316664383600
    x23 = 0x0000ffff6d1fd820    fp = 0x0000ffff6d1fd650
     sp = 0x0000ffff6d1fd670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff6d1fe458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fbbce   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fbbcf    fp = 0x0000ffff6d1fd820
     sp = 0x0000ffff6d1fd820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 136
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff512bf370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x000000000000c1a0
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000ffffffbc
    x10 = 0x000000000000c1a0   x11 = 0x0000000000000022
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000000000000064   x19 = 0x0000ffff512bf4a4
    x20 = 0x0000ffff5aeb5a58   x21 = 0x0000000000000001
    x22 = 0x0000ffff512bf370   x23 = 0x0000000000000001
    x24 = 0x0000ffff512c0040   x25 = 0x0000ffff875525a0
    x26 = 0x0000ffff512bf424   x27 = 0x0000ffff54d05a80
    x28 = 0x0000000000000000    fp = 0x0000ffff512bf320
     lr = 0x0000ffff874b754c    sp = 0x0000ffff512bf310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000ffff512bf400    lr = 0x0000ffff874b7c58
     sp = 0x0000ffff512bf330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000ffff512bf608
    x21 = 0x02028001512bf4e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000ffff512bf520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff85a29c20   x26 = 0x0000ffff512bf608
     fp = 0x0000ffff512bf430    sp = 0x0000ffff512bf4e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff85a29c20   x20 = 0x0000ffff512bf608
    x21 = 0x0000ffff54d052a0   x22 = 0x0000ffff512bf618
    x23 = 0x0000ffff54d05a80   x24 = 0x0000000000000000
    x25 = 0x0000ffff54b67da8   x26 = 0x0000ffff85a29c58
    x27 = 0x000000000000000b   x28 = 0x0000ffff512c0040
     fp = 0x0000ffff512bf520    sp = 0x0000ffff512bf520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff5ada2a00   x20 = 0x0000ffff747da400
    x21 = 0x0000ffff5493e5a0   x22 = 0x0000ffff85a29b00
    x23 = 0x0000ffff85a2ca40   x24 = 0x0000ffff512bf608
    x25 = 0x0000ffff512bf618   x26 = 0x0000ffff54b67da8
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff512c0040
     fp = 0x0000ffff512bf5a0    sp = 0x0000ffff512bf5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff5ada2a30   x20 = 0x0000ffff5493e7a0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff5ada2a00   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff512c0740   x26 = 0x0000ffff512c0040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff512c0040
     fp = 0x0000ffff512bf650    sp = 0x0000ffff512bf640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff512c0458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffaeae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffaeaf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff512c0740   x26 = 0x0000ffff512c0040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff512c0040
     fp = 0x0000ffff512bf820    sp = 0x0000ffff512bf820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 137
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffd567c4610    x1 = 0x0000000000000001
     x2 = 0x0000fffd567c45b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000fffeefbf5000
     x8 = 0x0000000000000049    x9 = 0x0000ffff88626fb8
    x10 = 0x00000000ffffffff   x11 = 0x000000fb00001f60
    x12 = 0x0000fffd567c5768   x13 = 0x0000fffd567c5770
    x14 = 0x0000fffd567c5aa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000fffd567c4610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffd567c5740
    x22 = 0x00000000000000b9   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000fffd567c5740
    x26 = 0x0000fffd567c5040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000fffd567c5040    fp = 0x0000fffd567c4570
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffd567c4570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffd567c45d0    lr = 0x0000ffff874ad15c
     sp = 0x0000fffd567c4580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000fffd567c4620    lr = 0x0000ffff874b0188
     sp = 0x0000fffd567c45e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000fffeefbf7f10   x20 = 0x0000fffd6f8cd980
    x21 = 0x0000000000000000   x22 = 0x0000000000000000
    x23 = 0x0000fffd567c4820    fp = 0x0000fffd567c4650
     sp = 0x0000fffd567c4670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd567c5458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a1bde   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a1bdf    fp = 0x0000fffd567c4820
     sp = 0x0000fffd567c4820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 138
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000c8    x1 = 0x0000fffd4b95c9a0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000fffd5349f560    x5 = 0x0000fffd420ca3c4
     x6 = 0x0000000000000000    x7 = 0x0000fffeef21bb28
     x8 = 0x00000000000000cf    x9 = 0x000000000002d1ed
    x10 = 0x0000fffd3f169028   x11 = 0x0004ffff7f21be01
    x12 = 0x0000000000000004   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000004   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000000000020610   x19 = 0x00000000000000c8
    x20 = 0x0000fffd420ca3c4   x21 = 0x0000fffd5349f560
    x22 = 0x0000000000010000   x23 = 0x0000fffd4b95c9a0
    x24 = 0x0000fffd420cb740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000fffd4b2a1570
    x28 = 0x0000fffd420cb040    fp = 0x0000fffd420ca350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffd420ca350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffd420ca3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffd420ca360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffd420ca400    lr = 0x0000ffff87399c00
     sp = 0x0000fffd420ca3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7e8c8e80   x20 = 0x0000fffd420ca608
     fp = 0x0000fffd420ca520    sp = 0x0000fffd420ca430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000fffd6f7162f0   x20 = 0x0000fffd534ebc00
    x21 = 0x0000fffd4b098cc0   x22 = 0x0000ffff7e8c8d60
    x23 = 0x0000ffff7e8d2000   x24 = 0x0000fffd420ca608
    x25 = 0x0000fffd420ca618   x26 = 0x0000fffd4b44ed50
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffd420cb040
     fp = 0x0000fffd420ca5a0    sp = 0x0000fffd420ca5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074ccad0   x20 = 0x0000fffd4b098d00
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000fffd6f7162f0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd420cb740   x26 = 0x0000fffd420cb040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd420cb040
     fp = 0x0000fffd420ca650    sp = 0x0000fffd420ca640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffd420cb458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a16ae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a16af   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffd420cb740   x26 = 0x0000fffd420cb040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffd420cb040
     fp = 0x0000fffd420ca820    sp = 0x0000fffd420ca820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 139
 0  libpthread.so.0 + 0x129c8
     x0 = 0x000000000000001e    x1 = 0x0000ffff549cbb20
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff549430e0    x5 = 0x0000ffff50abe3c4
     x6 = 0x0000000000000000    x7 = 0x00000000bb0843d2
     x8 = 0x00000000000000cf    x9 = 0x000000000003be42
    x10 = 0x0000000000000017   x11 = 0x0015ade297396bca
    x12 = 0x000000037861c92c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000100d0   x19 = 0x000000000000001e
    x20 = 0x0000ffff50abe3c4   x21 = 0x0000ffff549430e0
    x22 = 0x0000000000010000   x23 = 0x0000ffff549cbb20
    x24 = 0x0000ffff50abf740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff54d05af0
    x28 = 0x0000ffff50abf040    fp = 0x0000ffff50abe350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff50abe350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff50abe3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff50abe360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff50abe400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff50abe3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff750176a0   x20 = 0x0000ffff50abe608
     fp = 0x0000ffff50abe520    sp = 0x0000ffff50abe430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff5ada2a60   x20 = 0x0000ffff747da400
    x21 = 0x0000ffff5493e7e0   x22 = 0x0000ffff75017580
    x23 = 0x0000ffff750a1000   x24 = 0x0000ffff50abe608
    x25 = 0x0000ffff50abe618   x26 = 0x0000ffff54ce98d0
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff50abf040
     fp = 0x0000ffff50abe5a0    sp = 0x0000ffff50abe5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff7461d4b0   x20 = 0x0000ffff5493e820
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff5ada2a60   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff50abf740   x26 = 0x0000ffff50abf040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff50abf040
     fp = 0x0000ffff50abe650    sp = 0x0000ffff50abe640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff50abf458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffaeae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffaeaf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff50abf740   x26 = 0x0000ffff50abf040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff50abf040
     fp = 0x0000ffff50abe820    sp = 0x0000ffff50abe820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 140
 0  libc.so.6 + 0xd33e4
     x0 = 0x0000000000000000    x1 = 0x0000fffdc7190370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x00000000000ab578
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000fffffed4
    x10 = 0x00000000000ab578   x11 = 0x000000000000000a
    x12 = 0x0000000000000000   x13 = 0x0000ffff8840a000
    x14 = 0x0000000000000004   x15 = 0x0000ffff88629e40
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x00000000009868d0   x19 = 0x0000fffdc71904a4
    x20 = 0x0000ffff07267f58   x21 = 0x0000000000000001
    x22 = 0x0000fffdc7190370   x23 = 0x0000000000000001
    x24 = 0x0000fffdc7191040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffdc7190424   x27 = 0x0000ffff074a43b0
    x28 = 0x0000000000000000    fp = 0x0000fffdc7190320
     lr = 0x0000ffff874b754c    sp = 0x0000fffdc7190310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffdc7190400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffdc7190330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000fffdc7190608
    x21 = 0x02028001c71904e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffdc7190520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff04545540   x26 = 0x0000fffdc7190608
     fp = 0x0000fffdc7190430    sp = 0x0000fffdc71904e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff04545540   x20 = 0x0000fffdc7190608
    x21 = 0x0000ffff074a3cb0   x22 = 0x0000fffdc7190618
    x23 = 0x0000ffff074a43b0   x24 = 0x0000000000000000
    x25 = 0x0000ffff073cb5a8   x26 = 0x0000ffff04545578
    x27 = 0x0000000000000173   x28 = 0x0000fffdc7191040
     fp = 0x0000fffdc7190520    sp = 0x0000fffdc7190520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff074cc7d0   x20 = 0x0000ffff07488800
    x21 = 0x0000ffff07478a60   x22 = 0x0000ffff04545420
    x23 = 0x0000ffff044f0200   x24 = 0x0000fffdc7190608
    x25 = 0x0000fffdc7190618   x26 = 0x0000ffff073cb5a8
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdc7191040
     fp = 0x0000fffdc71905a0    sp = 0x0000fffdc71905a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074cc800   x20 = 0x0000ffff07478a80
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff074cc7d0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc7191740   x26 = 0x0000fffdc7191040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc7191040
     fp = 0x0000fffdc7190650    sp = 0x0000fffdc7190640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc7191458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a176e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a176f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc7191740   x26 = 0x0000fffdc7191040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc7191040
     fp = 0x0000fffdc7190820    sp = 0x0000fffdc7190820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 141
 0  libpthread.so.0 + 0xe41c
     x0 = 0x0000ffff76a9b8e0    x1 = 0x0000000000000080
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000ffff76a9b8e0
     x6 = 0x0000000000000000    x7 = 0x0000fffdcb226478
     x8 = 0x0000000000000062    x9 = 0x0000fffdcb226350
    x10 = 0x0000000000000000   x11 = 0x0000fffdcb225f88
    x12 = 0x0000000000000001   x13 = 0x0000000000000003
    x14 = 0x000000000000000a   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8208
    x18 = 0x000000000000ff60   x19 = 0x0000000000000000
    x20 = 0x0000ffff76a9b888   x21 = 0x0000ffff76a9b8c8
    x22 = 0x0000000000000000   x23 = 0x0000ffff86fe5000
    x24 = 0x0000ffff86fc8158   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000ffff76a9b8e0
    x28 = 0x0000ffff76a9b8b8    fp = 0x0000fffdcb2263f0
     lr = 0x0000ffff86fc8400    sp = 0x0000fffdcb2263f0
     pc = 0x0000ffff86fc841c
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe3fc
     fp = 0x0000fffdcb2264d0    lr = 0x0000ffff874b3688
     sp = 0x0000fffdcb226400    pc = 0x0000ffff86fc8400
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x39c
     fp = 0x0000fffdcb2265f0    lr = 0x0000ffff873ae7a0
     sp = 0x0000fffdcb2264e0    pc = 0x0000ffff874b3688
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76a0d480   x20 = 0x0000fffdcb66e2e0
    x21 = 0x0000000000000000   x22 = 0x0000ffff873ae72c
    x23 = 0x0000ffff76b1ee20   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdcb227740   x26 = 0x0000fffdcb227040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdcb226650    sp = 0x0000fffdcb226710
     pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdcb227458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29bf72e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29bf72f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdcb227740   x26 = 0x0000fffdcb227040
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000fffdcb226820    sp = 0x0000fffdcb226820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 142
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000fffdd6e7a370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000000000000000   x13 = 0x0000000000000040
    x14 = 0x0000000000000040   x15 = 0x0000fffdd6e7ba88
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000fffdd6e7ba90   x19 = 0x0000fffdd6e7a4a4
    x20 = 0x0000ffff76ae1f98   x21 = 0x0000000000000001
    x22 = 0x0000fffdd6e7a370   x23 = 0x0000000000000001
    x24 = 0x0000fffdd6e7b040   x25 = 0x0000ffff875525a0
    x26 = 0x0000fffdd6e7a424   x27 = 0x0000ffff76a08270
    x28 = 0x0000000000000000    fp = 0x0000fffdd6e7a320
     lr = 0x0000ffff874b754c    sp = 0x0000fffdd6e7a310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000fffdd6e7a400    lr = 0x0000ffff874b7c58
     sp = 0x0000fffdd6e7a330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000000   x20 = 0x0000fffdd6e7b758
    x21 = 0x02028001d6e7a4e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000fffdd6e7a520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff82583ae0   x26 = 0x0000fffdd6e7a608
     fp = 0x0000fffdd6e7a430    sp = 0x0000fffdd6e7a4e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff82583ae0   x20 = 0x0000fffdd6e7a608
    x21 = 0x0000ffff76a02460   x22 = 0x0000fffdd6e7a618
    x23 = 0x0000ffff76a08270   x24 = 0x0000000000000000
    x25 = 0x0000ffff76b1a098   x26 = 0x0000ffff82583b18
    x27 = 0x00000000000000fb   x28 = 0x0000fffdd6e7b040
     fp = 0x0000fffdd6e7a520    sp = 0x0000fffdd6e7a520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff76ad4fb0   x20 = 0x0000ffff76b0d400
    x21 = 0x0000ffff76acc360   x22 = 0x0000ffff825839c0
    x23 = 0x0000fffd6c7c7000   x24 = 0x0000fffdd6e7a608
    x25 = 0x0000fffdd6e7a618   x26 = 0x0000ffff76b1a098
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdd6e7b040
     fp = 0x0000fffdd6e7a5a0    sp = 0x0000fffdd6e7a5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76ad4f80   x20 = 0x0000ffff76acc340
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff76ad4fb0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd6e7b740   x26 = 0x0000fffdd6e7b040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd6e7b040
     fp = 0x0000fffdd6e7a650    sp = 0x0000fffdd6e7a640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdd6e7b458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c077e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c077f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd6e7b740   x26 = 0x0000fffdd6e7b040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd6e7b040
     fp = 0x0000fffdd6e7a820    sp = 0x0000fffdd6e7a820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 143
 0  libpthread.so.0 + 0xe788
     x0 = 0x0000ffff0744a960    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffdcaa25518
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x00000000000001d5    x7 = 0x0000fffdcaa25438
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000017   x11 = 0x0018a8d2950c671b
    x12 = 0x000000036d647254   x13 = 0x000000007fffffff
    x14 = 0x0000000000000010   x15 = 0x000000007fffffde
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fc8518
    x18 = 0x0000fffdcaa26a90   x19 = 0x0000000000000000
    x20 = 0x0000ffff0744a960   x21 = 0x0000ffff0744a908
    x22 = 0x0000ffff86fe5000   x23 = 0x000000000000004c
    x24 = 0x0000fffdcaa25518   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000026
    x28 = 0x0000ffff0744a938    fp = 0x0000fffdcaa25390
     lr = 0x0000ffff86fc8768    sp = 0x0000fffdcaa25390
     pc = 0x0000ffff86fc8788
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0xe764
     fp = 0x0000fffdcaa25490    lr = 0x0000ffff874b37f0
     sp = 0x0000fffdcaa253a0    pc = 0x0000ffff86fc8768
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSemaphore_take + 0x504
     fp = 0x0000fffdcaa255b0    lr = 0x0000ffff873a7464
     sp = 0x0000fffdcaa254a0    pc = 0x0000ffff874b37f0
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIEventActiveDatabaseThread_loop + 0x1e4
    x19 = 0x0000fffdcaa25650   x20 = 0x0000ffff874b065c
    x21 = 0x0000ffff074ccad0   x22 = 0x0000fffeefc196c0
    x23 = 0x0000000000000000   x24 = 0x0000ffff873a9c8c
    x25 = 0x0000ffff3c1ae070   x26 = 0x0000ffff874b0204
    x27 = 0x0000fffdcaa26740   x28 = 0x0000fffdcaa26aa0
     fp = 0x0000fffdcaa255c0    sp = 0x0000fffdcaa256d0
     pc = 0x0000ffff873a9e74
    Found by: call frame info

Thread 144
 0  libpthread.so.0 + 0x129c8
     x0 = 0x00000000000000a5    x1 = 0x0000fffddae2c4a0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff76ad6ea0    x5 = 0x0000fffdd4d313c4
     x6 = 0x0000000000000000    x7 = 0x0000000000000018
     x8 = 0x00000000000000cf    x9 = 0x0000000000009988
    x10 = 0x0000fffeef169680   x11 = 0x0000fffeef169688
    x12 = 0x0000000000000000   x13 = 0x0000000000000225
    x14 = 0x0000000000000023   x15 = 0x0000fffdd4d32a88
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000fffdd4d32a90   x19 = 0x00000000000000a5
    x20 = 0x0000fffdd4d313c4   x21 = 0x0000ffff76ad6ea0
    x22 = 0x0000000000010000   x23 = 0x0000fffddae2c4a0
    x24 = 0x0000fffdd4d32740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff76a082e0
    x28 = 0x0000fffdd4d32040    fp = 0x0000fffdd4d31350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdd4d31350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdd4d313a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdd4d31360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdd4d31400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdd4d313b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff75016a40   x20 = 0x0000fffdd4d31608
     fp = 0x0000fffdd4d31520    sp = 0x0000fffdd4d31430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff76a0d8d0   x20 = 0x0000ffff76b0d400
    x21 = 0x0000ffff76acc320   x22 = 0x0000ffff75016920
    x23 = 0x0000ffff75013980   x24 = 0x0000fffdd4d31608
    x25 = 0x0000fffdd4d31618   x26 = 0x0000ffff76b1a0e0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdd4d32040
     fp = 0x0000fffdd4d315a0    sp = 0x0000fffdd4d315a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76ad50a0   x20 = 0x0000ffff76acc300
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff76a0d8d0   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd4d32740   x26 = 0x0000fffdd4d32040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd4d32040
     fp = 0x0000fffdd4d31650    sp = 0x0000fffdd4d31640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdd4d32458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c077e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c077f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdd4d32740   x26 = 0x0000fffdd4d32040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdd4d32040
     fp = 0x0000fffdd4d31820    sp = 0x0000fffdd4d31820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 145
 0  libc.so.6 + 0xd33e4
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffff6e4c9370
     x2 = 0x0000000000000001    x3 = 0x0000000000000000
     x4 = 0x0000ffff875d3850    x5 = 0x0000000000008ef0
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x00000000000000c0    x9 = 0x00000000fffffd20
    x10 = 0x0000000000008ef0   x11 = 0x0000000000000021
    x12 = 0x0000000000000000   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000000000000
    x16 = 0x0000ffff875dd1d8   x17 = 0x0000ffff86bc42a8
    x18 = 0x0000ffff6e4caa90   x19 = 0x0000ffff6e4c94a4
    x20 = 0x0000ffff72bf5558   x21 = 0x0000000000000001
    x22 = 0x0000ffff6e4c9370   x23 = 0x0000000000000001
    x24 = 0x0000ffff6e4ca040   x25 = 0x0000ffff875525a0
    x26 = 0x0000ffff6e4c9424   x27 = 0x0000ffff72d87840
    x28 = 0x0000000000000000    fp = 0x0000ffff6e4c9320
     lr = 0x0000ffff874b754c    sp = 0x0000ffff6e4c9310
     pc = 0x0000ffff86bc43e4
    Found by: given as instruction pointer in context
 1  libnddscore.so!RTIOsapiSharedMemorySemMutex_take_os + 0xa0
     fp = 0x0000ffff6e4c9400    lr = 0x0000ffff874b7c58
     sp = 0x0000ffff6e4c9330    pc = 0x0000ffff874b754c
    Found by: previous frame's frame pointer
 2  libnddscore.so!NDDS_Transport_Shmem_receive_rEA + 0x2fc
    x19 = 0x0000000000000001   x20 = 0x0000ffff6e4c9608
    x21 = 0x020280016e4c94e0   x22 = 0x89a3e21ead3e1d00
    x23 = 0x0000ffff6e4c9520   x24 = 0x0000ffff873903cc
    x25 = 0x0000ffff754171e0   x26 = 0x0000ffff6e4c9608
     fp = 0x0000ffff6e4c9430    sp = 0x0000ffff6e4c94e0
     pc = 0x0000ffff873a37ac
    Found by: call frame info
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff754171e0   x20 = 0x0000ffff6e4c9608
    x21 = 0x0000ffff72d87060   x22 = 0x0000ffff6e4c9618
    x23 = 0x0000ffff72d87840   x24 = 0x0000000000000000
    x25 = 0x0000ffff72d591d0   x26 = 0x0000ffff75417218
    x27 = 0x0000000000000083   x28 = 0x0000ffff6e4ca040
     fp = 0x0000ffff6e4c9520    sp = 0x0000ffff6e4c9520
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff72d07960   x20 = 0x0000ffff72bb8800
    x21 = 0x0000ffff72c625a0   x22 = 0x0000ffff754170c0
    x23 = 0x0000ffff072600c0   x24 = 0x0000ffff6e4c9608
    x25 = 0x0000ffff6e4c9618   x26 = 0x0000ffff72d591d0
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff6e4ca040
     fp = 0x0000ffff6e4c95a0    sp = 0x0000ffff6e4c95a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff85be5c10   x20 = 0x0000ffff72c625e0
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff72d07960   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6e4ca740   x26 = 0x0000ffff6e4ca040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6e4ca040
     fp = 0x0000ffff6e4c9650    sp = 0x0000ffff6e4c9640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff6e4ca458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fb69e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fb69f   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6e4ca740   x26 = 0x0000ffff6e4ca040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6e4ca040
     fp = 0x0000ffff6e4c9820    sp = 0x0000ffff6e4c9820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 146
 0  libpthread.so.0 + 0x129c8
     x0 = 0x000000000000006d    x1 = 0x0000ffff76341d20
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff72bda7e0    x5 = 0x0000ffff6ecca3c4
     x6 = 0x0000000000000000    x7 = 0x0000000000000000
     x8 = 0x00000000000000cf    x9 = 0x0000ffff745370e0
    x10 = 0x0000000000000040   x11 = 0x0000000000000007
    x12 = 0x0000000000000000   x13 = 0x0000003700000000
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000100d0   x19 = 0x000000000000006d
    x20 = 0x0000ffff6ecca3c4   x21 = 0x0000ffff72bda7e0
    x22 = 0x0000000000010000   x23 = 0x0000ffff76341d20
    x24 = 0x0000ffff6eccb740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff72d877d0
    x28 = 0x0000ffff6eccb040    fp = 0x0000ffff6ecca350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff6ecca350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff6ecca3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff6ecca360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff6ecca400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff6ecca3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff76a09180   x20 = 0x0000ffff6ecca608
     fp = 0x0000ffff6ecca520    sp = 0x0000ffff6ecca430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff72d078d0   x20 = 0x0000ffff72bb8800
    x21 = 0x0000ffff72c62540   x22 = 0x0000ffff76a09060
    x23 = 0x0000ffff76a34000   x24 = 0x0000ffff6ecca608
    x25 = 0x0000ffff6ecca618   x26 = 0x0000ffff72dea6a8
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff6eccb040
     fp = 0x0000ffff6ecca5a0    sp = 0x0000ffff6ecca5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff72d07900   x20 = 0x0000ffff72c62560
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff72d078d0   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6eccb740   x26 = 0x0000ffff6eccb040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6eccb040
     fp = 0x0000ffff6ecca650    sp = 0x0000ffff6ecca640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff6eccb458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fb69e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fb69f   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff6eccb740   x26 = 0x0000ffff6eccb040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff6eccb040
     fp = 0x0000ffff6ecca820    sp = 0x0000ffff6ecca820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 147
 0  libpthread.so.0 + 0x129c8
     x0 = 0x000000000000006e    x1 = 0x0000ffff76321de0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff72bda860    x5 = 0x0000ffff7117e3c4
     x6 = 0x0000000000000000    x7 = 0x000000000003be12
     x8 = 0x00000000000000cf    x9 = 0x000000000003be12
    x10 = 0x0000000000000017   x11 = 0x001063dafe4c34f4
    x12 = 0x000000037834026c   x13 = 0x000000007fffffff
    x14 = 0x0000ffff7445fe00   x15 = 0x000000007fffffff
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000000100d0   x19 = 0x000000000000006e
    x20 = 0x0000ffff7117e3c4   x21 = 0x0000ffff72bda860
    x22 = 0x0000000000010000   x23 = 0x0000ffff76321de0
    x24 = 0x0000ffff7117f740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff72d87760
    x28 = 0x0000ffff7117f040    fp = 0x0000ffff7117e350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff7117e350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff7117e3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff7117e360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff7117e400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff7117e3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff7e825300   x20 = 0x0000ffff7117e608
     fp = 0x0000ffff7117e520    sp = 0x0000ffff7117e430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff72d7a8a0   x20 = 0x0000ffff72bb8800
    x21 = 0x0000ffff72c62260   x22 = 0x0000ffff7e825120
    x23 = 0x0000ffff7e851000   x24 = 0x0000ffff7117e608
    x25 = 0x0000ffff7117e618   x26 = 0x0000ffff72dea660
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff7117f040
     fp = 0x0000ffff7117e5a0    sp = 0x0000ffff7117e5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff72d078a0   x20 = 0x0000ffff72c62240
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff72d7a8a0   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff7117f740   x26 = 0x0000ffff7117f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff7117f040
     fp = 0x0000ffff7117e650    sp = 0x0000ffff7117e640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff7117f458   x20 = 0x0000000000000000
    x21 = 0x0000ffff735fb69e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff735fb69f   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff7117f740   x26 = 0x0000ffff7117f040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff7117f040
     fp = 0x0000ffff7117e820    sp = 0x0000ffff7117e820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 148
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffdc925c610    x1 = 0x0000000000000001
     x2 = 0x0000fffdc925c5b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000fffdc5230000
     x8 = 0x0000000000000049    x9 = 0x000000062030b008
    x10 = 0x00000000ffffffff   x11 = 0x0000007800000f00
    x12 = 0x0000fffdc925d768   x13 = 0x0000fffdc925d770
    x14 = 0x0000fffdc925daa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000fffdc925c610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffdc925d740
    x22 = 0x00000000000000a9   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000fffdc925d740
    x26 = 0x0000fffdc925d040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000fffdc925d040    fp = 0x0000fffdc925c570
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffdc925c570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffdc925c5d0    lr = 0x0000ffff874ad15c
     sp = 0x0000fffdc925c580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000fffdc925c620    lr = 0x0000ffff874b0188
     sp = 0x0000fffdc925c5e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074cc680   x20 = 0x0000ffff07478a40
    x21 = 0x0000000000000000   x22 = 0x0000000000000000
    x23 = 0x0000fffdc925c820    fp = 0x0000fffdc925c650
     sp = 0x0000fffdc925c670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc925d458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a1c9e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a1c9f    fp = 0x0000fffdc925c820
     sp = 0x0000fffdc925c820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 149
 0  libpthread.so.0 + 0x129c8
     x0 = 0x000000000000001f    x1 = 0x0000ffff5494a3a0
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff549430a0    x5 = 0x0000ffff52ac23c4
     x6 = 0x0000000000000000    x7 = 0x0000ffff52ac21f8
     x8 = 0x00000000000000cf    x9 = 0x000000000003b12a
    x10 = 0x0000000000000017   x11 = 0x0006e5b439b315af
    x12 = 0x000000036c48406c   x13 = 0x000000007fffffff
    x14 = 0x0000000000000000   x15 = 0x0000000080000000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x0000ffff72dba908   x19 = 0x000000000000001f
    x20 = 0x0000ffff52ac23c4   x21 = 0x0000ffff549430a0
    x22 = 0x0000000000010000   x23 = 0x0000ffff5494a3a0
    x24 = 0x0000ffff52ac3740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff54d05930
    x28 = 0x0000ffff52ac3040    fp = 0x0000ffff52ac2350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000ffff52ac2350
     pc = 0x0000ffff86fcc9c8
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000ffff52ac23a0    lr = 0x0000ffff874aeb70
     sp = 0x0000ffff52ac2360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000ffff52ac2400    lr = 0x0000ffff87399c00
     sp = 0x0000ffff52ac23b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff54c86e60   x20 = 0x0000ffff52ac2608
     fp = 0x0000ffff52ac2520    sp = 0x0000ffff52ac2430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff5ada2940   x20 = 0x0000ffff747da400
    x21 = 0x0000ffff5493e660   x22 = 0x0000ffff54c86d40
    x23 = 0x0000ffff3fdab600   x24 = 0x0000ffff52ac2608
    x25 = 0x0000ffff52ac2618   x26 = 0x0000ffff54b67cd0
    x27 = 0x0000ffff87525a28   x28 = 0x0000ffff52ac3040
     fp = 0x0000ffff52ac25a0    sp = 0x0000ffff52ac25a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff5ada2910   x20 = 0x0000ffff5493e640
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff5ada2940   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff52ac3740   x26 = 0x0000ffff52ac3040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff52ac3040
     fp = 0x0000ffff52ac2650    sp = 0x0000ffff52ac2640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000ffff52ac3458   x20 = 0x0000000000000000
    x21 = 0x0000ffff74ffaebe   x22 = 0x0000ffff86fe5000
    x23 = 0x0000ffff74ffaebf   x24 = 0x0000ffff874b0204
    x25 = 0x0000ffff52ac3740   x26 = 0x0000ffff52ac3040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000ffff52ac3040
     fp = 0x0000ffff52ac2820    sp = 0x0000ffff52ac2820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 150
 0  libc.so.6 + 0xc80d8
     x0 = 0x0000fffddeafc610    x1 = 0x0000000000000001
     x2 = 0x0000fffddeafc5b8    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x00000000000003e8    x7 = 0x0000fffde29bb000
     x8 = 0x0000000000000049    x9 = 0x0000000010303007
    x10 = 0x00000000ffffffff   x11 = 0x000000ad000015a0
    x12 = 0x0000fffddeafd768   x13 = 0x0000fffddeafd770
    x14 = 0x0000fffddeafdaa0   x15 = 0x0000000000010000
    x16 = 0x0000000000000001   x17 = 0x0000ffff86bb8ff0
    x18 = 0x000000000000ff60   x19 = 0x0000fffddeafc610
    x20 = 0x0000ffff86c5e000   x21 = 0x0000fffddeafd740
    x22 = 0x00000000000000a4   x23 = 0x0000ffff87550f08
    x24 = 0x0000ffff874b0204   x25 = 0x0000fffddeafd740
    x26 = 0x0000fffddeafd040   x27 = 0x0000ffff86fe6000
    x28 = 0x0000fffddeafd040    fp = 0x0000fffddeafc570
     lr = 0x0000ffff86bb90c0    sp = 0x0000fffddeafc570
     pc = 0x0000ffff86bb90d8
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xc80bc
     fp = 0x0000fffddeafc5d0    lr = 0x0000ffff874ad15c
     sp = 0x0000fffddeafc580    pc = 0x0000ffff86bb90c0
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiInterfaceTracker_linuxNotificationFnc + 0x58
     fp = 0x0000fffddeafc620    lr = 0x0000ffff874b0188
     sp = 0x0000fffddeafc5e0    pc = 0x0000ffff874ad15c
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff76ad5070   x20 = 0x0000ffff76acc5e0
    x21 = 0x0000000000000000   x22 = 0x0000000000000000
    x23 = 0x0000fffddeafc820    fp = 0x0000fffddeafc650
     sp = 0x0000fffddeafc670    pc = 0x0000ffff874b065c
    Found by: call frame info
 4  libpthread.so.0 + 0x7620
    x19 = 0x0000fffddeafd458   x20 = 0x0000000000000000
    x21 = 0x0000fffef29c0cae   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffef29c0caf    fp = 0x0000fffddeafc820
     sp = 0x0000fffddeafc820    pc = 0x0000ffff86fc1624
    Found by: call frame info

Thread 151
 0  libpthread.so.0 + 0x129cc
     x0 = 0x0000000000008078    x1 = 0x0000fffdc9890520
     x2 = 0x0000000000010000    x3 = 0x0000000000000000
     x4 = 0x0000ffff0750f2e0    x5 = 0x0000fffdc8a5b3c4
     x6 = 0x0000000000000000    x7 = 0x000000000003be44
     x8 = 0x00000000000000cf    x9 = 0x000000000003be44
    x10 = 0x0000000000000017   x11 = 0x00135ecb87131102
    x12 = 0x000000037863b174   x13 = 0x000000007fffffff
    x14 = 0x0000000000000003   x15 = 0x0000ffff88629e40
    x16 = 0x0000000000000001   x17 = 0x0000ffff86fcc938
    x18 = 0x00000000007d9bf0   x19 = 0x00000000000000ad
    x20 = 0x0000fffdc8a5b3c4   x21 = 0x0000ffff0750f2e0
    x22 = 0x0000000000010000   x23 = 0x0000fffdc9890520
    x24 = 0x0000fffdc8a5c740   x25 = 0x0000000000000000
    x26 = 0x0000ffff875e1388   x27 = 0x0000ffff074a4260
    x28 = 0x0000fffdc8a5c040    fp = 0x0000fffdc8a5b350
     lr = 0x0000ffff86fcc9a8    sp = 0x0000fffdc8a5b350
     pc = 0x0000ffff86fcc9cc
    Found by: given as instruction pointer in context
 1  libpthread.so.0 + 0x129a4
     fp = 0x0000fffdc8a5b3a0    lr = 0x0000ffff874aeb70
     sp = 0x0000fffdc8a5b360    pc = 0x0000ffff86fcc9a8
    Found by: previous frame's frame pointer
 2  libnddscore.so!RTIOsapiSocket_recvFrom + 0x38
     fp = 0x0000fffdc8a5b400    lr = 0x0000ffff87399c00
     sp = 0x0000fffdc8a5b3b0    pc = 0x0000ffff874aeb70
    Found by: previous frame's frame pointer
 3  libnddscore.so!RTINetioReceiver_receiveFast + 0x68c
    x19 = 0x0000ffff04124ca0   x20 = 0x0000fffdc8a5b608
     fp = 0x0000fffdc8a5b520    sp = 0x0000fffdc8a5b430
     pc = 0x0000ffff873903cc
    Found by: call frame info
 4  libnddscore.so!COMMENDActiveFacadeReceiver_loop + 0x2a0
    x19 = 0x0000ffff074cc620   x20 = 0x0000ffff07488800
    x21 = 0x0000ffff07478920   x22 = 0x0000ffff04124b80
    x23 = 0x0000fffeef085900   x24 = 0x0000fffdc8a5b608
    x25 = 0x0000fffdc8a5b618   x26 = 0x0000ffff073cb4d0
    x27 = 0x0000ffff87525a28   x28 = 0x0000fffdc8a5c040
     fp = 0x0000fffdc8a5b5a0    sp = 0x0000fffdc8a5b5a0
     pc = 0x0000ffff8730aa9c
    Found by: call frame info
 5  libnddscore.so!RTIOsapiThreadChild_onSpawned + 0x454
    x19 = 0x0000ffff074cc590   x20 = 0x0000ffff07478900
    x21 = 0x0000000000000000   x22 = 0x0000ffff8730a7f8
    x23 = 0x0000ffff074cc620   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc8a5c740   x26 = 0x0000fffdc8a5c040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc8a5c040
     fp = 0x0000fffdc8a5b650    sp = 0x0000fffdc8a5b640
     pc = 0x0000ffff874b065c
    Found by: call frame info
 6  libpthread.so.0 + 0x7620
    x19 = 0x0000fffdc8a5c458   x20 = 0x0000000000000000
    x21 = 0x0000fffe7d1a177e   x22 = 0x0000ffff86fe5000
    x23 = 0x0000fffe7d1a177f   x24 = 0x0000ffff874b0204
    x25 = 0x0000fffdc8a5c740   x26 = 0x0000fffdc8a5c040
    x27 = 0x0000ffff86fe6000   x28 = 0x0000fffdc8a5c040
     fp = 0x0000fffdc8a5b820    sp = 0x0000fffdc8a5b820
     pc = 0x0000ffff86fc1624
    Found by: call frame info

Loaded modules:
0x00400000 - 0x00409fff  vla_arch_container  ???  (main)
0x200060000 - 0x20006ffff  dmabuf:  ???
0x200270000 - 0x203e8ffff  dmabuf:  ???
0x204090000 - 0x20449ffff  dmabuf:  ???
0x2046a0000 - 0x2046bffff  dmabuf:  ???
0x2046e1000 - 0x2048e0fff  dmabuf:  ???
0x2048f1000 - 0x204af0fff  dmabuf:  ???
0x204b11000 - 0x205053fff  dmabuf:  ???
0x2059c7000 - 0x2063c6fff  dmabuf:  ???
0x2063d7000 - 0x2065d6fff  dmabuf:  ???
0x2067d7000 - 0x206fd6fff  dmabuf:  ???
0x20707d000 - 0x207a7cfff  dmabuf:  ???
0x207f57000 - 0x209956fff  dmabuf:  ???
0x209989000 - 0x20b588fff  dmabuf:  ???
0x20b599000 - 0x20bd98fff  dmabuf:  ???
0x214042000 - 0x214241fff  dmabuf:  ???
0x214262000 - 0x214461fff  dmabuf:  ???
0x21a8ce000 - 0x21aacdfff  dmabuf:  ???
0x2274cd000 - 0x233893fff  dmabuf:  ???
0x23fc5b000 - 0x24c021fff  dmabuf:  ???
0x2583e9000 - 0x2647affff  dmabuf:  ???
0x270d77000 - 0x27d13dfff  dmabuf:  ???
0x289505000 - 0x2958cbfff  dmabuf:  ???
0x2a1c93000 - 0x2ae059fff  dmabuf:  ???
0x2ba421000 - 0x2c67e7fff  dmabuf:  ???
0x3078e4000 - 0x307ae3fff  dmabuf:  ???
0x307b34000 - 0x308133fff  dmabuf:  ???
0x308144000 - 0x308543fff  dmabuf:  ???
0x308574000 - 0x308773fff  dmabuf:  ???
0x308784000 - 0x308d83fff  dmabuf:  ???
0x345e20000 - 0x34601ffff  dmabuf:  ???
0x34dcb4000 - 0x34e0b3fff  dmabuf:  ???
0x34e2b4000 - 0x35b613fff  dmabuf:  ???
0x371fd4000 - 0x371fe3fff  dmabuf:  ???
0xfffc6d0ba000 - 0xfffcf9e1ffff  model.tar  ???
0xfffcfd620000 - 0xfffcfed50fff  dmabuf:  ???
0xfffcffd53000 - 0xfffcffe54fff  nvscic2c_pcie_s0_c5_1  ???
0xfffd1f655000 - 0xfffd1f71cfff  SYSV00404737 (deleted)  ???
0xfffd23b1d000 - 0xfffd2431dfff  SYSV0040471e (deleted)  ???
0xfffd2431e000 - 0xfffd243e5fff  SYSV00401d2e (deleted)  ???
0xfffd243e6000 - 0xfffd245f6fff  SYSV00401d18 (deleted)  ???
0xfffd245f7000 - 0xfffd246befff  SYSV00401d28 (deleted)  ???
0xfffd24bbf000 - 0xfffd24c86fff  SYSV00401d2c (deleted)  ???
0xfffd24c87000 - 0xfffd24d4efff  SYSV00401cf4 (deleted)  ???
0xfffd24d4f000 - 0xfffd24e16fff  SYSV004046fa (deleted)  ???
0xfffd24e17000 - 0xfffd24edefff  SYSV00404700 (deleted)  ???
0xfffd24edf000 - 0xfffd24fa6fff  SYSV0040470c (deleted)  ???
0xfffd24fa7000 - 0xfffd251b7fff  SYSV00404728 (deleted)  ???
0xfffd251b8000 - 0xfffd2527ffff  SYSV004046f2 (deleted)  ???
0xfffd25c01000 - 0xfffd25c45fff  dmabuf:  ???
0xfffd25c46000 - 0xfffd25d56fff  SYSV00401d30 (deleted)  ???
0xfffd27957000 - 0xfffd27a1efff  SYSV00404719 (deleted)  ???
0xfffd27a1f000 - 0xfffd2821ffff  SYSV0040471e (deleted)  ???
0xfffd28220000 - 0xfffd282e7fff  SYSV0040472e (deleted)  ???
0xfffd282e8000 - 0xfffd283affff  SYSV004046fa (deleted)  ???
0xfffd283b0000 - 0xfffd285c0fff  SYSV00404728 (deleted)  ???
0xfffd285c1000 - 0xfffd28688fff  SYSV0040470c (deleted)  ???
0xfffd28689000 - 0xfffd28750fff  SYSV00404700 (deleted)  ???
0xfffd28751000 - 0xfffd28818fff  SYSV004046f2 (deleted)  ???
0xfffd28819000 - 0xfffd288e0fff  SYSV00404738 (deleted)  ???
0xfffd2a0e1000 - 0xfffd2a1a8fff  SYSV00404746 (deleted)  ???
0xfffd2a1a9000 - 0xfffd2a3b9fff  SYSV00404744 (deleted)  ???
0xfffd2a3ba000 - 0xfffd2a481fff  SYSV00404704 (deleted)  ???
0xfffd2a482000 - 0xfffd2a549fff  SYSV0040472a (deleted)  ???
0xfffd2a54a000 - 0xfffd2a75afff  SYSV0040473c (deleted)  ???
0xfffd2a75b000 - 0xfffd2a822fff  SYSV004046f0 (deleted)  ???
0xfffd2aba3000 - 0xfffd2ac6afff  SYSV00404742 (deleted)  ???
0xfffd2ac6b000 - 0xfffd2ad32fff  SYSV00404746 (deleted)  ???
0xfffd2ad33000 - 0xfffd2adfafff  SYSV00404708 (deleted)  ???
0xfffd2adfb000 - 0xfffd2aec2fff  SYSV0040473e (deleted)  ???
0xfffd2aec3000 - 0xfffd2b0d3fff  SYSV00404744 (deleted)  ???
0xfffd2b0d4000 - 0xfffd2b19bfff  SYSV0040472a (deleted)  ???
0xfffd2b19c000 - 0xfffd2b3acfff  SYSV0040473c (deleted)  ???
0xfffd2b3ad000 - 0xfffd2b474fff  SYSV0040471c (deleted)  ???
0xfffd2b475000 - 0xfffd2b53cfff  SYSV00404702 (deleted)  ???
0xfffd2b53d000 - 0xfffd2b604fff  SYSV004046f4 (deleted)  ???
0xfffd2b605000 - 0xfffd2b6ccfff  SYSV004046fc (deleted)  ???
0xfffd2b6cd000 - 0xfffd2b794fff  SYSV0040470a (deleted)  ???
0xfffd2b795000 - 0xfffd2b85cfff  SYSV00404706 (deleted)  ???
0xfffd2b85d000 - 0xfffd2b924fff  SYSV00404710 (deleted)  ???
0xfffd2b925000 - 0xfffd2bb35fff  SYSV00404716 (deleted)  ???
0xfffd2bb36000 - 0xfffd2bbfdfff  SYSV00404730 (deleted)  ???
0xfffd2bbfe000 - 0xfffd2c3fefff  SYSV00404722 (deleted)  ???
0xfffd2c3ff000 - 0xfffd2c4c6fff  SYSV00404732 (deleted)  ???
0xfffd2c4c7000 - 0xfffd2c6d7fff  SYSV004046fe (deleted)  ???
0xfffd2c6d8000 - 0xfffd2ced8fff  SYSV00404726 (deleted)  ???
0xfffd2ced9000 - 0xfffd2cfa0fff  SYSV00404720 (deleted)  ???
0xfffd2d221000 - 0xfffd2d2e8fff  SYSV00404736 (deleted)  ???
0xfffd2d2e9000 - 0xfffd2d3b0fff  SYSV00404748 (deleted)  ???
0xfffd2d3b1000 - 0xfffd2d478fff  SYSV00404736 (deleted)  ???
0xfffd2d479000 - 0xfffd2d689fff  SYSV00404716 (deleted)  ???
0xfffd2d68a000 - 0xfffd2de8afff  SYSV00404722 (deleted)  ???
0xfffd2de8b000 - 0xfffd2e68bfff  SYSV00404726 (deleted)  ???
0xfffd2e68c000 - 0xfffd2e753fff  SYSV00404748 (deleted)  ???
0xfffd2e754000 - 0xfffd2e8e3fff  SYSV00404718 (deleted)  ???
0xfffd2e8e4000 - 0xfffd2e9abfff  SYSV0040471a (deleted)  ???
0xfffd2e9ac000 - 0xfffd2eb3bfff  SYSV004046f8 (deleted)  ???
0xfffd2eb3c000 - 0xfffd2eccbfff  SYSV00404712 (deleted)  ???
0xfffd2eccc000 - 0xfffd2ee5bfff  SYSV00404714 (deleted)  ???
0xfffd2ee5c000 - 0xfffd2efebfff  SYSV004046f6 (deleted)  ???
0xfffd2efec000 - 0xfffd2f17bfff  SYSV0040470e (deleted)  ???
0xfffd2f17c000 - 0xfffd2f243fff  SYSV0040472c (deleted)  ???
0xfffd2f244000 - 0xfffd2f30bfff  SYSV00404740 (deleted)  ???
0xfffd2f30c000 - 0xfffd2f3d3fff  SYSV0040472c (deleted)  ???
0xfffd2f3d4000 - 0xfffd2f49bfff  SYSV00404740 (deleted)  ???
0xfffd2f49c000 - 0xfffd2f563fff  SYSV00404724 (deleted)  ???
0xfffd2f564000 - 0xfffd2f62bfff  SYSV00404738 (deleted)  ???
0xfffd2f62c000 - 0xfffd2f6f3fff  SYSV00401d14 (deleted)  ???
0xfffd2f6f4000 - 0xfffd2f7bbfff  SYSV0040472e (deleted)  ???
0xfffd2fb3c000 - 0xfffd2fc03fff  SYSV00404704 (deleted)  ???
0xfffd2fc04000 - 0xfffd2fccbfff  SYSV00401d0c (deleted)  ???
0xfffd2fccc000 - 0xfffd2fd93fff  SYSV004046f0 (deleted)  ???
0xfffd2fd94000 - 0xfffd2fe5bfff  SYSV00401d04 (deleted)  ???
0xfffd2fe5c000 - 0xfffd2ff23fff  SYSV00404742 (deleted)  ???
0xfffd2ff24000 - 0xfffd2ffebfff  SYSV00404746 (deleted)  ???
0xfffd2ffec000 - 0xfffd300b3fff  SYSV0040473e (deleted)  ???
0xfffd300b4000 - 0xfffd302c4fff  SYSV00404744 (deleted)  ???
0xfffd302c5000 - 0xfffd3038cfff  SYSV00404734 (deleted)  ???
0xfffd3038d000 - 0xfffd30454fff  SYSV0040472a (deleted)  ???
0xfffd30455000 - 0xfffd30665fff  SYSV0040473c (deleted)  ???
0xfffd30666000 - 0xfffd3072dfff  SYSV00404708 (deleted)  ???
0xfffd3072e000 - 0xfffd307f5fff  SYSV0040471c (deleted)  ???
0xfffd307f6000 - 0xfffd308bdfff  SYSV00404702 (deleted)  ???
0xfffd308be000 - 0xfffd30985fff  SYSV004046f4 (deleted)  ???
0xfffd30986000 - 0xfffd30b96fff  SYSV00401d2a (deleted)  ???
0xfffd30b97000 - 0xfffd30c5efff  SYSV004046fc (deleted)  ???
0xfffd30c5f000 - 0xfffd30d26fff  SYSV00404736 (deleted)  ???
0xfffd30d27000 - 0xfffd30deefff  SYSV00404748 (deleted)  ???
0xfffd30def000 - 0xfffd30eb6fff  SYSV0040470a (deleted)  ???
0xfffd30eb7000 - 0xfffd30f7efff  SYSV00401cf2 (deleted)  ???
0xfffd30f7f000 - 0xfffd31046fff  SYSV00401d10 (deleted)  ???
0xfffd31047000 - 0xfffd3110efff  SYSV00401d20 (deleted)  ???
0xfffd3110f000 - 0xfffd311d6fff  SYSV00404706 (deleted)  ???
0xfffd311d7000 - 0xfffd3129efff  SYSV00404730 (deleted)  ???
0xfffd3169f000 - 0xfffd318affff  SYSV00404716 (deleted)  ???
0xfffd318b0000 - 0xfffd31977fff  SYSV00404ce4 (deleted)  ???
0xfffd31978000 - 0xfffd31a3ffff  SYSV00401d06 (deleted)  ???
0xfffd31a40000 - 0xfffd31b07fff  SYSV00404ce2 (deleted)  ???
0xfffd31b08000 - 0xfffd31bcffff  SYSV00401cf6 (deleted)  ???
0xfffd31bd0000 - 0xfffd31c97fff  SYSV00404710 (deleted)  ???
0xfffd31f98000 - 0xfffd3205ffff  SYSV00401cfc (deleted)  ???
0xfffd32060000 - 0xfffd32127fff  SYSV00401d1c (deleted)  ???
0xfffd32128000 - 0xfffd321effff  SYSV00401d0e (deleted)  ???
0xfffd321f0000 - 0xfffd322b7fff  SYSV00401d02 (deleted)  ???
0xfffd325b8000 - 0xfffd3267ffff  SYSV00401cf8 (deleted)  ???
0xfffd32680000 - 0xfffd32747fff  SYSV00401d0a (deleted)  ???
0xfffd32748000 - 0xfffd3280ffff  SYSV00401d08 (deleted)  ???
0xfffd32810000 - 0xfffd328d7fff  SYSV00401d12 (deleted)  ???
0xfffd328d8000 - 0xfffd32ae8fff  SYSV00401d16 (deleted)  ???
0xfffd32ae9000 - 0xfffd32bb0fff  SYSV00401d22 (deleted)  ???
0xfffd32bb1000 - 0xfffd32dc1fff  SYSV00401d1a (deleted)  ???
0xfffd32dc2000 - 0xfffd32e89fff  SYSV00404cde (deleted)  ???
0xfffd32e8a000 - 0xfffd3309afff  SYSV00401d1e (deleted)  ???
0xfffd3309b000 - 0xfffd3389bfff  SYSV00404722 (deleted)  ???
0xfffd34c9c000 - 0xfffd34d63fff  SYSV00401cfa (deleted)  ???
0xfffd34d64000 - 0xfffd35564fff  SYSV00404726 (deleted)  ???
0xfffd35565000 - 0xfffd3562cfff  SYSV00404cd0 (deleted)  ???
0xfffd3562d000 - 0xfffd3583dfff  SYSV004046fe (deleted)  ???
0xfffd3583e000 - 0xfffd35905fff  SYSV00401d00 (deleted)  ???
0xfffd36d06000 - 0xfffd36dcdfff  SYSV00404cce (deleted)  ???
0xfffd389ce000 - 0xfffd38a95fff  SYSV00401d26 (deleted)  ???
0xfffd38a96000 - 0xfffd38b5dfff  SYSV00401d24 (deleted)  ???
0xfffd38b5e000 - 0xfffd3d4d1fff  dmabuf:  ???
0xfffd3e2d2000 - 0xfffd3effffff  dmabuf:  ???
0xfffd3fc00000 - 0xfffd3fc03fff  dmabuf:  ???
0xfffd40804000 - 0xfffd408cbfff  SYSV00401d26 (deleted)  ???
0xfffd420cd000 - 0xfffd42194fff  SYSV00401d27 (deleted)  ???
0xfffd43197000 - 0xfffd4325efff  SYSV00401d26 (deleted)  ???
0xfffd44261000 - 0xfffd44344fff  __db.001  ???
0xfffd473c8000 - 0xfffd4748ffff  SYSV00404712 (deleted)  ???
0xfffd47490000 - 0xfffd47557fff  SYSV0040470e (deleted)  ???
0xfffd47558000 - 0xfffd4761ffff  SYSV00404718 (deleted)  ???
0xfffd47620000 - 0xfffd476e7fff  SYSV0040471a (deleted)  ???
0xfffd476e8000 - 0xfffd477affff  SYSV004046f6 (deleted)  ???
0xfffd4afb0000 - 0xfffd4b077fff  SYSV00404714 (deleted)  ???
0xfffd4be78000 - 0xfffd4bf3ffff  SYSV00404cd6 (deleted)  ???
0xfffd4bf40000 - 0xfffd4c007fff  SYSV00404ce6 (deleted)  ???
0xfffd4c008000 - 0xfffd4c0cffff  SYSV00404cd4 (deleted)  ???
0xfffd4f0d0000 - 0xfffd4f197fff  SYSV00404732 (deleted)  ???
0xfffd4f198000 - 0xfffd4f25ffff  SYSV00404ccc (deleted)  ???
0xfffd4f260000 - 0xfffd4f327fff  SYSV00404cda (deleted)  ???
0xfffd4f328000 - 0xfffd4f3effff  SYSV00404cd8 (deleted)  ???
0xfffd4f3f0000 - 0xfffd4f4b7fff  SYSV00404cd2 (deleted)  ???
0xfffd4f4b8000 - 0xfffd4f57ffff  SYSV004046f8 (deleted)  ???
0xfffd4f580000 - 0xfffd4f647fff  SYSV00404720 (deleted)  ???
0xfffd4f648000 - 0xfffd4f70ffff  SYSV00404740 (deleted)  ???
0xfffd4f710000 - 0xfffd4f7d7fff  SYSV00404724 (deleted)  ???
0xfffd527d9000 - 0xfffd528a0fff  SYSV00404734 (deleted)  ???
0xfffd528a1000 - 0xfffd52968fff  SYSV00404ce0 (deleted)  ???
0xfffd52969000 - 0xfffd52a30fff  SYSV00404734 (deleted)  ???
0xfffd53e32000 - 0xfffd53ef9fff  SYSV00404735 (deleted)  ???
0xfffd54efc000 - 0xfffd54fc3fff  SYSV00404734 (deleted)  ???
0xfffd661c9000 - 0xfffd66290fff  SYSV0040471d (deleted)  ???
0xfffd68e12000 - 0xfffd68ed9fff  SYSV00404742 (deleted)  ???
0xfffd68eda000 - 0xfffd696dafff  SYSV0040471e (deleted)  ???
0xfffd696db000 - 0xfffd697a2fff  SYSV0040471a (deleted)  ???
0xfffd697a3000 - 0xfffd6986afff  SYSV00404724 (deleted)  ???
0xfffd6b46b000 - 0xfffd6b532fff  SYSV004046fa (deleted)  ???
0xfffd6b533000 - 0xfffd6b5fafff  SYSV0040470c (deleted)  ???
0xfffd6c87d000 - 0xfffd6c944fff  SYSV00404730 (deleted)  ???
0xfffd6c945000 - 0xfffd6ca0cfff  SYSV004046f0 (deleted)  ???
0xfffd6ca0d000 - 0xfffd6cad4fff  SYSV00404720 (deleted)  ???
0xfffd6cad5000 - 0xfffd6cb9cfff  SYSV00404732 (deleted)  ???
0xfffd6cb9d000 - 0xfffd6cc64fff  SYSV00404704 (deleted)  ???
0xfffd6cc65000 - 0xfffd6cd2cfff  SYSV00404706 (deleted)  ???
0xfffd6cd2d000 - 0xfffd6cdf4fff  SYSV0040471c (deleted)  ???
0xfffd6cdf5000 - 0xfffd6d005fff  SYSV00404728 (deleted)  ???
0xfffd6d006000 - 0xfffd6d0cdfff  SYSV00404708 (deleted)  ???
0xfffd6d0ce000 - 0xfffd6d2defff  SYSV004046fe (deleted)  ???
0xfffd6d2df000 - 0xfffd6d3a6fff  SYSV004046f4 (deleted)  ???
0xfffd6d3a7000 - 0xfffd6d46efff  SYSV00404700 (deleted)  ???
0xfffd6d46f000 - 0xfffd6d536fff  SYSV004046fc (deleted)  ???
0xfffd70d3a000 - 0xfffd70e01fff  SYSV00404710 (deleted)  ???
0xfffd72202000 - 0xfffd722c9fff  SYSV004046f2 (deleted)  ???
0xfffd732cc000 - 0xfffd73393fff  SYSV00404702 (deleted)  ???
0xfffd75396000 - 0xfffd7545dfff  SYSV00404738 (deleted)  ???
0xfffd7545e000 - 0xfffd75525fff  SYSV0040473e (deleted)  ???
0xfffd75526000 - 0xfffd755edfff  SYSV0040470a (deleted)  ???
0xfffd75def000 - 0xfffd75eb6fff  SYSV0040472e (deleted)  ???
0xfffd786b8000 - 0xfffd7877ffff  SYSV0040472f (deleted)  ???
0xfffd79782000 - 0xfffd79849fff  SYSV0040472e (deleted)  ???
0xfffd7b44b000 - 0xfffdaa36cfff  model.tar  ???
0xfffdaaa6d000 - 0xfffdc21fdfff  model.tar  ???
0xfffdc3c00000 - 0xfffdc3cc7fff  SYSV0040472c (deleted)  ???
0xfffdc68ca000 - 0xfffdc6991fff  SYSV0040472d (deleted)  ???
0xfffdc7994000 - 0xfffdc7a5bfff  SYSV0040472c (deleted)  ???
0xfffdca15f000 - 0xfffdca226fff  SYSV00404cdc (deleted)  ???
0xfffdcb829000 - 0xfffdcc468fff  dmabuf:  ???
0xfffdcc969000 - 0xfffdce1e8fff  dmabuf:  ???
0xfffdce6e9000 - 0xfffdcf328fff  dmabuf:  ???
0xfffdcff2a000 - 0xfffdd0b69fff  dmabuf:  ???
0xfffdd0b6a000 - 0xfffdd0c31fff  SYSV00404cdd (deleted)  ???
0xfffdd0c32000 - 0xfffdd1871fff  dmabuf:  ???
0xfffdd2073000 - 0xfffdd4532fff  dmabuf:  ???
0xfffdd4d34000 - 0xfffdd4dfbfff  SYSV00404cdc (deleted)  ???
0xfffdd4dfc000 - 0xfffdd667bfff  dmabuf:  ???
0xfffdd71fd000 - 0xfffdd8a7cfff  dmabuf:  ???
0xfffdd8d7d000 - 0xfffdda5fcfff  dmabuf:  ???
0xfffddb1fe000 - 0xfffdde2fdfff  dmabuf:  ???
0xfffddeaff000 - 0xfffde037efff  dmabuf:  ???
0xfffde06ff000 - 0xfffde1f7efff  dmabuf:  ???
0xfffde3400000 - 0xfffde343dfff  dmabuf:  ???
0xfffde343e000 - 0xfffdfabcefff  model.tar  ???
0xfffe06bcf000 - 0xfffe7c1a3fff  model.tar  ???
0xfffe7d9a7000 - 0xfffe7e5e6fff  dmabuf:  ???
0xfffe7efe7000 - 0xfffe92e66fff  dmabuf:  ???
0xfffe9566c000 - 0xfffe9ac2bfff  dmabuf:  ???
0xfffe9b42d000 - 0xfffea782cfff  dmabuf:  ???
0xfffea782d000 - 0xfffea78f4fff  SYSV0040441e (deleted)  ???
0xfffea78f5000 - 0xfffea79bcfff  SYSV0040441a (deleted)  ???
0xfffea7c3d000 - 0xfffead1fcfff  dmabuf:  ???
0xfffead1fd000 - 0xfffead2c4fff  SYSV0040440c (deleted)  ???
0xfffead2c5000 - 0xfffeb96c4fff  dmabuf:  ???
0xfffeb9ec6000 - 0xfffebb745fff  dmabuf:  ???
0xfffebbf47000 - 0xfffebd7c6fff  dmabuf:  ???
0xfffebe7c9000 - 0xfffebe890fff  SYSV0040440a (deleted)  ???
0xfffebe891000 - 0xfffebe958fff  SYSV00404412 (deleted)  ???
0xfffebe959000 - 0xfffebea20fff  SYSV0040440e (deleted)  ???
0xfffebea21000 - 0xfffebeb31fff  SYSV00404420 (deleted)  ???
0xfffebeb32000 - 0xfffebebf9fff  SYSV00404406 (deleted)  ???
0xfffebebfa000 - 0xfffebecc1fff  SYSV00404414 (deleted)  ???
0xfffebecc2000 - 0xfffebed89fff  SYSV00404410 (deleted)  ???
0xfffebed8a000 - 0xfffebee9afff  SYSV00404404 (deleted)  ???
0xfffebee9b000 - 0xfffebef62fff  SYSV00404422 (deleted)  ???
0xfffebef63000 - 0xfffebf02afff  SYSV00404402 (deleted)  ???
0xfffebf02b000 - 0xfffebf0f2fff  SYSV00404408 (deleted)  ???
0xfffeec600000 - 0xfffeec63cfff  dmabuf:  ???
0xfffeec63d000 - 0xfffeec6fdfff  val_camera_stream_3_1  ???
0xfffeec6fe000 - 0xfffeec7befff  val_camera_stream_2_1  ???
0xfffeef9bf000 - 0xfffeef9ebfff  dmabuf:  ???
0xfffeef9ec000 - 0xfffeefa3dfff  __db.003  ???
0xfffeefdbe000 - 0xfffeefe7efff  val_camera_stream_9_1  ???
0xfffeefe7f000 - 0xfffeeff3ffff  val_camera_stream_10_1  ???
0xfffef19c3000 - 0xfffef19e6fff  dmabuf:  ???
0xfffef19e9000 - 0xfffef19eafff  ctrl  ???
0xfffef19eb000 - 0xfffef1ab2fff  SYSV00404719 (deleted)  ???
0xfffef1ab3000 - 0xfffef1cc3fff  SYSV00401d32 (deleted)  ???
0xfffef79cf000 - 0xfffef7a96fff  SYSV00404416 (deleted)  ???
0xfffef9a9b000 - 0xfffef9b62fff  SYSV00404418 (deleted)  ???
0xffff04577000 - 0xffff06973fff  libnvrtc.so.11.2  ???
0xffff06eab000 - 0xffff06eb5fff  libnss_files.so.2  ???
0xffff077ce000 - 0xffff077dffff  libnvidia-kms.so.541.2.0  ???
0xffff077f3000 - 0xffff077fafff  libnvrm_stream.so  ???
0xffff0780c000 - 0xffff07824fff  libtegrawfd.so  ???
0xffff07837000 - 0xffff07838fff  liberror_interface.so  ???
0xffff0784b000 - 0xffff07a3ffff  libdb_cxx-18.1.so  ???
0xffff07a5c000 - 0xffff07c23fff  libdb-18.1.so  ???
0xffff07c3f000 - 0xffff0acf3fff  libnvinfer_safe.so  ???
0xffff0ad5e000 - 0xffff0ad82fff  libcudla.so.1  ???
0xffff0ad96000 - 0xffff0b545fff  libnvdla_compiler.so  ???
0xffff0b588000 - 0xffff0b65bfff  libnvvideo.so  ???
0xffff0b66d000 - 0xffff0b6a4fff  libnvparser.so  ???
0xffff0b6b6000 - 0xffff0bce7fff  libnvdla_runtime.so  ???
0xffff0bd15000 - 0xffff0bf51fff  libnvvic.so  ???
0xffff0bf64000 - 0xffff0bf89fff  libnvrm_surface.so  ???
0xffff0bf9d000 - 0xffff0c0ddfff  libnppc.so.11  ???
0xffff0c0ff000 - 0xffff0c101fff  libvic_interface.so  ???
0xffff0c113000 - 0xffff0c116fff  libwfd_interface.so  ???
0xffff0c128000 - 0xffff0c12afff  libjpe_interface.so  ???
0xffff0c13c000 - 0xffff0c15dfff  libencoder_interface.so  ???
0xffff0c16f000 - 0xffff0c1a0fff  libpersist.so.3  ???
0xffff0c1b3000 - 0xffff0c524fff  libtokenizers_c.so  ???
0xffff0c5a4000 - 0xffff0c5c6fff  libtokenizers_cpp.so  ???
0xffff0c5d8000 - 0xffff13079fff  libhpo_llm.so  ???
0xffff13168000 - 0xffff16582fff  libtce.so  ???
0xffff165a8000 - 0xffff16634fff  libcognition_vlm_gpu_platform_mlc_model_runtime.so  ???  (WARNING: No symbols, libcognition_vlm_gpu_platform_mlc_model_runtime.so, E7D7FE4983A47F54072A0CA5A4AE62C90)
0xffff16648000 - 0xffff171b7fff  libagent_vla_idls.so  ???
0xffff1720b000 - 0xffff17d30fff  libvla_idls.so  ???
0xffff17d79000 - 0xffff18900fff  liblmm_idls.so  ???
0xffff18954000 - 0xffff197a3fff  libmap_and_navi_idls.so  ???
0xffff1984c000 - 0xffff1a3a6fff  libe2e_pnp_idls.so  ???
0xffff1a3f4000 - 0xffff1af04fff  libdds_location_idls.so  ???
0xffff1af4c000 - 0xffff1bb3bfff  libehorizon_idls.so  ???
0xffff1bb96000 - 0xffff1c665fff  libdiagnose_idls.so  ???
0xffff1c6a4000 - 0xffff1d3ddfff  libvs_idls.so  ???
0xffff1d463000 - 0xffff1df35fff  libradar.so  ???
0xffff1df76000 - 0xffff1ea52fff  libgnss.so  ???
0xffff1ea95000 - 0xffff1f5d2fff  libdata_recorder_idls.so  ???
0xffff1f61c000 - 0xffff201cafff  libvehicle_status.so  ???
0xffff2023a000 - 0xffff20cf8fff  libodometry_idls.so  ???
0xffff20d36000 - 0xffff21ac0fff  libenv_model_idls.so  ???
0xffff21b49000 - 0xffff2262cfff  libpas.so  ???
0xffff2266f000 - 0xffff23216fff  libbev_perception_idls.so  ???
0xffff2326f000 - 0xffff23fedfff  libperception_idls.so  ???
0xffff24078000 - 0xffff2409bfff  libglog.so.1  ???
0xffff240be000 - 0xffff245ebfff  libnvinfer_plugin.so.10  ???
0xffff25097000 - 0xffff25470fff  libnvonnxparser.so.10  ???
0xffff2549c000 - 0xffff2d0cffff  libnvinfer.so.10  ???
0xffff2f085000 - 0xffff2f0adfff  libcudnn.so.8  ???
0xffff2f0c1000 - 0xffff2f0d7fff  libnvmedialdc.so  ???
0xffff2f0e9000 - 0xffff2f0f7fff  libnvmedia_tensor.so  ???
0xffff2f109000 - 0xffff2f116fff  libnvmedia_iofa_sci.so  ???
0xffff2f128000 - 0xffff2f131fff  libnvmedia_ijpe_sci.so  ???
0xffff2f143000 - 0xffff2f14cfff  libnvmedia_ijpd_sci.so  ???
0xffff2f15e000 - 0xffff2f16dfff  libnvmedia_iep_sci.so  ???
0xffff2f17f000 - 0xffff2f18bfff  libnvmedia_ide_sci.so  ???
0xffff2f19d000 - 0xffff2f1a3fff  libnvmedia_ide_parser.so  ???
0xffff2f1b6000 - 0xffff2f1bbfff  libnvmedia_eglstream.so  ???
0xffff2f1cd000 - 0xffff2f1e3fff  libnvmedia_dla.so  ???
0xffff2f1f5000 - 0xffff2f205fff  libnvmedia2d.so  ???
0xffff2f218000 - 0xffff2f21dfff  libnvscicommon_debug.so  ???
0xffff2f22f000 - 0xffff2f23bfff  libnvscic2cpcie.so  ???
0xffff2f24d000 - 0xffff2f250fff  libnvscic2ccommon.so  ???
0xffff2f262000 - 0xffff2f26afff  libnvscic2c.so  ???
0xffff2f27c000 - 0xffff313b4fff  libnppig.so.11  ???
0xffff313dc000 - 0xffff319e5fff  libnppicc.so.11  ???
0xffff31a0a000 - 0xffff31a1dfff  dmabuf:  ???
0xffff31a1e000 - 0xffff31a6ffff  libopencv_xphoto.so.4.3  ???
0xffff31a87000 - 0xffff31a98fff  libopencv_xobjdetect.so.4.3  ???
0xffff31aaa000 - 0xffff31bb7fff  libopencv_ximgproc.so.4.3  ???
0xffff31ccd000 - 0xffff31f6afff  libopencv_xfeatures2d.so.4.3  ???
0xffff31f80000 - 0xffff31fc2fff  libopencv_videostab.so.4.3  ???
0xffff31fd5000 - 0xffff32025fff  libopencv_videoio.so.4.3  ???
0xffff3203a000 - 0xffff32094fff  libopencv_video.so.4.3  ???
0xffff320a8000 - 0xffff322b8fff  libopencv_tracking.so.4.3  ???
0xffff322cf000 - 0xffff32319fff  libopencv_text.so.4.3  ???
0xffff3232b000 - 0xffff3236cfff  libopencv_surface_matching.so.4.3  ???
0xffff3237e000 - 0xffff3239dfff  libopencv_superres.so.4.3  ???
0xffff323a0000 - 0xffff323b1fff  libopencv_structured_light.so.4.3  ???
0xffff323c3000 - 0xffff3244ffff  libopencv_stitching.so.4.3  ???
0xffff32464000 - 0xffff32488fff  libopencv_stereo.so.4.3  ???
0xffff3249b000 - 0xffff324bffff  libopencv_shape.so.4.3  ???
0xffff324d1000 - 0xffff324f6fff  libopencv_saliency.so.4.3  ???
0xffff32509000 - 0xffff325defff  libopencv_rgbd.so.4.3  ???
0xffff325f7000 - 0xffff32614fff  libopencv_reg.so.4.3  ???
0xffff32626000 - 0xffff32631fff  libopencv_rapid.so.4.3  ???
0xffff32642000 - 0xffff32652fff  libopencv_quality.so.4.3  ???
0xffff32664000 - 0xffff3266cfff  libopencv_plot.so.4.3  ???
0xffff3267d000 - 0xffff3271dfff  libopencv_photo.so.4.3  ???
0xffff32730000 - 0xffff32737fff  libopencv_phase_unwrapping.so.4.3  ???
0xffff32748000 - 0xffff327a7fff  libopencv_optflow.so.4.3  ???
0xffff327aa000 - 0xffff32808fff  libopencv_objdetect.so.4.3  ???
0xffff3281b000 - 0xffff328adfff  libopencv_ml.so.4.3  ???
0xffff328c1000 - 0xffff328e3fff  libopencv_line_descriptor.so.4.3  ???
0xffff328f5000 - 0xffff32c0cfff  libopencv_imgproc.so.4.3  ???
0xffff32ccf000 - 0xffff32e95fff  libopencv_imgcodecs.so.4.3  ???
0xffff32eb8000 - 0xffff330b7fff  libopencv_gapi.so.4.3  ???
0xffff330ce000 - 0xffff33162fff  libopencv_features2d.so.4.3  ???
0xffff3317a000 - 0xffff331e8fff  libopencv_face.so.4.3  ???
0xffff331fc000 - 0xffff33598fff  libopencv_dnn.so.4.3  ???
0xffff335c6000 - 0xffff338c2fff  libopencv_core.so.4.3  ???
0xffff338e5000 - 0xffff33a22fff  libopencv_calib3d.so.4.3  ???
0xffff33a37000 - 0xffff345e8fff  libcity_noa_hmi_idls.so  ???
0xffff34644000 - 0xffff351eefff  libdriver_interaction_idls.so  ???
0xffff3524c000 - 0xffff35ed8fff  libxcu_soa_msg.so  ???
0xffff35f79000 - 0xffff36c11fff  libpnc_idls.so  ???
0xffff36c9a000 - 0xffff3795afff  libbehavior_idls.so  ???
0xffff37a00000 - 0xffff38584fff  libfunction_idls.so  ???
0xffff385d9000 - 0xffff38748fff  libperception_base.so  ???
0xffff38763000 - 0xffff396d6fff  libe2e_nodes.so  ???  (WARNING: No symbols, libe2e_nodes.so, F99A583C467A650D3689BEA318CE29C40)
0xffff39731000 - 0xffff39739fff  dmabuf:  ???
0xffff3973a000 - 0xffff39783fff  libopencv_flann.so.4.3  ???
0xffff39796000 - 0xffff39811fff  libopencv_datasets.so.4.3  ???
0xffff39825000 - 0xffff39a8afff  libceres.so.2  ???
0xffff39aa1000 - 0xffff39c9afff  libe2e_common.so  ???
0xffff3acba000 - 0xffff3aecafff  SYSV00401d16 (deleted)  ???
0xffff3aecb000 - 0xffff3af92fff  SYSV00401cf4 (deleted)  ???
0xffff3af93000 - 0xffff3b05afff  SYSV00401d05 (deleted)  ???
0xffff3b05b000 - 0xffff3b122fff  SYSV00401d0c (deleted)  ???
0xffff3b3a3000 - 0xffff3b46afff  SYSV00401d04 (deleted)  ???
0xffff3b46b000 - 0xffff3b532fff  SYSV00401d22 (deleted)  ???
0xffff3b533000 - 0xffff3b5fafff  SYSV00401d2c (deleted)  ???
0xffff3b5fb000 - 0xffff3b80bfff  SYSV00401d1a (deleted)  ???
0xffff3b80c000 - 0xffff3b8d3fff  SYSV00401d28 (deleted)  ???
0xffff3b8d4000 - 0xffff3bae4fff  SYSV00401d18 (deleted)  ???
0xffff3bae5000 - 0xffff3bbacfff  SYSV00401d08 (deleted)  ???
0xffff3bbad000 - 0xffff3bc74fff  SYSV00401cf6 (deleted)  ???
0xffff3bc75000 - 0xffff3be85fff  SYSV00401d2a (deleted)  ???
0xffff3be86000 - 0xffff3bf4dfff  SYSV00401d0e (deleted)  ???
0xffff3bf4e000 - 0xffff3c015fff  SYSV00401d2e (deleted)  ???
0xffff3c016000 - 0xffff3c0ddfff  SYSV00401cf2 (deleted)  ???
0xffff3c35e000 - 0xffff3c56efff  SYSV00401d1e (deleted)  ???
0xffff3c56f000 - 0xffff3c636fff  SYSV00401cfc (deleted)  ???
0xffff3c637000 - 0xffff3c6fefff  SYSV00401d10 (deleted)  ???
0xffff50000000 - 0xffff50007fff  dmabuf:  ???
0xffff50008000 - 0xffff50009fff  libopencv_intensity_transform.so.4.3  ???
0xffff5001b000 - 0xffff5002ffff  libopencv_img_hash.so.4.3  ???
0xffff50041000 - 0xffff5004afff  libopencv_highgui.so.4.3  ???
0xffff5005b000 - 0xffff5009dfff  libopencv_ccalib.so.4.3  ???
0xffff500af000 - 0xffff502bffff  SYSV00401d32 (deleted)  ???
0xffff5afc8000 - 0xffff64c39fff  libcublasLt.so.11  ???
0xffff685cd000 - 0xffff6a9c5fff  libcublas.so.11  ???
0xffff6aa2a000 - 0xffff6b4d7fff  libmps_idls.so  ???
0xffff6bd14000 - 0xffff6c1d0fff  libgnat-24.so  ???
0xffff6d200000 - 0xffff6d232fff  libnvscisync.so.1  ???
0xffff6eccd000 - 0xffff70802fff  libcuda.so.1  ???  (WARNING: No symbols, libcuda.so.1, FD0E58F6C7D468F37DEAE1DEF149D10F0)
0xffff71181000 - 0xffff71c7cfff  liblidar.so  ???
0xffff71cc1000 - 0xffff72ad9fff  libactive_safety_idls.so  ???
0xffff73c00000 - 0xffff73c08fff  dmabuf:  ???
0xffff73c09000 - 0xffff73c17fff  libopencv_hfs.so.4.3  ???
0xffff73c29000 - 0xffff73c5bfff  libopencv_bioinspired.so.4.3  ???
0xffff73c6e000 - 0xffff73d35fff  SYSV00401d0a (deleted)  ???
0xffff73d36000 - 0xffff73dfdfff  SYSV00401d14 (deleted)  ???
0xffff73dfe000 - 0xffff73e47fff  dmabuf:  ???
0xffff73e48000 - 0xffff73e81fff  __db.002  ???
0xffff73e82000 - 0xffff73e95fff  libnvcudla.so  ???
0xffff73ea7000 - 0xffff73f6efff  SYSV0040441c (deleted)  ???
0xffff7446f000 - 0xffff74536fff  SYSV00404417 (deleted)  ???
0xffff74537000 - 0xffff745fefff  SYSV00404416 (deleted)  ???
0xffff75800000 - 0xffff75801fff  dmabuf:  ???
0xffff75802000 - 0xffff75813fff  libopencv_fuzzy.so.4.3  ???
0xffff75825000 - 0xffff758ecfff  SYSV00401cfa (deleted)  ???
0xffff758ed000 - 0xffff759fdfff  SYSV00401d30 (deleted)  ???
0xffff761ff000 - 0xffff76257fff  dmabuf:  ???
0xffff76258000 - 0xffff76258fff  sem.val_camera_stream_9_1  ???
0xffff76259000 - 0xffff76259fff  sem.val_camera_stream_10_1  ???
0xffff7625a000 - 0xffff7625dfff  ctrl  ???
0xffff7625e000 - 0xffff7627ffff  dmabuf:  ???
0xffff76e00000 - 0xffff76e04fff  dmabuf:  ???
0xffff76e05000 - 0xffff76e39fff  libopencv_aruco.so.4.3  ???
0xffff76e6f000 - 0xffff76f36fff  SYSV00401d02 (deleted)  ???
0xffff76f37000 - 0xffff76ffefff  SYSV00401d12 (deleted)  ???
0xffff77c00000 - 0xffff77c10fff  dmabuf:  ???
0xffff77c11000 - 0xffff77c26fff  libopencv_dpm.so.4.3  ???
0xffff77c37000 - 0xffff77c57fff  libgflags.so.2.2  ???
0xffff77c6a000 - 0xffff77d31fff  SYSV00401cf8 (deleted)  ???
0xffff77d32000 - 0xffff77df9fff  SYSV00401d1c (deleted)  ???
0xffff795fd000 - 0xffff7a5fcfff  lios-ipc-shm-reset.pubsub.a0  ???
0xffff7a5fd000 - 0xffff7b5fcfff  low_fps.pubsub.a0  ???
0xffff7d600000 - 0xffff7d61cfff  libopencv_bgsegm.so.4.3  ???
0xffff7d62e000 - 0xffff7d65cfff  libgrid_map_core.so  ???
0xffff7d66f000 - 0xffff7d736fff  SYSV00401d00 (deleted)  ???
0xffff7d737000 - 0xffff7d7fefff  SYSV00401d24 (deleted)  ???
0xffff7e600000 - 0xffff7e603fff  dmabuf:  ???
0xffff7e604000 - 0xffff7e60ffff  libopencv_dnn_superres.so.4.3  ???
0xffff7e621000 - 0xffff7e65cfff  libnvstream_core_pubsub.so  ???
0xffff7e66f000 - 0xffff7e736fff  SYSV00401d06 (deleted)  ???
0xffff7e737000 - 0xffff7e7fefff  SYSV00401d20 (deleted)  ???
0xffff7f400000 - 0xffff7f405fff  dmabuf:  ???
0xffff7f406000 - 0xffff7f406fff  sem.val_camera_stream_3_1  ???
0xffff7f407000 - 0xffff7f408fff  ctrl  ???
0xffff7f409000 - 0xffff7f409fff  sem.val_camera_stream_2_1  ???
0xffff7f40a000 - 0xffff7f40cfff  ctrl  ???
0xffff7f40d000 - 0xffff7f413fff  libopencv_dnn_objdetect.so.4.3  ???
0xffff7f424000 - 0xffff7f427fff  libimage_interface.so  ???
0xffff7f439000 - 0xffff7f43afff  libnvstream_core_image.so  ???
0xffff7f44c000 - 0xffff7f46dfff  dmabuf:  ???
0xffff7f46e000 - 0xffff7f535fff  SYSV00401d07 (deleted)  ???
0xffff7f536000 - 0xffff7f5fdfff  SYSV00401d06 (deleted)  ???
0xffff80a00000 - 0xffff80a60fff  dmabuf:  ???
0xffff80a61000 - 0xffff80a73fff  libnvcucompat.so  ???
0xffff80a86000 - 0xffff80ac1fff  libgomp.so.1  ???
0xffff80ad4000 - 0xffff80b13fff  libmps_util.so  ???
0xffff80b27000 - 0xffff80b29fff  libnvrm_chip.so  ???
0xffff80b3b000 - 0xffff80b41fff  libnvrm_sync.so  ???
0xffff80b53000 - 0xffff80b54fff  libnvtegrahv.so  ???
0xffff80b66000 - 0xffff80b68fff  libnvsocsys.so  ???
0xffff80b7a000 - 0xffff80b8bfff  libnvrm_host1x.so  ???
0xffff80b9d000 - 0xffff80ba1fff  libnvscievent.so  ???  (WARNING: No symbols, libnvscievent.so, A2705538F1508BBE6BB4D76A98D5A3D30)
0xffff80bb3000 - 0xffff80bcdfff  libnvsciipc.so  ???
0xffff80be7000 - 0xffff80bebfff  libnvscicommon.so.1  ???
0xffff82800000 - 0xffff82808fff  dmabuf:  ???
0xffff82809000 - 0xffff82818fff  libnvos.so  ???
0xffff8282b000 - 0xffff82832fff  libnvrm_mem.so  ???
0xffff82844000 - 0xffff828a3fff  libnvrm_gpu.so  ???
0xffff828b9000 - 0xffff8293ffff  libnvscistream.so.1  ???  (WARNING: No symbols, libnvscistream.so.1, 0269BA2A01839B433B3D864B58DFDEFB0)
0xffff82956000 - 0xffff82960fff  libnvstream_core_block.so  ???
0xffff82972000 - 0xffff82979fff  libnvstream_core_network.so  ???
0xffff8298b000 - 0xffff829d1fff  libnvscibuf.so.1  ???
0xffff84c00000 - 0xffff84c0bfff  dmabuf:  ???
0xffff84c0c000 - 0xffff84c0ffff  libnvstream_core_error.so  ???
0xffff84c21000 - 0xffff84ccafff  libcudart.so.11.0  ???
0xffff84ce2000 - 0xffff84ce7fff  libnvstream_core_sync.so  ???
0xffff84cf9000 - 0xffff84d11fff  libnvstream_core_stream.so  ???
0xffff84d24000 - 0xffff84d4cfff  libnvstream_core_helper.so  ???
0xffff84d5f000 - 0xffff84d95fff  libnvstream_core_channel.so  ???
0xffff84da8000 - 0xffff84db4fff  libnvstream_core_buf.so  ???
0xffff84dc6000 - 0xffff84dd3fff  libgpu_interface.so  ???
0xffff84de5000 - 0xffff84decfff  libnvstream_core_utils.so  ???
0xffff85e00000 - 0xffff85e01fff  libnvstream_core_nvipc.so  ???
0xffff85e13000 - 0xffff85e97fff  libcamera_interface.so  ???
0xffff85eae000 - 0xffff85f0bfff  libfreq_reducer_node.so  ???
0xffff85f38000 - 0xffff85fc7fff  libzstd.so.1  ???
0xffff85fd9000 - 0xffff8601afff  libsodium.so.23  ???
0xffff8602c000 - 0xffff86118fff  libzmq.so.5  ???
0xffff86135000 - 0xffff86191fff  libyaml-cpp.so.0.7  ???
0xffff861a6000 - 0xffff861c5fff  liblog_upload_trigger.so  ???
0xffff861d8000 - 0xffff86209fff  libfmt.so.7  ???
0xffff8621d000 - 0xffff86224fff  libatomic.so.1  ???
0xffff86238000 - 0xffff864f9fff  libprotobuf.so.********  ???
0xffff8651a000 - 0xffff867defff  libprotoc.so.********  ???
0xffff867f9000 - 0xffff8681efff  libcompression.so  ???
0xffff86830000 - 0xffff86858fff  libconcurrent.so.3  ???
0xffff8686b000 - 0xffff868a4fff  libutils.so.3  ???
0xffff868b7000 - 0xffff868cbfff  libtimer.so.3  ???
0xffff868e0000 - 0xffff86a1cfff  libipc.so.3  ???
0xffff86a34000 - 0xffff86a34fff  libtrigger_log.so.3  ???
0xffff86a46000 - 0xffff86adefff  libm.so.6  ???
0xffff86af1000 - 0xffff86c4bfff  libc.so.6  ???  (WARNING: No symbols, libc.so.6, 28FB374C98BEC3DEEF5911628B6643300)
0xffff86c64000 - 0xffff86c76fff  libgcc_s.so.1  ???
0xffff86c88000 - 0xffff86e4cfff  libstdc++.so.6  ???
0xffff86e6d000 - 0xffff86e86fff  liblog.so.3  ???
0xffff86e99000 - 0xffff86f8afff  libconfig.so.3  ???
0xffff86fa2000 - 0xffff86fa8fff  librt.so.1  ???
0xffff86fba000 - 0xffff86fd5fff  libpthread.so.0  ???  (WARNING: No symbols, libpthread.so.0, C399C41C2FE4E5F96B4460B76B2ED5E60)
0xffff86feb000 - 0xffff86fedfff  libdl.so.2  ???
0xffff86fff000 - 0xffff87012fff  dmabuf:  ???
0xffff87013000 - 0xffff875c0fff  libnddscore.so  ???
0xffff87642000 - 0xffff87c0afff  libnddsc.so  ???
0xffff87cac000 - 0xffff87dbefff  libnddscpp2.so  ???
0xffff87e21000 - 0xffff87fb0fff  libnddscpp.so  ???
0xffff8804f000 - 0xffff88076fff  librticonnextmsgcpp2.so  ???
0xffff88094000 - 0xffff880b4fff  librticonnextmsgcpp.so  ???
0xffff880cf000 - 0xffff880dafff  librticonnextmsgc.so  ???
0xffff880ee000 - 0xffff88108fff  libnddsmetp.so  ???
0xffff8811d000 - 0xffff88131fff  librtidds.so.3  ???
0xffff88144000 - 0xffff88150fff  libcom.so.3  ???
0xffff88163000 - 0xffff881d2fff  libnode.so.3  ???
0xffff881e7000 - 0xffff88267fff  libscheduling.so.3  ???
0xffff8827b000 - 0xffff882ecfff  librecording.so.3  ???
0xffff88302000 - 0xffff8833cfff  libapp.so.3  ???
0xffff8834f000 - 0xffff883f5fff  libjemalloc.so.2  ???
0xffff8862b000 - 0xffff8864bfff  ld-linux-aarch64.so.1  ???
0xffff8864c000 - 0xffff8864cfff  ctrl  ???
0xffff8865b000 - 0xffff8865bfff  linux-gate.so  ???
