MODULE Linux arm64 A388D6CAA9D4514BD97BE97E7015555B0 libpamc.so.0
INFO CODE_ID CAD688A3D4A94B51D97BE97E7015555B139ABBF0
PUBLIC f88 0 pamc_start
PUBLIC 1118 0 pamc_end
PUBLIC 1268 0 pamc_converse
PUBLIC 18c8 0 pamc_disable
PUBLIC 19e0 0 pamc_load
PUBLIC 1dc8 0 pamc_list_agents
STACK CFI INIT e78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee8 48 .cfa: sp 0 + .ra: x30
STACK CFI eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ef4 x19: .cfa -16 + ^
STACK CFI f2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f38 50 .cfa: sp 0 + .ra: x30
STACK CFI f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT f88 18c .cfa: sp 0 + .ra: x30
STACK CFI f8c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f9c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI fac x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1020 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1028 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1030 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10a4 x19: x19 x20: x20
STACK CFI 10a8 x21: x21 x22: x22
STACK CFI 10ac x25: x25 x26: x26
STACK CFI 10b0 x27: x27 x28: x28
STACK CFI 10bc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 10c0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 10cc x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 10ec x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10f8 x19: x19 x20: x20
STACK CFI 10fc x21: x21 x22: x22
STACK CFI 1100 x27: x27 x28: x28
STACK CFI 1110 x25: x25 x26: x26
STACK CFI INIT 1118 14c .cfa: sp 0 + .ra: x30
STACK CFI 111c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1124 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 112c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1158 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1174 x25: .cfa -32 + ^
STACK CFI 120c x25: x25
STACK CFI 1218 x23: x23 x24: x24
STACK CFI 1244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1248 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 1250 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1258 x23: x23 x24: x24
STACK CFI 125c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1260 x25: .cfa -32 + ^
STACK CFI INIT 1268 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 126c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1274 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1284 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 13ac x23: x23 x24: x24
STACK CFI 13d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 141c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 142c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1480 x25: x25 x26: x26
STACK CFI 1494 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1500 x25: x25 x26: x26
STACK CFI 1504 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 150c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1564 x25: x25 x26: x26
STACK CFI 1568 x27: x27 x28: x28
STACK CFI 156c x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 15f0 x25: x25 x26: x26
STACK CFI 15f4 x27: x27 x28: x28
STACK CFI 15f8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 15fc x25: x25 x26: x26
STACK CFI 1600 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1604 x23: x23 x24: x24
STACK CFI 1608 x25: x25 x26: x26
STACK CFI 160c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1614 x23: x23 x24: x24
STACK CFI 1618 x25: x25 x26: x26
STACK CFI 161c x27: x27 x28: x28
STACK CFI 1620 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1624 x23: x23 x24: x24
STACK CFI 1628 x25: x25 x26: x26
STACK CFI 162c x27: x27 x28: x28
STACK CFI 1630 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1670 x25: x25 x26: x26
STACK CFI 1674 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1684 x25: x25 x26: x26
STACK CFI 1688 x23: x23 x24: x24
STACK CFI 168c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1690 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1694 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1698 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16b4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 16b8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 16c4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 16e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 16e8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 1718 64 .cfa: sp 0 + .ra: x30
STACK CFI 1720 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1728 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1734 x21: .cfa -16 + ^
STACK CFI 1774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1780 5c .cfa: sp 0 + .ra: x30
STACK CFI 1788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1790 x19: .cfa -16 + ^
STACK CFI 17d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17e0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 17e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1834 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1840 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1890 x21: x21 x22: x22
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1898 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 18b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 18c8 114 .cfa: sp 0 + .ra: x30
STACK CFI 18d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1924 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 195c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1960 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 197c x23: .cfa -16 + ^
STACK CFI 19a4 x23: x23
STACK CFI 19bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 19d8 x23: x23
STACK CFI INIT 19e0 348 .cfa: sp 0 + .ra: x30
STACK CFI 19e4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 19ec x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 19f8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a68 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 1a9c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1ad8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1b40 x25: x25 x26: x26
STACK CFI 1b50 x23: x23 x24: x24
STACK CFI 1b54 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1c54 x25: x25 x26: x26
STACK CFI 1c5c x23: x23 x24: x24
STACK CFI 1c60 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1c94 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1c98 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1c9c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1cec x25: x25 x26: x26
STACK CFI 1cf0 x23: x23 x24: x24
STACK CFI 1cf4 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 1d28 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1dc8 178 .cfa: sp 0 + .ra: x30
STACK CFI 1dcc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1dd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1dec x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^
STACK CFI 1f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1f0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
