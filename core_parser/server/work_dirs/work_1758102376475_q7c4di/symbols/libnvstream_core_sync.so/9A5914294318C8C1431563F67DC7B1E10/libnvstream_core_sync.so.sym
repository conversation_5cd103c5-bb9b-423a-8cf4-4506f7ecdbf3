MODULE Linux arm64 9A5914294318C8C1431563F67DC7B1E10 libnvstream_core_sync.so
INFO CODE_ID 2914599A1843C1C8431563F67DC7B1E1
PUBLIC 3478 0 _init
PUBLIC 3800 0 call_weak_fn
PUBLIC 3814 0 deregister_tm_clones
PUBLIC 3844 0 register_tm_clones
PUBLIC 3880 0 __do_global_dtors_aux
PUBLIC 38d0 0 frame_dummy
PUBLIC 38e0 0 linvs::sync::GetBaseSyncAttrList(linvs::sync::SyncAttrList&, linvs::sync::BaseSyncDesc const&)
PUBLIC 3990 0 linvs::sync::SyncAttrList::SyncAttrList()
PUBLIC 3a80 0 linvs::sync::SyncAttrList::SyncAttrList(linvs::sync::SyncAttrList const&)
PUBLIC 3b80 0 linvs::sync::SyncAttrList::operator=(linvs::sync::SyncAttrList const&)
PUBLIC 3cb0 0 linvs::sync::SyncAttrList::operator bool() const
PUBLIC 3cd0 0 linvs::sync::SyncAttrList::operator*()
PUBLIC 3ce0 0 linvs::sync::SyncAttrList::operator*() const
PUBLIC 3cf0 0 linvs::sync::SyncAttrList::~SyncAttrList()
PUBLIC 3db0 0 linvs::sync::SyncAttrList::AllocObj(linvs::sync::SyncObj&)
PUBLIC 3e30 0 linvs::sync::SyncAttrList::SetAttrs(NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3e80 0 linvs::sync::SyncAttrList::GetSlotCount()
PUBLIC 3e90 0 linvs::sync::SyncAttrList::GetAttrs(NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3ee0 0 linvs::sync::SyncAttrList::GetSlotAttrs(unsigned long, NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3f30 0 linvs::sync::SyncAttrList::DebugDump(void**, unsigned long*)
PUBLIC 3f80 0 linvs::sync::SyncAttrList::AppendUnreconciled(std::vector<linvs::sync::SyncAttrList*, std::allocator<linvs::sync::SyncAttrList*> > const&)
PUBLIC 4090 0 linvs::sync::SyncAttrList::Reconciled()
PUBLIC 40f0 0 linvs::sync::SyncAttrList::ValidateReconciled(std::vector<linvs::sync::SyncAttrList*, std::allocator<linvs::sync::SyncAttrList*> > const&)
PUBLIC 4200 0 linvs::sync::SyncAttrList::Reconcile(std::vector<linvs::sync::SyncAttrList const*, std::allocator<linvs::sync::SyncAttrList const*> > const&, linvs::sync::SyncAttrList&)
PUBLIC 4350 0 linvs::sync::SyncAttrList::SyncAttrList(NvSciSyncAttrListRec* const&)
PUBLIC 4470 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4480 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 44e0 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4500 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4510 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4520 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 45e0 0 linvs::sync::CpuWaiter::~CpuWaiter()
PUBLIC 4600 0 linvs::sync::CpuWaiter::operator*()
PUBLIC 4610 0 linvs::sync::CpuWaiter::Wait(linvs::sync::SyncFence&, int)
PUBLIC 4680 0 linvs::sync::SyncFence::operator*() const
PUBLIC 4690 0 linvs::sync::SyncFence::operator*()
PUBLIC 46a0 0 linvs::sync::SyncFence::Reset()
PUBLIC 46b0 0 linvs::sync::SyncFence::~SyncFence()
PUBLIC 46d0 0 linvs::sync::SyncFence::Clear()
PUBLIC 46e0 0 linvs::sync::SyncFence::ExtractFence(unsigned long&, unsigned long&)
PUBLIC 4730 0 linvs::sync::SyncFence::Dup(linvs::sync::SyncFence const&)
PUBLIC 4790 0 linvs::sync::SyncFence::SyncFence(linvs::sync::SyncFence const&)
PUBLIC 47a0 0 linvs::sync::SyncFence::operator=(linvs::sync::SyncFence const&)
PUBLIC 47e0 0 linvs::sync::SyncModule::SyncModule()
PUBLIC 4830 0 linvs::sync::SyncModule::~SyncModule()
PUBLIC 4850 0 linvs::sync::SyncModule::operator*()
PUBLIC 4860 0 linvs::sync::SyncModule::operator*() const
PUBLIC 4870 0 linvs::sync::SyncModule::operator bool()
PUBLIC 4880 0 linvs::sync::SyncModule::CreateAttrList(linvs::sync::SyncAttrList&)
PUBLIC 48f0 0 linvs::sync::SyncModule::AllocCpuWaiter(linvs::sync::CpuWaiter&)
PUBLIC 4960 0 linvs::sync::SyncObj::SyncObj(NvSciSyncObjRec* const&)
PUBLIC 4970 0 linvs::sync::SyncObj::operator bool() const
PUBLIC 4980 0 linvs::sync::SyncObj::operator*() const
PUBLIC 4990 0 linvs::sync::SyncObj::operator*()
PUBLIC 49a0 0 linvs::sync::SyncObj::AllocFence(linvs::sync::SyncFence&)
PUBLIC 4a10 0 linvs::sync::SyncObj::Reset()
PUBLIC 4a40 0 linvs::sync::SyncObj::~SyncObj()
PUBLIC 4a60 0 linvs::sync::SyncObj::Dup(linvs::sync::SyncObj const&)
PUBLIC 4ac0 0 linvs::sync::SyncObj::SyncObj(linvs::sync::SyncObj const&)
PUBLIC 4b10 0 linvs::sync::SyncObj::operator=(linvs::sync::SyncObj const&)
PUBLIC 4b6c 0 _fini
STACK CFI INIT 3814 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3844 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3880 50 .cfa: sp 0 + .ra: x30
STACK CFI 3890 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3898 x19: .cfa -16 + ^
STACK CFI 38c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3960 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3964 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4480 60 .cfa: sp 0 + .ra: x30
STACK CFI 4484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4494 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 44ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4500 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3990 ec .cfa: sp 0 + .ra: x30
STACK CFI 3994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 399c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3a80 fc .cfa: sp 0 + .ra: x30
STACK CFI 3a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3aa4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3af0 x21: x21 x22: x22
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3b80 124 .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ba0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3c4c x21: x21 x22: x22
STACK CFI 3c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3c6c x21: x21 x22: x22
STACK CFI 3c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3cb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ce0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cf0 bc .cfa: sp 0 + .ra: x30
STACK CFI 3cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3db0 74 .cfa: sp 0 + .ra: x30
STACK CFI 3db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3dc8 x19: .cfa -16 + ^
STACK CFI 3dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3df0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e30 50 .cfa: sp 0 + .ra: x30
STACK CFI 3e34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3e80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e90 50 .cfa: sp 0 + .ra: x30
STACK CFI 3e94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3eb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3eb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ee0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3ee4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3f30 50 .cfa: sp 0 + .ra: x30
STACK CFI 3f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3f80 104 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f98 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 403c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4040 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4090 5c .cfa: sp 0 + .ra: x30
STACK CFI 4094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40f0 10c .cfa: sp 0 + .ra: x30
STACK CFI 40f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 40fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4108 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 41b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 41bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4200 14c .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 420c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4218 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 42f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4520 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 452c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4350 11c .cfa: sp 0 + .ra: x30
STACK CFI 4354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 435c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4368 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 45e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 45ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4600 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4610 68 .cfa: sp 0 + .ra: x30
STACK CFI 4614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 461c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 464c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46b0 14 .cfa: sp 0 + .ra: x30
STACK CFI 46b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46e0 50 .cfa: sp 0 + .ra: x30
STACK CFI 46e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4708 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 470c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4730 60 .cfa: sp 0 + .ra: x30
STACK CFI 4734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 473c x19: .cfa -16 + ^
STACK CFI 4760 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4764 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4790 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 47e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 47e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 47f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4808 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4830 20 .cfa: sp 0 + .ra: x30
STACK CFI 483c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4848 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4870 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4880 68 .cfa: sp 0 + .ra: x30
STACK CFI 4884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4894 x19: .cfa -16 + ^
STACK CFI 48b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 48f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4904 x19: .cfa -16 + ^
STACK CFI 4928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 492c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4960 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4970 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 49a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49b4 x19: .cfa -16 + ^
STACK CFI 49d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a10 2c .cfa: sp 0 + .ra: x30
STACK CFI 4a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a1c x19: .cfa -16 + ^
STACK CFI 4a38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a40 14 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a60 60 .cfa: sp 0 + .ra: x30
STACK CFI 4a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a6c x19: .cfa -16 + ^
STACK CFI 4a90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4aa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ac0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4acc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b10 5c .cfa: sp 0 + .ra: x30
STACK CFI 4b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
