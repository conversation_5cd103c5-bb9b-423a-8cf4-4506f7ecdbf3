MODULE Linux arm64 6ED34F8A1A9219EC0A55337CDEABB1550 libefivar.so.1
INFO CODE_ID 8A4FD36E921AEC190A55337CDEABB1557F0FB4E2
PUBLIC 3310 0 efi_error_clear
PUBLIC 3f78 0 efi_variables_supported
PUBLIC 3f98 0 efi_guid_cmp
PUBLIC 3fa0 0 efi_guid_is_empty
PUBLIC 3fc8 0 efi_variable_get_attributes
PUBLIC 4018 0 efi_variable_set_attributes
PUBLIC 4028 0 efi_variable_get_data
PUBLIC 4070 0 efi_variable_set_data
PUBLIC 40a8 0 efi_variable_get_guid
PUBLIC 40e0 0 efi_variable_set_guid
PUBLIC 40f0 0 efi_variable_get_name
PUBLIC 4128 0 efi_variable_set_name
PUBLIC 4138 0 efi_variable_free
PUBLIC 4198 0 efi_variable_export
PUBLIC 4290 0 efi_variable_import
PUBLIC 44c8 0 efi_get_verbose
PUBLIC 44d8 0 efi_set_verbose
PUBLIC 44f0 0 efi_get_logfile
PUBLIC 4510 0 efi_error_set
PUBLIC 65a8 0 efi_chmod_variable
PUBLIC 6668 0 efi_get_next_variable_name
PUBLIC 6720 0 efi_get_variable_size
PUBLIC 67e0 0 efi_get_variable_attributes
PUBLIC 68a0 0 efi_get_variable_exists
PUBLIC 68f0 0 efi_get_variable
PUBLIC 6a68 0 efi_del_variable
PUBLIC 7020 0 efi_set_variable
PUBLIC 7098 0 efi_append_variable
PUBLIC 7390 0 efi_variable_realize
PUBLIC 7428 0 _efi_set_variable_variadic
PUBLIC 74a0 0 _efi_set_variable
PUBLIC 7658 0 efi_guid_to_id_guid
PUBLIC 7800 0 efi_guid_to_symbol
PUBLIC 7898 0 efi_guid_to_str
PUBLIC 8970 0 efi_guid_to_name
PUBLIC 8a20 0 efi_str_to_guid
PUBLIC 8a90 0 efi_name_to_guid
PUBLIC a8d8 0 efi_error_get
PUBLIC e860 0 efidp_make_emmc
PUBLIC e8f8 0 efidp_make_nvdimm
PUBLIC e9b0 0 efidp_make_sas
PUBLIC ea78 0 efidp_make_atapi
PUBLIC eb30 0 efidp_make_sata
PUBLIC ebe8 0 efidp_make_nvme
PUBLIC eca8 0 efidp_make_scsi
PUBLIC ed48 0 efidp_make_ipv4
PUBLIC ee60 0 efidp_make_mac_addr
PUBLIC ef40 0 efidp_make_generic
PUBLIC eff0 0 efidp_make_hd
PUBLIC f0d8 0 efidp_make_file
PUBLIC f320 0 efidp_make_edd10
PUBLIC f410 0 efidp_make_pci
PUBLIC f4b0 0 efidp_make_acpi_hid_ex
PUBLIC f678 0 efidp_make_acpi_hid
PUBLIC f718 0 efidp_make_vendor
PUBLIC f7b0 0 efidp_parse_device_path
PUBLIC f808 0 efidp_parse_device_node
PUBLIC f860 0 efidp_append_node
PUBLIC fbc8 0 efidp_duplicate_path
PUBLIC fd98 0 efidp_append_instance
PUBLIC ffd0 0 efidp_append_path
PUBLIC 104e0 0 efidp_set_node_data
PUBLIC 12750 0 efidp_format_device_path
STACK CFI INIT 3650 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3680 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 36c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36cc x19: .cfa -16 + ^
STACK CFI 3704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3708 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3718 44 .cfa: sp 0 + .ra: x30
STACK CFI 371c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3738 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3740 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3758 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3760 48 .cfa: sp 0 + .ra: x30
STACK CFI 3764 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3780 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37a8 168 .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 37b4 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 37c0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 37d4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 37e8 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 38f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 38f8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3910 5bc .cfa: sp 0 + .ra: x30
STACK CFI 3914 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3924 x23: .cfa -64 + ^
STACK CFI 392c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3940 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3e70 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ed0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3edc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f04 x21: .cfa -32 + ^
STACK CFI 3f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3f78 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fa0 24 .cfa: sp 0 + .ra: x30
STACK CFI 3fa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fc8 4c .cfa: sp 0 + .ra: x30
STACK CFI 3ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4018 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4028 44 .cfa: sp 0 + .ra: x30
STACK CFI 404c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4070 38 .cfa: sp 0 + .ra: x30
STACK CFI 4088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40a8 38 .cfa: sp 0 + .ra: x30
STACK CFI 40c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 40f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40fc x19: .cfa -16 + ^
STACK CFI 4124 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4128 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4138 5c .cfa: sp 0 + .ra: x30
STACK CFI 4140 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4148 x19: .cfa -16 + ^
STACK CFI 4184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4188 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4198 f8 .cfa: sp 0 + .ra: x30
STACK CFI 419c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 427c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 428c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4290 234 .cfa: sp 0 + .ra: x30
STACK CFI 4294 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 429c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 42ac x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 42c0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 42f8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4350 x27: .cfa -80 + ^
STACK CFI 43f0 x25: x25 x26: x26
STACK CFI 43f4 x27: x27
STACK CFI 441c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4420 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 4454 x25: x25 x26: x26
STACK CFI 4458 x27: x27
STACK CFI 445c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4460 x25: x25 x26: x26
STACK CFI 4468 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^
STACK CFI 446c x27: x27
STACK CFI 447c x25: x25 x26: x26
STACK CFI 4484 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4488 x27: .cfa -80 + ^
STACK CFI 449c x25: x25 x26: x26
STACK CFI 44a0 x27: x27
STACK CFI 44a4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^
STACK CFI 44bc x25: x25 x26: x26
STACK CFI 44c0 x27: x27
STACK CFI INIT 44c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 44d8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44f0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3310 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 331c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3330 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3398 x19: x19 x20: x20
STACK CFI 33ac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 33b0 fc .cfa: sp 0 + .ra: x30
STACK CFI 33b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 34b0 18c .cfa: sp 0 + .ra: x30
STACK CFI 34b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 34c4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 34d4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3514 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3524 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3554 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 3558 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3568 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 35b8 x23: x23 x24: x24
STACK CFI 35d4 x19: x19 x20: x20
STACK CFI 35e0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 35e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 362c x23: x23 x24: x24
STACK CFI 3638 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 4510 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4514 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 4520 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 452c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 4538 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 4548 x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 4550 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 4698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 469c .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI INIT 46d8 178 .cfa: sp 0 + .ra: x30
STACK CFI 46dc .cfa: sp 96 +
STACK CFI 46e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 46ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 46f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4700 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 470c x25: .cfa -16 + ^
STACK CFI 4788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 478c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4850 44c .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4864 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 486c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4884 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4b50 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4ca0 348 .cfa: sp 0 + .ra: x30
STACK CFI 4ca4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4cac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4cb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4cc0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4cf4 x25: .cfa -32 + ^
STACK CFI 4d38 x25: x25
STACK CFI 4db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4db8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 4dfc x25: x25
STACK CFI 4e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 4e88 x25: .cfa -32 + ^
STACK CFI 4f00 x25: x25
STACK CFI 4f04 x25: .cfa -32 + ^
STACK CFI 4f34 x25: x25
STACK CFI 4fb8 x25: .cfa -32 + ^
STACK CFI INIT 4fe8 198 .cfa: sp 0 + .ra: x30
STACK CFI 4fec .cfa: sp 160 +
STACK CFI 4ff0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4ff8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5020 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 512c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5130 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5180 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 5184 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5188 .cfa: x29 160 +
STACK CFI 518c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5198 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 51b4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 51c4 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 538c .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5728 470 .cfa: sp 0 + .ra: x30
STACK CFI 572c .cfa: sp 208 +
STACK CFI 5734 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 573c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 575c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 576c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5820 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 58bc x27: x27 x28: x28
STACK CFI 58c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5970 x27: x27 x28: x28
STACK CFI 59b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 59bc .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 59c4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 59fc x27: x27 x28: x28
STACK CFI 5a64 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5a80 x27: x27 x28: x28
STACK CFI 5af0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5b28 x27: x27 x28: x28
STACK CFI 5b5c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5b94 x27: x27 x28: x28
STACK CFI INIT 5b98 190 .cfa: sp 0 + .ra: x30
STACK CFI 5ba0 .cfa: sp 4448 +
STACK CFI 5ba8 .ra: .cfa -4440 + ^ x29: .cfa -4448 + ^
STACK CFI 5bb0 x21: .cfa -4416 + ^ x22: .cfa -4408 + ^
STACK CFI 5bbc x19: .cfa -4432 + ^ x20: .cfa -4424 + ^
STACK CFI 5c18 x23: .cfa -4400 + ^ x24: .cfa -4392 + ^
STACK CFI 5c24 x25: .cfa -4384 + ^
STACK CFI 5c84 x25: x25
STACK CFI 5c8c x23: x23 x24: x24
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cc4 .cfa: sp 4448 + .ra: .cfa -4440 + ^ x19: .cfa -4432 + ^ x20: .cfa -4424 + ^ x21: .cfa -4416 + ^ x22: .cfa -4408 + ^ x23: .cfa -4400 + ^ x24: .cfa -4392 + ^ x25: .cfa -4384 + ^ x29: .cfa -4448 + ^
STACK CFI 5cd8 x23: x23 x24: x24
STACK CFI 5cdc x25: x25
STACK CFI 5ce0 x23: .cfa -4400 + ^ x24: .cfa -4392 + ^ x25: .cfa -4384 + ^
STACK CFI 5ce8 x23: x23 x24: x24 x25: x25
STACK CFI 5d20 x23: .cfa -4400 + ^ x24: .cfa -4392 + ^
STACK CFI 5d24 x25: .cfa -4384 + ^
STACK CFI INIT 5d28 128 .cfa: sp 0 + .ra: x30
STACK CFI 5d2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d40 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e50 584 .cfa: sp 0 + .ra: x30
STACK CFI 5e54 .cfa: sp 208 +
STACK CFI 5e58 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5e60 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5e6c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5e88 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5f60 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5ffc x27: x27 x28: x28
STACK CFI 6000 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 60cc x27: x27 x28: x28
STACK CFI 6118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 611c .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 615c x27: x27 x28: x28
STACK CFI 6168 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 61a0 x27: x27 x28: x28
STACK CFI 61d4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6258 x27: x27 x28: x28
STACK CFI 62c4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 62fc x27: x27 x28: x28
STACK CFI 6304 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6308 x27: x27 x28: x28
STACK CFI 6334 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6368 x27: x27 x28: x28
STACK CFI 636c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 63d8 1cc .cfa: sp 0 + .ra: x30
STACK CFI 63dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63e4 .cfa: x29 48 +
STACK CFI 63e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 656c .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 65a8 bc .cfa: sp 0 + .ra: x30
STACK CFI 65ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6624 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6668 b8 .cfa: sp 0 + .ra: x30
STACK CFI 666c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 667c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 66a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 66dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6720 bc .cfa: sp 0 + .ra: x30
STACK CFI 6724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6734 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 675c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6760 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 679c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 67e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 67e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 67f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 681c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6820 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 685c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 68a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 68a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68ac x19: .cfa -48 + ^
STACK CFI 68e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 68f0 bc .cfa: sp 0 + .ra: x30
STACK CFI 68f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6904 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 692c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6930 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 696c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 69b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 69b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 69bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 69c8 x21: .cfa -64 + ^
STACK CFI 6a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6a34 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6a68 bc .cfa: sp 0 + .ra: x30
STACK CFI 6a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6aa8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b28 4f4 .cfa: sp 0 + .ra: x30
STACK CFI 6b2c .cfa: sp 2304 +
STACK CFI 6b30 .ra: .cfa -2232 + ^ x29: .cfa -2240 + ^
STACK CFI 6b34 .cfa: x29 2240 +
STACK CFI 6b38 x23: .cfa -2192 + ^ x24: .cfa -2184 + ^
STACK CFI 6b44 x27: .cfa -2160 + ^ x28: .cfa -2152 + ^
STACK CFI 6b68 x19: .cfa -2224 + ^ x20: .cfa -2216 + ^ x21: .cfa -2208 + ^ x22: .cfa -2200 + ^
STACK CFI 6b74 x25: .cfa -2176 + ^ x26: .cfa -2168 + ^
STACK CFI 6dc0 .cfa: sp 2304 +
STACK CFI 6de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6de4 .cfa: x29 2240 + .ra: .cfa -2232 + ^ x19: .cfa -2224 + ^ x20: .cfa -2216 + ^ x21: .cfa -2208 + ^ x22: .cfa -2200 + ^ x23: .cfa -2192 + ^ x24: .cfa -2184 + ^ x25: .cfa -2176 + ^ x26: .cfa -2168 + ^ x27: .cfa -2160 + ^ x28: .cfa -2152 + ^ x29: .cfa -2240 + ^
STACK CFI INIT 7020 78 .cfa: sp 0 + .ra: x30
STACK CFI 7024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 703c x19: .cfa -32 + ^
STACK CFI 7058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 705c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 7094 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7098 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 709c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 70ac x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 7100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7104 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 713c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7148 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7158 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7188 x27: .cfa -80 + ^
STACK CFI 721c x27: x27
STACK CFI 7228 x21: x21 x22: x22
STACK CFI 722c x23: x23 x24: x24
STACK CFI 7230 x25: x25 x26: x26
STACK CFI 7234 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^
STACK CFI 7278 x27: x27
STACK CFI 72a0 x21: x21 x22: x22
STACK CFI 72a4 x23: x23 x24: x24
STACK CFI 72a8 x25: x25 x26: x26
STACK CFI 72ac x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 72e8 x27: .cfa -80 + ^
STACK CFI 7318 x27: x27
STACK CFI 7344 x27: .cfa -80 + ^
STACK CFI 7364 x27: x27
STACK CFI 736c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7370 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7374 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7378 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 737c x27: .cfa -80 + ^
STACK CFI 7380 x27: x27
STACK CFI INIT 7390 94 .cfa: sp 0 + .ra: x30
STACK CFI 7394 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 73f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 73fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7414 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7428 78 .cfa: sp 0 + .ra: x30
STACK CFI 742c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7448 x19: .cfa -32 + ^
STACK CFI 7460 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7464 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 749c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 74a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 74a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74c0 x19: .cfa -32 + ^
STACK CFI 74d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 74dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 7514 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7518 140 .cfa: sp 0 + .ra: x30
STACK CFI 751c .cfa: sp 624 +
STACK CFI 752c .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 753c x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 7544 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 7558 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 7560 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 7640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7644 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x29: .cfa -624 + ^
STACK CFI INIT 7658 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 765c .cfa: sp 144 +
STACK CFI 7660 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7668 x21: .cfa -48 + ^
STACK CFI 7670 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 76ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 76f0 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7800 98 .cfa: sp 0 + .ra: x30
STACK CFI 7804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 780c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 786c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7898 15c .cfa: sp 0 + .ra: x30
STACK CFI 789c .cfa: sp 128 +
STACK CFI 78a0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 78ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 78d8 x21: .cfa -32 + ^
STACK CFI 7974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7978 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 79f8 524 .cfa: sp 0 + .ra: x30
STACK CFI 79fc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7a00 .cfa: x29 176 +
STACK CFI 7a04 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7a10 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7a30 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7a40 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 7d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7d24 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7f20 524 .cfa: sp 0 + .ra: x30
STACK CFI 7f24 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7f28 .cfa: x29 176 +
STACK CFI 7f2c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7f38 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7f58 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7f68 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 824c .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8448 524 .cfa: sp 0 + .ra: x30
STACK CFI 844c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8450 .cfa: x29 176 +
STACK CFI 8454 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 8460 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 8480 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 8490 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8774 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8970 ac .cfa: sp 0 + .ra: x30
STACK CFI 8974 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 897c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 899c x21: .cfa -48 + ^
STACK CFI 89dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 89e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8a20 6c .cfa: sp 0 + .ra: x30
STACK CFI 8a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8a90 220 .cfa: sp 0 + .ra: x30
STACK CFI 8a94 .cfa: sp 912 +
STACK CFI 8aa4 .ra: .cfa -904 + ^ x29: .cfa -912 + ^
STACK CFI 8ab0 x21: .cfa -880 + ^ x22: .cfa -872 + ^
STACK CFI 8ab8 x27: .cfa -832 + ^ x28: .cfa -824 + ^
STACK CFI 8ac4 x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI 8adc x19: .cfa -896 + ^ x20: .cfa -888 + ^
STACK CFI 8af8 x23: .cfa -864 + ^ x24: .cfa -856 + ^
STACK CFI 8c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8c64 .cfa: sp 912 + .ra: .cfa -904 + ^ x19: .cfa -896 + ^ x20: .cfa -888 + ^ x21: .cfa -880 + ^ x22: .cfa -872 + ^ x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^ x27: .cfa -832 + ^ x28: .cfa -824 + ^ x29: .cfa -912 + ^
STACK CFI INIT 8cb0 174 .cfa: sp 0 + .ra: x30
STACK CFI 8cb4 .cfa: sp 144 +
STACK CFI 8cbc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8cc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8ce8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8db8 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8e28 330 .cfa: sp 0 + .ra: x30
STACK CFI 8e2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8e34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8e40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8e4c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ef8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8f74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9158 6ac .cfa: sp 0 + .ra: x30
STACK CFI 915c .cfa: sp 480 +
STACK CFI 9160 .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 9168 x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 9174 x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 9180 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 919c x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 9244 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 9338 x27: x27 x28: x28
STACK CFI 9370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9374 .cfa: sp 480 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^ x29: .cfa -416 + ^
STACK CFI 9474 x27: x27 x28: x28
STACK CFI 9480 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 96d0 x27: x27 x28: x28
STACK CFI 977c x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 97c8 x27: x27 x28: x28
STACK CFI 97cc x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT 9808 70 .cfa: sp 0 + .ra: x30
STACK CFI 980c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 981c x19: .cfa -32 + ^
STACK CFI 9838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 983c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 9874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9878 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 987c .cfa: sp 192 +
STACK CFI 9884 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9890 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 98b0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 999c .cfa: sp 192 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 99a4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 99f4 x23: x23 x24: x24
STACK CFI 9a68 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9a84 x23: x23 x24: x24
STACK CFI 9a88 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9ab4 x25: .cfa -64 + ^
STACK CFI 9af8 x23: x23 x24: x24
STACK CFI 9afc x25: x25
STACK CFI 9b00 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9b2c x25: .cfa -64 + ^
STACK CFI 9b34 x23: x23 x24: x24 x25: x25
STACK CFI 9b68 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9b6c x25: .cfa -64 + ^
STACK CFI INIT 9b70 458 .cfa: sp 0 + .ra: x30
STACK CFI 9b74 .cfa: sp 224 +
STACK CFI 9b78 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9b80 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9b8c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9ba8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9c98 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9d3c x25: x25 x26: x26
STACK CFI 9d40 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9dac x25: x25 x26: x26
STACK CFI 9e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 9e14 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 9e20 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9e58 x25: x25 x26: x26
STACK CFI 9f20 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9f58 x25: x25 x26: x26
STACK CFI 9f8c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9fc4 x25: x25 x26: x26
STACK CFI INIT 9fc8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 9fcc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9fd4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9fe0 x21: .cfa -64 + ^
STACK CFI a048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a04c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT a080 1c0 .cfa: sp 0 + .ra: x30
STACK CFI a084 .cfa: sp 288 +
STACK CFI a08c .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI a094 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI a0b8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI a0c0 x23: .cfa -176 + ^
STACK CFI a1cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a1d0 .cfa: sp 288 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT a240 190 .cfa: sp 0 + .ra: x30
STACK CFI a244 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a250 x21: .cfa -160 + ^
STACK CFI a258 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI a330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a334 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT a3d0 160 .cfa: sp 0 + .ra: x30
STACK CFI a3d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a3e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a46c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a470 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT a530 3a4 .cfa: sp 0 + .ra: x30
STACK CFI a534 .cfa: sp 192 +
STACK CFI a548 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a554 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a55c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a580 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a718 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT a8d8 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT a938 3f24 .cfa: sp 0 + .ra: x30
STACK CFI a93c .cfa: sp 752 +
STACK CFI a940 .ra: .cfa -696 + ^ x29: .cfa -704 + ^
STACK CFI a944 .cfa: x29 704 +
STACK CFI a948 x21: .cfa -672 + ^ x22: .cfa -664 + ^
STACK CFI a950 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI a964 x19: .cfa -688 + ^ x20: .cfa -680 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^
STACK CFI a970 x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI b3dc .cfa: sp 752 +
STACK CFI b3fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b400 .cfa: x29 704 + .ra: .cfa -696 + ^ x19: .cfa -688 + ^ x20: .cfa -680 + ^ x21: .cfa -672 + ^ x22: .cfa -664 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^ x25: .cfa -640 + ^ x26: .cfa -632 + ^ x27: .cfa -624 + ^ x28: .cfa -616 + ^ x29: .cfa -704 + ^
STACK CFI INIT e860 98 .cfa: sp 0 + .ra: x30
STACK CFI e864 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e874 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e880 x21: .cfa -16 + ^
STACK CFI e8b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e8b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e8f8 b4 .cfa: sp 0 + .ra: x30
STACK CFI e8fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e90c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e918 x21: .cfa -16 + ^
STACK CFI e948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e94c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e98c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e9a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e9b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI e9b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e9c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e9cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ea30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ea34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ea74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ea78 b4 .cfa: sp 0 + .ra: x30
STACK CFI ea7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ea84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ea90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI eaa4 x23: .cfa -16 + ^
STACK CFI eae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI eae4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI eb28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT eb30 b4 .cfa: sp 0 + .ra: x30
STACK CFI eb34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI eb3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI eb48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI eb5c x23: .cfa -16 + ^
STACK CFI eb98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI eb9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI ebe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT ebe8 c0 .cfa: sp 0 + .ra: x30
STACK CFI ebec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ebf8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ec04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ec48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ec4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ec8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ec90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI eca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT eca8 a0 .cfa: sp 0 + .ra: x30
STACK CFI ecac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ecb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ecc4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ed00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ed04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ed44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ed48 118 .cfa: sp 0 + .ra: x30
STACK CFI ed4c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ed54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ed60 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ed70 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ed84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ed90 x27: .cfa -16 + ^
STACK CFI ee0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ee10 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI ee5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT ee60 dc .cfa: sp 0 + .ra: x30
STACK CFI ee64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ee6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ee74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ee84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI eec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI eec4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ef04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ef08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ef38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT ef40 b0 .cfa: sp 0 + .ra: x30
STACK CFI ef48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ef78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ef84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT eff0 e8 .cfa: sp 0 + .ra: x30
STACK CFI eff4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI effc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f004 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f014 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f028 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f034 x27: .cfa -16 + ^
STACK CFI f084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI f088 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI f0d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT f0d8 244 .cfa: sp 0 + .ra: x30
STACK CFI f0dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f0e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f0ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f2f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f2f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT f320 ec .cfa: sp 0 + .ra: x30
STACK CFI f324 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f334 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f358 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f3c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f3c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT f410 a0 .cfa: sp 0 + .ra: x30
STACK CFI f414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f420 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f42c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f46c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f4ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT f4b0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI f4b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f4bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f4c4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f4cc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI f4d8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f4e0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f5b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f5b8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI f614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f618 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT f678 9c .cfa: sp 0 + .ra: x30
STACK CFI f67c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f688 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f694 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f6cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f6d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT f718 94 .cfa: sp 0 + .ra: x30
STACK CFI f71c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f724 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f730 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f744 x23: .cfa -32 + ^
STACK CFI f774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f778 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI f7a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT f7b0 54 .cfa: sp 0 + .ra: x30
STACK CFI f7b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f7bc x19: .cfa -16 + ^
STACK CFI f800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f808 54 .cfa: sp 0 + .ra: x30
STACK CFI f80c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f814 x19: .cfa -16 + ^
STACK CFI f858 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f860 364 .cfa: sp 0 + .ra: x30
STACK CFI f864 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f870 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f878 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI f958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f95c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI f9f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f9f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI fa30 x25: .cfa -16 + ^
STACK CFI fa74 x25: x25
STACK CFI fa8c x25: .cfa -16 + ^
STACK CFI faa0 x25: x25
STACK CFI INIT fbc8 1cc .cfa: sp 0 + .ra: x30
STACK CFI fbcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fbd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fbe4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fcdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fce0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI fd24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fd28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT fd98 234 .cfa: sp 0 + .ra: x30
STACK CFI fd9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fda8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fdb4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fdd8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fe74 x19: x19 x20: x20
STACK CFI fe88 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fe8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI fef4 x19: x19 x20: x20
STACK CFI ff08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ff0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ff28 x19: x19 x20: x20
STACK CFI ff3c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ff40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ff4c x19: x19 x20: x20
STACK CFI ff68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ffc0 x19: x19 x20: x20
STACK CFI ffc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT ffd0 510 .cfa: sp 0 + .ra: x30
STACK CFI ffd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ffe4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI fff0 x25: .cfa -16 + ^
STACK CFI 10020 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 100e0 x21: x21 x22: x22
STACK CFI 100f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 100fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 10140 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10214 x21: x21 x22: x22
STACK CFI 10218 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 102a8 x21: x21 x22: x22
STACK CFI 102b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 102b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1033c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10410 x21: x21 x22: x22
STACK CFI 10414 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1044c x21: x21 x22: x22
STACK CFI 10450 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10488 x21: x21 x22: x22
STACK CFI 1048c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 104dc x21: x21 x22: x22
STACK CFI INIT 104e0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 104e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1051c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 10520 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10530 x21: .cfa -16 + ^
STACK CFI 10584 x19: x19 x20: x20
STACK CFI 10588 x21: x21
STACK CFI INIT 105c8 1a38 .cfa: sp 0 + .ra: x30
STACK CFI 105cc .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 105d0 .cfa: x29 464 +
STACK CFI 105d4 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 105f8 x19: .cfa -448 + ^ x20: .cfa -440 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 10610 x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 10908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1090c .cfa: x29 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 12000 750 .cfa: sp 0 + .ra: x30
STACK CFI 12004 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1200c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 12014 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12020 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1202c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 12034 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12454 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 12750 2a48 .cfa: sp 0 + .ra: x30
STACK CFI 12754 .cfa: sp 288 +
STACK CFI 1275c .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 12768 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 12780 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 127ac x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 127b8 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 127c8 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 1289c x21: x21 x22: x22
STACK CFI 128a0 x25: x25 x26: x26
STACK CFI 128a4 x27: x27 x28: x28
STACK CFI 128a8 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 129b4 x21: x21 x22: x22
STACK CFI 129b8 x25: x25 x26: x26
STACK CFI 129bc x27: x27 x28: x28
STACK CFI 129ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 129f0 .cfa: sp 288 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 12ee8 x21: x21 x22: x22
STACK CFI 12eec x25: x25 x26: x26
STACK CFI 12ef0 x27: x27 x28: x28
STACK CFI 12ef4 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 135f8 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13608 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 13a9c x21: x21 x22: x22
STACK CFI 13aa0 x25: x25 x26: x26
STACK CFI 13aa4 x27: x27 x28: x28
STACK CFI 13ab0 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 13f48 x21: x21 x22: x22
STACK CFI 13f4c x25: x25 x26: x26
STACK CFI 13f50 x27: x27 x28: x28
STACK CFI 13f54 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 14b78 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14b80 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 15178 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1517c x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 15180 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 15184 x27: .cfa -192 + ^ x28: .cfa -184 + ^
