MODULE Linux arm64 2223303E467AA3F5D433A3FFECDFFA1D0 libcanberra.so.0
INFO CODE_ID 3E3023227A46F5A3D433A3FFECDFFA1D3F724A45
PUBLIC 2f38 0 ca_context_destroy
PUBLIC 3080 0 ca_context_set_driver
PUBLIC 31e0 0 ca_context_change_device
PUBLIC 3310 0 ca_context_create
PUBLIC 3490 0 ca_context_open
PUBLIC 35c8 0 ca_context_change_props_full
PUBLIC 37f8 0 ca_context_change_props
PUBLIC 39b8 0 ca_context_play_full
PUBLIC 3d88 0 ca_context_play
PUBLIC 3f50 0 ca_context_cancel
PUBLIC 4098 0 ca_context_cache_full
PUBLIC 42b8 0 ca_context_cache
PUBLIC 4478 0 ca_strerror
PUBLIC 4588 0 ca_parse_cache_control
PUBLIC 46c8 0 ca_context_playing
PUBLIC 4878 0 ca_mutex_new
PUBLIC 48c0 0 ca_mutex_free
PUBLIC 4968 0 ca_mutex_lock
PUBLIC 4a00 0 ca_mutex_try_lock
PUBLIC 4aa8 0 ca_mutex_unlock
PUBLIC 4d20 0 ca_proplist_create
PUBLIC 4de0 0 ca_proplist_setf
PUBLIC 5108 0 ca_proplist_set
PUBLIC 5378 0 ca_proplist_sets
PUBLIC 55f8 0 ca_proplist_get_unlocked
PUBLIC 5738 0 ca_proplist_gets_unlocked
PUBLIC 5820 0 ca_proplist_destroy
PUBLIC 58d0 0 ca_proplist_merge
PUBLIC 5a90 0 ca_proplist_contains
PUBLIC 5b88 0 ca_proplist_merge_ap
PUBLIC 5cb0 0 ca_proplist_from_ap
PUBLIC 5dd8 0 ca_sound_file_open
PUBLIC 5fd0 0 ca_sound_file_close
PUBLIC 6050 0 ca_sound_file_get_nchannels
PUBLIC 60a0 0 ca_sound_file_get_rate
PUBLIC 60f0 0 ca_sound_file_get_sample_type
PUBLIC 6140 0 ca_sound_file_get_channel_map
PUBLIC 61a0 0 ca_sound_file_read_int16
PUBLIC 63c0 0 ca_sound_file_read_uint8
PUBLIC 65c8 0 ca_sound_file_read_arbitrary
PUBLIC 67d8 0 ca_sound_file_get_size
PUBLIC 6850 0 ca_sound_file_frame_size
PUBLIC 68c8 0 ca_vorbis_close
PUBLIC 6930 0 ca_vorbis_get_nchannels
PUBLIC 69d0 0 ca_vorbis_open
PUBLIC 6bd0 0 ca_vorbis_get_rate
PUBLIC 6c70 0 ca_vorbis_get_channel_map
PUBLIC 6dc8 0 ca_vorbis_read_s16ne
PUBLIC 7098 0 ca_vorbis_get_size
PUBLIC 7288 0 ca_wav_open
PUBLIC 75c0 0 ca_wav_close
PUBLIC 7628 0 ca_wav_get_nchannels
PUBLIC 7678 0 ca_wav_get_rate
PUBLIC 76c8 0 ca_wav_get_channel_map
PUBLIC 77d0 0 ca_wav_get_sample_type
PUBLIC 7830 0 ca_wav_read_s16le
PUBLIC 7ac0 0 ca_wav_read_u8
PUBLIC 7d48 0 ca_wav_get_size
PUBLIC 8ac0 0 ca_get_data_home
PUBLIC 8bf8 0 ca_get_data_dirs
PUBLIC 9c40 0 ca_theme_data_free
PUBLIC 9ef8 0 ca_lookup_sound_with_callback
PUBLIC a470 0 ca_lookup_sound
PUBLIC a490 0 ca_debug
PUBLIC a4c8 0 ca_memdup
PUBLIC a550 0 ca_sprintf_malloc
PUBLIC a6d8 0 ca_detect_fork
PUBLIC ab40 0 ca_cache_lookup_sound
PUBLIC b2b8 0 ca_cache_store_sound
PUBLIC c360 0 driver_playing
STACK CFI INIT 2d90 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2dc0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2df8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e04 x19: .cfa -16 + ^
STACK CFI 2e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e48 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f38 144 .cfa: sp 0 + .ra: x30
STACK CFI 2f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3080 15c .cfa: sp 0 + .ra: x30
STACK CFI 3084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 308c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3098 x21: .cfa -16 + ^
STACK CFI 30ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 30f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31e0 130 .cfa: sp 0 + .ra: x30
STACK CFI 31e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31f8 x21: .cfa -16 + ^
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3254 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3310 17c .cfa: sp 0 + .ra: x30
STACK CFI 3314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3320 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 33c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 33c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 33e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 33e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3490 134 .cfa: sp 0 + .ra: x30
STACK CFI 3494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 349c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 35c8 22c .cfa: sp 0 + .ra: x30
STACK CFI 35cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 35d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 35e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3674 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 37f8 1bc .cfa: sp 0 + .ra: x30
STACK CFI 37fc .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 3804 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 380c x21: .cfa -288 + ^
STACK CFI 38e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38e4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 39b8 3cc .cfa: sp 0 + .ra: x30
STACK CFI 39bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 39c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 39d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 39d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3af4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3b98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3d88 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 3d8c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3d94 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3d9c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e80 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3f50 144 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4098 21c .cfa: sp 0 + .ra: x30
STACK CFI 409c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40ac x21: .cfa -16 + ^
STACK CFI 4120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4124 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 41c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 42b8 1bc .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 42c4 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 42cc x21: .cfa -288 + ^
STACK CFI 43a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43a4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 4478 110 .cfa: sp 0 + .ra: x30
STACK CFI 447c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 448c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 44e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44ec .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4588 13c .cfa: sp 0 + .ra: x30
STACK CFI 458c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4598 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45d8 x19: x19 x20: x20
STACK CFI 45dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45e8 x19: x19 x20: x20
STACK CFI 45ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4610 x19: x19 x20: x20
STACK CFI 4614 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4618 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4628 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 462c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 463c x19: x19 x20: x20
STACK CFI 4680 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46c0 x19: x19 x20: x20
STACK CFI INIT 46c8 1ac .cfa: sp 0 + .ra: x30
STACK CFI 46cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4734 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4798 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4878 48 .cfa: sp 0 + .ra: x30
STACK CFI 487c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4888 x19: .cfa -16 + ^
STACK CFI 48ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 48c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48cc x19: .cfa -16 + ^
STACK CFI 48e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4968 94 .cfa: sp 0 + .ra: x30
STACK CFI 496c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4984 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a00 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4a04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4aa8 94 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ac0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b40 1dc .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4c24 x21: x21 x22: x22
STACK CFI 4c28 x23: x23 x24: x24
STACK CFI 4c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c48 x21: x21 x22: x22
STACK CFI 4c4c x23: x23 x24: x24
STACK CFI 4c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c68 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4c94 x21: x21 x22: x22
STACK CFI 4cd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d18 x21: x21 x22: x22
STACK CFI INIT 4d20 bc .cfa: sp 0 + .ra: x30
STACK CFI 4d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d2c x21: .cfa -16 + ^
STACK CFI 4d38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d64 x19: x19 x20: x20
STACK CFI 4d70 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4dbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4dc4 x19: x19 x20: x20
STACK CFI 4dc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4dd8 x19: x19 x20: x20
STACK CFI INIT 4de0 324 .cfa: sp 0 + .ra: x30
STACK CFI 4de4 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 4dec x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 4df8 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 4e3c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 4e48 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 4e70 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 4f0c x21: x21 x22: x22
STACK CFI 4f10 x23: x23 x24: x24
STACK CFI 4f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4f3c .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI 4fe0 x21: x21 x22: x22
STACK CFI 4fe4 x23: x23 x24: x24
STACK CFI 5040 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 5050 x21: x21 x22: x22
STACK CFI 5094 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 50a8 x23: x23 x24: x24
STACK CFI 50e8 x21: x21 x22: x22
STACK CFI 50ec x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 50f4 x21: x21 x22: x22
STACK CFI 50fc x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 5100 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI INIT 5108 26c .cfa: sp 0 + .ra: x30
STACK CFI 510c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5114 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 511c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5128 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5210 x19: x19 x20: x20
STACK CFI 5214 x23: x23 x24: x24
STACK CFI 5220 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5224 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5238 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 5250 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5254 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5264 x19: x19 x20: x20
STACK CFI 5268 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5278 x19: x19 x20: x20
STACK CFI 527c x23: x23 x24: x24
STACK CFI 52c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5300 x19: x19 x20: x20
STACK CFI 5304 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5344 x19: x19 x20: x20
STACK CFI 5348 x23: x23 x24: x24
STACK CFI 534c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5354 x19: x19 x20: x20
STACK CFI 5358 x23: x23 x24: x24
STACK CFI 535c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 536c x19: x19 x20: x20
STACK CFI 5370 x23: x23 x24: x24
STACK CFI INIT 5378 150 .cfa: sp 0 + .ra: x30
STACK CFI 537c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5388 x21: .cfa -16 + ^
STACK CFI 5394 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53bc x19: x19 x20: x20
STACK CFI 53c0 x21: x21
STACK CFI 53c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 53c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 53dc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 53e8 x21: x21
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 53f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5400 x19: x19 x20: x20
STACK CFI 5404 x21: x21
STACK CFI 5408 x21: .cfa -16 + ^
STACK CFI 5444 x21: x21
STACK CFI 5484 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 54c0 x19: x19 x20: x20
STACK CFI 54c4 x21: x21
STACK CFI INIT 54c8 130 .cfa: sp 0 + .ra: x30
STACK CFI 54cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 552c x21: x21 x22: x22
STACK CFI 5538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 553c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5558 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5568 x21: x21 x22: x22
STACK CFI 55ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 55f4 x21: x21 x22: x22
STACK CFI INIT 55f8 140 .cfa: sp 0 + .ra: x30
STACK CFI 55fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5604 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5694 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5738 e8 .cfa: sp 0 + .ra: x30
STACK CFI 573c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5744 x19: .cfa -16 + ^
STACK CFI 5778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 577c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5798 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5820 ac .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 582c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5834 x21: .cfa -16 + ^
STACK CFI 5874 x21: x21
STACK CFI 5880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 58d0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 58d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 58dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 58e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5908 x23: .cfa -32 + ^
STACK CFI 5958 x23: x23
STACK CFI 597c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5980 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 5984 x23: x23
STACK CFI 5988 x23: .cfa -32 + ^
STACK CFI 5994 x23: x23
STACK CFI 59f0 x23: .cfa -32 + ^
STACK CFI 5a00 x23: x23
STACK CFI 5a44 x23: .cfa -32 + ^
STACK CFI 5a84 x23: x23
STACK CFI 5a8c x23: .cfa -32 + ^
STACK CFI INIT 5a90 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ae0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5afc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b88 124 .cfa: sp 0 + .ra: x30
STACK CFI 5b8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b98 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5be8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5cb0 128 .cfa: sp 0 + .ra: x30
STACK CFI 5cb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5cbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5cc4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5dd8 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 5ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5de4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5df8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e64 x21: x21 x22: x22
STACK CFI 5e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5ee4 x21: x21 x22: x22
STACK CFI 5f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5f4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f78 x21: x21 x22: x22
STACK CFI 5fbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5fcc x21: x21 x22: x22
STACK CFI INIT 5fd0 80 .cfa: sp 0 + .ra: x30
STACK CFI 5fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fdc x19: .cfa -16 + ^
STACK CFI 6010 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6014 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6050 50 .cfa: sp 0 + .ra: x30
STACK CFI 6060 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 60a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 60b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 60f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 6100 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6140 60 .cfa: sp 0 + .ra: x30
STACK CFI 6160 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 61a0 220 .cfa: sp 0 + .ra: x30
STACK CFI 61a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 61dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 61f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6204 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 62f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 62fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 63c0 208 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 63fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6414 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6418 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 646c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6470 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 65c8 210 .cfa: sp 0 + .ra: x30
STACK CFI 65cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6648 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 67d8 74 .cfa: sp 0 + .ra: x30
STACK CFI 67f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 680c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6810 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6850 78 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 685c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 688c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 68c8 64 .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68d4 x19: .cfa -16 + ^
STACK CFI 68ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6930 9c .cfa: sp 0 + .ra: x30
STACK CFI 6934 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 694c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6954 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 69d0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 69d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a60 x21: x21 x22: x22
STACK CFI 6a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6aa4 x21: x21 x22: x22
STACK CFI 6ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6ad4 x21: x21 x22: x22
STACK CFI 6ad8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6af0 x21: x21 x22: x22
STACK CFI 6af4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6b08 x21: x21 x22: x22
STACK CFI 6b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6b70 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6b78 x21: x21 x22: x22
STACK CFI 6bb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6bc8 x21: x21 x22: x22
STACK CFI INIT 6bd0 9c .cfa: sp 0 + .ra: x30
STACK CFI 6bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6bec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6c70 158 .cfa: sp 0 + .ra: x30
STACK CFI 6c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c7c x19: .cfa -16 + ^
STACK CFI 6cec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6cf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6d5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6d88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6dc8 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 6dcc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6dd4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6ddc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6de8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6e1c x25: .cfa -32 + ^
STACK CFI 6e8c x25: x25
STACK CFI 6e90 x25: .cfa -32 + ^
STACK CFI 6eb4 x25: x25
STACK CFI 6edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ee0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 6ef8 x25: x25
STACK CFI 6efc x25: .cfa -32 + ^
STACK CFI 6f10 x25: x25
STACK CFI 703c x25: .cfa -32 + ^
STACK CFI 7080 x25: x25
STACK CFI 7084 x25: .cfa -32 + ^
STACK CFI 708c x25: x25
STACK CFI 7094 x25: .cfa -32 + ^
STACK CFI INIT 7098 6c .cfa: sp 0 + .ra: x30
STACK CFI 709c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70a4 x19: .cfa -16 + ^
STACK CFI 70b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 70bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7108 17c .cfa: sp 0 + .ra: x30
STACK CFI 710c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7114 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7120 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7130 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 71dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 71e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7288 338 .cfa: sp 0 + .ra: x30
STACK CFI 728c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7294 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 72a0 x23: .cfa -96 + ^
STACK CFI 72ac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 734c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7350 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 75c0 68 .cfa: sp 0 + .ra: x30
STACK CFI 75c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75cc x19: .cfa -16 + ^
STACK CFI 75e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 75ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7628 50 .cfa: sp 0 + .ra: x30
STACK CFI 7638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7678 50 .cfa: sp 0 + .ra: x30
STACK CFI 7688 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 76c8 104 .cfa: sp 0 + .ra: x30
STACK CFI 76cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7740 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7744 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7750 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 77d0 5c .cfa: sp 0 + .ra: x30
STACK CFI 77ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7830 290 .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7840 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 788c x19: x19 x20: x20
STACK CFI 7890 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 78cc x19: x19 x20: x20
STACK CFI 78d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 78d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 78e8 x19: x19 x20: x20
STACK CFI 78f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 78fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 790c x19: x19 x20: x20
STACK CFI 7910 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7958 x19: x19 x20: x20
STACK CFI 795c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79a4 x19: x19 x20: x20
STACK CFI 79a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79f0 x19: x19 x20: x20
STACK CFI 7a34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a74 x19: x19 x20: x20
STACK CFI 7a78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7abc x19: x19 x20: x20
STACK CFI INIT 7ac0 284 .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ad0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b14 x19: x19 x20: x20
STACK CFI 7b18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7b50 x19: x19 x20: x20
STACK CFI 7b54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7b6c x19: x19 x20: x20
STACK CFI 7b7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7b90 x19: x19 x20: x20
STACK CFI 7b94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7bdc x19: x19 x20: x20
STACK CFI 7be0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c28 x19: x19 x20: x20
STACK CFI 7c2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c74 x19: x19 x20: x20
STACK CFI 7cb8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7cf8 x19: x19 x20: x20
STACK CFI 7cfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7d40 x19: x19 x20: x20
STACK CFI INIT 7d48 6c .cfa: sp 0 + .ra: x30
STACK CFI 7d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d54 x19: .cfa -16 + ^
STACK CFI 7d68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7db8 124 .cfa: sp 0 + .ra: x30
STACK CFI 7dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7dc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7dcc x21: .cfa -16 + ^
STACK CFI 7e18 x19: x19 x20: x20
STACK CFI 7e1c x21: x21
STACK CFI 7e20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7e2c x21: .cfa -16 + ^
STACK CFI INIT 7ee0 304 .cfa: sp 0 + .ra: x30
STACK CFI 7ee4 .cfa: sp 80 +
STACK CFI 7ee8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ef0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7f08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7f1c x23: .cfa -16 + ^
STACK CFI 7fac x21: x21 x22: x22
STACK CFI 7fb0 x23: x23
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7fc4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8038 x21: x21 x22: x22
STACK CFI 803c x23: x23
STACK CFI 8040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8044 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8064 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 813c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 814c x21: x21 x22: x22
STACK CFI 8190 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 81d0 x21: x21 x22: x22
STACK CFI 81d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 81dc x21: x21 x22: x22
STACK CFI 81e0 x23: x23
STACK CFI INIT 81e8 348 .cfa: sp 0 + .ra: x30
STACK CFI 81ec .cfa: sp 112 +
STACK CFI 81f0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 81f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8200 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8238 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8240 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 824c x27: .cfa -16 + ^
STACK CFI 82c0 x21: x21 x22: x22
STACK CFI 82c4 x23: x23 x24: x24
STACK CFI 82c8 x25: x25 x26: x26
STACK CFI 82cc x27: x27
STACK CFI 82dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82e0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8384 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8390 x21: x21 x22: x22
STACK CFI 83a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 83a8 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 83e8 x21: x21 x22: x22
STACK CFI 8434 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 847c x21: x21 x22: x22
STACK CFI 8480 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 84c8 x21: x21 x22: x22
STACK CFI 84cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8514 x21: x21 x22: x22
STACK CFI 8518 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8520 x21: x21 x22: x22
STACK CFI 8524 x23: x23 x24: x24
STACK CFI 8528 x25: x25 x26: x26
STACK CFI 852c x27: x27
STACK CFI INIT 8530 37c .cfa: sp 0 + .ra: x30
STACK CFI 8534 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 853c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8544 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8568 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8578 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8584 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 859c x21: x21 x22: x22
STACK CFI 85a0 x23: x23 x24: x24
STACK CFI 85a4 x25: x25 x26: x26
STACK CFI 85a8 x27: x27 x28: x28
STACK CFI 85b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 85b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 86e0 x21: x21 x22: x22
STACK CFI 86e4 x23: x23 x24: x24
STACK CFI 86e8 x25: x25 x26: x26
STACK CFI 86ec x27: x27 x28: x28
STACK CFI 86f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 86f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 8700 x23: x23 x24: x24
STACK CFI 8710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8714 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 8754 x23: x23 x24: x24
STACK CFI 87a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 87e8 x23: x23 x24: x24
STACK CFI 87ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8834 x23: x23 x24: x24
STACK CFI 8838 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8844 x21: x21 x22: x22
STACK CFI 8848 x23: x23 x24: x24
STACK CFI 884c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 888c x21: x21 x22: x22
STACK CFI 8890 x23: x23 x24: x24
STACK CFI 8894 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 889c x21: x21 x22: x22
STACK CFI 88a0 x23: x23 x24: x24
STACK CFI 88a4 x25: x25 x26: x26
STACK CFI 88a8 x27: x27 x28: x28
STACK CFI INIT 88b0 20c .cfa: sp 0 + .ra: x30
STACK CFI 88b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 88bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 88cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 88e0 v8: .cfa -16 + ^
STACK CFI 88e8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 88f0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 88fc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8994 x19: x19 x20: x20
STACK CFI 8998 x21: x21 x22: x22
STACK CFI 899c x25: x25 x26: x26
STACK CFI 89a0 x27: x27 x28: x28
STACK CFI 89a4 v8: v8
STACK CFI 89b0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 89b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 89c0 x19: x19 x20: x20
STACK CFI 89d0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 89d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 8a64 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8aa4 x19: x19 x20: x20
STACK CFI 8aa8 v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 8ac0 138 .cfa: sp 0 + .ra: x30
STACK CFI 8ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8acc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ae0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b70 x21: x21 x22: x22
STACK CFI 8b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b84 x21: x21 x22: x22
STACK CFI 8b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8ba4 x21: x21 x22: x22
STACK CFI 8bec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8bf4 x21: x21 x22: x22
STACK CFI INIT 8bf8 44 .cfa: sp 0 + .ra: x30
STACK CFI 8bfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8c40 250 .cfa: sp 0 + .ra: x30
STACK CFI 8c44 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8c4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8c60 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8cdc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8d54 x19: x19 x20: x20
STACK CFI 8d80 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8d84 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 8d90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8d98 x19: x19 x20: x20
STACK CFI 8e7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8e84 x19: x19 x20: x20
STACK CFI 8e8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT 8e90 668 .cfa: sp 0 + .ra: x30
STACK CFI 8e94 .cfa: sp 1200 +
STACK CFI 8e98 .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 8ea0 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 8ea8 x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 8eb4 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 8ed0 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 8f58 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 9054 x23: x23 x24: x24
STACK CFI 9058 x25: x25 x26: x26
STACK CFI 905c x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 90c0 x23: x23 x24: x24
STACK CFI 90cc x25: x25 x26: x26
STACK CFI 9108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 910c .cfa: sp 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI 91e8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9240 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 9250 x25: x25 x26: x26
STACK CFI 9254 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 92f4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9334 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 9424 x23: x23 x24: x24
STACK CFI 9464 x25: x25 x26: x26
STACK CFI 9468 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 9470 x25: x25 x26: x26
STACK CFI 9474 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 9490 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9494 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 9498 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI INIT 94f8 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 94fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9504 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 950c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 952c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9540 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 954c v10: .cfa -32 + ^
STACK CFI 9558 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 95b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 963c x19: x19 x20: x20
STACK CFI 9640 x27: x27 x28: x28
STACK CFI 9644 v8: v8 v9: v9
STACK CFI 9648 v10: v10
STACK CFI 964c v10: .cfa -32 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9650 x27: x27 x28: x28
STACK CFI 9654 v8: v8 v9: v9
STACK CFI 9658 v10: v10
STACK CFI 9680 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9684 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -32 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 968c v10: v10
STACK CFI 9690 x19: x19 x20: x20
STACK CFI 9694 x27: x27 x28: x28
STACK CFI 9698 v8: v8 v9: v9
STACK CFI 977c v10: .cfa -32 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9784 v10: v10
STACK CFI 9788 x19: x19 x20: x20
STACK CFI 978c x27: x27 x28: x28
STACK CFI 9790 v8: v8 v9: v9
STACK CFI 9798 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 979c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 97a0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 97a4 v10: .cfa -32 + ^
STACK CFI INIT 97a8 250 .cfa: sp 0 + .ra: x30
STACK CFI 97ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 97bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 97c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 97d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 97e0 x25: .cfa -16 + ^
STACK CFI 983c x19: x19 x20: x20
STACK CFI 9840 x21: x21 x22: x22
STACK CFI 9844 x23: x23 x24: x24
STACK CFI 9848 x25: x25
STACK CFI 984c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 988c x25: x25
STACK CFI 9894 x19: x19 x20: x20
STACK CFI 9898 x21: x21 x22: x22
STACK CFI 989c x23: x23 x24: x24
STACK CFI 98a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 98a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 98b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 98b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9900 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9910 x21: x21 x22: x22
STACK CFI 9914 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9924 x21: x21 x22: x22
STACK CFI 9928 x23: x23 x24: x24
STACK CFI 996c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 99ac x21: x21 x22: x22
STACK CFI 99b0 x23: x23 x24: x24
STACK CFI 99b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 99f4 x21: x21 x22: x22
STACK CFI INIT 99f8 244 .cfa: sp 0 + .ra: x30
STACK CFI 99fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9a1c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9a28 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9a34 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9a50 x19: x19 x20: x20
STACK CFI 9a54 x21: x21 x22: x22
STACK CFI 9a58 x23: x23 x24: x24
STACK CFI 9a5c x25: x25 x26: x26
STACK CFI 9a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9a64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9aa0 x19: x19 x20: x20
STACK CFI 9aa4 x21: x21 x22: x22
STACK CFI 9aa8 x23: x23 x24: x24
STACK CFI 9aac x25: x25 x26: x26
STACK CFI 9ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9ab4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9ae4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9af4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9af8 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9b08 x21: x21 x22: x22
STACK CFI 9b0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9b54 x21: x21 x22: x22
STACK CFI 9b58 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9b68 x21: x21 x22: x22
STACK CFI 9b6c x23: x23 x24: x24
STACK CFI 9bb0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9bf0 x21: x21 x22: x22
STACK CFI 9bf4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9c34 x21: x21 x22: x22
STACK CFI 9c38 x23: x23 x24: x24
STACK CFI INIT 9c40 10c .cfa: sp 0 + .ra: x30
STACK CFI 9c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9d50 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 9d54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9de8 x21: x21 x22: x22
STACK CFI 9dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9df0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9df4 x21: x21 x22: x22
STACK CFI 9e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9e1c x21: x21 x22: x22
STACK CFI 9e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9e38 x21: x21 x22: x22
STACK CFI 9e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9e64 x21: x21 x22: x22
STACK CFI 9ea8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9ee8 x21: x21 x22: x22
STACK CFI 9eec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9ef4 x21: x21 x22: x22
STACK CFI INIT 9ef8 574 .cfa: sp 0 + .ra: x30
STACK CFI 9efc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9f04 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9f0c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9f18 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9f2c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9f34 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a028 x25: x25 x26: x26
STACK CFI a054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI a058 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a180 x25: x25 x26: x26
STACK CFI a190 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a1a0 x25: x25 x26: x26
STACK CFI a1a4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a1ec x25: x25 x26: x26
STACK CFI a1f0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a238 x25: x25 x26: x26
STACK CFI a23c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a284 x25: x25 x26: x26
STACK CFI a288 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a33c x25: x25 x26: x26
STACK CFI a37c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a3bc x25: x25 x26: x26
STACK CFI a3c0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a464 x25: x25 x26: x26
STACK CFI a468 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT a470 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a490 34 .cfa: sp 0 + .ra: x30
STACK CFI a494 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a4c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a4c8 84 .cfa: sp 0 + .ra: x30
STACK CFI a4cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a50c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a510 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a550 184 .cfa: sp 0 + .ra: x30
STACK CFI a554 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI a55c x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI a564 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI a584 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI a588 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI a678 x19: x19 x20: x20
STACK CFI a680 x23: x23 x24: x24
STACK CFI a688 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI a68c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x29: .cfa -384 + ^
STACK CFI INIT a6d8 40 .cfa: sp 0 + .ra: x30
STACK CFI a6dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a6e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a718 d0 .cfa: sp 0 + .ra: x30
STACK CFI a71c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a728 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a730 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a73c x25: .cfa -32 + ^
STACK CFI a7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT a7e8 1c .cfa: sp 0 + .ra: x30
STACK CFI a7ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a7fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a808 338 .cfa: sp 0 + .ra: x30
STACK CFI a80c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a81c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a844 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a870 x23: x23 x24: x24
STACK CFI a890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a894 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI a8a0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a8e8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a920 x27: .cfa -64 + ^
STACK CFI a9b0 x21: x21 x22: x22
STACK CFI a9b4 x25: x25 x26: x26
STACK CFI a9b8 x27: x27
STACK CFI a9bc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a9cc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a9d0 x27: .cfa -64 + ^
STACK CFI aa80 x21: x21 x22: x22
STACK CFI aa84 x25: x25 x26: x26
STACK CFI aa88 x27: x27
STACK CFI aa8c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI aab0 x25: x25 x26: x26 x27: x27
STACK CFI aab8 x21: x21 x22: x22
STACK CFI aabc x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI aaf0 x27: x27
STACK CFI aaf8 x21: x21 x22: x22
STACK CFI aafc x25: x25 x26: x26
STACK CFI ab00 x23: x23 x24: x24
STACK CFI ab08 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI ab20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI ab28 x23: x23 x24: x24
STACK CFI ab30 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ab34 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ab38 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ab3c x27: .cfa -64 + ^
STACK CFI INIT 2d20 58 .cfa: sp 0 + .ra: x30
STACK CFI 2d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d3c x19: .cfa -16 + ^
STACK CFI 2d68 x19: x19
STACK CFI 2d6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2d74 x19: x19
STACK CFI INIT ab40 778 .cfa: sp 0 + .ra: x30
STACK CFI ab44 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI ab4c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI ab58 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI ab74 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI ab9c x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI abc4 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI ad30 x23: x23 x24: x24
STACK CFI ad34 x25: x25 x26: x26
STACK CFI ad5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI ad60 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI ad70 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI adbc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI aedc x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI b01c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b0a4 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI b284 x25: x25 x26: x26
STACK CFI b28c x23: x23 x24: x24
STACK CFI b290 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI b2ac x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b2b0 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI b2b4 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI INIT b2b8 34c .cfa: sp 0 + .ra: x30
STACK CFI b2bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b2c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b2fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b30c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b3c4 x21: x21 x22: x22
STACK CFI b3c8 x23: x23 x24: x24
STACK CFI b3e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI b514 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b5d4 x23: x23 x24: x24
STACK CFI b5dc x21: x21 x22: x22
STACK CFI b5e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b5f0 x21: x21 x22: x22
STACK CFI b5f4 x23: x23 x24: x24
STACK CFI b5fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b600 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT b608 5c .cfa: sp 0 + .ra: x30
STACK CFI b60c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b61c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b654 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b668 180 .cfa: sp 0 + .ra: x30
STACK CFI b66c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI b674 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI b684 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI b738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b73c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT b7e8 bc .cfa: sp 0 + .ra: x30
STACK CFI b7ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b7f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b810 x21: .cfa -16 + ^
STACK CFI b840 x21: x21
STACK CFI b848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b84c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b888 x21: x21
STACK CFI b88c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b898 x21: .cfa -16 + ^
STACK CFI b8a0 x21: x21
STACK CFI INIT b8a8 10c .cfa: sp 0 + .ra: x30
STACK CFI b8ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b8b4 x21: .cfa -16 + ^
STACK CFI b8bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b900 x19: x19 x20: x20
STACK CFI b90c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI b910 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b918 x19: x19 x20: x20
STACK CFI b960 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b970 x19: x19 x20: x20
STACK CFI b974 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b9b0 x19: x19 x20: x20
STACK CFI INIT b9b8 3b4 .cfa: sp 0 + .ra: x30
STACK CFI b9bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b9c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b9cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba10 x23: .cfa -16 + ^
STACK CFI ba4c x23: x23
STACK CFI bb50 x19: x19 x20: x20
STACK CFI bb5c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI bb60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI bbc4 x19: x19 x20: x20
STACK CFI bbc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bbdc x19: x19 x20: x20
STACK CFI bbe0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bc0c x19: x19 x20: x20
STACK CFI bc14 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI bc18 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI bc60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bca8 x19: x19 x20: x20
STACK CFI bcac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bcbc x19: x19 x20: x20
STACK CFI bcc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bcc8 x23: .cfa -16 + ^
STACK CFI bd18 x19: x19 x20: x20
STACK CFI bd1c x23: x23
STACK CFI bd20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bd38 x19: x19 x20: x20
STACK CFI bd3c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI bd48 x23: x23
STACK CFI bd60 x19: x19 x20: x20
STACK CFI bd64 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI bd68 x23: x23
STACK CFI INIT bd70 12c .cfa: sp 0 + .ra: x30
STACK CFI bd74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bd94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd98 x19: .cfa -16 + ^
STACK CFI bdac x19: x19
STACK CFI bdb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bdb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bdb8 x19: .cfa -16 + ^
STACK CFI bdcc x19: x19
STACK CFI bdd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bdd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bdd8 x19: .cfa -16 + ^
STACK CFI INIT bea0 12c .cfa: sp 0 + .ra: x30
STACK CFI bea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bec0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bec8 x19: .cfa -16 + ^
STACK CFI bedc x19: x19
STACK CFI bee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bee8 x19: .cfa -16 + ^
STACK CFI befc x19: x19
STACK CFI bf00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bf04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf08 x19: .cfa -16 + ^
STACK CFI INIT bfd0 12c .cfa: sp 0 + .ra: x30
STACK CFI bfd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bff8 x19: .cfa -16 + ^
STACK CFI c00c x19: x19
STACK CFI c010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c014 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c018 x19: .cfa -16 + ^
STACK CFI c02c x19: x19
STACK CFI c030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c038 x19: .cfa -16 + ^
STACK CFI INIT c100 12c .cfa: sp 0 + .ra: x30
STACK CFI c104 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c120 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c128 x19: .cfa -16 + ^
STACK CFI c13c x19: x19
STACK CFI c140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c148 x19: .cfa -16 + ^
STACK CFI c15c x19: x19
STACK CFI c160 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c168 x19: .cfa -16 + ^
STACK CFI INIT c230 12c .cfa: sp 0 + .ra: x30
STACK CFI c234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c250 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c258 x19: .cfa -16 + ^
STACK CFI c26c x19: x19
STACK CFI c270 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c278 x19: .cfa -16 + ^
STACK CFI c28c x19: x19
STACK CFI c290 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c298 x19: .cfa -16 + ^
STACK CFI INIT c360 15c .cfa: sp 0 + .ra: x30
STACK CFI c364 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c384 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c388 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c398 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c39c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c3b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
