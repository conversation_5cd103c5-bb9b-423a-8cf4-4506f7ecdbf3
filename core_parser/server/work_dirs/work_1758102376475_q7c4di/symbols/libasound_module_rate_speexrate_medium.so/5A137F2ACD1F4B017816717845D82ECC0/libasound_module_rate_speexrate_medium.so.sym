MODULE Linux arm64 5A137F2ACD1F4B017816717845D82ECC0 libasound_module_rate_speexrate.so
INFO CODE_ID 2A7F135A1FCD014B7816717845D82ECC17DDA6A6
PUBLIC 1368 0 _snd_pcm_rate_speexrate_open
PUBLIC 1370 0 _snd_pcm_rate_speexrate_best_open
PUBLIC 1378 0 _snd_pcm_rate_speexrate_medium_open
PUBLIC 25a8 0 alsa_lib_resampler_destroy
PUBLIC 25f0 0 alsa_lib_resampler_process_float
PUBLIC 25f8 0 alsa_lib_resampler_process_int
PUBLIC 27c8 0 alsa_lib_resampler_process_interleaved_float
PUBLIC 2868 0 alsa_lib_resampler_process_interleaved_int
PUBLIC 2908 0 alsa_lib_resampler_get_rate
PUBLIC 2920 0 alsa_lib_resampler_set_rate_frac
PUBLIC 2a38 0 alsa_lib_resampler_set_rate
PUBLIC 2a48 0 alsa_lib_resampler_get_ratio
PUBLIC 2a60 0 alsa_lib_resampler_set_quality
PUBLIC 2aa8 0 alsa_lib_resampler_init_frac
PUBLIC 2c28 0 alsa_lib_resampler_init
PUBLIC 2c40 0 alsa_lib_resampler_get_quality
PUBLIC 2c50 0 alsa_lib_resampler_set_input_stride
PUBLIC 2c58 0 alsa_lib_resampler_get_input_stride
PUBLIC 2c68 0 alsa_lib_resampler_set_output_stride
PUBLIC 2c70 0 alsa_lib_resampler_get_output_stride
PUBLIC 2c80 0 alsa_lib_resampler_skip_zeros
PUBLIC 2cb8 0 alsa_lib_resampler_reset_mem
PUBLIC 2ce8 0 alsa_lib_resampler_strerror
STACK CFI INIT f98 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1008 48 .cfa: sp 0 + .ra: x30
STACK CFI 100c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1014 x19: .cfa -16 + ^
STACK CFI 104c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1058 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1068 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1078 30 .cfa: sp 0 + .ra: x30
STACK CFI 107c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10a8 30 .cfa: sp 0 + .ra: x30
STACK CFI 10b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e0 2c .cfa: sp 0 + .ra: x30
STACK CFI 10e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10ec x19: .cfa -16 + ^
STACK CFI 1108 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1110 ac .cfa: sp 0 + .ra: x30
STACK CFI 1114 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1120 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1130 x21: .cfa -32 + ^
STACK CFI 11ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11c8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 11cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1258 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 126c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1270 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1278 74 .cfa: sp 0 + .ra: x30
STACK CFI 127c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1284 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12f0 74 .cfa: sp 0 + .ra: x30
STACK CFI 12f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 135c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1360 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1378 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1380 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1400 11c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1520 130 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1650 254 .cfa: sp 0 + .ra: x30
STACK CFI 1654 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1688 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 16b8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 16c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 16c8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1844 x19: x19 x20: x20
STACK CFI 1848 x23: x23 x24: x24
STACK CFI 184c x25: x25 x26: x26
STACK CFI 187c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1880 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 1894 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1898 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 189c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 18a0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 18a8 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 18ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 18e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1910 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 191c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1920 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1af8 x19: x19 x20: x20
STACK CFI 1afc x23: x23 x24: x24
STACK CFI 1b00 x25: x25 x26: x26
STACK CFI 1b30 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1b34 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 1b48 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1b4c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1b50 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1b54 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 1b58 21c .cfa: sp 0 + .ra: x30
STACK CFI 1b5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1b6c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1b78 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1b80 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1b94 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1cc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1d78 158 .cfa: sp 0 + .ra: x30
STACK CFI 1d80 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d94 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 1da4 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI 1db0 v12: .cfa -40 + ^
STACK CFI 1dd0 x19: .cfa -48 + ^
STACK CFI 1e18 x19: x19
STACK CFI 1e9c v10: v10 v11: v11
STACK CFI 1ea4 v12: v12
STACK CFI 1eb8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 1ebc .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1ec0 v12: v12
STACK CFI 1ec8 v10: v10 v11: v11
STACK CFI 1ecc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI INIT 1ed0 6d4 .cfa: sp 0 + .ra: x30
STACK CFI 1ed4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1ee0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1ee8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1ef8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1f30 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1ff0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2018 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 208c x25: x25 x26: x26
STACK CFI 2090 v8: v8 v9: v9
STACK CFI 2250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2254 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 22e0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2338 v8: v8 v9: v9
STACK CFI 23c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 23c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 24b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 24b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 25a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 25ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25b4 x19: .cfa -16 + ^
STACK CFI 25ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f8 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 25fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2604 .cfa: x29 96 +
STACK CFI 2608 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2620 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2648 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^
STACK CFI 27b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 27b4 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 27c8 9c .cfa: sp 0 + .ra: x30
STACK CFI 27cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 27e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 27f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2800 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2848 x21: x21 x22: x22
STACK CFI 284c x23: x23 x24: x24
STACK CFI 2860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2868 9c .cfa: sp 0 + .ra: x30
STACK CFI 286c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2874 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2880 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2894 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 28a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 28e8 x21: x21 x22: x22
STACK CFI 28ec x23: x23 x24: x24
STACK CFI 2900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2908 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2920 118 .cfa: sp 0 + .ra: x30
STACK CFI 2a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2a38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a48 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a60 48 .cfa: sp 0 + .ra: x30
STACK CFI 2a8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2aa8 180 .cfa: sp 0 + .ra: x30
STACK CFI 2aac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2abc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2ad8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2ae4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2aec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2bc4 x23: x23 x24: x24
STACK CFI 2bc8 x25: x25 x26: x26
STACK CFI 2bcc x27: x27 x28: x28
STACK CFI 2be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2be4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2bf4 x23: x23 x24: x24
STACK CFI 2bf8 x25: x25 x26: x26
STACK CFI 2bfc x27: x27 x28: x28
STACK CFI 2c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 2c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2c28 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c58 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c80 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ce8 70 .cfa: sp 0 + .ra: x30
