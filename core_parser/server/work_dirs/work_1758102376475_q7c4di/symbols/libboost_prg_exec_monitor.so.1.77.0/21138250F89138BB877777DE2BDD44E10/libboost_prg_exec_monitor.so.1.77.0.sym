MODULE Linux arm64 21138250F89138BB877777DE2BDD44E10 libboost_prg_exec_monitor.so.1.77.0
INFO CODE_ID 5082132191F8BB38877777DE2BDD44E1
PUBLIC 41f0 0 _init
PUBLIC 4850 0 boost::wrapexcept<boost::bad_function_call>::rethrow() const
PUBLIC 4938 0 boost::detail::signal_action::~signal_action() [clone .part.0]
PUBLIC 4948 0 void boost::unit_test::ut_detail::throw_exception<boost::system_error>(boost::system_error const&) [clone .isra.0] [clone .constprop.0]
PUBLIC 4978 0 boost::detail::report_error(boost::execution_exception::error_code, boost::exception const*, char const*, ...) [clone .constprop.0]
PUBLIC 49d4 0 boost::exception const* boost::current_exception_cast<boost::exception const>()
PUBLIC 4a18 0 void boost::throw_exception<boost::bad_function_call>(boost::bad_function_call const&)
PUBLIC 4a90 0 _GLOBAL__sub_I_execution_monitor.cpp
PUBLIC 4ad0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.0]
PUBLIC 4b80 0 boost::debug::(anonymous namespace)::info_t::info_t() [clone .constprop.0]
PUBLIC 4f00 0 _GLOBAL__sub_I_debug.cpp
PUBLIC 4f30 0 _GLOBAL__sub_I_cpp_main.cpp
PUBLIC 4f6c 0 call_weak_fn
PUBLIC 4f80 0 deregister_tm_clones
PUBLIC 4fb0 0 register_tm_clones
PUBLIC 4fec 0 __do_global_dtors_aux
PUBLIC 503c 0 frame_dummy
PUBLIC 5040 0 boost_execution_monitor_jumping_signal_handler
PUBLIC 5070 0 boost_execution_monitor_attaching_signal_handler
PUBLIC 5100 0 boost::detail::signal_action::signal_action(int, bool, bool, char*) [clone .part.0]
PUBLIC 5200 0 boost::detail::signal_action::signal_action()
PUBLIC 5210 0 boost::detail::signal_action::signal_action(int, bool, bool, char*)
PUBLIC 5370 0 boost::detail::signal_action::~signal_action()
PUBLIC 5390 0 boost::detail::signal_handler::signal_handler(bool, bool, unsigned long, bool, char*)
PUBLIC 5740 0 boost::detail::signal_handler::~signal_handler()
PUBLIC 5a60 0 boost::execution_monitor::execution_monitor()
PUBLIC 5a80 0 boost::system_error::system_error(char const*)
PUBLIC 5ab0 0 boost::execution_exception::execution_exception(boost::execution_exception::error_code, boost::unit_test::basic_cstring<char const>, boost::execution_exception::location const&)
PUBLIC 5b20 0 boost::execution_exception::location::location(char const*, unsigned long, char const*)
PUBLIC 5b90 0 boost::detail::report_error(boost::execution_exception::error_code, boost::exception const*, char const*, std::__va_list*)
PUBLIC 5c90 0 boost::detail::report_error(boost::execution_exception::error_code, char const*, ...)
PUBLIC 5d00 0 boost::detail::system_signal_exception::report() const
PUBLIC 6170 0 boost::execution_exception::location::location(boost::unit_test::basic_cstring<char const>, unsigned long, char const*)
PUBLIC 61c0 0 boost::fpe::enable(unsigned int)
PUBLIC 6200 0 boost::fpe::disable(unsigned int)
PUBLIC 6240 0 boost::execution_monitor::catch_signals(boost::function<int ()> const&)
PUBLIC 6440 0 boost::execution_monitor::execute(boost::function<int ()> const&)
PUBLIC 6d30 0 boost::execution_monitor::vexecute(boost::function<void ()> const&)
PUBLIC 6de0 0 boost::detail::sp_counted_base::destroy()
PUBLIC 6df0 0 std::ctype<char>::do_widen(char) const
PUBLIC 6e00 0 boost::exception_detail::error_info_container_impl::add_ref() const
PUBLIC 6e10 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 6e20 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::dispose()
PUBLIC 6e40 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_deleter(std::type_info const&)
PUBLIC 6e50 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_local_deleter(std::type_info const&)
PUBLIC 6e60 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_untyped_deleter()
PUBLIC 6e70 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 6e80 0 boost::bad_function_call::~bad_function_call()
PUBLIC 6ea0 0 boost::bad_function_call::~bad_function_call()
PUBLIC 6ee0 0 boost::detail::function::functor_manager<boost::detail::forward>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC 6f80 0 boost::exception_detail::error_info_container_impl::get(boost::exception_detail::type_info_ const&) const
PUBLIC 70b0 0 boost::detail::sp_counted_base::release()
PUBLIC 7180 0 boost::core::demangle[abi:cxx11](char const*)
PUBLIC 72a0 0 boost::detail::function::has_empty_target(...)
PUBLIC 72b0 0 boost::exception_detail::get_std_exception(...)
PUBLIC 72c0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 7320 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 7380 0 boost::exception_detail::error_info_container_impl::diagnostic_information(char const*) const
PUBLIC 7670 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_erase(std::_Rb_tree_node<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >*)
PUBLIC 7790 0 boost::exception_detail::error_info_container_impl::release() const
PUBLIC 7810 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 78f0 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 79e0 0 boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7ac0 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7b90 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7c60 0 boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7d70 0 boost::wrapexcept<boost::bad_function_call>::clone() const
PUBLIC 8150 0 boost::exception_detail::diagnostic_information_impl[abi:cxx11](boost::exception const*, std::exception const*, bool, bool)
PUBLIC 8f60 0 boost::detail::function::function_obj_invoker0<boost::detail::forward, int>::invoke(boost::detail::function::function_buffer&)
PUBLIC 8fe0 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_unique_pos(boost::exception_detail::type_info_ const&)
PUBLIC 9120 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, boost::exception_detail::type_info_ const&)
PUBLIC 9350 0 boost::exception_detail::error_info_container_impl::set(boost::shared_ptr<boost::exception_detail::error_info_base> const&, boost::exception_detail::type_info_ const&)
PUBLIC 9670 0 boost::exception_detail::error_info_container_impl::clone() const
PUBLIC 9ab0 0 boost::debug::(anonymous namespace)::start_gdb_in_xemacs(boost::debug::dbg_startup_info const&)
PUBLIC 9ac0 0 boost::debug::(anonymous namespace)::process_info::process_info(int)
PUBLIC 9c60 0 boost::debug::(anonymous namespace)::prepare_gdb_cmnd_file(boost::debug::dbg_startup_info const&)
PUBLIC 9ea0 0 boost::debug::(anonymous namespace)::start_dbx_in_xemacs(boost::debug::dbg_startup_info const&)
PUBLIC 9eb0 0 boost::debug::(anonymous namespace)::start_dbx_in_emacs(boost::debug::dbg_startup_info const&)
PUBLIC 9ec0 0 boost::debug::(anonymous namespace)::safe_execlp(char const*, ...)
PUBLIC a120 0 boost::debug::(anonymous namespace)::start_dbx_in_console(boost::debug::dbg_startup_info const&)
PUBLIC a1e0 0 boost::debug::(anonymous namespace)::start_gdb_in_console(boost::debug::dbg_startup_info const&)
PUBLIC a220 0 boost::debug::under_debugger()
PUBLIC a350 0 boost::debug::debugger_break()
PUBLIC a370 0 boost::debug::detect_memory_leaks(bool, boost::unit_test::basic_cstring<char const>)
PUBLIC a380 0 boost::debug::break_memory_alloc(long)
PUBLIC a390 0 boost::debug::(anonymous namespace)::info_t::~info_t()
PUBLIC a450 0 boost::debug::(anonymous namespace)::prepare_window_title(boost::debug::dbg_startup_info const&)
PUBLIC a540 0 boost::debug::(anonymous namespace)::start_dbx_in_ddd(boost::debug::dbg_startup_info const&)
PUBLIC a620 0 boost::debug::(anonymous namespace)::start_dbx_in_xterm(boost::debug::dbg_startup_info const&)
PUBLIC a770 0 boost::debug::(anonymous namespace)::start_gdb_in_xterm(boost::debug::dbg_startup_info const&)
PUBLIC a850 0 boost::debug::(anonymous namespace)::start_gdb_in_emacs(boost::debug::dbg_startup_info const&)
PUBLIC a920 0 boost::debug::attach_debugger(bool)
PUBLIC ac90 0 boost::debug::set_debugger[abi:cxx11](boost::unit_test::basic_cstring<char const>, boost::function<void (boost::debug::dbg_startup_info const&)>)
PUBLIC b100 0 boost::detail::function::void_function_invoker1<void (*)(boost::debug::dbg_startup_info const&), void, boost::debug::dbg_startup_info const&>::invoke(boost::detail::function::function_buffer&, boost::debug::dbg_startup_info const&)
PUBLIC b120 0 boost::detail::function::functor_manager<void (*)(boost::debug::dbg_startup_info const&)>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC b1e0 0 boost::enable_if_<!boost::is_integral<void (*)(boost::debug::dbg_startup_info const&)>::value, boost::function<void (boost::debug::dbg_startup_info const&)>&>::type boost::function<void (boost::debug::dbg_startup_info const&)>::operator=<void (*)(boost::debug::dbg_startup_info const&)>(void (*)(boost::debug::dbg_startup_info const&))
PUBLIC b3a0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >*)
PUBLIC b440 0 char const* std::__find_if<char const*, __gnu_cxx::__ops::_Iter_equals_val<char const> >(char const*, char const*, __gnu_cxx::__ops::_Iter_equals_val<char const>, std::random_access_iterator_tag)
PUBLIC b530 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC b6b0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC b950 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>&&, std::tuple<>&&)
PUBLIC bae0 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, boost::function<void (boost::debug::dbg_startup_info const&)>, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::operator[](std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC bc20 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>&&, std::tuple<>&&)
PUBLIC be30 0 boost::detail::function::function_obj_invoker0<(anonymous namespace)::cpp_main_caller, int>::invoke(boost::detail::function::function_buffer&)
PUBLIC be50 0 boost::detail::function::functor_manager<(anonymous namespace)::cpp_main_caller>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC bf00 0 boost::prg_exec_monitor_main(int (*)(int, char**), int, char**)
PUBLIC c4e0 0 boost::execution_monitor::~execution_monitor()
PUBLIC c5a0 0 std::basic_ostream<char, std::char_traits<char> >& boost::unit_test::operator<< <char, std::char_traits<char>, char const>(std::basic_ostream<char, std::char_traits<char> >&, boost::unit_test::basic_cstring<char const> const&)
PUBLIC c69c 0 _fini
STACK CFI INIT 4f80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fb0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fec 50 .cfa: sp 0 + .ra: x30
STACK CFI 4ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5004 x19: .cfa -16 + ^
STACK CFI 5034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 503c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6de0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6df0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e20 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5040 28 .cfa: sp 0 + .ra: x30
STACK CFI 5044 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6e70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ea0 38 .cfa: sp 0 + .ra: x30
STACK CFI 6ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6eb4 x19: .cfa -16 + ^
STACK CFI 6ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4850 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 485c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4868 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 4938 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ee0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ef0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4948 30 .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4954 x19: .cfa -16 + ^
STACK CFI INIT 5070 84 .cfa: sp 0 + .ra: x30
STACK CFI 5074 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 507c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 508c x21: .cfa -32 + ^
STACK CFI 50bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5100 100 .cfa: sp 0 + .ra: x30
STACK CFI 5104 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 510c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5118 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 517c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6f80 128 .cfa: sp 0 + .ra: x30
STACK CFI 6f84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fa4 x23: .cfa -16 + ^
STACK CFI 7030 x21: x21 x22: x22
STACK CFI 7034 x23: x23
STACK CFI 7044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7048 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7078 x21: x21 x22: x22
STACK CFI 707c x23: x23
STACK CFI 7080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7084 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 709c x21: x21 x22: x22
STACK CFI 70a0 x23: x23
STACK CFI 70a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70b0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 70d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70e8 x19: .cfa -16 + ^
STACK CFI 7130 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7134 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7158 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7164 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 717c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7180 11c .cfa: sp 0 + .ra: x30
STACK CFI 7184 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7190 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 71a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 71ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 721c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7220 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 72a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 72b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5200 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5210 15c .cfa: sp 0 + .ra: x30
STACK CFI 5228 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5230 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 523c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 529c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5370 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 539c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 53a8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 53c0 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 53cc x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 54dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 54e0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5740 318 .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 574c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 57d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5804 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5890 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5998 x21: x21 x22: x22
STACK CFI 599c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5a60 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a80 2c .cfa: sp 0 + .ra: x30
STACK CFI 5a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5ab0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b20 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b90 fc .cfa: sp 0 + .ra: x30
STACK CFI 5b94 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 5ba0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 5bac x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI INIT 5c90 64 .cfa: sp 0 + .ra: x30
STACK CFI 5c94 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI INIT 5d00 464 .cfa: sp 0 + .ra: x30
STACK CFI 5d0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4978 5c .cfa: sp 0 + .ra: x30
STACK CFI 497c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI INIT 6170 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 61c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61cc x19: .cfa -16 + ^
STACK CFI 61f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6200 38 .cfa: sp 0 + .ra: x30
STACK CFI 6204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 620c x19: .cfa -16 + ^
STACK CFI 6234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 72c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72d8 x19: .cfa -16 + ^
STACK CFI 7310 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7320 60 .cfa: sp 0 + .ra: x30
STACK CFI 7324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7338 x19: .cfa -16 + ^
STACK CFI 737c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7380 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 512 +
STACK CFI 7388 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 7390 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 739c x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 73ac x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 73b0 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 73b8 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 75a8 x19: x19 x20: x20
STACK CFI 75ac x23: x23 x24: x24
STACK CFI 75b0 x25: x25 x26: x26
STACK CFI 75b4 x27: x27 x28: x28
STACK CFI 75c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 75c8 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI INIT 49d4 44 .cfa: sp 0 + .ra: x30
STACK CFI 49d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49e0 x19: .cfa -16 + ^
STACK CFI 4a14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7670 120 .cfa: sp 0 + .ra: x30
STACK CFI 7678 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7680 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7688 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7690 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 76f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 76f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 775c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7790 78 .cfa: sp 0 + .ra: x30
STACK CFI 7794 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 779c x19: .cfa -16 + ^
STACK CFI 77bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7810 e0 .cfa: sp 0 + .ra: x30
STACK CFI 7814 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7834 x21: .cfa -16 + ^
STACK CFI 78a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 78a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 78f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7904 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7914 x21: .cfa -16 + ^
STACK CFI 7984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7988 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ac0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ad4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b90 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ba4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 79e0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 79e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c60 104 .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7d70 3d4 .cfa: sp 0 + .ra: x30
STACK CFI 7d74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7d80 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7f48 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7fc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8084 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8150 e0c .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 992 +
STACK CFI 8158 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI 8160 x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI 8168 x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI 8178 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI 8180 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI 8188 x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI 88a0 x21: x21 x22: x22
STACK CFI 88a4 x23: x23 x24: x24
STACK CFI 88ac x27: x27 x28: x28
STACK CFI 88b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 88b4 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI 891c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 8920 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^ x29: .cfa -992 + ^
STACK CFI 8cbc x21: x21 x22: x22
STACK CFI 8cc0 x23: x23 x24: x24
STACK CFI 8cc4 x27: x27 x28: x28
STACK CFI 8cc8 x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI INIT 4a18 78 .cfa: sp 0 + .ra: x30
STACK CFI 4a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 6240 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 6244 .cfa: sp 2944 +
STACK CFI 6248 .ra: .cfa -2936 + ^ x29: .cfa -2944 + ^
STACK CFI 6260 x19: .cfa -2928 + ^ x20: .cfa -2920 + ^ x21: .cfa -2912 + ^
STACK CFI 62f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 62f8 .cfa: sp 2944 + .ra: .cfa -2936 + ^ x19: .cfa -2928 + ^ x20: .cfa -2920 + ^ x21: .cfa -2912 + ^ x29: .cfa -2944 + ^
STACK CFI INIT 6440 8ec .cfa: sp 0 + .ra: x30
STACK CFI 6444 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 644c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6454 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 64b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 64bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6d30 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6d34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6d3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8f60 7c .cfa: sp 0 + .ra: x30
STACK CFI 8f64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8f90 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8fa4 x21: .cfa -32 + ^
STACK CFI INIT 8fe0 134 .cfa: sp 0 + .ra: x30
STACK CFI 8fe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8fec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8ff4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8ffc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 90ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 90b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 90f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 90f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9120 224 .cfa: sp 0 + .ra: x30
STACK CFI 9124 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9134 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 914c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 91e4 x19: x19 x20: x20
STACK CFI 91e8 x21: x21 x22: x22
STACK CFI 91ec .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 91f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9218 x21: x21 x22: x22
STACK CFI 9224 x19: x19 x20: x20
STACK CFI 922c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9230 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9274 x19: x19 x20: x20
STACK CFI 9278 x21: x21 x22: x22
STACK CFI 9288 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 928c .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9298 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 92e0 x19: x19 x20: x20
STACK CFI 92e8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 92ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 92f8 x19: x19 x20: x20
STACK CFI 9300 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9304 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9318 x21: x21 x22: x22
STACK CFI 931c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9324 x19: x19 x20: x20
STACK CFI 9328 x21: x21 x22: x22
STACK CFI 9330 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9334 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9340 x21: x21 x22: x22
STACK CFI INIT 9350 31c .cfa: sp 0 + .ra: x30
STACK CFI 9354 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 935c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9364 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9370 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9378 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9474 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9670 43c .cfa: sp 0 + .ra: x30
STACK CFI 9674 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 967c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9690 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 98f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 98f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4a90 40 .cfa: sp 0 + .ra: x30
STACK CFI 4a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a9c x19: .cfa -16 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b100 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ac0 198 .cfa: sp 0 + .ra: x30
STACK CFI 9ac4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9ad4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9adc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9ae4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9af0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9c24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 9c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9c44 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9c60 23c .cfa: sp 0 + .ra: x30
STACK CFI 9c64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9c74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9c80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9c90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9e64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9e80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT b120 b4 .cfa: sp 0 + .ra: x30
STACK CFI b124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b130 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b154 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b16c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9ea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ad0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4adc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ae4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9ec0 254 .cfa: sp 0 + .ra: x30
STACK CFI 9ec4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 9ee0 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 9ef0 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a0bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a0c0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI a0dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a0e0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI a110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT a120 b4 .cfa: sp 0 + .ra: x30
STACK CFI a124 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a138 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a140 x21: .cfa -32 + ^
STACK CFI a1d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a1e0 40 .cfa: sp 0 + .ra: x30
STACK CFI a1e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a1f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a21c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a220 12c .cfa: sp 0 + .ra: x30
STACK CFI a224 .cfa: sp 1120 +
STACK CFI a228 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI a238 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI a244 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI a250 x23: .cfa -1072 + ^
STACK CFI a304 x19: x19 x20: x20
STACK CFI a308 x21: x21 x22: x22
STACK CFI a30c x23: x23
STACK CFI a310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a314 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x29: .cfa -1120 + ^
STACK CFI a318 x19: x19 x20: x20
STACK CFI a31c x21: x21 x22: x22
STACK CFI a320 x23: x23
STACK CFI a32c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a330 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x29: .cfa -1120 + ^
STACK CFI a33c x19: x19 x20: x20
STACK CFI a340 x21: x21 x22: x22
STACK CFI a344 x23: x23
STACK CFI a348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a350 18 .cfa: sp 0 + .ra: x30
STACK CFI a354 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b1e0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI b1e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b1ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b2b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b2b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI INIT b3a0 9c .cfa: sp 0 + .ra: x30
STACK CFI b3a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b3b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3b8 x21: .cfa -16 + ^
STACK CFI b434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a390 b4 .cfa: sp 0 + .ra: x30
STACK CFI a394 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a39c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a3ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a420 x21: x21 x22: x22
STACK CFI a434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a438 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b440 ec .cfa: sp 0 + .ra: x30
STACK CFI INIT a450 ec .cfa: sp 0 + .ra: x30
STACK CFI a454 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a460 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a468 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a474 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a52c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a540 dc .cfa: sp 0 + .ra: x30
STACK CFI a544 .cfa: sp 80 +
STACK CFI a548 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a550 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a564 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a5fc x21: x21 x22: x22
STACK CFI a608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a60c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a620 14c .cfa: sp 0 + .ra: x30
STACK CFI a624 .cfa: sp 192 +
STACK CFI a628 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a630 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a644 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a660 x23: .cfa -32 + ^
STACK CFI a758 x21: x21 x22: x22
STACK CFI a75c x23: x23
STACK CFI a768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a770 dc .cfa: sp 0 + .ra: x30
STACK CFI a774 .cfa: sp 128 +
STACK CFI a778 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a780 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a850 c4 .cfa: sp 0 + .ra: x30
STACK CFI a854 .cfa: sp 544 +
STACK CFI a858 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI a860 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI a8dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a8e0 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x29: .cfa -544 + ^
STACK CFI a910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b530 178 .cfa: sp 0 + .ra: x30
STACK CFI b534 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b53c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b548 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b550 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b558 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b62c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b684 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b6a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT b6b0 29c .cfa: sp 0 + .ra: x30
STACK CFI b6b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b6c4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b6d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b6dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b6e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b76c x25: x25 x26: x26
STACK CFI b778 x19: x19 x20: x20
STACK CFI b77c x21: x21 x22: x22
STACK CFI b784 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b788 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b810 x19: x19 x20: x20
STACK CFI b814 x21: x21 x22: x22
STACK CFI b818 x25: x25 x26: x26
STACK CFI b81c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b820 .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI b82c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b834 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b888 x19: x19 x20: x20
STACK CFI b88c x21: x21 x22: x22
STACK CFI b89c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b8a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b900 x25: x25 x26: x26
STACK CFI b910 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b91c x19: x19 x20: x20
STACK CFI b920 x21: x21 x22: x22
STACK CFI b928 x25: x25 x26: x26
STACK CFI b92c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b930 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b938 x25: x25 x26: x26
STACK CFI b93c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b948 x25: x25 x26: x26
STACK CFI INIT b950 18c .cfa: sp 0 + .ra: x30
STACK CFI b954 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b95c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b968 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b974 x23: .cfa -32 + ^
STACK CFI ba0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ba10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI bac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI bac8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT bae0 140 .cfa: sp 0 + .ra: x30
STACK CFI bae4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI baec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI baf8 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bb00 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bb08 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bbcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bbd0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI bc1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 4b80 378 .cfa: sp 0 + .ra: x30
STACK CFI 4b84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4b94 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4bac x21: .cfa -48 + ^
STACK CFI 4e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT bc20 208 .cfa: sp 0 + .ra: x30
STACK CFI bc24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bc2c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bc34 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bc40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bc48 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bd04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bd08 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI bdf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bdfc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT a920 364 .cfa: sp 0 + .ra: x30
STACK CFI a924 .cfa: sp 1296 +
STACK CFI a928 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI a934 x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI a940 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^
STACK CFI a96c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a970 .cfa: sp 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x29: .cfa -1296 + ^
STACK CFI a984 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI abe8 x21: x21 x22: x22
STACK CFI abf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI abfc .cfa: sp 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x29: .cfa -1296 + ^
STACK CFI ac10 x21: x21 x22: x22
STACK CFI ac14 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI INIT ac90 468 .cfa: sp 0 + .ra: x30
STACK CFI ac94 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI ac9c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI acac x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI acb8 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI aee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI aeec .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4f00 30 .cfa: sp 0 + .ra: x30
STACK CFI 4f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT be50 a8 .cfa: sp 0 + .ra: x30
STACK CFI be54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI be60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI be80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI beec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c4e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI c4e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c4ec x19: .cfa -16 + ^
STACK CFI c528 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c52c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c57c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c5a0 fc .cfa: sp 0 + .ra: x30
STACK CFI c5a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c5ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c5b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c62c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c630 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT bf00 5d4 .cfa: sp 0 + .ra: x30
STACK CFI bf04 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI bf10 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI bf18 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI bf20 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI c0e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c0e4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI c21c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c220 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI INIT 4f30 3c .cfa: sp 0 + .ra: x30
STACK CFI 4f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f3c x19: .cfa -16 + ^
STACK CFI 4f64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
