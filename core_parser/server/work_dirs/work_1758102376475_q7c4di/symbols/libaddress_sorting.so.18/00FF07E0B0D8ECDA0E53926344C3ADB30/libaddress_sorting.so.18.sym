MODULE Linux arm64 00FF07E0B0D8ECDA0E53926344C3ADB30 libaddress_sorting.so.18
INFO CODE_ID E007FF00D8B0DAEC0E53926344C3ADB3
PUBLIC b88 0 _init
PUBLIC ca0 0 call_weak_fn
PUBLIC cb4 0 deregister_tm_clones
PUBLIC ce4 0 register_tm_clones
PUBLIC d20 0 __do_global_dtors_aux
PUBLIC d70 0 frame_dummy
PUBLIC d80 0 get_label_value.part.0
PUBLIC e90 0 get_precedence_value.part.0
PUBLIC f90 0 address_sorting_get_source_addr_for_testing
PUBLIC fc0 0 address_sorting_abstract_get_family
PUBLIC ff0 0 sockaddr_get_scope
PUBLIC 10a0 0 rfc_6724_compare
PUBLIC 1690 0 address_sorting_override_source_addr_factory_for_testing
PUBLIC 16d0 0 address_sorting_rfc_6724_sort
PUBLIC 17b0 0 address_sorting_init
PUBLIC 17e0 0 address_sorting_shutdown
PUBLIC 1820 0 posix_source_addr_factory_destroy
PUBLIC 1830 0 posix_source_addr_factory_get_source_addr
PUBLIC 1910 0 address_sorting_create_source_addr_factory_for_current_platform
PUBLIC 1934 0 _fini
STACK CFI INIT cb4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ce4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d20 50 .cfa: sp 0 + .ra: x30
STACK CFI d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d38 x19: .cfa -16 + ^
STACK CFI d68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d80 104 .cfa: sp 0 + .ra: x30
STACK CFI INIT e90 100 .cfa: sp 0 + .ra: x30
STACK CFI INIT f90 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff0 a8 .cfa: sp 0 + .ra: x30
STACK CFI ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ffc x19: .cfa -16 + ^
STACK CFI 1028 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 102c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 108c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1090 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10a0 5e8 .cfa: sp 0 + .ra: x30
STACK CFI 10a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 124c x25: .cfa -16 + ^
STACK CFI 128c x25: x25
STACK CFI 12d4 x23: x23 x24: x24
STACK CFI 12dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13a4 x23: x23 x24: x24
STACK CFI 13bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 13c4 x23: x23 x24: x24
STACK CFI 13dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1504 x25: .cfa -16 + ^
STACK CFI 152c x25: x25
STACK CFI 163c x23: x23 x24: x24
STACK CFI 1640 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1668 x23: x23 x24: x24
STACK CFI 166c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1670 x25: x25
STACK CFI 1674 x25: .cfa -16 + ^
STACK CFI 1678 x23: x23 x24: x24
STACK CFI 167c x25: x25
STACK CFI 1680 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1684 x25: x25
STACK CFI INIT 1690 40 .cfa: sp 0 + .ra: x30
STACK CFI 1694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 169c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16d0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 16d4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 16dc x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 16e4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 16f0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 177c x19: x19 x20: x20
STACK CFI 179c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 17a0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 17b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 17b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17bc x19: .cfa -16 + ^
STACK CFI 17d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 17dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 17e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 17e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17ec x19: .cfa -16 + ^
STACK CFI 1810 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1814 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1820 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1830 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1834 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 183c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 1848 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 1880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1884 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 190c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1910 24 .cfa: sp 0 + .ra: x30
STACK CFI 1914 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1930 .cfa: sp 0 + .ra: .ra x29: x29
