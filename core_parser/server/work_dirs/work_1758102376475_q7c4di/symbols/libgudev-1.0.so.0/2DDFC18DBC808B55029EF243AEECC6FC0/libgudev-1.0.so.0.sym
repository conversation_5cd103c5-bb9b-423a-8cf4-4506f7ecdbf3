MODULE Linux arm64 2DDFC18DBC808B55029EF243AEECC6FC0 libgudev-1.0.so.0
INFO CODE_ID 8DC1DF2D80BC558B029EF243AEECC6FCFD92C6C5
PUBLIC 3c30 0 g_udev_client_get_type
PUBLIC 40c0 0 g_udev_client_new
PUBLIC 4100 0 g_udev_client_query_by_subsystem
PUBLIC 4230 0 g_udev_client_query_by_device_number
PUBLIC 4308 0 g_udev_client_query_by_device_file
PUBLIC 4438 0 g_udev_client_query_by_sysfs_path
PUBLIC 4530 0 g_udev_client_query_by_subsystem_and_name
PUBLIC 48d0 0 g_udev_device_get_type
PUBLIC 4a60 0 g_udev_device_get_subsystem
PUBLIC 4ae0 0 g_udev_device_get_devtype
PUBLIC 4b60 0 g_udev_device_get_name
PUBLIC 4be0 0 g_udev_device_get_number
PUBLIC 4c60 0 g_udev_device_get_sysfs_path
PUBLIC 4ce0 0 g_udev_device_get_driver
PUBLIC 4d60 0 g_udev_device_get_action
PUBLIC 4de0 0 g_udev_device_get_seqnum
PUBLIC 4e60 0 g_udev_device_get_device_number
PUBLIC 4ee0 0 g_udev_device_get_device_file
PUBLIC 4f60 0 g_udev_device_get_device_type
PUBLIC 5048 0 g_udev_device_get_device_file_symlinks
PUBLIC 5150 0 g_udev_device_get_parent
PUBLIC 51d8 0 g_udev_device_get_parent_with_subsystem
PUBLIC 52b8 0 g_udev_device_get_property_keys
PUBLIC 53c0 0 g_udev_device_has_property
PUBLIC 5488 0 g_udev_device_get_property
PUBLIC 5548 0 g_udev_device_get_property_as_int
PUBLIC 5638 0 g_udev_device_get_property_as_uint64
PUBLIC 5710 0 g_udev_device_get_property_as_double
PUBLIC 57e8 0 g_udev_device_get_property_as_boolean
PUBLIC 58f8 0 g_udev_device_get_property_as_strv
PUBLIC 5a88 0 g_udev_device_get_sysfs_attr_keys
PUBLIC 5b90 0 g_udev_device_has_sysfs_attr
PUBLIC 5c58 0 g_udev_device_get_sysfs_attr
PUBLIC 5d18 0 g_udev_device_get_sysfs_attr_as_int
PUBLIC 5e08 0 g_udev_device_get_sysfs_attr_as_uint64
PUBLIC 5ee0 0 g_udev_device_get_sysfs_attr_as_double
PUBLIC 5fb8 0 g_udev_device_get_sysfs_attr_as_boolean
PUBLIC 60c8 0 g_udev_device_get_sysfs_attr_as_strv
PUBLIC 6258 0 g_udev_device_get_tags
PUBLIC 6360 0 g_udev_device_get_is_initialized
PUBLIC 63e0 0 g_udev_device_get_usec_since_initialized
PUBLIC 6580 0 g_udev_enumerator_get_type
PUBLIC 6920 0 g_udev_enumerator_new
PUBLIC 69b8 0 g_udev_enumerator_add_match_subsystem
PUBLIC 6a80 0 g_udev_enumerator_add_nomatch_subsystem
PUBLIC 6b48 0 g_udev_enumerator_add_match_sysfs_attr
PUBLIC 6c48 0 g_udev_enumerator_add_nomatch_sysfs_attr
PUBLIC 6d48 0 g_udev_enumerator_add_match_property
PUBLIC 6e48 0 g_udev_enumerator_add_match_name
PUBLIC 6f10 0 g_udev_enumerator_add_sysfs_path
PUBLIC 6fd8 0 g_udev_enumerator_add_match_tag
PUBLIC 70a0 0 g_udev_enumerator_add_match_is_initialized
PUBLIC 7128 0 g_udev_enumerator_execute
PUBLIC 7240 0 g_udev_device_type_get_type
STACK CFI INIT 3978 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39a8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 39ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39f4 x19: .cfa -16 + ^
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a38 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a50 68 .cfa: sp 0 + .ra: x30
STACK CFI 3a54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a64 x19: .cfa -16 + ^
STACK CFI 3ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ab8 ec .cfa: sp 0 + .ra: x30
STACK CFI 3abc .cfa: sp 64 +
STACK CFI 3ac0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ac8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b98 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ba8 88 .cfa: sp 0 + .ra: x30
STACK CFI 3bb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3bc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3bd4 x21: .cfa -16 + ^
STACK CFI 3c18 x21: x21
STACK CFI 3c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c30 64 .cfa: sp 0 + .ra: x30
STACK CFI 3c34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c98 bc .cfa: sp 0 + .ra: x30
STACK CFI 3c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d58 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3d5c .cfa: sp 64 +
STACK CFI 3d60 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d68 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3dfc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3e20 ec .cfa: sp 0 + .ra: x30
STACK CFI 3e24 .cfa: sp 80 +
STACK CFI 3e28 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e44 x23: .cfa -16 + ^
STACK CFI 3e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3ea0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3f10 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f20 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 408c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 40b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 40c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 40c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40cc x19: .cfa -16 + ^
STACK CFI 40f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4100 12c .cfa: sp 0 + .ra: x30
STACK CFI 4104 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4110 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 411c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4120 x23: .cfa -16 + ^
STACK CFI 41e8 x19: x19 x20: x20
STACK CFI 41ec x21: x21 x22: x22
STACK CFI 41f0 x23: x23
STACK CFI 41f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 41fc x19: x19 x20: x20
STACK CFI 4200 x21: x21 x22: x22
STACK CFI 4204 x23: x23
STACK CFI 4228 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4230 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 423c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 424c x21: .cfa -16 + ^
STACK CFI 42b0 x21: x21
STACK CFI 42b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 42bc x21: x21
STACK CFI 42ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4300 x21: x21
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4308 130 .cfa: sp 0 + .ra: x30
STACK CFI 430c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4314 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4324 x21: .cfa -160 + ^
STACK CFI 43f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4438 f8 .cfa: sp 0 + .ra: x30
STACK CFI 443c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4444 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 451c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 452c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4530 144 .cfa: sp 0 + .ra: x30
STACK CFI 4534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 453c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 454c x21: .cfa -16 + ^
STACK CFI 45b0 x21: x21
STACK CFI 45bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45c4 x21: x21
STACK CFI 45f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4628 x21: x21
STACK CFI 462c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4630 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4660 x21: x21
STACK CFI 4664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4668 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4670 x21: x21
STACK CFI INIT 4678 d8 .cfa: sp 0 + .ra: x30
STACK CFI 467c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4684 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4694 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 46c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 473c x19: x19 x20: x20
STACK CFI 474c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4750 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4768 68 .cfa: sp 0 + .ra: x30
STACK CFI 476c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 477c x19: .cfa -16 + ^
STACK CFI 47cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 47d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47dc x19: .cfa -16 + ^
STACK CFI 480c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4810 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 482c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4830 9c .cfa: sp 0 + .ra: x30
STACK CFI 4834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4848 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4884 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 48d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4904 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4938 d4 .cfa: sp 0 + .ra: x30
STACK CFI 493c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4944 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a10 50 .cfa: sp 0 + .ra: x30
STACK CFI 4a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a60 7c .cfa: sp 0 + .ra: x30
STACK CFI 4a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a70 x19: .cfa -16 + ^
STACK CFI 4aa4 x19: x19
STACK CFI 4aa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ab0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4ab4 x19: x19
STACK CFI 4ad8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ae0 80 .cfa: sp 0 + .ra: x30
STACK CFI 4ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4af0 x19: .cfa -16 + ^
STACK CFI 4b24 x19: x19
STACK CFI 4b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4b34 x19: x19
STACK CFI 4b5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b60 80 .cfa: sp 0 + .ra: x30
STACK CFI 4b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b70 x19: .cfa -16 + ^
STACK CFI 4ba4 x19: x19
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4bb4 x19: x19
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4be0 80 .cfa: sp 0 + .ra: x30
STACK CFI 4be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bf0 x19: .cfa -16 + ^
STACK CFI 4c24 x19: x19
STACK CFI 4c28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4c34 x19: x19
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4c60 80 .cfa: sp 0 + .ra: x30
STACK CFI 4c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c70 x19: .cfa -16 + ^
STACK CFI 4ca4 x19: x19
STACK CFI 4ca8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4cb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4cb4 x19: x19
STACK CFI 4cdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ce0 80 .cfa: sp 0 + .ra: x30
STACK CFI 4ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cf0 x19: .cfa -16 + ^
STACK CFI 4d24 x19: x19
STACK CFI 4d28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4d34 x19: x19
STACK CFI 4d5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4d60 80 .cfa: sp 0 + .ra: x30
STACK CFI 4d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d70 x19: .cfa -16 + ^
STACK CFI 4da4 x19: x19
STACK CFI 4da8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4db4 x19: x19
STACK CFI 4ddc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4de0 80 .cfa: sp 0 + .ra: x30
STACK CFI 4de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4df0 x19: .cfa -16 + ^
STACK CFI 4e24 x19: x19
STACK CFI 4e28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4e34 x19: x19
STACK CFI 4e5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e60 80 .cfa: sp 0 + .ra: x30
STACK CFI 4e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e70 x19: .cfa -16 + ^
STACK CFI 4ea4 x19: x19
STACK CFI 4ea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4eb4 x19: x19
STACK CFI 4edc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ee0 80 .cfa: sp 0 + .ra: x30
STACK CFI 4ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ef0 x19: .cfa -16 + ^
STACK CFI 4f24 x19: x19
STACK CFI 4f28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4f34 x19: x19
STACK CFI 4f5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4f60 e4 .cfa: sp 0 + .ra: x30
STACK CFI 4f64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4f6c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ff4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5048 108 .cfa: sp 0 + .ra: x30
STACK CFI 504c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5058 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5094 x19: x19 x20: x20
STACK CFI 5098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 509c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 50a0 x19: x19 x20: x20
STACK CFI 50c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 50d0 x21: .cfa -16 + ^
STACK CFI 513c x21: x21
STACK CFI 5144 x19: x19 x20: x20
STACK CFI 5148 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5150 88 .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5160 x19: .cfa -16 + ^
STACK CFI 519c x19: x19
STACK CFI 51a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51ac x19: x19
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51d8 dc .cfa: sp 0 + .ra: x30
STACK CFI 51dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 51f4 x21: .cfa -16 + ^
STACK CFI 523c x19: x19 x20: x20
STACK CFI 5240 x21: x21
STACK CFI 5248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 524c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5250 x19: x19 x20: x20
STACK CFI 5254 x21: x21
STACK CFI 527c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5280 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 52a8 x19: x19 x20: x20
STACK CFI 52ac x21: x21
STACK CFI 52b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 52b8 108 .cfa: sp 0 + .ra: x30
STACK CFI 52bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5304 x19: x19 x20: x20
STACK CFI 5308 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 530c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5310 x19: x19 x20: x20
STACK CFI 5338 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 533c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5340 x21: .cfa -16 + ^
STACK CFI 53ac x21: x21
STACK CFI 53b4 x19: x19 x20: x20
STACK CFI 53b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 53c0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 53c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5420 x19: x19 x20: x20
STACK CFI 5424 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5428 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 542c x19: x19 x20: x20
STACK CFI 5454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5458 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5480 x19: x19 x20: x20
STACK CFI 5484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5488 bc .cfa: sp 0 + .ra: x30
STACK CFI 548c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5498 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54d8 x19: x19 x20: x20
STACK CFI 54dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 54e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 54e8 x19: x19 x20: x20
STACK CFI 5510 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5514 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 553c x19: x19 x20: x20
STACK CFI 5540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5548 f0 .cfa: sp 0 + .ra: x30
STACK CFI 554c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5558 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 55b8 x19: x19 x20: x20
STACK CFI 55bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 55c4 x19: x19 x20: x20
STACK CFI 55f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5620 x19: x19 x20: x20
STACK CFI 5624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5628 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5630 x19: x19 x20: x20
STACK CFI 5634 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5638 d8 .cfa: sp 0 + .ra: x30
STACK CFI 563c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5648 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5690 x19: x19 x20: x20
STACK CFI 5698 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 56a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56a4 x19: x19 x20: x20
STACK CFI 56cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 56d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56f8 x19: x19 x20: x20
STACK CFI 56fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5700 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5708 x19: x19 x20: x20
STACK CFI 570c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5710 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5768 x19: x19 x20: x20
STACK CFI 5770 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5774 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5778 x19: x19 x20: x20
STACK CFI 57a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 57a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 57cc x19: x19 x20: x20
STACK CFI 57d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 57d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 57dc x19: x19 x20: x20
STACK CFI 57e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 57e8 110 .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 585c x19: x19 x20: x20
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5864 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5868 x19: x19 x20: x20
STACK CFI 5890 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 58b0 x19: x19 x20: x20
STACK CFI 58b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 58b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 58e0 x19: x19 x20: x20
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 58e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 58f0 x19: x19 x20: x20
STACK CFI 58f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 58f8 190 .cfa: sp 0 + .ra: x30
STACK CFI 58fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5904 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5910 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5968 x21: x21 x22: x22
STACK CFI 596c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5970 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5990 x23: .cfa -16 + ^
STACK CFI 59c0 x21: x21 x22: x22
STACK CFI 59c4 x23: x23
STACK CFI 59c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 59d0 x21: x21 x22: x22
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5a34 x21: x21 x22: x22
STACK CFI 5a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5a4c x21: x21 x22: x22
STACK CFI 5a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5a88 108 .cfa: sp 0 + .ra: x30
STACK CFI 5a8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ad4 x19: x19 x20: x20
STACK CFI 5ad8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5adc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5ae0 x19: x19 x20: x20
STACK CFI 5b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5b10 x21: .cfa -16 + ^
STACK CFI 5b7c x21: x21
STACK CFI 5b84 x19: x19 x20: x20
STACK CFI 5b88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b90 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ba0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5bf0 x19: x19 x20: x20
STACK CFI 5bf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5bfc x19: x19 x20: x20
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5c50 x19: x19 x20: x20
STACK CFI 5c54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c58 bc .cfa: sp 0 + .ra: x30
STACK CFI 5c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c68 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5ca8 x19: x19 x20: x20
STACK CFI 5cac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5cb8 x19: x19 x20: x20
STACK CFI 5ce0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5d0c x19: x19 x20: x20
STACK CFI 5d10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5d18 f0 .cfa: sp 0 + .ra: x30
STACK CFI 5d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5d88 x19: x19 x20: x20
STACK CFI 5d8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5d94 x19: x19 x20: x20
STACK CFI 5dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5df0 x19: x19 x20: x20
STACK CFI 5df4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5df8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5e00 x19: x19 x20: x20
STACK CFI 5e04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5e08 d8 .cfa: sp 0 + .ra: x30
STACK CFI 5e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e60 x19: x19 x20: x20
STACK CFI 5e68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5e74 x19: x19 x20: x20
STACK CFI 5e9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5ec8 x19: x19 x20: x20
STACK CFI 5ecc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5ed8 x19: x19 x20: x20
STACK CFI 5edc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ee0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ef0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f38 x19: x19 x20: x20
STACK CFI 5f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5f48 x19: x19 x20: x20
STACK CFI 5f70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5f9c x19: x19 x20: x20
STACK CFI 5fa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5fac x19: x19 x20: x20
STACK CFI 5fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5fb8 110 .cfa: sp 0 + .ra: x30
STACK CFI 5fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fc8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 602c x19: x19 x20: x20
STACK CFI 6030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6034 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6038 x19: x19 x20: x20
STACK CFI 6060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6064 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6080 x19: x19 x20: x20
STACK CFI 6084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6088 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60b0 x19: x19 x20: x20
STACK CFI 60b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 60b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60c0 x19: x19 x20: x20
STACK CFI 60c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 60c8 190 .cfa: sp 0 + .ra: x30
STACK CFI 60cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6138 x21: x21 x22: x22
STACK CFI 613c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6140 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6160 x23: .cfa -16 + ^
STACK CFI 6190 x21: x21 x22: x22
STACK CFI 6194 x23: x23
STACK CFI 6198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 619c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 61a0 x21: x21 x22: x22
STACK CFI 61d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 61d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6204 x21: x21 x22: x22
STACK CFI 6208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 620c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 621c x21: x21 x22: x22
STACK CFI 6220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6224 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6258 108 .cfa: sp 0 + .ra: x30
STACK CFI 625c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6268 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62a4 x19: x19 x20: x20
STACK CFI 62a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 62ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 62b0 x19: x19 x20: x20
STACK CFI 62d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 62dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 62e0 x21: .cfa -16 + ^
STACK CFI 634c x21: x21
STACK CFI 6354 x19: x19 x20: x20
STACK CFI 6358 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6360 80 .cfa: sp 0 + .ra: x30
STACK CFI 6364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6370 x19: .cfa -16 + ^
STACK CFI 63a4 x19: x19
STACK CFI 63a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 63b4 x19: x19
STACK CFI 63dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 63e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 63e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63f0 x19: .cfa -16 + ^
STACK CFI 6424 x19: x19
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6430 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6434 x19: x19
STACK CFI 645c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6460 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6478 68 .cfa: sp 0 + .ra: x30
STACK CFI 647c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 648c x19: .cfa -16 + ^
STACK CFI 64dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 64e0 9c .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64ec x19: .cfa -16 + ^
STACK CFI 656c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6580 64 .cfa: sp 0 + .ra: x30
STACK CFI 6584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 658c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 65e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 65e8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 65ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65fc x21: .cfa -16 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6694 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 66c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 66d0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 66d4 .cfa: sp 64 +
STACK CFI 66d8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6774 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 678c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6798 e8 .cfa: sp 0 + .ra: x30
STACK CFI 679c .cfa: sp 80 +
STACK CFI 67a0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 67a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 67b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 67bc x23: .cfa -16 + ^
STACK CFI 6810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6814 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 687c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6880 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 688c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6914 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 691c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6920 98 .cfa: sp 0 + .ra: x30
STACK CFI 6924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6930 x19: .cfa -16 + ^
STACK CFI 6980 x19: x19
STACK CFI 6984 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6988 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 698c x19: x19
STACK CFI 69b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 69b8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 69bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6a14 x19: x19 x20: x20
STACK CFI 6a18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6a20 x19: x19 x20: x20
STACK CFI 6a48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6a74 x19: x19 x20: x20
STACK CFI 6a78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a80 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6adc x19: x19 x20: x20
STACK CFI 6ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6ae8 x19: x19 x20: x20
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6b3c x19: x19 x20: x20
STACK CFI 6b40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b48 fc .cfa: sp 0 + .ra: x30
STACK CFI 6b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b64 x21: .cfa -16 + ^
STACK CFI 6bb4 x19: x19 x20: x20
STACK CFI 6bb8 x21: x21
STACK CFI 6bbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6bc4 x19: x19 x20: x20
STACK CFI 6bc8 x21: x21
STACK CFI 6bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6c1c x19: x19 x20: x20
STACK CFI 6c20 x21: x21
STACK CFI 6c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6c48 fc .cfa: sp 0 + .ra: x30
STACK CFI 6c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c64 x21: .cfa -16 + ^
STACK CFI 6cb4 x19: x19 x20: x20
STACK CFI 6cb8 x21: x21
STACK CFI 6cbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6cc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6cc4 x19: x19 x20: x20
STACK CFI 6cc8 x21: x21
STACK CFI 6cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6d1c x19: x19 x20: x20
STACK CFI 6d20 x21: x21
STACK CFI 6d24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6d48 fc .cfa: sp 0 + .ra: x30
STACK CFI 6d4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d64 x21: .cfa -16 + ^
STACK CFI 6db4 x19: x19 x20: x20
STACK CFI 6db8 x21: x21
STACK CFI 6dbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6dc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6dc4 x19: x19 x20: x20
STACK CFI 6dc8 x21: x21
STACK CFI 6df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6e1c x19: x19 x20: x20
STACK CFI 6e20 x21: x21
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e48 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6ea4 x19: x19 x20: x20
STACK CFI 6ea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6eac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6eb0 x19: x19 x20: x20
STACK CFI 6ed8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6edc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6f04 x19: x19 x20: x20
STACK CFI 6f08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6f10 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6f6c x19: x19 x20: x20
STACK CFI 6f70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6f78 x19: x19 x20: x20
STACK CFI 6fa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6fcc x19: x19 x20: x20
STACK CFI 6fd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6fd8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6fdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fe8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7034 x19: x19 x20: x20
STACK CFI 7038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 703c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7040 x19: x19 x20: x20
STACK CFI 7068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 706c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7094 x19: x19 x20: x20
STACK CFI 7098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 70a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 70a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70b0 x19: .cfa -16 + ^
STACK CFI 70f0 x19: x19
STACK CFI 70f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 70f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 70fc x19: x19
STACK CFI 7124 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7128 114 .cfa: sp 0 + .ra: x30
STACK CFI 712c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7138 x23: .cfa -16 + ^
STACK CFI 7140 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 71f4 x19: x19 x20: x20
STACK CFI 71f8 x21: x21 x22: x22
STACK CFI 71fc x23: x23
STACK CFI 7200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7204 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7208 x19: x19 x20: x20
STACK CFI 720c x21: x21 x22: x22
STACK CFI 7210 x23: x23
STACK CFI 7238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7240 6c .cfa: sp 0 + .ra: x30
STACK CFI 7244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 724c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 726c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 72a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
