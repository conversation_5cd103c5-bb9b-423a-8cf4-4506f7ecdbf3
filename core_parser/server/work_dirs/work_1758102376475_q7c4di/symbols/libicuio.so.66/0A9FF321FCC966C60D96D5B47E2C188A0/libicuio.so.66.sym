MODULE Linux arm64 0A9FF321FCC966C60D96D5B47E2C188A0 libicuio.so.66
INFO CODE_ID 21F39F0AC9FCC6660D96D5B47E2C188A1870AA1C
PUBLIC 34b0 0 u_locbund_init_66
PUBLIC 3558 0 u_locbund_close_66
PUBLIC 35b8 0 u_locbund_getNumberFormat_66
PUBLIC 3890 0 u_finit_66
PUBLIC 3898 0 u_fadopt_66
PUBLIC 38a0 0 u_fopen_66
PUBLIC 3928 0 u_fopen_u_66
PUBLIC 39b0 0 u_fstropen_66
PUBLIC 3a48 0 u_feof_66
PUBLIC 3a98 0 u_fflush_66
PUBLIC 3af0 0 u_frewind_66
PUBLIC 3b40 0 u_fclose_66
PUBLIC 3bb8 0 u_fgetfile_66
PUBLIC 3bc0 0 u_fgetlocale_66
PUBLIC 3bc8 0 u_fsetlocale_66
PUBLIC 3c08 0 u_fgetcodepage_66
PUBLIC 3c78 0 u_fsetcodepage_66
PUBLIC 3d30 0 u_fgetConverter_66
PUBLIC 3d38 0 u_fgetNumberFormat_66
PUBLIC 3d48 0 ufmt_digitvalue(char16_t)
PUBLIC 3d98 0 ufmt_isdigit(char16_t, int)
PUBLIC 3dc8 0 ufmt_64tou(char16_t*, int*, unsigned long, unsigned char, signed char, int)
PUBLIC 3e98 0 ufmt_ptou(char16_t*, int*, void*, signed char)
PUBLIC 3f48 0 ufmt_uto64(char16_t const*, int*, signed char)
PUBLIC 3fd0 0 ufmt_utop(char16_t const*, int*)
PUBLIC 4130 0 ufmt_defaultCPToUnicode(char const*, int, char16_t*, int)
PUBLIC 43b8 0 u_get_stdout_66
PUBLIC 4430 0 u_vfprintf_u_66
PUBLIC 44b0 0 u_fprintf_u_66
PUBLIC 4558 0 u_vfprintf_66
PUBLIC 4668 0 u_fprintf_66
PUBLIC 4710 0 u_printf_66
PUBLIC 47c8 0 u_printf_u_66
PUBLIC 5d28 0 u_printf_parse_66
PUBLIC 6b38 0 u_vfscanf_u_66
PUBLIC 6b68 0 u_fscanf_u_66
PUBLIC 6c10 0 u_vfscanf_66
PUBLIC 6d20 0 u_fscanf_66
PUBLIC 8208 0 u_scanf_parse_66
PUBLIC 8640 0 u_file_write_flush_66
PUBLIC 8958 0 ufile_flush_translit(UFILE*)
PUBLIC 8980 0 u_fsettransliterator_66
PUBLIC 8a80 0 ufile_close_translit(UFILE*)
PUBLIC 8ae0 0 ufile_flush_io(UFILE*)
PUBLIC 8b08 0 u_file_write_66
PUBLIC 8b18 0 u_fputs_66
PUBLIC 8b68 0 u_fputc_66
PUBLIC 8c38 0 ufile_fill_uchar_buffer(UFILE*)
PUBLIC 8e20 0 u_fgets_66
PUBLIC 9020 0 ufile_getch_66
PUBLIC 9090 0 u_fgetc_66
PUBLIC 90e0 0 ufile_getch32_66
PUBLIC 91a0 0 u_fgetcx_66
PUBLIC 91f0 0 u_fungetc_66
PUBLIC 9270 0 u_file_read_66
PUBLIC 9510 0 u_vsnprintf_u_66
PUBLIC 9620 0 u_sprintf_u_66
PUBLIC 96d0 0 u_snprintf_u_66
PUBLIC 9778 0 u_vsnprintf_66
PUBLIC 98a0 0 u_sprintf_66
PUBLIC 9950 0 u_vsprintf_66
PUBLIC 9988 0 u_snprintf_66
PUBLIC 9a30 0 u_vsprintf_u_66
PUBLIC 9a68 0 u_vsscanf_u_66
PUBLIC 9b58 0 u_sscanf_u_66
PUBLIC 9c00 0 u_vsscanf_66
PUBLIC 9d10 0 u_sscanf_66
PUBLIC 9db8 0 icu_66::operator<<(std::ostream&, icu_66::UnicodeString const&)
PUBLIC 9f48 0 icu_66::operator>>(std::istream&, icu_66::UnicodeString&)
PUBLIC a288 0 ucln_io_registerCleanup_66
STACK CFI INIT 33b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33e0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3420 48 .cfa: sp 0 + .ra: x30
STACK CFI 3424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 342c x19: .cfa -16 + ^
STACK CFI 3464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3468 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3470 3c .cfa: sp 0 + .ra: x30
STACK CFI 3474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 347c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 34b0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 34b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34cc x21: .cfa -16 + ^
STACK CFI 3538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 353c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3558 5c .cfa: sp 0 + .ra: x30
STACK CFI 355c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3564 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3574 x21: .cfa -16 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 35b8 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 35bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 35c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 35d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3604 x23: x23 x24: x24
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3630 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 363c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3664 x23: x23 x24: x24
STACK CFI 3668 x25: x25 x26: x26
STACK CFI 3674 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 36ac x23: x23 x24: x24
STACK CFI 36b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3720 x25: x25 x26: x26
STACK CFI 3728 x23: x23 x24: x24
STACK CFI 372c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3744 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3748 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 374c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 3768 128 .cfa: sp 0 + .ra: x30
STACK CFI 376c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3774 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3780 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3818 x23: x23 x24: x24
STACK CFI 3840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3844 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3878 x23: x23 x24: x24
STACK CFI 387c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3880 x23: x23 x24: x24
STACK CFI 388c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3898 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 38a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38b4 x21: .cfa -16 + ^
STACK CFI 38ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3928 84 .cfa: sp 0 + .ra: x30
STACK CFI 392c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3934 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 3944 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 3964 x23: .cfa -288 + ^
STACK CFI 39a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 39a8 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x29: .cfa -336 + ^
STACK CFI INIT 39b0 98 .cfa: sp 0 + .ra: x30
STACK CFI 39b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 39bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3a0c x21: x21 x22: x22
STACK CFI 3a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3a20 x21: x21 x22: x22
STACK CFI 3a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3a44 x21: x21 x22: x22
STACK CFI INIT 3a48 50 .cfa: sp 0 + .ra: x30
STACK CFI 3a74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3a98 54 .cfa: sp 0 + .ra: x30
STACK CFI 3a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3aa4 x19: .cfa -16 + ^
STACK CFI 3ac4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3ae8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3af0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3afc x19: .cfa -16 + ^
STACK CFI 3b28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3b3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b40 74 .cfa: sp 0 + .ra: x30
STACK CFI 3b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b50 x19: .cfa -16 + ^
STACK CFI 3b84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3bb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3bb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bc8 3c .cfa: sp 0 + .ra: x30
STACK CFI 3bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c08 70 .cfa: sp 0 + .ra: x30
STACK CFI 3c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c18 x19: .cfa -32 + ^
STACK CFI 3c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3c78 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3c7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cb4 x21: .cfa -32 + ^
STACK CFI 3ce0 x21: x21
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 3d18 x21: x21
STACK CFI 3d28 x21: .cfa -32 + ^
STACK CFI INIT 3d30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d48 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d98 2c .cfa: sp 0 + .ra: x30
STACK CFI 3d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3da4 x19: .cfa -16 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3dc8 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e98 ac .cfa: sp 0 + .ra: x30
STACK CFI 3e9c .cfa: sp 16 +
STACK CFI 3f40 .cfa: sp 0 +
STACK CFI INIT 3f48 88 .cfa: sp 0 + .ra: x30
STACK CFI 3f4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3f60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f70 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f78 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 3fd0 160 .cfa: sp 0 + .ra: x30
STACK CFI 3fd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3fdc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3fe4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3ff0 x25: .cfa -32 + ^
STACK CFI 3ff8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4124 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4130 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 413c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4148 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 415c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 420c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4210 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4228 3c .cfa: sp 0 + .ra: x30
STACK CFI 422c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4234 x19: .cfa -16 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4268 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4280 138 .cfa: sp 0 + .ra: x30
STACK CFI 4284 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 428c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4298 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 42a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 42cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 42d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 42e0 x25: .cfa -16 + ^
STACK CFI 432c x25: x25
STACK CFI 4344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4348 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 43b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 43b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 43bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 442c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4430 80 .cfa: sp 0 + .ra: x30
STACK CFI 4434 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4450 x19: .cfa -64 + ^
STACK CFI 44a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 44b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 44c4 x19: .cfa -272 + ^
STACK CFI 454c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4550 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 4558 10c .cfa: sp 0 + .ra: x30
STACK CFI 455c .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 4564 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 456c x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 457c x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 4618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 461c .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI INIT 4668 a4 .cfa: sp 0 + .ra: x30
STACK CFI 466c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 467c x19: .cfa -272 + ^
STACK CFI 4704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4708 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 4710 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4714 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 4720 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 47bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47c0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 47c8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 47cc .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 47d8 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 4874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4878 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 4880 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48b8 x19: .cfa -32 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4904 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4908 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4920 178 .cfa: sp 0 + .ra: x30
STACK CFI 4924 .cfa: sp 2144 +
STACK CFI 4928 .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 4930 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 493c x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 4950 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI 4988 x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 49d4 x25: x25 x26: x26
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a04 .cfa: sp 2144 + .ra: .cfa -2136 + ^ x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x21: .cfa -2112 + ^ x22: .cfa -2104 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x29: .cfa -2144 + ^
STACK CFI 4a2c x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 4a90 x25: x25 x26: x26
STACK CFI 4a94 x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI INIT 4a98 18c .cfa: sp 0 + .ra: x30
STACK CFI 4a9c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 4aa4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 4ab0 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 4ac4 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 4ad0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4b9c .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x29: .cfa -352 + ^
STACK CFI INIT 4c28 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4c2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4c34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4c44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c5c x23: .cfa -48 + ^
STACK CFI 4cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4cc8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4ce0 78 .cfa: sp 0 + .ra: x30
STACK CFI 4ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4cf8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4d58 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4d5c .cfa: sp 2128 +
STACK CFI 4d60 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 4d6c x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 4d78 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 4d9c x23: .cfa -2080 + ^
STACK CFI 4df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4df4 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x23: .cfa -2080 + ^ x29: .cfa -2128 + ^
STACK CFI INIT 4df8 154 .cfa: sp 0 + .ra: x30
STACK CFI 4dfc .cfa: sp 2128 +
STACK CFI 4e04 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 4e0c x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 4e1c x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 4e24 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^
STACK CFI 4ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ec0 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x29: .cfa -2128 + ^
STACK CFI INIT 4f50 168 .cfa: sp 0 + .ra: x30
STACK CFI 4f54 .cfa: sp 2128 +
STACK CFI 4f58 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 4f60 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 4f6c x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 4f7c x23: .cfa -2080 + ^ x24: .cfa -2072 + ^
STACK CFI 501c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5020 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x29: .cfa -2128 + ^
STACK CFI INIT 50b8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 50bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 50c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 50d8 x23: .cfa -48 + ^
STACK CFI 5114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 5118 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 512c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5168 x21: x21 x22: x22
STACK CFI 516c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 51a4 x21: x21 x22: x22
STACK CFI 51ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 51b0 260 .cfa: sp 0 + .ra: x30
STACK CFI 51b8 .cfa: sp 4272 +
STACK CFI 51bc .ra: .cfa -4264 + ^ x29: .cfa -4272 + ^
STACK CFI 51c4 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^
STACK CFI 51d4 x23: .cfa -4224 + ^ x24: .cfa -4216 + ^
STACK CFI 51e8 v8: .cfa -4176 + ^
STACK CFI 5200 x19: .cfa -4256 + ^ x20: .cfa -4248 + ^
STACK CFI 521c x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI 523c x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI 5350 x25: x25 x26: x26
STACK CFI 5354 x27: x27 x28: x28
STACK CFI 538c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5390 .cfa: sp 4272 + .ra: .cfa -4264 + ^ v8: .cfa -4176 + ^ x19: .cfa -4256 + ^ x20: .cfa -4248 + ^ x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x23: .cfa -4224 + ^ x24: .cfa -4216 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^ x29: .cfa -4272 + ^
STACK CFI 5404 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5408 x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI 540c x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI INIT 5410 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 5418 .cfa: sp 4224 +
STACK CFI 5420 .ra: .cfa -4216 + ^ x29: .cfa -4224 + ^
STACK CFI 5428 x21: .cfa -4192 + ^ x22: .cfa -4184 + ^
STACK CFI 5434 x19: .cfa -4208 + ^ x20: .cfa -4200 + ^
STACK CFI 5448 x23: .cfa -4176 + ^ x24: .cfa -4168 + ^
STACK CFI 5450 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 5488 x27: .cfa -4144 + ^
STACK CFI 54f4 x27: x27
STACK CFI 5528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 552c .cfa: sp 4224 + .ra: .cfa -4216 + ^ x19: .cfa -4208 + ^ x20: .cfa -4200 + ^ x21: .cfa -4192 + ^ x22: .cfa -4184 + ^ x23: .cfa -4176 + ^ x24: .cfa -4168 + ^ x25: .cfa -4160 + ^ x26: .cfa -4152 + ^ x29: .cfa -4224 + ^
STACK CFI 5554 x27: .cfa -4144 + ^
STACK CFI 55d0 x27: x27
STACK CFI 55d4 x27: .cfa -4144 + ^
STACK CFI INIT 55d8 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 55e0 .cfa: sp 4224 +
STACK CFI 55e4 .ra: .cfa -4216 + ^ x29: .cfa -4224 + ^
STACK CFI 55ec x21: .cfa -4192 + ^ x22: .cfa -4184 + ^
STACK CFI 55fc x23: .cfa -4176 + ^ x24: .cfa -4168 + ^
STACK CFI 5610 v8: .cfa -4136 + ^
STACK CFI 5624 x19: .cfa -4208 + ^ x20: .cfa -4200 + ^
STACK CFI 5648 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 564c x27: .cfa -4144 + ^
STACK CFI 5704 x25: x25 x26: x26
STACK CFI 5708 x27: x27
STACK CFI 5740 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5744 .cfa: sp 4224 + .ra: .cfa -4216 + ^ v8: .cfa -4136 + ^ x19: .cfa -4208 + ^ x20: .cfa -4200 + ^ x21: .cfa -4192 + ^ x22: .cfa -4184 + ^ x23: .cfa -4176 + ^ x24: .cfa -4168 + ^ x25: .cfa -4160 + ^ x26: .cfa -4152 + ^ x27: .cfa -4144 + ^ x29: .cfa -4224 + ^
STACK CFI 5780 x25: x25 x26: x26 x27: x27
STACK CFI 5784 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 5788 x27: .cfa -4144 + ^
STACK CFI INIT 5790 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 5798 .cfa: sp 4224 +
STACK CFI 579c .ra: .cfa -4216 + ^ x29: .cfa -4224 + ^
STACK CFI 57a4 x21: .cfa -4192 + ^ x22: .cfa -4184 + ^
STACK CFI 57b4 x23: .cfa -4176 + ^ x24: .cfa -4168 + ^
STACK CFI 57c8 v8: .cfa -4136 + ^
STACK CFI 57dc x19: .cfa -4208 + ^ x20: .cfa -4200 + ^
STACK CFI 5800 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 5804 x27: .cfa -4144 + ^
STACK CFI 58bc x25: x25 x26: x26
STACK CFI 58c0 x27: x27
STACK CFI 58f8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 58fc .cfa: sp 4224 + .ra: .cfa -4216 + ^ v8: .cfa -4136 + ^ x19: .cfa -4208 + ^ x20: .cfa -4200 + ^ x21: .cfa -4192 + ^ x22: .cfa -4184 + ^ x23: .cfa -4176 + ^ x24: .cfa -4168 + ^ x25: .cfa -4160 + ^ x26: .cfa -4152 + ^ x27: .cfa -4144 + ^ x29: .cfa -4224 + ^
STACK CFI 5938 x25: x25 x26: x26 x27: x27
STACK CFI 593c x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 5940 x27: .cfa -4144 + ^
STACK CFI INIT 5948 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 5950 .cfa: sp 4224 +
STACK CFI 5954 .ra: .cfa -4216 + ^ x29: .cfa -4224 + ^
STACK CFI 595c x21: .cfa -4192 + ^ x22: .cfa -4184 + ^
STACK CFI 596c x23: .cfa -4176 + ^ x24: .cfa -4168 + ^
STACK CFI 5980 v8: .cfa -4136 + ^
STACK CFI 5994 x19: .cfa -4208 + ^ x20: .cfa -4200 + ^
STACK CFI 59b8 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 59bc x27: .cfa -4144 + ^
STACK CFI 5a74 x25: x25 x26: x26
STACK CFI 5a78 x27: x27
STACK CFI 5ab0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ab4 .cfa: sp 4224 + .ra: .cfa -4216 + ^ v8: .cfa -4136 + ^ x19: .cfa -4208 + ^ x20: .cfa -4200 + ^ x21: .cfa -4192 + ^ x22: .cfa -4184 + ^ x23: .cfa -4176 + ^ x24: .cfa -4168 + ^ x25: .cfa -4160 + ^ x26: .cfa -4152 + ^ x27: .cfa -4144 + ^ x29: .cfa -4224 + ^
STACK CFI 5af0 x25: x25 x26: x26 x27: x27
STACK CFI 5af4 x25: .cfa -4160 + ^ x26: .cfa -4152 + ^
STACK CFI 5af8 x27: .cfa -4144 + ^
STACK CFI INIT 5b00 228 .cfa: sp 0 + .ra: x30
STACK CFI 5b04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5b0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5b1c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5b30 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5b3c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5b4c v8: .cfa -40 + ^
STACK CFI 5bcc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5bd0 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -40 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 5c2c x27: .cfa -48 + ^
STACK CFI 5cac x27: x27
STACK CFI 5d24 x27: .cfa -48 + ^
STACK CFI INIT 5d28 e10 .cfa: sp 0 + .ra: x30
STACK CFI 5d2c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 5d3c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 5d70 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5d78 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 5d80 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 65dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 65e0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 6b38 2c .cfa: sp 0 + .ra: x30
STACK CFI 6b40 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b68 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6b6c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6b7c x19: .cfa -272 + ^
STACK CFI 6c04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c08 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6c10 10c .cfa: sp 0 + .ra: x30
STACK CFI 6c14 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 6c1c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 6c24 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 6c34 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 6cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6cd4 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI INIT 6d20 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6d24 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6d34 x19: .cfa -272 + ^
STACK CFI 6dbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6dc0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6dc8 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e20 3c .cfa: sp 0 + .ra: x30
STACK CFI 6e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e30 x19: .cfa -16 + ^
STACK CFI 6e58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e60 cc .cfa: sp 0 + .ra: x30
STACK CFI 6e64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6e6c x23: .cfa -32 + ^
STACK CFI 6e74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6e84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6f18 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6f30 270 .cfa: sp 0 + .ra: x30
STACK CFI 6f34 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6f3c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6f48 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6f78 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6fb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6fc0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 70a0 x21: x21 x22: x22
STACK CFI 70a4 x27: x27 x28: x28
STACK CFI 70f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 70f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 710c x21: x21 x22: x22
STACK CFI 7110 x27: x27 x28: x28
STACK CFI 7134 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7138 x21: x21 x22: x22
STACK CFI 713c x27: x27 x28: x28
STACK CFI 7154 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7164 x21: x21 x22: x22
STACK CFI 7168 x27: x27 x28: x28
STACK CFI 716c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7180 x21: x21 x22: x22
STACK CFI 7184 x27: x27 x28: x28
STACK CFI 7198 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 719c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 71a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 71b8 170 .cfa: sp 0 + .ra: x30
STACK CFI 71bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 71c4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 71d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 71ec x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 71f4 x27: .cfa -32 + ^
STACK CFI 72f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 72f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7328 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7340 f0 .cfa: sp 0 + .ra: x30
STACK CFI 7344 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 734c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7358 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7374 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7424 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7430 108 .cfa: sp 0 + .ra: x30
STACK CFI 7434 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 743c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7448 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7464 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7514 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7538 140 .cfa: sp 0 + .ra: x30
STACK CFI 753c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7544 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7550 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 756c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7628 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7678 110 .cfa: sp 0 + .ra: x30
STACK CFI 767c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7684 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7694 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 76b8 x25: .cfa -32 + ^
STACK CFI 776c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7770 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7788 21c .cfa: sp 0 + .ra: x30
STACK CFI 778c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7794 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 779c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 77a8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 77b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 77c8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 784c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 79a8 108 .cfa: sp 0 + .ra: x30
STACK CFI 79ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 79b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 79c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7a28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7a40 x23: x23 x24: x24
STACK CFI 7a44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7a8c x23: x23 x24: x24
STACK CFI 7a90 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7aa4 x23: x23 x24: x24
STACK CFI 7aac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 7ab0 150 .cfa: sp 0 + .ra: x30
STACK CFI 7ab4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7abc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7ac8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7ad8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7aec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7bc0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7c00 11c .cfa: sp 0 + .ra: x30
STACK CFI 7c04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7c0c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7c40 x25: .cfa -32 + ^
STACK CFI 7d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7d04 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7d20 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 7d24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7d2c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7d38 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7d48 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7d5c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 7d6c x27: .cfa -48 + ^
STACK CFI 7e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7e04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 7e14 v8: .cfa -40 + ^
STACK CFI 7ea0 v8: v8
STACK CFI 7eb0 v8: .cfa -40 + ^
STACK CFI 7ec4 v8: v8
STACK CFI 7ecc v8: .cfa -40 + ^
STACK CFI 7ed8 v8: v8
STACK CFI 7edc v8: .cfa -40 + ^
STACK CFI INIT 7ee0 14c .cfa: sp 0 + .ra: x30
STACK CFI 7ee4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7eec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7ef8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7f1c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7ff0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8030 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8038 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 803c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8044 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 8050 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 8070 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 807c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 80c8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8194 x27: x27 x28: x28
STACK CFI 81d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 81d8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 81e4 x27: x27 x28: x28
STACK CFI 81ec x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8200 x27: x27 x28: x28
STACK CFI 8204 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 8208 434 .cfa: sp 0 + .ra: x30
STACK CFI 820c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8220 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8230 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 823c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8264 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 8270 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 85f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 85fc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8640 318 .cfa: sp 0 + .ra: x30
STACK CFI 8644 .cfa: sp 1200 +
STACK CFI 864c .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 8658 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 8678 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 8684 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 868c x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 87e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 87e8 .cfa: sp 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI INIT 8958 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8980 100 .cfa: sp 0 + .ra: x30
STACK CFI 8984 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 898c x19: .cfa -48 + ^
STACK CFI 89a0 x21: .cfa -40 + ^
STACK CFI 89b0 x22: .cfa -32 + ^
STACK CFI 89d4 x21: x21 x22: x22
STACK CFI 89e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 89e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x21: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 89ec x21: x21
STACK CFI 89fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8a00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 8a24 x22: x22
STACK CFI 8a2c x21: x21
STACK CFI 8a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8a40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 8a64 x21: x21 x22: x22
STACK CFI 8a68 x21: .cfa -40 + ^ x22: .cfa -32 + ^
STACK CFI 8a6c x21: x21 x22: x22
STACK CFI 8a70 x21: .cfa -40 + ^ x22: .cfa -32 + ^
STACK CFI 8a78 x21: x21 x22: x22
STACK CFI INIT 8a80 60 .cfa: sp 0 + .ra: x30
STACK CFI 8a88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a94 x19: .cfa -16 + ^
STACK CFI 8ad8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ae0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b08 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b18 50 .cfa: sp 0 + .ra: x30
STACK CFI 8b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8b68 cc .cfa: sp 0 + .ra: x30
STACK CFI 8b6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8b9c x21: .cfa -32 + ^
STACK CFI 8be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8bec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8c38 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 8c3c .cfa: sp 1152 +
STACK CFI 8c40 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 8c48 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 8c54 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 8c70 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 8c98 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 8ca0 x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI 8d50 x23: x23 x24: x24
STACK CFI 8d54 x27: x27 x28: x28
STACK CFI 8d60 x25: x25 x26: x26
STACK CFI 8d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8d8c .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^ x29: .cfa -1152 + ^
STACK CFI 8dfc x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 8e00 x25: x25 x26: x26
STACK CFI 8e04 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI 8e10 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8e14 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 8e18 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 8e1c x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI INIT 8e20 1fc .cfa: sp 0 + .ra: x30
STACK CFI 8e2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8e34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8e3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8e48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8e54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8e94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8ec8 x21: x21 x22: x22
STACK CFI 8ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8eec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8fa0 x21: x21 x22: x22
STACK CFI 8fa4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8fbc x21: x21 x22: x22
STACK CFI 8fc4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8fcc x21: x21 x22: x22
STACK CFI 9008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 900c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 9020 6c .cfa: sp 0 + .ra: x30
STACK CFI 9024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9030 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 906c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9090 50 .cfa: sp 0 + .ra: x30
STACK CFI 9094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 909c x19: .cfa -32 + ^
STACK CFI 90d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 90dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 90e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 90e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9100 x21: .cfa -16 + ^
STACK CFI 9150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9154 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 91a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 91a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 91ac x19: .cfa -32 + ^
STACK CFI 91e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 91ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 91f0 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9270 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9274 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 927c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9288 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9290 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 92a0 x25: .cfa -16 + ^
STACK CFI 9320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 9328 4c .cfa: sp 0 + .ra: x30
STACK CFI 932c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9334 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9378 194 .cfa: sp 0 + .ra: x30
STACK CFI 937c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9388 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9398 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 93a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 9404 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9408 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9474 x23: x23 x24: x24
STACK CFI 9488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 948c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 94dc x23: x23 x24: x24
STACK CFI 94f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 94f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9510 110 .cfa: sp 0 + .ra: x30
STACK CFI 9514 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 9524 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 9534 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 9544 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 955c x25: .cfa -144 + ^
STACK CFI 9618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 961c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x29: .cfa -208 + ^
STACK CFI INIT 9620 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9624 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 964c x19: .cfa -272 + ^
STACK CFI 96c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 96cc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 96d0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 96d4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 96e4 x19: .cfa -272 + ^
STACK CFI 976c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9770 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 9778 128 .cfa: sp 0 + .ra: x30
STACK CFI 977c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 9784 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 978c x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 979c x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 97cc x25: .cfa -320 + ^
STACK CFI 981c x25: x25
STACK CFI 9848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 984c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x29: .cfa -384 + ^
STACK CFI 988c x25: .cfa -320 + ^
STACK CFI 9894 x25: x25
STACK CFI 989c x25: .cfa -320 + ^
STACK CFI INIT 98a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 98a4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 98cc x19: .cfa -272 + ^
STACK CFI 9948 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 994c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 9950 34 .cfa: sp 0 + .ra: x30
STACK CFI 9958 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9988 a4 .cfa: sp 0 + .ra: x30
STACK CFI 998c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 999c x19: .cfa -272 + ^
STACK CFI 9a24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9a28 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 9a30 34 .cfa: sp 0 + .ra: x30
STACK CFI 9a38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a68 ec .cfa: sp 0 + .ra: x30
STACK CFI 9a6c .cfa: sp 2288 +
STACK CFI 9a70 .ra: .cfa -2280 + ^ x29: .cfa -2288 + ^
STACK CFI 9a78 x19: .cfa -2272 + ^ x20: .cfa -2264 + ^
STACK CFI 9a88 x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 9a9c x21: .cfa -2256 + ^ x22: .cfa -2248 + ^
STACK CFI 9af4 x25: .cfa -2224 + ^
STACK CFI 9b18 x25: x25
STACK CFI 9b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9b4c .cfa: sp 2288 + .ra: .cfa -2280 + ^ x19: .cfa -2272 + ^ x20: .cfa -2264 + ^ x21: .cfa -2256 + ^ x22: .cfa -2248 + ^ x23: .cfa -2240 + ^ x24: .cfa -2232 + ^ x29: .cfa -2288 + ^
STACK CFI 9b50 x25: .cfa -2224 + ^
STACK CFI INIT 9b58 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9b5c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9b6c x19: .cfa -272 + ^
STACK CFI 9bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9bf8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 9c00 10c .cfa: sp 0 + .ra: x30
STACK CFI 9c04 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 9c0c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 9c14 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 9c24 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 9cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9cc4 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI INIT 9d10 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9d14 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9d24 x19: .cfa -272 + ^
STACK CFI 9dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9db0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 9db8 18c .cfa: sp 0 + .ra: x30
STACK CFI 9dbc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 9dc4 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 9dd8 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 9e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e24 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI 9e28 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 9e4c x23: x23 x24: x24
STACK CFI 9e58 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 9e64 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 9e6c x27: .cfa -256 + ^
STACK CFI 9f20 x23: x23 x24: x24
STACK CFI 9f24 x25: x25 x26: x26
STACK CFI 9f28 x27: x27
STACK CFI 9f2c x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^
STACK CFI 9f34 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 9f38 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 9f3c x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 9f40 x27: .cfa -256 + ^
STACK CFI INIT 9f48 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 9f4c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 9f54 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 9f60 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 9f68 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 9fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9fc4 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI a000 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI a00c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI a0b4 x21: x21 x22: x22
STACK CFI a0b8 x27: x27 x28: x28
STACK CFI a0bc x21: .cfa -208 + ^ x22: .cfa -200 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI a210 x21: x21 x22: x22
STACK CFI a214 x27: x27 x28: x28
STACK CFI a218 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI a230 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI a234 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI a238 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 3360 3c .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 336c x19: .cfa -16 + ^
STACK CFI 3390 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a240 44 .cfa: sp 0 + .ra: x30
STACK CFI a244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a24c x19: .cfa -16 + ^
STACK CFI a280 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a288 68 .cfa: sp 0 + .ra: x30
STACK CFI a28c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a294 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a2d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
