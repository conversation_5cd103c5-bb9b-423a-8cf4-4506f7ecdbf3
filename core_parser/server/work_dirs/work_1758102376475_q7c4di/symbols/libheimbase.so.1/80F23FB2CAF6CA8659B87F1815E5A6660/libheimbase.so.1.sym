MODULE Linux arm64 80F23FB2CAF6CA8659B87F1815E5A6660 libheimbase.so.1
INFO CODE_ID B23FF280F6CA86CA59B87F1815E5A66606753705
PUBLIC 2b68 0 heim_array_create
PUBLIC 2b98 0 heim_array_get_type_id
PUBLIC 2ba0 0 heim_array_append_value
PUBLIC 2cb8 0 heim_array_insert_value
PUBLIC 2e78 0 heim_array_iterate_f
PUBLIC 2f28 0 heim_array_iterate_reverse_f
PUBLIC 2fd0 0 heim_array_get_length
PUBLIC 2fd8 0 heim_array_get_value
PUBLIC 3008 0 heim_array_copy_value
PUBLIC 3038 0 heim_array_set_value
PUBLIC 3098 0 heim_array_delete_value
PUBLIC 3130 0 heim_array_filter_f
PUBLIC 36c8 0 _bsearch_text
PUBLIC 36e8 0 _bsearch_file_open
PUBLIC 3ab0 0 _bsearch_file_info
PUBLIC 3ae0 0 _bsearch_file_close
PUBLIC 3bb0 0 _bsearch_file
PUBLIC 4018 0 heim_bool_create
PUBLIC 4030 0 heim_bool_val
PUBLIC 40e8 0 heim_data_create
PUBLIC 4140 0 heim_data_ref_create
PUBLIC 4198 0 heim_data_get_type_id
PUBLIC 41a0 0 heim_data_get_data
PUBLIC 41a8 0 heim_data_get_ptr
PUBLIC 41b0 0 heim_data_get_length
PUBLIC 5800 0 heim_db_register
PUBLIC 59d8 0 heim_db_create
PUBLIC 5cf0 0 heim_db_clone
PUBLIC 5e08 0 heim_db_begin
PUBLIC 5f88 0 heim_db_commit
PUBLIC 62d0 0 heim_db_rollback
PUBLIC 6368 0 heim_db_get_type_id
PUBLIC 6370 0 heim_db_copy_value
PUBLIC 64f8 0 heim_db_delete_key
PUBLIC 6728 0 heim_db_set_value
PUBLIC 6988 0 heim_db_iterate_f
PUBLIC 6b18 0 heim_dict_create
PUBLIC 6be8 0 heim_dict_get_type_id
PUBLIC 6bf0 0 heim_dict_get_value
PUBLIC 6c18 0 heim_dict_copy_value
PUBLIC 6c48 0 heim_dict_set_value
PUBLIC 6d30 0 heim_dict_delete_key
PUBLIC 6d88 0 heim_dict_iterate_f
PUBLIC 6e40 0 heim_error_create_enomem
PUBLIC 6e48 0 heim_error_createv
PUBLIC 6f40 0 heim_error_create_opt
PUBLIC 7000 0 heim_error_create
PUBLIC 70a8 0 heim_error_copy_string
PUBLIC 7110 0 heim_error_get_code
PUBLIC 7180 0 heim_error_append
PUBLIC 7328 0 heim_alloc
PUBLIC 7450 0 heim_base_once_f
PUBLIC 7608 0 heim_abortv
PUBLIC 7668 0 heim_abort
PUBLIC 76e8 0 heim_retain
PUBLIC 7738 0 heim_release
PUBLIC 7898 0 heim_get_tid
PUBLIC 78b0 0 heim_get_hash
PUBLIC 78f0 0 heim_cmp
PUBLIC 7ba0 0 heim_auto_release_create
PUBLIC 7c28 0 heim_auto_release
PUBLIC 7d28 0 heim_auto_release_drain
PUBLIC 7e10 0 heim_path_vget
PUBLIC 7e78 0 heim_path_vcopy
PUBLIC 7ee8 0 heim_path_get
PUBLIC 7fa8 0 heim_path_copy
PUBLIC 8070 0 heim_path_vcreate
PUBLIC 8420 0 heim_path_create
PUBLIC 84c0 0 heim_path_vdelete
PUBLIC 85c0 0 heim_path_delete
PUBLIC 98d8 0 heim_json_create_with_bytes
PUBLIC 99a8 0 heim_json_create
PUBLIC 99f0 0 heim_show
PUBLIC 9a90 0 heim_json_copy_serialize
PUBLIC 9cc8 0 heim_null_create
PUBLIC 9d40 0 heim_number_create
PUBLIC 9d98 0 heim_number_get_type_id
PUBLIC 9da0 0 heim_number_get_int
PUBLIC 9ef0 0 heim_string_ref_create
PUBLIC 9f58 0 heim_string_create_with_bytes
PUBLIC 9fb0 0 heim_string_create
PUBLIC 9fd8 0 heim_string_create_with_format
PUBLIC a0d8 0 heim_string_get_type_id
PUBLIC a0e0 0 heim_string_get_utf8
PUBLIC a128 0 __heim_string_constant
STACK CFI INIT 2a58 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2acc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ad4 x19: .cfa -16 + ^
STACK CFI 2b0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b18 4c .cfa: sp 0 + .ra: x30
STACK CFI 2b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b68 2c .cfa: sp 0 + .ra: x30
STACK CFI 2b6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ba0 114 .cfa: sp 0 + .ra: x30
STACK CFI 2ba4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2bb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2bc4 x23: .cfa -16 + ^
STACK CFI 2c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2cac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2cb8 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 2cbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2cc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ccc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d18 x23: .cfa -16 + ^
STACK CFI 2d40 x23: x23
STACK CFI 2d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d5c x23: .cfa -16 + ^
STACK CFI 2dcc x23: x23
STACK CFI 2de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2de8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2e20 x23: x23
STACK CFI 2e30 x23: .cfa -16 + ^
STACK CFI 2e50 x23: x23
STACK CFI 2e68 x23: .cfa -16 + ^
STACK CFI 2e74 x23: x23
STACK CFI INIT 2e78 ac .cfa: sp 0 + .ra: x30
STACK CFI 2e7c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2e84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2eb8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2ef4 x21: x21 x22: x22
STACK CFI 2f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2f1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2f20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 2f28 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2f2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f34 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f68 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2fa0 x21: x21 x22: x22
STACK CFI 2fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2fc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2fcc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 2fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd8 2c .cfa: sp 0 + .ra: x30
STACK CFI 2ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3008 2c .cfa: sp 0 + .ra: x30
STACK CFI 3024 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3038 60 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3044 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3050 x21: .cfa -16 + ^
STACK CFI 3088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 308c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3098 98 .cfa: sp 0 + .ra: x30
STACK CFI 309c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a8 x19: .cfa -16 + ^
STACK CFI 30e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3124 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3130 78 .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 313c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3150 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 319c x21: x21 x22: x22
STACK CFI 31a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 31a8 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 31ac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 31b8 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 31c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 320c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3438 x23: x23 x24: x24
STACK CFI 3470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3474 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 34ac x23: x23 x24: x24
STACK CFI 34b0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 34b8 x23: x23 x24: x24
STACK CFI 34bc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 34fc x23: x23 x24: x24
STACK CFI 3510 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3550 x23: x23 x24: x24
STACK CFI 3554 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI INIT 3570 158 .cfa: sp 0 + .ra: x30
STACK CFI 3580 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3588 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3598 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35a0 x23: .cfa -16 + ^
STACK CFI 3610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3614 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 369c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 36b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 36b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 36c8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 36e8 288 .cfa: sp 0 + .ra: x30
STACK CFI 36ec .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 36f4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3700 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3710 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3724 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3888 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3970 140 .cfa: sp 0 + .ra: x30
STACK CFI 3974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 397c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 398c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ab0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ae0 70 .cfa: sp 0 + .ra: x30
STACK CFI 3aec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3af4 x19: .cfa -16 + ^
STACK CFI 3b34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b50 60 .cfa: sp 0 + .ra: x30
STACK CFI 3b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b5c x19: .cfa -32 + ^
STACK CFI 3ba8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3bac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3bb0 324 .cfa: sp 0 + .ra: x30
STACK CFI 3bb4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3bc4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3be0 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 3c08 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3c7c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3d80 x21: x21 x22: x22
STACK CFI 3d8c x19: x19 x20: x20
STACK CFI 3d90 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3e60 x19: x19 x20: x20
STACK CFI 3e64 x21: x21 x22: x22
STACK CFI 3e94 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3e98 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 3ea0 x19: x19 x20: x20
STACK CFI 3ea4 x21: x21 x22: x22
STACK CFI 3ecc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3ed0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 3ed8 13c .cfa: sp 0 + .ra: x30
STACK CFI 3edc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3ee4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3ef4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3f08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3fc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4018 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4038 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4070 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4098 4c .cfa: sp 0 + .ra: x30
STACK CFI 40a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40b0 x19: .cfa -16 + ^
STACK CFI 40d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40e8 58 .cfa: sp 0 + .ra: x30
STACK CFI 40ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40f8 x21: .cfa -16 + ^
STACK CFI 4104 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 413c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4140 58 .cfa: sp 0 + .ra: x30
STACK CFI 4144 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4150 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4160 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4198 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b8 7c .cfa: sp 0 + .ra: x30
STACK CFI 41bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41c4 x19: .cfa -16 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4228 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4238 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4240 1c .cfa: sp 0 + .ra: x30
STACK CFI 4244 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4254 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4260 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4288 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b0 9c .cfa: sp 0 + .ra: x30
STACK CFI 42b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 42bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 42cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4334 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4350 58 .cfa: sp 0 + .ra: x30
STACK CFI 4354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 435c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4368 x21: .cfa -16 + ^
STACK CFI 43a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43a8 124 .cfa: sp 0 + .ra: x30
STACK CFI 43ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43cc x23: .cfa -16 + ^
STACK CFI 4418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 441c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 446c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 44d0 15c .cfa: sp 0 + .ra: x30
STACK CFI 44d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4548 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 45a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 45b0 x25: .cfa -16 + ^
STACK CFI 45e0 x25: x25
STACK CFI 45e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 45fc x25: x25
STACK CFI INIT 4630 5c .cfa: sp 0 + .ra: x30
STACK CFI 4634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 463c x19: .cfa -16 + ^
STACK CFI 4688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4690 188 .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 469c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 46a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 46b0 x23: .cfa -16 + ^
STACK CFI 4718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 471c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 477c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4780 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 47f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 47f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4818 22c .cfa: sp 0 + .ra: x30
STACK CFI 481c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 482c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4838 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 484c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 489c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48a0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 48ac x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4928 x25: x25 x26: x26
STACK CFI 492c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 496c x25: x25 x26: x26
STACK CFI 4970 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 497c x25: x25 x26: x26
STACK CFI 4980 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 49ac x25: x25 x26: x26
STACK CFI 49b0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 49b8 x25: x25 x26: x26
STACK CFI 49bc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4a10 x25: x25 x26: x26
STACK CFI 4a14 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4a30 x25: x25 x26: x26
STACK CFI 4a34 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 4a48 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4a4c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4a54 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4a64 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4a78 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ba8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 4c28 168 .cfa: sp 0 + .ra: x30
STACK CFI 4c2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4c78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ccc x23: x23 x24: x24
STACK CFI 4cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cfc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4d0c x23: x23 x24: x24
STACK CFI 4d24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d50 x23: x23 x24: x24
STACK CFI 4d60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d64 x23: x23 x24: x24
STACK CFI 4d74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d7c x23: x23 x24: x24
STACK CFI 4d8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 4d90 94 .cfa: sp 0 + .ra: x30
STACK CFI 4d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4df0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e28 43c .cfa: sp 0 + .ra: x30
STACK CFI 4e2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4e34 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4e44 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4e60 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4f14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 4f4c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4f54 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4fcc x25: x25 x26: x26
STACK CFI 4fd0 x27: x27 x28: x28
STACK CFI 5074 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 50d8 x27: x27 x28: x28
STACK CFI 5184 x25: x25 x26: x26
STACK CFI 5188 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51a4 x25: x25 x26: x26
STACK CFI 51a8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51b0 x25: x25 x26: x26
STACK CFI 51b4 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 51d4 x25: x25 x26: x26
STACK CFI 51d8 x27: x27 x28: x28
STACK CFI 51dc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51f0 x25: x25 x26: x26
STACK CFI 51f4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5218 x25: x25 x26: x26
STACK CFI 521c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 523c x25: x25 x26: x26
STACK CFI 5240 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5254 x25: x25 x26: x26
STACK CFI 525c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5260 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 5268 88 .cfa: sp 0 + .ra: x30
STACK CFI 526c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5274 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 527c x21: .cfa -16 + ^
STACK CFI 52c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 52cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 52f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 52fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5308 x21: .cfa -32 + ^
STACK CFI 5378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 537c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 53a0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 53a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53b8 x21: .cfa -16 + ^
STACK CFI 5424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5428 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5448 208 .cfa: sp 0 + .ra: x30
STACK CFI 544c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5454 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5460 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 54a0 x23: .cfa -32 + ^
STACK CFI 5508 x23: x23
STACK CFI 550c x23: .cfa -32 + ^
STACK CFI 5510 x23: x23
STACK CFI 553c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5540 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 554c x23: x23
STACK CFI 5550 x23: .cfa -32 + ^
STACK CFI 55b4 x23: x23
STACK CFI 55c8 x23: .cfa -32 + ^
STACK CFI 55cc x23: x23
STACK CFI 55d8 x23: .cfa -32 + ^
STACK CFI 55f8 x23: x23
STACK CFI 55fc x23: .cfa -32 + ^
STACK CFI 560c x23: x23
STACK CFI 5610 x23: .cfa -32 + ^
STACK CFI 5644 x23: x23
STACK CFI 564c x23: .cfa -32 + ^
STACK CFI INIT 5650 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5654 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5668 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 56f8 80 .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5708 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5724 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5728 x21: .cfa -16 + ^
STACK CFI 575c x21: x21
STACK CFI 576c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5770 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5774 x21: x21
STACK CFI INIT 5778 88 .cfa: sp 0 + .ra: x30
STACK CFI 577c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 579c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 57a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 57e8 x21: x21 x22: x22
STACK CFI 57ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57f8 x21: x21 x22: x22
STACK CFI INIT 5800 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 5804 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 580c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5818 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5820 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 597c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5980 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 599c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 59d8 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 59dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 59e4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5a04 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5a1c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5b2c x25: x25 x26: x26
STACK CFI 5b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5b5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 5b84 x25: x25 x26: x26
STACK CFI 5ba0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5bf4 x25: x25 x26: x26
STACK CFI 5bfc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c08 x25: x25 x26: x26
STACK CFI 5c0c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c38 x25: x25 x26: x26
STACK CFI 5c3c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c54 x25: x25 x26: x26
STACK CFI 5c5c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c74 x25: x25 x26: x26
STACK CFI 5c7c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c8c x25: x25 x26: x26
STACK CFI 5ca8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 5cb0 3c .cfa: sp 0 + .ra: x30
STACK CFI 5cc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cc8 x19: .cfa -16 + ^
STACK CFI 5ce8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5cf0 118 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d04 x21: .cfa -16 + ^
STACK CFI 5d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5dac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e08 180 .cfa: sp 0 + .ra: x30
STACK CFI 5e0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e20 x21: .cfa -16 + ^
STACK CFI 5ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ea4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5f88 344 .cfa: sp 0 + .ra: x30
STACK CFI 5f8c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5f94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5fa4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 603c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6040 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 6054 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 60b8 x23: x23 x24: x24
STACK CFI 60f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 60fc x23: x23 x24: x24
STACK CFI 6100 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 619c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 61f4 x25: x25 x26: x26
STACK CFI 6204 x23: x23 x24: x24
STACK CFI 6210 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6234 x23: x23 x24: x24
STACK CFI 6238 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6280 x23: x23 x24: x24
STACK CFI 6284 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 628c x23: x23 x24: x24
STACK CFI 6290 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 62b0 x25: x25 x26: x26
STACK CFI 62b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 62bc x25: x25 x26: x26
STACK CFI 62c0 x23: x23 x24: x24
STACK CFI 62c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 62c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 62d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 62d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62e8 x21: .cfa -16 + ^
STACK CFI 6354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6358 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6370 134 .cfa: sp 0 + .ra: x30
STACK CFI 6374 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 637c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6388 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6394 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 63e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 647c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 64a8 50 .cfa: sp 0 + .ra: x30
STACK CFI 64ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 64f8 230 .cfa: sp 0 + .ra: x30
STACK CFI 64fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6504 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6510 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6518 x23: .cfa -16 + ^
STACK CFI 6604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6608 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 668c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 66b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 66b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 66f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 66f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 670c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6710 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6728 25c .cfa: sp 0 + .ra: x30
STACK CFI 672c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6734 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 673c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6748 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 678c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 685c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 68ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 68b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 696c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6988 90 .cfa: sp 0 + .ra: x30
STACK CFI 698c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6994 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 69a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 69ac x23: .cfa -16 + ^
STACK CFI 6a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6a18 78 .cfa: sp 0 + .ra: x30
STACK CFI 6a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a24 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a84 x19: x19 x20: x20
STACK CFI 6a8c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6a90 84 .cfa: sp 0 + .ra: x30
STACK CFI 6a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a9c x21: .cfa -16 + ^
STACK CFI 6aa8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6afc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6b18 d0 .cfa: sp 0 + .ra: x30
STACK CFI 6b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6be0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6be8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bf0 24 .cfa: sp 0 + .ra: x30
STACK CFI 6bf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6c18 2c .cfa: sp 0 + .ra: x30
STACK CFI 6c1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6c48 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6d30 58 .cfa: sp 0 + .ra: x30
STACK CFI 6d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d4c x19: .cfa -16 + ^
STACK CFI 6d78 x19: x19
STACK CFI 6d7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d88 7c .cfa: sp 0 + .ra: x30
STACK CFI 6d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d94 x23: .cfa -16 + ^
STACK CFI 6d9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6da8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6e08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e18 28 .cfa: sp 0 + .ra: x30
STACK CFI 6e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e24 x19: .cfa -16 + ^
STACK CFI 6e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e48 f4 .cfa: sp 0 + .ra: x30
STACK CFI 6e4c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6e54 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6e5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6e64 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6f04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 6f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6f28 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 6f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6f40 bc .cfa: sp 0 + .ra: x30
STACK CFI 6f44 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6f4c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ff8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7000 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7004 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 7014 x19: .cfa -272 + ^
STACK CFI 709c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 70a0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 70a8 68 .cfa: sp 0 + .ra: x30
STACK CFI 70ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7104 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7110 6c .cfa: sp 0 + .ra: x30
STACK CFI 7118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7120 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 713c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7140 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7180 6c .cfa: sp 0 + .ra: x30
STACK CFI 7184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 718c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7208 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7218 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7220 3c .cfa: sp 0 + .ra: x30
STACK CFI 7224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7234 x19: .cfa -16 + ^
STACK CFI 7258 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7260 68 .cfa: sp 0 + .ra: x30
STACK CFI 7264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 726c x19: .cfa -16 + ^
STACK CFI 7298 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 729c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 72c8 4c .cfa: sp 0 + .ra: x30
STACK CFI 72cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7318 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7328 4c .cfa: sp 0 + .ra: x30
STACK CFI 732c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7334 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7378 8c .cfa: sp 0 + .ra: x30
STACK CFI 737c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7384 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7390 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 739c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 73ac x25: .cfa -16 + ^
STACK CFI 7400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 7408 3c .cfa: sp 0 + .ra: x30
STACK CFI 740c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7418 x19: .cfa -16 + ^
STACK CFI 7440 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7448 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7450 100 .cfa: sp 0 + .ra: x30
STACK CFI 7454 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 745c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 746c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 74f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 74f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7550 b8 .cfa: sp 0 + .ra: x30
STACK CFI 7554 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7564 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 759c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 75a4 x21: x21
STACK CFI 75b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 75c4 x21: .cfa -16 + ^
STACK CFI 75f0 x21: x21
STACK CFI 75f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7604 x21: x21
STACK CFI INIT 7608 60 .cfa: sp 0 + .ra: x30
STACK CFI 760c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7628 x19: .cfa -80 + ^
STACK CFI INIT 7668 80 .cfa: sp 0 + .ra: x30
STACK CFI 766c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT 76e8 50 .cfa: sp 0 + .ra: x30
STACK CFI 7728 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7738 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7740 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 774c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7794 x21: .cfa -16 + ^
STACK CFI 77c4 x21: x21
STACK CFI 77e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 77f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7800 x21: x21
STACK CFI 780c x21: .cfa -16 + ^
STACK CFI INIT 7810 48 .cfa: sp 0 + .ra: x30
STACK CFI 7818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7820 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7858 40 .cfa: sp 0 + .ra: x30
STACK CFI 7888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7898 18 .cfa: sp 0 + .ra: x30
STACK CFI 789c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 78b0 3c .cfa: sp 0 + .ra: x30
STACK CFI 78b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78bc x19: .cfa -16 + ^
STACK CFI 78dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 78e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 78e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7904 x21: .cfa -16 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7934 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 795c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7960 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7978 5c .cfa: sp 0 + .ra: x30
STACK CFI 797c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 79b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 79b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 79d8 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 79dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 79e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7a00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7a0c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7a18 x27: .cfa -16 + ^
STACK CFI 7a60 x23: x23 x24: x24
STACK CFI 7a64 x25: x25 x26: x26
STACK CFI 7a68 x27: x27
STACK CFI 7a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a80 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 7ad4 x27: x27
STACK CFI 7adc x25: x25 x26: x26
STACK CFI 7af0 x23: x23 x24: x24
STACK CFI 7af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7af8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 7b70 x23: x23 x24: x24
STACK CFI 7b78 x27: x27
STACK CFI 7b8c x25: x25 x26: x26
STACK CFI 7b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7ba0 88 .cfa: sp 0 + .ra: x30
STACK CFI 7ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bd4 x21: .cfa -16 + ^
STACK CFI 7c00 x21: x21
STACK CFI 7c08 x19: x19 x20: x20
STACK CFI 7c0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7c10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7c18 x21: x21
STACK CFI 7c24 x21: .cfa -16 + ^
STACK CFI INIT 7c28 cc .cfa: sp 0 + .ra: x30
STACK CFI 7c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7cc8 x21: x21 x22: x22
STACK CFI 7cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7cf8 30 .cfa: sp 0 + .ra: x30
STACK CFI 7cfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d28 5c .cfa: sp 0 + .ra: x30
STACK CFI 7d2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d34 x21: .cfa -16 + ^
STACK CFI 7d3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7d88 88 .cfa: sp 0 + .ra: x30
STACK CFI 7d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d9c x21: .cfa -16 + ^
STACK CFI 7de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7dec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e10 68 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e2c x19: .cfa -80 + ^
STACK CFI 7e70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e78 70 .cfa: sp 0 + .ra: x30
STACK CFI 7e7c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e90 x19: .cfa -80 + ^
STACK CFI 7ee0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ee4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7ee8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7eec .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7ef4 x19: .cfa -288 + ^
STACK CFI 7f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f9c .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 7fa8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7fac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7fb4 x19: .cfa -288 + ^
STACK CFI 805c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8060 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 8070 3ac .cfa: sp 0 + .ra: x30
STACK CFI 8074 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 807c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8088 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8090 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 809c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 815c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8254 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 832c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8364 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8420 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8424 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 8434 x19: .cfa -256 + ^
STACK CFI 84b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 84bc .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT 84c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 84c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 84d0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 84e0 x21: .cfa -80 + ^
STACK CFI 8570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8574 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 85c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 85c4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 85d4 x19: .cfa -272 + ^
STACK CFI 865c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8660 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 8668 24 .cfa: sp 0 + .ra: x30
STACK CFI 866c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8684 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8690 280 .cfa: sp 0 + .ra: x30
STACK CFI 8694 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 869c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 86ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8740 x21: x21 x22: x22
STACK CFI 8750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8754 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 87d4 x21: x21 x22: x22
STACK CFI 87e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 880c x21: x21 x22: x22
STACK CFI 8820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8824 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8840 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 88f0 x21: x21 x22: x22
STACK CFI 88f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8908 x21: x21 x22: x22
STACK CFI INIT 8910 618 .cfa: sp 0 + .ra: x30
STACK CFI 8914 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 891c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 892c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8974 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 89a4 x23: x23 x24: x24
STACK CFI 8a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8a04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 8ad4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8ba4 x23: x23 x24: x24
STACK CFI 8bbc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8bec x23: x23 x24: x24
STACK CFI 8ce8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8d58 x23: x23 x24: x24
STACK CFI 8d70 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8e3c x23: x23 x24: x24
STACK CFI 8e40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8e70 x23: x23 x24: x24
STACK CFI 8ea8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8edc x23: x23 x24: x24
STACK CFI 8ee8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8ef8 x23: x23 x24: x24
STACK CFI 8f24 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 8f28 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f40 758 .cfa: sp 0 + .ra: x30
STACK CFI 8f44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8f64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8fb4 x21: x21 x22: x22
STACK CFI 8fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8fc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9038 x21: x21 x22: x22
STACK CFI 904c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9050 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 909c x23: .cfa -16 + ^
STACK CFI 90ec x23: x23
STACK CFI 9100 x21: x21 x22: x22
STACK CFI 9114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9118 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9180 x21: x21 x22: x22
STACK CFI 918c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9190 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9318 x23: x23
STACK CFI 931c x23: .cfa -16 + ^
STACK CFI 93ac x23: x23
STACK CFI 953c x21: x21 x22: x22
STACK CFI 9540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9544 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 958c x23: x23
STACK CFI 959c x21: x21 x22: x22
STACK CFI 95a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 95a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 95bc x21: x21 x22: x22
STACK CFI 95c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95d0 x23: .cfa -16 + ^
STACK CFI 95d4 x23: x23
STACK CFI 95ec x21: x21 x22: x22
STACK CFI 95f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 95fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 961c x23: .cfa -16 + ^
STACK CFI 9624 x23: x23
STACK CFI 9638 x21: x21 x22: x22
STACK CFI 9644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9648 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9654 x23: .cfa -16 + ^
STACK CFI 9658 x23: x23
STACK CFI 9664 x23: .cfa -16 + ^
STACK CFI 9670 x23: x23
STACK CFI 9674 x23: .cfa -16 + ^
STACK CFI 9690 x23: x23
STACK CFI INIT 9698 d0 .cfa: sp 0 + .ra: x30
STACK CFI 969c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 96a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 96bc x21: .cfa -16 + ^
STACK CFI 972c x21: x21
STACK CFI 973c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9740 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9760 x21: x21
STACK CFI 9764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9768 64 .cfa: sp 0 + .ra: x30
STACK CFI 976c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 97c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 97d0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 97d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 97dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 97e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 97f0 x23: .cfa -16 + ^
STACK CFI 9870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9874 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9894 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 98a0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 98d8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 98dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 98ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 98fc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 991c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9998 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT 99a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 99ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 99b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 99c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 99ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 99f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 99f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9a04 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9a30 x21: .cfa -64 + ^
STACK CFI 9a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9a8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9a90 238 .cfa: sp 0 + .ra: x30
STACK CFI 9a94 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9a9c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9aac x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9ac0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9b9c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9cc8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9cd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9cd8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9d20 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9d40 54 .cfa: sp 0 + .ra: x30
STACK CFI 9d44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d54 x19: .cfa -16 + ^
STACK CFI 9d6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9d70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9d90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9d98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9da0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9dc0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9de0 80 .cfa: sp 0 + .ra: x30
STACK CFI 9de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9dec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9e60 38 .cfa: sp 0 + .ra: x30
STACK CFI 9e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9e6c x19: .cfa -16 + ^
STACK CFI 9e88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9e8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9e98 58 .cfa: sp 0 + .ra: x30
STACK CFI 9ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9eb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9ef0 68 .cfa: sp 0 + .ra: x30
STACK CFI 9ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9f0c x21: .cfa -16 + ^
STACK CFI 9f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9f58 54 .cfa: sp 0 + .ra: x30
STACK CFI 9f5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f68 x21: .cfa -16 + ^
STACK CFI 9f74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9fb0 28 .cfa: sp 0 + .ra: x30
STACK CFI 9fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9fbc x19: .cfa -16 + ^
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9fd8 100 .cfa: sp 0 + .ra: x30
STACK CFI 9fdc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 9fec x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI a0bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a0c0 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT a0d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a0e0 44 .cfa: sp 0 + .ra: x30
STACK CFI a0e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a0ec x19: .cfa -16 + ^
STACK CFI a104 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a108 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a128 bc .cfa: sp 0 + .ra: x30
STACK CFI a12c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a13c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a14c x21: .cfa -16 + ^
STACK CFI a1a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a1a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a1e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a1e8 128 .cfa: sp 0 + .ra: x30
STACK CFI a1ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a1f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a200 x21: .cfa -16 + ^
STACK CFI a2f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a2fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a318 150 .cfa: sp 0 + .ra: x30
STACK CFI a31c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a324 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a32c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a344 x23: .cfa -16 + ^
STACK CFI a3d0 x23: x23
STACK CFI a3e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a3e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a444 x23: x23
STACK CFI a448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a44c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a454 x23: .cfa -16 + ^
STACK CFI a464 x23: x23
STACK CFI INIT a468 4 .cfa: sp 0 + .ra: x30
