MODULE Linux arm64 65E82F6F10A154139C05759F5F1577C10 libpci.so.3
INFO CODE_ID 6F2FE865A11013549C05759F5F1577C1EC28B5CB
PUBLIC 2c30 0 pci_lookup_method
PUBLIC 2ca8 0 pci_get_method_name
PUBLIC 2ce0 0 pci_alloc
PUBLIC 2dc8 0 pci_init
PUBLIC 2f80 0 pci_cleanup
PUBLIC 2ff0 0 pci_scan_bus
PUBLIC 3090 0 pci_get_dev
PUBLIC 30d8 0 pci_free_dev
PUBLIC 3130 0 pci_read_byte
PUBLIC 31b8 0 pci_read_word
PUBLIC 3278 0 pci_read_long
PUBLIC 3338 0 pci_read_block
PUBLIC 3348 0 pci_read_vpd
PUBLIC 3368 0 pci_write_byte
PUBLIC 33c8 0 pci_write_word
PUBLIC 3480 0 pci_write_long
PUBLIC 3538 0 pci_write_block
PUBLIC 35b8 0 pci_fill_info
PUBLIC 3638 0 pci_setup_cache
PUBLIC 3708 0 pci_get_string_property
PUBLIC 48f0 0 pci_lookup_name
PUBLIC 51f0 0 pci_filter_init
PUBLIC 5208 0 pci_filter_parse_slot
PUBLIC 5460 0 pci_filter_parse_id
PUBLIC 5680 0 pci_filter_match
PUBLIC 5768 0 pci_filter_init
PUBLIC 57d0 0 pci_filter_parse_slot
PUBLIC 5860 0 pci_filter_parse_id
PUBLIC 5900 0 pci_filter_match
PUBLIC 6168 0 pci_free_name_list
PUBLIC 61a0 0 pci_set_name_list_path
PUBLIC 6200 0 pci_load_name_list
PUBLIC 6c70 0 pci_id_cache_flush
PUBLIC 7268 0 pci_get_param
PUBLIC 73d8 0 pci_set_param
PUBLIC 7440 0 pci_walk_params
PUBLIC 7528 0 pci_find_cap_nr
PUBLIC 75e0 0 pci_find_cap
STACK CFI INIT 27e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2818 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2858 48 .cfa: sp 0 + .ra: x30
STACK CFI 285c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2864 x19: .cfa -16 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 28a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 28c4 x19: .cfa -320 + ^
STACK CFI 2970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2974 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 2978 f4 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 298c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 29b0 x21: .cfa -320 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a68 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x29: .cfa -352 + ^
STACK CFI INIT 2a70 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2a74 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 2a84 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI INIT 2b48 54 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b5c x21: .cfa -16 + ^
STACK CFI 2b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ba0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bb0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2bc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2bd0 x23: .cfa -16 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c30 74 .cfa: sp 0 + .ra: x30
STACK CFI 2c34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ca8 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ce0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d00 x21: .cfa -16 + ^
STACK CFI 2dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2dc8 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 2dcc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2dd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2de0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e2c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e40 x25: .cfa -16 + ^
STACK CFI 2ea4 x23: x23 x24: x24
STACK CFI 2ea8 x25: x25
STACK CFI 2f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2f50 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2f60 x23: x23 x24: x24
STACK CFI 2f64 x25: x25
STACK CFI 2f70 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2f74 x23: x23 x24: x24
STACK CFI 2f78 x25: x25
STACK CFI INIT 2f80 6c .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ff0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3000 5c .cfa: sp 0 + .ra: x30
STACK CFI 3004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3010 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3060 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3090 44 .cfa: sp 0 + .ra: x30
STACK CFI 3094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 309c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 30d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 30d8 54 .cfa: sp 0 + .ra: x30
STACK CFI 30dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30ec x19: .cfa -16 + ^
STACK CFI 3128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3130 84 .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3140 x19: .cfa -32 + ^
STACK CFI 31a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 31a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31b8 bc .cfa: sp 0 + .ra: x30
STACK CFI 31bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 31c4 x21: .cfa -32 + ^
STACK CFI 31cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3264 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3278 bc .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3284 x21: .cfa -32 + ^
STACK CFI 328c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3324 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3338 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3348 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3368 5c .cfa: sp 0 + .ra: x30
STACK CFI 336c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 339c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 33c8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 33cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 33d4 x21: .cfa -32 + ^
STACK CFI 33e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3468 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3480 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3484 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 348c x21: .cfa -32 + ^
STACK CFI 349c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3524 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3538 7c .cfa: sp 0 + .ra: x30
STACK CFI 353c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3550 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 35b8 80 .cfa: sp 0 + .ra: x30
STACK CFI 35bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3604 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3638 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3648 bc .cfa: sp 0 + .ra: x30
STACK CFI 364c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3654 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3660 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3708 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3730 16c .cfa: sp 0 + .ra: x30
STACK CFI 3734 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3740 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 374c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 375c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3784 x25: .cfa -16 + ^
STACK CFI 37c8 x25: x25
STACK CFI 37dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 37e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 37e4 x25: x25
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3848 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 38a0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 38a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 38ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 38b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 38c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3904 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 3908 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3914 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3a28 x25: x25 x26: x26
STACK CFI 3a2c x27: x27 x28: x28
STACK CFI 3a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a34 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3a58 70 .cfa: sp 0 + .ra: x30
STACK CFI 3a5c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3a6c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ac4 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3ac8 24c .cfa: sp 0 + .ra: x30
STACK CFI 3acc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3ad8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3ae4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3b54 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 3b78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3b90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3b98 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3c08 x21: x21 x22: x22
STACK CFI 3c0c x25: x25 x26: x26
STACK CFI 3c10 x27: x27 x28: x28
STACK CFI 3c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3c94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 3cb4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3d0c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3d10 x21: x21 x22: x22
STACK CFI INIT 3d18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d28 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d40 2c .cfa: sp 0 + .ra: x30
STACK CFI 3d48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d70 2c .cfa: sp 0 + .ra: x30
STACK CFI 3d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d7c x19: .cfa -16 + ^
STACK CFI 3d98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3da0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3dcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e48 38 .cfa: sp 0 + .ra: x30
STACK CFI 3e4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e80 98 .cfa: sp 0 + .ra: x30
STACK CFI 3e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3ea0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ee0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3f18 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f40 428 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 3f54 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 3f74 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 3f7c x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 40a4 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 4368 12c .cfa: sp 0 + .ra: x30
STACK CFI 436c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4374 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 437c x21: .cfa -16 + ^
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 446c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4470 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4498 1cc .cfa: sp 0 + .ra: x30
STACK CFI 449c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44c0 x21: .cfa -16 + ^
STACK CFI 453c x21: x21
STACK CFI 4540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4544 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45b8 x21: x21
STACK CFI 45c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 45d0 x21: .cfa -16 + ^
STACK CFI INIT 4668 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 466c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4674 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4680 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 468c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 469c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 46a8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 471c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4820 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4824 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4834 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 484c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4888 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 48ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 48f0 900 .cfa: sp 0 + .ra: x30
STACK CFI 48f4 .cfa: sp 240 +
STACK CFI 4900 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4908 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4918 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4944 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 49e0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4a64 x25: x25 x26: x26
STACK CFI 4a80 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4ba8 x25: x25 x26: x26
STACK CFI 4bc4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4c40 x25: x25 x26: x26
STACK CFI 4c54 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4c58 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 4ce8 x25: x25 x26: x26
STACK CFI 4cec x27: x27 x28: x28
STACK CFI 4d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d28 .cfa: sp 240 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 4d30 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4da0 x25: x25 x26: x26
STACK CFI 4dac x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4e4c x25: x25 x26: x26
STACK CFI 4e58 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4ec8 x25: x25 x26: x26
STACK CFI 4ed4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4ed8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 4f90 x25: x25 x26: x26
STACK CFI 4f94 x27: x27 x28: x28
STACK CFI 4f98 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5020 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5098 x27: x27 x28: x28
STACK CFI 50ac x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 50d4 x27: x27 x28: x28
STACK CFI 5114 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 516c x27: x27 x28: x28
STACK CFI 51ac x25: x25 x26: x26
STACK CFI 51b0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 51b4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 51b8 x27: x27 x28: x28
STACK CFI 51c8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 51d8 x27: x27 x28: x28
STACK CFI 51e0 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 51f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5208 254 .cfa: sp 0 + .ra: x30
STACK CFI 520c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5214 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5220 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5234 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 538c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5390 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5460 220 .cfa: sp 0 + .ra: x30
STACK CFI 5464 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 546c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 547c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 54ac x23: .cfa -32 + ^
STACK CFI 55b0 x23: x23
STACK CFI 55b4 x23: .cfa -32 + ^
STACK CFI 55c0 x23: x23
STACK CFI 55e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 55ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 5608 x23: x23
STACK CFI 560c x23: .cfa -32 + ^
STACK CFI 5638 x23: x23
STACK CFI 5648 x23: .cfa -32 + ^
STACK CFI 5650 x23: x23
STACK CFI 5658 x23: .cfa -32 + ^
STACK CFI 5664 x23: x23
STACK CFI 5668 x23: .cfa -32 + ^
STACK CFI 5674 x23: x23
STACK CFI 567c x23: .cfa -32 + ^
STACK CFI INIT 5680 e8 .cfa: sp 0 + .ra: x30
STACK CFI 5684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 568c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5768 68 .cfa: sp 0 + .ra: x30
STACK CFI 576c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5774 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 57c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 57d0 8c .cfa: sp 0 + .ra: x30
STACK CFI 57d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 57e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 583c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5860 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5864 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5870 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 58c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5900 68 .cfa: sp 0 + .ra: x30
STACK CFI 5904 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5918 x19: .cfa -64 + ^
STACK CFI 5960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5964 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5968 18c .cfa: sp 0 + .ra: x30
STACK CFI 596c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5974 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5980 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5998 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 59bc x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ab8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5af8 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c00 48 .cfa: sp 0 + .ra: x30
STACK CFI 5c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c0c x19: .cfa -16 + ^
STACK CFI 5c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c48 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cc0 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 5cc4 .cfa: sp 1152 +
STACK CFI 5cc8 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 5cd0 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 5cdc x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 5ce8 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 5d04 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x27: .cfa -1072 + ^
STACK CFI 5ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5ec4 .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x29: .cfa -1152 + ^
STACK CFI INIT 6168 34 .cfa: sp 0 + .ra: x30
STACK CFI 616c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6174 x19: .cfa -16 + ^
STACK CFI 6198 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 61a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 61b8 x21: .cfa -16 + ^
STACK CFI 61d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 61dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6200 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 6204 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 620c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 621c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6230 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 62c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 62c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 63a0 564 .cfa: sp 0 + .ra: x30
STACK CFI 63a8 .cfa: sp 5312 +
STACK CFI 63b0 .ra: .cfa -5304 + ^ x29: .cfa -5312 + ^
STACK CFI 63bc x19: .cfa -5296 + ^ x20: .cfa -5288 + ^
STACK CFI 63d4 x21: .cfa -5280 + ^ x22: .cfa -5272 + ^
STACK CFI 63e4 x23: .cfa -5264 + ^ x24: .cfa -5256 + ^
STACK CFI 63f0 x25: .cfa -5248 + ^ x26: .cfa -5240 + ^
STACK CFI 640c x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI 6554 x27: x27 x28: x28
STACK CFI 6558 x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI 65ac x27: x27 x28: x28
STACK CFI 65e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 65ec .cfa: sp 5312 + .ra: .cfa -5304 + ^ x19: .cfa -5296 + ^ x20: .cfa -5288 + ^ x21: .cfa -5280 + ^ x22: .cfa -5272 + ^ x23: .cfa -5264 + ^ x24: .cfa -5256 + ^ x25: .cfa -5248 + ^ x26: .cfa -5240 + ^ x27: .cfa -5232 + ^ x28: .cfa -5224 + ^ x29: .cfa -5312 + ^
STACK CFI 6690 x27: x27 x28: x28
STACK CFI 6694 x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI 67d8 x27: x27 x28: x28
STACK CFI 67dc x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI 6880 x27: x27 x28: x28
STACK CFI 6884 x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI 68fc x27: x27 x28: x28
STACK CFI 6900 x27: .cfa -5232 + ^ x28: .cfa -5224 + ^
STACK CFI INIT 6908 f4 .cfa: sp 0 + .ra: x30
STACK CFI 690c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6914 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6924 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 696c x23: .cfa -16 + ^
STACK CFI 69dc x23: x23
STACK CFI 69e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 69e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 69f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6a00 26c .cfa: sp 0 + .ra: x30
STACK CFI 6a04 .cfa: sp 1168 +
STACK CFI 6a0c .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI 6a14 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 6a24 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 6a3c x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI 6ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ab4 .cfa: sp 1168 + .ra: .cfa -1160 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x29: .cfa -1168 + ^
STACK CFI 6ac4 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 6ae4 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 6b80 x25: x25 x26: x26
STACK CFI 6b84 x27: x27 x28: x28
STACK CFI 6b90 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 6c0c x27: x27 x28: x28
STACK CFI 6c20 x25: x25 x26: x26
STACK CFI 6c24 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 6c60 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6c64 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 6c68 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI INIT 6c70 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 6c74 .cfa: sp 368 +
STACK CFI 6c78 .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 6c84 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 6ca8 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6cac x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 6cc0 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 6e90 x19: x19 x20: x20
STACK CFI 6e94 x21: x21 x22: x22
STACK CFI 6e98 x25: x25 x26: x26
STACK CFI 6ebc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 6ec0 .cfa: sp 368 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x29: .cfa -352 + ^
STACK CFI 6ed8 x19: x19 x20: x20
STACK CFI 6edc x21: x21 x22: x22
STACK CFI 6ee0 x25: x25 x26: x26
STACK CFI 6ee4 x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 6f48 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 6f4c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 6f50 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6f54 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI INIT 6f58 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f70 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 6f74 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6f7c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6f8c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6fac x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6fb8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7128 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7228 3c .cfa: sp 0 + .ra: x30
STACK CFI 722c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7234 x19: .cfa -16 + ^
STACK CFI 7260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7268 48 .cfa: sp 0 + .ra: x30
STACK CFI 726c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7274 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 72ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 72b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 72b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 72bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 72fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7300 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7304 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 730c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7318 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7324 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7360 x21: x21 x22: x22
STACK CFI 7368 x23: x23 x24: x24
STACK CFI 7378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 737c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 738c x21: x21 x22: x22
STACK CFI 7390 x23: x23 x24: x24
STACK CFI 7394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7398 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 73b8 x21: x21 x22: x22
STACK CFI 73bc x23: x23 x24: x24
STACK CFI 73cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 73d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 73e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 743c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7440 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7458 94 .cfa: sp 0 + .ra: x30
STACK CFI 745c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7464 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7478 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 74e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 74e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 74f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 74f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74fc x19: .cfa -16 + ^
STACK CFI 7524 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7528 b8 .cfa: sp 0 + .ra: x30
STACK CFI 752c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7534 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7540 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 754c x23: .cfa -16 + ^
STACK CFI 75d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 75d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 75e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75e8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 75f0 .cfa: sp 4176 +
STACK CFI 75fc .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 7604 x23: .cfa -4128 + ^
STACK CFI 760c x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 763c x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 7698 x19: x19 x20: x20
STACK CFI 76c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 76c8 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI 76cc x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI INIT 76d0 184 .cfa: sp 0 + .ra: x30
STACK CFI 76d4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 76dc x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 76e8 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 76fc x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 774c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7750 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x29: .cfa -352 + ^
STACK CFI 776c x25: .cfa -288 + ^
STACK CFI 7794 x25: x25
STACK CFI 77b8 x25: .cfa -288 + ^
STACK CFI 7840 x25: x25
STACK CFI 7850 x25: .cfa -288 + ^
STACK CFI INIT 7858 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7868 48 .cfa: sp 0 + .ra: x30
STACK CFI 786c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7874 x19: .cfa -16 + ^
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 78d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78d8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 78f8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 78fc .cfa: sp 80 +
STACK CFI 7900 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7908 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7920 x21: .cfa -16 + ^
STACK CFI 7990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7994 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 79a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 79a8 158 .cfa: sp 0 + .ra: x30
STACK CFI 79ac .cfa: sp 1104 +
STACK CFI 79b0 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 79b8 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 79c0 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 7a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a30 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x29: .cfa -1104 + ^
STACK CFI 7a7c x23: .cfa -1056 + ^
STACK CFI 7ad4 x23: x23
STACK CFI 7adc x23: .cfa -1056 + ^
STACK CFI 7af8 x23: x23
STACK CFI 7afc x23: .cfa -1056 + ^
STACK CFI INIT 7b00 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7b04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b18 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7ba8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bc0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7c04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7c18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7c50 168 .cfa: sp 0 + .ra: x30
STACK CFI 7c54 .cfa: sp 1104 +
STACK CFI 7c58 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 7c64 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 7c6c x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 7c88 x23: .cfa -1056 + ^
STACK CFI 7d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7d10 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x29: .cfa -1104 + ^
STACK CFI INIT 7db8 7c .cfa: sp 0 + .ra: x30
STACK CFI 7dbc .cfa: sp 1072 +
STACK CFI 7dc4 .ra: .cfa -1064 + ^ x29: .cfa -1072 + ^
STACK CFI 7dcc x19: .cfa -1056 + ^ x20: .cfa -1048 + ^
STACK CFI 7e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e28 .cfa: sp 1072 + .ra: .cfa -1064 + ^ x19: .cfa -1056 + ^ x20: .cfa -1048 + ^ x29: .cfa -1072 + ^
STACK CFI INIT 7e38 354 .cfa: sp 0 + .ra: x30
STACK CFI 7e3c .cfa: sp 2496 +
STACK CFI 7e4c .ra: .cfa -2488 + ^ x29: .cfa -2496 + ^
STACK CFI 7e58 x19: .cfa -2480 + ^ x20: .cfa -2472 + ^
STACK CFI 7e7c x21: .cfa -2464 + ^ x22: .cfa -2456 + ^ x23: .cfa -2448 + ^ x24: .cfa -2440 + ^ x25: .cfa -2432 + ^ x26: .cfa -2424 + ^ x27: .cfa -2416 + ^ x28: .cfa -2408 + ^
STACK CFI 7fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7fd8 .cfa: sp 2496 + .ra: .cfa -2488 + ^ x19: .cfa -2480 + ^ x20: .cfa -2472 + ^ x21: .cfa -2464 + ^ x22: .cfa -2456 + ^ x23: .cfa -2448 + ^ x24: .cfa -2440 + ^ x25: .cfa -2432 + ^ x26: .cfa -2424 + ^ x27: .cfa -2416 + ^ x28: .cfa -2408 + ^ x29: .cfa -2496 + ^
STACK CFI INIT 8190 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8194 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 819c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 81ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 820c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8210 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8224 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8250 90 .cfa: sp 0 + .ra: x30
STACK CFI 8254 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 825c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 826c x21: .cfa -16 + ^
STACK CFI 82b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 82b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 82dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 82e0 3f8 .cfa: sp 0 + .ra: x30
STACK CFI 82e4 .cfa: sp 3248 +
STACK CFI 82e8 .ra: .cfa -3240 + ^ x29: .cfa -3248 + ^
STACK CFI 82f0 x21: .cfa -3216 + ^ x22: .cfa -3208 + ^
STACK CFI 82f8 x19: .cfa -3232 + ^ x20: .cfa -3224 + ^
STACK CFI 8378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 837c .cfa: sp 3248 + .ra: .cfa -3240 + ^ x19: .cfa -3232 + ^ x20: .cfa -3224 + ^ x21: .cfa -3216 + ^ x22: .cfa -3208 + ^ x29: .cfa -3248 + ^
STACK CFI 839c x23: .cfa -3200 + ^ x24: .cfa -3192 + ^
STACK CFI 841c x23: x23 x24: x24
STACK CFI 8480 x25: .cfa -3184 + ^ x26: .cfa -3176 + ^
STACK CFI 8490 x23: .cfa -3200 + ^ x24: .cfa -3192 + ^
STACK CFI 84ec x27: .cfa -3168 + ^ x28: .cfa -3160 + ^
STACK CFI 85e8 x27: x27 x28: x28
STACK CFI 8610 x23: x23 x24: x24
STACK CFI 8614 x25: x25 x26: x26
STACK CFI 8654 x23: .cfa -3200 + ^ x24: .cfa -3192 + ^ x25: .cfa -3184 + ^ x26: .cfa -3176 + ^ x27: .cfa -3168 + ^ x28: .cfa -3160 + ^
STACK CFI 86c0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 86c4 x23: x23 x24: x24
STACK CFI 86cc x23: .cfa -3200 + ^ x24: .cfa -3192 + ^
STACK CFI 86d0 x25: .cfa -3184 + ^ x26: .cfa -3176 + ^
STACK CFI 86d4 x27: .cfa -3168 + ^ x28: .cfa -3160 + ^
STACK CFI INIT 86d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 86e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8708 30 .cfa: sp 0 + .ra: x30
STACK CFI 870c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8714 x19: .cfa -16 + ^
STACK CFI 8734 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8738 1cc .cfa: sp 0 + .ra: x30
STACK CFI 873c .cfa: sp 1120 +
STACK CFI 8740 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 8748 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 8750 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 8780 x23: .cfa -1056 + ^ x24: .cfa -1048 + ^
STACK CFI 8800 x23: x23 x24: x24
STACK CFI 8834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8838 .cfa: sp 1120 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^ x29: .cfa -1104 + ^
STACK CFI 8854 x23: x23 x24: x24
STACK CFI 8864 x23: .cfa -1056 + ^ x24: .cfa -1048 + ^
STACK CFI 88fc x23: x23 x24: x24
STACK CFI 8900 x23: .cfa -1056 + ^ x24: .cfa -1048 + ^
STACK CFI INIT 8908 a8 .cfa: sp 0 + .ra: x30
STACK CFI 890c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8914 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8920 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8978 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 89ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 89b0 218 .cfa: sp 0 + .ra: x30
STACK CFI 89b4 .cfa: sp 720 +
STACK CFI 89c0 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 89c8 x27: .cfa -544 + ^
STACK CFI 89d8 x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 89f4 x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 8ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8bac .cfa: sp 720 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x29: .cfa -624 + ^
STACK CFI INIT 8bc8 74 .cfa: sp 0 + .ra: x30
STACK CFI 8bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8bdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8c40 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c60 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8c7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8cdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
