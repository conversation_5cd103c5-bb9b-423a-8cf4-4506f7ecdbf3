MODULE Linux arm64 229819F821C5F4BEF5DE229F40D2EB4D0 libmenuw.so.6
INFO CODE_ID F8199822C521BEF4F5DE229F40D2EB4D5AC110FD
PUBLIC 26b8 0 set_menu_fore
PUBLIC 2750 0 menu_fore
PUBLIC 2768 0 set_menu_back
PUBLIC 2800 0 menu_back
PUBLIC 2818 0 set_menu_grey
PUBLIC 28b0 0 menu_grey
PUBLIC 2938 0 pos_menu_cursor
PUBLIC 2cf8 0 menu_driver
PUBLIC 3498 0 set_menu_format
PUBLIC 3608 0 menu_format
PUBLIC 40c8 0 set_menu_init
PUBLIC 4100 0 menu_init
PUBLIC 4118 0 set_menu_term
PUBLIC 4150 0 menu_term
PUBLIC 4168 0 set_item_init
PUBLIC 41a0 0 item_init
PUBLIC 41b8 0 set_item_term
PUBLIC 41f0 0 item_term
PUBLIC 4208 0 set_current_item
PUBLIC 4330 0 current_item
PUBLIC 4350 0 item_index
PUBLIC 4370 0 item_name
PUBLIC 4388 0 item_description
PUBLIC 4468 0 new_item
PUBLIC 4580 0 free_item
PUBLIC 45e0 0 set_menu_mark
PUBLIC 4740 0 menu_mark
PUBLIC 4758 0 set_item_opts
PUBLIC 4840 0 item_opts_off
PUBLIC 4888 0 item_opts_on
PUBLIC 48a8 0 item_opts
PUBLIC 48c8 0 set_top_row
PUBLIC 49c0 0 top_row
PUBLIC 49e8 0 set_item_userptr
PUBLIC 4a20 0 item_userptr
PUBLIC 4a38 0 set_item_value
PUBLIC 4b28 0 item_value
PUBLIC 4b40 0 item_visible
PUBLIC 4b90 0 set_menu_items
PUBLIC 4c58 0 menu_items
PUBLIC 4c70 0 item_count
PUBLIC 4c88 0 new_menu_sp
PUBLIC 4d48 0 new_menu
PUBLIC 4d60 0 free_menu
PUBLIC 4de8 0 set_menu_opts
PUBLIC 4ec0 0 menu_opts_off
PUBLIC 4ee0 0 menu_opts_on
PUBLIC 4f00 0 menu_opts
PUBLIC 4f20 0 set_menu_pad
PUBLIC 4fb8 0 menu_pad
PUBLIC 4fd0 0 menu_pattern
PUBLIC 4ff8 0 set_menu_pattern
PUBLIC 5820 0 post_menu
PUBLIC 59f8 0 unpost_menu
PUBLIC 5b10 0 menu_request_name
PUBLIC 5b58 0 menu_request_by_name
PUBLIC 5c58 0 scale_menu
PUBLIC 5ce0 0 set_menu_spacing
PUBLIC 5dc0 0 menu_spacing
PUBLIC 5e18 0 set_menu_sub
PUBLIC 5ea0 0 menu_sub
PUBLIC 5ed8 0 set_menu_userptr
PUBLIC 5f10 0 menu_userptr
PUBLIC 5f28 0 set_menu_win
PUBLIC 5fb0 0 menu_win
STACK CFI INIT 25f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2628 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2668 48 .cfa: sp 0 + .ra: x30
STACK CFI 266c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2674 x19: .cfa -16 + ^
STACK CFI 26ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 26b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b8 94 .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26d0 x21: .cfa -16 + ^
STACK CFI 2718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 271c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2750 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2768 94 .cfa: sp 0 + .ra: x30
STACK CFI 276c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2774 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2780 x21: .cfa -16 + ^
STACK CFI 27c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2800 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2818 94 .cfa: sp 0 + .ra: x30
STACK CFI 281c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2830 x21: .cfa -16 + ^
STACK CFI 2878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 287c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 28b0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2938 184 .cfa: sp 0 + .ra: x30
STACK CFI 293c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2948 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 295c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2980 x23: .cfa -32 + ^
STACK CFI 29fc x23: x23
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 2a44 x23: x23
STACK CFI 2a48 x23: .cfa -32 + ^
STACK CFI 2a78 x23: x23
STACK CFI 2a7c x23: .cfa -32 + ^
STACK CFI 2ab0 x23: x23
STACK CFI 2ab8 x23: .cfa -32 + ^
STACK CFI INIT 2ac0 238 .cfa: sp 0 + .ra: x30
STACK CFI 2ac4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2acc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2adc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2b14 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2b20 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2b24 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2b48 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2b4c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2b50 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2b54 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2c1c x23: x23 x24: x24
STACK CFI 2c20 x25: x25 x26: x26
STACK CFI 2c2c x27: x27 x28: x28
STACK CFI 2c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c40 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2cb8 x23: x23 x24: x24
STACK CFI 2cbc x25: x25 x26: x26
STACK CFI 2cc0 x27: x27 x28: x28
STACK CFI 2cc8 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2ce4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 2cf8 7a0 .cfa: sp 0 + .ra: x30
STACK CFI 2cfc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2d04 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2d0c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2d28 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e00 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 2e24 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2e28 x25: x25 x26: x26
STACK CFI 2e60 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2e6c x25: x25 x26: x26
STACK CFI 30f4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3164 x25: x25 x26: x26
STACK CFI 32a0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 32bc x25: x25 x26: x26
STACK CFI 3320 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 340c x25: x25 x26: x26
STACK CFI 3414 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3464 x25: x25 x26: x26
STACK CFI 3468 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3494 x25: x25 x26: x26
STACK CFI INIT 3498 170 .cfa: sp 0 + .ra: x30
STACK CFI 349c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 34a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 34b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34c0 x23: .cfa -16 + ^
STACK CFI 357c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3580 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3608 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3650 64 .cfa: sp 0 + .ra: x30
STACK CFI 3658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3664 x19: .cfa -16 + ^
STACK CFI 36ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 36b8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 36bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 36c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3700 x21: x21 x22: x22
STACK CFI 3704 x23: x23 x24: x24
STACK CFI 3710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3714 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3774 x21: x21 x22: x22
STACK CFI 3778 x23: x23 x24: x24
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3780 180 .cfa: sp 0 + .ra: x30
STACK CFI 3784 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3790 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 37a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3804 x19: x19 x20: x20
STACK CFI 3810 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3814 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3850 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 38d4 x23: x23 x24: x24
STACK CFI 38e4 x19: x19 x20: x20
STACK CFI 38ec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 38f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 38f8 x23: x23 x24: x24
STACK CFI INIT 3900 f0 .cfa: sp 0 + .ra: x30
STACK CFI 3904 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 390c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3918 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 399c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 39a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39f0 3c8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3db8 dc .cfa: sp 0 + .ra: x30
STACK CFI 3dbc .cfa: sp 48 +
STACK CFI 3dc0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3dc8 x19: .cfa -16 + ^
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dec .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3e68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e6c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3e98 22c .cfa: sp 0 + .ra: x30
STACK CFI 3e9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ea4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3eb4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ec4 x23: .cfa -16 + ^
STACK CFI 3f70 x23: x23
STACK CFI 3f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 405c x23: x23
STACK CFI 4060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4064 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 409c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40c8 34 .cfa: sp 0 + .ra: x30
STACK CFI 40cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4100 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4118 34 .cfa: sp 0 + .ra: x30
STACK CFI 411c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4148 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4150 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4168 34 .cfa: sp 0 + .ra: x30
STACK CFI 416c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4198 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b8 34 .cfa: sp 0 + .ra: x30
STACK CFI 41bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4208 128 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4218 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4228 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4330 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4350 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4370 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4388 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43b0 x23: .cfa -16 + ^
STACK CFI 43c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 43e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 43f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4448 x21: x21 x22: x22
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 4454 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4460 x21: x21 x22: x22
STACK CFI INIT 4468 114 .cfa: sp 0 + .ra: x30
STACK CFI 446c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4474 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 447c x21: .cfa -16 + ^
STACK CFI 4524 x21: x21
STACK CFI 4528 x21: .cfa -16 + ^
STACK CFI 452c x21: x21
STACK CFI 4548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 454c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4558 x21: x21
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4568 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4574 x21: x21
STACK CFI INIT 4580 60 .cfa: sp 0 + .ra: x30
STACK CFI 4584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 458c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45e0 160 .cfa: sp 0 + .ra: x30
STACK CFI 45e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 45ec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 45f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 45fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4608 x25: .cfa -16 + ^
STACK CFI 46ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 46b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4740 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4758 e4 .cfa: sp 0 + .ra: x30
STACK CFI 475c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 476c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47a8 x19: x19 x20: x20
STACK CFI 47b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4824 x19: x19 x20: x20
STACK CFI 4838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4840 44 .cfa: sp 0 + .ra: x30
STACK CFI 4868 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4880 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4888 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48c8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 48cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48e0 x21: .cfa -16 + ^
STACK CFI 4960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 49c0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49e8 34 .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a20 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a38 ec .cfa: sp 0 + .ra: x30
STACK CFI 4a3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a84 x19: x19 x20: x20
STACK CFI 4a98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ab0 x19: x19 x20: x20
STACK CFI 4ac4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b04 x19: x19 x20: x20
STACK CFI 4b08 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b1c x19: x19 x20: x20
STACK CFI INIT 4b28 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b40 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b90 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ba0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bb8 x21: .cfa -16 + ^
STACK CFI 4bfc x19: x19 x20: x20
STACK CFI 4c04 x21: x21
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4c18 x19: x19 x20: x20
STACK CFI 4c20 x21: x21
STACK CFI 4c24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c28 x19: x19 x20: x20
STACK CFI 4c40 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4c4c x19: x19 x20: x20
STACK CFI 4c54 x21: x21
STACK CFI INIT 4c58 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c88 bc .cfa: sp 0 + .ra: x30
STACK CFI 4c8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ca0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4d48 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d60 84 .cfa: sp 0 + .ra: x30
STACK CFI 4d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4de8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4df4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e00 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ec0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ee0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f00 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f20 98 .cfa: sp 0 + .ra: x30
STACK CFI 4f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f38 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4fb8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fd0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ff8 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 4ffc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5004 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 500c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5020 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5080 x25: .cfa -48 + ^
STACK CFI 50e0 x25: x25
STACK CFI 510c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5110 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 5130 x25: .cfa -48 + ^
STACK CFI 5138 x25: x25
STACK CFI 51cc x25: .cfa -48 + ^
STACK CFI INIT 51d0 4ac .cfa: sp 0 + .ra: x30
STACK CFI 51d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 51dc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 51f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5454 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5570 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5680 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 5684 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 568c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 56ac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 581c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 5820 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 582c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5834 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 594c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5950 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 59f8 118 .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a0c x21: .cfa -16 + ^
STACK CFI 5ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b10 44 .cfa: sp 0 + .ra: x30
STACK CFI 5b34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b58 100 .cfa: sp 0 + .ra: x30
STACK CFI 5b5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5b64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5b80 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c0c x19: x19 x20: x20
STACK CFI 5c40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5c44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5c4c x19: x19 x20: x20
STACK CFI 5c54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 5c58 88 .cfa: sp 0 + .ra: x30
STACK CFI 5c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c70 x21: .cfa -16 + ^
STACK CFI 5cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5cc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ce0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5ce4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5cf8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d10 x23: .cfa -16 + ^
STACK CFI 5d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5dc0 54 .cfa: sp 0 + .ra: x30
STACK CFI 5dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5e18 84 .cfa: sp 0 + .ra: x30
STACK CFI 5e1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e30 x21: .cfa -16 + ^
STACK CFI 5e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ea0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ed8 34 .cfa: sp 0 + .ra: x30
STACK CFI 5edc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5f10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f28 84 .cfa: sp 0 + .ra: x30
STACK CFI 5f2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f40 x21: .cfa -16 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5fb0 30 .cfa: sp 0 + .ra: x30
