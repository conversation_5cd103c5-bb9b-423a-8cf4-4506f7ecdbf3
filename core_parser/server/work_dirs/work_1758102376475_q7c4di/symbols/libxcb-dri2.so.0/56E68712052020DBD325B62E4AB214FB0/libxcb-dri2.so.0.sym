MODULE Linux arm64 56E68712052020DBD325B62E4AB214FB0 libxcb-dri2.so.0
INFO CODE_ID 1287E6562005DB20D325B62E4AB214FB45BCA6A7
PUBLIC 18b8 0 xcb_dri2_dri2_buffer_next
PUBLIC 18d8 0 xcb_dri2_dri2_buffer_end
PUBLIC 18f8 0 xcb_dri2_attach_format_next
PUBLIC 1918 0 xcb_dri2_attach_format_end
PUBLIC 1930 0 xcb_dri2_query_version
PUBLIC 19a0 0 xcb_dri2_query_version_unchecked
PUBLIC 1a10 0 xcb_dri2_query_version_reply
PUBLIC 1a18 0 xcb_dri2_connect_sizeof
PUBLIC 1a30 0 xcb_dri2_connect
PUBLIC 1aa0 0 xcb_dri2_connect_unchecked
PUBLIC 1b10 0 xcb_dri2_connect_driver_name
PUBLIC 1b18 0 xcb_dri2_connect_driver_name_length
PUBLIC 1b20 0 xcb_dri2_connect_driver_name_end
PUBLIC 1b38 0 xcb_dri2_connect_alignment_pad
PUBLIC 1b50 0 xcb_dri2_connect_alignment_pad_length
PUBLIC 1b68 0 xcb_dri2_connect_alignment_pad_end
PUBLIC 1ba8 0 xcb_dri2_connect_device_name
PUBLIC 1bc0 0 xcb_dri2_connect_device_name_length
PUBLIC 1bc8 0 xcb_dri2_connect_device_name_end
PUBLIC 1bf8 0 xcb_dri2_connect_reply
PUBLIC 1c00 0 xcb_dri2_authenticate
PUBLIC 1c70 0 xcb_dri2_authenticate_unchecked
PUBLIC 1ce0 0 xcb_dri2_authenticate_reply
PUBLIC 1ce8 0 xcb_dri2_create_drawable_checked
PUBLIC 1d58 0 xcb_dri2_create_drawable
PUBLIC 1dc0 0 xcb_dri2_destroy_drawable_checked
PUBLIC 1e30 0 xcb_dri2_destroy_drawable
PUBLIC 1e98 0 xcb_dri2_get_buffers_sizeof
PUBLIC 1ea8 0 xcb_dri2_get_buffers
PUBLIC 1f28 0 xcb_dri2_get_buffers_unchecked
PUBLIC 1fa0 0 xcb_dri2_get_buffers_buffers
PUBLIC 1fa8 0 xcb_dri2_get_buffers_buffers_length
PUBLIC 1fb0 0 xcb_dri2_get_buffers_buffers_iterator
PUBLIC 1fd0 0 xcb_dri2_get_buffers_reply
PUBLIC 1fd8 0 xcb_dri2_copy_region
PUBLIC 2050 0 xcb_dri2_copy_region_unchecked
PUBLIC 20c8 0 xcb_dri2_copy_region_reply
PUBLIC 20d0 0 xcb_dri2_get_buffers_with_format_sizeof
PUBLIC 20e0 0 xcb_dri2_get_buffers_with_format
PUBLIC 2160 0 xcb_dri2_get_buffers_with_format_unchecked
PUBLIC 21d8 0 xcb_dri2_get_buffers_with_format_buffers
PUBLIC 21e0 0 xcb_dri2_get_buffers_with_format_buffers_length
PUBLIC 21e8 0 xcb_dri2_get_buffers_with_format_buffers_iterator
PUBLIC 2208 0 xcb_dri2_get_buffers_with_format_reply
PUBLIC 2210 0 xcb_dri2_swap_buffers
PUBLIC 2290 0 xcb_dri2_swap_buffers_unchecked
PUBLIC 2310 0 xcb_dri2_swap_buffers_reply
PUBLIC 2318 0 xcb_dri2_get_msc
PUBLIC 2388 0 xcb_dri2_get_msc_unchecked
PUBLIC 23f0 0 xcb_dri2_get_msc_reply
PUBLIC 23f8 0 xcb_dri2_wait_msc
PUBLIC 2478 0 xcb_dri2_wait_msc_unchecked
PUBLIC 24f8 0 xcb_dri2_wait_msc_reply
PUBLIC 2500 0 xcb_dri2_wait_sbc
PUBLIC 2578 0 xcb_dri2_wait_sbc_unchecked
PUBLIC 25f0 0 xcb_dri2_wait_sbc_reply
PUBLIC 25f8 0 xcb_dri2_swap_interval_checked
PUBLIC 2668 0 xcb_dri2_swap_interval
PUBLIC 26d8 0 xcb_dri2_get_param
PUBLIC 2748 0 xcb_dri2_get_param_unchecked
PUBLIC 27b8 0 xcb_dri2_get_param_reply
STACK CFI INIT 17f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1828 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1868 48 .cfa: sp 0 + .ra: x30
STACK CFI 186c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1874 x19: .cfa -16 + ^
STACK CFI 18ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18d8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1918 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1930 6c .cfa: sp 0 + .ra: x30
STACK CFI 1934 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1944 x19: .cfa -112 + ^
STACK CFI 1994 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1998 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 19a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 19a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 19b4 x19: .cfa -112 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a08 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a18 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a30 70 .cfa: sp 0 + .ra: x30
STACK CFI 1a34 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1a44 x19: .cfa -112 + ^
STACK CFI 1a98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1aa0 6c .cfa: sp 0 + .ra: x30
STACK CFI 1aa4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1ab4 x19: .cfa -112 + ^
STACK CFI 1b04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b08 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1b10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b38 14 .cfa: sp 0 + .ra: x30
STACK CFI 1b3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b68 3c .cfa: sp 0 + .ra: x30
STACK CFI 1b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b74 x19: .cfa -16 + ^
STACK CFI 1ba0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ba8 14 .cfa: sp 0 + .ra: x30
STACK CFI 1bac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bc8 30 .cfa: sp 0 + .ra: x30
STACK CFI 1bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bd4 x19: .cfa -16 + ^
STACK CFI 1bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1bf8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 70 .cfa: sp 0 + .ra: x30
STACK CFI 1c04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1c14 x19: .cfa -112 + ^
STACK CFI 1c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c6c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1c70 6c .cfa: sp 0 + .ra: x30
STACK CFI 1c74 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1c84 x19: .cfa -112 + ^
STACK CFI 1cd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cd8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1ce0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ce8 6c .cfa: sp 0 + .ra: x30
STACK CFI 1cec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1cfc x19: .cfa -96 + ^
STACK CFI 1d4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d50 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1d58 68 .cfa: sp 0 + .ra: x30
STACK CFI 1d5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1d6c x19: .cfa -96 + ^
STACK CFI 1db8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1dbc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1dc0 6c .cfa: sp 0 + .ra: x30
STACK CFI 1dc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1dd4 x19: .cfa -96 + ^
STACK CFI 1e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e28 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1e30 68 .cfa: sp 0 + .ra: x30
STACK CFI 1e34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1e44 x19: .cfa -96 + ^
STACK CFI 1e90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e94 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1e98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ea8 7c .cfa: sp 0 + .ra: x30
STACK CFI 1eac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1ebc x19: .cfa -144 + ^
STACK CFI 1f1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f20 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1f28 78 .cfa: sp 0 + .ra: x30
STACK CFI 1f2c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1f3c x19: .cfa -144 + ^
STACK CFI 1f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f9c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1fa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fa8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fb0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fd8 78 .cfa: sp 0 + .ra: x30
STACK CFI 1fdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1fec x19: .cfa -112 + ^
STACK CFI 2048 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 204c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2050 74 .cfa: sp 0 + .ra: x30
STACK CFI 2054 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2064 x19: .cfa -112 + ^
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 20c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 20c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e0 7c .cfa: sp 0 + .ra: x30
STACK CFI 20e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 20f4 x19: .cfa -144 + ^
STACK CFI 2154 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2158 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2160 78 .cfa: sp 0 + .ra: x30
STACK CFI 2164 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2174 x19: .cfa -144 + ^
STACK CFI 21d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 21d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 21d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2208 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2210 80 .cfa: sp 0 + .ra: x30
STACK CFI 2214 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2224 x19: .cfa -128 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 228c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2290 7c .cfa: sp 0 + .ra: x30
STACK CFI 2294 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 22a4 x19: .cfa -128 + ^
STACK CFI 2304 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2308 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2318 6c .cfa: sp 0 + .ra: x30
STACK CFI 231c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 232c x19: .cfa -96 + ^
STACK CFI 237c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2380 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2388 68 .cfa: sp 0 + .ra: x30
STACK CFI 238c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 239c x19: .cfa -96 + ^
STACK CFI 23e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 23ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 23f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23f8 80 .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 240c x19: .cfa -128 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2474 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2478 7c .cfa: sp 0 + .ra: x30
STACK CFI 247c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 248c x19: .cfa -128 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 24f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 24f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2500 78 .cfa: sp 0 + .ra: x30
STACK CFI 2504 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2514 x19: .cfa -112 + ^
STACK CFI 2570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2574 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2578 74 .cfa: sp 0 + .ra: x30
STACK CFI 257c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 258c x19: .cfa -112 + ^
STACK CFI 25e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25e8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 25f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f8 70 .cfa: sp 0 + .ra: x30
STACK CFI 25fc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 260c x19: .cfa -112 + ^
STACK CFI 2660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2664 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2668 6c .cfa: sp 0 + .ra: x30
STACK CFI 266c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 267c x19: .cfa -112 + ^
STACK CFI 26cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26d0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 26d8 70 .cfa: sp 0 + .ra: x30
STACK CFI 26dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 26ec x19: .cfa -112 + ^
STACK CFI 2740 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2744 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2748 6c .cfa: sp 0 + .ra: x30
STACK CFI 274c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 275c x19: .cfa -112 + ^
STACK CFI 27ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 27b0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 27b8 4 .cfa: sp 0 + .ra: x30
