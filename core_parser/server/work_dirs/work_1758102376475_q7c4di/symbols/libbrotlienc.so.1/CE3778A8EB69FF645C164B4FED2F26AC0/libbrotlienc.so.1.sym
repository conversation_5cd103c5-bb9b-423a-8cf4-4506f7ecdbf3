MODULE Linux arm64 CE3778A8EB69FF645C164B4FED2F26AC0 libbrotlienc.so.1
INFO CODE_ID A87837CE69EB64FF5C164B4FED2F26AC9DC29049
PUBLIC 2ace8 0 BrotliEncoderSetParameter
PUBLIC 2adc8 0 BrotliEncoderCreateInstance
PUBLIC 2aef8 0 BrotliEncoderDestroyInstance
PUBLIC 2afb8 0 BrotliEncoderMaxCompressedSize
PUBLIC 2afe0 0 BrotliEncoderCompressStream
PUBLIC 2bcf8 0 BrotliEncoderHasMoreOutput
PUBLIC 2bd08 0 BrotliEncoderIsFinished
PUBLIC 2bd38 0 BrotliEncoderCompress
PUBLIC 2c048 0 BrotliEncoderTakeOutput
PUBLIC 2c0b8 0 BrotliEncoderVersion
STACK CFI INIT bd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT c08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT c48 48 .cfa: sp 0 + .ra: x30
STACK CFI c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c54 x19: .cfa -16 + ^
STACK CFI c8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c98 eb8 .cfa: sp 0 + .ra: x30
STACK CFI c9c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI cac x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI cb8 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI cc4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI cd4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI cec x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1074 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 1b50 b78 .cfa: sp 0 + .ra: x30
STACK CFI 1b54 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1b64 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1b84 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1ba8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1be0 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1bec x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1dd0 x19: x19 x20: x20
STACK CFI 1dd8 x21: x21 x22: x22
STACK CFI 1e14 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1e18 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 25d4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 25dc x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI INIT 26c8 f1c .cfa: sp 0 + .ra: x30
STACK CFI 26cc .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 26f0 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 2714 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 2748 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 275c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 2764 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 2cbc x19: x19 x20: x20
STACK CFI 2cc4 x21: x21 x22: x22
STACK CFI 2ccc x25: x25 x26: x26
STACK CFI 2d00 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2d04 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 3460 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 3468 x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI INIT 35e8 12e4 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 35fc x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3644 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 3658 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 36d4 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 36e0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 3dd4 x23: x23 x24: x24
STACK CFI 3ddc x25: x25 x26: x26
STACK CFI 3e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 3e1c .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 4574 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 45bc x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI INIT 48d0 1384 .cfa: sp 0 + .ra: x30
STACK CFI 48d4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 48e0 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 48f4 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 492c x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 494c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 49d8 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 52dc x25: x25 x26: x26
STACK CFI 5320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5324 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 58d4 x25: x25 x26: x26
STACK CFI 5918 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI INIT 5c58 1328 .cfa: sp 0 + .ra: x30
STACK CFI 5c5c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5c7c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 5c8c x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 5cb0 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5cec x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 5cf8 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 676c x23: x23 x24: x24
STACK CFI 6774 x25: x25 x26: x26
STACK CFI 67b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 67b4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI 6f58 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6f60 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI INIT 6f80 1390 .cfa: sp 0 + .ra: x30
STACK CFI 6f84 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 6fa4 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 6fac x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 6fcc x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 7040 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 7054 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 7ab0 x19: x19 x20: x20
STACK CFI 7ab8 x25: x25 x26: x26
STACK CFI 7af4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7af8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI 82e4 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 82f0 x19: .cfa -304 + ^ x20: .cfa -296 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI INIT 8310 1414 .cfa: sp 0 + .ra: x30
STACK CFI 8314 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 8324 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 8330 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 8354 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 8410 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 8424 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 9270 x19: x19 x20: x20
STACK CFI 9278 x23: x23 x24: x24
STACK CFI 92b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 92b8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI 96f8 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 9704 x19: .cfa -304 + ^ x20: .cfa -296 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI INIT 9728 b64 .cfa: sp 0 + .ra: x30
STACK CFI 972c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 973c x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 977c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 979c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 97b4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 97bc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 99a8 x19: x19 x20: x20
STACK CFI 99b0 x21: x21 x22: x22
STACK CFI 99b8 x25: x25 x26: x26
STACK CFI 99ec .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 99f0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI a1c8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI a1d0 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI INIT a290 e60 .cfa: sp 0 + .ra: x30
STACK CFI a294 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI a2f4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI a310 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI a318 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI a334 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI a338 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a518 x19: x19 x20: x20
STACK CFI a520 x21: x21 x22: x22
STACK CFI a528 x23: x23 x24: x24
STACK CFI a52c x25: x25 x26: x26
STACK CFI a55c .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI a560 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI afe8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI aff0 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT b0f0 e90 .cfa: sp 0 + .ra: x30
STACK CFI b0f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI b110 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI b158 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI b18c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI b190 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI b194 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI b380 x19: x19 x20: x20
STACK CFI b388 x21: x21 x22: x22
STACK CFI b390 x23: x23 x24: x24
STACK CFI b394 x25: x25 x26: x26
STACK CFI b3c4 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI b3c8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI be8c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI be94 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT bf80 1764 .cfa: sp 0 + .ra: x30
STACK CFI bf84 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI bf9c x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bfec x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI c06c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI c080 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI c46c x19: x19 x20: x20
STACK CFI c474 x25: x25 x26: x26
STACK CFI c4b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI c4b4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI d284 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI d2c4 x19: .cfa -304 + ^ x20: .cfa -296 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI INIT d6e8 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d790 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT d820 178 .cfa: sp 0 + .ra: x30
STACK CFI d828 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d834 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d840 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d84c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d858 x25: .cfa -32 + ^
STACK CFI d864 v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI d920 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d924 .cfa: sp 96 + .ra: .cfa -88 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI d994 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT d998 154 .cfa: sp 0 + .ra: x30
STACK CFI d99c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d9ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d9c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d9d4 x23: .cfa -16 + ^
STACK CFI dae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT daf0 a4 .cfa: sp 0 + .ra: x30
STACK CFI daf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dafc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI db08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI db48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI db4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI db90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT db98 234 .cfa: sp 0 + .ra: x30
STACK CFI db9c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI dc2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI dc30 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT ddd0 858 .cfa: sp 0 + .ra: x30
STACK CFI ddd4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI dde8 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI ddf0 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI de1c x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI de30 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI e2bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e2c0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT e628 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT e660 348 .cfa: sp 0 + .ra: x30
STACK CFI e664 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e66c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e678 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e6a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e6ac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e6b0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e968 x19: x19 x20: x20
STACK CFI e96c x23: x23 x24: x24
STACK CFI e970 x27: x27 x28: x28
STACK CFI e990 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e994 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI e9a0 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI INIT e9a8 cc8 .cfa: sp 0 + .ra: x30
STACK CFI e9b0 .cfa: sp 6656 +
STACK CFI e9bc .ra: .cfa -6600 + ^ x29: .cfa -6608 + ^
STACK CFI e9c4 x21: .cfa -6576 + ^ x22: .cfa -6568 + ^
STACK CFI e9e0 x19: .cfa -6592 + ^ x20: .cfa -6584 + ^
STACK CFI ea20 x23: .cfa -6560 + ^ x24: .cfa -6552 + ^
STACK CFI ea40 x25: .cfa -6544 + ^ x26: .cfa -6536 + ^
STACK CFI ea4c x27: .cfa -6528 + ^ x28: .cfa -6520 + ^
STACK CFI ef08 x25: x25 x26: x26
STACK CFI ef0c x27: x27 x28: x28
STACK CFI ef6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ef70 .cfa: sp 6656 + .ra: .cfa -6600 + ^ x19: .cfa -6592 + ^ x20: .cfa -6584 + ^ x21: .cfa -6576 + ^ x22: .cfa -6568 + ^ x23: .cfa -6560 + ^ x24: .cfa -6552 + ^ x25: .cfa -6544 + ^ x26: .cfa -6536 + ^ x27: .cfa -6528 + ^ x28: .cfa -6520 + ^ x29: .cfa -6608 + ^
STACK CFI f604 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f654 x25: .cfa -6544 + ^ x26: .cfa -6536 + ^
STACK CFI f658 x27: .cfa -6528 + ^ x28: .cfa -6520 + ^
STACK CFI f664 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f668 x25: .cfa -6544 + ^ x26: .cfa -6536 + ^
STACK CFI f66c x27: .cfa -6528 + ^ x28: .cfa -6520 + ^
STACK CFI INIT f670 11c .cfa: sp 0 + .ra: x30
STACK CFI f674 .cfa: sp 144 +
STACK CFI f67c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI f688 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI f694 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI f69c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI f6a8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI f730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f734 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT f790 10d0 .cfa: sp 0 + .ra: x30
STACK CFI f798 .cfa: sp 10272 +
STACK CFI f7a0 .ra: .cfa -10216 + ^ x29: .cfa -10224 + ^
STACK CFI f7c4 x27: .cfa -10144 + ^ x28: .cfa -10136 + ^
STACK CFI f7fc x19: .cfa -10208 + ^ x20: .cfa -10200 + ^ x21: .cfa -10192 + ^ x22: .cfa -10184 + ^
STACK CFI f808 x23: .cfa -10176 + ^ x24: .cfa -10168 + ^ x25: .cfa -10160 + ^ x26: .cfa -10152 + ^
STACK CFI f81c v8: .cfa -10128 + ^
STACK CFI 10790 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10794 .cfa: sp 10272 + .ra: .cfa -10216 + ^ v8: .cfa -10128 + ^ x19: .cfa -10208 + ^ x20: .cfa -10200 + ^ x21: .cfa -10192 + ^ x22: .cfa -10184 + ^ x23: .cfa -10176 + ^ x24: .cfa -10168 + ^ x25: .cfa -10160 + ^ x26: .cfa -10152 + ^ x27: .cfa -10144 + ^ x28: .cfa -10136 + ^ x29: .cfa -10224 + ^
STACK CFI INIT 10860 41c .cfa: sp 0 + .ra: x30
STACK CFI 10864 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1086c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 10874 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 108d0 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 108dc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 108f8 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 108fc v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI 109a4 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 109d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 109d4 .cfa: sp 240 + .ra: .cfa -232 + ^ v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 10ad8 x23: x23 x24: x24
STACK CFI 10adc x25: x25 x26: x26
STACK CFI 10ae4 v10: v10 v11: v11
STACK CFI 10aec v8: v8 v9: v9
STACK CFI 10af0 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10b1c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10b34 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10b4c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10b94 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 10c30 x23: x23 x24: x24
STACK CFI 10c5c v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10c68 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10c6c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 10c70 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10c74 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 10c78 v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI INIT 10c80 41c .cfa: sp 0 + .ra: x30
STACK CFI 10c84 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 10c8c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 10c94 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 10cf0 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 10cfc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10d18 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 10d1c v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI 10dc4 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10df4 .cfa: sp 240 + .ra: .cfa -232 + ^ v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 10ef8 x23: x23 x24: x24
STACK CFI 10efc x25: x25 x26: x26
STACK CFI 10f04 v10: v10 v11: v11
STACK CFI 10f0c v8: v8 v9: v9
STACK CFI 10f10 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10f3c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10f54 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10f6c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10fb4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 11050 x23: x23 x24: x24
STACK CFI 1107c v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 11088 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1108c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 11090 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 11094 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 11098 v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI INIT 110a0 41c .cfa: sp 0 + .ra: x30
STACK CFI 110a4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 110ac x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 110b4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 11110 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1111c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 11138 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 1113c v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI 111e4 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 11210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11214 .cfa: sp 240 + .ra: .cfa -232 + ^ v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 11318 x23: x23 x24: x24
STACK CFI 1131c x25: x25 x26: x26
STACK CFI 11324 v10: v10 v11: v11
STACK CFI 1132c v8: v8 v9: v9
STACK CFI 11330 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1135c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 11374 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1138c v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 113d4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 11470 x23: x23 x24: x24
STACK CFI 1149c v10: .cfa -144 + ^ v11: .cfa -136 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 114a8 v10: v10 v11: v11 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 114ac x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 114b0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 114b4 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 114b8 v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI INIT 114c0 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11548 b80 .cfa: sp 0 + .ra: x30
STACK CFI 1154c .cfa: sp 2400 +
STACK CFI 11558 .ra: .cfa -2376 + ^ x29: .cfa -2384 + ^
STACK CFI 11584 x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^
STACK CFI 115a8 x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^
STACK CFI 116e0 x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 116fc v8: .cfa -2288 + ^ v9: .cfa -2280 + ^
STACK CFI 11c7c x27: x27 x28: x28
STACK CFI 11c80 v8: v8 v9: v9
STACK CFI 11ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 11cec .cfa: sp 2400 + .ra: .cfa -2376 + ^ v8: .cfa -2288 + ^ v9: .cfa -2280 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^ x29: .cfa -2384 + ^
STACK CFI 11f18 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 11ff4 v8: .cfa -2288 + ^ v9: .cfa -2280 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 12000 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 120c0 x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 120c4 v8: .cfa -2288 + ^ v9: .cfa -2280 + ^
STACK CFI INIT 120c8 b7c .cfa: sp 0 + .ra: x30
STACK CFI 120d0 .cfa: sp 4192 +
STACK CFI 120dc .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 12108 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 1212c x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x25: .cfa -4112 + ^ x26: .cfa -4104 + ^
STACK CFI 12258 x27: .cfa -4096 + ^ x28: .cfa -4088 + ^
STACK CFI 12274 v8: .cfa -4080 + ^ v9: .cfa -4072 + ^
STACK CFI 127f4 x27: x27 x28: x28
STACK CFI 127f8 v8: v8 v9: v9
STACK CFI 12864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12868 .cfa: sp 4192 + .ra: .cfa -4168 + ^ v8: .cfa -4080 + ^ v9: .cfa -4072 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x25: .cfa -4112 + ^ x26: .cfa -4104 + ^ x27: .cfa -4096 + ^ x28: .cfa -4088 + ^ x29: .cfa -4176 + ^
STACK CFI 12a90 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 12b6c v8: .cfa -4080 + ^ v9: .cfa -4072 + ^ x27: .cfa -4096 + ^ x28: .cfa -4088 + ^
STACK CFI 12b78 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 12c3c x27: .cfa -4096 + ^ x28: .cfa -4088 + ^
STACK CFI 12c40 v8: .cfa -4080 + ^ v9: .cfa -4072 + ^
STACK CFI INIT 12c48 ba8 .cfa: sp 0 + .ra: x30
STACK CFI 12c4c .cfa: sp 3552 +
STACK CFI 12c58 .ra: .cfa -3528 + ^ x29: .cfa -3536 + ^
STACK CFI 12c84 x19: .cfa -3520 + ^ x20: .cfa -3512 + ^ x21: .cfa -3504 + ^ x22: .cfa -3496 + ^
STACK CFI 12ca8 x23: .cfa -3488 + ^ x24: .cfa -3480 + ^ x25: .cfa -3472 + ^ x26: .cfa -3464 + ^
STACK CFI 12ddc x27: .cfa -3456 + ^ x28: .cfa -3448 + ^
STACK CFI 12df8 v8: .cfa -3440 + ^ v9: .cfa -3432 + ^
STACK CFI 13390 x27: x27 x28: x28
STACK CFI 13394 v8: v8 v9: v9
STACK CFI 133fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13400 .cfa: sp 3552 + .ra: .cfa -3528 + ^ v8: .cfa -3440 + ^ v9: .cfa -3432 + ^ x19: .cfa -3520 + ^ x20: .cfa -3512 + ^ x21: .cfa -3504 + ^ x22: .cfa -3496 + ^ x23: .cfa -3488 + ^ x24: .cfa -3480 + ^ x25: .cfa -3472 + ^ x26: .cfa -3464 + ^ x27: .cfa -3456 + ^ x28: .cfa -3448 + ^ x29: .cfa -3536 + ^
STACK CFI 13638 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 13718 v8: .cfa -3440 + ^ v9: .cfa -3432 + ^ x27: .cfa -3456 + ^ x28: .cfa -3448 + ^
STACK CFI 13724 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 137e8 x27: .cfa -3456 + ^ x28: .cfa -3448 + ^
STACK CFI 137ec v8: .cfa -3440 + ^ v9: .cfa -3432 + ^
STACK CFI INIT 137f0 188 .cfa: sp 0 + .ra: x30
STACK CFI 137f4 .cfa: sp 2304 +
STACK CFI 13800 .ra: .cfa -2296 + ^ x29: .cfa -2304 + ^
STACK CFI 13808 x25: .cfa -2240 + ^ x26: .cfa -2232 + ^
STACK CFI 13814 x19: .cfa -2288 + ^ x20: .cfa -2280 + ^
STACK CFI 13824 x23: .cfa -2256 + ^ x24: .cfa -2248 + ^
STACK CFI 13854 x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 13864 x27: .cfa -2224 + ^
STACK CFI 13920 x21: x21 x22: x22
STACK CFI 13924 x27: x27
STACK CFI 1394c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13950 .cfa: sp 2304 + .ra: .cfa -2296 + ^ x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^ x25: .cfa -2240 + ^ x26: .cfa -2232 + ^ x27: .cfa -2224 + ^ x29: .cfa -2304 + ^
STACK CFI 1396c x21: x21 x22: x22 x27: x27
STACK CFI 13970 x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 13974 x27: .cfa -2224 + ^
STACK CFI INIT 13978 184 .cfa: sp 0 + .ra: x30
STACK CFI 1397c .cfa: sp 2944 +
STACK CFI 13988 .ra: .cfa -2936 + ^ x29: .cfa -2944 + ^
STACK CFI 13990 x25: .cfa -2880 + ^ x26: .cfa -2872 + ^
STACK CFI 1399c x19: .cfa -2928 + ^ x20: .cfa -2920 + ^
STACK CFI 139ac x23: .cfa -2896 + ^ x24: .cfa -2888 + ^
STACK CFI 139dc x21: .cfa -2912 + ^ x22: .cfa -2904 + ^
STACK CFI 139ec x27: .cfa -2864 + ^
STACK CFI 13aa4 x21: x21 x22: x22
STACK CFI 13aa8 x27: x27
STACK CFI 13ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13ad4 .cfa: sp 2944 + .ra: .cfa -2936 + ^ x19: .cfa -2928 + ^ x20: .cfa -2920 + ^ x21: .cfa -2912 + ^ x22: .cfa -2904 + ^ x23: .cfa -2896 + ^ x24: .cfa -2888 + ^ x25: .cfa -2880 + ^ x26: .cfa -2872 + ^ x27: .cfa -2864 + ^ x29: .cfa -2944 + ^
STACK CFI 13af0 x21: x21 x22: x22 x27: x27
STACK CFI 13af4 x21: .cfa -2912 + ^ x22: .cfa -2904 + ^
STACK CFI 13af8 x27: .cfa -2864 + ^
STACK CFI INIT 13b00 188 .cfa: sp 0 + .ra: x30
STACK CFI 13b04 .cfa: sp 1152 +
STACK CFI 13b14 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 13b20 x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI 13b2c x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 13b38 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 13b68 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 13b74 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 13c30 x21: x21 x22: x22
STACK CFI 13c34 x23: x23 x24: x24
STACK CFI 13c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13c60 .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^ x29: .cfa -1152 + ^
STACK CFI 13c7c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 13c80 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 13c84 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI INIT 13c88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13c98 3c .cfa: sp 0 + .ra: x30
STACK CFI 13c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13cd8 1644 .cfa: sp 0 + .ra: x30
STACK CFI 13cdc .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 13ce4 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 13cfc x19: .cfa -256 + ^ x20: .cfa -248 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 13d24 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 13d34 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 13d3c v8: .cfa -176 + ^ v9: .cfa -168 + ^
STACK CFI 13d40 v10: .cfa -160 + ^ v11: .cfa -152 + ^
STACK CFI 13d44 v12: .cfa -144 + ^ v13: .cfa -136 + ^
STACK CFI 14fbc x21: x21 x22: x22
STACK CFI 14fc4 x25: x25 x26: x26
STACK CFI 14fcc v8: v8 v9: v9
STACK CFI 14fd0 v10: v10 v11: v11
STACK CFI 14fd4 v12: v12 v13: v13
STACK CFI 14fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 14fdc .cfa: sp 272 + .ra: .cfa -264 + ^ v10: .cfa -160 + ^ v11: .cfa -152 + ^ v12: .cfa -144 + ^ v13: .cfa -136 + ^ v8: .cfa -176 + ^ v9: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 15014 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 15064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 15068 .cfa: sp 272 + .ra: .cfa -264 + ^ v10: .cfa -160 + ^ v11: .cfa -152 + ^ v12: .cfa -144 + ^ v13: .cfa -136 + ^ v8: .cfa -176 + ^ v9: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 1506c x21: x21 x22: x22
STACK CFI 15070 x25: x25 x26: x26
STACK CFI 15074 v8: v8 v9: v9
STACK CFI 15078 v10: v10 v11: v11
STACK CFI 1507c v12: v12 v13: v13
STACK CFI 15080 v10: .cfa -160 + ^ v11: .cfa -152 + ^ v12: .cfa -144 + ^ v13: .cfa -136 + ^ v8: .cfa -176 + ^ v9: .cfa -168 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI INIT 15320 1c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154e0 1c8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 156a8 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15788 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 1578c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15794 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 157b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 157b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 157c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 157dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15968 x19: x19 x20: x20
STACK CFI 1596c x21: x21 x22: x22
STACK CFI 15970 x23: x23 x24: x24
STACK CFI 15974 x27: x27 x28: x28
STACK CFI 1597c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 15980 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 15a58 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ae8 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15bb8 30c .cfa: sp 0 + .ra: x30
STACK CFI 15bbc .cfa: sp 1664 +
STACK CFI 15bc0 .ra: .cfa -1656 + ^ x29: .cfa -1664 + ^
STACK CFI 15bc8 x19: .cfa -1648 + ^ x20: .cfa -1640 + ^
STACK CFI 15bd4 x23: .cfa -1616 + ^ x24: .cfa -1608 + ^
STACK CFI 15be0 x25: .cfa -1600 + ^ x26: .cfa -1592 + ^
STACK CFI 15bf8 x21: .cfa -1632 + ^ x22: .cfa -1624 + ^
STACK CFI 15c0c x27: .cfa -1584 + ^ x28: .cfa -1576 + ^
STACK CFI 15eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15eb0 .cfa: sp 1664 + .ra: .cfa -1656 + ^ x19: .cfa -1648 + ^ x20: .cfa -1640 + ^ x21: .cfa -1632 + ^ x22: .cfa -1624 + ^ x23: .cfa -1616 + ^ x24: .cfa -1608 + ^ x25: .cfa -1600 + ^ x26: .cfa -1592 + ^ x27: .cfa -1584 + ^ x28: .cfa -1576 + ^ x29: .cfa -1664 + ^
STACK CFI INIT 15ec8 3d4 .cfa: sp 0 + .ra: x30
STACK CFI 15ecc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 15edc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 15eec x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 15f00 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 15f0c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 161c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 161c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 16248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1624c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 162a0 30c .cfa: sp 0 + .ra: x30
STACK CFI 162a4 .cfa: sp 1264 +
STACK CFI 162a8 .ra: .cfa -1256 + ^ x29: .cfa -1264 + ^
STACK CFI 162b0 x19: .cfa -1248 + ^ x20: .cfa -1240 + ^
STACK CFI 162bc x25: .cfa -1200 + ^ x26: .cfa -1192 + ^
STACK CFI 162cc x21: .cfa -1232 + ^ x22: .cfa -1224 + ^
STACK CFI 162d4 x23: .cfa -1216 + ^ x24: .cfa -1208 + ^
STACK CFI 162e8 x27: .cfa -1184 + ^ x28: .cfa -1176 + ^
STACK CFI 1643c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16440 .cfa: sp 1264 + .ra: .cfa -1256 + ^ x19: .cfa -1248 + ^ x20: .cfa -1240 + ^ x21: .cfa -1232 + ^ x22: .cfa -1224 + ^ x23: .cfa -1216 + ^ x24: .cfa -1208 + ^ x25: .cfa -1200 + ^ x26: .cfa -1192 + ^ x27: .cfa -1184 + ^ x28: .cfa -1176 + ^ x29: .cfa -1264 + ^
STACK CFI INIT 165b0 214 .cfa: sp 0 + .ra: x30
STACK CFI 165b4 .cfa: sp 2032 +
STACK CFI 165bc .ra: .cfa -2024 + ^ x29: .cfa -2032 + ^
STACK CFI 165c4 x19: .cfa -2016 + ^ x20: .cfa -2008 + ^
STACK CFI 165d0 x23: .cfa -1984 + ^ x24: .cfa -1976 + ^
STACK CFI 165d8 x27: .cfa -1952 + ^ x28: .cfa -1944 + ^
STACK CFI 165e8 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 16608 x25: .cfa -1968 + ^ x26: .cfa -1960 + ^
STACK CFI 167b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 167b8 .cfa: sp 2032 + .ra: .cfa -2024 + ^ x19: .cfa -2016 + ^ x20: .cfa -2008 + ^ x21: .cfa -2000 + ^ x22: .cfa -1992 + ^ x23: .cfa -1984 + ^ x24: .cfa -1976 + ^ x25: .cfa -1968 + ^ x26: .cfa -1960 + ^ x27: .cfa -1952 + ^ x28: .cfa -1944 + ^ x29: .cfa -2032 + ^
STACK CFI INIT 167c8 50c .cfa: sp 0 + .ra: x30
STACK CFI 167cc .cfa: sp 2032 +
STACK CFI 167d4 .ra: .cfa -2024 + ^ x29: .cfa -2032 + ^
STACK CFI 167dc x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 16800 x19: .cfa -2016 + ^ x20: .cfa -2008 + ^
STACK CFI 1680c x23: .cfa -1984 + ^ x24: .cfa -1976 + ^
STACK CFI 16814 x25: .cfa -1968 + ^ x26: .cfa -1960 + ^
STACK CFI 16824 x27: .cfa -1952 + ^ x28: .cfa -1944 + ^
STACK CFI 169b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 169b8 .cfa: sp 2032 + .ra: .cfa -2024 + ^ x19: .cfa -2016 + ^ x20: .cfa -2008 + ^ x21: .cfa -2000 + ^ x22: .cfa -1992 + ^ x23: .cfa -1984 + ^ x24: .cfa -1976 + ^ x25: .cfa -1968 + ^ x26: .cfa -1960 + ^ x27: .cfa -1952 + ^ x28: .cfa -1944 + ^ x29: .cfa -2032 + ^
STACK CFI INIT 16cd8 850 .cfa: sp 0 + .ra: x30
STACK CFI 16ce4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 16cf8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 16d04 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 16d18 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 16d38 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 16d40 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 172d4 x19: x19 x20: x20
STACK CFI 172d8 x25: x25 x26: x26
STACK CFI 172dc x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 172e0 x19: x19 x20: x20
STACK CFI 172e4 x25: x25 x26: x26
STACK CFI 17374 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 17378 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 17478 x19: x19 x20: x20
STACK CFI 1747c x25: x25 x26: x26
STACK CFI 174b4 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 174bc x19: x19 x20: x20
STACK CFI 174c4 x25: x25 x26: x26
STACK CFI 174d4 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 174ec x19: x19 x20: x20
STACK CFI 174f8 x25: x25 x26: x26
STACK CFI 1750c x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 1751c x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 17520 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 17524 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 17528 a90 .cfa: sp 0 + .ra: x30
STACK CFI 1752c .cfa: sp 3056 +
STACK CFI 17538 .ra: .cfa -3048 + ^ x29: .cfa -3056 + ^
STACK CFI 17570 x19: .cfa -3040 + ^ x20: .cfa -3032 + ^
STACK CFI 17584 x21: .cfa -3024 + ^ x22: .cfa -3016 + ^
STACK CFI 17590 x23: .cfa -3008 + ^ x24: .cfa -3000 + ^
STACK CFI 1759c x25: .cfa -2992 + ^ x26: .cfa -2984 + ^
STACK CFI 175a4 x27: .cfa -2976 + ^ x28: .cfa -2968 + ^
STACK CFI 17dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17dcc .cfa: sp 3056 + .ra: .cfa -3048 + ^ x19: .cfa -3040 + ^ x20: .cfa -3032 + ^ x21: .cfa -3024 + ^ x22: .cfa -3016 + ^ x23: .cfa -3008 + ^ x24: .cfa -3000 + ^ x25: .cfa -2992 + ^ x26: .cfa -2984 + ^ x27: .cfa -2976 + ^ x28: .cfa -2968 + ^ x29: .cfa -3056 + ^
STACK CFI INIT 17fb8 288 .cfa: sp 0 + .ra: x30
STACK CFI 17fc0 .cfa: sp 9600 +
STACK CFI 17fc8 .ra: .cfa -9544 + ^ x29: .cfa -9552 + ^
STACK CFI 17fd8 x19: .cfa -9536 + ^ x20: .cfa -9528 + ^
STACK CFI 17fe0 x23: .cfa -9504 + ^ x24: .cfa -9496 + ^
STACK CFI 17ff8 x27: .cfa -9472 + ^ x28: .cfa -9464 + ^
STACK CFI 18014 x21: .cfa -9520 + ^ x22: .cfa -9512 + ^
STACK CFI 18020 x25: .cfa -9488 + ^ x26: .cfa -9480 + ^
STACK CFI 1802c v8: .cfa -9456 + ^
STACK CFI 18238 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1823c .cfa: sp 9600 + .ra: .cfa -9544 + ^ v8: .cfa -9456 + ^ x19: .cfa -9536 + ^ x20: .cfa -9528 + ^ x21: .cfa -9520 + ^ x22: .cfa -9512 + ^ x23: .cfa -9504 + ^ x24: .cfa -9496 + ^ x25: .cfa -9488 + ^ x26: .cfa -9480 + ^ x27: .cfa -9472 + ^ x28: .cfa -9464 + ^ x29: .cfa -9552 + ^
STACK CFI INIT 18240 418 .cfa: sp 0 + .ra: x30
STACK CFI 18248 .cfa: sp 9584 +
STACK CFI 18250 .ra: .cfa -9528 + ^ x29: .cfa -9536 + ^
STACK CFI 1825c x19: .cfa -9520 + ^ x20: .cfa -9512 + ^
STACK CFI 18264 x27: .cfa -9456 + ^ x28: .cfa -9448 + ^
STACK CFI 18270 x21: .cfa -9504 + ^ x22: .cfa -9496 + ^
STACK CFI 18290 x23: .cfa -9488 + ^ x24: .cfa -9480 + ^
STACK CFI 1829c x25: .cfa -9472 + ^ x26: .cfa -9464 + ^
STACK CFI 18484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 18488 .cfa: sp 9584 + .ra: .cfa -9528 + ^ x19: .cfa -9520 + ^ x20: .cfa -9512 + ^ x21: .cfa -9504 + ^ x22: .cfa -9496 + ^ x23: .cfa -9488 + ^ x24: .cfa -9480 + ^ x25: .cfa -9472 + ^ x26: .cfa -9464 + ^ x27: .cfa -9456 + ^ x28: .cfa -9448 + ^ x29: .cfa -9536 + ^
STACK CFI 184bc v8: .cfa -9440 + ^
STACK CFI 18644 v8: v8
STACK CFI 18654 v8: .cfa -9440 + ^
STACK CFI INIT 18658 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 1865c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18668 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18678 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18680 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1868c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 18774 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 187c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 187cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18810 2bc .cfa: sp 0 + .ra: x30
STACK CFI 18814 .cfa: sp 1168 +
STACK CFI 1881c .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI 18824 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 18840 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 1884c x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI 18868 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 18874 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 18880 v8: .cfa -1072 + ^ v9: .cfa -1064 + ^
STACK CFI 189e0 x21: x21 x22: x22
STACK CFI 189e4 x23: x23 x24: x24
STACK CFI 189e8 x25: x25 x26: x26
STACK CFI 189ec x27: x27 x28: x28
STACK CFI 189f0 v8: v8 v9: v9
STACK CFI 18a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18a14 .cfa: sp 1168 + .ra: .cfa -1160 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^ x29: .cfa -1168 + ^
STACK CFI 18a7c x21: x21 x22: x22
STACK CFI 18a80 x23: x23 x24: x24
STACK CFI 18a84 x25: x25 x26: x26
STACK CFI 18a88 x27: x27 x28: x28
STACK CFI 18a8c v8: v8 v9: v9
STACK CFI 18a90 v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 18ab4 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18ab8 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 18abc x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI 18ac0 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 18ac4 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 18ac8 v8: .cfa -1072 + ^ v9: .cfa -1064 + ^
STACK CFI INIT 18ad0 34c .cfa: sp 0 + .ra: x30
STACK CFI 18ad4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 18ae4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 18b00 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 18b24 v8: .cfa -128 + ^ v9: .cfa -120 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 18c14 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 18c18 .cfa: sp 224 + .ra: .cfa -216 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 18e20 c4 .cfa: sp 0 + .ra: x30
STACK CFI 18e24 .cfa: sp 1104 +
STACK CFI 18e2c .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 18e34 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 18e50 x21: .cfa -1072 + ^
STACK CFI 18edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18ee0 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x29: .cfa -1104 + ^
STACK CFI INIT 18ee8 188 .cfa: sp 0 + .ra: x30
STACK CFI 18eec .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 18ef4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 18f00 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 18f08 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 18f1c v8: .cfa -48 + ^
STACK CFI 18f30 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 18fa4 x27: x27 x28: x28
STACK CFI 1905c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19060 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 19068 x27: x27 x28: x28
STACK CFI INIT 19070 188 .cfa: sp 0 + .ra: x30
STACK CFI 19074 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1907c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 19084 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 190b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 190b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 190bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 190cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 190d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 191d8 x19: x19 x20: x20
STACK CFI 191dc x25: x25 x26: x26
STACK CFI 191e0 x27: x27 x28: x28
STACK CFI 191f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 191f8 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 191fc .cfa: sp 160 +
STACK CFI 19200 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1920c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1921c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 19224 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 192fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19300 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 19498 2bc .cfa: sp 0 + .ra: x30
STACK CFI 1949c .cfa: sp 2960 +
STACK CFI 194a4 .ra: .cfa -2952 + ^ x29: .cfa -2960 + ^
STACK CFI 194ac x19: .cfa -2944 + ^ x20: .cfa -2936 + ^
STACK CFI 194c8 x21: .cfa -2928 + ^ x22: .cfa -2920 + ^
STACK CFI 194d4 x23: .cfa -2912 + ^ x24: .cfa -2904 + ^
STACK CFI 194f0 x25: .cfa -2896 + ^ x26: .cfa -2888 + ^
STACK CFI 194fc x27: .cfa -2880 + ^ x28: .cfa -2872 + ^
STACK CFI 19508 v8: .cfa -2864 + ^ v9: .cfa -2856 + ^
STACK CFI 19668 x21: x21 x22: x22
STACK CFI 1966c x23: x23 x24: x24
STACK CFI 19670 x25: x25 x26: x26
STACK CFI 19674 x27: x27 x28: x28
STACK CFI 19678 v8: v8 v9: v9
STACK CFI 19698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1969c .cfa: sp 2960 + .ra: .cfa -2952 + ^ v8: .cfa -2864 + ^ v9: .cfa -2856 + ^ x19: .cfa -2944 + ^ x20: .cfa -2936 + ^ x21: .cfa -2928 + ^ x22: .cfa -2920 + ^ x23: .cfa -2912 + ^ x24: .cfa -2904 + ^ x25: .cfa -2896 + ^ x26: .cfa -2888 + ^ x27: .cfa -2880 + ^ x28: .cfa -2872 + ^ x29: .cfa -2960 + ^
STACK CFI 19704 x21: x21 x22: x22
STACK CFI 19708 x23: x23 x24: x24
STACK CFI 1970c x25: x25 x26: x26
STACK CFI 19710 x27: x27 x28: x28
STACK CFI 19714 v8: v8 v9: v9
STACK CFI 19718 v8: .cfa -2864 + ^ v9: .cfa -2856 + ^ x21: .cfa -2928 + ^ x22: .cfa -2920 + ^ x23: .cfa -2912 + ^ x24: .cfa -2904 + ^ x25: .cfa -2896 + ^ x26: .cfa -2888 + ^ x27: .cfa -2880 + ^ x28: .cfa -2872 + ^
STACK CFI 1973c v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 19740 x21: .cfa -2928 + ^ x22: .cfa -2920 + ^
STACK CFI 19744 x23: .cfa -2912 + ^ x24: .cfa -2904 + ^
STACK CFI 19748 x25: .cfa -2896 + ^ x26: .cfa -2888 + ^
STACK CFI 1974c x27: .cfa -2880 + ^ x28: .cfa -2872 + ^
STACK CFI 19750 v8: .cfa -2864 + ^ v9: .cfa -2856 + ^
STACK CFI INIT 19758 34c .cfa: sp 0 + .ra: x30
STACK CFI 1975c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1976c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 19788 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 197ac v8: .cfa -128 + ^ v9: .cfa -120 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 1989c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 198a0 .cfa: sp 224 + .ra: .cfa -216 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 19aa8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 19aac .cfa: sp 2896 +
STACK CFI 19ab4 .ra: .cfa -2888 + ^ x29: .cfa -2896 + ^
STACK CFI 19abc x19: .cfa -2880 + ^ x20: .cfa -2872 + ^
STACK CFI 19ad8 x21: .cfa -2864 + ^
STACK CFI 19b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 19b68 .cfa: sp 2896 + .ra: .cfa -2888 + ^ x19: .cfa -2880 + ^ x20: .cfa -2872 + ^ x21: .cfa -2864 + ^ x29: .cfa -2896 + ^
STACK CFI INIT 19b70 188 .cfa: sp 0 + .ra: x30
STACK CFI 19b74 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 19b7c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 19b88 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 19b90 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 19ba4 v8: .cfa -48 + ^
STACK CFI 19bb8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 19c2c x27: x27 x28: x28
STACK CFI 19ce4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19ce8 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 19cf0 x27: x27 x28: x28
STACK CFI INIT 19cf8 188 .cfa: sp 0 + .ra: x30
STACK CFI 19cfc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19d04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19d0c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 19d3c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19d40 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 19d44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 19d54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 19d5c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19e60 x19: x19 x20: x20
STACK CFI 19e64 x21: x21 x22: x22
STACK CFI 19e68 x27: x27 x28: x28
STACK CFI 19e7c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 19e80 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 19e84 .cfa: sp 160 +
STACK CFI 19e88 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 19e94 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 19ea4 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 19eac x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 19f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19f88 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1a120 2bc .cfa: sp 0 + .ra: x30
STACK CFI 1a124 .cfa: sp 2320 +
STACK CFI 1a12c .ra: .cfa -2312 + ^ x29: .cfa -2320 + ^
STACK CFI 1a134 x19: .cfa -2304 + ^ x20: .cfa -2296 + ^
STACK CFI 1a150 x21: .cfa -2288 + ^ x22: .cfa -2280 + ^
STACK CFI 1a15c x23: .cfa -2272 + ^ x24: .cfa -2264 + ^
STACK CFI 1a178 x25: .cfa -2256 + ^ x26: .cfa -2248 + ^
STACK CFI 1a184 x27: .cfa -2240 + ^ x28: .cfa -2232 + ^
STACK CFI 1a190 v8: .cfa -2224 + ^ v9: .cfa -2216 + ^
STACK CFI 1a2f0 x21: x21 x22: x22
STACK CFI 1a2f4 x23: x23 x24: x24
STACK CFI 1a2f8 x25: x25 x26: x26
STACK CFI 1a2fc x27: x27 x28: x28
STACK CFI 1a300 v8: v8 v9: v9
STACK CFI 1a320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a324 .cfa: sp 2320 + .ra: .cfa -2312 + ^ v8: .cfa -2224 + ^ v9: .cfa -2216 + ^ x19: .cfa -2304 + ^ x20: .cfa -2296 + ^ x21: .cfa -2288 + ^ x22: .cfa -2280 + ^ x23: .cfa -2272 + ^ x24: .cfa -2264 + ^ x25: .cfa -2256 + ^ x26: .cfa -2248 + ^ x27: .cfa -2240 + ^ x28: .cfa -2232 + ^ x29: .cfa -2320 + ^
STACK CFI 1a38c x21: x21 x22: x22
STACK CFI 1a390 x23: x23 x24: x24
STACK CFI 1a394 x25: x25 x26: x26
STACK CFI 1a398 x27: x27 x28: x28
STACK CFI 1a39c v8: v8 v9: v9
STACK CFI 1a3a0 v8: .cfa -2224 + ^ v9: .cfa -2216 + ^ x21: .cfa -2288 + ^ x22: .cfa -2280 + ^ x23: .cfa -2272 + ^ x24: .cfa -2264 + ^ x25: .cfa -2256 + ^ x26: .cfa -2248 + ^ x27: .cfa -2240 + ^ x28: .cfa -2232 + ^
STACK CFI 1a3c4 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a3c8 x21: .cfa -2288 + ^ x22: .cfa -2280 + ^
STACK CFI 1a3cc x23: .cfa -2272 + ^ x24: .cfa -2264 + ^
STACK CFI 1a3d0 x25: .cfa -2256 + ^ x26: .cfa -2248 + ^
STACK CFI 1a3d4 x27: .cfa -2240 + ^ x28: .cfa -2232 + ^
STACK CFI 1a3d8 v8: .cfa -2224 + ^ v9: .cfa -2216 + ^
STACK CFI INIT 1a3e0 34c .cfa: sp 0 + .ra: x30
STACK CFI 1a3e4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1a3f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1a410 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1a434 v8: .cfa -128 + ^ v9: .cfa -120 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 1a524 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1a528 .cfa: sp 224 + .ra: .cfa -216 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 1a730 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a734 .cfa: sp 2256 +
STACK CFI 1a73c .ra: .cfa -2248 + ^ x29: .cfa -2256 + ^
STACK CFI 1a744 x19: .cfa -2240 + ^ x20: .cfa -2232 + ^
STACK CFI 1a760 x21: .cfa -2224 + ^
STACK CFI 1a7ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1a7f0 .cfa: sp 2256 + .ra: .cfa -2248 + ^ x19: .cfa -2240 + ^ x20: .cfa -2232 + ^ x21: .cfa -2224 + ^ x29: .cfa -2256 + ^
STACK CFI INIT 1a7f8 188 .cfa: sp 0 + .ra: x30
STACK CFI 1a7fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1a804 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1a810 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1a818 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1a82c v8: .cfa -48 + ^
STACK CFI 1a840 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1a8b4 x27: x27 x28: x28
STACK CFI 1a96c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1a970 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 1a978 x27: x27 x28: x28
STACK CFI INIT 1a980 190 .cfa: sp 0 + .ra: x30
STACK CFI 1a984 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a98c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1a994 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a9c4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1a9c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1a9cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a9dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1a9e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1aaf0 x19: x19 x20: x20
STACK CFI 1aaf4 x21: x21 x22: x22
STACK CFI 1aaf8 x27: x27 x28: x28
STACK CFI 1ab0c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1ab10 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 1ab14 .cfa: sp 160 +
STACK CFI 1ab18 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1ab24 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1ab34 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1ab3c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1ac14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ac18 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1adb0 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ae60 9c .cfa: sp 0 + .ra: x30
STACK CFI 1ae64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ae74 x21: .cfa -16 + ^
STACK CFI 1ae7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1aef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1af00 178 .cfa: sp 0 + .ra: x30
STACK CFI 1af04 .cfa: sp 2160 +
STACK CFI 1af08 .ra: .cfa -2152 + ^ x29: .cfa -2160 + ^
STACK CFI 1af10 x23: .cfa -2112 + ^ x24: .cfa -2104 + ^
STACK CFI 1af18 x19: .cfa -2144 + ^ x20: .cfa -2136 + ^
STACK CFI 1af28 x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI 1af50 v10: .cfa -2080 + ^ v8: .cfa -2096 + ^ v9: .cfa -2088 + ^
STACK CFI 1b070 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1b074 .cfa: sp 2160 + .ra: .cfa -2152 + ^ v10: .cfa -2080 + ^ v8: .cfa -2096 + ^ v9: .cfa -2088 + ^ x19: .cfa -2144 + ^ x20: .cfa -2136 + ^ x21: .cfa -2128 + ^ x22: .cfa -2120 + ^ x23: .cfa -2112 + ^ x24: .cfa -2104 + ^ x29: .cfa -2160 + ^
STACK CFI INIT 1b078 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 1b07c .cfa: sp 1136 +
STACK CFI 1b080 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 1b088 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 1b098 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 1b0a0 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 1b0c4 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 1b0d0 x27: .cfa -1056 + ^
STACK CFI 1b1dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1b1e0 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x29: .cfa -1136 + ^
STACK CFI INIT 1b268 208 .cfa: sp 0 + .ra: x30
STACK CFI 1b26c .cfa: sp 1968 +
STACK CFI 1b270 .ra: .cfa -1960 + ^ x29: .cfa -1968 + ^
STACK CFI 1b278 x21: .cfa -1936 + ^ x22: .cfa -1928 + ^
STACK CFI 1b284 x23: .cfa -1920 + ^ x24: .cfa -1912 + ^
STACK CFI 1b298 x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^
STACK CFI 1b2a4 x27: .cfa -1888 + ^
STACK CFI 1b468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1b46c .cfa: sp 1968 + .ra: .cfa -1960 + ^ x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x21: .cfa -1936 + ^ x22: .cfa -1928 + ^ x23: .cfa -1920 + ^ x24: .cfa -1912 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^ x27: .cfa -1888 + ^ x29: .cfa -1968 + ^
STACK CFI INIT 1b470 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b4e0 12b0 .cfa: sp 0 + .ra: x30
STACK CFI 1b4e4 .cfa: sp 1488 +
STACK CFI 1b4f0 .ra: .cfa -1480 + ^ x29: .cfa -1488 + ^
STACK CFI 1b4f8 x21: .cfa -1456 + ^ x22: .cfa -1448 + ^
STACK CFI 1b504 x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI 1b510 x19: .cfa -1472 + ^ x20: .cfa -1464 + ^
STACK CFI 1b51c x23: .cfa -1440 + ^ x24: .cfa -1432 + ^
STACK CFI 1b534 x25: .cfa -1424 + ^ x26: .cfa -1416 + ^
STACK CFI 1bba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1bba8 .cfa: sp 1488 + .ra: .cfa -1480 + ^ x19: .cfa -1472 + ^ x20: .cfa -1464 + ^ x21: .cfa -1456 + ^ x22: .cfa -1448 + ^ x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^ x29: .cfa -1488 + ^
STACK CFI INIT 1c790 12b0 .cfa: sp 0 + .ra: x30
STACK CFI 1c794 .cfa: sp 1488 +
STACK CFI 1c7a0 .ra: .cfa -1480 + ^ x29: .cfa -1488 + ^
STACK CFI 1c7a8 x21: .cfa -1456 + ^ x22: .cfa -1448 + ^
STACK CFI 1c7b4 x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI 1c7c0 x19: .cfa -1472 + ^ x20: .cfa -1464 + ^
STACK CFI 1c7cc x23: .cfa -1440 + ^ x24: .cfa -1432 + ^
STACK CFI 1c7e4 x25: .cfa -1424 + ^ x26: .cfa -1416 + ^
STACK CFI 1ce54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ce58 .cfa: sp 1488 + .ra: .cfa -1480 + ^ x19: .cfa -1472 + ^ x20: .cfa -1464 + ^ x21: .cfa -1456 + ^ x22: .cfa -1448 + ^ x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^ x29: .cfa -1488 + ^
STACK CFI INIT 1da40 12b0 .cfa: sp 0 + .ra: x30
STACK CFI 1da44 .cfa: sp 1488 +
STACK CFI 1da50 .ra: .cfa -1480 + ^ x29: .cfa -1488 + ^
STACK CFI 1da58 x21: .cfa -1456 + ^ x22: .cfa -1448 + ^
STACK CFI 1da64 x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI 1da70 x19: .cfa -1472 + ^ x20: .cfa -1464 + ^
STACK CFI 1da7c x23: .cfa -1440 + ^ x24: .cfa -1432 + ^
STACK CFI 1da94 x25: .cfa -1424 + ^ x26: .cfa -1416 + ^
STACK CFI 1e104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1e108 .cfa: sp 1488 + .ra: .cfa -1480 + ^ x19: .cfa -1472 + ^ x20: .cfa -1464 + ^ x21: .cfa -1456 + ^ x22: .cfa -1448 + ^ x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^ x29: .cfa -1488 + ^
STACK CFI INIT 1ecf0 12b0 .cfa: sp 0 + .ra: x30
STACK CFI 1ecf4 .cfa: sp 1488 +
STACK CFI 1ed00 .ra: .cfa -1480 + ^ x29: .cfa -1488 + ^
STACK CFI 1ed08 x21: .cfa -1456 + ^ x22: .cfa -1448 + ^
STACK CFI 1ed14 x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI 1ed20 x19: .cfa -1472 + ^ x20: .cfa -1464 + ^
STACK CFI 1ed2c x23: .cfa -1440 + ^ x24: .cfa -1432 + ^
STACK CFI 1ed44 x25: .cfa -1424 + ^ x26: .cfa -1416 + ^
STACK CFI 1f3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1f3b8 .cfa: sp 1488 + .ra: .cfa -1480 + ^ x19: .cfa -1472 + ^ x20: .cfa -1464 + ^ x21: .cfa -1456 + ^ x22: .cfa -1448 + ^ x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^ x29: .cfa -1488 + ^
STACK CFI INIT 1ffa0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 1ffa4 .cfa: sp 96 +
STACK CFI 1ffa8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ffb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ffc8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2005c x23: x23 x24: x24
STACK CFI 20060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20064 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 20098 x23: x23 x24: x24
STACK CFI 200ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 200f0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 20148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2014c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 20190 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20240 70 .cfa: sp 0 + .ra: x30
STACK CFI 20244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20254 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20264 x21: .cfa -16 + ^
STACK CFI 202ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 202b0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 202b4 .cfa: sp 1136 +
STACK CFI 202bc .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 202c4 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 202d0 v8: .cfa -1072 + ^ v9: .cfa -1064 + ^
STACK CFI 202d8 v10: .cfa -1056 + ^
STACK CFI 202e0 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 20318 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 203e8 x23: x23 x24: x24
STACK CFI 20428 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2042c .cfa: sp 1136 + .ra: .cfa -1128 + ^ v10: .cfa -1056 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x29: .cfa -1136 + ^
STACK CFI 20454 x23: x23 x24: x24
STACK CFI 20458 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI INIT 20460 448 .cfa: sp 0 + .ra: x30
STACK CFI 20468 .cfa: sp 4688 +
STACK CFI 20470 .ra: .cfa -4680 + ^ x29: .cfa -4688 + ^
STACK CFI 2047c x23: .cfa -4640 + ^ x24: .cfa -4632 + ^
STACK CFI 20490 x27: .cfa -4608 + ^ x28: .cfa -4600 + ^
STACK CFI 204a0 x19: .cfa -4672 + ^ x20: .cfa -4664 + ^
STACK CFI 204b0 x21: .cfa -4656 + ^ x22: .cfa -4648 + ^
STACK CFI 204b8 x25: .cfa -4624 + ^ x26: .cfa -4616 + ^
STACK CFI 208a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 208a4 .cfa: sp 4688 + .ra: .cfa -4680 + ^ x19: .cfa -4672 + ^ x20: .cfa -4664 + ^ x21: .cfa -4656 + ^ x22: .cfa -4648 + ^ x23: .cfa -4640 + ^ x24: .cfa -4632 + ^ x25: .cfa -4624 + ^ x26: .cfa -4616 + ^ x27: .cfa -4608 + ^ x28: .cfa -4600 + ^ x29: .cfa -4688 + ^
STACK CFI INIT 208a8 854 .cfa: sp 0 + .ra: x30
STACK CFI 208ac .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 208b4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 208d0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 208d8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 208e0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 208e8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 20c20 x19: x19 x20: x20
STACK CFI 20c24 x21: x21 x22: x22
STACK CFI 20c28 x23: x23 x24: x24
STACK CFI 20c2c x27: x27 x28: x28
STACK CFI 20c34 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 20c38 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 21100 854 .cfa: sp 0 + .ra: x30
STACK CFI 21104 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2110c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 21128 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 21130 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 21138 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 21140 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 21478 x19: x19 x20: x20
STACK CFI 2147c x21: x21 x22: x22
STACK CFI 21480 x23: x23 x24: x24
STACK CFI 21484 x27: x27 x28: x28
STACK CFI 2148c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 21490 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 21958 854 .cfa: sp 0 + .ra: x30
STACK CFI 2195c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 21964 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 21980 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 21988 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 21990 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 21998 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 21cd0 x19: x19 x20: x20
STACK CFI 21cd4 x21: x21 x22: x22
STACK CFI 21cd8 x23: x23 x24: x24
STACK CFI 21cdc x27: x27 x28: x28
STACK CFI 21ce4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 21ce8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 221b0 854 .cfa: sp 0 + .ra: x30
STACK CFI 221b4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 221bc x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 221d8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 221e0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 221e8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 221f0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 22528 x19: x19 x20: x20
STACK CFI 2252c x21: x21 x22: x22
STACK CFI 22530 x23: x23 x24: x24
STACK CFI 22534 x27: x27 x28: x28
STACK CFI 2253c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 22540 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 22a08 854 .cfa: sp 0 + .ra: x30
STACK CFI 22a0c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 22a14 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 22a30 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 22a38 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 22a40 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 22a48 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 22d80 x19: x19 x20: x20
STACK CFI 22d84 x21: x21 x22: x22
STACK CFI 22d88 x23: x23 x24: x24
STACK CFI 22d8c x27: x27 x28: x28
STACK CFI 22d94 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 22d98 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 23260 854 .cfa: sp 0 + .ra: x30
STACK CFI 23264 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2326c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 23288 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 23290 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 23298 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 232a0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 235d8 x19: x19 x20: x20
STACK CFI 235dc x21: x21 x22: x22
STACK CFI 235e0 x23: x23 x24: x24
STACK CFI 235e4 x27: x27 x28: x28
STACK CFI 235ec .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 235f0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 23ab8 854 .cfa: sp 0 + .ra: x30
STACK CFI 23abc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 23ac4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 23ae0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 23ae8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 23af0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 23af8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 23e30 x19: x19 x20: x20
STACK CFI 23e34 x21: x21 x22: x22
STACK CFI 23e38 x23: x23 x24: x24
STACK CFI 23e3c x27: x27 x28: x28
STACK CFI 23e44 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 23e48 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 24310 854 .cfa: sp 0 + .ra: x30
STACK CFI 24314 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2431c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 24338 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 24340 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 24348 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 24350 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 24688 x19: x19 x20: x20
STACK CFI 2468c x21: x21 x22: x22
STACK CFI 24690 x23: x23 x24: x24
STACK CFI 24694 x27: x27 x28: x28
STACK CFI 2469c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 246a0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 24b68 904 .cfa: sp 0 + .ra: x30
STACK CFI 24b6c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 24b74 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 24b90 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 24b98 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 24ba0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 24ba8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 24f40 x19: x19 x20: x20
STACK CFI 24f44 x21: x21 x22: x22
STACK CFI 24f48 x23: x23 x24: x24
STACK CFI 24f4c x27: x27 x28: x28
STACK CFI 24f54 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 24f58 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 25470 904 .cfa: sp 0 + .ra: x30
STACK CFI 25474 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2547c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 25498 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 254a0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 254a8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 254b0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 25848 x19: x19 x20: x20
STACK CFI 2584c x21: x21 x22: x22
STACK CFI 25850 x23: x23 x24: x24
STACK CFI 25854 x27: x27 x28: x28
STACK CFI 2585c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 25860 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 25d78 244 .cfa: sp 0 + .ra: x30
STACK CFI 25d7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25d8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25d94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 25d9c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 25e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 25e6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 25fc0 13c .cfa: sp 0 + .ra: x30
STACK CFI 25fc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25fcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25fd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 26008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2600c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 26100 70 .cfa: sp 0 + .ra: x30
STACK CFI 26104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2610c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2612c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 26130 x21: .cfa -16 + ^
STACK CFI 26150 x21: x21
STACK CFI 2615c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26160 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26170 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261d0 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26228 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26288 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 262e8 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26358 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2635c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2636c x19: .cfa -16 + ^
STACK CFI 263d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 263d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 263f8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 263fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2640c x19: .cfa -16 + ^
STACK CFI 26470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26498 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2649c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 264ac x19: .cfa -16 + ^
STACK CFI 26514 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26518 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26540 118 .cfa: sp 0 + .ra: x30
STACK CFI 26544 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26550 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 265c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 265cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 26624 x23: .cfa -16 + ^
STACK CFI 26650 x23: x23
STACK CFI INIT 26658 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 266b8 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26720 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26760 f8 .cfa: sp 0 + .ra: x30
STACK CFI 26764 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26774 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2677c x21: .cfa -16 + ^
STACK CFI 267b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 267b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 267c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 267c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26858 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2685c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2686c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26874 x21: .cfa -16 + ^
STACK CFI 268a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 268ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 268b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 268bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26950 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26990 170 .cfa: sp 0 + .ra: x30
STACK CFI 26994 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 269a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 269b0 x21: .cfa -16 + ^
STACK CFI 269e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 269e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 269f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 269f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26b00 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b90 218 .cfa: sp 0 + .ra: x30
STACK CFI 26b94 .cfa: sp 1136 +
STACK CFI 26b9c .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 26ba4 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 26bb0 v8: .cfa -1056 + ^ v9: .cfa -1048 + ^
STACK CFI 26bbc x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 26bc8 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 26c04 x25: .cfa -1072 + ^
STACK CFI 26c0c v10: .cfa -1064 + ^
STACK CFI 26d0c x25: x25
STACK CFI 26d14 v10: v10
STACK CFI 26d40 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26d44 .cfa: sp 1136 + .ra: .cfa -1128 + ^ v10: .cfa -1064 + ^ v8: .cfa -1056 + ^ v9: .cfa -1048 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x29: .cfa -1136 + ^
STACK CFI 26d5c x25: x25
STACK CFI 26d60 v10: v10
STACK CFI 26d64 v10: .cfa -1064 + ^ x25: .cfa -1072 + ^
STACK CFI 26d94 v10: v10 x25: x25
STACK CFI 26d98 x25: .cfa -1072 + ^
STACK CFI 26d9c v10: .cfa -1064 + ^
STACK CFI INIT 26da8 e90 .cfa: sp 0 + .ra: x30
STACK CFI 26dac .cfa: sp 2544 +
STACK CFI 26dc0 .ra: .cfa -2472 + ^ x29: .cfa -2480 + ^
STACK CFI 26dd0 x19: .cfa -2464 + ^ x20: .cfa -2456 + ^
STACK CFI 26dd8 x23: .cfa -2432 + ^ x24: .cfa -2424 + ^
STACK CFI 26de8 x21: .cfa -2448 + ^ x22: .cfa -2440 + ^
STACK CFI 26e78 x27: .cfa -2400 + ^ x28: .cfa -2392 + ^
STACK CFI 26e88 x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 27098 x25: x25 x26: x26
STACK CFI 2709c x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 270cc x25: x25 x26: x26
STACK CFI 270f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 270fc .cfa: sp 2544 + .ra: .cfa -2472 + ^ x19: .cfa -2464 + ^ x20: .cfa -2456 + ^ x21: .cfa -2448 + ^ x22: .cfa -2440 + ^ x23: .cfa -2432 + ^ x24: .cfa -2424 + ^ x27: .cfa -2400 + ^ x28: .cfa -2392 + ^ x29: .cfa -2480 + ^
STACK CFI 2712c x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 271a0 x25: x25 x26: x26
STACK CFI 271a4 x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 27254 v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 2726c v10: .cfa -2368 + ^ v11: .cfa -2360 + ^
STACK CFI 27274 v12: .cfa -2352 + ^ v13: .cfa -2344 + ^
STACK CFI 275b4 v8: v8 v9: v9
STACK CFI 275b8 v10: v10 v11: v11
STACK CFI 275bc v12: v12 v13: v13
STACK CFI 275c0 v10: .cfa -2368 + ^ v11: .cfa -2360 + ^ v12: .cfa -2352 + ^ v13: .cfa -2344 + ^ v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 27a38 v8: v8 v9: v9
STACK CFI 27a3c v10: v10 v11: v11
STACK CFI 27a40 v12: v12 v13: v13
STACK CFI 27a44 v10: .cfa -2368 + ^ v11: .cfa -2360 + ^ v12: .cfa -2352 + ^ v13: .cfa -2344 + ^ v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 27a88 v8: v8 v9: v9
STACK CFI 27a8c v10: v10 v11: v11
STACK CFI 27a90 v12: v12 v13: v13
STACK CFI 27a94 v10: .cfa -2368 + ^ v11: .cfa -2360 + ^ v12: .cfa -2352 + ^ v13: .cfa -2344 + ^ v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 27b8c v8: v8 v9: v9
STACK CFI 27b90 v10: v10 v11: v11
STACK CFI 27b94 v12: v12 v13: v13
STACK CFI 27b98 v10: .cfa -2368 + ^ v11: .cfa -2360 + ^ v12: .cfa -2352 + ^ v13: .cfa -2344 + ^ v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 27c1c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x25: x25 x26: x26
STACK CFI 27c20 x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 27c24 v8: .cfa -2384 + ^ v9: .cfa -2376 + ^
STACK CFI 27c28 v10: .cfa -2368 + ^ v11: .cfa -2360 + ^
STACK CFI 27c2c v12: .cfa -2352 + ^ v13: .cfa -2344 + ^
STACK CFI INIT 27c38 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c90 1e20 .cfa: sp 0 + .ra: x30
STACK CFI 27c94 .cfa: sp 304 +
STACK CFI 27c98 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 27ca0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 27cac x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 27cfc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 27d04 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 27d64 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 280d8 x25: x25 x26: x26
STACK CFI 280dc x27: x27 x28: x28
STACK CFI 280e8 x23: x23 x24: x24
STACK CFI 280ec x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 280f0 x23: x23 x24: x24
STACK CFI 280f4 x25: x25 x26: x26
STACK CFI 2811c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28120 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 28804 x27: x27 x28: x28
STACK CFI 28834 x23: x23 x24: x24
STACK CFI 28838 x25: x25 x26: x26
STACK CFI 2883c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 289e0 x25: x25 x26: x26
STACK CFI 289e4 x27: x27 x28: x28
STACK CFI 289ec x23: x23 x24: x24
STACK CFI 289f0 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 28a00 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 28ac0 x27: x27 x28: x28
STACK CFI 28acc x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 28b70 x27: x27 x28: x28
STACK CFI 28b98 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 29a84 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 29a88 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 29a8c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 29a90 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 29ab0 1238 .cfa: sp 0 + .ra: x30
STACK CFI 29ab4 .cfa: sp 928 +
STACK CFI 29ac0 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 29ac8 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 29ae4 x21: .cfa -832 + ^ x22: .cfa -824 + ^
STACK CFI 29b2c x23: .cfa -816 + ^ x24: .cfa -808 + ^
STACK CFI 29b6c x27: .cfa -784 + ^ x28: .cfa -776 + ^
STACK CFI 29dd0 x25: .cfa -800 + ^ x26: .cfa -792 + ^
STACK CFI 2a3f4 x25: x25 x26: x26
STACK CFI 2a440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2a444 .cfa: sp 928 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x24: .cfa -808 + ^ x25: .cfa -800 + ^ x26: .cfa -792 + ^ x27: .cfa -784 + ^ x28: .cfa -776 + ^ x29: .cfa -864 + ^
STACK CFI 2a794 x25: x25 x26: x26
STACK CFI 2a9b4 x25: .cfa -800 + ^ x26: .cfa -792 + ^
STACK CFI 2a9c8 x25: x25 x26: x26
STACK CFI 2ac9c x25: .cfa -800 + ^ x26: .cfa -792 + ^
STACK CFI 2acc0 x25: x25 x26: x26
STACK CFI 2acdc x25: .cfa -800 + ^ x26: .cfa -792 + ^
STACK CFI INIT 2ace8 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2adc8 12c .cfa: sp 0 + .ra: x30
STACK CFI 2adcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2add4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ade0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2aec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2aecc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2aef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2aef8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2af00 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2af08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2af1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2af24 x23: .cfa -16 + ^
STACK CFI 2afb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2afb8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2afe0 d14 .cfa: sp 0 + .ra: x30
STACK CFI 2afe4 .cfa: sp 256 +
STACK CFI 2afe8 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2aff0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2b000 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2b018 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2b024 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2b030 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2b18c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2b190 .cfa: sp 256 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2bcf8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bd08 30 .cfa: sp 0 + .ra: x30
STACK CFI 2bd20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bd34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2bd38 30c .cfa: sp 0 + .ra: x30
STACK CFI 2bd3c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2bd44 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2bd50 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2bd60 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2bd68 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2bd7c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2be90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2be94 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2c048 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c0b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c0c8 5c .cfa: sp 0 + .ra: x30
STACK CFI 2c0cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c0d4 x19: .cfa -16 + ^
STACK CFI 2c120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c128 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2c12c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c1d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c1dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2c1e8 334 .cfa: sp 0 + .ra: x30
STACK CFI 2c1ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c1f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c200 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c210 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c21c x25: .cfa -16 + ^
STACK CFI 2c518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2c520 324 .cfa: sp 0 + .ra: x30
STACK CFI 2c528 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c538 x21: .cfa -16 + ^
STACK CFI 2c540 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c62c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2c848 47c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ccc8 140 .cfa: sp 0 + .ra: x30
STACK CFI 2cccc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2ce00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ce04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2ce08 274 .cfa: sp 0 + .ra: x30
STACK CFI 2ce0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2ce20 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2ce30 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2cea0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2d05c x27: x27 x28: x28
STACK CFI 2d070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d074 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2d080 5c0 .cfa: sp 0 + .ra: x30
STACK CFI 2d088 .cfa: sp 6352 +
STACK CFI 2d094 .ra: .cfa -6344 + ^ x29: .cfa -6352 + ^
STACK CFI 2d0a0 x23: .cfa -6304 + ^ x24: .cfa -6296 + ^
STACK CFI 2d0b4 x21: .cfa -6320 + ^ x22: .cfa -6312 + ^
STACK CFI 2d0c4 x25: .cfa -6288 + ^ x26: .cfa -6280 + ^
STACK CFI 2d0d0 x27: .cfa -6272 + ^ x28: .cfa -6264 + ^
STACK CFI 2d0e4 x19: .cfa -6336 + ^ x20: .cfa -6328 + ^
STACK CFI 2d110 v8: .cfa -6256 + ^ v9: .cfa -6248 + ^
STACK CFI 2d264 x19: x19 x20: x20
STACK CFI 2d268 v8: v8 v9: v9
STACK CFI 2d29c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d2a0 .cfa: sp 6352 + .ra: .cfa -6344 + ^ x21: .cfa -6320 + ^ x22: .cfa -6312 + ^ x23: .cfa -6304 + ^ x24: .cfa -6296 + ^ x25: .cfa -6288 + ^ x26: .cfa -6280 + ^ x27: .cfa -6272 + ^ x28: .cfa -6264 + ^ x29: .cfa -6352 + ^
STACK CFI 2d2b8 x19: .cfa -6336 + ^ x20: .cfa -6328 + ^
STACK CFI 2d2c4 v8: .cfa -6256 + ^ v9: .cfa -6248 + ^
STACK CFI 2d2c8 v10: .cfa -6240 + ^ v11: .cfa -6232 + ^
STACK CFI 2d2cc v12: .cfa -6224 + ^
STACK CFI 2d5b0 x19: x19 x20: x20
STACK CFI 2d5b4 v8: v8 v9: v9
STACK CFI 2d5b8 v10: v10 v11: v11
STACK CFI 2d5bc v12: v12
STACK CFI 2d5c0 v10: .cfa -6240 + ^ v11: .cfa -6232 + ^ v12: .cfa -6224 + ^ v8: .cfa -6256 + ^ v9: .cfa -6248 + ^ x19: .cfa -6336 + ^ x20: .cfa -6328 + ^
STACK CFI 2d624 v10: v10 v11: v11 v12: v12 v8: v8 v9: v9
STACK CFI 2d628 x19: x19 x20: x20
STACK CFI 2d630 x19: .cfa -6336 + ^ x20: .cfa -6328 + ^
STACK CFI 2d634 v8: .cfa -6256 + ^ v9: .cfa -6248 + ^
STACK CFI 2d638 v10: .cfa -6240 + ^ v11: .cfa -6232 + ^
STACK CFI 2d63c v12: .cfa -6224 + ^
STACK CFI INIT 2d640 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d670 28 .cfa: sp 0 + .ra: x30
STACK CFI 2d674 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d68c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d690 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2d698 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6b0 724 .cfa: sp 0 + .ra: x30
STACK CFI 2d6b4 .cfa: sp 2304 +
STACK CFI 2d6b8 .ra: .cfa -2296 + ^ x29: .cfa -2304 + ^
STACK CFI 2d6c0 x27: .cfa -2224 + ^ x28: .cfa -2216 + ^
STACK CFI 2d6cc x25: .cfa -2240 + ^ x26: .cfa -2232 + ^
STACK CFI 2d6ec x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^
STACK CFI 2d710 x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2d718 v8: .cfa -2208 + ^ v9: .cfa -2200 + ^
STACK CFI 2d810 x21: x21 x22: x22
STACK CFI 2d814 v8: v8 v9: v9
STACK CFI 2d85c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d860 .cfa: sp 2304 + .ra: .cfa -2296 + ^ x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^ x25: .cfa -2240 + ^ x26: .cfa -2232 + ^ x27: .cfa -2224 + ^ x28: .cfa -2216 + ^ x29: .cfa -2304 + ^
STACK CFI 2d86c x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2d87c v8: .cfa -2208 + ^ v9: .cfa -2200 + ^
STACK CFI 2d88c v10: .cfa -2192 + ^
STACK CFI 2db28 x21: x21 x22: x22
STACK CFI 2db2c v8: v8 v9: v9
STACK CFI 2db30 v10: v10
STACK CFI 2db3c v10: .cfa -2192 + ^ v8: .cfa -2208 + ^ v9: .cfa -2200 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2db48 v10: v10
STACK CFI 2db70 v10: .cfa -2192 + ^
STACK CFI 2dc10 x21: x21 x22: x22
STACK CFI 2dc18 v8: v8 v9: v9
STACK CFI 2dc1c v10: v10
STACK CFI 2dc24 v8: .cfa -2208 + ^ v9: .cfa -2200 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2dc50 v10: .cfa -2192 + ^
STACK CFI 2dcdc v10: v10
STACK CFI 2dce4 x21: x21 x22: x22
STACK CFI 2dce8 v8: v8 v9: v9
STACK CFI 2dcfc v10: .cfa -2192 + ^ v8: .cfa -2208 + ^ v9: .cfa -2200 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2dd4c v10: v10
STACK CFI 2dd54 v10: .cfa -2192 + ^
STACK CFI 2dd64 x21: x21 x22: x22
STACK CFI 2dd68 v8: v8 v9: v9
STACK CFI 2dd6c v10: v10
STACK CFI 2dd70 v10: .cfa -2192 + ^ v8: .cfa -2208 + ^ v9: .cfa -2200 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2dda8 v10: v10 v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI 2ddac x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 2ddb0 v8: .cfa -2208 + ^ v9: .cfa -2200 + ^
STACK CFI 2ddb4 v10: .cfa -2192 + ^
STACK CFI INIT 2ddd8 714 .cfa: sp 0 + .ra: x30
STACK CFI 2dde0 .cfa: sp 5904 +
STACK CFI 2dde4 .ra: .cfa -5896 + ^ x29: .cfa -5904 + ^
STACK CFI 2ddec x27: .cfa -5824 + ^ x28: .cfa -5816 + ^
STACK CFI 2ddf8 x25: .cfa -5840 + ^ x26: .cfa -5832 + ^
STACK CFI 2de18 x19: .cfa -5888 + ^ x20: .cfa -5880 + ^ x23: .cfa -5856 + ^ x24: .cfa -5848 + ^
STACK CFI 2de3c x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2de44 v8: .cfa -5808 + ^ v9: .cfa -5800 + ^
STACK CFI 2df3c x21: x21 x22: x22
STACK CFI 2df40 v8: v8 v9: v9
STACK CFI 2df8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2df90 .cfa: sp 5904 + .ra: .cfa -5896 + ^ x19: .cfa -5888 + ^ x20: .cfa -5880 + ^ x23: .cfa -5856 + ^ x24: .cfa -5848 + ^ x25: .cfa -5840 + ^ x26: .cfa -5832 + ^ x27: .cfa -5824 + ^ x28: .cfa -5816 + ^ x29: .cfa -5904 + ^
STACK CFI 2dfa4 x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2dfb0 v8: .cfa -5808 + ^ v9: .cfa -5800 + ^
STACK CFI 2dfb8 v10: .cfa -5792 + ^
STACK CFI 2e244 x21: x21 x22: x22
STACK CFI 2e248 v8: v8 v9: v9
STACK CFI 2e24c v10: v10
STACK CFI 2e258 v10: .cfa -5792 + ^ v8: .cfa -5808 + ^ v9: .cfa -5800 + ^ x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2e264 v10: v10
STACK CFI 2e290 v10: .cfa -5792 + ^
STACK CFI 2e32c x21: x21 x22: x22
STACK CFI 2e334 v8: v8 v9: v9
STACK CFI 2e338 v10: v10
STACK CFI 2e340 v8: .cfa -5808 + ^ v9: .cfa -5800 + ^ x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2e36c v10: .cfa -5792 + ^
STACK CFI 2e3f4 v10: v10
STACK CFI 2e3fc x21: x21 x22: x22
STACK CFI 2e400 v8: v8 v9: v9
STACK CFI 2e414 v10: .cfa -5792 + ^ v8: .cfa -5808 + ^ v9: .cfa -5800 + ^ x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2e464 v10: v10
STACK CFI 2e46c v10: .cfa -5792 + ^
STACK CFI 2e470 x21: x21 x22: x22
STACK CFI 2e474 v8: v8 v9: v9
STACK CFI 2e478 v10: v10
STACK CFI 2e47c v10: .cfa -5792 + ^ v8: .cfa -5808 + ^ v9: .cfa -5800 + ^ x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2e4dc v10: v10 v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI 2e4e0 x21: .cfa -5872 + ^ x22: .cfa -5864 + ^
STACK CFI 2e4e4 v8: .cfa -5808 + ^ v9: .cfa -5800 + ^
STACK CFI 2e4e8 v10: .cfa -5792 + ^
STACK CFI INIT 2e4f0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e538 73c .cfa: sp 0 + .ra: x30
STACK CFI 2e540 .cfa: sp 4608 +
STACK CFI 2e544 .ra: .cfa -4600 + ^ x29: .cfa -4608 + ^
STACK CFI 2e54c x25: .cfa -4544 + ^ x26: .cfa -4536 + ^
STACK CFI 2e56c x19: .cfa -4592 + ^ x20: .cfa -4584 + ^ x27: .cfa -4528 + ^ x28: .cfa -4520 + ^
STACK CFI 2e580 x23: .cfa -4560 + ^ x24: .cfa -4552 + ^
STACK CFI 2e59c x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2e5a4 v8: .cfa -4512 + ^ v9: .cfa -4504 + ^
STACK CFI 2e69c x21: x21 x22: x22
STACK CFI 2e6a0 v8: v8 v9: v9
STACK CFI 2e6ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e6f0 .cfa: sp 4608 + .ra: .cfa -4600 + ^ x19: .cfa -4592 + ^ x20: .cfa -4584 + ^ x23: .cfa -4560 + ^ x24: .cfa -4552 + ^ x25: .cfa -4544 + ^ x26: .cfa -4536 + ^ x27: .cfa -4528 + ^ x28: .cfa -4520 + ^ x29: .cfa -4608 + ^
STACK CFI 2e6fc x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2e70c v8: .cfa -4512 + ^ v9: .cfa -4504 + ^
STACK CFI 2e71c v10: .cfa -4496 + ^
STACK CFI 2e9b8 x21: x21 x22: x22
STACK CFI 2e9bc v8: v8 v9: v9
STACK CFI 2e9c0 v10: v10
STACK CFI 2e9cc v10: .cfa -4496 + ^ v8: .cfa -4512 + ^ v9: .cfa -4504 + ^ x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2e9d8 v10: v10
STACK CFI 2ea04 v10: .cfa -4496 + ^
STACK CFI 2eaac x21: x21 x22: x22
STACK CFI 2eab4 v8: v8 v9: v9
STACK CFI 2eab8 v10: v10
STACK CFI 2eac0 v8: .cfa -4512 + ^ v9: .cfa -4504 + ^ x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2eaec v10: .cfa -4496 + ^
STACK CFI 2eb84 x21: x21 x22: x22
STACK CFI 2eb88 v8: v8 v9: v9
STACK CFI 2eb8c v10: v10
STACK CFI 2eba0 v10: .cfa -4496 + ^ v8: .cfa -4512 + ^ v9: .cfa -4504 + ^ x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2ebe8 v10: v10
STACK CFI 2ebf0 v10: .cfa -4496 + ^
STACK CFI 2ebf4 x21: x21 x22: x22
STACK CFI 2ebf8 v8: v8 v9: v9
STACK CFI 2ebfc v10: v10
STACK CFI 2ec00 v10: .cfa -4496 + ^ v8: .cfa -4512 + ^ v9: .cfa -4504 + ^ x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2ec64 v10: v10 v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI 2ec68 x21: .cfa -4576 + ^ x22: .cfa -4568 + ^
STACK CFI 2ec6c v8: .cfa -4512 + ^ v9: .cfa -4504 + ^
STACK CFI 2ec70 v10: .cfa -4496 + ^
STACK CFI INIT 2ec78 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ecc8 9d8 .cfa: sp 0 + .ra: x30
STACK CFI 2eccc .cfa: sp 592 +
STACK CFI 2ecd0 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 2ecd8 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 2ece0 x25: .cfa -528 + ^ x26: .cfa -520 + ^
STACK CFI 2ecec x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 2ed18 x23: .cfa -544 + ^ x24: .cfa -536 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 2ed64 v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2ee1c v8: v8 v9: v9
STACK CFI 2ee70 v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2eea8 v8: v8 v9: v9
STACK CFI 2eee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2eee8 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^ x29: .cfa -592 + ^
STACK CFI 2eef8 v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2ef6c v10: .cfa -480 + ^ v11: .cfa -472 + ^
STACK CFI 2f1dc v10: v10 v11: v11
STACK CFI 2f2f0 v8: v8 v9: v9
STACK CFI 2f2f4 v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2f358 v8: v8 v9: v9
STACK CFI 2f374 v10: .cfa -480 + ^ v11: .cfa -472 + ^ v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2f380 v10: v10 v11: v11
STACK CFI 2f3a4 v10: .cfa -480 + ^ v11: .cfa -472 + ^
STACK CFI 2f400 v10: v10 v11: v11
STACK CFI 2f438 v10: .cfa -480 + ^ v11: .cfa -472 + ^
STACK CFI 2f460 v10: v10 v11: v11
STACK CFI 2f668 v10: .cfa -480 + ^ v11: .cfa -472 + ^
STACK CFI 2f678 v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI 2f67c v8: .cfa -496 + ^ v9: .cfa -488 + ^
STACK CFI 2f680 v10: .cfa -480 + ^ v11: .cfa -472 + ^
STACK CFI INIT 2f6a0 220 .cfa: sp 0 + .ra: x30
STACK CFI 2f6a4 .cfa: sp 2288 +
STACK CFI 2f6ac .ra: .cfa -2280 + ^ x29: .cfa -2288 + ^
STACK CFI 2f6b4 x19: .cfa -2272 + ^ x20: .cfa -2264 + ^
STACK CFI 2f6c4 x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 2f6e4 x21: .cfa -2256 + ^ x22: .cfa -2248 + ^
STACK CFI 2f6f4 v8: .cfa -2224 + ^
STACK CFI 2f8b0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f8b4 .cfa: sp 2288 + .ra: .cfa -2280 + ^ v8: .cfa -2224 + ^ x19: .cfa -2272 + ^ x20: .cfa -2264 + ^ x21: .cfa -2256 + ^ x22: .cfa -2248 + ^ x23: .cfa -2240 + ^ x24: .cfa -2232 + ^ x29: .cfa -2288 + ^
STACK CFI INIT 2f8c0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 2f8c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f8d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2f8e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f8f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2f908 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2f934 x27: .cfa -16 + ^
STACK CFI 2f968 x27: x27
STACK CFI 2f97c x27: .cfa -16 + ^
STACK CFI 2f9b0 x27: x27
STACK CFI 2fa10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2fa14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2fa68 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2fa6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2fa78 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2faa4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fae4 x27: .cfa -16 + ^
STACK CFI 2fb18 x27: x27
STACK CFI 2fb2c x27: .cfa -16 + ^
STACK CFI 2fb60 x27: x27
STACK CFI 2fbbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2fbc0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2fc18 bc .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fcd8 728 .cfa: sp 0 + .ra: x30
STACK CFI 2fcdc .cfa: sp 480 +
STACK CFI 2fce4 .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 2fcec x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 2fd1c x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 2fd34 v8: .cfa -336 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 2fd44 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 2fd4c x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 301c0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 301c4 .cfa: sp 480 + .ra: .cfa -424 + ^ v8: .cfa -336 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI INIT 30400 86c .cfa: sp 0 + .ra: x30
STACK CFI 30404 .cfa: sp 768 +
STACK CFI 3040c .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 30424 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 30448 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 30454 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 305e8 v8: .cfa -672 + ^
STACK CFI 30620 v8: v8
STACK CFI 30838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3083c .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI 30c68 v8: .cfa -672 + ^
STACK CFI INIT 30c70 120 .cfa: sp 0 + .ra: x30
STACK CFI 30c74 .cfa: sp 784 +
STACK CFI 30c78 .ra: .cfa -776 + ^ x29: .cfa -784 + ^
STACK CFI 30c80 x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 30c8c x19: .cfa -768 + ^ x20: .cfa -760 + ^
STACK CFI 30c9c x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI 30d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30d8c .cfa: sp 784 + .ra: .cfa -776 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^ x29: .cfa -784 + ^
STACK CFI INIT 30d90 1d38 .cfa: sp 0 + .ra: x30
STACK CFI 30d94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 30dcc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3107c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 31080 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 32ac8 18c .cfa: sp 0 + .ra: x30
