MODULE Linux arm64 88646B1D8AD6D64586E54F2F834E7B650 liberror_interface.so
INFO CODE_ID 1D6B6488D68A45D686E54F2F834E7B65
PUBLIC 1280 0 _init
PUBLIC 13e0 0 _GLOBAL__sub_I_error_nvmedia.cpp
PUBLIC 1598 0 call_weak_fn
PUBLIC 15ac 0 deregister_tm_clones
PUBLIC 15dc 0 register_tm_clones
PUBLIC 1618 0 __do_global_dtors_aux
PUBLIC 1668 0 frame_dummy
PUBLIC 1670 0 lios::error::NvMediaErrorStr(int)
PUBLIC 16e0 0 std::unordered_map<int, char const*, std::hash<int>, std::equal_to<int>, std::allocator<std::pair<int const, char const*> > >::~unordered_map()
PUBLIC 1750 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 1880 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_Hashtable<std::pair<int const, char const*> const*>(std::pair<int const, char const*> const*, std::pair<int const, char const*> const*, unsigned long, std::hash<int> const&, std::__detail::_Mod_range_hashing const&, std::__detail::_Default_ranged_hash const&, std::equal_to<int> const&, std::__detail::_Select1st const&, std::allocator<std::pair<int const, char const*> > const&)
PUBLIC 1b24 0 _fini
STACK CFI INIT 15ac 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15dc 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1618 50 .cfa: sp 0 + .ra: x30
STACK CFI 1628 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1630 x19: .cfa -16 + ^
STACK CFI 1660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1668 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1738 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1670 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1750 124 .cfa: sp 0 + .ra: x30
STACK CFI 1754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1760 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 176c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 180c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1880 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1890 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 189c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 18b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 18c0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1a7c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 13e0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 13e4 .cfa: sp 352 +
STACK CFI 13f8 .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 1400 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 1410 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 1594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
