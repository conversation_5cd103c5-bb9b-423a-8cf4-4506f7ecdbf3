MODULE Linux arm64 4E399283D3C23A4559D6C2BE4A0C5F400 libboost_math_c99l.so.1.77.0
INFO CODE_ID 8392394EC2D3453A59D6C2BE4A0C5F40
PUBLIC 1560 0 _init
PUBLIC 17b0 0 _GLOBAL__sub_I_acoshl.cpp
PUBLIC 17f0 0 _GLOBAL__sub_I_asinhl.cpp
PUBLIC 1850 0 _GLOBAL__sub_I_atanhl.cpp
PUBLIC 1890 0 _GLOBAL__sub_I_cbrtl.cpp
PUBLIC 18d0 0 _GLOBAL__sub_I_copysignl.cpp
PUBLIC 1910 0 boost::math::tools::promote_args<long double, float, float, float, float, float>::type boost::math::erf<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 19f0 0 _GLOBAL__sub_I_erfcl.cpp
PUBLIC 1af0 0 _GLOBAL__sub_I_erfl.cpp
PUBLIC 1bf0 0 _GLOBAL__sub_I_expm1l.cpp
PUBLIC 1c50 0 _GLOBAL__sub_I_fmaxl.cpp
PUBLIC 1c90 0 _GLOBAL__sub_I_fminl.cpp
PUBLIC 1cd0 0 _GLOBAL__sub_I_fpclassifyl.cpp
PUBLIC 1d10 0 _GLOBAL__sub_I_hypotl.cpp
PUBLIC 1d50 0 _GLOBAL__sub_I_lgammal.cpp
PUBLIC 1e00 0 _GLOBAL__sub_I_llroundl.cpp
PUBLIC 1e40 0 _GLOBAL__sub_I_log1pl.cpp
PUBLIC 1e80 0 _GLOBAL__sub_I_lroundl.cpp
PUBLIC 1ec0 0 _GLOBAL__sub_I_nextafterl.cpp
PUBLIC 1f60 0 _GLOBAL__sub_I_nexttowardl.cpp
PUBLIC 2000 0 _GLOBAL__sub_I_roundl.cpp
PUBLIC 2040 0 _GLOBAL__sub_I_tgammal.cpp
PUBLIC 20a0 0 _GLOBAL__sub_I_truncl.cpp
PUBLIC 20dc 0 call_weak_fn
PUBLIC 20f0 0 deregister_tm_clones
PUBLIC 2120 0 register_tm_clones
PUBLIC 215c 0 __do_global_dtors_aux
PUBLIC 21ac 0 frame_dummy
PUBLIC 21b0 0 boost_acoshl
PUBLIC 24d0 0 boost_asinhl
PUBLIC 2dc0 0 long double boost::math::detail::expm1_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, std::integral_constant<int, 113> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 3150 0 long double boost::math::detail::asinh_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 36b0 0 boost_atanhl
PUBLIC 3a80 0 boost_cbrtl
PUBLIC 3f20 0 boost_copysignl
PUBLIC 3f80 0 boost_erfcl
PUBLIC 4090 0 long double boost::math::detail::erf_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, bool, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, std::integral_constant<int, 113> const&)
PUBLIC 5cf0 0 boost::math::tools::promote_args<long double, float, float, float, float, float>::type boost::math::erf<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 5e00 0 boost_erfl
PUBLIC 5f10 0 boost_expm1l
PUBLIC 6360 0 boost_fmaxl
PUBLIC 63c0 0 boost_fminl
PUBLIC 6430 0 bool boost::math::tr1::signbit<long double>(long double)
PUBLIC 6450 0 int boost::math::tr1::fpclassify<long double>(long double)
PUBLIC 6540 0 bool boost::math::tr1::isfinite<long double>(long double)
PUBLIC 65b0 0 bool boost::math::tr1::isinf<long double>(long double)
PUBLIC 6620 0 bool boost::math::tr1::isnan<long double>(long double)
PUBLIC 6640 0 bool boost::math::tr1::isnormal<long double>(long double)
PUBLIC 6700 0 boost_hypotl
PUBLIC 68c0 0 boost_lgammal
PUBLIC 6fa0 0 boost::math::tools::promote_args<long double, float, float, float, float, float>::type boost::math::lgamma<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double, int*, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0] [clone .constprop.0]
PUBLIC 7670 0 long double boost::math::unchecked_factorial<long double>(unsigned int)
PUBLIC 76c0 0 long double boost::math::detail::lgamma_small_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos24m113>(long double, long double, long double, std::integral_constant<int, 113> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos24m113 const&)
PUBLIC 82d0 0 long double boost::math::detail::gamma_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos24m113>(long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos24m113 const&)
PUBLIC 8bc0 0 long double boost::math::detail::lgamma_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos24m113>(long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos24m113 const&, int*)
PUBLIC 9200 0 boost_llroundl
PUBLIC 9460 0 boost_log1pl
PUBLIC 94f0 0 boost_lroundl
PUBLIC 9750 0 boost_nextafterl
PUBLIC 9d30 0 long double boost::math::detail::float_next_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC a050 0 long double boost::math::detail::float_prior_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC a380 0 boost_nexttowardl
PUBLIC a960 0 boost_roundl
PUBLIC ab40 0 boost_tgammal
PUBLIC ac50 0 boost_truncl
PUBLIC ad40 0 _fini
STACK CFI INIT 20f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2120 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 215c 50 .cfa: sp 0 + .ra: x30
STACK CFI 216c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2174 x19: .cfa -16 + ^
STACK CFI 21a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21ac 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b0 318 .cfa: sp 0 + .ra: x30
STACK CFI 21bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 21d0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 22ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 23f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 23fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 17b0 3c .cfa: sp 0 + .ra: x30
STACK CFI 17b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17bc x19: .cfa -16 + ^
STACK CFI 17e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2dc0 38c .cfa: sp 0 + .ra: x30
STACK CFI 2dc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2ddc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2de4 x21: .cfa -64 + ^
STACK CFI 2e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e60 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 3100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3104 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 313c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3140 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3150 560 .cfa: sp 0 + .ra: x30
STACK CFI 3154 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3164 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 31fc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3294 x21: x21 x22: x22
STACK CFI 32a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 32e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 336c x21: x21 x22: x22
STACK CFI 33e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 342c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3430 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 3440 x21: x21 x22: x22
STACK CFI 34d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3504 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 3514 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 35e0 x23: x23 x24: x24
STACK CFI 35f8 x21: x21 x22: x22
STACK CFI 3600 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 361c x23: x23 x24: x24
STACK CFI 3630 x21: x21 x22: x22
STACK CFI 3638 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3644 x23: x23 x24: x24
STACK CFI 365c x21: x21 x22: x22
STACK CFI 3664 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3678 x23: x23 x24: x24
STACK CFI 368c x21: x21 x22: x22
STACK CFI 3694 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 24d0 8f0 .cfa: sp 0 + .ra: x30
STACK CFI 24d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 24e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 24f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2654 x25: .cfa -64 + ^
STACK CFI 2748 x25: x25
STACK CFI 27dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 27e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 286c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2870 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2a68 x25: .cfa -64 + ^
STACK CFI 2b78 x25: x25
STACK CFI 2b7c x25: .cfa -64 + ^
STACK CFI 2bb0 x25: x25
STACK CFI 2bcc x25: .cfa -64 + ^
STACK CFI 2bfc x25: x25
STACK CFI 2c28 x25: .cfa -64 + ^
STACK CFI 2d14 x25: x25
STACK CFI 2d30 x25: .cfa -64 + ^
STACK CFI INIT 17f0 54 .cfa: sp 0 + .ra: x30
STACK CFI 17f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17fc x19: .cfa -16 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 36b0 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 36bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 36cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3700 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3708 x23: .cfa -48 + ^
STACK CFI 3744 x21: x21 x22: x22
STACK CFI 3748 x23: x23
STACK CFI 3758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 375c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 37dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 37f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 3804 x21: x21 x22: x22
STACK CFI 3808 x23: x23
STACK CFI 381c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3830 x23: .cfa -48 + ^
STACK CFI 3940 x21: x21 x22: x22
STACK CFI 3944 x23: x23
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 395c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 3a78 x21: x21 x22: x22
STACK CFI 3a7c x23: x23
STACK CFI INIT 1850 3c .cfa: sp 0 + .ra: x30
STACK CFI 1854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 185c x19: .cfa -16 + ^
STACK CFI 1884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a80 498 .cfa: sp 0 + .ra: x30
STACK CFI 3a84 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3a94 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3aa4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b18 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 3b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b80 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 3b90 x23: .cfa -96 + ^
STACK CFI 3dd0 x23: x23
STACK CFI 3dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ddc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1890 3c .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 189c x19: .cfa -16 + ^
STACK CFI 18c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f20 60 .cfa: sp 0 + .ra: x30
STACK CFI 3f24 .cfa: sp 48 +
STACK CFI 3f4c .cfa: sp 0 +
STACK CFI 3f50 .cfa: sp 48 +
STACK CFI 3f7c .cfa: sp 0 +
STACK CFI INIT 18d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 18d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18dc x19: .cfa -16 + ^
STACK CFI 1904 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4090 1c5c .cfa: sp 0 + .ra: x30
STACK CFI 4098 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 40a0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 40a8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 40b4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 41a0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 4244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4248 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 42bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 42c0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 4788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 478c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 3f80 108 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f9c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4038 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4064 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1910 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1914 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 192c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 19c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 19f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 19f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19fc x19: .cfa -16 + ^
STACK CFI 1a38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5cf0 108 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5d0c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5da8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5e00 108 .cfa: sp 0 + .ra: x30
STACK CFI 5e04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5e1c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5eb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ee4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1af0 fc .cfa: sp 0 + .ra: x30
STACK CFI 1af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1afc x19: .cfa -16 + ^
STACK CFI 1b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f10 44c .cfa: sp 0 + .ra: x30
STACK CFI 5f14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5f30 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5fbc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 6280 x23: .cfa -64 + ^
STACK CFI 62d8 x23: x23
STACK CFI 62e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 62f8 x23: x23
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6314 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 6348 x23: .cfa -64 + ^
STACK CFI 6354 x23: x23
STACK CFI INIT 1bf0 54 .cfa: sp 0 + .ra: x30
STACK CFI 1bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bfc x19: .cfa -16 + ^
STACK CFI 1c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6360 60 .cfa: sp 0 + .ra: x30
STACK CFI 6364 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c50 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c5c x19: .cfa -16 + ^
STACK CFI 1c84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 63c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 641c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c90 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c9c x19: .cfa -16 + ^
STACK CFI 1cc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6430 1c .cfa: sp 0 + .ra: x30
STACK CFI 6434 .cfa: sp 16 +
STACK CFI 6440 .cfa: sp 0 +
STACK CFI INIT 6450 f0 .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6460 x21: .cfa -48 + ^
STACK CFI 6468 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 64d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 64dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 6528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 652c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 653c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6540 70 .cfa: sp 0 + .ra: x30
STACK CFI 6544 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 655c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6564 x21: .cfa -32 + ^
STACK CFI 65ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 65b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 65b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 65cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 65d4 x21: .cfa -32 + ^
STACK CFI 661c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6620 20 .cfa: sp 0 + .ra: x30
STACK CFI 6624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 663c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6640 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6644 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 665c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6664 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 66f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1cd0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cdc x19: .cfa -16 + ^
STACK CFI 1d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6700 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 6704 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6710 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6718 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6724 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 674c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 67f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 67f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 68b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1d10 3c .cfa: sp 0 + .ra: x30
STACK CFI 1d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d1c x19: .cfa -16 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7670 48 .cfa: sp 0 + .ra: x30
STACK CFI 7674 .cfa: sp 2768 +
STACK CFI 7688 .ra: .cfa -2760 + ^ x29: .cfa -2768 + ^
STACK CFI 7690 x19: .cfa -2752 + ^
STACK CFI 76b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 76c0 c04 .cfa: sp 0 + .ra: x30
STACK CFI 76c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 76e4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 775c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI 7788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 778c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 82d0 8ec .cfa: sp 0 + .ra: x30
STACK CFI 82d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 82e0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 83f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 83f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 840c x23: .cfa -80 + ^
STACK CFI 8598 x23: x23
STACK CFI 85b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 85f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 8704 x23: .cfa -80 + ^
STACK CFI 87ac x23: x23
STACK CFI 87b0 x23: .cfa -80 + ^
STACK CFI 88c8 x23: x23
STACK CFI 88d0 x23: .cfa -80 + ^
STACK CFI 8928 x23: x23
STACK CFI 898c x23: .cfa -80 + ^
STACK CFI 89e8 x23: x23
STACK CFI 8a04 x23: .cfa -80 + ^
STACK CFI 8b84 x23: x23
STACK CFI 8b88 x23: .cfa -80 + ^
STACK CFI 8ba8 x23: x23
STACK CFI 8bac x23: .cfa -80 + ^
STACK CFI INIT 8bc0 63c .cfa: sp 0 + .ra: x30
STACK CFI 8bc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8bcc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8be0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8bf0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 8c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8c5c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 8d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8d98 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI INIT 68c0 6d4 .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 68e4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 697c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6980 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 6b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b3c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 6cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6cb4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 6f0c x25: .cfa -96 + ^
STACK CFI 6f90 x25: x25
STACK CFI INIT 6fa0 6d0 .cfa: sp 0 + .ra: x30
STACK CFI 6fac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6fc4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 705c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7060 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 721c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 7390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7394 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 75ec x25: .cfa -96 + ^
STACK CFI 766c x25: x25
STACK CFI INIT 1d50 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1d54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d5c x19: .cfa -16 + ^
STACK CFI 1da4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9200 260 .cfa: sp 0 + .ra: x30
STACK CFI 9204 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 921c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9224 x21: .cfa -48 + ^
STACK CFI 9338 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 933c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 9378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 937c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 945c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1e00 3c .cfa: sp 0 + .ra: x30
STACK CFI 1e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e0c x19: .cfa -16 + ^
STACK CFI 1e34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9460 84 .cfa: sp 0 + .ra: x30
STACK CFI 946c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 94b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 94d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e40 3c .cfa: sp 0 + .ra: x30
STACK CFI 1e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e4c x19: .cfa -16 + ^
STACK CFI 1e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 94f0 260 .cfa: sp 0 + .ra: x30
STACK CFI 94f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 950c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9514 x21: .cfa -48 + ^
STACK CFI 9628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 962c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 9668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 966c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 974c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1e80 3c .cfa: sp 0 + .ra: x30
STACK CFI 1e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e8c x19: .cfa -16 + ^
STACK CFI 1eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9d30 320 .cfa: sp 0 + .ra: x30
STACK CFI 9d34 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9d3c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9d4c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9d58 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9d64 x25: .cfa -80 + ^
STACK CFI 9e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9e04 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 9ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9ed0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 9fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9fb0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 9fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9fd4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI a010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a014 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT a050 330 .cfa: sp 0 + .ra: x30
STACK CFI a054 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a05c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a06c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a078 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a084 x25: .cfa -80 + ^
STACK CFI a118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a11c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI a1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a1ec .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI a2cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a2d0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI a2fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a300 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI a340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a344 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9750 5e0 .cfa: sp 0 + .ra: x30
STACK CFI 9754 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9764 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 97a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 97a8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 97c0 x23: .cfa -80 + ^
STACK CFI 9818 x21: x21 x22: x22
STACK CFI 981c x23: x23
STACK CFI 9824 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 983c x23: .cfa -80 + ^
STACK CFI 9894 x21: x21 x22: x22
STACK CFI 989c x23: x23
STACK CFI 98b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 98b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 9934 x21: x21 x22: x22
STACK CFI 9938 x23: x23
STACK CFI 993c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 99c0 x21: x21 x22: x22
STACK CFI 99c4 x23: x23
STACK CFI 99c8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 99f8 x21: x21 x22: x22
STACK CFI 99fc x23: x23
STACK CFI 9a04 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 9b30 x21: x21 x22: x22
STACK CFI 9b34 x23: x23
STACK CFI 9b3c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 9b48 x21: x21 x22: x22
STACK CFI 9b4c x23: x23
STACK CFI 9b50 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 9c2c x21: x21 x22: x22
STACK CFI 9c30 x23: x23
STACK CFI 9c38 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 1ec0 98 .cfa: sp 0 + .ra: x30
STACK CFI 1ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ecc x19: .cfa -16 + ^
STACK CFI 1f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1f54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a380 5e0 .cfa: sp 0 + .ra: x30
STACK CFI a384 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a394 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a3d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI a3d8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a3f0 x23: .cfa -80 + ^
STACK CFI a448 x21: x21 x22: x22
STACK CFI a44c x23: x23
STACK CFI a454 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a46c x23: .cfa -80 + ^
STACK CFI a4c4 x21: x21 x22: x22
STACK CFI a4cc x23: x23
STACK CFI a4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a4e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI a564 x21: x21 x22: x22
STACK CFI a568 x23: x23
STACK CFI a56c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI a5f0 x21: x21 x22: x22
STACK CFI a5f4 x23: x23
STACK CFI a5f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI a628 x21: x21 x22: x22
STACK CFI a62c x23: x23
STACK CFI a634 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI a760 x21: x21 x22: x22
STACK CFI a764 x23: x23
STACK CFI a76c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI a778 x21: x21 x22: x22
STACK CFI a77c x23: x23
STACK CFI a780 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI a85c x21: x21 x22: x22
STACK CFI a860 x23: x23
STACK CFI a868 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 1f60 98 .cfa: sp 0 + .ra: x30
STACK CFI 1f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f6c x19: .cfa -16 + ^
STACK CFI 1fa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1fac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a960 1e0 .cfa: sp 0 + .ra: x30
STACK CFI a964 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a97c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a984 x21: .cfa -48 + ^
STACK CFI aa50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aa54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI aaa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aaac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2000 3c .cfa: sp 0 + .ra: x30
STACK CFI 2004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 200c x19: .cfa -16 + ^
STACK CFI 2034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab40 104 .cfa: sp 0 + .ra: x30
STACK CFI ab44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ab58 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI abf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI abf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI ac1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ac20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI ac40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2040 58 .cfa: sp 0 + .ra: x30
STACK CFI 2044 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 204c x19: .cfa -16 + ^
STACK CFI 2088 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 208c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT ac50 f0 .cfa: sp 0 + .ra: x30
STACK CFI ac54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ac6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ac74 x21: .cfa -32 + ^
STACK CFI acd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI acd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI ad10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ad14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI ad2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ad30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI ad3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 20a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 20a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20ac x19: .cfa -16 + ^
STACK CFI 20d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
