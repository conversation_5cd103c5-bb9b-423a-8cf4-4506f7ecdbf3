MODULE Linux arm64 0E7CB412D3DD7C1B676F9C18182ECD490 libavahi-client.so.3
INFO CODE_ID 12B47C0EDDD31B7C676F9C18182ECD497B710F96
PUBLIC 3bd8 0 avahi_client_set_errno
PUBLIC 3c18 0 avahi_client_set_dbus_error
PUBLIC 3e50 0 avahi_client_free
PUBLIC 3f88 0 avahi_client_new
PUBLIC 42b8 0 avahi_client_get_state
PUBLIC 42f0 0 avahi_client_errno
PUBLIC 4328 0 avahi_client_simple_method_call
PUBLIC 4570 0 avahi_client_is_connected
PUBLIC 4fc0 0 avahi_client_get_version_string
PUBLIC 5048 0 avahi_client_get_domain_name
PUBLIC 50d0 0 avahi_client_get_host_name
PUBLIC 5158 0 avahi_client_get_host_name_fqdn
PUBLIC 51e0 0 avahi_client_get_local_service_cookie
PUBLIC 5378 0 avahi_client_set_host_name
PUBLIC 59e8 0 avahi_entry_group_set_state
PUBLIC 5a50 0 avahi_entry_group_free
PUBLIC 5b18 0 avahi_entry_group_new
PUBLIC 5d88 0 avahi_entry_group_commit
PUBLIC 5e10 0 avahi_entry_group_reset
PUBLIC 5e98 0 avahi_entry_group_get_state
PUBLIC 5ee0 0 avahi_entry_group_get_client
PUBLIC 5f18 0 avahi_entry_group_is_empty
PUBLIC 6128 0 avahi_entry_group_add_service_strlst
PUBLIC 6498 0 avahi_entry_group_add_service
PUBLIC 65b8 0 avahi_entry_group_add_service_subtype
PUBLIC 6930 0 avahi_entry_group_update_service_txt_strlst
PUBLIC 6c70 0 avahi_entry_group_update_service_txt
PUBLIC 6d50 0 avahi_entry_group_add_address
PUBLIC 7058 0 avahi_entry_group_add_record
PUBLIC 75b0 0 avahi_domain_browser_get_client
PUBLIC 75e8 0 avahi_domain_browser_free
PUBLIC 7760 0 avahi_domain_browser_new
PUBLIC 7ba8 0 avahi_domain_browser_event
PUBLIC 7dd0 0 avahi_service_type_browser_get_client
PUBLIC 7e08 0 avahi_service_type_browser_free
PUBLIC 7f18 0 avahi_service_type_browser_new
PUBLIC 8238 0 avahi_service_type_browser_event
PUBLIC 8428 0 avahi_service_browser_get_client
PUBLIC 8460 0 avahi_service_browser_free
PUBLIC 8578 0 avahi_service_browser_new
PUBLIC 88e8 0 avahi_service_browser_event
PUBLIC 8aa0 0 avahi_record_browser_get_client
PUBLIC 8ad8 0 avahi_record_browser_free
PUBLIC 8be8 0 avahi_record_browser_new
PUBLIC 8f38 0 avahi_record_browser_event
PUBLIC 91a0 0 avahi_service_resolver_event
PUBLIC 9668 0 avahi_service_resolver_get_client
PUBLIC 96a0 0 avahi_service_resolver_free
PUBLIC 97b0 0 avahi_service_resolver_new
PUBLIC 9b50 0 avahi_host_name_resolver_event
PUBLIC 9de0 0 avahi_host_name_resolver_free
PUBLIC 9ee0 0 avahi_host_name_resolver_new
PUBLIC a1d8 0 avahi_host_name_resolver_get_client
PUBLIC a210 0 avahi_address_resolver_event
PUBLIC a4a0 0 avahi_address_resolver_get_client
PUBLIC a4d8 0 avahi_address_resolver_free
PUBLIC a5d0 0 avahi_address_resolver_new
PUBLIC a908 0 avahi_xdg_config_open
PUBLIC ab58 0 avahi_nss_support
PUBLIC aba0 0 avahi_error_dbus_to_number
PUBLIC ac80 0 avahi_error_number_to_dbus
PUBLIC b7b8 0 avahi_dbus_connection_glue
STACK CFI INIT 36d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3708 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3748 48 .cfa: sp 0 + .ra: x30
STACK CFI 374c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3754 x19: .cfa -16 + ^
STACK CFI 378c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3798 d0 .cfa: sp 0 + .ra: x30
STACK CFI 379c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3848 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3868 36c .cfa: sp 0 + .ra: x30
STACK CFI 386c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3884 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 39fc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI 3a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3a6c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3bd8 40 .cfa: sp 0 + .ra: x30
STACK CFI 3bf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c18 7c .cfa: sp 0 + .ra: x30
STACK CFI 3c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c24 x19: .cfa -16 + ^
STACK CFI 3c48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c98 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 3c9c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3cac x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 3d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3d6c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3de4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3e50 134 .cfa: sp 0 + .ra: x30
STACK CFI 3e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e5c x19: .cfa -16 + ^
STACK CFI 3f5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3f60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f88 32c .cfa: sp 0 + .ra: x30
STACK CFI 3f8c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3f94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3f9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3fa8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3fb4 x25: .cfa -48 + ^
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4098 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 412c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4130 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 42b8 38 .cfa: sp 0 + .ra: x30
STACK CFI 42c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 42f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4300 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4328 244 .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4334 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4340 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4350 x23: .cfa -48 + ^
STACK CFI 43f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 43f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 443c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4440 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 4494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4498 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4570 74 .cfa: sp 0 + .ra: x30
STACK CFI 4574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 457c x19: .cfa -16 + ^
STACK CFI 45a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 45bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45e8 9d4 .cfa: sp 0 + .ra: x30
STACK CFI 45ec .cfa: sp 192 +
STACK CFI 45f0 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 45f8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4600 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4608 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4668 x19: x19 x20: x20
STACK CFI 466c x21: x21 x22: x22
STACK CFI 4670 x23: x23 x24: x24
STACK CFI 4674 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4678 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 474c x19: x19 x20: x20
STACK CFI 4750 x21: x21 x22: x22
STACK CFI 4754 x23: x23 x24: x24
STACK CFI 4758 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 475c .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 48e0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 493c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4b84 x25: x25 x26: x26
STACK CFI 4b88 x27: x27 x28: x28
STACK CFI 4bcc x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4be4 x27: x27 x28: x28
STACK CFI 4be8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c00 x27: x27 x28: x28
STACK CFI 4c14 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c2c x27: x27 x28: x28
STACK CFI 4c30 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c48 x25: x25 x26: x26
STACK CFI 4c4c x27: x27 x28: x28
STACK CFI 4c50 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c68 x25: x25 x26: x26
STACK CFI 4c6c x27: x27 x28: x28
STACK CFI 4c7c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c94 x25: x25 x26: x26
STACK CFI 4c98 x27: x27 x28: x28
STACK CFI 4cc0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4cc4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4cc8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4cec x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4cf0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4cf4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4cf8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4d14 x25: x25 x26: x26
STACK CFI 4d18 x27: x27 x28: x28
STACK CFI 4d1c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4d34 x25: x25 x26: x26
STACK CFI 4d38 x27: x27 x28: x28
STACK CFI 4d3c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4d54 x25: x25 x26: x26
STACK CFI 4d58 x27: x27 x28: x28
STACK CFI 4d5c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4d74 x25: x25 x26: x26
STACK CFI 4d78 x27: x27 x28: x28
STACK CFI 4d7c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4dd4 x25: x25 x26: x26
STACK CFI 4dd8 x27: x27 x28: x28
STACK CFI 4ddc x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4df4 x25: x25 x26: x26
STACK CFI 4df8 x27: x27 x28: x28
STACK CFI 4dfc x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e14 x25: x25 x26: x26
STACK CFI 4e18 x27: x27 x28: x28
STACK CFI 4e1c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e34 x25: x25 x26: x26
STACK CFI 4e38 x27: x27 x28: x28
STACK CFI 4e3c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e54 x25: x25 x26: x26
STACK CFI 4e58 x27: x27 x28: x28
STACK CFI 4e5c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e74 x25: x25 x26: x26
STACK CFI 4e78 x27: x27 x28: x28
STACK CFI 4e7c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e94 x25: x25 x26: x26
STACK CFI 4e98 x27: x27 x28: x28
STACK CFI 4e9c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4eb4 x25: x25 x26: x26
STACK CFI 4eb8 x27: x27 x28: x28
STACK CFI 4ebc x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4ed4 x25: x25 x26: x26
STACK CFI 4ed8 x27: x27 x28: x28
STACK CFI 4edc x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4ef4 x25: x25 x26: x26
STACK CFI 4ef8 x27: x27 x28: x28
STACK CFI 4efc x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f14 x25: x25 x26: x26
STACK CFI 4f18 x27: x27 x28: x28
STACK CFI 4f1c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f34 x25: x25 x26: x26
STACK CFI 4f38 x27: x27 x28: x28
STACK CFI 4f3c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f54 x25: x25 x26: x26
STACK CFI 4f58 x27: x27 x28: x28
STACK CFI 4f5c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f74 x25: x25 x26: x26
STACK CFI 4f78 x27: x27 x28: x28
STACK CFI 4f7c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f94 x25: x25 x26: x26
STACK CFI 4f98 x27: x27 x28: x28
STACK CFI 4f9c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4fb4 x25: x25 x26: x26
STACK CFI 4fb8 x27: x27 x28: x28
STACK CFI INIT 4fc0 88 .cfa: sp 0 + .ra: x30
STACK CFI 4fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fcc x19: .cfa -16 + ^
STACK CFI 4fec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ff0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 500c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5010 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5048 88 .cfa: sp 0 + .ra: x30
STACK CFI 504c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5054 x19: .cfa -16 + ^
STACK CFI 5074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5078 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5094 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5098 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 50d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 50d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50dc x19: .cfa -16 + ^
STACK CFI 50fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5100 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 511c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5120 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5158 88 .cfa: sp 0 + .ra: x30
STACK CFI 515c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5164 x19: .cfa -16 + ^
STACK CFI 5184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5188 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 51e0 198 .cfa: sp 0 + .ra: x30
STACK CFI 51e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 51ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 520c x19: x19 x20: x20
STACK CFI 5210 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5214 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5218 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5298 x19: x19 x20: x20
STACK CFI 529c x21: x21 x22: x22
STACK CFI 52a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 52b8 x19: x19 x20: x20
STACK CFI 52bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5308 x21: x21 x22: x22
STACK CFI 530c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5328 x19: x19 x20: x20
STACK CFI 532c x21: x21 x22: x22
STACK CFI 5330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5334 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5350 x21: x21 x22: x22
STACK CFI 5374 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5378 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 537c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5384 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 539c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5430 x21: x21 x22: x22
STACK CFI 5438 x19: x19 x20: x20
STACK CFI 543c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5440 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 5450 x19: x19 x20: x20
STACK CFI 5454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5458 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 54d8 x21: x21 x22: x22
STACK CFI 54dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 54f8 x21: x21 x22: x22
STACK CFI 551c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 5520 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 5524 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 552c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5540 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 55dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 55e0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 5630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5634 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 5688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 568c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 56f0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 56f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 56fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 570c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5714 x23: .cfa -48 + ^
STACK CFI 57ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 57b0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 57fc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 5850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5854 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 58c0 128 .cfa: sp 0 + .ra: x30
STACK CFI 58c4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 58d8 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 59a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 59ac .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI INIT 59e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5a50 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5a54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a64 x21: .cfa -16 + ^
STACK CFI 5ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b18 26c .cfa: sp 0 + .ra: x30
STACK CFI 5b1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5b2c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 5c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5c30 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 5c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5c84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5d88 84 .cfa: sp 0 + .ra: x30
STACK CFI 5d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d94 x19: .cfa -16 + ^
STACK CFI 5dc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5de4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e10 84 .cfa: sp 0 + .ra: x30
STACK CFI 5e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e1c x19: .cfa -16 + ^
STACK CFI 5e48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e98 44 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5ee0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5ef0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5f18 210 .cfa: sp 0 + .ra: x30
STACK CFI 5f1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5f24 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5f60 x19: x19 x20: x20
STACK CFI 5f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f68 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 5f6c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5fa4 x23: .cfa -64 + ^
STACK CFI 6000 x19: x19 x20: x20
STACK CFI 6004 x21: x21 x22: x22
STACK CFI 6008 x23: x23
STACK CFI 600c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6010 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 6050 x21: x21 x22: x22
STACK CFI 605c x23: x23
STACK CFI 6060 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 6088 x21: x21 x22: x22
STACK CFI 608c x23: x23
STACK CFI 6090 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 60b0 x21: x21 x22: x22
STACK CFI 60b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 60dc x23: x23
STACK CFI 60f8 x21: x21 x22: x22
STACK CFI 6120 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6124 x23: .cfa -64 + ^
STACK CFI INIT 6128 370 .cfa: sp 0 + .ra: x30
STACK CFI 612c .cfa: sp 240 +
STACK CFI 6130 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6138 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 613c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6168 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6174 x25: .cfa -96 + ^
STACK CFI 62a0 x23: x23 x24: x24
STACK CFI 62a4 x25: x25
STACK CFI 62a8 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 6300 x19: x19 x20: x20
STACK CFI 6304 x21: x21 x22: x22
STACK CFI 6308 x23: x23 x24: x24
STACK CFI 630c x25: x25
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6314 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6318 x23: x23 x24: x24
STACK CFI 6320 x25: x25
STACK CFI 633c x19: x19 x20: x20
STACK CFI 6340 x21: x21 x22: x22
STACK CFI 6344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6348 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6380 x19: x19 x20: x20
STACK CFI 6384 x21: x21 x22: x22
STACK CFI 6388 x23: x23 x24: x24
STACK CFI 638c x25: x25
STACK CFI 6390 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6394 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 63b4 x23: x23 x24: x24
STACK CFI 63b8 x25: x25
STACK CFI 63bc x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 640c x23: x23 x24: x24
STACK CFI 6410 x25: x25
STACK CFI 6438 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 643c x25: .cfa -96 + ^
STACK CFI 6440 x23: x23 x24: x24 x25: x25
STACK CFI 6464 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6468 x25: .cfa -96 + ^
STACK CFI 646c x23: x23 x24: x24 x25: x25
STACK CFI 6490 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6494 x25: .cfa -96 + ^
STACK CFI INIT 6498 11c .cfa: sp 0 + .ra: x30
STACK CFI 649c .cfa: sp 304 +
STACK CFI 64a0 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 64b8 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^
STACK CFI 658c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6590 .cfa: sp 304 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x29: .cfa -288 + ^
STACK CFI INIT 65b8 378 .cfa: sp 0 + .ra: x30
STACK CFI 65bc .cfa: sp 224 +
STACK CFI 65c0 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 65c8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 65cc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 65fc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6608 x25: .cfa -96 + ^
STACK CFI 670c x19: x19 x20: x20
STACK CFI 6710 x21: x21 x22: x22
STACK CFI 6714 x23: x23 x24: x24
STACK CFI 6718 x25: x25
STACK CFI 671c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6720 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6724 x23: x23 x24: x24
STACK CFI 672c x25: x25
STACK CFI 6748 x19: x19 x20: x20
STACK CFI 674c x21: x21 x22: x22
STACK CFI 6750 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6754 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 679c x23: x23 x24: x24
STACK CFI 67a0 x25: x25
STACK CFI 67a4 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 67c4 x23: x23 x24: x24
STACK CFI 67c8 x25: x25
STACK CFI 67cc x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 6828 x19: x19 x20: x20
STACK CFI 682c x21: x21 x22: x22
STACK CFI 6830 x23: x23 x24: x24
STACK CFI 6834 x25: x25
STACK CFI 6838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 683c .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6878 x23: x23 x24: x24
STACK CFI 687c x25: x25
STACK CFI 68a4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 68a8 x25: .cfa -96 + ^
STACK CFI 68ac x23: x23 x24: x24 x25: x25
STACK CFI 68d0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 68d4 x25: .cfa -96 + ^
STACK CFI 68d8 x23: x23 x24: x24 x25: x25
STACK CFI 68fc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6900 x25: .cfa -96 + ^
STACK CFI 6904 x23: x23 x24: x24 x25: x25
STACK CFI 6928 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 692c x25: .cfa -96 + ^
STACK CFI INIT 6930 33c .cfa: sp 0 + .ra: x30
STACK CFI 6934 .cfa: sp 208 +
STACK CFI 6938 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6940 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6944 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6970 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 697c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6a88 x23: x23 x24: x24
STACK CFI 6a8c x25: x25 x26: x26
STACK CFI 6a90 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6ae8 x19: x19 x20: x20
STACK CFI 6aec x21: x21 x22: x22
STACK CFI 6af0 x23: x23 x24: x24
STACK CFI 6af4 x25: x25 x26: x26
STACK CFI 6af8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6afc .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6b00 x23: x23 x24: x24
STACK CFI 6b04 x25: x25 x26: x26
STACK CFI 6b24 x19: x19 x20: x20
STACK CFI 6b28 x21: x21 x22: x22
STACK CFI 6b2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b30 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6b54 x19: x19 x20: x20
STACK CFI 6b58 x21: x21 x22: x22
STACK CFI 6b5c x23: x23 x24: x24
STACK CFI 6b60 x25: x25 x26: x26
STACK CFI 6b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b68 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6b88 x23: x23 x24: x24
STACK CFI 6b8c x25: x25 x26: x26
STACK CFI 6b90 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6be0 x23: x23 x24: x24
STACK CFI 6be4 x25: x25 x26: x26
STACK CFI 6c0c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6c10 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6c14 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6c38 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6c3c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6c40 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6c64 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6c68 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT 6c70 dc .cfa: sp 0 + .ra: x30
STACK CFI 6c74 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6c9c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6cb0 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6cbc x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6cc8 x25: .cfa -224 + ^
STACK CFI 6d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 6d50 304 .cfa: sp 0 + .ra: x30
STACK CFI 6d54 .cfa: sp 256 +
STACK CFI 6d58 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6d60 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6d6c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 6d80 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6da8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 6db0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6dc4 x23: x23 x24: x24
STACK CFI 6dc8 x25: x25 x26: x26
STACK CFI 6e04 x21: x21 x22: x22
STACK CFI 6e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 6e10 .cfa: sp 256 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 6f00 x23: x23 x24: x24
STACK CFI 6f04 x25: x25 x26: x26
STACK CFI 6f08 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6f30 x23: x23 x24: x24
STACK CFI 6f34 x25: x25 x26: x26
STACK CFI 6f38 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6f68 x23: x23 x24: x24
STACK CFI 6f6c x25: x25 x26: x26
STACK CFI 6f70 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6fbc x23: x23 x24: x24
STACK CFI 6fc0 x25: x25 x26: x26
STACK CFI 6fc4 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7014 x23: x23 x24: x24
STACK CFI 7018 x25: x25 x26: x26
STACK CFI 7040 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7044 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7048 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 704c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7050 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 7058 328 .cfa: sp 0 + .ra: x30
STACK CFI 705c .cfa: sp 368 +
STACK CFI 7060 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7068 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 706c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 7098 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 70a4 x25: .cfa -240 + ^
STACK CFI 70b4 x23: x23 x24: x24
STACK CFI 70bc x25: x25
STACK CFI 70d8 x19: x19 x20: x20
STACK CFI 70dc x21: x21 x22: x22
STACK CFI 70e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 70e4 .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 7238 x23: x23 x24: x24
STACK CFI 723c x25: x25
STACK CFI 7240 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 7298 x19: x19 x20: x20
STACK CFI 729c x21: x21 x22: x22
STACK CFI 72a0 x23: x23 x24: x24
STACK CFI 72a4 x25: x25
STACK CFI 72a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72ac .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 72c0 x19: x19 x20: x20
STACK CFI 72c4 x21: x21 x22: x22
STACK CFI 72c8 x23: x23 x24: x24
STACK CFI 72cc x25: x25
STACK CFI 72d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72d4 .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 731c x23: x23 x24: x24
STACK CFI 7320 x25: x25
STACK CFI 7324 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 734c x23: x23 x24: x24
STACK CFI 7350 x25: x25
STACK CFI 7378 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 737c x25: .cfa -240 + ^
STACK CFI INIT 7380 10c .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 2112 +
STACK CFI 7388 .ra: .cfa -2104 + ^ x29: .cfa -2112 + ^
STACK CFI 7390 x23: .cfa -2064 + ^ x24: .cfa -2056 + ^
STACK CFI 7398 x21: .cfa -2080 + ^ x22: .cfa -2072 + ^
STACK CFI 73a4 x19: .cfa -2096 + ^ x20: .cfa -2088 + ^
STACK CFI 7464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7468 .cfa: sp 2112 + .ra: .cfa -2104 + ^ x19: .cfa -2096 + ^ x20: .cfa -2088 + ^ x21: .cfa -2080 + ^ x22: .cfa -2072 + ^ x23: .cfa -2064 + ^ x24: .cfa -2056 + ^ x29: .cfa -2112 + ^
STACK CFI INIT 7490 120 .cfa: sp 0 + .ra: x30
STACK CFI 7498 .cfa: sp 4144 +
STACK CFI 749c .ra: .cfa -4136 + ^ x29: .cfa -4144 + ^
STACK CFI 74a4 x21: .cfa -4112 + ^ x22: .cfa -4104 + ^
STACK CFI 74ac x19: .cfa -4128 + ^ x20: .cfa -4120 + ^
STACK CFI 74dc x23: .cfa -4096 + ^
STACK CFI 7550 x23: x23
STACK CFI 757c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7580 .cfa: sp 4144 + .ra: .cfa -4136 + ^ x19: .cfa -4128 + ^ x20: .cfa -4120 + ^ x21: .cfa -4112 + ^ x22: .cfa -4104 + ^ x29: .cfa -4144 + ^
STACK CFI 75a4 x23: .cfa -4096 + ^
STACK CFI 75a8 x23: x23
STACK CFI 75ac x23: .cfa -4096 + ^
STACK CFI INIT 75b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 75c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 75e8 174 .cfa: sp 0 + .ra: x30
STACK CFI 75ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 75f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7618 x21: .cfa -16 + ^
STACK CFI 7684 x19: x19 x20: x20
STACK CFI 7688 x21: x21
STACK CFI 768c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 769c x19: x19 x20: x20
STACK CFI 76a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 76e8 x21: x21
STACK CFI 770c x21: .cfa -16 + ^
STACK CFI 7710 x21: x21
STACK CFI 7734 x21: .cfa -16 + ^
STACK CFI INIT 7760 370 .cfa: sp 0 + .ra: x30
STACK CFI 7764 .cfa: sp 224 +
STACK CFI 7768 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7780 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^
STACK CFI 7958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 795c .cfa: sp 224 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7ad0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7adc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ba8 224 .cfa: sp 0 + .ra: x30
STACK CFI 7bac .cfa: sp 160 +
STACK CFI 7bb0 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7bc0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 7cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7cc4 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 7d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7d40 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 7d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7d74 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7dd0 38 .cfa: sp 0 + .ra: x30
STACK CFI 7de0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7e08 10c .cfa: sp 0 + .ra: x30
STACK CFI 7e0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e18 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7f18 320 .cfa: sp 0 + .ra: x30
STACK CFI 7f1c .cfa: sp 176 +
STACK CFI 7f20 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7f34 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 809c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 80a0 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 80ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 80f0 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8238 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 823c .cfa: sp 192 +
STACK CFI 8240 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8250 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 8394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8398 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 83dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 83e0 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8428 38 .cfa: sp 0 + .ra: x30
STACK CFI 8438 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8460 114 .cfa: sp 0 + .ra: x30
STACK CFI 8464 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8470 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 84e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 84ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8578 370 .cfa: sp 0 + .ra: x30
STACK CFI 857c .cfa: sp 192 +
STACK CFI 8580 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8594 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 8724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8728 .cfa: sp 192 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 8774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8778 .cfa: sp 192 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 88e8 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 88ec .cfa: sp 208 +
STACK CFI 88f0 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 88f8 x23: .cfa -96 + ^
STACK CFI 8900 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 890c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8a4c .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 8a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8aa0 38 .cfa: sp 0 + .ra: x30
STACK CFI 8ab0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8ad8 10c .cfa: sp 0 + .ra: x30
STACK CFI 8adc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ae8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 8b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8be8 350 .cfa: sp 0 + .ra: x30
STACK CFI 8bec .cfa: sp 208 +
STACK CFI 8bf0 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8c04 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 8da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8da4 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 8df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8dfc .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8f38 268 .cfa: sp 0 + .ra: x30
STACK CFI 8f3c .cfa: sp 336 +
STACK CFI 8f40 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 8f48 x23: .cfa -240 + ^
STACK CFI 8f50 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 8f5c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 9068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 906c .cfa: sp 336 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x29: .cfa -288 + ^
STACK CFI 90f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 90f4 .cfa: sp 336 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x29: .cfa -288 + ^
STACK CFI INIT 91a0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 91a4 .cfa: sp 560 +
STACK CFI 91a8 .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 91b0 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 91bc x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 91d0 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 9310 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 931c x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 93dc x25: x25 x26: x26
STACK CFI 93e0 x27: x27 x28: x28
STACK CFI 9424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9428 .cfa: sp 560 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x29: .cfa -448 + ^
STACK CFI 94f8 x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 951c x25: x25 x26: x26
STACK CFI 9520 x27: x27 x28: x28
STACK CFI 9524 x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 9590 x25: x25 x26: x26
STACK CFI 9594 x27: x27 x28: x28
STACK CFI 9598 x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 95c8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 95e8 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 95ec x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 95f0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9610 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 9614 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 9618 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 961c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 9620 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 9668 38 .cfa: sp 0 + .ra: x30
STACK CFI 9678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 96a0 110 .cfa: sp 0 + .ra: x30
STACK CFI 96a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 96b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 972c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 97b0 39c .cfa: sp 0 + .ra: x30
STACK CFI 97b4 .cfa: sp 240 +
STACK CFI 97b8 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 97cc x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 9994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9998 .cfa: sp 240 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 9a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9a04 .cfa: sp 240 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9b50 28c .cfa: sp 0 + .ra: x30
STACK CFI 9b54 .cfa: sp 224 +
STACK CFI 9b58 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9b60 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9b6c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9b74 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9c40 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9de0 100 .cfa: sp 0 + .ra: x30
STACK CFI 9de4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9df0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ee0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 9ee4 .cfa: sp 208 +
STACK CFI 9ee8 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 9f00 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI a07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a080 .cfa: sp 208 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI INIT a1d8 38 .cfa: sp 0 + .ra: x30
STACK CFI a1e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a210 290 .cfa: sp 0 + .ra: x30
STACK CFI a214 .cfa: sp 224 +
STACK CFI a218 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI a220 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI a22c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI a234 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI a2fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a300 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT a4a0 38 .cfa: sp 0 + .ra: x30
STACK CFI a4b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a4d8 f8 .cfa: sp 0 + .ra: x30
STACK CFI a4dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI a548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a54c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a5d0 338 .cfa: sp 0 + .ra: x30
STACK CFI a5d4 .cfa: sp 224 +
STACK CFI a5d8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI a5e0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI a5ec x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI a5fc x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI a7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a7a4 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT a908 24c .cfa: sp 0 + .ra: x30
STACK CFI a910 .cfa: sp 6240 +
STACK CFI a914 .ra: .cfa -6232 + ^ x29: .cfa -6240 + ^
STACK CFI a91c x23: .cfa -6192 + ^ x24: .cfa -6184 + ^
STACK CFI a928 x19: .cfa -6224 + ^ x20: .cfa -6216 + ^
STACK CFI a93c x21: .cfa -6208 + ^ x22: .cfa -6200 + ^
STACK CFI a9ec x25: .cfa -6176 + ^
STACK CFI aa40 x25: x25
STACK CFI aa6c x21: x21 x22: x22
STACK CFI aa74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI aa78 .cfa: sp 6240 + .ra: .cfa -6232 + ^ x19: .cfa -6224 + ^ x20: .cfa -6216 + ^ x21: .cfa -6208 + ^ x22: .cfa -6200 + ^ x23: .cfa -6192 + ^ x24: .cfa -6184 + ^ x29: .cfa -6240 + ^
STACK CFI aa84 x25: .cfa -6176 + ^
STACK CFI aa88 x25: x25
STACK CFI aa98 x25: .cfa -6176 + ^
STACK CFI aabc x25: x25
STACK CFI ab2c x25: .cfa -6176 + ^
STACK CFI ab30 x25: x25
STACK CFI ab50 x25: .cfa -6176 + ^
STACK CFI INIT ab58 48 .cfa: sp 0 + .ra: x30
STACK CFI ab5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab6c x19: .cfa -16 + ^
STACK CFI ab9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aba0 dc .cfa: sp 0 + .ra: x30
STACK CFI aba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI abb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI ac00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ac04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ac40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ac44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ac58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ac5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT ac80 78 .cfa: sp 0 + .ra: x30
STACK CFI ac84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aca8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI acb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT acf8 180 .cfa: sp 0 + .ra: x30
STACK CFI acfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ad04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ad08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ad68 x19: x19 x20: x20
STACK CFI ad6c x21: x21 x22: x22
STACK CFI ad70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ad74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI ad9c x19: x19 x20: x20
STACK CFI ada0 x21: x21 x22: x22
STACK CFI ada4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ada8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI adac x23: .cfa -16 + ^
STACK CFI ae2c x19: x19 x20: x20
STACK CFI ae30 x21: x21 x22: x22
STACK CFI ae34 x23: x23
STACK CFI ae38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ae3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI ae44 x19: x19 x20: x20
STACK CFI ae48 x21: x21 x22: x22
STACK CFI ae4c x23: x23
STACK CFI ae50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ae54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI ae74 x23: .cfa -16 + ^
STACK CFI INIT ae78 144 .cfa: sp 0 + .ra: x30
STACK CFI ae7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ae88 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI af14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI af18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI af70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI af74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT afc0 70 .cfa: sp 0 + .ra: x30
STACK CFI afc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI afe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI afe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b030 ac .cfa: sp 0 + .ra: x30
STACK CFI b034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b03c x19: .cfa -16 + ^
STACK CFI b058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b064 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b084 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b094 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b0e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b0f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI b0fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b104 x19: .cfa -16 + ^
STACK CFI b144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b150 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b154 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b1a0 9c .cfa: sp 0 + .ra: x30
STACK CFI b1a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b1ac x19: .cfa -16 + ^
STACK CFI b1e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b1e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b1f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b1f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b240 b0 .cfa: sp 0 + .ra: x30
STACK CFI b244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b24c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b284 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b2f0 c8 .cfa: sp 0 + .ra: x30
STACK CFI b2f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b2fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b36c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT b3b8 64 .cfa: sp 0 + .ra: x30
STACK CFI b3bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b3d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b3d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b420 c0 .cfa: sp 0 + .ra: x30
STACK CFI b424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b42c x19: .cfa -16 + ^
STACK CFI b470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b4e0 6c .cfa: sp 0 + .ra: x30
STACK CFI b4e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b4f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b504 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b550 6c .cfa: sp 0 + .ra: x30
STACK CFI b554 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b574 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b5c0 9c .cfa: sp 0 + .ra: x30
STACK CFI b5c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b5cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b608 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b614 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b660 8c .cfa: sp 0 + .ra: x30
STACK CFI b664 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b67c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b6a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b6f0 c4 .cfa: sp 0 + .ra: x30
STACK CFI b6f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b6fc x19: .cfa -16 + ^
STACK CFI b744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b748 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b76c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b7b8 19c .cfa: sp 0 + .ra: x30
STACK CFI b7bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b7c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b87c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b880 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b8f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b8fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
