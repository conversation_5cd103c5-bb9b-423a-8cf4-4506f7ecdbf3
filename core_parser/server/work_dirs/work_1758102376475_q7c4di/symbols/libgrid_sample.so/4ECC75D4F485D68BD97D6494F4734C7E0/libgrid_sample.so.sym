MODULE Linux arm64 4ECC75D4F485D68BD97D6494F4734C7E0 libgrid_sample.so
INFO CODE_ID D475CC4E85F48BD6D97D6494F4734C7E
PUBLIC 3568 0 _init
PUBLIC 36f0 0 _GLOBAL__sub_I_trt_grid_sampler.cpp
PUBLIC 376c 0 call_weak_fn
PUBLIC 3780 0 deregister_tm_clones
PUBLIC 37b0 0 register_tm_clones
PUBLIC 37ec 0 __do_global_dtors_aux
PUBLIC 383c 0 frame_dummy
PUBLIC 3840 0 mmdeploy::TRTGridSampler::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 3890 0 mmdeploy::TRTGridSampler::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 38a0 0 mmdeploy::TRTGridSampler::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 38b0 0 mmdeploy::TRTGridSampler::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 38c0 0 mmdeploy::TRTGridSampler::getPluginType() const
PUBLIC 38d0 0 mmdeploy::TRTGridSamplerCreator::getPluginVersion() const
PUBLIC 38e0 0 mmdeploy::TRTGridSampler::getNbOutputs() const
PUBLIC 38f0 0 mmdeploy::TRTGridSampler::getSerializationSize() const
PUBLIC 3900 0 mmdeploy::TRTGridSampler::serialize(void*) const
PUBLIC 3920 0 mmdeploy::TRTGridSampler::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 3b30 0 mmdeploy::TRTGridSampler::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 3b60 0 mmdeploy::TRTGridSampler::TRTGridSampler(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, bool)
PUBLIC 3c60 0 mmdeploy::TRTGridSampler::clone() const
PUBLIC 3cf0 0 mmdeploy::TRTGridSamplerCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 4020 0 mmdeploy::TRTGridSampler::TRTGridSampler(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, void const*, unsigned long)
PUBLIC 4110 0 mmdeploy::TRTGridSamplerCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 42b0 0 mmdeploy::TRTGridSamplerCreator::TRTGridSamplerCreator()
PUBLIC 43d0 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 43e0 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 43f0 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 4400 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 4410 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 4420 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 4430 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 4450 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 4460 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 4470 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 4480 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 4490 0 mmdeploy::TRTPluginBase::getPluginVersion() const
PUBLIC 44a0 0 mmdeploy::TRTPluginBase::initialize()
PUBLIC 44b0 0 mmdeploy::TRTPluginBase::terminate()
PUBLIC 44c0 0 mmdeploy::TRTPluginBase::getPluginNamespace() const
PUBLIC 44d0 0 mmdeploy::TRTPluginBase::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 44e0 0 mmdeploy::TRTPluginBase::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 44f0 0 mmdeploy::TRTPluginBase::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 4500 0 mmdeploy::TRTPluginBase::detachFromContext()
PUBLIC 4510 0 mmdeploy::TRTPluginCreatorBase::getPluginVersion() const
PUBLIC 4520 0 mmdeploy::TRTPluginCreatorBase::getFieldNames()
PUBLIC 4530 0 mmdeploy::TRTPluginCreatorBase::getPluginNamespace() const
PUBLIC 4540 0 mmdeploy::TRTGridSampler::~TRTGridSampler()
PUBLIC 45a0 0 mmdeploy::TRTGridSampler::~TRTGridSampler()
PUBLIC 4600 0 mmdeploy::TRTPluginBase::setPluginNamespace(char const*)
PUBLIC 4640 0 mmdeploy::TRTPluginCreatorBase::setPluginNamespace(char const*)
PUBLIC 4680 0 mmdeploy::TRTPluginBase::destroy()
PUBLIC 4700 0 mmdeploy::TRTGridSamplerCreator::~TRTGridSamplerCreator()
PUBLIC 4760 0 nvinfer1::PluginRegistrar<mmdeploy::TRTGridSamplerCreator>::~PluginRegistrar()
PUBLIC 47c0 0 mmdeploy::TRTGridSamplerCreator::~TRTGridSamplerCreator()
PUBLIC 4814 0 _fini
STACK CFI INIT 3780 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 37ec 50 .cfa: sp 0 + .ra: x30
STACK CFI 37fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3804 x19: .cfa -16 + ^
STACK CFI 3834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 383c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4400 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4410 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4430 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4490 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 44a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4500 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4530 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3840 44 .cfa: sp 0 + .ra: x30
STACK CFI 3860 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 387c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3890 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3900 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3920 208 .cfa: sp 0 + .ra: x30
STACK CFI 3924 .cfa: sp 400 +
STACK CFI 392c .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 39a0 x19: .cfa -352 + ^
STACK CFI 3acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ad0 .cfa: sp 400 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x29: .cfa -368 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b20 .cfa: sp 400 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x29: .cfa -368 + ^
STACK CFI INIT 3b30 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4540 5c .cfa: sp 0 + .ra: x30
STACK CFI 4544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4558 x19: .cfa -16 + ^
STACK CFI 458c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4590 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4598 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 45a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45b8 x19: .cfa -16 + ^
STACK CFI 45f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4600 40 .cfa: sp 0 + .ra: x30
STACK CFI 4604 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 460c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 463c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4640 40 .cfa: sp 0 + .ra: x30
STACK CFI 4644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 464c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 467c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4680 7c .cfa: sp 0 + .ra: x30
STACK CFI 469c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46b0 x19: .cfa -16 + ^
STACK CFI 46f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4700 54 .cfa: sp 0 + .ra: x30
STACK CFI 4704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4718 x19: .cfa -16 + ^
STACK CFI 4744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4748 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4760 54 .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4778 x19: .cfa -16 + ^
STACK CFI 47a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d8 x19: .cfa -16 + ^
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b60 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3b64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3b78 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b8c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3c10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3c60 88 .cfa: sp 0 + .ra: x30
STACK CFI 3c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3cf0 324 .cfa: sp 0 + .ra: x30
STACK CFI 3cf4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3cfc x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3d14 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3d1c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 3f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3f28 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 4000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4004 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4020 ec .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4034 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4040 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4110 198 .cfa: sp 0 + .ra: x30
STACK CFI 4114 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 411c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4128 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4130 x25: .cfa -64 + ^
STACK CFI 4218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 421c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 42a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 42b0 11c .cfa: sp 0 + .ra: x30
STACK CFI 42b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 42cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 438c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4390 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 4398 x21: .cfa -96 + ^
STACK CFI INIT 36f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 36f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
