MODULE Linux arm64 0BC38883A9FEB1F47452F03012C667330 libsasldb.so.2
INFO CODE_ID 8388C30BFEA9F4B17452F03012C6673312700004
PUBLIC 1840 0 sasldb_auxprop_plug_init
PUBLIC 18b8 0 sasl_auxprop_plug_init
PUBLIC 18c0 0 __sasldb_internal_list
PUBLIC 18f0 0 _sasldb_alloc_key
PUBLIC 1a30 0 _sasldb_parse_key
PUBLIC 1bf0 0 _sasldb_getsecret
PUBLIC 1d18 0 _sasldb_putsecret
PUBLIC 1d48 0 _sasldb_listusers
PUBLIC 21b0 0 _sasldb_getdata
PUBLIC 2470 0 _sasldb_putdata
PUBLIC 26f0 0 _sasl_check_db
PUBLIC 2870 0 _sasldb_getkeyhandle
PUBLIC 2960 0 _sasldb_getnextkey
PUBLIC 2af8 0 _sasldb_releasekeyhandle
PUBLIC 2b88 0 _plug_ipfromstring
PUBLIC 2e28 0 _plug_buf_alloc
PUBLIC 2f80 0 _plug_iovec_to_buf
PUBLIC 3130 0 _plug_strdup
PUBLIC 3220 0 _plug_free_string
PUBLIC 3288 0 _plug_free_secret
PUBLIC 32e0 0 _plug_find_prompt
PUBLIC 3318 0 _plug_get_simple
PUBLIC 3440 0 _plug_get_password
PUBLIC 35c8 0 _plug_challenge_prompt
PUBLIC 3700 0 _plug_get_realm
PUBLIC 3820 0 _plug_make_prompts
PUBLIC 39e0 0 _plug_decode_init
PUBLIC 3a00 0 _plug_decode
PUBLIC 3c98 0 _plug_decode_free
PUBLIC 3cb8 0 _plug_parseuser
PUBLIC 3e38 0 _plug_make_fulluser
PUBLIC 3f28 0 _plug_get_error_message
PUBLIC 3f98 0 _plug_snprintf_os_info
STACK CFI INIT 11f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1228 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1268 48 .cfa: sp 0 + .ra: x30
STACK CFI 126c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1274 x19: .cfa -16 + ^
STACK CFI 12ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b8 248 .cfa: sp 0 + .ra: x30
STACK CFI 12bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 12c4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 12e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 12f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1304 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1380 x27: .cfa -48 + ^
STACK CFI 141c x27: x27
STACK CFI 145c x19: x19 x20: x20
STACK CFI 1460 x21: x21 x22: x22
STACK CFI 1464 x25: x25 x26: x26
STACK CFI 1488 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 148c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 1494 x27: x27
STACK CFI 14b0 x19: x19 x20: x20
STACK CFI 14b4 x21: x21 x22: x22
STACK CFI 14b8 x25: x25 x26: x26
STACK CFI 14bc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14c8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 14d0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 14d8 x27: x27
STACK CFI 14dc x25: x25 x26: x26
STACK CFI 14e4 x19: x19 x20: x20
STACK CFI 14e8 x21: x21 x22: x22
STACK CFI 14f0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14f4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14f8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14fc x27: .cfa -48 + ^
STACK CFI INIT 1500 340 .cfa: sp 0 + .ra: x30
STACK CFI 1508 .cfa: sp 8352 +
STACK CFI 1514 .ra: .cfa -8344 + ^ x29: .cfa -8352 + ^
STACK CFI 151c x19: .cfa -8336 + ^ x20: .cfa -8328 + ^
STACK CFI 1528 x27: .cfa -8272 + ^ x28: .cfa -8264 + ^
STACK CFI 1540 x21: .cfa -8320 + ^ x22: .cfa -8312 + ^
STACK CFI 154c x23: .cfa -8304 + ^ x24: .cfa -8296 + ^
STACK CFI 15c8 x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI 1704 x25: x25 x26: x26
STACK CFI 1744 x23: x23 x24: x24
STACK CFI 1778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 177c .cfa: sp 8352 + .ra: .cfa -8344 + ^ x19: .cfa -8336 + ^ x20: .cfa -8328 + ^ x21: .cfa -8320 + ^ x22: .cfa -8312 + ^ x23: .cfa -8304 + ^ x24: .cfa -8296 + ^ x27: .cfa -8272 + ^ x28: .cfa -8264 + ^ x29: .cfa -8352 + ^
STACK CFI 17a8 x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI 17ec x25: x25 x26: x26
STACK CFI 17fc x23: x23 x24: x24
STACK CFI 1800 x23: .cfa -8304 + ^ x24: .cfa -8296 + ^ x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI 1818 x25: x25 x26: x26
STACK CFI 181c x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI 1828 x25: x25 x26: x26
STACK CFI 182c x23: x23 x24: x24
STACK CFI 1838 x23: .cfa -8304 + ^ x24: .cfa -8296 + ^
STACK CFI 183c x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI INIT 1840 78 .cfa: sp 0 + .ra: x30
STACK CFI 1850 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1858 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1864 x21: .cfa -16 + ^
STACK CFI 189c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 18b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 18c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18f0 140 .cfa: sp 0 + .ra: x30
STACK CFI 1900 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1910 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1918 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1924 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1930 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 194c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19e0 x19: x19 x20: x20
STACK CFI 19e8 x25: x25 x26: x26
STACK CFI 19ec x27: x27 x28: x28
STACK CFI 19f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a04 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 1a0c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a14 x19: x19 x20: x20
STACK CFI 1a18 x25: x25 x26: x26
STACK CFI 1a1c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a24 x19: x19 x20: x20
STACK CFI 1a28 x25: x25 x26: x26
STACK CFI 1a2c x27: x27 x28: x28
STACK CFI INIT 1a30 1bc .cfa: sp 0 + .ra: x30
STACK CFI 1a34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1a44 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1a54 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1a5c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1a74 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1a8c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1b64 x21: x21 x22: x22
STACK CFI 1b68 x25: x25 x26: x26
STACK CFI 1b6c x27: x27 x28: x28
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1b80 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1b8c x21: x21 x22: x22
STACK CFI 1b94 x25: x25 x26: x26
STACK CFI 1b98 x27: x27 x28: x28
STACK CFI 1b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1ba0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1ba8 x21: x21 x22: x22
STACK CFI 1bac x25: x25 x26: x26
STACK CFI 1bb0 x27: x27 x28: x28
STACK CFI 1bb4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1bbc x21: x21 x22: x22
STACK CFI 1bc0 x25: x25 x26: x26
STACK CFI 1bc4 x27: x27 x28: x28
STACK CFI 1bc8 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1bd0 x25: x25 x26: x26
STACK CFI 1bd4 x27: x27 x28: x28
STACK CFI 1bd8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1be0 x25: x25 x26: x26
STACK CFI INIT 1bf0 124 .cfa: sp 0 + .ra: x30
STACK CFI 1bf8 .cfa: sp 8288 +
STACK CFI 1bfc .ra: .cfa -8280 + ^ x29: .cfa -8288 + ^
STACK CFI 1c04 x19: .cfa -8272 + ^ x20: .cfa -8264 + ^
STACK CFI 1c10 x21: .cfa -8256 + ^ x22: .cfa -8248 + ^
STACK CFI 1c24 x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI 1c2c x25: .cfa -8224 + ^
STACK CFI 1ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1cd0 .cfa: sp 8288 + .ra: .cfa -8280 + ^ x19: .cfa -8272 + ^ x20: .cfa -8264 + ^ x21: .cfa -8256 + ^ x22: .cfa -8248 + ^ x23: .cfa -8240 + ^ x24: .cfa -8232 + ^ x25: .cfa -8224 + ^ x29: .cfa -8288 + ^
STACK CFI INIT 1d18 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d48 244 .cfa: sp 0 + .ra: x30
STACK CFI 1d4c .cfa: sp 65536 +
STACK CFI 1d58 .cfa: sp 82048 +
STACK CFI 1d60 .ra: .cfa -82040 + ^ x29: .cfa -82048 + ^
STACK CFI 1d6c x23: .cfa -82000 + ^ x24: .cfa -81992 + ^
STACK CFI 1d80 x25: .cfa -81984 + ^ x26: .cfa -81976 + ^ x27: .cfa -81968 + ^ x28: .cfa -81960 + ^
STACK CFI 1dbc x19: .cfa -82032 + ^ x20: .cfa -82024 + ^
STACK CFI 1df0 x21: .cfa -82016 + ^ x22: .cfa -82008 + ^
STACK CFI 1e80 x21: x21 x22: x22
STACK CFI 1e9c x19: x19 x20: x20
STACK CFI 1ed0 .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ed4 .cfa: sp 81920 +
STACK CFI 1ed8 .cfa: sp 0 +
STACK CFI 1edc .cfa: sp 82048 + .ra: .cfa -82040 + ^ x19: .cfa -82032 + ^ x20: .cfa -82024 + ^ x21: .cfa -82016 + ^ x22: .cfa -82008 + ^ x23: .cfa -82000 + ^ x24: .cfa -81992 + ^ x25: .cfa -81984 + ^ x26: .cfa -81976 + ^ x27: .cfa -81968 + ^ x28: .cfa -81960 + ^ x29: .cfa -82048 + ^
STACK CFI 1f0c x21: x21 x22: x22
STACK CFI 1f28 x21: .cfa -82016 + ^ x22: .cfa -82008 + ^
STACK CFI 1f30 x21: x21 x22: x22
STACK CFI 1f50 x19: x19 x20: x20
STACK CFI 1f84 x19: .cfa -82032 + ^ x20: .cfa -82024 + ^
STACK CFI 1f88 x21: .cfa -82016 + ^ x22: .cfa -82008 + ^
STACK CFI INIT 1f90 1bc .cfa: sp 0 + .ra: x30
STACK CFI 1f94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1f9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1fb8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1fd4 x25: .cfa -48 + ^
STACK CFI 2070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2074 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2150 5c .cfa: sp 0 + .ra: x30
STACK CFI 2154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2160 x19: .cfa -16 + ^
STACK CFI 217c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2180 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 21a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21b0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 21b4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 21bc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 21d8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 21e0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 21ec x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2208 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 226c x23: x23 x24: x24
STACK CFI 2270 x25: x25 x26: x26
STACK CFI 2274 x27: x27 x28: x28
STACK CFI 229c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22a0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 238c x23: x23 x24: x24
STACK CFI 2390 x25: x25 x26: x26
STACK CFI 2394 x27: x27 x28: x28
STACK CFI 2398 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 23f0 x23: x23 x24: x24
STACK CFI 2410 x25: x25 x26: x26
STACK CFI 2414 x27: x27 x28: x28
STACK CFI 2418 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2434 x23: x23 x24: x24
STACK CFI 2438 x25: x25 x26: x26
STACK CFI 243c x27: x27 x28: x28
STACK CFI 2448 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2450 x23: x23 x24: x24
STACK CFI 2454 x25: x25 x26: x26
STACK CFI 2458 x27: x27 x28: x28
STACK CFI 2460 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2464 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2468 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 2470 280 .cfa: sp 0 + .ra: x30
STACK CFI 2474 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 247c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2498 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 24d4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2508 x23: x23 x24: x24
STACK CFI 2530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2534 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 2558 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 25c4 x25: x25 x26: x26
STACK CFI 25e4 x23: x23 x24: x24
STACK CFI 25e8 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2654 x25: x25 x26: x26
STACK CFI 2658 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2698 x25: x25 x26: x26
STACK CFI 269c x23: x23 x24: x24
STACK CFI 26e8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 26ec x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 26f0 180 .cfa: sp 0 + .ra: x30
STACK CFI 26f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 26fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2708 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2728 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 278c x23: x23 x24: x24
STACK CFI 27bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 2840 x23: x23 x24: x24
STACK CFI 2844 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 285c x23: x23 x24: x24
STACK CFI 286c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 2870 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2874 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2884 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2890 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 290c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2960 198 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2970 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2994 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 29b0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 29b8 x25: .cfa -112 + ^
STACK CFI 2a30 x21: x21 x22: x22
STACK CFI 2a34 x25: x25
STACK CFI 2a3c x23: x23 x24: x24
STACK CFI 2a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a60 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 2a64 x21: x21 x22: x22
STACK CFI 2a68 x23: x23 x24: x24
STACK CFI 2a6c x25: x25
STACK CFI 2a70 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^
STACK CFI 2aac x23: x23 x24: x24 x25: x25
STACK CFI 2ab4 x21: x21 x22: x22
STACK CFI 2ab8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^
STACK CFI 2ac0 x21: x21 x22: x22
STACK CFI 2ac4 x23: x23 x24: x24
STACK CFI 2ac8 x25: x25
STACK CFI 2acc x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^
STACK CFI 2ad4 x21: x21 x22: x22
STACK CFI 2ad8 x23: x23 x24: x24
STACK CFI 2adc x25: x25
STACK CFI 2aec x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2af0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2af4 x25: .cfa -112 + ^
STACK CFI INIT 2af8 8c .cfa: sp 0 + .ra: x30
STACK CFI 2b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b88 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 2b8c .cfa: sp 1312 +
STACK CFI 2b94 .ra: .cfa -1304 + ^ x29: .cfa -1312 + ^
STACK CFI 2b9c x21: .cfa -1280 + ^ x22: .cfa -1272 + ^
STACK CFI 2ba8 x19: .cfa -1296 + ^ x20: .cfa -1288 + ^
STACK CFI 2bd4 x23: .cfa -1264 + ^ x24: .cfa -1256 + ^
STACK CFI 2c20 x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI 2c7c x23: x23 x24: x24
STACK CFI 2c84 x25: x25 x26: x26
STACK CFI 2cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2cb4 .cfa: sp 1312 + .ra: .cfa -1304 + ^ x19: .cfa -1296 + ^ x20: .cfa -1288 + ^ x21: .cfa -1280 + ^ x22: .cfa -1272 + ^ x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^ x29: .cfa -1312 + ^
STACK CFI 2d54 x23: x23 x24: x24
STACK CFI 2d58 x25: x25 x26: x26
STACK CFI 2d5c x23: .cfa -1264 + ^ x24: .cfa -1256 + ^
STACK CFI 2d80 x23: x23 x24: x24
STACK CFI 2d84 x23: .cfa -1264 + ^ x24: .cfa -1256 + ^
STACK CFI 2d8c x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI 2dac x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2dd8 x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI 2dfc x23: x23 x24: x24
STACK CFI 2e00 x25: x25 x26: x26
STACK CFI 2e04 x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI 2e1c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2e20 x23: .cfa -1264 + ^ x24: .cfa -1256 + ^
STACK CFI 2e24 x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI INIT 2e28 158 .cfa: sp 0 + .ra: x30
STACK CFI 2e2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e3c x23: .cfa -16 + ^
STACK CFI 2e48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2eb0 x19: x19 x20: x20
STACK CFI 2eb4 x21: x21 x22: x22
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ec4 x19: x19 x20: x20
STACK CFI 2ec8 x21: x21 x22: x22
STACK CFI 2ed0 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ef4 x19: x19 x20: x20
STACK CFI 2ef8 x21: x21 x22: x22
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2f04 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2f30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f34 x21: x21 x22: x22
STACK CFI 2f38 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f60 x19: x19 x20: x20
STACK CFI 2f64 x21: x21 x22: x22
STACK CFI 2f68 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2f80 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f98 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2fb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3048 x19: x19 x20: x20
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 305c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 30d4 x19: x19 x20: x20
STACK CFI 3104 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3128 x19: x19 x20: x20
STACK CFI INIT 3130 f0 .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3144 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3150 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3168 x23: .cfa -16 + ^
STACK CFI 31a0 x21: x21 x22: x22
STACK CFI 31a4 x23: x23
STACK CFI 31ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 31b8 x21: x21 x22: x22
STACK CFI 31bc x23: x23
STACK CFI 31c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 31f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3210 x21: x21 x22: x22
STACK CFI 3214 x23: x23
STACK CFI 3218 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 321c x21: x21 x22: x22
STACK CFI INIT 3220 68 .cfa: sp 0 + .ra: x30
STACK CFI 3230 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3238 x21: .cfa -16 + ^
STACK CFI 3240 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3288 54 .cfa: sp 0 + .ra: x30
STACK CFI 3298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 32d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 32e0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3318 128 .cfa: sp 0 + .ra: x30
STACK CFI 331c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3324 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3334 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3348 x23: .cfa -48 + ^
STACK CFI 339c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 33a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3440 188 .cfa: sp 0 + .ra: x30
STACK CFI 3444 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 344c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3458 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3470 x23: .cfa -48 + ^
STACK CFI 3508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 350c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 35c8 138 .cfa: sp 0 + .ra: x30
STACK CFI 35cc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 35d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 35e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3600 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 3658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 365c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3700 120 .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 370c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 371c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3730 x23: .cfa -48 + ^
STACK CFI 3784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3788 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3820 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 3824 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3830 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 383c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3864 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 398c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 39e0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a00 294 .cfa: sp 0 + .ra: x30
STACK CFI 3a04 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3a0c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3a30 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3a40 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3a4c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3a54 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3ba8 x23: x23 x24: x24
STACK CFI 3bb0 x25: x25 x26: x26
STACK CFI 3bb8 x19: x19 x20: x20
STACK CFI 3bbc x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3be0 x19: x19 x20: x20
STACK CFI 3be4 x23: x23 x24: x24
STACK CFI 3be8 x25: x25 x26: x26
STACK CFI 3c10 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 3c14 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 3c18 x19: x19 x20: x20
STACK CFI 3c1c x23: x23 x24: x24
STACK CFI 3c20 x25: x25 x26: x26
STACK CFI 3c28 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3c30 x19: x19 x20: x20
STACK CFI 3c34 x23: x23 x24: x24
STACK CFI 3c38 x25: x25 x26: x26
STACK CFI 3c3c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3c64 x19: x19 x20: x20
STACK CFI 3c68 x23: x23 x24: x24
STACK CFI 3c6c x25: x25 x26: x26
STACK CFI 3c70 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3c78 x19: x19 x20: x20
STACK CFI 3c7c x23: x23 x24: x24
STACK CFI 3c80 x25: x25 x26: x26
STACK CFI 3c88 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3c8c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3c90 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 3c98 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cb8 180 .cfa: sp 0 + .ra: x30
STACK CFI 3cbc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3ccc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3cd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3ce8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3cf8 x25: .cfa -16 + ^
STACK CFI 3d58 x21: x21 x22: x22
STACK CFI 3d5c x25: x25
STACK CFI 3d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3d70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3dc4 x21: x21 x22: x22
STACK CFI 3dcc x25: x25
STACK CFI 3dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3dd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3dec x21: x21 x22: x22
STACK CFI 3df4 x25: x25
STACK CFI 3df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3dfc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3e14 x21: x21 x22: x22 x25: x25
STACK CFI INIT 3e38 f0 .cfa: sp 0 + .ra: x30
STACK CFI 3e3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e50 x23: .cfa -16 + ^
STACK CFI 3e60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ec8 x19: x19 x20: x20
STACK CFI 3ed0 x21: x21 x22: x22
STACK CFI 3ed8 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 3edc .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3f00 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f20 x19: x19 x20: x20
STACK CFI 3f24 x21: x21 x22: x22
STACK CFI INIT 3f28 70 .cfa: sp 0 + .ra: x30
STACK CFI 3f2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f98 84 .cfa: sp 0 + .ra: x30
STACK CFI 3f9c .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 3fa4 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 3fb4 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 4014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4018 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x29: .cfa -448 + ^
