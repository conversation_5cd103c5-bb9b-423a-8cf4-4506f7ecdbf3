MODULE Linux arm64 E2DCCA695C4B0D657A02808E38D4C18E0 libnss_nisplus.so.2
INFO CODE_ID 69CADCE24B5C650D7A02808E38D4C18E429ED0D2
PUBLIC 26a0 0 _nss_nisplus_setprotoent
PUBLIC 2760 0 _nss_nisplus_endprotoent
PUBLIC 27c8 0 _nss_nisplus_getprotoent_r
PUBLIC 29a8 0 _nss_nisplus_getprotobyname_r
PUBLIC 2d28 0 _nss_nisplus_getprotobynumber_r
PUBLIC 3320 0 _nss_nisplus_setservent
PUBLIC 33e0 0 _nss_nisplus_endservent
PUBLIC 3448 0 _nss_nisplus_getservent_r
PUBLIC 3620 0 _nss_nisplus_getservbyname_r
PUBLIC 44c8 0 _nss_nisplus_sethostent
PUBLIC 4588 0 _nss_nisplus_endhostent
PUBLIC 45f0 0 _nss_nisplus_gethostent_r
PUBLIC 4800 0 _nss_nisplus_gethostbyname2_r
PUBLIC 4820 0 _nss_nisplus_gethostbyname_r
PUBLIC 4848 0 _nss_nisplus_gethostbyaddr_r
PUBLIC 4a88 0 _nss_nisplus_gethostbyname4_r
PUBLIC 4f30 0 _nss_nisplus_setnetent
PUBLIC 4ff0 0 _nss_nisplus_endnetent
PUBLIC 5058 0 _nss_nisplus_getnetent_r
PUBLIC 5248 0 _nss_nisplus_getnetbyname_r
PUBLIC 5628 0 _nss_nisplus_getnetbyaddr_r
PUBLIC 5b98 0 _nss_nisplus_setgrent
PUBLIC 5c30 0 _nss_nisplus_endgrent
PUBLIC 5c80 0 _nss_nisplus_getgrent_r
PUBLIC 5f70 0 _nss_nisplus_getgrnam_r
PUBLIC 6160 0 _nss_nisplus_getgrgid_r
PUBLIC 6590 0 _nss_nisplus_setpwent
PUBLIC 6628 0 _nss_nisplus_endpwent
PUBLIC 6678 0 _nss_nisplus_getpwent_r
PUBLIC 6968 0 _nss_nisplus_getpwnam_r
PUBLIC 6b68 0 _nss_nisplus_getpwuid_r
PUBLIC 70b0 0 _nss_nisplus_setrpcent
PUBLIC 7170 0 _nss_nisplus_endrpcent
PUBLIC 71d8 0 _nss_nisplus_getrpcent_r
PUBLIC 73b8 0 _nss_nisplus_getrpcbyname_r
PUBLIC 7738 0 _nss_nisplus_getrpcbynumber_r
PUBLIC 7b78 0 _nss_nisplus_setetherent
PUBLIC 7c40 0 _nss_nisplus_endetherent
PUBLIC 7ca8 0 _nss_nisplus_getetherent_r
PUBLIC 7ea0 0 _nss_nisplus_gethostton_r
PUBLIC 8098 0 _nss_nisplus_getntohost_r
PUBLIC 82d8 0 _nss_nisplus_setspent
PUBLIC 8390 0 _nss_nisplus_endspent
PUBLIC 83f0 0 _nss_nisplus_getspent_r
PUBLIC 85e8 0 _nss_nisplus_getspnam_r
PUBLIC 87e8 0 _nss_nisplus_getnetgrent_r
PUBLIC 89d8 0 _nss_nisplus_setnetgrent
PUBLIC 8b38 0 _nss_nisplus_endnetgrent
PUBLIC 8f70 0 _nss_nisplus_setaliasent
PUBLIC 8fd0 0 _nss_nisplus_endaliasent
PUBLIC 9038 0 _nss_nisplus_getaliasent_r
PUBLIC 9138 0 _nss_nisplus_getaliasbyname_r
PUBLIC 9380 0 _nss_nisplus_getpublickey
PUBLIC 9590 0 _nss_nisplus_getsecretkey
PUBLIC 97e0 0 _nss_nisplus_netname2user
PUBLIC a628 0 _nss_nisplus_initgroups_dyn
STACK CFI INIT 2278 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 22ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22f4 x19: .cfa -16 + ^
STACK CFI 232c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2330 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2338 2bc .cfa: sp 0 + .ra: x30
STACK CFI 2340 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2348 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2354 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2368 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2390 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2424 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 24c4 x27: x27 x28: x28
STACK CFI 2578 x19: x19 x20: x20
STACK CFI 257c x23: x23 x24: x24
STACK CFI 258c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2590 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 2594 x19: x19 x20: x20
STACK CFI 25a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 25a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 25ac x19: x19 x20: x20
STACK CFI 25b0 x23: x23 x24: x24
STACK CFI 25b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 25bc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 25c0 x27: x27 x28: x28
STACK CFI 25cc x19: x19 x20: x20
STACK CFI 25d4 x23: x23 x24: x24
STACK CFI 25d8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 25dc x19: x19 x20: x20
STACK CFI 25e0 x23: x23 x24: x24
STACK CFI 25e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 25f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 25fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2608 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 267c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26a0 bc .cfa: sp 0 + .ra: x30
STACK CFI 26a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26d0 x21: .cfa -32 + ^
STACK CFI 2744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2748 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2760 64 .cfa: sp 0 + .ra: x30
STACK CFI 2764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 27c8 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 27cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 27dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27fc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 28c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 28c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 29a8 37c .cfa: sp 0 + .ra: x30
STACK CFI 29ac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 29b0 .cfa: x29 160 +
STACK CFI 29b4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 29c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 29d4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 29f4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2bc4 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2d28 23c .cfa: sp 0 + .ra: x30
STACK CFI 2d2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2d30 .cfa: x29 112 +
STACK CFI 2d34 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2d40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2d48 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2d70 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e98 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2f68 30c .cfa: sp 0 + .ra: x30
STACK CFI 2f70 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2f78 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2f84 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2f98 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3038 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 30ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3144 x25: x25 x26: x26
STACK CFI 3200 x23: x23 x24: x24
STACK CFI 3204 x27: x27 x28: x28
STACK CFI 3214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3218 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 3224 x23: x23 x24: x24
STACK CFI 3228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 322c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 3234 x23: x23 x24: x24
STACK CFI 3238 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 323c x25: x25 x26: x26
STACK CFI 3240 x27: x27 x28: x28
STACK CFI 3250 x23: x23 x24: x24
STACK CFI 3254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 325c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3260 x27: x27 x28: x28
STACK CFI 3264 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3278 a4 .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3288 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 32fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3300 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3320 bc .cfa: sp 0 + .ra: x30
STACK CFI 3324 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3330 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3350 x21: .cfa -32 + ^
STACK CFI 33c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 33c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 33e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 33e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3448 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 345c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3468 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3470 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 347c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 35b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3620 3dc .cfa: sp 0 + .ra: x30
STACK CFI 3624 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3628 .cfa: x29 176 +
STACK CFI 362c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3638 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3640 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 3668 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 36cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 36d0 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 3a00 268 .cfa: sp 0 + .ra: x30
STACK CFI 3a04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3a08 .cfa: x29 128 +
STACK CFI 3a0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3a18 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3a24 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3a30 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3a50 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3bf8 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c68 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 3c70 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3c78 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3c8c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3cb4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3cc0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3d7c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3f08 x21: x21 x22: x22
STACK CFI 3f0c x23: x23 x24: x24
STACK CFI 3f10 x25: x25 x26: x26
STACK CFI 3f14 x27: x27 x28: x28
STACK CFI 3f1c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3f20 x21: x21 x22: x22
STACK CFI 3f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f30 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 3f34 x21: x21 x22: x22
STACK CFI 3f38 x23: x23 x24: x24
STACK CFI 3f3c x25: x25 x26: x26
STACK CFI 3f40 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3f44 x27: x27 x28: x28
STACK CFI 3f54 x21: x21 x22: x22
STACK CFI 3f58 x25: x25 x26: x26
STACK CFI 3f60 x23: x23 x24: x24
STACK CFI 3f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f6c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 3fd0 x21: x21 x22: x22
STACK CFI 3fd8 x23: x23 x24: x24
STACK CFI 3fe4 x25: x25 x26: x26
STACK CFI 3fe8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 4010 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4014 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4020 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4098 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40b8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 40bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40d4 x21: .cfa -16 + ^
STACK CFI 411c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4120 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 415c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4168 35c .cfa: sp 0 + .ra: x30
STACK CFI 416c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4170 .cfa: x29 160 +
STACK CFI 4174 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4180 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 41a8 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 41bc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4398 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 44c8 bc .cfa: sp 0 + .ra: x30
STACK CFI 44cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 44f8 x21: .cfa -32 + ^
STACK CFI 456c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4570 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4588 64 .cfa: sp 0 + .ra: x30
STACK CFI 458c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 459c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45f0 210 .cfa: sp 0 + .ra: x30
STACK CFI 45f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4604 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4610 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 461c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4624 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 46f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4800 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4820 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4848 240 .cfa: sp 0 + .ra: x30
STACK CFI 484c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4858 .cfa: x29 128 +
STACK CFI 485c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4868 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4884 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4890 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 48a0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4a08 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4a88 148 .cfa: sp 0 + .ra: x30
STACK CFI 4a8c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4a98 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4aac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4ac4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4acc x25: .cfa -64 + ^
STACK CFI 4b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4b68 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4bd0 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 4bd8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4be0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4bec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4c00 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4c28 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4cb0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4d54 x27: x27 x28: x28
STACK CFI 4e08 x19: x19 x20: x20
STACK CFI 4e0c x23: x23 x24: x24
STACK CFI 4e1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4e20 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 4e24 x19: x19 x20: x20
STACK CFI 4e30 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4e34 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 4e3c x19: x19 x20: x20
STACK CFI 4e40 x23: x23 x24: x24
STACK CFI 4e44 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4e4c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4e50 x27: x27 x28: x28
STACK CFI 4e5c x19: x19 x20: x20
STACK CFI 4e64 x23: x23 x24: x24
STACK CFI 4e68 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4e6c x19: x19 x20: x20
STACK CFI 4e70 x23: x23 x24: x24
STACK CFI 4e74 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 4e88 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e98 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4f30 bc .cfa: sp 0 + .ra: x30
STACK CFI 4f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4f40 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f60 x21: .cfa -32 + ^
STACK CFI 4fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4fd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ff0 64 .cfa: sp 0 + .ra: x30
STACK CFI 4ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5004 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5058 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 506c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5078 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5084 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5090 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5198 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5248 3e0 .cfa: sp 0 + .ra: x30
STACK CFI 524c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5250 .cfa: x29 176 +
STACK CFI 5254 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5264 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5274 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5294 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 547c .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5628 314 .cfa: sp 0 + .ra: x30
STACK CFI 562c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5630 .cfa: x29 192 +
STACK CFI 5634 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5640 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 5648 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5658 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 567c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 5818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 581c .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5940 74 .cfa: sp 0 + .ra: x30
STACK CFI 5944 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 594c x19: .cfa -16 + ^
STACK CFI 59b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 59b8 fc .cfa: sp 0 + .ra: x30
STACK CFI 59bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 59c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 59e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 59e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 59ec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5a60 x21: x21 x22: x22
STACK CFI 5a64 x23: x23 x24: x24
STACK CFI 5a68 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5a7c x21: x21 x22: x22
STACK CFI 5a84 x23: x23 x24: x24
STACK CFI 5a8c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5aac x21: x21 x22: x22
STACK CFI 5ab0 x23: x23 x24: x24
STACK CFI INIT 5ab8 dc .cfa: sp 0 + .ra: x30
STACK CFI 5abc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ac4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ad0 x21: .cfa -16 + ^
STACK CFI 5b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b98 94 .cfa: sp 0 + .ra: x30
STACK CFI 5b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ba8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c30 50 .cfa: sp 0 + .ra: x30
STACK CFI 5c34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c80 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 5c84 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5c90 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 5c9c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5cb4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5cc0 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5e7c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 5f70 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 5f74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5f78 .cfa: x29 112 +
STACK CFI 5f7c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5f8c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5fa0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5fb4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6114 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6160 1dc .cfa: sp 0 + .ra: x30
STACK CFI 6164 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6168 .cfa: x29 112 +
STACK CFI 616c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 617c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6190 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 619c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 61ac x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 62cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 62d0 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6340 74 .cfa: sp 0 + .ra: x30
STACK CFI 6344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 634c x19: .cfa -16 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 63b8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 63bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 63e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 63ec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6458 x21: x21 x22: x22
STACK CFI 645c x23: x23 x24: x24
STACK CFI 6460 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6474 x21: x21 x22: x22
STACK CFI 647c x23: x23 x24: x24
STACK CFI 6484 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 64a4 x21: x21 x22: x22
STACK CFI 64a8 x23: x23 x24: x24
STACK CFI INIT 64b0 dc .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c8 x21: .cfa -16 + ^
STACK CFI 6518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 651c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6568 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6590 94 .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 661c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6620 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6628 50 .cfa: sp 0 + .ra: x30
STACK CFI 662c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6674 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6678 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 667c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6688 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6694 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 66ac x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 66b8 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 6870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6874 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6968 200 .cfa: sp 0 + .ra: x30
STACK CFI 696c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6970 .cfa: x29 112 +
STACK CFI 6974 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6984 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6998 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 69ac x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6aec .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6b68 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 6b6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6b70 .cfa: x29 112 +
STACK CFI 6b74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6b84 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6b98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6ba4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6bb4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6cdc .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6d50 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 6d58 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6d60 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6d6c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6d80 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6da8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6e34 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6ed4 x27: x27 x28: x28
STACK CFI 6f88 x19: x19 x20: x20
STACK CFI 6f8c x23: x23 x24: x24
STACK CFI 6f9c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6fa0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 6fa4 x19: x19 x20: x20
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6fb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 6fbc x19: x19 x20: x20
STACK CFI 6fc0 x23: x23 x24: x24
STACK CFI 6fc4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6fcc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6fd0 x27: x27 x28: x28
STACK CFI 6fdc x19: x19 x20: x20
STACK CFI 6fe4 x23: x23 x24: x24
STACK CFI 6fe8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6fec x19: x19 x20: x20
STACK CFI 6ff0 x23: x23 x24: x24
STACK CFI 6ff4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 7008 a4 .cfa: sp 0 + .ra: x30
STACK CFI 700c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7018 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 708c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 70b0 bc .cfa: sp 0 + .ra: x30
STACK CFI 70b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 70c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 70e0 x21: .cfa -32 + ^
STACK CFI 7154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7158 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7170 64 .cfa: sp 0 + .ra: x30
STACK CFI 7174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7184 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 71d8 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 71dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 71ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 71f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7200 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 720c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 72d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 73b8 37c .cfa: sp 0 + .ra: x30
STACK CFI 73bc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 73c0 .cfa: x29 160 +
STACK CFI 73c4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 73d4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 73e4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7404 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 75d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 75dc .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7738 23c .cfa: sp 0 + .ra: x30
STACK CFI 773c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7740 .cfa: x29 112 +
STACK CFI 7744 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7750 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7758 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7780 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 78a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 78ac .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7978 160 .cfa: sp 0 + .ra: x30
STACK CFI 797c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 798c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7998 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 79ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7a44 x19: x19 x20: x20
STACK CFI 7a48 x23: x23 x24: x24
STACK CFI 7a58 x21: x21 x22: x22
STACK CFI 7a5c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7a60 x19: x19 x20: x20
STACK CFI 7a64 x21: x21 x22: x22
STACK CFI 7a68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7a6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7a70 x19: x19 x20: x20
STACK CFI 7a74 x21: x21 x22: x22
STACK CFI 7a78 x23: x23 x24: x24
STACK CFI 7a7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7a80 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7a88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7a8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7a94 x19: x19 x20: x20
STACK CFI 7a98 x21: x21 x22: x22
STACK CFI 7a9c x23: x23 x24: x24
STACK CFI 7aa0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7aac x19: x19 x20: x20
STACK CFI 7ab0 x23: x23 x24: x24
STACK CFI 7ab8 x21: x21 x22: x22
STACK CFI 7abc x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7ac8 x19: x19 x20: x20
STACK CFI 7acc x23: x23 x24: x24
STACK CFI 7ad4 x21: x21 x22: x22
STACK CFI INIT 7ad8 9c .cfa: sp 0 + .ra: x30
STACK CFI 7adc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ae8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7b78 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7b7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7b88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7ba8 x21: .cfa -32 + ^
STACK CFI 7c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7c20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7c40 64 .cfa: sp 0 + .ra: x30
STACK CFI 7c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ca8 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 7cac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7cbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7cc4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7cd0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7cdc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7cfc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7de0 x27: x27 x28: x28
STACK CFI 7de4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7dec x27: x27 x28: x28
STACK CFI 7e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7e20 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7e48 x27: x27 x28: x28
STACK CFI 7e50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e64 x27: x27 x28: x28
STACK CFI 7e88 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e98 x27: x27 x28: x28
STACK CFI INIT 7ea0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 7ea4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7ea8 .cfa: x29 112 +
STACK CFI 7eac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7eb8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7ec8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7ee4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7ef4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8028 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8098 240 .cfa: sp 0 + .ra: x30
STACK CFI 809c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 80a0 .cfa: x29 112 +
STACK CFI 80a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 80b0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 80c0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 80dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 80ec x27: .cfa -32 + ^
STACK CFI 821c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8220 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 82d8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 82dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 82e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8308 x21: .cfa -32 + ^
STACK CFI 8374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8378 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8390 5c .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 83e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 83f0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 83f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8404 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 840c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8414 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8424 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 856c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8570 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 85e8 1fc .cfa: sp 0 + .ra: x30
STACK CFI 85ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 85f0 .cfa: x29 112 +
STACK CFI 85f4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8604 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8618 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 862c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 878c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8790 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 87e8 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 87f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8800 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8834 x21: .cfa -16 + ^
STACK CFI 8874 x21: x21
STACK CFI 888c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8924 x21: x21
STACK CFI 8934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8938 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 894c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8954 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8988 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 89d4 x21: x21
STACK CFI INIT 89d8 15c .cfa: sp 0 + .ra: x30
STACK CFI 89dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 89e0 .cfa: x29 64 +
STACK CFI 89e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 89f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8aec .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8b38 30 .cfa: sp 0 + .ra: x30
STACK CFI 8b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b44 x19: .cfa -16 + ^
STACK CFI 8b64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b68 24c .cfa: sp 0 + .ra: x30
STACK CFI 8b70 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8b78 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8b84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8b98 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8bd0 x25: .cfa -16 + ^
STACK CFI 8d34 x21: x21 x22: x22
STACK CFI 8d3c x25: x25
STACK CFI 8d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 8d50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8d58 x21: x21 x22: x22
STACK CFI 8d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 8d64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8d6c x21: x21 x22: x22
STACK CFI 8d70 x25: x25
STACK CFI 8d74 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 8d80 x21: x21 x22: x22
STACK CFI 8d88 x25: x25
STACK CFI 8d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 8d94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8da4 x21: x21 x22: x22
STACK CFI 8da8 x25: x25
STACK CFI 8dac x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI INIT 8db8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8dc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8e40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8e60 110 .cfa: sp 0 + .ra: x30
STACK CFI 8e64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8e6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8e88 x21: .cfa -32 + ^
STACK CFI 8f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8f28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8f70 5c .cfa: sp 0 + .ra: x30
STACK CFI 8f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f84 x19: .cfa -16 + ^
STACK CFI 8fc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8fd0 68 .cfa: sp 0 + .ra: x30
STACK CFI 8fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fe4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9038 100 .cfa: sp 0 + .ra: x30
STACK CFI 903c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 904c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9058 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9064 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 906c x25: .cfa -16 + ^
STACK CFI 9108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 910c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9138 244 .cfa: sp 0 + .ra: x30
STACK CFI 913c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9140 .cfa: x29 112 +
STACK CFI 9144 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9150 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9160 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 917c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 918c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 92bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 92c0 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9380 20c .cfa: sp 0 + .ra: x30
STACK CFI 9384 .cfa: sp 1104 +
STACK CFI 9388 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 9390 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 93a0 x23: .cfa -1056 + ^ x24: .cfa -1048 + ^
STACK CFI 93c0 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 9490 x19: x19 x20: x20
STACK CFI 94bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 94c0 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^ x29: .cfa -1104 + ^
STACK CFI 950c x19: x19 x20: x20
STACK CFI 9510 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 9544 x19: x19 x20: x20
STACK CFI 9548 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 954c x19: x19 x20: x20
STACK CFI 955c x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 956c x19: x19 x20: x20
STACK CFI 9570 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 957c x19: x19 x20: x20
STACK CFI 9588 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI INIT 9590 250 .cfa: sp 0 + .ra: x30
STACK CFI 9594 .cfa: sp 1120 +
STACK CFI 9598 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 95a0 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 95b0 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 95cc x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 95d8 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 969c x19: x19 x20: x20
STACK CFI 96cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 96d0 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x29: .cfa -1120 + ^
STACK CFI 9754 x19: x19 x20: x20
STACK CFI 9758 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 9798 x19: x19 x20: x20
STACK CFI 979c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 97a0 x19: x19 x20: x20
STACK CFI 97b0 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 97c0 x19: x19 x20: x20
STACK CFI 97c4 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 97d0 x19: x19 x20: x20
STACK CFI 97dc x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI INIT 97e0 560 .cfa: sp 0 + .ra: x30
STACK CFI 97e4 .cfa: sp 2192 +
STACK CFI 97e8 .ra: .cfa -2184 + ^ x29: .cfa -2192 + ^
STACK CFI 97f0 x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI 97fc x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI 9814 x23: .cfa -2144 + ^ x24: .cfa -2136 + ^
STACK CFI 9824 x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI 9868 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 991c x27: x27 x28: x28
STACK CFI 9950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9954 .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^ x27: .cfa -2112 + ^ x28: .cfa -2104 + ^ x29: .cfa -2192 + ^
STACK CFI 99e0 x27: x27 x28: x28
STACK CFI 99e4 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9a34 x27: x27 x28: x28
STACK CFI 9a40 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9b8c x27: x27 x28: x28
STACK CFI 9b90 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9ca4 x27: x27 x28: x28
STACK CFI 9ca8 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9cd8 x27: x27 x28: x28
STACK CFI 9cf0 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9cfc x27: x27 x28: x28
STACK CFI 9d04 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 9d34 x27: x27 x28: x28
STACK CFI 9d3c x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI INIT 9d40 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 9d50 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9d58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9d74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9d78 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9d7c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9da0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9dd8 x19: x19 x20: x20
STACK CFI 9ddc x23: x23 x24: x24
STACK CFI 9de4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9de8 x19: x19 x20: x20
STACK CFI 9dec x23: x23 x24: x24
STACK CFI 9df0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9df8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9fdc x23: x23 x24: x24
STACK CFI 9fe4 x19: x19 x20: x20
STACK CFI INIT 9fe8 340 .cfa: sp 0 + .ra: x30
STACK CFI 9ff8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a000 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a01c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI a020 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI a028 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a030 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a044 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a0c0 x27: .cfa -16 + ^
STACK CFI a274 x19: x19 x20: x20
STACK CFI a278 x21: x21 x22: x22
STACK CFI a27c x25: x25 x26: x26
STACK CFI a284 x27: x27
STACK CFI a288 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI a290 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI a294 x19: x19 x20: x20
STACK CFI a298 x21: x21 x22: x22
STACK CFI a29c x25: x25 x26: x26
STACK CFI a2a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a2a4 x27: x27
STACK CFI a2b4 x19: x19 x20: x20
STACK CFI a2b8 x25: x25 x26: x26
STACK CFI a2c0 x21: x21 x22: x22
STACK CFI a2c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a308 x19: x19 x20: x20
STACK CFI a30c x21: x21 x22: x22
STACK CFI a310 x25: x25 x26: x26
STACK CFI a314 x27: x27
STACK CFI a318 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT a328 300 .cfa: sp 0 + .ra: x30
STACK CFI a330 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a338 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a358 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a380 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a3b8 x21: x21 x22: x22
STACK CFI a3c0 x23: x23 x24: x24
STACK CFI a3c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a3c8 x21: x21 x22: x22
STACK CFI a3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a3e4 x21: x21 x22: x22
STACK CFI a3e8 x23: x23 x24: x24
STACK CFI a3ec x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a568 x21: x21 x22: x22
STACK CFI a56c x23: x23 x24: x24
STACK CFI a570 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a604 x23: x23 x24: x24
STACK CFI a60c x21: x21 x22: x22
STACK CFI a610 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT a628 368 .cfa: sp 0 + .ra: x30
STACK CFI a62c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI a630 .cfa: x29 160 +
STACK CFI a634 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI a644 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a64c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI a658 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI a664 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI a848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a84c .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT a990 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2240 24 .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 225c .cfa: sp 0 + .ra: .ra x29: x29
