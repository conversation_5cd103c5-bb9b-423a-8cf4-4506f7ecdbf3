MODULE Linux arm64 ED1A2850D5E85EC90A1144DA24B151C30 libapparmor.so.1
INFO CODE_ID 50281AEDE8D5C95E0A1144DA24B151C38616F651
PUBLIC 3d50 0 parse_record
PUBLIC 3d60 0 free_record
PUBLIC 4368 0 aa_find_mountpoint
PUBLIC 4540 0 aa_is_enabled
PUBLIC 4670 0 aa_splitcon
PUBLIC 46a8 0 aa_getprocattr_raw
PUBLIC 4870 0 aa_getprocattr
PUBLIC 4988 0 aa_change_hat
PUBLIC 4aa0 0 change_hat
PUBLIC 4aa8 0 aa_change_profile
PUBLIC 4b60 0 aa_change_onexec
PUBLIC 4c18 0 aa_change_hatv
PUBLIC 4dc0 0 aa_change_hat_vargs
PUBLIC 4f00 0 aa_stack_profile
PUBLIC 4fb8 0 aa_stack_onexec
PUBLIC 5070 0 aa_gettaskcon
PUBLIC 5088 0 aa_getcon
PUBLIC 50b8 0 aa_getpeercon_raw
PUBLIC 5200 0 aa_getpeercon
PUBLIC 5330 0 aa_query_label
PUBLIC 5580 0 aa_query_file_path_len
PUBLIC 5688 0 aa_query_file_path
PUBLIC 56f8 0 aa_query_link_path_len
PUBLIC 5840 0 aa_query_link_path
PUBLIC 7ad0 0 _aa_is_blacklisted
PUBLIC 7ba0 0 _aa_autofree
PUBLIC 7ba8 0 _aa_autoclose
PUBLIC 7c00 0 _aa_autofclose
PUBLIC 81b8 0 _aa_dirat_for_each
PUBLIC 8818 0 aa_features_ref
PUBLIC 8840 0 aa_features_unref
PUBLIC 88a8 0 aa_features_new
PUBLIC 8a00 0 aa_features_new_from_kernel
PUBLIC 8a10 0 aa_features_new_from_string
PUBLIC 8b90 0 aa_features_write_to_file
PUBLIC 8c70 0 aa_features_is_equal
PUBLIC 8cb0 0 aa_features_supports
PUBLIC 8ee0 0 aa_features_id
PUBLIC 9208 0 aa_kernel_interface_ref
PUBLIC 9230 0 aa_kernel_interface_unref
PUBLIC 9298 0 aa_kernel_interface_new
PUBLIC 9438 0 aa_kernel_interface_load_policy
PUBLIC 9458 0 aa_kernel_interface_load_policy_from_file
PUBLIC 94f0 0 aa_kernel_interface_load_policy_from_fd
PUBLIC 9500 0 aa_kernel_interface_replace_policy
PUBLIC 9520 0 aa_kernel_interface_replace_policy_from_file
PUBLIC 95b8 0 aa_kernel_interface_replace_policy_from_fd
PUBLIC 95c8 0 aa_kernel_interface_remove_policy
PUBLIC 9608 0 aa_kernel_interface_write_policy
PUBLIC 9cb0 0 aa_policy_cache_add_ro_dir
PUBLIC 9d90 0 aa_policy_cache_ref
PUBLIC 9db8 0 aa_policy_cache_unref
PUBLIC 9e50 0 aa_policy_cache_remove
PUBLIC 9e60 0 aa_policy_cache_new
PUBLIC a138 0 aa_policy_cache_replace_all
PUBLIC a1f0 0 aa_policy_cache_no_dirs
PUBLIC a1f8 0 aa_policy_cache_dir_path
PUBLIC a278 0 aa_policy_cache_dirfd
PUBLIC a2d0 0 aa_policy_cache_open
PUBLIC a360 0 aa_policy_cache_filename
PUBLIC a3f8 0 aa_policy_cache_dir_path_preview
STACK CFI INIT 2f88 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3004 x19: .cfa -16 + ^
STACK CFI 303c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3040 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3048 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3060 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3078 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30c8 bd4 .cfa: sp 0 + .ra: x30
STACK CFI 30cc .cfa: sp 2176 +
STACK CFI 30d8 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI 30e4 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI 30f4 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI 3100 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI 311c x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI 3128 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI 3594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3598 .cfa: sp 2176 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x28: .cfa -2088 + ^ x29: .cfa -2176 + ^
STACK CFI INIT 3ca0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3ca4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cc8 x21: .cfa -32 + ^
STACK CFI 3d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d60 184 .cfa: sp 0 + .ra: x30
STACK CFI 3d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d70 x19: .cfa -16 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ee8 40 .cfa: sp 0 + .ra: x30
STACK CFI 3ef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f00 x19: .cfa -16 + ^
STACK CFI 3f20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f28 100 .cfa: sp 0 + .ra: x30
STACK CFI 3f2c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3f34 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3f4c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3f54 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3f80 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3f90 x27: .cfa -48 + ^
STACK CFI 3fd4 x21: x21 x22: x22
STACK CFI 3fd8 x27: x27
STACK CFI 3fe0 x19: x19 x20: x20
STACK CFI 4008 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 400c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 4010 x19: x19 x20: x20
STACK CFI 401c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4020 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4024 x27: .cfa -48 + ^
STACK CFI INIT 4028 ac .cfa: sp 0 + .ra: x30
STACK CFI 402c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 403c x19: .cfa -32 + ^
STACK CFI 40cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40d8 140 .cfa: sp 0 + .ra: x30
STACK CFI 40dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40fc x23: .cfa -16 + ^
STACK CFI 4198 x19: x19 x20: x20
STACK CFI 41a0 x23: x23
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 41b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 41f0 x19: x19 x20: x20
STACK CFI 41f4 x23: x23
STACK CFI 4204 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 4210 x19: x19 x20: x20
STACK CFI 4214 x23: x23
STACK CFI INIT 4218 14c .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4224 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4240 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4250 x23: .cfa -32 + ^
STACK CFI 42e4 x21: x21 x22: x22
STACK CFI 42e8 x23: x23
STACK CFI 430c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4310 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 4318 x21: x21 x22: x22
STACK CFI 431c x23: x23
STACK CFI 4320 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 4324 x21: x21 x22: x22
STACK CFI 4328 x23: x23
STACK CFI 432c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 4344 x21: x21 x22: x22 x23: x23
STACK CFI 435c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4360 x23: .cfa -32 + ^
STACK CFI INIT 4368 154 .cfa: sp 0 + .ra: x30
STACK CFI 436c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4374 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 437c x25: .cfa -160 + ^
STACK CFI 4394 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 43bc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4450 x21: x21 x22: x22
STACK CFI 4480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4484 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x29: .cfa -224 + ^
STACK CFI 4494 x21: x21 x22: x22
STACK CFI 44b8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 44c0 80 .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4534 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4540 130 .cfa: sp 0 + .ra: x30
STACK CFI 4544 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 454c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 457c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 45a4 x23: .cfa -48 + ^
STACK CFI 45dc x23: x23
STACK CFI 45e4 x21: x21 x22: x22
STACK CFI 45e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 45f8 x21: x21 x22: x22
STACK CFI 461c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4620 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 4630 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 463c x23: x23
STACK CFI 4644 x21: x21 x22: x22
STACK CFI 4648 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4658 x21: x21 x22: x22
STACK CFI 465c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 4660 x23: x23
STACK CFI 4664 x21: x21 x22: x22
STACK CFI 4668 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 466c x23: .cfa -48 + ^
STACK CFI INIT 4670 34 .cfa: sp 0 + .ra: x30
STACK CFI 4674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 467c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 46a8 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 46ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 46b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 46bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 46cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 46e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4804 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4870 118 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 487c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4888 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4894 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 48a0 x25: .cfa -16 + ^
STACK CFI 4910 x19: x19 x20: x20
STACK CFI 4914 x23: x23 x24: x24
STACK CFI 4918 x25: x25
STACK CFI 4924 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4928 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 493c x19: x19 x20: x20
STACK CFI 4944 x23: x23 x24: x24
STACK CFI 4948 x25: x25
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4958 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4968 x19: x19 x20: x20
STACK CFI 496c x23: x23 x24: x24
STACK CFI 4970 x25: x25
STACK CFI INIT 4988 118 .cfa: sp 0 + .ra: x30
STACK CFI 498c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4994 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 49a0 x21: .cfa -32 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4a60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4aa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4aa8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ab8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b60 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4b64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c18 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 4c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c28 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c34 x23: .cfa -16 + ^
STACK CFI 4c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4c9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4d5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4dc0 140 .cfa: sp 0 + .ra: x30
STACK CFI 4dc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4dd0 .cfa: x29 128 +
STACK CFI 4dd4 x19: .cfa -112 + ^
STACK CFI 4ed0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ed4 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4f00 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4fb8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4fbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4fc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 504c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5070 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5088 30 .cfa: sp 0 + .ra: x30
STACK CFI 508c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5094 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 50b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 50b8 148 .cfa: sp 0 + .ra: x30
STACK CFI 50bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 50c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 50cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5108 x23: .cfa -32 + ^
STACK CFI 5184 x23: x23
STACK CFI 51ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 51b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 51e0 x23: x23
STACK CFI 51fc x23: .cfa -32 + ^
STACK CFI INIT 5200 130 .cfa: sp 0 + .ra: x30
STACK CFI 5204 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 520c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5218 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5230 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 52ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 52f0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5330 24c .cfa: sp 0 + .ra: x30
STACK CFI 5334 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 533c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 5348 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5358 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 537c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 5394 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 547c x27: x27 x28: x28
STACK CFI 54bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 54c0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 54e8 x27: x27 x28: x28
STACK CFI 54ec x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 54f8 x27: x27 x28: x28
STACK CFI 5510 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 551c x27: x27 x28: x28
STACK CFI 5524 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5538 x27: x27 x28: x28
STACK CFI 553c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5548 x27: x27 x28: x28
STACK CFI 5550 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 555c x27: x27 x28: x28
STACK CFI 5564 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 556c x27: x27 x28: x28
STACK CFI 5578 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 5580 108 .cfa: sp 0 + .ra: x30
STACK CFI 5584 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 558c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5598 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 55b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 55c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 55d0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 567c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5688 6c .cfa: sp 0 + .ra: x30
STACK CFI 568c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5694 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 56a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 56ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 56f8 144 .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5704 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 570c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5718 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5728 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5748 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 582c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5830 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5840 80 .cfa: sp 0 + .ra: x30
STACK CFI 5844 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 584c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5858 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5860 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5868 x25: .cfa -16 + ^
STACK CFI 58bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 58c0 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT 59a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 59a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 59d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5a10 7c .cfa: sp 0 + .ra: x30
STACK CFI 5a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a20 x19: .cfa -16 + ^
STACK CFI 5a44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5a48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a90 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5a94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5a9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5aa8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ab0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5abc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5b50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5b88 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bf8 9c .cfa: sp 0 + .ra: x30
STACK CFI 5bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c18 x23: .cfa -16 + ^
STACK CFI 5c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5c98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ca0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cc8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cf8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d18 30 .cfa: sp 0 + .ra: x30
STACK CFI 5d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5d48 30 .cfa: sp 0 + .ra: x30
STACK CFI 5d68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5d78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5da0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5da8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5db0 84 .cfa: sp 0 + .ra: x30
STACK CFI 5db4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5dbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5dc4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e38 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e48 x19: .cfa -16 + ^
STACK CFI 5ea4 x19: x19
STACK CFI 5ecc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5ee4 x19: x19
STACK CFI 5eec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5f10 11c .cfa: sp 0 + .ra: x30
STACK CFI 5f14 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 5f1c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5f2c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ff8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x29: .cfa -256 + ^
STACK CFI INIT 6030 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6038 ac .cfa: sp 0 + .ra: x30
STACK CFI 603c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6044 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 606c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 60e8 bc .cfa: sp 0 + .ra: x30
STACK CFI 60ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6158 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6160 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6190 x21: x21 x22: x22
STACK CFI 6194 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 619c x21: x21 x22: x22
STACK CFI INIT 61a8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 61ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 624c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6260 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6270 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6278 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 62e0 x21: x21 x22: x22
STACK CFI 62e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 62f8 x21: x21 x22: x22
STACK CFI 62fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6300 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6314 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6320 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6324 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 632c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 633c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 63c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 63ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 63f0 bc .cfa: sp 0 + .ra: x30
STACK CFI 63f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6400 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 649c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 64a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 64b0 9c .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 652c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6530 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6550 ebc .cfa: sp 0 + .ra: x30
STACK CFI 6554 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 656c x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 6574 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 6834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6838 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7410 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7418 74 .cfa: sp 0 + .ra: x30
STACK CFI 7420 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7428 x19: .cfa -32 + ^
STACK CFI 7458 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 745c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 7484 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7490 8c .cfa: sp 0 + .ra: x30
STACK CFI 7494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 749c x19: .cfa -16 + ^
STACK CFI 7518 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7520 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7530 x19: .cfa -16 + ^
STACK CFI 75d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 75d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 75e0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 75e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 75f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 760c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7624 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7708 x19: x19 x20: x20
STACK CFI 770c x25: x25 x26: x26
STACK CFI 771c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7720 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 776c x25: x25 x26: x26
STACK CFI 777c x19: x19 x20: x20
STACK CFI 7788 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 778c .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7794 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 77a4 x19: x19 x20: x20
STACK CFI 77a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 77b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77c0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 77c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 77d0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 77dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 77ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7800 x25: .cfa -16 + ^
STACK CFI 7884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7888 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 78b8 100 .cfa: sp 0 + .ra: x30
STACK CFI 78bc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 78c8 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 78ec x21: .cfa -304 + ^
STACK CFI 7998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 799c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI INIT 79b8 dc .cfa: sp 0 + .ra: x30
STACK CFI 79bc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 79c4 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 7a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a90 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT 7a98 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ab0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ad0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 7ad4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7adc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7af0 x21: .cfa -16 + ^
STACK CFI 7b54 x21: x21
STACK CFI 7b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7b74 x21: x21
STACK CFI 7b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7b88 x21: x21
STACK CFI 7b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ba8 54 .cfa: sp 0 + .ra: x30
STACK CFI 7bac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7bb4 x19: .cfa -16 + ^
STACK CFI 7bf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c00 2c .cfa: sp 0 + .ra: x30
STACK CFI 7c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c0c x19: .cfa -16 + ^
STACK CFI 7c28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c30 cc .cfa: sp 0 + .ra: x30
STACK CFI 7c34 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 7c44 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 7cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cf8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 7d00 138 .cfa: sp 0 + .ra: x30
STACK CFI 7d04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7d0c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7d14 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7d24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7d2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7e14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e38 37c .cfa: sp 0 + .ra: x30
STACK CFI 7e3c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 7e6c x19: .cfa -320 + ^ x20: .cfa -312 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 7e94 x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 7e9c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 7ea0 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 8064 x21: x21 x22: x22
STACK CFI 806c x23: x23 x24: x24
STACK CFI 8070 x27: x27 x28: x28
STACK CFI 80ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 80b0 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI 8164 x21: x21 x22: x22
STACK CFI 8168 x23: x23 x24: x24
STACK CFI 816c x27: x27 x28: x28
STACK CFI 8170 x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 817c x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 8194 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 8198 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 819c x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 81a8 x21: x21 x22: x22
STACK CFI 81ac x23: x23 x24: x24
STACK CFI 81b0 x27: x27 x28: x28
STACK CFI INIT 81b8 180 .cfa: sp 0 + .ra: x30
STACK CFI 81bc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 81c4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 81cc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 81dc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 81fc x27: .cfa -176 + ^
STACK CFI 8240 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 82c8 x21: x21 x22: x22
STACK CFI 8308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 830c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x29: .cfa -256 + ^
STACK CFI 8334 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI INIT 8338 6c .cfa: sp 0 + .ra: x30
STACK CFI 8368 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 83a8 14c .cfa: sp 0 + .ra: x30
STACK CFI 83ac .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 83b4 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 83d4 x21: .cfa -304 + ^
STACK CFI 84a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 84ac .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI INIT 84f8 84 .cfa: sp 0 + .ra: x30
STACK CFI 84fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8504 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8510 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 855c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8580 ac .cfa: sp 0 + .ra: x30
STACK CFI 8584 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 858c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 85b0 x21: .cfa -32 + ^
STACK CFI 8608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 860c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8630 134 .cfa: sp 0 + .ra: x30
STACK CFI 8634 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 863c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8658 x23: .cfa -32 + ^
STACK CFI 8694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 8698 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 86a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 86f4 x21: x21 x22: x22
STACK CFI 86f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8758 x21: x21 x22: x22
STACK CFI 8760 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 8768 ac .cfa: sp 0 + .ra: x30
STACK CFI 876c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8778 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8790 x21: .cfa -32 + ^
STACK CFI 880c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8810 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8818 24 .cfa: sp 0 + .ra: x30
STACK CFI 881c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8824 x19: .cfa -16 + ^
STACK CFI 8838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8840 64 .cfa: sp 0 + .ra: x30
STACK CFI 8844 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 884c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8854 x21: .cfa -16 + ^
STACK CFI 8884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8888 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 88a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 88a8 154 .cfa: sp 0 + .ra: x30
STACK CFI 88ac .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 88b8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 88cc x19: .cfa -208 + ^ x20: .cfa -200 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 898c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8990 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI INIT 8a00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a10 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8a1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8a28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8aa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8ad8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8adc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ae4 x21: .cfa -16 + ^
STACK CFI 8aec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b90 dc .cfa: sp 0 + .ra: x30
STACK CFI 8b94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8ba4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8bb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8c70 40 .cfa: sp 0 + .ra: x30
STACK CFI 8c8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ca8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8cb0 230 .cfa: sp 0 + .ra: x30
STACK CFI 8cb4 .cfa: sp 592 +
STACK CFI 8cb8 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 8cc0 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 8ccc x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 8cf4 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 8df4 x23: x23 x24: x24
STACK CFI 8e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8e24 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x29: .cfa -592 + ^
STACK CFI 8e7c x23: x23 x24: x24
STACK CFI 8e80 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 8eb8 x23: x23 x24: x24
STACK CFI 8ebc x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 8ed4 x23: x23 x24: x24
STACK CFI 8edc x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI INIT 8ee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ee8 54 .cfa: sp 0 + .ra: x30
STACK CFI 8eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ef8 x19: .cfa -16 + ^
STACK CFI 8f18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8f40 18c .cfa: sp 0 + .ra: x30
STACK CFI 8f44 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8f54 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8f64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8f8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8fc0 x25: .cfa -32 + ^
STACK CFI 9064 x25: x25
STACK CFI 909c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 90a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 90b8 x25: .cfa -32 + ^
STACK CFI 90bc x25: x25
STACK CFI 90c8 x25: .cfa -32 + ^
STACK CFI INIT 90d0 134 .cfa: sp 0 + .ra: x30
STACK CFI 90d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 90dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 90ec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9108 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9114 x27: .cfa -32 + ^
STACK CFI 91a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 91a8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9208 24 .cfa: sp 0 + .ra: x30
STACK CFI 920c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9214 x19: .cfa -16 + ^
STACK CFI 9228 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9230 68 .cfa: sp 0 + .ra: x30
STACK CFI 9234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 923c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9244 x21: .cfa -16 + ^
STACK CFI 9284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9288 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9298 19c .cfa: sp 0 + .ra: x30
STACK CFI 929c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 92ac x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 92d0 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 9380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9384 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI INIT 9438 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9458 94 .cfa: sp 0 + .ra: x30
STACK CFI 945c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9464 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9470 x21: .cfa -32 + ^
STACK CFI 94e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 94e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 94f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9500 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9520 94 .cfa: sp 0 + .ra: x30
STACK CFI 9524 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 952c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9538 x21: .cfa -32 + ^
STACK CFI 95ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 95b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 95b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95c8 40 .cfa: sp 0 + .ra: x30
STACK CFI 95cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 95d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9608 1c .cfa: sp 0 + .ra: x30
STACK CFI 960c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9620 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9628 fc .cfa: sp 0 + .ra: x30
STACK CFI 962c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9634 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9640 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9654 x23: .cfa -48 + ^
STACK CFI 96ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 96f0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9728 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 972c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9734 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 973c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 974c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 97d0 x25: .cfa -80 + ^
STACK CFI 9864 x25: x25
STACK CFI 989c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 98a0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 98b0 x25: .cfa -80 + ^
STACK CFI 98bc x25: x25
STACK CFI 98c4 x25: .cfa -80 + ^
STACK CFI 98c8 x25: x25
STACK CFI 98e4 x25: .cfa -80 + ^
STACK CFI INIT 98e8 15c .cfa: sp 0 + .ra: x30
STACK CFI 98ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 98f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9904 x23: .cfa -48 + ^
STACK CFI 990c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 995c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9960 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9a48 164 .cfa: sp 0 + .ra: x30
STACK CFI 9a4c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9a5c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 9a80 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x23: .cfa -176 + ^
STACK CFI 9b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9b74 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 9bb0 78 .cfa: sp 0 + .ra: x30
STACK CFI 9bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9bec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9c28 88 .cfa: sp 0 + .ra: x30
STACK CFI 9c3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9c48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9c54 x21: .cfa -16 + ^
STACK CFI 9c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9cb0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 9cb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9cbc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9cc8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9ce0 x23: .cfa -32 + ^
STACK CFI 9d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9d5c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9d90 24 .cfa: sp 0 + .ra: x30
STACK CFI 9d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d9c x19: .cfa -16 + ^
STACK CFI 9db0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9db8 98 .cfa: sp 0 + .ra: x30
STACK CFI 9dbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9dc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9dcc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9e0c x23: .cfa -16 + ^
STACK CFI 9e3c x23: x23
STACK CFI 9e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9e50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e60 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 9e64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9e6c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9e74 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9e98 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9eac x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9eb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9fb8 x19: x19 x20: x20
STACK CFI 9fbc x21: x21 x22: x22
STACK CFI 9fc0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a03c x19: x19 x20: x20
STACK CFI a044 x21: x21 x22: x22
STACK CFI a080 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a084 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a0a8 x19: x19 x20: x20
STACK CFI a0ac x21: x21 x22: x22
STACK CFI a0b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a0f0 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI a108 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a11c x19: x19 x20: x20
STACK CFI a120 x21: x21 x22: x22
STACK CFI a12c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a130 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT a138 b4 .cfa: sp 0 + .ra: x30
STACK CFI a13c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a144 x21: .cfa -64 + ^
STACK CFI a14c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a1c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a1c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT a1f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1f8 80 .cfa: sp 0 + .ra: x30
STACK CFI a1fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a208 x19: .cfa -16 + ^
STACK CFI a25c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a260 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a278 58 .cfa: sp 0 + .ra: x30
STACK CFI a298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2d0 8c .cfa: sp 0 + .ra: x30
STACK CFI a2d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a2dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a2f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a300 x23: .cfa -16 + ^
STACK CFI a338 x21: x21 x22: x22
STACK CFI a33c x23: x23
STACK CFI a340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a344 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a348 x21: x21 x22: x22
STACK CFI a34c x23: x23
STACK CFI a358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a360 98 .cfa: sp 0 + .ra: x30
STACK CFI a364 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a370 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a3f8 1a4 .cfa: sp 0 + .ra: x30
STACK CFI a3fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a404 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a414 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a428 x23: .cfa -64 + ^
STACK CFI a4e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a4e8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT a5a0 194 .cfa: sp 0 + .ra: x30
STACK CFI INIT a738 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7a0 70 .cfa: sp 0 + .ra: x30
STACK CFI a7a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a7b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a80c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a810 80 .cfa: sp 0 + .ra: x30
STACK CFI a814 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a824 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a844 x21: .cfa -32 + ^
STACK CFI a888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a88c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
