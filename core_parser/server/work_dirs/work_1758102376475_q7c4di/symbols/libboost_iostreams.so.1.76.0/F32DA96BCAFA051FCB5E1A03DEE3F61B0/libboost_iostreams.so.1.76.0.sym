MODULE Linux arm64 F32DA96BCAFA051FCB5E1A03DEE3F61B0 libboost_iostreams.so.1.76.0
INFO CODE_ID 6BA92DF3FACA1F05CB5E1A03DEE3F61B703E3EA6
FILE 0 /usr/src/debug/libgcc/10.2.0-r0/gcc-10.2.0/build.aarch64-fsl-linux.aarch64-fsl-linux/libgcc/../../../../../../../work-shared/gcc-10.2.0-r0/gcc-10.2.0/libgcc/config/aarch64/lse-init.c
FUNC 3ed0 24 0 init_have_lse_atomics
3ed0 4 43 0
3ed4 4 44 0
3ed8 4 43 0
3edc 4 44 0
3ee0 4 45 0
3ee4 4 45 0
3ee8 4 46 0
3eec 4 45 0
3ef0 4 46 0
PUBLIC 3948 0 _init
PUBLIC 3d60 0 boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::rethrow() const
PUBLIC 3e44 0 void boost::throw_exception<std::ios_base::failure[abi:cxx11]>(std::ios_base::failure[abi:cxx11] const&)
PUBLIC 3ef4 0 call_weak_fn
PUBLIC 3f10 0 deregister_tm_clones
PUBLIC 3f40 0 register_tm_clones
PUBLIC 3f80 0 __do_global_dtors_aux
PUBLIC 3fd0 0 frame_dummy
PUBLIC 3fe0 0 boost::iostreams::detail::file_descriptor_impl::file_descriptor_impl()
PUBLIC 3ff0 0 boost::iostreams::detail::file_descriptor_impl::is_open() const
PUBLIC 4000 0 boost::iostreams::detail::file_descriptor_impl::invalid_handle()
PUBLIC 4010 0 boost::iostreams::file_descriptor::file_descriptor(boost::iostreams::file_descriptor const&)
PUBLIC 4040 0 boost::iostreams::file_descriptor::is_open() const
PUBLIC 4054 0 boost::iostreams::file_descriptor::handle() const
PUBLIC 4060 0 boost::iostreams::file_descriptor_source::file_descriptor_source(boost::iostreams::file_descriptor_source const&)
PUBLIC 4064 0 boost::iostreams::file_descriptor_sink::file_descriptor_sink(boost::iostreams::file_descriptor_sink const&)
PUBLIC 4070 0 boost::iostreams::detail::file_descriptor_impl::close_impl(bool, bool)
PUBLIC 40d0 0 boost::iostreams::detail::file_descriptor_impl::read(char*, long)
PUBLIC 4134 0 boost::iostreams::detail::file_descriptor_impl::write(char const*, long)
PUBLIC 4170 0 boost::iostreams::file_descriptor::write(char const*, long)
PUBLIC 41b0 0 boost::iostreams::file_descriptor::read(char*, long)
PUBLIC 4214 0 boost::iostreams::detail::file_descriptor_impl::~file_descriptor_impl()
PUBLIC 4244 0 boost::iostreams::detail::file_descriptor_impl::close()
PUBLIC 4294 0 boost::iostreams::file_descriptor::close()
PUBLIC 42e4 0 void boost::checked_delete<boost::iostreams::detail::file_descriptor_impl>(boost::iostreams::detail::file_descriptor_impl*) [clone .part.0]
PUBLIC 4334 0 boost::iostreams::file_descriptor::open(int, boost::iostreams::file_descriptor_flags)
PUBLIC 43b0 0 boost::iostreams::file_descriptor_source::open(int, boost::iostreams::file_descriptor_flags)
PUBLIC 43b4 0 boost::iostreams::file_descriptor_sink::open(int, boost::iostreams::file_descriptor_flags)
PUBLIC 43c0 0 boost::iostreams::detail::file_descriptor_impl::open(int, boost::iostreams::detail::file_descriptor_impl::flags)
PUBLIC 4430 0 boost::iostreams::file_descriptor::open(int, bool)
PUBLIC 44b0 0 boost::iostreams::file_descriptor_source::open(int, bool)
PUBLIC 44b4 0 boost::iostreams::file_descriptor_sink::open(int, bool)
PUBLIC 44c0 0 boost::iostreams::file_descriptor::file_descriptor()
PUBLIC 4610 0 boost::iostreams::file_descriptor_source::file_descriptor_source(int, boost::iostreams::file_descriptor_flags)
PUBLIC 4670 0 boost::iostreams::file_descriptor_sink::file_descriptor_sink(int, boost::iostreams::file_descriptor_flags)
PUBLIC 46d0 0 boost::iostreams::file_descriptor_source::file_descriptor_source(int, bool)
PUBLIC 4730 0 boost::iostreams::file_descriptor_sink::file_descriptor_sink(int, bool)
PUBLIC 4790 0 boost::iostreams::file_descriptor::file_descriptor(int, bool)
PUBLIC 4910 0 boost::iostreams::file_descriptor::file_descriptor(int, boost::iostreams::file_descriptor_flags)
PUBLIC 4a90 0 boost::iostreams::file_descriptor::init()
PUBLIC 4bd0 0 boost::iostreams::detail::file_descriptor_impl::open(boost::iostreams::detail::path const&, std::_Ios_Openmode)
PUBLIC 4d50 0 boost::iostreams::detail::file_descriptor_impl::seek(long, std::_Ios_Seekdir)
PUBLIC 4dc0 0 boost::iostreams::file_descriptor::seek(long, std::_Ios_Seekdir)
PUBLIC 4e34 0 boost::iostreams::file_descriptor::open(boost::iostreams::detail::path const&, std::_Ios_Openmode, std::_Ios_Openmode)
PUBLIC 4fb0 0 boost::iostreams::file_descriptor::open(char const*, std::_Ios_Openmode)
PUBLIC 50e0 0 boost::iostreams::file_descriptor::file_descriptor(char const*, std::_Ios_Openmode)
PUBLIC 5260 0 boost::iostreams::file_descriptor_source::open(boost::iostreams::detail::path const&, std::_Ios_Openmode)
PUBLIC 52c4 0 boost::iostreams::file_descriptor_source::open(char const*, std::_Ios_Openmode)
PUBLIC 53f0 0 boost::iostreams::file_descriptor_source::file_descriptor_source(char const*, std::_Ios_Openmode)
PUBLIC 5450 0 boost::iostreams::file_descriptor_source::open(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 5574 0 boost::iostreams::file_descriptor_source::file_descriptor_source(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 55d0 0 boost::iostreams::file_descriptor_sink::open(boost::iostreams::detail::path const&, std::_Ios_Openmode)
PUBLIC 5630 0 boost::iostreams::file_descriptor_sink::open(char const*, std::_Ios_Openmode)
PUBLIC 5760 0 boost::iostreams::file_descriptor_sink::file_descriptor_sink(char const*, std::_Ios_Openmode)
PUBLIC 57c0 0 boost::iostreams::file_descriptor_sink::open(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 58e4 0 boost::iostreams::file_descriptor_sink::file_descriptor_sink(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 5940 0 boost::iostreams::file_descriptor::open(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 5a70 0 boost::iostreams::file_descriptor::file_descriptor(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::_Ios_Openmode)
PUBLIC 5bf0 0 boost::detail::sp_counted_base::destroy()
PUBLIC 5c00 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::~sp_counted_impl_p()
PUBLIC 5c10 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::get_deleter(std::type_info const&)
PUBLIC 5c20 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::get_local_deleter(std::type_info const&)
PUBLIC 5c30 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::get_untyped_deleter()
PUBLIC 5c40 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::~sp_counted_impl_p()
PUBLIC 5c50 0 boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5cb0 0 non-virtual thunk to boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5d10 0 non-virtual thunk to boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5d70 0 boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5dd4 0 non-virtual thunk to boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5e40 0 non-virtual thunk to boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::~wrapexcept()
PUBLIC 5eb0 0 boost::wrapexcept<std::ios_base::failure[abi:cxx11]>::clone() const
PUBLIC 6120 0 boost::iostreams::detail::system_failure[abi:cxx11](char const*)
PUBLIC 62a0 0 boost::iostreams::detail::path::~path()
PUBLIC 6300 0 boost::iostreams::detail::throw_system_failure(char const*)
PUBLIC 6340 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::file_descriptor_impl>::dispose()
PUBLIC 63a0 0 boost::detail::sp_counted_base::release()
PUBLIC 6480 0 boost::iostreams::detail::mapped_file_impl::mapped_file_impl()
PUBLIC 6594 0 boost::iostreams::detail::mapped_file_impl::alignment()
PUBLIC 65b0 0 boost::iostreams::detail::mapped_file_impl::unmap_file()
PUBLIC 65d0 0 boost::iostreams::detail::mapped_file_impl::clear(bool)
PUBLIC 66c0 0 boost::iostreams::mapped_file_source::mapped_file_source(boost::iostreams::mapped_file_source const&)
PUBLIC 66f0 0 boost::iostreams::mapped_file_source::is_open() const
PUBLIC 6704 0 boost::iostreams::mapped_file_source::operator int boost::iostreams::mapped_file_source::safe_bool_helper::*() const
PUBLIC 6720 0 boost::iostreams::mapped_file_source::operator!() const
PUBLIC 6730 0 boost::iostreams::mapped_file_source::flags() const
PUBLIC 6740 0 boost::iostreams::mapped_file_source::size() const
PUBLIC 6750 0 boost::iostreams::mapped_file_source::data() const
PUBLIC 6760 0 boost::iostreams::mapped_file_source::begin() const
PUBLIC 6764 0 boost::iostreams::mapped_file_source::end() const
PUBLIC 6794 0 boost::iostreams::mapped_file_source::alignment()
PUBLIC 67b0 0 boost::iostreams::mapped_file::mapped_file(boost::iostreams::mapped_file const&)
PUBLIC 67c0 0 boost::iostreams::mapped_file_sink::mapped_file_sink(boost::iostreams::mapped_file_sink const&)
PUBLIC 67c4 0 boost::iostreams::detail::mapped_file_params_base::normalize()
PUBLIC 6914 0 boost::iostreams::detail::mapped_file_impl::close()
PUBLIC 6994 0 boost::iostreams::detail::mapped_file_impl::cleanup_and_throw(char const*)
PUBLIC 69e0 0 boost::iostreams::detail::mapped_file_impl::open_file(boost::iostreams::basic_mapped_file_params<boost::iostreams::detail::path>)
PUBLIC 6b00 0 boost::iostreams::detail::mapped_file_impl::try_map_file(boost::iostreams::basic_mapped_file_params<boost::iostreams::detail::path>)
PUBLIC 6b70 0 boost::iostreams::detail::mapped_file_impl::map_file(boost::iostreams::basic_mapped_file_params<boost::iostreams::detail::path>&)
PUBLIC 6e60 0 boost::iostreams::mapped_file_source::close()
PUBLIC 6ee0 0 boost::iostreams::detail::mapped_file_impl::~mapped_file_impl()
PUBLIC 6fa0 0 boost::iostreams::detail::mapped_file_impl::open(boost::iostreams::basic_mapped_file_params<boost::iostreams::detail::path>)
PUBLIC 7250 0 boost::iostreams::mapped_file_source::open_impl(boost::iostreams::basic_mapped_file_params<boost::iostreams::detail::path> const&)
PUBLIC 7450 0 boost::iostreams::detail::mapped_file_impl::resize(long)
PUBLIC 77f0 0 boost::iostreams::mapped_file::resize(long)
PUBLIC 7800 0 boost::iostreams::mapped_file_source::init()
PUBLIC 7980 0 boost::iostreams::mapped_file_source::mapped_file_source()
PUBLIC 7b10 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::~sp_counted_impl_p()
PUBLIC 7b20 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::get_deleter(std::type_info const&)
PUBLIC 7b30 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::get_local_deleter(std::type_info const&)
PUBLIC 7b40 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::get_untyped_deleter()
PUBLIC 7b50 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::~sp_counted_impl_p()
PUBLIC 7b60 0 boost::iostreams::detail::path::path(boost::iostreams::detail::path const&)
PUBLIC 7cf0 0 boost::detail::sp_counted_impl_p<boost::iostreams::detail::mapped_file_impl>::dispose()
PUBLIC 7dc0 0 void boost::checked_delete<boost::iostreams::detail::mapped_file_impl>(boost::iostreams::detail::mapped_file_impl*)
PUBLIC 7e80 0 __aarch64_ldadd4_relax
PUBLIC 7eb0 0 __aarch64_ldadd4_acq_rel
PUBLIC 7ee0 0 _fini
STACK CFI INIT 3f10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f80 48 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f8c x19: .cfa -16 + ^
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3fd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bf0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c50 58 .cfa: sp 0 + .ra: x30
STACK CFI 5c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c68 x19: .cfa -16 + ^
STACK CFI 5ca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d60 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 5d70 64 .cfa: sp 0 + .ra: x30
STACK CFI 5d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d88 x19: .cfa -16 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5cb0 58 .cfa: sp 0 + .ra: x30
STACK CFI 5cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cc8 x19: .cfa -16 + ^
STACK CFI 5d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d10 58 .cfa: sp 0 + .ra: x30
STACK CFI 5d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d28 x19: .cfa -16 + ^
STACK CFI 5d64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5dd4 6c .cfa: sp 0 + .ra: x30
STACK CFI 5dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5de8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5e40 6c .cfa: sp 0 + .ra: x30
STACK CFI 5e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5eb0 268 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5ebc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ecc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 600c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6010 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 608c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6090 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6120 178 .cfa: sp 0 + .ra: x30
STACK CFI 6124 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 612c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6134 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 613c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6238 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3fe0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ff0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4000 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4010 30 .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 62a0 60 .cfa: sp 0 + .ra: x30
STACK CFI 62a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62b0 x19: .cfa -16 + ^
STACK CFI 62f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 62f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 62fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4040 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4054 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4064 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e44 88 .cfa: sp 0 + .ra: x30
STACK CFI 3e48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e54 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 6300 38 .cfa: sp 0 + .ra: x30
STACK CFI 6304 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 630c x19: .cfa -48 + ^
STACK CFI INIT 4070 58 .cfa: sp 0 + .ra: x30
STACK CFI 4074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 407c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 40d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 40d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4128 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4134 3c .cfa: sp 0 + .ra: x30
STACK CFI 4138 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4144 x19: .cfa -16 + ^
STACK CFI 4160 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4164 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4170 40 .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4184 x19: .cfa -16 + ^
STACK CFI 41a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41b0 64 .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41bc x21: .cfa -16 + ^
STACK CFI 41c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4208 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4214 30 .cfa: sp 0 + .ra: x30
STACK CFI 4230 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4244 50 .cfa: sp 0 + .ra: x30
STACK CFI 4248 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4250 x19: .cfa -16 + ^
STACK CFI 4278 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 427c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4294 50 .cfa: sp 0 + .ra: x30
STACK CFI 4298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42a0 x19: .cfa -16 + ^
STACK CFI 42c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 42e4 50 .cfa: sp 0 + .ra: x30
STACK CFI 42e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42f0 x19: .cfa -16 + ^
STACK CFI 4318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 431c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4330 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4334 74 .cfa: sp 0 + .ra: x30
STACK CFI 4338 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4348 x19: .cfa -32 + ^
STACK CFI 436c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 43b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43b4 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 43c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43d0 x19: .cfa -32 + ^
STACK CFI 43f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4430 80 .cfa: sp 0 + .ra: x30
STACK CFI 4434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4450 x19: .cfa -32 + ^
STACK CFI 4474 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4478 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 44b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44b4 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6340 60 .cfa: sp 0 + .ra: x30
STACK CFI 6344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 634c x19: .cfa -16 + ^
STACK CFI 6378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 637c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 639c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 63a0 dc .cfa: sp 0 + .ra: x30
STACK CFI 63a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 63cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6454 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44c0 150 .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44d8 x21: .cfa -16 + ^
STACK CFI 4538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 453c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4610 5c .cfa: sp 0 + .ra: x30
STACK CFI 4614 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 461c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4628 x21: .cfa -16 + ^
STACK CFI 464c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4650 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4670 5c .cfa: sp 0 + .ra: x30
STACK CFI 4674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 467c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4688 x21: .cfa -16 + ^
STACK CFI 46ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 46b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 46d0 5c .cfa: sp 0 + .ra: x30
STACK CFI 46d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 46e8 x21: .cfa -16 + ^
STACK CFI 470c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4710 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4730 5c .cfa: sp 0 + .ra: x30
STACK CFI 4734 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 473c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4748 x21: .cfa -16 + ^
STACK CFI 476c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4770 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4790 180 .cfa: sp 0 + .ra: x30
STACK CFI 4794 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 479c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47b0 x23: .cfa -16 + ^
STACK CFI 4828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 482c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4910 180 .cfa: sp 0 + .ra: x30
STACK CFI 4914 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 491c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4928 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4934 x23: .cfa -16 + ^
STACK CFI 49a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 49ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4a90 13c .cfa: sp 0 + .ra: x30
STACK CFI 4a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4aa8 x21: .cfa -16 + ^
STACK CFI 4b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4bd0 17c .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4bdc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4be8 x21: .cfa -64 + ^
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4c60 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4d50 70 .cfa: sp 0 + .ra: x30
STACK CFI 4d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 4dc0 74 .cfa: sp 0 + .ra: x30
STACK CFI 4dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4dfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e00 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 4e34 17c .cfa: sp 0 + .ra: x30
STACK CFI 4e38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4e40 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4e50 x21: .cfa -64 + ^
STACK CFI 4ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ec4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4fb0 12c .cfa: sp 0 + .ra: x30
STACK CFI 4fb4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4fc0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4fc8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 507c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5080 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 50e0 180 .cfa: sp 0 + .ra: x30
STACK CFI 50e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 50ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 50f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5104 x23: .cfa -16 + ^
STACK CFI 5178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 517c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5260 64 .cfa: sp 0 + .ra: x30
STACK CFI 5274 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 527c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 52c4 128 .cfa: sp 0 + .ra: x30
STACK CFI 52c8 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 52d4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 52dc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 538c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5390 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 53f0 5c .cfa: sp 0 + .ra: x30
STACK CFI 53f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5408 x21: .cfa -16 + ^
STACK CFI 542c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5430 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5450 124 .cfa: sp 0 + .ra: x30
STACK CFI 5454 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5460 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5468 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5518 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5574 5c .cfa: sp 0 + .ra: x30
STACK CFI 5578 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 558c x21: .cfa -16 + ^
STACK CFI 55b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 55b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 55d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 55e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 55e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 5630 128 .cfa: sp 0 + .ra: x30
STACK CFI 5634 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5640 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5648 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 56f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 56fc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5760 5c .cfa: sp 0 + .ra: x30
STACK CFI 5764 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 576c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5778 x21: .cfa -16 + ^
STACK CFI 579c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 57a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57c0 124 .cfa: sp 0 + .ra: x30
STACK CFI 57c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 57d0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 57d8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5888 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 58e4 5c .cfa: sp 0 + .ra: x30
STACK CFI 58e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58fc x21: .cfa -16 + ^
STACK CFI 5920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5924 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5940 128 .cfa: sp 0 + .ra: x30
STACK CFI 5944 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5950 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5958 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5a0c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5a70 180 .cfa: sp 0 + .ra: x30
STACK CFI 5a74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5a7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5a94 x23: .cfa -16 + ^
STACK CFI 5b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5b0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b60 184 .cfa: sp 0 + .ra: x30
STACK CFI 7b64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7b74 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7c3c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6480 114 .cfa: sp 0 + .ra: x30
STACK CFI 6484 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 6490 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 64a8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6578 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT 6594 18 .cfa: sp 0 + .ra: x30
STACK CFI 6598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 65a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 65b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 65cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 65d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 65e8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 65f8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6604 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 66a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 66a8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT 66c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 66d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 66e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 66f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6704 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6720 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6730 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6740 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6750 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6764 30 .cfa: sp 0 + .ra: x30
STACK CFI 6768 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6770 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6794 18 .cfa: sp 0 + .ra: x30
STACK CFI 6798 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 67c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67c4 150 .cfa: sp 0 + .ra: x30
STACK CFI 67c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 67f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6818 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 684c x19: x19 x20: x20
STACK CFI 6850 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6884 x19: x19 x20: x20
STACK CFI 6888 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 68bc x19: x19 x20: x20
STACK CFI 68c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 6914 80 .cfa: sp 0 + .ra: x30
STACK CFI 6918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6920 x19: .cfa -16 + ^
STACK CFI 6960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6964 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6994 4c .cfa: sp 0 + .ra: x30
STACK CFI 6998 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 69e0 118 .cfa: sp 0 + .ra: x30
STACK CFI 69e4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 69ec x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 69f4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 6a00 x23: .cfa -144 + ^
STACK CFI 6a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6a70 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT 6b00 70 .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b14 x19: .cfa -16 + ^
STACK CFI 6b5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b70 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 6b74 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 6b7c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 6b84 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 6b90 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 6b9c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 6ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6ce4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 6dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6e00 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI INIT 6e60 80 .cfa: sp 0 + .ra: x30
STACK CFI 6e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e6c x19: .cfa -16 + ^
STACK CFI 6eac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ee0 bc .cfa: sp 0 + .ra: x30
STACK CFI 6ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6eec x19: .cfa -16 + ^
STACK CFI 6f80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6f8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7cf0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7cfc x19: .cfa -16 + ^
STACK CFI 7d9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7da0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7da8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7dac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6fa0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 6fa4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6fac x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6fc4 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^
STACK CFI 7144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7148 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI INIT 7250 1fc .cfa: sp 0 + .ra: x30
STACK CFI 7254 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 725c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 7264 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7274 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7290 x25: .cfa -144 + ^
STACK CFI 738c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7390 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x29: .cfa -208 + ^
STACK CFI INIT 7450 398 .cfa: sp 0 + .ra: x30
STACK CFI 7454 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 745c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7470 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 761c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI INIT 77f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7dc0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7dc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7dd0 x19: .cfa -16 + ^
STACK CFI 7e6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7800 178 .cfa: sp 0 + .ra: x30
STACK CFI 7804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 780c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7818 x21: .cfa -16 + ^
STACK CFI 78a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 78a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7920 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7980 18c .cfa: sp 0 + .ra: x30
STACK CFI 7984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 798c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7998 x21: .cfa -16 + ^
STACK CFI 7a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7aa8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7eb0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ed0 24 .cfa: sp 0 + .ra: x30
STACK CFI 3ed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3eec .cfa: sp 0 + .ra: .ra x29: x29
