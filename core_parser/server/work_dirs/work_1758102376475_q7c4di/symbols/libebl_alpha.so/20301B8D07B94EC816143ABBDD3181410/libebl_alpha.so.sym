MODULE Linux arm64 20301B8D07B94EC816143ABBDD3181410 libebl_alpha.so
INFO CODE_ID 8D1B3020B907C84E16143ABBDD3181410F9473B8
PUBLIC 13c0 0 alpha_init
STACK CFI INIT 11d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1208 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1248 48 .cfa: sp 0 + .ra: x30
STACK CFI 124c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1254 x19: .cfa -16 + ^
STACK CFI 128c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1290 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1298 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12c8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1300 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1310 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1320 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1324 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 132c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1398 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 13c0 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14f8 194 .cfa: sp 0 + .ra: x30
STACK CFI 14fc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1504 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1550 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1554 .cfa: sp 176 + .ra: .cfa -168 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI 1558 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1568 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 15d8 x25: .cfa -112 + ^
STACK CFI 1628 x23: x23 x24: x24 x25: x25
STACK CFI 162c x19: x19 x20: x20
STACK CFI 1630 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1634 x19: x19 x20: x20
STACK CFI 1638 x23: x23 x24: x24
STACK CFI 163c x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^
STACK CFI 164c x19: x19 x20: x20
STACK CFI 1650 x23: x23 x24: x24
STACK CFI 1654 x25: x25
STACK CFI 1658 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^
STACK CFI 165c x25: x25
STACK CFI 1664 x19: x19 x20: x20
STACK CFI 1670 x23: x23 x24: x24
STACK CFI 1674 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 167c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 1680 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1684 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1688 x25: .cfa -112 + ^
STACK CFI INIT 1690 34 .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16c8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f0 228 .cfa: sp 0 + .ra: x30
STACK CFI 16f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 16fc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1710 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1724 x23: .cfa -96 + ^
STACK CFI 17cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17d0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1918 290 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ba8 218 .cfa: sp 0 + .ra: x30
STACK CFI 1bac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1bb8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1bc8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1bd0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1c54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ca0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1dc0 30 .cfa: sp 0 + .ra: x30
