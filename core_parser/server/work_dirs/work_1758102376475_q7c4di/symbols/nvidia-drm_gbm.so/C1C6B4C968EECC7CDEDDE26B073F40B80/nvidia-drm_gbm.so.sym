MODULE Linux arm64 C1C6B4C968EECC7CDEDDE26B073F40B80 libnvidia-allocator.so.1
INFO CODE_ID C9B4C6C1EE687CCCDEDDE26B073F40B8D1F0151A
PUBLIC 1fc8 0 gbmint_get_backend
STACK CFI INIT 10b4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 50 .cfa: sp 0 + .ra: x30
STACK CFI 1130 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1138 x19: .cfa -16 + ^
STACK CFI 1168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1170 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1178 54 .cfa: sp 0 + .ra: x30
STACK CFI 1188 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 119c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 11a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 11c4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 11d4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 11f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1200 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1208 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1210 30 .cfa: sp 0 + .ra: x30
STACK CFI 1214 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 122c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1230 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 123c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1240 6c .cfa: sp 0 + .ra: x30
STACK CFI 1244 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 124c .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 12a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 12b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 12b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 12e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12fc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1300 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 1304 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1308 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 131c .ra: .cfa -16 + ^
STACK CFI 1530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1534 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 155c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1560 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1598 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 15c0 2c .cfa: sp 0 + .ra: x30
STACK CFI 15d0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 15e8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 15f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 1600 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1618 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1620 88 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1654 .cfa: sp 0 + .ra: .ra
STACK CFI 1658 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 16a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 16c0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 16d8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 16e0 110 .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16f0 .ra: .cfa -8 + ^
STACK CFI 16fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1708 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1714 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1734 x27: .cfa -16 + ^
STACK CFI 176c x21: x21 x22: x22
STACK CFI 1770 x23: x23 x24: x24
STACK CFI 1774 x25: x25 x26: x26
STACK CFI 1778 x27: x27
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1788 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 17a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17a8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 17c4 x21: x21 x22: x22
STACK CFI 17c8 x23: x23 x24: x24
STACK CFI 17cc x25: x25 x26: x26
STACK CFI 17d0 x27: x27
STACK CFI 17d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 17e4 x21: x21 x22: x22
STACK CFI 17e8 x23: x23 x24: x24
STACK CFI 17ec x25: x25 x26: x26
STACK CFI INIT 17f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 17f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 1824 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1828 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 1858 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 185c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 1878 13c .cfa: sp 0 + .ra: x30
STACK CFI 18c8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 18fc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 19b8 120 .cfa: sp 0 + .ra: x30
STACK CFI 19bc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 19c8 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 19f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a40 x21: x21 x22: x22
STACK CFI 1a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 1a50 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 1a74 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1aa4 x21: x21 x22: x22
STACK CFI 1ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 1ab4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 1ad8 120 .cfa: sp 0 + .ra: x30
STACK CFI 1adc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1aec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1af8 .ra: .cfa -40 + ^ x23: .cfa -48 + ^
STACK CFI 1bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1bd8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 1be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1bec .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT 1bf8 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 1bfc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1c10 .ra: .cfa -24 + ^
STACK CFI 1c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1c40 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1c44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1c4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1c50 x25: .cfa -32 + ^
STACK CFI 1cc8 x21: x21 x22: x22
STACK CFI 1ccc x23: x23 x24: x24
STACK CFI 1cd0 x25: x25
STACK CFI 1cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1cdc .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1cf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1cf8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1cfc x25: .cfa -32 + ^
STACK CFI 1d40 x21: x21 x22: x22
STACK CFI 1d44 x23: x23 x24: x24
STACK CFI 1d48 x25: x25
STACK CFI 1d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d6c .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1da8 x21: x21 x22: x22
STACK CFI 1dac x23: x23 x24: x24
STACK CFI 1db0 x25: x25
STACK CFI 1db4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1de0 x21: x21 x22: x22
STACK CFI 1de4 x23: x23 x24: x24
STACK CFI 1de8 x25: x25
STACK CFI INIT 1df0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 1df4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1e00 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1e0c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1e20 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1e30 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1e38 .ra: .cfa -48 + ^
STACK CFI 1f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1f08 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1fa8 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 1fc8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fd8 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 1fdc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1ff4 .ra: .cfa -48 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 211c .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 2180 d8 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2190 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21a0 .ra: .cfa -16 + ^
STACK CFI 21f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 21f4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2218 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2258 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f8 108 .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2304 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 235c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 23b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 23b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 23f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 23f8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2400 104 .cfa: sp 0 + .ra: x30
STACK CFI 2404 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2408 .ra: .cfa -48 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2474 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 24f0 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 2508 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2578 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2588 84 .cfa: sp 0 + .ra: x30
STACK CFI 258c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2594 .ra: .cfa -16 + ^
STACK CFI 25c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 25c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2610 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2614 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2680 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2684 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 26bc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 26c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 26c8 40 .cfa: sp 0 + .ra: x30
STACK CFI 26cc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 26fc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2700 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2704 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2708 2c .cfa: sp 0 + .ra: x30
STACK CFI 2714 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 272c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2738 200 .cfa: sp 0 + .ra: x30
STACK CFI 273c .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2748 .ra: .cfa -128 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2858 .cfa: sp 160 + .ra: .cfa -128 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT 2938 88 .cfa: sp 0 + .ra: x30
STACK CFI 295c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 297c .cfa: sp 0 + .ra: .ra
STACK CFI 2988 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 29c0 240 .cfa: sp 0 + .ra: x30
STACK CFI 29c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29d0 .ra: .cfa -16 + ^
STACK CFI 2ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2ae4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2b2c .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2c00 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c48 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cc8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cd8 164 .cfa: sp 0 + .ra: x30
STACK CFI 2cdc .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2cec .ra: .cfa -64 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2d60 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2de0 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 2e40 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2e44 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e4c .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2e9c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 2eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2ef0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2f28 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 2f2c .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 2f38 .ra: .cfa -248 + ^
STACK CFI 2f44 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2f90 x23: .cfa -256 + ^
STACK CFI 2fe4 x23: x23
STACK CFI 2ffc x21: x21 x22: x22
STACK CFI 3004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3008 .cfa: sp 288 + .ra: .cfa -248 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^
STACK CFI 3160 x21: x21 x22: x22
STACK CFI 3164 x23: x23
STACK CFI 316c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3170 .cfa: sp 288 + .ra: .cfa -248 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^
STACK CFI 3188 x21: x21 x22: x22
STACK CFI 318c x23: x23
STACK CFI 3194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3198 .cfa: sp 288 + .ra: .cfa -248 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^
STACK CFI 31e0 x23: x23
STACK CFI 31e4 x21: x21 x22: x22
STACK CFI 31fc x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^
STACK CFI 3214 x21: x21 x22: x22
STACK CFI 3218 x23: x23
STACK CFI INIT 3220 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3240 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3260 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3298 d0 .cfa: sp 0 + .ra: x30
STACK CFI 32a0 .cfa: sp 48 +
STACK CFI 3358 .cfa: sp 0 +
STACK CFI 335c .cfa: sp 48 +
STACK CFI 3364 .cfa: sp 0 +
STACK CFI INIT 3368 9c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3408 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3418 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34c8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 34cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34d8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23
STACK CFI 34fc .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 3508 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3594 x21: x21 x22: x22
STACK CFI 359c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23
STACK CFI 35a0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 35a4 x21: x21 x22: x22
STACK CFI 35ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23
STACK CFI INIT 35b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 35b4 .cfa: sp 32 +
STACK CFI 35c8 .ra: .cfa -16 + ^
STACK CFI 35d4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 35d8 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3658 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3728 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3788 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 378c .cfa: sp 224 +
STACK CFI 3790 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 379c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 37a4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 37b0 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 37b8 .ra: .cfa -128 + ^
STACK CFI 39f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 39f8 .cfa: sp 224 + .ra: .cfa -128 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 3a30 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3a34 .cfa: sp 144 +
STACK CFI 3a38 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3a44 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3a50 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3a5c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3a68 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3a70 .ra: .cfa -32 + ^
STACK CFI 3b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3b64 .cfa: sp 144 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3bf8 38 .cfa: sp 0 + .ra: x30
STACK CFI 3bfc .cfa: sp 48 +
STACK CFI 3c20 .ra: .cfa -16 + ^
STACK CFI 3c2c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3c30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c48 34 .cfa: sp 0 + .ra: x30
STACK CFI 3c4c .cfa: sp 48 +
STACK CFI 3c6c .ra: .cfa -16 + ^
STACK CFI 3c78 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3c80 30 .cfa: sp 0 + .ra: x30
STACK CFI 3c84 .cfa: sp 48 +
STACK CFI 3ca0 .ra: .cfa -16 + ^
STACK CFI 3cac .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3cb0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc8 38 .cfa: sp 0 + .ra: x30
STACK CFI 3ccc .cfa: sp 48 +
STACK CFI 3cf0 .ra: .cfa -16 + ^
STACK CFI 3cfc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3d00 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d18 34 .cfa: sp 0 + .ra: x30
STACK CFI 3d1c .cfa: sp 48 +
STACK CFI 3d3c .ra: .cfa -16 + ^
STACK CFI 3d48 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3d50 30 .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 48 +
STACK CFI 3d70 .ra: .cfa -16 + ^
STACK CFI 3d7c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3d80 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d98 250 .cfa: sp 0 + .ra: x30
STACK CFI 3d9c .cfa: sp 176 +
STACK CFI 3da0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 3dc0 .ra: .cfa -80 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3dd8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3de0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 3de4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 3fc4 x23: x23 x24: x24
STACK CFI 3fc8 x25: x25 x26: x26
STACK CFI 3fcc x27: x27 x28: x28
STACK CFI 3fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3fe0 .cfa: sp 176 + .ra: .cfa -80 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 3fe8 40 .cfa: sp 0 + .ra: x30
STACK CFI 3fec .cfa: sp 64 +
STACK CFI 4018 .ra: .cfa -16 + ^
STACK CFI 4024 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 4028 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4038 b8 .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 128 +
STACK CFI 4040 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 404c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4058 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4078 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 408c .ra: .cfa -24 + ^ x27: .cfa -32 + ^
STACK CFI 40ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI INIT 40f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4100 2c .cfa: sp 0 + .ra: x30
STACK CFI 4104 .cfa: sp 48 +
STACK CFI 411c .ra: .cfa -16 + ^
STACK CFI 4128 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 4130 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4140 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4198 3c .cfa: sp 0 + .ra: x30
STACK CFI 419c .cfa: sp 64 +
STACK CFI 41c4 .ra: .cfa -16 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 41d8 38 .cfa: sp 0 + .ra: x30
STACK CFI 41dc .cfa: sp 48 +
STACK CFI 4200 .ra: .cfa -16 + ^
STACK CFI 420c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 4210 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4220 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4270 5c .cfa: sp 0 + .ra: x30
STACK CFI 427c .cfa: sp 48 +
STACK CFI 42b8 .ra: .cfa -16 + ^
STACK CFI 42c4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 42d0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4318 64 .cfa: sp 0 + .ra: x30
STACK CFI 4324 .cfa: sp 64 +
STACK CFI 4368 .ra: .cfa -16 + ^
STACK CFI 4374 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 4380 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c8 68 .cfa: sp 0 + .ra: x30
STACK CFI 43cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43d4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 43f8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 442c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 4430 84 .cfa: sp 0 + .ra: x30
STACK CFI 4434 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4440 .ra: .cfa -16 + ^
STACK CFI 445c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4460 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 44b8 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 44bc .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 44c4 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 44e4 .ra: .cfa -240 + ^
STACK CFI 44f8 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 4504 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 451c x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 464c x21: x21 x22: x22
STACK CFI 4650 x23: x23 x24: x24
STACK CFI 4654 x27: x27 x28: x28
STACK CFI 4660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 4664 .cfa: sp 320 + .ra: .cfa -240 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 4694 x21: x21 x22: x22
STACK CFI 4698 x23: x23 x24: x24
STACK CFI 46a0 x27: x27 x28: x28
STACK CFI 46a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 46ac .cfa: sp 320 + .ra: .cfa -240 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 47e4 x21: x21 x22: x22
STACK CFI 47e8 x23: x23 x24: x24
STACK CFI 47ec x27: x27 x28: x28
STACK CFI 4814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 4818 .cfa: sp 320 + .ra: .cfa -240 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 4914 x21: x21 x22: x22
STACK CFI 4918 x23: x23 x24: x24
STACK CFI 491c x27: x27 x28: x28
STACK CFI 4920 x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI INIT 4998 11c .cfa: sp 0 + .ra: x30
STACK CFI 499c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 49ac .ra: .cfa -16 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 49b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 49bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a78 x21: x21 x22: x22
STACK CFI 4a7c x23: x23 x24: x24
STACK CFI 4a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 4a90 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4aa8 x21: x21 x22: x22
STACK CFI 4aac x23: x23 x24: x24
