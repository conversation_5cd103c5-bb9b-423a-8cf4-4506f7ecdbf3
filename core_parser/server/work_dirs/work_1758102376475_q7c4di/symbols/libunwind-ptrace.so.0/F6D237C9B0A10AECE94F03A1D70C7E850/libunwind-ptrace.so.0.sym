MODULE Linux arm64 F6D237C9B0A10AECE94F03A1D70C7E850 libunwind-ptrace.so.0
INFO CODE_ID C937D2F6A1B0EC0AE94F03A1D70C7E8556C051D0
PUBLIC 15d0 0 _UPT_access_fpreg
PUBLIC 16c0 0 _UPT_access_mem
PUBLIC 1760 0 _UPT_access_reg
PUBLIC 1808 0 _UPT_create
PUBLIC 1848 0 _UPT_destroy
PUBLIC 1a60 0 _UPT_find_proc_info
PUBLIC 1b38 0 _UPT_get_dyn_info_list_addr
PUBLIC 1b40 0 _UPT_put_unwind_info
PUBLIC 1b70 0 _UPT_get_proc_name
PUBLIC 1b90 0 _UPT_resume
STACK CFI INIT e78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee8 48 .cfa: sp 0 + .ra: x30
STACK CFI eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ef4 x19: .cfa -16 + ^
STACK CFI f2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f38 208 .cfa: sp 0 + .ra: x30
STACK CFI f48 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI f58 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f68 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f70 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI f9c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fa8 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI fac x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI fb0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI fc0 x19: x19 x20: x20
STACK CFI fc8 x23: x23 x24: x24
STACK CFI fd4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fd8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1140 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 1144 .cfa: sp 272 +
STACK CFI 114c .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1154 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1170 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1194 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1284 .cfa: sp 272 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 1294 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 12a4 x23: x23 x24: x24
STACK CFI 12a8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1400 x23: x23 x24: x24
STACK CFI 1404 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 140c x23: x23 x24: x24
STACK CFI 1418 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 14e4 x23: x23 x24: x24
STACK CFI 14e8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 14f8 x23: x23 x24: x24
STACK CFI 1500 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT 1508 c8 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1514 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1520 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1534 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1550 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 15cc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 15d0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 15d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15e0 x23: .cfa -16 + ^
STACK CFI 15ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1660 x19: x19 x20: x20
STACK CFI 1664 x21: x21 x22: x22
STACK CFI 166c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 1670 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16ac x19: x19 x20: x20
STACK CFI 16b0 x21: x21 x22: x22
STACK CFI 16bc .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI INIT 16c0 9c .cfa: sp 0 + .ra: x30
STACK CFI 16c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16e4 x23: .cfa -16 + ^
STACK CFI 1728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 172c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1760 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1764 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 176c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1774 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1780 x23: .cfa -16 + ^
STACK CFI 17dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1808 3c .cfa: sp 0 + .ra: x30
STACK CFI 180c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1818 x19: .cfa -16 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1848 30 .cfa: sp 0 + .ra: x30
STACK CFI 184c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1854 x19: .cfa -16 + ^
STACK CFI 1874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1878 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 1880 .cfa: sp 4192 +
STACK CFI 1884 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 188c x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 189c x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 18b4 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 1a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a10 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x21: .cfa -4160 + ^ x22: .cfa -4152 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x29: .cfa -4192 + ^
STACK CFI INIT 1a60 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1a64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a90 x23: .cfa -16 + ^
STACK CFI 1acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1ad0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1afc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1b38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b40 30 .cfa: sp 0 + .ra: x30
STACK CFI 1b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b54 x19: .cfa -16 + ^
STACK CFI 1b68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b70 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b90 28 .cfa: sp 0 + .ra: x30
STACK CFI 1b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bb4 .cfa: sp 0 + .ra: .ra x29: x29
