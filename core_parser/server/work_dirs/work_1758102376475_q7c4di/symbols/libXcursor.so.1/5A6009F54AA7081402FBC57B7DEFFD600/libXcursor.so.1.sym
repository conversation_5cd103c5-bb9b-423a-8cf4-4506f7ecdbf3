MODULE Linux arm64 5A6009F54AA7081402FBC57B7DEFFD600 libXcursor.so.1
INFO CODE_ID F509605AA74A140802FBC57B7DEFFD603D989A59
PUBLIC 26e0 0 XcursorCursorsCreate
PUBLIC 2720 0 XcursorCursorsDestroy
PUBLIC 2798 0 XcursorAnimateCreate
PUBLIC 27d8 0 XcursorAnimateDestroy
PUBLIC 2808 0 XcursorAnimateNext
PUBLIC 2838 0 XcursorImageLoadCursor
PUBLIC 3408 0 XcursorImagesLoadCursors
PUBLIC 34b0 0 XcursorImagesLoadCursor
PUBLIC 35c8 0 XcursorFilenameLoadCursor
PUBLIC 3630 0 XcursorFilenameLoadCursors
PUBLIC 3698 0 _XcursorCreateGlyphCursor
PUBLIC 3780 0 _XcursorCreateFontCursor
PUBLIC 39e8 0 _XcursorGetDisplayInfo
PUBLIC 3f10 0 XcursorSupportsARGB
PUBLIC 3f40 0 XcursorSupportsAnim
PUBLIC 3f70 0 XcursorSetDefaultSize
PUBLIC 3fb0 0 XcursorGetDefaultSize
PUBLIC 3fd8 0 XcursorSetTheme
PUBLIC 4050 0 XcursorGetTheme
PUBLIC 4070 0 XcursorGetThemeCore
PUBLIC 4098 0 XcursorSetThemeCore
PUBLIC 4690 0 XcursorImageCreate
PUBLIC 4700 0 XcursorImageDestroy
PUBLIC 48b8 0 XcursorImagesCreate
PUBLIC 48e8 0 XcursorImagesDestroy
PUBLIC 4950 0 XcursorImagesSetName
PUBLIC 49a0 0 XcursorCommentCreate
PUBLIC 49f0 0 XcursorCommentDestroy
PUBLIC 49f8 0 XcursorCommentsCreate
PUBLIC 4a28 0 XcursorCommentsDestroy
PUBLIC 4a80 0 XcursorXcFileLoadImage
PUBLIC 4b38 0 XcursorXcFileLoadImages
PUBLIC 4cb0 0 XcursorXcFileLoadAllImages
PUBLIC 4e38 0 XcursorXcFileLoad
PUBLIC 5130 0 XcursorXcFileSave
PUBLIC 55e8 0 XcursorFileLoadImage
PUBLIC 5668 0 XcursorFileLoadImages
PUBLIC 56e8 0 XcursorFileLoadAllImages
PUBLIC 5768 0 XcursorFileLoad
PUBLIC 57e8 0 XcursorFileSaveImages
PUBLIC 58d0 0 XcursorFileSave
PUBLIC 5970 0 XcursorFilenameLoadImage
PUBLIC 59d8 0 XcursorFilenameLoadImages
PUBLIC 5a40 0 XcursorFilenameLoadAllImages
PUBLIC 5a98 0 XcursorFilenameLoad
PUBLIC 5b10 0 XcursorFilenameSaveImages
PUBLIC 5b88 0 XcursorFilenameSave
PUBLIC 5f40 0 XcursorLibraryPath
PUBLIC 5f98 0 XcursorLibraryShape
PUBLIC 63e8 0 XcursorLibraryLoadImage
PUBLIC 6488 0 XcursorShapeLoadImage
PUBLIC 64b8 0 XcursorLibraryLoadImages
PUBLIC 6560 0 XcursorShapeLoadImages
PUBLIC 6590 0 XcursorLibraryLoadCursor
PUBLIC 6650 0 XcursorShapeLoadCursor
PUBLIC 6680 0 XcursorLibraryLoadCursors
PUBLIC 6768 0 XcursorShapeLoadCursors
PUBLIC 68d0 0 XcursorTryShapeCursor
PUBLIC 6b58 0 XcursorNoticeCreateBitmap
PUBLIC 6c90 0 XcursorImageHash
PUBLIC 6d48 0 XcursorNoticePutBitmap
PUBLIC 6f58 0 XcursorTryShapeBitmapCursor
STACK CFI INIT 2478 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24a8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 24ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24f4 x19: .cfa -16 + ^
STACK CFI 252c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2530 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2538 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25a0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d0 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2638 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26e0 40 .cfa: sp 0 + .ra: x30
STACK CFI 26e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26f4 x19: .cfa -16 + ^
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2720 78 .cfa: sp 0 + .ra: x30
STACK CFI 2728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2730 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2788 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2798 3c .cfa: sp 0 + .ra: x30
STACK CFI 279c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27a4 x19: .cfa -16 + ^
STACK CFI 27d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27d8 30 .cfa: sp 0 + .ra: x30
STACK CFI 27e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e8 x19: .cfa -16 + ^
STACK CFI 2800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2808 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2838 bd0 .cfa: sp 0 + .ra: x30
STACK CFI 283c .cfa: sp 368 +
STACK CFI 2840 .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 2848 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 286c x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2908 .cfa: sp 368 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x29: .cfa -352 + ^
STACK CFI 291c x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 29ec x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 2ac8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2b98 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2ba4 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 2e60 x27: x27 x28: x28
STACK CFI 2fb4 x25: x25 x26: x26
STACK CFI 2fb8 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2fbc x25: x25 x26: x26
STACK CFI 2fc0 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 30ac x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 31e8 x27: x27 x28: x28
STACK CFI 31ec x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 3388 x27: x27 x28: x28
STACK CFI 338c x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 33f0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33f4 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 33f8 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 3400 x25: x25 x26: x26
STACK CFI 3404 x27: x27 x28: x28
STACK CFI INIT 3408 a4 .cfa: sp 0 + .ra: x30
STACK CFI 340c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3418 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3420 x23: .cfa -16 + ^
STACK CFI 34a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 34b0 118 .cfa: sp 0 + .ra: x30
STACK CFI 34b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 34bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3518 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3534 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 35b4 x23: x23 x24: x24
STACK CFI 35b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 35c4 x23: x23 x24: x24
STACK CFI INIT 35c8 68 .cfa: sp 0 + .ra: x30
STACK CFI 35cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 361c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3630 68 .cfa: sp 0 + .ra: x30
STACK CFI 3634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 363c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3684 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3698 e8 .cfa: sp 0 + .ra: x30
STACK CFI 369c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 36a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 36b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 36c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 3780 6c .cfa: sp 0 + .ra: x30
STACK CFI 3784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 378c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 37f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37fc x19: .cfa -16 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3840 cc .cfa: sp 0 + .ra: x30
STACK CFI 3844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3854 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3910 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3914 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 391c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3924 x21: .cfa -16 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 39a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39e8 528 .cfa: sp 0 + .ra: x30
STACK CFI 39ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 39f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3a00 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3a08 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3acc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 3ad0 x25: .cfa -48 + ^
STACK CFI 3ccc x25: x25
STACK CFI 3cd0 x25: .cfa -48 + ^
STACK CFI 3e90 x25: x25
STACK CFI 3e94 x25: .cfa -48 + ^
STACK CFI 3ea0 x25: x25
STACK CFI 3ea4 x25: .cfa -48 + ^
STACK CFI 3efc x25: x25
STACK CFI 3f00 x25: .cfa -48 + ^
STACK CFI 3f0c x25: x25
STACK CFI INIT 3f10 30 .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f40 30 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f70 40 .cfa: sp 0 + .ra: x30
STACK CFI 3f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f7c x19: .cfa -16 + ^
STACK CFI 3f9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3fac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3fb0 28 .cfa: sp 0 + .ra: x30
STACK CFI 3fb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3fcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fd8 74 .cfa: sp 0 + .ra: x30
STACK CFI 3fdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fe4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4050 1c .cfa: sp 0 + .ra: x30
STACK CFI 4054 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4070 28 .cfa: sp 0 + .ra: x30
STACK CFI 4074 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4088 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 408c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4098 40 .cfa: sp 0 + .ra: x30
STACK CFI 409c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40a4 x19: .cfa -16 + ^
STACK CFI 40c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40d8 6c .cfa: sp 0 + .ra: x30
STACK CFI 40dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40e4 x19: .cfa -32 + ^
STACK CFI 4134 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4138 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4148 60 .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4160 x19: .cfa -16 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 41b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41d8 28 .cfa: sp 0 + .ra: x30
STACK CFI 41e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4200 74 .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4210 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4268 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4278 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 427c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4284 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 429c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4358 x23: .cfa -48 + ^
STACK CFI 43d4 x23: x23
STACK CFI 4400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4404 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 4414 x23: x23
STACK CFI 441c x23: .cfa -48 + ^
STACK CFI INIT 4420 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4424 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 442c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4438 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4488 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 44e8 ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 4598 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 45f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4608 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 465c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4668 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4690 70 .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 469c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4700 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4708 1ac .cfa: sp 0 + .ra: x30
STACK CFI 470c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 471c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4734 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 47ec x23: .cfa -80 + ^
STACK CFI 4874 x23: x23
STACK CFI 48a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 48a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 48a8 x23: x23
STACK CFI 48b0 x23: .cfa -80 + ^
STACK CFI INIT 48b8 30 .cfa: sp 0 + .ra: x30
STACK CFI 48c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 48f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4950 4c .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 496c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 49a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49b8 x19: .cfa -16 + ^
STACK CFI 49e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 49f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49f8 30 .cfa: sp 0 + .ra: x30
STACK CFI 4a00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a28 58 .cfa: sp 0 + .ra: x30
STACK CFI 4a30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a38 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a80 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4a84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4a8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4aac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b00 x21: x21 x22: x22
STACK CFI 4b04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b08 x21: x21 x22: x22
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4b34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 4b38 174 .cfa: sp 0 + .ra: x30
STACK CFI 4b3c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b70 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4bb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4c18 x25: x25 x26: x26
STACK CFI 4c34 x21: x21 x22: x22
STACK CFI 4c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4c5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 4c60 x21: x21 x22: x22
STACK CFI 4c68 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c78 x21: x21 x22: x22
STACK CFI 4c7c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c8c x21: x21 x22: x22
STACK CFI 4c90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c9c x21: x21 x22: x22
STACK CFI 4ca4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4ca8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 4cb0 188 .cfa: sp 0 + .ra: x30
STACK CFI 4cb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4cbc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4cc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d3c x25: .cfa -16 + ^
STACK CFI 4dc8 x19: x19 x20: x20
STACK CFI 4dcc x21: x21 x22: x22
STACK CFI 4dd0 x25: x25
STACK CFI 4ddc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4de0 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4de4 x21: x21 x22: x22
STACK CFI 4df4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4df8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4e0c x19: x19 x20: x20
STACK CFI 4e10 x21: x21 x22: x22
STACK CFI 4e18 x25: x25
STACK CFI 4e1c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4e20 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e34 x21: x21 x22: x22
STACK CFI INIT 4e38 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 4e3c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4e5c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4e64 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4e6c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4e7c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4e80 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5028 x19: x19 x20: x20
STACK CFI 502c x23: x23 x24: x24
STACK CFI 5030 x25: x25 x26: x26
STACK CFI 5034 x27: x27 x28: x28
STACK CFI 5038 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 503c x19: x19 x20: x20
STACK CFI 5040 x23: x23 x24: x24
STACK CFI 5044 x25: x25 x26: x26
STACK CFI 5048 x27: x27 x28: x28
STACK CFI 506c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5070 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 50e8 x19: x19 x20: x20
STACK CFI 50ec x23: x23 x24: x24
STACK CFI 50f0 x25: x25 x26: x26
STACK CFI 50f4 x27: x27 x28: x28
STACK CFI 50f8 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 50fc x19: x19 x20: x20
STACK CFI 5100 x27: x27 x28: x28
STACK CFI 5104 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 511c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5120 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5124 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5128 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 512c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 5130 4b4 .cfa: sp 0 + .ra: x30
STACK CFI 5134 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 513c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5144 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5154 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5198 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 519c .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 51a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5268 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 52cc x27: x27 x28: x28
STACK CFI 52ec x19: x19 x20: x20
STACK CFI 52f0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5394 x19: x19 x20: x20
STACK CFI 5398 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 53a4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 54dc x27: x27 x28: x28
STACK CFI 54ec x19: x19 x20: x20
STACK CFI 54f0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 55bc x27: x27 x28: x28
STACK CFI 55c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 55d0 x19: x19 x20: x20
STACK CFI 55d4 x27: x27 x28: x28
STACK CFI 55dc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 55e0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 55e8 7c .cfa: sp 0 + .ra: x30
STACK CFI 55ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 55f8 x19: .cfa -64 + ^
STACK CFI 5654 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5658 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5668 7c .cfa: sp 0 + .ra: x30
STACK CFI 566c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5678 x19: .cfa -64 + ^
STACK CFI 56d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 56e8 7c .cfa: sp 0 + .ra: x30
STACK CFI 56ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 56f8 x19: .cfa -64 + ^
STACK CFI 5754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5758 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5768 80 .cfa: sp 0 + .ra: x30
STACK CFI 576c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 577c x19: .cfa -64 + ^
STACK CFI 57e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 57e8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 57f4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5804 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 584c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 5854 x23: .cfa -64 + ^
STACK CFI 58a4 x23: x23
STACK CFI 58a8 x23: .cfa -64 + ^
STACK CFI 58ac x23: x23
STACK CFI 58b0 x23: .cfa -64 + ^
STACK CFI 58c4 x23: x23
STACK CFI 58c8 x23: .cfa -64 + ^
STACK CFI INIT 58d0 9c .cfa: sp 0 + .ra: x30
STACK CFI 58d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 58e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5928 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5970 68 .cfa: sp 0 + .ra: x30
STACK CFI 5974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5984 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 59c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 59d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59d8 68 .cfa: sp 0 + .ra: x30
STACK CFI 59dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5a40 58 .cfa: sp 0 + .ra: x30
STACK CFI 5a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5a98 74 .cfa: sp 0 + .ra: x30
STACK CFI 5a9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5aa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5abc x21: .cfa -16 + ^
STACK CFI 5aec x21: x21
STACK CFI 5af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5af8 x21: x21
STACK CFI 5b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5b10 74 .cfa: sp 0 + .ra: x30
STACK CFI 5b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5b88 8c .cfa: sp 0 + .ra: x30
STACK CFI 5ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5bac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5bc0 x21: .cfa -16 + ^
STACK CFI 5bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5c18 bc .cfa: sp 0 + .ra: x30
STACK CFI 5c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c38 x23: .cfa -16 + ^
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5cd8 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 5ce0 .cfa: sp 8272 +
STACK CFI 5ce4 .ra: .cfa -8264 + ^ x29: .cfa -8272 + ^
STACK CFI 5cec x19: .cfa -8256 + ^ x20: .cfa -8248 + ^
STACK CFI 5d04 x21: .cfa -8240 + ^ x22: .cfa -8232 + ^
STACK CFI 5d24 x23: .cfa -8224 + ^
STACK CFI 5e54 x23: x23
STACK CFI 5e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e84 .cfa: sp 8272 + .ra: .cfa -8264 + ^ x19: .cfa -8256 + ^ x20: .cfa -8248 + ^ x21: .cfa -8240 + ^ x22: .cfa -8232 + ^ x29: .cfa -8272 + ^
STACK CFI 5e8c x23: .cfa -8224 + ^
STACK CFI 5e94 x23: x23
STACK CFI 5e98 x23: .cfa -8224 + ^
STACK CFI INIT 5ea0 9c .cfa: sp 0 + .ra: x30
STACK CFI 5ea4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5eac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5eb4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ebc x23: .cfa -16 + ^
STACK CFI 5f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5f40 58 .cfa: sp 0 + .ra: x30
STACK CFI 5f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f4c x19: .cfa -16 + ^
STACK CFI 5f60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f98 100 .cfa: sp 0 + .ra: x30
STACK CFI 5f9c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5fa4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5fb4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5fbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5fc4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6058 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6074 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 608c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6090 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6098 350 .cfa: sp 0 + .ra: x30
STACK CFI 609c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 60ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 60b8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 60d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 60e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 60f0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 61f0 x19: x19 x20: x20
STACK CFI 61f8 x23: x23 x24: x24
STACK CFI 61fc x25: x25 x26: x26
STACK CFI 6200 x27: x27 x28: x28
STACK CFI 6204 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6208 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6328 x19: x19 x20: x20
STACK CFI 6330 x23: x23 x24: x24
STACK CFI 6334 x25: x25 x26: x26
STACK CFI 6338 x27: x27 x28: x28
STACK CFI 633c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6340 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6360 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 637c x23: x23 x24: x24
STACK CFI 6380 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6384 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 63a8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 63ac x19: x19 x20: x20
STACK CFI 63b0 x23: x23 x24: x24
STACK CFI 63c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 63c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 63e8 9c .cfa: sp 0 + .ra: x30
STACK CFI 63ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6400 x21: .cfa -16 + ^
STACK CFI 6448 x21: x21
STACK CFI 644c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6470 x21: x21
STACK CFI 6480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6488 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 64b8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 64bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64cc x21: .cfa -16 + ^
STACK CFI 6528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 652c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 655c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6560 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6590 bc .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 659c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 660c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6610 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6634 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6650 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6680 e8 .cfa: sp 0 + .ra: x30
STACK CFI 6684 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 668c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6694 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 66ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 66f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6758 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6768 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6798 58 .cfa: sp 0 + .ra: x30
STACK CFI 679c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67f0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 6800 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6808 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6810 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 68c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 68d0 284 .cfa: sp 0 + .ra: x30
STACK CFI 68d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 68e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 690c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6910 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6918 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6924 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 693c x21: x21 x22: x22
STACK CFI 6940 x23: x23 x24: x24
STACK CFI 6944 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6974 x25: .cfa -16 + ^
STACK CFI 69c8 x21: x21 x22: x22
STACK CFI 69cc x23: x23 x24: x24
STACK CFI 69d0 x25: x25
STACK CFI 69d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 69d8 x25: x25
STACK CFI 6a2c x21: x21 x22: x22
STACK CFI 6a30 x23: x23 x24: x24
STACK CFI 6a34 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 6b58 138 .cfa: sp 0 + .ra: x30
STACK CFI 6b60 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ba4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6bac x23: .cfa -16 + ^
STACK CFI 6c48 x23: x23
STACK CFI 6c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6c80 x23: x23
STACK CFI 6c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c90 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d48 210 .cfa: sp 0 + .ra: x30
STACK CFI 6d4c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 6d58 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 6d64 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 6d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6da0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x29: .cfa -240 + ^
STACK CFI 6e54 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6e64 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e70 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6f3c x23: x23 x24: x24
STACK CFI 6f40 x25: x25 x26: x26
STACK CFI 6f44 x27: x27 x28: x28
STACK CFI 6f4c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6f50 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6f54 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 6f58 130 .cfa: sp 0 + .ra: x30
STACK CFI 6f5c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6f6c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6f88 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6fb8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 6ff0 x25: .cfa -160 + ^
STACK CFI 7000 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7050 x23: x23 x24: x24
STACK CFI 7054 x25: x25
STACK CFI 7058 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI 7074 x23: x23 x24: x24
STACK CFI 7078 x25: x25
STACK CFI 7080 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7084 x25: .cfa -160 + ^
