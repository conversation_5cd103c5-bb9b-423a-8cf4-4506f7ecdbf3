MODULE Linux arm64 A4D9276B04064BC39AC8F4DA5291A0040 libqdldl.so
INFO CODE_ID 6B27D9A40604C34B9AC8F4DA5291A004
PUBLIC 548 0 _init
PUBLIC 5c0 0 call_weak_fn
PUBLIC 5d4 0 deregister_tm_clones
PUBLIC 604 0 register_tm_clones
PUBLIC 640 0 __do_global_dtors_aux
PUBLIC 690 0 frame_dummy
PUBLIC 6a0 0 QDLDL_etree
PUBLIC 7c0 0 QDLDL_factor
PUBLIC ad0 0 QDLDL_Lsolve
PUBLIC b30 0 QDLDL_Ltsolve
PUBLIC b90 0 QDLDL_solve
PUBLIC c70 0 _fini
STACK CFI INIT 5d4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 604 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 640 50 .cfa: sp 0 + .ra: x30
STACK CFI 650 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 658 x19: .cfa -16 + ^
STACK CFI 688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a0 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c0 308 .cfa: sp 0 + .ra: x30
STACK CFI 7c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 870 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 878 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 87c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 880 x27: .cfa -16 + ^
STACK CFI a58 x21: x21 x22: x22
STACK CFI a5c x23: x23 x24: x24
STACK CFI a60 x25: x25 x26: x26
STACK CFI a64 x27: x27
STACK CFI a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI aa8 x21: x21 x22: x22
STACK CFI aac x23: x23 x24: x24
STACK CFI ab0 x25: x25 x26: x26
STACK CFI ab4 x27: x27
STACK CFI ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ad0 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT b30 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT b90 e0 .cfa: sp 0 + .ra: x30
STACK CFI b94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bb8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c4c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
