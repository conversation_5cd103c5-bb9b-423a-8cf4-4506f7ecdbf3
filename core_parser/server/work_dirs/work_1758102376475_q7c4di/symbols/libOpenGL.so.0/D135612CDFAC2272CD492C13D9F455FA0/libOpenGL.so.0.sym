MODULE Linux arm64 D135612CDFAC2272CD492C13D9F455FA0 libOpenGL.so.0
INFO CODE_ID 2C6135D1ACDF7222CD492C13D9F455FAD93A6742
PUBLIC 20000 0 glAccum
PUBLIC 20080 0 glActiveShaderProgram
PUBLIC 20100 0 glActiveTexture
PUBLIC 20180 0 glAlphaFunc
PUBLIC 20200 0 glAreTexturesResident
PUBLIC 20280 0 glArrayElement
PUBLIC 20300 0 glAttachShader
PUBLIC 20380 0 glBegin
PUBLIC 20400 0 glBeginConditionalRender
PUBLIC 20480 0 glBeginQuery
PUBLIC 20500 0 glBeginQueryIndexed
PUBLIC 20580 0 glBeginTransformFeedback
PUBLIC 20600 0 glBindAttribLocation
PUBLIC 20680 0 glBindBuffer
PUBLIC 20700 0 glBindBufferBase
PUBLIC 20780 0 glBindBufferRange
PUBLIC 20800 0 glBindBuffersBase
PUBLIC 20880 0 glBindBuffersRange
PUBLIC 20900 0 glBindFragDataLocation
PUBLIC 20980 0 glBindFragDataLocationIndexed
PUBLIC 20a00 0 glBindFramebuffer
PUBLIC 20a80 0 glBindImageTexture
PUBLIC 20b00 0 glBindImageTextures
PUBLIC 20b80 0 glBindProgramPipeline
PUBLIC 20c00 0 glBindRenderbuffer
PUBLIC 20c80 0 glBindSampler
PUBLIC 20d00 0 glBindSamplers
PUBLIC 20d80 0 glBindTexture
PUBLIC 20e00 0 glBindTextureUnit
PUBLIC 20e80 0 glBindTextures
PUBLIC 20f00 0 glBindTransformFeedback
PUBLIC 20f80 0 glBindVertexArray
PUBLIC 21000 0 glBindVertexBuffer
PUBLIC 21080 0 glBindVertexBuffers
PUBLIC 21100 0 glBitmap
PUBLIC 21180 0 glBlendColor
PUBLIC 21200 0 glBlendEquation
PUBLIC 21280 0 glBlendEquationSeparate
PUBLIC 21300 0 glBlendEquationSeparatei
PUBLIC 21380 0 glBlendEquationi
PUBLIC 21400 0 glBlendFunc
PUBLIC 21480 0 glBlendFuncSeparate
PUBLIC 21500 0 glBlendFuncSeparatei
PUBLIC 21580 0 glBlendFunci
PUBLIC 21600 0 glBlitFramebuffer
PUBLIC 21680 0 glBlitNamedFramebuffer
PUBLIC 21700 0 glBufferData
PUBLIC 21780 0 glBufferStorage
PUBLIC 21800 0 glBufferSubData
PUBLIC 21880 0 glCallList
PUBLIC 21900 0 glCallLists
PUBLIC 21980 0 glCheckFramebufferStatus
PUBLIC 21a00 0 glCheckNamedFramebufferStatus
PUBLIC 21a80 0 glClampColor
PUBLIC 21b00 0 glClear
PUBLIC 21b80 0 glClearAccum
PUBLIC 21c00 0 glClearBufferData
PUBLIC 21c80 0 glClearBufferSubData
PUBLIC 21d00 0 glClearBufferfi
PUBLIC 21d80 0 glClearBufferfv
PUBLIC 21e00 0 glClearBufferiv
PUBLIC 21e80 0 glClearBufferuiv
PUBLIC 21f00 0 glClearColor
PUBLIC 21f80 0 glClearDepth
PUBLIC 22000 0 glClearDepthf
PUBLIC 22080 0 glClearIndex
PUBLIC 22100 0 glClearNamedBufferData
PUBLIC 22180 0 glClearNamedBufferSubData
PUBLIC 22200 0 glClearNamedFramebufferfi
PUBLIC 22280 0 glClearNamedFramebufferfv
PUBLIC 22300 0 glClearNamedFramebufferiv
PUBLIC 22380 0 glClearNamedFramebufferuiv
PUBLIC 22400 0 glClearStencil
PUBLIC 22480 0 glClearTexImage
PUBLIC 22500 0 glClearTexSubImage
PUBLIC 22580 0 glClientActiveTexture
PUBLIC 22600 0 glClientWaitSync
PUBLIC 22680 0 glClipControl
PUBLIC 22700 0 glClipPlane
PUBLIC 22780 0 glColor3b
PUBLIC 22800 0 glColor3bv
PUBLIC 22880 0 glColor3d
PUBLIC 22900 0 glColor3dv
PUBLIC 22980 0 glColor3f
PUBLIC 22a00 0 glColor3fv
PUBLIC 22a80 0 glColor3i
PUBLIC 22b00 0 glColor3iv
PUBLIC 22b80 0 glColor3s
PUBLIC 22c00 0 glColor3sv
PUBLIC 22c80 0 glColor3ub
PUBLIC 22d00 0 glColor3ubv
PUBLIC 22d80 0 glColor3ui
PUBLIC 22e00 0 glColor3uiv
PUBLIC 22e80 0 glColor3us
PUBLIC 22f00 0 glColor3usv
PUBLIC 22f80 0 glColor4b
PUBLIC 23000 0 glColor4bv
PUBLIC 23080 0 glColor4d
PUBLIC 23100 0 glColor4dv
PUBLIC 23180 0 glColor4f
PUBLIC 23200 0 glColor4fv
PUBLIC 23280 0 glColor4i
PUBLIC 23300 0 glColor4iv
PUBLIC 23380 0 glColor4s
PUBLIC 23400 0 glColor4sv
PUBLIC 23480 0 glColor4ub
PUBLIC 23500 0 glColor4ubv
PUBLIC 23580 0 glColor4ui
PUBLIC 23600 0 glColor4uiv
PUBLIC 23680 0 glColor4us
PUBLIC 23700 0 glColor4usv
PUBLIC 23780 0 glColorMask
PUBLIC 23800 0 glColorMaski
PUBLIC 23880 0 glColorMaterial
PUBLIC 23900 0 glColorP3ui
PUBLIC 23980 0 glColorP3uiv
PUBLIC 23a00 0 glColorP4ui
PUBLIC 23a80 0 glColorP4uiv
PUBLIC 23b00 0 glColorPointer
PUBLIC 23b80 0 glCompileShader
PUBLIC 23c00 0 glCompressedTexImage1D
PUBLIC 23c80 0 glCompressedTexImage2D
PUBLIC 23d00 0 glCompressedTexImage3D
PUBLIC 23d80 0 glCompressedTexSubImage1D
PUBLIC 23e00 0 glCompressedTexSubImage2D
PUBLIC 23e80 0 glCompressedTexSubImage3D
PUBLIC 23f00 0 glCompressedTextureSubImage1D
PUBLIC 23f80 0 glCompressedTextureSubImage2D
PUBLIC 24000 0 glCompressedTextureSubImage3D
PUBLIC 24080 0 glCopyBufferSubData
PUBLIC 24100 0 glCopyImageSubData
PUBLIC 24180 0 glCopyNamedBufferSubData
PUBLIC 24200 0 glCopyPixels
PUBLIC 24280 0 glCopyTexImage1D
PUBLIC 24300 0 glCopyTexImage2D
PUBLIC 24380 0 glCopyTexSubImage1D
PUBLIC 24400 0 glCopyTexSubImage2D
PUBLIC 24480 0 glCopyTexSubImage3D
PUBLIC 24500 0 glCopyTextureSubImage1D
PUBLIC 24580 0 glCopyTextureSubImage2D
PUBLIC 24600 0 glCopyTextureSubImage3D
PUBLIC 24680 0 glCreateBuffers
PUBLIC 24700 0 glCreateFramebuffers
PUBLIC 24780 0 glCreateProgram
PUBLIC 24800 0 glCreateProgramPipelines
PUBLIC 24880 0 glCreateQueries
PUBLIC 24900 0 glCreateRenderbuffers
PUBLIC 24980 0 glCreateSamplers
PUBLIC 24a00 0 glCreateShader
PUBLIC 24a80 0 glCreateShaderProgramv
PUBLIC 24b00 0 glCreateTextures
PUBLIC 24b80 0 glCreateTransformFeedbacks
PUBLIC 24c00 0 glCreateVertexArrays
PUBLIC 24c80 0 glCullFace
PUBLIC 24d00 0 glDebugMessageCallback
PUBLIC 24d80 0 glDebugMessageControl
PUBLIC 24e00 0 glDebugMessageInsert
PUBLIC 24e80 0 glDeleteBuffers
PUBLIC 24f00 0 glDeleteFramebuffers
PUBLIC 24f80 0 glDeleteLists
PUBLIC 25000 0 glDeleteProgram
PUBLIC 25080 0 glDeleteProgramPipelines
PUBLIC 25100 0 glDeleteQueries
PUBLIC 25180 0 glDeleteRenderbuffers
PUBLIC 25200 0 glDeleteSamplers
PUBLIC 25280 0 glDeleteShader
PUBLIC 25300 0 glDeleteSync
PUBLIC 25380 0 glDeleteTextures
PUBLIC 25400 0 glDeleteTransformFeedbacks
PUBLIC 25480 0 glDeleteVertexArrays
PUBLIC 25500 0 glDepthFunc
PUBLIC 25580 0 glDepthMask
PUBLIC 25600 0 glDepthRange
PUBLIC 25680 0 glDepthRangeArrayv
PUBLIC 25700 0 glDepthRangeIndexed
PUBLIC 25780 0 glDepthRangef
PUBLIC 25800 0 glDetachShader
PUBLIC 25880 0 glDisable
PUBLIC 25900 0 glDisableClientState
PUBLIC 25980 0 glDisableVertexArrayAttrib
PUBLIC 25a00 0 glDisableVertexAttribArray
PUBLIC 25a80 0 glDisablei
PUBLIC 25b00 0 glDispatchCompute
PUBLIC 25b80 0 glDispatchComputeIndirect
PUBLIC 25c00 0 glDrawArrays
PUBLIC 25c80 0 glDrawArraysIndirect
PUBLIC 25d00 0 glDrawArraysInstanced
PUBLIC 25d80 0 glDrawArraysInstancedBaseInstance
PUBLIC 25e00 0 glDrawBuffer
PUBLIC 25e80 0 glDrawBuffers
PUBLIC 25f00 0 glDrawElements
PUBLIC 25f80 0 glDrawElementsBaseVertex
PUBLIC 26000 0 glDrawElementsIndirect
PUBLIC 26080 0 glDrawElementsInstanced
PUBLIC 26100 0 glDrawElementsInstancedBaseInstance
PUBLIC 26180 0 glDrawElementsInstancedBaseVertex
PUBLIC 26200 0 glDrawElementsInstancedBaseVertexBaseInstance
PUBLIC 26280 0 glDrawPixels
PUBLIC 26300 0 glDrawRangeElements
PUBLIC 26380 0 glDrawRangeElementsBaseVertex
PUBLIC 26400 0 glDrawTransformFeedback
PUBLIC 26480 0 glDrawTransformFeedbackInstanced
PUBLIC 26500 0 glDrawTransformFeedbackStream
PUBLIC 26580 0 glDrawTransformFeedbackStreamInstanced
PUBLIC 26600 0 glEdgeFlag
PUBLIC 26680 0 glEdgeFlagPointer
PUBLIC 26700 0 glEdgeFlagv
PUBLIC 26780 0 glEnable
PUBLIC 26800 0 glEnableClientState
PUBLIC 26880 0 glEnableVertexArrayAttrib
PUBLIC 26900 0 glEnableVertexAttribArray
PUBLIC 26980 0 glEnablei
PUBLIC 26a00 0 glEnd
PUBLIC 26a80 0 glEndConditionalRender
PUBLIC 26b00 0 glEndList
PUBLIC 26b80 0 glEndQuery
PUBLIC 26c00 0 glEndQueryIndexed
PUBLIC 26c80 0 glEndTransformFeedback
PUBLIC 26d00 0 glEvalCoord1d
PUBLIC 26d80 0 glEvalCoord1dv
PUBLIC 26e00 0 glEvalCoord1f
PUBLIC 26e80 0 glEvalCoord1fv
PUBLIC 26f00 0 glEvalCoord2d
PUBLIC 26f80 0 glEvalCoord2dv
PUBLIC 27000 0 glEvalCoord2f
PUBLIC 27080 0 glEvalCoord2fv
PUBLIC 27100 0 glEvalMesh1
PUBLIC 27180 0 glEvalMesh2
PUBLIC 27200 0 glEvalPoint1
PUBLIC 27280 0 glEvalPoint2
PUBLIC 27300 0 glFeedbackBuffer
PUBLIC 27380 0 glFenceSync
PUBLIC 27400 0 glFinish
PUBLIC 27480 0 glFlush
PUBLIC 27500 0 glFlushMappedBufferRange
PUBLIC 27580 0 glFlushMappedNamedBufferRange
PUBLIC 27600 0 glFogCoordPointer
PUBLIC 27680 0 glFogCoordd
PUBLIC 27700 0 glFogCoorddv
PUBLIC 27780 0 glFogCoordf
PUBLIC 27800 0 glFogCoordfv
PUBLIC 27880 0 glFogf
PUBLIC 27900 0 glFogfv
PUBLIC 27980 0 glFogi
PUBLIC 27a00 0 glFogiv
PUBLIC 27a80 0 glFramebufferParameteri
PUBLIC 27b00 0 glFramebufferRenderbuffer
PUBLIC 27b80 0 glFramebufferTexture
PUBLIC 27c00 0 glFramebufferTexture1D
PUBLIC 27c80 0 glFramebufferTexture2D
PUBLIC 27d00 0 glFramebufferTexture3D
PUBLIC 27d80 0 glFramebufferTextureLayer
PUBLIC 27e00 0 glFrontFace
PUBLIC 27e80 0 glFrustum
PUBLIC 27f00 0 glGenBuffers
PUBLIC 27f80 0 glGenFramebuffers
PUBLIC 28000 0 glGenLists
PUBLIC 28080 0 glGenProgramPipelines
PUBLIC 28100 0 glGenQueries
PUBLIC 28180 0 glGenRenderbuffers
PUBLIC 28200 0 glGenSamplers
PUBLIC 28280 0 glGenTextures
PUBLIC 28300 0 glGenTransformFeedbacks
PUBLIC 28380 0 glGenVertexArrays
PUBLIC 28400 0 glGenerateMipmap
PUBLIC 28480 0 glGenerateTextureMipmap
PUBLIC 28500 0 glGetActiveAtomicCounterBufferiv
PUBLIC 28580 0 glGetActiveAttrib
PUBLIC 28600 0 glGetActiveSubroutineName
PUBLIC 28680 0 glGetActiveSubroutineUniformName
PUBLIC 28700 0 glGetActiveSubroutineUniformiv
PUBLIC 28780 0 glGetActiveUniform
PUBLIC 28800 0 glGetActiveUniformBlockName
PUBLIC 28880 0 glGetActiveUniformBlockiv
PUBLIC 28900 0 glGetActiveUniformName
PUBLIC 28980 0 glGetActiveUniformsiv
PUBLIC 28a00 0 glGetAttachedShaders
PUBLIC 28a80 0 glGetAttribLocation
PUBLIC 28b00 0 glGetBooleani_v
PUBLIC 28b80 0 glGetBooleanv
PUBLIC 28c00 0 glGetBufferParameteri64v
PUBLIC 28c80 0 glGetBufferParameteriv
PUBLIC 28d00 0 glGetBufferPointerv
PUBLIC 28d80 0 glGetBufferSubData
PUBLIC 28e00 0 glGetClipPlane
PUBLIC 28e80 0 glGetCompressedTexImage
PUBLIC 28f00 0 glGetCompressedTextureImage
PUBLIC 28f80 0 glGetCompressedTextureSubImage
PUBLIC 29000 0 glGetDebugMessageLog
PUBLIC 29080 0 glGetDoublei_v
PUBLIC 29100 0 glGetDoublev
PUBLIC 29180 0 glGetError
PUBLIC 29200 0 glGetFloati_v
PUBLIC 29280 0 glGetFloatv
PUBLIC 29300 0 glGetFragDataIndex
PUBLIC 29380 0 glGetFragDataLocation
PUBLIC 29400 0 glGetFramebufferAttachmentParameteriv
PUBLIC 29480 0 glGetFramebufferParameteriv
PUBLIC 29500 0 glGetGraphicsResetStatus
PUBLIC 29580 0 glGetInteger64i_v
PUBLIC 29600 0 glGetInteger64v
PUBLIC 29680 0 glGetIntegeri_v
PUBLIC 29700 0 glGetIntegerv
PUBLIC 29780 0 glGetInternalformati64v
PUBLIC 29800 0 glGetInternalformativ
PUBLIC 29880 0 glGetLightfv
PUBLIC 29900 0 glGetLightiv
PUBLIC 29980 0 glGetMapdv
PUBLIC 29a00 0 glGetMapfv
PUBLIC 29a80 0 glGetMapiv
PUBLIC 29b00 0 glGetMaterialfv
PUBLIC 29b80 0 glGetMaterialiv
PUBLIC 29c00 0 glGetMultisamplefv
PUBLIC 29c80 0 glGetNamedBufferParameteri64v
PUBLIC 29d00 0 glGetNamedBufferParameteriv
PUBLIC 29d80 0 glGetNamedBufferPointerv
PUBLIC 29e00 0 glGetNamedBufferSubData
PUBLIC 29e80 0 glGetNamedFramebufferAttachmentParameteriv
PUBLIC 29f00 0 glGetNamedFramebufferParameteriv
PUBLIC 29f80 0 glGetNamedRenderbufferParameteriv
PUBLIC 2a000 0 glGetObjectLabel
PUBLIC 2a080 0 glGetObjectPtrLabel
PUBLIC 2a100 0 glGetPixelMapfv
PUBLIC 2a180 0 glGetPixelMapuiv
PUBLIC 2a200 0 glGetPixelMapusv
PUBLIC 2a280 0 glGetPointerv
PUBLIC 2a300 0 glGetPolygonStipple
PUBLIC 2a380 0 glGetProgramBinary
PUBLIC 2a400 0 glGetProgramInfoLog
PUBLIC 2a480 0 glGetProgramInterfaceiv
PUBLIC 2a500 0 glGetProgramPipelineInfoLog
PUBLIC 2a580 0 glGetProgramPipelineiv
PUBLIC 2a600 0 glGetProgramResourceIndex
PUBLIC 2a680 0 glGetProgramResourceLocation
PUBLIC 2a700 0 glGetProgramResourceLocationIndex
PUBLIC 2a780 0 glGetProgramResourceName
PUBLIC 2a800 0 glGetProgramResourceiv
PUBLIC 2a880 0 glGetProgramStageiv
PUBLIC 2a900 0 glGetProgramiv
PUBLIC 2a980 0 glGetQueryBufferObjecti64v
PUBLIC 2aa00 0 glGetQueryBufferObjectiv
PUBLIC 2aa80 0 glGetQueryBufferObjectui64v
PUBLIC 2ab00 0 glGetQueryBufferObjectuiv
PUBLIC 2ab80 0 glGetQueryIndexediv
PUBLIC 2ac00 0 glGetQueryObjecti64v
PUBLIC 2ac80 0 glGetQueryObjectiv
PUBLIC 2ad00 0 glGetQueryObjectui64v
PUBLIC 2ad80 0 glGetQueryObjectuiv
PUBLIC 2ae00 0 glGetQueryiv
PUBLIC 2ae80 0 glGetRenderbufferParameteriv
PUBLIC 2af00 0 glGetSamplerParameterIiv
PUBLIC 2af80 0 glGetSamplerParameterIuiv
PUBLIC 2b000 0 glGetSamplerParameterfv
PUBLIC 2b080 0 glGetSamplerParameteriv
PUBLIC 2b100 0 glGetShaderInfoLog
PUBLIC 2b180 0 glGetShaderPrecisionFormat
PUBLIC 2b200 0 glGetShaderSource
PUBLIC 2b280 0 glGetShaderiv
PUBLIC 2b300 0 glGetString
PUBLIC 2b380 0 glGetStringi
PUBLIC 2b400 0 glGetSubroutineIndex
PUBLIC 2b480 0 glGetSubroutineUniformLocation
PUBLIC 2b500 0 glGetSynciv
PUBLIC 2b580 0 glGetTexEnvfv
PUBLIC 2b600 0 glGetTexEnviv
PUBLIC 2b680 0 glGetTexGendv
PUBLIC 2b700 0 glGetTexGenfv
PUBLIC 2b780 0 glGetTexGeniv
PUBLIC 2b800 0 glGetTexImage
PUBLIC 2b880 0 glGetTexLevelParameterfv
PUBLIC 2b900 0 glGetTexLevelParameteriv
PUBLIC 2b980 0 glGetTexParameterIiv
PUBLIC 2ba00 0 glGetTexParameterIuiv
PUBLIC 2ba80 0 glGetTexParameterfv
PUBLIC 2bb00 0 glGetTexParameteriv
PUBLIC 2bb80 0 glGetTextureImage
PUBLIC 2bc00 0 glGetTextureLevelParameterfv
PUBLIC 2bc80 0 glGetTextureLevelParameteriv
PUBLIC 2bd00 0 glGetTextureParameterIiv
PUBLIC 2bd80 0 glGetTextureParameterIuiv
PUBLIC 2be00 0 glGetTextureParameterfv
PUBLIC 2be80 0 glGetTextureParameteriv
PUBLIC 2bf00 0 glGetTextureSubImage
PUBLIC 2bf80 0 glGetTransformFeedbackVarying
PUBLIC 2c000 0 glGetTransformFeedbacki64_v
PUBLIC 2c080 0 glGetTransformFeedbacki_v
PUBLIC 2c100 0 glGetTransformFeedbackiv
PUBLIC 2c180 0 glGetUniformBlockIndex
PUBLIC 2c200 0 glGetUniformIndices
PUBLIC 2c280 0 glGetUniformLocation
PUBLIC 2c300 0 glGetUniformSubroutineuiv
PUBLIC 2c380 0 glGetUniformdv
PUBLIC 2c400 0 glGetUniformfv
PUBLIC 2c480 0 glGetUniformiv
PUBLIC 2c500 0 glGetUniformuiv
PUBLIC 2c580 0 glGetVertexArrayIndexed64iv
PUBLIC 2c600 0 glGetVertexArrayIndexediv
PUBLIC 2c680 0 glGetVertexArrayiv
PUBLIC 2c700 0 glGetVertexAttribIiv
PUBLIC 2c780 0 glGetVertexAttribIuiv
PUBLIC 2c800 0 glGetVertexAttribLdv
PUBLIC 2c880 0 glGetVertexAttribPointerv
PUBLIC 2c900 0 glGetVertexAttribdv
PUBLIC 2c980 0 glGetVertexAttribfv
PUBLIC 2ca00 0 glGetVertexAttribiv
PUBLIC 2ca80 0 glGetnColorTable
PUBLIC 2cb00 0 glGetnCompressedTexImage
PUBLIC 2cb80 0 glGetnConvolutionFilter
PUBLIC 2cc00 0 glGetnHistogram
PUBLIC 2cc80 0 glGetnMapdv
PUBLIC 2cd00 0 glGetnMapfv
PUBLIC 2cd80 0 glGetnMapiv
PUBLIC 2ce00 0 glGetnMinmax
PUBLIC 2ce80 0 glGetnPixelMapfv
PUBLIC 2cf00 0 glGetnPixelMapuiv
PUBLIC 2cf80 0 glGetnPixelMapusv
PUBLIC 2d000 0 glGetnPolygonStipple
PUBLIC 2d080 0 glGetnSeparableFilter
PUBLIC 2d100 0 glGetnTexImage
PUBLIC 2d180 0 glGetnUniformdv
PUBLIC 2d200 0 glGetnUniformfv
PUBLIC 2d280 0 glGetnUniformiv
PUBLIC 2d300 0 glGetnUniformuiv
PUBLIC 2d380 0 glHint
PUBLIC 2d400 0 glIndexMask
PUBLIC 2d480 0 glIndexPointer
PUBLIC 2d500 0 glIndexd
PUBLIC 2d580 0 glIndexdv
PUBLIC 2d600 0 glIndexf
PUBLIC 2d680 0 glIndexfv
PUBLIC 2d700 0 glIndexi
PUBLIC 2d780 0 glIndexiv
PUBLIC 2d800 0 glIndexs
PUBLIC 2d880 0 glIndexsv
PUBLIC 2d900 0 glIndexub
PUBLIC 2d980 0 glIndexubv
PUBLIC 2da00 0 glInitNames
PUBLIC 2da80 0 glInterleavedArrays
PUBLIC 2db00 0 glInvalidateBufferData
PUBLIC 2db80 0 glInvalidateBufferSubData
PUBLIC 2dc00 0 glInvalidateFramebuffer
PUBLIC 2dc80 0 glInvalidateNamedFramebufferData
PUBLIC 2dd00 0 glInvalidateNamedFramebufferSubData
PUBLIC 2dd80 0 glInvalidateSubFramebuffer
PUBLIC 2de00 0 glInvalidateTexImage
PUBLIC 2de80 0 glInvalidateTexSubImage
PUBLIC 2df00 0 glIsBuffer
PUBLIC 2df80 0 glIsEnabled
PUBLIC 2e000 0 glIsEnabledi
PUBLIC 2e080 0 glIsFramebuffer
PUBLIC 2e100 0 glIsList
PUBLIC 2e180 0 glIsProgram
PUBLIC 2e200 0 glIsProgramPipeline
PUBLIC 2e280 0 glIsQuery
PUBLIC 2e300 0 glIsRenderbuffer
PUBLIC 2e380 0 glIsSampler
PUBLIC 2e400 0 glIsShader
PUBLIC 2e480 0 glIsSync
PUBLIC 2e500 0 glIsTexture
PUBLIC 2e580 0 glIsTransformFeedback
PUBLIC 2e600 0 glIsVertexArray
PUBLIC 2e680 0 glLightModelf
PUBLIC 2e700 0 glLightModelfv
PUBLIC 2e780 0 glLightModeli
PUBLIC 2e800 0 glLightModeliv
PUBLIC 2e880 0 glLightf
PUBLIC 2e900 0 glLightfv
PUBLIC 2e980 0 glLighti
PUBLIC 2ea00 0 glLightiv
PUBLIC 2ea80 0 glLineStipple
PUBLIC 2eb00 0 glLineWidth
PUBLIC 2eb80 0 glLinkProgram
PUBLIC 2ec00 0 glListBase
PUBLIC 2ec80 0 glLoadIdentity
PUBLIC 2ed00 0 glLoadMatrixd
PUBLIC 2ed80 0 glLoadMatrixf
PUBLIC 2ee00 0 glLoadName
PUBLIC 2ee80 0 glLoadTransposeMatrixd
PUBLIC 2ef00 0 glLoadTransposeMatrixf
PUBLIC 2ef80 0 glLogicOp
PUBLIC 2f000 0 glMap1d
PUBLIC 2f080 0 glMap1f
PUBLIC 2f100 0 glMap2d
PUBLIC 2f180 0 glMap2f
PUBLIC 2f200 0 glMapBuffer
PUBLIC 2f280 0 glMapBufferRange
PUBLIC 2f300 0 glMapGrid1d
PUBLIC 2f380 0 glMapGrid1f
PUBLIC 2f400 0 glMapGrid2d
PUBLIC 2f480 0 glMapGrid2f
PUBLIC 2f500 0 glMapNamedBuffer
PUBLIC 2f580 0 glMapNamedBufferRange
PUBLIC 2f600 0 glMaterialf
PUBLIC 2f680 0 glMaterialfv
PUBLIC 2f700 0 glMateriali
PUBLIC 2f780 0 glMaterialiv
PUBLIC 2f800 0 glMatrixMode
PUBLIC 2f880 0 glMemoryBarrier
PUBLIC 2f900 0 glMemoryBarrierByRegion
PUBLIC 2f980 0 glMinSampleShading
PUBLIC 2fa00 0 glMultMatrixd
PUBLIC 2fa80 0 glMultMatrixf
PUBLIC 2fb00 0 glMultTransposeMatrixd
PUBLIC 2fb80 0 glMultTransposeMatrixf
PUBLIC 2fc00 0 glMultiDrawArrays
PUBLIC 2fc80 0 glMultiDrawArraysIndirect
PUBLIC 2fd00 0 glMultiDrawElements
PUBLIC 2fd80 0 glMultiDrawElementsBaseVertex
PUBLIC 2fe00 0 glMultiDrawElementsIndirect
PUBLIC 2fe80 0 glMultiTexCoord1d
PUBLIC 2ff00 0 glMultiTexCoord1dv
PUBLIC 2ff80 0 glMultiTexCoord1f
PUBLIC 30000 0 glMultiTexCoord1fv
PUBLIC 30080 0 glMultiTexCoord1i
PUBLIC 30100 0 glMultiTexCoord1iv
PUBLIC 30180 0 glMultiTexCoord1s
PUBLIC 30200 0 glMultiTexCoord1sv
PUBLIC 30280 0 glMultiTexCoord2d
PUBLIC 30300 0 glMultiTexCoord2dv
PUBLIC 30380 0 glMultiTexCoord2f
PUBLIC 30400 0 glMultiTexCoord2fv
PUBLIC 30480 0 glMultiTexCoord2i
PUBLIC 30500 0 glMultiTexCoord2iv
PUBLIC 30580 0 glMultiTexCoord2s
PUBLIC 30600 0 glMultiTexCoord2sv
PUBLIC 30680 0 glMultiTexCoord3d
PUBLIC 30700 0 glMultiTexCoord3dv
PUBLIC 30780 0 glMultiTexCoord3f
PUBLIC 30800 0 glMultiTexCoord3fv
PUBLIC 30880 0 glMultiTexCoord3i
PUBLIC 30900 0 glMultiTexCoord3iv
PUBLIC 30980 0 glMultiTexCoord3s
PUBLIC 30a00 0 glMultiTexCoord3sv
PUBLIC 30a80 0 glMultiTexCoord4d
PUBLIC 30b00 0 glMultiTexCoord4dv
PUBLIC 30b80 0 glMultiTexCoord4f
PUBLIC 30c00 0 glMultiTexCoord4fv
PUBLIC 30c80 0 glMultiTexCoord4i
PUBLIC 30d00 0 glMultiTexCoord4iv
PUBLIC 30d80 0 glMultiTexCoord4s
PUBLIC 30e00 0 glMultiTexCoord4sv
PUBLIC 30e80 0 glMultiTexCoordP1ui
PUBLIC 30f00 0 glMultiTexCoordP1uiv
PUBLIC 30f80 0 glMultiTexCoordP2ui
PUBLIC 31000 0 glMultiTexCoordP2uiv
PUBLIC 31080 0 glMultiTexCoordP3ui
PUBLIC 31100 0 glMultiTexCoordP3uiv
PUBLIC 31180 0 glMultiTexCoordP4ui
PUBLIC 31200 0 glMultiTexCoordP4uiv
PUBLIC 31280 0 glNamedBufferData
PUBLIC 31300 0 glNamedBufferStorage
PUBLIC 31380 0 glNamedBufferSubData
PUBLIC 31400 0 glNamedFramebufferDrawBuffer
PUBLIC 31480 0 glNamedFramebufferDrawBuffers
PUBLIC 31500 0 glNamedFramebufferParameteri
PUBLIC 31580 0 glNamedFramebufferReadBuffer
PUBLIC 31600 0 glNamedFramebufferRenderbuffer
PUBLIC 31680 0 glNamedFramebufferTexture
PUBLIC 31700 0 glNamedFramebufferTextureLayer
PUBLIC 31780 0 glNamedRenderbufferStorage
PUBLIC 31800 0 glNamedRenderbufferStorageMultisample
PUBLIC 31880 0 glNewList
PUBLIC 31900 0 glNormal3b
PUBLIC 31980 0 glNormal3bv
PUBLIC 31a00 0 glNormal3d
PUBLIC 31a80 0 glNormal3dv
PUBLIC 31b00 0 glNormal3f
PUBLIC 31b80 0 glNormal3fv
PUBLIC 31c00 0 glNormal3i
PUBLIC 31c80 0 glNormal3iv
PUBLIC 31d00 0 glNormal3s
PUBLIC 31d80 0 glNormal3sv
PUBLIC 31e00 0 glNormalP3ui
PUBLIC 31e80 0 glNormalP3uiv
PUBLIC 31f00 0 glNormalPointer
PUBLIC 31f80 0 glObjectLabel
PUBLIC 32000 0 glObjectPtrLabel
PUBLIC 32080 0 glOrtho
PUBLIC 32100 0 glPassThrough
PUBLIC 32180 0 glPatchParameterfv
PUBLIC 32200 0 glPatchParameteri
PUBLIC 32280 0 glPauseTransformFeedback
PUBLIC 32300 0 glPixelMapfv
PUBLIC 32380 0 glPixelMapuiv
PUBLIC 32400 0 glPixelMapusv
PUBLIC 32480 0 glPixelStoref
PUBLIC 32500 0 glPixelStorei
PUBLIC 32580 0 glPixelTransferf
PUBLIC 32600 0 glPixelTransferi
PUBLIC 32680 0 glPixelZoom
PUBLIC 32700 0 glPointParameterf
PUBLIC 32780 0 glPointParameterfv
PUBLIC 32800 0 glPointParameteri
PUBLIC 32880 0 glPointParameteriv
PUBLIC 32900 0 glPointSize
PUBLIC 32980 0 glPolygonMode
PUBLIC 32a00 0 glPolygonOffset
PUBLIC 32a80 0 glPolygonStipple
PUBLIC 32b00 0 glPopAttrib
PUBLIC 32b80 0 glPopClientAttrib
PUBLIC 32c00 0 glPopDebugGroup
PUBLIC 32c80 0 glPopMatrix
PUBLIC 32d00 0 glPopName
PUBLIC 32d80 0 glPrimitiveRestartIndex
PUBLIC 32e00 0 glPrioritizeTextures
PUBLIC 32e80 0 glProgramBinary
PUBLIC 32f00 0 glProgramParameteri
PUBLIC 32f80 0 glProgramUniform1d
PUBLIC 33000 0 glProgramUniform1dv
PUBLIC 33080 0 glProgramUniform1f
PUBLIC 33100 0 glProgramUniform1fv
PUBLIC 33180 0 glProgramUniform1i
PUBLIC 33200 0 glProgramUniform1iv
PUBLIC 33280 0 glProgramUniform1ui
PUBLIC 33300 0 glProgramUniform1uiv
PUBLIC 33380 0 glProgramUniform2d
PUBLIC 33400 0 glProgramUniform2dv
PUBLIC 33480 0 glProgramUniform2f
PUBLIC 33500 0 glProgramUniform2fv
PUBLIC 33580 0 glProgramUniform2i
PUBLIC 33600 0 glProgramUniform2iv
PUBLIC 33680 0 glProgramUniform2ui
PUBLIC 33700 0 glProgramUniform2uiv
PUBLIC 33780 0 glProgramUniform3d
PUBLIC 33800 0 glProgramUniform3dv
PUBLIC 33880 0 glProgramUniform3f
PUBLIC 33900 0 glProgramUniform3fv
PUBLIC 33980 0 glProgramUniform3i
PUBLIC 33a00 0 glProgramUniform3iv
PUBLIC 33a80 0 glProgramUniform3ui
PUBLIC 33b00 0 glProgramUniform3uiv
PUBLIC 33b80 0 glProgramUniform4d
PUBLIC 33c00 0 glProgramUniform4dv
PUBLIC 33c80 0 glProgramUniform4f
PUBLIC 33d00 0 glProgramUniform4fv
PUBLIC 33d80 0 glProgramUniform4i
PUBLIC 33e00 0 glProgramUniform4iv
PUBLIC 33e80 0 glProgramUniform4ui
PUBLIC 33f00 0 glProgramUniform4uiv
PUBLIC 33f80 0 glProgramUniformMatrix2dv
PUBLIC 34000 0 glProgramUniformMatrix2fv
PUBLIC 34080 0 glProgramUniformMatrix2x3dv
PUBLIC 34100 0 glProgramUniformMatrix2x3fv
PUBLIC 34180 0 glProgramUniformMatrix2x4dv
PUBLIC 34200 0 glProgramUniformMatrix2x4fv
PUBLIC 34280 0 glProgramUniformMatrix3dv
PUBLIC 34300 0 glProgramUniformMatrix3fv
PUBLIC 34380 0 glProgramUniformMatrix3x2dv
PUBLIC 34400 0 glProgramUniformMatrix3x2fv
PUBLIC 34480 0 glProgramUniformMatrix3x4dv
PUBLIC 34500 0 glProgramUniformMatrix3x4fv
PUBLIC 34580 0 glProgramUniformMatrix4dv
PUBLIC 34600 0 glProgramUniformMatrix4fv
PUBLIC 34680 0 glProgramUniformMatrix4x2dv
PUBLIC 34700 0 glProgramUniformMatrix4x2fv
PUBLIC 34780 0 glProgramUniformMatrix4x3dv
PUBLIC 34800 0 glProgramUniformMatrix4x3fv
PUBLIC 34880 0 glProvokingVertex
PUBLIC 34900 0 glPushAttrib
PUBLIC 34980 0 glPushClientAttrib
PUBLIC 34a00 0 glPushDebugGroup
PUBLIC 34a80 0 glPushMatrix
PUBLIC 34b00 0 glPushName
PUBLIC 34b80 0 glQueryCounter
PUBLIC 34c00 0 glRasterPos2d
PUBLIC 34c80 0 glRasterPos2dv
PUBLIC 34d00 0 glRasterPos2f
PUBLIC 34d80 0 glRasterPos2fv
PUBLIC 34e00 0 glRasterPos2i
PUBLIC 34e80 0 glRasterPos2iv
PUBLIC 34f00 0 glRasterPos2s
PUBLIC 34f80 0 glRasterPos2sv
PUBLIC 35000 0 glRasterPos3d
PUBLIC 35080 0 glRasterPos3dv
PUBLIC 35100 0 glRasterPos3f
PUBLIC 35180 0 glRasterPos3fv
PUBLIC 35200 0 glRasterPos3i
PUBLIC 35280 0 glRasterPos3iv
PUBLIC 35300 0 glRasterPos3s
PUBLIC 35380 0 glRasterPos3sv
PUBLIC 35400 0 glRasterPos4d
PUBLIC 35480 0 glRasterPos4dv
PUBLIC 35500 0 glRasterPos4f
PUBLIC 35580 0 glRasterPos4fv
PUBLIC 35600 0 glRasterPos4i
PUBLIC 35680 0 glRasterPos4iv
PUBLIC 35700 0 glRasterPos4s
PUBLIC 35780 0 glRasterPos4sv
PUBLIC 35800 0 glReadBuffer
PUBLIC 35880 0 glReadPixels
PUBLIC 35900 0 glReadnPixels
PUBLIC 35980 0 glRectd
PUBLIC 35a00 0 glRectdv
PUBLIC 35a80 0 glRectf
PUBLIC 35b00 0 glRectfv
PUBLIC 35b80 0 glRecti
PUBLIC 35c00 0 glRectiv
PUBLIC 35c80 0 glRects
PUBLIC 35d00 0 glRectsv
PUBLIC 35d80 0 glReleaseShaderCompiler
PUBLIC 35e00 0 glRenderMode
PUBLIC 35e80 0 glRenderbufferStorage
PUBLIC 35f00 0 glRenderbufferStorageMultisample
PUBLIC 35f80 0 glResumeTransformFeedback
PUBLIC 36000 0 glRotated
PUBLIC 36080 0 glRotatef
PUBLIC 36100 0 glSampleCoverage
PUBLIC 36180 0 glSampleMaski
PUBLIC 36200 0 glSamplerParameterIiv
PUBLIC 36280 0 glSamplerParameterIuiv
PUBLIC 36300 0 glSamplerParameterf
PUBLIC 36380 0 glSamplerParameterfv
PUBLIC 36400 0 glSamplerParameteri
PUBLIC 36480 0 glSamplerParameteriv
PUBLIC 36500 0 glScaled
PUBLIC 36580 0 glScalef
PUBLIC 36600 0 glScissor
PUBLIC 36680 0 glScissorArrayv
PUBLIC 36700 0 glScissorIndexed
PUBLIC 36780 0 glScissorIndexedv
PUBLIC 36800 0 glSecondaryColor3b
PUBLIC 36880 0 glSecondaryColor3bv
PUBLIC 36900 0 glSecondaryColor3d
PUBLIC 36980 0 glSecondaryColor3dv
PUBLIC 36a00 0 glSecondaryColor3f
PUBLIC 36a80 0 glSecondaryColor3fv
PUBLIC 36b00 0 glSecondaryColor3i
PUBLIC 36b80 0 glSecondaryColor3iv
PUBLIC 36c00 0 glSecondaryColor3s
PUBLIC 36c80 0 glSecondaryColor3sv
PUBLIC 36d00 0 glSecondaryColor3ub
PUBLIC 36d80 0 glSecondaryColor3ubv
PUBLIC 36e00 0 glSecondaryColor3ui
PUBLIC 36e80 0 glSecondaryColor3uiv
PUBLIC 36f00 0 glSecondaryColor3us
PUBLIC 36f80 0 glSecondaryColor3usv
PUBLIC 37000 0 glSecondaryColorP3ui
PUBLIC 37080 0 glSecondaryColorP3uiv
PUBLIC 37100 0 glSecondaryColorPointer
PUBLIC 37180 0 glSelectBuffer
PUBLIC 37200 0 glShadeModel
PUBLIC 37280 0 glShaderBinary
PUBLIC 37300 0 glShaderSource
PUBLIC 37380 0 glShaderStorageBlockBinding
PUBLIC 37400 0 glStencilFunc
PUBLIC 37480 0 glStencilFuncSeparate
PUBLIC 37500 0 glStencilMask
PUBLIC 37580 0 glStencilMaskSeparate
PUBLIC 37600 0 glStencilOp
PUBLIC 37680 0 glStencilOpSeparate
PUBLIC 37700 0 glTexBuffer
PUBLIC 37780 0 glTexBufferRange
PUBLIC 37800 0 glTexCoord1d
PUBLIC 37880 0 glTexCoord1dv
PUBLIC 37900 0 glTexCoord1f
PUBLIC 37980 0 glTexCoord1fv
PUBLIC 37a00 0 glTexCoord1i
PUBLIC 37a80 0 glTexCoord1iv
PUBLIC 37b00 0 glTexCoord1s
PUBLIC 37b80 0 glTexCoord1sv
PUBLIC 37c00 0 glTexCoord2d
PUBLIC 37c80 0 glTexCoord2dv
PUBLIC 37d00 0 glTexCoord2f
PUBLIC 37d80 0 glTexCoord2fv
PUBLIC 37e00 0 glTexCoord2i
PUBLIC 37e80 0 glTexCoord2iv
PUBLIC 37f00 0 glTexCoord2s
PUBLIC 37f80 0 glTexCoord2sv
PUBLIC 38000 0 glTexCoord3d
PUBLIC 38080 0 glTexCoord3dv
PUBLIC 38100 0 glTexCoord3f
PUBLIC 38180 0 glTexCoord3fv
PUBLIC 38200 0 glTexCoord3i
PUBLIC 38280 0 glTexCoord3iv
PUBLIC 38300 0 glTexCoord3s
PUBLIC 38380 0 glTexCoord3sv
PUBLIC 38400 0 glTexCoord4d
PUBLIC 38480 0 glTexCoord4dv
PUBLIC 38500 0 glTexCoord4f
PUBLIC 38580 0 glTexCoord4fv
PUBLIC 38600 0 glTexCoord4i
PUBLIC 38680 0 glTexCoord4iv
PUBLIC 38700 0 glTexCoord4s
PUBLIC 38780 0 glTexCoord4sv
PUBLIC 38800 0 glTexCoordP1ui
PUBLIC 38880 0 glTexCoordP1uiv
PUBLIC 38900 0 glTexCoordP2ui
PUBLIC 38980 0 glTexCoordP2uiv
PUBLIC 38a00 0 glTexCoordP3ui
PUBLIC 38a80 0 glTexCoordP3uiv
PUBLIC 38b00 0 glTexCoordP4ui
PUBLIC 38b80 0 glTexCoordP4uiv
PUBLIC 38c00 0 glTexCoordPointer
PUBLIC 38c80 0 glTexEnvf
PUBLIC 38d00 0 glTexEnvfv
PUBLIC 38d80 0 glTexEnvi
PUBLIC 38e00 0 glTexEnviv
PUBLIC 38e80 0 glTexGend
PUBLIC 38f00 0 glTexGendv
PUBLIC 38f80 0 glTexGenf
PUBLIC 39000 0 glTexGenfv
PUBLIC 39080 0 glTexGeni
PUBLIC 39100 0 glTexGeniv
PUBLIC 39180 0 glTexImage1D
PUBLIC 39200 0 glTexImage2D
PUBLIC 39280 0 glTexImage2DMultisample
PUBLIC 39300 0 glTexImage3D
PUBLIC 39380 0 glTexImage3DMultisample
PUBLIC 39400 0 glTexParameterIiv
PUBLIC 39480 0 glTexParameterIuiv
PUBLIC 39500 0 glTexParameterf
PUBLIC 39580 0 glTexParameterfv
PUBLIC 39600 0 glTexParameteri
PUBLIC 39680 0 glTexParameteriv
PUBLIC 39700 0 glTexStorage1D
PUBLIC 39780 0 glTexStorage2D
PUBLIC 39800 0 glTexStorage2DMultisample
PUBLIC 39880 0 glTexStorage3D
PUBLIC 39900 0 glTexStorage3DMultisample
PUBLIC 39980 0 glTexSubImage1D
PUBLIC 39a00 0 glTexSubImage2D
PUBLIC 39a80 0 glTexSubImage3D
PUBLIC 39b00 0 glTextureBarrier
PUBLIC 39b80 0 glTextureBuffer
PUBLIC 39c00 0 glTextureBufferRange
PUBLIC 39c80 0 glTextureParameterIiv
PUBLIC 39d00 0 glTextureParameterIuiv
PUBLIC 39d80 0 glTextureParameterf
PUBLIC 39e00 0 glTextureParameterfv
PUBLIC 39e80 0 glTextureParameteri
PUBLIC 39f00 0 glTextureParameteriv
PUBLIC 39f80 0 glTextureStorage1D
PUBLIC 3a000 0 glTextureStorage2D
PUBLIC 3a080 0 glTextureStorage2DMultisample
PUBLIC 3a100 0 glTextureStorage3D
PUBLIC 3a180 0 glTextureStorage3DMultisample
PUBLIC 3a200 0 glTextureSubImage1D
PUBLIC 3a280 0 glTextureSubImage2D
PUBLIC 3a300 0 glTextureSubImage3D
PUBLIC 3a380 0 glTextureView
PUBLIC 3a400 0 glTransformFeedbackBufferBase
PUBLIC 3a480 0 glTransformFeedbackBufferRange
PUBLIC 3a500 0 glTransformFeedbackVaryings
PUBLIC 3a580 0 glTranslated
PUBLIC 3a600 0 glTranslatef
PUBLIC 3a680 0 glUniform1d
PUBLIC 3a700 0 glUniform1dv
PUBLIC 3a780 0 glUniform1f
PUBLIC 3a800 0 glUniform1fv
PUBLIC 3a880 0 glUniform1i
PUBLIC 3a900 0 glUniform1iv
PUBLIC 3a980 0 glUniform1ui
PUBLIC 3aa00 0 glUniform1uiv
PUBLIC 3aa80 0 glUniform2d
PUBLIC 3ab00 0 glUniform2dv
PUBLIC 3ab80 0 glUniform2f
PUBLIC 3ac00 0 glUniform2fv
PUBLIC 3ac80 0 glUniform2i
PUBLIC 3ad00 0 glUniform2iv
PUBLIC 3ad80 0 glUniform2ui
PUBLIC 3ae00 0 glUniform2uiv
PUBLIC 3ae80 0 glUniform3d
PUBLIC 3af00 0 glUniform3dv
PUBLIC 3af80 0 glUniform3f
PUBLIC 3b000 0 glUniform3fv
PUBLIC 3b080 0 glUniform3i
PUBLIC 3b100 0 glUniform3iv
PUBLIC 3b180 0 glUniform3ui
PUBLIC 3b200 0 glUniform3uiv
PUBLIC 3b280 0 glUniform4d
PUBLIC 3b300 0 glUniform4dv
PUBLIC 3b380 0 glUniform4f
PUBLIC 3b400 0 glUniform4fv
PUBLIC 3b480 0 glUniform4i
PUBLIC 3b500 0 glUniform4iv
PUBLIC 3b580 0 glUniform4ui
PUBLIC 3b600 0 glUniform4uiv
PUBLIC 3b680 0 glUniformBlockBinding
PUBLIC 3b700 0 glUniformMatrix2dv
PUBLIC 3b780 0 glUniformMatrix2fv
PUBLIC 3b800 0 glUniformMatrix2x3dv
PUBLIC 3b880 0 glUniformMatrix2x3fv
PUBLIC 3b900 0 glUniformMatrix2x4dv
PUBLIC 3b980 0 glUniformMatrix2x4fv
PUBLIC 3ba00 0 glUniformMatrix3dv
PUBLIC 3ba80 0 glUniformMatrix3fv
PUBLIC 3bb00 0 glUniformMatrix3x2dv
PUBLIC 3bb80 0 glUniformMatrix3x2fv
PUBLIC 3bc00 0 glUniformMatrix3x4dv
PUBLIC 3bc80 0 glUniformMatrix3x4fv
PUBLIC 3bd00 0 glUniformMatrix4dv
PUBLIC 3bd80 0 glUniformMatrix4fv
PUBLIC 3be00 0 glUniformMatrix4x2dv
PUBLIC 3be80 0 glUniformMatrix4x2fv
PUBLIC 3bf00 0 glUniformMatrix4x3dv
PUBLIC 3bf80 0 glUniformMatrix4x3fv
PUBLIC 3c000 0 glUniformSubroutinesuiv
PUBLIC 3c080 0 glUnmapBuffer
PUBLIC 3c100 0 glUnmapNamedBuffer
PUBLIC 3c180 0 glUseProgram
PUBLIC 3c200 0 glUseProgramStages
PUBLIC 3c280 0 glValidateProgram
PUBLIC 3c300 0 glValidateProgramPipeline
PUBLIC 3c380 0 glVertex2d
PUBLIC 3c400 0 glVertex2dv
PUBLIC 3c480 0 glVertex2f
PUBLIC 3c500 0 glVertex2fv
PUBLIC 3c580 0 glVertex2i
PUBLIC 3c600 0 glVertex2iv
PUBLIC 3c680 0 glVertex2s
PUBLIC 3c700 0 glVertex2sv
PUBLIC 3c780 0 glVertex3d
PUBLIC 3c800 0 glVertex3dv
PUBLIC 3c880 0 glVertex3f
PUBLIC 3c900 0 glVertex3fv
PUBLIC 3c980 0 glVertex3i
PUBLIC 3ca00 0 glVertex3iv
PUBLIC 3ca80 0 glVertex3s
PUBLIC 3cb00 0 glVertex3sv
PUBLIC 3cb80 0 glVertex4d
PUBLIC 3cc00 0 glVertex4dv
PUBLIC 3cc80 0 glVertex4f
PUBLIC 3cd00 0 glVertex4fv
PUBLIC 3cd80 0 glVertex4i
PUBLIC 3ce00 0 glVertex4iv
PUBLIC 3ce80 0 glVertex4s
PUBLIC 3cf00 0 glVertex4sv
PUBLIC 3cf80 0 glVertexArrayAttribBinding
PUBLIC 3d000 0 glVertexArrayAttribFormat
PUBLIC 3d080 0 glVertexArrayAttribIFormat
PUBLIC 3d100 0 glVertexArrayAttribLFormat
PUBLIC 3d180 0 glVertexArrayBindingDivisor
PUBLIC 3d200 0 glVertexArrayElementBuffer
PUBLIC 3d280 0 glVertexArrayVertexBuffer
PUBLIC 3d300 0 glVertexArrayVertexBuffers
PUBLIC 3d380 0 glVertexAttrib1d
PUBLIC 3d400 0 glVertexAttrib1dv
PUBLIC 3d480 0 glVertexAttrib1f
PUBLIC 3d500 0 glVertexAttrib1fv
PUBLIC 3d580 0 glVertexAttrib1s
PUBLIC 3d600 0 glVertexAttrib1sv
PUBLIC 3d680 0 glVertexAttrib2d
PUBLIC 3d700 0 glVertexAttrib2dv
PUBLIC 3d780 0 glVertexAttrib2f
PUBLIC 3d800 0 glVertexAttrib2fv
PUBLIC 3d880 0 glVertexAttrib2s
PUBLIC 3d900 0 glVertexAttrib2sv
PUBLIC 3d980 0 glVertexAttrib3d
PUBLIC 3da00 0 glVertexAttrib3dv
PUBLIC 3da80 0 glVertexAttrib3f
PUBLIC 3db00 0 glVertexAttrib3fv
PUBLIC 3db80 0 glVertexAttrib3s
PUBLIC 3dc00 0 glVertexAttrib3sv
PUBLIC 3dc80 0 glVertexAttrib4Nbv
PUBLIC 3dd00 0 glVertexAttrib4Niv
PUBLIC 3dd80 0 glVertexAttrib4Nsv
PUBLIC 3de00 0 glVertexAttrib4Nub
PUBLIC 3de80 0 glVertexAttrib4Nubv
PUBLIC 3df00 0 glVertexAttrib4Nuiv
PUBLIC 3df80 0 glVertexAttrib4Nusv
PUBLIC 3e000 0 glVertexAttrib4bv
PUBLIC 3e080 0 glVertexAttrib4d
PUBLIC 3e100 0 glVertexAttrib4dv
PUBLIC 3e180 0 glVertexAttrib4f
PUBLIC 3e200 0 glVertexAttrib4fv
PUBLIC 3e280 0 glVertexAttrib4iv
PUBLIC 3e300 0 glVertexAttrib4s
PUBLIC 3e380 0 glVertexAttrib4sv
PUBLIC 3e400 0 glVertexAttrib4ubv
PUBLIC 3e480 0 glVertexAttrib4uiv
PUBLIC 3e500 0 glVertexAttrib4usv
PUBLIC 3e580 0 glVertexAttribBinding
PUBLIC 3e600 0 glVertexAttribDivisor
PUBLIC 3e680 0 glVertexAttribFormat
PUBLIC 3e700 0 glVertexAttribI1i
PUBLIC 3e780 0 glVertexAttribI1iv
PUBLIC 3e800 0 glVertexAttribI1ui
PUBLIC 3e880 0 glVertexAttribI1uiv
PUBLIC 3e900 0 glVertexAttribI2i
PUBLIC 3e980 0 glVertexAttribI2iv
PUBLIC 3ea00 0 glVertexAttribI2ui
PUBLIC 3ea80 0 glVertexAttribI2uiv
PUBLIC 3eb00 0 glVertexAttribI3i
PUBLIC 3eb80 0 glVertexAttribI3iv
PUBLIC 3ec00 0 glVertexAttribI3ui
PUBLIC 3ec80 0 glVertexAttribI3uiv
PUBLIC 3ed00 0 glVertexAttribI4bv
PUBLIC 3ed80 0 glVertexAttribI4i
PUBLIC 3ee00 0 glVertexAttribI4iv
PUBLIC 3ee80 0 glVertexAttribI4sv
PUBLIC 3ef00 0 glVertexAttribI4ubv
PUBLIC 3ef80 0 glVertexAttribI4ui
PUBLIC 3f000 0 glVertexAttribI4uiv
PUBLIC 3f080 0 glVertexAttribI4usv
PUBLIC 3f100 0 glVertexAttribIFormat
PUBLIC 3f180 0 glVertexAttribIPointer
PUBLIC 3f200 0 glVertexAttribL1d
PUBLIC 3f280 0 glVertexAttribL1dv
PUBLIC 3f300 0 glVertexAttribL2d
PUBLIC 3f380 0 glVertexAttribL2dv
PUBLIC 3f400 0 glVertexAttribL3d
PUBLIC 3f480 0 glVertexAttribL3dv
PUBLIC 3f500 0 glVertexAttribL4d
PUBLIC 3f580 0 glVertexAttribL4dv
PUBLIC 3f600 0 glVertexAttribLFormat
PUBLIC 3f680 0 glVertexAttribLPointer
PUBLIC 3f700 0 glVertexAttribP1ui
PUBLIC 3f780 0 glVertexAttribP1uiv
PUBLIC 3f800 0 glVertexAttribP2ui
PUBLIC 3f880 0 glVertexAttribP2uiv
PUBLIC 3f900 0 glVertexAttribP3ui
PUBLIC 3f980 0 glVertexAttribP3uiv
PUBLIC 3fa00 0 glVertexAttribP4ui
PUBLIC 3fa80 0 glVertexAttribP4uiv
PUBLIC 3fb00 0 glVertexAttribPointer
PUBLIC 3fb80 0 glVertexBindingDivisor
PUBLIC 3fc00 0 glVertexP2ui
PUBLIC 3fc80 0 glVertexP2uiv
PUBLIC 3fd00 0 glVertexP3ui
PUBLIC 3fd80 0 glVertexP3uiv
PUBLIC 3fe00 0 glVertexP4ui
PUBLIC 3fe80 0 glVertexP4uiv
PUBLIC 3ff00 0 glVertexPointer
PUBLIC 3ff80 0 glViewport
PUBLIC 40000 0 glViewportArrayv
PUBLIC 40080 0 glViewportIndexedf
PUBLIC 40100 0 glViewportIndexedfv
PUBLIC 40180 0 glWaitSync
PUBLIC 40200 0 glWindowPos2d
PUBLIC 40280 0 glWindowPos2dv
PUBLIC 40300 0 glWindowPos2f
PUBLIC 40380 0 glWindowPos2fv
PUBLIC 40400 0 glWindowPos2i
PUBLIC 40480 0 glWindowPos2iv
PUBLIC 40500 0 glWindowPos2s
PUBLIC 40580 0 glWindowPos2sv
PUBLIC 40600 0 glWindowPos3d
PUBLIC 40680 0 glWindowPos3dv
PUBLIC 40700 0 glWindowPos3f
PUBLIC 40780 0 glWindowPos3fv
PUBLIC 40800 0 glWindowPos3i
PUBLIC 40880 0 glWindowPos3iv
PUBLIC 40900 0 glWindowPos3s
PUBLIC 40980 0 glWindowPos3sv
STACK CFI INIT 14338 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14368 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 143a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 143ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 143b4 x19: .cfa -16 + ^
STACK CFI 143ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 143f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14300 24 .cfa: sp 0 + .ra: x30
STACK CFI 14304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1431c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 142e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 142e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 142fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 143f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14408 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14418 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14420 84 .cfa: sp 0 + .ra: x30
STACK CFI 14424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1442c x19: .cfa -16 + ^
STACK CFI 14460 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14464 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 144a8 38 .cfa: sp 0 + .ra: x30
STACK CFI 144ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 144c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 144c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 144dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 144e0 14 .cfa: sp 0 + .ra: x30
STACK CFI 144e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 144f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 144f8 88 .cfa: sp 0 + .ra: x30
STACK CFI 144fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14504 x19: .cfa -16 + ^
STACK CFI 1452c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14530 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 14580 c4 .cfa: sp 0 + .ra: x30
STACK CFI 14584 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14594 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14624 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 14640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 14648 58 .cfa: sp 0 + .ra: x30
STACK CFI 1464c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14654 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1468c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 146a0 28 .cfa: sp 0 + .ra: x30
STACK CFI 146a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 146ac x19: .cfa -16 + ^
STACK CFI 146c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 146c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 146e8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 146ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 146f4 x19: .cfa -16 + ^
STACK CFI 14788 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14798 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 147a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 147c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 147c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 147cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 147d4 x21: .cfa -16 + ^
STACK CFI 14810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 14818 3c .cfa: sp 0 + .ra: x30
STACK CFI 1481c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1482c x19: .cfa -16 + ^
STACK CFI 14850 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14858 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14860 84 .cfa: sp 0 + .ra: x30
