MODULE Linux arm64 CA271093AE4710218D1480D04A2BC5F50 libebl_tilegx.so
INFO CODE_ID 931027CA47AE21108D1480D04A2BC5F57FFCE310
PUBLIC 11f0 0 tilegx_init
STACK CFI INIT 1008 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1038 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1078 48 .cfa: sp 0 + .ra: x30
STACK CFI 107c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1084 x19: .cfa -16 + ^
STACK CFI 10bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1130 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1140 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1150 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1154 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 115c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11f0 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1280 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12a0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1498 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 149c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 14a4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 14b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 14cc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 157c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1770 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 1774 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1780 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1790 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1798 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1848 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 188c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1890 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
