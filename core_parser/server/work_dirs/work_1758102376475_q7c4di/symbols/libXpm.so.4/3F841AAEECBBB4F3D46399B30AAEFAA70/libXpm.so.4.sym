MODULE Linux arm64 3F841AAEECBBB4F3D46399B30AAEFAA70 libXpm.so.4
INFO CODE_ID AE1A843FBBECF3B4D46399B30AAEFAA745C1FCF0
PUBLIC 29a8 0 xpmFreeColorTable
PUBLIC 2a28 0 XpmFreeExtensions
PUBLIC 2ad8 0 XpmAttributesSize
PUBLIC 2ae0 0 xpmInitAttributes
PUBLIC 2b18 0 xpmSetAttributes
PUBLIC 2c98 0 XpmFreeAttributes
PUBLIC 2e20 0 XpmCreateBufferFromXpmImage
PUBLIC 36d0 0 XpmCreateBufferFromImage
PUBLIC 37a8 0 XpmCreateBufferFromPixmap
PUBLIC 38b0 0 XpmCreateDataFromXpmImage
PUBLIC 4130 0 XpmCreateDataFromImage
PUBLIC 4208 0 XpmCreateDataFromPixmap
PUBLIC 4310 0 XpmCreateImageFromBuffer
PUBLIC 4450 0 XpmCreateXpmImageFromBuffer
PUBLIC 44e8 0 XpmCreateImageFromData
PUBLIC 4638 0 XpmCreateXpmImageFromData
PUBLIC 46e8 0 xpmCreateImageFromPixmap
PUBLIC 47b8 0 XpmCreatePixmapFromBuffer
PUBLIC 4948 0 XpmCreatePixmapFromData
PUBLIC 4ae0 0 xpmCreatePixmapFromImage
PUBLIC 4be0 0 xpmInitXpmImage
PUBLIC 4bf0 0 XpmFreeXpmImage
PUBLIC 4c30 0 xpmInitXpmInfo
PUBLIC 4c48 0 XpmFreeXpmInfo
PUBLIC 4ce8 0 xpmSetInfoMask
PUBLIC 4d18 0 xpmSetInfo
PUBLIC 4d90 0 XpmReadFileToBuffer
PUBLIC 4ef0 0 XpmReadFileToData
PUBLIC 4fa0 0 xpmPipeThrough
PUBLIC 5370 0 XpmReadFileToImage
PUBLIC 54d8 0 XpmReadFileToXpmImage
PUBLIC 55b0 0 XpmReadFileToPixmap
PUBLIC 5740 0 XpmWriteFileFromBuffer
PUBLIC 57b0 0 XpmWriteFileFromData
PUBLIC 5860 0 XpmWriteFileFromXpmImage
PUBLIC 5f98 0 XpmWriteFileFromImage
PUBLIC 6060 0 XpmWriteFileFromPixmap
PUBLIC 7970 0 xpm_xynormalizeimagebits
PUBLIC 7bf0 0 xpm_znormalizeimagebits
PUBLIC 82b8 0 XpmCreateImageFromXpmImage
PUBLIC 8b58 0 XpmCreatePixmapFromXpmImage
PUBLIC 8ce8 0 xpmParseDataAndCreate
PUBLIC 97e0 0 xpmNextString
PUBLIC 9a20 0 xpmNextWord
PUBLIC 9bf0 0 xpmNextUI
PUBLIC 9c70 0 xpmGetString
PUBLIC 9ef8 0 xpmGetCmt
PUBLIC 9f88 0 xpmParseHeader
PUBLIC a270 0 xpmHashSlot
PUBLIC a320 0 xpmHashIntern
PUBLIC a460 0 xpmHashTableInit
PUBLIC a4c0 0 xpmHashTableFree
PUBLIC a520 0 xpmatoui
PUBLIC a580 0 XpmGetErrorString
PUBLIC a618 0 XpmLibraryVersion
PUBLIC a620 0 XpmFree
PUBLIC ac40 0 xpmParseValues
PUBLIC b028 0 xpmParseColors
PUBLIC b648 0 xpmParseExtensions
PUBLIC ba20 0 xpmParseData
PUBLIC bcf0 0 xpmReadRgbNames
PUBLIC beb8 0 xpmGetRgbName
PUBLIC bf18 0 xpmFreeRgbNames
PUBLIC c670 0 XpmCreateXpmImageFromImage
PUBLIC d140 0 XpmCreateXpmImageFromPixmap
STACK CFI INIT 28e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2918 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2958 48 .cfa: sp 0 + .ra: x30
STACK CFI 295c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2964 x19: .cfa -16 + ^
STACK CFI 299c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29a8 7c .cfa: sp 0 + .ra: x30
STACK CFI 29b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 29d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a10 x19: x19 x20: x20
STACK CFI 2a1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2a28 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2a38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a40 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a4c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2a58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 2ad8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ae0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b18 180 .cfa: sp 0 + .ra: x30
STACK CFI 2b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2bdc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2c5c x23: x23 x24: x24
STACK CFI 2c60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2c7c x23: x23 x24: x24
STACK CFI 2c80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 2c98 188 .cfa: sp 0 + .ra: x30
STACK CFI 2c9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ca4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ce8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2cec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2cf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d58 x21: x21 x22: x22
STACK CFI 2da4 x23: x23 x24: x24
STACK CFI 2df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2dfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2e14 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e1c x23: x23 x24: x24
STACK CFI INIT 2e20 8b0 .cfa: sp 0 + .ra: x30
STACK CFI 2e28 .cfa: sp 16576 +
STACK CFI 2e30 .ra: .cfa -16568 + ^ x29: .cfa -16576 + ^
STACK CFI 2e38 x25: .cfa -16512 + ^ x26: .cfa -16504 + ^
STACK CFI 2e48 x19: .cfa -16560 + ^ x20: .cfa -16552 + ^
STACK CFI 2e50 x21: .cfa -16544 + ^ x22: .cfa -16536 + ^
STACK CFI 2e5c x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI 3274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3278 .cfa: sp 16576 + .ra: .cfa -16568 + ^ x19: .cfa -16560 + ^ x20: .cfa -16552 + ^ x21: .cfa -16544 + ^ x22: .cfa -16536 + ^ x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x25: .cfa -16512 + ^ x26: .cfa -16504 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^ x29: .cfa -16576 + ^
STACK CFI INIT 36d0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 36d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 36dc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 36ec x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 36f4 x23: .cfa -112 + ^
STACK CFI 3784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3788 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 37a8 104 .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 37b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 37c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 37cc x23: .cfa -48 + ^
STACK CFI 386c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3870 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 38b0 87c .cfa: sp 0 + .ra: x30
STACK CFI 38b8 .cfa: sp 16624 +
STACK CFI 38c0 .ra: .cfa -16616 + ^ x29: .cfa -16624 + ^
STACK CFI 38f4 x19: .cfa -16608 + ^ x20: .cfa -16600 + ^ x21: .cfa -16592 + ^ x22: .cfa -16584 + ^ x23: .cfa -16576 + ^ x24: .cfa -16568 + ^ x25: .cfa -16560 + ^ x26: .cfa -16552 + ^
STACK CFI 39b4 x27: .cfa -16544 + ^ x28: .cfa -16536 + ^
STACK CFI 3ae8 x27: x27 x28: x28
STACK CFI 3b0c x27: .cfa -16544 + ^ x28: .cfa -16536 + ^
STACK CFI 3b28 x27: x27 x28: x28
STACK CFI 3b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3b74 .cfa: sp 16624 + .ra: .cfa -16616 + ^ x19: .cfa -16608 + ^ x20: .cfa -16600 + ^ x21: .cfa -16592 + ^ x22: .cfa -16584 + ^ x23: .cfa -16576 + ^ x24: .cfa -16568 + ^ x25: .cfa -16560 + ^ x26: .cfa -16552 + ^ x29: .cfa -16624 + ^
STACK CFI 3c24 x27: .cfa -16544 + ^ x28: .cfa -16536 + ^
STACK CFI 3f04 x27: x27 x28: x28
STACK CFI 3f94 x27: .cfa -16544 + ^ x28: .cfa -16536 + ^
STACK CFI 4110 x27: x27 x28: x28
STACK CFI 411c x27: .cfa -16544 + ^ x28: .cfa -16536 + ^
STACK CFI INIT 4130 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 413c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 414c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4154 x23: .cfa -112 + ^
STACK CFI 41e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 41e8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4208 104 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4214 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4224 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 422c x23: .cfa -48 + ^
STACK CFI 42cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4310 13c .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 8432 +
STACK CFI 431c .ra: .cfa -8424 + ^ x29: .cfa -8432 + ^
STACK CFI 4324 x19: .cfa -8416 + ^ x20: .cfa -8408 + ^
STACK CFI 4334 x21: .cfa -8400 + ^ x22: .cfa -8392 + ^
STACK CFI 433c x23: .cfa -8384 + ^ x24: .cfa -8376 + ^
STACK CFI 4348 x25: .cfa -8368 + ^ x26: .cfa -8360 + ^
STACK CFI 4408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 440c .cfa: sp 8432 + .ra: .cfa -8424 + ^ x19: .cfa -8416 + ^ x20: .cfa -8408 + ^ x21: .cfa -8400 + ^ x22: .cfa -8392 + ^ x23: .cfa -8384 + ^ x24: .cfa -8376 + ^ x25: .cfa -8368 + ^ x26: .cfa -8360 + ^ x29: .cfa -8432 + ^
STACK CFI INIT 4450 98 .cfa: sp 0 + .ra: x30
STACK CFI 4458 .cfa: sp 8304 +
STACK CFI 445c .ra: .cfa -8296 + ^ x29: .cfa -8304 + ^
STACK CFI 4464 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 4474 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 44e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44e4 .cfa: sp 8304 + .ra: .cfa -8296 + ^ x19: .cfa -8288 + ^ x20: .cfa -8280 + ^ x21: .cfa -8272 + ^ x22: .cfa -8264 + ^ x29: .cfa -8304 + ^
STACK CFI INIT 44e8 150 .cfa: sp 0 + .ra: x30
STACK CFI 44f0 .cfa: sp 8432 +
STACK CFI 44f4 .ra: .cfa -8424 + ^ x29: .cfa -8432 + ^
STACK CFI 44fc x19: .cfa -8416 + ^ x20: .cfa -8408 + ^
STACK CFI 4508 x21: .cfa -8400 + ^ x22: .cfa -8392 + ^
STACK CFI 4518 x23: .cfa -8384 + ^ x24: .cfa -8376 + ^
STACK CFI 4520 x25: .cfa -8368 + ^ x26: .cfa -8360 + ^
STACK CFI 45f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 45f8 .cfa: sp 8432 + .ra: .cfa -8424 + ^ x19: .cfa -8416 + ^ x20: .cfa -8408 + ^ x21: .cfa -8400 + ^ x22: .cfa -8392 + ^ x23: .cfa -8384 + ^ x24: .cfa -8376 + ^ x25: .cfa -8368 + ^ x26: .cfa -8360 + ^ x29: .cfa -8432 + ^
STACK CFI INIT 4638 ac .cfa: sp 0 + .ra: x30
STACK CFI 4640 .cfa: sp 8304 +
STACK CFI 4644 .ra: .cfa -8296 + ^ x29: .cfa -8304 + ^
STACK CFI 464c x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 465c x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 46dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46e0 .cfa: sp 8304 + .ra: .cfa -8296 + ^ x19: .cfa -8288 + ^ x20: .cfa -8280 + ^ x21: .cfa -8272 + ^ x22: .cfa -8264 + ^ x29: .cfa -8304 + ^
STACK CFI INIT 46e8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 46ec .cfa: sp 112 +
STACK CFI 46f0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 46f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4704 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4714 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4788 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 47b8 18c .cfa: sp 0 + .ra: x30
STACK CFI 47bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 47c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 47d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 47dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4948 194 .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4954 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4964 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 496c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4ae0 100 .cfa: sp 0 + .ra: x30
STACK CFI 4ae4 .cfa: sp 224 +
STACK CFI 4ae8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4af0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4b00 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4b30 x23: .cfa -160 + ^
STACK CFI 4ba0 x23: x23
STACK CFI 4bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4bd0 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 4bdc x23: .cfa -160 + ^
STACK CFI INIT 4be0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bf0 3c .cfa: sp 0 + .ra: x30
STACK CFI 4bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bfc x19: .cfa -16 + ^
STACK CFI 4c28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c48 9c .cfa: sp 0 + .ra: x30
STACK CFI 4c50 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c58 x19: .cfa -16 + ^
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4ce0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ce8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d18 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d90 160 .cfa: sp 0 + .ra: x30
STACK CFI 4d94 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4d9c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4da8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4dd4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4e04 x25: .cfa -160 + ^
STACK CFI 4e50 x19: x19 x20: x20
STACK CFI 4e54 x25: x25
STACK CFI 4e7c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e80 .cfa: sp 224 + .ra: .cfa -216 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 4e88 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4e98 x19: x19 x20: x20
STACK CFI 4e9c x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^
STACK CFI 4eac x19: x19 x20: x20
STACK CFI 4eb0 x25: x25
STACK CFI 4eb4 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^
STACK CFI 4ec4 x19: x19 x20: x20
STACK CFI 4ec8 x25: x25
STACK CFI 4ecc x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^
STACK CFI 4edc x19: x19 x20: x20
STACK CFI 4ee0 x25: x25
STACK CFI 4ee8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4eec x25: .cfa -160 + ^
STACK CFI INIT 4ef0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4ef4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4f00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4f10 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4f18 x23: .cfa -112 + ^
STACK CFI 4f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4f9c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4fa0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4fac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4fb4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4fc0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4fcc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4fd4 x27: .cfa -48 + ^
STACK CFI 511c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5120 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5170 200 .cfa: sp 0 + .ra: x30
STACK CFI 5174 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 517c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5190 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5204 x21: x21 x22: x22
STACK CFI 5214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5218 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 524c x21: x21 x22: x22
STACK CFI 5268 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5270 x23: .cfa -16 + ^
STACK CFI 52d4 x23: x23
STACK CFI 5308 x23: .cfa -16 + ^
STACK CFI 5334 x23: x23
STACK CFI 5344 x21: x21 x22: x22
STACK CFI 5348 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5358 x21: x21 x22: x22
STACK CFI 535c x23: x23
STACK CFI 5360 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5368 x21: x21 x22: x22
STACK CFI 536c x23: x23
STACK CFI INIT 5370 164 .cfa: sp 0 + .ra: x30
STACK CFI 5378 .cfa: sp 8448 +
STACK CFI 537c .ra: .cfa -8440 + ^ x29: .cfa -8448 + ^
STACK CFI 5384 x21: .cfa -8416 + ^ x22: .cfa -8408 + ^
STACK CFI 5394 x19: .cfa -8432 + ^ x20: .cfa -8424 + ^
STACK CFI 53a0 x23: .cfa -8400 + ^ x24: .cfa -8392 + ^
STACK CFI 53a8 x25: .cfa -8384 + ^ x26: .cfa -8376 + ^
STACK CFI 53b4 x27: .cfa -8368 + ^
STACK CFI 54a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 54a8 .cfa: sp 8448 + .ra: .cfa -8440 + ^ x19: .cfa -8432 + ^ x20: .cfa -8424 + ^ x21: .cfa -8416 + ^ x22: .cfa -8408 + ^ x23: .cfa -8400 + ^ x24: .cfa -8392 + ^ x25: .cfa -8384 + ^ x26: .cfa -8376 + ^ x27: .cfa -8368 + ^ x29: .cfa -8448 + ^
STACK CFI INIT 54d8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 54e0 .cfa: sp 8320 +
STACK CFI 54e4 .ra: .cfa -8312 + ^ x29: .cfa -8320 + ^
STACK CFI 54ec x19: .cfa -8304 + ^ x20: .cfa -8296 + ^
STACK CFI 54f8 x21: .cfa -8288 + ^ x22: .cfa -8280 + ^
STACK CFI 5508 x23: .cfa -8272 + ^
STACK CFI 5570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5574 .cfa: sp 8320 + .ra: .cfa -8312 + ^ x19: .cfa -8304 + ^ x20: .cfa -8296 + ^ x21: .cfa -8288 + ^ x22: .cfa -8280 + ^ x23: .cfa -8272 + ^ x29: .cfa -8320 + ^
STACK CFI INIT 55b0 18c .cfa: sp 0 + .ra: x30
STACK CFI 55b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 55bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 55cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 55d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 56ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 56b0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5740 6c .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5750 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 57a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 57b0 ac .cfa: sp 0 + .ra: x30
STACK CFI 57b4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 57c0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 57cc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 57d4 x23: .cfa -112 + ^
STACK CFI 5854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5858 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5860 734 .cfa: sp 0 + .ra: x30
STACK CFI 5868 .cfa: sp 8368 +
STACK CFI 5870 .ra: .cfa -8360 + ^ x29: .cfa -8368 + ^
STACK CFI 5880 x19: .cfa -8352 + ^ x20: .cfa -8344 + ^ x21: .cfa -8336 + ^ x22: .cfa -8328 + ^
STACK CFI 5930 x23: .cfa -8320 + ^ x24: .cfa -8312 + ^
STACK CFI 5934 x25: .cfa -8304 + ^ x26: .cfa -8296 + ^
STACK CFI 5938 x27: .cfa -8288 + ^ x28: .cfa -8280 + ^
STACK CFI 5d20 x23: x23 x24: x24
STACK CFI 5d24 x25: x25 x26: x26
STACK CFI 5d28 x27: x27 x28: x28
STACK CFI 5d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d60 .cfa: sp 8368 + .ra: .cfa -8360 + ^ x19: .cfa -8352 + ^ x20: .cfa -8344 + ^ x21: .cfa -8336 + ^ x22: .cfa -8328 + ^ x29: .cfa -8368 + ^
STACK CFI 5da4 x23: .cfa -8320 + ^ x24: .cfa -8312 + ^
STACK CFI 5db4 x25: .cfa -8304 + ^ x26: .cfa -8296 + ^
STACK CFI 5db8 x27: .cfa -8288 + ^ x28: .cfa -8280 + ^
STACK CFI 5e2c x23: x23 x24: x24
STACK CFI 5e30 x25: x25 x26: x26
STACK CFI 5e34 x27: x27 x28: x28
STACK CFI 5e38 x23: .cfa -8320 + ^ x24: .cfa -8312 + ^ x25: .cfa -8304 + ^ x26: .cfa -8296 + ^ x27: .cfa -8288 + ^ x28: .cfa -8280 + ^
STACK CFI 5e94 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5ebc x23: .cfa -8320 + ^ x24: .cfa -8312 + ^ x25: .cfa -8304 + ^ x26: .cfa -8296 + ^ x27: .cfa -8288 + ^ x28: .cfa -8280 + ^
STACK CFI 5f6c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5f88 x23: .cfa -8320 + ^ x24: .cfa -8312 + ^
STACK CFI 5f8c x25: .cfa -8304 + ^ x26: .cfa -8296 + ^
STACK CFI 5f90 x27: .cfa -8288 + ^ x28: .cfa -8280 + ^
STACK CFI INIT 5f98 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5f9c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5fa4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5fb0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5fb8 x23: .cfa -112 + ^
STACK CFI 6040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6044 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6060 104 .cfa: sp 0 + .ra: x30
STACK CFI 6064 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 606c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 607c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6084 x23: .cfa -48 + ^
STACK CFI 6124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6128 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6168 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6178 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6288 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 62b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 62c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 62c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62d8 x21: .cfa -16 + ^
STACK CFI 630c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6310 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6328 12c .cfa: sp 0 + .ra: x30
STACK CFI 632c .cfa: sp 64 +
STACK CFI 6334 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 633c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6344 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63e4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6458 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6490 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64c0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6528 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6588 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65e0 570 .cfa: sp 0 + .ra: x30
STACK CFI 65e4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 65ec x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 65f8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6608 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 6614 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 661c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6934 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 6b50 6d8 .cfa: sp 0 + .ra: x30
STACK CFI 6b58 .cfa: sp 10400 +
STACK CFI 6b5c .ra: .cfa -10392 + ^ x29: .cfa -10400 + ^
STACK CFI 6b64 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^
STACK CFI 6b74 x21: .cfa -10368 + ^ x22: .cfa -10360 + ^
STACK CFI 6b80 x27: .cfa -10320 + ^ x28: .cfa -10312 + ^
STACK CFI 6bf0 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 6bf8 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 6cec x23: x23 x24: x24
STACK CFI 6cf0 x25: x25 x26: x26
STACK CFI 6cf8 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 6cfc x23: x23 x24: x24
STACK CFI 6d00 x25: x25 x26: x26
STACK CFI 6d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 6d3c .cfa: sp 10400 + .ra: .cfa -10392 + ^ x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x21: .cfa -10368 + ^ x22: .cfa -10360 + ^ x27: .cfa -10320 + ^ x28: .cfa -10312 + ^ x29: .cfa -10400 + ^
STACK CFI 6d40 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 6da0 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 6e54 x25: x25 x26: x26
STACK CFI 6e5c x23: x23 x24: x24
STACK CFI 6e74 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 6ef4 x23: x23 x24: x24
STACK CFI 6ef8 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 6f10 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 702c x23: x23 x24: x24
STACK CFI 7030 x25: x25 x26: x26
STACK CFI 7044 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 7054 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 719c x25: x25 x26: x26
STACK CFI 71a4 x23: x23 x24: x24
STACK CFI 71a8 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 71bc x25: x25 x26: x26
STACK CFI 71e4 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 720c x25: x25 x26: x26
STACK CFI 7214 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI 721c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7220 x23: .cfa -10352 + ^ x24: .cfa -10344 + ^
STACK CFI 7224 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI INIT 7228 744 .cfa: sp 0 + .ra: x30
STACK CFI 722c .cfa: sp 272 +
STACK CFI 7230 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 7238 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7240 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7248 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7398 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 73a8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 74a0 x25: x25 x26: x26
STACK CFI 74a4 x27: x27 x28: x28
STACK CFI 74c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 74cc .cfa: sp 272 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 7570 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 782c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 78a4 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 78d0 x25: x25 x26: x26
STACK CFI 78d4 x27: x27 x28: x28
STACK CFI 78d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 78dc .cfa: sp 272 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 791c x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7920 x25: x25 x26: x26
STACK CFI 7924 x27: x27 x28: x28
STACK CFI INIT 7970 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a20 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 7a24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7a34 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7a40 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7a60 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7a68 x25: .cfa -48 + ^
STACK CFI 7b74 x21: x21 x22: x22
STACK CFI 7b7c x25: x25
STACK CFI 7ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 7ba4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 7bd4 x21: x21 x22: x22 x25: x25
STACK CFI 7bdc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^
STACK CFI 7be4 x21: x21 x22: x22 x25: x25
STACK CFI 7be8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7bec x25: .cfa -48 + ^
STACK CFI INIT 7bf0 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c98 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 7c9c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7ca4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 7cd4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7ce4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 7cf8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 7d10 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 7e7c x21: x21 x22: x22
STACK CFI 7e80 x25: x25 x26: x26
STACK CFI 7e84 x27: x27 x28: x28
STACK CFI 7eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 7eb0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 7eb4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 7ed8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 7ee0 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8068 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 806c x25: x25 x26: x26
STACK CFI 8070 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 8074 x21: x21 x22: x22
STACK CFI 807c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 8080 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8084 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 8088 22c .cfa: sp 0 + .ra: x30
STACK CFI INIT 82b8 6e4 .cfa: sp 0 + .ra: x30
STACK CFI 82bc .cfa: sp 208 +
STACK CFI 82c4 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 82d4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 82e0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 82ec x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 82f4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 83ac x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 8514 x23: x23 x24: x24
STACK CFI 8564 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 85d4 x23: x23 x24: x24
STACK CFI 860c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8610 .cfa: sp 208 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 8614 x23: x23 x24: x24
STACK CFI 8624 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 86a0 x23: x23 x24: x24
STACK CFI 8708 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 890c x23: x23 x24: x24
STACK CFI 8914 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 8994 x23: x23 x24: x24
STACK CFI 8998 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI INIT 89a0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 89a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 89b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 89c0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 89dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 89e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8ad4 x23: x23 x24: x24
STACK CFI 8adc x25: x25 x26: x26
STACK CFI 8b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 8b10 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8b18 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8b48 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8b4c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8b50 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 8b58 18c .cfa: sp 0 + .ra: x30
STACK CFI 8b5c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8b64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8b7c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c58 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8ce8 7cc .cfa: sp 0 + .ra: x30
STACK CFI 8cec .cfa: sp 336 +
STACK CFI 8cf4 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 8cfc x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8d10 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 8d1c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 8d28 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 8d30 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 8f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8f74 .cfa: sp 336 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 94b8 324 .cfa: sp 0 + .ra: x30
STACK CFI 94bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 94c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 94e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 955c x19: x19 x20: x20
STACK CFI 956c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9570 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9578 x25: .cfa -16 + ^
STACK CFI 9668 x19: x19 x20: x20
STACK CFI 9674 x25: x25
STACK CFI 9678 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 967c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9690 x19: x19 x20: x20
STACK CFI 969c x25: x25
STACK CFI 96a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 96a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 97e0 23c .cfa: sp 0 + .ra: x30
STACK CFI 97e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 97ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 981c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9830 x21: .cfa -16 + ^
STACK CFI 9894 x21: x21
STACK CFI 98a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 98a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9904 x21: x21
STACK CFI 9908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 990c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9944 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9a10 x21: .cfa -16 + ^
STACK CFI 9a18 x21: x21
STACK CFI INIT 9a20 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 9a24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9a38 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9a44 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9b14 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9bd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9bf0 80 .cfa: sp 0 + .ra: x30
STACK CFI 9bf8 .cfa: sp 8256 +
STACK CFI 9c00 .ra: .cfa -8248 + ^ x29: .cfa -8256 + ^
STACK CFI 9c08 x19: .cfa -8240 + ^ x20: .cfa -8232 + ^
STACK CFI 9c18 x21: .cfa -8224 + ^
STACK CFI 9c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c6c .cfa: sp 8256 + .ra: .cfa -8248 + ^ x19: .cfa -8240 + ^ x20: .cfa -8232 + ^ x21: .cfa -8224 + ^ x29: .cfa -8256 + ^
STACK CFI INIT 9c70 284 .cfa: sp 0 + .ra: x30
STACK CFI 9c78 .cfa: sp 8320 +
STACK CFI 9c7c .ra: .cfa -8312 + ^ x29: .cfa -8320 + ^
STACK CFI 9c84 x25: .cfa -8256 + ^ x26: .cfa -8248 + ^
STACK CFI 9c8c x19: .cfa -8304 + ^ x20: .cfa -8296 + ^
STACK CFI 9ca0 x21: .cfa -8288 + ^ x22: .cfa -8280 + ^ x23: .cfa -8272 + ^ x24: .cfa -8264 + ^ x27: .cfa -8240 + ^ x28: .cfa -8232 + ^
STACK CFI 9d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9d78 .cfa: sp 8320 + .ra: .cfa -8312 + ^ x19: .cfa -8304 + ^ x20: .cfa -8296 + ^ x21: .cfa -8288 + ^ x22: .cfa -8280 + ^ x23: .cfa -8272 + ^ x24: .cfa -8264 + ^ x25: .cfa -8256 + ^ x26: .cfa -8248 + ^ x27: .cfa -8240 + ^ x28: .cfa -8232 + ^ x29: .cfa -8320 + ^
STACK CFI INIT 9ef8 8c .cfa: sp 0 + .ra: x30
STACK CFI 9efc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 9f58 x21: .cfa -32 + ^
STACK CFI 9f78 x21: x21
STACK CFI INIT 9f88 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 9f90 .cfa: sp 8288 +
STACK CFI 9f9c .ra: .cfa -8280 + ^ x29: .cfa -8288 + ^
STACK CFI 9fa4 x21: .cfa -8256 + ^ x22: .cfa -8248 + ^
STACK CFI 9fb0 x19: .cfa -8272 + ^ x20: .cfa -8264 + ^
STACK CFI a008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a00c .cfa: sp 8288 + .ra: .cfa -8280 + ^ x19: .cfa -8272 + ^ x20: .cfa -8264 + ^ x21: .cfa -8256 + ^ x22: .cfa -8248 + ^ x29: .cfa -8288 + ^
STACK CFI a010 x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI a0d8 x23: x23 x24: x24
STACK CFI a0dc x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI a11c x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI a14c x23: x23 x24: x24
STACK CFI a150 x25: x25 x26: x26
STACK CFI a154 x23: .cfa -8240 + ^ x24: .cfa -8232 + ^ x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI a1b4 x23: x23 x24: x24
STACK CFI a1b8 x25: x25 x26: x26
STACK CFI a1bc x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI a1d8 x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI a234 x23: x23 x24: x24
STACK CFI a238 x25: x25 x26: x26
STACK CFI a23c x23: .cfa -8240 + ^ x24: .cfa -8232 + ^ x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI a240 x25: x25 x26: x26
STACK CFI a248 x23: x23 x24: x24
STACK CFI a24c x23: .cfa -8240 + ^ x24: .cfa -8232 + ^ x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI a260 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a264 x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI a268 x25: .cfa -8224 + ^ x26: .cfa -8216 + ^
STACK CFI INIT a270 b0 .cfa: sp 0 + .ra: x30
STACK CFI a274 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a284 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI a314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a318 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a320 140 .cfa: sp 0 + .ra: x30
STACK CFI a324 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a32c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a334 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a35c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a360 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a39c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a460 60 .cfa: sp 0 + .ra: x30
STACK CFI a464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a474 x19: .cfa -16 + ^
STACK CFI a4b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a4b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a4c0 5c .cfa: sp 0 + .ra: x30
STACK CFI a4c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4d8 x21: .cfa -16 + ^
STACK CFI a510 x21: x21
STACK CFI a518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a520 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT a580 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT a618 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a628 614 .cfa: sp 0 + .ra: x30
STACK CFI a630 .cfa: sp 10400 +
STACK CFI a634 .ra: .cfa -10392 + ^ x29: .cfa -10400 + ^
STACK CFI a63c x21: .cfa -10368 + ^ x22: .cfa -10360 + ^
STACK CFI a66c x23: .cfa -10352 + ^ x24: .cfa -10344 + ^ x27: .cfa -10320 + ^ x28: .cfa -10312 + ^
STACK CFI a680 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^
STACK CFI a68c x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI a74c x19: x19 x20: x20
STACK CFI a750 x25: x25 x26: x26
STACK CFI a754 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI a77c x19: x19 x20: x20
STACK CFI a780 x25: x25 x26: x26
STACK CFI a784 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI a888 x19: x19 x20: x20
STACK CFI a88c x25: x25 x26: x26
STACK CFI a890 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI aa54 x19: x19 x20: x20
STACK CFI aa5c x25: x25 x26: x26
STACK CFI aa94 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI aa98 .cfa: sp 10400 + .ra: .cfa -10392 + ^ x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x21: .cfa -10368 + ^ x22: .cfa -10360 + ^ x23: .cfa -10352 + ^ x24: .cfa -10344 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^ x27: .cfa -10320 + ^ x28: .cfa -10312 + ^ x29: .cfa -10400 + ^
STACK CFI abc4 x19: x19 x20: x20
STACK CFI abc8 x25: x25 x26: x26
STACK CFI abcc x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI ac18 x19: x19 x20: x20
STACK CFI ac1c x25: x25 x26: x26
STACK CFI ac28 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^ x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI ac30 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI ac34 x19: .cfa -10384 + ^ x20: .cfa -10376 + ^
STACK CFI ac38 x25: .cfa -10336 + ^ x26: .cfa -10328 + ^
STACK CFI INIT ac40 3e4 .cfa: sp 0 + .ra: x30
STACK CFI ac48 .cfa: sp 8352 +
STACK CFI ac50 .ra: .cfa -8344 + ^ x29: .cfa -8352 + ^
STACK CFI ac5c x19: .cfa -8336 + ^ x20: .cfa -8328 + ^
STACK CFI ac68 x21: .cfa -8320 + ^ x22: .cfa -8312 + ^ x23: .cfa -8304 + ^ x24: .cfa -8296 + ^
STACK CFI ac74 x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI ac7c x27: .cfa -8272 + ^ x28: .cfa -8264 + ^
STACK CFI ad3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ad40 .cfa: sp 8352 + .ra: .cfa -8344 + ^ x19: .cfa -8336 + ^ x20: .cfa -8328 + ^ x21: .cfa -8320 + ^ x22: .cfa -8312 + ^ x23: .cfa -8304 + ^ x24: .cfa -8296 + ^ x25: .cfa -8288 + ^ x26: .cfa -8280 + ^ x27: .cfa -8272 + ^ x28: .cfa -8264 + ^ x29: .cfa -8352 + ^
STACK CFI INIT b028 620 .cfa: sp 0 + .ra: x30
STACK CFI b030 .cfa: sp 16576 +
STACK CFI b034 .ra: .cfa -16568 + ^ x29: .cfa -16576 + ^
STACK CFI b03c x21: .cfa -16544 + ^ x22: .cfa -16536 + ^
STACK CFI b048 x19: .cfa -16560 + ^ x20: .cfa -16552 + ^
STACK CFI b074 x25: .cfa -16512 + ^ x26: .cfa -16504 + ^
STACK CFI b084 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^
STACK CFI b180 x23: x23 x24: x24
STACK CFI b184 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^
STACK CFI b198 x23: x23 x24: x24
STACK CFI b1d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI b1d4 .cfa: sp 16576 + .ra: .cfa -16568 + ^ x19: .cfa -16560 + ^ x20: .cfa -16552 + ^ x21: .cfa -16544 + ^ x22: .cfa -16536 + ^ x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x25: .cfa -16512 + ^ x26: .cfa -16504 + ^ x29: .cfa -16576 + ^
STACK CFI b1fc x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b3b8 x27: x27 x28: x28
STACK CFI b3cc x23: x23 x24: x24
STACK CFI b3d0 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b3e4 x23: x23 x24: x24
STACK CFI b3e8 x27: x27 x28: x28
STACK CFI b3ec x23: .cfa -16528 + ^ x24: .cfa -16520 + ^
STACK CFI b3f0 x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b4f0 x27: x27 x28: x28
STACK CFI b4f4 x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b5bc x23: x23 x24: x24
STACK CFI b5c0 x27: x27 x28: x28
STACK CFI b5c4 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b5d0 x27: x27 x28: x28
STACK CFI b5d4 x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b5dc x27: x27 x28: x28
STACK CFI b5f0 x23: x23 x24: x24
STACK CFI b5f4 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b5fc x27: x27 x28: x28
STACK CFI b600 x23: x23 x24: x24
STACK CFI b608 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b618 x23: x23 x24: x24
STACK CFI b61c x27: x27 x28: x28
STACK CFI b620 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^ x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI b634 x23: x23 x24: x24
STACK CFI b638 x27: x27 x28: x28
STACK CFI b640 x23: .cfa -16528 + ^ x24: .cfa -16520 + ^
STACK CFI b644 x27: .cfa -16496 + ^ x28: .cfa -16488 + ^
STACK CFI INIT b648 3d8 .cfa: sp 0 + .ra: x30
STACK CFI b64c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI b65c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI b664 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI b66c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI b6bc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI b6c4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI b8cc x21: x21 x22: x22
STACK CFI b8d0 x27: x27 x28: x28
STACK CFI b900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b904 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI b920 x21: x21 x22: x22
STACK CFI b924 x27: x27 x28: x28
STACK CFI b928 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI b93c x21: x21 x22: x22
STACK CFI b940 x27: x27 x28: x28
STACK CFI b958 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI b9b4 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI b9b8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI b9bc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI b9e4 x21: x21 x22: x22
STACK CFI b9e8 x27: x27 x28: x28
STACK CFI b9ec x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI ba18 x21: x21 x22: x22
STACK CFI ba1c x27: x27 x28: x28
STACK CFI INIT ba20 2d0 .cfa: sp 0 + .ra: x30
STACK CFI ba24 .cfa: sp 208 +
STACK CFI ba28 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI ba30 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI ba3c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI ba48 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI ba50 x25: .cfa -128 + ^
STACK CFI bac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI bac4 .cfa: sp 208 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI INIT bcf0 1c8 .cfa: sp 0 + .ra: x30
STACK CFI bcf4 .cfa: sp 1168 +
STACK CFI bcf8 .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI bd00 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI bd10 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI bd3c x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI bd48 x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI be64 x19: x19 x20: x20
STACK CFI be68 x23: x23 x24: x24
STACK CFI be98 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI be9c .cfa: sp 1168 + .ra: .cfa -1160 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^ x29: .cfa -1168 + ^
STACK CFI bea4 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI beac x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI beb0 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI beb4 x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI INIT beb8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT bf18 4c .cfa: sp 0 + .ra: x30
STACK CFI bf24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bf5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bf68 178 .cfa: sp 0 + .ra: x30
STACK CFI bf6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bf74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bf84 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bf90 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bfbc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c014 x25: x25 x26: x26
STACK CFI c01c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI c020 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI c03c x25: x25 x26: x26
STACK CFI c054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI c058 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI c064 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c0dc x25: x25 x26: x26
STACK CFI INIT c0e0 47c .cfa: sp 0 + .ra: x30
STACK CFI c0e8 .cfa: sp 32944 +
STACK CFI c0f8 .ra: .cfa -32936 + ^ x29: .cfa -32944 + ^
STACK CFI c104 x19: .cfa -32928 + ^ x20: .cfa -32920 + ^
STACK CFI c110 x21: .cfa -32912 + ^ x22: .cfa -32904 + ^
STACK CFI c184 x23: .cfa -32896 + ^ x24: .cfa -32888 + ^
STACK CFI c19c x25: .cfa -32880 + ^ x26: .cfa -32872 + ^
STACK CFI c1a0 x27: .cfa -32864 + ^ x28: .cfa -32856 + ^
STACK CFI c358 x23: x23 x24: x24
STACK CFI c360 x25: x25 x26: x26
STACK CFI c364 x27: x27 x28: x28
STACK CFI c39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c3a0 .cfa: sp 32944 + .ra: .cfa -32936 + ^ x19: .cfa -32928 + ^ x20: .cfa -32920 + ^ x21: .cfa -32912 + ^ x22: .cfa -32904 + ^ x29: .cfa -32944 + ^
STACK CFI c3c0 x23: .cfa -32896 + ^ x24: .cfa -32888 + ^ x25: .cfa -32880 + ^ x26: .cfa -32872 + ^ x27: .cfa -32864 + ^ x28: .cfa -32856 + ^
STACK CFI c410 x23: x23 x24: x24
STACK CFI c414 x25: x25 x26: x26
STACK CFI c418 x27: x27 x28: x28
STACK CFI c41c x23: .cfa -32896 + ^ x24: .cfa -32888 + ^ x25: .cfa -32880 + ^ x26: .cfa -32872 + ^ x27: .cfa -32864 + ^ x28: .cfa -32856 + ^
STACK CFI c50c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c524 x23: .cfa -32896 + ^ x24: .cfa -32888 + ^
STACK CFI c528 x23: x23 x24: x24
STACK CFI c530 x23: .cfa -32896 + ^ x24: .cfa -32888 + ^ x25: .cfa -32880 + ^ x26: .cfa -32872 + ^ x27: .cfa -32864 + ^ x28: .cfa -32856 + ^
STACK CFI c540 x23: x23 x24: x24
STACK CFI c544 x25: x25 x26: x26
STACK CFI c548 x27: x27 x28: x28
STACK CFI c550 x23: .cfa -32896 + ^ x24: .cfa -32888 + ^
STACK CFI c554 x25: .cfa -32880 + ^ x26: .cfa -32872 + ^
STACK CFI c558 x27: .cfa -32864 + ^ x28: .cfa -32856 + ^
STACK CFI INIT c560 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT c5a0 b8 .cfa: sp 0 + .ra: x30
STACK CFI c5a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c5ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c5b4 x21: .cfa -32 + ^
STACK CFI c600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c604 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT c658 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c670 acc .cfa: sp 0 + .ra: x30
STACK CFI c674 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI c688 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI c698 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI c6bc x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI c738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c73c .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x29: .cfa -272 + ^
STACK CFI c768 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI c858 x27: x27 x28: x28
STACK CFI c868 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI cbc0 x27: x27 x28: x28
STACK CFI cbc8 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI cc28 x27: x27 x28: x28
STACK CFI cc2c x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI d114 x27: x27 x28: x28
STACK CFI d118 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI d128 x27: x27 x28: x28
STACK CFI d12c x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI d130 x27: x27 x28: x28
STACK CFI d138 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT d140 100 .cfa: sp 0 + .ra: x30
STACK CFI d144 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d14c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d15c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d164 x23: .cfa -48 + ^
STACK CFI d204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d208 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
