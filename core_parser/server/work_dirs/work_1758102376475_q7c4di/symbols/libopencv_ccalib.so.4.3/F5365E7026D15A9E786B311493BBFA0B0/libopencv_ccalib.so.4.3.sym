MODULE Linux arm64 F5365E7026D15A9E786B311493BBFA0B0 libopencv_ccalib.so.4.3
INFO CODE_ID 705E36F5D1269E5A786B311493BBFA0B5B70B921
PUBLIC 86d0 0 _init
PUBLIC 95e0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.125]
PUBLIC 9680 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.167]
PUBLIC 96c4 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.121]
PUBLIC 9764 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.120]
PUBLIC 9808 0 _GLOBAL__sub_I_multicalib.cpp
PUBLIC 9838 0 _GLOBAL__sub_I_omnidir.cpp
PUBLIC 9868 0 _GLOBAL__sub_I_randpattern.cpp
PUBLIC 9898 0 call_weak_fn
PUBLIC 98b0 0 deregister_tm_clones
PUBLIC 98e8 0 register_tm_clones
PUBLIC 9928 0 __do_global_dtors_aux
PUBLIC 9970 0 frame_dummy
PUBLIC 99a8 0 cv::Algorithm::clear()
PUBLIC 99b0 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 99b8 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 99c0 0 cv::Algorithm::empty() const
PUBLIC 99c8 0 cv::ccalib::CustomPattern::~CustomPattern()
PUBLIC 9d18 0 cv::ccalib::CustomPattern::~CustomPattern()
PUBLIC 9d30 0 cv::Mat::~Mat()
PUBLIC 9dc0 0 cv::ccalib::CustomPattern::CustomPattern()
PUBLIC 9e70 0 cv::ccalib::CustomPattern::isInitialized()
PUBLIC 9e78 0 cv::ccalib::CustomPattern::setFeatureDetector(cv::Ptr<cv::Feature2D>)
PUBLIC 9fb0 0 cv::ccalib::CustomPattern::setDescriptorExtractor(cv::Ptr<cv::Feature2D>)
PUBLIC a0e8 0 cv::ccalib::CustomPattern::setDescriptorMatcher(cv::Ptr<cv::DescriptorMatcher>)
PUBLIC a220 0 cv::ccalib::CustomPattern::getFeatureDetector()
PUBLIC a270 0 cv::ccalib::CustomPattern::getDescriptorExtractor()
PUBLIC a2c0 0 cv::ccalib::CustomPattern::getDescriptorMatcher()
PUBLIC a310 0 cv::ccalib::CustomPattern::updateKeypointsPos(std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&)
PUBLIC a360 0 cv::ccalib::CustomPattern::refinePointsPos(cv::Mat const&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&)
PUBLIC a4f0 0 cv::ccalib::CustomPattern::check_matches(std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >&, std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >&, cv::Mat const&)
PUBLIC a688 0 cv::ccalib::CustomPattern::getPixelSize()
PUBLIC a690 0 cv::ccalib::CustomPattern::calibrate(cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria)
PUBLIC a6e0 0 cv::ccalib::CustomPattern::findRt(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool, int)
PUBLIC a708 0 cv::ccalib::CustomPattern::findRtRANSAC(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool, int, float, int, cv::_OutputArray const&, int)
PUBLIC a9b0 0 cv::ccalib::CustomPattern::drawOrientation(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, double, int)
PUBLIC aea0 0 std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >::operator=(std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&)
PUBLIC b0e0 0 cv::ccalib::CustomPattern::getPatternPoints(std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >&)
PUBLIC b0f0 0 _ZNK2cv6Point_IfEcvNS0_IT_EEIiEEv
PUBLIC b128 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::operator=(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC b338 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC b3f0 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> const&>(cv::Point_<float> const&)
PUBLIC b4f0 0 cv::ccalib::CustomPattern::keypoints2points(std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&)
PUBLIC b640 0 cv::ccalib::CustomPattern::refineKeypointsPos(cv::Mat const&, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >&)
PUBLIC b6c8 0 void std::vector<cv::DMatch, std::allocator<cv::DMatch> >::_M_emplace_back_aux<cv::DMatch const&>(cv::DMatch const&)
PUBLIC b7c0 0 void std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::_M_emplace_back_aux<cv::Point3_<float> const&>(cv::Point3_<float> const&)
PUBLIC b900 0 cv::ccalib::CustomPattern::findPatternPass(cv::Mat const&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&, std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >&, cv::Mat&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&, double, double, bool, cv::Mat const&, cv::_OutputArray const&)
PUBLIC c690 0 cv::ccalib::CustomPattern::findPattern(cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, double, double, bool, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC d470 0 cv::ccalib::CustomPattern::findRt(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool, int)
PUBLIC d600 0 cv::ccalib::CustomPattern::findRtRANSAC(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool, int, float, int, cv::_OutputArray const&, int)
PUBLIC d800 0 void std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::_M_emplace_back_aux<cv::Point3_<float> >(cv::Point3_<float>&&)
PUBLIC d940 0 cv::ccalib::CustomPattern::scaleFoundPoints(double, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&, std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >&)
PUBLIC da40 0 cv::ccalib::CustomPattern::init(cv::Mat&, float, cv::_OutputArray const&)
PUBLIC e410 0 cv::ccalib::CustomPattern::create(cv::_InputArray const&, cv::Size_<float>, cv::_OutputArray const&)
PUBLIC e640 0 std::ctype<char>::do_widen(char) const
PUBLIC e648 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char const*>(char const*, char const*, std::forward_iterator_tag) [clone .isra.165]
PUBLIC e710 0 cv::Mat::Mat(cv::Mat const&)
PUBLIC e790 0 cv::Mat::operator=(cv::Mat const&)
PUBLIC e8b0 0 cv::Mat::create(int, int, int)
PUBLIC e910 0 cv::Mat::operator=(cv::Mat&&)
PUBLIC ea40 0 cv::MatExpr::operator cv::Mat() const
PUBLIC ead0 0 cv::MatExpr::~MatExpr()
PUBLIC eb00 0 cv::multicalib::MultiCameraCalibration::compose_motion(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC f6a0 0 cv::multicalib::MultiCameraCalibration::computePhotoCameraJacobian(cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat&, cv::Mat&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC 103b0 0 cv::multicalib::MultiCameraCalibration::computeJacobianExtrinsic(cv::Mat const&, cv::Mat&, cv::Mat&)
PUBLIC 110c0 0 cv::multicalib::MultiCameraCalibration::parameters2vector(std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > > const&, std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > > const&, cv::Mat&)
PUBLIC 117b8 0 std::vector<cv::multicalib::MultiCameraCalibration::edge, std::allocator<cv::multicalib::MultiCameraCalibration::edge> >::~vector()
PUBLIC 11878 0 std::vector<cv::multicalib::MultiCameraCalibration::vertex, std::allocator<cv::multicalib::MultiCameraCalibration::vertex> >::~vector()
PUBLIC 11938 0 std::vector<std::vector<cv::Mat, std::allocator<cv::Mat> >, std::allocator<std::vector<cv::Mat, std::allocator<cv::Mat> > > >::~vector()
PUBLIC 11a28 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 11ae8 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC 11b48 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::~vector()
PUBLIC 11ba8 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 11bf0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 11c40 0 cv::randpattern::RandomPatternCornerFinder::~RandomPatternCornerFinder()
PUBLIC 11ca0 0 cv::operator<<(std::ostream&, cv::Mat const&) [clone .constprop.278]
PUBLIC 11d98 0 std::vector<std::vector<cv::Mat, std::allocator<cv::Mat> >, std::allocator<std::vector<cv::Mat, std::allocator<cv::Mat> > > >::_M_default_append(unsigned long)
PUBLIC 11f58 0 std::vector<std::vector<cv::Mat, std::allocator<cv::Mat> >, std::allocator<std::vector<cv::Mat, std::allocator<cv::Mat> > > >::resize(unsigned long)
PUBLIC 12080 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_default_append(unsigned long)
PUBLIC 12248 0 void std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_emplace_back_aux<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 12408 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int const&>(int const&)
PUBLIC 124f0 0 cv::multicalib::MultiCameraCalibration::findRowNonZero(cv::Mat const&, cv::Mat&)
PUBLIC 12900 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 12b58 0 std::_Deque_base<int, std::allocator<int> >::~_Deque_base()
PUBLIC 12bb0 0 void std::vector<cv::multicalib::MultiCameraCalibration::vertex, std::allocator<cv::multicalib::MultiCameraCalibration::vertex> >::_M_emplace_back_aux<cv::multicalib::MultiCameraCalibration::vertex>(cv::multicalib::MultiCameraCalibration::vertex&&)
PUBLIC 12eb0 0 cv::multicalib::MultiCameraCalibration::getPhotoVertex(int)
PUBLIC 13110 0 void std::vector<cv::multicalib::MultiCameraCalibration::edge, std::allocator<cv::multicalib::MultiCameraCalibration::edge> >::_M_emplace_back_aux<cv::multicalib::MultiCameraCalibration::edge>(cv::multicalib::MultiCameraCalibration::edge&&)
PUBLIC 13430 0 std::vector<int, std::allocator<int> >::_M_fill_insert(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, unsigned long, int const&)
PUBLIC 13aa0 0 std::_Bvector_base<std::allocator<bool> >::_M_deallocate()
PUBLIC 13ab8 0 std::_Deque_base<int, std::allocator<int> >::_M_initialize_map(unsigned long)
PUBLIC 13bc8 0 void std::deque<int, std::allocator<int> >::_M_push_back_aux<int const&>(int const&)
PUBLIC 13d70 0 cv::multicalib::MultiCameraCalibration::graphTraverse(cv::Mat const&, int, std::vector<int, std::allocator<int> >&, std::vector<int, std::allocator<int> >&)
PUBLIC 14770 0 cv::multicalib::MultiCameraCalibration::initialize()
PUBLIC 14de0 0 void std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_emplace_back_aux<cv::Vec<float, 3> >(cv::Vec<float, 3>&&)
PUBLIC 14f20 0 cv::multicalib::MultiCameraCalibration::vector2parameters(cv::Mat const&, std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >&, std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >&)
PUBLIC 155a0 0 cv::multicalib::MultiCameraCalibration::computeProjectError(cv::Mat&)
PUBLIC 16810 0 cv::multicalib::MultiCameraCalibration::optimizeExtrinsics()
PUBLIC 17490 0 cv::Mat* std::__uninitialized_copy<false>::__uninit_copy<cv::Mat const*, cv::Mat*>(cv::Mat const*, cv::Mat const*, cv::Mat*)
PUBLIC 175c8 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_default_append(unsigned long)
PUBLIC 177e0 0 cv::multicalib::MultiCameraCalibration::MultiCameraCalibration(int, int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, float, float, int, int, int, int, cv::TermCriteria, cv::Ptr<cv::Feature2D>, cv::Ptr<cv::Feature2D>, cv::Ptr<cv::DescriptorMatcher>)
PUBLIC 17e20 0 cv::multicalib::MultiCameraCalibration::readStringList[abi:cxx11]()
PUBLIC 18190 0 cv::multicalib::MultiCameraCalibration::loadImages()
PUBLIC 19d78 0 cv::multicalib::MultiCameraCalibration::run()
PUBLIC 19d98 0 cv::multicalib::MultiCameraCalibration::writeParameters(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 1ad40 0 cv::Mat::Mat<cv::Vec<double, 3> >(std::vector<cv::Vec<double, 3>, std::allocator<cv::Vec<double, 3> > > const&, bool) [clone .constprop.212]
PUBLIC 1adc0 0 cv::Mat::create(int, int, int) [clone .constprop.217]
PUBLIC 1ae28 0 cv::Mat::Mat(int, int, int, void*, unsigned long) [clone .constprop.218]
PUBLIC 1af00 0 std::vector<cv::Vec<double, 3>, std::allocator<cv::Vec<double, 3> > >::vector(unsigned long, std::allocator<cv::Vec<double, 3> > const&) [clone .constprop.201]
PUBLIC 1af90 0 double& cv::Mat::at<double>(int) [clone .constprop.206]
PUBLIC 1af98 0 cv::_InputArray::getMat(int) const
PUBLIC 1b000 0 cv::_InputArray::getMat(int) const [clone .constprop.219]
PUBLIC 1b060 0 cv::Mat::Mat<double, 1, 4>(cv::Matx<double, 1, 4> const&, bool) [clone .constprop.200]
PUBLIC 1b110 0 cv::Mat::Mat<double, 3, 3>(cv::Matx<double, 3, 3> const&, bool) [clone .constprop.207]
PUBLIC 1b1c0 0 cv::Mat::Mat<double, 3>(cv::Vec<double, 3> const&, bool) [clone .constprop.211]
PUBLIC 1b270 0 cv::omnidir::internal::compose_motion(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC 1bc00 0 cv::omnidir::internal::decodeParameters(cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, double&)
PUBLIC 1c850 0 cv::omnidir::internal::computeMeanReproErr(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 1cf90 0 cv::omnidir::internal::subMatrix(cv::Mat const&, cv::Mat&, std::vector<int, std::allocator<int> > const&, std::vector<int, std::allocator<int> > const&)
PUBLIC 1d3f8 0 cv::omnidir::internal::flags2idx(int, std::vector<int, std::allocator<int> >&, int)
PUBLIC 1d680 0 cv::omnidir::internal::flags2idxStereo(int, std::vector<int, std::allocator<int> >&, int)
PUBLIC 1d960 0 cv::omnidir::internal::fillFixed(cv::Mat&, int, int)
PUBLIC 1ddf0 0 cv::omnidir::internal::fillFixedStereo(cv::Mat&, int, int)
PUBLIC 1e280 0 cv::omnidir::stereoRectify(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 1eca0 0 _ZNK2cv3MatcvNS_4MatxIT_XT0_EXT1_EEEIfLi3ELi3EEEv
PUBLIC 1ee50 0 _ZNK2cv3MatcvNS_4MatxIT_XT0_EXT1_EEEIdLi3ELi3EEEv
PUBLIC 1efe0 0 cv::omnidir::projectPoints(cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, double, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 20058 0 cv::omnidir::projectPoints(cv::_InputArray const&, cv::_OutputArray const&, cv::Affine3<double> const&, cv::_InputArray const&, double, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 20560 0 cv::omnidir::internal::computeJacobian(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Mat&, int, double)
PUBLIC 21c50 0 cv::omnidir::internal::estimateUncertainties(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Vec<double, 2>&, double&, int)
PUBLIC 22c90 0 cv::omnidir::internal::computeJacobianStereo(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Mat&, int, double)
PUBLIC 24c18 0 cv::omnidir::undistortPoints(cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 25588 0 cv::Matx<double, 3, 3>::inv(int, bool*) const
PUBLIC 256a8 0 cv::omnidir::initUndistortRectifyMap(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int> const&, int, cv::_OutputArray const&, cv::_OutputArray const&, int)
PUBLIC 26880 0 cv::omnidir::undistortImage(cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, int, cv::_InputArray const&, cv::Size_<int> const&, cv::_InputArray const&)
PUBLIC 26a40 0 double& cv::Mat::at<double>(int)
PUBLIC 26ac0 0 cv::omnidir::internal::findMedian(cv::Mat const&)
PUBLIC 26d70 0 cv::omnidir::internal::findMedian3(cv::_InputArray const&)
PUBLIC 27038 0 int& cv::Mat::at<int>(int)
PUBLIC 270b0 0 _ZNK2cv3MatcvNS_3VecIT_XT0_EEEIdLi4EEEv
PUBLIC 27230 0 cv::omnidir::internal::encodeParameters(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, double, cv::_OutputArray const&)
PUBLIC 27c00 0 cv::omnidir::internal::encodeParametersStereo(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, double, double, cv::_OutputArray const&)
PUBLIC 28c00 0 _ZNK2cv3MatcvNS_3VecIT_XT0_EEEIdLi3EEEv
PUBLIC 28d68 0 cv::omnidir::internal::getInterset(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 29748 0 void std::vector<cv::Vec<double, 3>, std::allocator<cv::Vec<double, 3> > >::_M_emplace_back_aux<cv::Vec<double, 3> const&>(cv::Vec<double, 3> const&)
PUBLIC 29888 0 void std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_emplace_back_aux<cv::Vec<float, 3> const&>(cv::Vec<float, 3> const&)
PUBLIC 299c8 0 void std::vector<cv::Vec<float, 6>, std::allocator<cv::Vec<float, 6> > >::_M_emplace_back_aux<cv::Vec<float, 6> const&>(cv::Vec<float, 6> const&)
PUBLIC 29b20 0 cv::omnidir::stereoReconstruct(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, int, int, int, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Size_<int> const&, cv::_InputArray const&, cv::_OutputArray const&, int)
PUBLIC 2b310 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC 2b660 0 void std::vector<cv::Vec<double, 3>, std::allocator<cv::Vec<double, 3> > >::_M_emplace_back_aux<cv::Vec<double, 3> >(cv::Vec<double, 3>&&)
PUBLIC 2b7a0 0 cv::omnidir::internal::decodeParametersStereo(cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, double&, double&)
PUBLIC 2c220 0 cv::omnidir::internal::estimateUncertaintiesStereo(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Mat&, cv::Vec<double, 2>&, double&, int)
PUBLIC 2d818 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, double, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, long, double, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 2d908 0 void std::__introselect<__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, __gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, __gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 2dbc0 0 cv::omnidir::internal::initializeCalibration(cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int>, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, double&, cv::_OutputArray const&)
PUBLIC 30b00 0 cv::omnidir::calibrate(cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria, cv::_OutputArray const&)
PUBLIC 32670 0 cv::omnidir::internal::initializeStereoCalibration(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int> const&, cv::Size_<int> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, double&, double&, int, cv::_OutputArray const&)
PUBLIC 33a10 0 cv::omnidir::stereoCalibrate(cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::Size_<int> const&, cv::Size_<int> const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria, cv::_OutputArray const&)
PUBLIC 35868 0 cv::randpattern::RandomPatternCornerFinder::getObjectPoints()
PUBLIC 35870 0 cv::randpattern::RandomPatternCornerFinder::getImagePoints()
PUBLIC 35880 0 cv::randpattern::RandomPatternCornerFinder::loadPattern(cv::Mat const&)
PUBLIC 35bd0 0 cv::randpattern::RandomPatternGenerator::RandomPatternGenerator(int, int)
PUBLIC 35c10 0 cv::randpattern::RandomPatternGenerator::generatePattern()
PUBLIC 366d8 0 cv::randpattern::RandomPatternGenerator::getPattern()
PUBLIC 36790 0 cv::randpattern::RandomPatternCornerFinder::loadPattern(cv::Mat const&, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&, cv::Mat const&)
PUBLIC 36dc0 0 std::vector<cv::DMatch, std::allocator<cv::DMatch> >::operator=(std::vector<cv::DMatch, std::allocator<cv::DMatch> > const&)
PUBLIC 36f08 0 std::vector<std::vector<cv::DMatch, std::allocator<cv::DMatch> >, std::allocator<std::vector<cv::DMatch, std::allocator<cv::DMatch> > > >::~vector()
PUBLIC 36f70 0 cv::randpattern::RandomPatternCornerFinder::RandomPatternCornerFinder(float, float, int, int, int, int, cv::Ptr<cv::Feature2D>, cv::Ptr<cv::Feature2D>, cv::Ptr<cv::DescriptorMatcher>)
PUBLIC 37310 0 void std::vector<cv::Vec<double, 2>, std::allocator<cv::Vec<double, 2> > >::_M_emplace_back_aux<cv::Vec<double, 2> const&>(cv::Vec<double, 2> const&)
PUBLIC 37410 0 cv::randpattern::RandomPatternCornerFinder::getFilteredLocation(cv::Mat&, cv::Mat&, cv::Mat)
PUBLIC 37bb0 0 cv::randpattern::RandomPatternCornerFinder::crossCheckMatching(cv::Ptr<cv::DescriptorMatcher>&, cv::Mat const&, cv::Mat const&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >&, int)
PUBLIC 37e70 0 cv::randpattern::RandomPatternCornerFinder::drawCorrespondence(cv::Mat const&, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >, cv::Mat const&, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> >, std::vector<cv::DMatch, std::allocator<cv::DMatch> >, cv::Mat const&, cv::Mat const&, int)
PUBLIC 385c0 0 void std::vector<cv::Vec<double, 2>, std::allocator<cv::Vec<double, 2> > >::_M_emplace_back_aux<cv::Vec<double, 2> >(cv::Vec<double, 2>&&)
PUBLIC 386c0 0 cv::randpattern::RandomPatternCornerFinder::keyPoints2MatchedLocation(std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&, std::vector<cv::KeyPoint, std::allocator<cv::KeyPoint> > const&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >, cv::Mat&, cv::Mat&)
PUBLIC 38bc0 0 cv::randpattern::RandomPatternCornerFinder::computeObjectImagePointsForSingle(cv::Mat)
PUBLIC 3a9c8 0 cv::randpattern::RandomPatternCornerFinder::computeObjectImagePoints(std::vector<cv::Mat, std::allocator<cv::Mat> >)
PUBLIC 3b040 0 cv::randpattern::RandomPatternCornerFinder::getObjectImagePoints(cv::Mat const&, cv::Mat const&)
PUBLIC 3b6c0 0 _fini
STACK CFI INIT 99a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 95e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 95f0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 9670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9674 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 99c8 34c .cfa: sp 0 + .ra: x30
STACK CFI 99cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 99dc .ra: .cfa -16 + ^
STACK CFI 9b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 9b90 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 9d18 18 .cfa: sp 0 + .ra: x30
STACK CFI 9d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9d2c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9d30 90 .cfa: sp 0 + .ra: x30
STACK CFI 9d34 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9da8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 9db0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9dbc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9dc0 94 .cfa: sp 0 + .ra: x30
STACK CFI 9dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9e50 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9e70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e78 138 .cfa: sp 0 + .ra: x30
STACK CFI 9e8c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9e90 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9f08 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 9fb0 138 .cfa: sp 0 + .ra: x30
STACK CFI 9fc4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9fc8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI a038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI a040 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT a0e8 138 .cfa: sp 0 + .ra: x30
STACK CFI a0fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a100 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI a170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI a178 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT a220 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT a270 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT a2c0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT a310 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT a360 170 .cfa: sp 0 + .ra: x30
STACK CFI a364 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI a380 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI a3c8 .ra: .cfa -176 + ^
STACK CFI a4ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI a4b0 .cfa: sp 208 + .ra: .cfa -176 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT a4f0 194 .cfa: sp 0 + .ra: x30
STACK CFI a4f8 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a51c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a544 .ra: .cfa -120 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x23: .cfa -128 + ^
STACK CFI a654 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI a658 .cfa: sp 160 + .ra: .cfa -120 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^
STACK CFI INIT a688 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a690 50 .cfa: sp 0 + .ra: x30
STACK CFI a694 .cfa: sp 48 +
STACK CFI a6a8 .ra: .cfa -32 + ^
STACK CFI a6dc .cfa: sp 0 + .ra: .ra
STACK CFI INIT a6e0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT a708 290 .cfa: sp 0 + .ra: x30
STACK CFI a70c .cfa: sp 208 +
STACK CFI a714 v8: .cfa -112 + ^
STACK CFI a71c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI a72c x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a73c .ra: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x27: .cfa -128 + ^
STACK CFI a878 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI a880 .cfa: sp 208 + .ra: .cfa -120 + ^ v8: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^
STACK CFI INIT a9b0 4c8 .cfa: sp 0 + .ra: x30
STACK CFI a9b4 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI a9c4 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI a9d4 x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI a9ec .ra: .cfa -264 + ^ v10: .cfa -240 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x27: .cfa -272 + ^
STACK CFI ad84 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI ad88 .cfa: sp 336 + .ra: .cfa -264 + ^ v10: .cfa -240 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^
STACK CFI INIT aea0 240 .cfa: sp 0 + .ra: x30
STACK CFI aea4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI aeac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI aeb4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI af78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI af80 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT b0e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b0f0 38 .cfa: sp 0 + .ra: x30
STACK CFI b0f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b100 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI b124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT b128 210 .cfa: sp 0 + .ra: x30
STACK CFI b12c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b134 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b13c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI b1ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI b1f0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT b338 b4 .cfa: sp 0 + .ra: x30
STACK CFI b340 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b34c .ra: .cfa -16 + ^
STACK CFI b374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI b378 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI b3c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT b3f0 100 .cfa: sp 0 + .ra: x30
STACK CFI b3f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b3fc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI b404 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b4b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI b4c0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT b4f0 150 .cfa: sp 0 + .ra: x30
STACK CFI b4f4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b50c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b514 .ra: .cfa -16 + ^
STACK CFI b5c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b5c8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT b640 88 .cfa: sp 0 + .ra: x30
STACK CFI b644 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b650 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b664 .ra: .cfa -48 + ^
STACK CFI b6a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI b6ac .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT b6c8 f8 .cfa: sp 0 + .ra: x30
STACK CFI b6cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b6d4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI b6dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI b790 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT b7c0 140 .cfa: sp 0 + .ra: x30
STACK CFI b7c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b7d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b7d8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI b8c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI b8c8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT b900 d50 .cfa: sp 0 + .ra: x30
STACK CFI b904 .cfa: sp 720 +
STACK CFI b920 .ra: .cfa -624 + ^ v8: .cfa -616 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI b94c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b950 .cfa: sp 720 + .ra: .cfa -624 + ^ v8: .cfa -616 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI INIT c690 da8 .cfa: sp 0 + .ra: x30
STACK CFI c694 .cfa: sp 1120 +
STACK CFI c698 v8: .cfa -1008 + ^ v9: .cfa -1000 + ^
STACK CFI c6ac x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI c6cc .ra: .cfa -1024 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI c964 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c968 .cfa: sp 1120 + .ra: .cfa -1024 + ^ v8: .cfa -1008 + ^ v9: .cfa -1000 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI INIT d470 188 .cfa: sp 0 + .ra: x30
STACK CFI d474 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI d480 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI d4b0 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI d4c4 .ra: .cfa -128 + ^
STACK CFI d570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d578 .cfa: sp 208 + .ra: .cfa -128 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT d600 1e8 .cfa: sp 0 + .ra: x30
STACK CFI d604 .cfa: sp 224 +
STACK CFI d614 v8: .cfa -120 + ^
STACK CFI d61c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI d63c x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI d660 .ra: .cfa -128 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI d7b0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d7b8 .cfa: sp 224 + .ra: .cfa -128 + ^ v8: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT d800 140 .cfa: sp 0 + .ra: x30
STACK CFI d804 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d810 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d818 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI d904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI d908 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT d940 f4 .cfa: sp 0 + .ra: x30
STACK CFI d944 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d948 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d958 .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x23: .cfa -48 + ^
STACK CFI da1c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI da20 .cfa: sp 80 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT da40 9a0 .cfa: sp 0 + .ra: x30
STACK CFI da44 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI da54 v8: .cfa -184 + ^
STACK CFI da64 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI da88 .ra: .cfa -192 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI dd04 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI dd08 .cfa: sp 256 + .ra: .cfa -192 + ^ v8: .cfa -184 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI dd28 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI dd30 .cfa: sp 256 + .ra: .cfa -192 + ^ v8: .cfa -184 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI INIT e410 22c .cfa: sp 0 + .ra: x30
STACK CFI e414 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI e41c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI e424 .ra: .cfa -104 + ^ x23: .cfa -112 + ^
STACK CFI e51c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI e520 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^
STACK CFI INIT e640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e648 c4 .cfa: sp 0 + .ra: x30
STACK CFI e64c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e658 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI e6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e6a8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI e6e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e6e8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI e708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 9680 44 .cfa: sp 0 + .ra: x30
STACK CFI 9684 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9694 .ra: .cfa -16 + ^
STACK CFI 96c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e710 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT e790 120 .cfa: sp 0 + .ra: x30
STACK CFI e794 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e7a0 .ra: .cfa -16 + ^
STACK CFI e86c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e870 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT e8b0 60 .cfa: sp 0 + .ra: x30
STACK CFI e8d0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI e8e4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT e910 120 .cfa: sp 0 + .ra: x30
STACK CFI e914 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e920 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI ea0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI ea10 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT ea40 80 .cfa: sp 0 + .ra: x30
STACK CFI ea50 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ea5c .ra: .cfa -16 + ^
STACK CFI eaa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI eaac .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT ead0 24 .cfa: sp 0 + .ra: x30
STACK CFI ead4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI eaf0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT eb00 b8c .cfa: sp 0 + .ra: x30
STACK CFI eb08 .cfa: sp 2352 +
STACK CFI eb3c x21: .cfa -2336 + ^ x22: .cfa -2328 + ^ x25: .cfa -2304 + ^ x26: .cfa -2296 + ^
STACK CFI eb4c x19: .cfa -2352 + ^ x20: .cfa -2344 + ^ x23: .cfa -2320 + ^ x24: .cfa -2312 + ^
STACK CFI eb6c .ra: .cfa -2272 + ^ x27: .cfa -2288 + ^ x28: .cfa -2280 + ^
STACK CFI f49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f4a0 .cfa: sp 2352 + .ra: .cfa -2272 + ^ x19: .cfa -2352 + ^ x20: .cfa -2344 + ^ x21: .cfa -2336 + ^ x22: .cfa -2328 + ^ x23: .cfa -2320 + ^ x24: .cfa -2312 + ^ x25: .cfa -2304 + ^ x26: .cfa -2296 + ^ x27: .cfa -2288 + ^ x28: .cfa -2280 + ^
STACK CFI INIT f6a0 ce0 .cfa: sp 0 + .ra: x30
STACK CFI f6a8 .cfa: sp 3056 +
STACK CFI f70c .ra: .cfa -2912 + ^ v8: .cfa -2896 + ^ v9: .cfa -2888 + ^ x19: .cfa -2992 + ^ x20: .cfa -2984 + ^ x21: .cfa -2976 + ^ x22: .cfa -2968 + ^ x23: .cfa -2960 + ^ x24: .cfa -2952 + ^ x25: .cfa -2944 + ^ x26: .cfa -2936 + ^ x27: .cfa -2928 + ^ x28: .cfa -2920 + ^
STACK CFI f75c v10: .cfa -2904 + ^
STACK CFI ffa8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ffb0 .cfa: sp 3056 + .ra: .cfa -2912 + ^ v10: .cfa -2904 + ^ v8: .cfa -2896 + ^ v9: .cfa -2888 + ^ x19: .cfa -2992 + ^ x20: .cfa -2984 + ^ x21: .cfa -2976 + ^ x22: .cfa -2968 + ^ x23: .cfa -2960 + ^ x24: .cfa -2952 + ^ x25: .cfa -2944 + ^ x26: .cfa -2936 + ^ x27: .cfa -2928 + ^ x28: .cfa -2920 + ^
STACK CFI INIT 103b0 cd0 .cfa: sp 0 + .ra: x30
STACK CFI 103b4 .cfa: sp 3104 +
STACK CFI 103b8 x21: .cfa -3024 + ^ x22: .cfa -3016 + ^
STACK CFI 103d4 .ra: .cfa -2960 + ^ v8: .cfa -2944 + ^ v9: .cfa -2936 + ^ x19: .cfa -3040 + ^ x20: .cfa -3032 + ^ x23: .cfa -3008 + ^ x24: .cfa -3000 + ^ x25: .cfa -2992 + ^ x26: .cfa -2984 + ^ x27: .cfa -2976 + ^ x28: .cfa -2968 + ^
STACK CFI 10e24 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10e28 .cfa: sp 3104 + .ra: .cfa -2960 + ^ v8: .cfa -2944 + ^ v9: .cfa -2936 + ^ x19: .cfa -3040 + ^ x20: .cfa -3032 + ^ x21: .cfa -3024 + ^ x22: .cfa -3016 + ^ x23: .cfa -3008 + ^ x24: .cfa -3000 + ^ x25: .cfa -2992 + ^ x26: .cfa -2984 + ^ x27: .cfa -2976 + ^ x28: .cfa -2968 + ^
STACK CFI INIT 110c0 6cc .cfa: sp 0 + .ra: x30
STACK CFI 110c4 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 110c8 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 110d8 x21: .cfa -416 + ^ x22: .cfa -408 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 110f0 .ra: .cfa -352 + ^ v8: .cfa -344 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 11654 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11658 .cfa: sp 432 + .ra: .cfa -352 + ^ v8: .cfa -344 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 117b8 bc .cfa: sp 0 + .ra: x30
STACK CFI 117bc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 117c0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11868 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11878 bc .cfa: sp 0 + .ra: x30
STACK CFI 1187c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11880 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11928 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11938 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1193c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11944 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 11a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 11a18 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 11a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 11a28 bc .cfa: sp 0 + .ra: x30
STACK CFI 11a2c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11a30 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11ad8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11ae8 5c .cfa: sp 0 + .ra: x30
STACK CFI 11aec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11af0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11b38 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11b48 5c .cfa: sp 0 + .ra: x30
STACK CFI 11b4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11b50 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11b98 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11ba8 48 .cfa: sp 0 + .ra: x30
STACK CFI 11bb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 11bec .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11bf0 50 .cfa: sp 0 + .ra: x30
STACK CFI 11bf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 11c3c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11c40 5c .cfa: sp 0 + .ra: x30
STACK CFI 11c44 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 11c98 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11ca0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 11ca4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11cb4 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 11d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11d68 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 11d98 1bc .cfa: sp 0 + .ra: x30
STACK CFI 11dfc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11e00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11e10 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 11f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 11f30 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 11f58 124 .cfa: sp 0 + .ra: x30
STACK CFI 11f5c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11f70 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12064 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 12080 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 120d4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 120d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 120e8 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 12210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 12218 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 12248 1bc .cfa: sp 0 + .ra: x30
STACK CFI 1224c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12254 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 12260 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1237c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12380 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 12408 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1240c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12414 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12420 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 124a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 124a8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 124f0 3f4 .cfa: sp 0 + .ra: x30
STACK CFI 124f4 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 12500 .ra: .cfa -160 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 12804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12808 .cfa: sp 208 + .ra: .cfa -160 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI INIT 12900 258 .cfa: sp 0 + .ra: x30
STACK CFI 12904 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12914 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 12920 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12aa8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 12b58 54 .cfa: sp 0 + .ra: x30
STACK CFI 12b5c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12b60 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 12b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 12ba0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 12ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 12bb0 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 12bb4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12bcc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12bd4 .ra: .cfa -16 + ^
STACK CFI 12dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12dd0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 12eb0 24c .cfa: sp 0 + .ra: x30
STACK CFI 12eb4 .cfa: sp 608 +
STACK CFI 12eb8 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 12ec8 .ra: .cfa -568 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^
STACK CFI 1309c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 130a0 .cfa: sp 608 + .ra: .cfa -568 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^
STACK CFI INIT 13110 30c .cfa: sp 0 + .ra: x30
STACK CFI 13114 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1312c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13134 .ra: .cfa -16 + ^
STACK CFI 13348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 13350 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 13430 670 .cfa: sp 0 + .ra: x30
STACK CFI 13438 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13444 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1344c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1345c .ra: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 135c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 135d0 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 139a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 139b0 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 13aa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13ab8 10c .cfa: sp 0 + .ra: x30
STACK CFI 13abc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13ac4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13ad4 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 13b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 13b74 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 13bc8 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 13bcc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13bd4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13be0 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 13c48 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 13d70 9ec .cfa: sp 0 + .ra: x30
STACK CFI 13d74 .cfa: sp 544 +
STACK CFI 13d8c .ra: .cfa -464 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 14260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14264 .cfa: sp 544 + .ra: .cfa -464 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT 14770 66c .cfa: sp 0 + .ra: x30
STACK CFI 14774 .cfa: sp 1168 +
STACK CFI 1478c x19: .cfa -1168 + ^ x20: .cfa -1160 + ^
STACK CFI 147b4 .ra: .cfa -1088 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 14d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14d24 .cfa: sp 1168 + .ra: .cfa -1088 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI INIT 14de0 140 .cfa: sp 0 + .ra: x30
STACK CFI 14de4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14df0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14df8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 14ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 14ee8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 14f20 66c .cfa: sp 0 + .ra: x30
STACK CFI 14f24 .cfa: sp 368 + x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 14f28 x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 14f40 .ra: .cfa -288 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 15480 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15488 .cfa: sp 368 + .ra: .cfa -288 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI INIT 155a0 121c .cfa: sp 0 + .ra: x30
STACK CFI 155a4 .cfa: sp 2368 +
STACK CFI 155a8 x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 155b0 x21: .cfa -2352 + ^ x22: .cfa -2344 + ^
STACK CFI 155dc .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^
STACK CFI 15fe8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15fec .cfa: sp 2368 + .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI INIT 16810 c18 .cfa: sp 0 + .ra: x30
STACK CFI 16814 .cfa: sp 1568 +
STACK CFI 16824 x21: .cfa -1552 + ^ x22: .cfa -1544 + ^
STACK CFI 16850 .ra: .cfa -1488 + ^ v8: .cfa -1472 + ^ v9: .cfa -1464 + ^ x19: .cfa -1568 + ^ x20: .cfa -1560 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI 1685c v10: .cfa -1480 + ^
STACK CFI 17248 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1724c .cfa: sp 1568 + .ra: .cfa -1488 + ^ v10: .cfa -1480 + ^ v8: .cfa -1472 + ^ v9: .cfa -1464 + ^ x19: .cfa -1568 + ^ x20: .cfa -1560 + ^ x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI INIT 17490 138 .cfa: sp 0 + .ra: x30
STACK CFI 17494 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1749c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 174a4 .ra: .cfa -16 + ^
STACK CFI 17570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 17578 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 175c8 214 .cfa: sp 0 + .ra: x30
STACK CFI 175d0 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 175e4 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 17700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 17708 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 17764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 17770 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 177e0 628 .cfa: sp 0 + .ra: x30
STACK CFI 177e8 .cfa: sp 656 +
STACK CFI 177ec x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 177f4 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 17814 v8: .cfa -560 + ^ v9: .cfa -552 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 17820 .ra: .cfa -576 + ^
STACK CFI 17ce4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17ce8 .cfa: sp 656 + .ra: .cfa -576 + ^ v8: .cfa -560 + ^ v9: .cfa -552 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI INIT 9808 30 .cfa: sp 0 + .ra: x30
STACK CFI 980c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9828 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 17e20 364 .cfa: sp 0 + .ra: x30
STACK CFI 17e24 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 17e3c x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 17e4c .ra: .cfa -272 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 180ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 180f0 .cfa: sp 352 + .ra: .cfa -272 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 18190 1ba0 .cfa: sp 0 + .ra: x30
STACK CFI 18194 .cfa: sp 2096 +
STACK CFI 181a0 x19: .cfa -2064 + ^ x20: .cfa -2056 + ^
STACK CFI 181a8 x21: .cfa -2048 + ^ x22: .cfa -2040 + ^
STACK CFI 181c8 .ra: .cfa -1984 + ^ v10: .cfa -1952 + ^ v11: .cfa -1944 + ^ v12: .cfa -1976 + ^ v8: .cfa -1968 + ^ v9: .cfa -1960 + ^ x23: .cfa -2032 + ^ x24: .cfa -2024 + ^ x25: .cfa -2016 + ^ x26: .cfa -2008 + ^ x27: .cfa -2000 + ^ x28: .cfa -1992 + ^
STACK CFI 194a8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 194b0 .cfa: sp 2096 + .ra: .cfa -1984 + ^ v10: .cfa -1952 + ^ v11: .cfa -1944 + ^ v12: .cfa -1976 + ^ v8: .cfa -1968 + ^ v9: .cfa -1960 + ^ x19: .cfa -2064 + ^ x20: .cfa -2056 + ^ x21: .cfa -2048 + ^ x22: .cfa -2040 + ^ x23: .cfa -2032 + ^ x24: .cfa -2024 + ^ x25: .cfa -2016 + ^ x26: .cfa -2008 + ^ x27: .cfa -2000 + ^ x28: .cfa -1992 + ^
STACK CFI INIT 19d78 20 .cfa: sp 0 + .ra: x30
STACK CFI 19d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 19d94 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 19d98 fa4 .cfa: sp 0 + .ra: x30
STACK CFI 19d9c .cfa: sp 832 +
STACK CFI 19dac x19: .cfa -832 + ^ x20: .cfa -824 + ^
STACK CFI 19db4 x23: .cfa -800 + ^ x24: .cfa -792 + ^
STACK CFI 19ddc .ra: .cfa -752 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 1a8ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a8f0 .cfa: sp 832 + .ra: .cfa -752 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI INIT 96c4 a0 .cfa: sp 0 + .ra: x30
STACK CFI 96c8 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 96d4 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 9754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9758 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 1ad40 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1adc0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1ade0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1adf8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1ae28 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1ae30 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ae3c .ra: .cfa -48 + ^
STACK CFI 1aea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1aea8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 1af00 8c .cfa: sp 0 + .ra: x30
STACK CFI 1af04 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1af0c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1af6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1af70 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1af84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1af88 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 1af90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af98 64 .cfa: sp 0 + .ra: x30
STACK CFI 1af9c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1afa8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1afd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1afe0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1aff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1b000 5c .cfa: sp 0 + .ra: x30
STACK CFI 1b004 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b010 .ra: .cfa -16 + ^
STACK CFI 1b038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1b040 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1b060 94 .cfa: sp 0 + .ra: x30
STACK CFI 1b064 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1b078 .ra: .cfa -144 + ^
STACK CFI 1b0dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1b0e0 .cfa: sp 160 + .ra: .cfa -144 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI INIT 1b110 94 .cfa: sp 0 + .ra: x30
STACK CFI 1b114 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1b128 .ra: .cfa -144 + ^
STACK CFI 1b18c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1b190 .cfa: sp 160 + .ra: .cfa -144 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI INIT 1b1c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 1b1c4 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1b1d8 .ra: .cfa -144 + ^
STACK CFI 1b23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1b240 .cfa: sp 160 + .ra: .cfa -144 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI INIT 1b270 980 .cfa: sp 0 + .ra: x30
STACK CFI 1b274 .cfa: sp 2368 +
STACK CFI 1b27c x19: .cfa -2368 + ^ x20: .cfa -2360 + ^
STACK CFI 1b28c x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^
STACK CFI 1b2a4 x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 1b2ac .ra: .cfa -2288 + ^
STACK CFI 1ba54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ba58 .cfa: sp 2368 + .ra: .cfa -2288 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI INIT 1bc00 c24 .cfa: sp 0 + .ra: x30
STACK CFI 1bc04 .cfa: sp 960 +
STACK CFI 1bc08 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 1bc18 x19: .cfa -960 + ^ x20: .cfa -952 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 1bc30 .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 1c69c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1c6a0 .cfa: sp 960 + .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI INIT 1c850 72c .cfa: sp 0 + .ra: x30
STACK CFI 1c854 .cfa: sp 992 +
STACK CFI 1c858 x27: .cfa -928 + ^ x28: .cfa -920 + ^
STACK CFI 1c878 .ra: .cfa -912 + ^ v8: .cfa -896 + ^ v9: .cfa -888 + ^ x19: .cfa -992 + ^ x20: .cfa -984 + ^ x21: .cfa -976 + ^ x22: .cfa -968 + ^ x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^
STACK CFI 1cc18 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1cc1c .cfa: sp 992 + .ra: .cfa -912 + ^ v8: .cfa -896 + ^ v9: .cfa -888 + ^ x19: .cfa -992 + ^ x20: .cfa -984 + ^ x21: .cfa -976 + ^ x22: .cfa -968 + ^ x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^ x27: .cfa -928 + ^ x28: .cfa -920 + ^
STACK CFI INIT 1cf90 444 .cfa: sp 0 + .ra: x30
STACK CFI 1cf94 .cfa: sp 624 +
STACK CFI 1cfb0 .ra: .cfa -544 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 1d2f8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d2fc .cfa: sp 624 + .ra: .cfa -544 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 1d3f8 288 .cfa: sp 0 + .ra: x30
STACK CFI 1d3fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d408 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d410 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1d634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1d638 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1d680 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 1d684 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d694 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d69c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1d910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1d918 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1d960 478 .cfa: sp 0 + .ra: x30
STACK CFI 1d968 .cfa: sp 560 +
STACK CFI 1d970 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1d980 x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 1d98c x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 1d9c8 .ra: .cfa -496 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI 1dd1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1dd20 .cfa: sp 560 + .ra: .cfa -496 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI INIT 1ddf0 480 .cfa: sp 0 + .ra: x30
STACK CFI 1ddf8 .cfa: sp 560 +
STACK CFI 1de00 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1de10 x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 1de1c x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 1de58 .ra: .cfa -496 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI 1e1b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1e1b8 .cfa: sp 560 + .ra: .cfa -496 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI INIT 1e280 9e0 .cfa: sp 0 + .ra: x30
STACK CFI 1e284 .cfa: sp 2032 +
STACK CFI 1e288 x19: .cfa -2032 + ^ x20: .cfa -2024 + ^
STACK CFI 1e298 x21: .cfa -2016 + ^ x22: .cfa -2008 + ^ x23: .cfa -2000 + ^ x24: .cfa -1992 + ^
STACK CFI 1e2a8 x25: .cfa -1984 + ^ x26: .cfa -1976 + ^ x27: .cfa -1968 + ^ x28: .cfa -1960 + ^
STACK CFI 1e2bc .ra: .cfa -1952 + ^ v8: .cfa -1944 + ^
STACK CFI 1e9b8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1e9c0 .cfa: sp 2032 + .ra: .cfa -1952 + ^ v8: .cfa -1944 + ^ x19: .cfa -2032 + ^ x20: .cfa -2024 + ^ x21: .cfa -2016 + ^ x22: .cfa -2008 + ^ x23: .cfa -2000 + ^ x24: .cfa -1992 + ^ x25: .cfa -1984 + ^ x26: .cfa -1976 + ^ x27: .cfa -1968 + ^ x28: .cfa -1960 + ^
STACK CFI INIT 1eca0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 1eca4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 1ecac .ra: .cfa -168 + ^ x21: .cfa -176 + ^
STACK CFI 1ede4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1ede8 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI 1ee08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1ee0c .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI INIT 1ee50 18c .cfa: sp 0 + .ra: x30
STACK CFI 1ee54 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 1ee58 .ra: .cfa -200 + ^ x21: .cfa -208 + ^
STACK CFI 1ef68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1ef70 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^
STACK CFI 1efa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1efa4 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^
STACK CFI INIT 1efe0 1064 .cfa: sp 0 + .ra: x30
STACK CFI 1efe4 .cfa: sp 1296 +
STACK CFI 1efe8 x19: .cfa -1296 + ^ x20: .cfa -1288 + ^
STACK CFI 1eff8 x21: .cfa -1280 + ^ x22: .cfa -1272 + ^ x23: .cfa -1264 + ^ x24: .cfa -1256 + ^
STACK CFI 1f010 v8: .cfa -1200 + ^ v9: .cfa -1192 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^ x27: .cfa -1232 + ^ x28: .cfa -1224 + ^
STACK CFI 1f02c .ra: .cfa -1216 + ^ v10: .cfa -1184 + ^ v11: .cfa -1176 + ^ v12: .cfa -1168 + ^ v13: .cfa -1160 + ^ v14: .cfa -1152 + ^ v15: .cfa -1144 + ^
STACK CFI 1fe4c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1fe50 .cfa: sp 1296 + .ra: .cfa -1216 + ^ v10: .cfa -1184 + ^ v11: .cfa -1176 + ^ v12: .cfa -1168 + ^ v13: .cfa -1160 + ^ v14: .cfa -1152 + ^ v15: .cfa -1144 + ^ v8: .cfa -1200 + ^ v9: .cfa -1192 + ^ x19: .cfa -1296 + ^ x20: .cfa -1288 + ^ x21: .cfa -1280 + ^ x22: .cfa -1272 + ^ x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^ x27: .cfa -1232 + ^ x28: .cfa -1224 + ^
STACK CFI INIT 20058 4f0 .cfa: sp 0 + .ra: x30
STACK CFI 2005c .cfa: sp 576 +
STACK CFI 20064 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 2007c x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^
STACK CFI 2008c .ra: .cfa -496 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 20094 v8: .cfa -480 + ^ v9: .cfa -472 + ^
STACK CFI 200b8 v10: .cfa -464 + ^ v11: .cfa -456 + ^ v12: .cfa -448 + ^ v13: .cfa -440 + ^ v14: .cfa -432 + ^ v15: .cfa -424 + ^
STACK CFI 20398 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 203a0 .cfa: sp 576 + .ra: .cfa -496 + ^ v10: .cfa -464 + ^ v11: .cfa -456 + ^ v12: .cfa -448 + ^ v13: .cfa -440 + ^ v14: .cfa -432 + ^ v15: .cfa -424 + ^ v8: .cfa -480 + ^ v9: .cfa -472 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI INIT 20560 16bc .cfa: sp 0 + .ra: x30
STACK CFI 20564 .cfa: sp 3136 +
STACK CFI 20568 v10: .cfa -3048 + ^
STACK CFI 20588 .ra: .cfa -3056 + ^ v8: .cfa -3040 + ^ v9: .cfa -3032 + ^ x19: .cfa -3136 + ^ x20: .cfa -3128 + ^ x21: .cfa -3120 + ^ x22: .cfa -3112 + ^ x23: .cfa -3104 + ^ x24: .cfa -3096 + ^ x25: .cfa -3088 + ^ x26: .cfa -3080 + ^ x27: .cfa -3072 + ^ x28: .cfa -3064 + ^
STACK CFI 2183c .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21840 .cfa: sp 3136 + .ra: .cfa -3056 + ^ v10: .cfa -3048 + ^ v8: .cfa -3040 + ^ v9: .cfa -3032 + ^ x19: .cfa -3136 + ^ x20: .cfa -3128 + ^ x21: .cfa -3120 + ^ x22: .cfa -3112 + ^ x23: .cfa -3104 + ^ x24: .cfa -3096 + ^ x25: .cfa -3088 + ^ x26: .cfa -3080 + ^ x27: .cfa -3072 + ^ x28: .cfa -3064 + ^
STACK CFI INIT 21c50 101c .cfa: sp 0 + .ra: x30
STACK CFI 21c54 .cfa: sp 1520 +
STACK CFI 21c58 x27: .cfa -1456 + ^ x28: .cfa -1448 + ^
STACK CFI 21c74 .ra: .cfa -1440 + ^ v8: .cfa -1424 + ^ v9: .cfa -1416 + ^ x19: .cfa -1520 + ^ x20: .cfa -1512 + ^ x21: .cfa -1504 + ^ x22: .cfa -1496 + ^ x23: .cfa -1488 + ^ x24: .cfa -1480 + ^ x25: .cfa -1472 + ^ x26: .cfa -1464 + ^
STACK CFI 22a4c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 22a50 .cfa: sp 1520 + .ra: .cfa -1440 + ^ v8: .cfa -1424 + ^ v9: .cfa -1416 + ^ x19: .cfa -1520 + ^ x20: .cfa -1512 + ^ x21: .cfa -1504 + ^ x22: .cfa -1496 + ^ x23: .cfa -1488 + ^ x24: .cfa -1480 + ^ x25: .cfa -1472 + ^ x26: .cfa -1464 + ^ x27: .cfa -1456 + ^ x28: .cfa -1448 + ^
STACK CFI INIT 22c90 1f44 .cfa: sp 0 + .ra: x30
STACK CFI 22c98 .cfa: sp 5072 +
STACK CFI 22c9c v12: .cfa -4896 + ^ v13: .cfa -4888 + ^
STACK CFI 22cc4 .ra: .cfa -4944 + ^ v10: .cfa -4912 + ^ v11: .cfa -4904 + ^ v14: .cfa -4936 + ^ v8: .cfa -4928 + ^ v9: .cfa -4920 + ^ x19: .cfa -5024 + ^ x20: .cfa -5016 + ^ x21: .cfa -5008 + ^ x22: .cfa -5000 + ^ x23: .cfa -4992 + ^ x24: .cfa -4984 + ^ x25: .cfa -4976 + ^ x26: .cfa -4968 + ^ x27: .cfa -4960 + ^ x28: .cfa -4952 + ^
STACK CFI 24640 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 24644 .cfa: sp 5072 + .ra: .cfa -4944 + ^ v10: .cfa -4912 + ^ v11: .cfa -4904 + ^ v12: .cfa -4896 + ^ v13: .cfa -4888 + ^ v14: .cfa -4936 + ^ v8: .cfa -4928 + ^ v9: .cfa -4920 + ^ x19: .cfa -5024 + ^ x20: .cfa -5016 + ^ x21: .cfa -5008 + ^ x22: .cfa -5000 + ^ x23: .cfa -4992 + ^ x24: .cfa -4984 + ^ x25: .cfa -4976 + ^ x26: .cfa -4968 + ^ x27: .cfa -4960 + ^ x28: .cfa -4952 + ^
STACK CFI INIT 24c18 964 .cfa: sp 0 + .ra: x30
STACK CFI 24c1c .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 24c24 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 24c34 x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 24c58 .ra: .cfa -432 + ^ v10: .cfa -400 + ^ v11: .cfa -392 + ^ v12: .cfa -384 + ^ v13: .cfa -376 + ^ v14: .cfa -368 + ^ v15: .cfa -360 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^
STACK CFI 25214 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 25218 .cfa: sp 496 + .ra: .cfa -432 + ^ v10: .cfa -400 + ^ v11: .cfa -392 + ^ v12: .cfa -384 + ^ v13: .cfa -376 + ^ v14: .cfa -368 + ^ v15: .cfa -360 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI INIT 25588 120 .cfa: sp 0 + .ra: x30
STACK CFI 255d8 .cfa: sp 80 +
STACK CFI 25684 .cfa: sp 0 +
STACK CFI INIT 256a8 11b8 .cfa: sp 0 + .ra: x30
STACK CFI 256ac .cfa: sp 960 +
STACK CFI 256b4 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 256c4 x19: .cfa -960 + ^ x20: .cfa -952 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 25704 .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -816 + ^ v15: .cfa -808 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 26160 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 26168 .cfa: sp 960 + .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -816 + ^ v15: .cfa -808 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI INIT 26880 1ac .cfa: sp 0 + .ra: x30
STACK CFI 26884 .cfa: sp 400 +
STACK CFI 26888 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 26898 x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 268b0 .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 269f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 269f8 .cfa: sp 400 + .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 26a40 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26ac0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 26ac4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 26acc .ra: .cfa -168 + ^ v8: .cfa -160 + ^ x21: .cfa -176 + ^
STACK CFI 26c58 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI 26c60 .cfa: sp 192 + .ra: .cfa -168 + ^ v8: .cfa -160 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI 26ca4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI 26ca8 .cfa: sp 192 + .ra: .cfa -168 + ^ v8: .cfa -160 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI INIT 26d70 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 26d74 .cfa: sp 1200 +
STACK CFI 26d7c x19: .cfa -1200 + ^ x20: .cfa -1192 + ^
STACK CFI 26d8c x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 26d9c .ra: .cfa -1144 + ^ v10: .cfa -1120 + ^ v8: .cfa -1136 + ^ v9: .cfa -1128 + ^ x25: .cfa -1152 + ^
STACK CFI 26f50 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 26f54 .cfa: sp 1200 + .ra: .cfa -1144 + ^ v10: .cfa -1120 + ^ v8: .cfa -1136 + ^ v9: .cfa -1128 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x25: .cfa -1152 + ^
STACK CFI INIT 27038 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 270b0 178 .cfa: sp 0 + .ra: x30
STACK CFI 270b4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 270b8 .ra: .cfa -168 + ^ x21: .cfa -176 + ^
STACK CFI 271b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 271c0 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI 271ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 271f0 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI INIT 27230 9a0 .cfa: sp 0 + .ra: x30
STACK CFI 27234 .cfa: sp 896 +
STACK CFI 27238 v8: .cfa -800 + ^ v9: .cfa -792 + ^
STACK CFI 27240 x21: .cfa -880 + ^ x22: .cfa -872 + ^
STACK CFI 27250 x19: .cfa -896 + ^ x20: .cfa -888 + ^ x23: .cfa -864 + ^ x24: .cfa -856 + ^
STACK CFI 2726c .ra: .cfa -816 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^ x27: .cfa -832 + ^ x28: .cfa -824 + ^
STACK CFI 27a34 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 27a38 .cfa: sp 896 + .ra: .cfa -816 + ^ v8: .cfa -800 + ^ v9: .cfa -792 + ^ x19: .cfa -896 + ^ x20: .cfa -888 + ^ x21: .cfa -880 + ^ x22: .cfa -872 + ^ x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^ x27: .cfa -832 + ^ x28: .cfa -824 + ^
STACK CFI INIT 27c00 fc0 .cfa: sp 0 + .ra: x30
STACK CFI 27c04 .cfa: sp 800 +
STACK CFI 27c08 v8: .cfa -704 + ^ v9: .cfa -696 + ^
STACK CFI 27c10 v10: .cfa -712 + ^
STACK CFI 27c18 x19: .cfa -800 + ^ x20: .cfa -792 + ^
STACK CFI 27c28 x21: .cfa -784 + ^ x22: .cfa -776 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI 27c40 .ra: .cfa -720 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^
STACK CFI 288c4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 288c8 .cfa: sp 800 + .ra: .cfa -720 + ^ v10: .cfa -712 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI INIT 28c00 168 .cfa: sp 0 + .ra: x30
STACK CFI 28c04 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 28c0c .ra: .cfa -152 + ^ x21: .cfa -160 + ^
STACK CFI 28d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 28d08 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^
STACK CFI 28d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 28d30 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^
STACK CFI INIT 28d68 9e0 .cfa: sp 0 + .ra: x30
STACK CFI 28d6c .cfa: sp 672 +
STACK CFI 28d78 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 28d8c x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 28da8 .ra: .cfa -592 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI 29460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 29468 .cfa: sp 672 + .ra: .cfa -592 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI INIT 29748 140 .cfa: sp 0 + .ra: x30
STACK CFI 2974c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29758 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29760 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2984c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 29850 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 29888 140 .cfa: sp 0 + .ra: x30
STACK CFI 2988c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29898 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 298a0 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2998c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 29990 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 299c8 158 .cfa: sp 0 + .ra: x30
STACK CFI 299cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 299e0 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 29ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 29ae8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 29b20 17d8 .cfa: sp 0 + .ra: x30
STACK CFI 29b24 .cfa: sp 2384 +
STACK CFI 29b28 x19: .cfa -2352 + ^ x20: .cfa -2344 + ^
STACK CFI 29b30 x21: .cfa -2336 + ^ x22: .cfa -2328 + ^
STACK CFI 29b38 x23: .cfa -2320 + ^ x24: .cfa -2312 + ^
STACK CFI 29b40 x25: .cfa -2304 + ^ x26: .cfa -2296 + ^
STACK CFI 29b50 v8: .cfa -2256 + ^ v9: .cfa -2248 + ^ x27: .cfa -2288 + ^ x28: .cfa -2280 + ^
STACK CFI 29b74 .ra: .cfa -2272 + ^ v10: .cfa -2240 + ^ v11: .cfa -2232 + ^ v12: .cfa -2224 + ^ v13: .cfa -2216 + ^
STACK CFI 2ac9c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2aca0 .cfa: sp 2384 + .ra: .cfa -2272 + ^ v10: .cfa -2240 + ^ v11: .cfa -2232 + ^ v12: .cfa -2224 + ^ v13: .cfa -2216 + ^ v8: .cfa -2256 + ^ v9: .cfa -2248 + ^ x19: .cfa -2352 + ^ x20: .cfa -2344 + ^ x21: .cfa -2336 + ^ x22: .cfa -2328 + ^ x23: .cfa -2320 + ^ x24: .cfa -2312 + ^ x25: .cfa -2304 + ^ x26: .cfa -2296 + ^ x27: .cfa -2288 + ^ x28: .cfa -2280 + ^
STACK CFI INIT 2b310 334 .cfa: sp 0 + .ra: x30
STACK CFI 2b314 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b320 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2b330 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2b584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2b588 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2b660 140 .cfa: sp 0 + .ra: x30
STACK CFI 2b664 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b670 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b678 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2b764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2b768 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2b7a0 a58 .cfa: sp 0 + .ra: x30
STACK CFI 2b7a4 .cfa: sp 880 +
STACK CFI 2b7a8 x19: .cfa -880 + ^ x20: .cfa -872 + ^
STACK CFI 2b7b8 x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^
STACK CFI 2b7c8 x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI 2b7d4 .ra: .cfa -800 + ^ v8: .cfa -792 + ^
STACK CFI 2bf1c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2bf20 .cfa: sp 880 + .ra: .cfa -800 + ^ v8: .cfa -792 + ^ x19: .cfa -880 + ^ x20: .cfa -872 + ^ x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI INIT 2c220 15d0 .cfa: sp 0 + .ra: x30
STACK CFI 2c224 .cfa: sp 2800 +
STACK CFI 2c228 x19: .cfa -2768 + ^ x20: .cfa -2760 + ^
STACK CFI 2c248 .ra: .cfa -2688 + ^ v10: .cfa -2680 + ^ v8: .cfa -2672 + ^ v9: .cfa -2664 + ^ x21: .cfa -2752 + ^ x22: .cfa -2744 + ^ x23: .cfa -2736 + ^ x24: .cfa -2728 + ^ x25: .cfa -2720 + ^ x26: .cfa -2712 + ^ x27: .cfa -2704 + ^ x28: .cfa -2696 + ^
STACK CFI 2d438 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2d43c .cfa: sp 2800 + .ra: .cfa -2688 + ^ v10: .cfa -2680 + ^ v8: .cfa -2672 + ^ v9: .cfa -2664 + ^ x19: .cfa -2768 + ^ x20: .cfa -2760 + ^ x21: .cfa -2752 + ^ x22: .cfa -2744 + ^ x23: .cfa -2736 + ^ x24: .cfa -2728 + ^ x25: .cfa -2720 + ^ x26: .cfa -2712 + ^ x27: .cfa -2704 + ^ x28: .cfa -2696 + ^
STACK CFI INIT 2d818 f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d908 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 2d90c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2d924 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d934 .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x25: .cfa -32 + ^
STACK CFI 2dabc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2dac0 .cfa: sp 80 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 2dbc0 2ec4 .cfa: sp 0 + .ra: x30
STACK CFI 2dbc8 .cfa: sp 4784 +
STACK CFI 2dbf8 .ra: .cfa -4704 + ^ v10: .cfa -4672 + ^ v11: .cfa -4664 + ^ v12: .cfa -4656 + ^ v13: .cfa -4648 + ^ v14: .cfa -4640 + ^ v15: .cfa -4632 + ^ v8: .cfa -4688 + ^ v9: .cfa -4680 + ^ x19: .cfa -4784 + ^ x20: .cfa -4776 + ^ x21: .cfa -4768 + ^ x22: .cfa -4760 + ^ x23: .cfa -4752 + ^ x24: .cfa -4744 + ^ x25: .cfa -4736 + ^ x26: .cfa -4728 + ^ x27: .cfa -4720 + ^ x28: .cfa -4712 + ^
STACK CFI 302e4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 302e8 .cfa: sp 4784 + .ra: .cfa -4704 + ^ v10: .cfa -4672 + ^ v11: .cfa -4664 + ^ v12: .cfa -4656 + ^ v13: .cfa -4648 + ^ v14: .cfa -4640 + ^ v15: .cfa -4632 + ^ v8: .cfa -4688 + ^ v9: .cfa -4680 + ^ x19: .cfa -4784 + ^ x20: .cfa -4776 + ^ x21: .cfa -4768 + ^ x22: .cfa -4760 + ^ x23: .cfa -4752 + ^ x24: .cfa -4744 + ^ x25: .cfa -4736 + ^ x26: .cfa -4728 + ^ x27: .cfa -4720 + ^ x28: .cfa -4712 + ^
STACK CFI INIT 30b00 1b34 .cfa: sp 0 + .ra: x30
STACK CFI 30b04 .cfa: sp 1968 +
STACK CFI 30b08 x23: .cfa -1936 + ^ x24: .cfa -1928 + ^
STACK CFI 30b14 v12: .cfa -1840 + ^ v13: .cfa -1832 + ^
STACK CFI 30b1c x25: .cfa -1920 + ^ x26: .cfa -1912 + ^
STACK CFI 30b2c x19: .cfa -1968 + ^ x20: .cfa -1960 + ^ x21: .cfa -1952 + ^ x22: .cfa -1944 + ^
STACK CFI 30b40 .ra: .cfa -1888 + ^ v10: .cfa -1856 + ^ v11: .cfa -1848 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI 32264 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 32268 .cfa: sp 1968 + .ra: .cfa -1888 + ^ v10: .cfa -1856 + ^ v11: .cfa -1848 + ^ v12: .cfa -1840 + ^ v13: .cfa -1832 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x19: .cfa -1968 + ^ x20: .cfa -1960 + ^ x21: .cfa -1952 + ^ x22: .cfa -1944 + ^ x23: .cfa -1936 + ^ x24: .cfa -1928 + ^ x25: .cfa -1920 + ^ x26: .cfa -1912 + ^ x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI INIT 32670 1358 .cfa: sp 0 + .ra: x30
STACK CFI 32678 .cfa: sp 3072 +
STACK CFI 3268c x19: .cfa -3040 + ^ x20: .cfa -3032 + ^ x21: .cfa -3024 + ^ x22: .cfa -3016 + ^
STACK CFI 32694 x23: .cfa -3008 + ^ x24: .cfa -3000 + ^
STACK CFI 3269c x25: .cfa -2992 + ^ x26: .cfa -2984 + ^
STACK CFI 326b8 .ra: .cfa -2960 + ^ v8: .cfa -2944 + ^ v9: .cfa -2936 + ^ x27: .cfa -2976 + ^ x28: .cfa -2968 + ^
STACK CFI 33694 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33698 .cfa: sp 3072 + .ra: .cfa -2960 + ^ v8: .cfa -2944 + ^ v9: .cfa -2936 + ^ x19: .cfa -3040 + ^ x20: .cfa -3032 + ^ x21: .cfa -3024 + ^ x22: .cfa -3016 + ^ x23: .cfa -3008 + ^ x24: .cfa -3000 + ^ x25: .cfa -2992 + ^ x26: .cfa -2984 + ^ x27: .cfa -2976 + ^ x28: .cfa -2968 + ^
STACK CFI INIT 33a10 1e20 .cfa: sp 0 + .ra: x30
STACK CFI 33a14 .cfa: sp 2448 +
STACK CFI 33a18 x23: .cfa -2336 + ^ x24: .cfa -2328 + ^
STACK CFI 33a2c v12: .cfa -2240 + ^ v13: .cfa -2232 + ^
STACK CFI 33a3c x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 33a4c x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^
STACK CFI 33a64 .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v14: .cfa -2224 + ^ v15: .cfa -2216 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^
STACK CFI 353e4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 353e8 .cfa: sp 2448 + .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v12: .cfa -2240 + ^ v13: .cfa -2232 + ^ v14: .cfa -2224 + ^ v15: .cfa -2216 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI INIT 9838 30 .cfa: sp 0 + .ra: x30
STACK CFI 983c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9858 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9764 a0 .cfa: sp 0 + .ra: x30
STACK CFI 9768 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9774 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 97f8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 35868 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35880 334 .cfa: sp 0 + .ra: x30
STACK CFI 35888 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 35890 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 35898 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 358b0 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 358e8 .ra: .cfa -144 + ^ v8: .cfa -136 + ^
STACK CFI 35ac4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 35ac8 .cfa: sp 208 + .ra: .cfa -144 + ^ v8: .cfa -136 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 35bd0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35c10 aa4 .cfa: sp 0 + .ra: x30
STACK CFI 35c18 .cfa: sp 1104 +
STACK CFI 35c28 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 35c30 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 35c40 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 35c50 .ra: .cfa -1024 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 3663c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 36640 .cfa: sp 1104 + .ra: .cfa -1024 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI INIT 366d8 ac .cfa: sp 0 + .ra: x30
STACK CFI 366dc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 36764 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 36768 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 36780 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 36790 614 .cfa: sp 0 + .ra: x30
STACK CFI 36794 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 3679c x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 367ac x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 367bc .ra: .cfa -176 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 36b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 36b38 .cfa: sp 256 + .ra: .cfa -176 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 36dc0 148 .cfa: sp 0 + .ra: x30
STACK CFI 36dc4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36dcc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36dd4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 36e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 36e48 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 36f08 5c .cfa: sp 0 + .ra: x30
STACK CFI 36f0c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36f10 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 36f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 36f58 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 36f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 36f70 388 .cfa: sp 0 + .ra: x30
STACK CFI 36f74 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 36f94 .ra: .cfa -32 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3713c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 37140 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 37310 100 .cfa: sp 0 + .ra: x30
STACK CFI 37314 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3731c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 37324 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 373d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 373e0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 37410 78c .cfa: sp 0 + .ra: x30
STACK CFI 37418 .cfa: sp 480 + x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 37424 x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 37440 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 37498 .ra: .cfa -400 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 37988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37990 .cfa: sp 480 + .ra: .cfa -400 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI INIT 37bb0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 37bb4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 37bc4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 37bd4 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 37bdc x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 37be4 .ra: .cfa -112 + ^
STACK CFI 37df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37df8 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 37e70 738 .cfa: sp 0 + .ra: x30
STACK CFI 37e78 .cfa: sp 416 +
STACK CFI 37e80 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 37e90 x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 37eb8 .ra: .cfa -320 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 37ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37ff8 .cfa: sp 416 + .ra: .cfa -320 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT 385c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 385c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 385cc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 385d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 38688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 38690 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 386c0 500 .cfa: sp 0 + .ra: x30
STACK CFI 386c4 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 386c8 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 386d0 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 386d8 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 386e0 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 386ec .ra: .cfa -224 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^
STACK CFI 38b30 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38b34 .cfa: sp 304 + .ra: .cfa -224 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 38bc0 1df0 .cfa: sp 0 + .ra: x30
STACK CFI 38bc4 .cfa: sp 1424 +
STACK CFI 38bc8 x19: .cfa -1408 + ^ x20: .cfa -1400 + ^
STACK CFI 38bf0 .ra: .cfa -1328 + ^ v8: .cfa -1320 + ^ x21: .cfa -1392 + ^ x22: .cfa -1384 + ^ x23: .cfa -1376 + ^ x24: .cfa -1368 + ^ x25: .cfa -1360 + ^ x26: .cfa -1352 + ^ x27: .cfa -1344 + ^ x28: .cfa -1336 + ^
STACK CFI 3a320 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3a324 .cfa: sp 1424 + .ra: .cfa -1328 + ^ v8: .cfa -1320 + ^ x19: .cfa -1408 + ^ x20: .cfa -1400 + ^ x21: .cfa -1392 + ^ x22: .cfa -1384 + ^ x23: .cfa -1376 + ^ x24: .cfa -1368 + ^ x25: .cfa -1360 + ^ x26: .cfa -1352 + ^ x27: .cfa -1344 + ^ x28: .cfa -1336 + ^
STACK CFI INIT 3a9c8 674 .cfa: sp 0 + .ra: x30
STACK CFI 3a9cc .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 3a9d0 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 3a9d8 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 3a9e8 .ra: .cfa -176 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 3af3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3af40 .cfa: sp 256 + .ra: .cfa -176 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 3b040 670 .cfa: sp 0 + .ra: x30
STACK CFI 3b044 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 3b05c x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 3b074 .ra: .cfa -352 + ^ v8: .cfa -344 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 3b5b4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3b5b8 .cfa: sp 416 + .ra: .cfa -352 + ^ v8: .cfa -344 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI INIT 9868 30 .cfa: sp 0 + .ra: x30
STACK CFI 986c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9888 .cfa: sp 0 + .ra: .ra x19: x19
