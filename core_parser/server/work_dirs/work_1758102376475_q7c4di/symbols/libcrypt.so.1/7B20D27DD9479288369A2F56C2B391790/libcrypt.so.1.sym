MODULE Linux arm64 7B20D27DD9479288369A2F56C2B391790 libcrypt.so.1
INFO CODE_ID 7DD2207B47D98892369A2F56C2B39179468BEF4C
PUBLIC 117a0 0 crypt_gensalt
PUBLIC 14270 0 crypt
PUBLIC 14eb0 0 crypt_rn
PUBLIC 14f58 0 crypt_ra
PUBLIC 15030 0 xcrypt_r
PUBLIC 15088 0 xcrypt_gensalt_r
PUBLIC 15218 0 crypt_gensalt_ra
PUBLIC 152c8 0 crypt_checksalt
PUBLIC 152f0 0 crypt_preferred_method
PUBLIC 156d0 0 setkey_r
PUBLIC 156e0 0 encrypt_r
PUBLIC 156f0 0 setkey
PUBLIC 15700 0 encrypt
STACK CFI INIT 19f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a28 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a68 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a74 x19: .cfa -16 + ^
STACK CFI 1aac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab8 204 .cfa: sp 0 + .ra: x30
STACK CFI 1abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1acc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1cc0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cf8 314 .cfa: sp 0 + .ra: x30
STACK CFI 1cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d18 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2010 6f0 .cfa: sp 0 + .ra: x30
STACK CFI 2014 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 2050 x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 2058 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 26f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 26f4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI INIT 2700 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2718 54 .cfa: sp 0 + .ra: x30
STACK CFI 271c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2730 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2770 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 2774 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 277c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 27a0 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 27a8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 27e0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 28a4 x27: x27 x28: x28
STACK CFI 28d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 28d8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 291c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 29e8 x27: x27 x28: x28
STACK CFI 2a20 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 2a28 1ec .cfa: sp 0 + .ra: x30
STACK CFI 2a2c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2a34 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2a40 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2a60 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 2bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2bf4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2c18 6c .cfa: sp 0 + .ra: x30
STACK CFI 2c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2c88 18c .cfa: sp 0 + .ra: x30
STACK CFI 2c8c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2cac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2dec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2e18 220 .cfa: sp 0 + .ra: x30
STACK CFI 2e1c .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 2e24 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 2e2c x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 2e3c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 2e50 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 2e58 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 2f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f94 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT 3038 614 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3054 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3060 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3080 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 3650 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3680 108 .cfa: sp 0 + .ra: x30
STACK CFI 3684 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3690 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 36d0 x25: .cfa -16 + ^
STACK CFI 371c x25: x25
STACK CFI 3744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 374c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3768 x25: .cfa -16 + ^
STACK CFI 377c x25: x25
STACK CFI 3780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3788 164 .cfa: sp 0 + .ra: x30
STACK CFI 378c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3798 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 38bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 38f0 a50 .cfa: sp 0 + .ra: x30
STACK CFI 38f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3908 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3910 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3920 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 433c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 4340 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4370 108 .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4380 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43c0 x25: .cfa -16 + ^
STACK CFI 440c x25: x25
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 443c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4458 x25: .cfa -16 + ^
STACK CFI 446c x25: x25
STACK CFI 4470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4478 164 .cfa: sp 0 + .ra: x30
STACK CFI 447c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4488 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4494 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 45e0 fec .cfa: sp 0 + .ra: x30
STACK CFI 45e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 45fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4604 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 462c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 55c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 55d0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5608 f0 .cfa: sp 0 + .ra: x30
STACK CFI 560c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5618 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5624 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 562c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5680 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 56f8 16c .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5704 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5714 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5734 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5858 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5868 9b4 .cfa: sp 0 + .ra: x30
STACK CFI 586c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 588c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 6220 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6230 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6238 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 62a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 62e0 110 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 62ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 62f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 632c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6338 x25: .cfa -16 + ^
STACK CFI 63c0 x23: x23 x24: x24
STACK CFI 63c4 x25: x25
STACK CFI 63c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 63f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 63f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6400 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6418 x21: .cfa -16 + ^
STACK CFI 6460 x21: x21
STACK CFI 646c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6470 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6478 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 64d8 74 .cfa: sp 0 + .ra: x30
STACK CFI 64dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6550 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6570 1ac .cfa: sp 0 + .ra: x30
STACK CFI 6574 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6580 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 658c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6598 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 65a4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6684 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6720 74 .cfa: sp 0 + .ra: x30
STACK CFI 6724 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 672c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 6784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6788 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT 6798 9c .cfa: sp 0 + .ra: x30
STACK CFI 679c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 67a4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 67b4 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6828 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI INIT 6838 d8 .cfa: sp 0 + .ra: x30
STACK CFI 683c .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 6844 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 6850 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 6860 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6904 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x29: .cfa -464 + ^
STACK CFI INIT 6910 ac .cfa: sp 0 + .ra: x30
STACK CFI 6914 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 691c x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 692c x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 6944 x23: .cfa -416 + ^
STACK CFI 69ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 69b0 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x29: .cfa -464 + ^
STACK CFI INIT 69c0 74 .cfa: sp 0 + .ra: x30
STACK CFI 69c4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 69cc x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 6a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a28 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT 6a38 8c .cfa: sp 0 + .ra: x30
STACK CFI 6a3c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 6a44 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 6a54 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 6ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ab8 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x29: .cfa -384 + ^
STACK CFI INIT 6ac8 108 .cfa: sp 0 + .ra: x30
STACK CFI 6acc .cfa: sp 688 +
STACK CFI 6ad8 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 6ae0 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 6af0 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 6b08 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 6b24 x25: .cfa -624 + ^
STACK CFI 6bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6bc4 .cfa: sp 688 + .ra: .cfa -680 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x29: .cfa -688 + ^
STACK CFI INIT 6bd0 568 .cfa: sp 0 + .ra: x30
STACK CFI 6bd4 .cfa: sp 1232 +
STACK CFI 6bd8 .ra: .cfa -1224 + ^ x29: .cfa -1232 + ^
STACK CFI 6be0 x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 6c08 x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 6f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6f18 .cfa: sp 1232 + .ra: .cfa -1224 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^ x29: .cfa -1232 + ^
STACK CFI INIT 7138 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7190 a7c .cfa: sp 0 + .ra: x30
STACK CFI 7194 .cfa: sp 848 +
STACK CFI 71a0 .ra: .cfa -840 + ^ x29: .cfa -848 + ^
STACK CFI 71d0 x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 7bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7c00 .cfa: sp 848 + .ra: .cfa -840 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^ x29: .cfa -848 + ^
STACK CFI INIT 7c10 98 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ca8 ec .cfa: sp 0 + .ra: x30
STACK CFI 7cac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7cb8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7cd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7d98 f0 .cfa: sp 0 + .ra: x30
STACK CFI 7d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7da8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7db0 x21: .cfa -16 + ^
STACK CFI 7e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e88 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7e8c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 7e94 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 7ea4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 7eb8 x23: .cfa -240 + ^
STACK CFI 7f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7f20 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7f30 f8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8028 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8138 21c .cfa: sp 0 + .ra: x30
STACK CFI 813c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 8160 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 8170 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8188 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 81b4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 81c8 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 8344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8348 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 8358 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8430 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8510 554 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 320 +
STACK CFI 851c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 8524 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 855c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 8568 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 8588 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8594 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 85c8 x21: x21 x22: x22
STACK CFI 85cc x23: x23 x24: x24
STACK CFI 85d0 x25: x25 x26: x26
STACK CFI 85d4 x27: x27 x28: x28
STACK CFI 85f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 85fc .cfa: sp 320 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 8600 x23: x23 x24: x24
STACK CFI 8604 x27: x27 x28: x28
STACK CFI 8608 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 87e0 x21: x21 x22: x22
STACK CFI 87e4 x23: x23 x24: x24
STACK CFI 87e8 x25: x25 x26: x26
STACK CFI 87ec x27: x27 x28: x28
STACK CFI 87f0 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 88e0 x21: x21 x22: x22
STACK CFI 88e4 x23: x23 x24: x24
STACK CFI 88e8 x25: x25 x26: x26
STACK CFI 88ec x27: x27 x28: x28
STACK CFI 88f0 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 8a48 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8a50 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8a58 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 8a5c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 8a60 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 8a68 d8 .cfa: sp 0 + .ra: x30
STACK CFI 8a6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8a74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8a84 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b2c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8b40 370 .cfa: sp 0 + .ra: x30
STACK CFI 8b44 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8b4c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 8b58 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 8b74 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8b84 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8b98 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8ca8 x19: x19 x20: x20
STACK CFI 8cac x27: x27 x28: x28
STACK CFI 8cb0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8cb8 x19: x19 x20: x20
STACK CFI 8cbc x19: .cfa -208 + ^ x20: .cfa -200 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8ce0 x19: x19 x20: x20
STACK CFI 8ce4 x27: x27 x28: x28
STACK CFI 8d0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8d10 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 8e84 x19: x19 x20: x20
STACK CFI 8e88 x27: x27 x28: x28
STACK CFI 8e94 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8e9c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8ea8 x19: x19 x20: x20
STACK CFI 8eac x27: x27 x28: x28
STACK CFI INIT 8eb0 288 .cfa: sp 0 + .ra: x30
STACK CFI 8ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8edc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 904c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9054 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9138 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9158 104 .cfa: sp 0 + .ra: x30
STACK CFI 915c .cfa: sp 176 +
STACK CFI 9160 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9168 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9174 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 919c x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 9244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9248 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9260 74 .cfa: sp 0 + .ra: x30
STACK CFI 9264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9278 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 92b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 92d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 92d8 1168 .cfa: sp 0 + .ra: x30
STACK CFI 92dc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 9308 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI a408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a40c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT a440 4c .cfa: sp 0 + .ra: x30
STACK CFI a444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a44c x19: .cfa -16 + ^
STACK CFI a480 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a484 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a490 520 .cfa: sp 0 + .ra: x30
STACK CFI a494 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a4c8 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a4fc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a500 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a970 x19: x19 x20: x20
STACK CFI a974 x21: x21 x22: x22
STACK CFI a998 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a99c .cfa: sp 176 + .ra: .cfa -168 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI a9a4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a9ac x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT a9b0 5f8 .cfa: sp 0 + .ra: x30
STACK CFI a9b8 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a9d8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI aa00 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI aa68 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI aa6c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI af64 x19: x19 x20: x20
STACK CFI af68 x21: x21 x22: x22
STACK CFI af90 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI af94 .cfa: sp 176 + .ra: .cfa -168 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI af9c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI afa4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT afa8 9c0 .cfa: sp 0 + .ra: x30
STACK CFI afac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI afdc x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI b92c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b930 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT b968 118c .cfa: sp 0 + .ra: x30
STACK CFI b96c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI b998 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI cab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cabc .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT caf8 600 .cfa: sp 0 + .ra: x30
STACK CFI cafc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI cb10 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI cb1c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI cb40 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI cca4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI cd78 x25: x25 x26: x26
STACK CFI cee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI cee4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI cef4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI cf58 x25: x25 x26: x26
STACK CFI cfb8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI d084 x25: x25 x26: x26
STACK CFI INIT d0f8 3c8 .cfa: sp 0 + .ra: x30
STACK CFI d0fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI d10c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI d118 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI d124 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI d130 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI d378 x21: x21 x22: x22
STACK CFI d37c x25: x25 x26: x26
STACK CFI d380 x27: x27 x28: x28
STACK CFI d38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI d390 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT d4c0 358 .cfa: sp 0 + .ra: x30
STACK CFI d4c4 .cfa: sp 224 +
STACK CFI d4c8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI d4d0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI d4d8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI d4e0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI d4ec x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI d560 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI d66c x23: x23 x24: x24
STACK CFI d690 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI d7c4 x23: x23 x24: x24
STACK CFI d7dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d7e0 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT d818 728 .cfa: sp 0 + .ra: x30
STACK CFI d81c .cfa: sp 304 +
STACK CFI d828 .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI d830 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI d83c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI d84c x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI d86c x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI d8d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d8d8 .cfa: sp 304 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x29: .cfa -272 + ^
STACK CFI d904 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI db80 x27: x27 x28: x28
STACK CFI dbc0 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI de44 x27: x27 x28: x28
STACK CFI de48 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI de4c x27: x27 x28: x28
STACK CFI de50 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI def4 x27: x27 x28: x28
STACK CFI def8 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI df00 x27: x27 x28: x28
STACK CFI df04 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI df20 x27: x27 x28: x28
STACK CFI df28 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT df40 2a4 .cfa: sp 0 + .ra: x30
STACK CFI df44 .cfa: sp 224 +
STACK CFI df48 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI df54 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI df5c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI df88 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI df90 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI df9c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI dffc x21: x21 x22: x22
STACK CFI e004 x23: x23 x24: x24
STACK CFI e008 x25: x25 x26: x26
STACK CFI e030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI e034 .cfa: sp 224 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI e16c x21: x21 x22: x22
STACK CFI e170 x23: x23 x24: x24
STACK CFI e174 x25: x25 x26: x26
STACK CFI e178 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI e17c x21: x21 x22: x22
STACK CFI e180 x23: x23 x24: x24
STACK CFI e184 x25: x25 x26: x26
STACK CFI e1a4 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI e1bc x21: x21 x22: x22
STACK CFI e1c0 x23: x23 x24: x24
STACK CFI e1c4 x25: x25 x26: x26
STACK CFI e1d4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI e1dc x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI e1e0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT e1e8 43c .cfa: sp 0 + .ra: x30
STACK CFI e1ec .cfa: sp 272 +
STACK CFI e1f0 .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI e1fc x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI e204 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI e25c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI e268 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI e288 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI e348 x23: x23 x24: x24
STACK CFI e34c x25: x25 x26: x26
STACK CFI e350 x27: x27 x28: x28
STACK CFI e37c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e380 .cfa: sp 272 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI e564 x23: x23 x24: x24
STACK CFI e568 x25: x25 x26: x26
STACK CFI e56c x27: x27 x28: x28
STACK CFI e570 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI e5d8 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI e5f4 x23: x23 x24: x24
STACK CFI e5f8 x25: x25 x26: x26
STACK CFI e5fc x27: x27 x28: x28
STACK CFI e600 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI e604 x23: x23 x24: x24
STACK CFI e608 x25: x25 x26: x26
STACK CFI e614 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI e61c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI e620 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT e628 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT e6a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e6b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT e6c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e6d0 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT e768 128 .cfa: sp 0 + .ra: x30
STACK CFI e76c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e880 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT e890 124 .cfa: sp 0 + .ra: x30
STACK CFI e894 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e8a4 x19: .cfa -48 + ^
STACK CFI e944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e948 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT e9b8 1e80 .cfa: sp 0 + .ra: x30
STACK CFI e9bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI e9c4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e9d4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ea0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI ea10 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI ea14 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ea30 x21: x21 x22: x22
STACK CFI ea34 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ebac x21: x21 x22: x22
STACK CFI ebb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ebbc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ebc0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1082c x21: x21 x22: x22
STACK CFI 10830 x25: x25 x26: x26
STACK CFI 10834 x27: x27 x28: x28
STACK CFI INIT 10838 354 .cfa: sp 0 + .ra: x30
STACK CFI 1083c .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 10848 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 10898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1089c .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x29: .cfa -448 + ^
STACK CFI 108a4 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 108b0 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 108d8 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 108e4 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 109b4 x21: x21 x22: x22
STACK CFI 109b8 x25: x25 x26: x26
STACK CFI 109bc x27: x27 x28: x28
STACK CFI 109c4 x23: x23 x24: x24
STACK CFI 109c8 x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 109cc x21: x21 x22: x22
STACK CFI 109d0 x23: x23 x24: x24
STACK CFI 109d4 x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 10b5c x21: x21 x22: x22
STACK CFI 10b60 x25: x25 x26: x26
STACK CFI 10b68 x23: x23 x24: x24
STACK CFI 10b6c x27: x27 x28: x28
STACK CFI 10b78 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 10b80 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 10b84 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 10b88 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 10b90 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10ba8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10bd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10be8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c28 24 .cfa: sp 0 + .ra: x30
STACK CFI 10c2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10c48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10c50 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c68 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c90 100 .cfa: sp 0 + .ra: x30
STACK CFI 10c94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10ca0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10d18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10d90 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10df0 128 .cfa: sp 0 + .ra: x30
STACK CFI 10df4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10dfc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10e08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10e18 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10efc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 10f18 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 10f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10f3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10fa4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11030 x19: x19 x20: x20
STACK CFI 11034 x21: x21 x22: x22
STACK CFI 11038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1103c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 11044 x19: x19 x20: x20
STACK CFI 1105c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 11074 x19: x19 x20: x20
STACK CFI 1107c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11080 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 11084 x19: x19 x20: x20
STACK CFI 1109c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 110a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 110a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 110ac x21: x21 x22: x22
STACK CFI 110bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 110c0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 110d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 110dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11170 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11174 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11178 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1117c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 112b4 x21: x21 x22: x22
STACK CFI 112b8 x23: x23 x24: x24
STACK CFI 112bc x25: x25 x26: x26
STACK CFI 112c0 x27: x27 x28: x28
STACK CFI 112c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 112c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 112e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 112e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 11304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11314 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 1131c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11320 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11324 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11328 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11344 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1134c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11350 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11354 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11358 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 11370 28c .cfa: sp 0 + .ra: x30
STACK CFI 11374 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11388 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 113b0 x19: x19 x20: x20
STACK CFI 113b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 113bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 113c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 113d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1142c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11480 x27: .cfa -16 + ^
STACK CFI 11534 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1154c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11550 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 11558 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11560 x23: x23 x24: x24
STACK CFI 11568 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 11590 x19: x19 x20: x20
STACK CFI 11594 x21: x21 x22: x22
STACK CFI 11598 x23: x23 x24: x24
STACK CFI 1159c x25: x25 x26: x26
STACK CFI 115a0 x27: x27
STACK CFI 115a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 115a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 115b0 x23: x23 x24: x24
STACK CFI 115c4 x19: x19 x20: x20
STACK CFI 115c8 x21: x21 x22: x22
STACK CFI 115cc x25: x25 x26: x26
STACK CFI 115d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 115e8 x19: x19 x20: x20
STACK CFI 115ec x21: x21 x22: x22
STACK CFI 115f0 x23: x23 x24: x24
STACK CFI 115f4 x25: x25 x26: x26
STACK CFI INIT 11600 88 .cfa: sp 0 + .ra: x30
STACK CFI 11604 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11628 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11650 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1166c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11684 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11688 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11698 108 .cfa: sp 0 + .ra: x30
STACK CFI 1169c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11714 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11758 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11770 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11774 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1178c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11790 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 117a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 117b8 218 .cfa: sp 0 + .ra: x30
STACK CFI 117bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 117c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 117d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 117e4 x23: .cfa -16 + ^
STACK CFI 11848 x19: x19 x20: x20
STACK CFI 1184c x23: x23
STACK CFI 11858 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1185c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1193c x19: x19 x20: x20
STACK CFI 11944 x23: x23
STACK CFI 11948 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1194c .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 11968 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1196c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 119d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 119e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11a2c x19: .cfa -16 + ^
STACK CFI 11a7c x19: x19
STACK CFI 11a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 11a88 x19: x19
STACK CFI 11a8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11a90 300 .cfa: sp 0 + .ra: x30
STACK CFI 11a94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11a9c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 11aac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 11b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11b10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 11b18 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11b24 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11b60 x23: x23 x24: x24
STACK CFI 11b64 x25: x25 x26: x26
STACK CFI 11b6c x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11c38 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11c8c x23: x23 x24: x24
STACK CFI 11c90 x25: x25 x26: x26
STACK CFI 11c94 x27: x27 x28: x28
STACK CFI 11c9c x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11ca0 x23: x23 x24: x24
STACK CFI 11ca4 x25: x25 x26: x26
STACK CFI 11ca8 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11cc0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11d6c x23: x23 x24: x24
STACK CFI 11d70 x25: x25 x26: x26
STACK CFI 11d74 x27: x27 x28: x28
STACK CFI 11d80 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11d88 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11d8c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 11d90 518 .cfa: sp 0 + .ra: x30
STACK CFI 11d94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11da0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11dc0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 11e18 x19: x19 x20: x20
STACK CFI 11e24 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 11e28 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 11e50 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11e5c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 11e6c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 121dc x19: x19 x20: x20
STACK CFI 121e0 x21: x21 x22: x22
STACK CFI 121e8 x25: x25 x26: x26
STACK CFI 121ec x27: x27 x28: x28
STACK CFI 121f0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 121f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 1226c x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12288 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 1228c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 122a8 6c .cfa: sp 0 + .ra: x30
STACK CFI 122ac .cfa: sp 32 +
STACK CFI 122b0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 122d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 122d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12318 194 .cfa: sp 0 + .ra: x30
STACK CFI 1231c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12328 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12358 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 12380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12384 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 12394 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12398 x23: .cfa -16 + ^
STACK CFI 12494 x21: x21 x22: x22
STACK CFI 12498 x23: x23
STACK CFI 1249c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 124a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 124a4 x21: x21 x22: x22
STACK CFI 124a8 x23: x23
STACK CFI INIT 124b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 124b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 124dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 124e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 124ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12500 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12518 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12520 320 .cfa: sp 0 + .ra: x30
STACK CFI 12524 .cfa: sp 160 +
STACK CFI 12530 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 12538 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 12588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1258c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 12594 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 125a0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 125a8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 125b4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1265c x21: x21 x22: x22
STACK CFI 12660 x23: x23 x24: x24
STACK CFI 12664 x25: x25 x26: x26
STACK CFI 12668 x27: x27 x28: x28
STACK CFI 12670 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12814 x21: x21 x22: x22
STACK CFI 12818 x23: x23 x24: x24
STACK CFI 1281c x25: x25 x26: x26
STACK CFI 12820 x27: x27 x28: x28
STACK CFI 1282c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12834 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 12838 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1283c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 12840 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 12844 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12854 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12870 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12984 x19: x19 x20: x20
STACK CFI 12988 x21: x21 x22: x22
STACK CFI 1298c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12990 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 129a4 x19: x19 x20: x20
STACK CFI 129a8 x21: x21 x22: x22
STACK CFI 129b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 129b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 129cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 129d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12a28 80 .cfa: sp 0 + .ra: x30
STACK CFI 12a80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12aa8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 12aac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12ae4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12b70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12b8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 12ba0 254 .cfa: sp 0 + .ra: x30
STACK CFI 12ba4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 12bac x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 12bb8 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 12bd4 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 12c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12c3c .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x29: .cfa -304 + ^
STACK CFI 12c40 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 12c6c x27: x27 x28: x28
STACK CFI 12c74 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 12ca0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 12d30 x25: x25 x26: x26
STACK CFI 12d34 x27: x27 x28: x28
STACK CFI 12d38 x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 12db0 x25: x25 x26: x26
STACK CFI 12db4 x27: x27 x28: x28
STACK CFI 12db8 x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 12dd4 x25: x25 x26: x26
STACK CFI 12dd8 x27: x27 x28: x28
STACK CFI 12de8 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 12df0 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 12df8 80 .cfa: sp 0 + .ra: x30
STACK CFI 12dfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12e08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12e10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12e18 x23: .cfa -16 + ^
STACK CFI 12e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 12e78 82c .cfa: sp 0 + .ra: x30
STACK CFI 12e7c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 12e88 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 12e94 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 12eb0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 12ec8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 12f48 x21: x21 x22: x22
STACK CFI 12f50 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 12f80 x21: x21 x22: x22
STACK CFI 12fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12fa8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 12fe8 x21: x21 x22: x22
STACK CFI 13000 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1300c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1353c x21: x21 x22: x22
STACK CFI 13540 x27: x27 x28: x28
STACK CFI 13544 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 135b0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 13690 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 13698 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 136a0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 136a8 4c .cfa: sp 0 + .ra: x30
STACK CFI 136ac .cfa: sp 32 +
STACK CFI 136c4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 136f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 136f8 80 .cfa: sp 0 + .ra: x30
STACK CFI 136fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13708 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13710 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13718 x23: .cfa -16 + ^
STACK CFI 13770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 13778 aa8 .cfa: sp 0 + .ra: x30
STACK CFI 1377c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 137a0 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 137bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 13840 x19: x19 x20: x20
STACK CFI 13848 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 13850 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 13878 x19: x19 x20: x20
STACK CFI 13880 x21: x21 x22: x22
STACK CFI 138a4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 138a8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 138e8 x19: x19 x20: x20
STACK CFI 13900 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 13914 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 13924 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 140b0 x19: x19 x20: x20
STACK CFI 140b4 x21: x21 x22: x22
STACK CFI 140b8 x27: x27 x28: x28
STACK CFI 140bc x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 14124 x21: x21 x22: x22
STACK CFI 14128 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 14208 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 14210 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 14218 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1421c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 14220 4c .cfa: sp 0 + .ra: x30
STACK CFI 14224 .cfa: sp 32 +
STACK CFI 1423c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14268 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14270 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14288 630 .cfa: sp 0 + .ra: x30
STACK CFI 1428c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14294 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 142a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 142ac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 142cc x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 143c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 143cc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 148b8 170 .cfa: sp 0 + .ra: x30
STACK CFI 148bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 148d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 149e8 x19: x19 x20: x20
STACK CFI 149ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 149f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14a08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a28 13c .cfa: sp 0 + .ra: x30
STACK CFI 14a2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14a3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14a74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 14a78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14a84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14aac x21: x21 x22: x22
STACK CFI 14ab0 x23: x23 x24: x24
STACK CFI 14ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14ab8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 14ac0 x25: .cfa -16 + ^
STACK CFI 14b28 x21: x21 x22: x22
STACK CFI 14b2c x23: x23 x24: x24
STACK CFI 14b30 x25: x25
STACK CFI 14b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14b3c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 14b40 x21: x21 x22: x22
STACK CFI 14b44 x23: x23 x24: x24
STACK CFI 14b48 x25: x25
STACK CFI 14b4c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 14b68 164 .cfa: sp 0 + .ra: x30
STACK CFI 14b6c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 14b80 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 14b8c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 14bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14bf8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x29: .cfa -288 + ^
STACK CFI INIT 14cd0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 14cd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14ce4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14cf0 x21: .cfa -16 + ^
STACK CFI 14d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 14d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 14d90 11c .cfa: sp 0 + .ra: x30
STACK CFI 14d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14da8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14db8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14dc0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14de8 x19: x19 x20: x20
STACK CFI 14dec x21: x21 x22: x22
STACK CFI 14df0 x23: x23 x24: x24
STACK CFI 14df8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14dfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 14e50 x19: x19 x20: x20
STACK CFI 14e58 x21: x21 x22: x22
STACK CFI 14e60 x23: x23 x24: x24
STACK CFI 14e64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14e6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14e84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14e88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 14e9c x19: x19 x20: x20
STACK CFI 14ea0 x21: x21 x22: x22
STACK CFI 14ea4 x23: x23 x24: x24
STACK CFI INIT 14eb0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 14eb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14ec0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14ed8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14f28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 14f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 14f58 d4 .cfa: sp 0 + .ra: x30
STACK CFI 14f5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14f64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14f74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 15028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 15030 58 .cfa: sp 0 + .ra: x30
STACK CFI 15034 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15044 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1504c x21: .cfa -16 + ^
STACK CFI 15084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 15088 190 .cfa: sp 0 + .ra: x30
STACK CFI 1508c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 15094 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 150a0 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 150b0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 150c4 x25: .cfa -288 + ^
STACK CFI 15164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 15168 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x29: .cfa -352 + ^
STACK CFI INIT 15218 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1521c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15224 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15234 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15240 x23: .cfa -16 + ^
STACK CFI 15294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15298 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 152bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 152c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 152c8 28 .cfa: sp 0 + .ra: x30
STACK CFI 152d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 152e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 152f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15300 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 15308 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15314 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15344 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1534c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 153a4 x21: x21 x22: x22
STACK CFI 153b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 153b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1541c x23: .cfa -16 + ^
STACK CFI 15480 x21: x21 x22: x22
STACK CFI 15488 x23: x23
STACK CFI 1548c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1549c x21: x21 x22: x22
STACK CFI 154a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 154b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 154d4 x23: .cfa -16 + ^
STACK CFI 154e0 x23: x23
STACK CFI INIT 154e8 dc .cfa: sp 0 + .ra: x30
STACK CFI 154ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 154f8 x21: .cfa -32 + ^
STACK CFI 15500 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 155b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 155b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 155c8 108 .cfa: sp 0 + .ra: x30
STACK CFI 155cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 155d8 x21: .cfa -48 + ^
STACK CFI 155e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 156c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 156c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 156d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 156e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 156f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15700 c .cfa: sp 0 + .ra: x30
