MODULE Linux arm64 AF42C412B9F6940D85D737708A36550D0 libusb-1.0.so.0
INFO CODE_ID 12C442AFF6B90D9485D737708A36550D7DDD2DAB
PUBLIC 43d8 0 libusb_get_bus_number
PUBLIC 43e0 0 libusb_get_port_number
PUBLIC 43e8 0 libusb_get_parent
PUBLIC 43f0 0 libusb_get_device_address
PUBLIC 43f8 0 libusb_get_device_speed
PUBLIC 4400 0 libusb_ref_device
PUBLIC 44d8 0 libusb_get_device
PUBLIC 44e0 0 libusb_dev_mem_alloc
PUBLIC 4510 0 libusb_dev_mem_free
PUBLIC 4530 0 libusb_set_auto_detach_kernel_driver
PUBLIC 4558 0 libusb_set_debug
PUBLIC 4590 0 libusb_set_log_cb
PUBLIC 45c0 0 libusb_set_option
PUBLIC 46d0 0 libusb_has_capability
PUBLIC 4d48 0 libusb_get_port_numbers
PUBLIC 4e08 0 libusb_get_port_path
PUBLIC 4e18 0 libusb_get_max_packet_size
PUBLIC 4ed0 0 libusb_get_max_iso_packet_size
PUBLIC 4ff8 0 libusb_unref_device
PUBLIC 50b0 0 libusb_free_device_list
PUBLIC 5208 0 libusb_get_device_list
PUBLIC 5530 0 libusb_wrap_sys_device
PUBLIC 56b0 0 libusb_open
PUBLIC 5838 0 libusb_open_device_with_vid_pid
PUBLIC 5930 0 libusb_close
PUBLIC 5c10 0 libusb_get_configuration
PUBLIC 5d88 0 libusb_set_configuration
PUBLIC 5de8 0 libusb_claim_interface
PUBLIC 5eb8 0 libusb_release_interface
PUBLIC 5f70 0 libusb_set_interface_alt_setting
PUBLIC 6048 0 libusb_clear_halt
PUBLIC 60c0 0 libusb_reset_device
PUBLIC 6130 0 libusb_alloc_streams
PUBLIC 61d8 0 libusb_free_streams
PUBLIC 6270 0 libusb_kernel_driver_active
PUBLIC 62f8 0 libusb_detach_kernel_driver
PUBLIC 6380 0 libusb_attach_kernel_driver
PUBLIC 6408 0 libusb_init
PUBLIC 67c8 0 libusb_exit
PUBLIC 6a60 0 libusb_error_name
PUBLIC 6b80 0 libusb_get_version
PUBLIC 7828 0 libusb_get_device_descriptor
PUBLIC 7880 0 libusb_get_active_config_descriptor
PUBLIC 79c8 0 libusb_get_config_descriptor
PUBLIC 7c90 0 libusb_get_config_descriptor_by_value
PUBLIC 7d58 0 libusb_free_config_descriptor
PUBLIC 7d88 0 libusb_get_ss_endpoint_companion_descriptor
PUBLIC 7ed8 0 libusb_free_ss_endpoint_companion_descriptor
PUBLIC 7ee0 0 libusb_free_bos_descriptor
PUBLIC 7f38 0 libusb_get_bos_descriptor
PUBLIC 83a8 0 libusb_get_usb_2_0_extension_descriptor
PUBLIC 8470 0 libusb_free_usb_2_0_extension_descriptor
PUBLIC 8478 0 libusb_get_ss_usb_device_capability_descriptor
PUBLIC 8540 0 libusb_free_ss_usb_device_capability_descriptor
PUBLIC 8548 0 libusb_get_container_id_descriptor
PUBLIC 8610 0 libusb_free_container_id_descriptor
PUBLIC 8618 0 libusb_get_string_descriptor_ascii
PUBLIC 8a38 0 libusb_hotplug_deregister_callback
PUBLIC 8b98 0 libusb_hotplug_register_callback
PUBLIC 91f8 0 libusb_alloc_transfer
PUBLIC 92a0 0 libusb_free_transfer
PUBLIC 9328 0 libusb_submit_transfer
PUBLIC 96f8 0 libusb_cancel_transfer
PUBLIC a140 0 libusb_transfer_set_stream_id
PUBLIC a148 0 libusb_transfer_get_stream_id
PUBLIC a430 0 libusb_try_lock_events
PUBLIC a4e0 0 libusb_lock_events
PUBLIC a538 0 libusb_unlock_events
PUBLIC a590 0 libusb_event_handling_ok
PUBLIC a630 0 libusb_event_handler_active
PUBLIC a6d8 0 libusb_interrupt_event_handler
PUBLIC a7a8 0 libusb_lock_event_waiters
PUBLIC a7c8 0 libusb_unlock_event_waiters
PUBLIC a7e8 0 libusb_wait_for_event
PUBLIC a848 0 libusb_pollfds_handle_timeouts
PUBLIC a878 0 libusb_get_next_timeout
PUBLIC ab78 0 libusb_handle_events_timeout_completed
PUBLIC ad60 0 libusb_handle_events_timeout
PUBLIC ad68 0 libusb_handle_events
PUBLIC adc0 0 libusb_handle_events_completed
PUBLIC ae18 0 libusb_handle_events_locked
PUBLIC aeb0 0 libusb_set_pollfd_notifiers
PUBLIC b390 0 libusb_get_pollfds
PUBLIC b430 0 libusb_free_pollfds
PUBLIC b598 0 libusb_setlocale
PUBLIC b670 0 libusb_strerror
PUBLIC b900 0 libusb_control_transfer
PUBLIC bb20 0 libusb_bulk_transfer
PUBLIC bb28 0 libusb_interrupt_transfer
STACK CFI INIT 41f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4228 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4268 48 .cfa: sp 0 + .ra: x30
STACK CFI 426c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4274 x19: .cfa -16 + ^
STACK CFI 42ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4300 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4400 38 .cfa: sp 0 + .ra: x30
STACK CFI 4404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 440c x19: .cfa -16 + ^
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4438 9c .cfa: sp 0 + .ra: x30
STACK CFI 443c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4444 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4450 x21: .cfa -16 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 44b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 44d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 44d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44e0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4530 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4558 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4590 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c0 10c .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 45cc x19: .cfa -272 + ^
STACK CFI 468c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4690 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 46d0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4740 90 .cfa: sp 0 + .ra: x30
STACK CFI 4744 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 474c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4758 x21: .cfa -16 + ^
STACK CFI 47bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 47c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 47cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 47d0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 47d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47ec x21: .cfa -16 + ^
STACK CFI 4840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4844 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 485c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4860 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4880 90 .cfa: sp 0 + .ra: x30
STACK CFI 4884 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 488c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4894 x21: .cfa -16 + ^
STACK CFI 48fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4900 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 490c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4910 314 .cfa: sp 0 + .ra: x30
STACK CFI 4914 .cfa: sp 1248 +
STACK CFI 4918 .ra: .cfa -1224 + ^ x29: .cfa -1232 + ^
STACK CFI 4920 x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 492c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^
STACK CFI 4948 x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^
STACK CFI 498c x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 4ad8 x27: x27 x28: x28
STACK CFI 4b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4b08 .cfa: sp 1248 + .ra: .cfa -1224 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x29: .cfa -1232 + ^
STACK CFI 4b20 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 4b50 x27: x27 x28: x28
STACK CFI 4b54 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 4c10 x27: x27 x28: x28
STACK CFI 4c14 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI INIT 4c28 9c .cfa: sp 0 + .ra: x30
STACK CFI 4c2c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4c3c x19: .cfa -256 + ^
STACK CFI 4cbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4cc0 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT 4cc8 80 .cfa: sp 0 + .ra: x30
STACK CFI 4ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d48 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d60 x19: .cfa -16 + ^
STACK CFI 4dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4dcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4dd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e18 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4e1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e40 x21: .cfa -32 + ^
STACK CFI 4e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ed0 128 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4edc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ef0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4ff8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5000 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5008 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5034 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 50b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 50b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 50f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 50f8 10c .cfa: sp 0 + .ra: x30
STACK CFI 50fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5104 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 510c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 513c x23: .cfa -16 + ^
STACK CFI 517c x23: x23
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5198 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 51c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 51cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5200 x23: x23
STACK CFI INIT 5208 200 .cfa: sp 0 + .ra: x30
STACK CFI 520c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5214 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5220 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5234 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 535c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5408 94 .cfa: sp 0 + .ra: x30
STACK CFI 540c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 541c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 546c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5470 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 54a0 8c .cfa: sp 0 + .ra: x30
STACK CFI 54a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5500 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5530 180 .cfa: sp 0 + .ra: x30
STACK CFI 5534 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5544 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5554 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 555c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5570 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 562c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5630 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 568c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5690 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 56b0 184 .cfa: sp 0 + .ra: x30
STACK CFI 56b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 56c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 56d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56e4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 57b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5814 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5838 f4 .cfa: sp 0 + .ra: x30
STACK CFI 583c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5844 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5854 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 587c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 58e8 x19: x19 x20: x20
STACK CFI 590c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5910 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 591c x19: x19 x20: x20
STACK CFI 5928 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 5930 2dc .cfa: sp 0 + .ra: x30
STACK CFI 5938 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5948 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5954 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5970 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ad0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5b24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5b7c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5c10 174 .cfa: sp 0 + .ra: x30
STACK CFI 5c14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5c1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c28 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5c40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5cd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d88 5c .cfa: sp 0 + .ra: x30
STACK CFI 5d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5dac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5de8 cc .cfa: sp 0 + .ra: x30
STACK CFI 5dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5eb8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5ebc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5edc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ef0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5f70 d8 .cfa: sp 0 + .ra: x30
STACK CFI 5f74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f84 x21: .cfa -16 + ^
STACK CFI 5fa0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 600c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6018 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 602c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6048 78 .cfa: sp 0 + .ra: x30
STACK CFI 604c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 605c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 60a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 60c0 6c .cfa: sp 0 + .ra: x30
STACK CFI 60c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 60d4 x19: .cfa -16 + ^
STACK CFI 6110 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 611c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6130 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6144 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 615c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 61b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 61c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 61d8 98 .cfa: sp 0 + .ra: x30
STACK CFI 61dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 620c x21: .cfa -16 + ^
STACK CFI 6250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6254 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6268 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6270 84 .cfa: sp 0 + .ra: x30
STACK CFI 6274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6284 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 62d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 62e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 62f8 84 .cfa: sp 0 + .ra: x30
STACK CFI 62fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 630c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6364 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6374 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6380 84 .cfa: sp 0 + .ra: x30
STACK CFI 6384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 63e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 63f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6408 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 640c .cfa: sp 112 +
STACK CFI 6410 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6418 x27: .cfa -16 + ^
STACK CFI 6420 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 642c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 643c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6478 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 65b8 x25: x25 x26: x26
STACK CFI 65c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 65cc .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 66f8 x25: x25 x26: x26
STACK CFI 6720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 6724 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 6788 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 67b4 x25: x25 x26: x26
STACK CFI 67bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 67c0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 67c8 294 .cfa: sp 0 + .ra: x30
STACK CFI 67cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 67dc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 67ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6818 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 694c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6950 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 69a0 x27: .cfa -48 + ^
STACK CFI 6a08 x27: x27
STACK CFI 6a44 x27: .cfa -48 + ^
STACK CFI 6a54 x27: x27
STACK CFI 6a58 x27: .cfa -48 + ^
STACK CFI INIT 6a60 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b90 d0 .cfa: sp 0 + .ra: x30
STACK CFI 6b94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b9c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6bbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6bc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6c48 x19: x19 x20: x20
STACK CFI 6c4c x21: x21 x22: x22
STACK CFI 6c5c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 6c60 5c .cfa: sp 0 + .ra: x30
STACK CFI 6c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6cc0 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d78 a38 .cfa: sp 0 + .ra: x30
STACK CFI 6d7c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6d8c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 6da4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6dc0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6dcc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 6e4c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 72f0 x25: x25 x26: x26
STACK CFI 72f4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7348 x25: x25 x26: x26
STACK CFI 7360 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7390 x25: x25 x26: x26
STACK CFI 73bc x21: x21 x22: x22
STACK CFI 73c0 x23: x23 x24: x24
STACK CFI 73e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 73ec .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 7420 x25: x25 x26: x26
STACK CFI 744c x23: x23 x24: x24
STACK CFI 7454 x21: x21 x22: x22
STACK CFI 7458 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7588 x25: x25 x26: x26
STACK CFI 758c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7628 x25: x25 x26: x26
STACK CFI 76a4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 76d8 x25: x25 x26: x26
STACK CFI 7718 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7738 x25: x25 x26: x26
STACK CFI 7744 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7750 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7754 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7758 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 77a8 x25: x25 x26: x26
STACK CFI INIT 77b0 74 .cfa: sp 0 + .ra: x30
STACK CFI 77b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77c8 x19: .cfa -32 + ^
STACK CFI 7814 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7818 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7828 54 .cfa: sp 0 + .ra: x30
STACK CFI 782c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 783c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7880 148 .cfa: sp 0 + .ra: x30
STACK CFI 7884 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7894 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 789c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 78a8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 78c0 x25: .cfa -96 + ^
STACK CFI 7968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 796c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 79c8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 79cc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 79dc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 79e8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 79fc x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7a3c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7a48 x27: .cfa -96 + ^
STACK CFI 7ad0 x25: x25 x26: x26
STACK CFI 7ad4 x27: x27
STACK CFI 7afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7b00 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI 7b24 x25: x25 x26: x26
STACK CFI 7b28 x27: x27
STACK CFI 7b34 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI 7b5c x25: x25 x26: x26
STACK CFI 7b60 x27: x27
STACK CFI 7b68 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7b6c x27: .cfa -96 + ^
STACK CFI 7b78 x25: x25 x26: x26
STACK CFI 7b7c x27: x27
STACK CFI INIT 7b80 110 .cfa: sp 0 + .ra: x30
STACK CFI 7b84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7b94 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7b9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7bac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7bbc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7c70 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7c90 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7c94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7ca8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7cb0 x21: .cfa -48 + ^
STACK CFI 7d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7d08 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7d58 2c .cfa: sp 0 + .ra: x30
STACK CFI 7d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d68 x19: .cfa -16 + ^
STACK CFI 7d7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d88 14c .cfa: sp 0 + .ra: x30
STACK CFI 7d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7dac x21: .cfa -32 + ^
STACK CFI 7e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7ed8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ee0 58 .cfa: sp 0 + .ra: x30
STACK CFI 7ee8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ef0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7f00 x21: .cfa -16 + ^
STACK CFI 7f24 x21: x21
STACK CFI 7f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f38 46c .cfa: sp 0 + .ra: x30
STACK CFI 7f3c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7f4c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7f58 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7f6c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 7f94 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7fd4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 81c8 x27: x27 x28: x28
STACK CFI 81f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 81f8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 8224 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 82bc x27: x27 x28: x28
STACK CFI 82e8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8388 x27: x27 x28: x28
STACK CFI 838c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8398 x27: x27 x28: x28
STACK CFI 839c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 83a8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 83ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8408 x19: x19 x20: x20
STACK CFI 840c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8410 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8438 x19: x19 x20: x20
STACK CFI 8464 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 846c x19: x19 x20: x20
STACK CFI INIT 8470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8478 c8 .cfa: sp 0 + .ra: x30
STACK CFI 847c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8490 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84d8 x19: x19 x20: x20
STACK CFI 84dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 84e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8508 x19: x19 x20: x20
STACK CFI 8534 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 853c x19: x19 x20: x20
STACK CFI INIT 8540 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8548 c8 .cfa: sp 0 + .ra: x30
STACK CFI 854c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8560 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 85a8 x19: x19 x20: x20
STACK CFI 85ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 85b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 85d8 x19: x19 x20: x20
STACK CFI 8604 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 860c x19: x19 x20: x20
STACK CFI INIT 8610 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8618 184 .cfa: sp 0 + .ra: x30
STACK CFI 861c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 8624 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 8630 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 8664 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 874c x21: x21 x22: x22
STACK CFI 876c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 8770 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x29: .cfa -336 + ^
STACK CFI 8774 x21: x21 x22: x22
STACK CFI 8778 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 8788 x21: x21 x22: x22
STACK CFI 8798 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI INIT 87a0 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8808 e4 .cfa: sp 0 + .ra: x30
STACK CFI 880c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8814 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8820 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 8830 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 88e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 88f0 148 .cfa: sp 0 + .ra: x30
STACK CFI 88f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 88fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8908 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8924 x23: .cfa -16 + ^
STACK CFI 8978 x23: x23
STACK CFI 897c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8980 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 89a4 x23: x23
STACK CFI 89a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 89ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 89dc x23: x23
STACK CFI 89e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 89e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8a08 x23: x23
STACK CFI 8a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8a10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8a38 160 .cfa: sp 0 + .ra: x30
STACK CFI 8a3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8a44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8ae4 x21: x21 x22: x22
STACK CFI 8aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8af0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b2c x21: x21 x22: x22
STACK CFI 8b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b4c x21: x21 x22: x22
STACK CFI 8b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b70 x21: x21 x22: x22
STACK CFI 8b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b98 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 8b9c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8ba4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8bc0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8bcc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8bdc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8be8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8d2c x19: x19 x20: x20
STACK CFI 8d30 x21: x21 x22: x22
STACK CFI 8d34 x23: x23 x24: x24
STACK CFI 8d58 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8d70 x19: x19 x20: x20
STACK CFI 8d74 x21: x21 x22: x22
STACK CFI 8d78 x23: x23 x24: x24
STACK CFI 8d7c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8ddc x19: x19 x20: x20
STACK CFI 8de0 x21: x21 x22: x22
STACK CFI 8de4 x23: x23 x24: x24
STACK CFI 8de8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8e14 x19: x19 x20: x20
STACK CFI 8e18 x21: x21 x22: x22
STACK CFI 8e1c x23: x23 x24: x24
STACK CFI 8e28 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8e30 x21: x21 x22: x22
STACK CFI 8e34 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8e3c x19: x19 x20: x20
STACK CFI 8e40 x21: x21 x22: x22
STACK CFI 8e44 x23: x23 x24: x24
STACK CFI 8e4c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8e50 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8e54 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8e60 x19: x19 x20: x20
STACK CFI 8e64 x21: x21 x22: x22
STACK CFI 8e68 x23: x23 x24: x24
STACK CFI INIT 8e70 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8e74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8e7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8e84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8e8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8ec4 x25: .cfa -16 + ^
STACK CFI 8f28 x25: x25
STACK CFI 8f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8f40 5c .cfa: sp 0 + .ra: x30
STACK CFI 8f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8fa0 138 .cfa: sp 0 + .ra: x30
STACK CFI 8fa4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8fb0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 90d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 90d8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 90dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 90f8 x21: .cfa -16 + ^
STACK CFI 9148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 914c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 91a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 91a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 91b0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 91f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 91fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9204 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 92a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 92a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92b8 x19: .cfa -16 + ^
STACK CFI 92f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 92fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 931c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9328 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 932c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 933c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9348 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9364 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9388 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9624 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 96f8 114 .cfa: sp 0 + .ra: x30
STACK CFI 96fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 970c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 972c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9738 x23: .cfa -16 + ^
STACK CFI 979c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 97a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9810 190 .cfa: sp 0 + .ra: x30
STACK CFI 9814 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 981c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9828 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9868 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 98a8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 995c x21: x21 x22: x22
STACK CFI 9960 x25: x25 x26: x26
STACK CFI 9984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 9988 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 9990 x21: x21 x22: x22
STACK CFI 9998 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 999c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 99a0 54 .cfa: sp 0 + .ra: x30
STACK CFI 99a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 99ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 99e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 99e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 99f8 748 .cfa: sp 0 + .ra: x30
STACK CFI 99fc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9a04 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9a14 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9a28 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9a7c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 9a88 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9acc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9ccc x27: x27 x28: x28
STACK CFI 9cdc x25: x25 x26: x26
STACK CFI 9ce0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9d3c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9de4 x27: x27 x28: x28
STACK CFI 9de8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9e28 x27: x27 x28: x28
STACK CFI 9e2c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a00c x27: x27 x28: x28
STACK CFI a010 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a03c x27: x27 x28: x28
STACK CFI a040 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a0f8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a0fc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI a100 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a110 x27: x27 x28: x28
STACK CFI a12c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI a130 x27: x27 x28: x28
STACK CFI INIT a140 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a148 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a150 150 .cfa: sp 0 + .ra: x30
STACK CFI a154 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a15c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a168 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a178 x25: .cfa -16 + ^
STACK CFI a218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a21c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT a2a0 84 .cfa: sp 0 + .ra: x30
STACK CFI a2a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a2ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a2ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a328 104 .cfa: sp 0 + .ra: x30
STACK CFI a32c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a334 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a34c x21: .cfa -16 + ^
STACK CFI a38c x21: x21
STACK CFI a390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a394 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a3c8 x21: x21
STACK CFI a3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a3d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a424 x21: x21
STACK CFI a428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a430 b0 .cfa: sp 0 + .ra: x30
STACK CFI a434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a43c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a444 x21: .cfa -16 + ^
STACK CFI a484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a488 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a49c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a4cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a4d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a4e0 58 .cfa: sp 0 + .ra: x30
STACK CFI a4e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a4ec x19: .cfa -16 + ^
STACK CFI a50c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a510 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a534 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a538 54 .cfa: sp 0 + .ra: x30
STACK CFI a53c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a544 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a57c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a590 9c .cfa: sp 0 + .ra: x30
STACK CFI a594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a59c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a5cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a5d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a630 a4 .cfa: sp 0 + .ra: x30
STACK CFI a634 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a63c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a644 x21: .cfa -16 + ^
STACK CFI a674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a678 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a6d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a6d8 cc .cfa: sp 0 + .ra: x30
STACK CFI a6dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a6e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a73c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a768 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a7a8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7e8 60 .cfa: sp 0 + .ra: x30
STACK CFI a7ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a81c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a820 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a844 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a848 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a878 250 .cfa: sp 0 + .ra: x30
STACK CFI a87c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a884 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a894 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a8d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a8dc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI a8e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a950 x23: x23 x24: x24
STACK CFI a954 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a9ec x23: x23 x24: x24
STACK CFI aa00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa50 x23: x23 x24: x24
STACK CFI aa58 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa8c x23: x23 x24: x24
STACK CFI aa90 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aabc x23: x23 x24: x24
STACK CFI aac4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT aac8 b0 .cfa: sp 0 + .ra: x30
STACK CFI aacc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aad4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aae4 x21: .cfa -48 + ^
STACK CFI ab50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ab54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT ab78 1e4 .cfa: sp 0 + .ra: x30
STACK CFI ab7c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ab84 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ab90 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI aba8 x25: .cfa -48 + ^
STACK CFI abc8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ac44 x21: x21 x22: x22
STACK CFI ac6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ac70 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI aca4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI acf8 x21: x21 x22: x22
STACK CFI acfc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ad04 x21: x21 x22: x22
STACK CFI ad08 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ad10 x21: x21 x22: x22
STACK CFI ad14 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ad50 x21: x21 x22: x22
STACK CFI ad58 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT ad60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad68 54 .cfa: sp 0 + .ra: x30
STACK CFI ad6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ad7c x19: .cfa -48 + ^
STACK CFI adb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI adb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT adc0 54 .cfa: sp 0 + .ra: x30
STACK CFI adc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI add4 x19: .cfa -48 + ^
STACK CFI ae0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ae10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT ae18 94 .cfa: sp 0 + .ra: x30
STACK CFI ae1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ae24 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ae40 x21: .cfa -48 + ^
STACK CFI ae88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ae8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT aeb0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT aed8 dc .cfa: sp 0 + .ra: x30
STACK CFI aedc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI aee4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI aef0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI aef8 x23: .cfa -16 + ^
STACK CFI afa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI afac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT afb8 130 .cfa: sp 0 + .ra: x30
STACK CFI afbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI afcc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI afe0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b0a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b0d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b0d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b0e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT b0e8 220 .cfa: sp 0 + .ra: x30
STACK CFI b0ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b0f8 x25: .cfa -16 + ^
STACK CFI b100 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b108 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b114 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI b238 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI b288 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT b308 88 .cfa: sp 0 + .ra: x30
STACK CFI b30c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b318 x19: .cfa -16 + ^
STACK CFI b38c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b390 9c .cfa: sp 0 + .ra: x30
STACK CFI b394 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b39c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b41c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b430 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b438 15c .cfa: sp 0 + .ra: x30
STACK CFI b43c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b44c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b45c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b468 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT b598 d8 .cfa: sp 0 + .ra: x30
STACK CFI b5a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b5e8 x21: .cfa -16 + ^
STACK CFI b628 x21: x21
STACK CFI b634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b638 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b644 x21: x21
STACK CFI b648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b64c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b65c x21: .cfa -16 + ^
STACK CFI b660 x21: x21
STACK CFI b668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b670 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b6b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT b6e0 c4 .cfa: sp 0 + .ra: x30
STACK CFI b6e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b6ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b704 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b71c x23: .cfa -16 + ^
STACK CFI b780 x23: x23
STACK CFI b798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b79c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b7a0 x23: x23
STACK CFI INIT b7a8 154 .cfa: sp 0 + .ra: x30
STACK CFI b7ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b7b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b7c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b7d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b7e0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI b7f0 x27: .cfa -32 + ^
STACK CFI b8b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b8b8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT b900 220 .cfa: sp 0 + .ra: x30
STACK CFI b904 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b90c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b91c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b924 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b94c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b958 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ba74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ba78 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT bb20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb30 e0 .cfa: sp 0 + .ra: x30
STACK CFI bb34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bb88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bb8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bbbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bbc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bc10 e4 .cfa: sp 0 + .ra: x30
STACK CFI bc14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bc24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI bc2c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bc50 x23: .cfa -48 + ^
STACK CFI bcec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI bcf0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT bcf8 18 .cfa: sp 0 + .ra: x30
STACK CFI bcfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bd0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bd10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT bd20 3c .cfa: sp 0 + .ra: x30
STACK CFI bd24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd2c x19: .cfa -16 + ^
STACK CFI bd4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bd50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bd58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bd60 f8 .cfa: sp 0 + .ra: x30
STACK CFI bd64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bd74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bd94 x21: .cfa -48 + ^
STACK CFI bddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI bde0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT be58 90 .cfa: sp 0 + .ra: x30
STACK CFI be5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI be64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI beac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI bee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bee8 b0 .cfa: sp 0 + .ra: x30
STACK CFI beec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bef4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bf70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI bf84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT bf98 cc .cfa: sp 0 + .ra: x30
STACK CFI bf9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bfac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bfcc x21: .cfa -32 + ^
STACK CFI c04c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c050 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT c068 d0 .cfa: sp 0 + .ra: x30
STACK CFI c06c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c07c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c0a0 x21: .cfa -32 + ^
STACK CFI c120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c124 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT c138 dc .cfa: sp 0 + .ra: x30
STACK CFI c13c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c144 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c160 x21: .cfa -32 + ^
STACK CFI c1cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c1d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI c1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c1ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI c200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c204 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT c218 12c .cfa: sp 0 + .ra: x30
STACK CFI c21c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI c22c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI c238 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI c318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c31c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT c348 100 .cfa: sp 0 + .ra: x30
STACK CFI c34c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI c35c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI c36c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI c3dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c3e0 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT c448 54 .cfa: sp 0 + .ra: x30
STACK CFI c44c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c454 x19: .cfa -16 + ^
STACK CFI c470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c4a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT c4e0 158 .cfa: sp 0 + .ra: x30
STACK CFI c4e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c4f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI c500 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c514 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c568 x21: x21 x22: x22
STACK CFI c56c x23: x23 x24: x24
STACK CFI c57c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI c580 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI c5b0 x21: x21 x22: x22
STACK CFI c5b4 x23: x23 x24: x24
STACK CFI c5bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI c5c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI c5f8 x21: x21 x22: x22
STACK CFI c5fc x23: x23 x24: x24
STACK CFI c604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI c608 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI c60c x21: x21 x22: x22
STACK CFI c610 x23: x23 x24: x24
STACK CFI c618 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c620 x21: x21 x22: x22
STACK CFI c624 x23: x23 x24: x24
STACK CFI c628 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c630 x21: x21 x22: x22
STACK CFI c634 x23: x23 x24: x24
STACK CFI INIT c638 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c650 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c670 e0 .cfa: sp 0 + .ra: x30
STACK CFI c674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c67c x19: .cfa -16 + ^
STACK CFI c6b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c6b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c724 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c72c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c750 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c758 68 .cfa: sp 0 + .ra: x30
STACK CFI c75c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c764 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c7a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c7b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c7b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c7c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7d8 e0 .cfa: sp 0 + .ra: x30
STACK CFI c7e0 .cfa: sp 4160 +
STACK CFI c7f4 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI c804 x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI c810 x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI c878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c87c .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT c8b8 1bc .cfa: sp 0 + .ra: x30
STACK CFI c8bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c8c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c8d0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c8f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI c924 x25: .cfa -48 + ^
STACK CFI c974 x25: x25
STACK CFI c9a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c9a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI c9c8 x25: x25
STACK CFI c9d0 x25: .cfa -48 + ^
STACK CFI c9f0 x25: x25
STACK CFI c9f4 x25: .cfa -48 + ^
STACK CFI ca14 x25: x25
STACK CFI ca18 x25: .cfa -48 + ^
STACK CFI ca44 x25: x25
STACK CFI ca48 x25: .cfa -48 + ^
STACK CFI ca68 x25: x25
STACK CFI ca70 x25: .cfa -48 + ^
STACK CFI INIT ca78 1d8 .cfa: sp 0 + .ra: x30
STACK CFI ca80 .cfa: sp 4176 +
STACK CFI ca94 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI caa4 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI cabc x23: .cfa -4128 + ^ x24: .cfa -4120 + ^
STACK CFI cadc x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI cb3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cb40 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x29: .cfa -4176 + ^
STACK CFI INIT cc50 1e8 .cfa: sp 0 + .ra: x30
STACK CFI cc58 .cfa: sp 4208 +
STACK CFI cc64 .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI cc70 x21: .cfa -4176 + ^ x22: .cfa -4168 + ^
STACK CFI cc90 x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI cc98 x25: .cfa -4144 + ^
STACK CFI cd44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI cd48 .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x25: .cfa -4144 + ^ x29: .cfa -4208 + ^
STACK CFI INIT ce38 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT cea0 6c .cfa: sp 0 + .ra: x30
STACK CFI cea4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ceac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cec8 x21: .cfa -16 + ^
STACK CFI cef4 x21: x21
STACK CFI cf08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT cf10 11c .cfa: sp 0 + .ra: x30
STACK CFI cf14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cf24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cf9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cfa0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI cfa4 x21: .cfa -64 + ^
STACK CFI cfe8 x21: x21
STACK CFI d018 x21: .cfa -64 + ^
STACK CFI d020 x21: x21
STACK CFI d028 x21: .cfa -64 + ^
STACK CFI INIT d030 410 .cfa: sp 0 + .ra: x30
STACK CFI d034 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI d03c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d044 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI d058 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d10c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d1cc x27: x27 x28: x28
STACK CFI d214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d218 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI d234 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d270 x27: x27 x28: x28
STACK CFI d288 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d298 x27: x27 x28: x28
STACK CFI d29c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d2ac x27: x27 x28: x28
STACK CFI d2c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d2c4 x27: x27 x28: x28
STACK CFI d2c8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d318 x27: x27 x28: x28
STACK CFI d350 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d36c x27: x27 x28: x28
STACK CFI d370 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d398 x27: x27 x28: x28
STACK CFI d3d8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d3fc x27: x27 x28: x28
STACK CFI d400 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d404 x27: x27 x28: x28
STACK CFI d40c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d434 x27: x27 x28: x28
STACK CFI d43c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT d440 60 .cfa: sp 0 + .ra: x30
STACK CFI d444 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d44c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d454 x21: .cfa -16 + ^
STACK CFI d47c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI d49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT d4a0 110 .cfa: sp 0 + .ra: x30
STACK CFI d4a4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI d4b4 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI d4e4 x21: .cfa -288 + ^
STACK CFI d580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d584 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT d5b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT d5c0 1ac .cfa: sp 0 + .ra: x30
STACK CFI d5c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d5cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d5d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d668 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI d66c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d688 x25: .cfa -16 + ^
STACK CFI d708 x23: x23 x24: x24
STACK CFI d70c x25: x25
STACK CFI d720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d724 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI d768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT d770 c8 .cfa: sp 0 + .ra: x30
STACK CFI d774 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d77c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d788 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d7d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d7dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI d834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT d838 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT d860 2c .cfa: sp 0 + .ra: x30
STACK CFI d864 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT d890 188 .cfa: sp 0 + .ra: x30
STACK CFI d894 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d89c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d8ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d8b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d8cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d8f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d9a8 x19: x19 x20: x20
STACK CFI d9b0 x23: x23 x24: x24
STACK CFI d9b4 x25: x25 x26: x26
STACK CFI d9b8 x27: x27 x28: x28
STACK CFI d9bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI d9c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI d9e8 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI d9f8 x25: x25 x26: x26
STACK CFI d9fc x27: x27 x28: x28
STACK CFI da00 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI da04 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI da08 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI da0c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI da10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI da14 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT da18 b8 .cfa: sp 0 + .ra: x30
STACK CFI da1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI da78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI da7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI da98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI daac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI dac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dacc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT dad0 8c .cfa: sp 0 + .ra: x30
STACK CFI dad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dae0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI db34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI db4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT db60 474 .cfa: sp 0 + .ra: x30
STACK CFI db64 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI db6c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI db78 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI db88 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI dbc0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI dbc8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI de38 x23: x23 x24: x24
STACK CFI de3c x27: x27 x28: x28
STACK CFI de50 x21: x21 x22: x22
STACK CFI de54 x25: x25 x26: x26
STACK CFI de58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI de5c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI de60 x23: x23 x24: x24
STACK CFI de68 x27: x27 x28: x28
STACK CFI de74 x21: x21 x22: x22
STACK CFI de78 x25: x25 x26: x26
STACK CFI de7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI de80 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI df48 x21: x21 x22: x22
STACK CFI df4c x23: x23 x24: x24
STACK CFI df50 x25: x25 x26: x26
STACK CFI df54 x27: x27 x28: x28
STACK CFI df58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI df5c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI df98 x23: x23 x24: x24
STACK CFI df9c x27: x27 x28: x28
STACK CFI dfa4 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI dfac x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI dfb4 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI dfbc x23: x23 x24: x24
STACK CFI dfc0 x27: x27 x28: x28
STACK CFI dfc4 x25: x25 x26: x26
STACK CFI dfc8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI dfcc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI dfd0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT dfd8 558 .cfa: sp 0 + .ra: x30
STACK CFI dfdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI dfe8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e004 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI e010 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e050 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e1f4 x25: x25 x26: x26
STACK CFI e1fc x21: x21 x22: x22
STACK CFI e200 x23: x23 x24: x24
STACK CFI e210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI e214 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI e220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI e224 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI e260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI e264 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI e26c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e348 x21: x21 x22: x22
STACK CFI e34c x23: x23 x24: x24
STACK CFI e350 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e380 x21: x21 x22: x22
STACK CFI e384 x23: x23 x24: x24
STACK CFI e388 x25: x25 x26: x26
STACK CFI e38c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e390 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e41c x21: x21 x22: x22
STACK CFI e420 x23: x23 x24: x24
STACK CFI e424 x25: x25 x26: x26
STACK CFI e428 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e42c x21: x21 x22: x22
STACK CFI e430 x23: x23 x24: x24
STACK CFI e434 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e484 x21: x21 x22: x22
STACK CFI e488 x23: x23 x24: x24
STACK CFI e48c x25: x25 x26: x26
STACK CFI e490 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e4d8 x25: x25 x26: x26
STACK CFI e4e0 x21: x21 x22: x22
STACK CFI e4e4 x23: x23 x24: x24
STACK CFI e4e8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e4f0 x21: x21 x22: x22
STACK CFI e4f4 x23: x23 x24: x24
STACK CFI e4f8 x25: x25 x26: x26
STACK CFI e4fc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e510 x21: x21 x22: x22
STACK CFI e514 x23: x23 x24: x24
STACK CFI e518 x25: x25 x26: x26
STACK CFI e51c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e520 x25: x25 x26: x26
STACK CFI e528 x21: x21 x22: x22
STACK CFI e52c x23: x23 x24: x24
STACK CFI INIT e530 ba4 .cfa: sp 0 + .ra: x30
STACK CFI e534 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI e544 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e554 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI e588 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI e590 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e684 x21: x21 x22: x22
STACK CFI e688 x25: x25 x26: x26
STACK CFI e6ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI e6f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI e708 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI e820 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e83c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e8c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI e9b4 x27: x27 x28: x28
STACK CFI e9d8 x21: x21 x22: x22
STACK CFI e9dc x25: x25 x26: x26
STACK CFI e9e0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ea38 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI eca0 x21: x21 x22: x22
STACK CFI eca4 x25: x25 x26: x26
STACK CFI eca8 x27: x27 x28: x28
STACK CFI ecac x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ece8 x27: x27 x28: x28
STACK CFI ed2c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ee0c x21: x21 x22: x22
STACK CFI ee10 x25: x25 x26: x26
STACK CFI ee14 x27: x27 x28: x28
STACK CFI ee18 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ee4c x27: x27 x28: x28
STACK CFI ee84 x21: x21 x22: x22
STACK CFI ee88 x25: x25 x26: x26
STACK CFI ee8c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ef7c x21: x21 x22: x22
STACK CFI ef80 x25: x25 x26: x26
STACK CFI ef84 x27: x27 x28: x28
STACK CFI ef88 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI efb4 x21: x21 x22: x22
STACK CFI efb8 x25: x25 x26: x26
STACK CFI efbc x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI efcc x21: x21 x22: x22
STACK CFI efd0 x25: x25 x26: x26
STACK CFI efd4 x27: x27 x28: x28
STACK CFI efd8 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f050 x27: x27 x28: x28
STACK CFI f090 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f09c x27: x27 x28: x28
STACK CFI f0bc x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI f0c0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI f0c4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f0c8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f0cc x27: x27 x28: x28
STACK CFI f0d0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT f0d8 60 .cfa: sp 0 + .ra: x30
STACK CFI f0dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f0e4 x19: .cfa -16 + ^
STACK CFI f100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f104 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT f138 70 .cfa: sp 0 + .ra: x30
STACK CFI f13c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f14c x19: .cfa -16 + ^
STACK CFI f170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f174 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT f1a8 80 .cfa: sp 0 + .ra: x30
STACK CFI f1ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f1b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f1ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f204 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f228 298 .cfa: sp 0 + .ra: x30
STACK CFI f22c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI f234 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI f240 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI f254 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI f2e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f2e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI f320 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f3c0 x25: x25 x26: x26
STACK CFI f3c4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f3c8 x25: x25 x26: x26
STACK CFI f3cc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f3f8 x25: x25 x26: x26
STACK CFI f3fc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f428 x25: x25 x26: x26
STACK CFI f4bc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT f4c0 90 .cfa: sp 0 + .ra: x30
STACK CFI f4c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f4cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f4e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI f534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f538 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI f54c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT f550 d8 .cfa: sp 0 + .ra: x30
STACK CFI f554 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f55c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f56c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f584 x23: .cfa -64 + ^
STACK CFI f5fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f600 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT f628 bc .cfa: sp 0 + .ra: x30
STACK CFI f62c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f634 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f64c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f658 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI f664 x25: .cfa -16 + ^
STACK CFI f6ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI f6b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI f6e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT f6e8 5a4 .cfa: sp 0 + .ra: x30
STACK CFI f6ec .cfa: sp 528 +
STACK CFI f6f0 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI f6f8 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI f708 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI f728 x21: .cfa -496 + ^ x22: .cfa -488 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^
STACK CFI f9b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI f9bc .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x29: .cfa -528 + ^
STACK CFI INIT fc90 238 .cfa: sp 0 + .ra: x30
STACK CFI fc98 .cfa: sp 8304 +
STACK CFI fc9c .ra: .cfa -8296 + ^ x29: .cfa -8304 + ^
STACK CFI fca4 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI fcb4 x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI fcc4 x25: .cfa -8240 + ^ x26: .cfa -8232 + ^
STACK CFI fcd8 x27: .cfa -8224 + ^
STACK CFI fcf8 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI fdbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI fdc0 .cfa: sp 8304 + .ra: .cfa -8296 + ^ x19: .cfa -8288 + ^ x20: .cfa -8280 + ^ x21: .cfa -8272 + ^ x22: .cfa -8264 + ^ x23: .cfa -8256 + ^ x24: .cfa -8248 + ^ x25: .cfa -8240 + ^ x26: .cfa -8232 + ^ x27: .cfa -8224 + ^ x29: .cfa -8304 + ^
STACK CFI INIT fec8 17c .cfa: sp 0 + .ra: x30
STACK CFI fecc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fedc x23: .cfa -48 + ^
STACK CFI fee4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI fef4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ffcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ffd0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10048 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 1004c .cfa: sp 144 +
STACK CFI 10050 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 10058 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 10064 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1007c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1008c x27: .cfa -48 + ^
STACK CFI 10134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 10138 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 10410 84 .cfa: sp 0 + .ra: x30
STACK CFI 10414 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1041c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10428 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10438 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10498 dc .cfa: sp 0 + .ra: x30
STACK CFI 1049c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 104a8 x25: .cfa -16 + ^
STACK CFI 104b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 104c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 104dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1055c x23: x23 x24: x24
STACK CFI 10570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI INIT 10578 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 1057c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10584 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1058c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10598 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 105b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 105d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10668 x19: x19 x20: x20
STACK CFI 1066c x25: x25 x26: x26
STACK CFI 10670 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 106a4 x19: x19 x20: x20
STACK CFI 106a8 x25: x25 x26: x26
STACK CFI 106c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 106cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10740 e0 .cfa: sp 0 + .ra: x30
STACK CFI 10744 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10754 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10760 x21: .cfa -16 + ^
STACK CFI 10790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10794 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 107e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 107ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10820 a0 .cfa: sp 0 + .ra: x30
STACK CFI 10824 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1082c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10834 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10844 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10850 x25: .cfa -16 + ^
STACK CFI 108a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 108a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 108bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 108c0 148 .cfa: sp 0 + .ra: x30
STACK CFI 108c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 108cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 108d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10964 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 1097c x23: .cfa -48 + ^
STACK CFI 109b4 x23: x23
STACK CFI 109b8 x23: .cfa -48 + ^
STACK CFI 109d8 x23: x23
STACK CFI 109dc x23: .cfa -48 + ^
STACK CFI 109fc x23: x23
STACK CFI 10a04 x23: .cfa -48 + ^
STACK CFI INIT 10a08 178 .cfa: sp 0 + .ra: x30
STACK CFI 10a0c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10a18 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10a28 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10a3c x25: .cfa -48 + ^
STACK CFI 10a6c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 10b44 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10b80 328 .cfa: sp 0 + .ra: x30
STACK CFI 10b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10b8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10bec x21: .cfa -16 + ^
STACK CFI 10c74 x21: x21
STACK CFI 10c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10c80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10cc8 x21: x21
STACK CFI 10d4c x21: .cfa -16 + ^
STACK CFI 10d70 x21: x21
STACK CFI 10d74 x21: .cfa -16 + ^
STACK CFI 10dac x21: x21
STACK CFI 10df0 x21: .cfa -16 + ^
STACK CFI 10e1c x21: x21
STACK CFI 10e20 x21: .cfa -16 + ^
STACK CFI 10e4c x21: x21
STACK CFI 10e50 x21: .cfa -16 + ^
STACK CFI 10e7c x21: x21
STACK CFI 10ea4 x21: .cfa -16 + ^
STACK CFI INIT 10ea8 180 .cfa: sp 0 + .ra: x30
STACK CFI 10eac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10eb8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10ee4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 10f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10f94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11028 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 1102c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11034 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1103c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 11078 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 110b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 110bc x27: .cfa -48 + ^
STACK CFI 11130 x23: x23 x24: x24
STACK CFI 11134 x27: x27
STACK CFI 11140 x19: x19 x20: x20
STACK CFI 11164 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 11168 .cfa: sp 128 + .ra: .cfa -120 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 111b4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 111b8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 111bc x27: .cfa -48 + ^
STACK CFI 111c0 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27
STACK CFI 111c4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 111c8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 111cc x27: .cfa -48 + ^
STACK CFI INIT 111d0 9c .cfa: sp 0 + .ra: x30
STACK CFI 111d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 111dc x23: .cfa -16 + ^
STACK CFI 111e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 111f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
