MODULE Linux arm64 8D8599BFBF61F8F5DE262C9FBD35B1E30 libdrm_nouveau.so.2
INFO CODE_ID BF99858D61BFF5F8DE262C9FBD35B1E3E37290FB
PUBLIC 1c50 0 nouveau_object_mthd
PUBLIC 1db8 0 nouveau_object_sclass_put
PUBLIC 1de0 0 nouveau_object_sclass_get
PUBLIC 1f40 0 nouveau_object_mclass
PUBLIC 2030 0 nouveau_object_new
PUBLIC 20e0 0 nouveau_object_del
PUBLIC 2118 0 nouveau_drm_del
PUBLIC 2140 0 nouveau_drm_new
PUBLIC 2298 0 nouveau_device_open_existing
PUBLIC 22a0 0 nouveau_device_del
PUBLIC 2358 0 nouveau_getparam
PUBLIC 23e8 0 nouveau_device_new
PUBLIC 26b8 0 nouveau_device_wrap
PUBLIC 2788 0 nouveau_device_open
PUBLIC 27f8 0 nouveau_setparam
PUBLIC 2880 0 nouveau_client_new
PUBLIC 2998 0 nouveau_client_del
PUBLIC 2a30 0 nouveau_bo_new
PUBLIC 2ae8 0 nouveau_bo_wrap
PUBLIC 2b48 0 nouveau_bo_name_ref
PUBLIC 2c58 0 nouveau_bo_name_get
PUBLIC 2d30 0 nouveau_bo_ref
PUBLIC 2e80 0 nouveau_bo_prime_handle_ref
PUBLIC 2f70 0 nouveau_bo_set_prime
PUBLIC 2ff0 0 nouveau_bo_wait
PUBLIC 3100 0 nouveau_bo_map
PUBLIC 3698 0 nouveau_pushbuf_del
PUBLIC 3830 0 nouveau_pushbuf_new
PUBLIC 3a40 0 nouveau_pushbuf_bufctx
PUBLIC 3a50 0 nouveau_pushbuf_data
PUBLIC 43a8 0 nouveau_pushbuf_space
PUBLIC 4830 0 nouveau_pushbuf_refn
PUBLIC 4900 0 nouveau_pushbuf_reloc
PUBLIC 4938 0 nouveau_pushbuf_validate
PUBLIC 4940 0 nouveau_pushbuf_refd
PUBLIC 49d8 0 nouveau_pushbuf_kick
PUBLIC 4a18 0 nouveau_bufctx_new
PUBLIC 4a90 0 nouveau_bufctx_reset
PUBLIC 4ae8 0 nouveau_bufctx_del
PUBLIC 4b60 0 nouveau_bufctx_refn
PUBLIC 4bf8 0 nouveau_bufctx_mthd
STACK CFI INIT 16e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1718 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1758 48 .cfa: sp 0 + .ra: x30
STACK CFI 175c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1764 x19: .cfa -16 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a8 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1830 17c .cfa: sp 0 + .ra: x30
STACK CFI 1834 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 183c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1844 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1854 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1868 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 194c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 19b0 84 .cfa: sp 0 + .ra: x30
STACK CFI 19b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a38 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 1a3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1a44 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1a54 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1a5c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1a74 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1a7c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1b48 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1be0 6c .cfa: sp 0 + .ra: x30
STACK CFI 1be4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bec x21: .cfa -16 + ^
STACK CFI 1bf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1c50 168 .cfa: sp 0 + .ra: x30
STACK CFI 1c54 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1c5c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1c70 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1c78 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1c90 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1d4c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 1db8 28 .cfa: sp 0 + .ra: x30
STACK CFI 1dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dc4 x19: .cfa -16 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1de0 160 .cfa: sp 0 + .ra: x30
STACK CFI 1de4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1df0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1df8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1e00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1f38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1f40 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1f44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2014 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2030 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2034 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 203c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2048 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2054 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2060 x25: .cfa -16 + ^
STACK CFI 20b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 20b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 20d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 20d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 20e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 20e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2118 28 .cfa: sp 0 + .ra: x30
STACK CFI 211c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2124 x19: .cfa -16 + ^
STACK CFI 213c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2140 158 .cfa: sp 0 + .ra: x30
STACK CFI 2144 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 214c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 215c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2204 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2230 x23: .cfa -32 + ^
STACK CFI 223c x23: x23
STACK CFI 228c x23: .cfa -32 + ^
STACK CFI 2290 x23: x23
STACK CFI INIT 2298 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 22a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 22ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 22b4 x21: .cfa -32 + ^
STACK CFI 2340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2344 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2358 90 .cfa: sp 0 + .ra: x30
STACK CFI 235c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2368 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 23d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23e8 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 23f8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2404 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2414 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2504 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 26b8 cc .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 26c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 26d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26e4 x23: .cfa -48 + ^
STACK CFI 2728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 272c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2788 6c .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2794 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 27e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 27f8 88 .cfa: sp 0 + .ra: x30
STACK CFI 27fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 280c x19: .cfa -48 + ^
STACK CFI 286c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2870 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2880 118 .cfa: sp 0 + .ra: x30
STACK CFI 2884 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 288c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2894 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 28a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 294c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2950 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2998 98 .cfa: sp 0 + .ra: x30
STACK CFI 299c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2a1c x21: x21 x22: x22
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2a30 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2a34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2a3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2a48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2a58 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2a64 x25: .cfa -16 + ^
STACK CFI 2ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2abc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2ae0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2ae8 5c .cfa: sp 0 + .ra: x30
STACK CFI 2aec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2af4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2b48 110 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b54 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2b64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b78 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2c30 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2c58 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2c5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ce4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d30 14c .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2dc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2dd0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e34 x23: x23 x24: x24
STACK CFI 2e38 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e74 x23: x23 x24: x24
STACK CFI 2e78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2e80 ec .cfa: sp 0 + .ra: x30
STACK CFI 2e84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2e8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e94 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2ea8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f4c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2f70 7c .cfa: sp 0 + .ra: x30
STACK CFI 2f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2ff0 10c .cfa: sp 0 + .ra: x30
STACK CFI 2ff4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ffc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3008 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3100 bc .cfa: sp 0 + .ra: x30
STACK CFI 3104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3110 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3118 x21: .cfa -16 + ^
STACK CFI 3158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 315c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 319c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 31b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 31c0 200 .cfa: sp 0 + .ra: x30
STACK CFI 31c4 .cfa: sp 160 +
STACK CFI 31d4 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 31dc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 31e8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 31f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3214 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 32ec x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 33a4 x27: x27 x28: x28
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 33c0 17c .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3540 154 .cfa: sp 0 + .ra: x30
STACK CFI 3544 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 354c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3554 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3564 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3588 x27: .cfa -32 + ^
STACK CFI 35a8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3658 x25: x25 x26: x26
STACK CFI 3688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 368c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 3690 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 3698 198 .cfa: sp 0 + .ra: x30
STACK CFI 369c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 36a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 36ac x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 36b8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 36dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 36e0 x27: .cfa -32 + ^
STACK CFI 379c x19: x19 x20: x20 x27: x27
STACK CFI 37fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3800 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 381c x19: x19 x20: x20
STACK CFI 3820 x27: x27
STACK CFI 3828 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 382c x27: .cfa -32 + ^
STACK CFI INIT 3830 210 .cfa: sp 0 + .ra: x30
STACK CFI 3834 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 383c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3848 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3854 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3860 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3874 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 3910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3914 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3a40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a50 12c .cfa: sp 0 + .ra: x30
STACK CFI 3a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a78 x23: .cfa -16 + ^
STACK CFI 3b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3b58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3b80 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3b8c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3bb0 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 3bec x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3c18 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3d6c x19: x19 x20: x20
STACK CFI 3d74 x23: x23 x24: x24
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3da8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 3e04 x19: x19 x20: x20
STACK CFI 3e08 x23: x23 x24: x24
STACK CFI 3e18 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3e1c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI INIT 3e20 21c .cfa: sp 0 + .ra: x30
STACK CFI 3e24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e2c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3e50 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3e5c x27: .cfa -32 + ^
STACK CFI 4008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 400c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4040 368 .cfa: sp 0 + .ra: x30
STACK CFI 4044 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 404c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4058 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4074 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4090 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 40b4 x27: .cfa -16 + ^
STACK CFI 40ec x27: x27
STACK CFI 4114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4118 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 411c x27: x27
STACK CFI 420c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4210 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 4240 x27: x27
STACK CFI 42f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 42f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4358 x27: .cfa -16 + ^
STACK CFI 4360 x27: x27
STACK CFI 4364 x27: .cfa -16 + ^
STACK CFI 4394 x27: x27
STACK CFI INIT 43a8 258 .cfa: sp 0 + .ra: x30
STACK CFI 43ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 44d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4600 22c .cfa: sp 0 + .ra: x30
STACK CFI 4604 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 460c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4614 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4620 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 462c x27: .cfa -16 + ^
STACK CFI 4634 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4758 x23: x23 x24: x24
STACK CFI 475c x25: x25 x26: x26
STACK CFI 4760 x27: x27
STACK CFI 4764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4768 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 47e0 x23: x23 x24: x24
STACK CFI 47e4 x25: x25 x26: x26
STACK CFI 47e8 x27: x27
STACK CFI 47ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47f0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 47fc x23: x23 x24: x24
STACK CFI 4800 x25: x25 x26: x26
STACK CFI 4804 x27: x27
STACK CFI 481c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4824 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4830 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4834 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 483c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4844 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4858 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4860 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 48f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 48f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4900 34 .cfa: sp 0 + .ra: x30
STACK CFI 4904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 490c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4938 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4940 94 .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 49d8 3c .cfa: sp 0 + .ra: x30
STACK CFI 49e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49ec x19: .cfa -16 + ^
STACK CFI 4a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a18 78 .cfa: sp 0 + .ra: x30
STACK CFI 4a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a38 x21: .cfa -16 + ^
STACK CFI 4a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4a90 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae8 74 .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4af4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b60 94 .cfa: sp 0 + .ra: x30
STACK CFI 4b64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4bd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4bf8 78 .cfa: sp 0 + .ra: x30
STACK CFI 4bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4c10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4c70 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4c74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4d58 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4d5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4e38 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4e3c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4e48 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4f08 dc .cfa: sp 0 + .ra: x30
STACK CFI 4f0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4f1c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fc0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4fe8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4fec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4ff8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI INIT 50d0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 50d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 511c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5180 b4 .cfa: sp 0 + .ra: x30
STACK CFI 5184 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5194 x19: .cfa -32 + ^
STACK CFI 5214 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5218 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5238 e8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5320 ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 53d0 194 .cfa: sp 0 + .ra: x30
STACK CFI 53d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 53e0 x21: .cfa -80 + ^
STACK CFI 53e8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 54e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 54ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
