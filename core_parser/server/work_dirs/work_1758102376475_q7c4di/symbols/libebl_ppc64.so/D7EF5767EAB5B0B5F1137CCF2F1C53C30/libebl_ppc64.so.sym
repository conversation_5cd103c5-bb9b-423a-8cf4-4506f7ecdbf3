MODULE Linux arm64 D7EF5767EAB5B0B5F1137CCF2F1C53C30 libebl_ppc64.so
INFO CODE_ID 6757EFD7B5EAB5B0F1137CCF2F1C53C387CCEB69
PUBLIC 1730 0 ppc64_init
STACK CFI INIT 1548 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1578 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 15bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15c4 x19: .cfa -16 + ^
STACK CFI 15fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1600 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1608 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1638 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1660 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1670 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1680 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1690 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1694 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 169c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1708 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1730 25c .cfa: sp 0 + .ra: x30
STACK CFI 1734 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1740 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 174c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 17c0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 189c x21: x21 x22: x22
STACK CFI 18c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 18c8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 18e0 x25: .cfa -160 + ^
STACK CFI 1968 x25: x25
STACK CFI 1970 x21: x21 x22: x22
STACK CFI 1978 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x25: .cfa -160 + ^
STACK CFI 197c x25: x25
STACK CFI 1980 x21: x21 x22: x22
STACK CFI 1984 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1988 x25: .cfa -160 + ^
STACK CFI INIT 1990 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d8 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a50 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a68 90 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a84 x21: .cfa -32 + ^
STACK CFI 1ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1ac8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1af8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b28 3cc .cfa: sp 0 + .ra: x30
STACK CFI 1b2c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1b34 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1b44 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1b6c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1c40 x21: x21 x22: x22
STACK CFI 1c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1c70 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 1ce8 x21: x21 x22: x22
STACK CFI 1cec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1d0c x21: x21 x22: x22
STACK CFI 1d10 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1d3c x25: .cfa -96 + ^
STACK CFI 1d6c x25: x25
STACK CFI 1dc0 x21: x21 x22: x22
STACK CFI 1dc4 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 1dcc x25: x25
STACK CFI 1df4 x21: x21 x22: x22
STACK CFI 1df8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1dfc x21: x21 x22: x22
STACK CFI 1e00 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 1e30 x25: x25
STACK CFI 1e44 x21: x21 x22: x22
STACK CFI 1e4c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 1e78 x25: x25
STACK CFI 1ed8 x25: .cfa -96 + ^
STACK CFI 1ee0 x21: x21 x22: x22
STACK CFI 1ee4 x25: x25
STACK CFI 1eec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1ef0 x25: .cfa -96 + ^
STACK CFI INIT 1ef8 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 1efc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1fb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2000 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 21e0 53c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2720 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2750 108 .cfa: sp 0 + .ra: x30
STACK CFI 2754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2768 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2774 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 27b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2858 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2890 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2918 150 .cfa: sp 0 + .ra: x30
STACK CFI 291c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2924 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2934 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 293c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2a58 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2a68 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2a6c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2a74 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2a84 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ad4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
