MODULE Linux arm64 EB27C0815CFDA4F3F62415FC417F25A30 libxcb-icccm.so.4
INFO CODE_ID 81C027EBFD5CF3A4F62415FC417F25A36CA94F04
PUBLIC 1f60 0 xcb_icccm_get_text_property
PUBLIC 1f80 0 xcb_icccm_get_text_property_unchecked
PUBLIC 1fa0 0 xcb_icccm_get_text_property_reply
PUBLIC 2020 0 xcb_icccm_get_text_property_reply_wipe
PUBLIC 2028 0 xcb_icccm_set_wm_name_checked
PUBLIC 2048 0 xcb_icccm_set_wm_name
PUBLIC 2068 0 xcb_icccm_get_wm_name
PUBLIC 2070 0 xcb_icccm_get_wm_name_unchecked
PUBLIC 2078 0 xcb_icccm_get_wm_name_reply
PUBLIC 2080 0 xcb_icccm_set_wm_icon_name_checked
PUBLIC 20a0 0 xcb_icccm_set_wm_icon_name
PUBLIC 20c0 0 xcb_icccm_get_wm_icon_name
PUBLIC 20c8 0 xcb_icccm_get_wm_icon_name_unchecked
PUBLIC 20d0 0 xcb_icccm_get_wm_icon_name_reply
PUBLIC 20d8 0 xcb_icccm_set_wm_colormap_windows_checked
PUBLIC 20f8 0 xcb_icccm_set_wm_colormap_windows
PUBLIC 2118 0 xcb_icccm_get_wm_colormap_windows
PUBLIC 2138 0 xcb_icccm_get_wm_colormap_windows_unchecked
PUBLIC 2158 0 xcb_icccm_get_wm_colormap_windows_from_reply
PUBLIC 21e0 0 xcb_icccm_get_wm_colormap_windows_reply
PUBLIC 2238 0 xcb_icccm_get_wm_colormap_windows_reply_wipe
PUBLIC 2240 0 xcb_icccm_set_wm_client_machine_checked
PUBLIC 2260 0 xcb_icccm_set_wm_client_machine
PUBLIC 2280 0 xcb_icccm_get_wm_client_machine
PUBLIC 2288 0 xcb_icccm_get_wm_client_machine_unchecked
PUBLIC 2290 0 xcb_icccm_get_wm_client_machine_reply
PUBLIC 2298 0 xcb_icccm_set_wm_class_checked
PUBLIC 22b8 0 xcb_icccm_set_wm_class
PUBLIC 22d8 0 xcb_icccm_get_wm_class
PUBLIC 22f8 0 xcb_icccm_get_wm_class_unchecked
PUBLIC 2318 0 xcb_icccm_get_wm_class_from_reply
PUBLIC 23e0 0 xcb_icccm_get_wm_class_reply
PUBLIC 2440 0 xcb_icccm_get_wm_class_reply_wipe
PUBLIC 2448 0 xcb_icccm_set_wm_transient_for_checked
PUBLIC 2480 0 xcb_icccm_set_wm_transient_for
PUBLIC 24b8 0 xcb_icccm_get_wm_transient_for
PUBLIC 24d8 0 xcb_icccm_get_wm_transient_for_unchecked
PUBLIC 24f8 0 xcb_icccm_get_wm_transient_for_from_reply
PUBLIC 2568 0 xcb_icccm_get_wm_transient_for_reply
PUBLIC 25b0 0 xcb_icccm_size_hints_set_position
PUBLIC 25d8 0 xcb_icccm_size_hints_set_size
PUBLIC 2600 0 xcb_icccm_size_hints_set_min_size
PUBLIC 2618 0 xcb_icccm_size_hints_set_max_size
PUBLIC 2630 0 xcb_icccm_size_hints_set_resize_inc
PUBLIC 2648 0 xcb_icccm_size_hints_set_aspect
PUBLIC 2660 0 xcb_icccm_size_hints_set_base_size
PUBLIC 2678 0 xcb_icccm_size_hints_set_win_gravity
PUBLIC 2690 0 xcb_icccm_set_wm_size_hints_checked
PUBLIC 26b0 0 xcb_icccm_set_wm_size_hints
PUBLIC 26d0 0 xcb_icccm_get_wm_size_hints
PUBLIC 26f0 0 xcb_icccm_get_wm_size_hints_unchecked
PUBLIC 2710 0 xcb_icccm_get_wm_size_hints_from_reply
PUBLIC 2808 0 xcb_icccm_get_wm_size_hints_reply
PUBLIC 2850 0 xcb_icccm_set_wm_normal_hints_checked
PUBLIC 2860 0 xcb_icccm_set_wm_normal_hints
PUBLIC 2870 0 xcb_icccm_get_wm_normal_hints
PUBLIC 2878 0 xcb_icccm_get_wm_normal_hints_unchecked
PUBLIC 2880 0 xcb_icccm_get_wm_normal_hints_reply
PUBLIC 2888 0 xcb_icccm_wm_hints_get_urgency
PUBLIC 2898 0 xcb_icccm_wm_hints_set_input
PUBLIC 28b0 0 xcb_icccm_wm_hints_set_iconic
PUBLIC 28c8 0 xcb_icccm_wm_hints_set_normal
PUBLIC 28e0 0 xcb_icccm_wm_hints_set_withdrawn
PUBLIC 28f8 0 xcb_icccm_wm_hints_set_none
PUBLIC 2908 0 xcb_icccm_wm_hints_set_icon_pixmap
PUBLIC 2920 0 xcb_icccm_wm_hints_set_icon_mask
PUBLIC 2938 0 xcb_icccm_wm_hints_set_icon_window
PUBLIC 2950 0 xcb_icccm_wm_hints_set_window_group
PUBLIC 2968 0 xcb_icccm_wm_hints_set_urgency
PUBLIC 2978 0 xcb_icccm_set_wm_hints_checked
PUBLIC 2998 0 xcb_icccm_set_wm_hints
PUBLIC 29b8 0 xcb_icccm_get_wm_hints
PUBLIC 29d8 0 xcb_icccm_get_wm_hints_unchecked
PUBLIC 29f8 0 xcb_icccm_get_wm_hints_from_reply
PUBLIC 2aa0 0 xcb_icccm_get_wm_hints_reply
PUBLIC 2ae8 0 xcb_icccm_set_wm_protocols_checked
PUBLIC 2b08 0 xcb_icccm_set_wm_protocols
PUBLIC 2b28 0 xcb_icccm_get_wm_protocols
PUBLIC 2b48 0 xcb_icccm_get_wm_protocols_unchecked
PUBLIC 2b68 0 xcb_icccm_get_wm_protocols_from_reply
PUBLIC 2bf0 0 xcb_icccm_get_wm_protocols_reply
PUBLIC 2c48 0 xcb_icccm_get_wm_protocols_reply_wipe
STACK CFI INIT 1ea8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ed8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f10 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f1c x19: .cfa -16 + ^
STACK CFI 1f54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f60 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f80 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fa0 7c .cfa: sp 0 + .ra: x30
STACK CFI 1fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fac x19: .cfa -16 + ^
STACK CFI 1fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2028 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2048 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2068 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2078 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2080 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2118 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2138 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2158 88 .cfa: sp 0 + .ra: x30
STACK CFI 2160 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2170 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 218c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 21d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 21e0 58 .cfa: sp 0 + .ra: x30
STACK CFI 21e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 221c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2220 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2238 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2240 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2260 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2288 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2290 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2298 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22d8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2318 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2344 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 234c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2358 x21: .cfa -16 + ^
STACK CFI 23c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 23e0 5c .cfa: sp 0 + .ra: x30
STACK CFI 23e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2424 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2448 34 .cfa: sp 0 + .ra: x30
STACK CFI 244c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2478 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2480 34 .cfa: sp 0 + .ra: x30
STACK CFI 2484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24b8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f8 70 .cfa: sp 0 + .ra: x30
STACK CFI 252c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2534 x19: .cfa -16 + ^
STACK CFI 2558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2568 44 .cfa: sp 0 + .ra: x30
STACK CFI 256c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2574 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 25a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25b0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2600 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2618 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2630 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2648 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2660 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2678 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2690 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26d0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2710 f4 .cfa: sp 0 + .ra: x30
STACK CFI 273c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2744 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2754 x21: .cfa -16 + ^
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2808 44 .cfa: sp 0 + .ra: x30
STACK CFI 280c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2850 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2860 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2878 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2888 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2898 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28b0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2908 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2920 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2938 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2950 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2968 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2978 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2998 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29b8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29d8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2a20 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a38 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2aa0 44 .cfa: sp 0 + .ra: x30
STACK CFI 2aa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ae8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b08 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b28 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b48 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b68 88 .cfa: sp 0 + .ra: x30
STACK CFI 2b70 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2bf0 58 .cfa: sp 0 + .ra: x30
STACK CFI 2bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c48 8 .cfa: sp 0 + .ra: x30
