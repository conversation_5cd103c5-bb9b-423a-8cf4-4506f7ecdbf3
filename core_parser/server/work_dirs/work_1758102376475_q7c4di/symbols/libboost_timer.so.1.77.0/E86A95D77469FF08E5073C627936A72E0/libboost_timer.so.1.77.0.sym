MODULE Linux arm64 E86A95D77469FF08E5073C627936A72E0 libboost_timer.so.1.77.0
INFO CODE_ID D7956AE8697408FFE5073C627936A72E
PUBLIC 31b0 0 _init
PUBLIC 34e0 0 _GLOBAL__sub_I_auto_timers_construction.cpp
PUBLIC 35b0 0 _GLOBAL__sub_I_cpu_timer.cpp
PUBLIC 3654 0 call_weak_fn
PUBLIC 3668 0 deregister_tm_clones
PUBLIC 3698 0 register_tm_clones
PUBLIC 36d4 0 __do_global_dtors_aux
PUBLIC 3724 0 frame_dummy
PUBLIC 3730 0 boost::timer::auto_cpu_timer::auto_cpu_timer(short)
PUBLIC 3840 0 boost::timer::auto_cpu_timer::auto_cpu_timer(short, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3950 0 boost::timer::auto_cpu_timer::auto_cpu_timer(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3a60 0 (anonymous namespace)::show_time(boost::timer::cpu_times const&, std::ostream&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, short)
PUBLIC 3cd0 0 boost::timer::cpu_timer::start()
PUBLIC 3da0 0 boost::timer::cpu_timer::stop()
PUBLIC 3ec0 0 boost::timer::cpu_timer::elapsed() const
PUBLIC 4000 0 boost::timer::cpu_timer::resume()
PUBLIC 4060 0 boost::timer::auto_cpu_timer::auto_cpu_timer(std::ostream&, short)
PUBLIC 4170 0 boost::timer::auto_cpu_timer::report()
PUBLIC 41b0 0 boost::timer::auto_cpu_timer::~auto_cpu_timer()
PUBLIC 4210 0 boost::timer::format(boost::timer::cpu_times const&, short, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4560 0 boost::timer::format[abi:cxx11](boost::timer::cpu_times const&, short)
PUBLIC 4590 0 boost::system::error_category::failed(int) const
PUBLIC 45a0 0 boost::system::detail::generic_error_category::name() const
PUBLIC 45b0 0 boost::system::detail::system_error_category::name() const
PUBLIC 45c0 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 45e0 0 boost::system::detail::interop_error_category::name() const
PUBLIC 45f0 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 4690 0 boost::system::detail::std_category::name() const
PUBLIC 46b0 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 46e0 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 46f0 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 4700 0 boost::system::detail::std_category::~std_category()
PUBLIC 4720 0 boost::system::detail::std_category::~std_category()
PUBLIC 4760 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 47f0 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 4890 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 4990 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 4a90 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 4c30 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 5220 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 56b0 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 5720 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 5760 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 5830 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 5974 0 _fini
STACK CFI INIT 3668 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3698 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 36d4 50 .cfa: sp 0 + .ra: x30
STACK CFI 36e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36ec x19: .cfa -16 + ^
STACK CFI 371c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3724 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3730 10c .cfa: sp 0 + .ra: x30
STACK CFI 3734 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 373c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3748 x21: .cfa -32 + ^
STACK CFI 37b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 37b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 37d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 37dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 382c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3830 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3840 10c .cfa: sp 0 + .ra: x30
STACK CFI 3844 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 384c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 38e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3940 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3950 10c .cfa: sp 0 + .ra: x30
STACK CFI 3954 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 395c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3964 x21: .cfa -32 + ^
STACK CFI 39d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 39d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 39fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 3a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 34e0 cc .cfa: sp 0 + .ra: x30
STACK CFI 34e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 34ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4590 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4690 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 46b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46c8 x19: .cfa -16 + ^
STACK CFI 46dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4700 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4720 38 .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4734 x19: .cfa -16 + ^
STACK CFI 4754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4760 88 .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 476c x19: .cfa -16 + ^
STACK CFI 4794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4798 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47f0 94 .cfa: sp 0 + .ra: x30
STACK CFI 47f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4808 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4868 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a60 268 .cfa: sp 0 + .ra: x30
STACK CFI 3a64 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3a78 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3a80 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3a88 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3a98 v10: .cfa -48 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3bd4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3bd8 .cfa: sp 160 + .ra: .cfa -152 + ^ v10: .cfa -48 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4890 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4894 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 48a4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 48b0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4904 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4924 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4978 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4990 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4994 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 49a4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 49b0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a04 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a24 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a78 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4a90 194 .cfa: sp 0 + .ra: x30
STACK CFI 4a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4aac x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4bb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c30 5e8 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4c3c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4c44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4c50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4d84 x25: x25 x26: x26
STACK CFI 4d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4e64 x25: x25 x26: x26
STACK CFI 4e70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4eec x25: x25 x26: x26
STACK CFI 4ef0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4f50 x25: x25 x26: x26
STACK CFI 5014 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5028 x25: x25 x26: x26
STACK CFI 502c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5054 x25: x25 x26: x26
STACK CFI 5060 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5134 x25: x25 x26: x26
STACK CFI 5148 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 515c x25: x25 x26: x26
STACK CFI 5188 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 5220 48c .cfa: sp 0 + .ra: x30
STACK CFI 5224 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 522c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5234 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5240 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5254 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5384 x23: x23 x24: x24
STACK CFI 538c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 5390 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 53d4 x23: x23 x24: x24
STACK CFI 53dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 53e0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5438 x23: x23 x24: x24
STACK CFI 5450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 5454 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 54d4 x23: x23 x24: x24
STACK CFI 5588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 558c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5590 x23: x23 x24: x24
STACK CFI 5594 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 55a8 x23: x23 x24: x24
STACK CFI 55bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5654 x23: x23 x24: x24
STACK CFI 5668 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 567c x23: x23 x24: x24
STACK CFI 5698 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 56b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 56b4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 571c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5720 3c .cfa: sp 0 + .ra: x30
STACK CFI 5724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 573c x19: .cfa -16 + ^
STACK CFI 5758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5760 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5764 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 576c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5788 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 57d0 x21: x21 x22: x22
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 57ec x21: x21 x22: x22
STACK CFI 57f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5808 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 582c x21: x21 x22: x22
STACK CFI INIT 5830 144 .cfa: sp 0 + .ra: x30
STACK CFI 5834 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5844 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5850 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 58cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 58ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 5940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5944 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3cd0 cc .cfa: sp 0 + .ra: x30
STACK CFI 3cd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3da0 11c .cfa: sp 0 + .ra: x30
STACK CFI 3da4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3dac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3dc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3dc8 v8: .cfa -48 + ^
STACK CFI 3dec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3df8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3e24 x23: x23 x24: x24
STACK CFI 3e34 x21: x21 x22: x22
STACK CFI 3e38 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3e3c x21: x21 x22: x22
STACK CFI 3e40 x23: x23 x24: x24
STACK CFI 3e50 v8: v8
STACK CFI 3e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e78 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -48 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ec0 13c .cfa: sp 0 + .ra: x30
STACK CFI 3ec4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ecc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ef8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3efc v8: .cfa -40 + ^
STACK CFI 3f24 v8: v8
STACK CFI 3f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f4c .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3f50 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3f54 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3f60 x25: .cfa -48 + ^
STACK CFI 3f90 x23: x23 x24: x24
STACK CFI 3fa0 x21: x21 x22: x22
STACK CFI 3fa4 x25: x25
STACK CFI 3fa8 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 3fac x21: x21 x22: x22
STACK CFI 3fb0 x23: x23 x24: x24
STACK CFI 3fb4 x25: x25
STACK CFI 3fb8 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI INIT 4000 5c .cfa: sp 0 + .ra: x30
STACK CFI 4010 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4018 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4020 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4058 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI INIT 4060 108 .cfa: sp 0 + .ra: x30
STACK CFI 4064 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 406c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4078 x21: .cfa -32 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 40e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 415c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4170 3c .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 417c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 41b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41c0 x19: .cfa -16 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4210 350 .cfa: sp 0 + .ra: x30
STACK CFI 4214 .cfa: sp 528 +
STACK CFI 4218 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 4220 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 422c x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 4234 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 4244 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 4460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4464 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT 4560 30 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4578 x19: .cfa -16 + ^
STACK CFI 458c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 35b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 35b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
