MODULE Linux arm64 DA9AD17E00D19EA810AD1B7A23EAF37D0 libosqp.so
INFO CODE_ID 7ED19ADAD100A89E10AD1B7A23EAF37D
PUBLIC 3e18 0 _init
PUBLIC 4710 0 call_weak_fn
PUBLIC 4724 0 deregister_tm_clones
PUBLIC 4754 0 register_tm_clones
PUBLIC 4790 0 __do_global_dtors_aux
PUBLIC 47e0 0 frame_dummy
PUBLIC 47f0 0 compute_rho_estimate
PUBLIC 4940 0 adapt_rho
PUBLIC 49b0 0 set_rho_vec
PUBLIC 4ad0 0 update_rho_vec
PUBLIC 4c30 0 swap_vectors
PUBLIC 4c50 0 cold_start
PUBLIC 4cb0 0 update_xz_tilde
PUBLIC 4e90 0 update_x
PUBLIC 5030 0 update_z
PUBLIC 51b0 0 update_y
PUBLIC 5230 0 compute_obj_val
PUBLIC 52a0 0 compute_pri_res
PUBLIC 5350 0 compute_pri_tol
PUBLIC 5420 0 compute_dua_res
PUBLIC 5550 0 compute_dua_tol
PUBLIC 5660 0 is_primal_infeasible
PUBLIC 5900 0 is_dual_infeasible
PUBLIC 5b10 0 has_solution
PUBLIC 5b40 0 store_solution
PUBLIC 5ca0 0 update_info
PUBLIC 5d90 0 update_status
PUBLIC 5ea0 0 reset_info
PUBLIC 5ed0 0 check_termination
PUBLIC 6180 0 validate_data
PUBLIC 6470 0 validate_linsys_solver
PUBLIC 6480 0 validate_settings
PUBLIC 6920 0 _osqp_error
PUBLIC 6960 0 vec_add_scaled
PUBLIC 6a00 0 vec_scaled_norm_inf
PUBLIC 6a40 0 vec_norm_inf
PUBLIC 6a80 0 vec_norm_inf_diff
PUBLIC 6ac0 0 vec_mean
PUBLIC 6af0 0 int_vec_set_scalar
PUBLIC 6b70 0 vec_set_scalar
PUBLIC 6bf0 0 vec_add_scalar
PUBLIC 6c50 0 vec_mult_scalar
PUBLIC 6cb0 0 vec_copy
PUBLIC 6d10 0 prea_int_vec_copy
PUBLIC 6d90 0 prea_vec_copy
PUBLIC 6e10 0 vec_ew_recipr
PUBLIC 6ea0 0 vec_prod
PUBLIC 6f20 0 vec_ew_prod
PUBLIC 6fc0 0 vec_ew_sqrt
PUBLIC 7020 0 vec_ew_max
PUBLIC 70b0 0 vec_ew_min
PUBLIC 7140 0 vec_ew_max_vec
PUBLIC 7210 0 vec_ew_min_vec
PUBLIC 72e0 0 mat_mult_scalar
PUBLIC 7350 0 mat_premult_diag
PUBLIC 73b0 0 mat_postmult_diag
PUBLIC 74a0 0 mat_vec
PUBLIC 75e0 0 mat_tpose_vec
PUBLIC 7830 0 mat_inf_norm_cols
PUBLIC 78f0 0 mat_inf_norm_rows
PUBLIC 79b0 0 mat_inf_norm_cols_sym_triu
PUBLIC 7a70 0 quad_form
PUBLIC 7b60 0 osqp_set_default_settings
PUBLIC 7bf0 0 osqp_setup
PUBLIC 82e0 0 osqp_solve
PUBLIC 8920 0 osqp_cleanup
PUBLIC 8bf0 0 osqp_update_lin_cost
PUBLIC 8cd0 0 osqp_update_bounds
PUBLIC 8e50 0 osqp_update_lower_bound
PUBLIC 8f90 0 osqp_update_upper_bound
PUBLIC 90d0 0 osqp_warm_start
PUBLIC 91d0 0 osqp_warm_start_x
PUBLIC 92a0 0 osqp_warm_start_y
PUBLIC 9380 0 osqp_update_P
PUBLIC 9620 0 osqp_update_A
PUBLIC 98c0 0 osqp_update_P_A
PUBLIC 9c40 0 osqp_update_rho
PUBLIC 9e00 0 osqp_update_max_iter
PUBLIC 9e70 0 osqp_update_eps_abs
PUBLIC 9ee0 0 osqp_update_eps_rel
PUBLIC 9f50 0 osqp_update_eps_prim_inf
PUBLIC 9fc0 0 osqp_update_eps_dual_inf
PUBLIC a030 0 osqp_update_alpha
PUBLIC a0b0 0 osqp_update_warm_start
PUBLIC a120 0 osqp_update_scaled_termination
PUBLIC a190 0 osqp_update_check_termination
PUBLIC a200 0 osqp_update_delta
PUBLIC a270 0 osqp_update_polish
PUBLIC a2f0 0 osqp_update_polish_refine_iter
PUBLIC a360 0 osqp_update_verbose
PUBLIC a3d0 0 osqp_update_time_limit
PUBLIC a420 0 project
PUBLIC a530 0 project_normalcone
PUBLIC a6e0 0 limit_scaling
PUBLIC a7e0 0 compute_inf_norm_cols_KKT
PUBLIC a850 0 scale_data
PUBLIC ab30 0 unscale_data
PUBLIC ac20 0 unscale_solution
PUBLIC ac90 0 osqp_version
PUBLIC aca0 0 c_strcpy
PUBLIC acd0 0 print_header
PUBLIC ad10 0 print_setup_header
PUBLIC afe0 0 print_summary
PUBLIC b0c0 0 print_polish
PUBLIC b170 0 print_footer
PUBLIC b240 0 copy_settings
PUBLIC b2d0 0 osqp_tic
PUBLIC b2e0 0 osqp_toc
PUBLIC b380 0 form_KKT
PUBLIC b9a0 0 update_KKT_P
PUBLIC ba10 0 update_KKT_A
PUBLIC ba50 0 update_KKT_param2
PUBLIC ba80 0 csc_matrix
PUBLIC bae0 0 csc_spfree
PUBLIC bb30 0 csc_spalloc
PUBLIC bc20 0 csc_cumsum
PUBLIC bc70 0 csc_pinv
PUBLIC bce0 0 copy_csc_mat
PUBLIC bd60 0 prea_copy_csc_mat
PUBLIC bdc0 0 csc_done
PUBLIC be30 0 triplet_to_csc
PUBLIC bfe0 0 triplet_to_csr
PUBLIC c190 0 csc_symperm
PUBLIC c650 0 csc_to_triu
PUBLIC c7f0 0 polish
PUBLIC d110 0 load_linsys_solver
PUBLIC d130 0 unload_linsys_solver
PUBLIC d150 0 init_linsys_solver
PUBLIC d170 0 handle_ctrlc
PUBLIC d190 0 osqp_start_interrupt_listener
PUBLIC d1e0 0 osqp_end_interrupt_listener
PUBLIC d210 0 osqp_is_interrupted
PUBLIC d220 0 lh_load_lib
PUBLIC d2d0 0 lh_unload_lib
PUBLIC d2f0 0 lh_load_sym
PUBLIC d500 0 amd_l1
PUBLIC d730 0 amd_l2
PUBLIC ec10 0 amd_l_aat
PUBLIC eee0 0 amd_l_control
PUBLIC f020 0 amd_l_defaults
PUBLIC f050 0 amd_l_info
PUBLIC f520 0 amd_l_order
PUBLIC f950 0 amd_l_post_tree
PUBLIC f9e0 0 amd_l_postorder
PUBLIC fc10 0 amd_l_preprocess
PUBLIC fe60 0 amd_l_valid
PUBLIC ff10 0 SuiteSparse_divcomplex
PUBLIC ff80 0 SuiteSparse_hypot
PUBLIC 10020 0 SuiteSparse_malloc
PUBLIC 10070 0 SuiteSparse_realloc
PUBLIC 10160 0 SuiteSparse_free
PUBLIC 10190 0 SuiteSparse_tic
PUBLIC 101a0 0 SuiteSparse_toc
PUBLIC 101e0 0 SuiteSparse_time
PUBLIC 10210 0 SuiteSparse_version
PUBLIC 10230 0 free_linsys_solver_qdldl
PUBLIC 10330 0 update_linsys_solver_matrices_qdldl
PUBLIC 103d0 0 update_linsys_solver_rho_vec_qdldl
PUBLIC 104f0 0 init_linsys_solver_qdldl
PUBLIC 10c30 0 permute_x
PUBLIC 10c60 0 permutet_x
PUBLIC 10c90 0 solve_linsys_qdldl
PUBLIC 10ee0 0 QDLDL_etree
PUBLIC 11000 0 QDLDL_factor
PUBLIC 11310 0 QDLDL_Lsolve
PUBLIC 11370 0 QDLDL_Ltsolve
PUBLIC 113d0 0 QDLDL_solve
PUBLIC 114b0 0 solve_linsys_pardiso
PUBLIC 116f0 0 update_linsys_solver_matrices_pardiso
PUBLIC 117a0 0 update_linsys_solver_rho_vec_pardiso
PUBLIC 118c0 0 free_linsys_solver_pardiso
PUBLIC 11a00 0 init_linsys_solver_pardiso
PUBLIC 11f80 0 pardiso
PUBLIC 11fd0 0 mkl_set_interface_layer
PUBLIC 11ff0 0 mkl_get_max_threads
PUBLIC 12010 0 lh_load_pardiso
PUBLIC 120b0 0 lh_unload_pardiso
PUBLIC 120c8 0 _fini
STACK CFI INIT 4724 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4754 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4790 50 .cfa: sp 0 + .ra: x30
STACK CFI 47a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47a8 x19: .cfa -16 + ^
STACK CFI 47d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47f0 14c .cfa: sp 0 + .ra: x30
STACK CFI 47f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4800 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI 480c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4820 v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -48 + ^
STACK CFI 4828 v12: .cfa -40 + ^
STACK CFI 490c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4910 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 492c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4930 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4940 68 .cfa: sp 0 + .ra: x30
STACK CFI 4944 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 494c x19: .cfa -16 + ^
STACK CFI 49a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 49b0 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ad0 160 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c30 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c50 54 .cfa: sp 0 + .ra: x30
STACK CFI 4c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c64 x19: .cfa -16 + ^
STACK CFI 4c9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4cb0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e90 1a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5030 178 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51b0 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5230 68 .cfa: sp 0 + .ra: x30
STACK CFI 5234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 523c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5250 v8: .cfa -16 + ^
STACK CFI 5294 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 52a0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 52a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 530c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5310 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5328 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 533c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5350 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5360 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 536c x19: .cfa -32 + ^
STACK CFI 5374 v10: .cfa -24 + ^
STACK CFI 53c4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 53c8 .cfa: sp 48 + .ra: .cfa -40 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 5414 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI INIT 5420 128 .cfa: sp 0 + .ra: x30
STACK CFI 5424 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 542c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5440 x21: .cfa -16 + ^
STACK CFI 5510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5514 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5528 v8: .cfa -8 + ^
STACK CFI 5540 v8: v8
STACK CFI 5544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5550 10c .cfa: sp 0 + .ra: x30
STACK CFI 5554 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5560 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 556c x19: .cfa -32 + ^
STACK CFI 5574 v10: .cfa -24 + ^
STACK CFI 55dc .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 55e0 .cfa: sp 48 + .ra: .cfa -40 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 5658 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI INIT 5660 294 .cfa: sp 0 + .ra: x30
STACK CFI 5664 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5678 v8: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5824 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x29: x29
STACK CFI 5828 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 58b4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x29: x29
STACK CFI 58b8 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5900 210 .cfa: sp 0 + .ra: x30
STACK CFI 5904 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5918 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^
STACK CFI 5920 v10: .cfa -24 + ^
STACK CFI 596c .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 5970 .cfa: sp 48 + .ra: .cfa -40 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b10 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b40 154 .cfa: sp 0 + .ra: x30
STACK CFI 5b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b4c x19: .cfa -16 + ^
STACK CFI 5ba0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ca0 ec .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5cb0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5cc4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5d38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d90 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ea0 30 .cfa: sp 0 + .ra: x30
STACK CFI 5ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5eb0 x19: .cfa -16 + ^
STACK CFI 5ecc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ed0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 5ed4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5edc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5f0c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 5f1c v10: .cfa -40 + ^
STACK CFI 5f74 v10: v10
STACK CFI 5f7c v8: v8 v9: v9
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fb0 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6038 v10: v10
STACK CFI 603c v8: v8 v9: v9
STACK CFI 6044 v10: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 6054 v10: v10
STACK CFI 605c v8: v8 v9: v9
STACK CFI 6060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6064 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6070 x21: .cfa -48 + ^
STACK CFI 60a0 x21: x21
STACK CFI 60a8 v10: v10
STACK CFI 60ac v8: v8 v9: v9
STACK CFI 60b0 v10: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -48 + ^
STACK CFI 60fc x21: x21
STACK CFI 6100 v10: v10
STACK CFI 6104 v8: v8 v9: v9
STACK CFI 6110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6114 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 6140 x21: x21
STACK CFI 616c x21: .cfa -48 + ^
STACK CFI 6170 x21: x21
STACK CFI INIT 6180 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 6184 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6194 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6244 x19: x19 x20: x20
STACK CFI 6248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 624c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6280 x19: x19 x20: x20
STACK CFI 6288 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 628c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 62e4 x21: .cfa -16 + ^
STACK CFI 634c x19: x19 x20: x20
STACK CFI 6350 x21: x21
STACK CFI 6354 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6388 x19: x19 x20: x20
STACK CFI 638c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6390 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6448 x21: .cfa -16 + ^
STACK CFI 6450 x19: x19 x20: x20
STACK CFI 6454 x21: x21
STACK CFI 6458 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6460 x19: x19 x20: x20
STACK CFI INIT 6470 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6480 4a0 .cfa: sp 0 + .ra: x30
STACK CFI 6484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6490 x19: .cfa -16 + ^
STACK CFI 65b0 x19: x19
STACK CFI 65b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 65bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6604 x19: x19
STACK CFI 6638 x19: .cfa -16 + ^
STACK CFI 6710 x19: x19
STACK CFI 6714 x19: .cfa -16 + ^
STACK CFI 691c x19: x19
STACK CFI INIT 6920 3c .cfa: sp 0 + .ra: x30
STACK CFI 6924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6934 x19: .cfa -16 + ^
STACK CFI 6958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6960 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a00 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a40 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a80 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ac0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6af0 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b70 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bf0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c50 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cb0 54 .cfa: sp 0 + .ra: x30
STACK CFI 6cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6ccc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6d10 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d90 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e10 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ea0 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f20 98 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc0 60 .cfa: sp 0 + .ra: x30
STACK CFI 6fcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6fd8 v8: .cfa -32 + ^
STACK CFI 6ffc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 7004 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7020 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70b0 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7140 c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7210 c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 72e0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7350 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73b0 e4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74a0 134 .cfa: sp 0 + .ra: x30
STACK CFI 74a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 74b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 74c0 x23: .cfa -16 + ^
STACK CFI 7558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 755c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 75d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 75e0 24c .cfa: sp 0 + .ra: x30
STACK CFI 75e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7600 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7698 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7794 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7830 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 783c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 78f0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7904 x19: .cfa -16 + ^
STACK CFI 7988 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 798c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 79b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7a70 f0 .cfa: sp 0 + .ra: x30
STACK CFI 7b20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b60 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7bf0 6ec .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7bfc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7c04 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7c6c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7cc4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7d48 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8020 x23: x23 x24: x24
STACK CFI 8024 x25: x25 x26: x26
STACK CFI 8028 x27: x27 x28: x28
STACK CFI 802c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8030 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 8048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 804c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 8064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8068 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 8080 x23: x23 x24: x24
STACK CFI 8084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8088 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 8090 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8188 x23: x23 x24: x24
STACK CFI 818c x25: x25 x26: x26
STACK CFI 8190 x27: x27 x28: x28
STACK CFI 8194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8198 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 82b4 x27: x27 x28: x28
STACK CFI 82c4 x23: x23 x24: x24
STACK CFI 82c8 x25: x25 x26: x26
STACK CFI INIT 82e0 638 .cfa: sp 0 + .ra: x30
STACK CFI 82e8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 82f0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8304 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 835c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8368 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8374 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 85ec x21: x21 x22: x22
STACK CFI 85f0 x23: x23 x24: x24
STACK CFI 85f4 v8: v8 v9: v9
STACK CFI 86d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 86d8 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 871c x21: x21 x22: x22
STACK CFI 8720 x23: x23 x24: x24
STACK CFI 8724 v8: v8 v9: v9
STACK CFI 873c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8740 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8754 x21: x21 x22: x22
STACK CFI 8758 x23: x23 x24: x24
STACK CFI 875c v8: v8 v9: v9
STACK CFI 87f8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8818 x21: x21 x22: x22
STACK CFI 881c x23: x23 x24: x24
STACK CFI 8820 v8: v8 v9: v9
STACK CFI 885c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8870 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8874 x21: x21 x22: x22
STACK CFI 887c x23: x23 x24: x24
STACK CFI 8880 v8: v8 v9: v9
STACK CFI 88bc v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 88f0 x21: x21 x22: x22
STACK CFI 88f4 x23: x23 x24: x24
STACK CFI 88f8 v8: v8 v9: v9
STACK CFI INIT 8920 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 892c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8bf0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 8bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8cd0 174 .cfa: sp 0 + .ra: x30
STACK CFI 8cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ce0 x21: .cfa -16 + ^
STACK CFI 8cec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8e50 140 .cfa: sp 0 + .ra: x30
STACK CFI 8e58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8f90 140 .cfa: sp 0 + .ra: x30
STACK CFI 8f98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 905c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9060 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 909c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 90bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 90d0 fc .cfa: sp 0 + .ra: x30
STACK CFI 90d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 914c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9150 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 91c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 91d0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 91d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91dc x19: .cfa -16 + ^
STACK CFI 9234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9238 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9290 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 92a0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 92a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92ac x19: .cfa -16 + ^
STACK CFI 92ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 92f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 936c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9380 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 9388 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9390 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 93a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 93a8 x23: .cfa -16 + ^
STACK CFI 9480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9484 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 95e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 95f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9620 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 9628 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9630 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9640 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9648 x23: .cfa -16 + ^
STACK CFI 9720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9898 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 98c0 380 .cfa: sp 0 + .ra: x30
STACK CFI 98c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 98d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 98e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 98ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 98f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9900 x27: .cfa -16 + ^
STACK CFI 9a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9a28 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 9bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9bc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9c40 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 9c48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9c58 v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d60 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 9d64 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9da4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 9da8 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9de8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 9e00 70 .cfa: sp 0 + .ra: x30
STACK CFI 9e28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9e58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9e70 70 .cfa: sp 0 + .ra: x30
STACK CFI 9e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ec8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9ee0 70 .cfa: sp 0 + .ra: x30
STACK CFI 9f08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9f38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9f50 70 .cfa: sp 0 + .ra: x30
STACK CFI 9f78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9fa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9fc0 70 .cfa: sp 0 + .ra: x30
STACK CFI 9fe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a018 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a030 7c .cfa: sp 0 + .ra: x30
STACK CFI a064 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a094 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a0b0 70 .cfa: sp 0 + .ra: x30
STACK CFI a0d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a120 70 .cfa: sp 0 + .ra: x30
STACK CFI a148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a178 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a190 6c .cfa: sp 0 + .ra: x30
STACK CFI a1b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a1e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a200 70 .cfa: sp 0 + .ra: x30
STACK CFI a228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a270 7c .cfa: sp 0 + .ra: x30
STACK CFI a2a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2f0 6c .cfa: sp 0 + .ra: x30
STACK CFI a314 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a360 70 .cfa: sp 0 + .ra: x30
STACK CFI a388 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a3b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a3d0 50 .cfa: sp 0 + .ra: x30
STACK CFI a3f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a420 104 .cfa: sp 0 + .ra: x30
STACK CFI INIT a530 1a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a6e0 fc .cfa: sp 0 + .ra: x30
STACK CFI INIT a7e0 6c .cfa: sp 0 + .ra: x30
STACK CFI a7e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a7ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a7fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a808 x23: .cfa -16 + ^
STACK CFI a848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT a850 2dc .cfa: sp 0 + .ra: x30
STACK CFI a854 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a860 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a86c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a878 v8: .cfa -32 + ^
STACK CFI a8e8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa98 x23: x23 x24: x24
STACK CFI ab1c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab20 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT ab30 f0 .cfa: sp 0 + .ra: x30
STACK CFI ab34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab3c x19: .cfa -16 + ^
STACK CFI ac1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac20 70 .cfa: sp 0 + .ra: x30
STACK CFI ac24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac2c x19: .cfa -16 + ^
STACK CFI ac8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT aca0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT acd0 38 .cfa: sp 0 + .ra: x30
STACK CFI acd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ad00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad10 2c8 .cfa: sp 0 + .ra: x30
STACK CFI ad14 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ad24 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ad2c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ad50 x23: .cfa -96 + ^
STACK CFI af1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI af20 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI afc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI afc8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT afe0 d8 .cfa: sp 0 + .ra: x30
STACK CFI afe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aff0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b004 x21: .cfa -16 + ^
STACK CFI b080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b084 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b0b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b0c0 ac .cfa: sp 0 + .ra: x30
STACK CFI b0c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b0d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0e8 x21: .cfa -16 + ^
STACK CFI b15c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b160 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b170 d0 .cfa: sp 0 + .ra: x30
STACK CFI b174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b17c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b1f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b1fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b240 90 .cfa: sp 0 + .ra: x30
STACK CFI b244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b24c x19: .cfa -16 + ^
STACK CFI b2cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b2d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b2e0 9c .cfa: sp 0 + .ra: x30
STACK CFI b2e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2ec x19: .cfa -16 + ^
STACK CFI b33c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b344 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b380 618 .cfa: sp 0 + .ra: x30
STACK CFI b384 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b38c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b398 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b3ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b3c0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b3cc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b3d8 v8: .cfa -32 + ^
STACK CFI b6d4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b6d8 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT b9a0 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba10 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT ba50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba80 60 .cfa: sp 0 + .ra: x30
STACK CFI ba84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI baa8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI badc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT bae0 4c .cfa: sp 0 + .ra: x30
STACK CFI bae8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI baf0 x19: .cfa -16 + ^
STACK CFI bb24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bb30 e4 .cfa: sp 0 + .ra: x30
STACK CFI bb34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bb3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bb44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bb54 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bbd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bbdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT bc20 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT bc70 68 .cfa: sp 0 + .ra: x30
STACK CFI bc78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bcbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bcc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bce0 78 .cfa: sp 0 + .ra: x30
STACK CFI bce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bcf8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bd54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bd60 60 .cfa: sp 0 + .ra: x30
STACK CFI bd64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bdbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bdc0 64 .cfa: sp 0 + .ra: x30
STACK CFI bdc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bdcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bddc x21: .cfa -16 + ^
STACK CFI be00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI be04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI be20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT be30 1b0 .cfa: sp 0 + .ra: x30
STACK CFI be34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI be48 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI be58 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI be70 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bf40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf44 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI bf98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT bfe0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI bfe4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bff8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c004 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c01c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c0f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c0f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI c148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c14c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c190 4b8 .cfa: sp 0 + .ra: x30
STACK CFI c194 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c1a8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c1b0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI c1d0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c1dc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI c34c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c350 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI c634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c638 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT c650 1a0 .cfa: sp 0 + .ra: x30
STACK CFI c654 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c65c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c664 x23: .cfa -16 + ^
STACK CFI c678 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c748 x19: x19 x20: x20
STACK CFI c758 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c75c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c77c x19: x19 x20: x20
STACK CFI c7b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c7bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c7ec x19: x19 x20: x20
STACK CFI INIT c7f0 920 .cfa: sp 0 + .ra: x30
STACK CFI c7f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c7fc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c808 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ca68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ca6c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI cb60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cb64 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI cb68 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI cb70 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ce1c x23: x23 x24: x24
STACK CFI ce20 x27: x27 x28: x28
STACK CFI ce24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ce28 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI ce6c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI cfac x25: x25 x26: x26
STACK CFI cfb0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI cfec x25: x25 x26: x26
STACK CFI cff4 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI cffc x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d030 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI d040 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d0b4 x23: x23 x24: x24
STACK CFI d0b8 x27: x27 x28: x28
STACK CFI d0bc x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d0d8 x23: x23 x24: x24
STACK CFI d0dc x27: x27 x28: x28
STACK CFI d0e0 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT d110 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d130 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d150 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d170 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d190 50 .cfa: sp 0 + .ra: x30
STACK CFI d194 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI d1a8 x19: .cfa -176 + ^
STACK CFI d1dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d1e0 24 .cfa: sp 0 + .ra: x30
STACK CFI d1e4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI d200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d210 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d220 a8 .cfa: sp 0 + .ra: x30
STACK CFI d224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d22c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d254 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d298 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT d2d0 18 .cfa: sp 0 + .ra: x30
STACK CFI d2d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d2e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d2f0 208 .cfa: sp 0 + .ra: x30
STACK CFI d2f4 .cfa: sp 880 +
STACK CFI d2f8 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI d300 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI d30c x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI d314 x23: .cfa -832 + ^ x24: .cfa -824 + ^
STACK CFI d320 x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI d490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d494 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^ x29: .cfa -880 + ^
STACK CFI INIT d500 230 .cfa: sp 0 + .ra: x30
STACK CFI d504 .cfa: sp 144 +
STACK CFI d514 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d51c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d52c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d534 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d540 x25: .cfa -16 + ^
STACK CFI d6ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d6f0 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT d730 14e0 .cfa: sp 0 + .ra: x30
STACK CFI d734 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI d73c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI d748 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI d760 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI d774 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI d78c v8: .cfa -272 + ^ v9: .cfa -264 + ^
STACK CFI e8f8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e8fc .cfa: sp 368 + .ra: .cfa -360 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI ebdc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ebe0 .cfa: sp 368 + .ra: .cfa -360 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT ec10 2c8 .cfa: sp 0 + .ra: x30
STACK CFI ec14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ec1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ec28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ec34 x23: .cfa -16 + ^
STACK CFI ee7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ee80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT eee0 138 .cfa: sp 0 + .ra: x30
STACK CFI eee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI eeec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ef00 v8: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI ef94 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ef9c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI efac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI efb0 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT f020 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT f050 4c8 .cfa: sp 0 + .ra: x30
STACK CFI f054 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f05c x21: .cfa -64 + ^
STACK CFI f064 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f09c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI f0a4 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI f0ac v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI f3ec v8: v8 v9: v9
STACK CFI f3f0 v10: v10 v11: v11
STACK CFI f3f4 v12: v12 v13: v13
STACK CFI f3f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f3fc .cfa: sp 96 + .ra: .cfa -88 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI f400 v8: v8 v9: v9
STACK CFI f404 v10: v10 v11: v11
STACK CFI f408 v12: v12 v13: v13
STACK CFI f414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f418 .cfa: sp 96 + .ra: .cfa -88 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT f520 424 .cfa: sp 0 + .ra: x30
STACK CFI f524 .cfa: sp 160 +
STACK CFI f52c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI f534 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI f540 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI f54c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f55c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f56c v8: .cfa -48 + ^
STACK CFI f770 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f774 .cfa: sp 160 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT f950 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT f9e0 228 .cfa: sp 0 + .ra: x30
STACK CFI f9e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f9f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f9fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI fa04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI fa10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fa1c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fbbc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI fbe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fbe4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT fc10 248 .cfa: sp 0 + .ra: x30
STACK CFI fc14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fc20 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI fc2c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI fc3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI fc44 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI fde8 x19: x19 x20: x20
STACK CFI fdec x21: x21 x22: x22
STACK CFI fdf4 x25: x25 x26: x26
STACK CFI fdf8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI fdfc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI fe24 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI fe30 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI fe34 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT fe60 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff10 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff80 94 .cfa: sp 0 + .ra: x30
STACK CFI ff84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ff8c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI ffb4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI ffb8 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ffec .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI fff0 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10020 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10070 ec .cfa: sp 0 + .ra: x30
STACK CFI 10074 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10084 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 10098 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 100d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 100d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 10108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1010c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 10130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10134 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10160 30 .cfa: sp 0 + .ra: x30
STACK CFI 10168 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10184 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10190 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 101a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 101a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 101ac x19: .cfa -32 + ^
STACK CFI 101d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 101e0 28 .cfa: sp 0 + .ra: x30
STACK CFI 101e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10210 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10230 f4 .cfa: sp 0 + .ra: x30
STACK CFI 10238 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10240 x19: .cfa -16 + ^
STACK CFI 1031c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10330 98 .cfa: sp 0 + .ra: x30
STACK CFI 10334 .cfa: sp 80 +
STACK CFI 10338 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 103c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 103d0 114 .cfa: sp 0 + .ra: x30
STACK CFI 103d4 .cfa: sp 80 +
STACK CFI 103d8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 103e0 x19: .cfa -16 + ^
STACK CFI 104b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 104bc .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 104f0 740 .cfa: sp 0 + .ra: x30
STACK CFI 104f4 .cfa: sp 208 +
STACK CFI 104f8 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 10500 v8: .cfa -64 + ^
STACK CFI 10508 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 10518 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 10520 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1052c x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 108e0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 108e4 .cfa: sp 208 + .ra: .cfa -152 + ^ v8: .cfa -64 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 10c30 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c60 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c90 244 .cfa: sp 0 + .ra: x30
STACK CFI 10c94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10ca0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10cac x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10cb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10d1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 10e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10e74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 10ee0 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11000 308 .cfa: sp 0 + .ra: x30
STACK CFI 11004 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11024 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 110b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 110b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 110bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 110c0 x27: .cfa -16 + ^
STACK CFI 11298 x21: x21 x22: x22
STACK CFI 1129c x23: x23 x24: x24
STACK CFI 112a0 x25: x25 x26: x26
STACK CFI 112a4 x27: x27
STACK CFI 112b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 112b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 112e8 x21: x21 x22: x22
STACK CFI 112ec x23: x23 x24: x24
STACK CFI 112f0 x25: x25 x26: x26
STACK CFI 112f4 x27: x27
STACK CFI 11304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11310 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11370 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 113d0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 113d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 113dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 113ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 113f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1148c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 114b0 234 .cfa: sp 0 + .ra: x30
STACK CFI 114b4 .cfa: sp 96 +
STACK CFI 114b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 114c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11648 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 116b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 116b4 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 116f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 116f4 .cfa: sp 96 +
STACK CFI 116f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11700 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 117a0 11c .cfa: sp 0 + .ra: x30
STACK CFI 117a4 .cfa: sp 96 +
STACK CFI 117ac .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 117bc x19: .cfa -16 + ^
STACK CFI 11894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11898 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 118c0 13c .cfa: sp 0 + .ra: x30
STACK CFI 118c8 .cfa: sp 96 +
STACK CFI 118d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 118dc x19: .cfa -16 + ^
STACK CFI 119b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 119c0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11a00 574 .cfa: sp 0 + .ra: x30
STACK CFI 11a04 .cfa: sp 208 +
STACK CFI 11a08 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 11a10 v8: .cfa -48 + ^
STACK CFI 11a18 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 11a28 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 11a30 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 11a40 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 11dac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11db0 .cfa: sp 208 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 11f80 48 .cfa: sp 0 + .ra: x30
STACK CFI 11f98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11fd0 20 .cfa: sp 0 + .ra: x30
STACK CFI 11fd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11fec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11ff0 20 .cfa: sp 0 + .ra: x30
STACK CFI 11ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1200c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12010 98 .cfa: sp 0 + .ra: x30
STACK CFI 12014 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1201c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1208c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1209c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 120b0 18 .cfa: sp 0 + .ra: x30
