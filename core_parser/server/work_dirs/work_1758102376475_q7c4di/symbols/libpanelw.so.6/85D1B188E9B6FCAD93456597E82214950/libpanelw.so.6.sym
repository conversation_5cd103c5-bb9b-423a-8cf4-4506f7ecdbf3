MODULE Linux arm64 85D1B188E9B6FCAD93456597E82214950 libpanelw.so.6
INFO CODE_ID 88B1D185B6E9ADFC93456597E8221495695D2513
PUBLIC 1638 0 ground_panel
PUBLIC 1688 0 panel_above
PUBLIC 16a8 0 ceiling_panel
PUBLIC 16f8 0 panel_below
PUBLIC 1750 0 bottom_panel
PUBLIC 19f0 0 del_panel
PUBLIC 1c70 0 hide_panel
PUBLIC 1ee8 0 panel_hidden
PUBLIC 1f48 0 move_panel
PUBLIC 2168 0 new_panel
PUBLIC 2220 0 replace_panel
PUBLIC 2438 0 show_panel
PUBLIC 26d0 0 top_panel
PUBLIC 26d8 0 update_panels_sp
PUBLIC 28b0 0 update_panels
PUBLIC 28c0 0 set_panel_userptr
PUBLIC 28e0 0 panel_userptr
PUBLIC 28f8 0 panel_window
STACK CFI INIT 1578 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15a8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 15ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15f4 x19: .cfa -16 + ^
STACK CFI 162c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1638 4c .cfa: sp 0 + .ra: x30
STACK CFI 1660 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1678 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 167c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1688 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 16a8 4c .cfa: sp 0 + .ra: x30
STACK CFI 16d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16f8 54 .cfa: sp 0 + .ra: x30
STACK CFI 1700 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1708 x19: .cfa -16 + ^
STACK CFI 1738 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1750 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 1758 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1760 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 176c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 184c x23: x23 x24: x24
STACK CFI 18a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 18ec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1918 x25: .cfa -16 + ^
STACK CFI 19c8 x25: x25
STACK CFI 19d0 x25: .cfa -16 + ^
STACK CFI 19e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 19f0 280 .cfa: sp 0 + .ra: x30
STACK CFI 19f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 19fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1a08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a4c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1ad4 x23: x23 x24: x24
STACK CFI 1b18 x21: x21 x22: x22
STACK CFI 1b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1b6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1b98 x25: .cfa -16 + ^
STACK CFI 1c48 x25: x25
STACK CFI 1c50 x25: .cfa -16 + ^
STACK CFI 1c68 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 1c70 278 .cfa: sp 0 + .ra: x30
STACK CFI 1c78 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1c80 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1c8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1ccc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d54 x23: x23 x24: x24
STACK CFI 1d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1da0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1de4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1e10 x25: .cfa -16 + ^
STACK CFI 1ec0 x25: x25
STACK CFI 1ec8 x25: .cfa -16 + ^
STACK CFI 1ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 1ee8 5c .cfa: sp 0 + .ra: x30
STACK CFI 1ef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ef8 x19: .cfa -16 + ^
STACK CFI 1f20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f48 21c .cfa: sp 0 + .ra: x30
STACK CFI 1f50 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1fb0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 202c x23: x23 x24: x24
STACK CFI 2040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2044 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 205c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2070 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2124 x25: x25 x26: x26
STACK CFI 212c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2154 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 215c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2168 b8 .cfa: sp 0 + .ra: x30
STACK CFI 216c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2174 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 21d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 21e0 x21: .cfa -16 + ^
STACK CFI 21f4 x21: x21
STACK CFI 21f8 x21: .cfa -16 + ^
STACK CFI 221c x21: x21
STACK CFI INIT 2220 218 .cfa: sp 0 + .ra: x30
STACK CFI 2228 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2230 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 223c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2280 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2304 x23: x23 x24: x24
STACK CFI 2318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 231c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2334 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2360 x25: .cfa -16 + ^
STACK CFI 2410 x25: x25
STACK CFI 2418 x25: .cfa -16 + ^
STACK CFI 2430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2438 294 .cfa: sp 0 + .ra: x30
STACK CFI 2440 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2448 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2454 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 252c x23: x23 x24: x24
STACK CFI 257c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2580 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 25b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 25e0 x25: .cfa -16 + ^
STACK CFI 2690 x25: x25
STACK CFI 2698 x25: .cfa -16 + ^
STACK CFI 26b0 x23: x23 x24: x24 x25: x25
STACK CFI 26c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 26d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26d8 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 26e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 26ec x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2700 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2704 x25: .cfa -16 + ^
STACK CFI 27b4 x23: x23 x24: x24
STACK CFI 27b8 x25: x25
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 28a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 28b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28f8 14 .cfa: sp 0 + .ra: x30
