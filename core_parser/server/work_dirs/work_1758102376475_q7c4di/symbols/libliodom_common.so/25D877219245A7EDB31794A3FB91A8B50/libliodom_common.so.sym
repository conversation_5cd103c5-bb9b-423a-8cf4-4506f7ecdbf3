MODULE Linux arm64 25D877219245A7EDB31794A3FB91A8B50 libliodom_common.so
INFO CODE_ID 2177D8254592EDA7B31794A3FB91A8B5
FILE 0 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/common/base.cpp
FILE 1 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/common/common.cpp
FILE 2 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/cmath
FILE 3 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/iostream
FILE 4 /opt/aarch64--glibc--stable-2022.03-1/lib/gcc/aarch64-buildroot-linux-gnu/9.3.0/include/arm_neon.h
FILE 5 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/CoreEvaluators.h
FILE 6 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/Matrix.h
FILE 7 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/PlainObjectBase.h
FILE 8 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/ProductEvaluators.h
FILE 9 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h
FILE 10 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h
FILE 11 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h
FILE 12 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h
FILE 13 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/AngleAxis.h
FILE 14 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/OrthoMethods.h
FILE 15 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/Quaternion.h
FILE 16 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/arch/Geometry_SIMD.h
FUNC ff0 40 0 _GLOBAL__sub_I_base.cpp
ff0 c 380 0
ffc 28 74 3
1024 c 380 0
FUNC 1030 4 0 _GLOBAL__sub_I_common.cpp
1030 4 5 1
FUNC 1110 48 0 li::distance2d(Eigen::Matrix<float, 2, 1, 0, 2, 1> const&, Eigen::Matrix<float, 2, 1, 0, 2, 1> const&)
1110 8 6 0
1118 4 7 0
111c 4 6 0
1120 8 7 0
1128 4 7 0
112c 4 7 0
1130 4 7 0
1134 c 464 2
1140 10 8 0
1150 4 464 2
1154 4 7 0
FUNC 1160 58 0 li::distance3d(Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&)
1160 8 10 0
1168 4 11 0
116c 4 10 0
1170 4 11 0
1174 4 11 0
1178 4 11 0
117c 4 11 0
1180 4 11 0
1184 4 11 0
1188 4 11 0
118c 4 11 0
1190 4 11 0
1194 c 464 2
11a0 10 12 0
11b0 4 464 2
11b4 4 11 0
FUNC 11c0 164 0 li::df_rotatePoint(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, float*, float*, float*)
11c0 4 27 0
11c4 4 26 0
11c8 4 30 0
11cc 4 31 0
11d0 4 40 0
11d4 4 39 0
11d8 4 34 0
11dc 4 39 0
11e0 4 41 0
11e4 4 32 0
11e8 4 41 0
11ec 4 40 0
11f0 4 40 0
11f4 4 39 0
11f8 4 39 0
11fc 4 40 0
1200 4 41 0
1204 4 37 0
1208 4 40 0
120c 4 39 0
1210 4 37 0
1214 4 39 0
1218 4 41 0
121c 4 41 0
1220 4 40 0
1224 4 41 0
1228 4 39 0
122c 4 39 0
1230 4 40 0
1234 4 41 0
1238 4 40 0
123c 4 41 0
1240 4 39 0
1244 4 40 0
1248 4 41 0
124c 4 39 0
1250 4 40 0
1254 4 41 0
1258 4 40 0
125c 4 43 0
1260 4 44 0
1264 4 44 0
1268 4 48 0
126c 4 47 0
1270 4 48 0
1274 4 47 0
1278 4 46 0
127c 4 49 0
1280 4 46 0
1284 4 49 0
1288 4 58 0
128c 4 51 0
1290 4 53 0
1294 4 52 0
1298 4 47 0
129c 4 48 0
12a0 4 46 0
12a4 4 49 0
12a8 4 51 0
12ac 4 58 0
12b0 4 53 0
12b4 4 52 0
12b8 4 47 0
12bc 4 48 0
12c0 4 46 0
12c4 4 49 0
12c8 4 51 0
12cc 4 58 0
12d0 4 53 0
12d4 4 52 0
12d8 4 47 0
12dc 4 49 0
12e0 4 52 0
12e4 4 54 0
12e8 4 57 0
12ec 4 59 0
12f0 4 63 0
12f4 4 65 0
12f8 4 75 0
12fc 24 65 0
1320 4 78 0
FUNC 1330 50 0 li::toGlobal(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1>&)
1330 4 14 0
1334 8 15 0
133c 8 14 0
1344 4 14 0
1348 4 14 0
134c 4 15 0
1350 4 15 0
1354 8 17068 4
135c 4 49 10
1360 4 676 4
1364 4 27500 4
1368 8 49 10
1370 4 49 10
1374 4 17 0
1378 8 17 0
FUNC 1380 78 0 li::toLocal(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1>&)
1380 4 19 0
1384 8 60 16
138c 8 19 0
1394 8 19 0
139c 4 60 16
13a0 4 20 0
13a4 4 60 16
13a8 4 20 0
13ac 4 60 16
13b0 4 20 0
13b4 4 17541 4
13b8 4 20 0
13bc 4 17541 4
13c0 4 1819 4
13c4 4 27605 4
13c8 4 20 0
13cc 4 17068 4
13d0 4 17068 4
13d4 4 70 10
13d8 4 2078 4
13dc 4 27500 4
13e0 8 70 10
13e8 4 70 10
13ec 4 22 0
13f0 8 22 0
FUNC 1400 114 0 li::df_rotate(Eigen::Quaternion<float, 0> const&, Eigen::Quaternion<float, 0> const&, float*, float*, float*)
1400 4 83 0
1404 4 87 0
1408 4 81 0
140c 4 85 0
1410 4 99 0
1414 4 91 0
1418 4 92 0
141c 4 91 0
1420 4 92 0
1424 4 93 0
1428 4 90 0
142c 4 93 0
1430 4 90 0
1434 4 101 0
1438 4 91 0
143c 4 92 0
1440 4 93 0
1444 4 90 0
1448 4 92 0
144c 4 91 0
1450 4 93 0
1454 4 90 0
1458 4 95 0
145c 4 95 0
1460 4 95 0
1464 4 106 0
1468 4 101 0
146c 4 97 0
1470 4 96 0
1474 4 96 0
1478 4 103 0
147c 4 102 0
1480 4 105 0
1484 4 106 0
1488 4 101 0
148c 4 111 0
1490 4 109 0
1494 4 107 0
1498 4 110 0
149c 4 105 0
14a0 4 102 0
14a4 4 107 0
14a8 4 110 0
14ac 4 111 0
14b0 4 115 0
14b4 4 118 0
14b8 4 120 0
14bc 4 119 0
14c0 4 122 0
14c4 4 128 0
14c8 4 118 0
14cc 4 120 0
14d0 4 124 0
14d4 4 126 0
14d8 4 130 0
14dc 4 132 0
14e0 4 136 0
14e4 4 140 0
14e8 4 141 0
14ec 4 139 0
14f0 4 143 0
14f4 4 147 0
14f8 4 139 0
14fc 4 141 0
1500 4 145 0
1504 4 149 0
1508 4 151 0
150c 4 153 0
1510 4 156 0
FUNC 1520 1f8 0 li::eulerangle2quaternion(Eigen::Matrix<double, 3, 1, 0, 3, 1> const&, Eigen::Quaternion<double, 0>&)
1520 4 160 0
1524 4 24 10
1528 c 160 0
1534 4 24 10
1538 8 24 10
1540 4 80 13
1544 4 160 0
1548 4 565 15
154c 18 160 0
1564 4 160 0
1568 4 27612 4
156c 4 24 10
1570 4 80 13
1574 4 24 10
1578 4 24 10
157c 4 80 13
1580 8 561 15
1588 4 122 5
158c 8 17548 4
1594 4 80 11
1598 4 565 15
159c 4 1461 4
15a0 8 80 11
15a8 4 27612 4
15ac 4 566 15
15b0 4 561 15
15b4 8 80 11
15bc c 1826 4
15c8 8 80 11
15d0 4 17548 4
15d4 4 24 10
15d8 4 114 16
15dc 4 566 15
15e0 4 1461 4
15e4 4 15667 4
15e8 4 17548 4
15ec 4 112 16
15f0 4 27612 4
15f4 4 80 13
15f8 4 1461 4
15fc 4 15667 4
1600 4 1461 4
1604 4 80 13
1608 8 1461 4
1610 8 24 10
1618 8 2162 4
1620 4 1461 4
1624 4 24 10
1628 4 760 4
162c 4 6281 4
1630 4 760 4
1634 4 3322 4
1638 4 3322 4
163c 4 1826 4
1640 10 6545 4
1650 4 760 4
1654 4 1826 4
1658 4 3322 4
165c 4 760 4
1660 8 27612 4
1668 4 561 15
166c 8 80 11
1674 8 1826 4
167c 4 80 11
1680 4 112 16
1684 4 114 16
1688 4 566 15
168c 4 17548 4
1690 8 15667 4
1698 4 1461 4
169c 4 17548 4
16a0 4 165 0
16a4 c 1461 4
16b0 4 1461 4
16b4 8 2162 4
16bc 4 760 4
16c0 4 165 0
16c4 4 760 4
16c8 4 3322 4
16cc 4 760 4
16d0 4 3322 4
16d4 4 1826 4
16d8 10 6545 4
16e8 4 760 4
16ec 4 1826 4
16f0 4 3322 4
16f4 4 760 4
16f8 8 27612 4
1700 4 504 7
1704 4 165 0
1708 4 504 7
170c 4 165 0
1710 4 165 0
1714 4 165 0
FUNC 1720 b4 0 li::df_rotateinv(Eigen::Quaternion<float, 0> const&, float*, float*)
1720 4 183 0
1724 4 170 0
1728 4 168 0
172c 4 177 0
1730 4 178 0
1734 4 185 0
1738 4 190 0
173c 4 179 0
1740 4 181 0
1744 4 186 0
1748 4 187 0
174c 4 189 0
1750 4 190 0
1754 4 185 0
1758 4 195 0
175c 4 193 0
1760 4 191 0
1764 4 194 0
1768 4 185 0
176c 4 190 0
1770 4 195 0
1774 4 186 0
1778 4 189 0
177c 4 187 0
1780 4 193 0
1784 4 191 0
1788 4 194 0
178c 4 186 0
1790 4 189 0
1794 4 191 0
1798 4 194 0
179c 4 195 0
17a0 4 198 0
17a4 4 200 0
17a8 28 200 0
17d0 4 218 0
FUNC 17e0 61c 0 li::df_delta_pqv(float, float const*, float const*, float const*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*)
17e0 4 223 0
17e4 4 80 11
17e8 4 1390 4
17ec 4 223 0
17f0 4 395 6
17f4 4 393 6
17f8 4 223 0
17fc 4 80 11
1800 4 393 6
1804 4 1390 4
1808 4 393 6
180c 4 15560 4
1810 4 395 6
1814 4 223 0
1818 4 395 6
181c 4 17068 4
1820 4 2078 4
1824 4 395 6
1828 4 405 6
182c 4 359 11
1830 4 42 11
1834 4 393 6
1838 4 405 6
183c 4 676 4
1840 4 393 6
1844 4 359 11
1848 8 393 6
1850 4 393 6
1854 4 17068 4
1858 4 405 6
185c 4 223 0
1860 4 395 6
1864 4 393 6
1868 4 223 0
186c 4 2078 4
1870 4 405 6
1874 8 223 0
187c 4 393 6
1880 c 223 0
188c 4 223 0
1890 4 393 6
1894 4 60 16
1898 4 395 6
189c 4 223 0
18a0 4 393 6
18a4 4 395 6
18a8 4 393 6
18ac 4 393 6
18b0 4 2078 4
18b4 4 393 6
18b8 4 27500 4
18bc 4 359 11
18c0 4 45 14
18c4 4 42 11
18c8 4 405 6
18cc 8 85 9
18d4 4 676 4
18d8 4 45 14
18dc 4 395 6
18e0 4 45 14
18e4 4 46 14
18e8 4 46 14
18ec 4 393 6
18f0 4 45 14
18f4 4 60 16
18f8 4 46 14
18fc 4 45 14
1900 4 47 14
1904 4 395 6
1908 4 47 14
190c 10 85 9
191c 4 60 16
1920 4 540 15
1924 8 85 9
192c 4 393 6
1930 4 394 6
1934 4 49 10
1938 4 17068 4
193c 4 395 6
1940 4 46 14
1944 4 27500 4
1948 4 676 4
194c 4 24 10
1950 4 60 16
1954 4 42 11
1958 4 45 14
195c 4 46 14
1960 4 223 0
1964 4 676 4
1968 8 223 0
1970 4 27500 4
1974 4 45 14
1978 4 85 9
197c 4 46 14
1980 4 85 9
1984 4 45 14
1988 4 46 14
198c 4 45 14
1990 4 47 14
1994 4 45 14
1998 4 47 14
199c 8 85 9
19a4 4 45 14
19a8 4 393 6
19ac 4 394 6
19b0 4 17068 4
19b4 4 42 11
19b8 4 60 16
19bc 4 17541 4
19c0 4 676 4
19c4 8 85 9
19cc 4 24 10
19d0 4 1819 4
19d4 8 32 16
19dc 4 85 9
19e0 4 32 16
19e4 4 17541 4
19e8 8 27605 4
19f0 4 393 6
19f4 4 85 9
19f8 4 27500 4
19fc 4 1454 4
1a00 8 85 9
1a08 4 1454 4
1a0c 4 2155 4
1a10 4 753 4
1a14 4 46 14
1a18 4 46 14
1a1c 4 47 14
1a20 4 1819 4
1a24 4 47 14
1a28 4 753 4
1a2c 4 394 6
1a30 4 17068 4
1a34 4 49 10
1a38 4 27605 4
1a3c 4 676 4
1a40 4 46 14
1a44 4 42 11
1a48 4 49 10
1a4c 4 27500 4
1a50 4 676 4
1a54 4 45 14
1a58 4 46 14
1a5c 4 45 14
1a60 4 45 14
1a64 4 46 14
1a68 4 47 14
1a6c 4 47 14
1a70 4 393 6
1a74 4 394 6
1a78 4 17068 4
1a7c 4 42 11
1a80 4 395 6
1a84 4 676 4
1a88 4 24 10
1a8c 4 27500 4
1a90 4 237 0
1a94 8 239 0
1a9c 8 238 0
1aa4 4 239 0
1aa8 4 238 0
1aac 4 240 0
1ab0 4 238 0
1ab4 8 241 0
1abc 4 239 0
1ac0 4 240 0
1ac4 4 240 0
1ac8 4 239 0
1acc 4 241 0
1ad0 4 239 0
1ad4 4 240 0
1ad8 4 241 0
1adc 4 244 0
1ae0 4 24 12
1ae4 4 22884 4
1ae8 8 245 0
1af0 4 245 0
1af4 4 245 0
1af8 4 24 12
1afc 4 245 0
1b00 4 27500 4
1b04 4 24 10
1b08 4 245 0
1b0c 4 248 0
1b10 8 80 11
1b18 4 1390 4
1b1c 8 359 11
1b24 4 249 0
1b28 4 80 11
1b2c 8 17068 4
1b34 4 249 0
1b38 4 17068 4
1b3c 8 359 11
1b44 4 249 0
1b48 4 42 11
1b4c 4 2078 4
1b50 4 1390 4
1b54 4 17068 4
1b58 8 249 0
1b60 4 359 11
1b64 4 676 4
1b68 4 2078 4
1b6c 4 24 10
1b70 4 27500 4
1b74 4 249 0
1b78 4 252 0
1b7c 4 17068 4
1b80 4 253 0
1b84 4 24 12
1b88 4 253 0
1b8c 4 253 0
1b90 4 253 0
1b94 4 22884 4
1b98 4 253 0
1b9c 4 80 11
1ba0 4 1390 4
1ba4 4 24 10
1ba8 4 27500 4
1bac 4 253 0
1bb0 4 256 0
1bb4 8 257 0
1bbc 4 257 0
1bc0 c 257 0
1bcc 4 260 0
1bd0 c 261 0
1bdc 4 264 0
1be0 c 265 0
1bec 8 269 0
1bf4 c 60 16
1c00 4 272 0
1c04 4 17541 4
1c08 4 27605 4
1c0c 4 60 16
1c10 4 272 0
1c14 4 60 16
1c18 4 272 0
1c1c 4 17541 4
1c20 8 272 0
1c28 4 17541 4
1c2c 4 1819 4
1c30 4 27605 4
1c34 4 272 0
1c38 10 273 0
1c48 8 689 8
1c50 4 17541 4
1c54 10 689 8
1c64 10 1454 4
1c74 4 16729 4
1c78 4 689 8
1c7c 4 16729 4
1c80 4 689 8
1c84 4 16729 4
1c88 4 689 8
1c8c 4 16729 4
1c90 4 689 8
1c94 4 17541 4
1c98 4 16729 4
1c9c 4 689 8
1ca0 4 16729 4
1ca4 4 689 8
1ca8 4 16729 4
1cac 4 689 8
1cb0 4 16729 4
1cb4 4 689 8
1cb8 4 17541 4
1cbc 4 16729 4
1cc0 c 16729 4
1ccc 4 275 0
1cd0 c 275 0
1cdc 4 27605 4
1ce0 4 27605 4
1ce4 4 278 0
1ce8 c 60 16
1cf4 4 279 0
1cf8 4 17541 4
1cfc 4 279 0
1d00 4 60 16
1d04 4 279 0
1d08 4 60 16
1d0c 4 279 0
1d10 4 17541 4
1d14 4 279 0
1d18 4 1819 4
1d1c 4 27605 4
1d20 4 279 0
1d24 4 283 0
1d28 4 17068 4
1d2c 4 284 0
1d30 4 24 12
1d34 4 284 0
1d38 4 284 0
1d3c 4 284 0
1d40 4 22884 4
1d44 4 284 0
1d48 4 24 12
1d4c 4 27500 4
1d50 4 24 10
1d54 4 284 0
1d58 8 287 0
1d60 4 17068 4
1d64 4 288 0
1d68 4 17068 4
1d6c 4 288 0
1d70 4 359 11
1d74 4 288 0
1d78 4 359 11
1d7c 4 288 0
1d80 4 2078 4
1d84 4 42 11
1d88 4 288 0
1d8c 4 359 11
1d90 4 17068 4
1d94 4 42 11
1d98 8 676 4
1da0 4 24 10
1da4 4 27500 4
1da8 4 288 0
1dac 8 291 0
1db4 8 292 0
1dbc 4 292 0
1dc0 c 292 0
1dcc 4 295 0
1dd0 c 296 0
1ddc 4 299 0
1de0 10 299 0
1df0 8 299 0
1df8 4 299 0
FUNC 1e00 288 0 li::df_delta_speed(float, float, float const*, float*, float*, float*, float*, float*)
1e00 4 302 0
1e04 4 60 16
1e08 4 60 16
1e0c 4 302 0
1e10 4 405 6
1e14 c 60 16
1e20 4 408 6
1e24 4 395 6
1e28 4 17541 4
1e2c 8 302 0
1e34 4 393 6
1e38 4 393 6
1e3c 4 1819 4
1e40 4 395 6
1e44 4 45 14
1e48 4 302 0
1e4c 4 46 14
1e50 4 17068 4
1e54 4 27605 4
1e58 4 45 14
1e5c 4 405 6
1e60 4 540 15
1e64 4 46 14
1e68 4 47 14
1e6c 4 47 14
1e70 4 45 14
1e74 4 46 14
1e78 4 45 14
1e7c 4 49 10
1e80 4 394 6
1e84 4 17068 4
1e88 4 42 11
1e8c 4 46 14
1e90 4 49 10
1e94 4 676 4
1e98 4 676 4
1e9c 4 27500 4
1ea0 8 45 14
1ea8 4 46 14
1eac 4 45 14
1eb0 4 47 14
1eb4 4 47 14
1eb8 4 394 6
1ebc 4 17068 4
1ec0 4 42 11
1ec4 4 676 4
1ec8 4 24 10
1ecc 4 27500 4
1ed0 4 306 0
1ed4 8 306 0
1edc 4 306 0
1ee0 4 308 0
1ee4 4 309 0
1ee8 4 311 0
1eec 4 312 0
1ef0 4 314 0
1ef4 4 60 16
1ef8 4 60 16
1efc 4 60 16
1f00 4 316 0
1f04 4 316 0
1f08 4 316 0
1f0c 4 316 0
1f10 4 316 0
1f14 8 60 16
1f1c 4 17541 4
1f20 4 27605 4
1f24 8 17541 4
1f2c 4 1819 4
1f30 4 27605 4
1f34 4 316 0
1f38 10 318 0
1f48 4 689 8
1f4c 4 17068 4
1f50 8 689 8
1f58 4 1390 4
1f5c 4 689 8
1f60 4 1390 4
1f64 4 17068 4
1f68 4 1390 4
1f6c 4 80 11
1f70 4 1390 4
1f74 4 42 11
1f78 4 689 8
1f7c 4 16722 4
1f80 4 689 8
1f84 8 80 11
1f8c 4 16722 4
1f90 8 42 11
1f98 4 17068 4
1f9c 4 16722 4
1fa0 4 689 8
1fa4 4 16722 4
1fa8 c 80 11
1fb4 c 42 11
1fc0 4 689 8
1fc4 4 16722 4
1fc8 4 17068 4
1fcc 4 16722 4
1fd0 4 689 8
1fd4 8 16722 4
1fdc 4 689 8
1fe0 4 16722 4
1fe4 c 16722 4
1ff0 8 42 11
1ff8 4 27500 4
1ffc 4 320 0
2000 4 27500 4
2004 4 27500 4
2008 4 27500 4
200c 4 320 0
2010 4 24 10
2014 4 24 10
2018 4 321 0
201c 8 322 0
2024 4 322 0
2028 4 327 0
202c c 60 16
2038 4 329 0
203c 4 17541 4
2040 4 329 0
2044 4 60 16
2048 4 329 0
204c 4 60 16
2050 4 329 0
2054 4 17541 4
2058 4 329 0
205c 4 1819 4
2060 4 27605 4
2064 4 329 0
2068 4 330 0
206c 4 331 0
2070 4 332 0
2074 4 331 0
2078 4 332 0
207c 4 335 0
2080 4 335 0
2084 4 335 0
FUNC 2090 54 0 li::df_delta_xy(float const*, float const*, float*, float*, float*, float*)
2090 4 341 0
2094 4 394 6
2098 8 342 0
20a0 4 342 0
20a4 4 343 0
20a8 4 343 0
20ac 4 343 0
20b0 4 346 0
20b4 4 348 0
20b8 8 348 0
20c0 4 355 0
20c4 8 357 0
20cc 4 362 0
20d0 4 357 0
20d4 4 366 0
20d8 4 368 0
20dc 4 374 0
20e0 4 379 0
PUBLIC f28 0 _init
PUBLIC 1034 0 call_weak_fn
PUBLIC 1048 0 deregister_tm_clones
PUBLIC 1078 0 register_tm_clones
PUBLIC 10b4 0 __do_global_dtors_aux
PUBLIC 1104 0 frame_dummy
PUBLIC 20e4 0 _fini
STACK CFI INIT 1048 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1078 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10b4 50 .cfa: sp 0 + .ra: x30
STACK CFI 10c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10cc x19: .cfa -16 + ^
STACK CFI 10fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1104 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1110 48 .cfa: sp 0 + .ra: x30
STACK CFI 1114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1120 v8: .cfa -16 + ^
STACK CFI 114c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 1150 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1160 58 .cfa: sp 0 + .ra: x30
STACK CFI 1164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1170 v8: .cfa -16 + ^
STACK CFI 11ac .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 11b0 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11c0 164 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1330 50 .cfa: sp 0 + .ra: x30
STACK CFI 1334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 137c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1380 78 .cfa: sp 0 + .ra: x30
STACK CFI 1384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1394 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1400 114 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1520 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 1524 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1530 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 1548 v8: .cfa -216 + ^
STACK CFI 1550 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1568 x23: .cfa -224 + ^
STACK CFI 1714 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1720 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e0 61c .cfa: sp 0 + .ra: x30
STACK CFI 17e4 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 180c v8: .cfa -368 + ^ v9: .cfa -360 + ^
STACK CFI 1878 x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 188c x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^
STACK CFI 1df8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 1e00 288 .cfa: sp 0 + .ra: x30
STACK CFI 1e04 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1e30 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1f24 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 2024 x21: x21 x22: x22
STACK CFI 2084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2090 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff0 40 .cfa: sp 0 + .ra: x30
STACK CFI ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ffc x19: .cfa -16 + ^
STACK CFI 102c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1030 4 .cfa: sp 0 + .ra: x30
