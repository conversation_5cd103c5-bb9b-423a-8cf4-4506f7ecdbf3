MODULE Linux arm64 D136BCD424C9B821A2C1A548A3EED91D0 libebl_x86_64.so
INFO CODE_ID D4BC36D1C92421B8A2C1A548A3EED91DAF6F644A
PUBLIC 21d0 0 x86_64_init
STACK CFI INIT 1fe8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2018 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2058 48 .cfa: sp 0 + .ra: x30
STACK CFI 205c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2064 x19: .cfa -16 + ^
STACK CFI 209c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 20a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2100 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2110 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2120 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2130 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2134 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 213c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 21a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21a8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 21d0 fc .cfa: sp 0 + .ra: x30
STACK CFI INIT 22d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2310 264 .cfa: sp 0 + .ra: x30
STACK CFI 2314 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2320 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2330 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2338 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 23c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 23cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2418 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2578 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25a0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 25a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 25ac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 25c0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 25d4 x23: .cfa -96 + ^
STACK CFI 267c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2680 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2848 3d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c50 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c90 170 .cfa: sp 0 + .ra: x30
STACK CFI 2c94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2c9c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2cac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2cc0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2cf4 x25: .cfa -64 + ^
STACK CFI 2dbc x25: x25
STACK CFI 2dc8 x25: .cfa -64 + ^
STACK CFI 2dcc x25: x25
STACK CFI 2df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2df8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2dfc x25: .cfa -64 + ^
STACK CFI INIT 2e00 264 .cfa: sp 0 + .ra: x30
STACK CFI 2e04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e28 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ebc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3068 114 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3180 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3228 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32c8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3378 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 33a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 33a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 341c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3420 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 342c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3430 198 .cfa: sp 0 + .ra: x30
STACK CFI 3434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3440 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3450 x21: .cfa -16 + ^
STACK CFI 34d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 34dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 34f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 34f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 356c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 35c8 128 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35fc x21: .cfa -16 + ^
STACK CFI 3608 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3674 x19: x19 x20: x20
STACK CFI 367c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 36cc x19: x19 x20: x20
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 36d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 36e0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 36e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 36f0 174 .cfa: sp 0 + .ra: x30
STACK CFI 36f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3700 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3718 x21: .cfa -16 + ^
STACK CFI 379c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 37a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3804 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 385c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3868 94 .cfa: sp 0 + .ra: x30
STACK CFI 3880 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 38f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3900 c8 .cfa: sp 0 + .ra: x30
STACK CFI 3904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 390c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3988 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 39bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 39c8 94 .cfa: sp 0 + .ra: x30
STACK CFI 39cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3a60 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b08 10c .cfa: sp 0 + .ra: x30
STACK CFI 3b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b20 x21: .cfa -16 + ^
STACK CFI 3b2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3b9c x19: x19 x20: x20
STACK CFI 3ba8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3bac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3bb8 x19: x19 x20: x20
STACK CFI 3bc0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3bc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3bcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3c18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c38 100 .cfa: sp 0 + .ra: x30
STACK CFI 3c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3cc8 x19: x19 x20: x20
STACK CFI 3ccc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3cd8 x19: x19 x20: x20
STACK CFI 3cdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ce0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d10 x19: x19 x20: x20
STACK CFI 3d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3d38 dc .cfa: sp 0 + .ra: x30
STACK CFI 3d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3dcc x19: x19 x20: x20
STACK CFI 3dd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ddc x19: x19 x20: x20
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3e18 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e70 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 3e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4050 1dc .cfa: sp 0 + .ra: x30
STACK CFI 4074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4080 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 408c x21: .cfa -16 + ^
STACK CFI 4144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4148 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 41b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4208 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4230 184 .cfa: sp 0 + .ra: x30
STACK CFI 4234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4240 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4364 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4378 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43b8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 43bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4448 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4458 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4480 578 .cfa: sp 0 + .ra: x30
STACK CFI 4484 .cfa: sp 160 +
STACK CFI 4488 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4490 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 44b0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 44cc x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 455c x27: .cfa -64 + ^
STACK CFI 4598 x27: x27
STACK CFI 4638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 463c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 46e8 x27: .cfa -64 + ^
STACK CFI 4818 x27: x27
STACK CFI 4860 x27: .cfa -64 + ^
STACK CFI 487c x27: x27
STACK CFI 4890 x27: .cfa -64 + ^
STACK CFI 4934 x27: x27
STACK CFI 4944 x27: .cfa -64 + ^
STACK CFI 495c x27: x27
STACK CFI 498c x27: .cfa -64 + ^
STACK CFI 49f0 x27: x27
STACK CFI 49f4 x27: .cfa -64 + ^
STACK CFI INIT 49f8 264 .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a08 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a40 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4a58 x21: x21 x22: x22
STACK CFI 4a68 x19: x19 x20: x20
STACK CFI 4a6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4aec x21: x21 x22: x22
STACK CFI 4b00 x19: x19 x20: x20
STACK CFI 4b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b74 x19: x19 x20: x20
STACK CFI 4b78 x21: x21 x22: x22
STACK CFI 4b7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4bc4 x21: x21 x22: x22
STACK CFI 4bc8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4bfc x21: x21 x22: x22
STACK CFI 4c00 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c2c x21: x21 x22: x22
STACK CFI 4c58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 4c60 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d28 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d38 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4dc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e08 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4ea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4ed0 13c .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5010 180 .cfa: sp 0 + .ra: x30
STACK CFI 5014 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5048 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 50b0 x19: x19 x20: x20
STACK CFI 50b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5104 x19: x19 x20: x20
STACK CFI 5108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 510c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5118 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5120 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5144 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 5190 214 .cfa: sp 0 + .ra: x30
STACK CFI 521c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5250 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 52b0 x19: x19 x20: x20
STACK CFI 52b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5304 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 530c x19: x19 x20: x20
STACK CFI 5310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 534c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5378 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 537c x19: x19 x20: x20
STACK CFI 53a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 53a8 214 .cfa: sp 0 + .ra: x30
STACK CFI 5434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5468 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54c8 x19: x19 x20: x20
STACK CFI 54cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 551c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5524 x19: x19 x20: x20
STACK CFI 5528 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5590 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5594 x19: x19 x20: x20
STACK CFI 55b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 55c0 22c .cfa: sp 0 + .ra: x30
STACK CFI 55c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 56d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 56d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5734 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5754 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5778 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57f0 218 .cfa: sp 0 + .ra: x30
STACK CFI 57f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 585c x19: x19 x20: x20
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5874 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5968 x19: x19 x20: x20
STACK CFI 596c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5970 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5994 x19: x19 x20: x20
STACK CFI 5998 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 599c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 59d4 x19: x19 x20: x20
STACK CFI 59dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 5a08 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a28 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 5be0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5c08 23c .cfa: sp 0 + .ra: x30
STACK CFI 5c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5dc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e48 220 .cfa: sp 0 + .ra: x30
STACK CFI 5e4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fe0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6068 274 .cfa: sp 0 + .ra: x30
STACK CFI 606c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6078 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 60d0 x21: x21 x22: x22
STACK CFI 60e8 x19: x19 x20: x20
STACK CFI 60ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 60f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 615c x19: x19 x20: x20
STACK CFI 6160 x21: x21 x22: x22
STACK CFI 6164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6168 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 61e8 x19: x19 x20: x20
STACK CFI 61ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 61f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 623c x19: x19 x20: x20
STACK CFI 6240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6244 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6270 x21: x21 x22: x22
STACK CFI 62d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 62e0 214 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 62f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 633c x23: .cfa -16 + ^
STACK CFI 63c8 x23: x23
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 644c x23: x23
STACK CFI 6468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 646c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 64c0 x23: x23
STACK CFI 64c4 x23: .cfa -16 + ^
STACK CFI 64c8 x23: x23
STACK CFI 64f0 x23: .cfa -16 + ^
STACK CFI INIT 64f8 1954 .cfa: sp 0 + .ra: x30
STACK CFI 64fc .cfa: sp 976 +
STACK CFI 650c .ra: .cfa -968 + ^ x29: .cfa -976 + ^
STACK CFI 6518 x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 6520 x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 6554 x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 6b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6b84 .cfa: sp 976 + .ra: .cfa -968 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^ x29: .cfa -976 + ^
