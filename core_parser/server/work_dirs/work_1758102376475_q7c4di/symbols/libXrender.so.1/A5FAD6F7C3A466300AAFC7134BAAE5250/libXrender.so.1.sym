MODULE Linux arm64 A5FAD6F7C3A466300AAFC7134BAAE5250 libXrender.so.1
INFO CODE_ID F7D6FAA5A4C330660AAFC7134BAAE525F03DE79B
PUBLIC 1970 0 _init
PUBLIC 1cf0 0 XRenderAddTraps
PUBLIC 1f48 0 XRenderParseColor
PUBLIC 2130 0 XRenderComposite
PUBLIC 2278 0 XRenderCreateCursor
PUBLIC 2368 0 XRenderCreateAnimCursor
PUBLIC 24f8 0 XRenderFillRectangle
PUBLIC 26d0 0 XRenderFillRectangles
PUBLIC 2950 0 XRenderQueryFilters
PUBLIC 2c30 0 XRenderSetPictureFilter
PUBLIC 2db0 0 XRenderCreateGlyphSet
PUBLIC 2e88 0 XRenderReferenceGlyphSet
PUBLIC 2f58 0 XRenderFreeGlyphSet
PUBLIC 3000 0 XRenderAddGlyphs
PUBLIC 3230 0 XRenderFreeGlyphs
PUBLIC 3398 0 XRenderCompositeString8
PUBLIC 3618 0 XRenderCompositeString16
PUBLIC 38a8 0 XRenderCompositeString32
PUBLIC 3b28 0 XRenderCompositeText8
PUBLIC 3e60 0 XRenderCompositeText16
PUBLIC 41a8 0 XRenderCompositeText32
PUBLIC 47b8 0 XRenderCreatePicture
PUBLIC 48d0 0 XRenderChangePicture
PUBLIC 49a8 0 XRenderSetPictureClipRectangles
PUBLIC 4a70 0 XRenderSetPictureClipRegion
PUBLIC 4be0 0 XRenderSetPictureTransform
PUBLIC 4cd8 0 XRenderFreePicture
PUBLIC 4d80 0 XRenderCreateSolidFill
PUBLIC 4e70 0 XRenderCreateLinearGradient
PUBLIC 50c8 0 XRenderCreateRadialGradient
PUBLIC 5330 0 XRenderCreateConicalGradient
PUBLIC 5590 0 XRenderCompositeDoublePoly
PUBLIC 5a80 0 XRenderCompositeTrapezoids
PUBLIC 5d10 0 XRenderCompositeTriangles
PUBLIC 5fc8 0 XRenderCompositeTriStrip
PUBLIC 6240 0 XRenderCompositeTriFan
PUBLIC 6678 0 XRenderFindDisplay
PUBLIC 6b88 0 XRenderQueryExtension
PUBLIC 6bf0 0 XRenderQueryFormats
PUBLIC 72c8 0 XRenderQueryVersion
PUBLIC 7348 0 XRenderQuerySubpixelOrder
PUBLIC 73c0 0 XRenderSetSubpixelOrder
PUBLIC 7440 0 XRenderFindVisualFormat
PUBLIC 74f0 0 XRenderFindFormat
PUBLIC 7718 0 XRenderFindStandardFormat
PUBLIC 7750 0 XRenderQueryPictIndexValues
PUBLIC 7958 0 _fini
