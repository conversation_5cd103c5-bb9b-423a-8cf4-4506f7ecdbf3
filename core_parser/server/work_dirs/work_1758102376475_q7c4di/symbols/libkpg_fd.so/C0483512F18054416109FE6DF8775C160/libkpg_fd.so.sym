MODULE Linux arm64 C0483512F18054416109FE6DF8775C160 libkpg_fd.so
INFO CODE_ID 123548C080F141546109FE6DF8775C16
PUBLIC 7fa0 0 _init
PUBLIC 8470 0 _GLOBAL__sub_I_trt_kpg_fd.cpp
PUBLIC 8510 0 _GLOBAL__sub_I_checkMacrosPlugin.cpp
PUBLIC 88b0 0 call_weak_fn
PUBLIC 88c4 0 deregister_tm_clones
PUBLIC 88f4 0 register_tm_clones
PUBLIC 8930 0 __do_global_dtors_aux
PUBLIC 8980 0 frame_dummy
PUBLIC 8990 0 trt_plugin::KPG_Plugin::getNbOutputs() const
PUBLIC 89a0 0 trt_plugin::KPG_Plugin::initialize() [clone .localalias]
PUBLIC 89b0 0 trt_plugin::KPG_Plugin::detachFromContext()
PUBLIC 89c0 0 trt_plugin::KPG_Plugin::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 89f0 0 trt_plugin::KPG_Plugin::getSerializationSize() const
PUBLIC 8a00 0 trt_plugin::KPG_Plugin::serialize(void*) const
PUBLIC 8a30 0 trt_plugin::KPG_Plugin::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 8a60 0 trt_plugin::KPG_Plugin::getPluginType() const
PUBLIC 8a70 0 trt_plugin::KPG_PluginCreator::getPluginVersion() const
PUBLIC 8a80 0 trt_plugin::KPG_Plugin::getPluginNamespace() const
PUBLIC 8a90 0 trt_plugin::KPG_Plugin::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 8ab0 0 trt_plugin::KPG_Plugin::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 8ac0 0 trt_plugin::KPG_Plugin::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 8ad0 0 trt_plugin::KPG_PluginCreator::getFieldNames()
PUBLIC 8ae0 0 trt_plugin::KPG_Plugin::~KPG_Plugin()
PUBLIC 8b40 0 trt_plugin::KPG_Plugin::~KPG_Plugin() [clone .localalias]
PUBLIC 8b70 0 trt_plugin::KPG_Plugin::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 8d60 0 trt_plugin::KPG_Plugin::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 8e10 0 trt_plugin::KPG_Plugin::setPluginNamespace(char const*)
PUBLIC 8e50 0 trt_plugin::KPG_Plugin::destroy()
PUBLIC 8ea0 0 trt_plugin::KPG_Plugin::KPG_Plugin(int, int, int, int)
PUBLIC 8ee0 0 trt_plugin::KPG_Plugin::clone() const
PUBLIC 8fa0 0 trt_plugin::KPG_PluginCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 9280 0 trt_plugin::KPG_Plugin::KPG_Plugin(void const*, unsigned long)
PUBLIC 92c0 0 trt_plugin::KPG_PluginCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 9390 0 trt_plugin::KPG_PluginCreator::KPG_PluginCreator()
PUBLIC 95e0 0 std::ctype<char>::do_widen(char) const
PUBLIC 95f0 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 9600 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 9610 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 9620 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 9630 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 9640 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 9650 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 9670 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 9680 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 9690 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 96a0 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 96b0 0 trt_plugin::BaseCreator::getPluginNamespace() const
PUBLIC 96c0 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 96f0 0 nvinfer1::PluginRegistrar<trt_plugin::KPG_PluginCreator>::~PluginRegistrar()
PUBLIC 9720 0 std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::~vector()
PUBLIC 9730 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 9780 0 trt_plugin::BaseCreator::setPluginNamespace(char const*)
PUBLIC 97c0 0 nvinfer1::plugin::caughtError(std::exception const&)
PUBLIC 9930 0 void std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::_M_realloc_insert<nvinfer1::PluginField>(__gnu_cxx::__normal_iterator<nvinfer1::PluginField*, std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> > >, nvinfer1::PluginField&&)
PUBLIC 9ad0 0 nvinfer1::plugin::TRTException::log(std::ostream&) const
PUBLIC 9cf0 0 nvinfer1::plugin::throwCudaError(char const*, char const*, int, int, char const*)
PUBLIC 9da0 0 nvinfer1::plugin::throwCublasError(char const*, char const*, int, int, char const*)
PUBLIC 9f30 0 nvinfer1::plugin::throwCudnnError(char const*, char const*, int, int, char const*)
PUBLIC 9fe0 0 nvinfer1::plugin::logError(char const*, char const*, char const*, int)
PUBLIC a380 0 nvinfer1::plugin::reportValidationFailure(char const*, char const*, int)
PUBLIC a770 0 nvinfer1::plugin::throwPluginError(char const*, char const*, int, int, char const*)
PUBLIC a820 0 nvinfer1::plugin::reportAssertion(char const*, char const*, int)
PUBLIC ac40 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC ac60 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC aca0 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC acc0 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC ad00 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC ad20 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC ad60 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC ad80 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC adc0 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC ade0 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC ae20 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC aed0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC af70 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b020 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b0b0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b160 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b200 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b2b0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b350 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b400 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b490 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b540 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b5e0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b690 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b720 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b7d0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b860 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC b8c0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC b920 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b980 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC b9e0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC ba40 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC baa0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC bb00 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC bb60 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::sync()
PUBLIC bd10 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::sync()
PUBLIC bec0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::sync()
PUBLIC c070 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::sync()
PUBLIC c220 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c280 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c2e0 0 _fini
STACK CFI INIT 88c4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8930 50 .cfa: sp 0 + .ra: x30
STACK CFI 8940 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8948 x19: .cfa -16 + ^
STACK CFI 8978 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9600 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9610 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9620 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9650 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a00 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ab0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ad0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ae0 60 .cfa: sp 0 + .ra: x30
STACK CFI 8ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8af4 x19: .cfa -16 + ^
STACK CFI 8b30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 96f0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9720 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b40 28 .cfa: sp 0 + .ra: x30
STACK CFI 8b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b4c x19: .cfa -16 + ^
STACK CFI 8b64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9730 48 .cfa: sp 0 + .ra: x30
STACK CFI 9734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9748 x19: .cfa -16 + ^
STACK CFI 9774 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b70 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8b74 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8b7c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8b8c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c2c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ce8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8d60 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 128 +
STACK CFI 8d68 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d74 x19: .cfa -16 + ^
STACK CFI 8df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8dfc .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8e10 40 .cfa: sp 0 + .ra: x30
STACK CFI 8e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e50 48 .cfa: sp 0 + .ra: x30
STACK CFI 8e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e74 x19: .cfa -16 + ^
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9780 40 .cfa: sp 0 + .ra: x30
STACK CFI 9784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 978c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 97bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 97c0 168 .cfa: sp 0 + .ra: x30
STACK CFI 97c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 97d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 97e0 x21: .cfa -16 + ^
STACK CFI 9894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9898 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 98d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 98dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8ea0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ee0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8eec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8f60 x21: .cfa -16 + ^
STACK CFI 8f78 x21: x21
STACK CFI 8f9c x21: .cfa -16 + ^
STACK CFI INIT 8fa0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 8fa4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8fb0 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8fc8 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 91b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 91bc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 9228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 922c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9280 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 92c0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 92c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 932c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9348 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9930 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 9934 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9944 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9950 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9968 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9a90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9a94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9390 244 .cfa: sp 0 + .ra: x30
STACK CFI 9394 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 93a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 93b8 x21: .cfa -48 + ^
STACK CFI 94f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 94f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8470 9c .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 847c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ac40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac60 38 .cfa: sp 0 + .ra: x30
STACK CFI ac64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac74 x19: .cfa -16 + ^
STACK CFI ac94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aca0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT acc0 38 .cfa: sp 0 + .ra: x30
STACK CFI acc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acd4 x19: .cfa -16 + ^
STACK CFI acf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad20 38 .cfa: sp 0 + .ra: x30
STACK CFI ad24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad34 x19: .cfa -16 + ^
STACK CFI ad54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad80 38 .cfa: sp 0 + .ra: x30
STACK CFI ad84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad94 x19: .cfa -16 + ^
STACK CFI adb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT adc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ade0 38 .cfa: sp 0 + .ra: x30
STACK CFI ade4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI adf4 x19: .cfa -16 + ^
STACK CFI ae14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae20 b0 .cfa: sp 0 + .ra: x30
STACK CFI ae24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae44 x21: .cfa -16 + ^
STACK CFI aecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT af70 a4 .cfa: sp 0 + .ra: x30
STACK CFI af74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI af84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af94 x21: .cfa -16 + ^
STACK CFI b010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b0b0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b0b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b0c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0d4 x21: .cfa -16 + ^
STACK CFI b15c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b200 b0 .cfa: sp 0 + .ra: x30
STACK CFI b204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b214 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b224 x21: .cfa -16 + ^
STACK CFI b2ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b350 a4 .cfa: sp 0 + .ra: x30
STACK CFI b354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b364 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b374 x21: .cfa -16 + ^
STACK CFI b3f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b490 b0 .cfa: sp 0 + .ra: x30
STACK CFI b494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b4a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b4b4 x21: .cfa -16 + ^
STACK CFI b53c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b5e0 a4 .cfa: sp 0 + .ra: x30
STACK CFI b5e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b604 x21: .cfa -16 + ^
STACK CFI b680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b720 a4 .cfa: sp 0 + .ra: x30
STACK CFI b724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b734 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b744 x21: .cfa -16 + ^
STACK CFI b7c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b860 54 .cfa: sp 0 + .ra: x30
STACK CFI b864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b878 x19: .cfa -16 + ^
STACK CFI b8b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b8c0 54 .cfa: sp 0 + .ra: x30
STACK CFI b8c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b8d8 x19: .cfa -16 + ^
STACK CFI b910 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b920 54 .cfa: sp 0 + .ra: x30
STACK CFI b924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b938 x19: .cfa -16 + ^
STACK CFI b970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b980 54 .cfa: sp 0 + .ra: x30
STACK CFI b984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b998 x19: .cfa -16 + ^
STACK CFI b9d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b160 98 .cfa: sp 0 + .ra: x30
STACK CFI b164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b178 x19: .cfa -16 + ^
STACK CFI b1f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aed0 98 .cfa: sp 0 + .ra: x30
STACK CFI aed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aee8 x19: .cfa -16 + ^
STACK CFI af64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b540 98 .cfa: sp 0 + .ra: x30
STACK CFI b544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b558 x19: .cfa -16 + ^
STACK CFI b5d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b2b0 98 .cfa: sp 0 + .ra: x30
STACK CFI b2b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2c8 x19: .cfa -16 + ^
STACK CFI b344 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b9e0 60 .cfa: sp 0 + .ra: x30
STACK CFI b9e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b9f8 x19: .cfa -16 + ^
STACK CFI ba3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba40 60 .cfa: sp 0 + .ra: x30
STACK CFI ba44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba58 x19: .cfa -16 + ^
STACK CFI ba9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT baa0 60 .cfa: sp 0 + .ra: x30
STACK CFI baa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bab8 x19: .cfa -16 + ^
STACK CFI bafc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bb00 60 .cfa: sp 0 + .ra: x30
STACK CFI bb04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb18 x19: .cfa -16 + ^
STACK CFI bb5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b020 8c .cfa: sp 0 + .ra: x30
STACK CFI b024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b038 x19: .cfa -16 + ^
STACK CFI b0a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b400 8c .cfa: sp 0 + .ra: x30
STACK CFI b404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b418 x19: .cfa -16 + ^
STACK CFI b488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b7d0 8c .cfa: sp 0 + .ra: x30
STACK CFI b7d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b7e8 x19: .cfa -16 + ^
STACK CFI b858 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b690 8c .cfa: sp 0 + .ra: x30
STACK CFI b694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b6a8 x19: .cfa -16 + ^
STACK CFI b718 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9ad0 214 .cfa: sp 0 + .ra: x30
STACK CFI 9ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9adc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9ae4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9aec x23: .cfa -16 + ^
STACK CFI 9c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9c3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT bb60 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bb64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bb6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bb84 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bc80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bc84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT bd10 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bd14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bd1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bd34 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI be30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI be34 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT bec0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bec4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI becc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bee4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bfe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bfe4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT c070 1a4 .cfa: sp 0 + .ra: x30
STACK CFI c074 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c07c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c094 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c194 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9cf0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9d04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9da0 184 .cfa: sp 0 + .ra: x30
STACK CFI 9da4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9db4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9f30 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9f34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9f44 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9fe0 394 .cfa: sp 0 + .ra: x30
STACK CFI 9fe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9fec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9ffc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a004 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a284 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a2c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a2cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT c220 54 .cfa: sp 0 + .ra: x30
STACK CFI c224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c238 x19: .cfa -16 + ^
STACK CFI c270 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c280 60 .cfa: sp 0 + .ra: x30
STACK CFI c284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c298 x19: .cfa -16 + ^
STACK CFI c2dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a380 3ec .cfa: sp 0 + .ra: x30
STACK CFI a384 .cfa: sp 528 +
STACK CFI a388 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI a390 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI a398 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI a3a4 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI a3b0 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI a628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a62c .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT a770 a8 .cfa: sp 0 + .ra: x30
STACK CFI a774 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a784 x19: .cfa -64 + ^
STACK CFI INIT a820 414 .cfa: sp 0 + .ra: x30
STACK CFI a824 .cfa: sp 512 +
STACK CFI a828 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI a830 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI a840 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI a84c x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI a854 x25: .cfa -448 + ^
STACK CFI INIT 8510 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 851c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8528 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 884c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8854 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
