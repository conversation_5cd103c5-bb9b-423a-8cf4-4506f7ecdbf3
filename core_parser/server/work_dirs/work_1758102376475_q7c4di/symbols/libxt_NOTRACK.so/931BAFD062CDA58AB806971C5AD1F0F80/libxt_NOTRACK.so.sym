MODULE Linux arm64 931BAFD062CDA58AB806971C5AD1F0F80 libxt_NOTRACK.so
INFO CODE_ID D0AF1B93CD628AA5B806971C5AD1F0F819675554
PUBLIC ec0 0 libxt_CT_init
STACK CFI INIT ee8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f18 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT f58 48 .cfa: sp 0 + .ra: x30
STACK CFI f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f64 x19: .cfa -16 + ^
STACK CFI f9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fa8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT fd8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT fe8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ff8 170 .cfa: sp 0 + .ra: x30
STACK CFI ffc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1000 .cfa: x29 112 +
STACK CFI 1004 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 100c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1018 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 102c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 103c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 115c .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1168 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1178 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1188 a8 .cfa: sp 0 + .ra: x30
STACK CFI 118c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1194 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 122c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1230 9c .cfa: sp 0 + .ra: x30
STACK CFI 1234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 123c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 127c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1288 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 129c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 12d0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 12d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1334 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1348 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13c0 150 .cfa: sp 0 + .ra: x30
STACK CFI 13cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 143c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1448 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1510 108 .cfa: sp 0 + .ra: x30
STACK CFI 151c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1524 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 157c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1618 130 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 162c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1680 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 168c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1748 110 .cfa: sp 0 + .ra: x30
STACK CFI 174c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1758 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1768 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1858 c0 .cfa: sp 0 + .ra: x30
STACK CFI 185c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1864 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 18fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1900 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1918 c0 .cfa: sp 0 + .ra: x30
STACK CFI 191c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1924 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1974 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1998 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 19bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 19c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 19d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ec0 10 .cfa: sp 0 + .ra: x30
