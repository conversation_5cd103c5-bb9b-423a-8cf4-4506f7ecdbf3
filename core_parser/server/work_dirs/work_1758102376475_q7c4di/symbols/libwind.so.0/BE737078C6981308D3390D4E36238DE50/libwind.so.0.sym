MODULE Linux arm64 BE737078C6981308D3390D4E36238DE50 libwind.so.0
INFO CODE_ID 787073BE98C60813D3390D4E36238DE5BD592107
PUBLIC 11c8 0 _wind_stringprep_testbidi
PUBLIC 12e8 0 _wind_combining_class
PUBLIC 1350 0 _wind_stringprep_error
PUBLIC 13e0 0 _wind_stringprep_prohibited
PUBLIC 1460 0 _wind_stringprep_map
PUBLIC 1588 0 _wind_ldap_case_exact_attribute
PUBLIC 1918 0 _wind_stringprep_normalize
PUBLIC 1d70 0 wind_punycode_label_toascii
PUBLIC 2018 0 wind_stringprep
PUBLIC 2180 0 wind_profile
PUBLIC 2338 0 wind_utf8ucs4
PUBLIC 23f8 0 wind_utf8ucs4_length
PUBLIC 2408 0 wind_ucs4utf8
PUBLIC 2578 0 wind_ucs4utf8_length
PUBLIC 2588 0 wind_ucs2read
PUBLIC 26b0 0 wind_ucs2write
PUBLIC 2788 0 wind_utf8ucs2
PUBLIC 2858 0 wind_utf8ucs2_length
PUBLIC 2868 0 wind_ucs2utf8
PUBLIC 2950 0 wind_ucs2utf8_length
STACK CFI INIT 1088 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 10fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1104 x19: .cfa -16 + ^
STACK CFI 113c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1140 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1148 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11c8 11c .cfa: sp 0 + .ra: x30
STACK CFI 11dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1298 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 129c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 12e8 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1350 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e0 7c .cfa: sp 0 + .ra: x30
STACK CFI 13e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1400 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 143c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1440 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1460 128 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1588 170 .cfa: sp 0 + .ra: x30
STACK CFI 16d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16f8 21c .cfa: sp 0 + .ra: x30
STACK CFI 16fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1704 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1710 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1728 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 173c x27: .cfa -32 + ^
STACK CFI 174c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 180c x21: x21 x22: x22
STACK CFI 1810 x27: x27
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1844 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 18f4 x21: x21 x22: x22
STACK CFI 18f8 x27: x27
STACK CFI 18fc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^
STACK CFI 1900 x21: x21 x22: x22
STACK CFI 1904 x27: x27
STACK CFI 190c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1910 x27: .cfa -32 + ^
STACK CFI INIT 1918 454 .cfa: sp 0 + .ra: x30
STACK CFI 191c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 192c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1944 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1998 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 199c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1ba0 x25: x25 x26: x26
STACK CFI 1ba4 x27: x27 x28: x28
STACK CFI 1bb8 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1cd4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1d10 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 1d1c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1d58 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d5c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 1d60 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1d64 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 1d70 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1d74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1da4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e04 x21: x21 x22: x22
STACK CFI 1e14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e18 x21: x21 x22: x22
STACK CFI 1e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1e30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2000 x21: x21 x22: x22
STACK CFI 2004 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2018 168 .cfa: sp 0 + .ra: x30
STACK CFI 201c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2024 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2038 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 207c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2080 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 2088 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2090 x25: .cfa -48 + ^
STACK CFI 2144 x23: x23 x24: x24
STACK CFI 2148 x25: x25
STACK CFI 214c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 2164 x23: x23 x24: x24 x25: x25
STACK CFI 2168 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 216c x25: .cfa -48 + ^
STACK CFI 2178 x23: x23 x24: x24
STACK CFI 217c x25: x25
STACK CFI INIT 2180 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 218c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 21e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2214 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2228 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2338 bc .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 23f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2408 170 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2578 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2588 128 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2788 d0 .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2828 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 282c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2858 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2868 e4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2950 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2960 90 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2970 x19: .cfa -16 + ^
STACK CFI 29a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 29f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a00 80 .cfa: sp 0 + .ra: x30
STACK CFI 2a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a10 x19: .cfa -16 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
