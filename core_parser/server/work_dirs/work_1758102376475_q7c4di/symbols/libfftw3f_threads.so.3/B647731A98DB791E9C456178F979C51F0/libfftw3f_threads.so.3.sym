MODULE Linux arm64 B647731A98DB791E9C456178F979C51F0 libfftw3f_threads.so.3
INFO CODE_ID 1A7347B6DB981E799C456178F979C51F10F45623
PUBLIC 1f48 0 fftwf_init_threads
PUBLIC 1fc8 0 fftwf_cleanup_threads
PUBLIC 2020 0 fftwf_plan_with_nthreads
PUBLIC 2070 0 fftwf_make_planner_thread_safe
PUBLIC 3078 0 fftwf_threads_conf_standard
PUBLIC 3168 0 fftwf_ithreads_init
PUBLIC 31e0 0 fftwf_spawn_loop
PUBLIC 3470 0 fftwf_threads_cleanup
PUBLIC 3550 0 fftwf_threads_register_planner_hooks
PUBLIC 3ba8 0 fftwf_dft_thr_vrank_geq1_register
PUBLIC 4448 0 fftwf_mksolver_ct_threads
PUBLIC 4a68 0 fftwf_rdft_thr_vrank_geq1_register
PUBLIC 5248 0 fftwf_mksolver_hc2hc_threads
PUBLIC 5860 0 fftwf_rdft2_thr_vrank_geq1_register
PUBLIC 58e0 0 sfftw_plan_with_nthreads_
PUBLIC 58e8 0 sfftw_init_threads_
PUBLIC 5910 0 sfftw_cleanup_threads_
PUBLIC 5918 0 sfftw_plan_with_nthreads__
PUBLIC 5920 0 sfftw_init_threads__
PUBLIC 5928 0 sfftw_cleanup_threads__
STACK CFI INIT 1e88 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1eb8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ef8 48 .cfa: sp 0 + .ra: x30
STACK CFI 1efc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f04 x19: .cfa -16 + ^
STACK CFI 1f3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f48 7c .cfa: sp 0 + .ra: x30
STACK CFI 1f4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1fc8 54 .cfa: sp 0 + .ra: x30
STACK CFI 1fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fd4 x19: .cfa -16 + ^
STACK CFI 1fec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ff0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2020 4c .cfa: sp 0 + .ra: x30
STACK CFI 2024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2030 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 205c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2060 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2070 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3078 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3088 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3098 64 .cfa: sp 0 + .ra: x30
STACK CFI 309c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a4 x19: .cfa -16 + ^
STACK CFI 30d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3100 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3110 54 .cfa: sp 0 + .ra: x30
STACK CFI 3114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 311c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3168 78 .cfa: sp 0 + .ra: x30
STACK CFI 316c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3174 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3180 x21: .cfa -16 + ^
STACK CFI 31dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 31e0 28c .cfa: sp 0 + .ra: x30
STACK CFI 31e4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 31ec .cfa: x29 224 +
STACK CFI 31f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3218 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 33f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 33f4 .cfa: x29 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3470 dc .cfa: sp 0 + .ra: x30
STACK CFI 3474 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 347c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 348c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 34b4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3500 x23: x23 x24: x24
STACK CFI 3540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3544 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 3548 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 3550 78 .cfa: sp 0 + .ra: x30
STACK CFI 3554 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 355c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35c8 60 .cfa: sp 0 + .ra: x30
STACK CFI 35cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3628 d4 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3634 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 366c x23: .cfa -16 + ^
STACK CFI 3678 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36dc x19: x19 x20: x20
STACK CFI 36e0 x23: x23
STACK CFI 36f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 3700 4c .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 370c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3750 78 .cfa: sp 0 + .ra: x30
STACK CFI 3754 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3778 x19: .cfa -80 + ^
STACK CFI 37c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 37c8 58 .cfa: sp 0 + .ra: x30
STACK CFI 37cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37e8 x21: .cfa -16 + ^
STACK CFI 3814 x21: x21
STACK CFI 381c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3820 388 .cfa: sp 0 + .ra: x30
STACK CFI 3824 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 382c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3858 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 38a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 38ac .cfa: sp 192 + .ra: .cfa -184 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 38b0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 38bc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 38e0 x19: x19 x20: x20
STACK CFI 38e4 x23: x23 x24: x24
STACK CFI 38e8 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3aa8 x19: x19 x20: x20
STACK CFI 3aac x23: x23 x24: x24
STACK CFI 3ab0 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3b3c x19: x19 x20: x20
STACK CFI 3b40 x23: x23 x24: x24
STACK CFI 3b44 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3b9c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 3ba0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3ba4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI INIT 3ba8 7c .cfa: sp 0 + .ra: x30
STACK CFI 3bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3bb4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3bc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3c28 30 .cfa: sp 0 + .ra: x30
STACK CFI 3c2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c58 fc .cfa: sp 0 + .ra: x30
STACK CFI 3c5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3cbc x23: .cfa -16 + ^
STACK CFI 3cc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d2c x19: x19 x20: x20
STACK CFI 3d30 x23: x23
STACK CFI 3d50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 3d58 54 .cfa: sp 0 + .ra: x30
STACK CFI 3d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3db0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3db4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3dbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3dcc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3df8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e50 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3e58 84 .cfa: sp 0 + .ra: x30
STACK CFI 3e5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3e64 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3e74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ed8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3ee0 60 .cfa: sp 0 + .ra: x30
STACK CFI 3ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3eec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ef8 x21: .cfa -16 + ^
STACK CFI 3f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f40 508 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 224 +
STACK CFI 3f4c .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3f6c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3f78 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3f80 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 3f94 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 3fa4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4050 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 4054 x19: x19 x20: x20
STACK CFI 4058 x27: x27 x28: x28
STACK CFI 4080 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4084 .cfa: sp 224 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4234 x19: x19 x20: x20
STACK CFI 4238 x21: x21 x22: x22
STACK CFI 423c x25: x25 x26: x26
STACK CFI 4240 x27: x27 x28: x28
STACK CFI 4244 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4304 x19: x19 x20: x20
STACK CFI 4308 x21: x21 x22: x22
STACK CFI 430c x25: x25 x26: x26
STACK CFI 4310 x27: x27 x28: x28
STACK CFI 4314 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4434 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4438 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 443c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4440 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4444 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 4448 48 .cfa: sp 0 + .ra: x30
STACK CFI 444c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4454 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4460 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 448c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4490 44 .cfa: sp 0 + .ra: x30
STACK CFI 4494 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44d8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 44dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 451c x23: .cfa -16 + ^
STACK CFI 4528 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 458c x19: x19 x20: x20
STACK CFI 4590 x23: x23
STACK CFI 45a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 45b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 45b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4600 78 .cfa: sp 0 + .ra: x30
STACK CFI 4604 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4628 x19: .cfa -64 + ^
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4674 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4678 58 .cfa: sp 0 + .ra: x30
STACK CFI 467c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4684 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4698 x21: .cfa -16 + ^
STACK CFI 46c4 x21: x21
STACK CFI 46cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 46d0 398 .cfa: sp 0 + .ra: x30
STACK CFI 46d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 46dc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4704 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4750 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 4754 .cfa: sp 192 + .ra: .cfa -184 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 475c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4768 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4770 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4788 x19: x19 x20: x20
STACK CFI 478c x23: x23 x24: x24
STACK CFI 4790 x25: x25 x26: x26
STACK CFI 4794 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4958 x19: x19 x20: x20
STACK CFI 495c x23: x23 x24: x24
STACK CFI 4960 x25: x25 x26: x26
STACK CFI 4964 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 49f4 x19: x19 x20: x20
STACK CFI 49f8 x23: x23 x24: x24
STACK CFI 49fc x25: x25 x26: x26
STACK CFI 4a00 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4a58 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4a5c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4a60 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4a64 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 4a68 7c .cfa: sp 0 + .ra: x30
STACK CFI 4a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4ae8 2c .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b18 fc .cfa: sp 0 + .ra: x30
STACK CFI 4b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b7c x23: .cfa -16 + ^
STACK CFI 4b88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4bec x19: x19 x20: x20
STACK CFI 4bf0 x23: x23
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 4c18 54 .cfa: sp 0 + .ra: x30
STACK CFI 4c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4c70 88 .cfa: sp 0 + .ra: x30
STACK CFI 4c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4cf8 7c .cfa: sp 0 + .ra: x30
STACK CFI 4cfc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d28 x21: .cfa -48 + ^
STACK CFI 4d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4d70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4d78 60 .cfa: sp 0 + .ra: x30
STACK CFI 4d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d90 x21: .cfa -16 + ^
STACK CFI 4dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4dd8 470 .cfa: sp 0 + .ra: x30
STACK CFI 4ddc .cfa: sp 192 +
STACK CFI 4de4 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4e04 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4e0c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4e18 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4e2c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4e34 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4ef4 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 4ef8 x21: x21 x22: x22
STACK CFI 4efc x27: x27 x28: x28
STACK CFI 4f24 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 4f28 .cfa: sp 192 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 502c x19: x19 x20: x20
STACK CFI 5030 x21: x21 x22: x22
STACK CFI 5034 x23: x23 x24: x24
STACK CFI 5038 x27: x27 x28: x28
STACK CFI 503c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5184 x19: x19 x20: x20
STACK CFI 5188 x21: x21 x22: x22
STACK CFI 518c x23: x23 x24: x24
STACK CFI 5190 x27: x27 x28: x28
STACK CFI 5194 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5234 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 5238 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 523c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5240 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5244 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 5248 30 .cfa: sp 0 + .ra: x30
STACK CFI 524c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5254 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5278 60 .cfa: sp 0 + .ra: x30
STACK CFI 527c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 52d8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 52dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 52e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 531c x23: .cfa -16 + ^
STACK CFI 5328 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 538c x19: x19 x20: x20
STACK CFI 5390 x23: x23
STACK CFI 53a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 53b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 53b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5400 78 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5428 x19: .cfa -80 + ^
STACK CFI 5470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5474 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5478 58 .cfa: sp 0 + .ra: x30
STACK CFI 547c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5484 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5498 x21: .cfa -16 + ^
STACK CFI 54c4 x21: x21
STACK CFI 54cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54d0 38c .cfa: sp 0 + .ra: x30
STACK CFI 54d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 54dc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5514 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5520 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5530 x23: x23 x24: x24
STACK CFI 5560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5564 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 5598 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 572c x21: x21 x22: x22
STACK CFI 5730 x23: x23 x24: x24
STACK CFI 5734 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5760 x21: x21 x22: x22
STACK CFI 5764 x23: x23 x24: x24
STACK CFI 5768 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 57f4 x21: x21 x22: x22
STACK CFI 57f8 x23: x23 x24: x24
STACK CFI 57fc x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5850 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5854 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5858 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI INIT 5860 7c .cfa: sp 0 + .ra: x30
STACK CFI 5864 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 586c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 587c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 58e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 58e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 58ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58f4 x19: .cfa -16 + ^
STACK CFI 5908 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5910 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5918 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5928 4 .cfa: sp 0 + .ra: x30
