MODULE Linux arm64 8A5F78ADAE52095D8817AE6BE08063340 libgpu_interface.so
INFO CODE_ID AD785F8A52AE5D098817AE6BE0806334
PUBLIC 6620 0 _init
PUBLIC 6c50 0 call_weak_fn
PUBLIC 6c64 0 deregister_tm_clones
PUBLIC 6c94 0 register_tm_clones
PUBLIC 6cd0 0 __do_global_dtors_aux
PUBLIC 6d20 0 frame_dummy
PUBLIC 6d30 0 lios::gpu::GpuCuda::GpuCuda()
PUBLIC 6d80 0 lios::gpu::GpuCuda::GetBufAttrList(int, linvs::buf::BufAttrList&)
PUBLIC 6fd0 0 lios::gpu::GpuCuda::GetSignalAttrList(int, linvs::sync::SyncAttrList&)
PUBLIC 7040 0 lios::gpu::GpuCuda::GetWaitAttrList(int, linvs::sync::SyncAttrList&)
PUBLIC 70b0 0 lios::gpu::GpuCuda::ImportMem(NvSciBufObjRefRec* const&, unsigned long)
PUBLIC 7150 0 lios::gpu::GpuCuda::ImportMem(linvs::buf::BufObj const&)
PUBLIC 72e0 0 lios::gpu::GpuCuda::GetGpuRawPtr(lios::gpu::GpuExternalMemory&, unsigned long)
PUBLIC 7380 0 lios::gpu::GpuCuda::RegisterSyncObj(linvs::sync::SyncObj const&, lios::gpu::GpuExternalSemaphore&)
PUBLIC 7410 0 lios::gpu::GpuCuda::InsertPerfence(linvs::sync::SyncFence&, lios::gpu::GpuExternalSemaphore&, lios::gpu::GpuStream&)
PUBLIC 74d0 0 lios::gpu::GpuCuda::Instance(int)
PUBLIC 7560 0 lios::gpu::GpuCuda::GpuCuda(int)
PUBLIC 76a0 0 lios::gpu::GpuCuda::MapBuf(linvs::buf::BufObj const&, lios::camera::camera_nv::CudaMapInfo&)
PUBLIC 7ba0 0 lios::gpu::GpuCuda::MapBuf(linvs::buf::BufObj const&, lios::camera::camera_nv::CudaMapInfo*)
PUBLIC 7bb0 0 lios::gpu::GpuCuda::GetGpuRawPtr(linvs::buf::BufObj&)
PUBLIC 7db0 0 lios::gpu::GpuCuda::DeepCopyAsync(linvs::buf::BufObj&, linvs::buf::BufObj&, lios::gpu::GpuStream&)
PUBLIC 80f0 0 lios::gpu::GpuCuda::DeepCopySync(linvs::buf::BufObj&, linvs::buf::BufObj&, lios::gpu::GpuStream&)
PUBLIC 8130 0 lios::gpu::GpuCuda::InitMipmap(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&)
PUBLIC 8810 0 lios::gpu::GpuCuda::InitLevel(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&)
PUBLIC 8ba0 0 lios::gpu::GpuCuda::Bl2PlAsync(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 8e30 0 lios::gpu::GpuCuda::Bl2PlAsync(lios::camera::camera_nv::CudaMapInfo&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 8e80 0 lios::gpu::GpuCuda::Bl2PlSync(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 8ee0 0 lios::gpu::GpuCuda::Bl2PlSync(lios::camera::camera_nv::CudaMapInfo&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 8f60 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8f70 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8f80 0 lios::gpu::GpuCuda::~GpuCuda()
PUBLIC 91b0 0 lios::camera::camera_nv::CudaMapInfo::~CudaMapInfo()
PUBLIC 9330 0 linvs::buf::BufAttrValues::~BufAttrValues()
PUBLIC 9420 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::~_Hashtable()
PUBLIC 95e0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 96d0 0 std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> >::_M_default_append(unsigned long)
PUBLIC 9a10 0 std::vector<cudaArray*, std::allocator<cudaArray*> >::_M_default_append(unsigned long)
PUBLIC 9b30 0 void std::vector<unsigned int, std::allocator<unsigned int> >::_M_realloc_insert<unsigned int const&>(__gnu_cxx::__normal_iterator<unsigned int*, std::vector<unsigned int, std::allocator<unsigned int> > >, unsigned int const&)
PUBLIC 9c60 0 void std::vector<unsigned long, std::allocator<unsigned long> >::_M_realloc_insert<unsigned long const&>(__gnu_cxx::__normal_iterator<unsigned long*, std::vector<unsigned long, std::allocator<unsigned long> > >, unsigned long const&)
PUBLIC 9d90 0 void std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> >::_M_realloc_insert<NvSciBufAttrValColorFmt const&>(__gnu_cxx::__normal_iterator<NvSciBufAttrValColorFmt*, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > >, NvSciBufAttrValColorFmt const&)
PUBLIC 9ec0 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_erase(std::integral_constant<bool, true>, CUexternalMemory_st* const&)
PUBLIC a100 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_erase(std::integral_constant<bool, true>, CUexternalMemory_st* const&)
PUBLIC a220 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC a350 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC a480 0 std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> >::~vector()
PUBLIC a5d0 0 lios::gpu::GpuExternalMemory::GpuExternalMemory()
PUBLIC a620 0 lios::gpu::GpuExternalMemory::GpuExternalMemory(CUexternalMemory_st*&)
PUBLIC a680 0 lios::gpu::GpuExternalMemory::GpuExternalMemory(lios::gpu::GpuExternalMemory const&)
PUBLIC a780 0 lios::gpu::GpuExternalMemory::operator=(lios::gpu::GpuExternalMemory const&)
PUBLIC a890 0 lios::gpu::GpuExternalMemory::operator bool() const
PUBLIC a8b0 0 lios::gpu::GpuExternalMemory::operator*()
PUBLIC a8c0 0 lios::gpu::GpuPtr::GpuPtr(void*, unsigned long, lios::gpu::GpuExternalMemory&)
PUBLIC a960 0 lios::gpu::GpuPtr::GpuPtr(lios::gpu::GpuPtr const&)
PUBLIC aa60 0 lios::gpu::GpuPtr::operator=(lios::gpu::GpuPtr const&)
PUBLIC ab70 0 lios::gpu::GpuPtr::operator bool() const
PUBLIC ab90 0 lios::gpu::GpuPtr::operator*()
PUBLIC aba0 0 lios::gpu::GpuPtr::GetSize() const
PUBLIC abb0 0 lios::gpu::GpuExternalSemaphore::GpuExternalSemaphore(CUexternalSemaphore_st*&)
PUBLIC abc0 0 lios::gpu::GpuExternalSemaphore::~GpuExternalSemaphore()
PUBLIC abe0 0 lios::gpu::GpuStream::GpuStream()
PUBLIC ac30 0 lios::gpu::GpuStream::GpuStream(int)
PUBLIC acf0 0 lios::gpu::GpuStream::GpuStream(CUstream_st*&)
PUBLIC ad50 0 lios::gpu::GpuStream::GpuStream(lios::gpu::GpuStream const&)
PUBLIC ae50 0 lios::gpu::GpuStream::operator=(lios::gpu::GpuStream const&)
PUBLIC af60 0 lios::gpu::GpuStream::operator bool() const
PUBLIC af80 0 lios::gpu::GpuStream::operator*()
PUBLIC af90 0 lios::gpu::GpuStream::Sync()
PUBLIC afe0 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray()
PUBLIC b030 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray(cudaMipmappedArray*&)
PUBLIC b090 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray(lios::gpu::GpuMipmappedArray const&)
PUBLIC b190 0 lios::gpu::GpuMipmappedArray::operator=(lios::gpu::GpuMipmappedArray const&)
PUBLIC b2a0 0 lios::gpu::GpuMipmappedArray::operator bool() const
PUBLIC b2c0 0 lios::gpu::GpuMipmappedArray::operator*()
PUBLIC b2d0 0 lios::gpu::GpuArray::GpuArray(cudaArray*&)
PUBLIC b2e0 0 lios::gpu::GpuArray::~GpuArray()
PUBLIC b300 0 lios::gpu::GpuPtr::GpuPtr(unsigned long)
PUBLIC b3f0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b400 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<lios::gpu::GpuStream::GpuStreamImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b410 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<lios::gpu::GpuPtr::GpuPtrImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b420 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b430 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b490 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<lios::gpu::GpuPtr::GpuPtrImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b4f0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<lios::gpu::GpuStream::GpuStreamImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b550 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b5b0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b5d0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b5e0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<lios::gpu::GpuPtr::GpuPtrImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b5f0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<lios::gpu::GpuStream::GpuStreamImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b600 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b610 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b620 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<lios::gpu::GpuStream::GpuStreamImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b630 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<lios::gpu::GpuPtr::GpuPtrImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b640 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b650 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b670 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<lios::gpu::GpuStream::GpuStreamImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b690 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<lios::gpu::GpuPtr::GpuPtrImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b760 0 _fini
STACK CFI INIT 6c64 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c94 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd0 50 .cfa: sp 0 + .ra: x30
STACK CFI 6ce0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ce8 x19: .cfa -16 + ^
STACK CFI 6d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f70 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f80 228 .cfa: sp 0 + .ra: x30
STACK CFI 8f84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8f8c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8f98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9014 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9024 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 902c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9134 x23: x23 x24: x24
STACK CFI 9138 x25: x25 x26: x26
STACK CFI 913c x27: x27 x28: x28
STACK CFI 916c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9170 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 9198 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 91a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 91b0 180 .cfa: sp 0 + .ra: x30
STACK CFI 91b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 927c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9280 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 92f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9300 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d30 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d80 250 .cfa: sp 0 + .ra: x30
STACK CFI 6d84 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6d8c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6d9c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6da4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 6dac x25: .cfa -160 + ^
STACK CFI 6e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6e90 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x29: .cfa -224 + ^
STACK CFI INIT 6fd0 68 .cfa: sp 0 + .ra: x30
STACK CFI 6fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fdc x19: .cfa -16 + ^
STACK CFI 7008 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 700c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7040 68 .cfa: sp 0 + .ra: x30
STACK CFI 7044 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 704c x19: .cfa -16 + ^
STACK CFI 7078 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 707c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 70b0 98 .cfa: sp 0 + .ra: x30
STACK CFI 70b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 70cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 712c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 7144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9330 e4 .cfa: sp 0 + .ra: x30
STACK CFI 9334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 933c x19: .cfa -16 + ^
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9408 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9410 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7150 188 .cfa: sp 0 + .ra: x30
STACK CFI 7158 .cfa: sp 528 +
STACK CFI 7160 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 7168 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 7174 x21: .cfa -496 + ^
STACK CFI 72b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 72b8 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x29: .cfa -528 + ^
STACK CFI INIT 72e0 9c .cfa: sp 0 + .ra: x30
STACK CFI 72e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 72ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 72f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7354 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7380 84 .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7398 x19: .cfa -48 + ^
STACK CFI 73d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7410 bc .cfa: sp 0 + .ra: x30
STACK CFI 7414 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7420 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 742c x21: .cfa -160 + ^
STACK CFI 749c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 74a0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 74d0 90 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7504 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7548 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9420 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 9424 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 942c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9434 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9440 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9450 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9458 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9564 x23: x23 x24: x24
STACK CFI 9568 x25: x25 x26: x26
STACK CFI 956c x27: x27 x28: x28
STACK CFI 959c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95a0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 95c8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 95d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7560 134 .cfa: sp 0 + .ra: x30
STACK CFI 7564 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7580 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 758c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 75ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 75f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 762c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 95e0 ec .cfa: sp 0 + .ra: x30
STACK CFI 95e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 95ec x19: .cfa -32 + ^
STACK CFI 961c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9620 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 9698 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 969c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 96c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96d0 338 .cfa: sp 0 + .ra: x30
STACK CFI 96d8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 96e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 96e8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 96f0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9708 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 971c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9868 x23: x23 x24: x24
STACK CFI 989c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 98a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 98d4 x23: x23 x24: x24
STACK CFI 98e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 98e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 9938 x23: x23 x24: x24
STACK CFI 993c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 9a10 114 .cfa: sp 0 + .ra: x30
STACK CFI 9a18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9a34 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9a88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9b00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9b30 128 .cfa: sp 0 + .ra: x30
STACK CFI 9b34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9b44 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9b58 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9be8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9c60 128 .cfa: sp 0 + .ra: x30
STACK CFI 9c64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9c74 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9c88 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9d18 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9d90 128 .cfa: sp 0 + .ra: x30
STACK CFI 9d94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9da4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9db8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9e48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 76a0 4f4 .cfa: sp 0 + .ra: x30
STACK CFI 76a8 .cfa: sp 608 +
STACK CFI 76b0 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI 76b8 x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI 76cc x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI 76dc x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI 7868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 786c .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^ x29: .cfa -608 + ^
STACK CFI INIT 7ba0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7bb0 200 .cfa: sp 0 + .ra: x30
STACK CFI 7bb4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 7bbc x21: .cfa -320 + ^
STACK CFI 7bc4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 7ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7ce8 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x29: .cfa -352 + ^
STACK CFI INIT 7db0 340 .cfa: sp 0 + .ra: x30
STACK CFI 7db4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7dbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7dc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7ee4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 80f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 80f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 80fc x19: .cfa -16 + ^
STACK CFI 811c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8120 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9ec0 23c .cfa: sp 0 + .ra: x30
STACK CFI 9ec4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9ed0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9eec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9f2c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9f70 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a034 x25: x25 x26: x26
STACK CFI a058 x19: x19 x20: x20
STACK CFI a060 x21: x21 x22: x22
STACK CFI a06c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI a070 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a074 x19: x19 x20: x20
STACK CFI a080 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI a084 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI a094 x25: x25 x26: x26
STACK CFI a0cc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a0d8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI a0e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a0ec x25: x25 x26: x26
STACK CFI INIT a100 11c .cfa: sp 0 + .ra: x30
STACK CFI a104 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a110 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a1c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a1c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a1d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a1d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a220 124 .cfa: sp 0 + .ra: x30
STACK CFI a224 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a230 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a23c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a2d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a350 124 .cfa: sp 0 + .ra: x30
STACK CFI a354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a360 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a36c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a40c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a480 144 .cfa: sp 0 + .ra: x30
STACK CFI a484 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a490 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a4a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a4b8 x25: .cfa -16 + ^
STACK CFI a570 x23: x23 x24: x24
STACK CFI a574 x25: x25
STACK CFI a588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a58c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a5a8 x23: x23 x24: x24 x25: x25
STACK CFI a5b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a5b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8130 6d4 .cfa: sp 0 + .ra: x30
STACK CFI 8134 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 8140 x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 8148 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 8150 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 815c x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 8364 v8: .cfa -176 + ^
STACK CFI 84b4 v8: v8
STACK CFI 84ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 84f0 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 85c8 v8: .cfa -176 + ^
STACK CFI 8608 v8: v8
STACK CFI 87c0 v8: .cfa -176 + ^
STACK CFI 87c4 v8: v8
STACK CFI 87d4 v8: .cfa -176 + ^
STACK CFI 87dc v8: v8
STACK CFI INIT 8810 384 .cfa: sp 0 + .ra: x30
STACK CFI 8814 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 881c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 882c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8834 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8840 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 884c x27: .cfa -32 + ^
STACK CFI 89c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 89c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 89e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 89e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8ba0 28c .cfa: sp 0 + .ra: x30
STACK CFI 8ba4 .cfa: sp 128 +
STACK CFI 8ba8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8bb0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8bbc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8bd4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8be0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8dc0 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8e30 50 .cfa: sp 0 + .ra: x30
STACK CFI 8e34 .cfa: sp 48 +
STACK CFI 8e4c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8e80 5c .cfa: sp 0 + .ra: x30
STACK CFI 8e84 .cfa: sp 64 +
STACK CFI 8e88 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ea4 x19: .cfa -16 + ^
STACK CFI 8ed0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ed4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8ee0 74 .cfa: sp 0 + .ra: x30
STACK CFI 8ee4 .cfa: sp 64 +
STACK CFI 8efc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f08 x19: .cfa -16 + ^
STACK CFI 8f48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f4c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b3f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b410 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b420 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b430 60 .cfa: sp 0 + .ra: x30
STACK CFI b434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b444 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b48c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b490 60 .cfa: sp 0 + .ra: x30
STACK CFI b494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b4a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b4ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b4f0 60 .cfa: sp 0 + .ra: x30
STACK CFI b4f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b504 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b54c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b550 60 .cfa: sp 0 + .ra: x30
STACK CFI b554 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b564 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b5ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b5b0 20 .cfa: sp 0 + .ra: x30
STACK CFI b5bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b5c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b5d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b5e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b5f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b610 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b650 20 .cfa: sp 0 + .ra: x30
STACK CFI b65c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b670 20 .cfa: sp 0 + .ra: x30
STACK CFI b67c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b688 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b690 d0 .cfa: sp 0 + .ra: x30
STACK CFI b694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b69c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b6e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b6ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b750 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a5d0 48 .cfa: sp 0 + .ra: x30
STACK CFI a5d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a5dc x19: .cfa -16 + ^
STACK CFI a614 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a620 54 .cfa: sp 0 + .ra: x30
STACK CFI a624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a62c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a680 fc .cfa: sp 0 + .ra: x30
STACK CFI a684 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a68c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a6a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a6f0 x21: x21 x22: x22
STACK CFI a6fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a700 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a780 110 .cfa: sp 0 + .ra: x30
STACK CFI a784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a78c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a798 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a814 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a890 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8c0 94 .cfa: sp 0 + .ra: x30
STACK CFI a8c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a8cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a8d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a8e4 x23: .cfa -16 + ^
STACK CFI a938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a93c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a960 fc .cfa: sp 0 + .ra: x30
STACK CFI a964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a96c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a984 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a9d0 x21: x21 x22: x22
STACK CFI a9dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a9e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT aa60 110 .cfa: sp 0 + .ra: x30
STACK CFI aa64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aa6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aa78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aaf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aaf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ab70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT aba0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT abb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT abc0 20 .cfa: sp 0 + .ra: x30
STACK CFI abcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI abd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT abe0 48 .cfa: sp 0 + .ra: x30
STACK CFI abe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abec x19: .cfa -16 + ^
STACK CFI ac24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac30 bc .cfa: sp 0 + .ra: x30
STACK CFI ac34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ac3c x21: .cfa -16 + ^
STACK CFI ac44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ac98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ac9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI acd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI acd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT acf0 54 .cfa: sp 0 + .ra: x30
STACK CFI acf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ad40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ad50 fc .cfa: sp 0 + .ra: x30
STACK CFI ad54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ad74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI adc0 x21: x21 x22: x22
STACK CFI adcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI add0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ae50 110 .cfa: sp 0 + .ra: x30
STACK CFI ae54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae68 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT af60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT af80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT af90 50 .cfa: sp 0 + .ra: x30
STACK CFI af94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI afb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI afb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT afe0 48 .cfa: sp 0 + .ra: x30
STACK CFI afe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI afec x19: .cfa -16 + ^
STACK CFI b024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b030 54 .cfa: sp 0 + .ra: x30
STACK CFI b034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b03c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b090 fc .cfa: sp 0 + .ra: x30
STACK CFI b094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b09c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b100 x21: x21 x22: x22
STACK CFI b10c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b110 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b190 110 .cfa: sp 0 + .ra: x30
STACK CFI b194 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b19c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b1a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b224 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b2a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b2c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b2d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b2e0 20 .cfa: sp 0 + .ra: x30
STACK CFI b2ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b2f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b300 e4 .cfa: sp 0 + .ra: x30
STACK CFI b304 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b30c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b318 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b37c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b3b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
