MODULE Linux arm64 A865A948E231AC0AD9EC6260ADF88CB70 librspreload.so.1
INFO CODE_ID 48A965A831E20AACD9EC6260ADF88CB70839B40E
PUBLIC 2ab8 0 recv
PUBLIC 2b78 0 recvfrom
PUBLIC 2c48 0 read
PUBLIC 2d30 0 poll
PUBLIC 2f18 0 socket
PUBLIC 3218 0 bind
PUBLIC 3280 0 listen
PUBLIC 3368 0 accept
PUBLIC 3538 0 connect
PUBLIC 3738 0 recvmsg
PUBLIC 37f8 0 readv
PUBLIC 38e0 0 send
PUBLIC 39a0 0 sendto
PUBLIC 3a70 0 sendmsg
PUBLIC 3b30 0 write
PUBLIC 3c18 0 writev
PUBLIC 3d00 0 select
PUBLIC 41d0 0 shutdown
PUBLIC 4230 0 close
PUBLIC 4358 0 getpeername
PUBLIC 43c0 0 getsockname
PUBLIC 4470 0 setsockopt
PUBLIC 44e8 0 getsockopt
PUBLIC 4560 0 fcntl
PUBLIC 4718 0 dup2
PUBLIC 4960 0 sendfile
STACK CFI INIT 1958 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1988 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 19cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19d4 x19: .cfa -16 + ^
STACK CFI 1a0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a18 58 .cfa: sp 0 + .ra: x30
STACK CFI 1a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a24 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1a30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1a70 154 .cfa: sp 0 + .ra: x30
STACK CFI 1a74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1a8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1aa4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1ae4 x25: .cfa -32 + ^
STACK CFI 1b64 x25: x25
STACK CFI 1b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1b90 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 1bb8 x25: x25
STACK CFI 1bc0 x25: .cfa -32 + ^
STACK CFI INIT 1bc8 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 1bcc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1bd8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1be0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1c04 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1cac .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1ea0 8cc .cfa: sp 0 + .ra: x30
STACK CFI 1ea4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1eb4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1ec8 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 1f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f10 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI 1f20 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 2464 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 246c x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2634 x23: x23 x24: x24
STACK CFI 2638 x25: x25 x26: x26
STACK CFI 2644 x27: x27 x28: x28
STACK CFI 264c x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 2740 x23: x23 x24: x24
STACK CFI 2744 x25: x25 x26: x26
STACK CFI 2748 x27: x27 x28: x28
STACK CFI 2750 x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 275c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2760 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2764 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2768 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 2770 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2774 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2784 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 27ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 27b4 x23: .cfa -16 + ^
STACK CFI 2804 x21: x21 x22: x22
STACK CFI 2808 x23: x23
STACK CFI 280c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2810 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2824 x21: x21 x22: x22
STACK CFI 2828 x23: x23
STACK CFI 283c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2840 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2858 25c .cfa: sp 0 + .ra: x30
STACK CFI 285c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 2868 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2870 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2880 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2894 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 289c x27: .cfa -160 + ^
STACK CFI 299c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 29a0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x29: .cfa -240 + ^
STACK CFI INIT 2ab8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2abc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2acc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2b78 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2b7c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2b8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c08 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2c48 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2c4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ca0 x23: .cfa -16 + ^
STACK CFI 2ce4 x23: x23
STACK CFI 2ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2cec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2cf0 x23: x23
STACK CFI 2d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d30 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d58 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2de4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2f18 2fc .cfa: sp 0 + .ra: x30
STACK CFI 2f1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f30 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2f40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2f48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2f7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 302c x27: x27 x28: x28
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 305c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3060 x27: x27 x28: x28
STACK CFI 316c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3170 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3218 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3280 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3290 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 329c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3350 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3368 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 336c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3374 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3380 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3394 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 33d4 x23: x23 x24: x24
STACK CFI 33f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 33f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 344c x23: x23 x24: x24
STACK CFI 3450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3454 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3538 200 .cfa: sp 0 + .ra: x30
STACK CFI 353c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3544 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3554 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3574 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3590 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 35d0 x27: x27 x28: x28
STACK CFI 3618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 361c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3624 x27: x27 x28: x28
STACK CFI 3630 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3710 x27: x27 x28: x28
STACK CFI 3714 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 372c x27: x27 x28: x28
STACK CFI 3734 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3738 c0 .cfa: sp 0 + .ra: x30
STACK CFI 373c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 374c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 37f8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 37fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3804 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3814 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3850 x23: .cfa -16 + ^
STACK CFI 3894 x23: x23
STACK CFI 3898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 389c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 38a0 x23: x23
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 38e0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 38f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3954 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 396c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3970 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 39a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 39a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3a70 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3b30 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3b34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3b4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b88 x23: .cfa -16 + ^
STACK CFI 3bcc x23: x23
STACK CFI 3bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3bd8 x23: x23
STACK CFI 3bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3c00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c18 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3c34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3c70 x23: .cfa -16 + ^
STACK CFI 3cb4 x23: x23
STACK CFI 3cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3cbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3cc0 x23: x23
STACK CFI 3ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ce8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d00 4cc .cfa: sp 0 + .ra: x30
STACK CFI 3d04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3d18 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3d3c x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3d4c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3fe4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 41a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 41d0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4230 124 .cfa: sp 0 + .ra: x30
STACK CFI 4234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 423c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 427c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42c8 x21: x21 x22: x22
STACK CFI 42cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42d4 x21: x21 x22: x22
STACK CFI 42ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4340 x21: x21 x22: x22
STACK CFI 4344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4348 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4358 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 43c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4454 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 446c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4470 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44e8 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4560 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 456c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4584 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 46d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4718 244 .cfa: sp 0 + .ra: x30
STACK CFI 471c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4724 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4730 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 473c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 48a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4904 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4960 14c .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 496c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 497c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 49b4 x23: .cfa -16 + ^
STACK CFI 49c4 x23: x23
STACK CFI 49e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 49ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4a5c x23: x23
STACK CFI 4a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ab0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4abc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ac8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4b78 144 .cfa: sp 0 + .ra: x30
STACK CFI 4b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4bbc x21: .cfa -16 + ^
STACK CFI 4c74 x21: x21
STACK CFI 4c78 x21: .cfa -16 + ^
STACK CFI 4c8c x21: x21
STACK CFI 4c90 x21: .cfa -16 + ^
STACK CFI 4c9c x21: x21
STACK CFI 4ca0 x21: .cfa -16 + ^
STACK CFI 4cb4 x21: x21
STACK CFI INIT 4cc0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ce8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d00 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d48 x21: x21 x22: x22
STACK CFI 4d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4d78 x21: x21 x22: x22
STACK CFI 4d94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4da8 x21: x21 x22: x22
STACK CFI INIT 4db0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dc8 10 .cfa: sp 0 + .ra: x30
