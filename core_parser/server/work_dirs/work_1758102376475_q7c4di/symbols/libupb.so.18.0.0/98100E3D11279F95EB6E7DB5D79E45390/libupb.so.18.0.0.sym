MODULE Linux arm64 98100E3D11279F95EB6E7DB5D79E45390 libupb.so.18
INFO CODE_ID 3D0E10982711959FEB6E7DB5D79E4539
PUBLIC 6288 0 _init
PUBLIC 6c00 0 call_weak_fn
PUBLIC 6c14 0 deregister_tm_clones
PUBLIC 6c44 0 register_tm_clones
PUBLIC 6c80 0 __do_global_dtors_aux
PUBLIC 6cd0 0 frame_dummy
PUBLIC 6ce0 0 decode_longvarint64
PUBLIC 6e00 0 decode_err
PUBLIC 6e20 0 fastdecode_err
PUBLIC 6e40 0 decode_isdonefallback
PUBLIC 6ee0 0 decode_msg
PUBLIC 7f30 0 fastdecode_generic
PUBLIC 7f50 0 _upb_decode
PUBLIC 8080 0 field_number_cmp
PUBLIC 8090 0 count_types_in_msg
PUBLIC 8150 0 symtab_oomerr
PUBLIC 8180 0 symtab_errf
PUBLIC 8200 0 check_ident.constprop.0
PUBLIC 82e0 0 upb_enumdef_fullname
PUBLIC 82f0 0 upb_enumdef_name
PUBLIC 8330 0 upb_enumdef_file
PUBLIC 8340 0 upb_enumdef_default
PUBLIC 8350 0 upb_enumdef_numvals
PUBLIC 8360 0 upb_enum_begin
PUBLIC 8370 0 upb_enum_next
PUBLIC 8380 0 upb_enum_done
PUBLIC 8390 0 upb_enumdef_ntoi
PUBLIC 83d0 0 upb_enumdef_iton
PUBLIC 8440 0 upb_enum_iter_name
PUBLIC 8460 0 upb_enum_iter_number
PUBLIC 8480 0 upb_fielddef_fullname
PUBLIC 8490 0 upb_fielddef_type
PUBLIC 84b0 0 upb_fielddef_descriptortype
PUBLIC 84c0 0 upb_fielddef_index
PUBLIC 84d0 0 upb_fielddef_label
PUBLIC 84e0 0 upb_fielddef_number
PUBLIC 84f0 0 upb_fielddef_isextension
PUBLIC 8500 0 upb_fielddef_lazy
PUBLIC 8510 0 upb_fielddef_packed
PUBLIC 8520 0 upb_fielddef_name
PUBLIC 8560 0 upb_fielddef_jsonname
PUBLIC 8570 0 upb_fielddef_selectorbase
PUBLIC 8580 0 upb_fielddef_file
PUBLIC 8590 0 upb_fielddef_containingtype
PUBLIC 85a0 0 upb_fielddef_containingoneof
PUBLIC 85b0 0 upb_fielddef_defaultint64
PUBLIC 85c0 0 upb_fielddef_defaultint32
PUBLIC 85d0 0 upb_fielddef_defaultuint64
PUBLIC 85e0 0 upb_fielddef_defaultuint32
PUBLIC 85f0 0 upb_fielddef_defaultbool
PUBLIC 8600 0 upb_fielddef_defaultfloat
PUBLIC 8610 0 upb_fielddef_defaultdouble
PUBLIC 8620 0 upb_fielddef_defaultstr
PUBLIC 8650 0 upb_fielddef_msgsubdef
PUBLIC 8690 0 upb_fielddef_enumsubdef
PUBLIC 86d0 0 upb_fielddef_layout
PUBLIC 86f0 0 upb_fielddef_issubmsg
PUBLIC 8710 0 field_rank
PUBLIC 8750 0 cmp_fields
PUBLIC 8790 0 upb_fielddef_isstring
PUBLIC 87e0 0 upb_fielddef_isseq
PUBLIC 8800 0 upb_fielddef_isprimitive
PUBLIC 8850 0 upb_fielddef_hassubdef
PUBLIC 88a0 0 upb_fielddef_haspresence
PUBLIC 8910 0 upb_fielddef_checklabel
PUBLIC 8920 0 upb_fielddef_checktype
PUBLIC 8930 0 upb_fielddef_checkintfmt
PUBLIC 8940 0 upb_fielddef_checkdescriptortype
PUBLIC 8950 0 upb_msgdef_fullname
PUBLIC 8960 0 upb_msgdef_file
PUBLIC 8970 0 upb_msgdef_name
PUBLIC 89b0 0 upb_msgdef_syntax
PUBLIC 89c0 0 upb_msgdef_selectorcount
PUBLIC 89d0 0 upb_msgdef_submsgfieldcount
PUBLIC 89e0 0 upb_msgdef_itof
PUBLIC 8a50 0 upb_msgdef_ntof
PUBLIC 8a90 0 upb_msgdef_ntoo
PUBLIC 8ae0 0 upb_msgdef_lookupname
PUBLIC 8b60 0 upb_msgdef_lookupjsonname
PUBLIC 8bb0 0 upb_msgdef_numfields
PUBLIC 8bc0 0 upb_msgdef_numoneofs
PUBLIC 8bd0 0 upb_msgdef_numrealoneofs
PUBLIC 8be0 0 upb_msgdef_fieldcount
PUBLIC 8bf0 0 upb_msgdef_oneofcount
PUBLIC 8c00 0 upb_msgdef_realoneofcount
PUBLIC 8c10 0 upb_msgdef_layout
PUBLIC 8c20 0 upb_msgdef_field
PUBLIC 8c30 0 upb_msgdef_oneof
PUBLIC 8c40 0 upb_msgdef_mapentry
PUBLIC 8c50 0 upb_fielddef_ismap
PUBLIC 8ca0 0 upb_msgdef_wellknowntype
PUBLIC 8cb0 0 upb_msgdef_isnumberwrapper
PUBLIC 8cd0 0 upb_msgdef_iswrapper
PUBLIC 8cf0 0 upb_msg_field_begin
PUBLIC 8d00 0 upb_msg_field_next
PUBLIC 8d10 0 upb_msg_field_done
PUBLIC 8d20 0 upb_msg_iter_field
PUBLIC 8d40 0 upb_msg_field_iter_setdone
PUBLIC 8d50 0 upb_msg_field_iter_isequal
PUBLIC 8d60 0 upb_msg_oneof_begin
PUBLIC 8de0 0 upb_msg_oneof_next
PUBLIC 8e40 0 upb_msg_oneof_done
PUBLIC 8e50 0 upb_msg_iter_oneof
PUBLIC 8e80 0 upb_msg_oneof_iter_setdone
PUBLIC 8e90 0 upb_msg_oneof_iter_isequal
PUBLIC 8ea0 0 upb_oneofdef_name
PUBLIC 8ee0 0 upb_oneofdef_containingtype
PUBLIC 8ef0 0 upb_oneofdef_fieldcount
PUBLIC 8f00 0 upb_oneofdef_field
PUBLIC 8f10 0 upb_oneofdef_numfields
PUBLIC 8f20 0 upb_oneofdef_index
PUBLIC 8f40 0 upb_oneofdef_issynthetic
PUBLIC 8f50 0 upb_fielddef_realcontainingoneof
PUBLIC 8fa0 0 upb_oneofdef_ntof
PUBLIC 8fd0 0 upb_oneofdef_itof
PUBLIC 9040 0 upb_oneof_begin
PUBLIC 9050 0 upb_oneof_next
PUBLIC 9060 0 upb_oneof_done
PUBLIC 9070 0 upb_oneof_iter_field
PUBLIC 9090 0 upb_oneof_iter_setdone
PUBLIC 90a0 0 upb_filedef_name
PUBLIC 90b0 0 upb_filedef_package
PUBLIC 90c0 0 upb_filedef_phpprefix
PUBLIC 90d0 0 upb_filedef_phpnamespace
PUBLIC 90e0 0 upb_filedef_syntax
PUBLIC 90f0 0 upb_filedef_msgcount
PUBLIC 9100 0 upb_filedef_depcount
PUBLIC 9110 0 upb_filedef_enumcount
PUBLIC 9120 0 upb_filedef_dep
PUBLIC 9150 0 upb_filedef_msg
PUBLIC 9180 0 upb_filedef_enum
PUBLIC 91b0 0 upb_filedef_symtab
PUBLIC 91c0 0 upb_symtab_free
PUBLIC 9200 0 upb_symtab_new
PUBLIC 92e0 0 upb_symtab_lookupmsg
PUBLIC 9350 0 upb_symtab_lookupmsg2
PUBLIC 93a0 0 upb_symtab_lookupenum
PUBLIC 9410 0 upb_symtab_lookupfile
PUBLIC 9460 0 upb_symtab_lookupfile2
PUBLIC 9490 0 upb_symtab_filecount
PUBLIC 94a0 0 symtab_alloc
PUBLIC 9500 0 resolve_fielddef.isra.0
PUBLIC 9a90 0 create_enumdef
PUBLIC 9d60 0 getjsonname
PUBLIC 9ea0 0 create_fielddef
PUBLIC a470 0 create_msgdef
PUBLIC adb0 0 _upb_symtab_addfile
PUBLIC bcb0 0 upb_symtab_addfile
PUBLIC bcc0 0 _upb_symtab_loaddefinit.localalias
PUBLIC be80 0 _upb_symtab_bytesloaded
PUBLIC be90 0 encode_varint64
PUBLIC bee0 0 encode_growbuffer
PUBLIC bfb0 0 encode_longvarint
PUBLIC c020 0 encode_message
PUBLIC d060 0 encode_scalar
PUBLIC d4a0 0 upb_encode_ex
PUBLIC d540 0 _upb_mapsorter_cmpu32
PUBLIC d560 0 _upb_mapsorter_cmpstr
PUBLIC d5b0 0 _upb_mapsorter_cmpbool
PUBLIC d5e0 0 _upb_mapsorter_cmpi64
PUBLIC d600 0 _upb_mapsorter_cmpu64
PUBLIC d620 0 _upb_mapsorter_cmpi32
PUBLIC d640 0 _upb_msg_new
PUBLIC d6d0 0 _upb_msg_clear
PUBLIC d6f0 0 _upb_msg_addunknown
PUBLIC d880 0 _upb_msg_discardunknown_shallow
PUBLIC d890 0 upb_msg_getunknown
PUBLIC d8b0 0 _upb_array_realloc
PUBLIC d990 0 _upb_array_resize_fallback
PUBLIC da80 0 _upb_array_append_fallback
PUBLIC db80 0 _upb_map_new
PUBLIC dc00 0 _upb_mapsorter_pushmap
PUBLIC ddd0 0 upb_msg_new
PUBLIC de00 0 upb_msg_has
PUBLIC ded0 0 upb_msg_whichoneof
PUBLIC df80 0 upb_msg_get
PUBLIC e130 0 upb_msg_set
PUBLIC e1e0 0 upb_msg_clearfield
PUBLIC e290 0 upb_msg_clear
PUBLIC e2c0 0 upb_array_new
PUBLIC e340 0 upb_array_size
PUBLIC e350 0 upb_array_get
PUBLIC e390 0 upb_array_set
PUBLIC e3d0 0 upb_array_append
PUBLIC e440 0 upb_array_resize
PUBLIC e490 0 upb_map_new
PUBLIC e4b0 0 upb_msg_mutable
PUBLIC e630 0 upb_map_size
PUBLIC e640 0 upb_msg_next
PUBLIC e800 0 upb_map_get
PUBLIC e8a0 0 upb_map_clear
PUBLIC e8b0 0 upb_map_set
PUBLIC e990 0 upb_map_delete
PUBLIC e9d0 0 upb_mapiter_next
PUBLIC ea50 0 upb_mapiter_done
PUBLIC ea70 0 upb_mapiter_key
PUBLIC ead0 0 upb_mapiter_value
PUBLIC eb40 0 _upb_msg_discardunknown.localalias
PUBLIC ed00 0 upb_msg_discardunknown
PUBLIC ed10 0 inthash
PUBLIC ed20 0 insert.isra.0.constprop.0
PUBLIC ee30 0 is_pow2
PUBLIC ee50 0 log2ceil
PUBLIC eed0 0 upb_strdup2
PUBLIC ef50 0 upb_strdup
PUBLIC ef80 0 upb_strtable_init2
PUBLIC f090 0 upb_strtable_clear
PUBLIC f0c0 0 upb_strtable_uninit2
PUBLIC f170 0 upb_strtable_lookup2
PUBLIC f480 0 upb_strtable_remove3
PUBLIC f860 0 upb_strtable_begin
PUBLIC f8c0 0 upb_strtable_next
PUBLIC f920 0 upb_strtable_done
PUBLIC f970 0 upb_strtable_iter_key
PUBLIC f990 0 upb_strtable_iter_value
PUBLIC f9b0 0 upb_strtable_resize
PUBLIC fb00 0 upb_strtable_insert3
PUBLIC 101a0 0 upb_strtable_iter_setdone
PUBLIC 101b0 0 upb_strtable_iter_isequal
PUBLIC 10230 0 upb_inttable_count
PUBLIC 10240 0 upb_inttable_sizedinit
PUBLIC 10370 0 upb_inttable_init2
PUBLIC 10380 0 upb_inttable_uninit2
PUBLIC 103d0 0 upb_inttable_insert2
PUBLIC 10600 0 upb_inttable_lookup
PUBLIC 10690 0 upb_inttable_replace
PUBLIC 10720 0 upb_inttable_remove
PUBLIC 10830 0 upb_inttable_insertptr2
PUBLIC 10840 0 upb_inttable_lookupptr
PUBLIC 10850 0 upb_inttable_removeptr
PUBLIC 10860 0 upb_inttable_next
PUBLIC 10940 0 upb_inttable_begin
PUBLIC 10960 0 upb_inttable_done
PUBLIC 109e0 0 upb_inttable_iter_key
PUBLIC 10a10 0 upb_inttable_iter_value
PUBLIC 10a40 0 upb_inttable_compact2
PUBLIC 10ee0 0 upb_inttable_iter_setdone
PUBLIC 10ef0 0 upb_inttable_iter_isequal
PUBLIC 10f80 0 txtenc_printf
PUBLIC 11050 0 txtenc_string
PUBLIC 11340 0 txtenc_unknown
PUBLIC 11ca0 0 txtenc_field
PUBLIC 12180 0 txtenc_mapentry
PUBLIC 12470 0 txtenc_msg
PUBLIC 12780 0 txtenc_nullz
PUBLIC 127c0 0 upb_text_encode
PUBLIC 12840 0 upb_global_allocfunc
PUBLIC 12870 0 upb_status_clear
PUBLIC 12880 0 upb_ok
PUBLIC 12890 0 upb_status_errmsg
PUBLIC 128a0 0 upb_status_seterrmsg
PUBLIC 128e0 0 upb_status_vseterrf
PUBLIC 12930 0 upb_status_seterrf
PUBLIC 129a0 0 upb_status_vappenderrf
PUBLIC 12a20 0 _upb_arena_slowmalloc
PUBLIC 12b50 0 upb_arena_doalloc
PUBLIC 12be0 0 arena_initslow
PUBLIC 12c80 0 upb_arena_init
PUBLIC 12cd0 0 upb_arena_free
PUBLIC 12dd0 0 upb_arena_addcleanup
PUBLIC 12f00 0 upb_arena_fuse
PUBLIC 12fb4 0 _fini
STACK CFI INIT 6c14 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c44 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c80 50 .cfa: sp 0 + .ra: x30
STACK CFI 6c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c98 x19: .cfa -16 + ^
STACK CFI 6cc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ce0 118 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e00 14 .cfa: sp 0 + .ra: x30
STACK CFI 6e04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6e20 14 .cfa: sp 0 + .ra: x30
STACK CFI 6e24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6e40 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6ee0 1050 .cfa: sp 0 + .ra: x30
STACK CFI 6ee4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 6eec x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6efc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6f04 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6f10 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 6f1c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7094 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 7134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7138 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 7f30 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7f50 124 .cfa: sp 0 + .ra: x30
STACK CFI 7f54 .cfa: sp 544 +
STACK CFI 7f58 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 7f64 x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^
STACK CFI 801c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8020 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x29: .cfa -544 + ^
STACK CFI 8068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 806c .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x29: .cfa -544 + ^
STACK CFI INIT 8080 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8090 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 809c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 80a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8128 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8150 2c .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8164 x19: .cfa -16 + ^
STACK CFI INIT 8180 80 .cfa: sp 0 + .ra: x30
STACK CFI 8184 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 8198 x19: .cfa -256 + ^
STACK CFI INIT 8200 d8 .cfa: sp 0 + .ra: x30
STACK CFI 8204 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 829c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 82a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 82e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 82f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 82f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82fc x19: .cfa -16 + ^
STACK CFI 8324 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8330 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8360 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8390 3c .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 83a0 x19: .cfa -32 + ^
STACK CFI 83c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 83d0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8440 14 .cfa: sp 0 + .ra: x30
STACK CFI 8444 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8450 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8460 14 .cfa: sp 0 + .ra: x30
STACK CFI 8464 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8490 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8500 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8510 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8520 38 .cfa: sp 0 + .ra: x30
STACK CFI 8524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 852c x19: .cfa -16 + ^
STACK CFI 8554 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8560 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8590 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8620 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8650 3c .cfa: sp 0 + .ra: x30
STACK CFI 8654 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 865c x19: .cfa -16 + ^
STACK CFI 8678 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 867c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8690 3c .cfa: sp 0 + .ra: x30
STACK CFI 8694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 869c x19: .cfa -16 + ^
STACK CFI 86b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 86bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 86c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86d0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 86f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 86f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8708 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8710 38 .cfa: sp 0 + .ra: x30
STACK CFI 8714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 871c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8750 34 .cfa: sp 0 + .ra: x30
STACK CFI 8754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8760 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8790 48 .cfa: sp 0 + .ra: x30
STACK CFI 8794 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 879c x19: .cfa -16 + ^
STACK CFI 87b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 87bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 87d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 87e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 87e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8800 48 .cfa: sp 0 + .ra: x30
STACK CFI 8804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 880c x19: .cfa -16 + ^
STACK CFI 8828 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 882c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8850 44 .cfa: sp 0 + .ra: x30
STACK CFI 8854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 885c x19: .cfa -16 + ^
STACK CFI 8874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8878 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 88a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 88a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88ac x19: .cfa -16 + ^
STACK CFI 88c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 88cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 88e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 88ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8910 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8920 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8930 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8940 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8970 38 .cfa: sp 0 + .ra: x30
STACK CFI 8974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 897c x19: .cfa -16 + ^
STACK CFI 89a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89e0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a50 40 .cfa: sp 0 + .ra: x30
STACK CFI 8a54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a90 44 .cfa: sp 0 + .ra: x30
STACK CFI 8a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ab4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ad0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ae0 80 .cfa: sp 0 + .ra: x30
STACK CFI 8ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8af0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b60 50 .cfa: sp 0 + .ra: x30
STACK CFI 8b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8be0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c20 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c50 50 .cfa: sp 0 + .ra: x30
STACK CFI 8c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c5c x19: .cfa -16 + ^
STACK CFI 8c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8c9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ca0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cb0 20 .cfa: sp 0 + .ra: x30
STACK CFI 8cb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ccc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8cd0 20 .cfa: sp 0 + .ra: x30
STACK CFI 8cd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8cec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8cf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d20 14 .cfa: sp 0 + .ra: x30
STACK CFI 8d24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8d30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d60 78 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d70 x19: .cfa -16 + ^
STACK CFI 8dd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8de0 54 .cfa: sp 0 + .ra: x30
STACK CFI 8de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8dec x19: .cfa -16 + ^
STACK CFI 8e30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e50 24 .cfa: sp 0 + .ra: x30
STACK CFI 8e54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8e70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8e80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ea0 38 .cfa: sp 0 + .ra: x30
STACK CFI 8ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8eac x19: .cfa -16 + ^
STACK CFI 8ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f20 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f50 44 .cfa: sp 0 + .ra: x30
STACK CFI 8f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f5c x19: .cfa -16 + ^
STACK CFI 8f80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8f90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8fa0 28 .cfa: sp 0 + .ra: x30
STACK CFI 8fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8fd0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9070 14 .cfa: sp 0 + .ra: x30
STACK CFI 9074 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9080 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9090 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9100 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9120 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9150 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9180 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 91b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 91c0 40 .cfa: sp 0 + .ra: x30
STACK CFI 91c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91cc x19: .cfa -16 + ^
STACK CFI 91f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9200 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9214 x21: .cfa -16 + ^
STACK CFI 921c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 929c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 92a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 92d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 92e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 92e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9350 44 .cfa: sp 0 + .ra: x30
STACK CFI 9354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9374 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9388 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9390 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 93a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 93a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 93ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 93f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 93f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9410 48 .cfa: sp 0 + .ra: x30
STACK CFI 9414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 941c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9460 28 .cfa: sp 0 + .ra: x30
STACK CFI 9464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9480 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 94a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94bc x19: .cfa -16 + ^
STACK CFI 94e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 94e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9500 58c .cfa: sp 0 + .ra: x30
STACK CFI 9504 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 950c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9518 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9524 x23: .cfa -96 + ^
STACK CFI 96a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 96a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 9834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9838 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9a90 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 9a94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9aa0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9aa8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9ab0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9ab8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9d60 13c .cfa: sp 0 + .ra: x30
STACK CFI 9d64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9d6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9d74 x25: .cfa -16 + ^
STACK CFI 9d80 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9d8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9e00 x19: x19 x20: x20
STACK CFI 9e04 x21: x21 x22: x22
STACK CFI 9e14 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9e18 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9e4c x19: x19 x20: x20
STACK CFI 9e50 x21: x21 x22: x22
STACK CFI 9e5c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9e60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9e7c x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 9e98 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 9ea0 5c8 .cfa: sp 0 + .ra: x30
STACK CFI 9ea4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9eac x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9eb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9ebc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9ec8 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a168 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a334 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT a470 938 .cfa: sp 0 + .ra: x30
STACK CFI a474 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a480 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a488 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI a494 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI aae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI aaec .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT adb0 efc .cfa: sp 0 + .ra: x30
STACK CFI adb4 .cfa: sp 608 +
STACK CFI adbc .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI adc4 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI ade0 x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI b2d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b2d8 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^ x29: .cfa -608 + ^
STACK CFI INIT bcb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT bcc0 1bc .cfa: sp 0 + .ra: x30
STACK CFI bcc4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI bccc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI bcd8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI bce4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI bd28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bd2c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI bd68 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI bd80 x27: .cfa -144 + ^
STACK CFI bdfc x25: x25 x26: x26
STACK CFI be00 x27: x27
STACK CFI be04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI be08 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI be30 x25: x25 x26: x26
STACK CFI be34 x27: x27
STACK CFI be78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT be80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT be90 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT bee0 c4 .cfa: sp 0 + .ra: x30
STACK CFI bee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI beec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bef4 x23: .cfa -16 + ^
STACK CFI befc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bf7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI bf80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT bfb0 6c .cfa: sp 0 + .ra: x30
STACK CFI bfb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bfbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c00c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c020 1038 .cfa: sp 0 + .ra: x30
STACK CFI c024 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI c02c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI c034 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI c074 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI c080 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI c084 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI c27c x21: x21 x22: x22
STACK CFI c280 x23: x23 x24: x24
STACK CFI c284 x25: x25 x26: x26
STACK CFI c2a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI c2ac .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI c4e4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c524 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI d010 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d02c x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT d060 438 .cfa: sp 0 + .ra: x30
STACK CFI d064 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d070 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d10c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI d128 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d178 x21: x21 x22: x22
STACK CFI d1c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d1c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI d1cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d1e4 x21: x21 x22: x22
STACK CFI d1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d1ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI d1f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d224 x21: x21 x22: x22
STACK CFI d23c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d260 x21: x21 x22: x22
STACK CFI d27c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d2a0 x21: x21 x22: x22
STACK CFI d2cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d33c x21: x21 x22: x22
STACK CFI d410 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d434 x21: x21 x22: x22
STACK CFI d438 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d45c x21: x21 x22: x22
STACK CFI d468 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d488 x21: x21 x22: x22
STACK CFI d48c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT d4a0 9c .cfa: sp 0 + .ra: x30
STACK CFI d4a4 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI d4b4 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI d528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d52c .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x29: .cfa -432 + ^
STACK CFI INIT d540 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT d560 48 .cfa: sp 0 + .ra: x30
STACK CFI d564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d570 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d5b0 28 .cfa: sp 0 + .ra: x30
STACK CFI d5b8 .cfa: sp 16 +
STACK CFI d5d0 .cfa: sp 0 +
STACK CFI INIT d5e0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT d600 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT d620 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT d640 84 .cfa: sp 0 + .ra: x30
STACK CFI d644 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d65c x19: .cfa -32 + ^
STACK CFI d694 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d698 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI d6c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d6d0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT d6f0 188 .cfa: sp 0 + .ra: x30
STACK CFI d6f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d6fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d708 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d710 x23: .cfa -32 + ^
STACK CFI d764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d768 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT d880 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT d890 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT d8b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI d8b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d8c0 x23: .cfa -16 + ^
STACK CFI d8c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d8d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d95c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d960 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT d990 ec .cfa: sp 0 + .ra: x30
STACK CFI d994 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d99c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d9c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d9cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI d9d4 x21: .cfa -32 + ^
STACK CFI da2c x21: x21
STACK CFI da50 x21: .cfa -32 + ^
STACK CFI da58 x21: x21
STACK CFI da5c x21: .cfa -32 + ^
STACK CFI INIT da80 fc .cfa: sp 0 + .ra: x30
STACK CFI da84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI da8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI da9c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI daf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI daf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT db80 7c .cfa: sp 0 + .ra: x30
STACK CFI db84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI db90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI db9c x21: .cfa -32 + ^
STACK CFI dbe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI dbe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT dc00 1c4 .cfa: sp 0 + .ra: x30
STACK CFI dc04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI dc0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI dc18 x23: .cfa -16 + ^
STACK CFI dc24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI dce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI dce4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT ddd0 24 .cfa: sp 0 + .ra: x30
STACK CFI ddd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dddc x19: .cfa -16 + ^
STACK CFI ddf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT de00 c8 .cfa: sp 0 + .ra: x30
STACK CFI de04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI de0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI de4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI de50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI dea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dea4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI dec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ded0 a4 .cfa: sp 0 + .ra: x30
STACK CFI ded4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dedc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dee8 x21: .cfa -16 + ^
STACK CFI df2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI df30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI df5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI df60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI df70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT df80 1a4 .cfa: sp 0 + .ra: x30
STACK CFI df84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI df8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e000 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI e044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT e130 a8 .cfa: sp 0 + .ra: x30
STACK CFI e134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e13c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e1b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e1b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI e1d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT e1e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI e1e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e1ec x19: .cfa -16 + ^
STACK CFI e25c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e260 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e284 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e288 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT e290 2c .cfa: sp 0 + .ra: x30
STACK CFI e294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e29c x19: .cfa -16 + ^
STACK CFI e2b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e2c0 7c .cfa: sp 0 + .ra: x30
STACK CFI e2cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e2dc x19: .cfa -16 + ^
STACK CFI e32c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT e340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e350 40 .cfa: sp 0 + .ra: x30
STACK CFI e358 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e38c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e390 40 .cfa: sp 0 + .ra: x30
STACK CFI e394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e3cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e3d0 68 .cfa: sp 0 + .ra: x30
STACK CFI e3d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e3dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e3f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT e440 44 .cfa: sp 0 + .ra: x30
STACK CFI e444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e450 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e490 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT e4b0 17c .cfa: sp 0 + .ra: x30
STACK CFI e4b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e4bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e4c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e4d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e538 x25: .cfa -16 + ^
STACK CFI e584 x25: x25
STACK CFI e5ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e5b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI e618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e61c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT e630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e640 1b4 .cfa: sp 0 + .ra: x30
STACK CFI e644 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI e64c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e654 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI e664 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI e684 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e68c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI e70c x27: x27 x28: x28
STACK CFI e718 x25: x25 x26: x26
STACK CFI e730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e734 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI e798 x25: x25 x26: x26
STACK CFI e79c x27: x27 x28: x28
STACK CFI e7ac x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT e800 98 .cfa: sp 0 + .ra: x30
STACK CFI e804 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e80c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e814 x21: .cfa -48 + ^
STACK CFI e874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e878 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI e894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e8a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e8b0 e0 .cfa: sp 0 + .ra: x30
STACK CFI e8b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e8bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e8c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e95c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI e97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e980 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT e990 34 .cfa: sp 0 + .ra: x30
STACK CFI e994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e9c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e9d0 78 .cfa: sp 0 + .ra: x30
STACK CFI e9d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e9dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ea28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ea38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ea44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ea50 20 .cfa: sp 0 + .ra: x30
STACK CFI ea54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ea6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ea70 5c .cfa: sp 0 + .ra: x30
STACK CFI ea74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ea7c x19: .cfa -48 + ^
STACK CFI eaac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI eab0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI eac8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ead0 64 .cfa: sp 0 + .ra: x30
STACK CFI ead4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI eadc x19: .cfa -64 + ^
STACK CFI eb14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI eb18 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI eb30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT eb40 1c0 .cfa: sp 0 + .ra: x30
STACK CFI eb44 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI eb4c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI eb6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI eb70 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI eb74 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI eb80 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI eb8c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI eb98 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ec9c x21: x21 x22: x22
STACK CFI eca0 x23: x23 x24: x24
STACK CFI eca4 x25: x25 x26: x26
STACK CFI eca8 x27: x27 x28: x28
STACK CFI ecac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ecb0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT ed00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ed10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ed20 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee30 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT ee50 78 .cfa: sp 0 + .ra: x30
STACK CFI ee54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ee5c x19: .cfa -16 + ^
STACK CFI eea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI eea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI eec4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT eed0 80 .cfa: sp 0 + .ra: x30
STACK CFI eed4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI eee0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eee8 x21: .cfa -16 + ^
STACK CFI ef24 x21: x21
STACK CFI ef30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ef34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ef40 x21: x21
STACK CFI ef44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ef48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT ef50 30 .cfa: sp 0 + .ra: x30
STACK CFI ef54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ef5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ef7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ef80 110 .cfa: sp 0 + .ra: x30
STACK CFI ef88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ef94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI efb4 x21: .cfa -16 + ^
STACK CFI f03c x21: x21
STACK CFI f040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f044 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI f064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f068 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f074 x21: x21
STACK CFI f078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f07c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f080 x21: x21
STACK CFI f088 x21: .cfa -16 + ^
STACK CFI f08c x21: x21
STACK CFI INIT f090 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f0c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI f0c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f0cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f0d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI f114 x23: .cfa -16 + ^
STACK CFI f158 x23: x23
STACK CFI f15c x23: .cfa -16 + ^
STACK CFI f160 x23: x23
STACK CFI INIT f170 304 .cfa: sp 0 + .ra: x30
STACK CFI f174 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f180 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f18c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f288 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f2b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f2b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT f480 3d8 .cfa: sp 0 + .ra: x30
STACK CFI f484 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f490 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f49c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f4a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f4b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI f5fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI f600 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI f620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI f624 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT f860 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT f8c0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT f920 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT f970 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT f990 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT f9b0 144 .cfa: sp 0 + .ra: x30
STACK CFI f9b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f9c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f9cc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI fae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI faec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT fb00 69c .cfa: sp 0 + .ra: x30
STACK CFI fb04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fb0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fb1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fb2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fd08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fd0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI fef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 101a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 101b0 7c .cfa: sp 0 + .ra: x30
STACK CFI 101b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 101ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 101f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10230 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10240 130 .cfa: sp 0 + .ra: x30
STACK CFI 10244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1024c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10258 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 10364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10368 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10370 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10380 50 .cfa: sp 0 + .ra: x30
STACK CFI 10384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 103cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 103d0 230 .cfa: sp 0 + .ra: x30
STACK CFI 103d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 103dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 103ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1041c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10420 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 10454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10458 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 104a0 x23: .cfa -48 + ^
STACK CFI 104e8 x23: x23
STACK CFI 105d4 x23: .cfa -48 + ^
STACK CFI 105dc x23: x23
STACK CFI 105f4 x23: .cfa -48 + ^
STACK CFI 105fc x23: x23
STACK CFI INIT 10600 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10690 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10720 104 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10860 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT 10940 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10960 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109e0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a10 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a40 498 .cfa: sp 0 + .ra: x30
STACK CFI 10a44 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 10a50 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 10a58 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 10a64 x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 10a70 v8: .cfa -368 + ^
STACK CFI 10e54 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10e58 .cfa: sp 432 + .ra: .cfa -424 + ^ v8: .cfa -368 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x29: .cfa -432 + ^
STACK CFI INIT 10ee0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10ef0 88 .cfa: sp 0 + .ra: x30
STACK CFI 10ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10efc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10f80 d0 .cfa: sp 0 + .ra: x30
STACK CFI 10f84 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 10f98 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 11028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1102c .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x29: .cfa -272 + ^
STACK CFI 1104c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11050 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 11054 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1105c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11064 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11074 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 110a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 110b8 x27: .cfa -16 + ^
STACK CFI 1115c x23: x23 x24: x24
STACK CFI 11164 x27: x27
STACK CFI 11194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 11198 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 112e8 x23: x23 x24: x24 x27: x27
STACK CFI 11314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 11318 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 11320 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI INIT 11340 960 .cfa: sp 0 + .ra: x30
STACK CFI 11344 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11350 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11360 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 11374 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1137c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11384 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 11494 x21: x21 x22: x22
STACK CFI 11498 x23: x23 x24: x24
STACK CFI 1149c x25: x25 x26: x26
STACK CFI 114a0 x27: x27 x28: x28
STACK CFI 114ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 114b0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 11598 x21: x21 x22: x22
STACK CFI 1159c x23: x23 x24: x24
STACK CFI 115a0 x25: x25 x26: x26
STACK CFI 115a4 x27: x27 x28: x28
STACK CFI 115bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 115c0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 11a64 x21: x21 x22: x22
STACK CFI 11a6c x23: x23 x24: x24
STACK CFI 11a70 x25: x25 x26: x26
STACK CFI 11a74 x27: x27 x28: x28
STACK CFI 11a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11a84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 11be4 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 11be8 x25: x25 x26: x26
STACK CFI 11bec x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 11ca0 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 11ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11cb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11dac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 11e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12180 2ec .cfa: sp 0 + .ra: x30
STACK CFI 12184 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1218c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1219c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 121a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 121b0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 12370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12374 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 123b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 123b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 12470 304 .cfa: sp 0 + .ra: x30
STACK CFI 12474 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1247c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 12488 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 12494 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1249c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 124a8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 12738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1273c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 12780 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 127c0 74 .cfa: sp 0 + .ra: x30
STACK CFI 127c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 127e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 12830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12840 28 .cfa: sp 0 + .ra: x30
STACK CFI 12854 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12870 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 128a0 34 .cfa: sp 0 + .ra: x30
STACK CFI 128a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 128b4 x19: .cfa -16 + ^
STACK CFI 128cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 128e0 50 .cfa: sp 0 + .ra: x30
STACK CFI 128ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12904 x19: .cfa -48 + ^
STACK CFI 12928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12930 70 .cfa: sp 0 + .ra: x30
STACK CFI 12934 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1299c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 129a0 74 .cfa: sp 0 + .ra: x30
STACK CFI 129a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 129b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 129bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 12a20 12c .cfa: sp 0 + .ra: x30
STACK CFI 12a24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12a2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12a40 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 12afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12b00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 12b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12b50 84 .cfa: sp 0 + .ra: x30
STACK CFI 12b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12b68 x19: .cfa -32 + ^
STACK CFI 12b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 12bb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12bbc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12be0 98 .cfa: sp 0 + .ra: x30
STACK CFI 12be8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12bf8 x19: .cfa -16 + ^
STACK CFI 12c5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12c60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 12c6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c80 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12cd0 fc .cfa: sp 0 + .ra: x30
STACK CFI 12cd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 12d28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12d34 x23: .cfa -16 + ^
STACK CFI 12db8 x21: x21 x22: x22
STACK CFI 12dbc x23: x23
STACK CFI 12dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 12dc8 x21: x21 x22: x22
STACK CFI INIT 12dd0 124 .cfa: sp 0 + .ra: x30
STACK CFI 12dd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12de0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12de8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12df0 x23: .cfa -16 + ^
STACK CFI 12ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12ecc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 12ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12ee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12f00 b4 .cfa: sp 0 + .ra: x30
