MODULE Linux arm64 93E53655853AF8B127BD43BF58B9C8CC0 libdconf.so.1
INFO CODE_ID 5536E5933A85B1F827BD43BF58B9C8CC03D21444
PUBLIC 4960 0 dconf_client_get_type
PUBLIC 4c38 0 dconf_client_new
PUBLIC 4ca0 0 dconf_client_read
PUBLIC 4d30 0 dconf_client_read_full
PUBLIC 4dd0 0 dconf_client_list
PUBLIC 4e68 0 dconf_client_list_locks
PUBLIC 4f48 0 dconf_client_is_writable
PUBLIC 4fd0 0 dconf_client_write_fast
PUBLIC 5098 0 dconf_client_write_sync
PUBLIC 5170 0 dconf_client_change_fast
PUBLIC 5210 0 dconf_client_change_sync
PUBLIC 52b0 0 dconf_client_watch_fast
PUBLIC 5330 0 dconf_client_watch_sync
PUBLIC 53b0 0 dconf_client_unwatch_fast
PUBLIC 5430 0 dconf_client_unwatch_sync
PUBLIC 54b0 0 dconf_client_sync
PUBLIC 5648 0 dconf_changeset_new
PUBLIC 56a8 0 dconf_changeset_new_database
PUBLIC 57d0 0 dconf_changeset_unref
PUBLIC 5848 0 dconf_changeset_ref
PUBLIC 5860 0 dconf_changeset_set
PUBLIC 5a18 0 dconf_changeset_get
PUBLIC 5b08 0 dconf_changeset_is_similar_to
PUBLIC 5bc0 0 dconf_changeset_all
PUBLIC 5c60 0 dconf_changeset_seal
PUBLIC 5f40 0 dconf_changeset_describe
PUBLIC 5fb8 0 dconf_changeset_serialise
PUBLIC 6080 0 dconf_changeset_deserialise
PUBLIC 61a8 0 dconf_changeset_new_write
PUBLIC 61e0 0 dconf_changeset_is_empty
PUBLIC 6200 0 dconf_changeset_change
PUBLIC 62b8 0 dconf_changeset_filter_changes
PUBLIC 6498 0 dconf_changeset_diff
PUBLIC 65d0 0 dconf_error_quark
PUBLIC 6608 0 dconf_is_path
PUBLIC 66f0 0 dconf_is_key
PUBLIC 6818 0 dconf_is_dir
PUBLIC 6948 0 dconf_is_rel_path
PUBLIC 6a38 0 dconf_is_rel_key
PUBLIC 6b68 0 dconf_is_rel_dir
STACK CFI INIT 4768 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4798 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 47dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47e4 x19: .cfa -16 + ^
STACK CFI 481c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4820 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4828 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4830 40 .cfa: sp 0 + .ra: x30
STACK CFI 4834 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 484c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4870 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 487c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4938 28 .cfa: sp 0 + .ra: x30
STACK CFI 493c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4944 x19: .cfa -16 + ^
STACK CFI 495c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4960 6c .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 496c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 499c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 49c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 49d0 fc .cfa: sp 0 + .ra: x30
STACK CFI 49d4 .cfa: sp 80 +
STACK CFI 49d8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 49e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4abc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ad0 60 .cfa: sp 0 + .ra: x30
STACK CFI 4ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4adc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b30 104 .cfa: sp 0 + .ra: x30
STACK CFI 4b34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b40 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b4c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4b60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b90 x19: x19 x20: x20
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4bbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c14 x19: x19 x20: x20
STACK CFI 4c20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4c24 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c30 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4c38 64 .cfa: sp 0 + .ra: x30
STACK CFI 4c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ca0 8c .cfa: sp 0 + .ra: x30
STACK CFI 4ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cfc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4d30 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4dd0 98 .cfa: sp 0 + .ra: x30
STACK CFI 4dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ddc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4de8 x21: .cfa -16 + ^
STACK CFI 4e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4e68 dc .cfa: sp 0 + .ra: x30
STACK CFI 4e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e80 x21: .cfa -16 + ^
STACK CFI 4ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4edc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4f48 84 .cfa: sp 0 + .ra: x30
STACK CFI 4f4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4fd0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4fd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4fdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4fe8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 505c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5098 d8 .cfa: sp 0 + .ra: x30
STACK CFI 509c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 50a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 50b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 50bc x23: .cfa -16 + ^
STACK CFI 5130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5134 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 516c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5170 9c .cfa: sp 0 + .ra: x30
STACK CFI 5174 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 517c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5188 x21: .cfa -16 + ^
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5210 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 521c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5228 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 527c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 52ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 52b0 7c .cfa: sp 0 + .ra: x30
STACK CFI 52b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5304 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5330 7c .cfa: sp 0 + .ra: x30
STACK CFI 5334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 533c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5384 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 53b0 7c .cfa: sp 0 + .ra: x30
STACK CFI 53b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5404 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5430 7c .cfa: sp 0 + .ra: x30
STACK CFI 5434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 543c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5484 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54b0 74 .cfa: sp 0 + .ra: x30
STACK CFI 54b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54bc x19: .cfa -16 + ^
STACK CFI 54f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 54fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5528 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5538 fc .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5580 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5594 x21: .cfa -16 + ^
STACK CFI 55d0 x21: x21
STACK CFI 55d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 55d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 55e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 55fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5624 x21: x21
STACK CFI INIT 5638 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5648 5c .cfa: sp 0 + .ra: x30
STACK CFI 564c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5658 x19: .cfa -16 + ^
STACK CFI 56a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 56a8 124 .cfa: sp 0 + .ra: x30
STACK CFI 56ac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 56b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 56bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 56e0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 56ec x25: .cfa -80 + ^
STACK CFI 5754 x21: x21 x22: x22
STACK CFI 5758 x25: x25
STACK CFI 577c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5780 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 57c4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 57c8 x25: .cfa -80 + ^
STACK CFI INIT 57d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 57d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57dc x19: .cfa -16 + ^
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5808 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5848 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5860 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 5864 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 586c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5878 x23: .cfa -80 + ^
STACK CFI 5880 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 58dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 58e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5a18 f0 .cfa: sp 0 + .ra: x30
STACK CFI 5a1c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5a24 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5a30 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5a68 x23: .cfa -80 + ^
STACK CFI 5aa0 x23: x23
STACK CFI 5ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ac4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 5aec x23: .cfa -80 + ^
STACK CFI 5afc x23: x23
STACK CFI 5b04 x23: .cfa -80 + ^
STACK CFI INIT 5b08 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5b0c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5b14 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5b24 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b78 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5bc0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5bc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5bcc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5bdc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5bf4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5c60 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5c6c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5c74 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cb4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 5cc0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5ce4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5da4 x25: x25 x26: x26
STACK CFI 5da8 x23: x23 x24: x24
STACK CFI 5dac x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5f2c x23: x23 x24: x24
STACK CFI 5f30 x25: x25 x26: x26
STACK CFI 5f38 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5f3c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 5f40 74 .cfa: sp 0 + .ra: x30
STACK CFI 5f44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f68 x23: .cfa -16 + ^
STACK CFI 5fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5fb8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 5fbc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 5fc4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 5fd4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5ff4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6078 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI INIT 6080 124 .cfa: sp 0 + .ra: x30
STACK CFI 6084 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 608c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6094 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 60a8 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 60bc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6178 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI INIT 61a8 38 .cfa: sp 0 + .ra: x30
STACK CFI 61ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 61dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 61e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6200 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 620c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6224 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6240 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6244 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62a8 x19: x19 x20: x20
STACK CFI 62b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 62b8 1dc .cfa: sp 0 + .ra: x30
STACK CFI 62bc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 62c4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 62d0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 62f0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 6308 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 6314 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 6394 x19: x19 x20: x20
STACK CFI 6398 x23: x23 x24: x24
STACK CFI 639c x25: x25 x26: x26
STACK CFI 63a0 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 63d0 x19: x19 x20: x20
STACK CFI 63d8 x23: x23 x24: x24
STACK CFI 63dc x25: x25 x26: x26
STACK CFI 6400 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 6404 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 645c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6488 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 648c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 6490 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 6498 138 .cfa: sp 0 + .ra: x30
STACK CFI 649c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 64a4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 64c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 64d8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6524 x19: x19 x20: x20
STACK CFI 6528 x21: x21 x22: x22
STACK CFI 6548 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 654c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 6570 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6598 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 65c0 x19: x19 x20: x20
STACK CFI 65c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 65cc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI INIT 65d0 34 .cfa: sp 0 + .ra: x30
STACK CFI 65d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65dc x19: .cfa -16 + ^
STACK CFI 6600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6608 e4 .cfa: sp 0 + .ra: x30
STACK CFI 660c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6614 x19: .cfa -16 + ^
STACK CFI 6654 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6658 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 668c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 66bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 66c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66f0 124 .cfa: sp 0 + .ra: x30
STACK CFI 66f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 674c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6784 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6818 130 .cfa: sp 0 + .ra: x30
STACK CFI 681c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6824 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6874 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 68a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 68ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 68e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 68e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 691c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6948 ec .cfa: sp 0 + .ra: x30
STACK CFI 694c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6954 x19: .cfa -16 + ^
STACK CFI 699c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 69a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 69d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 69d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6a38 12c .cfa: sp 0 + .ra: x30
STACK CFI 6a3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b68 138 .cfa: sp 0 + .ra: x30
STACK CFI 6b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6ca0 60 .cfa: sp 0 + .ra: x30
STACK CFI 6ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6d00 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d18 94 .cfa: sp 0 + .ra: x30
STACK CFI 6d1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d30 x21: .cfa -16 + ^
STACK CFI 6d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6d70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6db0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6db4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6dbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6de4 x21: .cfa -48 + ^
STACK CFI 6e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6e18 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6e40 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6e50 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e70 x23: .cfa -16 + ^
STACK CFI 6ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ed0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6ef0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6f00 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f14 x23: .cfa -16 + ^
STACK CFI 6f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6f70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6f90 58 .cfa: sp 0 + .ra: x30
STACK CFI 6fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6fd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6fe8 7c .cfa: sp 0 + .ra: x30
STACK CFI 6fec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ff4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ffc x21: .cfa -16 + ^
STACK CFI 7038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 703c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7068 a0 .cfa: sp 0 + .ra: x30
STACK CFI 706c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7074 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 707c x21: .cfa -16 + ^
STACK CFI 70e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 70e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7108 80 .cfa: sp 0 + .ra: x30
STACK CFI 710c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7114 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 711c x21: .cfa -16 + ^
STACK CFI 7158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 715c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7188 50 .cfa: sp 0 + .ra: x30
STACK CFI 718c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 71d8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 71e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 71f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7200 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7210 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 721c x27: .cfa -16 + ^
STACK CFI 7224 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 72a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 72a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 72b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7318 e0 .cfa: sp 0 + .ra: x30
STACK CFI 731c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7324 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 733c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7340 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7354 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 73f8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 73fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7404 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7410 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 74ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 74f0 14c .cfa: sp 0 + .ra: x30
STACK CFI 74f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7500 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 750c x21: .cfa -16 + ^
STACK CFI 7544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7548 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7640 11c .cfa: sp 0 + .ra: x30
STACK CFI 7644 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 764c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7658 x23: .cfa -16 + ^
STACK CFI 7664 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7760 228 .cfa: sp 0 + .ra: x30
STACK CFI 7764 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 776c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7778 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 77b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 77bc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 77c4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 77c8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7874 x23: x23 x24: x24
STACK CFI 7878 x25: x25 x26: x26
STACK CFI 787c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 78e8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7948 x27: x27 x28: x28
STACK CFI 794c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7974 x27: x27 x28: x28
STACK CFI 7978 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 797c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7980 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7984 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 7988 30 .cfa: sp 0 + .ra: x30
STACK CFI 798c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7994 x19: .cfa -16 + ^
STACK CFI 79b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 79b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 79bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79cc x21: .cfa -16 + ^
STACK CFI 7a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7a20 1cc .cfa: sp 0 + .ra: x30
STACK CFI 7a24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7a30 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7a44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7aa8 x25: .cfa -16 + ^
STACK CFI 7b3c x25: x25
STACK CFI 7b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7b94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7bbc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7bf0 220 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bfc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7c0c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7c20 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c28 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7d08 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e10 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7e24 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7e30 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7e48 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7e6c x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^
STACK CFI 7f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7f78 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8000 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8018 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8038 20c .cfa: sp 0 + .ra: x30
STACK CFI 803c .cfa: sp 96 +
STACK CFI 8040 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8048 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8050 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 805c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 80ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 80f0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 812c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8130 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 818c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 823c x25: x25 x26: x26
STACK CFI 8240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8248 194 .cfa: sp 0 + .ra: x30
STACK CFI 824c .cfa: sp 96 +
STACK CFI 8250 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8258 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8264 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 8270 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 827c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 83c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 83c8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 83e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 83e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 83ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 846c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8470 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8498 b0 .cfa: sp 0 + .ra: x30
STACK CFI 849c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 84a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8524 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8548 ec .cfa: sp 0 + .ra: x30
STACK CFI 854c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8568 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8584 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 85b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8638 154 .cfa: sp 0 + .ra: x30
STACK CFI 863c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8658 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8668 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8678 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 86c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 86cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 86ec x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 877c x25: x25 x26: x26
STACK CFI 8780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8784 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 8788 x25: x25 x26: x26
STACK CFI INIT 8790 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 8794 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 879c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 87ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 87d0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 881c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8820 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 88b8 x25: .cfa -64 + ^
STACK CFI 891c x25: x25
STACK CFI 8a68 x25: .cfa -64 + ^
STACK CFI 8a6c x25: x25
STACK CFI 8a74 x25: .cfa -64 + ^
STACK CFI INIT 8a78 40 .cfa: sp 0 + .ra: x30
STACK CFI 8a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8ab8 88 .cfa: sp 0 + .ra: x30
STACK CFI 8abc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8aec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8b14 x21: .cfa -16 + ^
STACK CFI 8b30 x21: x21
STACK CFI 8b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8b40 e4 .cfa: sp 0 + .ra: x30
STACK CFI 8b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b48 .cfa: x29 64 +
STACK CFI 8b4c x21: .cfa -32 + ^
STACK CFI 8b58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8c18 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8c28 11c .cfa: sp 0 + .ra: x30
STACK CFI 8c2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8c34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c40 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8c4c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ca8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ce4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8d48 f4 .cfa: sp 0 + .ra: x30
STACK CFI 8d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8de0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e40 29c .cfa: sp 0 + .ra: x30
STACK CFI 8e44 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8e4c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 8e54 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 8e60 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8e74 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 8e9c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8f9c x27: x27 x28: x28
STACK CFI 8fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8fcc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 9054 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 9058 x27: x27 x28: x28
STACK CFI 90d8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 90e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 90e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90ec x19: .cfa -16 + ^
STACK CFI 913c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9140 98 .cfa: sp 0 + .ra: x30
STACK CFI 9144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9150 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 916c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 91d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 91d8 194 .cfa: sp 0 + .ra: x30
STACK CFI 91dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 91e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 91f0 x21: .cfa -16 + ^
STACK CFI 925c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9260 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9370 54 .cfa: sp 0 + .ra: x30
STACK CFI 9374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9380 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 93c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 93c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 93d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 93e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 93f0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 93f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9400 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9420 x21: .cfa -32 + ^
STACK CFI 9458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 945c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 94c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94e8 6c .cfa: sp 0 + .ra: x30
STACK CFI 94ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9558 50 .cfa: sp 0 + .ra: x30
STACK CFI 955c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9568 x19: .cfa -16 + ^
STACK CFI 95a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 95a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95b0 158 .cfa: sp 0 + .ra: x30
STACK CFI 95b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 95bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 95c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 963c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9640 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 969c x23: x23 x24: x24
STACK CFI 96a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 96b4 x23: x23 x24: x24
STACK CFI 96b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9700 x23: x23 x24: x24
STACK CFI 9704 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 9708 2c .cfa: sp 0 + .ra: x30
STACK CFI 9714 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9738 50 .cfa: sp 0 + .ra: x30
STACK CFI 973c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9748 x19: .cfa -16 + ^
STACK CFI 9784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9788 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9790 ec .cfa: sp 0 + .ra: x30
STACK CFI 9794 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 979c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 97a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9818 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9880 2c .cfa: sp 0 + .ra: x30
STACK CFI 988c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 98a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 98b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 98b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 98c0 x19: .cfa -16 + ^
STACK CFI 98f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 98f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9900 c8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99c8 21c .cfa: sp 0 + .ra: x30
STACK CFI 99cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 99d4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 99e0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 9a00 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9a08 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9a10 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9afc x19: x19 x20: x20
STACK CFI 9b04 x21: x21 x22: x22
STACK CFI 9b08 x27: x27 x28: x28
STACK CFI 9b18 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9b1c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 9bcc x19: x19 x20: x20
STACK CFI 9bd0 x21: x21 x22: x22
STACK CFI 9bdc x27: x27 x28: x28
STACK CFI 9be0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 9be8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9bec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9bf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9c2c x21: .cfa -16 + ^
STACK CFI 9c90 x21: x21
STACK CFI 9c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9ca4 x21: x21
STACK CFI 9ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9cb0 12c .cfa: sp 0 + .ra: x30
STACK CFI 9cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9cbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cc8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9de0 98 .cfa: sp 0 + .ra: x30
STACK CFI 9de4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9dec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9df4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9e78 210 .cfa: sp 0 + .ra: x30
STACK CFI 9e7c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9e84 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9e8c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9ea4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a04c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a050 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT a088 178 .cfa: sp 0 + .ra: x30
STACK CFI a08c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a098 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a0a0 x25: .cfa -16 + ^
STACK CFI a0b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a0c4 x21: x21 x22: x22
STACK CFI a0d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI a0d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a0e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a118 x21: x21 x22: x22
STACK CFI a11c x23: x23 x24: x24
STACK CFI a124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI a128 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a1cc x23: x23 x24: x24
STACK CFI a1dc x21: x21 x22: x22
STACK CFI a1e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI a1e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT a200 78 .cfa: sp 0 + .ra: x30
STACK CFI a204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a210 x19: .cfa -16 + ^
STACK CFI a24c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a250 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a264 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a268 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a278 80 .cfa: sp 0 + .ra: x30
STACK CFI a27c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a288 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a2bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a2f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a2f8 3c .cfa: sp 0 + .ra: x30
STACK CFI a2fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a308 x19: .cfa -16 + ^
STACK CFI a324 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a328 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a330 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a338 80 .cfa: sp 0 + .ra: x30
STACK CFI a33c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a348 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a358 x21: .cfa -16 + ^
STACK CFI a39c x21: x21
STACK CFI a3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a3b8 2c .cfa: sp 0 + .ra: x30
STACK CFI a3bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3c4 x19: .cfa -16 + ^
STACK CFI a3e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a3e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a400 68 .cfa: sp 0 + .ra: x30
STACK CFI a404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a40c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a42c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a468 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a478 1e4 .cfa: sp 0 + .ra: x30
STACK CFI a47c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a484 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a48c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a53c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a660 dc .cfa: sp 0 + .ra: x30
STACK CFI a664 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a66c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a6a8 x21: .cfa -16 + ^
STACK CFI a6cc x21: x21
STACK CFI a6d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a740 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a748 ac .cfa: sp 0 + .ra: x30
STACK CFI a74c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a754 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a764 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a77c x23: .cfa -32 + ^
STACK CFI a7ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a7f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT a7f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a808 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT a830 e8 .cfa: sp 0 + .ra: x30
STACK CFI a834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a83c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a864 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a874 x21: .cfa -16 + ^
STACK CFI a908 x21: x21
STACK CFI a914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a918 28 .cfa: sp 0 + .ra: x30
STACK CFI a91c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a924 x19: .cfa -16 + ^
STACK CFI INIT a940 58 .cfa: sp 0 + .ra: x30
STACK CFI a94c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a95c x19: .cfa -16 + ^
STACK CFI a970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a978 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a994 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a998 1d8 .cfa: sp 0 + .ra: x30
STACK CFI a99c .cfa: sp 112 +
STACK CFI a9a0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a9a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a9b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a9cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI aa40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI aa44 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI aab4 x25: .cfa -32 + ^
STACK CFI ab28 x25: x25
STACK CFI ab2c x25: .cfa -32 + ^
STACK CFI ab68 x25: x25
STACK CFI ab6c x25: .cfa -32 + ^
STACK CFI INIT ab70 10c .cfa: sp 0 + .ra: x30
STACK CFI ab74 .cfa: sp 96 +
STACK CFI ab7c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ab84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI abc8 x21: .cfa -32 + ^
STACK CFI ac18 x21: x21
STACK CFI ac3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac40 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI ac78 x21: .cfa -32 + ^
STACK CFI INIT ac80 24 .cfa: sp 0 + .ra: x30
STACK CFI ac84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT aca8 b8 .cfa: sp 0 + .ra: x30
STACK CFI acac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI acb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI acbc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI acc8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI acd4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ad5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT ad60 1ac .cfa: sp 0 + .ra: x30
STACK CFI ad64 .cfa: sp 160 +
STACK CFI ad68 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ad70 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ad78 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ad88 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI ad94 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI ad9c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ae48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ae4c .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT af10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT af18 194 .cfa: sp 0 + .ra: x30
STACK CFI af20 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI af28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI af34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI af40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI af4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI af58 x27: .cfa -16 + ^
STACK CFI b008 x19: x19 x20: x20
STACK CFI b00c x21: x21 x22: x22
STACK CFI b014 x25: x25 x26: x26
STACK CFI b018 x27: x27
STACK CFI b01c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b020 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI b028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b044 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b050 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b068 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b06c x19: x19 x20: x20
STACK CFI b07c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b090 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT b0b0 7c .cfa: sp 0 + .ra: x30
