MODULE Linux arm64 E5B652E846D0DA5BDE0CAE4825D233760 libebl_sh.so
INFO CODE_ID E852B6E5D0465BDADE0CAE4825D233764BE3808C
PUBLIC 11f0 0 sh_init
STACK CFI INIT 1008 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1038 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1078 48 .cfa: sp 0 + .ra: x30
STACK CFI 107c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1084 x19: .cfa -16 + ^
STACK CFI 10bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1130 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1140 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1150 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1154 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 115c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1298 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12a8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12c0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12f8 21c .cfa: sp 0 + .ra: x30
STACK CFI 12fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1308 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1318 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1320 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 13ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1518 350 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1868 1fc .cfa: sp 0 + .ra: x30
STACK CFI 186c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1874 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1888 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 189c x23: .cfa -96 + ^
STACK CFI 1934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1938 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
