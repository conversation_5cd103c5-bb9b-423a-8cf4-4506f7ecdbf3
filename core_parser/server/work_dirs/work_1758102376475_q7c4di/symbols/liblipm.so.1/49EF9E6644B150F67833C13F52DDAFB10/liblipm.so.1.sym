MODULE Linux arm64 49EF9E6644B150F67833C13F52DDAFB10 liblipm.so.1
INFO CODE_ID 669EEF49B144F6507833C13F52DDAFB1
PUBLIC 3e18 0 _init
PUBLIC 41f0 0 _GLOBAL__sub_I_client_manager.cpp
PUBLIC 43b0 0 _GLOBAL__sub_I_lipm.cpp
PUBLIC 4570 0 _GLOBAL__sub_I_pm_client.cc
PUBLIC 4750 0 _GLOBAL__sub_I_pm_client_interface.cc
PUBLIC 478c 0 call_weak_fn
PUBLIC 47a0 0 deregister_tm_clones
PUBLIC 47d0 0 register_tm_clones
PUBLIC 480c 0 __do_global_dtors_aux
PUBLIC 485c 0 frame_dummy
PUBLIC 4860 0 liware::lipm::ClientManager::GetName[abi:cxx11]()
PUBLIC 4950 0 liware::lipm::ClientManager::GetPid()
PUBLIC 4960 0 liware::lipm::ClientManager::ClientManager()
PUBLIC 4de0 0 liware::lipm::ClientManager::GetInstance()
PUBLIC 4e70 0 liware::lipm::ClientManager::SendMessage(int)
PUBLIC 5510 0 liware::lipm::ClientManager::Release()
PUBLIC 5520 0 liware::lipm::ClientManager::Acquire()
PUBLIC 5530 0 liware::lipm::ClientManager::Reboot()
PUBLIC 5540 0 liware::lipm::ClientManager::~ClientManager()
PUBLIC 5560 0 lifmt::v7::system_error::~system_error()
PUBLIC 5580 0 lifmt::v7::system_error::~system_error()
PUBLIC 55c0 0 lifmt::v7::format_error::~format_error()
PUBLIC 55e0 0 lifmt::v7::format_error::~format_error()
PUBLIC 5620 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > __gnu_cxx::__to_xstring<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, char>(int (*)(char*, unsigned long, char const*, std::__va_list), unsigned long, char const*, ...)
PUBLIC 5770 0 std::_Function_base::_Base_manager<liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 5890 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5990 0 liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)
PUBLIC 5bf0 0 liware::lipm::Acquire()
PUBLIC 5c10 0 liware::lipm::Release()
PUBLIC 5c30 0 liware::lipm::Reboot()
PUBLIC 5c50 0 std::_Function_base::_Base_manager<lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 5c90 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5e00 0 lipm_init
PUBLIC 5e90 0 lipm_acquire
PUBLIC 5ea0 0 lipm_release
PUBLIC 5eb0 0 lipm_reboot
PUBLIC 5ec0 0 liware::libs::pmclient::PmClient::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 5f10 0 std::_Function_base::_Base_manager<liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 6030 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6180 0 liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 6380 0 liware::libs::pmclient::PmClient::PmClient()
PUBLIC 6450 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 6490 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 64e0 0 liware::libs::pmclient::PmClientInterface::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 6500 0 liware::libs::pmclient::PmClientInterface::PmClientInterface(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 66e0 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 66f0 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 6750 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6760 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 6770 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 67e0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 6898 0 _fini
STACK CFI INIT 47a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 480c 50 .cfa: sp 0 + .ra: x30
STACK CFI 481c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4824 x19: .cfa -16 + ^
STACK CFI 4854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 485c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5560 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5580 38 .cfa: sp 0 + .ra: x30
STACK CFI 5584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5594 x19: .cfa -16 + ^
STACK CFI 55b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 55e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55f4 x19: .cfa -16 + ^
STACK CFI 5614 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4860 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4864 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4870 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4878 x21: .cfa -32 + ^
STACK CFI 48c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 48e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 493c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5620 150 .cfa: sp 0 + .ra: x30
STACK CFI 5624 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5630 .cfa: x29 304 +
STACK CFI 5648 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 5660 x21: .cfa -272 + ^
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 56f4 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 5714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5718 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 576c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4960 474 .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 960 +
STACK CFI 496c .ra: .cfa -952 + ^ x29: .cfa -960 + ^
STACK CFI 4974 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 4988 x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^
STACK CFI 4990 x25: .cfa -896 + ^
STACK CFI 4c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4c48 .cfa: sp 960 + .ra: .cfa -952 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x29: .cfa -960 + ^
STACK CFI INIT 4de0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4dec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e70 698 .cfa: sp 0 + .ra: x30
STACK CFI 4e74 .cfa: sp 560 +
STACK CFI 4e80 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 4e8c x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 4eac x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 527c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5280 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI INIT 5510 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5530 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41f0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4224 x23: .cfa -16 + ^
STACK CFI 4230 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 423c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5770 114 .cfa: sp 0 + .ra: x30
STACK CFI 5774 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5780 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 57f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5830 x21: x21 x22: x22
STACK CFI 5834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5838 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5854 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5890 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5894 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 58ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 58d4 x21: .cfa -80 + ^
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5938 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5990 25c .cfa: sp 0 + .ra: x30
STACK CFI 5994 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 599c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 59a4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 59d4 x23: .cfa -112 + ^
STACK CFI 5b08 x23: x23
STACK CFI 5b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b10 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI 5b20 x23: x23
STACK CFI 5b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b28 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 5b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b58 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5bf0 14 .cfa: sp 0 + .ra: x30
STACK CFI 5bf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c10 14 .cfa: sp 0 + .ra: x30
STACK CFI 5c14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c30 14 .cfa: sp 0 + .ra: x30
STACK CFI 5c34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43b0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 43c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43e4 x23: .cfa -16 + ^
STACK CFI 43f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5c50 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c90 170 .cfa: sp 0 + .ra: x30
STACK CFI 5c94 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5ca4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5cb4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5cc0 x23: .cfa -112 + ^
STACK CFI 5da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5da4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5e00 84 .cfa: sp 0 + .ra: x30
STACK CFI 5e04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5e90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6450 3c .cfa: sp 0 + .ra: x30
STACK CFI 6470 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6490 4c .cfa: sp 0 + .ra: x30
STACK CFI 6494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64a8 x19: .cfa -16 + ^
STACK CFI 64d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ec0 4c .cfa: sp 0 + .ra: x30
STACK CFI 5ec4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ee4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5efc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5f10 114 .cfa: sp 0 + .ra: x30
STACK CFI 5f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5f90 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5fd0 x21: x21 x22: x22
STACK CFI 5fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ff4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6030 14c .cfa: sp 0 + .ra: x30
STACK CFI 6034 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6044 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6060 x21: .cfa -80 + ^
STACK CFI 60f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6180 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 6184 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6190 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 61a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 62d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62dc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6380 cc .cfa: sp 0 + .ra: x30
STACK CFI 6384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6394 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 639c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6428 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4570 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4574 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 457c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45cc x23: .cfa -16 + ^
STACK CFI 45dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 471c x21: x21 x22: x22
STACK CFI 473c x23: x23
STACK CFI 474c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 66e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 66f0 60 .cfa: sp 0 + .ra: x30
STACK CFI 66f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6704 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 674c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6770 64 .cfa: sp 0 + .ra: x30
STACK CFI 67a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 64e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67e0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 67e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6824 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 687c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6888 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6500 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6504 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 650c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6518 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6520 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 652c x25: .cfa -48 + ^
STACK CFI 6604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6608 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4750 3c .cfa: sp 0 + .ra: x30
STACK CFI 4754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 475c x19: .cfa -16 + ^
STACK CFI 4784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
