MODULE Linux arm64 49EE1E6684B150E97833C53F525DAEB10 libpmclient.so.1
INFO CODE_ID 661EEE49B184E9507833C53F525DAEB1
PUBLIC 3e20 0 _init
PUBLIC 4200 0 _GLOBAL__sub_I_client_manager.cpp
PUBLIC 43c0 0 _GLOBAL__sub_I_lipm.cpp
PUBLIC 4580 0 _GLOBAL__sub_I_pm_client.cc
PUBLIC 4760 0 _GLOBAL__sub_I_pm_client_interface.cc
PUBLIC 479c 0 call_weak_fn
PUBLIC 47b0 0 deregister_tm_clones
PUBLIC 47e0 0 register_tm_clones
PUBLIC 481c 0 __do_global_dtors_aux
PUBLIC 486c 0 frame_dummy
PUBLIC 4870 0 liware::lipm::ClientManager::GetName[abi:cxx11]()
PUBLIC 4960 0 liware::lipm::ClientManager::GetPid()
PUBLIC 4970 0 liware::lipm::ClientManager::ClientManager()
PUBLIC 4df0 0 liware::lipm::ClientManager::GetInstance()
PUBLIC 4e80 0 liware::lipm::ClientManager::SendMessage(int)
PUBLIC 5520 0 liware::lipm::ClientManager::Release()
PUBLIC 5530 0 liware::lipm::ClientManager::Acquire()
PUBLIC 5540 0 liware::lipm::ClientManager::Reboot()
PUBLIC 5550 0 liware::lipm::ClientManager::~ClientManager()
PUBLIC 5570 0 lifmt::v7::system_error::~system_error()
PUBLIC 5590 0 lifmt::v7::system_error::~system_error()
PUBLIC 55d0 0 lifmt::v7::format_error::~format_error()
PUBLIC 55f0 0 lifmt::v7::format_error::~format_error()
PUBLIC 5630 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > __gnu_cxx::__to_xstring<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, char>(int (*)(char*, unsigned long, char const*, std::__va_list), unsigned long, char const*, ...)
PUBLIC 5780 0 std::_Function_base::_Base_manager<liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 58a0 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 59a0 0 liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)
PUBLIC 5c00 0 liware::lipm::Acquire()
PUBLIC 5c20 0 liware::lipm::Release()
PUBLIC 5c40 0 liware::lipm::Reboot()
PUBLIC 5c60 0 std::_Function_base::_Base_manager<lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 5ca0 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5e10 0 lipm_init
PUBLIC 5ea0 0 lipm_acquire
PUBLIC 5eb0 0 lipm_release
PUBLIC 5ec0 0 lipm_reboot
PUBLIC 5ed0 0 liware::libs::pmclient::PmClient::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 5f20 0 std::_Function_base::_Base_manager<liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 6040 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6190 0 liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 6390 0 liware::libs::pmclient::PmClient::PmClient()
PUBLIC 6460 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 64a0 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 64f0 0 liware::libs::pmclient::PmClientInterface::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 6510 0 liware::libs::pmclient::PmClientInterface::PmClientInterface(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 66f0 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6700 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 6760 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6770 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 6780 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<liware::libs::pmclient::PmClient>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 67f0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 68a8 0 _fini
STACK CFI INIT 47b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 481c 50 .cfa: sp 0 + .ra: x30
STACK CFI 482c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4834 x19: .cfa -16 + ^
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 486c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5550 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5570 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5590 38 .cfa: sp 0 + .ra: x30
STACK CFI 5594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55a4 x19: .cfa -16 + ^
STACK CFI 55c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 55f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5604 x19: .cfa -16 + ^
STACK CFI 5624 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4870 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4880 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4888 x21: .cfa -32 + ^
STACK CFI 48d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 48f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 494c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5630 150 .cfa: sp 0 + .ra: x30
STACK CFI 5634 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5640 .cfa: x29 304 +
STACK CFI 5658 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 5670 x21: .cfa -272 + ^
STACK CFI 5700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5704 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 5724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5728 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 577c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4970 474 .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 960 +
STACK CFI 497c .ra: .cfa -952 + ^ x29: .cfa -960 + ^
STACK CFI 4984 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 4998 x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^
STACK CFI 49a0 x25: .cfa -896 + ^
STACK CFI 4c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4c58 .cfa: sp 960 + .ra: .cfa -952 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x29: .cfa -960 + ^
STACK CFI INIT 4df0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4dfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e80 698 .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 560 +
STACK CFI 4e90 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 4e9c x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 4ebc x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 528c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5290 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI INIT 5520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5530 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4200 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4214 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4234 x23: .cfa -16 + ^
STACK CFI 4240 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 424c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5780 114 .cfa: sp 0 + .ra: x30
STACK CFI 5784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5790 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5800 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5840 x21: x21 x22: x22
STACK CFI 5844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5848 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5864 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 58a0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 58a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 58bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 58e4 x21: .cfa -80 + ^
STACK CFI 5944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5948 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 59a0 25c .cfa: sp 0 + .ra: x30
STACK CFI 59a4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 59ac x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 59b4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 59e4 x23: .cfa -112 + ^
STACK CFI 5b18 x23: x23
STACK CFI 5b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b20 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI 5b30 x23: x23
STACK CFI 5b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b38 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 5b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b68 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5c00 14 .cfa: sp 0 + .ra: x30
STACK CFI 5c04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c20 14 .cfa: sp 0 + .ra: x30
STACK CFI 5c24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c40 14 .cfa: sp 0 + .ra: x30
STACK CFI 5c44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43c0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 43d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43f4 x23: .cfa -16 + ^
STACK CFI 4400 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 440c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5c60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ca0 170 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5cb4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5cc4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5cd0 x23: .cfa -112 + ^
STACK CFI 5db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5db4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5e10 84 .cfa: sp 0 + .ra: x30
STACK CFI 5e14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5ea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ec0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6460 3c .cfa: sp 0 + .ra: x30
STACK CFI 6480 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6494 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 64a0 4c .cfa: sp 0 + .ra: x30
STACK CFI 64a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64b8 x19: .cfa -16 + ^
STACK CFI 64e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ed0 4c .cfa: sp 0 + .ra: x30
STACK CFI 5ed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ef4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ef8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5f20 114 .cfa: sp 0 + .ra: x30
STACK CFI 5f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5fa0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5fe0 x21: x21 x22: x22
STACK CFI 5fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6004 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6040 14c .cfa: sp 0 + .ra: x30
STACK CFI 6044 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6054 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6070 x21: .cfa -80 + ^
STACK CFI 6100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6104 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6190 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 6194 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 61a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 61b4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 62e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62ec .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6390 cc .cfa: sp 0 + .ra: x30
STACK CFI 6394 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 63ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4580 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4584 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 458c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45dc x23: .cfa -16 + ^
STACK CFI 45ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 472c x21: x21 x22: x22
STACK CFI 474c x23: x23
STACK CFI 475c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 66f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6700 60 .cfa: sp 0 + .ra: x30
STACK CFI 6704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6714 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 675c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6770 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6780 64 .cfa: sp 0 + .ra: x30
STACK CFI 67b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 64f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67f0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 67f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6834 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 688c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6898 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6510 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6514 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 651c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6528 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6530 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 653c x25: .cfa -48 + ^
STACK CFI 6614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6618 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4760 3c .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 476c x19: .cfa -16 + ^
STACK CFI 4794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
