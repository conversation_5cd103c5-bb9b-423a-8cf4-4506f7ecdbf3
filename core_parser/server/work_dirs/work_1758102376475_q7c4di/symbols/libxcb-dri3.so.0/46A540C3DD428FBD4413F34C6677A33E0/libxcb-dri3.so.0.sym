MODULE Linux arm64 46A540C3DD428FBD4413F34C6677A33E0 libxcb-dri3.so.0
INFO CODE_ID C340A54642DDBD8F4413F34C6677A33E81AE5017
PUBLIC 15d8 0 xcb_dri3_query_version
PUBLIC 1648 0 xcb_dri3_query_version_unchecked
PUBLIC 16b8 0 xcb_dri3_query_version_reply
PUBLIC 16c0 0 xcb_dri3_open
PUBLIC 1730 0 xcb_dri3_open_unchecked
PUBLIC 17a0 0 xcb_dri3_open_reply
PUBLIC 17a8 0 xcb_dri3_open_reply_fds
PUBLIC 17b8 0 xcb_dri3_pixmap_from_buffer_checked
PUBLIC 1858 0 xcb_dri3_pixmap_from_buffer
PUBLIC 18f8 0 xcb_dri3_buffer_from_pixmap
PUBLIC 1968 0 xcb_dri3_buffer_from_pixmap_unchecked
PUBLIC 19d8 0 xcb_dri3_buffer_from_pixmap_reply
PUBLIC 19e0 0 xcb_dri3_buffer_from_pixmap_reply_fds
PUBLIC 19f0 0 xcb_dri3_fence_from_fd_checked
PUBLIC 1a80 0 xcb_dri3_fence_from_fd
PUBLIC 1b08 0 xcb_dri3_fd_from_fence
PUBLIC 1b78 0 xcb_dri3_fd_from_fence_unchecked
PUBLIC 1be8 0 xcb_dri3_fd_from_fence_reply
PUBLIC 1bf0 0 xcb_dri3_fd_from_fence_reply_fds
PUBLIC 1c00 0 xcb_dri3_get_supported_modifiers_sizeof
PUBLIC 1c18 0 xcb_dri3_get_supported_modifiers
PUBLIC 1c98 0 xcb_dri3_get_supported_modifiers_unchecked
PUBLIC 1d18 0 xcb_dri3_get_supported_modifiers_window_modifiers
PUBLIC 1d20 0 xcb_dri3_get_supported_modifiers_window_modifiers_length
PUBLIC 1d28 0 xcb_dri3_get_supported_modifiers_window_modifiers_end
PUBLIC 1d40 0 xcb_dri3_get_supported_modifiers_screen_modifiers
PUBLIC 1d68 0 xcb_dri3_get_supported_modifiers_screen_modifiers_length
PUBLIC 1d70 0 xcb_dri3_get_supported_modifiers_screen_modifiers_end
PUBLIC 1db0 0 xcb_dri3_get_supported_modifiers_reply
PUBLIC 1db8 0 xcb_dri3_pixmap_from_buffers_checked
PUBLIC 1f08 0 xcb_dri3_pixmap_from_buffers
PUBLIC 2058 0 xcb_dri3_buffers_from_pixmap_sizeof
PUBLIC 2068 0 xcb_dri3_buffers_from_pixmap
PUBLIC 20d8 0 xcb_dri3_buffers_from_pixmap_unchecked
PUBLIC 2148 0 xcb_dri3_buffers_from_pixmap_strides
PUBLIC 2150 0 xcb_dri3_buffers_from_pixmap_strides_length
PUBLIC 2158 0 xcb_dri3_buffers_from_pixmap_strides_end
PUBLIC 2170 0 xcb_dri3_buffers_from_pixmap_offsets
PUBLIC 2198 0 xcb_dri3_buffers_from_pixmap_offsets_length
PUBLIC 21a0 0 xcb_dri3_buffers_from_pixmap_offsets_end
PUBLIC 21e0 0 xcb_dri3_buffers_from_pixmap_buffers
PUBLIC 2208 0 xcb_dri3_buffers_from_pixmap_buffers_length
PUBLIC 2210 0 xcb_dri3_buffers_from_pixmap_buffers_end
PUBLIC 2250 0 xcb_dri3_buffers_from_pixmap_reply
PUBLIC 2258 0 xcb_dri3_buffers_from_pixmap_reply_fds
STACK CFI INIT 1518 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1548 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1588 48 .cfa: sp 0 + .ra: x30
STACK CFI 158c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1594 x19: .cfa -16 + ^
STACK CFI 15cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15d8 6c .cfa: sp 0 + .ra: x30
STACK CFI 15dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 15ec x19: .cfa -112 + ^
STACK CFI 163c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1640 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1648 6c .cfa: sp 0 + .ra: x30
STACK CFI 164c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 165c x19: .cfa -112 + ^
STACK CFI 16ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 16b0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 16b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 16d4 x19: .cfa -112 + ^
STACK CFI 1728 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 172c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1730 70 .cfa: sp 0 + .ra: x30
STACK CFI 1734 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1744 x19: .cfa -112 + ^
STACK CFI 1798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 179c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 17a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b8 9c .cfa: sp 0 + .ra: x30
STACK CFI 17bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 17cc x19: .cfa -128 + ^
STACK CFI 184c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1850 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1858 9c .cfa: sp 0 + .ra: x30
STACK CFI 185c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 186c x19: .cfa -128 + ^
STACK CFI 18ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 18f8 6c .cfa: sp 0 + .ra: x30
STACK CFI 18fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 190c x19: .cfa -96 + ^
STACK CFI 195c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1960 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1968 6c .cfa: sp 0 + .ra: x30
STACK CFI 196c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 197c x19: .cfa -96 + ^
STACK CFI 19cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 19d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f0 8c .cfa: sp 0 + .ra: x30
STACK CFI 19f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1a04 x19: .cfa -112 + ^
STACK CFI 1a74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a78 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a80 88 .cfa: sp 0 + .ra: x30
STACK CFI 1a84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1a94 x19: .cfa -112 + ^
STACK CFI 1b00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1b08 70 .cfa: sp 0 + .ra: x30
STACK CFI 1b0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1b1c x19: .cfa -112 + ^
STACK CFI 1b70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b74 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1b78 70 .cfa: sp 0 + .ra: x30
STACK CFI 1b7c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1b8c x19: .cfa -112 + ^
STACK CFI 1be0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1be4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1be8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bf0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c18 7c .cfa: sp 0 + .ra: x30
STACK CFI 1c1c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1c2c x19: .cfa -112 + ^
STACK CFI 1c8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c90 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1c98 7c .cfa: sp 0 + .ra: x30
STACK CFI 1c9c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1cac x19: .cfa -112 + ^
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1d18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d28 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d40 24 .cfa: sp 0 + .ra: x30
STACK CFI 1d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d70 40 .cfa: sp 0 + .ra: x30
STACK CFI 1d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d7c x19: .cfa -16 + ^
STACK CFI 1dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1db0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1db8 14c .cfa: sp 0 + .ra: x30
STACK CFI 1dbc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1dc8 .cfa: x29 176 +
STACK CFI 1dcc x19: .cfa -160 + ^
STACK CFI 1ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ef8 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1f08 14c .cfa: sp 0 + .ra: x30
STACK CFI 1f0c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1f18 .cfa: x29 176 +
STACK CFI 1f1c x19: .cfa -160 + ^
STACK CFI 2044 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2048 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2058 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2068 6c .cfa: sp 0 + .ra: x30
STACK CFI 206c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 207c x19: .cfa -96 + ^
STACK CFI 20cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 20d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 20d8 6c .cfa: sp 0 + .ra: x30
STACK CFI 20dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 20ec x19: .cfa -96 + ^
STACK CFI 213c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2140 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2148 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2150 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2158 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2170 24 .cfa: sp 0 + .ra: x30
STACK CFI 2174 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2190 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2198 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 21a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21ac x19: .cfa -16 + ^
STACK CFI 21dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 21e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2208 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2210 40 .cfa: sp 0 + .ra: x30
STACK CFI 2214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 221c x19: .cfa -16 + ^
STACK CFI 224c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2250 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2258 10 .cfa: sp 0 + .ra: x30
