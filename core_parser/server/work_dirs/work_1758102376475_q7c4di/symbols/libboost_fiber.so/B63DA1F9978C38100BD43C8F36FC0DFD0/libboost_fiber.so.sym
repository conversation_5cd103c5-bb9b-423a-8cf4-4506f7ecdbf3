MODULE Linux arm64 B63DA1F9978C38100BD43C8F36FC0DFD0 libboost_fiber.so.1.77.0
INFO CODE_ID F9A13DB68C9710380BD43C8F36FC0DFD
PUBLIC 6370 0 _init
PUBLIC 6b20 0 boost::context::detail::fiber_unwind(boost::context::detail::transfer_t)
PUBLIC 6b4c 0 std::unique_lock<boost::fibers::detail::spinlock_ttas>::unlock() [clone .isra.0]
PUBLIC 6b78 0 std::unique_lock<boost::fibers::detail::spinlock_ttas>::unlock() [clone .isra.0]
PUBLIC 6ba4 0 std::unique_lock<boost::fibers::detail::spinlock_ttas>::unlock() [clone .isra.0]
PUBLIC 6bd0 0 std::unique_lock<boost::fibers::detail::spinlock_ttas>::unlock() [clone .isra.0]
PUBLIC 6c00 0 _GLOBAL__sub_I_algorithm.cpp
PUBLIC 6c40 0 _GLOBAL__sub_I_round_robin.cpp
PUBLIC 6c80 0 _GLOBAL__sub_I_shared_work.cpp
PUBLIC 6d80 0 _GLOBAL__sub_I_work_stealing.cpp
PUBLIC 6df0 0 _GLOBAL__sub_I_barrier.cpp
PUBLIC 6e30 0 _GLOBAL__sub_I_condition_variable.cpp
PUBLIC 6e70 0 _GLOBAL__sub_I_context.cpp
PUBLIC 6eb0 0 _GLOBAL__sub_I_fiber.cpp
PUBLIC 6ef0 0 _GLOBAL__sub_I_waker.cpp
PUBLIC 6f30 0 _GLOBAL__sub_I_mutex.cpp
PUBLIC 6f70 0 _GLOBAL__sub_I_properties.cpp
PUBLIC 6fb0 0 _GLOBAL__sub_I_recursive_mutex.cpp
PUBLIC 6ff0 0 _GLOBAL__sub_I_recursive_timed_mutex.cpp
PUBLIC 7030 0 _GLOBAL__sub_I_timed_mutex.cpp
PUBLIC 7070 0 _GLOBAL__sub_I_scheduler.cpp
PUBLIC 70ac 0 call_weak_fn
PUBLIC 70c0 0 deregister_tm_clones
PUBLIC 70f0 0 register_tm_clones
PUBLIC 712c 0 __do_global_dtors_aux
PUBLIC 717c 0 frame_dummy
PUBLIC 7180 0 boost::fibers::algo::algorithm_with_properties_base::get_properties(boost::fibers::context*)
PUBLIC 7190 0 boost::fibers::algo::algorithm_with_properties_base::set_properties(boost::fibers::context*, boost::fibers::fiber_properties*)
PUBLIC 71a0 0 boost::fibers::algo::round_robin::awakened(boost::fibers::context*)
PUBLIC 71c0 0 boost::fibers::algo::round_robin::pick_next()
PUBLIC 7210 0 boost::fibers::algo::round_robin::has_ready_fibers() const
PUBLIC 7230 0 boost::fibers::algo::round_robin::notify()
PUBLIC 7290 0 boost::fibers::algo::round_robin::suspend_until(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC 7430 0 boost::fibers::algo::round_robin::~round_robin()
PUBLIC 7490 0 boost::fibers::algo::round_robin::~round_robin()
PUBLIC 74f0 0 boost::fibers::algo::shared_work::notify()
PUBLIC 7570 0 boost::fibers::algo::shared_work::pick_next()
PUBLIC 7680 0 boost::fibers::algo::shared_work::suspend_until(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC 7830 0 boost::fibers::algo::shared_work::awakened(boost::fibers::context*)
PUBLIC 78f0 0 std::deque<boost::fibers::context*, std::allocator<boost::fibers::context*> >::~deque()
PUBLIC 7950 0 boost::fibers::algo::shared_work::~shared_work()
PUBLIC 79b0 0 boost::fibers::algo::shared_work::~shared_work()
PUBLIC 7a10 0 boost::fibers::algo::shared_work::has_ready_fibers() const
PUBLIC 7aa0 0 void std::deque<boost::fibers::context*, std::allocator<boost::fibers::context*> >::_M_push_back_aux<boost::fibers::context* const&>(boost::fibers::context* const&)
PUBLIC 7c60 0 boost::fibers::algo::work_stealing::notify()
PUBLIC 7ce0 0 boost::fibers::algo::work_stealing::init_(unsigned int, std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > >&)
PUBLIC 7e10 0 boost::fibers::algo::work_stealing::suspend_until(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC 7fc0 0 boost::fibers::algo::work_stealing::work_stealing(unsigned int, bool)
PUBLIC 82a0 0 boost::fibers::algo::work_stealing::awakened(boost::fibers::context*)
PUBLIC 8410 0 boost::fibers::algo::work_stealing::pick_next() [clone .part.0]
PUBLIC 8810 0 boost::fibers::algo::work_stealing::pick_next()
PUBLIC 88d0 0 std::call_once<void (*)(unsigned int, std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > >&), unsigned int&, std::reference_wrapper<std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > > > >(std::once_flag&, void (*&&)(unsigned int, std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > >&), unsigned int&, std::reference_wrapper<std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > > >&&)::{lambda()#2}::_FUN()
PUBLIC 8910 0 boost::fibers::detail::thread_barrier::~thread_barrier()
PUBLIC 8920 0 boost::fibers::algo::work_stealing::~work_stealing()
PUBLIC 8970 0 boost::fibers::algo::work_stealing::~work_stealing()
PUBLIC 89c0 0 std::vector<boost::intrusive_ptr<boost::fibers::algo::work_stealing>, std::allocator<boost::intrusive_ptr<boost::fibers::algo::work_stealing> > >::~vector()
PUBLIC 8a50 0 unsigned long std::uniform_int_distribution<unsigned long>::operator()<std::linear_congruential_engine<unsigned long, 48271ul, 0ul, 2147483647ul> >(std::linear_congruential_engine<unsigned long, 48271ul, 0ul, 2147483647ul>&, std::uniform_int_distribution<unsigned long>::param_type const&)
PUBLIC 8c10 0 boost::fibers::detail::spinlock_ttas::lock()
PUBLIC 8e00 0 boost::fibers::algo::work_stealing::has_ready_fibers() const
PUBLIC 8e40 0 boost::fibers::algo::work_stealing::steal()
PUBLIC 8ec0 0 unsigned int std::uniform_int_distribution<unsigned int>::operator()<std::linear_congruential_engine<unsigned long, 48271ul, 0ul, 2147483647ul> >(std::linear_congruential_engine<unsigned long, 48271ul, 0ul, 2147483647ul>&, std::uniform_int_distribution<unsigned int>::param_type const&)
PUBLIC 9070 0 boost::fibers::barrier::barrier(unsigned long)
PUBLIC 9170 0 boost::fibers::barrier::wait()
PUBLIC 9320 0 boost::fibers::fiber_error::~fiber_error()
PUBLIC 9330 0 boost::fibers::fiber_error::~fiber_error()
PUBLIC 9370 0 std::system_error::system_error(std::error_code, char const*)
PUBLIC 9540 0 boost::fibers::condition_variable_any::notify_all()
PUBLIC 9570 0 boost::fibers::condition_variable_any::notify_one()
PUBLIC 95a0 0 boost::context::detail::transfer_t boost::context::detail::fiber_ontop<boost::context::fiber, boost::fibers::context::suspend_with_cc()::{lambda(boost::context::fiber&&)#1}>(boost::fibers::context::suspend_with_cc()::{lambda(boost::context::fiber&&)#1})
PUBLIC 95f0 0 boost::context::detail::transfer_t boost::context::detail::fiber_ontop<boost::context::fiber, boost::fibers::context::resume()::{lambda(boost::context::fiber&&)#1}>(boost::fibers::context::resume()::{lambda(boost::context::fiber&&)#1})
PUBLIC 9640 0 boost::context::detail::transfer_t boost::context::detail::fiber_ontop<boost::context::fiber, boost::fibers::context::resume(std::unique_lock<boost::fibers::detail::spinlock_ttas>&)::{lambda(boost::context::fiber&&)#1}>(boost::fibers::context::resume(std::unique_lock<boost::fibers::detail::spinlock_ttas>&)::{lambda(boost::context::fiber&&)#1})
PUBLIC 96b0 0 boost::fibers::context::reset_active()
PUBLIC 96e0 0 boost::fibers::context::get_id() const
PUBLIC 96f0 0 boost::fibers::context::resume()
PUBLIC 9760 0 boost::fibers::context::resume(std::unique_lock<boost::fibers::detail::spinlock_ttas>&)
PUBLIC 97d0 0 boost::fibers::context::resume(boost::fibers::context*)
PUBLIC 9840 0 boost::fibers::context::suspend()
PUBLIC 9850 0 boost::fibers::context::suspend(std::unique_lock<boost::fibers::detail::spinlock_ttas>&)
PUBLIC 9860 0 boost::fibers::context::suspend_with_cc()
PUBLIC 98d0 0 boost::fibers::context::wait_until(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC 98e0 0 boost::fibers::context::wait_until(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&, std::unique_lock<boost::fibers::detail::spinlock_ttas>&, boost::fibers::waker&&)
PUBLIC 9900 0 boost::fibers::context::schedule(boost::fibers::context*)
PUBLIC 9920 0 boost::fibers::context::get_fss_data(void const*) const
PUBLIC 9970 0 boost::fibers::context::set_properties(boost::fibers::fiber_properties*)
PUBLIC 99d0 0 boost::fibers::context::worker_is_linked() const
PUBLIC 99f0 0 boost::fibers::context::ready_is_linked() const
PUBLIC 9a10 0 boost::fibers::context::remote_ready_is_linked() const
PUBLIC 9a30 0 boost::fibers::context::sleep_is_linked() const
PUBLIC 9a40 0 boost::fibers::context::terminated_is_linked() const
PUBLIC 9a60 0 boost::fibers::context::worker_unlink()
PUBLIC 9a80 0 boost::fibers::context::ready_unlink()
PUBLIC 9aa0 0 boost::fibers::context::sleep_unlink()
PUBLIC 9fc0 0 boost::fibers::context::detach()
PUBLIC 9fd0 0 boost::fibers::context::attach(boost::fibers::context*)
PUBLIC 9fe0 0 boost::fibers::context::terminate()
PUBLIC a120 0 boost::fibers::context::~context()
PUBLIC a770 0 boost::fibers::context::~context()
PUBLIC a7a0 0 boost::fibers::context::active()
PUBLIC a830 0 boost::context::detail::transfer_t boost::context::detail::fiber_ontop<boost::context::fiber, boost::fibers::context::resume(boost::fibers::context*)::{lambda(boost::context::fiber&&)#1}>(boost::fibers::context::resume(boost::fibers::context*)::{lambda(boost::context::fiber&&)#1})
PUBLIC a890 0 boost::fibers::context::join()
PUBLIC a930 0 boost::fibers::context::yield()
PUBLIC a960 0 boost::fibers::context::wake(unsigned long)
PUBLIC a9e0 0 boost::fibers::context::set_fss_data(void const*, boost::intrusive_ptr<boost::fibers::detail::fss_cleanup_function> const&, void*, bool)
PUBLIC ac00 0 boost::fibers::fiber_properties::~fiber_properties()
PUBLIC ac10 0 boost::fibers::dispatcher_context::run_(boost::context::fiber&&)
PUBLIC ac40 0 boost::context::detail::transfer_t boost::context::detail::fiber_exit<boost::context::detail::fiber_record<boost::context::fiber, boost::context::basic_fixedsize_stack<boost::context::stack_traits>&, std::_Bind<boost::context::fiber (boost::fibers::dispatcher_context::*(boost::fibers::dispatcher_context*, std::_Placeholder<1>))(boost::context::fiber&&)> > >(boost::context::detail::transfer_t)
PUBLIC ac70 0 void boost::context::detail::fiber_entry<boost::context::detail::fiber_record<boost::context::fiber, boost::context::basic_fixedsize_stack<boost::context::stack_traits>&, std::_Bind<boost::context::fiber (boost::fibers::dispatcher_context::*(boost::fibers::dispatcher_context*, std::_Placeholder<1>))(boost::context::fiber&&)> > >(boost::context::detail::transfer_t)
PUBLIC ad60 0 boost::fibers::context_initializer::~context_initializer()
PUBLIC add0 0 std::_Rb_tree<unsigned long, std::pair<unsigned long const, boost::fibers::context::fss_data>, std::_Select1st<std::pair<unsigned long const, boost::fibers::context::fss_data> >, std::less<unsigned long>, std::allocator<std::pair<unsigned long const, boost::fibers::context::fss_data> > >::_M_erase(std::_Rb_tree_node<std::pair<unsigned long const, boost::fibers::context::fss_data> >*)
PUBLIC ae60 0 boost::fibers::dispatcher_context::~dispatcher_context()
PUBLIC ae70 0 boost::fibers::dispatcher_context::~dispatcher_context()
PUBLIC aeb0 0 boost::fibers::main_context::~main_context()
PUBLIC aec0 0 boost::fibers::main_context::~main_context()
PUBLIC af00 0 boost::fibers::context_initializer::context_initializer()
PUBLIC b1c0 0 std::pair<std::_Rb_tree_iterator<std::pair<unsigned long const, boost::fibers::context::fss_data> >, bool> std::_Rb_tree<unsigned long, std::pair<unsigned long const, boost::fibers::context::fss_data>, std::_Select1st<std::pair<unsigned long const, boost::fibers::context::fss_data> >, std::less<unsigned long>, std::allocator<std::pair<unsigned long const, boost::fibers::context::fss_data> > >::_M_emplace_unique<std::pair<unsigned long, boost::fibers::context::fss_data> >(std::pair<unsigned long, boost::fibers::context::fss_data>&&)
PUBLIC b360 0 boost::fibers::fiber_properties::~fiber_properties()
PUBLIC b370 0 boost::fibers::fiber::start_()
PUBLIC b3e0 0 boost::fibers::fiber::join()
PUBLIC b520 0 boost::fibers::fiber::detach()
PUBLIC b610 0 boost::fibers::waker::wake() const
PUBLIC b620 0 boost::fibers::wait_queue::suspend_and_wait(std::unique_lock<boost::fibers::detail::spinlock_ttas>&, boost::fibers::context*)
PUBLIC b670 0 boost::fibers::wait_queue::notify_one()
PUBLIC b6e0 0 boost::fibers::wait_queue::notify_all()
PUBLIC b750 0 boost::fibers::wait_queue::empty() const
PUBLIC b770 0 boost::fibers::wait_queue::suspend_and_wait_until(std::unique_lock<boost::fibers::detail::spinlock_ttas>&, boost::fibers::context*, std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC b960 0 boost::fibers::future_category()
PUBLIC b9d0 0 boost::fibers::future_error_category::name() const
PUBLIC b9e0 0 boost::fibers::future_error_category::~future_error_category()
PUBLIC b9f0 0 boost::fibers::future_error_category::~future_error_category()
PUBLIC ba30 0 boost::fibers::future_error_category::message[abi:cxx11](int) const
PUBLIC bcb0 0 boost::fibers::future_error_category::default_error_condition(int) const
PUBLIC bd70 0 boost::fibers::future_error_category::equivalent(std::error_code const&, int) const
PUBLIC be10 0 boost::fibers::mutex::try_lock()
PUBLIC bf30 0 boost::fibers::mutex::lock()
PUBLIC c060 0 boost::fibers::mutex::unlock()
PUBLIC c160 0 boost::fibers::lock_error::~lock_error()
PUBLIC c170 0 boost::fibers::lock_error::~lock_error()
PUBLIC c1b0 0 boost::fibers::fiber_properties::notify()
PUBLIC c230 0 boost::fibers::recursive_mutex::unlock()
PUBLIC c340 0 boost::fibers::recursive_mutex::lock()
PUBLIC c420 0 boost::fibers::recursive_mutex::try_lock()
PUBLIC c4a0 0 boost::fibers::recursive_timed_mutex::unlock()
PUBLIC c5b0 0 boost::fibers::recursive_timed_mutex::lock()
PUBLIC c690 0 boost::fibers::recursive_timed_mutex::try_lock_until_(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC c7a0 0 boost::fibers::recursive_timed_mutex::try_lock()
PUBLIC c820 0 boost::fibers::timed_mutex::try_lock()
PUBLIC c940 0 boost::fibers::timed_mutex::try_lock_until_(std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC ca30 0 boost::fibers::timed_mutex::lock()
PUBLIC cb60 0 boost::fibers::timed_mutex::unlock()
PUBLIC cc60 0 boost::fibers::scheduler::~scheduler()
PUBLIC ce50 0 boost::fibers::scheduler::~scheduler()
PUBLIC ce80 0 boost::fibers::scheduler::release_terminated_()
PUBLIC cf60 0 boost::fibers::scheduler::sleep2ready_()
PUBLIC d550 0 boost::fibers::scheduler::scheduler()
PUBLIC d640 0 boost::fibers::scheduler::schedule(boost::fibers::context*)
PUBLIC d6b0 0 boost::fibers::scheduler::terminate(std::unique_lock<boost::fibers::detail::spinlock_ttas>&, boost::fibers::context*)
PUBLIC d750 0 boost::fibers::scheduler::yield(boost::fibers::context*)
PUBLIC d780 0 boost::fibers::scheduler::wait_until(boost::fibers::context*, std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)
PUBLIC da10 0 boost::fibers::scheduler::wait_until(boost::fibers::context*, std::chrono::time_point<std::chrono::_V2::steady_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&, std::unique_lock<boost::fibers::detail::spinlock_ttas>&, boost::fibers::waker&&)
PUBLIC dca0 0 boost::fibers::scheduler::suspend()
PUBLIC dcc0 0 boost::fibers::scheduler::suspend(std::unique_lock<boost::fibers::detail::spinlock_ttas>&)
PUBLIC dcf0 0 boost::fibers::scheduler::has_ready_fibers() const
PUBLIC dd10 0 boost::fibers::scheduler::set_algo(boost::intrusive_ptr<boost::fibers::algo::algorithm>)
PUBLIC ddd0 0 boost::fibers::scheduler::attach_main_context(boost::fibers::context*)
PUBLIC dde0 0 boost::fibers::scheduler::attach_dispatcher_context(boost::intrusive_ptr<boost::fibers::context>)
PUBLIC de10 0 boost::fibers::scheduler::attach_worker_context(boost::fibers::context*)
PUBLIC de30 0 boost::fibers::scheduler::detach_worker_context(boost::fibers::context*)
PUBLIC de60 0 boost::fibers::scheduler::remote_ready2ready_()
PUBLIC e030 0 boost::fibers::scheduler::dispatch()
PUBLIC e120 0 boost::fibers::scheduler::schedule_from_remote(boost::fibers::context*)
PUBLIC e190 0 _fini
STACK CFI INIT 70c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 712c 50 .cfa: sp 0 + .ra: x30
STACK CFI 713c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7144 x19: .cfa -16 + ^
STACK CFI 7174 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 717c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7180 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7190 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c00 3c .cfa: sp 0 + .ra: x30
STACK CFI 6c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c0c x19: .cfa -16 + ^
STACK CFI 6c34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 71a0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7430 58 .cfa: sp 0 + .ra: x30
STACK CFI 7434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7444 x19: .cfa -16 + ^
STACK CFI 7484 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7490 60 .cfa: sp 0 + .ra: x30
STACK CFI 7494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74a4 x19: .cfa -16 + ^
STACK CFI 74ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 71c0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7210 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7230 60 .cfa: sp 0 + .ra: x30
STACK CFI 7234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 723c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 724c x21: .cfa -16 + ^
STACK CFI 7288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 728c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7290 198 .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 729c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 72a8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 72b4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 72c0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 72f0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 736c x25: x25 x26: x26
STACK CFI 7394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7398 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 73a4 x25: x25 x26: x26
STACK CFI 741c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7420 x25: x25 x26: x26
STACK CFI 7424 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 6c40 3c .cfa: sp 0 + .ra: x30
STACK CFI 6c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c4c x19: .cfa -16 + ^
STACK CFI 6c74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78f0 60 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7910 x21: .cfa -16 + ^
STACK CFI 793c x21: x21
STACK CFI 7940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7944 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 794c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7950 58 .cfa: sp 0 + .ra: x30
STACK CFI 7954 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7964 x19: .cfa -16 + ^
STACK CFI 79a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 79b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79c4 x19: .cfa -16 + ^
STACK CFI 7a0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7a10 90 .cfa: sp 0 + .ra: x30
STACK CFI 7a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a2c x21: .cfa -16 + ^
STACK CFI 7a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 74f0 74 .cfa: sp 0 + .ra: x30
STACK CFI 74f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7514 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 751c x21: .cfa -16 + ^
STACK CFI 7558 x21: x21
STACK CFI 755c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7560 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7570 10c .cfa: sp 0 + .ra: x30
STACK CFI 7574 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 757c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7588 x21: .cfa -16 + ^
STACK CFI 7604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7608 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7680 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 7684 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 768c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 769c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 76ac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 76b4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 76e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7764 x25: x25 x26: x26
STACK CFI 777c x21: x21 x22: x22
STACK CFI 7780 x23: x23 x24: x24
STACK CFI 7784 x27: x27 x28: x28
STACK CFI 778c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7790 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 779c x25: x25 x26: x26
STACK CFI 77f8 x21: x21 x22: x22
STACK CFI 77fc x23: x23 x24: x24
STACK CFI 7800 x27: x27 x28: x28
STACK CFI 7804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7808 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7824 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7828 x25: x25 x26: x26
STACK CFI 782c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 7aa0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 7aa4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7ab0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7ab8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7ac4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7ad0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7b68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7830 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7868 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 786c x19: .cfa -32 + ^
STACK CFI 78d0 x19: x19
STACK CFI 78d4 x19: .cfa -32 + ^
STACK CFI 78d8 x19: x19
STACK CFI 78dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 78e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6c80 100 .cfa: sp 0 + .ra: x30
STACK CFI 6c84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6c8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ca4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 88d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 88d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 88f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8920 44 .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8934 x19: .cfa -16 + ^
STACK CFI 8954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8970 44 .cfa: sp 0 + .ra: x30
STACK CFI 8974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8984 x19: .cfa -16 + ^
STACK CFI 89b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89c0 8c .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 89cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 89dc x21: .cfa -16 + ^
STACK CFI 8a18 x21: x21
STACK CFI 8a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8a40 x21: x21
STACK CFI 8a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7c60 74 .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7c8c x21: .cfa -16 + ^
STACK CFI 7cc8 x21: x21
STACK CFI 7ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ce0 124 .cfa: sp 0 + .ra: x30
STACK CFI 7ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7cec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7cf4 x21: .cfa -16 + ^
STACK CFI 7dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7dc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7dec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e10 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7e1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7e2c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7e3c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7e44 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7e78 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7ef4 x25: x25 x26: x26
STACK CFI 7f0c x21: x21 x22: x22
STACK CFI 7f10 x23: x23 x24: x24
STACK CFI 7f14 x27: x27 x28: x28
STACK CFI 7f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f20 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7f2c x25: x25 x26: x26
STACK CFI 7f88 x21: x21 x22: x22
STACK CFI 7f8c x23: x23 x24: x24
STACK CFI 7f90 x27: x27 x28: x28
STACK CFI 7f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f98 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7fb4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7fb8 x25: x25 x26: x26
STACK CFI 7fbc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 7fc0 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 7fc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7fd0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7ff4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 81a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 81a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 8220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8224 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8a50 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 8a54 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8a5c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8a68 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8a70 x27: .cfa -32 + ^
STACK CFI 8af4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8af8 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 8b10 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8b24 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8b98 x19: x19 x20: x20
STACK CFI 8ba0 x21: x21 x22: x22
STACK CFI 8bb0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8bb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 8c04 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 8c10 1ec .cfa: sp 0 + .ra: x30
STACK CFI 8c18 .cfa: sp 5120 +
STACK CFI 8c1c .ra: .cfa -5112 + ^ x29: .cfa -5120 + ^
STACK CFI 8c24 x23: .cfa -5072 + ^ x24: .cfa -5064 + ^
STACK CFI 8c2c x19: .cfa -5104 + ^ x20: .cfa -5096 + ^
STACK CFI 8c4c x21: .cfa -5088 + ^ x22: .cfa -5080 + ^ x25: .cfa -5056 + ^
STACK CFI 8d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8d24 .cfa: sp 5120 + .ra: .cfa -5112 + ^ x19: .cfa -5104 + ^ x20: .cfa -5096 + ^ x21: .cfa -5088 + ^ x22: .cfa -5080 + ^ x23: .cfa -5072 + ^ x24: .cfa -5064 + ^ x25: .cfa -5056 + ^ x29: .cfa -5120 + ^
STACK CFI INIT 82a0 164 .cfa: sp 0 + .ra: x30
STACK CFI 82a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 82ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 82c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 830c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8310 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 8320 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 832c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8334 x27: .cfa -16 + ^
STACK CFI 8380 x25: x25 x26: x26
STACK CFI 8388 x23: x23 x24: x24
STACK CFI 838c x27: x27
STACK CFI 8390 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 83c4 x23: x23 x24: x24
STACK CFI 83c8 x25: x25 x26: x26
STACK CFI 83d0 x27: x27
STACK CFI 83f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 83f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8e00 3c .cfa: sp 0 + .ra: x30
STACK CFI 8e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e40 7c .cfa: sp 0 + .ra: x30
STACK CFI 8e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8ec0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 8ec4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8ecc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8ed4 x27: .cfa -32 + ^
STACK CFI 8ee0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8f60 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8f64 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 8f7c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8f98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9004 x19: x19 x20: x20
STACK CFI 900c x21: x21 x22: x22
STACK CFI 901c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9020 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 906c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 8410 3fc .cfa: sp 0 + .ra: x30
STACK CFI 8418 .cfa: sp 5168 +
STACK CFI 841c .ra: .cfa -5160 + ^ x29: .cfa -5168 + ^
STACK CFI 8424 x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI 8440 x19: .cfa -5152 + ^ x20: .cfa -5144 + ^
STACK CFI 845c x21: .cfa -5136 + ^ x22: .cfa -5128 + ^ x23: .cfa -5120 + ^ x24: .cfa -5112 + ^ x25: .cfa -5104 + ^ x26: .cfa -5096 + ^
STACK CFI 8650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8654 .cfa: sp 5168 + .ra: .cfa -5160 + ^ x19: .cfa -5152 + ^ x20: .cfa -5144 + ^ x21: .cfa -5136 + ^ x22: .cfa -5128 + ^ x23: .cfa -5120 + ^ x24: .cfa -5112 + ^ x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^ x29: .cfa -5168 + ^
STACK CFI INIT 8810 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8814 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 881c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8844 x21: .cfa -16 + ^
STACK CFI 8888 x21: x21
STACK CFI 888c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 88a8 x21: x21
STACK CFI 88ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 88b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 88c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 88c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 88cc x21: x21
STACK CFI INIT 6d80 64 .cfa: sp 0 + .ra: x30
STACK CFI 6d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9320 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9330 34 .cfa: sp 0 + .ra: x30
STACK CFI 9334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9344 x19: .cfa -16 + ^
STACK CFI 9360 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9370 1cc .cfa: sp 0 + .ra: x30
STACK CFI 9374 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 937c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9388 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 939c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 94c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 94c8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9070 100 .cfa: sp 0 + .ra: x30
STACK CFI 9074 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9080 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 908c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 90c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 90cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 90d4 x23: .cfa -16 + ^
STACK CFI INIT 9170 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 9174 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 917c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9184 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9190 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 91ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9264 x25: x25 x26: x26
STACK CFI 9268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 926c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 929c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 92a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 92b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 92e0 x25: x25 x26: x26
STACK CFI 92e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 6df0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6dfc x19: .cfa -16 + ^
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9540 30 .cfa: sp 0 + .ra: x30
STACK CFI 9544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 954c x19: .cfa -16 + ^
STACK CFI 956c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9570 30 .cfa: sp 0 + .ra: x30
STACK CFI 9574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 957c x19: .cfa -16 + ^
STACK CFI 959c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e30 3c .cfa: sp 0 + .ra: x30
STACK CFI 6e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e3c x19: .cfa -16 + ^
STACK CFI 6e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b20 2c .cfa: sp 0 + .ra: x30
STACK CFI 6b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b2c x19: .cfa -16 + ^
STACK CFI INIT ac10 28 .cfa: sp 0 + .ra: x30
STACK CFI ac14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac20 x19: .cfa -16 + ^
STACK CFI ac34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac40 24 .cfa: sp 0 + .ra: x30
STACK CFI ac44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ac60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ac70 e8 .cfa: sp 0 + .ra: x30
STACK CFI ac74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ac7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI acd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI acdc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI ad1c x21: .cfa -48 + ^
STACK CFI ad4c x21: x21
STACK CFI ad54 x21: .cfa -48 + ^
STACK CFI INIT ad60 70 .cfa: sp 0 + .ra: x30
STACK CFI ad68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad94 x19: .cfa -16 + ^
STACK CFI adb8 x19: x19
STACK CFI adbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI adc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI adcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 95c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 9610 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9634 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9640 70 .cfa: sp 0 + .ra: x30
STACK CFI 9644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9650 x19: .cfa -16 + ^
STACK CFI 968c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9690 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 96b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 96b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 96d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 96e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96f0 6c .cfa: sp 0 + .ra: x30
STACK CFI 96f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9740 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9758 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9760 70 .cfa: sp 0 + .ra: x30
STACK CFI 9768 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 97b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 97b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 97cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 97d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 97d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9824 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9828 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 983c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9860 68 .cfa: sp 0 + .ra: x30
STACK CFI 9868 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9894 x19: .cfa -32 + ^
STACK CFI 98c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 98d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 98e0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9900 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9920 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9970 60 .cfa: sp 0 + .ra: x30
STACK CFI 9974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 997c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 99b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 99bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 99cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 99d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a60 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a80 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9aa0 514 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9fc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT add0 88 .cfa: sp 0 + .ra: x30
STACK CFI add8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ade0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ade8 x21: .cfa -16 + ^
STACK CFI ae3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ae40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ae54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9fe0 13c .cfa: sp 0 + .ra: x30
STACK CFI 9fe4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9fec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ff8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a000 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a0dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a0e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT a120 644 .cfa: sp 0 + .ra: x30
STACK CFI a124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a134 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a140 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a484 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a770 28 .cfa: sp 0 + .ra: x30
STACK CFI a774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a77c x19: .cfa -16 + ^
STACK CFI a794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae70 34 .cfa: sp 0 + .ra: x30
STACK CFI ae74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae84 x19: .cfa -16 + ^
STACK CFI aea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aeb0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT aec0 34 .cfa: sp 0 + .ra: x30
STACK CFI aec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aed4 x19: .cfa -16 + ^
STACK CFI aef0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af00 2c0 .cfa: sp 0 + .ra: x30
STACK CFI af04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI af1c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI af38 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI af3c .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI af44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI af48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI af4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b110 x23: x23 x24: x24
STACK CFI b114 x25: x25 x26: x26
STACK CFI b11c x19: x19 x20: x20
STACK CFI b124 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b128 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT a7a0 88 .cfa: sp 0 + .ra: x30
STACK CFI a7a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a7bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a7e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a7f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a7f4 x21: .cfa -16 + ^
STACK CFI a824 x21: x21
STACK CFI INIT a830 58 .cfa: sp 0 + .ra: x30
STACK CFI a834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a840 x19: .cfa -16 + ^
STACK CFI a86c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a870 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a890 98 .cfa: sp 0 + .ra: x30
STACK CFI a894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a89c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a8e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a8e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a90c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a930 28 .cfa: sp 0 + .ra: x30
STACK CFI a934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a93c x19: .cfa -16 + ^
STACK CFI a954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a960 7c .cfa: sp 0 + .ra: x30
STACK CFI a964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a970 x19: .cfa -16 + ^
STACK CFI a9bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a9d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b1c0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI b1c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b1cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b1e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b1e8 x25: .cfa -16 + ^
STACK CFI b2a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI b2a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b30c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI b310 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT a9e0 220 .cfa: sp 0 + .ra: x30
STACK CFI a9e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a9f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a9fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI aa0c x23: .cfa -48 + ^
STACK CFI aa48 x23: x23
STACK CFI aab0 x23: .cfa -48 + ^
STACK CFI ab00 x23: x23
STACK CFI ab0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab10 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI ab74 x23: x23
STACK CFI ab88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI aba0 x23: x23
STACK CFI aba4 x23: .cfa -48 + ^
STACK CFI abb8 x23: x23
STACK CFI abc0 x23: .cfa -48 + ^
STACK CFI INIT b360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e70 3c .cfa: sp 0 + .ra: x30
STACK CFI 6e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e7c x19: .cfa -16 + ^
STACK CFI 6ea4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b370 68 .cfa: sp 0 + .ra: x30
STACK CFI b374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b37c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b3c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b3e0 140 .cfa: sp 0 + .ra: x30
STACK CFI b3e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b3ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b450 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b520 f0 .cfa: sp 0 + .ra: x30
STACK CFI b524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b554 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b558 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b55c x19: .cfa -16 + ^
STACK CFI b58c x19: x19
STACK CFI b590 x19: .cfa -16 + ^
STACK CFI b5a4 x19: x19
STACK CFI b5b0 x19: .cfa -16 + ^
STACK CFI INIT 6eb0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ebc x19: .cfa -16 + ^
STACK CFI 6ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b620 50 .cfa: sp 0 + .ra: x30
STACK CFI b62c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b66c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b670 6c .cfa: sp 0 + .ra: x30
STACK CFI b674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b67c x19: .cfa -16 + ^
STACK CFI b6c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b6cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b6e0 68 .cfa: sp 0 + .ra: x30
STACK CFI b6e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b6f0 x19: .cfa -16 + ^
STACK CFI b708 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b70c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b750 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b770 1e8 .cfa: sp 0 + .ra: x30
STACK CFI b774 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b780 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b78c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b798 x23: .cfa -64 + ^
STACK CFI b800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b804 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI b858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b85c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6ef0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6efc x19: .cfa -16 + ^
STACK CFI 6f24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b9d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b9e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b9f0 34 .cfa: sp 0 + .ra: x30
STACK CFI b9f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba04 x19: .cfa -16 + ^
STACK CFI ba20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba30 280 .cfa: sp 0 + .ra: x30
STACK CFI ba34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ba44 x19: .cfa -32 + ^
STACK CFI bad4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bad8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI bb54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bb58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI bbc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bbc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI bc2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bc30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI bcac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b960 70 .cfa: sp 0 + .ra: x30
STACK CFI b964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b96c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b98c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b990 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b9cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bcb0 b4 .cfa: sp 0 + .ra: x30
STACK CFI bcb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bcf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bcf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bd18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bd1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bd34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bd38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bd50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bd54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bd60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bd70 9c .cfa: sp 0 + .ra: x30
STACK CFI bd74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bd98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bd9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bde8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bdec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c160 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c170 34 .cfa: sp 0 + .ra: x30
STACK CFI c174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c184 x19: .cfa -16 + ^
STACK CFI c1a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b4c 2c .cfa: sp 0 + .ra: x30
STACK CFI 6b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be10 11c .cfa: sp 0 + .ra: x30
STACK CFI be14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI be1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI be88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI bea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bea8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT bf30 124 .cfa: sp 0 + .ra: x30
STACK CFI bf34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bf3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bf44 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bf50 x23: .cfa -32 + ^
STACK CFI bfd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI bfd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT c060 f4 .cfa: sp 0 + .ra: x30
STACK CFI c064 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c06c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c0b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c0bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c0d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c0d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6f30 3c .cfa: sp 0 + .ra: x30
STACK CFI 6f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f3c x19: .cfa -16 + ^
STACK CFI 6f64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c1b0 74 .cfa: sp 0 + .ra: x30
STACK CFI c1b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1bc x19: .cfa -16 + ^
STACK CFI c204 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c210 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c21c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6f70 3c .cfa: sp 0 + .ra: x30
STACK CFI 6f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f7c x19: .cfa -16 + ^
STACK CFI 6fa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b78 2c .cfa: sp 0 + .ra: x30
STACK CFI 6b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c230 104 .cfa: sp 0 + .ra: x30
STACK CFI c234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c23c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c298 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c2b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c2b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT c340 dc .cfa: sp 0 + .ra: x30
STACK CFI c344 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c34c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c354 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c360 x23: .cfa -32 + ^
STACK CFI c3ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c3f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT c420 74 .cfa: sp 0 + .ra: x30
STACK CFI c424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c42c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c478 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6fb0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fbc x19: .cfa -16 + ^
STACK CFI 6fe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ba4 2c .cfa: sp 0 + .ra: x30
STACK CFI 6bb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6bbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c4a0 104 .cfa: sp 0 + .ra: x30
STACK CFI c4a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c4ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c508 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c528 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT c5b0 dc .cfa: sp 0 + .ra: x30
STACK CFI c5b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c5bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c5c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c5d0 x23: .cfa -32 + ^
STACK CFI c65c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c660 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT c690 10c .cfa: sp 0 + .ra: x30
STACK CFI c694 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c69c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c6a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c6b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c74c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c750 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI c78c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c790 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT c7a0 74 .cfa: sp 0 + .ra: x30
STACK CFI c7a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c7ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c7f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c7f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6ff0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ffc x19: .cfa -16 + ^
STACK CFI 7024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6bd0 2c .cfa: sp 0 + .ra: x30
STACK CFI 6bdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6be8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c820 11c .cfa: sp 0 + .ra: x30
STACK CFI c824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c82c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c89c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c8b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c8b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT c940 f0 .cfa: sp 0 + .ra: x30
STACK CFI c944 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c94c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c954 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c960 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c9f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c9f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI ca2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT ca30 124 .cfa: sp 0 + .ra: x30
STACK CFI ca34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ca3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ca44 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ca50 x23: .cfa -32 + ^
STACK CFI cad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cad8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT cb60 f4 .cfa: sp 0 + .ra: x30
STACK CFI cb64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cb6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cbbc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cbd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cbd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7030 3c .cfa: sp 0 + .ra: x30
STACK CFI 7034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 703c x19: .cfa -16 + ^
STACK CFI 7064 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cc60 1f0 .cfa: sp 0 + .ra: x30
STACK CFI cc64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cc74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cdb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cdbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ce50 28 .cfa: sp 0 + .ra: x30
STACK CFI ce54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ce5c x19: .cfa -16 + ^
STACK CFI ce74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ce80 e0 .cfa: sp 0 + .ra: x30
STACK CFI ce84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ce8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ce98 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cf3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cf40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT cf60 5e8 .cfa: sp 0 + .ra: x30
STACK CFI cf64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cf6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cf8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cf98 x23: .cfa -16 + ^
STACK CFI d070 x21: x21 x22: x22
STACK CFI d074 x23: x23
STACK CFI d07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d080 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT d550 e4 .cfa: sp 0 + .ra: x30
STACK CFI d554 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d564 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d574 x21: .cfa -16 + ^
STACK CFI d630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT d640 6c .cfa: sp 0 + .ra: x30
STACK CFI d644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d64c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d684 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d6b0 98 .cfa: sp 0 + .ra: x30
STACK CFI d6b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d6bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d6dc x21: .cfa -16 + ^
STACK CFI d73c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d740 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT d750 30 .cfa: sp 0 + .ra: x30
STACK CFI d754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d760 x19: .cfa -16 + ^
STACK CFI d77c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d780 284 .cfa: sp 0 + .ra: x30
STACK CFI d784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d790 x19: .cfa -16 + ^
STACK CFI d8ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d8b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT da10 284 .cfa: sp 0 + .ra: x30
STACK CFI da14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI db38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT dca0 20 .cfa: sp 0 + .ra: x30
STACK CFI dca4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dcbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT dcc0 30 .cfa: sp 0 + .ra: x30
STACK CFI dcc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dcd0 x19: .cfa -16 + ^
STACK CFI dcec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dcf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd10 bc .cfa: sp 0 + .ra: x30
STACK CFI dd14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dd1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dd24 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI dda8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ddac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ddc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ddd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dde0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT de10 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT de30 28 .cfa: sp 0 + .ra: x30
STACK CFI de34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI de40 x19: .cfa -16 + ^
STACK CFI de54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT de60 1c8 .cfa: sp 0 + .ra: x30
STACK CFI de64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI de6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI de78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI df34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI df38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI df74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI df78 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT e030 e8 .cfa: sp 0 + .ra: x30
STACK CFI e034 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e03c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e048 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e054 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e0ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e0b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT e120 70 .cfa: sp 0 + .ra: x30
STACK CFI e124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e12c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e138 x21: .cfa -16 + ^
STACK CFI e184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7070 3c .cfa: sp 0 + .ra: x30
STACK CFI 7074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 707c x19: .cfa -16 + ^
STACK CFI 70a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
