MODULE Linux arm64 C0C7039C80D6DDE8E2B2086CE420207E0 libpolkit-agent-1.so.0
INFO CODE_ID 9C03C7C0D680E8DDE2B2086CE420207EDFD3FAD9
PUBLIC 3ee8 0 polkit_agent_register_flags_get_type
PUBLIC 4260 0 polkit_agent_session_get_type
PUBLIC 4a70 0 polkit_agent_session_new
PUBLIC 4b48 0 polkit_agent_session_response
PUBLIC 4c68 0 polkit_agent_session_initiate
PUBLIC 4f28 0 polkit_agent_session_cancel
PUBLIC 5450 0 polkit_agent_listener_unregister
PUBLIC 5488 0 polkit_agent_listener_get_type
PUBLIC 54f8 0 polkit_agent_listener_register_with_options
PUBLIC 5838 0 polkit_agent_listener_register
PUBLIC 5848 0 polkit_agent_register_listener
PUBLIC 58b8 0 polkit_agent_listener_initiate_authentication
PUBLIC 5d80 0 polkit_agent_listener_initiate_authentication_finish
PUBLIC 6308 0 polkit_agent_text_listener_get_type
PUBLIC 6d20 0 polkit_agent_text_listener_new
STACK CFI INIT 3e28 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e58 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e98 48 .cfa: sp 0 + .ra: x30
STACK CFI 3e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ea4 x19: .cfa -16 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ee0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ee8 74 .cfa: sp 0 + .ra: x30
STACK CFI 3eec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ef4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f60 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 400c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4010 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4020 40 .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 403c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4060 7c .cfa: sp 0 + .ra: x30
STACK CFI 4064 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 406c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4098 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 40d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 40e0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 40e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4198 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 41d8 84 .cfa: sp 0 + .ra: x30
STACK CFI 41dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 420c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4260 6c .cfa: sp 0 + .ra: x30
STACK CFI 4264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 426c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 429c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 42c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42d0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 42d4 .cfa: sp 96 +
STACK CFI 42d8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42ec x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 449c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 44a0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 44b0 fc .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 80 +
STACK CFI 44b8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 44cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 44d4 x23: .cfa -16 + ^
STACK CFI 4560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4564 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4588 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 45a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 45b0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 45b4 .cfa: sp 64 +
STACK CFI 45b8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4658 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4674 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 468c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4690 94 .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 469c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 470c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4718 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4728 348 .cfa: sp 0 + .ra: x30
STACK CFI 472c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4734 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4744 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 477c x23: .cfa -48 + ^
STACK CFI 47d4 x23: x23
STACK CFI 4814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4818 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 48b4 x23: x23
STACK CFI 4900 x23: .cfa -48 + ^
STACK CFI 494c x23: x23
STACK CFI 4950 x23: .cfa -48 + ^
STACK CFI 49ac x23: x23
STACK CFI 49b0 x23: .cfa -48 + ^
STACK CFI 49fc x23: x23
STACK CFI 4a00 x23: .cfa -48 + ^
STACK CFI 4a24 x23: x23
STACK CFI 4a28 x23: .cfa -48 + ^
STACK CFI 4a64 x23: x23
STACK CFI 4a6c x23: .cfa -48 + ^
STACK CFI INIT 4a70 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ae4 x19: x19 x20: x20
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4aec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4af0 x19: x19 x20: x20
STACK CFI 4b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b3c x19: x19 x20: x20
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b48 11c .cfa: sp 0 + .ra: x30
STACK CFI 4b4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b7c x21: .cfa -32 + ^
STACK CFI 4bf0 x21: x21
STACK CFI 4bf4 x21: .cfa -32 + ^
STACK CFI 4bf8 x21: x21
STACK CFI 4c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4c58 x21: x21
STACK CFI 4c60 x21: .cfa -32 + ^
STACK CFI INIT 4c68 2bc .cfa: sp 0 + .ra: x30
STACK CFI 4c6c .cfa: sp 128 +
STACK CFI 4c74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4c7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4ca0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4e38 x21: x21 x22: x22
STACK CFI 4e40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4e44 x21: x21 x22: x22
STACK CFI 4e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e88 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4ed8 x21: x21 x22: x22
STACK CFI 4edc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4f1c x21: x21 x22: x22
STACK CFI 4f20 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 4f28 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f38 x19: .cfa -16 + ^
STACK CFI 4f88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4fd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fd8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fe4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4ff4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5078 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 50b0 12c .cfa: sp 0 + .ra: x30
STACK CFI 50b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 50bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 50dc x21: .cfa -32 + ^
STACK CFI 511c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5120 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 51e0 13c .cfa: sp 0 + .ra: x30
STACK CFI 51e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 52b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 52bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5328 40 .cfa: sp 0 + .ra: x30
STACK CFI 532c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5368 40 .cfa: sp 0 + .ra: x30
STACK CFI 536c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5374 x19: .cfa -16 + ^
STACK CFI 5394 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5398 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 53a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53a8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 53ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53b4 x19: .cfa -16 + ^
STACK CFI 5420 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5424 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5448 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5450 38 .cfa: sp 0 + .ra: x30
STACK CFI 5454 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 545c x19: .cfa -16 + ^
STACK CFI 5484 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5488 6c .cfa: sp 0 + .ra: x30
STACK CFI 548c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5494 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 54f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54f8 33c .cfa: sp 0 + .ra: x30
STACK CFI 54fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5504 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 550c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 551c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5528 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 55a8 x21: x21 x22: x22
STACK CFI 55ac x23: x23 x24: x24
STACK CFI 55b0 x25: x25 x26: x26
STACK CFI 55d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 55dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5604 x21: x21 x22: x22
STACK CFI 5608 x23: x23 x24: x24
STACK CFI 560c x25: x25 x26: x26
STACK CFI 5610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5614 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 57a0 x21: x21 x22: x22
STACK CFI 57a4 x23: x23 x24: x24
STACK CFI 57a8 x25: x25 x26: x26
STACK CFI 57ac x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 57d0 x21: x21 x22: x22
STACK CFI 57d4 x23: x23 x24: x24
STACK CFI 57d8 x25: x25 x26: x26
STACK CFI 57dc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5818 x21: x21 x22: x22
STACK CFI 581c x23: x23 x24: x24
STACK CFI 5820 x25: x25 x26: x26
STACK CFI 5824 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 5838 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5848 70 .cfa: sp 0 + .ra: x30
STACK CFI 584c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5874 x19: .cfa -16 + ^
STACK CFI 58a4 x19: x19
STACK CFI 58a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 58ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 58b8 218 .cfa: sp 0 + .ra: x30
STACK CFI 58bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 58c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 58cc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 58e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 58ec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 58f8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5974 x21: x21 x22: x22
STACK CFI 5978 x23: x23 x24: x24
STACK CFI 597c x25: x25 x26: x26
STACK CFI 5984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 5988 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5a10 x21: x21 x22: x22
STACK CFI 5a14 x23: x23 x24: x24
STACK CFI 5a18 x25: x25 x26: x26
STACK CFI 5a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 5a2c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5a30 x21: x21 x22: x22
STACK CFI 5a34 x23: x23 x24: x24
STACK CFI 5a38 x25: x25 x26: x26
STACK CFI 5a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 5a60 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5ad0 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 5ad4 .cfa: sp 304 +
STACK CFI 5adc .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5ae4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5af4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5b14 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 5b48 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5b50 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5c74 x25: x25 x26: x26
STACK CFI 5c78 x27: x27 x28: x28
STACK CFI 5cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5cf8 .cfa: sp 304 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 5d24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5d6c x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5d70 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5d74 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5d78 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5d7c x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 5d80 12c .cfa: sp 0 + .ra: x30
STACK CFI 5d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e24 x19: x19 x20: x20
STACK CFI 5e28 x21: x21 x22: x22
STACK CFI 5e2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5e34 x19: x19 x20: x20
STACK CFI 5e38 x21: x21 x22: x22
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5e80 x19: x19 x20: x20
STACK CFI 5e84 x21: x21 x22: x22
STACK CFI 5e88 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e98 x19: x19 x20: x20
STACK CFI 5ea0 x21: x21 x22: x22
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5eb0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ebc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ecc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5f80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f98 ac .cfa: sp 0 + .ra: x30
STACK CFI 5f9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5fa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 603c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6040 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6048 80 .cfa: sp 0 + .ra: x30
STACK CFI 604c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6054 x19: .cfa -16 + ^
STACK CFI 60b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 60b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 60c8 90 .cfa: sp 0 + .ra: x30
STACK CFI 60cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60dc x21: .cfa -16 + ^
STACK CFI 6154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6158 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 615c .cfa: sp 2160 +
STACK CFI 6160 .ra: .cfa -2152 + ^ x29: .cfa -2160 + ^
STACK CFI 6168 x19: .cfa -2144 + ^ x20: .cfa -2136 + ^
STACK CFI 6188 x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI 61b4 x21: x21 x22: x22
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 61fc .cfa: sp 2160 + .ra: .cfa -2152 + ^ x19: .cfa -2144 + ^ x20: .cfa -2136 + ^ x21: .cfa -2128 + ^ x22: .cfa -2120 + ^ x29: .cfa -2160 + ^
STACK CFI 6278 x21: x21 x22: x22
STACK CFI 627c x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI 62a8 x21: x21 x22: x22
STACK CFI 62ac x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI 62fc x21: x21 x22: x22
STACK CFI 6304 x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI INIT 6308 6c .cfa: sp 0 + .ra: x30
STACK CFI 630c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6314 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6344 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6378 e8 .cfa: sp 0 + .ra: x30
STACK CFI 637c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6384 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 638c x21: .cfa -16 + ^
STACK CFI 63dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 63e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6424 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6460 498 .cfa: sp 0 + .ra: x30
STACK CFI 6464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 646c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 647c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6490 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 649c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 64a4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6548 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 68f8 54 .cfa: sp 0 + .ra: x30
STACK CFI 68fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6904 x19: .cfa -16 + ^
STACK CFI 6948 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6950 50 .cfa: sp 0 + .ra: x30
STACK CFI 6954 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 695c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 699c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 69a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 69ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69f0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 69f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 69fc x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 6a08 x23: .cfa -160 + ^
STACK CFI 6a10 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 6b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6b88 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI INIT 6bc8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 6bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6bd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6c98 88 .cfa: sp 0 + .ra: x30
STACK CFI 6c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6d20 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
