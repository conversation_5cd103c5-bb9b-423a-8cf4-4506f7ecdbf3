MODULE Linux arm64 2C5C5491DAB22007998EDB50E876A2D00 libwrap.so.0
INFO CODE_ID 91545C2CB2DA0720998EDB50E876A2D040103F95
PUBLIC 3c48 0 hosts_access
PUBLIC 48f8 0 process_options
PUBLIC 4c20 0 shell_cmd
PUBLIC 4e70 0 rfc931
PUBLIC 5180 0 eval_user
PUBLIC 51e8 0 eval_hostaddr
PUBLIC 5250 0 eval_hostname
PUBLIC 52b0 0 eval_hostinfo
PUBLIC 5320 0 eval_port
PUBLIC 5388 0 eval_client
PUBLIC 5410 0 eval_server
PUBLIC 5490 0 hosts_ctl
PUBLIC 5510 0 refuse
PUBLIC 5570 0 percent_x
PUBLIC 57e0 0 clean_exit
PUBLIC 5808 0 fix_options
PUBLIC 5aa8 0 sock_hostaddr
PUBLIC 5ae8 0 sock_hostname
PUBLIC 5e80 0 sock_host
PUBLIC 6210 0 request_init
PUBLIC 6318 0 request_set
PUBLIC 63c0 0 xgets
PUBLIC 64a8 0 split_at
PUBLIC 6510 0 dot_quad_addr
PUBLIC 6588 0 cidr_mask_addr
PUBLIC 66a8 0 tcpd_warn
PUBLIC 6760 0 tcpd_jump
PUBLIC 6800 0 percent_m
STACK CFI INIT 2cd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d48 48 .cfa: sp 0 + .ra: x30
STACK CFI 2d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d54 x19: .cfa -16 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d98 8c .cfa: sp 0 + .ra: x30
STACK CFI 2d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2da4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2db0 x21: .cfa -16 + ^
STACK CFI 2e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2e28 13c .cfa: sp 0 + .ra: x30
STACK CFI 2e2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e38 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e8c x23: .cfa -16 + ^
STACK CFI 2ed4 x23: x23
STACK CFI 2ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2edc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ef0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2f54 x23: x23
STACK CFI 2f58 x23: .cfa -16 + ^
STACK CFI 2f60 x23: x23
STACK CFI INIT 2f68 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 2f6c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2f78 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f88 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3004 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 3008 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3084 x23: x23 x24: x24
STACK CFI 30a4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 30e8 x23: x23 x24: x24
STACK CFI 30ec x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3104 x23: x23 x24: x24
STACK CFI 3108 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3110 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3118 x27: .cfa -144 + ^
STACK CFI 3160 x23: x23 x24: x24
STACK CFI 3164 x25: x25 x26: x26
STACK CFI 3168 x27: x27
STACK CFI 316c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^
STACK CFI 31b4 x23: x23 x24: x24
STACK CFI 31b8 x25: x25 x26: x26
STACK CFI 31bc x27: x27
STACK CFI 31c0 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^
STACK CFI 320c x23: x23 x24: x24
STACK CFI 3210 x25: x25 x26: x26
STACK CFI 3214 x27: x27
STACK CFI 3218 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^
STACK CFI 322c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3230 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3234 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3238 x27: .cfa -144 + ^
STACK CFI INIT 3240 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3244 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 324c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3278 x21: .cfa -32 + ^
STACK CFI 32e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 32e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 32f0 3f8 .cfa: sp 0 + .ra: x30
STACK CFI 32f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 32fc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 330c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 3360 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3394 x23: x23 x24: x24
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 33c0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 3414 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3460 x23: x23 x24: x24
STACK CFI 348c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 34a0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 34d4 x23: x23 x24: x24
STACK CFI 34d8 x25: x25 x26: x26
STACK CFI 3514 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3548 x23: x23 x24: x24
STACK CFI 3560 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 35d8 x23: x23 x24: x24
STACK CFI 35dc x25: x25 x26: x26
STACK CFI 35e0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3600 x23: x23 x24: x24
STACK CFI 3604 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 36d4 x23: x23 x24: x24
STACK CFI 36d8 x25: x25 x26: x26
STACK CFI 36e0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 36e4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 36e8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 36f0 .cfa: sp 8272 +
STACK CFI 36f4 .ra: .cfa -8264 + ^ x29: .cfa -8272 + ^
STACK CFI 36fc x23: .cfa -8224 + ^ x24: .cfa -8216 + ^
STACK CFI 3708 x19: .cfa -8256 + ^ x20: .cfa -8248 + ^
STACK CFI 371c x21: .cfa -8240 + ^ x22: .cfa -8232 + ^
STACK CFI 37ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 37b0 .cfa: sp 8272 + .ra: .cfa -8264 + ^ x19: .cfa -8256 + ^ x20: .cfa -8248 + ^ x21: .cfa -8240 + ^ x22: .cfa -8232 + ^ x23: .cfa -8224 + ^ x24: .cfa -8216 + ^ x29: .cfa -8272 + ^
STACK CFI INIT 37e0 74 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 381c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3844 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3858 88 .cfa: sp 0 + .ra: x30
STACK CFI 385c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3864 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3870 x21: .cfa -16 + ^
STACK CFI 38a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 38dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 38e0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 38ec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 38f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3910 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 395c x19: x19 x20: x20
STACK CFI 396c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3970 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 399c x19: x19 x20: x20
STACK CFI 39a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 39ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 39c8 x19: x19 x20: x20
STACK CFI 39d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 39d8 270 .cfa: sp 0 + .ra: x30
STACK CFI 39dc .cfa: sp 2176 +
STACK CFI 39e0 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI 39e8 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI 39f0 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI 39f8 x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI 3a20 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI 3a48 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI 3b24 x19: x19 x20: x20
STACK CFI 3b64 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3b68 .cfa: sp 2176 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x28: .cfa -2088 + ^ x29: .cfa -2176 + ^
STACK CFI 3b98 x19: x19 x20: x20
STACK CFI 3bcc x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI 3bf4 x19: x19 x20: x20
STACK CFI 3bf8 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI 3c3c x19: x19 x20: x20
STACK CFI 3c44 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI INIT 3c48 94 .cfa: sp 0 + .ra: x30
STACK CFI 3c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ce0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3cf0 x21: .cfa -16 + ^
STACK CFI 3d0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d80 x19: x19 x20: x20
STACK CFI 3d88 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3db0 x19: x19 x20: x20
STACK CFI 3dbc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 3dc0 18c .cfa: sp 0 + .ra: x30
STACK CFI 3dc8 .cfa: sp 32976 +
STACK CFI 3ddc .ra: .cfa -32968 + ^ x29: .cfa -32976 + ^
STACK CFI 3de8 x23: .cfa -32928 + ^ x24: .cfa -32920 + ^
STACK CFI 3df0 x21: .cfa -32944 + ^ x22: .cfa -32936 + ^
STACK CFI 3dfc x19: .cfa -32960 + ^ x20: .cfa -32952 + ^
STACK CFI 3f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3f20 .cfa: sp 32976 + .ra: .cfa -32968 + ^ x19: .cfa -32960 + ^ x20: .cfa -32952 + ^ x21: .cfa -32944 + ^ x22: .cfa -32936 + ^ x23: .cfa -32928 + ^ x24: .cfa -32920 + ^ x29: .cfa -32976 + ^
STACK CFI INIT 3f50 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3f68 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3f80 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4038 a4 .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4044 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4050 x21: .cfa -32 + ^
STACK CFI 40d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 40d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40f8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 40fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4104 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4114 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 417c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 41e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 41e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4268 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4270 7c .cfa: sp 0 + .ra: x30
STACK CFI 4274 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 427c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 428c x21: .cfa -16 + ^
STACK CFI 42cc x21: x21
STACK CFI 42d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 42e4 x21: x21
STACK CFI 42e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 42f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4304 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4370 74 .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 437c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 438c x21: .cfa -16 + ^
STACK CFI 43bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 43e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43e8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 43ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4408 x21: .cfa -16 + ^
STACK CFI 4448 x21: x21
STACK CFI 4454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4458 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4490 160 .cfa: sp 0 + .ra: x30
STACK CFI 4494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 449c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 44c4 x21: .cfa -16 + ^
STACK CFI 453c x21: x21
STACK CFI 4540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4544 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45d8 x21: x21
STACK CFI 45dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 45f0 124 .cfa: sp 0 + .ra: x30
STACK CFI 45f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4604 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 464c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4718 58 .cfa: sp 0 + .ra: x30
STACK CFI 4730 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 475c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4760 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4764 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4770 98 .cfa: sp 0 + .ra: x30
STACK CFI 4774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 477c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 47dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4808 f0 .cfa: sp 0 + .ra: x30
STACK CFI 480c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4814 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4824 x21: .cfa -16 + ^
STACK CFI 486c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48f8 300 .cfa: sp 0 + .ra: x30
STACK CFI 4900 .cfa: sp 8304 +
STACK CFI 4904 .ra: .cfa -8296 + ^ x29: .cfa -8304 + ^
STACK CFI 490c x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI 4914 x25: .cfa -8240 + ^ x26: .cfa -8232 + ^
STACK CFI 492c x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 4950 x27: .cfa -8224 + ^ x28: .cfa -8216 + ^
STACK CFI 4960 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 4a20 x19: x19 x20: x20
STACK CFI 4a24 x27: x27 x28: x28
STACK CFI 4a50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4a54 .cfa: sp 8304 + .ra: .cfa -8296 + ^ x19: .cfa -8288 + ^ x20: .cfa -8280 + ^ x21: .cfa -8272 + ^ x22: .cfa -8264 + ^ x23: .cfa -8256 + ^ x24: .cfa -8248 + ^ x25: .cfa -8240 + ^ x26: .cfa -8232 + ^ x27: .cfa -8224 + ^ x28: .cfa -8216 + ^ x29: .cfa -8304 + ^
STACK CFI 4be4 x19: x19 x20: x20
STACK CFI 4be8 x27: x27 x28: x28
STACK CFI 4bf0 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 4bf4 x27: .cfa -8224 + ^ x28: .cfa -8216 + ^
STACK CFI INIT 4bf8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c20 234 .cfa: sp 0 + .ra: x30
STACK CFI 4c24 .cfa: sp 784 +
STACK CFI 4c30 .ra: .cfa -776 + ^ x29: .cfa -784 + ^
STACK CFI 4c38 x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI 4c48 x19: .cfa -768 + ^ x20: .cfa -760 + ^
STACK CFI 4c60 x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 4c6c x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 4d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4d80 .cfa: sp 784 + .ra: .cfa -776 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^ x29: .cfa -784 + ^
STACK CFI INIT 4e58 18 .cfa: sp 0 + .ra: x30
STACK CFI 4e5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4e70 310 .cfa: sp 0 + .ra: x30
STACK CFI 4e74 .cfa: sp 1424 +
STACK CFI 4e84 .ra: .cfa -1416 + ^ x29: .cfa -1424 + ^
STACK CFI 4e9c x19: .cfa -1408 + ^ x20: .cfa -1400 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f78 .cfa: sp 1424 + .ra: .cfa -1416 + ^ x19: .cfa -1408 + ^ x20: .cfa -1400 + ^ x29: .cfa -1424 + ^
STACK CFI INIT 5180 64 .cfa: sp 0 + .ra: x30
STACK CFI 5184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 518c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 51a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 51ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 51e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 51ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5214 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5250 5c .cfa: sp 0 + .ra: x30
STACK CFI 5254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 525c x19: .cfa -16 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 52a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 52b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 52bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52c4 x21: .cfa -16 + ^
STACK CFI 52cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5308 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5320 64 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 532c x19: .cfa -16 + ^
STACK CFI 5364 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5368 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5388 88 .cfa: sp 0 + .ra: x30
STACK CFI 538c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 540c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5410 7c .cfa: sp 0 + .ra: x30
STACK CFI 5414 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 541c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 544c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5450 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5490 7c .cfa: sp 0 + .ra: x30
STACK CFI 5494 .cfa: sp 928 +
STACK CFI 54ac .ra: .cfa -904 + ^ x29: .cfa -912 + ^
STACK CFI 54bc x19: .cfa -896 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5508 .cfa: sp 928 + .ra: .cfa -904 + ^ x19: .cfa -896 + ^ x29: .cfa -912 + ^
STACK CFI INIT 5510 60 .cfa: sp 0 + .ra: x30
STACK CFI 5514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 552c x21: .cfa -16 + ^
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5570 26c .cfa: sp 0 + .ra: x30
STACK CFI 5574 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 557c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5584 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5594 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 55b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 55bc x27: .cfa -16 + ^
STACK CFI 5704 x25: x25 x26: x26
STACK CFI 5708 x27: x27
STACK CFI 5720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5724 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5764 x25: x25 x26: x26 x27: x27
STACK CFI 576c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 57e0 28 .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5808 22c .cfa: sp 0 + .ra: x30
STACK CFI 580c .cfa: sp 912 +
STACK CFI 581c .ra: .cfa -904 + ^ x29: .cfa -912 + ^
STACK CFI 5824 x19: .cfa -896 + ^ x20: .cfa -888 + ^
STACK CFI 5834 x21: .cfa -880 + ^ x22: .cfa -872 + ^
STACK CFI 5888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 588c .cfa: sp 912 + .ra: .cfa -904 + ^ x19: .cfa -896 + ^ x20: .cfa -888 + ^ x21: .cfa -880 + ^ x22: .cfa -872 + ^ x29: .cfa -912 + ^
STACK CFI 589c x23: .cfa -864 + ^ x24: .cfa -856 + ^
STACK CFI 58d4 x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI 5928 x25: x25 x26: x26
STACK CFI 592c x23: x23 x24: x24
STACK CFI 5950 x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI 59d0 x23: x23 x24: x24
STACK CFI 59d4 x25: x25 x26: x26
STACK CFI 59d8 x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI 59fc x23: x23 x24: x24
STACK CFI 5a00 x25: x25 x26: x26
STACK CFI 5a04 x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI 5a28 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5a2c x23: .cfa -864 + ^ x24: .cfa -856 + ^
STACK CFI 5a30 x25: .cfa -848 + ^ x26: .cfa -840 + ^
STACK CFI INIT 5a38 70 .cfa: sp 0 + .ra: x30
STACK CFI 5a40 .cfa: sp 8368 +
STACK CFI 5a54 .ra: .cfa -8360 + ^ x29: .cfa -8368 + ^
STACK CFI 5a64 x19: .cfa -8352 + ^
STACK CFI 5aa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5aa4 .cfa: sp 8368 + .ra: .cfa -8360 + ^ x19: .cfa -8352 + ^ x29: .cfa -8368 + ^
STACK CFI INIT 5aa8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ae8 398 .cfa: sp 0 + .ra: x30
STACK CFI 5aec .cfa: sp 1216 +
STACK CFI 5af0 .ra: .cfa -1208 + ^ x29: .cfa -1216 + ^
STACK CFI 5af8 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 5b00 x19: .cfa -1200 + ^ x20: .cfa -1192 + ^
STACK CFI 5b0c x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 5b2c x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 5b80 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 5c48 x25: x25 x26: x26
STACK CFI 5c4c x27: x27 x28: x28
STACK CFI 5c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c7c .cfa: sp 1216 + .ra: .cfa -1208 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x29: .cfa -1216 + ^
STACK CFI 5cac x25: x25 x26: x26
STACK CFI 5cb0 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 5d68 x25: x25 x26: x26
STACK CFI 5d6c x27: x27 x28: x28
STACK CFI 5d70 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 5dc0 x25: x25 x26: x26
STACK CFI 5dc4 x27: x27 x28: x28
STACK CFI 5dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5dcc .cfa: sp 1216 + .ra: .cfa -1208 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^ x29: .cfa -1216 + ^
STACK CFI 5e1c x27: x27 x28: x28
STACK CFI 5e40 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 5e6c x25: x25 x26: x26
STACK CFI 5e70 x27: x27 x28: x28
STACK CFI 5e78 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 5e7c x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI INIT 5e80 128 .cfa: sp 0 + .ra: x30
STACK CFI 5e88 .cfa: sp 8288 +
STACK CFI 5e8c .ra: .cfa -8280 + ^ x29: .cfa -8288 + ^
STACK CFI 5e94 x19: .cfa -8272 + ^ x20: .cfa -8264 + ^
STACK CFI 5ea0 x21: .cfa -8256 + ^ x22: .cfa -8248 + ^
STACK CFI 5eb0 x23: .cfa -8240 + ^ x24: .cfa -8232 + ^
STACK CFI 5eb8 x25: .cfa -8224 + ^
STACK CFI 5f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5f54 .cfa: sp 8288 + .ra: .cfa -8280 + ^ x19: .cfa -8272 + ^ x20: .cfa -8264 + ^ x21: .cfa -8256 + ^ x22: .cfa -8248 + ^ x23: .cfa -8240 + ^ x24: .cfa -8232 + ^ x25: .cfa -8224 + ^ x29: .cfa -8288 + ^
STACK CFI INIT 5fa8 264 .cfa: sp 0 + .ra: x30
STACK CFI 5fac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5fb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5fbc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5fc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5fd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 60f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 60fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6210 104 .cfa: sp 0 + .ra: x30
STACK CFI 6214 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 6224 x21: .cfa -288 + ^
STACK CFI 622c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 630c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6310 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 6318 a4 .cfa: sp 0 + .ra: x30
STACK CFI 631c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 632c x19: .cfa -288 + ^
STACK CFI 63b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 63b8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 63c0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 63dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 63e8 x23: .cfa -16 + ^
STACK CFI 6484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6488 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 64a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 64a8 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6510 78 .cfa: sp 0 + .ra: x30
STACK CFI 6568 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 657c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6588 3c .cfa: sp 0 + .ra: x30
STACK CFI 658c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 65bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65c8 dc .cfa: sp 0 + .ra: x30
STACK CFI 65d0 .cfa: sp 8320 +
STACK CFI 65dc .ra: .cfa -8312 + ^ x29: .cfa -8320 + ^
STACK CFI 65e4 x19: .cfa -8304 + ^ x20: .cfa -8296 + ^
STACK CFI 6608 x21: .cfa -8288 + ^
STACK CFI 6678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 667c .cfa: sp 8320 + .ra: .cfa -8312 + ^ x19: .cfa -8304 + ^ x20: .cfa -8296 + ^ x21: .cfa -8288 + ^ x29: .cfa -8320 + ^
STACK CFI INIT 66a8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 66ac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 66d4 x19: .cfa -288 + ^
STACK CFI 6754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6758 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6760 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6764 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 6774 x19: .cfa -288 + ^
STACK CFI INIT 6800 94 .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 680c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6814 x21: .cfa -16 + ^
STACK CFI 6890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
