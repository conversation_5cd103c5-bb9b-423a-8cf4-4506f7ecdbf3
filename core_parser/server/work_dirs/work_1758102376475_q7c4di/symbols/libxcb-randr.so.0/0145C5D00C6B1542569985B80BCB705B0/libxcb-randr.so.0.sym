MODULE Linux arm64 0145C5D00C6B1542569985B80BCB705B0 libxcb-randr.so.0
INFO CODE_ID D0C545016B0C4215569985B80BCB705B42093079
PUBLIC 67e8 0 xcb_randr_mode_next
PUBLIC 6808 0 xcb_randr_mode_end
PUBLIC 6820 0 xcb_randr_crtc_next
PUBLIC 6840 0 xcb_randr_crtc_end
PUBLIC 6858 0 xcb_randr_output_next
PUBLIC 6878 0 xcb_randr_output_end
PUBLIC 6890 0 xcb_randr_provider_next
PUBLIC 68b0 0 xcb_randr_provider_end
PUBLIC 68c8 0 xcb_randr_lease_next
PUBLIC 68e8 0 xcb_randr_lease_end
PUBLIC 6900 0 xcb_randr_screen_size_next
PUBLIC 6920 0 xcb_randr_screen_size_end
PUBLIC 6938 0 xcb_randr_refresh_rates_sizeof
PUBLIC 6948 0 xcb_randr_refresh_rates_rates
PUBLIC 6950 0 xcb_randr_refresh_rates_rates_length
PUBLIC 6958 0 xcb_randr_refresh_rates_rates_end
PUBLIC 6970 0 xcb_randr_refresh_rates_next
PUBLIC 69b8 0 xcb_randr_refresh_rates_end
PUBLIC 6a10 0 xcb_randr_query_version
PUBLIC 6a80 0 xcb_randr_query_version_unchecked
PUBLIC 6af0 0 xcb_randr_query_version_reply
PUBLIC 6af8 0 xcb_randr_set_screen_config
PUBLIC 6b80 0 xcb_randr_set_screen_config_unchecked
PUBLIC 6c08 0 xcb_randr_set_screen_config_reply
PUBLIC 6c10 0 xcb_randr_select_input_checked
PUBLIC 6c88 0 xcb_randr_select_input
PUBLIC 6cf8 0 xcb_randr_get_screen_info_sizeof
PUBLIC 6da8 0 xcb_randr_get_screen_info
PUBLIC 6e18 0 xcb_randr_get_screen_info_unchecked
PUBLIC 6e80 0 xcb_randr_get_screen_info_sizes
PUBLIC 6e88 0 xcb_randr_get_screen_info_sizes_length
PUBLIC 6e90 0 xcb_randr_get_screen_info_sizes_iterator
PUBLIC 6eb0 0 xcb_randr_get_screen_info_rates_length
PUBLIC 6ec0 0 xcb_randr_get_screen_info_rates_iterator
PUBLIC 6f10 0 xcb_randr_get_screen_info_reply
PUBLIC 6f18 0 xcb_randr_get_screen_size_range
PUBLIC 6f88 0 xcb_randr_get_screen_size_range_unchecked
PUBLIC 6ff0 0 xcb_randr_get_screen_size_range_reply
PUBLIC 6ff8 0 xcb_randr_set_screen_size_checked
PUBLIC 7070 0 xcb_randr_set_screen_size
PUBLIC 70e8 0 xcb_randr_mode_info_next
PUBLIC 7108 0 xcb_randr_mode_info_end
PUBLIC 7120 0 xcb_randr_get_screen_resources_sizeof
PUBLIC 7148 0 xcb_randr_get_screen_resources
PUBLIC 71b8 0 xcb_randr_get_screen_resources_unchecked
PUBLIC 7220 0 xcb_randr_get_screen_resources_crtcs
PUBLIC 7228 0 xcb_randr_get_screen_resources_crtcs_length
PUBLIC 7230 0 xcb_randr_get_screen_resources_crtcs_end
PUBLIC 7248 0 xcb_randr_get_screen_resources_outputs
PUBLIC 7270 0 xcb_randr_get_screen_resources_outputs_length
PUBLIC 7278 0 xcb_randr_get_screen_resources_outputs_end
PUBLIC 72b8 0 xcb_randr_get_screen_resources_modes
PUBLIC 72e0 0 xcb_randr_get_screen_resources_modes_length
PUBLIC 72e8 0 xcb_randr_get_screen_resources_modes_iterator
PUBLIC 7330 0 xcb_randr_get_screen_resources_names
PUBLIC 7348 0 xcb_randr_get_screen_resources_names_length
PUBLIC 7350 0 xcb_randr_get_screen_resources_names_end
PUBLIC 7388 0 xcb_randr_get_screen_resources_reply
PUBLIC 7390 0 xcb_randr_get_output_info_sizeof
PUBLIC 73b8 0 xcb_randr_get_output_info
PUBLIC 7428 0 xcb_randr_get_output_info_unchecked
PUBLIC 7498 0 xcb_randr_get_output_info_crtcs
PUBLIC 74a0 0 xcb_randr_get_output_info_crtcs_length
PUBLIC 74a8 0 xcb_randr_get_output_info_crtcs_end
PUBLIC 74c0 0 xcb_randr_get_output_info_modes
PUBLIC 74e8 0 xcb_randr_get_output_info_modes_length
PUBLIC 74f0 0 xcb_randr_get_output_info_modes_end
PUBLIC 7530 0 xcb_randr_get_output_info_clones
PUBLIC 7558 0 xcb_randr_get_output_info_clones_length
PUBLIC 7560 0 xcb_randr_get_output_info_clones_end
PUBLIC 75a0 0 xcb_randr_get_output_info_name
PUBLIC 75b8 0 xcb_randr_get_output_info_name_length
PUBLIC 75c0 0 xcb_randr_get_output_info_name_end
PUBLIC 75f0 0 xcb_randr_get_output_info_reply
PUBLIC 75f8 0 xcb_randr_list_output_properties_sizeof
PUBLIC 7608 0 xcb_randr_list_output_properties
PUBLIC 7678 0 xcb_randr_list_output_properties_unchecked
PUBLIC 76e0 0 xcb_randr_list_output_properties_atoms
PUBLIC 76e8 0 xcb_randr_list_output_properties_atoms_length
PUBLIC 76f0 0 xcb_randr_list_output_properties_atoms_end
PUBLIC 7708 0 xcb_randr_list_output_properties_reply
PUBLIC 7710 0 xcb_randr_query_output_property_sizeof
PUBLIC 7720 0 xcb_randr_query_output_property
PUBLIC 7790 0 xcb_randr_query_output_property_unchecked
PUBLIC 7800 0 xcb_randr_query_output_property_valid_values
PUBLIC 7808 0 xcb_randr_query_output_property_valid_values_length
PUBLIC 7810 0 xcb_randr_query_output_property_valid_values_end
PUBLIC 7828 0 xcb_randr_query_output_property_reply
PUBLIC 7830 0 xcb_randr_configure_output_property_sizeof
PUBLIC 7840 0 xcb_randr_configure_output_property_checked
PUBLIC 78c8 0 xcb_randr_configure_output_property
PUBLIC 7950 0 xcb_randr_configure_output_property_values
PUBLIC 7958 0 xcb_randr_configure_output_property_values_length
PUBLIC 7968 0 xcb_randr_configure_output_property_values_end
PUBLIC 7980 0 xcb_randr_change_output_property_sizeof
PUBLIC 7998 0 xcb_randr_change_output_property_checked
PUBLIC 7a40 0 xcb_randr_change_output_property
PUBLIC 7ae0 0 xcb_randr_change_output_property_data
PUBLIC 7ae8 0 xcb_randr_change_output_property_data_length
PUBLIC 7b00 0 xcb_randr_change_output_property_data_end
PUBLIC 7b20 0 xcb_randr_delete_output_property_checked
PUBLIC 7b90 0 xcb_randr_delete_output_property
PUBLIC 7c00 0 xcb_randr_get_output_property_sizeof
PUBLIC 7c18 0 xcb_randr_get_output_property
PUBLIC 7ca0 0 xcb_randr_get_output_property_unchecked
PUBLIC 7d28 0 xcb_randr_get_output_property_data
PUBLIC 7d30 0 xcb_randr_get_output_property_data_length
PUBLIC 7d48 0 xcb_randr_get_output_property_data_end
PUBLIC 7d68 0 xcb_randr_get_output_property_reply
PUBLIC 7d70 0 xcb_randr_create_mode_sizeof
PUBLIC 7d78 0 xcb_randr_create_mode
PUBLIC 7e08 0 xcb_randr_create_mode_unchecked
PUBLIC 7e98 0 xcb_randr_create_mode_reply
PUBLIC 7ea0 0 xcb_randr_destroy_mode_checked
PUBLIC 7f10 0 xcb_randr_destroy_mode
PUBLIC 7f78 0 xcb_randr_add_output_mode_checked
PUBLIC 7fe8 0 xcb_randr_add_output_mode
PUBLIC 8058 0 xcb_randr_delete_output_mode_checked
PUBLIC 80c8 0 xcb_randr_delete_output_mode
PUBLIC 8138 0 xcb_randr_get_crtc_info_sizeof
PUBLIC 8150 0 xcb_randr_get_crtc_info
PUBLIC 81c0 0 xcb_randr_get_crtc_info_unchecked
PUBLIC 8230 0 xcb_randr_get_crtc_info_outputs
PUBLIC 8238 0 xcb_randr_get_crtc_info_outputs_length
PUBLIC 8240 0 xcb_randr_get_crtc_info_outputs_end
PUBLIC 8258 0 xcb_randr_get_crtc_info_possible
PUBLIC 8280 0 xcb_randr_get_crtc_info_possible_length
PUBLIC 8288 0 xcb_randr_get_crtc_info_possible_end
PUBLIC 82c8 0 xcb_randr_get_crtc_info_reply
PUBLIC 82d0 0 xcb_randr_set_crtc_config_sizeof
PUBLIC 82e0 0 xcb_randr_set_crtc_config
PUBLIC 8380 0 xcb_randr_set_crtc_config_unchecked
PUBLIC 8420 0 xcb_randr_set_crtc_config_reply
PUBLIC 8428 0 xcb_randr_get_crtc_gamma_size
PUBLIC 8498 0 xcb_randr_get_crtc_gamma_size_unchecked
PUBLIC 8500 0 xcb_randr_get_crtc_gamma_size_reply
PUBLIC 8508 0 xcb_randr_get_crtc_gamma_sizeof
PUBLIC 8520 0 xcb_randr_get_crtc_gamma
PUBLIC 8590 0 xcb_randr_get_crtc_gamma_unchecked
PUBLIC 85f8 0 xcb_randr_get_crtc_gamma_red
PUBLIC 8600 0 xcb_randr_get_crtc_gamma_red_length
PUBLIC 8608 0 xcb_randr_get_crtc_gamma_red_end
PUBLIC 8620 0 xcb_randr_get_crtc_gamma_green
PUBLIC 8640 0 xcb_randr_get_crtc_gamma_green_length
PUBLIC 8648 0 xcb_randr_get_crtc_gamma_green_end
PUBLIC 8680 0 xcb_randr_get_crtc_gamma_blue
PUBLIC 86a0 0 xcb_randr_get_crtc_gamma_blue_length
PUBLIC 86a8 0 xcb_randr_get_crtc_gamma_blue_end
PUBLIC 86e0 0 xcb_randr_get_crtc_gamma_reply
PUBLIC 86e8 0 xcb_randr_set_crtc_gamma_sizeof
PUBLIC 8700 0 xcb_randr_set_crtc_gamma_checked
PUBLIC 87a0 0 xcb_randr_set_crtc_gamma
PUBLIC 8840 0 xcb_randr_set_crtc_gamma_red
PUBLIC 8848 0 xcb_randr_set_crtc_gamma_red_length
PUBLIC 8850 0 xcb_randr_set_crtc_gamma_red_end
PUBLIC 8868 0 xcb_randr_set_crtc_gamma_green
PUBLIC 8888 0 xcb_randr_set_crtc_gamma_green_length
PUBLIC 8890 0 xcb_randr_set_crtc_gamma_green_end
PUBLIC 88c8 0 xcb_randr_set_crtc_gamma_blue
PUBLIC 88e8 0 xcb_randr_set_crtc_gamma_blue_length
PUBLIC 88f0 0 xcb_randr_set_crtc_gamma_blue_end
PUBLIC 8928 0 xcb_randr_get_screen_resources_current_sizeof
PUBLIC 8950 0 xcb_randr_get_screen_resources_current
PUBLIC 89c0 0 xcb_randr_get_screen_resources_current_unchecked
PUBLIC 8a28 0 xcb_randr_get_screen_resources_current_crtcs
PUBLIC 8a30 0 xcb_randr_get_screen_resources_current_crtcs_length
PUBLIC 8a38 0 xcb_randr_get_screen_resources_current_crtcs_end
PUBLIC 8a50 0 xcb_randr_get_screen_resources_current_outputs
PUBLIC 8a78 0 xcb_randr_get_screen_resources_current_outputs_length
PUBLIC 8a80 0 xcb_randr_get_screen_resources_current_outputs_end
PUBLIC 8ac0 0 xcb_randr_get_screen_resources_current_modes
PUBLIC 8ae8 0 xcb_randr_get_screen_resources_current_modes_length
PUBLIC 8af0 0 xcb_randr_get_screen_resources_current_modes_iterator
PUBLIC 8b38 0 xcb_randr_get_screen_resources_current_names
PUBLIC 8b50 0 xcb_randr_get_screen_resources_current_names_length
PUBLIC 8b58 0 xcb_randr_get_screen_resources_current_names_end
PUBLIC 8b90 0 xcb_randr_get_screen_resources_current_reply
PUBLIC 8b98 0 xcb_randr_set_crtc_transform_sizeof
PUBLIC 8bb8 0 xcb_randr_set_crtc_transform_checked
PUBLIC 8c68 0 xcb_randr_set_crtc_transform
PUBLIC 8d18 0 xcb_randr_set_crtc_transform_filter_name
PUBLIC 8d20 0 xcb_randr_set_crtc_transform_filter_name_length
PUBLIC 8d28 0 xcb_randr_set_crtc_transform_filter_name_end
PUBLIC 8d40 0 xcb_randr_set_crtc_transform_filter_params
PUBLIC 8d68 0 xcb_randr_set_crtc_transform_filter_params_length
PUBLIC 8d98 0 xcb_randr_set_crtc_transform_filter_params_end
PUBLIC 8df8 0 xcb_randr_get_crtc_transform_sizeof
PUBLIC 8e38 0 xcb_randr_get_crtc_transform
PUBLIC 8ea8 0 xcb_randr_get_crtc_transform_unchecked
PUBLIC 8f10 0 xcb_randr_get_crtc_transform_pending_filter_name
PUBLIC 8f18 0 xcb_randr_get_crtc_transform_pending_filter_name_length
PUBLIC 8f20 0 xcb_randr_get_crtc_transform_pending_filter_name_end
PUBLIC 8f38 0 xcb_randr_get_crtc_transform_pending_params
PUBLIC 8f60 0 xcb_randr_get_crtc_transform_pending_params_length
PUBLIC 8f68 0 xcb_randr_get_crtc_transform_pending_params_end
PUBLIC 8fa8 0 xcb_randr_get_crtc_transform_current_filter_name
PUBLIC 8fc0 0 xcb_randr_get_crtc_transform_current_filter_name_length
PUBLIC 8fc8 0 xcb_randr_get_crtc_transform_current_filter_name_end
PUBLIC 8ff8 0 xcb_randr_get_crtc_transform_current_params
PUBLIC 9020 0 xcb_randr_get_crtc_transform_current_params_length
PUBLIC 9028 0 xcb_randr_get_crtc_transform_current_params_end
PUBLIC 9068 0 xcb_randr_get_crtc_transform_reply
PUBLIC 9070 0 xcb_randr_get_panning
PUBLIC 90e0 0 xcb_randr_get_panning_unchecked
PUBLIC 9148 0 xcb_randr_get_panning_reply
PUBLIC 9150 0 xcb_randr_set_panning
PUBLIC 9210 0 xcb_randr_set_panning_unchecked
PUBLIC 92c8 0 xcb_randr_set_panning_reply
PUBLIC 92d0 0 xcb_randr_set_output_primary_checked
PUBLIC 9340 0 xcb_randr_set_output_primary
PUBLIC 93b0 0 xcb_randr_get_output_primary
PUBLIC 9420 0 xcb_randr_get_output_primary_unchecked
PUBLIC 9488 0 xcb_randr_get_output_primary_reply
PUBLIC 9490 0 xcb_randr_get_providers_sizeof
PUBLIC 94a0 0 xcb_randr_get_providers
PUBLIC 9510 0 xcb_randr_get_providers_unchecked
PUBLIC 9578 0 xcb_randr_get_providers_providers
PUBLIC 9580 0 xcb_randr_get_providers_providers_length
PUBLIC 9588 0 xcb_randr_get_providers_providers_end
PUBLIC 95a0 0 xcb_randr_get_providers_reply
PUBLIC 95a8 0 xcb_randr_get_provider_info_sizeof
PUBLIC 95d0 0 xcb_randr_get_provider_info
PUBLIC 9640 0 xcb_randr_get_provider_info_unchecked
PUBLIC 96b0 0 xcb_randr_get_provider_info_crtcs
PUBLIC 96b8 0 xcb_randr_get_provider_info_crtcs_length
PUBLIC 96c0 0 xcb_randr_get_provider_info_crtcs_end
PUBLIC 96d8 0 xcb_randr_get_provider_info_outputs
PUBLIC 9700 0 xcb_randr_get_provider_info_outputs_length
PUBLIC 9708 0 xcb_randr_get_provider_info_outputs_end
PUBLIC 9748 0 xcb_randr_get_provider_info_associated_providers
PUBLIC 9770 0 xcb_randr_get_provider_info_associated_providers_length
PUBLIC 9778 0 xcb_randr_get_provider_info_associated_providers_end
PUBLIC 97b8 0 xcb_randr_get_provider_info_associated_capability
PUBLIC 97e0 0 xcb_randr_get_provider_info_associated_capability_length
PUBLIC 97e8 0 xcb_randr_get_provider_info_associated_capability_end
PUBLIC 9828 0 xcb_randr_get_provider_info_name
PUBLIC 9840 0 xcb_randr_get_provider_info_name_length
PUBLIC 9848 0 xcb_randr_get_provider_info_name_end
PUBLIC 9878 0 xcb_randr_get_provider_info_reply
PUBLIC 9880 0 xcb_randr_set_provider_offload_sink_checked
PUBLIC 98f8 0 xcb_randr_set_provider_offload_sink
PUBLIC 9970 0 xcb_randr_set_provider_output_source_checked
PUBLIC 99e8 0 xcb_randr_set_provider_output_source
PUBLIC 9a60 0 xcb_randr_list_provider_properties_sizeof
PUBLIC 9a70 0 xcb_randr_list_provider_properties
PUBLIC 9ae0 0 xcb_randr_list_provider_properties_unchecked
PUBLIC 9b48 0 xcb_randr_list_provider_properties_atoms
PUBLIC 9b50 0 xcb_randr_list_provider_properties_atoms_length
PUBLIC 9b58 0 xcb_randr_list_provider_properties_atoms_end
PUBLIC 9b70 0 xcb_randr_list_provider_properties_reply
PUBLIC 9b78 0 xcb_randr_query_provider_property_sizeof
PUBLIC 9b88 0 xcb_randr_query_provider_property
PUBLIC 9bf8 0 xcb_randr_query_provider_property_unchecked
PUBLIC 9c68 0 xcb_randr_query_provider_property_valid_values
PUBLIC 9c70 0 xcb_randr_query_provider_property_valid_values_length
PUBLIC 9c78 0 xcb_randr_query_provider_property_valid_values_end
PUBLIC 9c90 0 xcb_randr_query_provider_property_reply
PUBLIC 9c98 0 xcb_randr_configure_provider_property_sizeof
PUBLIC 9ca8 0 xcb_randr_configure_provider_property_checked
PUBLIC 9d30 0 xcb_randr_configure_provider_property
PUBLIC 9db8 0 xcb_randr_configure_provider_property_values
PUBLIC 9dc0 0 xcb_randr_configure_provider_property_values_length
PUBLIC 9dd0 0 xcb_randr_configure_provider_property_values_end
PUBLIC 9de8 0 xcb_randr_change_provider_property_sizeof
PUBLIC 9e00 0 xcb_randr_change_provider_property_checked
PUBLIC 9ea8 0 xcb_randr_change_provider_property
PUBLIC 9f48 0 xcb_randr_change_provider_property_data
PUBLIC 9f50 0 xcb_randr_change_provider_property_data_length
PUBLIC 9f68 0 xcb_randr_change_provider_property_data_end
PUBLIC 9f88 0 xcb_randr_delete_provider_property_checked
PUBLIC 9ff8 0 xcb_randr_delete_provider_property
PUBLIC a068 0 xcb_randr_get_provider_property_sizeof
PUBLIC a080 0 xcb_randr_get_provider_property
PUBLIC a108 0 xcb_randr_get_provider_property_unchecked
PUBLIC a190 0 xcb_randr_get_provider_property_data
PUBLIC a198 0 xcb_randr_get_provider_property_data_length
PUBLIC a1b0 0 xcb_randr_get_provider_property_data_end
PUBLIC a1d0 0 xcb_randr_get_provider_property_reply
PUBLIC a1d8 0 xcb_randr_crtc_change_next
PUBLIC a1f8 0 xcb_randr_crtc_change_end
PUBLIC a218 0 xcb_randr_output_change_next
PUBLIC a238 0 xcb_randr_output_change_end
PUBLIC a258 0 xcb_randr_output_property_next
PUBLIC a278 0 xcb_randr_output_property_end
PUBLIC a298 0 xcb_randr_provider_change_next
PUBLIC a2b8 0 xcb_randr_provider_change_end
PUBLIC a2d8 0 xcb_randr_provider_property_next
PUBLIC a2f8 0 xcb_randr_provider_property_end
PUBLIC a318 0 xcb_randr_resource_change_next
PUBLIC a338 0 xcb_randr_resource_change_end
PUBLIC a358 0 xcb_randr_monitor_info_sizeof
PUBLIC a368 0 xcb_randr_monitor_info_outputs
PUBLIC a370 0 xcb_randr_monitor_info_outputs_length
PUBLIC a378 0 xcb_randr_monitor_info_outputs_end
PUBLIC a390 0 xcb_randr_monitor_info_next
PUBLIC a3d8 0 xcb_randr_monitor_info_end
PUBLIC a430 0 xcb_randr_get_monitors_sizeof
PUBLIC a4b0 0 xcb_randr_get_monitors
PUBLIC a520 0 xcb_randr_get_monitors_unchecked
PUBLIC a590 0 xcb_randr_get_monitors_monitors_length
PUBLIC a598 0 xcb_randr_get_monitors_monitors_iterator
PUBLIC a5b8 0 xcb_randr_get_monitors_reply
PUBLIC a5c0 0 xcb_randr_set_monitor_sizeof
PUBLIC a5e8 0 xcb_randr_set_monitor_checked
PUBLIC a670 0 xcb_randr_set_monitor
PUBLIC a6f8 0 xcb_randr_set_monitor_monitorinfo
PUBLIC a700 0 xcb_randr_delete_monitor_checked
PUBLIC a770 0 xcb_randr_delete_monitor
PUBLIC a7e0 0 xcb_randr_create_lease_sizeof
PUBLIC a7f8 0 xcb_randr_create_lease
PUBLIC a890 0 xcb_randr_create_lease_unchecked
PUBLIC a928 0 xcb_randr_create_lease_reply
PUBLIC a930 0 xcb_randr_create_lease_reply_fds
PUBLIC a940 0 xcb_randr_free_lease_checked
PUBLIC a9b0 0 xcb_randr_free_lease
PUBLIC aa20 0 xcb_randr_lease_notify_next
PUBLIC aa40 0 xcb_randr_lease_notify_end
PUBLIC aa60 0 xcb_randr_notify_data_next
PUBLIC aa80 0 xcb_randr_notify_data_end
STACK CFI INIT 6728 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6758 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6798 48 .cfa: sp 0 + .ra: x30
STACK CFI 679c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67a4 x19: .cfa -16 + ^
STACK CFI 67dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 67e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67e8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6808 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6820 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6840 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6858 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6878 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6890 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68b0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68e8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6900 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6920 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6938 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6948 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6958 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6970 48 .cfa: sp 0 + .ra: x30
STACK CFI 6974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 697c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 69b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69b8 54 .cfa: sp 0 + .ra: x30
STACK CFI 69bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69d4 x19: .cfa -32 + ^
STACK CFI 69f0 x19: x19
STACK CFI 6a08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a10 6c .cfa: sp 0 + .ra: x30
STACK CFI 6a14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6a24 x19: .cfa -112 + ^
STACK CFI 6a74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a78 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6a80 6c .cfa: sp 0 + .ra: x30
STACK CFI 6a84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6a94 x19: .cfa -112 + ^
STACK CFI 6ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ae8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6af8 88 .cfa: sp 0 + .ra: x30
STACK CFI 6afc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6b0c x19: .cfa -112 + ^
STACK CFI 6b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b7c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6b80 84 .cfa: sp 0 + .ra: x30
STACK CFI 6b84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6b94 x19: .cfa -112 + ^
STACK CFI 6bfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c00 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6c08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c10 74 .cfa: sp 0 + .ra: x30
STACK CFI 6c14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6c24 x19: .cfa -112 + ^
STACK CFI 6c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c80 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6c88 70 .cfa: sp 0 + .ra: x30
STACK CFI 6c8c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6c9c x19: .cfa -112 + ^
STACK CFI 6cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6cf4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6cf8 ac .cfa: sp 0 + .ra: x30
STACK CFI 6cfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d1c x23: .cfa -16 + ^
STACK CFI 6d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6da8 6c .cfa: sp 0 + .ra: x30
STACK CFI 6dac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6dbc x19: .cfa -96 + ^
STACK CFI 6e0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e10 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6e18 68 .cfa: sp 0 + .ra: x30
STACK CFI 6e1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6e2c x19: .cfa -96 + ^
STACK CFI 6e78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e7c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6e80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e90 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6eb0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ec0 4c .cfa: sp 0 + .ra: x30
STACK CFI 6ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ecc x19: .cfa -16 + ^
STACK CFI 6f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6f10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f18 6c .cfa: sp 0 + .ra: x30
STACK CFI 6f1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6f2c x19: .cfa -96 + ^
STACK CFI 6f7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f80 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6f88 68 .cfa: sp 0 + .ra: x30
STACK CFI 6f8c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6f9c x19: .cfa -96 + ^
STACK CFI 6fe8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6ff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ff8 78 .cfa: sp 0 + .ra: x30
STACK CFI 6ffc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 700c x19: .cfa -112 + ^
STACK CFI 7068 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 706c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7070 74 .cfa: sp 0 + .ra: x30
STACK CFI 7074 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7084 x19: .cfa -112 + ^
STACK CFI 70dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 70e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 70e8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7108 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7120 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7148 6c .cfa: sp 0 + .ra: x30
STACK CFI 714c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 715c x19: .cfa -96 + ^
STACK CFI 71ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 71b0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 71b8 68 .cfa: sp 0 + .ra: x30
STACK CFI 71bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 71cc x19: .cfa -96 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 721c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7228 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7230 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7248 24 .cfa: sp 0 + .ra: x30
STACK CFI 724c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7268 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7278 40 .cfa: sp 0 + .ra: x30
STACK CFI 727c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7284 x19: .cfa -16 + ^
STACK CFI 72b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72b8 24 .cfa: sp 0 + .ra: x30
STACK CFI 72bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 72e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 72e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 72ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72f4 x19: .cfa -16 + ^
STACK CFI 732c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7330 18 .cfa: sp 0 + .ra: x30
STACK CFI 7334 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7348 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7350 34 .cfa: sp 0 + .ra: x30
STACK CFI 7354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 735c x19: .cfa -16 + ^
STACK CFI 7380 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7388 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7390 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 73bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 73cc x19: .cfa -112 + ^
STACK CFI 7420 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7424 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7428 6c .cfa: sp 0 + .ra: x30
STACK CFI 742c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 743c x19: .cfa -112 + ^
STACK CFI 748c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7490 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7498 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 74c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 74e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 74e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74f0 40 .cfa: sp 0 + .ra: x30
STACK CFI 74f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74fc x19: .cfa -16 + ^
STACK CFI 752c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7530 24 .cfa: sp 0 + .ra: x30
STACK CFI 7534 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7550 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7558 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7560 40 .cfa: sp 0 + .ra: x30
STACK CFI 7564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 756c x19: .cfa -16 + ^
STACK CFI 759c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75a0 14 .cfa: sp 0 + .ra: x30
STACK CFI 75a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 75b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 75b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 75c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75cc x19: .cfa -16 + ^
STACK CFI 75ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7608 6c .cfa: sp 0 + .ra: x30
STACK CFI 760c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 761c x19: .cfa -96 + ^
STACK CFI 766c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7670 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7678 68 .cfa: sp 0 + .ra: x30
STACK CFI 767c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 768c x19: .cfa -96 + ^
STACK CFI 76d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 76e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7708 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7710 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7720 70 .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7734 x19: .cfa -112 + ^
STACK CFI 7788 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 778c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7790 6c .cfa: sp 0 + .ra: x30
STACK CFI 7794 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 77a4 x19: .cfa -112 + ^
STACK CFI 77f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77f8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7808 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7810 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7828 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7830 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7840 88 .cfa: sp 0 + .ra: x30
STACK CFI 7844 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7854 x19: .cfa -144 + ^
STACK CFI 78c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 78c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 78c8 84 .cfa: sp 0 + .ra: x30
STACK CFI 78cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 78dc x19: .cfa -144 + ^
STACK CFI 7944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7948 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7958 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7968 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7980 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7998 a4 .cfa: sp 0 + .ra: x30
STACK CFI 79a0 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 79b0 x19: .cfa -144 + ^
STACK CFI 7a34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7a38 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7a40 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7a48 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7a58 x19: .cfa -144 + ^
STACK CFI 7ad8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7adc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7ae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ae8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b00 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b20 70 .cfa: sp 0 + .ra: x30
STACK CFI 7b24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7b34 x19: .cfa -112 + ^
STACK CFI 7b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b8c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7b90 6c .cfa: sp 0 + .ra: x30
STACK CFI 7b94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7ba4 x19: .cfa -112 + ^
STACK CFI 7bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bf8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7c00 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c18 88 .cfa: sp 0 + .ra: x30
STACK CFI 7c1c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7c2c x19: .cfa -128 + ^
STACK CFI 7c98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c9c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7ca0 84 .cfa: sp 0 + .ra: x30
STACK CFI 7ca4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7cb4 x19: .cfa -128 + ^
STACK CFI 7d1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d20 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7d28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d30 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d48 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d78 90 .cfa: sp 0 + .ra: x30
STACK CFI 7d7c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7d98 x19: .cfa -160 + ^
STACK CFI 7e00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e04 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7e08 8c .cfa: sp 0 + .ra: x30
STACK CFI 7e0c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7e28 x19: .cfa -160 + ^
STACK CFI 7e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e90 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7e98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ea0 6c .cfa: sp 0 + .ra: x30
STACK CFI 7ea4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7eb4 x19: .cfa -96 + ^
STACK CFI 7f04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f08 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7f10 68 .cfa: sp 0 + .ra: x30
STACK CFI 7f14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7f24 x19: .cfa -96 + ^
STACK CFI 7f70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f74 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7f78 70 .cfa: sp 0 + .ra: x30
STACK CFI 7f7c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7f8c x19: .cfa -112 + ^
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7fe4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7fe8 6c .cfa: sp 0 + .ra: x30
STACK CFI 7fec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7ffc x19: .cfa -112 + ^
STACK CFI 804c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8050 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8058 70 .cfa: sp 0 + .ra: x30
STACK CFI 805c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 806c x19: .cfa -112 + ^
STACK CFI 80c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 80c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 80c8 6c .cfa: sp 0 + .ra: x30
STACK CFI 80cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 80dc x19: .cfa -112 + ^
STACK CFI 812c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8130 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8138 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8150 70 .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8164 x19: .cfa -112 + ^
STACK CFI 81b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 81bc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 81c0 6c .cfa: sp 0 + .ra: x30
STACK CFI 81c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 81d4 x19: .cfa -112 + ^
STACK CFI 8224 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8228 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8238 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8240 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8258 24 .cfa: sp 0 + .ra: x30
STACK CFI 825c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8278 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8288 40 .cfa: sp 0 + .ra: x30
STACK CFI 828c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8294 x19: .cfa -16 + ^
STACK CFI 82c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 82c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 82d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 82e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 82e4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 82fc x19: .cfa -160 + ^
STACK CFI 8378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 837c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8380 9c .cfa: sp 0 + .ra: x30
STACK CFI 8384 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 839c x19: .cfa -160 + ^
STACK CFI 8414 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8418 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 8420 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8428 6c .cfa: sp 0 + .ra: x30
STACK CFI 842c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 843c x19: .cfa -96 + ^
STACK CFI 848c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8490 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8498 68 .cfa: sp 0 + .ra: x30
STACK CFI 849c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 84ac x19: .cfa -96 + ^
STACK CFI 84f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 84fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8500 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8508 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8520 6c .cfa: sp 0 + .ra: x30
STACK CFI 8524 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8534 x19: .cfa -96 + ^
STACK CFI 8584 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8588 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8590 68 .cfa: sp 0 + .ra: x30
STACK CFI 8594 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 85a4 x19: .cfa -96 + ^
STACK CFI 85f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 85f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8608 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8620 1c .cfa: sp 0 + .ra: x30
STACK CFI 8624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8648 38 .cfa: sp 0 + .ra: x30
STACK CFI 864c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8654 x19: .cfa -16 + ^
STACK CFI 867c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8680 1c .cfa: sp 0 + .ra: x30
STACK CFI 8684 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8698 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 86a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86a8 38 .cfa: sp 0 + .ra: x30
STACK CFI 86ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86b4 x19: .cfa -16 + ^
STACK CFI 86dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8700 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8704 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8714 x19: .cfa -208 + ^
STACK CFI 8798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 879c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 87a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 87a4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 87b8 x19: .cfa -208 + ^
STACK CFI 8834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8838 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 8840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8848 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8850 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8868 1c .cfa: sp 0 + .ra: x30
STACK CFI 886c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8880 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8888 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8890 38 .cfa: sp 0 + .ra: x30
STACK CFI 8894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 889c x19: .cfa -16 + ^
STACK CFI 88c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 88c8 1c .cfa: sp 0 + .ra: x30
STACK CFI 88cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 88e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 88e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 88f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88fc x19: .cfa -16 + ^
STACK CFI 8924 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8928 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8950 6c .cfa: sp 0 + .ra: x30
STACK CFI 8954 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8964 x19: .cfa -96 + ^
STACK CFI 89b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 89b8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 89c0 68 .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 89d4 x19: .cfa -96 + ^
STACK CFI 8a20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8a24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8a28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a38 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a50 24 .cfa: sp 0 + .ra: x30
STACK CFI 8a54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a80 40 .cfa: sp 0 + .ra: x30
STACK CFI 8a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a8c x19: .cfa -16 + ^
STACK CFI 8abc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ac0 24 .cfa: sp 0 + .ra: x30
STACK CFI 8ac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ae8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8af0 48 .cfa: sp 0 + .ra: x30
STACK CFI 8af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8afc x19: .cfa -16 + ^
STACK CFI 8b34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b38 18 .cfa: sp 0 + .ra: x30
STACK CFI 8b3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8b4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b58 34 .cfa: sp 0 + .ra: x30
STACK CFI 8b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b64 x19: .cfa -16 + ^
STACK CFI 8b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b98 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bb8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8bbc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8be0 x19: .cfa -208 + ^
STACK CFI 8c60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8c64 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 8c68 ac .cfa: sp 0 + .ra: x30
STACK CFI 8c6c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8c94 x19: .cfa -208 + ^
STACK CFI 8d0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8d10 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 8d18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d28 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d40 24 .cfa: sp 0 + .ra: x30
STACK CFI 8d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d68 2c .cfa: sp 0 + .ra: x30
STACK CFI 8d6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d74 x19: .cfa -16 + ^
STACK CFI 8d90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8d98 60 .cfa: sp 0 + .ra: x30
STACK CFI 8d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8da4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8dac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8df8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e38 6c .cfa: sp 0 + .ra: x30
STACK CFI 8e3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8e4c x19: .cfa -96 + ^
STACK CFI 8e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ea0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8ea8 68 .cfa: sp 0 + .ra: x30
STACK CFI 8eac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8ebc x19: .cfa -96 + ^
STACK CFI 8f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f0c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f38 24 .cfa: sp 0 + .ra: x30
STACK CFI 8f3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f68 40 .cfa: sp 0 + .ra: x30
STACK CFI 8f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f74 x19: .cfa -16 + ^
STACK CFI 8fa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8fa8 14 .cfa: sp 0 + .ra: x30
STACK CFI 8fac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8fb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8fc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8fc8 30 .cfa: sp 0 + .ra: x30
STACK CFI 8fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fd4 x19: .cfa -16 + ^
STACK CFI 8ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ff8 24 .cfa: sp 0 + .ra: x30
STACK CFI 8ffc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9018 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9028 40 .cfa: sp 0 + .ra: x30
STACK CFI 902c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9034 x19: .cfa -16 + ^
STACK CFI 9064 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9068 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9070 6c .cfa: sp 0 + .ra: x30
STACK CFI 9074 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9084 x19: .cfa -96 + ^
STACK CFI 90d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 90d8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 90e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 90e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 90f4 x19: .cfa -96 + ^
STACK CFI 9140 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9144 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9148 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9150 bc .cfa: sp 0 + .ra: x30
STACK CFI 9154 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9164 x19: .cfa -128 + ^
STACK CFI 9204 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9208 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9210 b8 .cfa: sp 0 + .ra: x30
STACK CFI 9214 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9224 x19: .cfa -128 + ^
STACK CFI 92c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 92c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 92c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 92d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 92d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 92e4 x19: .cfa -112 + ^
STACK CFI 9338 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 933c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9340 6c .cfa: sp 0 + .ra: x30
STACK CFI 9344 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9354 x19: .cfa -112 + ^
STACK CFI 93a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 93a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 93b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 93b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 93c4 x19: .cfa -96 + ^
STACK CFI 9414 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9418 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9420 68 .cfa: sp 0 + .ra: x30
STACK CFI 9424 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9434 x19: .cfa -96 + ^
STACK CFI 9480 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9484 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9488 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9490 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 94a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 94b4 x19: .cfa -96 + ^
STACK CFI 9504 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9508 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9510 68 .cfa: sp 0 + .ra: x30
STACK CFI 9514 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9524 x19: .cfa -96 + ^
STACK CFI 9570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9574 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9578 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9588 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95a8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 95d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 95e4 x19: .cfa -112 + ^
STACK CFI 9638 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 963c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9640 6c .cfa: sp 0 + .ra: x30
STACK CFI 9644 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9654 x19: .cfa -112 + ^
STACK CFI 96a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 96a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 96b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96d8 24 .cfa: sp 0 + .ra: x30
STACK CFI 96dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 96f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9708 40 .cfa: sp 0 + .ra: x30
STACK CFI 970c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9714 x19: .cfa -16 + ^
STACK CFI 9744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9748 24 .cfa: sp 0 + .ra: x30
STACK CFI 974c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9768 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9778 40 .cfa: sp 0 + .ra: x30
STACK CFI 977c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9784 x19: .cfa -16 + ^
STACK CFI 97b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 97b8 24 .cfa: sp 0 + .ra: x30
STACK CFI 97bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 97d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 97e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 97e8 40 .cfa: sp 0 + .ra: x30
STACK CFI 97ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 97f4 x19: .cfa -16 + ^
STACK CFI 9824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9828 14 .cfa: sp 0 + .ra: x30
STACK CFI 982c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9848 30 .cfa: sp 0 + .ra: x30
STACK CFI 984c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9854 x19: .cfa -16 + ^
STACK CFI 9874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9878 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9880 78 .cfa: sp 0 + .ra: x30
STACK CFI 9884 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9894 x19: .cfa -112 + ^
STACK CFI 98f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 98f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 98f8 74 .cfa: sp 0 + .ra: x30
STACK CFI 98fc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 990c x19: .cfa -112 + ^
STACK CFI 9964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9968 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9970 78 .cfa: sp 0 + .ra: x30
STACK CFI 9974 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9984 x19: .cfa -112 + ^
STACK CFI 99e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 99e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 99e8 74 .cfa: sp 0 + .ra: x30
STACK CFI 99ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 99fc x19: .cfa -112 + ^
STACK CFI 9a54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9a58 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9a60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a70 6c .cfa: sp 0 + .ra: x30
STACK CFI 9a74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9a84 x19: .cfa -96 + ^
STACK CFI 9ad4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ad8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9ae0 68 .cfa: sp 0 + .ra: x30
STACK CFI 9ae4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9af4 x19: .cfa -96 + ^
STACK CFI 9b40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9b44 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9b48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b58 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b78 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b88 70 .cfa: sp 0 + .ra: x30
STACK CFI 9b8c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9b9c x19: .cfa -112 + ^
STACK CFI 9bf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9bf4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9bf8 6c .cfa: sp 0 + .ra: x30
STACK CFI 9bfc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9c0c x19: .cfa -112 + ^
STACK CFI 9c5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9c60 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9c68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c78 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ca8 88 .cfa: sp 0 + .ra: x30
STACK CFI 9cac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9cbc x19: .cfa -144 + ^
STACK CFI 9d28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9d2c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9d30 84 .cfa: sp 0 + .ra: x30
STACK CFI 9d34 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9d44 x19: .cfa -144 + ^
STACK CFI 9dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9db0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9db8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9dc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9dd0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9de8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e00 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9e08 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9e1c x19: .cfa -144 + ^
STACK CFI 9e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ea0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9ea8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 9eb0 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9ec4 x19: .cfa -144 + ^
STACK CFI 9f40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9f44 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9f48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f68 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f88 70 .cfa: sp 0 + .ra: x30
STACK CFI 9f8c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9f9c x19: .cfa -112 + ^
STACK CFI 9ff0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ff4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9ff8 6c .cfa: sp 0 + .ra: x30
STACK CFI 9ffc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a00c x19: .cfa -112 + ^
STACK CFI a05c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a060 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a068 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a080 88 .cfa: sp 0 + .ra: x30
STACK CFI a084 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a094 x19: .cfa -128 + ^
STACK CFI a100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a104 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT a108 84 .cfa: sp 0 + .ra: x30
STACK CFI a10c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a11c x19: .cfa -128 + ^
STACK CFI a184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a188 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT a190 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a198 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1b0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1d8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1f8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a218 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a238 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a258 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a278 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a298 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a2b8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a2d8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a2f8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a318 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a338 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a358 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a378 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a390 48 .cfa: sp 0 + .ra: x30
STACK CFI a394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a39c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a3d8 54 .cfa: sp 0 + .ra: x30
STACK CFI a3dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a3f4 x19: .cfa -32 + ^
STACK CFI a410 x19: x19
STACK CFI a428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a430 7c .cfa: sp 0 + .ra: x30
STACK CFI a434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a43c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a444 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a498 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a4a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a4b0 70 .cfa: sp 0 + .ra: x30
STACK CFI a4b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a4c4 x19: .cfa -112 + ^
STACK CFI a518 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a51c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a520 6c .cfa: sp 0 + .ra: x30
STACK CFI a524 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a534 x19: .cfa -112 + ^
STACK CFI a584 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a588 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a590 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a598 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT a5b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a5c0 28 .cfa: sp 0 + .ra: x30
STACK CFI a5c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a5e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a5e8 88 .cfa: sp 0 + .ra: x30
STACK CFI a5ec .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a5f8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a66c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT a670 88 .cfa: sp 0 + .ra: x30
STACK CFI a674 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a680 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a6f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT a6f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a700 70 .cfa: sp 0 + .ra: x30
STACK CFI a704 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a714 x19: .cfa -112 + ^
STACK CFI a768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a76c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a770 6c .cfa: sp 0 + .ra: x30
STACK CFI a774 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a784 x19: .cfa -112 + ^
STACK CFI a7d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a7d8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a7e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7f8 98 .cfa: sp 0 + .ra: x30
STACK CFI a7fc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a80c x19: .cfa -176 + ^
STACK CFI a888 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a88c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT a890 98 .cfa: sp 0 + .ra: x30
STACK CFI a894 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a8a4 x19: .cfa -176 + ^
STACK CFI a920 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a924 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT a928 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a930 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a940 70 .cfa: sp 0 + .ra: x30
STACK CFI a944 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a954 x19: .cfa -112 + ^
STACK CFI a9a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9ac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT a9b0 6c .cfa: sp 0 + .ra: x30
STACK CFI a9b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a9c4 x19: .cfa -112 + ^
STACK CFI aa14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa18 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT aa20 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT aa40 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT aa60 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT aa80 1c .cfa: sp 0 + .ra: x30
