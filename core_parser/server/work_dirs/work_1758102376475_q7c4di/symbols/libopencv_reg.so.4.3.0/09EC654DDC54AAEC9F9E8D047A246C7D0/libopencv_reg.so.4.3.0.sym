MODULE Linux arm64 09EC654DDC54AAEC9F9E8D047A246C7D0 libopencv_reg.so.4.3
INFO CODE_ID 4D65EC0954DCECAA9F9E8D047A246C7D0074A031
PUBLIC 4db8 0 _init
PUBLIC 5210 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.27]
PUBLIC 52b0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.37]
PUBLIC 5350 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.26]
PUBLIC 53f0 0 call_weak_fn
PUBLIC 5408 0 deregister_tm_clones
PUBLIC 5440 0 register_tm_clones
PUBLIC 5480 0 __do_global_dtors_aux
PUBLIC 54c8 0 frame_dummy
PUBLIC 5500 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 5508 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5518 0 cv::reg::Map::~Map()
PUBLIC 5520 0 cv::reg::Map::~Map()
PUBLIC 5538 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 5628 0 cv::reg::Map::warp(cv::_InputArray const&, cv::_OutputArray const&) const
PUBLIC 5760 0 cv::reg::MapAffine::scale(double)
PUBLIC 5770 0 std::_Sp_counted_ptr<cv::reg::MapAffine*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 5778 0 std::_Sp_counted_ptr<cv::reg::MapAffine*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5780 0 cv::reg::MapAffine::~MapAffine()
PUBLIC 5798 0 cv::reg::MapAffine::~MapAffine() [clone .localalias.28]
PUBLIC 57b0 0 std::_Sp_counted_ptr<cv::reg::MapAffine*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 57b8 0 std::_Sp_counted_ptr<cv::reg::MapAffine*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 57c0 0 std::_Sp_counted_ptr<cv::reg::MapAffine*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 5808 0 cv::reg::MapAffine::compose(cv::Ptr<cv::reg::Map>)
PUBLIC 5878 0 cv::Mat::~Mat()
PUBLIC 5910 0 cv::reg::MapAffine::inverseWarp(cv::_InputArray const&, cv::_OutputArray const&) const
PUBLIC 5fc0 0 cv::reg::MapAffine::MapAffine()
PUBLIC 5fe8 0 cv::reg::MapAffine::MapAffine(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 6670 0 cv::reg::MapAffine::inverseMap() const
PUBLIC 67d8 0 cv::MatExpr::~MatExpr()
PUBLIC 6990 0 cv::reg::Mapper::gradient(cv::Mat const&, cv::Mat const&, cv::Mat&, cv::Mat&, cv::Mat&) const
PUBLIC 8700 0 void cv::reg::fillGridMatrices<unsigned char>(cv::Mat, cv::Mat, cv::Mat)
PUBLIC 8c70 0 void cv::reg::fillGridMatrices<unsigned short>(cv::Mat, cv::Mat, cv::Mat)
PUBLIC 9200 0 void cv::reg::fillGridMatrices<float>(cv::Mat, cv::Mat, cv::Mat)
PUBLIC 95b0 0 void cv::reg::fillGridMatrices<double>(cv::Mat, cv::Mat, cv::Mat)
PUBLIC 9960 0 cv::reg::Mapper::grid(cv::Mat const&, cv::Mat&, cv::Mat&) const
PUBLIC a7e8 0 cv::reg::MapperGradAffine::~MapperGradAffine()
PUBLIC a7f0 0 cv::reg::MapperGradAffine::~MapperGradAffine()
PUBLIC a810 0 cv::reg::Mapper::sqr(cv::Mat const&) const
PUBLIC aa90 0 cv::reg::MapperGradAffine::MapperGradAffine()
PUBLIC aaa8 0 cv::reg::MapperGradAffine::getMap() const
PUBLIC ab40 0 cv::reg::MapperGradAffine::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC d8f0 0 cv::reg::MapperGradEuclid::~MapperGradEuclid()
PUBLIC d8f8 0 cv::reg::MapperGradEuclid::~MapperGradEuclid()
PUBLIC d910 0 cv::reg::MapperGradEuclid::MapperGradEuclid()
PUBLIC d928 0 cv::reg::MapperGradEuclid::getMap() const
PUBLIC d9c0 0 cv::reg::MapperGradEuclid::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC fd50 0 cv::reg::MapperGradProj::~MapperGradProj()
PUBLIC fd58 0 std::_Sp_counted_ptr<cv::reg::MapProjec*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC fd60 0 std::_Sp_counted_ptr<cv::reg::MapProjec*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC fd80 0 std::_Sp_counted_ptr<cv::reg::MapProjec*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC fd88 0 cv::reg::MapperGradProj::~MapperGradProj()
PUBLIC fda0 0 std::_Sp_counted_ptr<cv::reg::MapProjec*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC fda8 0 std::_Sp_counted_ptr<cv::reg::MapProjec*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC fdb0 0 cv::reg::MapperGradProj::MapperGradProj()
PUBLIC fdc8 0 cv::reg::MapperGradProj::getMap() const
PUBLIC fe60 0 cv::reg::MapperGradProj::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC 14528 0 cv::reg::MapperGradShift::~MapperGradShift()
PUBLIC 14530 0 std::_Sp_counted_ptr<cv::reg::MapShift*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 14538 0 std::_Sp_counted_ptr<cv::reg::MapShift*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 14558 0 std::_Sp_counted_ptr<cv::reg::MapShift*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 14560 0 cv::reg::MapperGradShift::~MapperGradShift()
PUBLIC 14578 0 std::_Sp_counted_ptr<cv::reg::MapShift*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 14580 0 std::_Sp_counted_ptr<cv::reg::MapShift*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 14588 0 cv::reg::MapperGradShift::MapperGradShift()
PUBLIC 145a0 0 cv::reg::MapperGradShift::getMap() const
PUBLIC 14630 0 cv::reg::MapperGradShift::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC 15a90 0 cv::reg::MapperGradSimilar::~MapperGradSimilar()
PUBLIC 15a98 0 cv::reg::MapperGradSimilar::~MapperGradSimilar()
PUBLIC 15ab0 0 cv::reg::MapperGradSimilar::MapperGradSimilar()
PUBLIC 15ac8 0 cv::reg::MapperGradSimilar::getMap() const
PUBLIC 15b60 0 cv::reg::MapperGradSimilar::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC 187c0 0 cv::reg::MapperPyramid::getMap() const [clone .localalias.20]
PUBLIC 187d0 0 cv::reg::MapperPyramid::~MapperPyramid()
PUBLIC 187d8 0 cv::reg::MapperPyramid::~MapperPyramid()
PUBLIC 187e0 0 cv::reg::MapperPyramid::MapperPyramid(cv::Ptr<cv::reg::Mapper>)
PUBLIC 18808 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 188d0 0 cv::reg::MapperPyramid::calculate(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::reg::Map>) const
PUBLIC 19e60 0 cv::reg::MapProjec::compose(cv::Ptr<cv::reg::Map>)
PUBLIC 19f38 0 cv::reg::MapProjec::scale(double)
PUBLIC 19f68 0 cv::reg::MapProjec::~MapProjec()
PUBLIC 19f80 0 cv::reg::MapProjec::~MapProjec() [clone .localalias.27]
PUBLIC 19fa0 0 cv::reg::MapProjec::inverseWarp(cv::_InputArray const&, cv::_OutputArray const&) const
PUBLIC 1a710 0 cv::reg::MapProjec::MapProjec()
PUBLIC 1a748 0 cv::reg::MapProjec::MapProjec(cv::_InputArray const&)
PUBLIC 1abb8 0 cv::reg::MapProjec::inverseMap() const
PUBLIC 1ad80 0 cv::reg::MapShift::compose(cv::Ptr<cv::reg::Map>)
PUBLIC 1ad98 0 cv::reg::MapShift::scale(double)
PUBLIC 1ada8 0 cv::reg::MapShift::~MapShift()
PUBLIC 1adc0 0 cv::reg::MapShift::~MapShift() [clone .localalias.6]
PUBLIC 1ade0 0 cv::reg::MapShift::inverseWarp(cv::_InputArray const&, cv::_OutputArray const&) const
PUBLIC 1b3f0 0 cv::reg::MapShift::MapShift()
PUBLIC 1b410 0 cv::reg::MapShift::MapShift(cv::_InputArray const&)
PUBLIC 1b5d0 0 cv::reg::MapShift::inverseMap() const
PUBLIC 1b690 0 _fini
STACK CFI INIT 5500 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5508 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5518 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5520 18 .cfa: sp 0 + .ra: x30
STACK CFI 5524 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5534 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5538 ec .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5548 .ra: .cfa -16 + ^
STACK CFI 5570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5578 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 55f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 5628 134 .cfa: sp 0 + .ra: x30
STACK CFI 562c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5638 .ra: .cfa -32 + ^
STACK CFI 569c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 56a0 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 5760 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5770 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5778 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5780 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5798 18 .cfa: sp 0 + .ra: x30
STACK CFI 579c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 57ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 57b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5210 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5220 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 52a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 52a4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 57c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 57c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 57f4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 57f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 57fc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5800 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5808 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5878 90 .cfa: sp 0 + .ra: x30
STACK CFI 587c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 58f0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 58f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5904 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5910 684 .cfa: sp 0 + .ra: x30
STACK CFI 5914 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 591c x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 5924 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 592c .ra: .cfa -416 + ^
STACK CFI 5eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5eb8 .cfa: sp 464 + .ra: .cfa -416 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI INIT 5fc0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5fe8 680 .cfa: sp 0 + .ra: x30
STACK CFI 5fec .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 5ff8 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6008 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 6018 .ra: .cfa -264 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^
STACK CFI 6588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 6590 .cfa: sp 336 + .ra: .cfa -264 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^
STACK CFI INIT 6670 168 .cfa: sp 0 + .ra: x30
STACK CFI 6674 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 667c .ra: .cfa -120 + ^ x21: .cfa -128 + ^
STACK CFI 678c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6790 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^
STACK CFI INIT 52b0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 52b4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 52c0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5344 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 67d8 1ac .cfa: sp 0 + .ra: x30
STACK CFI 67dc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 67e8 .ra: .cfa -16 + ^
STACK CFI 6944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6948 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6990 1d60 .cfa: sp 0 + .ra: x30
STACK CFI 6998 .cfa: sp 1040 +
STACK CFI 69a0 x21: .cfa -1024 + ^ x22: .cfa -1016 + ^
STACK CFI 69a8 x19: .cfa -1040 + ^ x20: .cfa -1032 + ^
STACK CFI 69b8 x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^
STACK CFI 69c8 .ra: .cfa -960 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
STACK CFI 78c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 78c8 .cfa: sp 1040 + .ra: .cfa -960 + ^ x19: .cfa -1040 + ^ x20: .cfa -1032 + ^ x21: .cfa -1024 + ^ x22: .cfa -1016 + ^ x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
STACK CFI INIT 8700 55c .cfa: sp 0 + .ra: x30
STACK CFI 8728 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8ba0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 8ba8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8bf0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8c70 580 .cfa: sp 0 + .ra: x30
STACK CFI 8c74 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8c80 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8f30 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 9200 398 .cfa: sp 0 + .ra: x30
STACK CFI 922c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 93d4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 95b0 39c .cfa: sp 0 + .ra: x30
STACK CFI 95dc .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 97b4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 9960 e88 .cfa: sp 0 + .ra: x30
STACK CFI 9964 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 996c x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 997c .ra: .cfa -296 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^
STACK CFI 9a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 9a38 .cfa: sp 352 + .ra: .cfa -296 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^
STACK CFI a03c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a040 .cfa: sp 352 + .ra: .cfa -296 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^
STACK CFI a3ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a3b0 .cfa: sp 352 + .ra: .cfa -296 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^
STACK CFI INIT a7e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7f0 18 .cfa: sp 0 + .ra: x30
STACK CFI a7f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI a804 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT a810 26c .cfa: sp 0 + .ra: x30
STACK CFI a820 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI a82c .ra: .cfa -392 + ^ x21: .cfa -400 + ^
STACK CFI aa20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI aa28 .cfa: sp 416 + .ra: .cfa -392 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^
STACK CFI INIT aa90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT aaa8 8c .cfa: sp 0 + .ra: x30
STACK CFI aaac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aab8 .ra: .cfa -16 + ^
STACK CFI aafc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI ab00 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT ab40 2d90 .cfa: sp 0 + .ra: x30
STACK CFI ab44 .cfa: sp 3552 +
STACK CFI ab48 x19: .cfa -3552 + ^ x20: .cfa -3544 + ^
STACK CFI ab7c .ra: .cfa -3472 + ^ v10: .cfa -3440 + ^ v11: .cfa -3432 + ^ v12: .cfa -3424 + ^ v13: .cfa -3416 + ^ v14: .cfa -3408 + ^ v15: .cfa -3400 + ^ v8: .cfa -3456 + ^ v9: .cfa -3448 + ^ x21: .cfa -3536 + ^ x22: .cfa -3528 + ^ x23: .cfa -3520 + ^ x24: .cfa -3512 + ^ x25: .cfa -3504 + ^ x26: .cfa -3496 + ^ x27: .cfa -3488 + ^ x28: .cfa -3480 + ^
STACK CFI d098 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d0a0 .cfa: sp 3552 + .ra: .cfa -3472 + ^ v10: .cfa -3440 + ^ v11: .cfa -3432 + ^ v12: .cfa -3424 + ^ v13: .cfa -3416 + ^ v14: .cfa -3408 + ^ v15: .cfa -3400 + ^ v8: .cfa -3456 + ^ v9: .cfa -3448 + ^ x19: .cfa -3552 + ^ x20: .cfa -3544 + ^ x21: .cfa -3536 + ^ x22: .cfa -3528 + ^ x23: .cfa -3520 + ^ x24: .cfa -3512 + ^ x25: .cfa -3504 + ^ x26: .cfa -3496 + ^ x27: .cfa -3488 + ^ x28: .cfa -3480 + ^
STACK CFI INIT d8f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d8f8 18 .cfa: sp 0 + .ra: x30
STACK CFI d8fc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI d90c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT d910 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d928 8c .cfa: sp 0 + .ra: x30
STACK CFI d92c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d938 .ra: .cfa -16 + ^
STACK CFI d97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI d980 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT d9c0 2368 .cfa: sp 0 + .ra: x30
STACK CFI d9c4 .cfa: sp 1712 +
STACK CFI d9c8 x19: .cfa -1712 + ^ x20: .cfa -1704 + ^
STACK CFI d9d8 x21: .cfa -1696 + ^ x22: .cfa -1688 + ^ x27: .cfa -1648 + ^ x28: .cfa -1640 + ^
STACK CFI da00 .ra: .cfa -1632 + ^ v10: .cfa -1600 + ^ v11: .cfa -1592 + ^ v12: .cfa -1584 + ^ v13: .cfa -1576 + ^ v14: .cfa -1568 + ^ v15: .cfa -1560 + ^ v8: .cfa -1616 + ^ v9: .cfa -1608 + ^ x23: .cfa -1680 + ^ x24: .cfa -1672 + ^ x25: .cfa -1664 + ^ x26: .cfa -1656 + ^
STACK CFI f5ac .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f5b0 .cfa: sp 1712 + .ra: .cfa -1632 + ^ v10: .cfa -1600 + ^ v11: .cfa -1592 + ^ v12: .cfa -1584 + ^ v13: .cfa -1576 + ^ v14: .cfa -1568 + ^ v15: .cfa -1560 + ^ v8: .cfa -1616 + ^ v9: .cfa -1608 + ^ x19: .cfa -1712 + ^ x20: .cfa -1704 + ^ x21: .cfa -1696 + ^ x22: .cfa -1688 + ^ x23: .cfa -1680 + ^ x24: .cfa -1672 + ^ x25: .cfa -1664 + ^ x26: .cfa -1656 + ^ x27: .cfa -1648 + ^ x28: .cfa -1640 + ^
STACK CFI INIT fd50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd60 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT fd80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd88 18 .cfa: sp 0 + .ra: x30
STACK CFI fd8c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI fd9c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT fda0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fda8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fdb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT fdc8 8c .cfa: sp 0 + .ra: x30
STACK CFI fdcc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fdd8 .ra: .cfa -16 + ^
STACK CFI fe1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI fe20 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT fe60 46a4 .cfa: sp 0 + .ra: x30
STACK CFI fe68 .cfa: sp 5456 +
STACK CFI fe6c x19: .cfa -5456 + ^ x20: .cfa -5448 + ^
STACK CFI fe7c x21: .cfa -5440 + ^ x22: .cfa -5432 + ^ x23: .cfa -5424 + ^ x24: .cfa -5416 + ^
STACK CFI fea0 .ra: .cfa -5376 + ^ v10: .cfa -5344 + ^ v11: .cfa -5336 + ^ v12: .cfa -5328 + ^ v13: .cfa -5320 + ^ v14: .cfa -5312 + ^ v15: .cfa -5304 + ^ v8: .cfa -5360 + ^ v9: .cfa -5352 + ^ x25: .cfa -5408 + ^ x26: .cfa -5400 + ^ x27: .cfa -5392 + ^ x28: .cfa -5384 + ^
STACK CFI 13cf4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13cf8 .cfa: sp 5456 + .ra: .cfa -5376 + ^ v10: .cfa -5344 + ^ v11: .cfa -5336 + ^ v12: .cfa -5328 + ^ v13: .cfa -5320 + ^ v14: .cfa -5312 + ^ v15: .cfa -5304 + ^ v8: .cfa -5360 + ^ v9: .cfa -5352 + ^ x19: .cfa -5456 + ^ x20: .cfa -5448 + ^ x21: .cfa -5440 + ^ x22: .cfa -5432 + ^ x23: .cfa -5424 + ^ x24: .cfa -5416 + ^ x25: .cfa -5408 + ^ x26: .cfa -5400 + ^ x27: .cfa -5392 + ^ x28: .cfa -5384 + ^
STACK CFI INIT 14528 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14530 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14538 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14558 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14560 18 .cfa: sp 0 + .ra: x30
STACK CFI 14564 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 14574 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 14578 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14588 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 145a0 8c .cfa: sp 0 + .ra: x30
STACK CFI 145a4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 145b0 .ra: .cfa -16 + ^
STACK CFI 145f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 145f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 14630 1434 .cfa: sp 0 + .ra: x30
STACK CFI 14634 .cfa: sp 1168 +
STACK CFI 14638 x19: .cfa -1168 + ^ x20: .cfa -1160 + ^
STACK CFI 14640 x21: .cfa -1152 + ^ x22: .cfa -1144 + ^
STACK CFI 14650 x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^
STACK CFI 14668 .ra: .cfa -1088 + ^ v10: .cfa -1056 + ^ v11: .cfa -1048 + ^ v12: .cfa -1080 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 15494 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15498 .cfa: sp 1168 + .ra: .cfa -1088 + ^ v10: .cfa -1056 + ^ v11: .cfa -1048 + ^ v12: .cfa -1080 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI INIT 15a90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15a98 18 .cfa: sp 0 + .ra: x30
STACK CFI 15a9c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 15aac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 15ab0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ac8 8c .cfa: sp 0 + .ra: x30
STACK CFI 15acc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15ad8 .ra: .cfa -16 + ^
STACK CFI 15b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15b20 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 15b60 2c3c .cfa: sp 0 + .ra: x30
STACK CFI 15b64 .cfa: sp 2176 +
STACK CFI 15b68 x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI 15b9c .ra: .cfa -2096 + ^ v10: .cfa -2064 + ^ v11: .cfa -2056 + ^ v12: .cfa -2048 + ^ v13: .cfa -2040 + ^ v14: .cfa -2088 + ^ v8: .cfa -2080 + ^ v9: .cfa -2072 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^ x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 17f2c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17f30 .cfa: sp 2176 + .ra: .cfa -2096 + ^ v10: .cfa -2064 + ^ v11: .cfa -2056 + ^ v12: .cfa -2048 + ^ v13: .cfa -2040 + ^ v14: .cfa -2088 + ^ v8: .cfa -2080 + ^ v9: .cfa -2072 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^ x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI INIT 187c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 187d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 187d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 187e0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18808 bc .cfa: sp 0 + .ra: x30
STACK CFI 1880c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18810 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 188b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 188b8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 188c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 188d0 157c .cfa: sp 0 + .ra: x30
STACK CFI 188d4 .cfa: sp 720 +
STACK CFI 188d8 x19: .cfa -720 + ^ x20: .cfa -712 + ^
STACK CFI 188e8 x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^
STACK CFI 18900 .ra: .cfa -640 + ^ v8: .cfa -632 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^ x28: .cfa -648 + ^
STACK CFI 194cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 194d0 .cfa: sp 720 + .ra: .cfa -640 + ^ v8: .cfa -632 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^ x28: .cfa -648 + ^
STACK CFI INIT 19e60 d4 .cfa: sp 0 + .ra: x30
STACK CFI 19e70 .cfa: sp 80 +
STACK CFI 19f30 .cfa: sp 0 +
STACK CFI INIT 19f38 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f68 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f80 18 .cfa: sp 0 + .ra: x30
STACK CFI 19f84 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 19f94 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5350 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5354 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5360 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 53e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 53e4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 19fa0 74c .cfa: sp 0 + .ra: x30
STACK CFI 19fa4 .cfa: sp 528 +
STACK CFI 19fa8 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 19fb0 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 19fb8 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 19fd4 .ra: .cfa -480 + ^ v10: .cfa -448 + ^ v11: .cfa -440 + ^ v12: .cfa -432 + ^ v13: .cfa -424 + ^ v14: .cfa -416 + ^ v15: .cfa -408 + ^ v8: .cfa -464 + ^ v9: .cfa -456 + ^
STACK CFI 1a60c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1a610 .cfa: sp 528 + .ra: .cfa -480 + ^ v10: .cfa -448 + ^ v11: .cfa -440 + ^ v12: .cfa -432 + ^ v13: .cfa -424 + ^ v14: .cfa -416 + ^ v15: .cfa -408 + ^ v8: .cfa -464 + ^ v9: .cfa -456 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI INIT 1a710 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a748 468 .cfa: sp 0 + .ra: x30
STACK CFI 1a74c .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1a758 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 1a768 .ra: .cfa -304 + ^
STACK CFI 1a9b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1a9c0 .cfa: sp 336 + .ra: .cfa -304 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI INIT 1abb8 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 1abbc .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 1abc4 .ra: .cfa -160 + ^
STACK CFI 1ad28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1ad30 .cfa: sp 176 + .ra: .cfa -160 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI INIT 1ad80 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad98 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ada8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1adc0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1adc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1add4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1ade0 5ec .cfa: sp 0 + .ra: x30
STACK CFI 1ade4 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 1adec x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 1adf4 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 1adfc .ra: .cfa -416 + ^
STACK CFI 1b2e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1b2f0 .cfa: sp 464 + .ra: .cfa -416 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI INIT 1b3f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b410 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 1b418 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1b42c .ra: .cfa -136 + ^ x21: .cfa -144 + ^
STACK CFI 1b4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1b500 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^
STACK CFI INIT 1b5d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1b5d4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b5e4 .ra: .cfa -64 + ^
STACK CFI 1b658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1b65c .cfa: sp 80 + .ra: .cfa -64 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^
