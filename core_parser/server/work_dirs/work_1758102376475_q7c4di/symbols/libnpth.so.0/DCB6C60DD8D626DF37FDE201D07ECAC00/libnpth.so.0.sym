MODULE Linux arm64 DCB6C60DD8D626DF37FDE201D07ECAC00 libnpth.so.0
INFO CODE_ID 0DC6B6DCD6D8DF2637FDE201D07ECAC088ACDC56
PUBLIC 1c40 0 npth_init
PUBLIC 1cb0 0 npth_getname_np
PUBLIC 1cb8 0 npth_setname_np
PUBLIC 1cc0 0 npth_create
PUBLIC 1d78 0 npth_join
PUBLIC 1dd0 0 npth_exit
PUBLIC 1de8 0 npth_mutex_lock
PUBLIC 1e38 0 npth_mutex_timedlock
PUBLIC 1e90 0 npth_rwlock_rdlock
PUBLIC 1ee0 0 npth_rwlock_timedrdlock
PUBLIC 1f38 0 npth_rwlock_wrlock
PUBLIC 1f88 0 npth_rwlock_timedwrlock
PUBLIC 1fe0 0 npth_cond_wait
PUBLIC 2010 0 npth_cond_timedwait
PUBLIC 2048 0 npth_usleep
PUBLIC 2078 0 npth_sleep
PUBLIC 20a8 0 npth_system
PUBLIC 20d8 0 npth_waitpid
PUBLIC 2110 0 npth_connect
PUBLIC 2148 0 npth_accept
PUBLIC 2180 0 npth_select
PUBLIC 21c0 0 npth_pselect
PUBLIC 2208 0 npth_read
PUBLIC 2240 0 npth_write
PUBLIC 2278 0 npth_recvmsg
PUBLIC 22b8 0 npth_sendmsg
PUBLIC 22f8 0 npth_unprotect
PUBLIC 2310 0 npth_protect
PUBLIC 2328 0 npth_is_protected
PUBLIC 2338 0 npth_clock_gettime
PUBLIC 2378 0 npth_sigev_init
PUBLIC 23c0 0 npth_sigev_add
PUBLIC 24a8 0 npth_sigev_fini
PUBLIC 24e0 0 npth_sigev_sigmask
PUBLIC 24f0 0 npth_sigev_get_pending
STACK CFI INIT 1a78 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1aa8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ae0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1aec x19: .cfa -16 + ^
STACK CFI 1b24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b30 84 .cfa: sp 0 + .ra: x30
STACK CFI 1b34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b48 x21: .cfa -16 + ^
STACK CFI 1b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1bb8 4c .cfa: sp 0 + .ra: x30
STACK CFI 1bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1be0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c08 38 .cfa: sp 0 + .ra: x30
STACK CFI 1c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c40 6c .cfa: sp 0 + .ra: x30
STACK CFI 1c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1cb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ccc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1cd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1d78 54 .cfa: sp 0 + .ra: x30
STACK CFI 1d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1da4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1dd0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1de8 4c .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1df4 x19: .cfa -32 + ^
STACK CFI 1e0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1e30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e38 54 .cfa: sp 0 + .ra: x30
STACK CFI 1e3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e44 x19: .cfa -32 + ^
STACK CFI 1e60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1e88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e90 4c .cfa: sp 0 + .ra: x30
STACK CFI 1e94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e9c x19: .cfa -32 + ^
STACK CFI 1eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1eb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1ed8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ee0 54 .cfa: sp 0 + .ra: x30
STACK CFI 1ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1eec x19: .cfa -32 + ^
STACK CFI 1f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1f30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f38 4c .cfa: sp 0 + .ra: x30
STACK CFI 1f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f44 x19: .cfa -32 + ^
STACK CFI 1f5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1f80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f88 54 .cfa: sp 0 + .ra: x30
STACK CFI 1f8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f94 x19: .cfa -32 + ^
STACK CFI 1fb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1fb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 1fd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1fe0 2c .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2008 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2010 34 .cfa: sp 0 + .ra: x30
STACK CFI 2014 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2048 2c .cfa: sp 0 + .ra: x30
STACK CFI 204c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2078 2c .cfa: sp 0 + .ra: x30
STACK CFI 207c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20a8 2c .cfa: sp 0 + .ra: x30
STACK CFI 20ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20d8 34 .cfa: sp 0 + .ra: x30
STACK CFI 20dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2110 34 .cfa: sp 0 + .ra: x30
STACK CFI 2114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2148 34 .cfa: sp 0 + .ra: x30
STACK CFI 214c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2178 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2180 3c .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 21b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 21c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2208 34 .cfa: sp 0 + .ra: x30
STACK CFI 220c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2240 34 .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2270 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2278 3c .cfa: sp 0 + .ra: x30
STACK CFI 227c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22b8 3c .cfa: sp 0 + .ra: x30
STACK CFI 22bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2310 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2328 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2338 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2348 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2360 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2378 44 .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2384 x19: .cfa -16 + ^
STACK CFI 23b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23c0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 23c4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 23cc x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 23e0 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 247c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2480 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT 24a8 38 .cfa: sp 0 + .ra: x30
STACK CFI 24ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f0 9c .cfa: sp 0 + .ra: x30
STACK CFI 24f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 24fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2514 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 251c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2564 x19: x19 x20: x20
STACK CFI 256c x23: x23 x24: x24
STACK CFI 2570 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2574 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2578 x19: x19 x20: x20
STACK CFI 257c x23: x23 x24: x24
STACK CFI 2588 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
