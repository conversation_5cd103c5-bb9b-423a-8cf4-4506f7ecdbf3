MODULE Linux arm64 7BDED558F9B163A83AD1619CA147D8710 libltdl.so.7
INFO CODE_ID 58D5DE7BB1F9A8633AD1619CA147D871088453FC
PUBLIC 2098 0 preopen_LTX_get_vtable
PUBLIC 2160 0 lt_dlpreload_default
PUBLIC 2178 0 lt_dlpreload
PUBLIC 2268 0 lt_dlpreload_open
PUBLIC 23d0 0 lt__malloc
PUBLIC 2408 0 lt__zalloc
PUBLIC 2440 0 lt__realloc
PUBLIC 2478 0 lt__memdup
PUBLIC 24b8 0 lt__strdup
PUBLIC 2540 0 lt_dlloader_add
PUBLIC 2638 0 lt_dlloader_next
PUBLIC 2650 0 lt_dlloader_get
PUBLIC 2668 0 lt_dlloader_find
PUBLIC 2690 0 lt_dlloader_remove
PUBLIC 27b0 0 lt_dladderror
PUBLIC 2840 0 lt__error_string
PUBLIC 28b8 0 lt__get_last_error
PUBLIC 28c8 0 lt__set_last_error
PUBLIC 28d8 0 lt_dlseterror
PUBLIC 2960 0 lt__alloc_die_callback
PUBLIC 3a70 0 lt_dlinit
PUBLIC 3af8 0 lt_dladvise_init
PUBLIC 3b28 0 lt_dladvise_destroy
PUBLIC 3b60 0 lt_dladvise_ext
PUBLIC 3bb0 0 lt_dladvise_resident
PUBLIC 3c00 0 lt_dladvise_local
PUBLIC 3c50 0 lt_dladvise_global
PUBLIC 3ca0 0 lt_dladvise_preload
PUBLIC 3cf0 0 lt_dlforeachfile
PUBLIC 3de0 0 lt_dlclose
PUBLIC 3f10 0 lt_dlexit
PUBLIC 4ce8 0 lt_dlopenadvise
PUBLIC 4ec8 0 lt_dlopen
PUBLIC 4ed0 0 lt_dlopenext
PUBLIC 4f60 0 lt_dlsym
PUBLIC 51f0 0 lt_dlerror
PUBLIC 5220 0 lt_dladdsearchdir
PUBLIC 5260 0 lt_dlinsertsearchdir
PUBLIC 5310 0 lt_dlsetsearchpath
PUBLIC 5380 0 lt_dlgetsearchpath
PUBLIC 5390 0 lt_dlmakeresident
PUBLIC 53d0 0 lt_dlisresident
PUBLIC 5400 0 lt_dlinterface_register
PUBLIC 5460 0 lt_dlinterface_free
PUBLIC 5488 0 lt_dlcaller_set_data
PUBLIC 5570 0 lt_dlcaller_get_data
PUBLIC 55a8 0 lt_dlgetinfo
PUBLIC 55d8 0 lt_dlhandle_iterate
PUBLIC 5660 0 lt_dlhandle_fetch
PUBLIC 56e0 0 lt_dlhandle_map
PUBLIC 5788 0 lt__slist_delete
PUBLIC 57e8 0 lt__slist_remove
PUBLIC 5890 0 lt__slist_find
PUBLIC 5918 0 lt__slist_concat
PUBLIC 5940 0 lt__slist_cons
PUBLIC 5990 0 lt__slist_tail
PUBLIC 59a8 0 lt__slist_nth
PUBLIC 59c8 0 lt__slist_length
PUBLIC 59e8 0 lt__slist_reverse
PUBLIC 5a10 0 lt__slist_foreach
PUBLIC 5a80 0 lt__slist_sort
PUBLIC 5bc0 0 lt__slist_box
PUBLIC 5bf0 0 lt__slist_unbox
PUBLIC 5c20 0 lt_strlcat
PUBLIC 5d18 0 lt_strlcpy
PUBLIC 5f00 0 dlopen_LTX_get_vtable
STACK CFI INIT 1e48 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e78 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1eb8 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ec4 x19: .cfa -16 + ^
STACK CFI 1efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f10 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1f58 84 .cfa: sp 0 + .ra: x30
STACK CFI 1f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f68 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1fc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1fe0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ff0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ff8 x23: .cfa -16 + ^
STACK CFI 200c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 206c x19: x19 x20: x20
STACK CFI 207c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2080 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2098 c8 .cfa: sp 0 + .ra: x30
STACK CFI 209c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2128 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2144 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 215c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2160 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2178 d0 .cfa: sp 0 + .ra: x30
STACK CFI 217c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2184 x21: .cfa -32 + ^
STACK CFI 2190 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 223c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2240 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2248 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2268 138 .cfa: sp 0 + .ra: x30
STACK CFI 226c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2278 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2280 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2290 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2294 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 22a4 x27: .cfa -16 + ^
STACK CFI 2344 x19: x19 x20: x20
STACK CFI 234c x23: x23 x24: x24
STACK CFI 2354 x27: x27
STACK CFI 2358 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 235c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 236c x19: x19 x20: x20
STACK CFI 2374 x23: x23 x24: x24
STACK CFI 2378 x27: x27
STACK CFI 2394 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2398 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 23a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 23a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 23d0 34 .cfa: sp 0 + .ra: x30
STACK CFI 23d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2408 38 .cfa: sp 0 + .ra: x30
STACK CFI 240c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2414 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 243c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2440 34 .cfa: sp 0 + .ra: x30
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2478 40 .cfa: sp 0 + .ra: x30
STACK CFI 247c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2484 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 24b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 24b8 2c .cfa: sp 0 + .ra: x30
STACK CFI 24bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24c4 x19: .cfa -16 + ^
STACK CFI 24e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 24e8 54 .cfa: sp 0 + .ra: x30
STACK CFI 24ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24f4 x19: .cfa -16 + ^
STACK CFI 2518 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 251c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2540 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2554 x19: .cfa -16 + ^
STACK CFI 257c x19: x19
STACK CFI 2590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2594 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 25cc x19: x19
STACK CFI 25d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 25f0 x19: x19
STACK CFI 25f4 x19: .cfa -16 + ^
STACK CFI 260c x19: x19
STACK CFI 2610 x19: .cfa -16 + ^
STACK CFI INIT 2638 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2650 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2668 28 .cfa: sp 0 + .ra: x30
STACK CFI 266c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 268c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2690 120 .cfa: sp 0 + .ra: x30
STACK CFI 2694 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 269c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 26c4 x23: .cfa -32 + ^
STACK CFI 275c x19: x19 x20: x20
STACK CFI 2764 x23: x23
STACK CFI 2768 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 276c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 2770 x19: x19 x20: x20
STACK CFI 2774 x23: x23
STACK CFI 2780 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2784 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 2798 x19: x19 x20: x20
STACK CFI 279c x23: x23
STACK CFI INIT 27b0 90 .cfa: sp 0 + .ra: x30
STACK CFI 27b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2818 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2840 78 .cfa: sp 0 + .ra: x30
STACK CFI 2844 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2868 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2870 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28d8 84 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2908 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 290c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2938 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2958 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2960 18 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2974 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2978 108 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2984 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a80 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2a84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2aa4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2bac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2c0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c58 174 .cfa: sp 0 + .ra: x30
STACK CFI 2c5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c70 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2dd0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2dd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ddc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2de4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e44 x23: .cfa -16 + ^
STACK CFI 2e70 x23: x23
STACK CFI 2ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ea4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ea8 x23: x23
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ec0 90 .cfa: sp 0 + .ra: x30
STACK CFI 2ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ed8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2edc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ef8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2efc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f10 x21: .cfa -16 + ^
STACK CFI 2f30 x19: x19 x20: x20
STACK CFI 2f34 x21: x21
STACK CFI 2f4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f50 10c .cfa: sp 0 + .ra: x30
STACK CFI 2f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f60 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3060 84 .cfa: sp 0 + .ra: x30
STACK CFI 3064 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 30a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 30c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 30e8 268 .cfa: sp 0 + .ra: x30
STACK CFI 30ec .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 30f4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3104 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3120 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 312c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 31b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3350 70 .cfa: sp 0 + .ra: x30
STACK CFI 3354 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 335c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 336c x21: .cfa -16 + ^
STACK CFI 3388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 338c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 33c0 268 .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 33cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 33d8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3424 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 343c x25: .cfa -48 + ^
STACK CFI 349c x23: x23 x24: x24
STACK CFI 34a0 x25: x25
STACK CFI 34a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 34b8 x23: x23 x24: x24
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 34fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 3508 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 3540 x23: x23 x24: x24
STACK CFI 3544 x25: x25
STACK CFI 3548 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 354c x23: x23 x24: x24
STACK CFI 3550 x25: x25
STACK CFI 3578 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 357c x25: .cfa -48 + ^
STACK CFI 3580 x23: x23 x24: x24 x25: x25
STACK CFI 35a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 35a8 x25: .cfa -48 + ^
STACK CFI 35ac x23: x23 x24: x24 x25: x25
STACK CFI 35b0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 35b4 x25: .cfa -48 + ^
STACK CFI 3600 x25: x25
STACK CFI 3624 x25: .cfa -48 + ^
STACK CFI INIT 3628 380 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3634 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3640 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 365c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 36a4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 372c x27: x27 x28: x28
STACK CFI 3760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3764 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 38dc x27: x27 x28: x28
STACK CFI 38e4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 38f8 x27: x27 x28: x28
STACK CFI 3904 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 392c x27: x27 x28: x28
STACK CFI 3950 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3954 x27: x27 x28: x28
STACK CFI 3958 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 395c x27: x27 x28: x28
STACK CFI 3980 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 39a8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 39ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 39b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3a70 88 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3acc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ad0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3af8 30 .cfa: sp 0 + .ra: x30
STACK CFI 3afc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b04 x19: .cfa -16 + ^
STACK CFI 3b24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b28 38 .cfa: sp 0 + .ra: x30
STACK CFI 3b30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b38 x19: .cfa -16 + ^
STACK CFI 3b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b60 4c .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3bb0 4c .cfa: sp 0 + .ra: x30
STACK CFI 3bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c00 4c .cfa: sp 0 + .ra: x30
STACK CFI 3c24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c50 4c .cfa: sp 0 + .ra: x30
STACK CFI 3c74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ca0 4c .cfa: sp 0 + .ra: x30
STACK CFI 3cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3cf0 ec .cfa: sp 0 + .ra: x30
STACK CFI 3cf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cfc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3d3c x21: .cfa -32 + ^
STACK CFI 3d64 x21: x21
STACK CFI 3d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 3dd8 x21: x21
STACK CFI INIT 3de0 12c .cfa: sp 0 + .ra: x30
STACK CFI 3de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3df4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f10 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fa8 x19: x19 x20: x20
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3fc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 403c x19: x19 x20: x20
STACK CFI 4044 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40c8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 40cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 414c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 416c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4170 b74 .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 417c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 41a0 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 41a4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 41a8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 4240 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4520 x27: x27 x28: x28
STACK CFI 4550 x19: x19 x20: x20
STACK CFI 4558 x23: x23 x24: x24
STACK CFI 455c x25: x25 x26: x26
STACK CFI 4560 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4564 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 4568 x27: x27 x28: x28
STACK CFI 45e4 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 45ec x27: x27 x28: x28
STACK CFI 46a8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 46fc x27: x27 x28: x28
STACK CFI 4704 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4720 x27: x27 x28: x28
STACK CFI 4728 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4834 x27: x27 x28: x28
STACK CFI 4838 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 490c x27: x27 x28: x28
STACK CFI 4920 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4990 x27: x27 x28: x28
STACK CFI 4998 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4aa0 x27: x27 x28: x28
STACK CFI 4aa8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4c3c x27: x27 x28: x28
STACK CFI 4c40 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4c60 x27: x27 x28: x28
STACK CFI 4c84 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4c88 x27: x27 x28: x28
STACK CFI 4c8c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4cb4 x27: x27 x28: x28
STACK CFI 4cd8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 4ce8 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4cec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4cf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d80 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4d8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4df4 x23: x23 x24: x24
STACK CFI 4df8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4dfc x23: x23 x24: x24
STACK CFI 4e00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4e0c x25: .cfa -32 + ^
STACK CFI 4e28 x23: x23 x24: x24
STACK CFI 4e2c x25: x25
STACK CFI 4e34 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: x25
STACK CFI 4e48 x23: x23 x24: x24
STACK CFI 4e60 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4e64 x25: x25
STACK CFI 4eb4 x23: x23 x24: x24
STACK CFI 4ec0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4ec4 x25: .cfa -32 + ^
STACK CFI INIT 4ec8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ed0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4edc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ef8 x21: .cfa -32 + ^
STACK CFI 4f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4f60 270 .cfa: sp 0 + .ra: x30
STACK CFI 4f64 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4f6c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4f78 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4f90 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4f9c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 507c x25: x25 x26: x26
STACK CFI 50a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 50a8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 5124 x25: x25 x26: x26
STACK CFI 5128 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5188 x25: x25 x26: x26
STACK CFI 51b0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 51c4 x25: x25 x26: x26
STACK CFI 51cc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 51d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 51d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 51f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51fc x19: .cfa -16 + ^
STACK CFI 5218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5220 40 .cfa: sp 0 + .ra: x30
STACK CFI 523c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5254 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5260 ac .cfa: sp 0 + .ra: x30
STACK CFI 5264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 526c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5280 x21: .cfa -16 + ^
STACK CFI 52b0 x21: x21
STACK CFI 52c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 52cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 52e4 x21: x21
STACK CFI 52e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 52ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5310 70 .cfa: sp 0 + .ra: x30
STACK CFI 5314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 531c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 534c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5350 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 536c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5370 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 537c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5380 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 3c .cfa: sp 0 + .ra: x30
STACK CFI 53b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 53c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 53d0 30 .cfa: sp 0 + .ra: x30
STACK CFI 53e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 53fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5400 60 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 540c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5418 x21: .cfa -16 + ^
STACK CFI 544c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5460 28 .cfa: sp 0 + .ra: x30
STACK CFI 5464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 546c x19: .cfa -16 + ^
STACK CFI 5484 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5488 e4 .cfa: sp 0 + .ra: x30
STACK CFI 548c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5494 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 54a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 5528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 552c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5570 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55a8 2c .cfa: sp 0 + .ra: x30
STACK CFI 55b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55d8 84 .cfa: sp 0 + .ra: x30
STACK CFI 55dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 562c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5660 80 .cfa: sp 0 + .ra: x30
STACK CFI 5664 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 566c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5678 x21: .cfa -16 + ^
STACK CFI 56b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 56bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 56e0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 56e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 56f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 574c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5750 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5764 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5788 60 .cfa: sp 0 + .ra: x30
STACK CFI 578c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5794 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 57c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 57e8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 580c x21: .cfa -16 + ^
STACK CFI 5828 x21: x21
STACK CFI 5838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 583c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5858 x21: x21
STACK CFI 5864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5868 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 588c x21: .cfa -16 + ^
STACK CFI INIT 5890 88 .cfa: sp 0 + .ra: x30
STACK CFI 5894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 58dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 58e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 58f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 58f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5918 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5940 50 .cfa: sp 0 + .ra: x30
STACK CFI 5968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5990 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 59a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 59c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 59e8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a10 6c .cfa: sp 0 + .ra: x30
STACK CFI 5a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a20 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5a58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5a80 13c .cfa: sp 0 + .ra: x30
STACK CFI 5a84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5a98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5ab0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5b7c x19: x19 x20: x20
STACK CFI 5ba0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ba4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 5ba8 x19: x19 x20: x20
STACK CFI 5bb8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT 5bc0 2c .cfa: sp 0 + .ra: x30
STACK CFI 5bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bcc x19: .cfa -16 + ^
STACK CFI 5be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5bf0 2c .cfa: sp 0 + .ra: x30
STACK CFI 5bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bfc x19: .cfa -16 + ^
STACK CFI 5c18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c20 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c30 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d18 fc .cfa: sp 0 + .ra: x30
STACK CFI 5d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5e18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e28 5c .cfa: sp 0 + .ra: x30
STACK CFI 5e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e3c x19: .cfa -16 + ^
STACK CFI 5e68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e88 30 .cfa: sp 0 + .ra: x30
STACK CFI 5e8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ea0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ea4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5eb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5eb8 48 .cfa: sp 0 + .ra: x30
STACK CFI 5ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ecc x19: .cfa -16 + ^
STACK CFI 5ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5ee8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f00 bc .cfa: sp 0 + .ra: x30
STACK CFI 5f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
