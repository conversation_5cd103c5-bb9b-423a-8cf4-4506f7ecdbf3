MODULE Linux arm64 37DB67E13D02E94B5092ABBDD028B8D20 libebl_i386.so
INFO CODE_ID E167DB37023D4BE95092ABBDD028B8D212F3F563
PUBLIC 1c90 0 i386_init
STACK CFI INIT 1aa8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b18 48 .cfa: sp 0 + .ra: x30
STACK CFI 1b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b24 x19: .cfa -16 + ^
STACK CFI 1b5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b68 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b98 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bf0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1bf4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1bfc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c68 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1c90 108 .cfa: sp 0 + .ra: x30
STACK CFI 1c9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1da8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc8 70 .cfa: sp 0 + .ra: x30
STACK CFI 1dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ddc x19: .cfa -16 + ^
STACK CFI 1e10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e38 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 1e3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1e48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1e58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1f40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2130 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2158 210 .cfa: sp 0 + .ra: x30
STACK CFI 215c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2164 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2178 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 218c x23: .cfa -96 + ^
STACK CFI 2234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2238 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2368 2a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2608 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2638 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2678 144 .cfa: sp 0 + .ra: x30
STACK CFI 267c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2684 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2694 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 26ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2714 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 273c x25: .cfa -64 + ^
STACK CFI 2770 x25: x25
STACK CFI 2774 x25: .cfa -64 + ^
STACK CFI 27b0 x25: x25
STACK CFI 27b8 x25: .cfa -64 + ^
STACK CFI INIT 27c0 114 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28d8 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2970 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a10 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2ac0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2ae8 90 .cfa: sp 0 + .ra: x30
STACK CFI 2aec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b78 158 .cfa: sp 0 + .ra: x30
STACK CFI 2b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b88 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b98 x21: .cfa -16 + ^
STACK CFI 2c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2cac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2cd0 94 .cfa: sp 0 + .ra: x30
STACK CFI 2cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d68 94 .cfa: sp 0 + .ra: x30
STACK CFI 2d80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e00 94 .cfa: sp 0 + .ra: x30
STACK CFI 2e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2e98 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ea8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f40 10c .cfa: sp 0 + .ra: x30
STACK CFI 2f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f58 x21: .cfa -16 + ^
STACK CFI 2f64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2fd4 x19: x19 x20: x20
STACK CFI 2fe0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2fe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ff0 x19: x19 x20: x20
STACK CFI 2ff8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3004 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3050 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3060 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3070 100 .cfa: sp 0 + .ra: x30
STACK CFI 3074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3100 x19: x19 x20: x20
STACK CFI 3104 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3108 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3110 x19: x19 x20: x20
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3144 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3148 x19: x19 x20: x20
STACK CFI 316c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3170 dc .cfa: sp 0 + .ra: x30
STACK CFI 3174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3198 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3204 x19: x19 x20: x20
STACK CFI 3208 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 320c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3214 x19: x19 x20: x20
STACK CFI 3218 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 321c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3248 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3250 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32a8 fc .cfa: sp 0 + .ra: x30
STACK CFI 32ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 32ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 337c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3380 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 33a8 108 .cfa: sp 0 + .ra: x30
STACK CFI 3488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 34b0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3570 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3598 c4 .cfa: sp 0 + .ra: x30
STACK CFI 359c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3628 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3638 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3660 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3664 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3670 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 36ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 36fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3700 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3728 e0 .cfa: sp 0 + .ra: x30
STACK CFI 372c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3738 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3808 c8 .cfa: sp 0 + .ra: x30
STACK CFI 38a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 38d0 554 .cfa: sp 0 + .ra: x30
STACK CFI 38d4 .cfa: sp 176 +
STACK CFI 38d8 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 38e0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3904 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 39e0 x27: .cfa -80 + ^
STACK CFI 3a3c x27: x27
STACK CFI 3ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3ab8 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 3adc x27: .cfa -80 + ^
STACK CFI 3bd8 x27: x27
STACK CFI 3bdc x27: .cfa -80 + ^
STACK CFI 3c00 x27: x27
STACK CFI 3c14 x27: .cfa -80 + ^
STACK CFI 3c60 x27: x27
STACK CFI 3d88 x27: .cfa -80 + ^
STACK CFI 3d90 x27: x27
STACK CFI 3e20 x27: .cfa -80 + ^
STACK CFI INIT 3e28 13c .cfa: sp 0 + .ra: x30
STACK CFI 3e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e38 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ee0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f68 180 .cfa: sp 0 + .ra: x30
STACK CFI 3f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4008 x19: x19 x20: x20
STACK CFI 400c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4010 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 405c x19: x19 x20: x20
STACK CFI 4060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4078 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 409c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 40e8 154 .cfa: sp 0 + .ra: x30
STACK CFI 40ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4110 x21: .cfa -16 + ^
STACK CFI 4194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4198 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 41f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 420c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4210 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4240 e0 .cfa: sp 0 + .ra: x30
STACK CFI 42f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4320 214 .cfa: sp 0 + .ra: x30
STACK CFI 43ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4440 x19: x19 x20: x20
STACK CFI 4444 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4494 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 449c x19: x19 x20: x20
STACK CFI 44a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 44dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4508 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 450c x19: x19 x20: x20
STACK CFI 4530 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 4538 214 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4658 x19: x19 x20: x20
STACK CFI 465c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 46ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 46b4 x19: x19 x20: x20
STACK CFI 46b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 46f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4724 x19: x19 x20: x20
STACK CFI 4748 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 4750 218 .cfa: sp 0 + .ra: x30
STACK CFI 4754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47bc x19: x19 x20: x20
STACK CFI 47c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48c8 x19: x19 x20: x20
STACK CFI 48cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 48d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 48f4 x19: x19 x20: x20
STACK CFI 48f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 48fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4934 x19: x19 x20: x20
STACK CFI 493c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 4968 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4978 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4988 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b50 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4d08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4d30 218 .cfa: sp 0 + .ra: x30
STACK CFI 4d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4da0 x19: x19 x20: x20
STACK CFI 4da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4e28 x19: x19 x20: x20
STACK CFI 4e2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4e4c x21: .cfa -16 + ^
STACK CFI 4e8c x21: x21
STACK CFI 4e98 x19: x19 x20: x20
STACK CFI 4e9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ea0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4ed4 x19: x19 x20: x20
STACK CFI 4ed8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4edc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4f44 x21: .cfa -16 + ^
STACK CFI INIT 4f48 220 .cfa: sp 0 + .ra: x30
STACK CFI 4f4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4fa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4fa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5168 268 .cfa: sp 0 + .ra: x30
STACK CFI 516c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 51c0 x19: x19 x20: x20
STACK CFI 51cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5244 x19: x19 x20: x20
STACK CFI 5248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 524c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 531c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5320 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5360 x19: x19 x20: x20
STACK CFI 53cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 53d0 17e4 .cfa: sp 0 + .ra: x30
STACK CFI 53d4 .cfa: sp 976 +
STACK CFI 53e8 .ra: .cfa -968 + ^ x29: .cfa -976 + ^
STACK CFI 5404 x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 5414 x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 5a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5a48 .cfa: sp 976 + .ra: .cfa -968 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^ x29: .cfa -976 + ^
