MODULE Linux arm64 05696862C70A9E5C2D5F3F4331D939C10 libencoder_interface.so
INFO CODE_ID 626869050AC75C9E2D5F3F4331D939C1
PUBLIC c608 0 _init
PUBLIC d190 0 _GLOBAL__sub_I_encoder_factory.cpp
PUBLIC d1d0 0 __static_initialization_and_destruction_0(int, int) [clone .constprop.0]
PUBLIC d4f0 0 _GLOBAL__sub_I_h265_init_config_parser.cpp
PUBLIC d4f4 0 call_weak_fn
PUBLIC d508 0 deregister_tm_clones
PUBLIC d538 0 register_tm_clones
PUBLIC d574 0 __do_global_dtors_aux
PUBLIC d5c4 0 frame_dummy
PUBLIC d5d0 0 lios::encoder::EncoderFactory::CreateEncoderImpl(lios::encoder::EncoderType, std::function<void (unsigned char const*, unsigned int, std::any const&)>&&, std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&)
PUBLIC d7a0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderVP9Nvmedia, std::allocator<lios::encoder::EncoderVP9Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d7b0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH265Nvmedia, std::allocator<lios::encoder::EncoderH265Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d7c0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH264Nvmedia, std::allocator<lios::encoder::EncoderH264Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d7d0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH264Nvmedia, std::allocator<lios::encoder::EncoderH264Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC d830 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH265Nvmedia, std::allocator<lios::encoder::EncoderH265Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC d890 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderVP9Nvmedia, std::allocator<lios::encoder::EncoderVP9Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC d8f0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderVP9Nvmedia, std::allocator<lios::encoder::EncoderVP9Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d910 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH265Nvmedia, std::allocator<lios::encoder::EncoderH265Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d930 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH264Nvmedia, std::allocator<lios::encoder::EncoderH264Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d950 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH264Nvmedia, std::allocator<lios::encoder::EncoderH264Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d960 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH265Nvmedia, std::allocator<lios::encoder::EncoderH265Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d970 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderVP9Nvmedia, std::allocator<lios::encoder::EncoderVP9Nvmedia>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d980 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderVP9Nvmedia, std::allocator<lios::encoder::EncoderVP9Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC d990 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH265Nvmedia, std::allocator<lios::encoder::EncoderH265Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC d9a0 0 std::_Sp_counted_ptr_inplace<lios::encoder::EncoderH264Nvmedia, std::allocator<lios::encoder::EncoderH264Nvmedia>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC d9b0 0 lios::encoder::GetEncoderBufAttrList(linvs::buf::BufAttrList&, NvMediaEncoderInstanceId)
PUBLIC da80 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::encoder::EncoderCommonNvMedia::Start()::{lambda()#1}> > >::~_State_impl()
PUBLIC daa0 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::encoder::EncoderCommonNvMedia::Start()::{lambda()#1}> > >::~_State_impl()
PUBLIC dae0 0 lios::encoder::EncoderCommonNvMedia::Stop()
PUBLIC db30 0 lios::encoder::EncoderCommonNvMedia::Start()
PUBLIC dbf0 0 lios::encoder::EncoderCommonNvMedia::SetOptions(int, void*, int)
PUBLIC dc90 0 lios::encoder::EncoderCommonNvMedia::PopImageInfo()
PUBLIC dda0 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::encoder::EncoderCommonNvMedia::Start()::{lambda()#1}> > >::_M_run()
PUBLIC e0b0 0 lios::encoder::EncoderCommonNvMedia::InitSyncCtx()
PUBLIC e4a0 0 lios::encoder::EncoderCommonNvMedia::Init()
PUBLIC e630 0 lios::encoder::EncoderCommonNvMedia::FeedFrameImpl(void*, std::any const&)
PUBLIC e7e0 0 lios::encoder::EncoderCommonNvMedia::EncoderCommonNvMedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&, NvMediaIEPType, void const*, void const*, void const*, std::function<void (unsigned char const*, unsigned int, std::any const&)>&&)
PUBLIC eb50 0 lios::encoder::EncoderCommonNvMedia::~EncoderCommonNvMedia()
PUBLIC ec90 0 lios::encoder::EncoderCommonNvMedia::~EncoderCommonNvMedia()
PUBLIC ecc0 0 lios::encoder::EncoderCommonNvMedia::GetOptions(int, void*, int*)
PUBLIC ecd0 0 void std::deque<std::any, std::allocator<std::any> >::_M_push_back_aux<std::any const&>(std::any const&)
PUBLIC eef0 0 std::deque<std::any, std::allocator<std::any> >::_M_destroy_data_aux(std::_Deque_iterator<std::any, std::any&, std::any*>, std::_Deque_iterator<std::any, std::any&, std::any*>)
PUBLIC f080 0 std::deque<std::any, std::allocator<std::any> >::~deque()
PUBLIC f100 0 lios::encoder::EncoderH264Nvmedia::SetOptions(int, void*, int)
PUBLIC f150 0 lios::encoder::EncoderH264Nvmedia::EncoderH264Nvmedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&, std::function<void (unsigned char const*, unsigned int, std::any const&)>&&)
PUBLIC f1f0 0 lios::encoder::EncoderH264Nvmedia::GetOptions(int, void*, int*)
PUBLIC f200 0 lios::encoder::EncoderH264Nvmedia::InitConfigParser(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, bool)
PUBLIC f210 0 lios::encoder::EncoderH264Nvmedia::update_next_pic()
PUBLIC f220 0 lios::encoder::EncoderH264Nvmedia::correct_bitstream(unsigned char const*, unsigned int, unsigned char**, unsigned int*)
PUBLIC f230 0 lios::encoder::EncoderH264Nvmedia::~EncoderH264Nvmedia()
PUBLIC f250 0 lios::encoder::EncoderH264Nvmedia::~EncoderH264Nvmedia()
PUBLIC f290 0 lios::encoder::EncoderH265Nvmedia::InitConfigParser(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, bool)
PUBLIC f300 0 lios::encoder::EncoderH265Nvmedia::EncoderH265Nvmedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&, std::function<void (unsigned char const*, unsigned int, std::any const&)>&&)
PUBLIC f430 0 lios::encoder::EncoderH265Nvmedia::SetOptions(int, void*, int)
PUBLIC f520 0 lios::encoder::EncoderH265Nvmedia::GetOptions(int, void*, int*)
PUBLIC f530 0 lios::encoder::EncoderH265Nvmedia::update_next_pic()
PUBLIC f540 0 lios::encoder::EncoderH265Nvmedia::correct_bitstream(unsigned char const*, unsigned int, unsigned char**, unsigned int*)
PUBLIC f550 0 lios::encoder::EncoderH265Nvmedia::~EncoderH265Nvmedia()
PUBLIC f570 0 lios::encoder::EncoderH265Nvmedia::~EncoderH265Nvmedia()
PUBLIC f5b0 0 lios::encoder::EncoderVP9Nvmedia::SetOptions(int, void*, int)
PUBLIC f600 0 lios::encoder::EncoderVP9Nvmedia::EncoderVP9Nvmedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&, std::function<void (unsigned char const*, unsigned int, std::any const&)>&&)
PUBLIC f6b0 0 lios::encoder::EncoderVP9Nvmedia::GetOptions(int, void*, int*)
PUBLIC f6c0 0 lios::encoder::EncoderVP9Nvmedia::InitConfigParser(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, bool)
PUBLIC f6d0 0 lios::encoder::EncoderVP9Nvmedia::update_next_pic()
PUBLIC f6e0 0 lios::encoder::EncoderVP9Nvmedia::correct_bitstream(unsigned char const*, unsigned int, unsigned char**, unsigned int*)
PUBLIC f6f0 0 lios::encoder::EncoderVP9Nvmedia::~EncoderVP9Nvmedia()
PUBLIC f710 0 lios::encoder::EncoderVP9Nvmedia::~EncoderVP9Nvmedia()
PUBLIC f750 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC f830 0 lios::encoder::NvMediaH265ConfigParser::PrintConfig(NvMediaEncodeInitializeParamsH265 const&, NvMediaEncodeConfigH265 const&, bool)
PUBLIC 10040 0 YAML::detail::node_data::get<char [7]>(char const (&) [7], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}::operator()(std::pair<YAML::detail::node*, YAML::detail::node*>) const [clone .isra.0]
PUBLIC 10360 0 lios::encoder::NvMediaH265ConfigParser::ParseParams(YAML::Node const&, NvMediaEncodeInitializeParamsH265&)
PUBLIC 10890 0 lios::encoder::NvMediaH265ConfigParser::ParseH265VUIParams(YAML::Node const&, NvMediaEncodeConfigH265VUIParams*)
PUBLIC 10ef0 0 lios::encoder::NvMediaH265ConfigParser::ParseQP(YAML::Node const&, NvMediaEncodeQP&)
PUBLIC 11020 0 lios::encoder::NvMediaH265ConfigParser::ParseRCParams(YAML::Node const&, NvMediaEncodeRCParams&)
PUBLIC 11d70 0 lios::encoder::NvMediaH265ConfigParser::ParseConfig(YAML::Node const&, NvMediaEncodeConfigH265&)
PUBLIC 124b0 0 lios::encoder::NvMediaH265ConfigParser::ParseNode(YAML::Node const&, NvMediaEncodeInitializeParamsH265&, NvMediaEncodeConfigH265&)
PUBLIC 12800 0 lios::encoder::NvMediaH265ConfigParser::ParseFromFile(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, NvMediaEncodeInitializeParamsH265&, NvMediaEncodeConfigH265&)
PUBLIC 12970 0 lios::encoder::NvMediaH265ConfigParser::ParseFromString(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, NvMediaEncodeInitializeParamsH265&, NvMediaEncodeConfigH265&)
PUBLIC 12ae0 0 std::_Sp_counted_ptr<YAML::detail::memory_holder*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 12af0 0 std::_Sp_counted_ptr<YAML::detail::memory*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 12b00 0 std::_Sp_counted_ptr<YAML::detail::memory_holder*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 12b10 0 std::_Sp_counted_ptr<YAML::detail::memory*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 12b20 0 std::_Sp_counted_ptr<YAML::detail::memory_holder*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 12b30 0 std::_Sp_counted_ptr<YAML::detail::memory_holder*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 12b40 0 std::_Sp_counted_ptr<YAML::detail::memory*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 12b50 0 std::_Sp_counted_ptr<YAML::detail::memory*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 12b60 0 YAML::TypedBadConversion<unsigned short>::~TypedBadConversion()
PUBLIC 12b80 0 YAML::TypedBadConversion<unsigned short>::~TypedBadConversion()
PUBLIC 12bc0 0 YAML::TypedBadConversion<unsigned char>::~TypedBadConversion()
PUBLIC 12be0 0 YAML::TypedBadConversion<unsigned char>::~TypedBadConversion()
PUBLIC 12c20 0 YAML::TypedBadConversion<unsigned int>::~TypedBadConversion()
PUBLIC 12c40 0 YAML::TypedBadConversion<unsigned int>::~TypedBadConversion()
PUBLIC 12c80 0 YAML::TypedBadConversion<bool>::~TypedBadConversion()
PUBLIC 12ca0 0 YAML::TypedBadConversion<bool>::~TypedBadConversion()
PUBLIC 12ce0 0 YAML::TypedBadConversion<YAML::Node>::~TypedBadConversion()
PUBLIC 12d00 0 YAML::TypedBadConversion<YAML::Node>::~TypedBadConversion()
PUBLIC 12d40 0 YAML::TypedBadConversion<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >::~TypedBadConversion()
PUBLIC 12d60 0 YAML::TypedBadConversion<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >::~TypedBadConversion()
PUBLIC 12da0 0 YAML::TypedBadConversion<signed char>::~TypedBadConversion()
PUBLIC 12dc0 0 YAML::TypedBadConversion<signed char>::~TypedBadConversion()
PUBLIC 12e00 0 YAML::TypedBadConversion<short>::~TypedBadConversion()
PUBLIC 12e20 0 YAML::TypedBadConversion<short>::~TypedBadConversion()
PUBLIC 12e60 0 std::_Sp_counted_ptr<YAML::detail::memory_holder*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 12f40 0 YAML::Node::~Node()
PUBLIC 13020 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 13080 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 130e0 0 YAML::ErrorMsg::BAD_SUBSCRIPT_WITH_KEY[abi:cxx11](char const*)
PUBLIC 13440 0 std::_Rb_tree<std::shared_ptr<YAML::detail::node>, std::shared_ptr<YAML::detail::node>, std::_Identity<std::shared_ptr<YAML::detail::node> >, std::less<std::shared_ptr<YAML::detail::node> >, std::allocator<std::shared_ptr<YAML::detail::node> > >::_M_erase(std::_Rb_tree_node<std::shared_ptr<YAML::detail::node> >*)
PUBLIC 13580 0 std::_Sp_counted_ptr<YAML::detail::memory*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 136d0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 13790 0 std::_Rb_tree<YAML::detail::node*, YAML::detail::node*, std::_Identity<YAML::detail::node*>, YAML::detail::node::less, std::allocator<YAML::detail::node*> >::_M_erase(std::_Rb_tree_node<YAML::detail::node*>*)
PUBLIC 137e0 0 YAML::detail::node::mark_defined()
PUBLIC 13880 0 std::pair<std::_Rb_tree_iterator<YAML::detail::node*>, bool> std::_Rb_tree<YAML::detail::node*, YAML::detail::node*, std::_Identity<YAML::detail::node*>, YAML::detail::node::less, std::allocator<YAML::detail::node*> >::_M_insert_unique<YAML::detail::node*>(YAML::detail::node*&&)
PUBLIC 139f0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> >*)
PUBLIC 13a70 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncPreset, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> > >::~map()
PUBLIC 13af0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> >*)
PUBLIC 13b70 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncodeParamsRCMode, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> > >::~map()
PUBLIC 13bf0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> >*)
PUBLIC 13c70 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncodeH264SPSPPSRepeatMode, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> > >::~map()
PUBLIC 13cf0 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag)
PUBLIC 13dd0 0 YAML::Exception::build_what(YAML::Mark const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 14180 0 YAML::Exception::Exception(YAML::Mark const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 142f0 0 YAML::BadConversion::BadConversion(YAML::Mark const&)
PUBLIC 144c0 0 YAML::InvalidNode::InvalidNode(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 149d0 0 YAML::Node::Mark() const
PUBLIC 14a90 0 YAML::Node::EnsureNodeExists() const
PUBLIC 14cb0 0 YAML::detail::node_data::get<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}::operator()(std::pair<YAML::detail::node*, YAML::detail::node*>) const [clone .isra.0]
PUBLIC 150f0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 15270 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncPreset, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncPreset> > const&)
PUBLIC 154c0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 15640 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncodeParamsRCMode, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeParamsRCMode> > const&)
PUBLIC 15890 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 15a10 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, NvMediaEncodeH264SPSPPSRepeatMode, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, NvMediaEncodeH264SPSPPSRepeatMode> > const&)
PUBLIC 15c60 0 YAML::BadSubscript::BadSubscript<char [9]>(YAML::Mark const&, char const (&) [9])
PUBLIC 15e10 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [7]>(char const (&) [7], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [7]>(char const (&) [7], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 15fb0 0 YAML::Node const YAML::Node::operator[]<char [7]>(char const (&) [7]) const
PUBLIC 16770 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [9]>(char const (&) [9], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [9]>(char const (&) [9], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 16910 0 YAML::Node const YAML::Node::operator[]<char [9]>(char const (&) [9]) const
PUBLIC 17060 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [18]>(char const (&) [18], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [18]>(char const (&) [18], std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 17200 0 YAML::Node const YAML::Node::operator[]<char [18]>(char const (&) [18]) const
PUBLIC 179c0 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [9]>(char const (&) [9], std::shared_ptr<YAML::detail::memory_holder>)::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [9]>(char const (&) [9], std::shared_ptr<YAML::detail::memory_holder>)::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 17b60 0 YAML::Node YAML::Node::operator[]<char [9]>(char const (&) [9])
PUBLIC 18490 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [8]>(char const (&) [8], std::shared_ptr<YAML::detail::memory_holder>)::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*>*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<char [8]>(char const (&) [8], std::shared_ptr<YAML::detail::memory_holder>)::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 18630 0 YAML::Node YAML::Node::operator[]<char [8]>(char const (&) [8])
PUBLIC 18fd0 0 __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > > std::__find_if<__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}> >(__gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__normal_iterator<std::pair<YAML::detail::node*, YAML::detail::node*> const*, std::vector<std::pair<YAML::detail::node*, YAML::detail::node*>, std::allocator<std::pair<YAML::detail::node*, YAML::detail::node*> > > >, __gnu_cxx::__ops::_Iter_pred<YAML::detail::node_data::get<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::shared_ptr<YAML::detail::memory_holder>) const::{lambda(std::pair<YAML::detail::node*, YAML::detail::node*>)#1}>, std::random_access_iterator_tag)
PUBLIC 19fb0 0 YAML::Node const YAML::Node::operator[]<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) const
PUBLIC 1a7d0 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<unsigned short>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short&)
PUBLIC 1acf0 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<unsigned char>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned char&)
PUBLIC 1b160 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<unsigned int>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned int&)
PUBLIC 1b5d0 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<bool>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, bool&)
PUBLIC 1b970 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<YAML::Node>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, YAML::Node&)
PUBLIC 1bfb0 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&)
PUBLIC 1c3e0 0 bool lios::encoder::NvMediaH265ConfigParser::SafeGet<short>(YAML::Node const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, short&)
PUBLIC 1c8f0 0 _fini
STACK CFI INIT d508 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d538 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d574 50 .cfa: sp 0 + .ra: x30
STACK CFI d584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d58c x19: .cfa -16 + ^
STACK CFI d5bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d5c4 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7d0 60 .cfa: sp 0 + .ra: x30
STACK CFI d7d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d7e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d82c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d830 60 .cfa: sp 0 + .ra: x30
STACK CFI d834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d844 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d88c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d890 60 .cfa: sp 0 + .ra: x30
STACK CFI d894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d8a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d8ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d8f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT d910 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT d930 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT d950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d970 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d9a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d5d0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI d5d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d5e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d5ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI d620 x23: .cfa -16 + ^
STACK CFI d698 x23: x23
STACK CFI d69c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d6a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI d6a8 x23: .cfa -16 + ^
STACK CFI d6e0 x23: x23
STACK CFI d6e8 x23: .cfa -16 + ^
STACK CFI d768 x23: x23
STACK CFI d76c x23: .cfa -16 + ^
STACK CFI INIT d190 3c .cfa: sp 0 + .ra: x30
STACK CFI d194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d19c x19: .cfa -16 + ^
STACK CFI d1c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d9b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI d9b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI d9bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI da50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI da54 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT ecc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT da80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT daa0 38 .cfa: sp 0 + .ra: x30
STACK CFI daa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dab8 x19: .cfa -16 + ^
STACK CFI dad4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dae0 44 .cfa: sp 0 + .ra: x30
STACK CFI dae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI daf0 x19: .cfa -16 + ^
STACK CFI db18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT db30 c0 .cfa: sp 0 + .ra: x30
STACK CFI db34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI db3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dbc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dbc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT dbf0 94 .cfa: sp 0 + .ra: x30
STACK CFI dbf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dc00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI dc38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI dc68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI dc80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT dc90 108 .cfa: sp 0 + .ra: x30
STACK CFI dc94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dc9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dcb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI dd20 x21: x21 x22: x22
STACK CFI dd24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dd28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI dd30 x21: x21 x22: x22
STACK CFI dd34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dd38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI dd44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dd54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT dda0 308 .cfa: sp 0 + .ra: x30
STACK CFI dda4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI ddac x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI ddc8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI ddd4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI dde0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI ddec x27: .cfa -80 + ^
STACK CFI df14 x21: x21 x22: x22
STACK CFI df18 x23: x23 x24: x24
STACK CFI df1c x25: x25 x26: x26
STACK CFI df20 x27: x27
STACK CFI df40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI df44 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI INIT e0b0 3f0 .cfa: sp 0 + .ra: x30
STACK CFI e0b4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI e0bc x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI e0cc x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI e0d8 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI e0e8 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI e384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e388 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT e4a0 18c .cfa: sp 0 + .ra: x30
STACK CFI e4a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e4ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e4c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e518 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e530 x25: .cfa -32 + ^
STACK CFI e58c x23: x23 x24: x24
STACK CFI e590 x25: x25
STACK CFI e5a4 x21: x21 x22: x22
STACK CFI e5b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e5b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI e5b8 x25: x25
STACK CFI e5d0 x23: x23 x24: x24
STACK CFI e5f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e620 x23: x23 x24: x24
STACK CFI e624 x21: x21 x22: x22
STACK CFI INIT ecd0 220 .cfa: sp 0 + .ra: x30
STACK CFI ecd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ece0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ecec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ed04 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI edb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI edb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT e630 1a8 .cfa: sp 0 + .ra: x30
STACK CFI e634 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e640 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e648 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e664 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e748 x23: x23 x24: x24
STACK CFI e758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e75c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI e7a8 x23: x23 x24: x24
STACK CFI e7d4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT eef0 188 .cfa: sp 0 + .ra: x30
STACK CFI eef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ef00 x23: .cfa -16 + ^
STACK CFI ef10 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI efdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI efe0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI f01c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f020 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT f080 80 .cfa: sp 0 + .ra: x30
STACK CFI f084 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f08c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f0c4 x21: .cfa -80 + ^
STACK CFI f0f4 x21: x21
STACK CFI f0fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT e7e0 364 .cfa: sp 0 + .ra: x30
STACK CFI e7e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e7f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e800 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e808 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e818 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e824 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e9c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e9c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI e9fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ea00 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ea34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ea38 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ea6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ea70 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT eb50 134 .cfa: sp 0 + .ra: x30
STACK CFI eb54 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI eb70 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ebc8 x21: .cfa -80 + ^
STACK CFI ebf4 x21: x21
STACK CFI ec70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ec74 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI ec80 x21: .cfa -80 + ^
STACK CFI INIT ec90 28 .cfa: sp 0 + .ra: x30
STACK CFI ec94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ec9c x19: .cfa -16 + ^
STACK CFI ecb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f1f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f200 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f210 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f230 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT f250 38 .cfa: sp 0 + .ra: x30
STACK CFI f254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f264 x19: .cfa -16 + ^
STACK CFI f284 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f100 44 .cfa: sp 0 + .ra: x30
STACK CFI f11c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f150 9c .cfa: sp 0 + .ra: x30
STACK CFI f154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f164 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f170 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT f520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f530 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f540 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f550 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT f290 68 .cfa: sp 0 + .ra: x30
STACK CFI f294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f2a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f2ac x21: .cfa -16 + ^
STACK CFI f2e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f2ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT f570 38 .cfa: sp 0 + .ra: x30
STACK CFI f574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f584 x19: .cfa -16 + ^
STACK CFI f5a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f300 12c .cfa: sp 0 + .ra: x30
STACK CFI f304 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f314 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f320 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f414 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT f430 e8 .cfa: sp 0 + .ra: x30
STACK CFI f4d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f500 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f6b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f6c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f6d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f6e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f6f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT f710 38 .cfa: sp 0 + .ra: x30
STACK CFI f714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f724 x19: .cfa -16 + ^
STACK CFI f744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f5b0 48 .cfa: sp 0 + .ra: x30
STACK CFI f5d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f5f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f600 a4 .cfa: sp 0 + .ra: x30
STACK CFI f604 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f614 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f620 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 12ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b80 38 .cfa: sp 0 + .ra: x30
STACK CFI 12b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12b94 x19: .cfa -16 + ^
STACK CFI 12bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12bc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12be0 38 .cfa: sp 0 + .ra: x30
STACK CFI 12be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12bf4 x19: .cfa -16 + ^
STACK CFI 12c14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12c40 38 .cfa: sp 0 + .ra: x30
STACK CFI 12c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12c54 x19: .cfa -16 + ^
STACK CFI 12c74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ca0 38 .cfa: sp 0 + .ra: x30
STACK CFI 12ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12cb4 x19: .cfa -16 + ^
STACK CFI 12cd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12ce0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d00 38 .cfa: sp 0 + .ra: x30
STACK CFI 12d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12d14 x19: .cfa -16 + ^
STACK CFI 12d34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12d40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d60 38 .cfa: sp 0 + .ra: x30
STACK CFI 12d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12d74 x19: .cfa -16 + ^
STACK CFI 12d94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12da0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12dc0 38 .cfa: sp 0 + .ra: x30
STACK CFI 12dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12dd4 x19: .cfa -16 + ^
STACK CFI 12df4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12e00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e20 38 .cfa: sp 0 + .ra: x30
STACK CFI 12e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12e34 x19: .cfa -16 + ^
STACK CFI 12e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f750 dc .cfa: sp 0 + .ra: x30
STACK CFI f754 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f760 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f7b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f7b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI f7cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f7d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI f81c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f820 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12e60 d8 .cfa: sp 0 + .ra: x30
STACK CFI 12e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12e6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12e80 x21: .cfa -16 + ^
STACK CFI 12eac x21: x21
STACK CFI 12ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12ec0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 12f14 x21: x21
STACK CFI 12f18 x21: .cfa -16 + ^
STACK CFI INIT 12f40 e0 .cfa: sp 0 + .ra: x30
STACK CFI 12f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12f4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12f5c x21: .cfa -16 + ^
STACK CFI 12f88 x21: x21
STACK CFI 12fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12fa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 13000 x21: x21
STACK CFI 1300c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13010 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT f830 810 .cfa: sp 0 + .ra: x30
STACK CFI f834 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f83c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f848 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f860 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI f86c x25: .cfa -16 + ^
STACK CFI fc80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI fc84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13020 54 .cfa: sp 0 + .ra: x30
STACK CFI 13024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13038 x19: .cfa -16 + ^
STACK CFI 13070 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 13080 60 .cfa: sp 0 + .ra: x30
STACK CFI 13084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13098 x19: .cfa -16 + ^
STACK CFI 130dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 130e0 354 .cfa: sp 0 + .ra: x30
STACK CFI 130e4 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 130ec x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 130f8 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 13100 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 13108 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 13114 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI 13348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1334c .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT 13440 13c .cfa: sp 0 + .ra: x30
STACK CFI 13448 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13450 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1345c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 134b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 134bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 13580 148 .cfa: sp 0 + .ra: x30
STACK CFI 13584 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1358c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 135a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 135f4 x21: x21 x22: x22
STACK CFI 13604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13608 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13644 x21: x21 x22: x22
STACK CFI 13650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 136d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 136d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 136dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13714 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1376c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13778 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13790 44 .cfa: sp 0 + .ra: x30
STACK CFI 13798 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 137a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 137cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 137e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 137e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 137ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1380c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 13810 x21: .cfa -16 + ^
STACK CFI 13878 x21: x21
STACK CFI 1387c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13880 164 .cfa: sp 0 + .ra: x30
STACK CFI 13884 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1388c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13898 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 138a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13964 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 139a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 139ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 139f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 139f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13a00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13a08 x21: .cfa -16 + ^
STACK CFI 13a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 13a70 74 .cfa: sp 0 + .ra: x30
STACK CFI 13a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13a8c x21: .cfa -16 + ^
STACK CFI 13ad8 x21: x21
STACK CFI 13ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13af0 78 .cfa: sp 0 + .ra: x30
STACK CFI 13af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13b00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13b08 x21: .cfa -16 + ^
STACK CFI 13b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 13b70 74 .cfa: sp 0 + .ra: x30
STACK CFI 13b74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13b7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13b8c x21: .cfa -16 + ^
STACK CFI 13bd8 x21: x21
STACK CFI 13be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13bf0 78 .cfa: sp 0 + .ra: x30
STACK CFI 13bf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13c00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13c08 x21: .cfa -16 + ^
STACK CFI 13c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 13c70 74 .cfa: sp 0 + .ra: x30
STACK CFI 13c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13c7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13c8c x21: .cfa -16 + ^
STACK CFI 13cd8 x21: x21
STACK CFI 13ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13cf0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 13cf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13d08 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 13d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 13d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 13d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 13d74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 13db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 13db8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 13dd0 3ac .cfa: sp 0 + .ra: x30
STACK CFI 13dd4 .cfa: sp 512 +
STACK CFI 13dd8 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 13de0 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 13de8 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 13df4 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 13e08 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 13e14 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 14050 x25: x25 x26: x26
STACK CFI 14054 x27: x27 x28: x28
STACK CFI 1406c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14070 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI 14088 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 140c4 x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 14180 170 .cfa: sp 0 + .ra: x30
STACK CFI 14184 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1418c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14198 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14248 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 14260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14264 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 142a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 142ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 142f0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 142f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14304 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14310 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14328 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 14414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14418 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT 144c0 508 .cfa: sp 0 + .ra: x30
STACK CFI 144c4 .cfa: sp 544 +
STACK CFI 144c8 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 144d0 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 144dc x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 144e8 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 144f0 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 144f8 x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 147f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 147f4 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI INIT 149d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 149d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 149dc x19: .cfa -32 + ^
STACK CFI 14a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 14a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14a4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 14a90 21c .cfa: sp 0 + .ra: x30
STACK CFI 14a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14a9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14abc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 14ac4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14be4 x21: x21 x22: x22
STACK CFI 14be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14bec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 14c04 x21: x21 x22: x22
STACK CFI 14c28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14c34 x21: x21 x22: x22
STACK CFI 14c44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 10040 318 .cfa: sp 0 + .ra: x30
STACK CFI 10044 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 10050 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 10060 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^
STACK CFI 101ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 101b0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI INIT 14cb0 434 .cfa: sp 0 + .ra: x30
STACK CFI 14cb4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 14cbc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 14cc8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 14cd0 x25: .cfa -112 + ^
STACK CFI 14e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 14e70 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI INIT 150f0 178 .cfa: sp 0 + .ra: x30
STACK CFI 150f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 150fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 15108 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 15110 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15118 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 151e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 151ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15244 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 15270 248 .cfa: sp 0 + .ra: x30
STACK CFI 15274 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1527c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15284 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 15290 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 152b0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 152b8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15458 x19: x19 x20: x20
STACK CFI 1545c x21: x21 x22: x22
STACK CFI 1546c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15470 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 154c0 178 .cfa: sp 0 + .ra: x30
STACK CFI 154c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 154cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 154d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 154e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 154e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 155b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 155bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15614 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 15640 248 .cfa: sp 0 + .ra: x30
STACK CFI 15644 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1564c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15654 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 15660 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 15680 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15688 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15828 x19: x19 x20: x20
STACK CFI 1582c x21: x21 x22: x22
STACK CFI 1583c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15840 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 15890 178 .cfa: sp 0 + .ra: x30
STACK CFI 15894 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1589c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 158a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 158b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 158b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1598c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 159e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 159e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 15a10 248 .cfa: sp 0 + .ra: x30
STACK CFI 15a14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 15a1c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15a24 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 15a30 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 15a50 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15a58 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15bf8 x19: x19 x20: x20
STACK CFI 15bfc x21: x21 x22: x22
STACK CFI 15c0c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15c10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT d1d0 318 .cfa: sp 0 + .ra: x30
STACK CFI d1d4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI d1dc x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI d1ec x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI d1f4 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI d200 x25: .cfa -240 + ^
STACK CFI d44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d450 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI INIT 15c60 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 15c64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 15c6c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15c7c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15c88 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15d6c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT 15e10 19c .cfa: sp 0 + .ra: x30
STACK CFI 15e14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15e1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15e28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15e34 x23: .cfa -16 + ^
STACK CFI 15ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15edc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 15ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 15f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15f0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 15f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15f24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15fb0 7bc .cfa: sp 0 + .ra: x30
STACK CFI 15fb4 .cfa: sp 512 +
STACK CFI 15fb8 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 15fc0 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 15fc8 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 15fd0 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 15fd8 x25: .cfa -448 + ^
STACK CFI 1617c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 16180 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 163d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 163dc .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 165a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 165ac .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI INIT 16770 19c .cfa: sp 0 + .ra: x30
STACK CFI 16774 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1677c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16788 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16794 x23: .cfa -16 + ^
STACK CFI 16838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1683c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 16854 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1686c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 16884 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 16910 74c .cfa: sp 0 + .ra: x30
STACK CFI 16914 .cfa: sp 512 +
STACK CFI 16918 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 16920 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 16928 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 16930 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 16938 x25: .cfa -448 + ^
STACK CFI 16adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 16ae0 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 16d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 16d3c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 16f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 16f0c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI INIT 17060 19c .cfa: sp 0 + .ra: x30
STACK CFI 17064 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1706c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17078 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17084 x23: .cfa -16 + ^
STACK CFI 17128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1712c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17144 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1715c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17174 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 17200 7bc .cfa: sp 0 + .ra: x30
STACK CFI 17204 .cfa: sp 512 +
STACK CFI 17208 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 17210 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 17218 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 17220 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 17228 x25: .cfa -448 + ^
STACK CFI 173cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 173d0 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 17628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1762c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI 177f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 177fc .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x29: .cfa -512 + ^
STACK CFI INIT 179c0 19c .cfa: sp 0 + .ra: x30
STACK CFI 179c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 179cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 179d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 179e4 x23: .cfa -16 + ^
STACK CFI 17a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17a8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17abc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 17b60 928 .cfa: sp 0 + .ra: x30
STACK CFI 17b64 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 17b6c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 17b74 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 17b80 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 17b88 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 17e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17e7c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 17f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17f90 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 18490 19c .cfa: sp 0 + .ra: x30
STACK CFI 18494 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1849c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 184a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 184b4 x23: .cfa -16 + ^
STACK CFI 18558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1855c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 18570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 18574 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 18588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1858c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 185a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 185a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 18630 998 .cfa: sp 0 + .ra: x30
STACK CFI 18634 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1863c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 18644 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 18650 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 18658 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 18948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1894c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 18a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 18a60 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 18fd0 fe0 .cfa: sp 0 + .ra: x30
STACK CFI 18fd4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 18fdc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 18fec x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 19004 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 19014 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 19020 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 195f8 x19: x19 x20: x20
STACK CFI 19600 x21: x21 x22: x22
STACK CFI 19608 x25: x25 x26: x26
STACK CFI 19628 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 19674 x19: x19 x20: x20
STACK CFI 1967c x21: x21 x22: x22
STACK CFI 19680 x25: x25 x26: x26
STACK CFI 1968c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 19690 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 196e0 x19: x19 x20: x20
STACK CFI 196e4 x21: x21 x22: x22
STACK CFI 196ec x25: x25 x26: x26
STACK CFI 196f4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 196f8 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 19748 x19: x19 x20: x20
STACK CFI 1974c x21: x21 x22: x22
STACK CFI 19754 x25: x25 x26: x26
STACK CFI 1975c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 19760 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 197b0 x19: x19 x20: x20
STACK CFI 197b4 x21: x21 x22: x22
STACK CFI 197bc x25: x25 x26: x26
STACK CFI 197c4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 197c8 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 19e0c x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 19e6c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 19e70 .cfa: sp 240 + .ra: .cfa -232 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 19e78 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 19fb0 820 .cfa: sp 0 + .ra: x30
STACK CFI 19fb4 .cfa: sp 528 +
STACK CFI 19fb8 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 19fc0 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 19fc8 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 19fd0 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 19fd8 x25: .cfa -464 + ^
STACK CFI 1a17c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1a180 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x29: .cfa -528 + ^
STACK CFI 1a3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1a3d0 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x29: .cfa -528 + ^
STACK CFI 1a59c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1a5a0 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x29: .cfa -528 + ^
STACK CFI INIT 1a7d0 518 .cfa: sp 0 + .ra: x30
STACK CFI 1a7d4 .cfa: sp 544 +
STACK CFI 1a7d8 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 1a7e0 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 1a7ec x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 1a814 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 1a828 x25: .cfa -480 + ^
STACK CFI 1aa90 x19: x19 x20: x20
STACK CFI 1aa98 x23: x23 x24: x24
STACK CFI 1aa9c x25: x25
STACK CFI 1aaa0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1aaa4 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x29: .cfa -544 + ^
STACK CFI 1aaa8 x19: x19 x20: x20
STACK CFI 1aabc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1aac0 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1aadc x19: x19 x20: x20
STACK CFI 1aae4 x23: x23 x24: x24
STACK CFI 1aae8 x25: x25
STACK CFI 1aaec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1aaf0 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1ab10 x19: x19 x20: x20
STACK CFI 1ab18 x23: x23 x24: x24
STACK CFI 1ab1c x25: x25
STACK CFI 1ab20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1ab24 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI INIT 1acf0 470 .cfa: sp 0 + .ra: x30
STACK CFI 1acf4 .cfa: sp 544 +
STACK CFI 1acf8 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 1ad00 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 1ad10 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 1ad34 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 1ad48 x25: .cfa -480 + ^
STACK CFI 1af04 x23: x23 x24: x24 x25: x25
STACK CFI 1af08 x21: x21 x22: x22
STACK CFI 1af1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1af20 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1af44 x21: x21 x22: x22
STACK CFI 1af48 x23: x23 x24: x24
STACK CFI 1af4c x25: x25
STACK CFI 1af50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1af54 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1b000 x21: x21 x22: x22
STACK CFI 1b004 x23: x23 x24: x24
STACK CFI 1b008 x25: x25
STACK CFI 1b00c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b010 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1b020 x21: x21 x22: x22
STACK CFI 1b024 x23: x23 x24: x24
STACK CFI 1b028 x25: x25
STACK CFI 1b02c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b030 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI INIT 1b160 468 .cfa: sp 0 + .ra: x30
STACK CFI 1b164 .cfa: sp 544 +
STACK CFI 1b168 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 1b170 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 1b180 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 1b1a4 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 1b1b8 x25: .cfa -480 + ^
STACK CFI 1b374 x23: x23 x24: x24 x25: x25
STACK CFI 1b378 x21: x21 x22: x22
STACK CFI 1b38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b390 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1b3b4 x21: x21 x22: x22
STACK CFI 1b3b8 x23: x23 x24: x24
STACK CFI 1b3bc x25: x25
STACK CFI 1b3c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b3c4 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1b468 x21: x21 x22: x22
STACK CFI 1b46c x23: x23 x24: x24
STACK CFI 1b470 x25: x25
STACK CFI 1b474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b478 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1b488 x21: x21 x22: x22
STACK CFI 1b48c x23: x23 x24: x24
STACK CFI 1b490 x25: x25
STACK CFI 1b494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b498 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI INIT 1b5d0 398 .cfa: sp 0 + .ra: x30
STACK CFI 1b5d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1b5dc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1b5ec x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1b610 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1b624 x25: .cfa -112 + ^
STACK CFI 1b798 x21: x21 x22: x22
STACK CFI 1b79c x23: x23 x24: x24
STACK CFI 1b7a0 x25: x25
STACK CFI 1b7a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b7a8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI 1b7ac x21: x21 x22: x22
STACK CFI 1b7bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b7c0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 1b7cc x21: x21 x22: x22
STACK CFI 1b7d0 x23: x23 x24: x24
STACK CFI 1b7d4 x25: x25
STACK CFI 1b7d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b7dc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 1b80c x21: x21 x22: x22
STACK CFI 1b810 x23: x23 x24: x24
STACK CFI 1b814 x25: x25
STACK CFI 1b818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b81c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI INIT 10360 52c .cfa: sp 0 + .ra: x30
STACK CFI 10364 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10374 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10394 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10834 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10890 660 .cfa: sp 0 + .ra: x30
STACK CFI 10894 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 108a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 108b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10e94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1b970 638 .cfa: sp 0 + .ra: x30
STACK CFI 1b974 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1b97c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1b984 x27: .cfa -144 + ^
STACK CFI 1b990 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1b99c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1b9c8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1bc38 x21: x21 x22: x22
STACK CFI 1bc3c x23: x23 x24: x24
STACK CFI 1bc40 x25: x25 x26: x26
STACK CFI 1bc48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 1bc4c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI 1bcd0 x25: x25 x26: x26
STACK CFI 1bcd4 x21: x21 x22: x22
STACK CFI 1bcd8 x23: x23 x24: x24
STACK CFI 1bcec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 1bcf0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI 1bcfc x21: x21 x22: x22
STACK CFI 1bd00 x23: x23 x24: x24
STACK CFI 1bd04 x25: x25 x26: x26
STACK CFI 1bd0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 1bd10 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI 1bd60 x21: x21 x22: x22
STACK CFI 1bd64 x23: x23 x24: x24
STACK CFI 1bd68 x25: x25 x26: x26
STACK CFI 1bd70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 1bd74 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI INIT 1bfb0 430 .cfa: sp 0 + .ra: x30
STACK CFI 1bfb4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1bfbc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1bfcc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1bff0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1c004 x25: .cfa -112 + ^
STACK CFI 1c1b8 x21: x21 x22: x22
STACK CFI 1c1bc x23: x23 x24: x24
STACK CFI 1c1c0 x25: x25
STACK CFI 1c1c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c1c8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI 1c1cc x21: x21 x22: x22
STACK CFI 1c1dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c1e0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 1c1fc x21: x21 x22: x22
STACK CFI 1c200 x23: x23 x24: x24
STACK CFI 1c204 x25: x25
STACK CFI 1c208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c20c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 1c27c x21: x21 x22: x22
STACK CFI 1c280 x23: x23 x24: x24
STACK CFI 1c284 x25: x25
STACK CFI 1c288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c28c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1c3e0 510 .cfa: sp 0 + .ra: x30
STACK CFI 1c3e4 .cfa: sp 544 +
STACK CFI 1c3e8 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 1c3f0 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 1c3fc x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 1c424 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 1c438 x25: .cfa -480 + ^
STACK CFI 1c698 x19: x19 x20: x20
STACK CFI 1c6a0 x23: x23 x24: x24
STACK CFI 1c6a4 x25: x25
STACK CFI 1c6a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c6ac .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x29: .cfa -544 + ^
STACK CFI 1c6b0 x19: x19 x20: x20
STACK CFI 1c6c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c6c8 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1c704 x19: x19 x20: x20
STACK CFI 1c70c x23: x23 x24: x24
STACK CFI 1c710 x25: x25
STACK CFI 1c714 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c718 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI 1c738 x19: x19 x20: x20
STACK CFI 1c740 x23: x23 x24: x24
STACK CFI 1c744 x25: x25
STACK CFI 1c748 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c74c .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x29: .cfa -544 + ^
STACK CFI INIT 10ef0 130 .cfa: sp 0 + .ra: x30
STACK CFI 10ef4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10f04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10f28 x21: .cfa -48 + ^
STACK CFI 10ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10ff4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11020 d48 .cfa: sp 0 + .ra: x30
STACK CFI 11024 .cfa: sp 720 +
STACK CFI 11028 .ra: .cfa -712 + ^ x29: .cfa -720 + ^
STACK CFI 11030 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 1103c x19: .cfa -704 + ^ x20: .cfa -696 + ^
STACK CFI 11060 x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI 11440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11444 .cfa: sp 720 + .ra: .cfa -712 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^ x29: .cfa -720 + ^
STACK CFI INIT 11d70 734 .cfa: sp 0 + .ra: x30
STACK CFI 11d74 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 11d7c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 11d8c x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 11da4 x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 121c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 121cc .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^ x29: .cfa -352 + ^
STACK CFI INIT 124b0 34c .cfa: sp 0 + .ra: x30
STACK CFI 124b4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 124c0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 124d0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 124f0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 124f8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 124fc x27: .cfa -80 + ^
STACK CFI 126ac x21: x21 x22: x22
STACK CFI 126b0 x23: x23 x24: x24
STACK CFI 126b4 x25: x25 x26: x26
STACK CFI 126b8 x27: x27
STACK CFI 126bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 126c0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 126c4 x23: x23 x24: x24
STACK CFI 126c8 x25: x25 x26: x26
STACK CFI 126cc x27: x27
STACK CFI 126f0 x21: x21 x22: x22
STACK CFI 126f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 126f8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 1274c x21: x21 x22: x22
STACK CFI 12750 x23: x23 x24: x24
STACK CFI 12754 x25: x25 x26: x26
STACK CFI 12758 x27: x27
STACK CFI 1275c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12760 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 12794 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 127b8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 127c0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 127c8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 127cc x27: .cfa -80 + ^
STACK CFI 127d4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 127e4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 127e8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 127ec x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 127f0 x27: .cfa -80 + ^
STACK CFI INIT 12800 164 .cfa: sp 0 + .ra: x30
STACK CFI 12804 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1280c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1281c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12894 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12970 164 .cfa: sp 0 + .ra: x30
STACK CFI 12974 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1297c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1298c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12a04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT d4f0 4 .cfa: sp 0 + .ra: x30
