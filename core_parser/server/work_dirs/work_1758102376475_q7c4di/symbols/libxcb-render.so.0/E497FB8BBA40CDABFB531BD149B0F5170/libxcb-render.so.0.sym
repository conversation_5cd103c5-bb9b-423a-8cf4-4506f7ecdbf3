MODULE Linux arm64 E497FB8BBA40CDABFB531BD149B0F5170 libxcb-render.so.0
INFO CODE_ID 8BFB97E440BAABCDFB531BD149B0F517640999AD
PUBLIC 4d98 0 xcb_render_glyph_next
PUBLIC 4db8 0 xcb_render_glyph_end
PUBLIC 4dd0 0 xcb_render_glyphset_next
PUBLIC 4df0 0 xcb_render_glyphset_end
PUBLIC 4e08 0 xcb_render_picture_next
PUBLIC 4e28 0 xcb_render_picture_end
PUBLIC 4e40 0 xcb_render_pictformat_next
PUBLIC 4e60 0 xcb_render_pictformat_end
PUBLIC 4e78 0 xcb_render_fixed_next
PUBLIC 4e98 0 xcb_render_fixed_end
PUBLIC 4eb0 0 xcb_render_directformat_next
PUBLIC 4ed0 0 xcb_render_directformat_end
PUBLIC 4ee8 0 xcb_render_pictforminfo_next
PUBLIC 4f08 0 xcb_render_pictforminfo_end
PUBLIC 4f28 0 xcb_render_pictvisual_next
PUBLIC 4f48 0 xcb_render_pictvisual_end
PUBLIC 4f60 0 xcb_render_pictdepth_sizeof
PUBLIC 4f70 0 xcb_render_pictdepth_visuals
PUBLIC 4f78 0 xcb_render_pictdepth_visuals_length
PUBLIC 4f80 0 xcb_render_pictdepth_visuals_iterator
PUBLIC 4fa0 0 xcb_render_pictdepth_next
PUBLIC 4fe8 0 xcb_render_pictdepth_end
PUBLIC 5040 0 xcb_render_pictscreen_sizeof
PUBLIC 50b8 0 xcb_render_pictscreen_depths_length
PUBLIC 50c0 0 xcb_render_pictscreen_depths_iterator
PUBLIC 50d8 0 xcb_render_pictscreen_next
PUBLIC 5120 0 xcb_render_pictscreen_end
PUBLIC 5178 0 xcb_render_indexvalue_next
PUBLIC 5198 0 xcb_render_indexvalue_end
PUBLIC 51b8 0 xcb_render_color_next
PUBLIC 51d8 0 xcb_render_color_end
PUBLIC 51f0 0 xcb_render_pointfix_next
PUBLIC 5210 0 xcb_render_pointfix_end
PUBLIC 5228 0 xcb_render_linefix_next
PUBLIC 5248 0 xcb_render_linefix_end
PUBLIC 5260 0 xcb_render_triangle_next
PUBLIC 5280 0 xcb_render_triangle_end
PUBLIC 52a0 0 xcb_render_trapezoid_next
PUBLIC 52c0 0 xcb_render_trapezoid_end
PUBLIC 52e0 0 xcb_render_glyphinfo_next
PUBLIC 5300 0 xcb_render_glyphinfo_end
PUBLIC 5320 0 xcb_render_query_version
PUBLIC 5390 0 xcb_render_query_version_unchecked
PUBLIC 5400 0 xcb_render_query_version_reply
PUBLIC 5408 0 xcb_render_query_pict_formats_sizeof
PUBLIC 54a8 0 xcb_render_query_pict_formats
PUBLIC 5510 0 xcb_render_query_pict_formats_unchecked
PUBLIC 5578 0 xcb_render_query_pict_formats_formats
PUBLIC 5580 0 xcb_render_query_pict_formats_formats_length
PUBLIC 5588 0 xcb_render_query_pict_formats_formats_iterator
PUBLIC 55a8 0 xcb_render_query_pict_formats_screens_length
PUBLIC 55b0 0 xcb_render_query_pict_formats_screens_iterator
PUBLIC 5600 0 xcb_render_query_pict_formats_subpixels
PUBLIC 5628 0 xcb_render_query_pict_formats_subpixels_length
PUBLIC 5630 0 xcb_render_query_pict_formats_subpixels_end
PUBLIC 5678 0 xcb_render_query_pict_formats_reply
PUBLIC 5680 0 xcb_render_query_pict_index_values_sizeof
PUBLIC 5698 0 xcb_render_query_pict_index_values
PUBLIC 5708 0 xcb_render_query_pict_index_values_unchecked
PUBLIC 5770 0 xcb_render_query_pict_index_values_values
PUBLIC 5778 0 xcb_render_query_pict_index_values_values_length
PUBLIC 5780 0 xcb_render_query_pict_index_values_values_iterator
PUBLIC 57a0 0 xcb_render_query_pict_index_values_reply
PUBLIC 57a8 0 xcb_render_create_picture_value_list_serialize
PUBLIC 5b00 0 xcb_render_create_picture_value_list_unpack
PUBLIC 5c30 0 xcb_render_create_picture_value_list_sizeof
PUBLIC 5c78 0 xcb_render_create_picture_sizeof
PUBLIC 5ca0 0 xcb_render_create_picture_checked
PUBLIC 5d38 0 xcb_render_create_picture
PUBLIC 5dd0 0 xcb_render_create_picture_aux_checked
PUBLIC 5e80 0 xcb_render_create_picture_aux
PUBLIC 5f30 0 xcb_render_create_picture_value_list
PUBLIC 5f38 0 xcb_render_change_picture_value_list_serialize
PUBLIC 6290 0 xcb_render_change_picture_value_list_unpack
PUBLIC 63c0 0 xcb_render_change_picture_value_list_sizeof
PUBLIC 6408 0 xcb_render_change_picture_sizeof
PUBLIC 6430 0 xcb_render_change_picture_checked
PUBLIC 64c0 0 xcb_render_change_picture
PUBLIC 6550 0 xcb_render_change_picture_aux_checked
PUBLIC 6600 0 xcb_render_change_picture_aux
PUBLIC 66b0 0 xcb_render_change_picture_value_list
PUBLIC 66b8 0 xcb_render_set_picture_clip_rectangles_sizeof
PUBLIC 66c8 0 xcb_render_set_picture_clip_rectangles_checked
PUBLIC 6748 0 xcb_render_set_picture_clip_rectangles
PUBLIC 67c8 0 xcb_render_set_picture_clip_rectangles_rectangles
PUBLIC 67d0 0 xcb_render_set_picture_clip_rectangles_rectangles_length
PUBLIC 67e8 0 xcb_render_set_picture_clip_rectangles_rectangles_iterator
PUBLIC 6818 0 xcb_render_free_picture_checked
PUBLIC 6888 0 xcb_render_free_picture
PUBLIC 68f0 0 xcb_render_composite_checked
PUBLIC 69a8 0 xcb_render_composite
PUBLIC 6a60 0 xcb_render_trapezoids_sizeof
PUBLIC 6a70 0 xcb_render_trapezoids_checked
PUBLIC 6b10 0 xcb_render_trapezoids
PUBLIC 6bb0 0 xcb_render_trapezoids_traps
PUBLIC 6bb8 0 xcb_render_trapezoids_traps_length
PUBLIC 6bd8 0 xcb_render_trapezoids_traps_iterator
PUBLIC 6c10 0 xcb_render_triangles_sizeof
PUBLIC 6c20 0 xcb_render_triangles_checked
PUBLIC 6cb8 0 xcb_render_triangles
PUBLIC 6d50 0 xcb_render_triangles_triangles
PUBLIC 6d58 0 xcb_render_triangles_triangles_length
PUBLIC 6d78 0 xcb_render_triangles_triangles_iterator
PUBLIC 6db0 0 xcb_render_tri_strip_sizeof
PUBLIC 6dc0 0 xcb_render_tri_strip_checked
PUBLIC 6e58 0 xcb_render_tri_strip
PUBLIC 6ef0 0 xcb_render_tri_strip_points
PUBLIC 6ef8 0 xcb_render_tri_strip_points_length
PUBLIC 6f10 0 xcb_render_tri_strip_points_iterator
PUBLIC 6f40 0 xcb_render_tri_fan_sizeof
PUBLIC 6f50 0 xcb_render_tri_fan_checked
PUBLIC 6fe8 0 xcb_render_tri_fan
PUBLIC 7080 0 xcb_render_tri_fan_points
PUBLIC 7088 0 xcb_render_tri_fan_points_length
PUBLIC 70a0 0 xcb_render_tri_fan_points_iterator
PUBLIC 70d0 0 xcb_render_create_glyph_set_checked
PUBLIC 7140 0 xcb_render_create_glyph_set
PUBLIC 71b0 0 xcb_render_reference_glyph_set_checked
PUBLIC 7220 0 xcb_render_reference_glyph_set
PUBLIC 7290 0 xcb_render_free_glyph_set_checked
PUBLIC 7300 0 xcb_render_free_glyph_set
PUBLIC 7368 0 xcb_render_add_glyphs_sizeof
PUBLIC 7378 0 xcb_render_add_glyphs_checked
PUBLIC 7418 0 xcb_render_add_glyphs
PUBLIC 74b8 0 xcb_render_add_glyphs_glyphids
PUBLIC 74c0 0 xcb_render_add_glyphs_glyphids_length
PUBLIC 74c8 0 xcb_render_add_glyphs_glyphids_end
PUBLIC 74e0 0 xcb_render_add_glyphs_glyphs
PUBLIC 7508 0 xcb_render_add_glyphs_glyphs_length
PUBLIC 7510 0 xcb_render_add_glyphs_glyphs_iterator
PUBLIC 7558 0 xcb_render_add_glyphs_data
PUBLIC 7570 0 xcb_render_add_glyphs_data_length
PUBLIC 7598 0 xcb_render_add_glyphs_data_end
PUBLIC 75e8 0 xcb_render_free_glyphs_sizeof
PUBLIC 75f8 0 xcb_render_free_glyphs_checked
PUBLIC 7678 0 xcb_render_free_glyphs
PUBLIC 76f0 0 xcb_render_free_glyphs_glyphs
PUBLIC 76f8 0 xcb_render_free_glyphs_glyphs_length
PUBLIC 7708 0 xcb_render_free_glyphs_glyphs_end
PUBLIC 7720 0 xcb_render_composite_glyphs_8_sizeof
PUBLIC 7728 0 xcb_render_composite_glyphs_8_checked
PUBLIC 77c8 0 xcb_render_composite_glyphs_8
PUBLIC 7868 0 xcb_render_composite_glyphs_8_glyphcmds
PUBLIC 7870 0 xcb_render_composite_glyphs_8_glyphcmds_length
PUBLIC 7880 0 xcb_render_composite_glyphs_8_glyphcmds_end
PUBLIC 7898 0 xcb_render_composite_glyphs_16_sizeof
PUBLIC 78a0 0 xcb_render_composite_glyphs_16_checked
PUBLIC 7940 0 xcb_render_composite_glyphs_16
PUBLIC 79e0 0 xcb_render_composite_glyphs_16_glyphcmds
PUBLIC 79e8 0 xcb_render_composite_glyphs_16_glyphcmds_length
PUBLIC 79f8 0 xcb_render_composite_glyphs_16_glyphcmds_end
PUBLIC 7a10 0 xcb_render_composite_glyphs_32_sizeof
PUBLIC 7a18 0 xcb_render_composite_glyphs_32_checked
PUBLIC 7ab8 0 xcb_render_composite_glyphs_32
PUBLIC 7b58 0 xcb_render_composite_glyphs_32_glyphcmds
PUBLIC 7b60 0 xcb_render_composite_glyphs_32_glyphcmds_length
PUBLIC 7b70 0 xcb_render_composite_glyphs_32_glyphcmds_end
PUBLIC 7b88 0 xcb_render_fill_rectangles_sizeof
PUBLIC 7b98 0 xcb_render_fill_rectangles_checked
PUBLIC 7c28 0 xcb_render_fill_rectangles
PUBLIC 7cb8 0 xcb_render_fill_rectangles_rects
PUBLIC 7cc0 0 xcb_render_fill_rectangles_rects_length
PUBLIC 7cd8 0 xcb_render_fill_rectangles_rects_iterator
PUBLIC 7d08 0 xcb_render_create_cursor_checked
PUBLIC 7d80 0 xcb_render_create_cursor
PUBLIC 7df8 0 xcb_render_transform_next
PUBLIC 7e18 0 xcb_render_transform_end
PUBLIC 7e38 0 xcb_render_set_picture_transform_checked
PUBLIC 7ec0 0 xcb_render_set_picture_transform
PUBLIC 7f40 0 xcb_render_query_filters_sizeof
PUBLIC 7fc0 0 xcb_render_query_filters
PUBLIC 8030 0 xcb_render_query_filters_unchecked
PUBLIC 8098 0 xcb_render_query_filters_aliases
PUBLIC 80a0 0 xcb_render_query_filters_aliases_length
PUBLIC 80a8 0 xcb_render_query_filters_aliases_end
PUBLIC 80c0 0 xcb_render_query_filters_filters_length
PUBLIC 80c8 0 xcb_render_query_filters_filters_iterator
PUBLIC 8100 0 xcb_render_query_filters_reply
PUBLIC 8108 0 xcb_render_set_picture_filter_sizeof
PUBLIC 8128 0 xcb_render_set_picture_filter_checked
PUBLIC 81c8 0 xcb_render_set_picture_filter
PUBLIC 8260 0 xcb_render_set_picture_filter_filter
PUBLIC 8268 0 xcb_render_set_picture_filter_filter_length
PUBLIC 8270 0 xcb_render_set_picture_filter_filter_end
PUBLIC 8288 0 xcb_render_set_picture_filter_values
PUBLIC 82b0 0 xcb_render_set_picture_filter_values_length
PUBLIC 82e0 0 xcb_render_set_picture_filter_values_end
PUBLIC 8340 0 xcb_render_animcursorelt_next
PUBLIC 8360 0 xcb_render_animcursorelt_end
PUBLIC 8378 0 xcb_render_create_anim_cursor_sizeof
PUBLIC 8388 0 xcb_render_create_anim_cursor_checked
PUBLIC 8408 0 xcb_render_create_anim_cursor
PUBLIC 8480 0 xcb_render_create_anim_cursor_cursors
PUBLIC 8488 0 xcb_render_create_anim_cursor_cursors_length
PUBLIC 84a0 0 xcb_render_create_anim_cursor_cursors_iterator
PUBLIC 84d0 0 xcb_render_spanfix_next
PUBLIC 84f0 0 xcb_render_spanfix_end
PUBLIC 8510 0 xcb_render_trap_next
PUBLIC 8530 0 xcb_render_trap_end
PUBLIC 8550 0 xcb_render_add_traps_sizeof
PUBLIC 8560 0 xcb_render_add_traps_checked
PUBLIC 85e8 0 xcb_render_add_traps
PUBLIC 8668 0 xcb_render_add_traps_traps
PUBLIC 8670 0 xcb_render_add_traps_traps_length
PUBLIC 8690 0 xcb_render_add_traps_traps_iterator
PUBLIC 86c8 0 xcb_render_create_solid_fill_checked
PUBLIC 8740 0 xcb_render_create_solid_fill
PUBLIC 87b0 0 xcb_render_create_linear_gradient_sizeof
PUBLIC 87c8 0 xcb_render_create_linear_gradient_checked
PUBLIC 8860 0 xcb_render_create_linear_gradient
PUBLIC 88f0 0 xcb_render_create_linear_gradient_stops
PUBLIC 88f8 0 xcb_render_create_linear_gradient_stops_length
PUBLIC 8900 0 xcb_render_create_linear_gradient_stops_end
PUBLIC 8918 0 xcb_render_create_linear_gradient_colors
PUBLIC 8940 0 xcb_render_create_linear_gradient_colors_length
PUBLIC 8948 0 xcb_render_create_linear_gradient_colors_iterator
PUBLIC 8990 0 xcb_render_create_radial_gradient_sizeof
PUBLIC 89a8 0 xcb_render_create_radial_gradient_checked
PUBLIC 8a48 0 xcb_render_create_radial_gradient
PUBLIC 8ae0 0 xcb_render_create_radial_gradient_stops
PUBLIC 8ae8 0 xcb_render_create_radial_gradient_stops_length
PUBLIC 8af0 0 xcb_render_create_radial_gradient_stops_end
PUBLIC 8b08 0 xcb_render_create_radial_gradient_colors
PUBLIC 8b30 0 xcb_render_create_radial_gradient_colors_length
PUBLIC 8b38 0 xcb_render_create_radial_gradient_colors_iterator
PUBLIC 8b80 0 xcb_render_create_conical_gradient_sizeof
PUBLIC 8b98 0 xcb_render_create_conical_gradient_checked
PUBLIC 8c30 0 xcb_render_create_conical_gradient
PUBLIC 8cc0 0 xcb_render_create_conical_gradient_stops
PUBLIC 8cc8 0 xcb_render_create_conical_gradient_stops_length
PUBLIC 8cd0 0 xcb_render_create_conical_gradient_stops_end
PUBLIC 8ce8 0 xcb_render_create_conical_gradient_colors
PUBLIC 8d10 0 xcb_render_create_conical_gradient_colors_length
PUBLIC 8d18 0 xcb_render_create_conical_gradient_colors_iterator
STACK CFI INIT 4cd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d48 48 .cfa: sp 0 + .ra: x30
STACK CFI 4d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d54 x19: .cfa -16 + ^
STACK CFI 4d8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d98 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4db8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dd0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4df0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e08 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e28 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e40 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e60 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e78 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e98 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4eb0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ed0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ee8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f08 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f28 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f48 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f80 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fa0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4fe8 54 .cfa: sp 0 + .ra: x30
STACK CFI 4fec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5004 x19: .cfa -32 + ^
STACK CFI 5020 x19: x19
STACK CFI 5038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5040 78 .cfa: sp 0 + .ra: x30
STACK CFI 5044 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 504c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5054 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 50a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 50b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 50b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 50dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 511c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5120 54 .cfa: sp 0 + .ra: x30
STACK CFI 5124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 513c x19: .cfa -32 + ^
STACK CFI 5158 x19: x19
STACK CFI 5170 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5178 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5198 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 51b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51d8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51f0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5210 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5228 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5248 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5260 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5280 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 52a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52c0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 52e0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5300 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5320 6c .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5334 x19: .cfa -112 + ^
STACK CFI 5384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5388 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5390 6c .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 53a4 x19: .cfa -112 + ^
STACK CFI 53f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 53f8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5408 a0 .cfa: sp 0 + .ra: x30
STACK CFI 540c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5414 x23: .cfa -16 + ^
STACK CFI 5420 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 542c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 549c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 54a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54a8 68 .cfa: sp 0 + .ra: x30
STACK CFI 54ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 54bc x19: .cfa -96 + ^
STACK CFI 5508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 550c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5510 64 .cfa: sp 0 + .ra: x30
STACK CFI 5514 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5524 x19: .cfa -96 + ^
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5570 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5578 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5588 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 55b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55bc x19: .cfa -16 + ^
STACK CFI 55f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5600 28 .cfa: sp 0 + .ra: x30
STACK CFI 5604 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5628 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5630 44 .cfa: sp 0 + .ra: x30
STACK CFI 5634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 563c x19: .cfa -16 + ^
STACK CFI 5670 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5678 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5680 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5698 6c .cfa: sp 0 + .ra: x30
STACK CFI 569c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 56ac x19: .cfa -96 + ^
STACK CFI 56fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5700 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5708 68 .cfa: sp 0 + .ra: x30
STACK CFI 570c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 571c x19: .cfa -96 + ^
STACK CFI 5768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 576c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5778 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5780 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57a8 354 .cfa: sp 0 + .ra: x30
STACK CFI 57ac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 57bc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 57cc x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 57e0 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 5a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5aa0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x29: .cfa -304 + ^
STACK CFI INIT 5b00 12c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c30 48 .cfa: sp 0 + .ra: x30
STACK CFI 5c34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5c3c x19: .cfa -80 + ^
STACK CFI 5c70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5c78 24 .cfa: sp 0 + .ra: x30
STACK CFI 5c80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ca0 94 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5cb4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d30 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5d38 94 .cfa: sp 0 + .ra: x30
STACK CFI 5d3c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5d4c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5dc8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5dd0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5dd4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5de4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e7c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5e80 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5e84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5e94 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f2c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5f30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f38 354 .cfa: sp 0 + .ra: x30
STACK CFI 5f3c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5f4c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 5f5c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 5f70 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 622c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6230 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6290 12c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 63cc x19: .cfa -80 + ^
STACK CFI 6400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6404 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6408 24 .cfa: sp 0 + .ra: x30
STACK CFI 6410 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6430 90 .cfa: sp 0 + .ra: x30
STACK CFI 6434 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6444 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 64b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64bc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 64c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 64c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 64d4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 654c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6550 ac .cfa: sp 0 + .ra: x30
STACK CFI 6554 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6564 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 65f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65f8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6600 ac .cfa: sp 0 + .ra: x30
STACK CFI 6604 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6614 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 66a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66a8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 66b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 66b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66c8 80 .cfa: sp 0 + .ra: x30
STACK CFI 66cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 66dc x19: .cfa -144 + ^
STACK CFI 6740 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6744 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6748 7c .cfa: sp 0 + .ra: x30
STACK CFI 674c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 675c x19: .cfa -144 + ^
STACK CFI 67bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 67c0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 67c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67e8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6818 6c .cfa: sp 0 + .ra: x30
STACK CFI 681c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 682c x19: .cfa -96 + ^
STACK CFI 687c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6880 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6888 68 .cfa: sp 0 + .ra: x30
STACK CFI 688c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 689c x19: .cfa -96 + ^
STACK CFI 68e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 68f0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 68f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6904 x19: .cfa -128 + ^
STACK CFI 699c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 69a0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 69a8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 69ac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 69bc x19: .cfa -128 + ^
STACK CFI 6a54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a58 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6a60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a70 9c .cfa: sp 0 + .ra: x30
STACK CFI 6a74 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6a84 x19: .cfa -144 + ^
STACK CFI 6b04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b08 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6b10 9c .cfa: sp 0 + .ra: x30
STACK CFI 6b14 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6b24 x19: .cfa -144 + ^
STACK CFI 6ba4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ba8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bb8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bd8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c20 98 .cfa: sp 0 + .ra: x30
STACK CFI 6c24 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6c34 x19: .cfa -144 + ^
STACK CFI 6cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6cb4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6cb8 98 .cfa: sp 0 + .ra: x30
STACK CFI 6cbc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6ccc x19: .cfa -144 + ^
STACK CFI 6d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d4c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6d50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d58 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d78 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6db0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6dc0 98 .cfa: sp 0 + .ra: x30
STACK CFI 6dc4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6dd4 x19: .cfa -144 + ^
STACK CFI 6e50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e54 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6e58 98 .cfa: sp 0 + .ra: x30
STACK CFI 6e5c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6e6c x19: .cfa -144 + ^
STACK CFI 6ee8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6eec .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6ef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ef8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f10 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f50 98 .cfa: sp 0 + .ra: x30
STACK CFI 6f54 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6f64 x19: .cfa -144 + ^
STACK CFI 6fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fe4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6fe8 98 .cfa: sp 0 + .ra: x30
STACK CFI 6fec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6ffc x19: .cfa -144 + ^
STACK CFI 7078 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 707c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7088 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70a0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 70d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 70d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 70e4 x19: .cfa -112 + ^
STACK CFI 7138 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 713c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7140 6c .cfa: sp 0 + .ra: x30
STACK CFI 7144 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7154 x19: .cfa -112 + ^
STACK CFI 71a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 71a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 71b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 71b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 71c4 x19: .cfa -112 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 721c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7220 6c .cfa: sp 0 + .ra: x30
STACK CFI 7224 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7234 x19: .cfa -112 + ^
STACK CFI 7284 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7288 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7290 6c .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 72a4 x19: .cfa -96 + ^
STACK CFI 72f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 72f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7300 68 .cfa: sp 0 + .ra: x30
STACK CFI 7304 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7314 x19: .cfa -96 + ^
STACK CFI 7360 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7364 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7368 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7378 a0 .cfa: sp 0 + .ra: x30
STACK CFI 737c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 738c x19: .cfa -208 + ^
STACK CFI 7410 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7414 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 7418 9c .cfa: sp 0 + .ra: x30
STACK CFI 741c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 7430 x19: .cfa -208 + ^
STACK CFI 74ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 74b0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 74b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74c8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 74e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7500 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7508 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7510 48 .cfa: sp 0 + .ra: x30
STACK CFI 7514 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 751c x19: .cfa -16 + ^
STACK CFI 7554 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7558 18 .cfa: sp 0 + .ra: x30
STACK CFI 755c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 756c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7570 28 .cfa: sp 0 + .ra: x30
STACK CFI 7574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 757c x19: .cfa -16 + ^
STACK CFI 7594 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7598 50 .cfa: sp 0 + .ra: x30
STACK CFI 759c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 75a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 75ac x21: .cfa -16 + ^
STACK CFI 75e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 75e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f8 7c .cfa: sp 0 + .ra: x30
STACK CFI 75fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 760c x19: .cfa -128 + ^
STACK CFI 766c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7670 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7678 78 .cfa: sp 0 + .ra: x30
STACK CFI 767c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 768c x19: .cfa -128 + ^
STACK CFI 76e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76ec .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 76f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7708 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7720 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7728 a0 .cfa: sp 0 + .ra: x30
STACK CFI 772c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7744 x19: .cfa -160 + ^
STACK CFI 77c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77c4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 77c8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 77cc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 77e4 x19: .cfa -160 + ^
STACK CFI 7860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7864 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7868 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7870 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7880 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7898 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78a0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 78a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 78bc x19: .cfa -160 + ^
STACK CFI 7938 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 793c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7940 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7944 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 795c x19: .cfa -160 + ^
STACK CFI 79d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 79dc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 79e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 79e8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 79f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a18 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7a1c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7a34 x19: .cfa -160 + ^
STACK CFI 7ab0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ab4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7ab8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7abc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7ad4 x19: .cfa -160 + ^
STACK CFI 7b50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b54 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7b58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b88 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b98 8c .cfa: sp 0 + .ra: x30
STACK CFI 7b9c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7bac x19: .cfa -144 + ^
STACK CFI 7c1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c20 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7c28 8c .cfa: sp 0 + .ra: x30
STACK CFI 7c2c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7c3c x19: .cfa -144 + ^
STACK CFI 7cac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7cb0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7cb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cd8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d08 78 .cfa: sp 0 + .ra: x30
STACK CFI 7d0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7d1c x19: .cfa -112 + ^
STACK CFI 7d78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d7c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7d80 74 .cfa: sp 0 + .ra: x30
STACK CFI 7d84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7d94 x19: .cfa -112 + ^
STACK CFI 7dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7df0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7df8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e18 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e38 84 .cfa: sp 0 + .ra: x30
STACK CFI 7e3c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7e60 x19: .cfa -144 + ^
STACK CFI 7eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7eb8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7ec0 80 .cfa: sp 0 + .ra: x30
STACK CFI 7ec4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7eec x19: .cfa -144 + ^
STACK CFI 7f38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f3c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7f40 7c .cfa: sp 0 + .ra: x30
STACK CFI 7f44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7f4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7f54 x23: .cfa -16 + ^
STACK CFI 7f5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7fc0 6c .cfa: sp 0 + .ra: x30
STACK CFI 7fc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7fd4 x19: .cfa -96 + ^
STACK CFI 8024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8028 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8030 68 .cfa: sp 0 + .ra: x30
STACK CFI 8034 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8044 x19: .cfa -96 + ^
STACK CFI 8090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8094 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8098 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80c8 34 .cfa: sp 0 + .ra: x30
STACK CFI 80cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 80d4 x19: .cfa -16 + ^
STACK CFI 80f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8100 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8108 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8128 9c .cfa: sp 0 + .ra: x30
STACK CFI 812c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 813c x19: .cfa -176 + ^
STACK CFI 81bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 81c0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT 81c8 98 .cfa: sp 0 + .ra: x30
STACK CFI 81cc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 81dc x19: .cfa -176 + ^
STACK CFI 8258 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 825c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8268 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8270 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8288 24 .cfa: sp 0 + .ra: x30
STACK CFI 828c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 82a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 82b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 82b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82bc x19: .cfa -16 + ^
STACK CFI 82d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 82e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 82e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 82f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 833c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8340 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8360 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8378 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8388 7c .cfa: sp 0 + .ra: x30
STACK CFI 838c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 839c x19: .cfa -128 + ^
STACK CFI 83fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8400 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8408 78 .cfa: sp 0 + .ra: x30
STACK CFI 840c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 841c x19: .cfa -128 + ^
STACK CFI 8478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 847c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8488 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84a0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 84d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8510 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8530 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8550 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8560 84 .cfa: sp 0 + .ra: x30
STACK CFI 8564 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8574 x19: .cfa -144 + ^
STACK CFI 85dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 85e8 80 .cfa: sp 0 + .ra: x30
STACK CFI 85ec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 85fc x19: .cfa -144 + ^
STACK CFI 8660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8664 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8668 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8670 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8690 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86c8 74 .cfa: sp 0 + .ra: x30
STACK CFI 86cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 86dc x19: .cfa -112 + ^
STACK CFI 8734 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8738 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8740 70 .cfa: sp 0 + .ra: x30
STACK CFI 8744 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8754 x19: .cfa -112 + ^
STACK CFI 87a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 87ac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 87b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87c8 94 .cfa: sp 0 + .ra: x30
STACK CFI 87cc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 87dc x19: .cfa -192 + ^
STACK CFI 8854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8858 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 8860 90 .cfa: sp 0 + .ra: x30
STACK CFI 8864 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8874 x19: .cfa -192 + ^
STACK CFI 88e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 88ec .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 88f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8900 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8918 24 .cfa: sp 0 + .ra: x30
STACK CFI 891c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8938 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8948 48 .cfa: sp 0 + .ra: x30
STACK CFI 894c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8954 x19: .cfa -16 + ^
STACK CFI 898c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8990 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89a8 9c .cfa: sp 0 + .ra: x30
STACK CFI 89ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 89bc x19: .cfa -192 + ^
STACK CFI 8a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8a40 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 8a48 98 .cfa: sp 0 + .ra: x30
STACK CFI 8a4c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8a5c x19: .cfa -192 + ^
STACK CFI 8ad8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8adc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 8ae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ae8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8af0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b08 24 .cfa: sp 0 + .ra: x30
STACK CFI 8b0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8b30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b38 48 .cfa: sp 0 + .ra: x30
STACK CFI 8b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b44 x19: .cfa -16 + ^
STACK CFI 8b7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b98 94 .cfa: sp 0 + .ra: x30
STACK CFI 8b9c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8bac x19: .cfa -176 + ^
STACK CFI 8c24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8c28 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8c30 90 .cfa: sp 0 + .ra: x30
STACK CFI 8c34 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8c44 x19: .cfa -176 + ^
STACK CFI 8cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8cbc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8cc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cc8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ce8 24 .cfa: sp 0 + .ra: x30
STACK CFI 8cec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8d08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d18 48 .cfa: sp 0 + .ra: x30
STACK CFI 8d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d24 x19: .cfa -16 + ^
STACK CFI 8d5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
