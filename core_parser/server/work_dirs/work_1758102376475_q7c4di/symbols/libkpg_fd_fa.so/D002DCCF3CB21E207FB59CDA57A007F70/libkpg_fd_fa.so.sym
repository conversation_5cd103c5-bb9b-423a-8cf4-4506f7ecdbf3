MODULE Linux arm64 D002DCCF3CB21E207FB59CDA57A007F70 libkpg_fd_fa.so
INFO CODE_ID CFDC02D0B23C201E7FB59CDA57A007F7
PUBLIC 7fa8 0 _init
PUBLIC 8470 0 _GLOBAL__sub_I_trt_kpg_fd_fa.cpp
PUBLIC 8510 0 _GLOBAL__sub_I_checkMacrosPlugin.cpp
PUBLIC 88b0 0 call_weak_fn
PUBLIC 88c4 0 deregister_tm_clones
PUBLIC 88f4 0 register_tm_clones
PUBLIC 8930 0 __do_global_dtors_aux
PUBLIC 8980 0 frame_dummy
PUBLIC 8990 0 trt_plugin::KPG_Plugin::getNbOutputs() const
PUBLIC 89a0 0 trt_plugin::KPG_Plugin::initialize() [clone .localalias]
PUBLIC 89b0 0 trt_plugin::KPG_Plugin::detachFromContext()
PUBLIC 89c0 0 trt_plugin::KPG_Plugin::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 89e0 0 trt_plugin::KPG_Plugin::getSerializationSize() const
PUBLIC 89f0 0 trt_plugin::KPG_Plugin::serialize(void*) const
PUBLIC 8a20 0 trt_plugin::KPG_Plugin::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 8a50 0 trt_plugin::KPG_Plugin::getPluginType() const
PUBLIC 8a60 0 trt_plugin::KPG_PluginCreator::getPluginVersion() const
PUBLIC 8a70 0 trt_plugin::KPG_Plugin::getPluginNamespace() const
PUBLIC 8a80 0 trt_plugin::KPG_Plugin::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 8aa0 0 trt_plugin::KPG_Plugin::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 8ab0 0 trt_plugin::KPG_Plugin::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 8ac0 0 trt_plugin::KPG_PluginCreator::getFieldNames()
PUBLIC 8ad0 0 trt_plugin::KPG_Plugin::~KPG_Plugin()
PUBLIC 8b30 0 trt_plugin::KPG_Plugin::~KPG_Plugin() [clone .localalias]
PUBLIC 8b60 0 trt_plugin::KPG_Plugin::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 8d50 0 trt_plugin::KPG_Plugin::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 8e00 0 trt_plugin::KPG_Plugin::setPluginNamespace(char const*)
PUBLIC 8e40 0 trt_plugin::KPG_Plugin::destroy()
PUBLIC 8e90 0 trt_plugin::KPG_Plugin::KPG_Plugin(int, int, int, int)
PUBLIC 8ed0 0 trt_plugin::KPG_Plugin::clone() const
PUBLIC 8f90 0 trt_plugin::KPG_PluginCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 9270 0 trt_plugin::KPG_Plugin::KPG_Plugin(void const*, unsigned long)
PUBLIC 92b0 0 trt_plugin::KPG_PluginCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 9380 0 trt_plugin::KPG_PluginCreator::KPG_PluginCreator()
PUBLIC 95d0 0 std::ctype<char>::do_widen(char) const
PUBLIC 95e0 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 95f0 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 9600 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 9610 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 9620 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 9630 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 9640 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 9660 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 9670 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 9680 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 9690 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 96a0 0 trt_plugin::BaseCreator::getPluginNamespace() const
PUBLIC 96b0 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 96e0 0 nvinfer1::PluginRegistrar<trt_plugin::KPG_PluginCreator>::~PluginRegistrar()
PUBLIC 9710 0 std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::~vector()
PUBLIC 9720 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 9770 0 trt_plugin::BaseCreator::setPluginNamespace(char const*)
PUBLIC 97b0 0 nvinfer1::plugin::caughtError(std::exception const&)
PUBLIC 9920 0 void std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::_M_realloc_insert<nvinfer1::PluginField>(__gnu_cxx::__normal_iterator<nvinfer1::PluginField*, std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> > >, nvinfer1::PluginField&&)
PUBLIC 9ac0 0 nvinfer1::plugin::TRTException::log(std::ostream&) const
PUBLIC 9ce0 0 nvinfer1::plugin::throwCudaError(char const*, char const*, int, int, char const*)
PUBLIC 9d90 0 nvinfer1::plugin::throwCublasError(char const*, char const*, int, int, char const*)
PUBLIC 9f20 0 nvinfer1::plugin::throwCudnnError(char const*, char const*, int, int, char const*)
PUBLIC 9fd0 0 nvinfer1::plugin::logError(char const*, char const*, char const*, int)
PUBLIC a370 0 nvinfer1::plugin::reportValidationFailure(char const*, char const*, int)
PUBLIC a760 0 nvinfer1::plugin::throwPluginError(char const*, char const*, int, int, char const*)
PUBLIC a810 0 nvinfer1::plugin::reportAssertion(char const*, char const*, int)
PUBLIC ac30 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC ac50 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC ac90 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC acb0 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC acf0 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC ad10 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC ad50 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC ad70 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC adb0 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC add0 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC ae10 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC aec0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC af60 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b010 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b0a0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b150 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b1f0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b2a0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b340 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b3f0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b480 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b530 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b5d0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b680 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b710 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b7c0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b850 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC b8b0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC b910 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b970 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC b9d0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC ba30 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC ba90 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC baf0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC bb50 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::sync()
PUBLIC bd00 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::sync()
PUBLIC beb0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::sync()
PUBLIC c060 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::sync()
PUBLIC c210 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c270 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c2d0 0 _fini
STACK CFI INIT 88c4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8930 50 .cfa: sp 0 + .ra: x30
STACK CFI 8940 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8948 x19: .cfa -16 + ^
STACK CFI 8978 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9600 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9610 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9640 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89f0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8aa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ac0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ad0 60 .cfa: sp 0 + .ra: x30
STACK CFI 8ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ae4 x19: .cfa -16 + ^
STACK CFI 8b20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96b0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 96e0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9710 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b30 28 .cfa: sp 0 + .ra: x30
STACK CFI 8b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b3c x19: .cfa -16 + ^
STACK CFI 8b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9720 48 .cfa: sp 0 + .ra: x30
STACK CFI 9724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9738 x19: .cfa -16 + ^
STACK CFI 9764 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b60 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8b64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8b6c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8b7c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c1c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8cd8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8d50 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8d54 .cfa: sp 128 +
STACK CFI 8d58 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d64 x19: .cfa -16 + ^
STACK CFI 8de8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8dec .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8e00 40 .cfa: sp 0 + .ra: x30
STACK CFI 8e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e40 48 .cfa: sp 0 + .ra: x30
STACK CFI 8e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e64 x19: .cfa -16 + ^
STACK CFI 8e7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9770 40 .cfa: sp 0 + .ra: x30
STACK CFI 9774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 977c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 97ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 97b0 168 .cfa: sp 0 + .ra: x30
STACK CFI 97b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 97c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 97d0 x21: .cfa -16 + ^
STACK CFI 9884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9888 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 98c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 98cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8e90 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ed0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8ed4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8edc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8f50 x21: .cfa -16 + ^
STACK CFI 8f68 x21: x21
STACK CFI 8f8c x21: .cfa -16 + ^
STACK CFI INIT 8f90 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 8f94 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8fa0 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8fb8 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 91a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 91ac .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 9218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 921c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9270 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 92b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 92b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 931c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9920 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 9924 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9934 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9940 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9958 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9a84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9380 244 .cfa: sp 0 + .ra: x30
STACK CFI 9384 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9390 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 93a8 x21: .cfa -48 + ^
STACK CFI 94e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 94e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8470 9c .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 847c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ac30 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac50 38 .cfa: sp 0 + .ra: x30
STACK CFI ac54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac64 x19: .cfa -16 + ^
STACK CFI ac84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT acb0 38 .cfa: sp 0 + .ra: x30
STACK CFI acb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acc4 x19: .cfa -16 + ^
STACK CFI ace4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT acf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad10 38 .cfa: sp 0 + .ra: x30
STACK CFI ad14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad24 x19: .cfa -16 + ^
STACK CFI ad44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad70 38 .cfa: sp 0 + .ra: x30
STACK CFI ad74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad84 x19: .cfa -16 + ^
STACK CFI ada4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT adb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT add0 38 .cfa: sp 0 + .ra: x30
STACK CFI add4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ade4 x19: .cfa -16 + ^
STACK CFI ae04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae10 b0 .cfa: sp 0 + .ra: x30
STACK CFI ae14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae34 x21: .cfa -16 + ^
STACK CFI aebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT af60 a4 .cfa: sp 0 + .ra: x30
STACK CFI af64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI af74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af84 x21: .cfa -16 + ^
STACK CFI b000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b0a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b0a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b0b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0c4 x21: .cfa -16 + ^
STACK CFI b14c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b1f0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b1f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b204 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b214 x21: .cfa -16 + ^
STACK CFI b29c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b340 a4 .cfa: sp 0 + .ra: x30
STACK CFI b344 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b354 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b364 x21: .cfa -16 + ^
STACK CFI b3e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b480 b0 .cfa: sp 0 + .ra: x30
STACK CFI b484 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b494 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b4a4 x21: .cfa -16 + ^
STACK CFI b52c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b5d0 a4 .cfa: sp 0 + .ra: x30
STACK CFI b5d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b5f4 x21: .cfa -16 + ^
STACK CFI b670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b710 a4 .cfa: sp 0 + .ra: x30
STACK CFI b714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b724 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b734 x21: .cfa -16 + ^
STACK CFI b7b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b850 54 .cfa: sp 0 + .ra: x30
STACK CFI b854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b868 x19: .cfa -16 + ^
STACK CFI b8a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b8b0 54 .cfa: sp 0 + .ra: x30
STACK CFI b8b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b8c8 x19: .cfa -16 + ^
STACK CFI b900 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b910 54 .cfa: sp 0 + .ra: x30
STACK CFI b914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b928 x19: .cfa -16 + ^
STACK CFI b960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b970 54 .cfa: sp 0 + .ra: x30
STACK CFI b974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b988 x19: .cfa -16 + ^
STACK CFI b9c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b150 98 .cfa: sp 0 + .ra: x30
STACK CFI b154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b168 x19: .cfa -16 + ^
STACK CFI b1e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aec0 98 .cfa: sp 0 + .ra: x30
STACK CFI aec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aed8 x19: .cfa -16 + ^
STACK CFI af54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b530 98 .cfa: sp 0 + .ra: x30
STACK CFI b534 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b548 x19: .cfa -16 + ^
STACK CFI b5c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b2a0 98 .cfa: sp 0 + .ra: x30
STACK CFI b2a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2b8 x19: .cfa -16 + ^
STACK CFI b334 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b9d0 60 .cfa: sp 0 + .ra: x30
STACK CFI b9d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b9e8 x19: .cfa -16 + ^
STACK CFI ba2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba30 60 .cfa: sp 0 + .ra: x30
STACK CFI ba34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba48 x19: .cfa -16 + ^
STACK CFI ba8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba90 60 .cfa: sp 0 + .ra: x30
STACK CFI ba94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI baa8 x19: .cfa -16 + ^
STACK CFI baec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT baf0 60 .cfa: sp 0 + .ra: x30
STACK CFI baf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb08 x19: .cfa -16 + ^
STACK CFI bb4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b010 8c .cfa: sp 0 + .ra: x30
STACK CFI b014 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b028 x19: .cfa -16 + ^
STACK CFI b098 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b3f0 8c .cfa: sp 0 + .ra: x30
STACK CFI b3f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b408 x19: .cfa -16 + ^
STACK CFI b478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b7c0 8c .cfa: sp 0 + .ra: x30
STACK CFI b7c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b7d8 x19: .cfa -16 + ^
STACK CFI b848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b680 8c .cfa: sp 0 + .ra: x30
STACK CFI b684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b698 x19: .cfa -16 + ^
STACK CFI b708 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9ac0 214 .cfa: sp 0 + .ra: x30
STACK CFI 9ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9acc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9ad4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9adc x23: .cfa -16 + ^
STACK CFI 9c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9c2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT bb50 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bb54 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bb5c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bb74 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bc70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bc74 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT bd00 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bd04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bd0c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bd24 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI be20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI be24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT beb0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI beb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bebc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bed4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bfd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bfd4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT c060 1a4 .cfa: sp 0 + .ra: x30
STACK CFI c064 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c06c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c084 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c184 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9ce0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9ce4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9cf4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9d90 184 .cfa: sp 0 + .ra: x30
STACK CFI 9d94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9da4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9f20 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9f24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9f34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9fd0 394 .cfa: sp 0 + .ra: x30
STACK CFI 9fd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9fdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9fec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9ff4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a274 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a2b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a2bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT c210 54 .cfa: sp 0 + .ra: x30
STACK CFI c214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c228 x19: .cfa -16 + ^
STACK CFI c260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c270 60 .cfa: sp 0 + .ra: x30
STACK CFI c274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c288 x19: .cfa -16 + ^
STACK CFI c2cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a370 3ec .cfa: sp 0 + .ra: x30
STACK CFI a374 .cfa: sp 528 +
STACK CFI a378 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI a380 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI a388 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI a394 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI a3a0 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI a618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a61c .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT a760 a8 .cfa: sp 0 + .ra: x30
STACK CFI a764 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a774 x19: .cfa -64 + ^
STACK CFI INIT a810 414 .cfa: sp 0 + .ra: x30
STACK CFI a814 .cfa: sp 512 +
STACK CFI a818 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI a820 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI a830 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI a83c x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI a844 x25: .cfa -448 + ^
STACK CFI INIT 8510 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 851c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8528 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 884c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8854 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
