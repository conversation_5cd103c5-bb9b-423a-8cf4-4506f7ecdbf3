MODULE Linux arm64 D097ED124C120C841DE60795C5A657170 libebl_sparc.so
INFO CODE_ID 12ED97D0124C840C1DE60795C5A6571793510B66
PUBLIC 20c0 0 sparc_init
STACK CFI INIT 1ec8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ef8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f38 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f44 x19: .cfa -16 + ^
STACK CFI 1f7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f88 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fb8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fe8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ff8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2008 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2018 a4 .cfa: sp 0 + .ra: x30
STACK CFI 201c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2024 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2094 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 20c0 154 .cfa: sp 0 + .ra: x30
STACK CFI 20cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20dc x19: .cfa -16 + ^
STACK CFI 21f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 21fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 220c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2218 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2238 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 148 .cfa: sp 0 + .ra: x30
STACK CFI 2254 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 225c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2274 x23: .cfa -112 + ^
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22b4 .cfa: sp 160 + .ra: .cfa -152 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI 22bc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 236c x19: x19 x20: x20
STACK CFI 2370 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2374 x19: x19 x20: x20
STACK CFI 2378 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2384 x19: x19 x20: x20
STACK CFI 2394 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI INIT 2398 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23e8 21c .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23f8 x19: .cfa -16 + ^
STACK CFI 24b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 24b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2608 27c .cfa: sp 0 + .ra: x30
STACK CFI 260c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2614 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2624 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2640 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 26e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26e8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2888 21c .cfa: sp 0 + .ra: x30
STACK CFI 288c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2898 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 28b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2934 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 297c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2980 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2aa8 21c .cfa: sp 0 + .ra: x30
STACK CFI 2aac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ab8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ac8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ad0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ba0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2cc8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cf8 124 .cfa: sp 0 + .ra: x30
STACK CFI 2cfc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2d28 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2d30 x25: .cfa -16 + ^
STACK CFI 2d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2d70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2e08 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2e20 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e48 8 .cfa: sp 0 + .ra: x30
