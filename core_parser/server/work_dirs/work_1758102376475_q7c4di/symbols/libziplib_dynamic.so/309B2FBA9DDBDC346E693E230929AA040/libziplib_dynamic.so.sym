MODULE Linux arm64 309B2FBA9DDBDC346E693E230929AA040 libziplib_dynamic.so
INFO CODE_ID BA2F9B30DB9D34DC6E693E230929AA04
PUBLIC 5614 0 GetFilePosU(_IO_FILE*)
PUBLIC 566c 0 FileExists(char const*)
PUBLIC 56ac 0 dosdatetime2filetime(unsigned short, unsigned short)
PUBLIC 575c 0 LocalFileTimeToFileTime(long*, long*)
PUBLIC 5784 0 timet2filetime(unsigned long)
PUBLIC 5798 0 inflate_flush(inflate_blocks_state*, z_stream_s*, int)
PUBLIC 5a68 0 inflate_codes_new(unsigned int, unsigned int, inflate_huft_s const*, inflate_huft_s const*, z_stream_s*)
PUBLIC 5b10 0 inflate_codes(inflate_blocks_state*, z_stream_s*, int)
PUBLIC 6d74 0 inflate_codes_free(inflate_codes_state*, z_stream_s*)
PUBLIC 6db0 0 inflate_blocks_reset(inflate_blocks_state*, z_stream_s*, unsigned long*)
PUBLIC 6ed4 0 inflate_blocks_new(z_stream_s*, unsigned long (*)(unsigned long, unsigned char const*, unsigned int), unsigned int)
PUBLIC 7064 0 inflate_blocks(inflate_blocks_state*, z_stream_s*, int)
PUBLIC 8ad8 0 inflate_blocks_free(inflate_blocks_state*, z_stream_s*)
PUBLIC 8b6c 0 huft_build(unsigned int*, unsigned int, unsigned int, unsigned int const*, unsigned int const*, inflate_huft_s**, unsigned int*, inflate_huft_s*, unsigned int*, unsigned int*)
PUBLIC 93ac 0 inflate_trees_bits(unsigned int*, unsigned int*, inflate_huft_s**, inflate_huft_s*, z_stream_s*)
PUBLIC 94cc 0 inflate_trees_dynamic(unsigned int, unsigned int, unsigned int*, unsigned int*, unsigned int*, inflate_huft_s**, inflate_huft_s**, inflate_huft_s*, z_stream_s*)
PUBLIC 9730 0 inflate_trees_fixed(unsigned int*, unsigned int*, inflate_huft_s const**, inflate_huft_s const**, z_stream_s*)
PUBLIC 978c 0 inflate_fast(unsigned int, unsigned int, inflate_huft_s const*, inflate_huft_s const*, inflate_blocks_state*, z_stream_s*)
PUBLIC a2a8 0 get_crc_table()
PUBLIC a2b4 0 ucrc32(unsigned long, unsigned char const*, unsigned int)
PUBLIC a584 0 Uupdate_keys(unsigned long*, char)
PUBLIC a680 0 Udecrypt_byte(unsigned long*)
PUBLIC a6c0 0 zdecode(unsigned long*, char)
PUBLIC a708 0 adler32(unsigned long, unsigned char const*, unsigned int)
PUBLIC ab54 0 zlibVersion()
PUBLIC ab60 0 zError(int)
PUBLIC ab8c 0 zcalloc(void*, unsigned int, unsigned int)
PUBLIC abc4 0 zcfree(void*, void*)
PUBLIC abf4 0 inflateReset(z_stream_s*)
PUBLIC ac9c 0 inflateEnd(z_stream_s*)
PUBLIC ad44 0 inflateInit2(z_stream_s*)
PUBLIC af5c 0 inflate(z_stream_s*, int)
PUBLIC b8ec 0 lufopen(void*, unsigned int, unsigned long, unsigned long*)
PUBLIC bad0 0 lufclose(LUFILE*)
PUBLIC bb34 0 luferror(LUFILE*)
PUBLIC bb70 0 luftell(LUFILE*)
PUBLIC bbf0 0 lufseek(LUFILE*, long, int)
PUBLIC bce8 0 lufread(void*, unsigned long, unsigned long, LUFILE*)
PUBLIC bde4 0 unzlocal_getByte(LUFILE*, int*)
PUBLIC be68 0 unzlocal_getShort(LUFILE*, unsigned long*)
PUBLIC bf08 0 unzlocal_getLong(LUFILE*, unsigned long*)
PUBLIC c018 0 strcmpcasenosensitive_internal(char const*, char const*)
PUBLIC c104 0 unzStringFileNameCompare(char const*, char const*, int)
PUBLIC c154 0 unzlocal_SearchCentralDir(LUFILE*)
PUBLIC c39c 0 unzOpenInternal(LUFILE*)
PUBLIC c7a0 0 unzClose(unz_s*)
PUBLIC c814 0 unzGetGlobalInfo(unz_s*, unz_global_info_s*)
PUBLIC c858 0 unzlocal_DosDateToTmuDate(unsigned long, tm_unz_s*)
PUBLIC c904 0 unzlocal_GetCurrentFileInfoInternal(unz_s*, unz_file_info_s*, unz_file_info_internal_s*, char*, unsigned long, void*, unsigned long, char*, unsigned long)
PUBLIC d044 0 unzGetCurrentFileInfo(unz_s*, unz_file_info_s*, char*, unsigned long, void*, unsigned long, char*, unsigned long)
PUBLIC d0b0 0 unzGoToFirstFile(unz_s*)
PUBLIC d160 0 unzGoToNextFile(unz_s*)
PUBLIC d280 0 unzLocateFile(unz_s*, char const*, int)
PUBLIC d3dc 0 unzlocal_CheckCurrentFileCoherencyHeader(unz_s*, unsigned int*, unsigned long*, unsigned int*)
PUBLIC d7ec 0 unzOpenCurrentFile(unz_s*, char const*)
PUBLIC db50 0 unzReadCurrentFile(unz_s*, void*, unsigned int, bool*)
PUBLIC e198 0 unztell(unz_s*)
PUBLIC e1ec 0 unzeof(unz_s*)
PUBLIC e254 0 unzGetLocalExtrafield(unz_s*, void*, unsigned int)
PUBLIC e3a4 0 unzCloseCurrentFile(unz_s*)
PUBLIC e4a8 0 unzGetGlobalComment(unz_s*, char*, unsigned long)
PUBLIC e5d8 0 TUnzip::Open(void*, unsigned int, unsigned long)
PUBLIC e73c 0 TUnzip::SetUnzipBaseDir(char const*)
PUBLIC e7d0 0 TUnzip::Get(int, ZIPENTRY*)
PUBLIC f0f0 0 TUnzip::Find(char const*, bool, int*, ZIPENTRY*)
PUBLIC f27c 0 EnsureDirectory(char const*, char const*)
PUBLIC f4d0 0 TUnzip::Unzip(int, void*, unsigned int, unsigned long)
PUBLIC fb7c 0 TUnzip::Close()
PUBLIC fbe8 0 FormatZipMessageU(unsigned long, char*, unsigned int)
PUBLIC 1002c 0 OpenZipInternal(void*, unsigned int, unsigned long, char const*)
PUBLIC 1011c 0 OpenZipHandle(_IO_FILE*, char const*)
PUBLIC 10150 0 OpenZip(char const*, char const*)
PUBLIC 10184 0 OpenZip(void*, unsigned int, char const*)
PUBLIC 101bc 0 GetZipItem(HZIP__*, int, ZIPENTRY*)
PUBLIC 10284 0 FindZipItem(HZIP__*, char const*, bool, int*, ZIPENTRY*)
PUBLIC 10344 0 UnzipItemInternal(HZIP__*, int, void*, unsigned int, unsigned long)
PUBLIC 10404 0 UnzipItemHandle(HZIP__*, int, _IO_FILE*)
PUBLIC 10440 0 UnzipItem(HZIP__*, int, char const*)
PUBLIC 1047c 0 UnzipItem(HZIP__*, int, void*, unsigned int)
PUBLIC 104bc 0 SetUnzipBaseDir(HZIP__*, char const*)
PUBLIC 10564 0 CloseZipU(HZIP__*)
PUBLIC 10640 0 IsZipHandleU(HZIP__*)
PUBLIC 10680 0 TUnzip::TUnzip(char const*)
PUBLIC 1070c 0 TUnzip::~TUnzip()
PUBLIC 10794 0 TTreeState::TTreeState()
PUBLIC 10908 0 filetime2dosdatetime(long, unsigned short*, unsigned short*)
PUBLIC 10a34 0 GetNow(long*, unsigned short*, unsigned short*)
PUBLIC 10a84 0 GetFilePosZ(_IO_FILE*)
PUBLIC 10adc 0 GetFileInfo(_IO_FILE*, unsigned long*, long*, iztimes*, unsigned long*)
PUBLIC 10c3c 0 Assert(TState&, bool, char const*)
PUBLIC 10c78 0 ZipTrace(char const*, ...)
PUBLIC 10cf0 0 ZipTracec(bool, char const*, ...)
PUBLIC 10d68 0 ct_init(TState&, unsigned short*)
PUBLIC 11268 0 init_block(TState&)
PUBLIC 113ac 0 pqdownheap(TState&, ct_data*, int)
PUBLIC 11658 0 gen_bitlen(TState&, tree_desc*)
PUBLIC 11bac 0 gen_codes(TState&, ct_data*, int)
PUBLIC 11d24 0 build_tree(TState&, tree_desc*)
PUBLIC 121cc 0 scan_tree(TState&, ct_data*, int)
PUBLIC 12444 0 send_tree(TState&, ct_data*, int)
PUBLIC 12778 0 build_bl_tree(TState&)
PUBLIC 1289c 0 send_all_trees(TState&, int, int, int)
PUBLIC 12aa4 0 flush_block(TState&, char*, unsigned long, int)
PUBLIC 12f34 0 ct_tally(TState&, int, int)
PUBLIC 133f0 0 compress_block(TState&, ct_data*, ct_data*)
PUBLIC 13710 0 set_file_type(TState&)
PUBLIC 13828 0 bi_init(TState&, char*, unsigned int, int)
PUBLIC 138a8 0 send_bits(TState&, int, int)
PUBLIC 13ac0 0 bi_reverse(unsigned int, int)
PUBLIC 13b24 0 bi_windup(TState&)
PUBLIC 13d88 0 copy_block(TState&, char*, unsigned int, int)
PUBLIC 140fc 0 lm_init(TState&, int, unsigned short*)
PUBLIC 143d4 0 longest_match(TState&, unsigned int)
PUBLIC 147ec 0 fill_window(TState&)
PUBLIC 14ae0 0 deflate_fast(TState&)
PUBLIC 150f8 0 deflate(TState&)
PUBLIC 15898 0 putlocal(zlist*, unsigned int (*)(void*, char const*, unsigned int), void*)
PUBLIC 15e2c 0 putextended(zlist*, unsigned int (*)(void*, char const*, unsigned int), void*)
PUBLIC 160d8 0 putcentral(zlist*, unsigned int (*)(void*, char const*, unsigned int), void*)
PUBLIC 16954 0 putend(int, unsigned long, unsigned long, unsigned long, char*, unsigned int (*)(void*, char const*, unsigned int), void*)
PUBLIC 16ce8 0 crc32(unsigned long, unsigned char const*, unsigned long)
PUBLIC 16fb8 0 update_keys(unsigned long*, char)
PUBLIC 170b4 0 decrypt_byte(unsigned long*)
PUBLIC 170f4 0 zencode(unsigned long*, char)
PUBLIC 17148 0 lustricmp(char const*, char const*)
PUBLIC 1721c 0 HasZipSuffix(char const*)
PUBLIC 1740c 0 TZip::Create(void*, unsigned int, unsigned long)
PUBLIC 17604 0 TZip::sflush(void*, char const*, unsigned int*)
PUBLIC 1767c 0 TZip::swrite(void*, char const*, unsigned int)
PUBLIC 176d0 0 TZip::write(char const*, unsigned int)
PUBLIC 178f4 0 TZip::oseek(unsigned int)
PUBLIC 179e0 0 TZip::GetMemory(void**, unsigned long*)
PUBLIC 17a88 0 TZip::Close()
PUBLIC 17b18 0 TZip::open_file(char const*)
PUBLIC 17bec 0 TZip::open_handle(_IO_FILE*, unsigned int)
PUBLIC 17db4 0 TZip::open_mem(void*, unsigned int)
PUBLIC 17ec4 0 TZip::open_dir()
PUBLIC 17f90 0 TZip::sread(TState&, char*, unsigned int)
PUBLIC 17fd0 0 TZip::read(char*, unsigned int)
PUBLIC 18180 0 TZip::iclose()
PUBLIC 18230 0 TZip::ideflate(zlist*)
PUBLIC 18408 0 TZip::istore()
PUBLIC 184b4 0 TZip::Add(char const*, void*, unsigned int, unsigned long)
PUBLIC 18fb4 0 TZip::AddCentral()
PUBLIC 1917c 0 FormatZipMessageZ(unsigned long, char*, unsigned int)
PUBLIC 19598 0 CreateZipInternal(void*, unsigned int, unsigned long, char const*)
PUBLIC 19688 0 CreateZipHandle(_IO_FILE*, char const*)
PUBLIC 196bc 0 CreateZip(char const*, char const*)
PUBLIC 196f0 0 CreateZip(void*, unsigned int, char const*)
PUBLIC 19728 0 ZipAddInternal(HZIP__*, char const*, void*, unsigned int, unsigned long)
PUBLIC 197e8 0 ZipAdd(HZIP__*, char const*, char const*)
PUBLIC 19824 0 ZipAdd(HZIP__*, char const*, void*, unsigned int)
PUBLIC 19864 0 ZipAddHandle(HZIP__*, char const*, _IO_FILE*)
PUBLIC 198a0 0 ZipAddHandle(HZIP__*, char const*, _IO_FILE*, unsigned int)
PUBLIC 198e0 0 ZipAddFolder(HZIP__*, char const*)
PUBLIC 19918 0 ZipGetMemory(HZIP__*, void**, unsigned long*)
PUBLIC 199f0 0 CloseZipZ(HZIP__*)
PUBLIC 19acc 0 IsZipHandleZ(HZIP__*)
PUBLIC 19b0c 0 TDeflateState::TDeflateState()
PUBLIC 19b2c 0 TZip::TZip(char const*)
PUBLIC 19c08 0 TZip::~TZip()
PUBLIC 19cc4 0 TState::TState()
STACK CFI INIT 5554 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5584 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 55c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 55d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55d8 x19: .cfa -16 + ^
STACK CFI 5608 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5610 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5614 58 .cfa: sp 0 + .ra: x30
STACK CFI 5620 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 566c 40 .cfa: sp 0 + .ra: x30
STACK CFI 5678 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 56a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 56ac b0 .cfa: sp 0 + .ra: x30
STACK CFI 56b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5758 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 575c 28 .cfa: sp 0 + .ra: x30
STACK CFI 5760 .cfa: sp 16 +
STACK CFI 5780 .cfa: sp 0 +
STACK CFI INIT 5784 14 .cfa: sp 0 + .ra: x30
STACK CFI 5788 .cfa: sp 16 +
STACK CFI 5794 .cfa: sp 0 +
STACK CFI INIT 5798 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 57a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a68 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5a74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b10 1264 .cfa: sp 0 + .ra: x30
STACK CFI 5b1c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6d70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d74 3c .cfa: sp 0 + .ra: x30
STACK CFI 6d80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6dac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6db0 124 .cfa: sp 0 + .ra: x30
STACK CFI 6dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ed0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6ed4 190 .cfa: sp 0 + .ra: x30
STACK CFI 6ee0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7064 1a74 .cfa: sp 0 + .ra: x30
STACK CFI 7070 .cfa: sp 160 +
STACK CFI 7074 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8ad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ad8 94 .cfa: sp 0 + .ra: x30
STACK CFI 8ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8b6c 840 .cfa: sp 0 + .ra: x30
STACK CFI 8b70 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 8b78 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^
STACK CFI 93a8 .cfa: sp 0 + x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 93ac 120 .cfa: sp 0 + .ra: x30
STACK CFI 93b8 .cfa: sp 96 +
STACK CFI 93bc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 94c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 94cc 264 .cfa: sp 0 + .ra: x30
STACK CFI 94d8 .cfa: sp 112 +
STACK CFI 94dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 972c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9730 5c .cfa: sp 0 + .ra: x30
STACK CFI 9734 .cfa: sp 48 +
STACK CFI 9788 .cfa: sp 0 +
STACK CFI INIT 978c b1c .cfa: sp 0 + .ra: x30
STACK CFI 9790 .cfa: sp 128 +
STACK CFI a2a4 .cfa: sp 0 +
STACK CFI INIT a2a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a2b4 2d0 .cfa: sp 0 + .ra: x30
STACK CFI a2b8 .cfa: sp 32 +
STACK CFI a580 .cfa: sp 0 +
STACK CFI INIT a584 fc .cfa: sp 0 + .ra: x30
STACK CFI a588 .cfa: sp 16 +
STACK CFI a67c .cfa: sp 0 +
STACK CFI INIT a680 40 .cfa: sp 0 + .ra: x30
STACK CFI a684 .cfa: sp 32 +
STACK CFI a6bc .cfa: sp 0 +
STACK CFI INIT a6c0 48 .cfa: sp 0 + .ra: x30
STACK CFI a6cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a704 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a708 44c .cfa: sp 0 + .ra: x30
STACK CFI a70c .cfa: sp 64 +
STACK CFI ab50 .cfa: sp 0 +
STACK CFI INIT ab54 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ab60 2c .cfa: sp 0 + .ra: x30
STACK CFI ab64 .cfa: sp 16 +
STACK CFI ab88 .cfa: sp 0 +
STACK CFI INIT ab8c 38 .cfa: sp 0 + .ra: x30
STACK CFI ab98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT abc4 30 .cfa: sp 0 + .ra: x30
STACK CFI abd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT abf4 a8 .cfa: sp 0 + .ra: x30
STACK CFI ac00 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ac9c a8 .cfa: sp 0 + .ra: x30
STACK CFI aca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad44 218 .cfa: sp 0 + .ra: x30
STACK CFI ad50 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ad58 x19: .cfa -48 + ^
STACK CFI af58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af5c 990 .cfa: sp 0 + .ra: x30
STACK CFI af68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b8e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b8ec 1e4 .cfa: sp 0 + .ra: x30
STACK CFI b8f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bacc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bad0 64 .cfa: sp 0 + .ra: x30
STACK CFI badc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bb34 3c .cfa: sp 0 + .ra: x30
STACK CFI bb38 .cfa: sp 16 +
STACK CFI bb6c .cfa: sp 0 +
STACK CFI INIT bb70 80 .cfa: sp 0 + .ra: x30
STACK CFI bb7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bbec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bbf0 f8 .cfa: sp 0 + .ra: x30
STACK CFI bbfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bce8 fc .cfa: sp 0 + .ra: x30
STACK CFI bcf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bde0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bde4 84 .cfa: sp 0 + .ra: x30
STACK CFI bdf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI be64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be68 a0 .cfa: sp 0 + .ra: x30
STACK CFI be74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bf04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bf08 110 .cfa: sp 0 + .ra: x30
STACK CFI bf14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c018 ec .cfa: sp 0 + .ra: x30
STACK CFI c01c .cfa: sp 32 +
STACK CFI c100 .cfa: sp 0 +
STACK CFI INIT c104 50 .cfa: sp 0 + .ra: x30
STACK CFI c110 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c150 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c154 248 .cfa: sp 0 + .ra: x30
STACK CFI c160 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c398 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c39c 404 .cfa: sp 0 + .ra: x30
STACK CFI c3a8 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI c79c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c7a0 74 .cfa: sp 0 + .ra: x30
STACK CFI c7ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c810 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c814 44 .cfa: sp 0 + .ra: x30
STACK CFI c818 .cfa: sp 32 +
STACK CFI c854 .cfa: sp 0 +
STACK CFI INIT c858 ac .cfa: sp 0 + .ra: x30
STACK CFI c85c .cfa: sp 32 +
STACK CFI c900 .cfa: sp 0 +
STACK CFI INIT c904 740 .cfa: sp 0 + .ra: x30
STACK CFI c910 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI d040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d044 6c .cfa: sp 0 + .ra: x30
STACK CFI d050 .cfa: sp 96 +
STACK CFI d054 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d0ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d0b0 b0 .cfa: sp 0 + .ra: x30
STACK CFI d0bc .cfa: sp 64 +
STACK CFI d0c0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d15c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d160 120 .cfa: sp 0 + .ra: x30
STACK CFI d16c .cfa: sp 64 +
STACK CFI d170 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d27c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d280 15c .cfa: sp 0 + .ra: x30
STACK CFI d28c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI d3d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d3dc 410 .cfa: sp 0 + .ra: x30
STACK CFI d3e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d7e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d7ec 364 .cfa: sp 0 + .ra: x30
STACK CFI d7f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI db4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT db50 648 .cfa: sp 0 + .ra: x30
STACK CFI db5c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI db64 x19: .cfa -144 + ^
STACK CFI e194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e198 54 .cfa: sp 0 + .ra: x30
STACK CFI e19c .cfa: sp 32 +
STACK CFI e1e8 .cfa: sp 0 +
STACK CFI INIT e1ec 68 .cfa: sp 0 + .ra: x30
STACK CFI e1f0 .cfa: sp 32 +
STACK CFI e250 .cfa: sp 0 +
STACK CFI INIT e254 150 .cfa: sp 0 + .ra: x30
STACK CFI e260 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e3a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e3a4 104 .cfa: sp 0 + .ra: x30
STACK CFI e3b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e4a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e4a8 130 .cfa: sp 0 + .ra: x30
STACK CFI e4b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e5d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10680 8c .cfa: sp 0 + .ra: x30
STACK CFI 1068c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10708 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1070c 88 .cfa: sp 0 + .ra: x30
STACK CFI 10718 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10790 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e5d8 164 .cfa: sp 0 + .ra: x30
STACK CFI e5e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e738 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e73c 94 .cfa: sp 0 + .ra: x30
STACK CFI e748 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e7cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e7d0 920 .cfa: sp 0 + .ra: x30
STACK CFI e7dc .cfa: sp 2368 +
STACK CFI e7e0 .ra: .cfa -2360 + ^ x29: .cfa -2368 + ^
STACK CFI f0ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f0f0 18c .cfa: sp 0 + .ra: x30
STACK CFI f0fc .cfa: sp 1120 +
STACK CFI f100 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI f278 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f27c 254 .cfa: sp 0 + .ra: x30
STACK CFI f288 .cfa: sp 1104 +
STACK CFI f28c .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI f4cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f4d0 6ac .cfa: sp 0 + .ra: x30
STACK CFI f4dc .cfa: sp 3280 +
STACK CFI f4e0 .ra: .cfa -3272 + ^ x29: .cfa -3280 + ^
STACK CFI fb78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fb7c 6c .cfa: sp 0 + .ra: x30
STACK CFI fb88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fbe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fbe8 444 .cfa: sp 0 + .ra: x30
STACK CFI fbf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10028 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1002c f0 .cfa: sp 0 + .ra: x30
STACK CFI 10038 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10040 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1011c 34 .cfa: sp 0 + .ra: x30
STACK CFI 10128 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1014c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10150 34 .cfa: sp 0 + .ra: x30
STACK CFI 1015c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10180 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10184 38 .cfa: sp 0 + .ra: x30
STACK CFI 10190 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 101b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 101bc c8 .cfa: sp 0 + .ra: x30
STACK CFI 101c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10280 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10284 c0 .cfa: sp 0 + .ra: x30
STACK CFI 10290 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10344 c0 .cfa: sp 0 + .ra: x30
STACK CFI 10350 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10400 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10404 3c .cfa: sp 0 + .ra: x30
STACK CFI 10410 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1043c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10440 3c .cfa: sp 0 + .ra: x30
STACK CFI 1044c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10478 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1047c 40 .cfa: sp 0 + .ra: x30
STACK CFI 10488 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 104b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 104bc a8 .cfa: sp 0 + .ra: x30
STACK CFI 104c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10560 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10564 dc .cfa: sp 0 + .ra: x30
STACK CFI 10570 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10578 x19: .cfa -48 + ^
STACK CFI 1063c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10640 40 .cfa: sp 0 + .ra: x30
STACK CFI 10644 .cfa: sp 32 +
STACK CFI 1067c .cfa: sp 0 +
STACK CFI INIT 10794 174 .cfa: sp 0 + .ra: x30
STACK CFI 10798 .cfa: sp 144 +
STACK CFI 10904 .cfa: sp 0 +
STACK CFI INIT 19b0c 20 .cfa: sp 0 + .ra: x30
STACK CFI 19b10 .cfa: sp 16 +
STACK CFI 19b28 .cfa: sp 0 +
STACK CFI INIT 10908 12c .cfa: sp 0 + .ra: x30
STACK CFI 10914 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10a30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10a34 50 .cfa: sp 0 + .ra: x30
STACK CFI 10a40 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10a84 58 .cfa: sp 0 + .ra: x30
STACK CFI 10a90 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 10ad8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10adc 160 .cfa: sp 0 + .ra: x30
STACK CFI 10ae8 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 10c38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10c3c 3c .cfa: sp 0 + .ra: x30
STACK CFI 10c40 .cfa: sp 32 +
STACK CFI 10c74 .cfa: sp 0 +
STACK CFI INIT 10c78 78 .cfa: sp 0 + .ra: x30
STACK CFI 10c7c .cfa: sp 240 +
STACK CFI 10cec .cfa: sp 0 +
STACK CFI INIT 10cf0 78 .cfa: sp 0 + .ra: x30
STACK CFI 10cf4 .cfa: sp 224 +
STACK CFI 10d64 .cfa: sp 0 +
STACK CFI INIT 10d68 500 .cfa: sp 0 + .ra: x30
STACK CFI 10d74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11264 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11268 144 .cfa: sp 0 + .ra: x30
STACK CFI 1126c .cfa: sp 32 +
STACK CFI 113a8 .cfa: sp 0 +
STACK CFI INIT 113ac 2ac .cfa: sp 0 + .ra: x30
STACK CFI 113b0 .cfa: sp 48 +
STACK CFI 11654 .cfa: sp 0 +
STACK CFI INIT 11658 554 .cfa: sp 0 + .ra: x30
STACK CFI 11664 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11bac 178 .cfa: sp 0 + .ra: x30
STACK CFI 11bb8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11d24 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 11d30 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 121c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 121cc 278 .cfa: sp 0 + .ra: x30
STACK CFI 121d0 .cfa: sp 64 +
STACK CFI 12440 .cfa: sp 0 +
STACK CFI INIT 12444 334 .cfa: sp 0 + .ra: x30
STACK CFI 12450 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12774 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12778 124 .cfa: sp 0 + .ra: x30
STACK CFI 12784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12898 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1289c 208 .cfa: sp 0 + .ra: x30
STACK CFI 128a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12aa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12aa4 490 .cfa: sp 0 + .ra: x30
STACK CFI 12ab0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12f30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12f34 4bc .cfa: sp 0 + .ra: x30
STACK CFI 12f40 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 133ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 133f0 320 .cfa: sp 0 + .ra: x30
STACK CFI 133fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1370c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13710 118 .cfa: sp 0 + .ra: x30
STACK CFI 13714 .cfa: sp 32 +
STACK CFI 13824 .cfa: sp 0 +
STACK CFI INIT 13828 80 .cfa: sp 0 + .ra: x30
STACK CFI 1382c .cfa: sp 32 +
STACK CFI 138a4 .cfa: sp 0 +
STACK CFI INIT 138a8 218 .cfa: sp 0 + .ra: x30
STACK CFI 138b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13abc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13ac0 64 .cfa: sp 0 + .ra: x30
STACK CFI 13ac4 .cfa: sp 32 + x19: .cfa -32 + ^
STACK CFI 13b20 .cfa: sp 0 + x19: x19
STACK CFI INIT 13b24 264 .cfa: sp 0 + .ra: x30
STACK CFI 13b30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13d84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13d88 374 .cfa: sp 0 + .ra: x30
STACK CFI 13d94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 140f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 140fc 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 14108 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14110 x19: .cfa -48 + ^
STACK CFI 143d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 143d4 418 .cfa: sp 0 + .ra: x30
STACK CFI 143e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 143f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 147e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 147ec 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 147f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14800 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 14ae0 618 .cfa: sp 0 + .ra: x30
STACK CFI 14aec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 150f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 150f8 7a0 .cfa: sp 0 + .ra: x30
STACK CFI 15104 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1510c x19: .cfa -64 + ^
STACK CFI 15894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15898 594 .cfa: sp 0 + .ra: x30
STACK CFI 158a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15e28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15e2c 2ac .cfa: sp 0 + .ra: x30
STACK CFI 15e38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 160d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 160d8 87c .cfa: sp 0 + .ra: x30
STACK CFI 160e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16950 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16954 394 .cfa: sp 0 + .ra: x30
STACK CFI 16960 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16ce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16ce8 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 16cec .cfa: sp 32 +
STACK CFI 16fb4 .cfa: sp 0 +
STACK CFI INIT 16fb8 fc .cfa: sp 0 + .ra: x30
STACK CFI 16fbc .cfa: sp 16 +
STACK CFI 170b0 .cfa: sp 0 +
STACK CFI INIT 170b4 40 .cfa: sp 0 + .ra: x30
STACK CFI 170b8 .cfa: sp 32 +
STACK CFI 170f0 .cfa: sp 0 +
STACK CFI INIT 170f4 54 .cfa: sp 0 + .ra: x30
STACK CFI 17100 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17148 d4 .cfa: sp 0 + .ra: x30
STACK CFI 17154 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17218 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1721c 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 17228 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19b2c dc .cfa: sp 0 + .ra: x30
STACK CFI 19b38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19c04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19c08 bc .cfa: sp 0 + .ra: x30
STACK CFI 19c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19cc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1740c 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 17418 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17604 78 .cfa: sp 0 + .ra: x30
STACK CFI 17610 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17678 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1767c 54 .cfa: sp 0 + .ra: x30
STACK CFI 17688 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 176cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 176d0 224 .cfa: sp 0 + .ra: x30
STACK CFI 176dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 176e4 x19: .cfa -80 + ^
STACK CFI 178f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 178f4 ec .cfa: sp 0 + .ra: x30
STACK CFI 17900 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 179dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 179e0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 179ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17a84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17a88 90 .cfa: sp 0 + .ra: x30
STACK CFI 17a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17b18 d4 .cfa: sp 0 + .ra: x30
STACK CFI 17b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17be8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17bec 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 17bf8 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 17db0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17db4 110 .cfa: sp 0 + .ra: x30
STACK CFI 17dc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17ec0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17ec4 cc .cfa: sp 0 + .ra: x30
STACK CFI 17ed0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17f90 40 .cfa: sp 0 + .ra: x30
STACK CFI 17f9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17fcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17fd0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 17fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1817c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18180 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1818c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1822c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19cc4 40 .cfa: sp 0 + .ra: x30
STACK CFI 19cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19d00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18230 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 1823c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18244 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 18408 ac .cfa: sp 0 + .ra: x30
STACK CFI 18414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 184b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 184b4 b00 .cfa: sp 0 + .ra: x30
STACK CFI 184cc .cfa: sp 4448 +
STACK CFI 184d0 .ra: .cfa -4440 + ^ x29: .cfa -4448 + ^
STACK CFI 18fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18fb4 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 18fc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 19178 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1917c 41c .cfa: sp 0 + .ra: x30
STACK CFI 19188 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19598 f0 .cfa: sp 0 + .ra: x30
STACK CFI 195a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 195ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 19684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 19688 34 .cfa: sp 0 + .ra: x30
STACK CFI 19694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 196b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 196bc 34 .cfa: sp 0 + .ra: x30
STACK CFI 196c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 196ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 196f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 196fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19724 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19728 c0 .cfa: sp 0 + .ra: x30
STACK CFI 19734 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 197e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 197e8 3c .cfa: sp 0 + .ra: x30
STACK CFI 197f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19820 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19824 40 .cfa: sp 0 + .ra: x30
STACK CFI 19830 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19860 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19864 3c .cfa: sp 0 + .ra: x30
STACK CFI 19870 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1989c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 198a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 198ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 198dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 198e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 198ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19918 d8 .cfa: sp 0 + .ra: x30
STACK CFI 19924 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 199ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 199f0 dc .cfa: sp 0 + .ra: x30
STACK CFI 199fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19a04 x19: .cfa -48 + ^
STACK CFI 19ac8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 19acc 40 .cfa: sp 0 + .ra: x30
STACK CFI 19ad0 .cfa: sp 32 +
STACK CFI 19b08 .cfa: sp 0 +
STACK CFI INIT 19d10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d20 10 .cfa: sp 0 + .ra: x30
