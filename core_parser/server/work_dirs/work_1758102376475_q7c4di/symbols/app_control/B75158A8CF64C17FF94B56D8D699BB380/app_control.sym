MODULE Linux arm64 B75158A8CF64C17FF94B56D8D699BB380 app_control
INFO CODE_ID A85851B764CF7FC1F94B56D8D699BB38
PUBLIC 11f8 0 _init
PUBLIC 1310 0 main
PUBLIC 1478 0 _GLOBAL__sub_I_app_control.cpp
PUBLIC 14b4 0 _start
PUBLIC 1504 0 call_weak_fn
PUBLIC 1518 0 deregister_tm_clones
PUBLIC 155c 0 register_tm_clones
PUBLIC 15ac 0 __do_global_dtors_aux
PUBLIC 15dc 0 frame_dummy
PUBLIC 15e0 0 help()
PUBLIC 1690 0 __libc_csu_init
PUBLIC 1710 0 __libc_csu_fini
PUBLIC 1714 0 _fini
STACK CFI INIT 1518 44 .cfa: sp 0 + .ra: x30
STACK CFI 1534 .cfa: sp 16 +
STACK CFI 154c .cfa: sp 0 +
STACK CFI 1550 .cfa: sp 16 +
STACK CFI 1554 .cfa: sp 0 +
STACK CFI INIT 155c 50 .cfa: sp 0 + .ra: x30
STACK CFI 1584 .cfa: sp 16 +
STACK CFI 159c .cfa: sp 0 +
STACK CFI 15a0 .cfa: sp 16 +
STACK CFI 15a4 .cfa: sp 0 +
STACK CFI INIT 15ac 30 .cfa: sp 0 + .ra: x30
STACK CFI 15b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15b8 x19: .cfa -16 + ^
STACK CFI 15d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15dc 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 15e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15f4 x19: .cfa -16 + ^
STACK CFI 1684 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1310 168 .cfa: sp 0 + .ra: x30
STACK CFI 1314 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 131c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1328 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1344 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 134c x25: .cfa -80 + ^
STACK CFI 1424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1428 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1478 3c .cfa: sp 0 + .ra: x30
STACK CFI 147c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1484 x19: .cfa -16 + ^
STACK CFI 14a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1690 7c .cfa: sp 0 + .ra: x30
STACK CFI 1694 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 169c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1710 4 .cfa: sp 0 + .ra: x30
