MODULE Linux arm64 F17DF7C99BFB44843DFFBEF3E463D8BF0 libtrigger_log.so.3
INFO CODE_ID C9F77DF1FB9B84443DFFBEF3E463D8BF
PUBLIC 758 0 _init
PUBLIC 7d0 0 call_weak_fn
PUBLIC 7e4 0 deregister_tm_clones
PUBLIC 814 0 register_tm_clones
PUBLIC 850 0 __do_global_dtors_aux
PUBLIC 8a0 0 frame_dummy
PUBLIC 8a8 0 lios::trigger_log::Init()
PUBLIC 8b0 0 lios::trigger_log::Trigger()
PUBLIC 8b4 0 _fini
STACK CFI INIT 7e4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 814 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 850 50 .cfa: sp 0 + .ra: x30
STACK CFI 860 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 868 x19: .cfa -16 + ^
STACK CFI 898 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b0 4 .cfa: sp 0 + .ra: x30
