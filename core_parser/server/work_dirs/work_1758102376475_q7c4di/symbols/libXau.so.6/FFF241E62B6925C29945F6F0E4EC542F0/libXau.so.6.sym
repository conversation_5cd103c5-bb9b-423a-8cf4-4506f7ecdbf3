MODULE Linux arm64 FFF241E62B6925C29945F6F0E4EC542F0 libXau.so.6
INFO CODE_ID E641F2FF692BC2259945F6F0E4EC542F5740BB76
PUBLIC e88 0 XauDisposeAuth
PUBLIC f10 0 XauFileName
PUBLIC 1048 0 XauGetAuthByAddr
PUBLIC 11c0 0 XauGetBestAuthByAddr
PUBLIC 13a8 0 XauLockAuth
PUBLIC 1720 0 XauReadAuth
PUBLIC 1890 0 XauUnlockAuth
PUBLIC 1a00 0 XauWriteAuth
STACK CFI INIT dc8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT df8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT e38 48 .cfa: sp 0 + .ra: x30
STACK CFI e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e44 x19: .cfa -16 + ^
STACK CFI e7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e88 5c .cfa: sp 0 + .ra: x30
STACK CFI e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e98 x19: .cfa -16 + ^
STACK CFI edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ee8 28 .cfa: sp 0 + .ra: x30
STACK CFI eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ef4 x19: .cfa -16 + ^
STACK CFI f0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f10 134 .cfa: sp 0 + .ra: x30
STACK CFI f14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI f58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fc8 x21: x21 x22: x22
STACK CFI fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI fe0 x23: .cfa -16 + ^
STACK CFI 1014 x23: x23
STACK CFI 1018 x23: .cfa -16 + ^
STACK CFI 103c x21: x21 x22: x22
STACK CFI 1040 x23: x23
STACK CFI INIT 1048 178 .cfa: sp 0 + .ra: x30
STACK CFI 104c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1054 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 105c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1064 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1070 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1078 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1178 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 11bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 11c0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 11c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 11cc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 11e0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 11f8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1208 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1240 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 132c x21: x21 x22: x22
STACK CFI 1338 x27: x27 x28: x28
STACK CFI 133c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1340 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 1368 x27: x27 x28: x28
STACK CFI 136c x21: x21 x22: x22
STACK CFI 1384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1388 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 13a8 1fc .cfa: sp 0 + .ra: x30
STACK CFI 13ac .cfa: sp 2288 +
STACK CFI 13b0 .ra: .cfa -2280 + ^ x29: .cfa -2288 + ^
STACK CFI 13b8 x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 13c4 x19: .cfa -2272 + ^ x20: .cfa -2264 + ^
STACK CFI 13d0 x21: .cfa -2256 + ^ x22: .cfa -2248 + ^
STACK CFI 13d8 x25: .cfa -2224 + ^
STACK CFI 1424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1428 .cfa: sp 2288 + .ra: .cfa -2280 + ^ x19: .cfa -2272 + ^ x20: .cfa -2264 + ^ x21: .cfa -2256 + ^ x22: .cfa -2248 + ^ x23: .cfa -2240 + ^ x24: .cfa -2232 + ^ x25: .cfa -2224 + ^ x29: .cfa -2288 + ^
STACK CFI INIT 15a8 84 .cfa: sp 0 + .ra: x30
STACK CFI 15ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1630 ec .cfa: sp 0 + .ra: x30
STACK CFI 1634 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 163c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 164c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1654 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 16b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 16bc x25: .cfa -32 + ^
STACK CFI 16f0 x25: x25
STACK CFI 16f4 x25: .cfa -32 + ^
STACK CFI 16f8 x25: x25
STACK CFI 1700 x25: .cfa -32 + ^
STACK CFI 1710 x25: x25
STACK CFI 1718 x25: .cfa -32 + ^
STACK CFI INIT 1720 170 .cfa: sp 0 + .ra: x30
STACK CFI 1724 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1730 x21: .cfa -96 + ^
STACK CFI 1738 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 180c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1890 cc .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 2128 +
STACK CFI 1898 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 18a0 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 18cc x21: .cfa -2096 + ^
STACK CFI 1928 x21: x21
STACK CFI 1950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1954 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x29: .cfa -2128 + ^
STACK CFI 1958 x21: .cfa -2096 + ^
STACK CFI INIT 1960 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1964 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1970 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1980 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a00 dc .cfa: sp 0 + .ra: x30
STACK CFI 1a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a20 x21: .cfa -32 + ^
STACK CFI 1a90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1a94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1ae0 10 .cfa: sp 0 + .ra: x30
