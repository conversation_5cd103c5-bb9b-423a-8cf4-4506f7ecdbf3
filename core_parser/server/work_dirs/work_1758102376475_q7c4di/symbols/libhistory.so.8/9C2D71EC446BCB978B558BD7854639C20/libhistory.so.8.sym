MODULE Linux arm64 9C2D71EC446BCB978B558BD7854639C20 libhistory.so.8
INFO CODE_ID EC712D9C6B4497CB8B558BD7854639C265A48537
PUBLIC 2448 0 history_get_history_state
PUBLIC 24a0 0 history_set_history_state
PUBLIC 24e8 0 using_history
PUBLIC 2508 0 history_total_bytes
PUBLIC 2570 0 where_history
PUBLIC 2580 0 history_set_pos
PUBLIC 25c8 0 history_list
PUBLIC 25d8 0 current_history
PUBLIC 2618 0 previous_history
PUBLIC 2648 0 next_history
PUBLIC 2688 0 history_get
PUBLIC 26c8 0 alloc_history_entry
PUBLIC 2728 0 history_get_time
PUBLIC 27a8 0 add_history_time
PUBLIC 2820 0 free_history_entry
PUBLIC 2870 0 add_history
PUBLIC 2a80 0 copy_history_entry
PUBLIC 2af8 0 replace_history_entry
PUBLIC 2bc0 0 _hs_append_history_line
PUBLIC 2c78 0 _hs_replace_history_data
PUBLIC 2d28 0 remove_history
PUBLIC 2da8 0 remove_history_range
PUBLIC 2ec0 0 stifle_history
PUBLIC 2fc8 0 unstifle_history
PUBLIC 2ff8 0 history_is_stifled
PUBLIC 3008 0 clear_history
PUBLIC 39b8 0 get_history_event
PUBLIC 3f58 0 history_tokenize
PUBLIC 3f68 0 history_arg_extract
PUBLIC 4138 0 history_expand
PUBLIC 60b8 0 read_history_range
PUBLIC 65c0 0 read_history
PUBLIC 65d0 0 history_truncate_file
PUBLIC 6ac0 0 append_history
PUBLIC 6ad8 0 write_history
PUBLIC 6de0 0 _hs_history_patsearch
PUBLIC 6ef8 0 history_search
PUBLIC 6f00 0 history_search_prefix
PUBLIC 6f08 0 history_search_pos
PUBLIC 6f70 0 sh_single_quote
PUBLIC 6ff0 0 sh_set_lines_and_columns
PUBLIC 7078 0 sh_get_env_value
PUBLIC 7080 0 sh_get_home_dir
PUBLIC 70f8 0 sh_unset_nodelay_mode
PUBLIC 7150 0 _rl_find_prev_mbchar_internal
PUBLIC 7318 0 _rl_get_char_len
PUBLIC 73e0 0 _rl_compare_chars
PUBLIC 74a8 0 _rl_adjust_point
PUBLIC 75a0 0 _rl_is_mbchar_matched
PUBLIC 75f0 0 _rl_char_value
PUBLIC 76d8 0 _rl_find_next_mbchar
PUBLIC 7990 0 _rl_find_prev_mbchar
PUBLIC 79c8 0 xmalloc
PUBLIC 79f0 0 xrealloc
PUBLIC 7a28 0 xfree
STACK CFI INIT 2388 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2404 x19: .cfa -16 + ^
STACK CFI 243c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2448 54 .cfa: sp 0 + .ra: x30
STACK CFI 244c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2498 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24a0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2508 64 .cfa: sp 0 + .ra: x30
STACK CFI 250c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2518 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2528 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2554 x19: x19 x20: x20
STACK CFI 2560 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2564 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2570 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2580 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2618 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2648 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2688 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26c8 60 .cfa: sp 0 + .ra: x30
STACK CFI 26cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26e0 x21: .cfa -16 + ^
STACK CFI 2724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2728 7c .cfa: sp 0 + .ra: x30
STACK CFI 2730 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2738 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2768 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 27a8 74 .cfa: sp 0 + .ra: x30
STACK CFI 27b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2820 50 .cfa: sp 0 + .ra: x30
STACK CFI 2824 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 282c x19: .cfa -16 + ^
STACK CFI 2864 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2868 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2870 20c .cfa: sp 0 + .ra: x30
STACK CFI 2874 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 287c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2884 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2894 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 28ac x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 29c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 29c8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2a80 74 .cfa: sp 0 + .ra: x30
STACK CFI 2a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ae0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2af8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2afc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2b8c x23: x23 x24: x24
STACK CFI 2ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ba8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2bc0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2bd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2c78 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d28 7c .cfa: sp 0 + .ra: x30
STACK CFI 2d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2da8 118 .cfa: sp 0 + .ra: x30
STACK CFI 2dac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2db8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2dc0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2dc8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2dd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e14 x19: x19 x20: x20
STACK CFI 2e18 x25: x25 x26: x26
STACK CFI 2e2c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2e9c x19: x19 x20: x20
STACK CFI 2eb8 x25: x25 x26: x26
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 2ec0 108 .cfa: sp 0 + .ra: x30
STACK CFI 2ec4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2ecc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2ed4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2ee4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2eec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2fc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2fc8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3008 84 .cfa: sp 0 + .ra: x30
STACK CFI 300c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3014 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 301c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3090 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3118 ec .cfa: sp 0 + .ra: x30
STACK CFI 311c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3128 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3130 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3138 x23: .cfa -16 + ^
STACK CFI 31c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 31c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3208 340 .cfa: sp 0 + .ra: x30
STACK CFI 320c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3214 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3220 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3230 x27: .cfa -16 + ^
STACK CFI 3248 x27: x27
STACK CFI 325c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3260 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3264 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3268 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 332c x23: x23 x24: x24
STACK CFI 3330 x25: x25 x26: x26
STACK CFI 3334 x27: x27
STACK CFI 3338 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 333c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3414 x23: x23 x24: x24
STACK CFI 3418 x25: x25 x26: x26
STACK CFI 341c x27: x27
STACK CFI 3420 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 348c x23: x23 x24: x24
STACK CFI 3490 x25: x25 x26: x26
STACK CFI 3494 x27: x27
STACK CFI 3498 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 34ec x23: x23 x24: x24
STACK CFI 34f0 x25: x25 x26: x26
STACK CFI 34f4 x27: x27
STACK CFI 34f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 353c x23: x23 x24: x24
STACK CFI 3540 x25: x25 x26: x26
STACK CFI 3544 x27: x27
STACK CFI INIT 3548 20c .cfa: sp 0 + .ra: x30
STACK CFI 354c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3558 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3568 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3598 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 35b8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 35f0 x23: x23 x24: x24
STACK CFI 35f4 x27: x27 x28: x28
STACK CFI 3608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 360c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3758 25c .cfa: sp 0 + .ra: x30
STACK CFI 375c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3768 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3778 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3780 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 378c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 37ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 397c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3980 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 39b8 5a0 .cfa: sp 0 + .ra: x30
STACK CFI 39bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 39cc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 39ec x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3a44 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3a64 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3b14 x27: x27 x28: x28
STACK CFI 3b18 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3b4c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3bc8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 3c30 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3c64 x27: x27 x28: x28
STACK CFI 3d1c x25: x25 x26: x26
STACK CFI 3d20 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3d38 x25: x25 x26: x26
STACK CFI 3d5c x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3d80 x27: x27 x28: x28
STACK CFI 3d94 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3da8 x27: x27 x28: x28
STACK CFI 3e84 x25: x25 x26: x26
STACK CFI 3e88 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3ea8 x25: x25 x26: x26
STACK CFI 3eac x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3f28 x25: x25 x26: x26
STACK CFI 3f2c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3f4c x25: x25 x26: x26
STACK CFI 3f50 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3f54 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 3f58 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f68 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 3f6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f80 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f88 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 404c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4074 x25: .cfa -16 + ^
STACK CFI 40fc x25: x25
STACK CFI INIT 4138 186c .cfa: sp 0 + .ra: x30
STACK CFI 413c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 4160 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 416c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 4184 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 4188 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 418c x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 433c x21: x21 x22: x22
STACK CFI 4340 x23: x23 x24: x24
STACK CFI 4344 x25: x25 x26: x26
STACK CFI 4348 x27: x27 x28: x28
STACK CFI 4350 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 451c x21: x21 x22: x22
STACK CFI 4524 x23: x23 x24: x24
STACK CFI 452c x25: x25 x26: x26
STACK CFI 4530 x27: x27 x28: x28
STACK CFI 4554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4558 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 4850 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4870 x21: x21 x22: x22
STACK CFI 4878 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4e04 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4e0c x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4e7c x21: x21 x22: x22
STACK CFI 4e80 x23: x23 x24: x24
STACK CFI 4e84 x25: x25 x26: x26
STACK CFI 4e88 x27: x27 x28: x28
STACK CFI 4e8c x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 5224 x21: x21 x22: x22
STACK CFI 5228 x23: x23 x24: x24
STACK CFI 522c x25: x25 x26: x26
STACK CFI 5230 x27: x27 x28: x28
STACK CFI 5234 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 5928 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 592c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 5930 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 5934 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5938 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 59a8 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 59b0 .cfa: sp 4160 +
STACK CFI 59b8 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 59c0 x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 59d4 x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI 5b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b4c .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 5b50 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5b58 .cfa: sp 4160 +
STACK CFI 5b5c .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 5b64 x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 5b74 x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI 5bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bd8 .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 5bf8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5bfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ca0 414 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 5cb0 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 5cbc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5ce0 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 5cf4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5d00 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 5f40 x21: x21 x22: x22
STACK CFI 5f44 x25: x25 x26: x26
STACK CFI 5f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5f80 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 5fec x21: x21 x22: x22
STACK CFI 5ff0 x25: x25 x26: x26
STACK CFI 5ff4 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 6054 x21: x21 x22: x22
STACK CFI 6058 x25: x25 x26: x26
STACK CFI 6060 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6064 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 60ac x21: x21 x22: x22
STACK CFI 60b0 x25: x25 x26: x26
STACK CFI INIT 60b8 504 .cfa: sp 0 + .ra: x30
STACK CFI 60bc .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 60c4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 60cc x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 60d4 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 60e0 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6144 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 6254 x25: x25 x26: x26
STACK CFI 62c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 62c8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 62d8 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 62f0 x25: x25 x26: x26
STACK CFI 630c x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 640c x25: x25 x26: x26
STACK CFI 6410 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 64b4 x25: x25 x26: x26
STACK CFI 64d4 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 6568 x25: x25 x26: x26
STACK CFI 6570 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 659c x25: x25 x26: x26
STACK CFI 65a0 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 65b4 x25: x25 x26: x26
STACK CFI INIT 65c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 65d0 4ec .cfa: sp 0 + .ra: x30
STACK CFI 65d4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 65dc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 65e8 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 65f4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 6620 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 66a0 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6804 x25: x25 x26: x26
STACK CFI 6808 x27: x27 x28: x28
STACK CFI 680c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 685c x25: x25 x26: x26
STACK CFI 6884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6888 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 68b4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 68c0 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 68e4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 68f0 x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6900 x27: x27 x28: x28
STACK CFI 6974 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 69c4 x27: x27 x28: x28
STACK CFI 6a08 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6a5c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6a60 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 6a64 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6a68 x27: x27 x28: x28
STACK CFI 6a90 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6ab4 x25: x25 x26: x26
STACK CFI 6ab8 x27: x27 x28: x28
STACK CFI INIT 6ac0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ad8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6af0 2ec .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6afc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6b14 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6b38 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6b4c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6bfc x19: x19 x20: x20
STACK CFI 6c08 x25: x25 x26: x26
STACK CFI 6c1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6c20 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6c7c x19: x19 x20: x20
STACK CFI 6c80 x25: x25 x26: x26
STACK CFI 6c98 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6c9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6cf8 x19: x19 x20: x20
STACK CFI 6d04 x25: x25 x26: x26
STACK CFI 6d18 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6d1c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6dd0 x19: x19 x20: x20
STACK CFI 6dd8 x25: x25 x26: x26
STACK CFI INIT 6de0 118 .cfa: sp 0 + .ra: x30
STACK CFI 6de4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6df0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6dfc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6e90 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6ef8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f08 64 .cfa: sp 0 + .ra: x30
STACK CFI 6f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6f14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6f20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6f70 7c .cfa: sp 0 + .ra: x30
STACK CFI 6f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f7c x19: .cfa -16 + ^
STACK CFI 6fe8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ff0 88 .cfa: sp 0 + .ra: x30
STACK CFI 6ff4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7004 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 701c x21: .cfa -16 + ^
STACK CFI 7074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7078 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7080 74 .cfa: sp 0 + .ra: x30
STACK CFI 7084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 708c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 70a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 70ac x21: .cfa -16 + ^
STACK CFI 70e4 x21: x21
STACK CFI 70f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70f8 58 .cfa: sp 0 + .ra: x30
STACK CFI 70fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 710c x19: .cfa -16 + ^
STACK CFI 712c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7130 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7150 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 7154 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7164 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 717c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7194 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 71c4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 727c x19: x19 x20: x20
STACK CFI 7280 x25: x25 x26: x26
STACK CFI 72ac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 72b0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 7300 x19: x19 x20: x20
STACK CFI 7308 x25: x25 x26: x26
STACK CFI 7310 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 7314 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 7318 c4 .cfa: sp 0 + .ra: x30
STACK CFI 731c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7324 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 732c x21: .cfa -16 + ^
STACK CFI 7398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 739c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 73e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 73e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 73ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 73f8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7404 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7410 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 745c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7460 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 74a8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 74ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 74b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 74bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74c4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 752c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 75a0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 75f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7604 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7610 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 76bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 76d8 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 76dc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 76e8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 76f4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7708 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 771c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7744 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7804 x21: x21 x22: x22
STACK CFI 7808 x25: x25 x26: x26
STACK CFI 7830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7834 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 7898 x25: x25 x26: x26
STACK CFI 78a0 x21: x21 x22: x22
STACK CFI 78a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7980 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 7984 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7988 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 7990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7998 30 .cfa: sp 0 + .ra: x30
STACK CFI 799c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 79c8 24 .cfa: sp 0 + .ra: x30
STACK CFI 79cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 79dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 79e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 79f0 34 .cfa: sp 0 + .ra: x30
STACK CFI 79f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7a08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7a0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7a28 c .cfa: sp 0 + .ra: x30
