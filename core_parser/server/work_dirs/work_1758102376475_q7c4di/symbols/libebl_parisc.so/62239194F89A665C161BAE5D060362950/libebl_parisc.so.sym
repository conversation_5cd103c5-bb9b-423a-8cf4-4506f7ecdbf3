MODULE Linux arm64 62239194F89A665C161BAE5D060362950 libebl_parisc.so
INFO CODE_ID 949123629AF85C66161BAE5D06036295A06D35E8
PUBLIC c40 0 parisc_init
STACK CFI INIT a68 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a98 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad8 48 .cfa: sp 0 + .ra: x30
STACK CFI adc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae4 x19: .cfa -16 + ^
STACK CFI b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b28 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT b58 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT b80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ba0 a0 .cfa: sp 0 + .ra: x30
STACK CFI ba4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c18 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT c40 1a4 .cfa: sp 0 + .ra: x30
STACK CFI c44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c50 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d40 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT de8 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT e20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT e38 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT e90 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT ec8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT f10 2b8 .cfa: sp 0 + .ra: x30
STACK CFI f14 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI f1c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI f2c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI f48 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI f50 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 101c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1020 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 11c8 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 11cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 11d8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 11e8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 11fc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 121c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 12dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1488 8 .cfa: sp 0 + .ra: x30
