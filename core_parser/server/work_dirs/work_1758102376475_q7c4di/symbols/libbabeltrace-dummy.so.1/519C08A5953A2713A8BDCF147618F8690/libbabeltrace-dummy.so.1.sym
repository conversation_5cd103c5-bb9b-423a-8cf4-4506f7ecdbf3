MODULE Linux arm64 519C08A5953A2713A8BDCF147618F8690 libbabeltrace-dummy.so.1
INFO CODE_ID A5089C513A951327A8BDCF147618F8697042EFB6
PUBLIC 8e0 0 bt_dummy_hook
STACK CFI INIT 7c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7f8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 838 48 .cfa: sp 0 + .ra: x30
STACK CFI 83c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 844 x19: .cfa -16 + ^
STACK CFI 87c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 888 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 890 1c .cfa: sp 0 + .ra: x30
STACK CFI 894 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 8b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 760 54 .cfa: sp 0 + .ra: x30
STACK CFI 764 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 790 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 794 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 750 c .cfa: sp 0 + .ra: x30
