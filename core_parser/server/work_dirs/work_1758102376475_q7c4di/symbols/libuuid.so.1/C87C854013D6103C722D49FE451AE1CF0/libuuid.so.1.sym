MODULE Linux arm64 C87C854013D6103C722D49FE451AE1CF0 libuuid.so.1
INFO CODE_ID 40857CC8D6133C10722D49FE451AE1CF3490B633
PUBLIC 17a8 0 uuid_clear
PUBLIC 17b0 0 uuid_compare
PUBLIC 1880 0 uuid_copy
PUBLIC 1a18 0 __uuid_generate_time
PUBLIC 23b8 0 uuid_generate_time
PUBLIC 23c0 0 uuid_generate_time_safe
PUBLIC 23c8 0 __uuid_generate_random
PUBLIC 24b0 0 uuid_generate_random
PUBLIC 2508 0 uuid_generate
PUBLIC 2560 0 uuid_generate_md5
PUBLIC 2640 0 uuid_generate_sha1
PUBLIC 2728 0 uuid_is_null
PUBLIC 2798 0 uuid_parse
PUBLIC 2a70 0 uuid_unparse_upper
PUBLIC 2a80 0 uuid_unparse
PUBLIC 2a90 0 uuid_unparse_lower
PUBLIC 2a98 0 uuid_time
PUBLIC 2b58 0 uuid_type
PUBLIC 2bb0 0 uuid_variant
PUBLIC 2c20 0 uuid_get_template
STACK CFI INIT 16e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1718 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1758 48 .cfa: sp 0 + .ra: x30
STACK CFI 175c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1764 x19: .cfa -16 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b0 cc .cfa: sp 0 + .ra: x30
STACK CFI 17b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 17bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 17cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1844 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1880 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 1152 +
STACK CFI 18b4 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 18bc x23: .cfa -1104 + ^
STACK CFI 18c4 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 18f4 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 19bc x21: x21 x22: x22
STACK CFI 19e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 19ec .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x29: .cfa -1152 + ^
STACK CFI 1a08 x21: x21 x22: x22
STACK CFI 1a10 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI INIT 1a18 594 .cfa: sp 0 + .ra: x30
STACK CFI 1a1c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1a24 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1a30 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1a40 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1a54 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1a5c x27: .cfa -80 + ^
STACK CFI 1ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1cec .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1fb0 408 .cfa: sp 0 + .ra: x30
STACK CFI 1fb4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 1fbc x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 1fc8 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2010 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 20a0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 20ac x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2228 x23: x23 x24: x24
STACK CFI 2230 x25: x25 x26: x26
STACK CFI 2234 x27: x27 x28: x28
STACK CFI 22d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22dc .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 22e0 x25: x25 x26: x26
STACK CFI 22e4 x27: x27 x28: x28
STACK CFI 2314 x23: x23 x24: x24
STACK CFI 2328 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 233c x25: x25 x26: x26
STACK CFI 2340 x27: x27 x28: x28
STACK CFI 2344 x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 23a0 x25: x25 x26: x26
STACK CFI 23a4 x27: x27 x28: x28
STACK CFI 23a8 x23: x23 x24: x24
STACK CFI 23ac x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 23b0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 23b4 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 23b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 23cc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 23d4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 23dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2408 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2470 x21: x21 x22: x22
STACK CFI 2494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2498 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 24a0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 24a4 x21: x21 x22: x22
STACK CFI 24a8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 24b0 54 .cfa: sp 0 + .ra: x30
STACK CFI 24b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24c0 x19: .cfa -32 + ^
STACK CFI 24fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2500 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2508 58 .cfa: sp 0 + .ra: x30
STACK CFI 250c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2518 x19: .cfa -16 + ^
STACK CFI 2538 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 253c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 255c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2560 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2564 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 256c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 257c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2598 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 263c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2640 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2644 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 264c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 265c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2678 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2724 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI INIT 2728 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2750 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2798 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 279c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 27a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 27b0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 27d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2828 x19: x19 x20: x20
STACK CFI 2850 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2854 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 2874 x19: x19 x20: x20
STACK CFI 2878 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2888 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2930 x19: x19 x20: x20
STACK CFI 2934 x25: x25 x26: x26
STACK CFI 2944 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2948 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 2950 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29a8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 29ac .cfa: sp 144 +
STACK CFI 29b0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 29b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29c8 x21: .cfa -48 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a68 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2a70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a98 bc .cfa: sp 0 + .ra: x30
STACK CFI 2a9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2aa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2b58 54 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b64 x19: .cfa -48 + ^
STACK CFI 2ba4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ba8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2bb0 6c .cfa: sp 0 + .ra: x30
STACK CFI 2bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bbc x19: .cfa -48 + ^
STACK CFI 2c14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c20 104 .cfa: sp 0 + .ra: x30
STACK CFI 2c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c30 x19: .cfa -16 + ^
STACK CFI 2cb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2ce4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d28 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2d2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d38 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d50 x21: .cfa -48 + ^
STACK CFI 2e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2e20 3c .cfa: sp 0 + .ra: x30
STACK CFI 2e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e60 90 .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e78 x19: .cfa -16 + ^
STACK CFI 2e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2eec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ef0 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 2ef4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2efc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f08 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2f20 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2f34 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2f40 x27: .cfa -48 + ^
STACK CFI 3068 x25: x25 x26: x26
STACK CFI 306c x27: x27
STACK CFI 30ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30b0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 3124 x25: x25 x26: x26 x27: x27
STACK CFI 3174 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 318c x25: x25 x26: x26 x27: x27
STACK CFI 31a0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 31a4 x27: .cfa -48 + ^
STACK CFI INIT 31a8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 31f0 9ec .cfa: sp 0 + .ra: x30
STACK CFI 31f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3220 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 322c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 32b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 3be0 11c .cfa: sp 0 + .ra: x30
STACK CFI 3be4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3bec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3bf4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3c00 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3cdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3cf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d24 x21: .cfa -16 + ^
STACK CFI 3da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3dac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3dc0 fec .cfa: sp 0 + .ra: x30
STACK CFI 3dc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3ddc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3de4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3e0c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 4db0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4de8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4dec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4df4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4e00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4e18 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4e20 x25: .cfa -16 + ^
STACK CFI 4e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4e68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4ee0 148 .cfa: sp 0 + .ra: x30
STACK CFI 4ee4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4eec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4efc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4f1c x23: .cfa -64 + ^
STACK CFI 5020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5024 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5028 ac .cfa: sp 0 + .ra: x30
STACK CFI 502c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5034 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5044 x23: .cfa -128 + ^
STACK CFI 504c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 50cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 50d0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x29: .cfa -176 + ^
