MODULE Linux arm64 0B2A2E4E42082449B62549F8635543880 libkrb5support.so.0
INFO CODE_ID 4E2E2A0B08424924B62549F863554388F8109017
PUBLIC 2f40 0 krb5int_pthread_loaded
PUBLIC 30f8 0 k5_os_mutex_init
PUBLIC 3130 0 krb5int_mutex_alloc
PUBLIC 31a0 0 k5_os_mutex_destroy
PUBLIC 31d8 0 krb5int_mutex_free
PUBLIC 3200 0 k5_os_mutex_lock
PUBLIC 3238 0 krb5int_mutex_lock
PUBLIC 3288 0 k5_os_mutex_unlock
PUBLIC 32c0 0 krb5int_mutex_unlock
PUBLIC 3310 0 krb5int_key_delete
PUBLIC 3518 0 k5_once
PUBLIC 35c0 0 krb5int_getspecific
PUBLIC 3698 0 krb5int_setspecific
PUBLIC 37d8 0 krb5int_key_register
PUBLIC 3ad8 0 krb5int_open_plugin
PUBLIC 3cb0 0 krb5int_get_plugin_data
PUBLIC 3cb8 0 krb5int_get_plugin_func
PUBLIC 3d20 0 krb5int_close_plugin
PUBLIC 3d50 0 krb5int_open_plugin_dirs
PUBLIC 4178 0 krb5int_close_plugin_dirs
PUBLIC 41d0 0 krb5int_free_plugin_dir_data
PUBLIC 41d8 0 krb5int_get_plugin_dir_data
PUBLIC 4338 0 krb5int_free_plugin_dir_func
PUBLIC 4340 0 krb5int_get_plugin_dir_func
PUBLIC 44e8 0 k5_get_error
PUBLIC 4728 0 k5_free_error
PUBLIC 4748 0 k5_clear_error
PUBLIC 4770 0 k5_vset_error
PUBLIC 4810 0 k5_set_error
PUBLIC 48b8 0 k5_set_error_info_callout_fn
PUBLIC 4b40 0 k5_buf_init_fixed
PUBLIC 4b88 0 k5_buf_init_dynamic
PUBLIC 4bd8 0 k5_buf_init_dynamic_zap
PUBLIC 4c10 0 k5_buf_add_len
PUBLIC 4c80 0 k5_buf_add
PUBLIC 4cb8 0 k5_buf_get_space
PUBLIC 4d18 0 k5_buf_truncate
PUBLIC 4d68 0 k5_buf_status
PUBLIC 4d80 0 k5_buf_free
PUBLIC 4e20 0 k5_buf_add_vfmt
PUBLIC 50d8 0 k5_buf_add_fmt
PUBLIC 5180 0 krb5int_gmt_mktime
PUBLIC 5188 0 krb5int_getaddrinfo
PUBLIC 5190 0 krb5int_freeaddrinfo
PUBLIC 5198 0 krb5int_gai_strerror
PUBLIC 51a0 0 krb5int_getnameinfo
PUBLIC 5248 0 krb5int_utf8_to_ucs4
PUBLIC 5390 0 krb5int_ucs4_to_utf8
PUBLIC 5478 0 krb5int_utf8_next
PUBLIC 5bd0 0 k5_utf8_to_utf16le
PUBLIC 5da0 0 k5_utf16le_to_utf8
PUBLIC 5fb0 0 krb5int_zap
PUBLIC 5fd0 0 k5_path_split
PUBLIC 6108 0 k5_path_isabs
PUBLIC 6118 0 k5_path_join
PUBLIC 6220 0 k5_base64_encode
PUBLIC 6338 0 k5_base64_decode
PUBLIC 6868 0 k5_json_retain
PUBLIC 68b0 0 k5_json_release
PUBLIC 69e8 0 k5_json_get_tid
PUBLIC 69f8 0 k5_json_null_create
PUBLIC 6a58 0 k5_json_null_create_val
PUBLIC 6ab8 0 k5_json_bool_create
PUBLIC 6b28 0 k5_json_bool_value
PUBLIC 6b30 0 k5_json_array_create
PUBLIC 6b90 0 k5_json_array_add
PUBLIC 6c30 0 k5_json_array_length
PUBLIC 6c38 0 k5_json_array_get
PUBLIC 6c60 0 k5_json_array_set
PUBLIC 6cb8 0 k5_json_object_create
PUBLIC 6d18 0 k5_json_object_count
PUBLIC 6d20 0 k5_json_object_get
PUBLIC 6d90 0 k5_json_object_set
PUBLIC 6f38 0 k5_json_object_iterate
PUBLIC 6fa0 0 k5_json_string_create_len
PUBLIC 7030 0 k5_json_string_create
PUBLIC 7060 0 k5_json_string_create_base64
PUBLIC 70c0 0 k5_json_string_utf8
PUBLIC 70c8 0 k5_json_string_unbase64
PUBLIC 7160 0 k5_json_number_create
PUBLIC 71c8 0 k5_json_array_fmt
PUBLIC 7be0 0 k5_json_number_value
PUBLIC 7e78 0 k5_json_encode
PUBLIC 7f28 0 k5_json_decode
PUBLIC 7fe8 0 k5_hex_encode
PUBLIC 80d8 0 k5_hex_decode
PUBLIC 84b0 0 k5_hashtab_create
PUBLIC 8550 0 k5_hashtab_free
PUBLIC 85d0 0 k5_hashtab_add
PUBLIC 8720 0 k5_hashtab_remove
PUBLIC 8818 0 k5_hashtab_get
PUBLIC 88a8 0 k5_bcmp
PUBLIC 88e8 0 k5_strerror_r
PUBLIC 8958 0 k5_free_filenames
PUBLIC 8998 0 k5_dir_filenames
PUBLIC 8aa0 0 krb5int_strlcpy
PUBLIC 8b10 0 krb5int_strlcat
STACK CFI INIT 2e00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e30 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e70 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e7c x19: .cfa -16 + ^
STACK CFI 2eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2eb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ec0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ee8 28 .cfa: sp 0 + .ra: x30
STACK CFI 2eec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2f10 2c .cfa: sp 0 + .ra: x30
STACK CFI 2f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2f40 13c .cfa: sp 0 + .ra: x30
STACK CFI 2f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 302c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3038 x21: .cfa -16 + ^
STACK CFI 3070 x21: x21
STACK CFI 3074 x21: .cfa -16 + ^
STACK CFI 3078 x21: x21
STACK CFI INIT 3080 74 .cfa: sp 0 + .ra: x30
STACK CFI 3084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3090 x19: .cfa -16 + ^
STACK CFI 30d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30f8 38 .cfa: sp 0 + .ra: x30
STACK CFI 30fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3104 x19: .cfa -16 + ^
STACK CFI 3118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 311c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 312c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3130 6c .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3140 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3178 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31a0 34 .cfa: sp 0 + .ra: x30
STACK CFI 31a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31ac x19: .cfa -16 + ^
STACK CFI 31c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 31c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 31d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d90 5c .cfa: sp 0 + .ra: x30
STACK CFI 2db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2db8 x19: .cfa -16 + ^
STACK CFI 2dd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ddc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 31d8 24 .cfa: sp 0 + .ra: x30
STACK CFI 31dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31e4 x19: .cfa -16 + ^
STACK CFI 31f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3200 34 .cfa: sp 0 + .ra: x30
STACK CFI 3204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 320c x19: .cfa -16 + ^
STACK CFI 3220 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3224 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3230 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3238 50 .cfa: sp 0 + .ra: x30
STACK CFI 323c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 324c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3250 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3258 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3288 34 .cfa: sp 0 + .ra: x30
STACK CFI 328c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3294 x19: .cfa -16 + ^
STACK CFI 32a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 32ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 32b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 32c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 32d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3310 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 331c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3328 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 337c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3408 110 .cfa: sp 0 + .ra: x30
STACK CFI 340c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3418 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3430 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 34b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 34b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3518 a8 .cfa: sp 0 + .ra: x30
STACK CFI 351c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3524 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 35c0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 35c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3630 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 364c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3650 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3698 140 .cfa: sp 0 + .ra: x30
STACK CFI 369c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 36a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36d4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 370c x23: x23 x24: x24
STACK CFI 3720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3778 x23: x23 x24: x24
STACK CFI 377c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 37c8 x23: x23 x24: x24
STACK CFI 37cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 37d4 x23: x23 x24: x24
STACK CFI INIT 37d8 158 .cfa: sp 0 + .ra: x30
STACK CFI 37dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 37e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 37f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3828 x23: .cfa -16 + ^
STACK CFI 3870 x23: x23
STACK CFI 3880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3884 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 38e4 x23: x23
STACK CFI 3908 x23: .cfa -16 + ^
STACK CFI INIT 3930 60 .cfa: sp 0 + .ra: x30
STACK CFI 3934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 393c x19: .cfa -16 + ^
STACK CFI 3968 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 396c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3998 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 39a8 74 .cfa: sp 0 + .ra: x30
STACK CFI 39ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 39c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39d4 x23: .cfa -16 + ^
STACK CFI 3a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3a20 3c .cfa: sp 0 + .ra: x30
STACK CFI 3a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a60 74 .cfa: sp 0 + .ra: x30
STACK CFI 3a68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3aa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ad8 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 3adc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3ae4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3af0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3b00 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3b18 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3be0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3cb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cb8 68 .cfa: sp 0 + .ra: x30
STACK CFI 3cbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3cc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3d20 2c .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d2c x19: .cfa -16 + ^
STACK CFI 3d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d50 424 .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3d64 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3d7c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 3d88 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 3da4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3dac x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 3e4c x25: x25 x26: x26
STACK CFI 3e70 x19: x19 x20: x20
STACK CFI 3ea0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3ea4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 3f00 x19: x19 x20: x20
STACK CFI 3f04 x25: x25 x26: x26
STACK CFI 3f08 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 3fe0 x19: x19 x20: x20
STACK CFI 3fe4 x25: x25 x26: x26
STACK CFI 3fec x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4068 x25: x25 x26: x26
STACK CFI 40a8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4138 x25: x25 x26: x26
STACK CFI 4144 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4154 x25: x25 x26: x26
STACK CFI 4160 x19: x19 x20: x20
STACK CFI 4164 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4168 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 416c x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI INIT 4178 58 .cfa: sp 0 + .ra: x30
STACK CFI 4184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 418c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 41d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d8 15c .cfa: sp 0 + .ra: x30
STACK CFI 41dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 41e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 41f0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4208 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4218 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4274 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 4290 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4314 x27: x27 x28: x28
STACK CFI 4318 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4320 x27: x27 x28: x28
STACK CFI 4330 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4338 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4340 148 .cfa: sp 0 + .ra: x30
STACK CFI 4344 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 434c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 435c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4378 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 43d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 43dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 43f4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4468 x27: x27 x28: x28
STACK CFI 446c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4474 x27: x27 x28: x28
STACK CFI 4484 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4488 28 .cfa: sp 0 + .ra: x30
STACK CFI 448c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 44b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 44e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44e8 23c .cfa: sp 0 + .ra: x30
STACK CFI 44ec .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 44f4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 452c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4590 x21: x21 x22: x22
STACK CFI 45b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45b8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI 45f8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4684 x21: x21 x22: x22
STACK CFI 4690 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 46cc x21: x21 x22: x22
STACK CFI 46d0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4718 x21: x21 x22: x22
STACK CFI 4720 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI INIT 4728 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4748 28 .cfa: sp 0 + .ra: x30
STACK CFI 474c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4758 x19: .cfa -16 + ^
STACK CFI 476c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4770 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4774 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 477c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 478c x23: .cfa -96 + ^
STACK CFI 4794 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 480c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4810 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4814 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 4824 x19: .cfa -272 + ^
STACK CFI 48ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48b0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 48b8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 48bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48d0 x21: .cfa -16 + ^
STACK CFI 4904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4970 1cc .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 497c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 498c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 49e4 x23: .cfa -16 + ^
STACK CFI 4a08 x23: x23
STACK CFI 4a18 x21: x21 x22: x22
STACK CFI 4a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4a2c x21: x21 x22: x22
STACK CFI 4a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4a68 x21: x21 x22: x22
STACK CFI 4a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4a90 x23: .cfa -16 + ^
STACK CFI 4ad4 x23: x23
STACK CFI 4aec x23: .cfa -16 + ^
STACK CFI 4af0 x23: x23
STACK CFI 4af4 x23: .cfa -16 + ^
STACK CFI 4b08 x23: x23
STACK CFI 4b2c x23: .cfa -16 + ^
STACK CFI 4b38 x23: x23
STACK CFI INIT 4b40 48 .cfa: sp 0 + .ra: x30
STACK CFI 4b60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b88 4c .cfa: sp 0 + .ra: x30
STACK CFI 4b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b98 x19: .cfa -16 + ^
STACK CFI 4bc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4bd8 34 .cfa: sp 0 + .ra: x30
STACK CFI 4bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4be4 x19: .cfa -16 + ^
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c10 6c .cfa: sp 0 + .ra: x30
STACK CFI 4c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c28 x21: .cfa -16 + ^
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4c60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c80 34 .cfa: sp 0 + .ra: x30
STACK CFI 4c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4cb8 5c .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cc4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4d18 50 .cfa: sp 0 + .ra: x30
STACK CFI 4d40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4d68 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d80 9c .cfa: sp 0 + .ra: x30
STACK CFI 4d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d8c x19: .cfa -16 + ^
STACK CFI 4dcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4dd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e20 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 4e24 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4e2c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4e40 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4e58 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4e70 x25: .cfa -128 + ^
STACK CFI 4ee0 x21: x21 x22: x22
STACK CFI 4ee4 x25: x25
STACK CFI 4f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4f10 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI 4f5c x21: x21 x22: x22
STACK CFI 4f64 x25: x25
STACK CFI 4f6c x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^
STACK CFI 4f78 x21: x21 x22: x22
STACK CFI 4f7c x25: x25
STACK CFI 4f80 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^
STACK CFI 4fe4 x21: x21 x22: x22
STACK CFI 4fe8 x25: x25
STACK CFI 4fec x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 502c x21: x21 x22: x22
STACK CFI 503c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5044 x21: x21 x22: x22
STACK CFI 5050 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^
STACK CFI 50a8 x21: x21 x22: x22 x25: x25
STACK CFI 50ac x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 50b0 x25: .cfa -128 + ^
STACK CFI INIT 50d8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 50dc .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 50ec x19: .cfa -272 + ^
STACK CFI 5174 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5178 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 5180 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5188 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5190 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5198 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51a8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51d0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51f8 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5248 c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5310 80 .cfa: sp 0 + .ra: x30
STACK CFI 5314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 531c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5384 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5390 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT 5470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5478 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 54b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54bc x19: .cfa -16 + ^
STACK CFI 54f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5500 54 .cfa: sp 0 + .ra: x30
STACK CFI 5504 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 550c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5558 34 .cfa: sp 0 + .ra: x30
STACK CFI 556c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5574 x19: .cfa -16 + ^
STACK CFI 5588 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5590 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55c0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5608 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5618 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5630 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5668 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5698 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56c0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56f8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5704 x21: .cfa -32 + ^
STACK CFI 570c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 57b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 57b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 57c0 11c .cfa: sp 0 + .ra: x30
STACK CFI 57c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 57cc x25: .cfa -32 + ^
STACK CFI 57d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 57f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5800 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5860 x19: x19 x20: x20
STACK CFI 5864 x21: x21 x22: x22
STACK CFI 5888 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 588c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 58c8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 58d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 58d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 58e0 118 .cfa: sp 0 + .ra: x30
STACK CFI 58e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 58ec x25: .cfa -32 + ^
STACK CFI 58f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5918 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5920 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5998 x19: x19 x20: x20
STACK CFI 599c x21: x21 x22: x22
STACK CFI 59c0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 59c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 59d4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 59dc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 59ec x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 59f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 59f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 59f8 10c .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5a04 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5a14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5a30 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5a8c x21: x21 x22: x22
STACK CFI 5ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5ab8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5ae4 x21: x21 x22: x22
STACK CFI 5aec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5afc x21: x21 x22: x22
STACK CFI 5b00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5b08 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b74 x19: x19 x20: x20
STACK CFI 5b80 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bb0 x19: x19 x20: x20
STACK CFI 5bb8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5bbc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5bd0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 5bd4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5bdc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5be4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5bf4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5c0c x27: .cfa -64 + ^
STACK CFI 5c28 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5c64 x23: x23 x24: x24
STACK CFI 5c7c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5cd4 x23: x23 x24: x24
STACK CFI 5d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5d08 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI 5d3c x23: x23 x24: x24
STACK CFI 5d40 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5d98 x23: x23 x24: x24
STACK CFI 5d9c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 5da0 20c .cfa: sp 0 + .ra: x30
STACK CFI 5da4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5dac x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5dbc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5ddc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5dec x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5df8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5e28 x21: x21 x22: x22
STACK CFI 5e30 x25: x25 x26: x26
STACK CFI 5e34 x27: x27 x28: x28
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5e60 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 5efc x21: x21 x22: x22
STACK CFI 5f00 x25: x25 x26: x26
STACK CFI 5f04 x27: x27 x28: x28
STACK CFI 5f0c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5f28 x21: x21 x22: x22
STACK CFI 5f2c x25: x25 x26: x26
STACK CFI 5f30 x27: x27 x28: x28
STACK CFI 5f38 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5f3c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5f40 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 5fb0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5fd0 134 .cfa: sp 0 + .ra: x30
STACK CFI 5fd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5fdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5fe4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ff0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6088 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 60dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 60e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6108 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6118 104 .cfa: sp 0 + .ra: x30
STACK CFI 611c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6124 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6130 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 619c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6220 114 .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 622c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 631c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 632c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6338 168 .cfa: sp 0 + .ra: x30
STACK CFI 633c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 634c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6354 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 635c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 644c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6450 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6490 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 64a0 104 .cfa: sp 0 + .ra: x30
STACK CFI 64a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 64ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 64b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 64bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 64e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 64f0 x27: .cfa -16 + ^
STACK CFI 6584 x23: x23 x24: x24
STACK CFI 6588 x27: x27
STACK CFI 65a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI INIT 65a8 2bc .cfa: sp 0 + .ra: x30
STACK CFI 65ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 65b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 65d0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6620 x19: x19 x20: x20
STACK CFI 662c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6630 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6638 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6660 x25: .cfa -16 + ^
STACK CFI 66d4 x25: x25
STACK CFI 66e0 x19: x19 x20: x20
STACK CFI 66ec x23: x23 x24: x24
STACK CFI 66f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 66f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6728 x19: x19 x20: x20
STACK CFI 6730 x23: x23 x24: x24
STACK CFI 6734 x25: x25
STACK CFI 6738 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 673c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6848 x25: x25
STACK CFI 6858 x19: x19 x20: x20
STACK CFI 685c x23: x23 x24: x24
STACK CFI 6860 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 6868 44 .cfa: sp 0 + .ra: x30
STACK CFI 6884 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 68b0 7c .cfa: sp 0 + .ra: x30
STACK CFI 68b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68c4 x19: .cfa -16 + ^
STACK CFI 68f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6908 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6930 4c .cfa: sp 0 + .ra: x30
STACK CFI 6934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 693c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6980 64 .cfa: sp 0 + .ra: x30
STACK CFI 6984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 698c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69a0 x21: .cfa -16 + ^
STACK CFI 69d4 x21: x21
STACK CFI 69e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 69f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 69fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a08 x19: .cfa -16 + ^
STACK CFI 6a44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6a58 5c .cfa: sp 0 + .ra: x30
STACK CFI 6a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a68 x19: .cfa -16 + ^
STACK CFI 6aa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6ab8 6c .cfa: sp 0 + .ra: x30
STACK CFI 6abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ac4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b30 60 .cfa: sp 0 + .ra: x30
STACK CFI 6b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b40 x19: .cfa -16 + ^
STACK CFI 6b80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b90 9c .cfa: sp 0 + .ra: x30
STACK CFI 6b94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ba4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6bc4 x23: .cfa -16 + ^
STACK CFI 6bf0 x23: x23
STACK CFI 6c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6c28 x23: x23
STACK CFI INIT 6c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c38 24 .cfa: sp 0 + .ra: x30
STACK CFI 6c54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6c60 58 .cfa: sp 0 + .ra: x30
STACK CFI 6c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c78 x21: .cfa -16 + ^
STACK CFI 6cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6cb8 60 .cfa: sp 0 + .ra: x30
STACK CFI 6cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cc8 x19: .cfa -16 + ^
STACK CFI 6d08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d20 6c .cfa: sp 0 + .ra: x30
STACK CFI 6d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d30 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6d90 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 6d94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6da0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6db0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6e20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6e80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6ed8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6f30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6f38 64 .cfa: sp 0 + .ra: x30
STACK CFI 6f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6f44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6f54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6f90 x21: x21 x22: x22
STACK CFI 6f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6fa0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6fa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6fac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6fb8 x21: .cfa -16 + ^
STACK CFI 7008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 700c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7030 30 .cfa: sp 0 + .ra: x30
STACK CFI 7034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 703c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 705c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7060 5c .cfa: sp 0 + .ra: x30
STACK CFI 7064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7070 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 70b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70c8 94 .cfa: sp 0 + .ra: x30
STACK CFI 70cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 70d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 70f0 x21: .cfa -32 + ^
STACK CFI 713c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7140 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7160 64 .cfa: sp 0 + .ra: x30
STACK CFI 7164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 716c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71c8 368 .cfa: sp 0 + .ra: x30
STACK CFI 71cc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 71d4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 71e4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 71f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7248 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7254 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7360 x25: x25 x26: x26
STACK CFI 7364 x27: x27 x28: x28
STACK CFI 739c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 73a0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 73e0 x25: x25 x26: x26
STACK CFI 73e4 x27: x27 x28: x28
STACK CFI 73e8 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 751c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7520 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7524 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 7530 6b0 .cfa: sp 0 + .ra: x30
STACK CFI 7534 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 753c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 754c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 756c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 75cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 75d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 7848 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 78bc x23: x23 x24: x24
STACK CFI 78d8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7928 x23: x23 x24: x24
STACK CFI 7948 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 79cc x23: x23 x24: x24
STACK CFI 79d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7a30 x23: x23 x24: x24
STACK CFI 7a58 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7ad8 x23: x23 x24: x24
STACK CFI 7aec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7b4c x23: x23 x24: x24
STACK CFI 7b90 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7ba0 x23: x23 x24: x24
STACK CFI 7bb0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7bb4 x23: x23 x24: x24
STACK CFI 7bd0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7bd8 x23: x23 x24: x24
STACK CFI 7bdc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 7be0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7be8 20c .cfa: sp 0 + .ra: x30
STACK CFI 7bec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7c90 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7d40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7db4 x23: x23 x24: x24
STACK CFI 7dd0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7de8 x23: x23 x24: x24
STACK CFI 7df0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 7df8 80 .cfa: sp 0 + .ra: x30
STACK CFI 7dfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e18 x21: .cfa -16 + ^
STACK CFI 7e54 x21: x21
STACK CFI 7e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e78 ac .cfa: sp 0 + .ra: x30
STACK CFI 7e7c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7e90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f08 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7f28 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7f2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7fd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7fe8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 7fec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ff8 x23: .cfa -16 + ^
STACK CFI 8000 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8010 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8098 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 80d8 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 80dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 80e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 80f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8108 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 820c x23: x23 x24: x24
STACK CFI 8210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8214 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8228 x23: x23 x24: x24
STACK CFI 8238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 823c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 825c x23: x23 x24: x24
STACK CFI 8260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8264 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8274 x23: x23 x24: x24
STACK CFI INIT 8278 234 .cfa: sp 0 + .ra: x30
STACK CFI 827c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8284 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8290 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 82a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 84a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 84b0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 84b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 84c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 852c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8530 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8550 80 .cfa: sp 0 + .ra: x30
STACK CFI 8554 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8560 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8570 x21: .cfa -16 + ^
STACK CFI 85b8 x21: x21
STACK CFI 85cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 85d0 14c .cfa: sp 0 + .ra: x30
STACK CFI 85d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 85dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 85e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 85f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 866c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 8670 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8680 x27: .cfa -16 + ^
STACK CFI 8704 x25: x25 x26: x26
STACK CFI 8708 x27: x27
STACK CFI 870c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8710 x25: x25 x26: x26
STACK CFI 8714 x27: x27
STACK CFI INIT 8720 f4 .cfa: sp 0 + .ra: x30
STACK CFI 8724 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 872c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 873c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8750 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 87c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 87cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 8818 8c .cfa: sp 0 + .ra: x30
STACK CFI 881c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8834 x21: .cfa -16 + ^
STACK CFI 88a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 88a8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 88e8 60 .cfa: sp 0 + .ra: x30
STACK CFI 88ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 892c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8930 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8948 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8958 3c .cfa: sp 0 + .ra: x30
STACK CFI 895c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8964 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8998 108 .cfa: sp 0 + .ra: x30
STACK CFI 899c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 89a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 89bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 89c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 89d0 x25: .cfa -16 + ^
STACK CFI 8a34 x19: x19 x20: x20
STACK CFI 8a38 x21: x21 x22: x22
STACK CFI 8a40 x25: x25
STACK CFI 8a44 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 8a48 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8a6c x19: x19 x20: x20
STACK CFI 8a74 x21: x21 x22: x22
STACK CFI 8a78 x25: x25
STACK CFI 8a84 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 8a88 .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8a94 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 8a98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8aa0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b10 ac .cfa: sp 0 + .ra: x30
STACK CFI 8b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b1c x19: .cfa -16 + ^
STACK CFI 8b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8bb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
