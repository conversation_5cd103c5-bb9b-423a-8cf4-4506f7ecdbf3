MODULE Linux arm64 20E3570A252B11F9A81D0961F844D7DD0 libnvstream_core_error.so
INFO CODE_ID 0A57E3202B25F911A81D0961F844D7DD
PUBLIC 1310 0 _init
PUBLIC 14a0 0 __static_initialization_and_destruction_0(int, int) [clone .constprop.0]
PUBLIC 1c90 0 _GLOBAL__sub_I_error_nvsci_error.cpp
PUBLIC 1ca0 0 _GLOBAL__sub_I_error_nvsci_event.cpp
PUBLIC 1e30 0 call_weak_fn
PUBLIC 1e44 0 deregister_tm_clones
PUBLIC 1e74 0 register_tm_clones
PUBLIC 1eb0 0 __do_global_dtors_aux
PUBLIC 1f00 0 frame_dummy
PUBLIC 1f10 0 linvs::error::NvSciErrorStr(int)
PUBLIC 1fd0 0 std::unordered_map<int, char const*, std::hash<int>, std::equal_to<int>, std::allocator<std::pair<int const, char const*> > >::~unordered_map()
PUBLIC 2040 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 2170 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_Hashtable<std::pair<int const, char const*> const*>(std::pair<int const, char const*> const*, std::pair<int const, char const*> const*, unsigned long, std::hash<int> const&, std::__detail::_Mod_range_hashing const&, std::__detail::_Default_ranged_hash const&, std::equal_to<int> const&, std::__detail::_Select1st const&, std::allocator<std::pair<int const, char const*> > const&)
PUBLIC 2420 0 linvs::error::NvSciEventStr(int)
PUBLIC 24d4 0 _fini
STACK CFI INIT 1e44 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e74 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1eb0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1ec0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ec8 x19: .cfa -16 + ^
STACK CFI 1ef8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fd0 64 .cfa: sp 0 + .ra: x30
STACK CFI 1fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1f10 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f94 x19: .cfa -16 + ^
STACK CFI 1fc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2040 124 .cfa: sp 0 + .ra: x30
STACK CFI 2044 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2050 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 205c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 20f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2170 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 2174 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2180 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 218c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 21a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 21b0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 236c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 14a0 7e4 .cfa: sp 0 + .ra: x30
STACK CFI 14a4 .cfa: sp 1568 +
STACK CFI 14b8 .ra: .cfa -1544 + ^ x29: .cfa -1552 + ^
STACK CFI 14c0 x19: .cfa -1536 + ^ x20: .cfa -1528 + ^
STACK CFI 14cc x21: .cfa -1520 + ^
STACK CFI 1c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1c90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2420 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24a4 x19: .cfa -16 + ^
STACK CFI 24d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ca0 190 .cfa: sp 0 + .ra: x30
STACK CFI 1ca4 .cfa: sp 288 +
STACK CFI 1cbc .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1d3c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 1e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
