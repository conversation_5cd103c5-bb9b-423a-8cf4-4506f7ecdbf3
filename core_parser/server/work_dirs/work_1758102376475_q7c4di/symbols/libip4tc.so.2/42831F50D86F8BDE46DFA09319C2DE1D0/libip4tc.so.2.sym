MODULE Linux arm64 42831F50D86F8BDE46DFA09319C2DE1D0 libip4tc.so.2
INFO CODE_ID 501F83426FD8DE8B46DFA09319C2DE1D32883808
PUBLIC 1a70 0 iptc_first_chain
PUBLIC 1ab0 0 iptc_next_chain
PUBLIC 1ae8 0 iptc_next_rule
PUBLIC 1b30 0 iptc_strerror
PUBLIC 1c60 0 iptc_free
PUBLIC 22e0 0 iptc_is_chain
PUBLIC 2310 0 iptc_delete_num_entry
PUBLIC 24b0 0 iptc_set_policy
PUBLIC 2610 0 dump_entries
PUBLIC 2ac8 0 iptc_get_target
PUBLIC 2c58 0 iptc_init
PUBLIC 31e0 0 iptc_first_rule
PUBLIC 3248 0 iptc_set_counter
PUBLIC 3330 0 iptc_get_policy
PUBLIC 3398 0 iptc_read_counter
PUBLIC 3438 0 iptc_zero_counter
PUBLIC 3510 0 iptc_create_chain
PUBLIC 3650 0 iptc_commit
PUBLIC 3c20 0 iptc_builtin
PUBLIC 3e50 0 iptc_insert_entry
PUBLIC 4050 0 iptc_replace_entry
PUBLIC 4218 0 iptc_append_entry
PUBLIC 47b8 0 iptc_delete_entry
PUBLIC 47c0 0 iptc_rename_chain
PUBLIC 4908 0 iptc_get_references
PUBLIC 4970 0 iptc_delete_chain
PUBLIC 4ac8 0 iptc_flush_entries
PUBLIC 4b68 0 iptc_zero_entries
PUBLIC 4c10 0 iptc_check_entry
STACK CFI INIT 1928 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1958 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1998 48 .cfa: sp 0 + .ra: x30
STACK CFI 199c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19a4 x19: .cfa -16 + ^
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 19e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19e8 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a70 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ae8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b30 ec .cfa: sp 0 + .ra: x30
STACK CFI 1b34 .cfa: sp 576 +
STACK CFI 1b40 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 1b48 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bec .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x29: .cfa -576 + ^
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c18 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x29: .cfa -576 + ^
STACK CFI INIT 1c20 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c60 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1c64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c70 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ca4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1cf0 x19: x19 x20: x20
STACK CFI 1d10 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1d18 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d60 70 .cfa: sp 0 + .ra: x30
STACK CFI 1d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1dd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ddc x19: .cfa -16 + ^
STACK CFI 1e0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e18 150 .cfa: sp 0 + .ra: x30
STACK CFI 1e1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1e24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1e30 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1e50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1efc x23: x23 x24: x24
STACK CFI 1f00 x27: x27 x28: x28
STACK CFI 1f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1f18 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1f38 x23: x23 x24: x24
STACK CFI 1f40 x27: x27 x28: x28
STACK CFI 1f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1f48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1f68 100 .cfa: sp 0 + .ra: x30
STACK CFI 1f6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1f90 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1fa8 x23: .cfa -32 + ^
STACK CFI 2004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2008 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2068 154 .cfa: sp 0 + .ra: x30
STACK CFI 206c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 207c x25: .cfa -32 + ^
STACK CFI 2084 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2090 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 209c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 214c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2150 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 21c0 11c .cfa: sp 0 + .ra: x30
STACK CFI 21c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 21cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 21d4 x23: .cfa -32 + ^
STACK CFI 21dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 22cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 22e0 2c .cfa: sp 0 + .ra: x30
STACK CFI 22e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2310 140 .cfa: sp 0 + .ra: x30
STACK CFI 2314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 232c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2424 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2450 5c .cfa: sp 0 + .ra: x30
STACK CFI 2454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 245c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2468 x21: .cfa -16 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 24b0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 24b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2540 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 258c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 25a8 68 .cfa: sp 0 + .ra: x30
STACK CFI 25c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2610 438 .cfa: sp 0 + .ra: x30
STACK CFI 2614 .cfa: sp 112 +
STACK CFI 2624 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2630 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 26bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 26d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 26dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 29ec x19: x19 x20: x20
STACK CFI 29f0 x23: x23 x24: x24
STACK CFI 29f4 x25: x25 x26: x26
STACK CFI 29f8 x27: x27 x28: x28
STACK CFI 2a04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2a08 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2a48 80 .cfa: sp 0 + .ra: x30
STACK CFI 2aa0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2ac8 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b38 78 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b5c x19: .cfa -16 + ^
STACK CFI 2bac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bb0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2bcc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2bdc x23: .cfa -16 + ^
STACK CFI 2c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c58 584 .cfa: sp 0 + .ra: x30
STACK CFI 2c5c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2c64 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2c70 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2c8c x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2df4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2f18 x25: x25 x26: x26
STACK CFI 300c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3010 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 3050 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 30ac x25: x25 x26: x26
STACK CFI 30b4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3128 x25: x25 x26: x26
STACK CFI 3148 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3154 x25: x25 x26: x26
STACK CFI 3170 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 318c x25: x25 x26: x26
STACK CFI 3198 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 31c8 x25: x25 x26: x26
STACK CFI 31d8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 31e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 31e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 322c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3230 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3248 e4 .cfa: sp 0 + .ra: x30
STACK CFI 324c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3264 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3274 x21: .cfa -16 + ^
STACK CFI 32d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 32d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3308 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3330 68 .cfa: sp 0 + .ra: x30
STACK CFI 3334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 334c x19: .cfa -16 + ^
STACK CFI 3378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 337c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3394 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3398 a0 .cfa: sp 0 + .ra: x30
STACK CFI 339c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3428 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3438 d8 .cfa: sp 0 + .ra: x30
STACK CFI 343c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3454 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 34e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 350c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3510 13c .cfa: sp 0 + .ra: x30
STACK CFI 3514 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 352c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 355c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3650 5cc .cfa: sp 0 + .ra: x30
STACK CFI 3654 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3660 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3684 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3688 .cfa: sp 128 + .ra: .cfa -120 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 368c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3694 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 369c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 36a0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3a48 x19: x19 x20: x20
STACK CFI 3a4c x21: x21 x22: x22
STACK CFI 3a50 x25: x25 x26: x26
STACK CFI 3a54 x27: x27 x28: x28
STACK CFI 3a58 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3ad0 x19: x19 x20: x20
STACK CFI 3ad4 x21: x21 x22: x22
STACK CFI 3adc x25: x25 x26: x26
STACK CFI 3ae0 x27: x27 x28: x28
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3ae8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3afc x19: x19 x20: x20
STACK CFI 3b00 x21: x21 x22: x22
STACK CFI 3b04 x25: x25 x26: x26
STACK CFI 3b08 x27: x27 x28: x28
STACK CFI 3b14 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3b18 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c20 50 .cfa: sp 0 + .ra: x30
STACK CFI 3c24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c70 1dc .cfa: sp 0 + .ra: x30
STACK CFI 3c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c80 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3c8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e50 1fc .cfa: sp 0 + .ra: x30
STACK CFI 3e54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e7c x23: .cfa -16 + ^
STACK CFI 3f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3f6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3f94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3ffc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4024 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4050 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 4054 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 406c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4074 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4154 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 41b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 41d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 41f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4218 10c .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4234 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4250 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42bc x19: x19 x20: x20
STACK CFI 42cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 42d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42e0 x19: x19 x20: x20
STACK CFI 42f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 42f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4310 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4314 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4320 x19: x19 x20: x20
STACK CFI INIT 4328 48c .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4344 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 434c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4358 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4388 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 43b4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4584 x25: x25 x26: x26
STACK CFI 4588 x27: x27 x28: x28
STACK CFI 45b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45b8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 45dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45e0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4720 x25: x25 x26: x26
STACK CFI 4724 x27: x27 x28: x28
STACK CFI 4728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 472c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4744 x27: x27 x28: x28
STACK CFI 4750 x25: x25 x26: x26
STACK CFI 4754 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4770 x25: x25 x26: x26
STACK CFI 4774 x27: x27 x28: x28
STACK CFI 4778 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 47b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47c0 148 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 481c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4820 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4908 68 .cfa: sp 0 + .ra: x30
STACK CFI 490c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4924 x19: .cfa -16 + ^
STACK CFI 494c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4950 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 496c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4970 158 .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4984 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4994 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ac8 9c .cfa: sp 0 + .ra: x30
STACK CFI 4acc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ae4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4af4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b28 x19: x19 x20: x20
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b60 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 4b68 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b84 x19: .cfa -16 + ^
STACK CFI 4be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c10 8 .cfa: sp 0 + .ra: x30
