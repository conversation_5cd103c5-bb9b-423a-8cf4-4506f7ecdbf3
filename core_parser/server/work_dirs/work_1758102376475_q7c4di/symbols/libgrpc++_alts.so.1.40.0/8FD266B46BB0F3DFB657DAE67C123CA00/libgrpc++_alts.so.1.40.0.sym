MODULE Linux arm64 8FD266B46BB0F3DFB657DAE67C123CA00 libgrpc++_alts.so.1.40
INFO CODE_ID B466D28FB06BDFF3B657DAE67C123CA0
PUBLIC 1d58 0 _init
PUBLIC 1ff0 0 _GLOBAL__sub_I_alts_context.cc
PUBLIC 2030 0 _GLOBAL__sub_I_alts_util.cc
PUBLIC 206c 0 call_weak_fn
PUBLIC 2080 0 deregister_tm_clones
PUBLIC 20b0 0 register_tm_clones
PUBLIC 20ec 0 __do_global_dtors_aux
PUBLIC 213c 0 frame_dummy
PUBLIC 2140 0 grpc::experimental::AltsContext::application_protocol[abi:cxx11]() const
PUBLIC 2230 0 grpc::experimental::AltsContext::record_protocol[abi:cxx11]() const
PUBLIC 2320 0 grpc::experimental::AltsContext::peer_service_account[abi:cxx11]() const
PUBLIC 2410 0 grpc::experimental::AltsContext::local_service_account[abi:cxx11]() const
PUBLIC 2500 0 grpc::experimental::AltsContext::security_level() const
PUBLIC 2510 0 grpc::experimental::AltsContext::peer_rpc_versions() const
PUBLIC 2520 0 grpc::experimental::AltsContext::peer_attributes[abi:cxx11]() const
PUBLIC 2530 0 grpc::experimental::AltsContext::AltsContext(grpc_gcp_AltsContext const*)
PUBLIC 2ef0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*)
PUBLIC 2f80 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3100 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>&&, std::tuple<>&&)
PUBLIC 34e0 0 grpc::experimental::GetAltsContextFromAuthContext(std::shared_ptr<grpc::AuthContext const> const&)
PUBLIC 3b40 0 grpc::experimental::AltsClientAuthzCheck(std::shared_ptr<grpc::AuthContext const> const&, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 4160 0 grpc::experimental::AltsContext::~AltsContext()
PUBLIC 4240 0 __gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > > std::__find_if<__gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >, __gnu_cxx::__ops::_Iter_equals_val<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const> >(__gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >, __gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >, __gnu_cxx::__ops::_Iter_equals_val<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const>, std::random_access_iterator_tag)
PUBLIC 4450 0 std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >* std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_copy<std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_Alloc_node>(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const*, std::_Rb_tree_node_base*, std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_Alloc_node&)
PUBLIC 4804 0 _fini
STACK CFI INIT 2080 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ec 50 .cfa: sp 0 + .ra: x30
STACK CFI 20fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2104 x19: .cfa -16 + ^
STACK CFI 2134 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 213c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2140 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2144 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2150 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2158 x21: .cfa -32 + ^
STACK CFI 21a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 21a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 21c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 21c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 2218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 221c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2230 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2234 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2240 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2248 x21: .cfa -32 + ^
STACK CFI 2294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2298 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 22b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 230c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2320 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2330 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2338 x21: .cfa -32 + ^
STACK CFI 2384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2388 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 23a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 23f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2410 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2414 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2420 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2428 x21: .cfa -32 + ^
STACK CFI 2474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2478 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 2494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2498 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 24e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 24ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2500 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2510 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef0 8c .cfa: sp 0 + .ra: x30
STACK CFI 2ef8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f08 x21: .cfa -16 + ^
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2f80 178 .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f98 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2fa0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fa8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 307c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 30d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 30d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 30f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 3100 3dc .cfa: sp 0 + .ra: x30
STACK CFI 3104 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 310c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 311c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 312c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 325c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3260 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 332c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3330 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2530 9c0 .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 253c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 2548 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 2554 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 256c x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 28e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 28e4 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^ x29: .cfa -352 + ^
STACK CFI INIT 1ff0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ffc x19: .cfa -16 + ^
STACK CFI 2024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4160 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4164 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 416c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 417c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41e0 x21: x21 x22: x22
STACK CFI 4230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4234 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 423c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4240 208 .cfa: sp 0 + .ra: x30
STACK CFI 4244 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 424c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4254 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4260 x23: .cfa -16 + ^
STACK CFI 42e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4318 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4348 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4378 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4450 3b4 .cfa: sp 0 + .ra: x30
STACK CFI 4454 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 445c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4464 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4470 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 447c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4638 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 34e0 658 .cfa: sp 0 + .ra: x30
STACK CFI 34e4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 34f0 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 34f8 x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 359c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 35a0 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI 35b8 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 35c0 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 35d0 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 3644 x21: x21 x22: x22
STACK CFI 3648 x23: x23 x24: x24
STACK CFI 364c x25: x25 x26: x26
STACK CFI 3680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 3684 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI 36ac x21: x21 x22: x22
STACK CFI 36b0 x23: x23 x24: x24
STACK CFI 36b4 x25: x25 x26: x26
STACK CFI 36b8 x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 3ad4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3adc x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 3ae0 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 3ae4 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 3b0c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3b14 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 3b1c x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 3b28 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI INIT 3b40 620 .cfa: sp 0 + .ra: x30
STACK CFI 3b44 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3b4c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3b58 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3b64 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3f6c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2030 3c .cfa: sp 0 + .ra: x30
STACK CFI 2034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 203c x19: .cfa -16 + ^
STACK CFI 2064 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
