MODULE Linux arm64 71782AA0A5C268BB4F7E7AD517C38D610 libgmodule-2.0.so.0
INFO CODE_ID A02A7871C2A5BB684F7E7AD517C38D6130F2A100
PUBLIC 1288 0 g_module_supported
PUBLIC 12c0 0 g_module_make_resident
PUBLIC 12f0 0 g_module_error
PUBLIC 1300 0 g_module_close
PUBLIC 1480 0 g_module_symbol
PUBLIC 1650 0 g_module_open
PUBLIC 1e38 0 g_module_name
PUBLIC 1e98 0 g_module_build_path
STACK CFI INIT 1188 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 11fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1204 x19: .cfa -16 + ^
STACK CFI 123c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1248 40 .cfa: sp 0 + .ra: x30
STACK CFI 124c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1288 38 .cfa: sp 0 + .ra: x30
STACK CFI 128c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1300 17c .cfa: sp 0 + .ra: x30
STACK CFI 1304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 130c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1428 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1458 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1480 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1484 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 148c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1498 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 159c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 15d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 15d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1614 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 164c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 1650 7e4 .cfa: sp 0 + .ra: x30
STACK CFI 1654 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 165c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 166c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1680 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 168c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1754 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1e38 5c .cfa: sp 0 + .ra: x30
STACK CFI 1e58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e98 bc .cfa: sp 0 + .ra: x30
STACK CFI 1f0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f38 .cfa: sp 0 + .ra: .ra x29: x29
