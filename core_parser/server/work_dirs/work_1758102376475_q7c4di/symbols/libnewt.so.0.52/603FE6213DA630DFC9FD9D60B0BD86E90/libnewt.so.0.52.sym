MODULE Linux arm64 603FE6213DA630DFC9FD9D60B0BD86E90 libnewt.so.0.52
INFO CODE_ID 21E63F60A63DDF30C9FD9D60B0BD86E91995415C
PUBLIC 64d0 0 newtSetSuspendCallback
PUBLIC 64e0 0 _newt_wstrlen
PUBLIC 6818 0 newtFlushInput
PUBLIC 6840 0 newtRefresh
PUBLIC 6848 0 newtSuspend
PUBLIC 6870 0 newtResume
PUBLIC 6898 0 newtCls
PUBLIC 68c0 0 newtResizeScreen
PUBLIC 6908 0 newtSetColors
PUBLIC 6ce0 0 newtSetColor
PUBLIC 6d18 0 newtGetKey
PUBLIC 6e70 0 newtClearKeyBuffer
PUBLIC 6e98 0 newtWaitForKey
PUBLIC 6eb0 0 newtGetWindowPos
PUBLIC 6ee0 0 newtGetrc
PUBLIC 6f28 0 newtGotorc
PUBLIC 6f50 0 newtDrawBox
PUBLIC 7008 0 newtClearBox
PUBLIC 7040 0 newtDelay
PUBLIC 7048 0 newtDefaultEventHandler
PUBLIC 7058 0 newtRedrawHelpLine
PUBLIC 7228 0 newtPushHelpLine
PUBLIC 72c0 0 newtPopHelpLine
PUBLIC 7320 0 newtDrawRootText
PUBLIC 7398 0 newtSetFlags
PUBLIC 73c8 0 newtBell
PUBLIC 73d0 0 newtGetScreenSize
PUBLIC 7400 0 newtDefaultPlaceHandler
PUBLIC 7408 0 newtDefaultMappedHandler
PUBLIC 7410 0 newtCursorOff
PUBLIC 7420 0 newtInit
PUBLIC 7640 0 newtCursorOn
PUBLIC 7650 0 newtFinished
PUBLIC 7770 0 newtTrashScreen
PUBLIC 7798 0 newtOpenWindow
PUBLIC 7aa0 0 newtCenteredWindow
PUBLIC 7ae8 0 newtPopWindowNoRefresh
PUBLIC 7be0 0 newtPopWindow
PUBLIC 7bf8 0 newtComponentGetPosition
PUBLIC 7c18 0 newtComponentGetSize
PUBLIC 8068 0 newtCompactButton
PUBLIC 8070 0 newtButton
PUBLIC 89a0 0 newtFormGetCurrent
PUBLIC 89c8 0 newtFormGetScrollPosition
PUBLIC 89d8 0 newtFormSetCurrent
PUBLIC 8ad8 0 newtFormSetTimer
PUBLIC 8ae8 0 newtFormSetHeight
PUBLIC 8b00 0 newtFormSetWidth
PUBLIC 8b08 0 newtFormAddComponent
PUBLIC 8b98 0 newtFormAddComponents
PUBLIC 8c68 0 newtComponentDestroy
PUBLIC 8cc8 0 newtFormDestroy
PUBLIC 8d40 0 newtFormAddHotKey
PUBLIC 8d88 0 newtForm
PUBLIC 8e78 0 newtFormSetSize
PUBLIC 9068 0 newtFormSetScrollPosition
PUBLIC 90d0 0 newtDrawForm
PUBLIC 9658 0 newtFormRun
PUBLIC 9dc8 0 newtRunForm
PUBLIC 9e40 0 newtComponentAddCallback
PUBLIC 9e48 0 newtComponentAddDestroyCallback
PUBLIC 9e50 0 newtComponentTakesFocus
PUBLIC 9e58 0 newtFormSetBackground
PUBLIC 9e68 0 newtFormWatchFd
PUBLIC 9f10 0 newtSetHelpCallback
PUBLIC a058 0 newtRadioGetCurrent
PUBLIC a090 0 newtRadioSetCurrent
PUBLIC a240 0 newtCheckboxGetValue
PUBLIC a250 0 newtCheckboxSetValue
PUBLIC a260 0 newtCheckbox
PUBLIC a380 0 newtRadiobutton
PUBLIC a3e0 0 newtCheckboxSetFlags
PUBLIC b020 0 newtEntrySet
PUBLIC b0e8 0 newtEntry
PUBLIC b240 0 newtEntrySetFlags
PUBLIC b2c8 0 newtEntrySetColors
PUBLIC b2d8 0 newtEntryGetValue
PUBLIC b2e8 0 newtEntrySetFilter
PUBLIC b2f8 0 newtEntryGetCursorPosition
PUBLIC b308 0 newtEntrySetCursorPosition
PUBLIC b3a0 0 newtLabel
PUBLIC b450 0 newtLabelSetText
PUBLIC b4f0 0 newtLabelSetColors
PUBLIC b858 0 newtListbox
PUBLIC b9a0 0 newtListboxSetCurrent
PUBLIC ba28 0 newtListboxSetCurrentByKey
PUBLIC ba60 0 newtListboxSetWidth
PUBLIC baa0 0 newtListboxGetCurrent
PUBLIC bad8 0 newtListboxSelectItem
PUBLIC c178 0 newtListboxClearSelection
PUBLIC c1a0 0 newtListboxGetSelection
PUBLIC c238 0 newtListboxSetEntry
PUBLIC c348 0 newtListboxSetData
PUBLIC c380 0 newtListboxAppendEntry
PUBLIC c4a8 0 newtListboxInsertEntry
PUBLIC c648 0 newtListboxDeleteEntry
PUBLIC c7e8 0 newtListboxClear
PUBLIC c898 0 newtListboxItemCount
PUBLIC c8a8 0 newtListboxGetEntry
PUBLIC cac0 0 newtScrollbarSet
PUBLIC cb60 0 newtVerticalScrollbar
PUBLIC cc00 0 newtScrollbarSetColors
PUBLIC d5b0 0 newtTextboxSetHeight
PUBLIC d5b8 0 newtTextboxGetNumLines
PUBLIC d5c8 0 newtTextbox
PUBLIC d6e8 0 newtTextboxSetColors
PUBLIC d6f8 0 newtReflowText
PUBLIC d818 0 newtTextboxSetText
PUBLIC d9b8 0 newtTextboxReflowed
PUBLIC db90 0 newtScale
PUBLIC dc18 0 newtScaleSet
PUBLIC dcd0 0 newtScaleSetColors
PUBLIC e278 0 newtCreateGrid
PUBLIC e310 0 newtGridPlace
PUBLIC e318 0 newtGridFree
PUBLIC e3f8 0 newtGridSetField
PUBLIC e670 0 newtGridGetSize
PUBLIC e700 0 newtGridWrappedWindow
PUBLIC e7c0 0 newtGridWrappedWindowAt
PUBLIC e858 0 newtGridAddComponentsToForm
PUBLIC e930 0 newtGridHCloseStacked
PUBLIC e9e0 0 newtGridVCloseStacked
PUBLIC ea98 0 newtGridVStacked
PUBLIC eb50 0 newtGridHStacked
PUBLIC ec00 0 newtGridBasicWindow
PUBLIC ecd8 0 newtGridSimpleWindow
PUBLIC f2b0 0 newtWinChoice
PUBLIC f388 0 newtWinMessage
PUBLIC f438 0 newtWinMessagev
PUBLIC f470 0 newtWinTernary
PUBLIC f550 0 newtWinMenu
PUBLIC f928 0 newtWinEntries
PUBLIC fee8 0 newtButtonBarv
PUBLIC 10078 0 newtButtonBar
PUBLIC 108e8 0 newtCheckboxTreeFindItem
PUBLIC 10990 0 newtCheckboxTreeAddArray
PUBLIC 10c48 0 newtCheckboxTreeAddItem
PUBLIC 10e18 0 newtCheckboxTreeSetWidth
PUBLIC 10e50 0 newtCheckboxTreeGetMultiSelection
PUBLIC 10f10 0 newtCheckboxTreeGetSelection
PUBLIC 10f18 0 newtCheckboxTreeMulti
PUBLIC 11020 0 newtCheckboxTree
PUBLIC 115e8 0 newtCheckboxTreeGetCurrent
PUBLIC 11610 0 newtCheckboxTreeSetEntry
PUBLIC 116d8 0 newtCheckboxTreeGetEntryValue
PUBLIC 11738 0 newtCheckboxTreeSetEntryValue
PUBLIC 117a8 0 newtCheckboxTreeSetCurrent
STACK CFI INIT 5348 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5378 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 53bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53c4 x19: .cfa -16 + ^
STACK CFI 53fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5408 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5428 178 .cfa: sp 0 + .ra: x30
STACK CFI 542c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5438 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5444 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5458 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5468 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5514 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5560 x27: .cfa -32 + ^
STACK CFI 5584 x27: x27
STACK CFI 559c x27: .cfa -32 + ^
STACK CFI INIT 55a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 55a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55ac x19: .cfa -16 + ^
STACK CFI 55dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55e0 544 .cfa: sp 0 + .ra: x30
STACK CFI 55e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 55ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 55fc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5610 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 561c x27: .cfa -16 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 56c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5b28 d8 .cfa: sp 0 + .ra: x30
STACK CFI 5b30 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b40 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c00 9c .cfa: sp 0 + .ra: x30
STACK CFI 5c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c20 x21: .cfa -16 + ^
STACK CFI 5c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5ca0 514 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5cb0 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5cbc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5cc8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5d20 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5e2c x27: x27 x28: x28
STACK CFI 5e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5e78 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 6070 x27: x27 x28: x28
STACK CFI 60c0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6138 x27: x27 x28: x28
STACK CFI 6168 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 61a0 x27: x27 x28: x28
STACK CFI INIT 61b8 19c .cfa: sp 0 + .ra: x30
STACK CFI 61bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 61c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 61d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6214 x23: .cfa -32 + ^
STACK CFI 62bc x23: x23
STACK CFI 62c0 x23: .cfa -32 + ^
STACK CFI 62c4 x23: x23
STACK CFI 62f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6300 x23: .cfa -32 + ^
STACK CFI 6310 x23: x23
STACK CFI 6314 x23: .cfa -32 + ^
STACK CFI 6324 x23: x23
STACK CFI 6328 x23: .cfa -32 + ^
STACK CFI 6338 x23: x23
STACK CFI 6340 x23: .cfa -32 + ^
STACK CFI 6350 x23: x23
STACK CFI INIT 6358 dc .cfa: sp 0 + .ra: x30
STACK CFI 635c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6364 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 63ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 63b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6404 x21: x21 x22: x22
STACK CFI 6410 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6420 x21: x21 x22: x22
STACK CFI 6428 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6430 x21: x21 x22: x22
STACK CFI INIT 6438 98 .cfa: sp 0 + .ra: x30
STACK CFI 643c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6444 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 644c x21: .cfa -32 + ^
STACK CFI 64a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 64a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 64d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64e0 ec .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 64f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 64f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 651c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6584 x23: x23 x24: x24
STACK CFI 65a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 65ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 65c0 x23: x23 x24: x24
STACK CFI 65c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 65d0 170 .cfa: sp 0 + .ra: x30
STACK CFI 65d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 65dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 65ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6600 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6608 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6644 x27: .cfa -32 + ^
STACK CFI 6688 x27: x27
STACK CFI 66e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 66e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 672c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6730 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 6734 x27: .cfa -32 + ^
STACK CFI 673c x27: x27
STACK CFI INIT 6740 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6744 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 674c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6758 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 676c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 678c x25: .cfa -48 + ^
STACK CFI 67dc x25: x25
STACK CFI 6800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6804 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 6808 x25: x25
STACK CFI 6810 x25: .cfa -48 + ^
STACK CFI INIT 6818 24 .cfa: sp 0 + .ra: x30
STACK CFI 681c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6848 28 .cfa: sp 0 + .ra: x30
STACK CFI 684c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6870 28 .cfa: sp 0 + .ra: x30
STACK CFI 6874 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6884 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6898 28 .cfa: sp 0 + .ra: x30
STACK CFI 689c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 68c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 68c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68cc x19: .cfa -16 + ^
STACK CFI 68e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6908 274 .cfa: sp 0 + .ra: x30
STACK CFI 690c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 691c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 69e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 69fc x21: .cfa -16 + ^
STACK CFI 6b6c x21: x21
STACK CFI 6b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6b80 15c .cfa: sp 0 + .ra: x30
STACK CFI 6b88 .cfa: sp 17152 +
STACK CFI 6b94 .ra: .cfa -17144 + ^ x29: .cfa -17152 + ^
STACK CFI 6ba0 x19: .cfa -17136 + ^ x20: .cfa -17128 + ^
STACK CFI 6ba8 x21: .cfa -17120 + ^ x22: .cfa -17112 + ^
STACK CFI 6c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c58 .cfa: sp 17152 + .ra: .cfa -17144 + ^ x19: .cfa -17136 + ^ x20: .cfa -17128 + ^ x21: .cfa -17120 + ^ x22: .cfa -17112 + ^ x29: .cfa -17152 + ^
STACK CFI INIT 6ce0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d18 158 .cfa: sp 0 + .ra: x30
STACK CFI 6d1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6d28 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6d34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6d40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6d4c x25: .cfa -16 + ^
STACK CFI 6dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6dcc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6e00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6e70 24 .cfa: sp 0 + .ra: x30
STACK CFI 6e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6e98 18 .cfa: sp 0 + .ra: x30
STACK CFI 6e9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6eac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6eb0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ee0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f28 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f50 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f78 x23: .cfa -16 + ^
STACK CFI 6fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6fbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7008 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7040 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7048 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7058 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 705c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7064 .cfa: x29 80 +
STACK CFI 7068 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7074 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7090 x23: .cfa -32 + ^
STACK CFI 71a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 71a8 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7228 98 .cfa: sp 0 + .ra: x30
STACK CFI 722c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7234 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7288 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7294 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 72bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 72c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 72c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 72cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72d8 x21: .cfa -16 + ^
STACK CFI 7308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 730c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7320 74 .cfa: sp 0 + .ra: x30
STACK CFI 7324 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 732c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 733c x21: .cfa -16 + ^
STACK CFI 737c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7380 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7398 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73d0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7400 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7408 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7410 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7420 21c .cfa: sp 0 + .ra: x30
STACK CFI 7424 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7434 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 74b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 74d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75dc x21: x21 x22: x22
STACK CFI 75e0 x23: x23 x24: x24
STACK CFI 75f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7600 x23: x23 x24: x24
STACK CFI 7604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7608 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 762c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 7640 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7650 11c .cfa: sp 0 + .ra: x30
STACK CFI 7654 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7660 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7770 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7798 304 .cfa: sp 0 + .ra: x30
STACK CFI 779c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 77a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 77b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 77b8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 77f4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 77fc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7a60 x25: x25 x26: x26
STACK CFI 7a68 x27: x27 x28: x28
STACK CFI 7a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7a7c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7a88 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7a8c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7a94 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 7aa0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ae8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 7aec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7af8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7b04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7b10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7bc0 x23: x23 x24: x24
STACK CFI 7bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 7bcc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI INIT 7be0 14 .cfa: sp 0 + .ra: x30
STACK CFI 7be4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7bf8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c18 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c38 34 .cfa: sp 0 + .ra: x30
STACK CFI 7c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7c70 e0 .cfa: sp 0 + .ra: x30
STACK CFI 7c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7c7c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c94 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7c9c x25: .cfa -16 + ^
STACK CFI 7d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7d30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7d50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d60 1ac .cfa: sp 0 + .ra: x30
STACK CFI 7d64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 7d88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7e14 x21: x21 x22: x22
STACK CFI 7e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 7e20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7ec0 x23: x23 x24: x24
STACK CFI 7ec4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7ef4 x23: x23 x24: x24
STACK CFI INIT 7f10 148 .cfa: sp 0 + .ra: x30
STACK CFI 7f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8058 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8068 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8078 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8098 80 .cfa: sp 0 + .ra: x30
STACK CFI 809c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 80a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 80b0 x23: .cfa -16 + ^
STACK CFI 8114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8118 c0 .cfa: sp 0 + .ra: x30
STACK CFI 811c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8128 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 813c x21: .cfa -48 + ^
STACK CFI 81d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 81d8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 81dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 81e4 x21: .cfa -80 + ^
STACK CFI 81ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 827c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8280 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI 828c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8290 9c .cfa: sp 0 + .ra: x30
STACK CFI 8294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 82ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8330 120 .cfa: sp 0 + .ra: x30
STACK CFI 8334 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 833c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8344 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8350 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 837c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8380 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8390 x25: .cfa -16 + ^
STACK CFI 83d4 x25: x25
STACK CFI 8438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 843c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8440 x25: x25
STACK CFI 8444 x25: .cfa -16 + ^
STACK CFI 844c x25: x25
STACK CFI INIT 8450 3ac .cfa: sp 0 + .ra: x30
STACK CFI 8454 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 845c x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 846c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 8484 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 849c x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 84ac x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 854c x25: x25 x26: x26
STACK CFI 8550 x27: x27 x28: x28
STACK CFI 8578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 857c .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI 8710 x25: x25 x26: x26
STACK CFI 8718 x27: x27 x28: x28
STACK CFI 871c x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 87e8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 87f4 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 87f8 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 8800 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8810 x19: .cfa -16 + ^
STACK CFI 8854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8858 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 887c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8880 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 88a8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 88ac .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 88b4 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 88c4 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 8998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 899c .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x29: .cfa -480 + ^
STACK CFI INIT 89a0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89d8 fc .cfa: sp 0 + .ra: x30
STACK CFI 89dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 89e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 89f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8ad8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ae8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b08 90 .cfa: sp 0 + .ra: x30
STACK CFI 8b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8b98 cc .cfa: sp 0 + .ra: x30
STACK CFI 8b9c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8ba4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c54 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8c68 5c .cfa: sp 0 + .ra: x30
STACK CFI 8c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c78 x19: .cfa -16 + ^
STACK CFI 8ca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8cc8 74 .cfa: sp 0 + .ra: x30
STACK CFI 8ccc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8cd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8cdc x21: .cfa -16 + ^
STACK CFI 8d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8d40 48 .cfa: sp 0 + .ra: x30
STACK CFI 8d44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8d88 ec .cfa: sp 0 + .ra: x30
STACK CFI 8d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8d98 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8da4 x23: .cfa -16 + ^
STACK CFI 8e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8e64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8e78 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8e7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8e84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8eb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8ec8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8fc0 x21: x21 x22: x22
STACK CFI 8fc4 x23: x23 x24: x24
STACK CFI 8fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8fe0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9068 68 .cfa: sp 0 + .ra: x30
STACK CFI 906c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9074 x21: .cfa -16 + ^
STACK CFI 907c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 90a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 90ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 90cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 90d0 11c .cfa: sp 0 + .ra: x30
STACK CFI 90d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 90e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 91d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 91e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 91f0 464 .cfa: sp 0 + .ra: x30
STACK CFI 91f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 91fc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9204 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9210 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9218 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 922c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9270 x21: x21 x22: x22
STACK CFI 9274 x27: x27 x28: x28
STACK CFI 9288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 928c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 9290 x21: x21 x22: x22
STACK CFI 92a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 92ac .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 94e4 x21: x21 x22: x22
STACK CFI 94e8 x27: x27 x28: x28
STACK CFI 94ec x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 95e8 x21: x21 x22: x22
STACK CFI 95ec x27: x27 x28: x28
STACK CFI 95f0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 9658 770 .cfa: sp 0 + .ra: x30
STACK CFI 965c .cfa: sp 656 +
STACK CFI 9660 .ra: .cfa -648 + ^ x29: .cfa -656 + ^
STACK CFI 9668 x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 9674 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 96a4 x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 9a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9a34 .cfa: sp 656 + .ra: .cfa -648 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^ x29: .cfa -656 + ^
STACK CFI INIT 9dc8 78 .cfa: sp 0 + .ra: x30
STACK CFI 9dcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9dd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e58 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e68 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9e74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9e80 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9f10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f20 f8 .cfa: sp 0 + .ra: x30
STACK CFI 9f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a018 3c .cfa: sp 0 + .ra: x30
STACK CFI a01c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a024 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a058 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT a090 98 .cfa: sp 0 + .ra: x30
STACK CFI a094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a09c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a11c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a128 114 .cfa: sp 0 + .ra: x30
STACK CFI a12c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a134 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a158 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a1a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a1e8 x21: x21 x22: x22
STACK CFI a218 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a224 x21: x21 x22: x22
STACK CFI a234 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a238 x21: x21 x22: x22
STACK CFI INIT a240 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a250 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a260 120 .cfa: sp 0 + .ra: x30
STACK CFI a264 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a270 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a28c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a29c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a368 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a380 60 .cfa: sp 0 + .ra: x30
STACK CFI a384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3a0 x19: .cfa -16 + ^
STACK CFI a3dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a3e0 94 .cfa: sp 0 + .ra: x30
STACK CFI a3e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a3ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a3f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a46c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a470 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a478 34 .cfa: sp 0 + .ra: x30
STACK CFI a47c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a484 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a4a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a4b0 3c .cfa: sp 0 + .ra: x30
STACK CFI a4b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a4bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a4f0 74 .cfa: sp 0 + .ra: x30
STACK CFI a4f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a500 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a50c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a54c x21: x21 x22: x22
STACK CFI a558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a55c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a568 458 .cfa: sp 0 + .ra: x30
STACK CFI a56c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a570 .cfa: x29 112 +
STACK CFI a574 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a580 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a5a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a7c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a7c8 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT a9c0 660 .cfa: sp 0 + .ra: x30
STACK CFI a9c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a9c8 .cfa: x29 128 +
STACK CFI a9cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a9f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI aa40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI aa44 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT b020 c4 .cfa: sp 0 + .ra: x30
STACK CFI b024 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b02c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b040 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI b0a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b0ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b0e8 154 .cfa: sp 0 + .ra: x30
STACK CFI b0ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b0f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b104 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b110 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b120 x27: .cfa -16 + ^
STACK CFI b1f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b1f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT b240 88 .cfa: sp 0 + .ra: x30
STACK CFI b244 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b24c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b25c x21: .cfa -32 + ^
STACK CFI b2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b2c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT b2c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b2d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b2e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b2f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b308 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b318 34 .cfa: sp 0 + .ra: x30
STACK CFI b31c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b324 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b350 50 .cfa: sp 0 + .ra: x30
STACK CFI b354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b35c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b374 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b3a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b3a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b3b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b3c0 x23: .cfa -16 + ^
STACK CFI b44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT b450 a0 .cfa: sp 0 + .ra: x30
STACK CFI b454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b45c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b468 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b4c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b4c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b4ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT b4f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b500 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT b528 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT b568 6c .cfa: sp 0 + .ra: x30
STACK CFI b56c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b574 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT b5d8 220 .cfa: sp 0 + .ra: x30
STACK CFI b5dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b5e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b5f8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b5fc .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI b600 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b65c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b694 x25: .cfa -16 + ^
STACK CFI b794 x21: x21 x22: x22
STACK CFI b79c x25: x25
STACK CFI b7a8 x19: x19 x20: x20
STACK CFI b7b8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI b7c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI b7cc x21: x21 x22: x22
STACK CFI INIT b7f8 60 .cfa: sp 0 + .ra: x30
STACK CFI b7fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b808 x19: .cfa -16 + ^
STACK CFI b848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b84c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b858 144 .cfa: sp 0 + .ra: x30
STACK CFI b85c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b868 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b878 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b958 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT b9a0 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba28 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba60 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT baa0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT bad8 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb80 5f4 .cfa: sp 0 + .ra: x30
STACK CFI bb90 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bb9c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI bba8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bbe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bbe8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI bccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bcdc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI c008 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c014 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c01c x27: .cfa -16 + ^
STACK CFI c120 x23: x23 x24: x24
STACK CFI c124 x25: x25 x26: x26
STACK CFI c128 x27: x27
STACK CFI c12c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c140 x23: x23 x24: x24
STACK CFI c144 x25: x25 x26: x26
STACK CFI c148 x27: x27
STACK CFI c14c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c150 x23: x23 x24: x24
STACK CFI c154 x25: x25 x26: x26
STACK CFI c158 x27: x27
STACK CFI c15c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c160 x23: x23 x24: x24
STACK CFI c164 x25: x25 x26: x26
STACK CFI c168 x27: x27
STACK CFI c16c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT c178 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c1a0 98 .cfa: sp 0 + .ra: x30
STACK CFI c1b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c1b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c1c4 x21: .cfa -16 + ^
STACK CFI c20c x21: x21
STACK CFI c214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c218 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c220 x21: x21
STACK CFI c224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c230 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT c238 10c .cfa: sp 0 + .ra: x30
STACK CFI c23c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c244 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c250 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c25c x23: .cfa -16 + ^
STACK CFI c2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c2d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT c348 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT c380 124 .cfa: sp 0 + .ra: x30
STACK CFI c384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c38c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c39c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI c434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT c4a8 19c .cfa: sp 0 + .ra: x30
STACK CFI c4ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c4b4 x25: .cfa -16 + ^
STACK CFI c4bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c4c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c4d0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI c514 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI c5fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI c600 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT c648 19c .cfa: sp 0 + .ra: x30
STACK CFI c64c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c654 x23: .cfa -16 + ^
STACK CFI c65c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c66c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c6b4 x21: x21 x22: x22
STACK CFI c6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI c6c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c770 x21: x21 x22: x22
STACK CFI c778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI c77c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c7a0 x21: x21 x22: x22
STACK CFI c7a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI c7ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c7b4 x21: x21 x22: x22
STACK CFI c7c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI c7c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT c7e8 b0 .cfa: sp 0 + .ra: x30
STACK CFI c7f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c7f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c808 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c868 x19: x19 x20: x20
STACK CFI c880 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c888 x19: x19 x20: x20
STACK CFI c890 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT c898 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c8a8 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT c918 70 .cfa: sp 0 + .ra: x30
STACK CFI c91c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c92c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c948 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT c988 10c .cfa: sp 0 + .ra: x30
STACK CFI c98c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c994 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c9a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c9ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ca14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ca90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ca98 28 .cfa: sp 0 + .ra: x30
STACK CFI ca9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI caa4 x19: .cfa -16 + ^
STACK CFI cabc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cac0 9c .cfa: sp 0 + .ra: x30
STACK CFI cac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cad0 x21: .cfa -16 + ^
STACK CFI cad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cb0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cb10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI cb50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cb54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT cb60 9c .cfa: sp 0 + .ra: x30
STACK CFI cb64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cb6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cb74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cb84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI cbec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cbf0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT cc00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT cc10 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc38 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc70 88 .cfa: sp 0 + .ra: x30
STACK CFI cc74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cc7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cc84 x21: .cfa -16 + ^
STACK CFI ccf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT ccf8 1b0 .cfa: sp 0 + .ra: x30
STACK CFI ccfc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cd04 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cd0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cd30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cea4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT cea8 208 .cfa: sp 0 + .ra: x30
STACK CFI ceac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ceb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cec0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ceec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cf2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cf30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d0b0 130 .cfa: sp 0 + .ra: x30
STACK CFI d0b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d0c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d0c8 x25: .cfa -16 + ^
STACK CFI d0f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d1a4 x19: x19 x20: x20
STACK CFI d1bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d1c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d1dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT d1e0 8c .cfa: sp 0 + .ra: x30
STACK CFI d1e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d1ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d1f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d200 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d270 340 .cfa: sp 0 + .ra: x30
STACK CFI d274 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI d284 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI d2a8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI d2f8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI d304 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI d3b8 x23: x23 x24: x24
STACK CFI d3bc x25: x25 x26: x26
STACK CFI d40c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI d410 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI d424 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI d584 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d5a8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI d5ac x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT d5b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d5b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d5c8 11c .cfa: sp 0 + .ra: x30
STACK CFI d5cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d5d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d5e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d5f0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d69c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d6e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT d6e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d6f8 11c .cfa: sp 0 + .ra: x30
STACK CFI d6fc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d708 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI d718 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d730 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI d738 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI d764 x27: .cfa -48 + ^
STACK CFI d7b4 x27: x27
STACK CFI d808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d80c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI d810 x27: .cfa -48 + ^
STACK CFI INIT d818 19c .cfa: sp 0 + .ra: x30
STACK CFI d81c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d824 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d830 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d838 x23: .cfa -48 + ^
STACK CFI d95c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d960 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT d9b8 9c .cfa: sp 0 + .ra: x30
STACK CFI d9bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d9c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d9d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI da4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI da50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT da58 138 .cfa: sp 0 + .ra: x30
STACK CFI da5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI da64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI da70 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI daa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI daac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI dab4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI db08 x25: .cfa -48 + ^
STACK CFI db64 x25: x25
STACK CFI db74 x23: x23 x24: x24
STACK CFI db78 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI db84 x23: x23 x24: x24 x25: x25
STACK CFI db88 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI db8c x25: .cfa -48 + ^
STACK CFI INIT db90 84 .cfa: sp 0 + .ra: x30
STACK CFI db94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI db9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI dba8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI dbb4 x23: .cfa -16 + ^
STACK CFI dc10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT dc18 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dcd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dce0 594 .cfa: sp 0 + .ra: x30
STACK CFI dce4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI dce8 .cfa: x29 144 +
STACK CFI dcec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI dcf8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI dd30 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e12c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e130 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT e278 94 .cfa: sp 0 + .ra: x30
STACK CFI e27c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e284 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e290 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e298 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT e310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e318 dc .cfa: sp 0 + .ra: x30
STACK CFI e31c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e324 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e338 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e344 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e36c x21: x21 x22: x22
STACK CFI e370 x23: x23 x24: x24
STACK CFI e384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e388 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT e3f8 ac .cfa: sp 0 + .ra: x30
STACK CFI e3fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e404 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e414 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e41c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e42c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e440 x27: .cfa -16 + ^
STACK CFI e490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI e494 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT e4a8 1c8 .cfa: sp 0 + .ra: x30
STACK CFI e4ac .cfa: sp 960 +
STACK CFI e4b8 .ra: .cfa -920 + ^ x29: .cfa -928 + ^
STACK CFI e4c4 x21: .cfa -896 + ^ x22: .cfa -888 + ^
STACK CFI e4d4 x23: .cfa -880 + ^ x24: .cfa -872 + ^
STACK CFI e4f0 x19: .cfa -912 + ^ x20: .cfa -904 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^
STACK CFI e500 x27: .cfa -848 + ^
STACK CFI e65c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI e660 .cfa: sp 960 + .ra: .cfa -920 + ^ x19: .cfa -912 + ^ x20: .cfa -904 + ^ x21: .cfa -896 + ^ x22: .cfa -888 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x29: .cfa -928 + ^
STACK CFI INIT e670 8c .cfa: sp 0 + .ra: x30
STACK CFI e674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e67c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e688 x21: .cfa -16 + ^
STACK CFI e6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e6c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e700 c0 .cfa: sp 0 + .ra: x30
STACK CFI e704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e70c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e71c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e7bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT e7c0 98 .cfa: sp 0 + .ra: x30
STACK CFI e7c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e7cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e7dc x23: .cfa -32 + ^
STACK CFI e7e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e854 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT e858 d4 .cfa: sp 0 + .ra: x30
STACK CFI e85c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e864 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e878 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e884 x25: .cfa -16 + ^
STACK CFI e890 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e8e8 x19: x19 x20: x20
STACK CFI e8ec x21: x21 x22: x22
STACK CFI e8f0 x25: x25
STACK CFI e8f8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI e8fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT e930 b0 .cfa: sp 0 + .ra: x30
STACK CFI e934 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI e964 x19: .cfa -272 + ^
STACK CFI e9d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e9dc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT e9e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI e9e4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI ea10 x19: .cfa -272 + ^
STACK CFI ea8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ea90 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT ea98 b4 .cfa: sp 0 + .ra: x30
STACK CFI ea9c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI eac8 x19: .cfa -272 + ^
STACK CFI eb44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI eb48 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT eb50 b0 .cfa: sp 0 + .ra: x30
STACK CFI eb54 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI eb84 x19: .cfa -272 + ^
STACK CFI ebf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ebfc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT ec00 d8 .cfa: sp 0 + .ra: x30
STACK CFI ec04 .cfa: sp 80 +
STACK CFI ec08 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ec10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ec20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ecd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ecd8 d8 .cfa: sp 0 + .ra: x30
STACK CFI ecdc .cfa: sp 80 +
STACK CFI ece0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ece8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ecf8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI edac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT edb0 500 .cfa: sp 0 + .ra: x30
STACK CFI edb4 .cfa: sp 272 +
STACK CFI edb8 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI edc0 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI edcc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI eddc x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI edf4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI ee08 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI f06c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f070 .cfa: sp 272 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT f2b0 d4 .cfa: sp 0 + .ra: x30
STACK CFI f2b4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f2c4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI f2fc x21: .cfa -256 + ^
STACK CFI f374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f378 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x29: .cfa -288 + ^
STACK CFI INIT f388 ac .cfa: sp 0 + .ra: x30
STACK CFI f38c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f3b8 x19: .cfa -272 + ^
STACK CFI f42c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f430 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT f438 38 .cfa: sp 0 + .ra: x30
STACK CFI f440 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f46c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f470 e0 .cfa: sp 0 + .ra: x30
STACK CFI f474 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f484 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI f4b8 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI f538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f53c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x29: .cfa -288 + ^
STACK CFI INIT f550 3d8 .cfa: sp 0 + .ra: x30
STACK CFI f554 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI f560 .cfa: x29 144 +
STACK CFI f564 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI f570 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI f580 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI f5bc x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f898 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT f928 5c0 .cfa: sp 0 + .ra: x30
STACK CFI f92c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI f938 .cfa: x29 160 +
STACK CFI f93c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI f988 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI f994 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI fcb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fcb8 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT fee8 190 .cfa: sp 0 + .ra: x30
STACK CFI feec .cfa: sp 928 +
STACK CFI fef0 .ra: .cfa -888 + ^ x29: .cfa -896 + ^
STACK CFI fefc x19: .cfa -880 + ^ x20: .cfa -872 + ^
STACK CFI ff08 x23: .cfa -848 + ^ x24: .cfa -840 + ^
STACK CFI ff1c x21: .cfa -864 + ^ x22: .cfa -856 + ^
STACK CFI ff30 x25: .cfa -832 + ^
STACK CFI 10030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 10034 .cfa: sp 928 + .ra: .cfa -888 + ^ x19: .cfa -880 + ^ x20: .cfa -872 + ^ x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x29: .cfa -896 + ^
STACK CFI INIT 10078 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1007c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 1008c x19: .cfa -272 + ^
STACK CFI 10110 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10114 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 10118 94 .cfa: sp 0 + .ra: x30
STACK CFI 1011c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10124 x21: .cfa -16 + ^
STACK CFI 10130 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10164 x19: x19 x20: x20
STACK CFI 10170 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 10174 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 101b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 101b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 101fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10200 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1020c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10210 f4 .cfa: sp 0 + .ra: x30
STACK CFI 10218 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10220 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1022c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10238 x23: .cfa -16 + ^
STACK CFI 102a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 102a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 102bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 102c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 102e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 102e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 102f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 10308 6c .cfa: sp 0 + .ra: x30
STACK CFI 10310 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10318 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1035c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10378 98 .cfa: sp 0 + .ra: x30
STACK CFI 10380 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10388 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10394 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 103dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 103e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1040c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 10410 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10438 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10470 48 .cfa: sp 0 + .ra: x30
STACK CFI 10478 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10480 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 104a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 104ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 104b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 104b8 4c .cfa: sp 0 + .ra: x30
STACK CFI 104c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 104c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 104fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10508 58 .cfa: sp 0 + .ra: x30
STACK CFI 1050c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10514 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1055c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10560 64 .cfa: sp 0 + .ra: x30
STACK CFI 10564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1056c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 105c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 105c8 31c .cfa: sp 0 + .ra: x30
STACK CFI 105cc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 105d0 .cfa: x29 112 +
STACK CFI 105d4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 105e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10604 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1088c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10890 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 108e8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 108ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 108f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10904 x21: .cfa -32 + ^
STACK CFI 10988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1098c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10990 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 10994 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1099c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 109a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 109bc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10a04 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10a78 x19: x19 x20: x20
STACK CFI 10a8c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10a90 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10aa4 x19: x19 x20: x20
STACK CFI 10ab8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10abc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10aec x19: x19 x20: x20
STACK CFI 10b94 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10b98 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10bb0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10bb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10bc4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10be0 x19: x19 x20: x20
STACK CFI 10bf8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10c38 x19: x19 x20: x20
STACK CFI 10c3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10c44 x19: x19 x20: x20
STACK CFI INIT 10c48 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 10c4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10c54 .cfa: x29 112 +
STACK CFI 10c58 x19: .cfa -96 + ^
STACK CFI 10df4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10df8 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10e18 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e50 c0 .cfa: sp 0 + .ra: x30
STACK CFI 10e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10e6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10e7c x21: .cfa -16 + ^
STACK CFI 10f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 10f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f18 108 .cfa: sp 0 + .ra: x30
STACK CFI 10f1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10f28 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10f34 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10f44 x25: .cfa -16 + ^
STACK CFI 10fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 10fcc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 11008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1100c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11020 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11030 164 .cfa: sp 0 + .ra: x30
STACK CFI 11038 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11044 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1104c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1107c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 11134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11138 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1118c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 11198 44c .cfa: sp 0 + .ra: x30
STACK CFI 111a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 111b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 111bc x21: .cfa -16 + ^
STACK CFI 111f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11200 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 11400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11404 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 115e8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11610 c4 .cfa: sp 0 + .ra: x30
STACK CFI 11618 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11620 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11628 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 116bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 116c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 116cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 116d8 5c .cfa: sp 0 + .ra: x30
STACK CFI 116e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1170c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11710 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1171c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1172c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11738 70 .cfa: sp 0 + .ra: x30
STACK CFI 11740 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11764 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11768 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 117a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 117a8 128 .cfa: sp 0 + .ra: x30
STACK CFI 117ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 117b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 117c0 x21: .cfa -16 + ^
STACK CFI 118b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 118b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 118c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 118c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
