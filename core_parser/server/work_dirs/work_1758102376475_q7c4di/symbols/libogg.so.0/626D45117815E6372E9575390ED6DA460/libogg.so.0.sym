MODULE Linux arm64 626D45117815E6372E9575390ED6DA460 libogg.so.0
INFO CODE_ID 11456D62157837E62E9575390ED6DA46164C3339
PUBLIC 1c48 0 ogg_page_version
PUBLIC 1c58 0 ogg_page_continued
PUBLIC 1c68 0 ogg_page_bos
PUBLIC 1c78 0 ogg_page_eos
PUBLIC 1c88 0 ogg_page_granulepos
PUBLIC 1cb8 0 ogg_page_serialno
PUBLIC 1cc8 0 ogg_page_pageno
PUBLIC 1cd8 0 ogg_page_packets
PUBLIC 2158 0 ogg_stream_check
PUBLIC 2178 0 ogg_stream_clear
PUBLIC 21d8 0 ogg_stream_init
PUBLIC 2328 0 ogg_stream_destroy
PUBLIC 2360 0 ogg_page_checksum_set
PUBLIC 29a8 0 ogg_stream_iovecin
PUBLIC 2c58 0 ogg_stream_packetin
PUBLIC 2c88 0 ogg_stream_flush
PUBLIC 3220 0 ogg_stream_flush_fill
PUBLIC 37c8 0 ogg_stream_pageout
PUBLIC 3848 0 ogg_stream_pageout_fill
PUBLIC 38e0 0 ogg_stream_eos
PUBLIC 3918 0 ogg_sync_init
PUBLIC 3930 0 ogg_sync_clear
PUBLIC 3970 0 ogg_sync_destroy
PUBLIC 39a8 0 ogg_sync_check
PUBLIC 39b8 0 ogg_sync_buffer
PUBLIC 3aa0 0 ogg_sync_wrote
PUBLIC 3af0 0 ogg_sync_pageseek
PUBLIC 3ca8 0 ogg_sync_pageout
PUBLIC 3d20 0 ogg_stream_pagein
PUBLIC 4188 0 ogg_sync_reset
PUBLIC 41c0 0 ogg_stream_reset
PUBLIC 4218 0 ogg_stream_reset_serialno
PUBLIC 4268 0 ogg_stream_packetout
PUBLIC 4388 0 ogg_stream_packetpeek
PUBLIC 4490 0 ogg_packet_clear
PUBLIC 44c0 0 oggpack_writeinit
PUBLIC 4508 0 oggpackB_writeinit
PUBLIC 4510 0 oggpack_writecheck
PUBLIC 4530 0 oggpackB_writecheck
PUBLIC 4538 0 oggpack_writetrunc
PUBLIC 4578 0 oggpackB_writetrunc
PUBLIC 45c0 0 oggpack_reset
PUBLIC 45e0 0 oggpackB_reset
PUBLIC 45e8 0 oggpack_writeclear
PUBLIC 4620 0 oggpack_write
PUBLIC 47a0 0 oggpack_writealign
PUBLIC 47c0 0 oggpackB_write
PUBLIC 4940 0 oggpackB_writealign
PUBLIC 4960 0 oggpackB_writecopy
PUBLIC 4ac0 0 oggpack_writecopy
PUBLIC 4c10 0 oggpackB_writeclear
PUBLIC 4c18 0 oggpack_readinit
PUBLIC 4c30 0 oggpackB_readinit
PUBLIC 4c38 0 oggpack_look
PUBLIC 4d30 0 oggpackB_look
PUBLIC 4e50 0 oggpack_look1
PUBLIC 4e80 0 oggpackB_look1
PUBLIC 4eb8 0 oggpack_adv
PUBLIC 4f20 0 oggpackB_adv
PUBLIC 4f28 0 oggpack_adv1
PUBLIC 4f60 0 oggpackB_adv1
PUBLIC 4f68 0 oggpack_read
PUBLIC 5090 0 oggpackB_read
PUBLIC 51c0 0 oggpack_read1
PUBLIC 5230 0 oggpackB_read1
PUBLIC 52a8 0 oggpack_bytes
PUBLIC 52c8 0 oggpack_bits
PUBLIC 52d8 0 oggpackB_bytes
PUBLIC 52e0 0 oggpackB_bits
PUBLIC 52e8 0 oggpack_get_buffer
PUBLIC 52f0 0 oggpackB_get_buffer
STACK CFI INIT 19f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a28 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a68 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a74 x19: .cfa -16 + ^
STACK CFI 1aac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab8 190 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c48 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c58 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c68 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c78 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c88 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cd8 47c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2158 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2178 60 .cfa: sp 0 + .ra: x30
STACK CFI 2180 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2188 x19: .cfa -16 + ^
STACK CFI 21cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21d8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 21e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 226c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2280 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 228c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2308 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2318 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2328 38 .cfa: sp 0 + .ra: x30
STACK CFI 2330 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2338 x19: .cfa -16 + ^
STACK CFI 2354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2360 84 .cfa: sp 0 + .ra: x30
STACK CFI 236c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23e8 5bc .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 23f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2400 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2418 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2444 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 289c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 29a8 2ac .cfa: sp 0 + .ra: x30
STACK CFI 29ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 29b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 29bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 29c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 29d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2a4c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2a50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b84 x27: x27 x28: x28
STACK CFI 2ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2ba4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2bfc x27: x27 x28: x28
STACK CFI 2c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c04 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2c24 x27: x27 x28: x28
STACK CFI 2c30 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c3c x27: x27 x28: x28
STACK CFI 2c40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c50 x27: x27 x28: x28
STACK CFI INIT 2c58 30 .cfa: sp 0 + .ra: x30
STACK CFI 2c60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c88 598 .cfa: sp 0 + .ra: x30
STACK CFI 2c8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ca0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2cd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 30ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3220 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 3224 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 322c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3238 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 326c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3270 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 32a0 x23: .cfa -16 + ^
STACK CFI 32fc x23: x23
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3698 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 37c8 80 .cfa: sp 0 + .ra: x30
STACK CFI 37cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3818 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3848 94 .cfa: sp 0 + .ra: x30
STACK CFI 384c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3854 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3860 x21: .cfa -16 + ^
STACK CFI 38a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 38d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 38e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38ec x19: .cfa -16 + ^
STACK CFI 3904 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3908 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3918 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3930 40 .cfa: sp 0 + .ra: x30
STACK CFI 3938 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3940 x19: .cfa -16 + ^
STACK CFI 3964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3970 38 .cfa: sp 0 + .ra: x30
STACK CFI 3978 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3980 x19: .cfa -16 + ^
STACK CFI 399c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 39a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 39b8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 39bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 39c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39cc x21: .cfa -16 + ^
STACK CFI 3a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3aa0 4c .cfa: sp 0 + .ra: x30
STACK CFI 3aa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3aac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3af0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3afc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3b08 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3b20 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3b3c x25: .cfa -64 + ^
STACK CFI 3bb4 x25: x25
STACK CFI 3bc8 x25: .cfa -64 + ^
STACK CFI 3bcc x25: x25
STACK CFI 3bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3bf8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 3c8c x25: x25
STACK CFI 3c94 x25: .cfa -64 + ^
STACK CFI 3ca0 x25: x25
STACK CFI 3ca4 x25: .cfa -64 + ^
STACK CFI INIT 3ca8 78 .cfa: sp 0 + .ra: x30
STACK CFI 3cac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d20 468 .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d2c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3d34 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3d40 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3d4c x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3fd4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4188 38 .cfa: sp 0 + .ra: x30
STACK CFI 418c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4194 x19: .cfa -16 + ^
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 41c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41cc x19: .cfa -16 + ^
STACK CFI 4208 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 420c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4218 50 .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4224 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 422c x21: .cfa -16 + ^
STACK CFI 425c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4260 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4268 120 .cfa: sp 0 + .ra: x30
STACK CFI 426c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4274 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4294 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4364 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 437c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4380 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4388 108 .cfa: sp 0 + .ra: x30
STACK CFI 438c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4464 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4488 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4490 30 .cfa: sp 0 + .ra: x30
STACK CFI 4494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 449c x19: .cfa -16 + ^
STACK CFI 44bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44cc x19: .cfa -16 + ^
STACK CFI 4500 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4508 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4530 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4538 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4578 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e8 34 .cfa: sp 0 + .ra: x30
STACK CFI 45ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45f4 x19: .cfa -16 + ^
STACK CFI 4618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4620 180 .cfa: sp 0 + .ra: x30
STACK CFI 4624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4648 x21: .cfa -16 + ^
STACK CFI 466c x21: x21
STACK CFI 4678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 467c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4774 x21: x21
STACK CFI 4778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 477c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 47a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47c0 180 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47e8 x21: .cfa -16 + ^
STACK CFI 480c x21: x21
STACK CFI 4818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 481c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4914 x21: x21
STACK CFI 4918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 491c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4940 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4960 15c .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4970 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 497c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4994 x23: .cfa -16 + ^
STACK CFI 4a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4a5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4ac0 14c .cfa: sp 0 + .ra: x30
STACK CFI 4ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4ad0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4adc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4af4 x23: .cfa -16 + ^
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4bac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4bf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4c10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c18 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c38 f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d30 11c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e80 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4eb8 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f28 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f68 124 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5090 130 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51c0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5230 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52a8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52f0 4 .cfa: sp 0 + .ra: x30
