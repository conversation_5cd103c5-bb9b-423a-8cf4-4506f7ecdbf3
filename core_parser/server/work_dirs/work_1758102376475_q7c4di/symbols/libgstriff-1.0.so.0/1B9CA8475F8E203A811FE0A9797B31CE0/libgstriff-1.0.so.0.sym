MODULE Linux arm64 1B9CA8475F8E203A811FE0A9797B31CE0 libgstriff-1.0.so.0
INFO CODE_ID 47A89C1B8E5F3A20811FE0A9797B31CE484277A9
PUBLIC 1b58 0 gst_riff_init
PUBLIC 1d10 0 gst_riff_create_video_caps
PUBLIC 4c08 0 gst_riff_create_audio_caps
PUBLIC 6b80 0 gst_riff_create_iavs_caps
PUBLIC 6cd0 0 gst_riff_create_video_template_caps
PUBLIC 6d50 0 gst_riff_create_audio_template_caps
PUBLIC 6de8 0 gst_riff_create_iavs_template_caps
PUBLIC 6e40 0 gst_riff_read_chunk
PUBLIC 7248 0 gst_riff_parse_chunk
PUBLIC 7680 0 gst_riff_parse_file_header
PUBLIC 79d0 0 gst_riff_parse_strh
PUBLIC 7f38 0 gst_riff_parse_strf_vids
PUBLIC 84e0 0 gst_riff_parse_strf_auds
PUBLIC 8958 0 gst_riff_parse_strf_iavs
PUBLIC 8d48 0 gst_riff_parse_info
STACK CFI INIT 1a98 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b08 48 .cfa: sp 0 + .ra: x30
STACK CFI 1b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b14 x19: .cfa -16 + ^
STACK CFI 1b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b58 4c .cfa: sp 0 + .ra: x30
STACK CFI 1b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b64 x19: .cfa -16 + ^
STACK CFI 1b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ba0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ba8 168 .cfa: sp 0 + .ra: x30
STACK CFI 1bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bb8 x21: .cfa -16 + ^
STACK CFI 1bc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c38 x19: x19 x20: x20
STACK CFI 1c44 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1c48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c68 x19: x19 x20: x20
STACK CFI 1c70 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c8c x19: x19 x20: x20
STACK CFI 1c90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c98 x19: x19 x20: x20
STACK CFI 1cf4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1d0c x19: x19 x20: x20
STACK CFI INIT 1d10 2ef8 .cfa: sp 0 + .ra: x30
STACK CFI 1d14 .cfa: sp 112 +
STACK CFI 1d18 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1d20 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1d30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1d3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1d44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1fc0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 426c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4c08 1f78 .cfa: sp 0 + .ra: x30
STACK CFI 4c0c .cfa: sp 304 +
STACK CFI 4c10 .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 4c18 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 4c24 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 4c34 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 4c40 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 4c48 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 4e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e2c .cfa: sp 304 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 6b80 150 .cfa: sp 0 + .ra: x30
STACK CFI 6b84 .cfa: sp 64 +
STACK CFI 6b94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c84 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6cd0 7c .cfa: sp 0 + .ra: x30
STACK CFI 6cd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ce8 x21: .cfa -16 + ^
STACK CFI 6d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6d50 98 .cfa: sp 0 + .ra: x30
STACK CFI 6d54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d68 x21: .cfa -16 + ^
STACK CFI 6de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6de8 54 .cfa: sp 0 + .ra: x30
STACK CFI 6dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6df4 x19: .cfa -16 + ^
STACK CFI 6e38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e40 408 .cfa: sp 0 + .ra: x30
STACK CFI 6e44 .cfa: sp 272 +
STACK CFI 6e4c .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 6e58 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e78 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 6e90 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 6e94 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6ea0 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 7074 x19: x19 x20: x20
STACK CFI 7078 x21: x21 x22: x22
STACK CFI 707c x23: x23 x24: x24
STACK CFI 7080 x27: x27 x28: x28
STACK CFI 70ac .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 70b0 .cfa: sp 272 + .ra: .cfa -232 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 70fc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7124 x21: x21 x22: x22
STACK CFI 7128 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7138 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 71b4 x19: x19 x20: x20
STACK CFI 71b8 x21: x21 x22: x22
STACK CFI 71bc x23: x23 x24: x24
STACK CFI 71c0 x27: x27 x28: x28
STACK CFI 71c4 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 7224 x19: x19 x20: x20
STACK CFI 7228 x21: x21 x22: x22
STACK CFI 722c x23: x23 x24: x24
STACK CFI 7230 x27: x27 x28: x28
STACK CFI 7238 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 723c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7240 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 7244 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 7248 434 .cfa: sp 0 + .ra: x30
STACK CFI 724c .cfa: sp 256 +
STACK CFI 7250 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 7258 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 727c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7290 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 729c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 72a4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 72e4 x21: x21 x22: x22
STACK CFI 72ec x23: x23 x24: x24
STACK CFI 72f0 x25: x25 x26: x26
STACK CFI 72f4 x27: x27 x28: x28
STACK CFI 7318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 731c .cfa: sp 256 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI 7360 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 74fc x21: x21 x22: x22
STACK CFI 7500 x23: x23 x24: x24
STACK CFI 7504 x25: x25 x26: x26
STACK CFI 7508 x27: x27 x28: x28
STACK CFI 750c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7530 x21: x21 x22: x22
STACK CFI 7534 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7558 x21: x21 x22: x22
STACK CFI 755c x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7668 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 766c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7670 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7674 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7678 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 7680 350 .cfa: sp 0 + .ra: x30
STACK CFI 7684 .cfa: sp 208 +
STACK CFI 7688 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7690 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 76b0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 76c0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 7730 x19: x19 x20: x20
STACK CFI 7738 x21: x21 x22: x22
STACK CFI 775c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7760 .cfa: sp 208 + .ra: .cfa -184 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 7784 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 77a8 x19: x19 x20: x20
STACK CFI 77ac x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 77b4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 78d4 x19: x19 x20: x20
STACK CFI 78d8 x21: x21 x22: x22
STACK CFI 78dc x25: x25 x26: x26
STACK CFI 78e0 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 78e8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 79c0 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 79c4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 79c8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 79cc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 79d0 564 .cfa: sp 0 + .ra: x30
STACK CFI 79d4 .cfa: sp 256 +
STACK CFI 79d8 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 79e0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7a00 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7a0c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7a98 x19: x19 x20: x20
STACK CFI 7aa0 x23: x23 x24: x24
STACK CFI 7ac4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7ac8 .cfa: sp 256 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 7b24 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7b30 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7eb0 x25: x25 x26: x26
STACK CFI 7eb4 x27: x27 x28: x28
STACK CFI 7ec0 x19: x19 x20: x20
STACK CFI 7ec4 x23: x23 x24: x24
STACK CFI 7eec x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7f10 x23: x23 x24: x24
STACK CFI 7f18 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7f1c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7f20 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7f24 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7f2c x25: x25 x26: x26
STACK CFI 7f30 x27: x27 x28: x28
STACK CFI INIT 7f38 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 7f3c .cfa: sp 240 +
STACK CFI 7f40 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 7f48 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7f54 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7f6c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 7f84 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7fcc x23: x23 x24: x24
STACK CFI 7fd4 x25: x25 x26: x26
STACK CFI 7ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8000 .cfa: sp 240 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 80b4 x27: .cfa -128 + ^
STACK CFI 83e0 x27: x27
STACK CFI 83ec x23: x23 x24: x24
STACK CFI 83f0 x25: x25 x26: x26
STACK CFI 8418 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 843c x23: x23 x24: x24
STACK CFI 8440 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8464 x23: x23 x24: x24
STACK CFI 8468 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 84c8 x27: .cfa -128 + ^
STACK CFI 84cc x27: x27
STACK CFI 84d0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 84d4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 84d8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 84dc x27: .cfa -128 + ^
STACK CFI INIT 84e0 474 .cfa: sp 0 + .ra: x30
STACK CFI 84e4 .cfa: sp 240 +
STACK CFI 84e8 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 84f0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 84fc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 8514 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8570 x25: x25 x26: x26
STACK CFI 859c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85a0 .cfa: sp 240 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 85ac x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 867c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 885c x27: x27 x28: x28
STACK CFI 887c x23: x23 x24: x24
STACK CFI 8880 x25: x25 x26: x26
STACK CFI 88a8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 88cc x25: x25 x26: x26
STACK CFI 88d0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 88f4 x25: x25 x26: x26
STACK CFI 88f8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 893c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8940 x27: x27 x28: x28
STACK CFI 8944 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8948 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 894c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8950 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 8958 3ec .cfa: sp 0 + .ra: x30
STACK CFI 895c .cfa: sp 224 +
STACK CFI 8960 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8968 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8988 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 899c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 89e8 x19: x19 x20: x20
STACK CFI 89f0 x23: x23 x24: x24
STACK CFI 8a14 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8a18 .cfa: sp 224 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI 8a24 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 8a64 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 8c54 x27: x27 x28: x28
STACK CFI 8c64 x19: x19 x20: x20
STACK CFI 8c68 x23: x23 x24: x24
STACK CFI 8c6c x25: x25 x26: x26
STACK CFI 8c94 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8cb8 x23: x23 x24: x24
STACK CFI 8cbc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8ce0 x23: x23 x24: x24
STACK CFI 8ce4 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8d28 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 8d2c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 8d30 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8d34 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 8d38 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 8d40 x27: x27 x28: x28
STACK CFI INIT 8d48 828 .cfa: sp 0 + .ra: x30
STACK CFI 8d4c .cfa: sp 320 +
STACK CFI 8d54 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 8d60 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 8d94 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 8d98 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 8da0 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 8dcc x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 9128 x25: x25 x26: x26
STACK CFI 91a0 x19: x19 x20: x20
STACK CFI 91a4 x21: x21 x22: x22
STACK CFI 91a8 x27: x27 x28: x28
STACK CFI 91d0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 91d4 .cfa: sp 320 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 9504 x25: x25 x26: x26
STACK CFI 9518 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 9530 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9560 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 9564 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 9568 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 956c x27: .cfa -208 + ^ x28: .cfa -200 + ^
