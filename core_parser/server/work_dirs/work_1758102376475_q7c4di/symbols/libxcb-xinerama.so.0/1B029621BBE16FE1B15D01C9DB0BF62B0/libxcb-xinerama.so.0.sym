MODULE Linux arm64 1B029621BBE16FE1B15D01C9DB0BF62B0 libxcb-xinerama.so.0
INFO CODE_ID 2196021BE1BBE16FB15D01C9DB0BF62BF8862020
PUBLIC e98 0 xcb_xinerama_screen_info_next
PUBLIC eb8 0 xcb_xinerama_screen_info_end
PUBLIC ed0 0 xcb_xinerama_query_version
PUBLIC f40 0 xcb_xinerama_query_version_unchecked
PUBLIC fb8 0 xcb_xinerama_query_version_reply
PUBLIC fc0 0 xcb_xinerama_get_state
PUBLIC 1030 0 xcb_xinerama_get_state_unchecked
PUBLIC 1098 0 xcb_xinerama_get_state_reply
PUBLIC 10a0 0 xcb_xinerama_get_screen_count
PUBLIC 1110 0 xcb_xinerama_get_screen_count_unchecked
PUBLIC 1178 0 xcb_xinerama_get_screen_count_reply
PUBLIC 1180 0 xcb_xinerama_get_screen_size
PUBLIC 11f0 0 xcb_xinerama_get_screen_size_unchecked
PUBLIC 1260 0 xcb_xinerama_get_screen_size_reply
PUBLIC 1268 0 xcb_xinerama_is_active
PUBLIC 12d0 0 xcb_xinerama_is_active_unchecked
PUBLIC 1338 0 xcb_xinerama_is_active_reply
PUBLIC 1340 0 xcb_xinerama_query_screens_sizeof
PUBLIC 1350 0 xcb_xinerama_query_screens
PUBLIC 13b8 0 xcb_xinerama_query_screens_unchecked
PUBLIC 1420 0 xcb_xinerama_query_screens_screen_info
PUBLIC 1428 0 xcb_xinerama_query_screens_screen_info_length
PUBLIC 1430 0 xcb_xinerama_query_screens_screen_info_iterator
PUBLIC 1450 0 xcb_xinerama_query_screens_reply
STACK CFI INIT dd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT e48 48 .cfa: sp 0 + .ra: x30
STACK CFI e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e54 x19: .cfa -16 + ^
STACK CFI e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e98 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT ed0 70 .cfa: sp 0 + .ra: x30
STACK CFI ed4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ee4 x19: .cfa -96 + ^
STACK CFI f38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f3c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT f40 74 .cfa: sp 0 + .ra: x30
STACK CFI f44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f54 x19: .cfa -96 + ^
STACK CFI fac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fb0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT fb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc0 6c .cfa: sp 0 + .ra: x30
STACK CFI fc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI fd4 x19: .cfa -96 + ^
STACK CFI 1024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1028 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1030 68 .cfa: sp 0 + .ra: x30
STACK CFI 1034 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1044 x19: .cfa -96 + ^
STACK CFI 1090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1094 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1098 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 10a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10b4 x19: .cfa -96 + ^
STACK CFI 1104 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1108 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1110 68 .cfa: sp 0 + .ra: x30
STACK CFI 1114 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1124 x19: .cfa -96 + ^
STACK CFI 1170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1174 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1178 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1180 70 .cfa: sp 0 + .ra: x30
STACK CFI 1184 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1194 x19: .cfa -112 + ^
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11ec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 11f0 6c .cfa: sp 0 + .ra: x30
STACK CFI 11f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1204 x19: .cfa -112 + ^
STACK CFI 1254 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1258 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1260 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1268 68 .cfa: sp 0 + .ra: x30
STACK CFI 126c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 127c x19: .cfa -96 + ^
STACK CFI 12c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 12d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 12e4 x19: .cfa -96 + ^
STACK CFI 132c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1330 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1338 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1340 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1350 68 .cfa: sp 0 + .ra: x30
STACK CFI 1354 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1364 x19: .cfa -96 + ^
STACK CFI 13b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 13b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 13b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 13bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 13cc x19: .cfa -96 + ^
STACK CFI 1414 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1418 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1428 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1430 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1450 4 .cfa: sp 0 + .ra: x30
