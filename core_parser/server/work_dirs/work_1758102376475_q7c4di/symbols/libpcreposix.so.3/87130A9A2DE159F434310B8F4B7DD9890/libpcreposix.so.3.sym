MODULE Linux arm64 87130A9A2DE159F434310B8F4B7DD9890 libpcreposix.so.3
INFO CODE_ID 9A0A1387E12DF45934310B8F4B7DD98908A5FE2B
PUBLIC b58 0 pcreposix_regerror
PUBLIC c58 0 pcreposix_regfree
PUBLIC c70 0 pcreposix_regcomp
PUBLIC d68 0 pcreposix_regexec
STACK CFI INIT a98 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT b08 48 .cfa: sp 0 + .ra: x30
STACK CFI b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b14 x19: .cfa -16 + ^
STACK CFI b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b58 100 .cfa: sp 0 + .ra: x30
STACK CFI b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b7c x23: .cfa -16 + ^
STACK CFI be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI bec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT c58 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c70 f4 .cfa: sp 0 + .ra: x30
STACK CFI c74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT d68 284 .cfa: sp 0 + .ra: x30
STACK CFI d6c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI d74 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI d80 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI d94 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI da4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI dbc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f58 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
