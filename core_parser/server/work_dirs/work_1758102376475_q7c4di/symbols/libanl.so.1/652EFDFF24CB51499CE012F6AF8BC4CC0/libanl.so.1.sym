MODULE Linux arm64 652EFDFF24CB51499CE012F6AF8BC4CC0 libanl.so.1
INFO CODE_ID FFFD2E65CB2449519CE012F6AF8BC4CC349ED491
PUBLIC 1098 0 gai_cancel
PUBLIC 10e8 0 gai_error
PUBLIC 19a0 0 gai_suspend
PUBLIC 1ce8 0 getaddrinfo_a
STACK CFI INIT fd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1008 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1048 48 .cfa: sp 0 + .ra: x30
STACK CFI 104c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1054 x19: .cfa -16 + ^
STACK CFI 108c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1090 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1098 50 .cfa: sp 0 + .ra: x30
STACK CFI 109c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f0 278 .cfa: sp 0 + .ra: x30
STACK CFI 10f4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1104 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1118 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1124 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 112c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1138 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 2108 54 .cfa: sp 0 + .ra: x30
STACK CFI 210c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2124 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1368 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1398 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1410 338 .cfa: sp 0 + .ra: x30
STACK CFI 1414 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 141c x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 142c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 1440 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 1450 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 14ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 14f0 .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x29: .cfa -432 + ^
STACK CFI 14f8 x27: .cfa -352 + ^
STACK CFI 15a0 x27: x27
STACK CFI 15b4 x27: .cfa -352 + ^
STACK CFI 162c x27: x27
STACK CFI 1650 x27: .cfa -352 + ^
STACK CFI 16a4 x27: x27
STACK CFI 16b8 x27: .cfa -352 + ^
STACK CFI 16dc x27: x27
STACK CFI 16e4 x27: .cfa -352 + ^
STACK CFI 16f0 x27: x27
STACK CFI 16f4 x27: .cfa -352 + ^
STACK CFI INIT 1748 a0 .cfa: sp 0 + .ra: x30
STACK CFI 174c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1754 x21: .cfa -160 + ^
STACK CFI 175c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 17c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17cc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 17e8 110 .cfa: sp 0 + .ra: x30
STACK CFI 17ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 17f8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1848 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 1860 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 18ac x21: x21 x22: x22
STACK CFI 18b0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 18c0 x21: x21 x22: x22
STACK CFI 18c4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 18e4 x21: x21 x22: x22
STACK CFI 18e8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 18f4 x21: x21 x22: x22
STACK CFI INIT 18f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 18fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1904 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 194c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19a0 348 .cfa: sp 0 + .ra: x30
STACK CFI 19a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 19ac .cfa: x29 128 +
STACK CFI 19b0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19bc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 19d4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 19e0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1bf0 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1ce8 41c .cfa: sp 0 + .ra: x30
STACK CFI 1cec .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1cf0 .cfa: x29 192 +
STACK CFI 1cf4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 1d00 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 1d24 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 200c .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
