MODULE Linux arm64 8438A4142EBBFF50E647252092C4885A0 libunwind.so.8
INFO CODE_ID 14A43884BB2E50FFE647252092C4885ADB8A0A2C
PUBLIC 2000 0 _Uaarch64_get_elf_image
PUBLIC 2750 0 _Uaarch64_flush_cache
PUBLIC 2b70 0 _Uaarch64_strerror
PUBLIC 2c60 0 _Uaarch64_is_fpreg
PUBLIC 2c70 0 _Uaarch64_regname
PUBLIC 2ca8 0 backtrace
PUBLIC 2e30 0 _U_dyn_cancel
PUBLIC 2eb8 0 _U_dyn_info_list_addr
PUBLIC 2ec8 0 _U_dyn_register
PUBLIC 3060 0 _Uaarch64_get_accessors
PUBLIC 30a8 0 _ULaarch64_get_proc_info_by_ip
PUBLIC 3138 0 _ULaarch64_get_proc_name
PUBLIC 32f0 0 _ULaarch64_destroy_addr_space
PUBLIC 32f8 0 _ULaarch64_get_reg
PUBLIC 3318 0 _ULaarch64_set_reg
PUBLIC 3338 0 _ULaarch64_get_fpreg
PUBLIC 3340 0 _ULaarch64_set_fpreg
PUBLIC 3360 0 _ULaarch64_set_caching_policy
PUBLIC 33c8 0 _ULaarch64_create_addr_space
PUBLIC 33d0 0 _ULaarch64_get_proc_info
PUBLIC 3420 0 _ULaarch64_get_save_loc
PUBLIC 3798 0 _ULaarch64_init_local
PUBLIC 3d70 0 _ULaarch64_init_remote
PUBLIC 3d78 0 _ULaarch64_is_signal_frame
PUBLIC 4078 0 _ULaarch64_resume
PUBLIC 4300 0 _ULaarch64_handle_signal_frame
PUBLIC 4470 0 _ULaarch64_step
PUBLIC 7e80 0 _ULaarch64_dwarf_find_debug_frame
PUBLIC 8518 0 _ULaarch64_dwarf_search_unwind_table
PUBLIC 8870 0 _ULaarch64_dwarf_find_unwind_table
STACK CFI INIT 2000 744 .cfa: sp 0 + .ra: x30
STACK CFI 2004 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 201c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 203c x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 2064 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 2104 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 213c x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2618 x21: x21 x22: x22
STACK CFI 261c x27: x27 x28: x28
STACK CFI 264c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2650 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 269c x21: x21 x22: x22
STACK CFI 26a0 x27: x27 x28: x28
STACK CFI 26a4 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 26fc x21: x21 x22: x22
STACK CFI 2700 x27: x27 x28: x28
STACK CFI 270c x21: .cfa -256 + ^ x22: .cfa -248 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2724 x27: x27 x28: x28
STACK CFI 2734 x21: x21 x22: x22
STACK CFI 273c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 2740 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 2748 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2750 70 .cfa: sp 0 + .ra: x30
STACK CFI 2754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 275c x21: .cfa -16 + ^
STACK CFI 2764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 27c0 114 .cfa: sp 0 + .ra: x30
STACK CFI 27c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2848 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2858 x21: .cfa -16 + ^
STACK CFI 2898 x21: x21
STACK CFI 289c x21: .cfa -16 + ^
STACK CFI 28d0 x21: x21
STACK CFI INIT 28d8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2910 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2914 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 291c x23: .cfa -16 + ^
STACK CFI 2924 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2930 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 29d8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 29dc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 29e8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 29f8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a9c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2ab0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2ab4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2ac0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2ad0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b6c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2b70 f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c70 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ca8 184 .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 65536 +
STACK CFI 2cb8 .cfa: sp 70208 +
STACK CFI 2cbc .ra: .cfa -70200 + ^ x29: .cfa -70208 + ^
STACK CFI 2cc4 x19: .cfa -70192 + ^ x20: .cfa -70184 + ^
STACK CFI 2cd8 x21: .cfa -70176 + ^ x22: .cfa -70168 + ^
STACK CFI 2ce0 x23: .cfa -70160 + ^ x24: .cfa -70152 + ^
STACK CFI 2d5c .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d60 .cfa: sp 69632 +
STACK CFI 2d64 .cfa: sp 0 +
STACK CFI 2d68 .cfa: sp 70208 + .ra: .cfa -70200 + ^ x19: .cfa -70192 + ^ x20: .cfa -70184 + ^ x21: .cfa -70176 + ^ x22: .cfa -70168 + ^ x23: .cfa -70160 + ^ x24: .cfa -70152 + ^ x29: .cfa -70208 + ^
STACK CFI 2ddc x25: .cfa -70144 + ^
STACK CFI 2e20 x25: x25
STACK CFI 2e28 x25: .cfa -70144 + ^
STACK CFI INIT 2e30 84 .cfa: sp 0 + .ra: x30
STACK CFI 2e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e44 x19: .cfa -16 + ^
STACK CFI 2ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2eac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2eb8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ec8 80 .cfa: sp 0 + .ra: x30
STACK CFI 2ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2edc x19: .cfa -16 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f48 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2fbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2fcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2fe4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ff0 x23: .cfa -16 + ^
STACK CFI 3024 x21: x21 x22: x22
STACK CFI 3028 x23: x23
STACK CFI 3034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3038 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3054 x21: x21 x22: x22
STACK CFI 3058 x23: x23
STACK CFI 305c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3060 44 .cfa: sp 0 + .ra: x30
STACK CFI 3064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3074 x19: .cfa -16 + ^
STACK CFI 308c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3090 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30a8 90 .cfa: sp 0 + .ra: x30
STACK CFI 30ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 30b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30cc x23: .cfa -16 + ^
STACK CFI 3104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3138 19c .cfa: sp 0 + .ra: x30
STACK CFI 313c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3144 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3150 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3158 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3168 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3170 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 3248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 324c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 32d8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3318 20 .cfa: sp 0 + .ra: x30
STACK CFI 331c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3334 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3338 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3340 20 .cfa: sp 0 + .ra: x30
STACK CFI 3344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 335c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3360 64 .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3374 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 33b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 33c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33d0 50 .cfa: sp 0 + .ra: x30
STACK CFI 33d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 341c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3420 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3460 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3464 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 346c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 348c x21: .cfa -160 + ^
STACK CFI 352c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3530 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3538 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3540 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3558 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3580 58 .cfa: sp 0 + .ra: x30
STACK CFI 3584 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 358c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3598 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35a4 x23: .cfa -16 + ^
STACK CFI 35d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 35d8 84 .cfa: sp 0 + .ra: x30
STACK CFI 35dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 35f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3638 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3660 80 .cfa: sp 0 + .ra: x30
STACK CFI 3664 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 367c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 36c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 36e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36f8 9c .cfa: sp 0 + .ra: x30
STACK CFI 36fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 370c x19: .cfa -16 + ^
STACK CFI 3790 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3798 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 379c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d78 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3d7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3da0 x21: .cfa -32 + ^
STACK CFI 3e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e18 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ef8 17c .cfa: sp 0 + .ra: x30
STACK CFI 3f00 .cfa: sp 128 +
STACK CFI INIT 4078 12c .cfa: sp 0 + .ra: x30
STACK CFI 407c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4084 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4090 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 40b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 40c0 x25: .cfa -64 + ^
STACK CFI 4164 x23: x23 x24: x24
STACK CFI 4168 x25: x25
STACK CFI 418c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4190 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 419c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 41a0 x25: .cfa -64 + ^
STACK CFI INIT 41a8 154 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4300 16c .cfa: sp 0 + .ra: x30
STACK CFI 4304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 430c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 445c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4470 5c .cfa: sp 0 + .ra: x30
STACK CFI 4474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 447c x19: .cfa -16 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 44c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 44d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 44d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44e4 x19: .cfa -16 + ^
STACK CFI 4514 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4518 90 .cfa: sp 0 + .ra: x30
STACK CFI 451c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 452c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45a8 ac .cfa: sp 0 + .ra: x30
STACK CFI 45ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45c8 x21: .cfa -16 + ^
STACK CFI 45d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 463c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4640 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4658 dc .cfa: sp 0 + .ra: x30
STACK CFI 465c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 466c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4678 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 470c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4710 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4738 90 .cfa: sp 0 + .ra: x30
STACK CFI 473c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4744 x19: .cfa -16 + ^
STACK CFI 47a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c8 540 .cfa: sp 0 + .ra: x30
STACK CFI 47dc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 47e4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 47f0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4804 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4818 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 488c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4908 x21: x21 x22: x22
STACK CFI 490c x27: x27 x28: x28
STACK CFI 4914 x25: x25 x26: x26
STACK CFI 4920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4924 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4934 x21: x21 x22: x22
STACK CFI 493c x27: x27 x28: x28
STACK CFI 4950 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4b54 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4bd0 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4bd8 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4c00 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 4d08 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d20 ad8 .cfa: sp 0 + .ra: x30
STACK CFI 4d24 .cfa: sp 656 +
STACK CFI 4d28 .ra: .cfa -648 + ^ x29: .cfa -656 + ^
STACK CFI 4d30 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 4d3c x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 4d48 x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 4d54 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 4d9c x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 4ddc x25: x25 x26: x26
STACK CFI 4e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4e14 .cfa: sp 656 + .ra: .cfa -648 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^ x29: .cfa -656 + ^
STACK CFI 4e64 x25: x25 x26: x26
STACK CFI 4e78 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 4ff8 x25: x25 x26: x26
STACK CFI 5010 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 5114 x25: x25 x26: x26
STACK CFI 511c x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 57ec x25: x25 x26: x26
STACK CFI 57f4 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI INIT 57f8 55c .cfa: sp 0 + .ra: x30
STACK CFI 57fc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 5804 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 583c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5848 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 5858 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 58f8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5a3c x27: x27 x28: x28
STACK CFI 5aa0 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5b94 x27: x27 x28: x28
STACK CFI 5b9c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5ba0 x27: x27 x28: x28
STACK CFI 5bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5bd0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 5c04 x27: x27 x28: x28
STACK CFI 5c44 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5c88 x27: x27 x28: x28
STACK CFI 5c98 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5ca0 x27: x27 x28: x28
STACK CFI 5ca4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5d2c x27: x27 x28: x28
STACK CFI 5d44 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5d50 x27: x27 x28: x28
STACK CFI INIT 5d58 f8 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e50 8e0 .cfa: sp 0 + .ra: x30
STACK CFI 5e54 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5e5c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5e68 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5e9c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5ef0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5f70 x25: x25 x26: x26
STACK CFI 5fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5fa8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 671c x25: x25 x26: x26
STACK CFI 6720 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6728 x25: x25 x26: x26
STACK CFI 672c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT 6730 120 .cfa: sp 0 + .ra: x30
STACK CFI 6734 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 673c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 674c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6768 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 684c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6850 328 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 685c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6864 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 686c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6884 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 68d0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 69fc x21: x21 x22: x22
STACK CFI 6a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6a30 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 6ab0 x21: x21 x22: x22
STACK CFI 6b00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6b24 x21: x21 x22: x22
STACK CFI 6b38 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6b60 x21: x21 x22: x22
STACK CFI 6b64 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6b6c x21: x21 x22: x22
STACK CFI 6b74 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 6b78 524 .cfa: sp 0 + .ra: x30
STACK CFI 6b80 .cfa: sp 5088 +
STACK CFI 6b84 .ra: .cfa -5080 + ^ x29: .cfa -5088 + ^
STACK CFI 6b8c x19: .cfa -5072 + ^ x20: .cfa -5064 + ^
STACK CFI 6b94 x23: .cfa -5040 + ^ x24: .cfa -5032 + ^
STACK CFI 6b9c x21: .cfa -5056 + ^ x22: .cfa -5048 + ^
STACK CFI 6dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6df0 .cfa: sp 5088 + .ra: .cfa -5080 + ^ x19: .cfa -5072 + ^ x20: .cfa -5064 + ^ x21: .cfa -5056 + ^ x22: .cfa -5048 + ^ x23: .cfa -5040 + ^ x24: .cfa -5032 + ^ x29: .cfa -5088 + ^
STACK CFI 6e88 x25: .cfa -5024 + ^
STACK CFI 6fd8 x25: x25
STACK CFI 6fdc x25: .cfa -5024 + ^
STACK CFI 6fe0 x25: x25
STACK CFI 703c x25: .cfa -5024 + ^
STACK CFI 704c x25: x25
STACK CFI 7068 x25: .cfa -5024 + ^
STACK CFI 7078 x25: x25
STACK CFI 707c x25: .cfa -5024 + ^
STACK CFI 7094 x25: x25
STACK CFI 7098 x25: .cfa -5024 + ^
STACK CFI INIT 70a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 70b8 1c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7280 34 .cfa: sp 0 + .ra: x30
STACK CFI 7284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 728c x19: .cfa -16 + ^
STACK CFI 72b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72b8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 72d8 498 .cfa: sp 0 + .ra: x30
STACK CFI 72dc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 72e4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 72ec x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 72fc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7344 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7348 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7388 x25: x25 x26: x26
STACK CFI 7390 x27: x27 x28: x28
STACK CFI 73bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 73c0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 75b4 x25: x25 x26: x26
STACK CFI 75b8 x27: x27 x28: x28
STACK CFI 75bc x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 75cc x25: x25 x26: x26
STACK CFI 75d0 x27: x27 x28: x28
STACK CFI 75d4 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7764 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7768 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 776c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 7770 710 .cfa: sp 0 + .ra: x30
STACK CFI 7778 .cfa: sp 4272 +
STACK CFI 777c .ra: .cfa -4264 + ^ x29: .cfa -4272 + ^
STACK CFI 778c x23: .cfa -4224 + ^ x24: .cfa -4216 + ^
STACK CFI 779c x19: .cfa -4256 + ^ x20: .cfa -4248 + ^
STACK CFI 77ec x21: .cfa -4240 + ^ x22: .cfa -4232 + ^
STACK CFI 77f0 x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI 77f8 x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI 79f4 x21: x21 x22: x22
STACK CFI 79f8 x25: x25 x26: x26
STACK CFI 79fc x27: x27 x28: x28
STACK CFI 7a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 7a8c .cfa: sp 4272 + .ra: .cfa -4264 + ^ x19: .cfa -4256 + ^ x20: .cfa -4248 + ^ x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x23: .cfa -4224 + ^ x24: .cfa -4216 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^ x29: .cfa -4272 + ^
STACK CFI 7cdc x21: x21 x22: x22
STACK CFI 7ce0 x25: x25 x26: x26
STACK CFI 7ce4 x27: x27 x28: x28
STACK CFI 7cec x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI 7e24 x21: x21 x22: x22
STACK CFI 7e28 x25: x25 x26: x26
STACK CFI 7e2c x27: x27 x28: x28
STACK CFI 7e30 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI 7e50 x21: x21 x22: x22
STACK CFI 7e54 x25: x25 x26: x26
STACK CFI 7e58 x27: x27 x28: x28
STACK CFI 7e5c x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI 7e70 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7e74 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^
STACK CFI 7e78 x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI 7e7c x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI INIT 7e80 24c .cfa: sp 0 + .ra: x30
STACK CFI 7e84 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 7e8c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7e9c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 7ea8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 7ec4 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 7ed0 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 7f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7f4c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 80d0 444 .cfa: sp 0 + .ra: x30
STACK CFI 80d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 80e0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 80f0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 8104 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8144 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 81c0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8374 x27: x27 x28: x28
STACK CFI 8380 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 83a4 x25: x25 x26: x26
STACK CFI 83a8 x27: x27 x28: x28
STACK CFI 83d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 83d8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 8440 x25: x25 x26: x26
STACK CFI 8444 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8458 x25: x25 x26: x26
STACK CFI 845c x27: x27 x28: x28
STACK CFI 8460 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 8464 x25: x25 x26: x26
STACK CFI 8468 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8480 x27: x27 x28: x28
STACK CFI 8484 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 84c0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 84c8 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 8504 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8508 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 850c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 8518 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 851c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8524 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8530 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8554 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8560 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 86a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 86ac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 86f0 174 .cfa: sp 0 + .ra: x30
STACK CFI 86f4 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 8700 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 870c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 8718 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 8728 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 8824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8828 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x29: .cfa -400 + ^
STACK CFI INIT 8868 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8870 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 8874 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 887c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8888 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 88e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 88e8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 88f8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 88fc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8900 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8910 x23: x23 x24: x24
STACK CFI 8914 x25: x25 x26: x26
STACK CFI 8918 x27: x27 x28: x28
STACK CFI 891c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8af0 x23: x23 x24: x24
STACK CFI 8af4 x25: x25 x26: x26
STACK CFI 8af8 x27: x27 x28: x28
STACK CFI 8afc x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8b3c x23: x23 x24: x24
STACK CFI 8b40 x25: x25 x26: x26
STACK CFI 8b44 x27: x27 x28: x28
STACK CFI 8b48 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8b50 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8b54 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 8b58 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8b5c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 8b68 3c .cfa: sp 0 + .ra: x30
STACK CFI 8b6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ba8 208 .cfa: sp 0 + .ra: x30
STACK CFI 8bb8 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8bc8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8bd8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 8be0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8c0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8c18 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8c1c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8c20 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8c30 x19: x19 x20: x20
STACK CFI 8c38 x23: x23 x24: x24
STACK CFI 8c44 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8c48 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8db0 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 8db4 .cfa: sp 272 +
STACK CFI 8dbc .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 8dc4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 8de0 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 8e04 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 8ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8ef4 .cfa: sp 272 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 8f04 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 8f14 x23: x23 x24: x24
STACK CFI 8f18 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9070 x23: x23 x24: x24
STACK CFI 9074 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 907c x23: x23 x24: x24
STACK CFI 9088 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9154 x23: x23 x24: x24
STACK CFI 9158 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9168 x23: x23 x24: x24
STACK CFI 9170 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT 9178 c8 .cfa: sp 0 + .ra: x30
STACK CFI 917c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9184 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9190 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 91a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 91c0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 923c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
