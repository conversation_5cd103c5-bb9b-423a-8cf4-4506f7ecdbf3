MODULE Linux arm64 C4656DC6BBD638E2276286F4A953D8480 libulockmgr.so.1
INFO CODE_ID C66D65C4D6BBE238276286F4A953D848317BCED9
PUBLIC f58 0 ulockmgr_op
STACK CFI INIT d38 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d68 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT da8 48 .cfa: sp 0 + .ra: x30
STACK CFI dac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI db4 x19: .cfa -16 + ^
STACK CFI dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT df0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT df8 114 .cfa: sp 0 + .ra: x30
STACK CFI dfc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI e0c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI e20 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI e3c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ecc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT f10 44 .cfa: sp 0 + .ra: x30
STACK CFI f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT f58 8e8 .cfa: sp 0 + .ra: x30
STACK CFI f5c .cfa: sp 800 +
STACK CFI f60 .ra: .cfa -792 + ^ x29: .cfa -800 + ^
STACK CFI f68 x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI f70 x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI f90 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 1024 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 1028 x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 1240 x25: x25 x26: x26
STACK CFI 1248 x27: x27 x28: x28
STACK CFI 1284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1288 .cfa: sp 800 + .ra: .cfa -792 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x29: .cfa -800 + ^
STACK CFI 129c x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 12a0 x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 1310 x25: x25 x26: x26
STACK CFI 1314 x27: x27 x28: x28
STACK CFI 1340 x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 148c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14ac x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 15a4 x25: x25 x26: x26
STACK CFI 15a8 x27: x27 x28: x28
STACK CFI 15ac x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 1604 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 160c x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 16ec x25: x25 x26: x26
STACK CFI 16f0 x27: x27 x28: x28
STACK CFI 16f4 x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 17d8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17dc x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 17e0 x27: .cfa -720 + ^ x28: .cfa -712 + ^
