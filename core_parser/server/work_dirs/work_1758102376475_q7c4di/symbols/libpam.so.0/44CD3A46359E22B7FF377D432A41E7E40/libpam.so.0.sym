MODULE Linux arm64 44CD3A46359E22B7FF377D432A41E7E40 libpam.so.0
INFO CODE_ID 463ACD449E35B722FF377D432A41E7E440E76513
PUBLIC 27b8 0 pam_acct_mgmt
PUBLIC 2800 0 pam_authenticate
PUBLIC 28c0 0 pam_setcred
PUBLIC 2978 0 pam_set_data
PUBLIC 2ab0 0 pam_get_data
PUBLIC 2d30 0 pam_fail_delay
PUBLIC 33f8 0 pam_end
PUBLIC 39c0 0 pam_putenv
PUBLIC 3d40 0 pam_getenv
PUBLIC 3e38 0 pam_getenvlist
PUBLIC 4648 0 pam_get_authtok
PUBLIC 4650 0 pam_get_authtok_noverify
PUBLIC 4668 0 pam_get_authtok_verify
PUBLIC 6058 0 pam_set_item
PUBLIC 65c0 0 pam_get_item
PUBLIC 6778 0 pam_get_user
PUBLIC 70c0 0 pam_chauthtok
PUBLIC 71e8 0 pam_open_session
PUBLIC 7230 0 pam_close_session
PUBLIC 7278 0 pam_start
PUBLIC 7598 0 pam_strerror
PUBLIC 78d0 0 pam_vprompt
PUBLIC 7b18 0 pam_prompt
PUBLIC 7bb8 0 pam_vsyslog
PUBLIC 7da0 0 pam_syslog
PUBLIC 8380 0 pam_modutil_audit_write
PUBLIC 8430 0 pam_modutil_getpwnam
PUBLIC 85f0 0 pam_modutil_read
PUBLIC 86d0 0 pam_modutil_write
PUBLIC 87b0 0 pam_modutil_getgrgid
PUBLIC 89a8 0 pam_modutil_getpwuid
PUBLIC 8ba0 0 pam_modutil_getgrnam
PUBLIC 8d60 0 pam_modutil_getspnam
PUBLIC 8f20 0 pam_modutil_getlogin
PUBLIC 91d8 0 pam_modutil_user_in_group_nam_nam
PUBLIC 9240 0 pam_modutil_user_in_group_nam_gid
PUBLIC 92a8 0 pam_modutil_user_in_group_uid_nam
PUBLIC 9310 0 pam_modutil_user_in_group_uid_gid
PUBLIC 9378 0 pam_modutil_drop_priv
PUBLIC 95f8 0 pam_modutil_regain_priv
PUBLIC 9908 0 pam_modutil_sanitize_helper_fds
STACK CFI INIT 26f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2728 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2768 48 .cfa: sp 0 + .ra: x30
STACK CFI 276c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2774 x19: .cfa -16 + ^
STACK CFI 27ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 27dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2800 bc .cfa: sp 0 + .ra: x30
STACK CFI 2804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 280c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 284c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2850 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2874 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 28c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 28ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 290c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2910 64 .cfa: sp 0 + .ra: x30
STACK CFI 2914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 291c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2978 138 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2988 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2994 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29b0 x23: .cfa -16 + ^
STACK CFI 29e4 x19: x19 x20: x20
STACK CFI 29e8 x21: x21 x22: x22
STACK CFI 29ec x23: x23
STACK CFI 29f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 29f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2a00 x19: x19 x20: x20
STACK CFI 2a04 x21: x21 x22: x22
STACK CFI 2a08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2a3c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2a58 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2a7c x19: x19 x20: x20
STACK CFI 2a80 x21: x21 x22: x22
STACK CFI 2a84 x23: x23
STACK CFI 2a88 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2aa4 x19: x19 x20: x20
STACK CFI 2aa8 x21: x21 x22: x22
STACK CFI 2aac x23: x23
STACK CFI INIT 2ab0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ac8 x19: .cfa -16 + ^
STACK CFI 2af0 x19: x19
STACK CFI 2afc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2b08 x19: x19
STACK CFI 2b0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b10 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2b30 84 .cfa: sp 0 + .ra: x30
STACK CFI 2b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2bb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bc0 28 .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bcc x19: .cfa -16 + ^
STACK CFI 2be4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2be8 144 .cfa: sp 0 + .ra: x30
STACK CFI 2bec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bfc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ce8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d30 68 .cfa: sp 0 + .ra: x30
STACK CFI 2d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d98 660 .cfa: sp 0 + .ra: x30
STACK CFI 2d9c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2da4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2db0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2dcc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2dd8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2df0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2fb8 x25: x25 x26: x26
STACK CFI 2fdc x19: x19 x20: x20
STACK CFI 2fe0 x27: x27 x28: x28
STACK CFI 3004 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3008 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 3040 x25: x25 x26: x26
STACK CFI 305c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 30d8 x25: x25 x26: x26
STACK CFI 30e0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 31f8 x19: x19 x20: x20
STACK CFI 31fc x25: x25 x26: x26
STACK CFI 3200 x27: x27 x28: x28
STACK CFI 3204 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3238 x25: x25 x26: x26
STACK CFI 3240 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 32a4 x25: x25 x26: x26
STACK CFI 32a8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 32dc x25: x25 x26: x26
STACK CFI 32e0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 32f8 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3314 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 33b8 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 33c0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 33c4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 33f4 x25: x25 x26: x26
STACK CFI INIT 33f8 348 .cfa: sp 0 + .ra: x30
STACK CFI 33fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3404 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3420 x21: .cfa -16 + ^
STACK CFI 3448 x21: x21
STACK CFI 3454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3458 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 371c x21: x21
STACK CFI 3720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3724 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3740 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3744 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3750 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3760 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 376c x25: .cfa -16 + ^
STACK CFI 377c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37b8 x19: x19 x20: x20
STACK CFI 37c0 x21: x21 x22: x22
STACK CFI 37c4 x25: x25
STACK CFI 37d0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 37d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 37dc x19: x19 x20: x20
STACK CFI 37e0 x21: x21 x22: x22
STACK CFI 37e8 x25: x25
STACK CFI 37ec .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 37f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 37f8 dc .cfa: sp 0 + .ra: x30
STACK CFI 37fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3808 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 384c x19: x19 x20: x20
STACK CFI 3854 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3858 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3874 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3878 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3894 x19: x19 x20: x20
STACK CFI 3898 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38c4 x19: x19 x20: x20
STACK CFI 38c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38d0 x19: x19 x20: x20
STACK CFI INIT 38d8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 38e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38ec x21: .cfa -16 + ^
STACK CFI 390c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 397c x19: x19 x20: x20
STACK CFI 39a8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 39c0 37c .cfa: sp 0 + .ra: x30
STACK CFI 39c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 39cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 39d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 39e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39fc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3ad0 x19: x19 x20: x20
STACK CFI 3ad8 x23: x23 x24: x24
STACK CFI 3adc x25: x25 x26: x26
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3ae4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3b6c x19: x19 x20: x20
STACK CFI 3b70 x23: x23 x24: x24
STACK CFI 3b74 x25: x25 x26: x26
STACK CFI 3b80 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3b84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3b8c x25: x25 x26: x26
STACK CFI 3ba8 x19: x19 x20: x20
STACK CFI 3bb0 x23: x23 x24: x24
STACK CFI 3bb4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3bb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3c2c x19: x19 x20: x20
STACK CFI 3c34 x23: x23 x24: x24
STACK CFI 3c38 x25: x25 x26: x26
STACK CFI 3c3c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3c40 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3c4c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3c68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3c80 x19: x19 x20: x20
STACK CFI 3c84 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3ca4 x19: x19 x20: x20
STACK CFI 3ca8 x23: x23 x24: x24
STACK CFI 3cac x25: x25 x26: x26
STACK CFI 3cb0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3d30 x19: x19 x20: x20
STACK CFI 3d34 x23: x23 x24: x24
STACK CFI 3d38 x25: x25 x26: x26
STACK CFI INIT 3d40 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3d44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3dac x21: x21 x22: x22
STACK CFI 3db4 x19: x19 x20: x20
STACK CFI 3db8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3de0 x19: x19 x20: x20
STACK CFI 3de4 x21: x21 x22: x22
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3dec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3df4 x19: x19 x20: x20
STACK CFI 3df8 x21: x21 x22: x22
STACK CFI 3dfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e00 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e34 x19: x19 x20: x20
STACK CFI INIT 3e38 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3e3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3eac x23: .cfa -16 + ^
STACK CFI 3ee8 x21: x21 x22: x22
STACK CFI 3eec x23: x23
STACK CFI 3ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3f0c x21: x21 x22: x22
STACK CFI 3f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3f70 x21: x21 x22: x22
STACK CFI 3f74 x23: x23
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3fc4 x21: x21 x22: x22
STACK CFI 3fc8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3fd4 x21: x21 x22: x22
STACK CFI 3ff4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ff8 x21: x21 x22: x22
STACK CFI INIT 4000 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4004 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 400c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4028 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4040 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4080 x19: x19 x20: x20
STACK CFI 4088 x21: x21 x22: x22
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4098 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 40a8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 40ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 40bc x19: x19 x20: x20
STACK CFI 40c0 x21: x21 x22: x22
STACK CFI 40c8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 40cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 40dc x19: x19 x20: x20
STACK CFI 40e0 x21: x21 x22: x22
STACK CFI 40e8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 40ec .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 40f4 x21: x21 x22: x22
STACK CFI INIT 40f8 550 .cfa: sp 0 + .ra: x30
STACK CFI 40fc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4104 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 410c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4130 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4140 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4180 x23: x23 x24: x24
STACK CFI 41ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 41b0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 41d0 x23: x23 x24: x24
STACK CFI 41d4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 41f8 x23: x23 x24: x24
STACK CFI 41fc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4258 x23: x23 x24: x24
STACK CFI 4264 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 433c x23: x23 x24: x24
STACK CFI 4340 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 43c4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4488 x27: x27 x28: x28
STACK CFI 448c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4534 x23: x23 x24: x24
STACK CFI 4538 x27: x27 x28: x28
STACK CFI 453c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4620 x23: x23 x24: x24
STACK CFI 4624 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4628 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4630 x27: x27 x28: x28
STACK CFI 4638 x23: x23 x24: x24
STACK CFI 4640 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4644 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 4648 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4650 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4668 264 .cfa: sp 0 + .ra: x30
STACK CFI 466c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4674 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4680 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 469c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4788 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 48d0 80 .cfa: sp 0 + .ra: x30
STACK CFI 48d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48e0 x19: .cfa -16 + ^
STACK CFI 4928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 492c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 494c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4950 810 .cfa: sp 0 + .ra: x30
STACK CFI 4954 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 495c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4968 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 498c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4994 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 49a0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4bc4 x21: x21 x22: x22
STACK CFI 4bc8 x25: x25 x26: x26
STACK CFI 4bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4c00 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4ca8 x21: x21 x22: x22
STACK CFI 4cac x25: x25 x26: x26
STACK CFI 4cb0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4d50 x21: x21 x22: x22
STACK CFI 4d54 x25: x25 x26: x26
STACK CFI 4d58 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4e10 x21: x21 x22: x22
STACK CFI 4e14 x25: x25 x26: x26
STACK CFI 4e18 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4e34 x21: x21 x22: x22
STACK CFI 4e38 x25: x25 x26: x26
STACK CFI 4e3c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4e4c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 4e68 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 50a0 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 50a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 50a8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 50e4 x21: x21 x22: x22
STACK CFI 50e8 x25: x25 x26: x26
STACK CFI 50ec x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5110 x21: x21 x22: x22
STACK CFI 5114 x25: x25 x26: x26
STACK CFI 5118 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5134 x21: x21 x22: x22
STACK CFI 5138 x25: x25 x26: x26
STACK CFI 513c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5158 x21: x21 x22: x22
STACK CFI 515c x25: x25 x26: x26
STACK CFI INIT 5160 60 .cfa: sp 0 + .ra: x30
STACK CFI 5164 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 516c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5174 x21: .cfa -16 + ^
STACK CFI 51bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 51c0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 51c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 51cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 51dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 51f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 522c x25: .cfa -32 + ^
STACK CFI 5248 x25: x25
STACK CFI 527c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5280 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 52d0 x25: x25
STACK CFI 5308 x25: .cfa -32 + ^
STACK CFI 530c x25: x25
STACK CFI 5338 x25: .cfa -32 + ^
STACK CFI 533c x25: x25
STACK CFI 5360 x25: .cfa -32 + ^
STACK CFI INIT 5368 6a0 .cfa: sp 0 + .ra: x30
STACK CFI 536c .cfa: sp 1456 +
STACK CFI 5374 .ra: .cfa -1432 + ^ x29: .cfa -1440 + ^
STACK CFI 5380 x19: .cfa -1424 + ^ x20: .cfa -1416 + ^
STACK CFI 5390 x21: .cfa -1408 + ^ x22: .cfa -1400 + ^
STACK CFI 5398 x25: .cfa -1376 + ^ x26: .cfa -1368 + ^
STACK CFI 53ac x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI 53b4 x23: .cfa -1392 + ^ x24: .cfa -1384 + ^
STACK CFI 544c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5450 .cfa: sp 1456 + .ra: .cfa -1432 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^ x29: .cfa -1440 + ^
STACK CFI INIT 5a08 16c .cfa: sp 0 + .ra: x30
STACK CFI 5a0c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5a3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5a44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5aa0 x21: x21 x22: x22
STACK CFI 5aa4 x23: x23 x24: x24
STACK CFI 5ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5acc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 5aec x21: x21 x22: x22
STACK CFI 5af0 x23: x23 x24: x24
STACK CFI 5af4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5b2c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5b44 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5b60 x21: x21 x22: x22
STACK CFI 5b64 x23: x23 x24: x24
STACK CFI 5b6c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5b70 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 5b78 104 .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b88 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c58 x19: x19 x20: x20
STACK CFI 5c5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5c60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5c80 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 5c84 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 5c8c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 5ca4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 5ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ce4 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x29: .cfa -240 + ^
STACK CFI 5d58 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5de4 x23: x23 x24: x24
STACK CFI 5e38 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5e40 x25: .cfa -176 + ^
STACK CFI 5e60 x25: x25
STACK CFI 5ed8 x23: x23 x24: x24
STACK CFI 5f34 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5f50 x23: x23 x24: x24
STACK CFI 5f70 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5fd0 x23: x23 x24: x24
STACK CFI 5fd4 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^
STACK CFI 5ff0 x23: x23 x24: x24
STACK CFI 5ff4 x25: x25
STACK CFI 5ff8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6004 x23: x23 x24: x24
STACK CFI 600c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6010 x25: .cfa -176 + ^
STACK CFI 6014 x23: x23 x24: x24 x25: x25
STACK CFI INIT 6030 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6058 564 .cfa: sp 0 + .ra: x30
STACK CFI 605c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6068 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6074 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 60d0 x21: x21 x22: x22
STACK CFI 60d8 x19: x19 x20: x20
STACK CFI 60e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 60e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 610c x19: x19 x20: x20
STACK CFI 6110 x21: x21 x22: x22
STACK CFI 6114 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6158 x21: x21 x22: x22
STACK CFI 6160 x19: x19 x20: x20
STACK CFI 6164 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6180 x19: x19 x20: x20
STACK CFI 6184 x21: x21 x22: x22
STACK CFI 6188 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 61c4 x21: x21 x22: x22
STACK CFI 61cc x19: x19 x20: x20
STACK CFI 61d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6240 x19: x19 x20: x20
STACK CFI 6244 x21: x21 x22: x22
STACK CFI 6248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 624c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 631c x19: x19 x20: x20
STACK CFI 6320 x21: x21 x22: x22
STACK CFI 6324 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6358 x21: x21 x22: x22
STACK CFI 6360 x19: x19 x20: x20
STACK CFI 6364 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 639c x19: x19 x20: x20
STACK CFI 63a0 x21: x21 x22: x22
STACK CFI 63a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 63d8 x21: x21 x22: x22
STACK CFI 63e4 x19: x19 x20: x20
STACK CFI 63e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6488 x21: x21 x22: x22
STACK CFI 6490 x19: x19 x20: x20
STACK CFI 6494 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64c8 x21: x21 x22: x22
STACK CFI 64d0 x19: x19 x20: x20
STACK CFI 64f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6548 x21: x21 x22: x22
STACK CFI 6550 x19: x19 x20: x20
STACK CFI 6554 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 655c x19: x19 x20: x20
STACK CFI 6560 x21: x21 x22: x22
STACK CFI 6564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6568 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6580 x19: x19 x20: x20
STACK CFI 6584 x21: x21 x22: x22
STACK CFI 6588 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6590 x19: x19 x20: x20
STACK CFI 6594 x21: x21 x22: x22
STACK CFI 6598 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 65b4 x19: x19 x20: x20
STACK CFI 65b8 x21: x21 x22: x22
STACK CFI INIT 65c0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 65c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6614 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6778 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 677c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6784 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6790 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 67a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 67fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6800 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6a20 190 .cfa: sp 0 + .ra: x30
STACK CFI 6a24 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b68 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6bb0 70 .cfa: sp 0 + .ra: x30
STACK CFI 6bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6bbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6bcc x21: .cfa -16 + ^
STACK CFI 6bf8 x21: x21
STACK CFI 6c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6c1c x21: x21
STACK CFI INIT 6c20 68 .cfa: sp 0 + .ra: x30
STACK CFI 6c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c2c x21: .cfa -16 + ^
STACK CFI 6c38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c60 x19: x19 x20: x20
STACK CFI 6c6c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 6c70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6c84 x19: x19 x20: x20
STACK CFI INIT 6c88 158 .cfa: sp 0 + .ra: x30
STACK CFI 6c8c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c94 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6ca0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6cac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6cc0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6d04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 6d10 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6da4 x27: x27 x28: x28
STACK CFI 6da8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6dc0 x27: x27 x28: x28
STACK CFI 6dc8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 6de0 48 .cfa: sp 0 + .ra: x30
STACK CFI 6de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6df4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6e28 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e50 270 .cfa: sp 0 + .ra: x30
STACK CFI 6e54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6e5c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6e6c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6e78 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6e7c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6e84 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6fec x21: x21 x22: x22
STACK CFI 6ff0 x23: x23 x24: x24
STACK CFI 6ff4 x25: x25 x26: x26
STACK CFI 6ff8 x27: x27 x28: x28
STACK CFI 7000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7004 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 70c0 124 .cfa: sp 0 + .ra: x30
STACK CFI 70c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 70cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 70d4 x21: .cfa -16 + ^
STACK CFI 7140 x21: x21
STACK CFI 714c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7150 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 718c x21: x21
STACK CFI 7190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 71a4 x21: x21
STACK CFI 71a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 71c4 x21: x21
STACK CFI INIT 71e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 720c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 722c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7230 48 .cfa: sp 0 + .ra: x30
STACK CFI 7254 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7274 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7278 31c .cfa: sp 0 + .ra: x30
STACK CFI 727c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7284 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7294 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 72a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 73b4 x21: x21 x22: x22
STACK CFI 73b8 x23: x23 x24: x24
STACK CFI 73c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7434 x23: x23 x24: x24
STACK CFI 7444 x21: x21 x22: x22
STACK CFI 7448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 744c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7468 x23: x23 x24: x24
STACK CFI 746c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7498 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 74cc x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 758c x21: x21 x22: x22
STACK CFI 7590 x23: x23 x24: x24
STACK CFI INIT 7598 338 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78d0 248 .cfa: sp 0 + .ra: x30
STACK CFI 78d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 78dc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 78ec x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7900 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 7908 x25: .cfa -128 + ^
STACK CFI 7a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7a44 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7b18 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7b1c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 7b2c x19: .cfa -256 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bb4 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT 7bb8 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 7bbc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7bc4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7bd4 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7be8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 7bf0 x25: .cfa -112 + ^
STACK CFI 7d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7d10 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7da0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7da4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 7db4 x19: .cfa -272 + ^
STACK CFI 7e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e40 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7e48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e68 19c .cfa: sp 0 + .ra: x30
STACK CFI 7e6c .cfa: sp 112 +
STACK CFI 7e70 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e78 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7ea4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 7f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7f8c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8008 68 .cfa: sp 0 + .ra: x30
STACK CFI 800c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8014 x19: .cfa -16 + ^
STACK CFI 8028 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 802c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 806c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8070 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 8074 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 807c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8088 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8094 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 80a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8108 x27: .cfa -16 + ^
STACK CFI 8174 x27: x27
STACK CFI 8220 x23: x23 x24: x24
STACK CFI 8228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 822c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 8254 x27: .cfa -16 + ^
STACK CFI 8264 x27: x27
STACK CFI 8274 x23: x23 x24: x24
STACK CFI 827c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 8280 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 82a4 x27: x27
STACK CFI 82a8 x23: x23 x24: x24
STACK CFI 82bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 82c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8348 38 .cfa: sp 0 + .ra: x30
STACK CFI 835c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 837c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8380 9c .cfa: sp 0 + .ra: x30
STACK CFI 8384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 838c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8394 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 83a0 x23: .cfa -16 + ^
STACK CFI 8400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8404 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8420 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8430 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 8434 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 843c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 844c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8460 x27: .cfa -48 + ^
STACK CFI 846c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8474 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 852c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 85f0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 85f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8600 x25: .cfa -16 + ^
STACK CFI 8608 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8614 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8620 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8690 x19: x19 x20: x20
STACK CFI 8694 x21: x21 x22: x22
STACK CFI 8698 x23: x23 x24: x24
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 86a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 86b4 x19: x19 x20: x20
STACK CFI 86b8 x21: x21 x22: x22
STACK CFI 86bc x23: x23 x24: x24
STACK CFI 86c4 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 86c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 86d0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 86d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 86e0 x25: .cfa -16 + ^
STACK CFI 86e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 86f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8700 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8770 x19: x19 x20: x20
STACK CFI 8774 x21: x21 x22: x22
STACK CFI 8778 x23: x23 x24: x24
STACK CFI 8784 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 8788 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8794 x19: x19 x20: x20
STACK CFI 8798 x21: x21 x22: x22
STACK CFI 879c x23: x23 x24: x24
STACK CFI 87a4 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 87a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 87b0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 87b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 87bc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 87cc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 87e0 x27: .cfa -48 + ^
STACK CFI 87ec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 87f4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 88a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 88ac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 89a8 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 89ac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 89b4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 89c4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 89d8 x27: .cfa -48 + ^
STACK CFI 89e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 89ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8aa4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8ba0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 8ba4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8bac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8bbc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8bd0 x27: .cfa -48 + ^
STACK CFI 8bdc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8be4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8c9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8d60 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8d6c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8d7c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8d90 x27: .cfa -48 + ^
STACK CFI 8d9c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8da4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8e5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8f20 160 .cfa: sp 0 + .ra: x30
STACK CFI 8f24 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 8f2c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 8f3c x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 8f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8f8c .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x29: .cfa -496 + ^
STACK CFI 9000 x23: .cfa -448 + ^
STACK CFI 903c x23: x23
STACK CFI 9058 x23: .cfa -448 + ^
STACK CFI 9064 x23: x23
STACK CFI 9070 x23: .cfa -448 + ^
STACK CFI 9078 x23: x23
STACK CFI INIT 9080 158 .cfa: sp 0 + .ra: x30
STACK CFI 9084 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9090 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 90a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 90c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 90ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9174 x19: x19 x20: x20
STACK CFI 917c x25: x25 x26: x26
STACK CFI 91a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 91a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 91b8 x19: x19 x20: x20
STACK CFI 91bc x25: x25 x26: x26
STACK CFI 91c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 91c8 x19: x19 x20: x20
STACK CFI 91d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 91d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 91d8 64 .cfa: sp 0 + .ra: x30
STACK CFI 91dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 91e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 91ec x21: .cfa -16 + ^
STACK CFI 9224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9228 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9240 64 .cfa: sp 0 + .ra: x30
STACK CFI 9244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 924c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9254 x21: .cfa -16 + ^
STACK CFI 928c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9290 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 92a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 92a8 64 .cfa: sp 0 + .ra: x30
STACK CFI 92ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92bc x21: .cfa -16 + ^
STACK CFI 92f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 92f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9310 64 .cfa: sp 0 + .ra: x30
STACK CFI 9314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 931c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9324 x21: .cfa -16 + ^
STACK CFI 935c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9360 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9378 280 .cfa: sp 0 + .ra: x30
STACK CFI 937c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9384 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9394 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9408 x23: .cfa -16 + ^
STACK CFI 9454 x21: x21 x22: x22
STACK CFI 9458 x23: x23
STACK CFI 9468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 946c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9478 x21: x21 x22: x22
STACK CFI 9488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 948c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 94b4 x23: .cfa -16 + ^
STACK CFI 94e0 x23: x23
STACK CFI 94e8 x21: x21 x22: x22
STACK CFI 94f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9520 x21: x21 x22: x22 x23: x23
STACK CFI 9538 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9554 x21: x21 x22: x22
STACK CFI 9558 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95a4 x23: .cfa -16 + ^
STACK CFI 95b4 x23: x23
STACK CFI 95d4 x21: x21 x22: x22
STACK CFI 95d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 95f8 178 .cfa: sp 0 + .ra: x30
STACK CFI 95fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 960c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 964c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9650 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9668 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9670 x21: .cfa -16 + ^
STACK CFI 96cc x21: x21
STACK CFI 96e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 96e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9718 x21: x21
STACK CFI 9724 x21: .cfa -16 + ^
STACK CFI INIT 9770 194 .cfa: sp 0 + .ra: x30
STACK CFI 9774 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9780 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9790 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 97b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 97f4 x23: x23 x24: x24
STACK CFI 9820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9824 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9860 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9888 x23: x23 x24: x24
STACK CFI 98b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 98b8 x23: x23 x24: x24
STACK CFI 98bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 98dc x23: x23 x24: x24
STACK CFI 9900 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 9908 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 990c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9914 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9924 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9a28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
