MODULE Linux arm64 F5878BE4DD5DBEC4DEDCD898DCCFE3A50 libjson-c.so.4
INFO CODE_ID E48B87F55DDDC4BEDEDCD898DCCFE3A5FE640F8E
PUBLIC 3728 0 array_list_new
PUBLIC 3788 0 array_list_free
PUBLIC 37e0 0 array_list_get_idx
PUBLIC 3800 0 array_list_put_idx
PUBLIC 3920 0 array_list_add
PUBLIC 3930 0 array_list_sort
PUBLIC 3940 0 array_list_bsearch
PUBLIC 39f0 0 array_list_length
PUBLIC 39f8 0 array_list_del_idx
PUBLIC 3ac0 0 mc_set_debug
PUBLIC 3ad0 0 mc_get_debug
PUBLIC 3ae0 0 mc_set_syslog
PUBLIC 3af0 0 mc_debug
PUBLIC 3c00 0 mc_error
PUBLIC 3d00 0 mc_info
PUBLIC 3e00 0 json_c_version
PUBLIC 3e10 0 json_c_version_num
PUBLIC 4250 0 json_object_userdata_to_json_string
PUBLIC 4290 0 json_object_free_userdata
PUBLIC 4660 0 json_object_get
PUBLIC 4678 0 json_object_put
PUBLIC 4750 0 json_object_is_type
PUBLIC 4770 0 json_object_get_type
PUBLIC 4788 0 json_object_get_userdata
PUBLIC 47a0 0 json_object_set_userdata
PUBLIC 4808 0 json_object_set_serializer
PUBLIC 48e0 0 json_object_to_json_string_length
PUBLIC 4978 0 json_object_to_json_string_ext
PUBLIC 4980 0 json_object_to_json_string
PUBLIC 4988 0 json_object_new_object
PUBLIC 4a18 0 json_object_get_object
PUBLIC 4ce8 0 json_object_object_add_ex
PUBLIC 4df0 0 json_object_object_add
PUBLIC 4df8 0 json_object_object_length
PUBLIC 4e48 0 json_c_object_sizeof
PUBLIC 4e50 0 json_object_object_get_ex
PUBLIC 4e80 0 json_object_object_get
PUBLIC 4ed0 0 json_object_object_del
PUBLIC 4f28 0 json_object_new_boolean
PUBLIC 4f78 0 json_object_get_boolean
PUBLIC 4fe8 0 json_object_set_boolean
PUBLIC 5018 0 json_object_new_int
PUBLIC 5070 0 json_object_get_int
PUBLIC 5170 0 json_object_set_int
PUBLIC 51a8 0 json_object_new_int64
PUBLIC 5200 0 json_object_get_int64
PUBLIC 52e8 0 json_object_set_int64
PUBLIC 5318 0 json_object_int_inc
PUBLIC 5388 0 json_c_set_serialization_double_format
PUBLIC 5470 0 json_object_double_to_json_string
PUBLIC 5488 0 json_object_new_double
PUBLIC 54e0 0 json_object_new_double_s
PUBLIC 5558 0 json_object_get_double
PUBLIC 5698 0 json_object_set_double
PUBLIC 56c8 0 json_object_new_string
PUBLIC 5780 0 json_object_new_string_len
PUBLIC 5848 0 json_object_get_string
PUBLIC 5888 0 json_object_get_string_len
PUBLIC 58b8 0 json_object_set_string_len
PUBLIC 59b0 0 json_object_set_string
PUBLIC 59e8 0 json_object_new_array
PUBLIC 5a60 0 json_c_shallow_copy_default
PUBLIC 5b88 0 json_object_get_array
PUBLIC 5bb0 0 json_object_array_sort
PUBLIC 5c08 0 json_object_array_bsearch
PUBLIC 5c78 0 json_object_array_length
PUBLIC 5cc8 0 json_object_array_add
PUBLIC 5d20 0 json_object_array_put_idx
PUBLIC 5d88 0 json_object_array_del_idx
PUBLIC 5df0 0 json_object_array_get_idx
PUBLIC 6270 0 json_object_equal
PUBLIC 6528 0 json_object_deep_copy
PUBLIC 65c0 0 json_object_iter_begin
PUBLIC 65d8 0 json_object_iter_end
PUBLIC 65e0 0 json_object_iter_next
PUBLIC 65f0 0 json_object_iter_peek_name
PUBLIC 6600 0 json_object_iter_peek_value
PUBLIC 6610 0 json_object_iter_equal
PUBLIC 6628 0 json_object_iter_init_default
PUBLIC 6a50 0 json_pointer_get
PUBLIC 6b08 0 json_pointer_getf
PUBLIC 6c38 0 json_pointer_set
PUBLIC 6da0 0 json_pointer_setf
PUBLIC 6f98 0 json_tokener_error_desc
PUBLIC 6fc0 0 json_tokener_get_error
PUBLIC 6fc8 0 json_tokener_reset
PUBLIC 7028 0 json_tokener_new_ex
PUBLIC 7098 0 json_tokener_new
PUBLIC 70a0 0 json_tokener_free
PUBLIC 70d8 0 json_tokener_parse_ex
PUBLIC 8fd8 0 json_tokener_parse_verbose
PUBLIC 9058 0 json_tokener_parse
PUBLIC 90a0 0 json_tokener_set_flags
PUBLIC 90a8 0 json_util_get_last_err
PUBLIC 90c0 0 _json_c_set_last_err
PUBLIC 9258 0 json_object_from_fd
PUBLIC 9358 0 json_object_from_file
PUBLIC 93d8 0 json_object_to_file_ext
PUBLIC 9498 0 json_object_to_fd
PUBLIC 94c8 0 json_object_to_file
PUBLIC 94d0 0 json_parse_double
PUBLIC 9538 0 json_parse_int64
PUBLIC 95e8 0 json_type_to_name
PUBLIC 9930 0 json_c_visit
PUBLIC 99a8 0 lh_ptr_equal
PUBLIC 9fe8 0 lh_char_equal
PUBLIC a008 0 json_global_set_string_hash
PUBLIC a050 0 lh_abort
PUBLIC a0f8 0 lh_table_new
PUBLIC a1c8 0 lh_kchar_table_new
PUBLIC a1e0 0 lh_kptr_table_new
PUBLIC a1f8 0 lh_table_free
PUBLIC a248 0 lh_table_insert_w_hash
PUBLIC a388 0 lh_table_resize
PUBLIC a460 0 lh_table_insert
PUBLIC a4b0 0 lh_table_lookup_entry_w_hash
PUBLIC a570 0 lh_table_lookup_entry
PUBLIC a5a8 0 lh_table_lookup_ex
PUBLIC a5f8 0 lh_table_lookup
PUBLIC a648 0 lh_table_delete_entry
PUBLIC a720 0 lh_table_delete
PUBLIC a760 0 lh_table_length
PUBLIC a808 0 printbuf_new
PUBLIC a868 0 printbuf_memappend
PUBLIC a908 0 printbuf_memset
PUBLIC a9b8 0 sprintbuf
PUBLIC ab40 0 printbuf_reset
PUBLIC ab50 0 printbuf_free
PUBLIC ab80 0 json_c_get_random_seed
PUBLIC aca8 0 _json_c_strerror
STACK CFI INIT 3668 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3698 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 36dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36e4 x19: .cfa -16 + ^
STACK CFI 371c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3728 60 .cfa: sp 0 + .ra: x30
STACK CFI 372c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3738 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3778 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3788 58 .cfa: sp 0 + .ra: x30
STACK CFI 378c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3798 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37e0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3800 120 .cfa: sp 0 + .ra: x30
STACK CFI 380c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3814 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3824 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3838 x23: .cfa -16 + ^
STACK CFI 3888 x23: x23
STACK CFI 38ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 38e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 38f4 x23: x23
STACK CFI 3908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 390c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3914 x23: x23
STACK CFI 3918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3920 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3930 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3940 ac .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 394c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3954 x25: .cfa -16 + ^
STACK CFI 3960 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3968 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39b4 x19: x19 x20: x20
STACK CFI 39b8 x23: x23 x24: x24
STACK CFI 39cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 39d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 39d8 x19: x19 x20: x20
STACK CFI 39e0 x23: x23 x24: x24
STACK CFI 39e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI INIT 39f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39f8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 39fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a28 x23: .cfa -16 + ^
STACK CFI 3a94 x19: x19 x20: x20
STACK CFI 3a98 x23: x23
STACK CFI 3aa0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3aac x19: x19 x20: x20
STACK CFI 3ab0 x23: x23
STACK CFI INIT 3ac0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ad0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ae0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3af0 110 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3b00 x19: .cfa -320 + ^
STACK CFI 3b68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b6c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 3c00 100 .cfa: sp 0 + .ra: x30
STACK CFI 3c04 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3c14 x19: .cfa -320 + ^
STACK CFI 3cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cc4 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 3d00 100 .cfa: sp 0 + .ra: x30
STACK CFI 3d04 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3d14 x19: .cfa -320 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dc4 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 3e00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e18 334 .cfa: sp 0 + .ra: x30
STACK CFI 3e1c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3e24 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3e48 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3e54 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3e60 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3e74 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3fa8 x19: x19 x20: x20
STACK CFI 3fac x21: x21 x22: x22
STACK CFI 3fb0 x23: x23 x24: x24
STACK CFI 3fb4 x27: x27 x28: x28
STACK CFI 3fd4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 3fd8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 40b4 x19: x19 x20: x20
STACK CFI 40b8 x21: x21 x22: x22
STACK CFI 40bc x23: x23 x24: x24
STACK CFI 40c0 x27: x27 x28: x28
STACK CFI 40c4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4138 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 413c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4140 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4144 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4148 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 4150 74 .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4160 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4170 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 41c8 88 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 41dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4200 x21: .cfa -48 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 424c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4250 40 .cfa: sp 0 + .ra: x30
STACK CFI 4254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 425c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 428c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4298 28 .cfa: sp 0 + .ra: x30
STACK CFI 429c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42a4 x19: .cfa -16 + ^
STACK CFI 42bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 42c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42cc x19: .cfa -16 + ^
STACK CFI 42ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42f0 54 .cfa: sp 0 + .ra: x30
STACK CFI 42f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42fc x19: .cfa -16 + ^
STACK CFI 4320 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4340 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4348 30 .cfa: sp 0 + .ra: x30
STACK CFI 434c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4354 x19: .cfa -16 + ^
STACK CFI 4374 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4378 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 43d8 270 .cfa: sp 0 + .ra: x30
STACK CFI 43dc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 43e8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4408 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4424 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 44f4 x23: x23 x24: x24
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4568 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 4574 x23: x23 x24: x24
STACK CFI 4598 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 45e4 x23: x23 x24: x24
STACK CFI 45fc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4630 x23: x23 x24: x24
STACK CFI 4634 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 463c x23: x23 x24: x24
STACK CFI 4644 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI INIT 4648 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4660 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4678 90 .cfa: sp 0 + .ra: x30
STACK CFI 4680 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 468c x19: .cfa -16 + ^
STACK CFI 46cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4708 40 .cfa: sp 0 + .ra: x30
STACK CFI 470c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4714 x19: .cfa -16 + ^
STACK CFI 472c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4748 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4750 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4770 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4788 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 47e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 47e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4808 d8 .cfa: sp 0 + .ra: x30
STACK CFI 480c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 483c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48e0 98 .cfa: sp 0 + .ra: x30
STACK CFI 48e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4900 x21: .cfa -16 + ^
STACK CFI 4930 x21: x21
STACK CFI 4948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 494c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4964 x21: x21
STACK CFI INIT 4978 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4980 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4988 90 .cfa: sp 0 + .ra: x30
STACK CFI 498c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 499c x19: .cfa -16 + ^
STACK CFI 49f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a18 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a40 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4a4c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4a64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4a6c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4a9c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4aa8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4c30 x23: x23 x24: x24
STACK CFI 4c34 x27: x27 x28: x28
STACK CFI 4c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4c6c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 4c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4c8c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4c94 x23: x23 x24: x24
STACK CFI 4c98 x27: x27 x28: x28
STACK CFI 4ca0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4cb0 x23: x23 x24: x24
STACK CFI 4cb8 x27: x27 x28: x28
STACK CFI 4cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4cc0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4ce8 104 .cfa: sp 0 + .ra: x30
STACK CFI 4cec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4cf4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4cfc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d04 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d68 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4db0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4df0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4df8 50 .cfa: sp 0 + .ra: x30
STACK CFI 4dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e04 x19: .cfa -16 + ^
STACK CFI 4e20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4e48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e80 50 .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e8c x19: .cfa -32 + ^
STACK CFI 4ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ed0 58 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4edc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f28 50 .cfa: sp 0 + .ra: x30
STACK CFI 4f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f38 x19: .cfa -16 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4f78 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fe8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5018 54 .cfa: sp 0 + .ra: x30
STACK CFI 501c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5028 x19: .cfa -16 + ^
STACK CFI 5068 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5070 fc .cfa: sp 0 + .ra: x30
STACK CFI 5074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 507c x19: .cfa -32 + ^
STACK CFI 50e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 50e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5170 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51a8 54 .cfa: sp 0 + .ra: x30
STACK CFI 51ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51b8 x19: .cfa -16 + ^
STACK CFI 51f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5200 e8 .cfa: sp 0 + .ra: x30
STACK CFI 5204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 520c x19: .cfa -32 + ^
STACK CFI 528c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5290 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5318 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5388 e8 .cfa: sp 0 + .ra: x30
STACK CFI 538c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 545c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5470 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5488 54 .cfa: sp 0 + .ra: x30
STACK CFI 548c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 549c v8: .cfa -16 + ^
STACK CFI 54d8 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 54e0 78 .cfa: sp 0 + .ra: x30
STACK CFI 54e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5534 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5558 13c .cfa: sp 0 + .ra: x30
STACK CFI 555c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5564 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 55c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 55d4 x21: .cfa -32 + ^
STACK CFI 5640 x21: x21
STACK CFI 565c x21: .cfa -32 + ^
STACK CFI 5664 x21: x21
STACK CFI 5670 x21: .cfa -32 + ^
STACK CFI 5680 x21: x21
STACK CFI 5684 x21: .cfa -32 + ^
STACK CFI 5688 x21: x21
STACK CFI 5690 x21: .cfa -32 + ^
STACK CFI INIT 5698 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56c8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 56cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5748 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5780 c4 .cfa: sp 0 + .ra: x30
STACK CFI 5784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 578c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5798 x21: .cfa -16 + ^
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5808 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5848 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5888 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 58b8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 58c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 58ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 592c x21: x21 x22: x22
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5938 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5988 x21: x21 x22: x22
STACK CFI 598c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5994 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 59a8 x21: x21 x22: x22
STACK CFI INIT 59b0 34 .cfa: sp 0 + .ra: x30
STACK CFI 59b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 59e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59e8 78 .cfa: sp 0 + .ra: x30
STACK CFI 59ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59fc x19: .cfa -16 + ^
STACK CFI 5a4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5a50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a60 128 .cfa: sp 0 + .ra: x30
STACK CFI 5a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ac0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5afc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b88 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bb0 58 .cfa: sp 0 + .ra: x30
STACK CFI 5bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5c08 70 .cfa: sp 0 + .ra: x30
STACK CFI 5c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c78 50 .cfa: sp 0 + .ra: x30
STACK CFI 5c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c84 x19: .cfa -16 + ^
STACK CFI 5ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5cc8 58 .cfa: sp 0 + .ra: x30
STACK CFI 5ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5cfc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5d20 68 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d38 x21: .cfa -16 + ^
STACK CFI 5d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d88 68 .cfa: sp 0 + .ra: x30
STACK CFI 5d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5da0 x21: .cfa -16 + ^
STACK CFI 5dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5df0 58 .cfa: sp 0 + .ra: x30
STACK CFI 5df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5dfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e48 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 5e4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5e54 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5e5c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5e64 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5e78 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5e84 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f6c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5fe8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6028 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6040 230 .cfa: sp 0 + .ra: x30
STACK CFI 6044 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 604c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6058 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6070 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 60a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6130 x25: x25 x26: x26
STACK CFI 6158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 615c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6160 x25: x25 x26: x26
STACK CFI 61a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6214 x25: x25 x26: x26
STACK CFI 6244 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6248 x25: x25 x26: x26
STACK CFI 626c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 6270 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 6274 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 627c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6284 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 62e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 637c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 63e8 x23: x23 x24: x24
STACK CFI 63ec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 63f0 x23: x23 x24: x24
STACK CFI 6410 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6470 x23: x23 x24: x24
STACK CFI 6488 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 64c8 x23: x23 x24: x24
STACK CFI 64d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 6528 94 .cfa: sp 0 + .ra: x30
STACK CFI 652c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 653c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 658c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 65a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 65c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 65c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 65d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6600 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6610 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6628 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6630 130 .cfa: sp 0 + .ra: x30
STACK CFI 6634 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 663c x23: .cfa -16 + ^
STACK CFI 6648 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 66cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 66d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6728 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6760 90 .cfa: sp 0 + .ra: x30
STACK CFI 6764 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6770 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 677c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 67ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 67f0 168 .cfa: sp 0 + .ra: x30
STACK CFI 67f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 67fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 680c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6814 x23: .cfa -48 + ^
STACK CFI 6898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 689c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6958 f4 .cfa: sp 0 + .ra: x30
STACK CFI 695c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6964 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6974 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 69e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 69e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a50 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6a54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6adc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b08 12c .cfa: sp 0 + .ra: x30
STACK CFI 6b0c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 6b1c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 6b24 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 6bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6bfc .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT 6c38 168 .cfa: sp 0 + .ra: x30
STACK CFI 6c3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6c44 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6c4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6c78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6cdc x23: x23 x24: x24
STACK CFI 6d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6d1c x23: x23 x24: x24
STACK CFI 6d20 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6d34 x23: x23 x24: x24
STACK CFI 6d38 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6d50 x23: x23 x24: x24
STACK CFI 6d54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6d64 x23: x23 x24: x24
STACK CFI 6d84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6d98 x23: x23 x24: x24
STACK CFI INIT 6da0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 6da4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 6db4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 6dbc x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 6e58 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 6e88 x23: x23 x24: x24
STACK CFI 6eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6eb0 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI 6f24 x23: x23 x24: x24
STACK CFI 6f3c x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI INIT 6f40 58 .cfa: sp 0 + .ra: x30
STACK CFI 6f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6f98 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc8 5c .cfa: sp 0 + .ra: x30
STACK CFI 6fd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6fd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6fe4 x21: .cfa -16 + ^
STACK CFI 701c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7028 6c .cfa: sp 0 + .ra: x30
STACK CFI 702c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7038 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7084 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7098 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70a0 38 .cfa: sp 0 + .ra: x30
STACK CFI 70a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70ac x19: .cfa -16 + ^
STACK CFI 70d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 70d8 1f00 .cfa: sp 0 + .ra: x30
STACK CFI 70dc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 70e4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 70f4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7148 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 718c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7190 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 7284 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 72b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 72bc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI 72d0 x23: x23 x24: x24
STACK CFI 72d4 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 7b88 x23: x23 x24: x24
STACK CFI 7b8c x25: x25 x26: x26
STACK CFI 7b90 x27: x27 x28: x28
STACK CFI 7b94 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 8fa0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8fa4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8fa8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 8fac x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 8fd8 80 .cfa: sp 0 + .ra: x30
STACK CFI 8fdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8fe4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8fec x21: .cfa -16 + ^
STACK CFI 903c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9040 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9058 48 .cfa: sp 0 + .ra: x30
STACK CFI 905c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9064 x19: .cfa -32 + ^
STACK CFI 9098 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 909c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 90a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 90c0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 90c4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 90ec x19: .cfa -320 + ^
STACK CFI 9180 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9184 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 9188 cc .cfa: sp 0 + .ra: x30
STACK CFI 918c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9198 x23: .cfa -16 + ^
STACK CFI 91ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 91c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9224 x19: x19 x20: x20
STACK CFI 9230 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9234 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9238 x19: x19 x20: x20
STACK CFI 9248 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 924c .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9258 fc .cfa: sp 0 + .ra: x30
STACK CFI 9260 .cfa: sp 4160 +
STACK CFI 9264 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 926c x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI 927c x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 9308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 930c .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 9358 80 .cfa: sp 0 + .ra: x30
STACK CFI 935c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 936c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 939c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 93a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 93d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 93d8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 93dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 93e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 93f4 x23: .cfa -16 + ^
STACK CFI 9400 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9440 x23: x23
STACK CFI 9448 x21: x21 x22: x22
STACK CFI 9454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9458 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 947c x21: x21 x22: x22
STACK CFI 9480 x23: x23
STACK CFI INIT 9498 2c .cfa: sp 0 + .ra: x30
STACK CFI 94a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 94c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 94c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 94d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 94dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 94fc x21: .cfa -32 + ^
STACK CFI 9530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9534 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9538 b0 .cfa: sp 0 + .ra: x30
STACK CFI 953c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9544 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9554 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 95e8 40 .cfa: sp 0 + .ra: x30
STACK CFI 9604 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9628 308 .cfa: sp 0 + .ra: x30
STACK CFI 962c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9634 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 964c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9664 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 96fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9700 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 9730 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 97a8 x27: x27 x28: x28
STACK CFI 97f0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 98b4 x27: x27 x28: x28
STACK CFI 98b8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 98e8 x27: x27 x28: x28
STACK CFI 98ec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9918 x27: x27 x28: x28
STACK CFI 991c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9924 x27: x27 x28: x28
STACK CFI 992c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 9930 60 .cfa: sp 0 + .ra: x30
STACK CFI 9934 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9974 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9984 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9990 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 99b8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99e8 5fc .cfa: sp 0 + .ra: x30
STACK CFI 99ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 99f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9fe8 1c .cfa: sp 0 + .ra: x30
STACK CFI 9fec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a000 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a008 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT a050 a8 .cfa: sp 0 + .ra: x30
STACK CFI a054 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI INIT a0f8 cc .cfa: sp 0 + .ra: x30
STACK CFI a0fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a110 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI a190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a194 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a1c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1f8 50 .cfa: sp 0 + .ra: x30
STACK CFI a1fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a208 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a248 13c .cfa: sp 0 + .ra: x30
STACK CFI a24c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a258 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a274 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a280 x23: .cfa -16 + ^
STACK CFI a354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a358 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a37c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a388 d4 .cfa: sp 0 + .ra: x30
STACK CFI a38c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a394 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a3b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a410 x19: x19 x20: x20
STACK CFI a418 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a41c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a444 x19: x19 x20: x20
STACK CFI a450 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a454 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a460 4c .cfa: sp 0 + .ra: x30
STACK CFI a464 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a46c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a480 x21: .cfa -16 + ^
STACK CFI a4a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a4b0 bc .cfa: sp 0 + .ra: x30
STACK CFI a4b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a4c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a4d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a4e4 x23: .cfa -16 + ^
STACK CFI a53c x23: x23
STACK CFI a54c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a550 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a564 x23: x23
STACK CFI a568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a570 38 .cfa: sp 0 + .ra: x30
STACK CFI a574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a57c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a5a8 50 .cfa: sp 0 + .ra: x30
STACK CFI a5ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a5b4 x19: .cfa -16 + ^
STACK CFI a5dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a5e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a5f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a5f8 4c .cfa: sp 0 + .ra: x30
STACK CFI a5fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a604 x19: .cfa -32 + ^
STACK CFI a63c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a640 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT a648 d8 .cfa: sp 0 + .ra: x30
STACK CFI a64c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a654 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a720 3c .cfa: sp 0 + .ra: x30
STACK CFI a724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a72c x19: .cfa -16 + ^
STACK CFI a748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a74c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a768 a0 .cfa: sp 0 + .ra: x30
STACK CFI a76c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a774 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a798 x21: .cfa -16 + ^
STACK CFI a7c8 x21: x21
STACK CFI a7d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a7d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a7ec x21: x21
STACK CFI a7f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a7fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a800 x21: x21
STACK CFI INIT a808 5c .cfa: sp 0 + .ra: x30
STACK CFI a80c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a81c x19: .cfa -16 + ^
STACK CFI a850 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a854 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a868 9c .cfa: sp 0 + .ra: x30
STACK CFI a86c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a878 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a894 x21: .cfa -16 + ^
STACK CFI a8ec x21: x21
STACK CFI a8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a8f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a8fc x21: x21
STACK CFI INIT a908 b0 .cfa: sp 0 + .ra: x30
STACK CFI a90c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a918 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a924 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a94c x23: .cfa -16 + ^
STACK CFI a994 x23: x23
STACK CFI a9a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a9a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a9b0 x23: x23
STACK CFI INIT a9b8 184 .cfa: sp 0 + .ra: x30
STACK CFI a9bc .cfa: sp 512 +
STACK CFI a9c4 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI a9cc x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI a9d8 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI a9e4 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI a9fc x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI aa1c x27: .cfa -432 + ^
STACK CFI ab14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ab18 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x29: .cfa -512 + ^
STACK CFI INIT ab40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab50 30 .cfa: sp 0 + .ra: x30
STACK CFI ab58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab60 x19: .cfa -16 + ^
STACK CFI ab78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab80 128 .cfa: sp 0 + .ra: x30
STACK CFI ab84 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI ab90 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI abd0 x21: .cfa -160 + ^
STACK CFI ac00 x21: x21
STACK CFI ac34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac38 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI ac3c x21: .cfa -160 + ^
STACK CFI INIT aca8 1a0 .cfa: sp 0 + .ra: x30
STACK CFI acac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ad58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ad80 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
