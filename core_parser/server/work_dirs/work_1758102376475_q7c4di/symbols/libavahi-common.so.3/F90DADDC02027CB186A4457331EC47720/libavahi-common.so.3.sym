MODULE Linux arm64 F90DADDC02027CB186A4457331EC47720 libavahi-common.so.3
INFO CODE_ID DCAD0DF90202B17C86A4457331EC47725BE0DF41
PUBLIC 2f50 0 avahi_malloc
PUBLIC 2fc0 0 avahi_malloc0
PUBLIC 3080 0 avahi_free
PUBLIC 30d8 0 avahi_realloc
PUBLIC 3148 0 avahi_strdup
PUBLIC 31c0 0 avahi_strndup
PUBLIC 3280 0 avahi_set_allocator
PUBLIC 3290 0 avahi_strdup_vprintf
PUBLIC 33a8 0 avahi_strdup_printf
PUBLIC 3448 0 avahi_memdup
PUBLIC 34b8 0 avahi_address_cmp
PUBLIC 3548 0 avahi_reverse_lookup_name
PUBLIC 37c8 0 avahi_proto_to_af
PUBLIC 3820 0 avahi_address_snprint
PUBLIC 38e8 0 avahi_address_parse
PUBLIC 39f8 0 avahi_af_to_proto
PUBLIC 3a58 0 avahi_proto_to_string
PUBLIC 3b30 0 avahi_alternative_host_name
PUBLIC 3d80 0 avahi_alternative_service_name
PUBLIC 3f58 0 avahi_strerror
PUBLIC 3fd0 0 avahi_string_list_add_anonymous
PUBLIC 4008 0 avahi_string_list_add_arbitrary
PUBLIC 4098 0 avahi_string_list_add
PUBLIC 40f8 0 avahi_string_list_free
PUBLIC 4130 0 avahi_string_list_parse
PUBLIC 4230 0 avahi_string_list_reverse
PUBLIC 4258 0 avahi_string_list_to_string
PUBLIC 4398 0 avahi_string_list_serialize
PUBLIC 44e8 0 avahi_string_list_equal
PUBLIC 4598 0 avahi_string_list_add_many_va
PUBLIC 4618 0 avahi_string_list_add_many
PUBLIC 4690 0 avahi_string_list_new
PUBLIC 4728 0 avahi_string_list_new_va
PUBLIC 4758 0 avahi_string_list_copy
PUBLIC 47c0 0 avahi_string_list_new_from_array
PUBLIC 4848 0 avahi_string_list_length
PUBLIC 4868 0 avahi_string_list_add_vprintf
PUBLIC 49b0 0 avahi_string_list_add_printf
PUBLIC 4a48 0 avahi_string_list_find
PUBLIC 4af0 0 avahi_string_list_add_pair
PUBLIC 4b40 0 avahi_string_list_add_pair_arbitrary
PUBLIC 4c20 0 avahi_string_list_get_pair
PUBLIC 4d68 0 avahi_string_list_get_next
PUBLIC 4da0 0 avahi_string_list_get_text
PUBLIC 4dd8 0 avahi_string_list_get_size
PUBLIC 4e10 0 avahi_string_list_get_service_cookie
PUBLIC 4eb0 0 avahi_unescape_label
PUBLIC 50a0 0 avahi_escape_label
PUBLIC 5330 0 avahi_normalize_name
PUBLIC 5510 0 avahi_normalize_name_strdup
PUBLIC 55a8 0 avahi_domain_equal
PUBLIC 5740 0 avahi_is_valid_service_type_generic
PUBLIC 5838 0 avahi_is_valid_service_type_strict
PUBLIC 5978 0 avahi_get_type_from_subtype
PUBLIC 5b10 0 avahi_is_valid_service_subtype
PUBLIC 5b58 0 avahi_is_valid_domain_name
PUBLIC 5c48 0 avahi_is_valid_service_name
PUBLIC 5ca8 0 avahi_is_valid_host_name
PUBLIC 5d68 0 avahi_domain_hash
PUBLIC 5e60 0 avahi_service_name_join
PUBLIC 6020 0 avahi_service_name_split
PUBLIC 6360 0 avahi_is_valid_fqdn
PUBLIC 6498 0 avahi_timeval_compare
PUBLIC 6528 0 avahi_timeval_diff
PUBLIC 65f0 0 avahi_timeval_add
PUBLIC 6680 0 avahi_age
PUBLIC 66e0 0 avahi_elapse_time
PUBLIC 6c10 0 avahi_simple_poll_wakeup
PUBLIC 7170 0 avahi_simple_poll_free
PUBLIC 7248 0 avahi_simple_poll_prepare
PUBLIC 7518 0 avahi_simple_poll_run
PUBLIC 75f8 0 avahi_simple_poll_dispatch
PUBLIC 7770 0 avahi_simple_poll_iterate
PUBLIC 77b0 0 avahi_simple_poll_quit
PUBLIC 77f0 0 avahi_simple_poll_get
PUBLIC 7828 0 avahi_simple_poll_set_func
PUBLIC 7878 0 avahi_simple_poll_new
PUBLIC 7950 0 avahi_simple_poll_loop
PUBLIC 7a90 0 avahi_threaded_poll_new
PUBLIC 7b10 0 avahi_threaded_poll_get
PUBLIC 7b50 0 avahi_threaded_poll_start
PUBLIC 7bf0 0 avahi_threaded_poll_stop
PUBLIC 7ca8 0 avahi_threaded_poll_free
PUBLIC 7d50 0 avahi_threaded_poll_quit
PUBLIC 7dd0 0 avahi_threaded_poll_lock
PUBLIC 7e58 0 avahi_threaded_poll_unlock
PUBLIC 7ee0 0 avahi_rlist_prepend
PUBLIC 7f20 0 avahi_rlist_remove
PUBLIC 7fb8 0 avahi_rlist_remove_by_link
PUBLIC 8078 0 avahi_utf8_valid
PUBLIC 8180 0 avahi_init_i18n
STACK CFI INIT 2e48 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e78 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ec4 x19: .cfa -16 + ^
STACK CFI 2efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f08 48 .cfa: sp 0 + .ra: x30
STACK CFI 2f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f18 x19: .cfa -16 + ^
STACK CFI INIT 2f50 6c .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2fc0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fcc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3000 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 302c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3030 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3044 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3080 54 .cfa: sp 0 + .ra: x30
STACK CFI 30ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 30d8 6c .cfa: sp 0 + .ra: x30
STACK CFI 30dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3104 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3114 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3148 78 .cfa: sp 0 + .ra: x30
STACK CFI 314c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3154 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3160 x21: .cfa -16 + ^
STACK CFI 3190 x21: x21
STACK CFI 3194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3198 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 31a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 31b8 x21: x21
STACK CFI 31bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 31c0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 31c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31cc x21: .cfa -16 + ^
STACK CFI 31d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3230 x19: x19 x20: x20
STACK CFI 323c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3240 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3248 x19: x19 x20: x20
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3254 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 325c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3280 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3290 114 .cfa: sp 0 + .ra: x30
STACK CFI 3294 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 329c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 32a0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 32c4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 334c x23: x23 x24: x24
STACK CFI 3358 x19: x19 x20: x20
STACK CFI 335c x21: x21 x22: x22
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3364 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 336c x19: x19 x20: x20
STACK CFI 3370 x21: x21 x22: x22
STACK CFI 3374 x23: x23 x24: x24
STACK CFI 3378 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 337c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 33a0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI INIT 33a8 9c .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 341c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3420 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 3448 70 .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3458 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3494 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 34b8 90 .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3504 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3548 27c .cfa: sp 0 + .ra: x30
STACK CFI 354c .cfa: sp 288 +
STACK CFI 3550 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3558 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3578 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36d8 x21: x21 x22: x22
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36e0 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3728 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 374c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3750 x21: x21 x22: x22
STACK CFI 3774 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3778 x21: x21 x22: x22
STACK CFI 379c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 37c8 58 .cfa: sp 0 + .ra: x30
STACK CFI 37f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3820 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3830 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3878 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 38e8 10c .cfa: sp 0 + .ra: x30
STACK CFI 38ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 394c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 397c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 3a2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3a58 64 .cfa: sp 0 + .ra: x30
STACK CFI 3a94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ac0 70 .cfa: sp 0 + .ra: x30
STACK CFI 3ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3acc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b30 250 .cfa: sp 0 + .ra: x30
STACK CFI 3b34 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 3b3c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 3b64 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 3c44 x21: x21 x22: x22
STACK CFI 3c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c6c .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x29: .cfa -416 + ^
STACK CFI 3c70 x21: x21 x22: x22
STACK CFI 3c78 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 3d28 x21: x21 x22: x22
STACK CFI 3d50 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 3d78 x21: x21 x22: x22
STACK CFI 3d7c x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI INIT 3d80 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 3d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3da0 x21: .cfa -16 + ^
STACK CFI 3e40 x21: x21
STACK CFI 3e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3ee0 x21: x21
STACK CFI 3ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f04 x21: x21
STACK CFI 3f2c x21: .cfa -16 + ^
STACK CFI INIT 3f58 78 .cfa: sp 0 + .ra: x30
STACK CFI 3f5c .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 3f70 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 3fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fb0 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x29: .cfa -464 + ^
STACK CFI 3fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3fd0 38 .cfa: sp 0 + .ra: x30
STACK CFI 3fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4008 90 .cfa: sp 0 + .ra: x30
STACK CFI 400c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4020 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4054 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4078 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4098 5c .cfa: sp 0 + .ra: x30
STACK CFI 409c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 40f8 38 .cfa: sp 0 + .ra: x30
STACK CFI 4100 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4108 x19: .cfa -16 + ^
STACK CFI 4128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4130 fc .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4144 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 41c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 41e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4230 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4258 13c .cfa: sp 0 + .ra: x30
STACK CFI 425c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 426c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 42ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 42c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 42cc x25: .cfa -16 + ^
STACK CFI 4340 x21: x21 x22: x22
STACK CFI 4344 x25: x25
STACK CFI 4350 x23: x23 x24: x24
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4358 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4360 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4364 x23: x23 x24: x24
STACK CFI 4388 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 438c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4390 x25: .cfa -16 + ^
STACK CFI INIT 4398 150 .cfa: sp 0 + .ra: x30
STACK CFI 439c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43b8 x25: .cfa -16 + ^
STACK CFI 445c x19: x19 x20: x20
STACK CFI 4460 x23: x23 x24: x24
STACK CFI 4464 x25: x25
STACK CFI 4470 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4474 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 44bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 44c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 44c8 x19: x19 x20: x20
STACK CFI 44d0 x23: x23 x24: x24
STACK CFI 44d4 x25: x25
STACK CFI 44d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 44dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 44e8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 44ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4598 80 .cfa: sp 0 + .ra: x30
STACK CFI 459c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4608 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4618 74 .cfa: sp 0 + .ra: x30
STACK CFI 461c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4688 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4690 94 .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4714 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4718 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4728 30 .cfa: sp 0 + .ra: x30
STACK CFI 4730 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4754 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4758 68 .cfa: sp 0 + .ra: x30
STACK CFI 4760 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 476c x19: .cfa -16 + ^
STACK CFI 47a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4824 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4848 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4868 148 .cfa: sp 0 + .ra: x30
STACK CFI 486c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4874 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4878 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 487c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 48a4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4938 x25: x25 x26: x26
STACK CFI 4940 x19: x19 x20: x20
STACK CFI 4944 x21: x21 x22: x22
STACK CFI 4948 x23: x23 x24: x24
STACK CFI 494c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4950 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 496c x19: x19 x20: x20
STACK CFI 4970 x21: x21 x22: x22
STACK CFI 4974 x23: x23 x24: x24
STACK CFI 4978 x25: x25 x26: x26
STACK CFI 497c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4980 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 49ac x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 49b0 98 .cfa: sp 0 + .ra: x30
STACK CFI 49b4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a24 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI INIT 4a48 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4a4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a58 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4acc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4af0 4c .cfa: sp 0 + .ra: x30
STACK CFI 4b14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b40 dc .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b6c x23: .cfa -16 + ^
STACK CFI 4bd0 x19: x19 x20: x20
STACK CFI 4bd4 x21: x21 x22: x22
STACK CFI 4bd8 x23: x23
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4be0 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4be4 x21: x21 x22: x22
STACK CFI 4be8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4bec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4c14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c18 x23: .cfa -16 + ^
STACK CFI INIT 4c20 144 .cfa: sp 0 + .ra: x30
STACK CFI 4c24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c38 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4cd0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4cec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4d2c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4d68 38 .cfa: sp 0 + .ra: x30
STACK CFI 4d78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4da0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4db0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4dd8 38 .cfa: sp 0 + .ra: x30
STACK CFI 4de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4e10 9c .cfa: sp 0 + .ra: x30
STACK CFI 4e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e24 x19: .cfa -32 + ^
STACK CFI 4e88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 4e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ea0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4eb0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 4eb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4ecc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 4f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 4f8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 503c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5040 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 50a0 290 .cfa: sp 0 + .ra: x30
STACK CFI 50a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 526c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5270 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5330 1dc .cfa: sp 0 + .ra: x30
STACK CFI 5334 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 533c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5348 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5384 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5390 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5418 x19: x19 x20: x20
STACK CFI 5420 x21: x21 x22: x22
STACK CFI 5444 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5448 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 544c x19: x19 x20: x20
STACK CFI 5450 x21: x21 x22: x22
STACK CFI 5458 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5474 x19: x19 x20: x20
STACK CFI 5478 x21: x21 x22: x22
STACK CFI 5480 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5484 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5488 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 54ac x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 54b0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 54b4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 54d8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 54dc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 54e0 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5504 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5508 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT 5510 94 .cfa: sp 0 + .ra: x30
STACK CFI 5514 .cfa: sp 1056 +
STACK CFI 5518 .ra: .cfa -1048 + ^ x29: .cfa -1056 + ^
STACK CFI 5520 x19: .cfa -1040 + ^ x20: .cfa -1032 + ^
STACK CFI 5578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 557c .cfa: sp 1056 + .ra: .cfa -1048 + ^ x19: .cfa -1040 + ^ x20: .cfa -1032 + ^ x29: .cfa -1056 + ^
STACK CFI INIT 55a8 194 .cfa: sp 0 + .ra: x30
STACK CFI 55ac .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 55b4 x23: .cfa -176 + ^
STACK CFI 55e0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 55ec x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5650 x19: x19 x20: x20
STACK CFI 5654 x21: x21 x22: x22
STACK CFI 5658 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 565c x19: x19 x20: x20
STACK CFI 5664 x21: x21 x22: x22
STACK CFI 5684 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 5688 .cfa: sp 224 + .ra: .cfa -216 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI 5690 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 56d8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 56dc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 56e0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 56e4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5708 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 570c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5710 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5734 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 5738 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 5740 f8 .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 574c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 578c x21: .cfa -112 + ^
STACK CFI 57d4 x21: x21
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57fc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x29: .cfa -144 + ^
STACK CFI 5804 x21: x21
STACK CFI 580c x21: .cfa -112 + ^
STACK CFI 5810 x21: x21
STACK CFI 5834 x21: .cfa -112 + ^
STACK CFI INIT 5838 13c .cfa: sp 0 + .ra: x30
STACK CFI 583c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5844 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 589c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58a0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 58a8 x21: .cfa -112 + ^
STACK CFI 58ec x21: x21
STACK CFI 58f0 x21: .cfa -112 + ^
STACK CFI 5934 x21: x21
STACK CFI 5968 x21: .cfa -112 + ^
STACK CFI 596c x21: x21
STACK CFI 5970 x21: .cfa -112 + ^
STACK CFI INIT 5978 198 .cfa: sp 0 + .ra: x30
STACK CFI 597c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5984 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5990 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ae8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5b10 44 .cfa: sp 0 + .ra: x30
STACK CFI 5b14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5b58 f0 .cfa: sp 0 + .ra: x30
STACK CFI 5b5c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5b64 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5b70 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bfc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5c48 60 .cfa: sp 0 + .ra: x30
STACK CFI 5c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c54 x19: .cfa -16 + ^
STACK CFI 5c80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ca8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5cac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5cb4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5d68 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5d6c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5d74 x23: .cfa -112 + ^
STACK CFI 5d94 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5d9c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5dfc x21: x21 x22: x22
STACK CFI 5e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 5e28 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI 5e30 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5e54 x21: x21 x22: x22
STACK CFI 5e58 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 5e60 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 5e64 .cfa: sp 2416 +
STACK CFI 5e68 .ra: .cfa -2392 + ^ x29: .cfa -2400 + ^
STACK CFI 5e70 x19: .cfa -2384 + ^ x20: .cfa -2376 + ^
STACK CFI 5e7c x21: .cfa -2368 + ^ x22: .cfa -2360 + ^
STACK CFI 5e90 x23: .cfa -2352 + ^ x24: .cfa -2344 + ^
STACK CFI 5ed0 x25: .cfa -2336 + ^
STACK CFI 5f68 x25: x25
STACK CFI 5f94 x23: x23 x24: x24
STACK CFI 5f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f9c .cfa: sp 2416 + .ra: .cfa -2392 + ^ x19: .cfa -2384 + ^ x20: .cfa -2376 + ^ x21: .cfa -2368 + ^ x22: .cfa -2360 + ^ x23: .cfa -2352 + ^ x24: .cfa -2344 + ^ x25: .cfa -2336 + ^ x29: .cfa -2400 + ^
STACK CFI 5fb0 x25: x25
STACK CFI 5fb8 x25: .cfa -2336 + ^
STACK CFI 5fbc x25: x25
STACK CFI 5ff0 x25: .cfa -2336 + ^
STACK CFI 5ff4 x25: x25
STACK CFI 5ff8 x25: .cfa -2336 + ^
STACK CFI INIT 6020 340 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 602c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 603c x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 61a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 61ac .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 6360 138 .cfa: sp 0 + .ra: x30
STACK CFI 6364 .cfa: sp 1168 +
STACK CFI 6368 .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI 6370 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 63c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63c8 .cfa: sp 1168 + .ra: .cfa -1160 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x29: .cfa -1168 + ^
STACK CFI 63d8 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 645c x21: x21 x22: x22
STACK CFI 6460 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 6464 x21: x21 x22: x22
STACK CFI 648c x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 6490 x21: x21 x22: x22
STACK CFI 6494 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI INIT 6498 90 .cfa: sp 0 + .ra: x30
STACK CFI 649c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 64e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 64e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6528 c4 .cfa: sp 0 + .ra: x30
STACK CFI 652c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6538 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 65a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 65a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 65f0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6654 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6680 60 .cfa: sp 0 + .ra: x30
STACK CFI 6684 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 668c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 66e0 11c .cfa: sp 0 + .ra: x30
STACK CFI 66e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 66ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 66f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6718 x19: x19 x20: x20
STACK CFI 671c x21: x21 x22: x22
STACK CFI 6720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 673c x23: .cfa -16 + ^
STACK CFI 67b0 x19: x19 x20: x20
STACK CFI 67b4 x21: x21 x22: x22
STACK CFI 67b8 x23: x23
STACK CFI 67bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 67d4 x23: x23
STACK CFI 67f8 x23: .cfa -16 + ^
STACK CFI INIT 6800 98 .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6834 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6838 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6854 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6898 c8 .cfa: sp 0 + .ra: x30
STACK CFI 689c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6900 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6968 98 .cfa: sp 0 + .ra: x30
STACK CFI 696c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 69a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 69a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 69ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 69b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6a00 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6a04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6aa0 2c .cfa: sp 0 + .ra: x30
STACK CFI 6aa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ad0 2c .cfa: sp 0 + .ra: x30
STACK CFI 6ad4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6b00 7c .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b0c x19: .cfa -16 + ^
STACK CFI 6b34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b80 90 .cfa: sp 0 + .ra: x30
STACK CFI 6b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6c10 64 .cfa: sp 0 + .ra: x30
STACK CFI 6c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c20 x19: .cfa -32 + ^
STACK CFI 6c4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6c78 94 .cfa: sp 0 + .ra: x30
STACK CFI 6c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d10 88 .cfa: sp 0 + .ra: x30
STACK CFI 6d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d1c x19: .cfa -16 + ^
STACK CFI 6d4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d98 108 .cfa: sp 0 + .ra: x30
STACK CFI 6d9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6dac x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6e28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6ea0 dc .cfa: sp 0 + .ra: x30
STACK CFI 6ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6eac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6f80 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f8c x19: .cfa -16 + ^
STACK CFI 6fe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7030 13c .cfa: sp 0 + .ra: x30
STACK CFI 7034 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7044 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 70d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 70dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7170 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 717c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7248 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 724c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7254 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7264 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 72d4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 73d4 x23: x23 x24: x24
STACK CFI 73fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7400 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 7464 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7498 x23: x23 x24: x24
STACK CFI 749c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 74c0 x23: x23 x24: x24
STACK CFI 74e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 74e8 x23: x23 x24: x24
STACK CFI 74ec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 74f0 x23: x23 x24: x24
STACK CFI 7514 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 7518 e0 .cfa: sp 0 + .ra: x30
STACK CFI 751c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7524 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 75ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 75f8 178 .cfa: sp 0 + .ra: x30
STACK CFI 75fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7604 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 769c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 76b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 76dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7770 40 .cfa: sp 0 + .ra: x30
STACK CFI 7774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 777c x19: .cfa -16 + ^
STACK CFI 7790 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7794 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 77ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 77b0 3c .cfa: sp 0 + .ra: x30
STACK CFI 77c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 77f0 34 .cfa: sp 0 + .ra: x30
STACK CFI 77fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7828 50 .cfa: sp 0 + .ra: x30
STACK CFI 7850 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7878 d8 .cfa: sp 0 + .ra: x30
STACK CFI 787c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7888 x19: .cfa -16 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7934 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 794c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7950 78 .cfa: sp 0 + .ra: x30
STACK CFI 7954 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 795c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 79c8 5c .cfa: sp 0 + .ra: x30
STACK CFI 79cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7a28 64 .cfa: sp 0 + .ra: x30
STACK CFI 7a2c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7a34 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7a44 x21: .cfa -144 + ^
STACK CFI 7a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7a90 7c .cfa: sp 0 + .ra: x30
STACK CFI 7a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7aa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7b10 3c .cfa: sp 0 + .ra: x30
STACK CFI 7b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b50 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7b54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ba0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7bf0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7bfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ca8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7cac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7cb4 x19: .cfa -16 + ^
STACK CFI 7ce4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7d50 7c .cfa: sp 0 + .ra: x30
STACK CFI 7d54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d5c x19: .cfa -16 + ^
STACK CFI 7d80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7dd0 84 .cfa: sp 0 + .ra: x30
STACK CFI 7dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ddc x19: .cfa -16 + ^
STACK CFI 7e08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7e58 84 .cfa: sp 0 + .ra: x30
STACK CFI 7e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e64 x19: .cfa -16 + ^
STACK CFI 7e90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ee0 40 .cfa: sp 0 + .ra: x30
STACK CFI 7ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7eec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f20 98 .cfa: sp 0 + .ra: x30
STACK CFI 7f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f2c x19: .cfa -16 + ^
STACK CFI 7f84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7fb8 bc .cfa: sp 0 + .ra: x30
STACK CFI 7fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fc4 x19: .cfa -16 + ^
STACK CFI 8004 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8008 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8028 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 802c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8078 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8180 60 .cfa: sp 0 + .ra: x30
STACK CFI 8184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 818c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 81a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 81a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 81dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
