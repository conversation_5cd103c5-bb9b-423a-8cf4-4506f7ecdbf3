MODULE Linux arm64 62D1A146ECF3B9495A10DBDD961783C40 libbabeltrace-ctf-text.so.1
INFO CODE_ID 46A1D162F3EC49B95A10DBDD961783C400CED652
PUBLIC 3be0 0 bt_ctf_text_hook
STACK CFI INIT 28d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2908 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2948 48 .cfa: sp 0 + .ra: x30
STACK CFI 294c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2954 x19: .cfa -16 + ^
STACK CFI 298c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2998 70 .cfa: sp 0 + .ra: x30
STACK CFI 299c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29bc x19: .cfa -16 + ^
STACK CFI 29f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a08 10c .cfa: sp 0 + .ra: x30
STACK CFI 2a0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2b18 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bb8 28 .cfa: sp 0 + .ra: x30
STACK CFI 2bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2be0 1000 .cfa: sp 0 + .ra: x30
STACK CFI 2be4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2bec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2bf8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c44 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2dd8 x27: .cfa -16 + ^
STACK CFI 2e54 x27: x27
STACK CFI 3150 x23: x23 x24: x24
STACK CFI 3158 x25: x25 x26: x26
STACK CFI 3168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 316c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 33b0 x27: .cfa -16 + ^
STACK CFI 3428 x27: x27
STACK CFI 3550 x23: x23 x24: x24
STACK CFI 3554 x25: x25 x26: x26
STACK CFI 3564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3568 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 3900 x27: .cfa -16 + ^
STACK CFI 3920 x27: x27
STACK CFI 3944 x27: .cfa -16 + ^
STACK CFI 394c x27: x27
STACK CFI 3ac0 x27: .cfa -16 + ^
STACK CFI 3adc x27: x27
STACK CFI 3b18 x27: .cfa -16 + ^
STACK CFI 3b2c x27: x27
STACK CFI 3b58 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3b7c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3ba0 x23: x23 x24: x24
STACK CFI 3ba4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3bc8 x23: x23 x24: x24
STACK CFI 3bcc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3bd0 x27: .cfa -16 + ^
STACK CFI 3bd4 x27: x27
STACK CFI INIT 27d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 27d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e4 x19: .cfa -16 + ^
STACK CFI 2860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3be0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3be8 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2868 5c .cfa: sp 0 + .ra: x30
STACK CFI 286c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 27c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c60 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 3c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e48 23c .cfa: sp 0 + .ra: x30
STACK CFI 3e4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3e54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3e60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3e68 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3e8c x25: .cfa -16 + ^
STACK CFI 3ffc x25: x25
STACK CFI 4010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4014 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 402c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4030 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4088 b8 .cfa: sp 0 + .ra: x30
STACK CFI 408c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4094 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40dc x21: .cfa -16 + ^
STACK CFI 4108 x21: x21
STACK CFI 412c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4130 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4140 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 4144 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 414c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4160 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4214 x25: .cfa -16 + ^
STACK CFI 42a8 x25: x25
STACK CFI 4378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 437c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 43a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 45ec x25: .cfa -16 + ^
STACK CFI 45f0 x25: x25
STACK CFI INIT 45f8 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 45fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4604 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4610 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 46b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 47e0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 47e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 483c x21: .cfa -16 + ^
STACK CFI 4868 x21: x21
STACK CFI 488c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 48c0 x21: .cfa -16 + ^
STACK CFI INIT 48c8 120 .cfa: sp 0 + .ra: x30
STACK CFI 48cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 48d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 48e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 49a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 49ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 49e8 114 .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 49f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a00 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b00 40 .cfa: sp 0 + .ra: x30
STACK CFI 4b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b10 x19: .cfa -16 + ^
STACK CFI 4b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b40 124 .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4b4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4b54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b9c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4bb0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4bbc x25: .cfa -16 + ^
STACK CFI 4c2c x23: x23 x24: x24
STACK CFI 4c30 x25: x25
STACK CFI 4c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c48 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4c54 x23: x23 x24: x24
STACK CFI 4c58 x25: x25
STACK CFI INIT 4c68 40 .cfa: sp 0 + .ra: x30
STACK CFI 4c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c74 x19: .cfa -16 + ^
STACK CFI 4ca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ca8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4cb8 200 .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4cc8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ce8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4cf8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d04 x25: .cfa -16 + ^
STACK CFI 4d24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d74 x21: x21 x22: x22
STACK CFI 4d78 x23: x23 x24: x24
STACK CFI 4d7c x25: x25
STACK CFI 4d80 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 4d84 x21: x21 x22: x22
STACK CFI 4d88 x25: x25
STACK CFI 4d8c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4e60 x21: x21 x22: x22
STACK CFI 4e64 x23: x23 x24: x24
STACK CFI 4e68 x25: x25
STACK CFI 4e6c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4eac x21: x21 x22: x22
STACK CFI 4eb0 x23: x23 x24: x24
STACK CFI 4eb4 x25: x25
STACK CFI INIT 4eb8 230 .cfa: sp 0 + .ra: x30
STACK CFI 4ebc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4ec4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4ed0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4ee8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4efc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4f2c x19: x19 x20: x20
STACK CFI 4f54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4f58 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 4f6c x27: .cfa -96 + ^
STACK CFI 5064 x27: x27
STACK CFI 5068 x27: .cfa -96 + ^
STACK CFI 507c x19: x19 x20: x20
STACK CFI 5080 x27: x27
STACK CFI 508c x19: .cfa -160 + ^ x20: .cfa -152 + ^ x27: .cfa -96 + ^
STACK CFI 50d0 x19: x19 x20: x20 x27: x27
STACK CFI 50d4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 50d8 x27: .cfa -96 + ^
STACK CFI 50dc x27: x27
STACK CFI 50e4 x19: x19 x20: x20
STACK CFI INIT 50e8 cc .cfa: sp 0 + .ra: x30
STACK CFI 50ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50f8 x19: .cfa -16 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5198 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51b8 34 .cfa: sp 0 + .ra: x30
STACK CFI 51c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51c8 x19: .cfa -16 + ^
STACK CFI 51e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51f0 428 .cfa: sp 0 + .ra: x30
STACK CFI 51f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5200 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 520c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 522c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 52e8 x21: x21 x22: x22
STACK CFI 530c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5310 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 5314 x21: x21 x22: x22
STACK CFI 5318 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 53e0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5480 x25: x25 x26: x26
STACK CFI 5514 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 55a0 x25: x25 x26: x26
STACK CFI 55a4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 560c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 5610 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5614 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 5618 f8 .cfa: sp 0 + .ra: x30
STACK CFI 561c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5634 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5674 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5710 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5714 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 571c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 573c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5744 x23: .cfa -48 + ^
STACK CFI 57a0 x19: x19 x20: x20
STACK CFI 57a4 x23: x23
STACK CFI 57c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 57c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 57c8 x19: x19 x20: x20
STACK CFI 57cc x23: x23
STACK CFI 57dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 57e0 x23: .cfa -48 + ^
STACK CFI INIT 57e8 98 .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 57f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5804 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 587c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5880 344 .cfa: sp 0 + .ra: x30
STACK CFI 5884 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 588c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 58b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 58bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 58c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 59e0 x21: x21 x22: x22
STACK CFI 59e4 x23: x23 x24: x24
STACK CFI 59e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5a54 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5ae8 x21: x21 x22: x22
STACK CFI 5aec x23: x23 x24: x24
STACK CFI 5af0 x25: x25 x26: x26
STACK CFI 5af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5af8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5b80 x25: x25 x26: x26
STACK CFI 5b84 x21: x21 x22: x22
STACK CFI 5b88 x23: x23 x24: x24
STACK CFI 5b90 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5ba0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5bb8 x21: x21 x22: x22
STACK CFI 5bbc x23: x23 x24: x24
STACK CFI 5bc0 x25: x25 x26: x26
STACK CFI INIT 5bc8 60 .cfa: sp 0 + .ra: x30
STACK CFI 5bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bd4 x19: .cfa -16 + ^
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c28 28 .cfa: sp 0 + .ra: x30
STACK CFI 5c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c34 x19: .cfa -16 + ^
STACK CFI 5c4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c50 7c .cfa: sp 0 + .ra: x30
STACK CFI 5c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5cd0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5cd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5cec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5cf8 x23: .cfa -16 + ^
STACK CFI 5d48 x23: x23
STACK CFI 5d5c x21: x21 x22: x22
STACK CFI 5d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5d8c x21: x21 x22: x22
STACK CFI 5d90 x23: x23
STACK CFI 5d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5d9c x21: x21 x22: x22
STACK CFI 5da0 x23: x23
STACK CFI 5db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5db8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5dc8 78 .cfa: sp 0 + .ra: x30
STACK CFI 5dd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5de0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5df0 x21: .cfa -16 + ^
STACK CFI 5e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5e30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5e40 58 .cfa: sp 0 + .ra: x30
STACK CFI 5e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ea8 608 .cfa: sp 0 + .ra: x30
STACK CFI 5eac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5eb8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5ec4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5ee4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5f18 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5f44 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6034 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 6038 x19: x19 x20: x20
STACK CFI 605c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6060 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6074 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 608c x21: x21 x22: x22
STACK CFI 6098 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 60dc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 617c x27: x27 x28: x28
STACK CFI 61ac x19: x19 x20: x20
STACK CFI 61b0 x21: x21 x22: x22
STACK CFI 61b8 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6224 x27: x27 x28: x28
STACK CFI 6228 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 622c x27: x27 x28: x28
STACK CFI 6244 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 626c x27: x27 x28: x28
STACK CFI 62bc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 62c8 x27: x27 x28: x28
STACK CFI 6318 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 635c x27: x27 x28: x28
STACK CFI 63cc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 63dc x27: x27 x28: x28
STACK CFI 63e0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 63ec x27: x27 x28: x28
STACK CFI 6410 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6460 x27: x27 x28: x28
STACK CFI 6464 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6488 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 648c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6490 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6494 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 64a4 x27: x27 x28: x28
STACK CFI 64a8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 64b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 653c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6568 324 .cfa: sp 0 + .ra: x30
STACK CFI 656c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6578 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 659c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 65f0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 65fc x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 671c x25: x25 x26: x26
STACK CFI 6720 x27: x27 x28: x28
STACK CFI 6760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6764 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 6784 x25: x25 x26: x26
STACK CFI 6788 x27: x27 x28: x28
STACK CFI 67ac x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6878 x25: x25 x26: x26
STACK CFI 687c x27: x27 x28: x28
STACK CFI 6884 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6888 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 6890 28 .cfa: sp 0 + .ra: x30
STACK CFI 6894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 689c x19: .cfa -16 + ^
STACK CFI 68b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 68b8 38 .cfa: sp 0 + .ra: x30
STACK CFI 68c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68c8 x19: .cfa -16 + ^
STACK CFI 68e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 68f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68f8 88 .cfa: sp 0 + .ra: x30
STACK CFI 68fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6904 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6974 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6980 80 .cfa: sp 0 + .ra: x30
STACK CFI 6984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 698c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 69d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 69dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 69fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6a00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a10 9c .cfa: sp 0 + .ra: x30
STACK CFI 6a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a54 x19: x19 x20: x20
STACK CFI 6a68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6a8c x19: x19 x20: x20
STACK CFI 6a94 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6a9c x19: x19 x20: x20
STACK CFI INIT 6ab0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 6ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ac0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 6b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6b58 50 .cfa: sp 0 + .ra: x30
STACK CFI 6b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b74 x19: .cfa -16 + ^
STACK CFI 6b94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6ba0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ba8 54 .cfa: sp 0 + .ra: x30
STACK CFI 6bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6bc4 x19: .cfa -16 + ^
STACK CFI 6be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6c00 114 .cfa: sp 0 + .ra: x30
STACK CFI 6c04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6c0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c1c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6c30 x23: .cfa -48 + ^
STACK CFI 6cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6cf0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6d18 bc .cfa: sp 0 + .ra: x30
STACK CFI 6d1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6dbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6dd8 3c .cfa: sp 0 + .ra: x30
STACK CFI 6ddc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6de8 x19: .cfa -16 + ^
STACK CFI 6e10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e18 10c .cfa: sp 0 + .ra: x30
STACK CFI 6e20 .cfa: sp 8272 +
STACK CFI 6e24 .ra: .cfa -8264 + ^ x29: .cfa -8272 + ^
STACK CFI 6e2c x19: .cfa -8256 + ^ x20: .cfa -8248 + ^
STACK CFI 6e34 x21: .cfa -8240 + ^ x22: .cfa -8232 + ^
STACK CFI 6e5c x23: .cfa -8224 + ^ x24: .cfa -8216 + ^
STACK CFI 6edc x23: x23 x24: x24
STACK CFI 6f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6f10 .cfa: sp 8272 + .ra: .cfa -8264 + ^ x19: .cfa -8256 + ^ x20: .cfa -8248 + ^ x21: .cfa -8240 + ^ x22: .cfa -8232 + ^ x23: .cfa -8224 + ^ x24: .cfa -8216 + ^ x29: .cfa -8272 + ^
STACK CFI 6f14 x23: x23 x24: x24
STACK CFI 6f20 x23: .cfa -8224 + ^ x24: .cfa -8216 + ^
STACK CFI INIT 6f28 68 .cfa: sp 0 + .ra: x30
STACK CFI 6f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f34 x19: .cfa -16 + ^
STACK CFI 6f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6f8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
