MODULE Linux arm64 86EDD6971BFFF14DA148F48A16A7D2EB0 libgmock_main.so
INFO CODE_ID 97D6ED86FF1B4DF1A148F48A16A7D2EB
PUBLIC 7d8 0 _init
PUBLIC 890 0 main
PUBLIC 8e0 0 _GLOBAL__sub_I_gmock_main.cc
PUBLIC 91c 0 call_weak_fn
PUBLIC 930 0 deregister_tm_clones
PUBLIC 960 0 register_tm_clones
PUBLIC 99c 0 __do_global_dtors_aux
PUBLIC 9ec 0 frame_dummy
PUBLIC 9f0 0 _fini
STACK CFI INIT 930 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 960 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 99c 50 .cfa: sp 0 + .ra: x30
STACK CFI 9ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9b4 x19: .cfa -16 + ^
STACK CFI 9e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9ec 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 890 48 .cfa: sp 0 + .ra: x30
STACK CFI 894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ac x19: .cfa -32 + ^
STACK CFI 8d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8e0 3c .cfa: sp 0 + .ra: x30
STACK CFI 8e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ec x19: .cfa -16 + ^
STACK CFI 914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
