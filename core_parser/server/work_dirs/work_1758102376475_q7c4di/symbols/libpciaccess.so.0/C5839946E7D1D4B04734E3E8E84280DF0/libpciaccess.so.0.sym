MODULE Linux arm64 C5839946E7D1D4B04734E3E8E84280DF0 libpciaccess.so.0
INFO CODE_ID 469983C5D1E7B0D44734E3E8E84280DFD2FDF376
PUBLIC 23d8 0 pci_device_get_bridge_info
PUBLIC 2420 0 pci_device_get_pcmcia_bridge_info
PUBLIC 2460 0 pci_device_get_bridge_buses
PUBLIC 2568 0 pci_device_get_parent_bridge
PUBLIC 2640 0 pci_slot_match_iterator_create
PUBLIC 26b8 0 pci_id_match_iterator_create
PUBLIC 2730 0 pci_iterator_destroy
PUBLIC 2740 0 pci_device_next
PUBLIC 2928 0 pci_device_find_by_slot
PUBLIC 2988 0 pci_system_init
PUBLIC 2990 0 pci_system_init_dev_mem
PUBLIC 2998 0 pci_system_cleanup
PUBLIC 2ab8 0 pci_device_read_rom
PUBLIC 2ae8 0 pci_device_is_boot_vga
PUBLIC 2b10 0 pci_device_has_kernel_driver
PUBLIC 2b38 0 pci_device_probe
PUBLIC 2b60 0 pci_device_map_range
PUBLIC 2d90 0 pci_device_map_region
PUBLIC 2df0 0 pci_device_map_memory_range
PUBLIC 2e00 0 pci_device_unmap_range
PUBLIC 2f18 0 pci_device_unmap_region
PUBLIC 2f78 0 pci_device_unmap_memory_range
PUBLIC 2f80 0 pci_device_cfg_read
PUBLIC 3000 0 pci_device_cfg_read_u8
PUBLIC 3068 0 pci_device_cfg_read_u16
PUBLIC 30d0 0 pci_device_cfg_read_u32
PUBLIC 3138 0 pci_device_cfg_write
PUBLIC 31b8 0 pci_device_cfg_write_u8
PUBLIC 3228 0 pci_device_cfg_write_u16
PUBLIC 3298 0 pci_device_cfg_write_u32
PUBLIC 3308 0 pci_device_cfg_write_bits
PUBLIC 33a0 0 pci_device_enable
PUBLIC 33c8 0 pci_device_map_legacy
PUBLIC 3410 0 pci_device_unmap_legacy
PUBLIC 3440 0 pci_device_open_io
PUBLIC 3540 0 pci_legacy_open_io
PUBLIC 35e8 0 pci_device_close_io
PUBLIC 3630 0 pci_io_read32
PUBLIC 3668 0 pci_io_read16
PUBLIC 36a0 0 pci_io_read8
PUBLIC 36d8 0 pci_io_write32
PUBLIC 3708 0 pci_io_write16
PUBLIC 3740 0 pci_io_write8
PUBLIC 3990 0 pci_device_get_agp_info
PUBLIC 3f30 0 pci_get_strings
PUBLIC 3ff0 0 pci_device_get_device_name
PUBLIC 4058 0 pci_device_get_subdevice_name
PUBLIC 40d8 0 pci_device_get_vendor_name
PUBLIC 40e0 0 pci_device_get_subvendor_name
PUBLIC 4410 0 pci_device_vgaarb_init
PUBLIC 4518 0 pci_device_vgaarb_fini
PUBLIC 4538 0 pci_device_vgaarb_set_target
PUBLIC 4670 0 pci_device_vgaarb_decodes
PUBLIC 47c0 0 pci_device_vgaarb_lock
PUBLIC 48c0 0 pci_device_vgaarb_trylock
PUBLIC 49c0 0 pci_device_vgaarb_unlock
PUBLIC 4ac0 0 pci_device_vgaarb_get_info
STACK CFI INIT 1fb8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fe8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2028 48 .cfa: sp 0 + .ra: x30
STACK CFI 202c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2034 x19: .cfa -16 + ^
STACK CFI 206c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2070 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2078 360 .cfa: sp 0 + .ra: x30
STACK CFI 207c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2084 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2094 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 20e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20ec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 23d8 44 .cfa: sp 0 + .ra: x30
STACK CFI 23dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23e4 x19: .cfa -16 + ^
STACK CFI 2410 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2414 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2420 40 .cfa: sp 0 + .ra: x30
STACK CFI 2424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 242c x19: .cfa -16 + ^
STACK CFI 2454 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2458 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2460 108 .cfa: sp 0 + .ra: x30
STACK CFI 2474 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2484 x19: .cfa -48 + ^
STACK CFI 24b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 24b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI 2510 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 251c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2568 d4 .cfa: sp 0 + .ra: x30
STACK CFI 256c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 257c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 258c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2614 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2640 78 .cfa: sp 0 + .ra: x30
STACK CFI 2644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2654 x19: .cfa -16 + ^
STACK CFI 2694 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2698 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 26a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 26b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 26b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26cc x19: .cfa -16 + ^
STACK CFI 270c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2710 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2720 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 272c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2730 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2740 1e8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2928 5c .cfa: sp 0 + .ra: x30
STACK CFI 292c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 293c x19: .cfa -64 + ^
STACK CFI 297c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2980 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2988 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2998 120 .cfa: sp 0 + .ra: x30
STACK CFI 299c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 29d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a80 x19: x19 x20: x20
STACK CFI 2ab4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ab8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ae8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b10 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b38 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b60 22c .cfa: sp 0 + .ra: x30
STACK CFI 2b64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2b70 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2b80 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2b98 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2bd4 x19: x19 x20: x20
STACK CFI 2bdc x21: x21 x22: x22
STACK CFI 2be0 x23: x23 x24: x24
STACK CFI 2bec .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 2bf0 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2c00 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 2c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2c6c x19: x19 x20: x20
STACK CFI 2c70 x21: x21 x22: x22
STACK CFI 2c74 x23: x23 x24: x24
STACK CFI 2c7c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 2c80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2d10 x19: x19 x20: x20
STACK CFI 2d14 x21: x21 x22: x22
STACK CFI 2d18 x23: x23 x24: x24
STACK CFI 2d20 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 2d24 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2d6c x19: x19 x20: x20
STACK CFI 2d70 x21: x21 x22: x22
STACK CFI 2d74 x23: x23 x24: x24
STACK CFI 2d78 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2d80 x19: x19 x20: x20
STACK CFI 2d84 x21: x21 x22: x22
STACK CFI 2d88 x23: x23 x24: x24
STACK CFI INIT 2d90 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2df0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e00 118 .cfa: sp 0 + .ra: x30
STACK CFI 2e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2eb0 x19: x19 x20: x20
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2ec0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2ecc x19: x19 x20: x20
STACK CFI 2ed4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2ed8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2efc x19: x19 x20: x20
STACK CFI 2f0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2f10 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2f18 60 .cfa: sp 0 + .ra: x30
STACK CFI 2f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f3c x19: .cfa -16 + ^
STACK CFI 2f68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f80 80 .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f94 x19: .cfa -32 + ^
STACK CFI 2ff8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3000 64 .cfa: sp 0 + .ra: x30
STACK CFI 3004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3010 x19: .cfa -32 + ^
STACK CFI 305c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3068 64 .cfa: sp 0 + .ra: x30
STACK CFI 306c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3078 x19: .cfa -32 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 30d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 30d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 30e0 x19: .cfa -32 + ^
STACK CFI 312c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3130 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3138 80 .cfa: sp 0 + .ra: x30
STACK CFI 313c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 314c x19: .cfa -32 + ^
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 31b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31b8 6c .cfa: sp 0 + .ra: x30
STACK CFI 31bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 31c8 x19: .cfa -48 + ^
STACK CFI 321c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3220 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3228 6c .cfa: sp 0 + .ra: x30
STACK CFI 322c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3238 x19: .cfa -48 + ^
STACK CFI 328c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3290 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3298 70 .cfa: sp 0 + .ra: x30
STACK CFI 329c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 32ac x19: .cfa -48 + ^
STACK CFI 3300 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3304 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3308 94 .cfa: sp 0 + .ra: x30
STACK CFI 330c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3314 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3324 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 332c x23: .cfa -32 + ^
STACK CFI 3374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3378 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 33a0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33c8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3410 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3438 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3440 fc .cfa: sp 0 + .ra: x30
STACK CFI 3444 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3454 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3464 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3478 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34ec x19: x19 x20: x20
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 34fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3510 x19: x19 x20: x20
STACK CFI 3524 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3528 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3538 x19: x19 x20: x20
STACK CFI INIT 3540 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3544 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3554 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3564 x23: .cfa -16 + ^
STACK CFI 3574 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35a0 x21: x21 x22: x22
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 35b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 35b8 x21: x21 x22: x22
STACK CFI 35cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 35d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 35e0 x21: x21 x22: x22
STACK CFI INIT 35e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35fc x19: .cfa -16 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3630 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3668 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36a0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3708 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3740 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3778 214 .cfa: sp 0 + .ra: x30
STACK CFI 377c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3788 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3798 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 37e8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 37f8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3944 x23: x23 x24: x24
STACK CFI 3948 x25: x25 x26: x26
STACK CFI 396c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3970 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 3978 x23: x23 x24: x24
STACK CFI 397c x25: x25 x26: x26
STACK CFI 3984 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3988 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 3990 64 .cfa: sp 0 + .ra: x30
STACK CFI 3998 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39a0 x19: .cfa -16 + ^
STACK CFI 39b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 39bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 39e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 39f8 108 .cfa: sp 0 + .ra: x30
STACK CFI 39fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a1c x23: .cfa -16 + ^
STACK CFI 3a74 x23: x23
STACK CFI 3a78 x23: .cfa -16 + ^
STACK CFI 3ac4 x23: x23
STACK CFI 3ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3aec x23: .cfa -16 + ^
STACK CFI INIT 3b00 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 3b04 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3b0c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3b1c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3b54 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3b58 .cfa: sp 224 + .ra: .cfa -216 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 3b5c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3b74 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3d04 x19: x19 x20: x20
STACK CFI 3d08 x21: x21 x22: x22
STACK CFI 3d0c x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3da8 x19: x19 x20: x20
STACK CFI 3dac x21: x21 x22: x22
STACK CFI 3db0 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3dc4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3dc8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3dcc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 3dd0 ec .cfa: sp 0 + .ra: x30
STACK CFI 3dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ddc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ec0 70 .cfa: sp 0 + .ra: x30
STACK CFI 3ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ed4 x19: .cfa -16 + ^
STACK CFI 3ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3f20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f30 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3f34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3f3c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3f54 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3fec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ff0 64 .cfa: sp 0 + .ra: x30
STACK CFI 3ff4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4004 x19: .cfa -64 + ^
STACK CFI 404c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4050 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4058 80 .cfa: sp 0 + .ra: x30
STACK CFI 405c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4068 x19: .cfa -64 + ^
STACK CFI 40c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 40d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40f8 38 .cfa: sp 0 + .ra: x30
STACK CFI 40fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 411c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4120 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 412c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4130 284 .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 413c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 414c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4154 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4160 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 41a4 x27: .cfa -80 + ^
STACK CFI 4344 x27: x27
STACK CFI 4348 x27: .cfa -80 + ^
STACK CFI 434c x27: x27
STACK CFI 4378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 437c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 43a8 x27: x27
STACK CFI 43b0 x27: .cfa -80 + ^
STACK CFI INIT 43b8 58 .cfa: sp 0 + .ra: x30
STACK CFI 43bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43c8 x19: .cfa -16 + ^
STACK CFI 43ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 440c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4410 108 .cfa: sp 0 + .ra: x30
STACK CFI 4414 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 441c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 442c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 44fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4500 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4518 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4538 134 .cfa: sp 0 + .ra: x30
STACK CFI 453c .cfa: sp 160 +
STACK CFI 4540 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4548 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4550 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4570 x23: .cfa -96 + ^
STACK CFI 45c8 x23: x23
STACK CFI 45f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45f4 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 463c x23: x23
STACK CFI 4658 x23: .cfa -96 + ^
STACK CFI 4660 x23: x23
STACK CFI 4668 x23: .cfa -96 + ^
STACK CFI INIT 4670 14c .cfa: sp 0 + .ra: x30
STACK CFI 4674 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 467c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4688 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4690 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 478c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4790 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 47c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 47cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 480c x21: .cfa -96 + ^
STACK CFI 4870 x21: x21
STACK CFI 488c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4890 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI 48a8 x21: x21
STACK CFI 48bc x21: .cfa -96 + ^
STACK CFI INIT 48c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 48c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 48cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 490c x21: .cfa -96 + ^
STACK CFI 4970 x21: x21
STACK CFI 498c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4990 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI 49a8 x21: x21
STACK CFI 49bc x21: .cfa -96 + ^
STACK CFI INIT 49c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 49c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 49cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4a0c x21: .cfa -96 + ^
STACK CFI 4a70 x21: x21
STACK CFI 4a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a90 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI 4aa8 x21: x21
STACK CFI 4abc x21: .cfa -96 + ^
STACK CFI INIT 4ac0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4af0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b10 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4b18 .cfa: sp 4208 +
STACK CFI 4b24 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 4b34 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 4b40 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 4b4c x23: .cfa -4128 + ^
STACK CFI 4be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4be4 .cfa: sp 4208 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 4bf0 104 .cfa: sp 0 + .ra: x30
STACK CFI 4bf8 .cfa: sp 4192 +
STACK CFI 4bfc .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 4c04 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 4c10 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 4c18 x25: .cfa -4128 + ^ x26: .cfa -4120 + ^
STACK CFI 4c44 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 4c98 x19: x19 x20: x20
STACK CFI 4cd4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4cd8 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x21: .cfa -4160 + ^ x22: .cfa -4152 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x25: .cfa -4128 + ^ x26: .cfa -4120 + ^ x29: .cfa -4192 + ^
STACK CFI 4cdc x19: x19 x20: x20
STACK CFI 4cf0 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI INIT 4cf8 164 .cfa: sp 0 + .ra: x30
STACK CFI 4d00 .cfa: sp 4208 +
STACK CFI 4d08 .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI 4d10 x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI 4d24 x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^
STACK CFI 4d2c x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 4d38 x27: .cfa -4128 + ^ x28: .cfa -4120 + ^
STACK CFI 4e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e28 .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x25: .cfa -4144 + ^ x26: .cfa -4136 + ^ x27: .cfa -4128 + ^ x28: .cfa -4120 + ^ x29: .cfa -4208 + ^
STACK CFI INIT 4e60 200 .cfa: sp 0 + .ra: x30
STACK CFI 4e64 .cfa: sp 400 +
STACK CFI 4e68 .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 4e70 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 4e7c x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 4e84 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 4e8c x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 4e98 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 5040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5044 .cfa: sp 400 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT 5060 54 .cfa: sp 0 + .ra: x30
STACK CFI 5064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50b8 54 .cfa: sp 0 + .ra: x30
STACK CFI 50bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5110 54 .cfa: sp 0 + .ra: x30
STACK CFI 5114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 514c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5150 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5160 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5168 94 .cfa: sp 0 + .ra: x30
STACK CFI 516c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 517c x19: .cfa -32 + ^
STACK CFI 51dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5200 90 .cfa: sp 0 + .ra: x30
STACK CFI 5204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5214 x19: .cfa -32 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5278 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5290 90 .cfa: sp 0 + .ra: x30
STACK CFI 5294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52a4 x19: .cfa -32 + ^
STACK CFI 5304 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5308 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5320 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 336 +
STACK CFI 533c .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5348 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5354 x21: .cfa -288 + ^
STACK CFI 53e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 53ec .cfa: sp 336 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 5400 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 320 +
STACK CFI 5418 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5420 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 54b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54bc .cfa: sp 320 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 54c0 154 .cfa: sp 0 + .ra: x30
STACK CFI 54c4 .cfa: sp 368 +
STACK CFI 54c8 .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 54d0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 54e0 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 54e8 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 54f0 x25: .cfa -288 + ^
STACK CFI 55e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 55ec .cfa: sp 368 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x29: .cfa -352 + ^
STACK CFI INIT 5618 150 .cfa: sp 0 + .ra: x30
STACK CFI 561c .cfa: sp 368 +
STACK CFI 5620 .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 5628 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 5638 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 5640 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 5648 x25: .cfa -288 + ^
STACK CFI 573c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5740 .cfa: sp 368 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x29: .cfa -352 + ^
STACK CFI INIT 5768 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5770 210 .cfa: sp 0 + .ra: x30
STACK CFI 5774 .cfa: sp 1152 +
STACK CFI 5788 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 5790 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 579c x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57ec .cfa: sp 1152 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x29: .cfa -1136 + ^
STACK CFI 5858 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 5868 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 5960 x23: x23 x24: x24
STACK CFI 5964 x25: x25 x26: x26
STACK CFI 5968 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 596c x23: x23 x24: x24
STACK CFI 5970 x25: x25 x26: x26
STACK CFI 5978 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 597c x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI INIT 5980 194 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 480 +
STACK CFI 5998 .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 59a0 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 59a8 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 59b4 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 5ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5aec .cfa: sp 480 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x29: .cfa -464 + ^
STACK CFI INIT 5b18 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5b1c .cfa: sp 448 +
STACK CFI 5b34 .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 5b40 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 5bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bb4 .cfa: sp 448 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x29: .cfa -432 + ^
STACK CFI INIT 5bb8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bf8 440 .cfa: sp 0 + .ra: x30
STACK CFI 5bfc .cfa: sp 1232 +
STACK CFI 5c08 .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 5c10 x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 5c1c x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 5c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 5c70 .cfa: sp 1232 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI 5c80 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 5ce4 x21: x21 x22: x22
STACK CFI 5ce8 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 5d24 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 5d48 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 5f28 x23: x23 x24: x24
STACK CFI 5f30 x25: x25 x26: x26
STACK CFI 5f78 x21: x21 x22: x22
STACK CFI 5f7c x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 5ffc x23: x23 x24: x24
STACK CFI 6000 x25: x25 x26: x26
STACK CFI 6008 x21: x21 x22: x22
STACK CFI 6010 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 6014 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 6018 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 601c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6034 x21: x21 x22: x22
STACK CFI INIT 6038 164 .cfa: sp 0 + .ra: x30
STACK CFI 603c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6044 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6058 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6060 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6128 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
