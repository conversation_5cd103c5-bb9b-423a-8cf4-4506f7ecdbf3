MODULE Linux arm64 5656650F6A1A2A61C08520B4670CC4040 libXaw.so.7
INFO CODE_ID 0F6556561A6A612AC08520B4670CC40440605705
PUBLIC 15700 0 _init
PUBLIC 16d38 0 XawParseBoolean
PUBLIC 17190 0 XawPrintActionErrorMsg
PUBLIC 172f8 0 XawGetActionResList
PUBLIC 17e10 0 XawGetActionVarList
PUBLIC 17f80 0 XawBooleanExpression
PUBLIC 18040 0 XawCallProcAction
PUBLIC 180f8 0 XawSetValuesAction
PUBLIC 18480 0 XawGetValuesAction
PUBLIC 18568 0 XawDeclareAction
PUBLIC 1d8e0 0 XawAsciiSourceFreeString
PUBLIC 1d9a8 0 XawAsciiSave
PUBLIC 1db60 0 XawAsciiSaveAsFile
PUBLIC 1dc90 0 XawAsciiSourceChanged
PUBLIC 207a0 0 XawInitializeDefaultConverters
PUBLIC 20b60 0 XawTypeToStringWarning
PUBLIC 216a0 0 XawDialogAddButton
PUBLIC 21708 0 XawDialogGetValueString
PUBLIC 24e30 0 XawRunDisplayList
PUBLIC 24eb8 0 XawDisplayListString
PUBLIC 24ed8 0 XawDestroyDisplayList
PUBLIC 25010 0 XawDeclareDisplayListProc
PUBLIC 25148 0 XawGetDisplayListClass
PUBLIC 251d8 0 XawCreateDisplayListClass
PUBLIC 25310 0 XawDisplayListInitialize
PUBLIC 25398 0 XawCreateDisplayList
PUBLIC 26158 0 CvtEdgeTypeToString
PUBLIC 26980 0 XawFormDoLayout
PUBLIC 28df0 0 XawListChange
PUBLIC 28f08 0 XawListUnhighlight
PUBLIC 28f30 0 XawListHighlight
PUBLIC 29138 0 XawListShowCurrent
PUBLIC 2aef0 0 _XawMultiSourceFreeString
PUBLIC 2af30 0 _XawMultiSave
PUBLIC 2b138 0 _XawMultiSaveAsFile
PUBLIC 2c778 0 _XawMultiSinkPosToXY
PUBLIC 2c7f0 0 _XawGetPageSize
PUBLIC 2e750 0 XawPanedSetMinMax
PUBLIC 2e848 0 XawPanedGetMinMax
PUBLIC 2e860 0 XawPanedSetRefigureMode
PUBLIC 2e868 0 XawPanedGetNumSub
PUBLIC 2e870 0 XawPanedAllowResize
PUBLIC 30850 0 XawParseParamsString
PUBLIC 30bc0 0 XawFreeParamsStruct
PUBLIC 30c50 0 XawFindArgVal
PUBLIC 31568 0 XawLoadPixmap
PUBLIC 31830 0 XawAddPixmapLoader
PUBLIC 31a38 0 XawPixmapsInitialize
PUBLIC 31ab0 0 XawPixmapFromXPixmap
PUBLIC 31b78 0 XawReshapeWidget
PUBLIC 33078 0 XawScrollbarSetThumb
PUBLIC 353f0 0 XawSimpleMenuAddGlobalActions
PUBLIC 35420 0 XawSimpleMenuGetActiveEntry
PUBLIC 35428 0 XawSimpleMenuClearActiveEntry
PUBLIC 38888 0 _XawTextNeedsUpdating
PUBLIC 38b60 0 _XawTextGetText
PUBLIC 38cc0 0 _XawTextGetSTRING
PUBLIC 396b8 0 _XawTextSetScrollBars
PUBLIC 39d60 0 _XawTextBuildLineTable
PUBLIC 39e98 0 XawTextScroll
PUBLIC 3a578 0 _XawTextVScroll
PUBLIC 3a580 0 _XawTextSetLineAndColumnNumber
PUBLIC 3aa98 0 _XawTextReplace
PUBLIC 3ab70 0 _XawTextCheckResize
PUBLIC 3ab78 0 _XawTextSelectionList
PUBLIC 3ac28 0 _XawTextPrepareToUpdate
PUBLIC 3ac70 0 _XawTextShowPosition
PUBLIC 3b120 0 _XawTextClearAndCenterDisplay
PUBLIC 3b250 0 _XawTextExecuteUpdate
PUBLIC 3b8d0 0 _XawTextPosToXY
PUBLIC 3b940 0 XawTextDisplay
PUBLIC 3b9b8 0 XawTextSetSelectionArray
PUBLIC 3b9c0 0 XawTextGetSelectionPos
PUBLIC 3b9d8 0 _XawTextSetSource
PUBLIC 3be00 0 XawTextSetSource
PUBLIC 3be08 0 XawTextReplace
PUBLIC 3bed8 0 XawTextTopPosition
PUBLIC 3bee0 0 XawTextLastPosition
PUBLIC 3bee8 0 XawTextSetInsertionPoint
PUBLIC 3bf48 0 XawTextGetInsertionPoint
PUBLIC 3bf50 0 XawTextUnsetSelection
PUBLIC 3c800 0 _XawTextSaltAwaySelection
PUBLIC 3ca10 0 _XawTextSetSelection
PUBLIC 3cab8 0 _XawTextAlterSelection
PUBLIC 3d200 0 _XawTextSourceChanged
PUBLIC 3d778 0 XawTextSetSelection
PUBLIC 3d7f0 0 XawTextInvalidate
PUBLIC 3d880 0 XawTextDisableRedisplay
PUBLIC 3d890 0 XawTextEnableRedisplay
PUBLIC 3d940 0 XawTextGetSource
PUBLIC 3d948 0 XawTextGetSink
PUBLIC 3d950 0 XawTextDisplayCaret
PUBLIC 3d9a0 0 XawTextSearch
PUBLIC 3ec20 0 _XawTextSinkDisplayText
PUBLIC 3ec30 0 XawTextSinkDisplayText
PUBLIC 3ec40 0 XawTextSinkInsertCursor
PUBLIC 3ec58 0 _XawTextSinkClearToBackground
PUBLIC 3ec68 0 XawTextSinkClearToBackground
PUBLIC 3ec80 0 XawTextSinkFindPosition
PUBLIC 3ec90 0 XawTextSinkFindDistance
PUBLIC 3eca0 0 XawTextSinkResolve
PUBLIC 3ecb0 0 XawTextSinkMaxLines
PUBLIC 3ecc0 0 XawTextSinkMaxHeight
PUBLIC 3ecd0 0 XawTextSinkSetTabs
PUBLIC 3ed90 0 XawTextSinkGetCursorBounds
PUBLIC 3eda0 0 XawTextSinkBeginPaint
PUBLIC 3edd8 0 XawTextSinkPreparePaint
PUBLIC 3ede8 0 XawTextSinkDoPaint
PUBLIC 3edf8 0 XawTextSinkEndPaint
PUBLIC 3ee08 0 XawTextSinkGetProperty
PUBLIC 3ee10 0 XawTextSinkCopyProperty
PUBLIC 3eea0 0 XawTextSinkAddProperty
PUBLIC 3eeb0 0 XawTextSinkCombineProperty
PUBLIC 3f2a8 0 XawTextSinkConvertPropertyList
PUBLIC 40300 0 _XawSourceAddText
PUBLIC 40390 0 _XawSourceRemoveText
PUBLIC 40438 0 XawTextSourceRead
PUBLIC 40448 0 _XawSourceSetUndoErase
PUBLIC 40460 0 _XawSourceSetUndoMerge
PUBLIC 40490 0 _XawTextSrcToggleUndo
PUBLIC 40530 0 XawTextSourceScan
PUBLIC 40540 0 XawTextSourceSearch
PUBLIC 40550 0 _XawTextSourceNewLineAtEOF
PUBLIC 40628 0 XawTextSourceConvertSelection
PUBLIC 40638 0 XawTextSourceSetSelection
PUBLIC 40648 0 _XawTextFormat
PUBLIC 40658 0 _XawTextWCToMB
PUBLIC 40708 0 _XawTextMBToWC
PUBLIC 40878 0 XawTextSourceFindAnchor
PUBLIC 40910 0 XawTextSourceAddAnchor
PUBLIC 40b88 0 XawTextSourceNextAnchor
PUBLIC 40be8 0 XawTextSourceAnchorAndEntity
PUBLIC 40d00 0 XawTextSourcePrevAnchor
PUBLIC 40d68 0 XawTextSourceRemoveAnchor
PUBLIC 40e40 0 XawTextSourceReplace
PUBLIC 41dc8 0 _XawTextSrcUndo
PUBLIC 41fe8 0 XawTextSourceClearEntities
PUBLIC 42288 0 _XawTextSourceFindAnchor
PUBLIC 422e8 0 XawTextSourceAddEntity
PUBLIC 4a178 0 _XawTextZapSelection
PUBLIC 4c1b8 0 _XawTextInsertFileAction
PUBLIC 4c1d0 0 _XawTextInsertFile
PUBLIC 4c2e8 0 _XawTextDoSearchAction
PUBLIC 4c370 0 _XawTextPopdownSearchAction
PUBLIC 4c388 0 _XawTextSearch
PUBLIC 4c670 0 _XawTextDoReplaceAction
PUBLIC 4c708 0 _XawTextSetField
PUBLIC 4cd70 0 XawToggleGetCurrent
PUBLIC 4cdc0 0 XawToggleSetCurrent
PUBLIC 4cec8 0 XawToggleUnsetCurrent
PUBLIC 4cf40 0 XawToggleChangeRadioGroup
PUBLIC 4e1f8 0 XawTreeForceLayout
PUBLIC 4e658 0 XawVendorShellExtResize
PUBLIC 4e770 0 XawVendorStructureNotifyHandler
PUBLIC 4fe00 0 XawViewportSetLocation
PUBLIC 4fe88 0 XawViewportSetCoordinates
PUBLIC 52278 0 _XawImResizeVendorShell
PUBLIC 52328 0 _XawImGetShellHeight
PUBLIC 52398 0 _XawImRealize
PUBLIC 52428 0 _XawImInitialize
PUBLIC 525c8 0 _XawImReconnect
PUBLIC 52668 0 _XawImRegister
PUBLIC 52718 0 _XawImUnregister
PUBLIC 527f8 0 _XawImSetValues
PUBLIC 52800 0 _XawImSetFocusValues
PUBLIC 52808 0 _XawImUnsetFocus
PUBLIC 528e8 0 _XawImWcLookupString
PUBLIC 52a10 0 _XawLookupString
PUBLIC 52ae0 0 _XawImGetImAreaHeight
PUBLIC 52b18 0 _XawImCallVendorShellExtResize
PUBLIC 52b58 0 _XawImDestroy
PUBLIC 52c78 0 XawInitializeWidgetSet
PUBLIC 52cb0 0 XawOpenApplication
PUBLIC 52da8 0 _Xaw_atowc
PUBLIC 52e08 0 _Xaw_iswalnum
PUBLIC 53a58 0 XawTipEnable
PUBLIC 53a70 0 XawTipDisable
PUBLIC 53ad8 0 _fini
