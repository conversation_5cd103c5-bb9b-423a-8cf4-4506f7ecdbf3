MODULE Linux arm64 8C4715E25CCECF875C84E79E385C5FB90 libjpe_interface.so
INFO CODE_ID E215478CCE5C87CF5C84E79E385C5FB9
PUBLIC 1c00 0 _init
PUBLIC 1f20 0 call_weak_fn
PUBLIC 1f34 0 deregister_tm_clones
PUBLIC 1f64 0 register_tm_clones
PUBLIC 1fa0 0 __do_global_dtors_aux
PUBLIC 1ff0 0 frame_dummy
PUBLIC 2000 0 lios::jpe::JpeNvMedia::JpeNvMedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&)
PUBLIC 22f0 0 lios::jpe::JpeNvMedia::~JpeNvMedia()
PUBLIC 2340 0 lios::jpe::JpeNvMedia::GetImage(unsigned char**)
PUBLIC 2350 0 lios::jpe::JpeNvMedia::GetJpeBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 2420 0 lios::jpe::JpeNvMedia::FeedFrame(NvSciBufObjRefRec*)
PUBLIC 2650 0 std::vector<unsigned char, std::allocator<unsigned char> >::_M_default_append(unsigned long)
PUBLIC 278c 0 _fini
STACK CFI INIT 1f34 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f64 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fa0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1fb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fb8 x19: .cfa -16 + ^
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2000 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 2004 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 200c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2018 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2020 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2028 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 225c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI INIT 22f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 22f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22fc x19: .cfa -16 + ^
STACK CFI 2334 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2340 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2350 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2354 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 235c x19: .cfa -128 + ^
STACK CFI 23ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 23f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2650 13c .cfa: sp 0 + .ra: x30
STACK CFI 2658 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2660 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2668 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 26b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2728 x23: x23 x24: x24
STACK CFI 272c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2734 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2420 228 .cfa: sp 0 + .ra: x30
STACK CFI 2424 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2434 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2444 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 251c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2520 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
