MODULE Linux arm64 B7CA79E77A0E14ED7A92775124EE736E0 libpopt.so.0
INFO CODE_ID E779CAB70E7AED147A92775124EE736E15E13C53
PUBLIC 3180 0 poptSetExecPath
PUBLIC 3210 0 poptGetContext
PUBLIC 3370 0 poptResetContext
PUBLIC 3480 0 poptBitsAdd
PUBLIC 3568 0 poptBitsChk
PUBLIC 3678 0 poptBitsClr
PUBLIC 36c0 0 poptBitsDel
PUBLIC 37a8 0 poptBitsIntersect
PUBLIC 3848 0 poptBitsUnion
PUBLIC 38e8 0 poptBitsArgs
PUBLIC 3988 0 poptSaveBits
PUBLIC 3ae0 0 poptSaveString
PUBLIC 3c00 0 poptSaveLongLong
PUBLIC 3d38 0 poptSaveLong
PUBLIC 3e70 0 poptSaveInt
PUBLIC 3fa8 0 poptSaveShort
PUBLIC 40e8 0 poptGetNextOpt
PUBLIC 4f80 0 poptGetOptArg
PUBLIC 4fa0 0 poptGetArg
PUBLIC 4fd8 0 poptPeekArg
PUBLIC 5008 0 poptGetArgs
PUBLIC 5040 0 poptFreeContext
PUBLIC 5138 0 poptAddItem
PUBLIC 52f0 0 poptAddAlias
PUBLIC 5380 0 poptBadOption
PUBLIC 53d8 0 poptStrerror
PUBLIC 5530 0 poptStuffArgs
PUBLIC 55b8 0 poptGetInvocationName
PUBLIC 55d8 0 poptStrippedArgv
PUBLIC 5680 0 poptDupArgv
PUBLIC 5790 0 poptParseArgvString
PUBLIC 59f8 0 poptConfigFileToString
PUBLIC 5ec0 0 poptSaneFile
PUBLIC 5f60 0 poptReadFile
PUBLIC 6110 0 poptReadConfigFile
PUBLIC 6750 0 poptReadConfigFiles
PUBLIC 6958 0 poptReadDefaultConfig
PUBLIC 6bb0 0 poptFini
PUBLIC 6bb8 0 poptInit
PUBLIC 83d0 0 poptPrintHelp
PUBLIC 8498 0 poptPrintUsage
PUBLIC 86c8 0 poptSetOtherOptionHelp
STACK CFI INIT 2178 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21a8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 21ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21f4 x19: .cfa -16 + ^
STACK CFI 222c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2230 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2238 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2240 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2248 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2254 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2264 x23: .cfa -16 + ^
STACK CFI 22cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2320 dc .cfa: sp 0 + .ra: x30
STACK CFI 2328 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2330 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 233c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 234c x23: .cfa -16 + ^
STACK CFI 23cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 23d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 23f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2400 4c .cfa: sp 0 + .ra: x30
STACK CFI 2404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 240c x19: .cfa -16 + ^
STACK CFI 2448 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2450 174 .cfa: sp 0 + .ra: x30
STACK CFI 2458 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2460 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 246c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2478 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2488 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2494 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 259c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 25a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 25c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 25c8 1cc .cfa: sp 0 + .ra: x30
STACK CFI 25cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2600 x23: .cfa -16 + ^
STACK CFI 26c8 x21: x21 x22: x22
STACK CFI 26cc x23: x23
STACK CFI 26d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 26e8 x21: x21 x22: x22
STACK CFI 26ec x23: x23
STACK CFI 26f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 26f4 x21: x21 x22: x22
STACK CFI 26fc x23: x23
STACK CFI 2704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2708 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2710 x21: x21 x22: x22
STACK CFI 2714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2718 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2738 x21: x21 x22: x22 x23: x23
STACK CFI 2744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2748 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2750 x23: x23
STACK CFI 2758 x21: x21 x22: x22
STACK CFI 275c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2788 x21: x21 x22: x22
STACK CFI 278c x23: x23
STACK CFI INIT 2798 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2820 90 .cfa: sp 0 + .ra: x30
STACK CFI 2824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2830 x21: .cfa -16 + ^
STACK CFI 283c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28a0 x19: x19 x20: x20
STACK CFI 28ac .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 28b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28c8 x19: .cfa -16 + ^
STACK CFI 2928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 292c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2968 70 .cfa: sp 0 + .ra: x30
STACK CFI 296c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2978 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 29a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 29d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 29d8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 29e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29fc x21: .cfa -16 + ^
STACK CFI 2a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ac8 258 .cfa: sp 0 + .ra: x30
STACK CFI 2acc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2ad4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ae4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2af4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c1c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c54 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2d20 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 2d24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2d30 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2d38 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2d48 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2d54 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2d5c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2ea0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2f18 268 .cfa: sp 0 + .ra: x30
STACK CFI 2f1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2f2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2f38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2f54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f70 x27: .cfa -16 + ^
STACK CFI 301c x27: x27
STACK CFI 3030 x19: x19 x20: x20
STACK CFI 3044 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3048 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 3064 x19: x19 x20: x20
STACK CFI 3074 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3078 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3154 x27: x27
STACK CFI 315c x27: .cfa -16 + ^
STACK CFI 3160 x19: x19 x20: x20
STACK CFI 3164 x27: x27
STACK CFI 316c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 317c x19: x19 x20: x20
STACK CFI INIT 3180 90 .cfa: sp 0 + .ra: x30
STACK CFI 3184 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 318c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 319c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 31e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3210 15c .cfa: sp 0 + .ra: x30
STACK CFI 3214 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 321c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3228 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3238 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3324 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3370 10c .cfa: sp 0 + .ra: x30
STACK CFI 3378 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 338c x21: .cfa -16 + ^
STACK CFI 3474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3480 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3484 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 348c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 349c x21: .cfa -32 + ^
STACK CFI 3558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 355c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3568 10c .cfa: sp 0 + .ra: x30
STACK CFI 356c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3574 x21: .cfa -32 + ^
STACK CFI 357c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3624 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3678 44 .cfa: sp 0 + .ra: x30
STACK CFI 3690 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36c0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 36c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 36cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36dc x21: .cfa -32 + ^
STACK CFI 3798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 379c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 37a8 9c .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37d8 x21: .cfa -16 + ^
STACK CFI 3818 x21: x21
STACK CFI 3824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3828 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 383c x21: x21
STACK CFI INIT 3848 9c .cfa: sp 0 + .ra: x30
STACK CFI 384c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3864 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3878 x21: .cfa -16 + ^
STACK CFI 38b8 x21: x21
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 38dc x21: x21
STACK CFI INIT 38e8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 38f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3900 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 395c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 396c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3988 158 .cfa: sp 0 + .ra: x30
STACK CFI 398c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 399c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 39a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39c4 x23: .cfa -16 + ^
STACK CFI 3a44 x19: x19 x20: x20
STACK CFI 3a48 x23: x23
STACK CFI 3a54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3a58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3aa4 x23: x23
STACK CFI 3ab0 x19: x19 x20: x20
STACK CFI 3ab8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI INIT 3ae0 11c .cfa: sp 0 + .ra: x30
STACK CFI 3af0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3af8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b0c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 3b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3b98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3bac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c00 134 .cfa: sp 0 + .ra: x30
STACK CFI 3c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c1c x21: .cfa -16 + ^
STACK CFI 3c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3c70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3d38 134 .cfa: sp 0 + .ra: x30
STACK CFI 3d40 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d54 x21: .cfa -16 + ^
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3e70 134 .cfa: sp 0 + .ra: x30
STACK CFI 3e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e8c x21: .cfa -16 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3ee0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3fa8 140 .cfa: sp 0 + .ra: x30
STACK CFI 3fb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fb8 x21: .cfa -16 + ^
STACK CFI 3fc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4010 x19: x19 x20: x20
STACK CFI 4018 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 401c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 403c x19: x19 x20: x20
STACK CFI 4044 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 40a0 x19: x19 x20: x20
STACK CFI 40a8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 40ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 40b4 x19: x19 x20: x20
STACK CFI 40bc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 40c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 40cc x19: x19 x20: x20
STACK CFI 40d4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 40d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 40e8 e94 .cfa: sp 0 + .ra: x30
STACK CFI 40ec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 40f4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4114 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 412c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4138 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4148 v8: .cfa -64 + ^
STACK CFI 4588 v8: v8
STACK CFI 458c x23: x23 x24: x24
STACK CFI 4590 x27: x27 x28: x28
STACK CFI 4594 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4694 x23: x23 x24: x24
STACK CFI 4698 x27: x27 x28: x28
STACK CFI 469c v8: v8
STACK CFI 46a0 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 472c v8: v8
STACK CFI 4730 x23: x23 x24: x24
STACK CFI 4734 x27: x27 x28: x28
STACK CFI 4738 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4860 x23: x23 x24: x24
STACK CFI 4864 x27: x27 x28: x28
STACK CFI 486c v8: v8
STACK CFI 4898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 489c .cfa: sp 160 + .ra: .cfa -152 + ^ v8: .cfa -64 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 48a4 v8: v8
STACK CFI 48a8 x23: x23 x24: x24
STACK CFI 48ac x27: x27 x28: x28
STACK CFI 48b0 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a7c x23: x23 x24: x24
STACK CFI 4a80 x27: x27 x28: x28
STACK CFI 4a84 v8: v8
STACK CFI 4a88 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4b14 x23: x23 x24: x24
STACK CFI 4b18 x27: x27 x28: x28
STACK CFI 4b1c v8: v8
STACK CFI 4b24 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4c88 x23: x23 x24: x24
STACK CFI 4c8c x27: x27 x28: x28
STACK CFI 4c90 v8: v8
STACK CFI 4c94 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4c9c v8: v8
STACK CFI 4ca0 x23: x23 x24: x24
STACK CFI 4ca4 x27: x27 x28: x28
STACK CFI 4ca8 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4cdc v8: v8
STACK CFI 4ce0 x23: x23 x24: x24
STACK CFI 4ce4 x27: x27 x28: x28
STACK CFI 4ce8 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4df0 v8: v8
STACK CFI 4df4 x23: x23 x24: x24
STACK CFI 4df8 x27: x27 x28: x28
STACK CFI 4dfc v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4f10 v8: v8
STACK CFI 4f14 x23: x23 x24: x24
STACK CFI 4f18 x27: x27 x28: x28
STACK CFI 4f1c v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4f50 v8: v8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 4f58 v8: .cfa -64 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4f6c v8: v8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 4f70 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4f74 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4f78 v8: .cfa -64 + ^
STACK CFI INIT 4f80 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fa0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5008 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5040 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5048 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5050 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50a0 x21: .cfa -16 + ^
STACK CFI 50cc x21: x21
STACK CFI 5128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5138 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 513c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5144 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 514c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5168 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 51a0 x25: .cfa -16 + ^
STACK CFI 51c8 x25: x25
STACK CFI 526c x21: x21 x22: x22
STACK CFI 5284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5288 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 529c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 52ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 52b8 x25: .cfa -16 + ^
STACK CFI 52e0 x25: x25
STACK CFI 52e8 x21: x21 x22: x22
STACK CFI INIT 52f0 8c .cfa: sp 0 + .ra: x30
STACK CFI 52f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5310 x19: .cfa -96 + ^
STACK CFI 5374 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5378 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5380 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53d8 158 .cfa: sp 0 + .ra: x30
STACK CFI 5508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5518 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5530 88 .cfa: sp 0 + .ra: x30
STACK CFI 5534 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5540 x19: .cfa -16 + ^
STACK CFI 55a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 55a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 55b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55d8 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5680 10c .cfa: sp 0 + .ra: x30
STACK CFI 5690 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5698 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 56bc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5754 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 576c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5770 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 577c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5784 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5790 264 .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 579c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 57ac x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 57b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 57f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 583c x21: x21 x22: x22
STACK CFI 5888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 588c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 59c4 x21: x21 x22: x22
STACK CFI 59c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 59d0 x21: x21 x22: x22
STACK CFI INIT 59f8 354 .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 1120 +
STACK CFI 5a04 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 5a0c x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 5a1c x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 5a40 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 5a54 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 5a68 x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 5c28 x25: x25 x26: x26
STACK CFI 5c2c x27: x27 x28: x28
STACK CFI 5c34 x19: x19 x20: x20
STACK CFI 5c58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c5c .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^ x29: .cfa -1120 + ^
STACK CFI 5c80 x19: x19 x20: x20
STACK CFI 5c84 x25: x25 x26: x26
STACK CFI 5c88 x27: x27 x28: x28
STACK CFI 5c8c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 5d20 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5d28 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 5d2c x25: x25 x26: x26
STACK CFI 5d30 x27: x27 x28: x28
STACK CFI 5d38 x19: x19 x20: x20
STACK CFI 5d40 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 5d44 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 5d48 x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI INIT 5d50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d58 164 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5d64 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5d74 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5d7c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5e60 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5ec0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5ec4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5ecc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f3c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5f60 1ac .cfa: sp 0 + .ra: x30
STACK CFI 5f64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f70 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 60a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 60ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6110 63c .cfa: sp 0 + .ra: x30
STACK CFI 6114 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 6120 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 614c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 6164 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 6174 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 6190 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 6220 x23: x23 x24: x24
STACK CFI 6228 x25: x25 x26: x26
STACK CFI 622c x27: x27 x28: x28
STACK CFI 6254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6258 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 65d0 x23: x23 x24: x24
STACK CFI 65e0 x25: x25 x26: x26
STACK CFI 65e4 x27: x27 x28: x28
STACK CFI 65fc x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 6690 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6698 x27: x27 x28: x28
STACK CFI 669c x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 6728 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6730 x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 673c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6740 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 6744 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 6748 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 6750 204 .cfa: sp 0 + .ra: x30
STACK CFI 6754 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 675c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6764 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6770 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6784 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6790 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 68e0 x25: x25 x26: x26
STACK CFI 690c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6910 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 691c x25: x25 x26: x26
STACK CFI 6928 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 6958 258 .cfa: sp 0 + .ra: x30
STACK CFI 695c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 6964 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 698c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 69a0 x21: x21 x22: x22
STACK CFI 69c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 69c4 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x29: .cfa -256 + ^
STACK CFI 6a54 x21: x21 x22: x22
STACK CFI 6a58 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6a5c x21: x21 x22: x22
STACK CFI 6a68 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6a94 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 6aa8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 6aac x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6b54 x21: x21 x22: x22
STACK CFI 6b58 x23: x23 x24: x24
STACK CFI 6b5c x25: x25 x26: x26
STACK CFI 6b60 x27: x27 x28: x28
STACK CFI 6b64 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6b7c x23: x23 x24: x24
STACK CFI 6b80 x25: x25 x26: x26
STACK CFI 6b84 x27: x27 x28: x28
STACK CFI 6b88 x21: x21 x22: x22
STACK CFI 6b8c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6b90 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 6b94 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 6b98 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6ba4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6bac x21: x21 x22: x22
STACK CFI INIT 6bb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bb8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6bbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6bc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6bcc x23: .cfa -16 + ^
STACK CFI 6bec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6c38 x21: x21 x22: x22
STACK CFI 6c3c x23: x23
STACK CFI 6c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6c48 x21: x21 x22: x22
STACK CFI 6c4c x23: x23
STACK CFI 6c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6c6c x21: x21 x22: x22
STACK CFI 6c70 x23: x23
STACK CFI 6c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6c78 200 .cfa: sp 0 + .ra: x30
STACK CFI 6c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6e78 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6e7c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6e84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6e8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6e94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6e9c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6eb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6fc0 x25: x25 x26: x26
STACK CFI 6fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6ff0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7024 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7058 ac .cfa: sp 0 + .ra: x30
STACK CFI 705c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7064 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7108 98 .cfa: sp 0 + .ra: x30
STACK CFI 710c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7114 x19: .cfa -32 + ^
STACK CFI 7188 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 718c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 71a0 93c .cfa: sp 0 + .ra: x30
STACK CFI 71a4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 71ac x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 71b8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 71c8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 71dc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 71f0 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 757c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7580 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 77ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 77f0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 7858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 785c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7ae0 94 .cfa: sp 0 + .ra: x30
STACK CFI 7aec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7af4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7b78 204 .cfa: sp 0 + .ra: x30
STACK CFI 7b7c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7b84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7b98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7ba4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7bc4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7ccc x27: x27 x28: x28
STACK CFI 7ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7d3c x27: x27 x28: x28
STACK CFI 7d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 7d80 184 .cfa: sp 0 + .ra: x30
STACK CFI 7d84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7d8c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7d98 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7db8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7dc0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7e98 x21: x21 x22: x22
STACK CFI 7e9c x25: x25 x26: x26
STACK CFI 7ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 7ec4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 7ef8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 7efc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7f00 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 7f08 244 .cfa: sp 0 + .ra: x30
STACK CFI 7f0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7f14 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7f24 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 7f3c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7f44 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7f50 x27: .cfa -48 + ^
STACK CFI 80c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 80c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8150 1ac .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 815c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8168 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8174 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8184 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 818c x27: .cfa -16 + ^
STACK CFI 81ec x19: x19 x20: x20
STACK CFI 81f0 x21: x21 x22: x22
STACK CFI 81f4 x23: x23 x24: x24
STACK CFI 81f8 x27: x27
STACK CFI 8204 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 8208 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8300 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8304 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 830c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8324 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 833c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8348 x25: .cfa -16 + ^
STACK CFI 8380 x19: x19 x20: x20
STACK CFI 8384 x23: x23 x24: x24
STACK CFI 8388 x25: x25
STACK CFI 8394 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8398 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 83d0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 83d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 83dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83e8 x21: .cfa -16 + ^
STACK CFI 8468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 846c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8498 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 849c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 84a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 84ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 84bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8634 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 8674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8678 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8680 48 .cfa: sp 0 + .ra: x30
STACK CFI 8684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8698 x19: .cfa -16 + ^
STACK CFI INIT 86c8 88 .cfa: sp 0 + .ra: x30
STACK CFI 86cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 86d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 86e4 x21: .cfa -16 + ^
STACK CFI 8724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8728 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8750 5a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d08 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d28 7c .cfa: sp 0 + .ra: x30
STACK CFI 8d2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d3c x21: .cfa -16 + ^
STACK CFI 8d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8d98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8da8 324 .cfa: sp 0 + .ra: x30
STACK CFI 8dac .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 8dbc x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 8e50 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 8e6c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 8e84 x23: x23 x24: x24
STACK CFI 8edc x21: x21 x22: x22
STACK CFI 8efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f00 .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x29: .cfa -432 + ^
STACK CFI 8f08 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 8f60 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 8f68 x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 8fec x23: x23 x24: x24
STACK CFI 8ff0 x25: x25 x26: x26
STACK CFI 8ff4 x27: x27 x28: x28
STACK CFI 9000 x21: x21 x22: x22
STACK CFI 9004 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 9024 x23: x23 x24: x24
STACK CFI 9028 x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 9084 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9088 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 908c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 9090 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 9094 x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 9098 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 909c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 90a0 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 90a4 x27: .cfa -352 + ^ x28: .cfa -344 + ^
