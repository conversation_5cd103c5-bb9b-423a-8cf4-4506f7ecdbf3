MODULE Linux arm64 AAFFC02EC883A87E744383FFB93E76BD0 lisa_dummy
INFO CODE_ID 2EC0FFAA83C87EA8744383FFB93E76BD
PUBLIC eb0 0 _init
PUBLIC 1090 0 main
PUBLIC 1290 0 _GLOBAL__sub_I__ZN5fLU6411FLAGS_c_perE
PUBLIC 132c 0 _start
PUBLIC 137c 0 call_weak_fn
PUBLIC 1390 0 deregister_tm_clones
PUBLIC 13d4 0 register_tm_clones
PUBLIC 1424 0 __do_global_dtors_aux
PUBLIC 1454 0 frame_dummy
PUBLIC 1460 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<main::{lambda()#1}> > >::~_State_impl()
PUBLIC 1480 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<main::{lambda()#1}> > >::~_State_impl()
PUBLIC 14c0 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<main::{lambda()#1}> > >::_M_run()
PUBLIC 1570 0 std::_Sp_counted_ptr_inplace<std::thread, std::allocator<std::thread>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 1580 0 std::_Sp_counted_ptr_inplace<std::vector<char, std::allocator<char> >, std::allocator<std::vector<char, std::allocator<char> > >, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 1590 0 std::_Sp_counted_ptr_inplace<std::thread, std::allocator<std::thread>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 15b0 0 std::_Sp_counted_ptr_inplace<std::thread, std::allocator<std::thread>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15c0 0 std::_Sp_counted_ptr_inplace<std::vector<char, std::allocator<char> >, std::allocator<std::vector<char, std::allocator<char> > >, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15d0 0 std::_Sp_counted_ptr_inplace<std::vector<char, std::allocator<char> >, std::allocator<std::vector<char, std::allocator<char> > >, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 15e0 0 std::_Sp_counted_ptr_inplace<std::vector<char, std::allocator<char> >, std::allocator<std::vector<char, std::allocator<char> > >, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 1640 0 std::_Sp_counted_ptr_inplace<std::thread, std::allocator<std::thread>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 16a0 0 std::_Sp_counted_ptr_inplace<std::vector<char, std::allocator<char> >, std::allocator<std::vector<char, std::allocator<char> > >, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 16b0 0 std::_Sp_counted_ptr_inplace<std::thread, std::allocator<std::thread>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 16c0 0 std::shared_ptr<std::thread>::~shared_ptr()
PUBLIC 1780 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 1840 0 __libc_csu_init
PUBLIC 18c0 0 __libc_csu_fini
PUBLIC 18c4 0 _fini
STACK CFI INIT 1390 44 .cfa: sp 0 + .ra: x30
STACK CFI 13ac .cfa: sp 16 +
STACK CFI 13c4 .cfa: sp 0 +
STACK CFI 13c8 .cfa: sp 16 +
STACK CFI 13cc .cfa: sp 0 +
STACK CFI INIT 13d4 50 .cfa: sp 0 + .ra: x30
STACK CFI 13fc .cfa: sp 16 +
STACK CFI 1414 .cfa: sp 0 +
STACK CFI 1418 .cfa: sp 16 +
STACK CFI 141c .cfa: sp 0 +
STACK CFI INIT 1424 30 .cfa: sp 0 + .ra: x30
STACK CFI 1428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1430 x19: .cfa -16 + ^
STACK CFI 1450 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1454 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1590 18 .cfa: sp 0 + .ra: x30
STACK CFI 15a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 15b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 15e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 163c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1640 60 .cfa: sp 0 + .ra: x30
STACK CFI 1644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1654 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 169c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 16a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1460 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1480 38 .cfa: sp 0 + .ra: x30
STACK CFI 1484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1498 x19: .cfa -16 + ^
STACK CFI 14b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 14c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 150c x23: .cfa -32 + ^
STACK CFI INIT 16c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1708 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 175c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1768 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1780 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 178c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 17c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1824 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1090 200 .cfa: sp 0 + .ra: x30
STACK CFI 1094 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 1158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 115c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1290 9c .cfa: sp 0 + .ra: x30
STACK CFI 1294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1840 7c .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 184c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1858 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 186c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 18c0 4 .cfa: sp 0 + .ra: x30
