MODULE Linux arm64 E6F511F75C61EB3AEB52823A3E08BF700 libfftw3f_omp.so.3
INFO CODE_ID F711F5E6615C3AEBEB52823A3E08BF70D6D3A504
PUBLIC 1b88 0 fftwf_init_threads
PUBLIC 1c08 0 fftwf_cleanup_threads
PUBLIC 1c60 0 fftwf_plan_with_nthreads
PUBLIC 1cb0 0 fftwf_make_planner_thread_safe
PUBLIC 1cb8 0 fftwf_threads_conf_standard
PUBLIC 1dc0 0 fftwf_ithreads_init
PUBLIC 1dc8 0 fftwf_spawn_loop
PUBLIC 1e60 0 fftwf_threads_cleanup
PUBLIC 1e68 0 fftwf_threads_register_planner_hooks
PUBLIC 2450 0 fftwf_dft_thr_vrank_geq1_register
PUBLIC 2cf0 0 fftwf_mksolver_ct_threads
PUBLIC 3310 0 fftwf_rdft_thr_vrank_geq1_register
PUBLIC 3af0 0 fftwf_mksolver_hc2hc_threads
PUBLIC 4108 0 fftwf_rdft2_thr_vrank_geq1_register
PUBLIC 4188 0 sfftw_plan_with_nthreads_
PUBLIC 4190 0 sfftw_init_threads_
PUBLIC 41b8 0 sfftw_cleanup_threads_
PUBLIC 41c0 0 sfftw_plan_with_nthreads__
PUBLIC 41c8 0 sfftw_init_threads__
PUBLIC 41d0 0 sfftw_cleanup_threads__
STACK CFI INIT 1ac8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b38 48 .cfa: sp 0 + .ra: x30
STACK CFI 1b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b44 x19: .cfa -16 + ^
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b88 7c .cfa: sp 0 + .ra: x30
STACK CFI 1b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c08 54 .cfa: sp 0 + .ra: x30
STACK CFI 1c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c14 x19: .cfa -16 + ^
STACK CFI 1c2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c60 4c .cfa: sp 0 + .ra: x30
STACK CFI 1c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ca0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1cb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 1ccc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1cd4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1cdc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1cf4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1d30 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1d40 x27: .cfa -48 + ^
STACK CFI 1d7c x25: x25 x26: x26
STACK CFI 1d80 x27: x27
STACK CFI 1da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1da8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 1db8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1dbc x27: .cfa -48 + ^
STACK CFI INIT 1dc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc8 98 .cfa: sp 0 + .ra: x30
STACK CFI 1dcc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1dd8 x19: .cfa -64 + ^
STACK CFI 1e58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e5c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1e60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e70 60 .cfa: sp 0 + .ra: x30
STACK CFI 1e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ecc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ed0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1edc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f14 x23: .cfa -16 + ^
STACK CFI 1f20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f84 x19: x19 x20: x20
STACK CFI 1f88 x23: x23
STACK CFI 1fa0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 1fa8 4c .cfa: sp 0 + .ra: x30
STACK CFI 1fac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1ff8 78 .cfa: sp 0 + .ra: x30
STACK CFI 1ffc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2020 x19: .cfa -80 + ^
STACK CFI 2068 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 206c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2070 58 .cfa: sp 0 + .ra: x30
STACK CFI 2074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 207c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2090 x21: .cfa -16 + ^
STACK CFI 20bc x21: x21
STACK CFI 20c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20c8 388 .cfa: sp 0 + .ra: x30
STACK CFI 20cc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 20d4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2100 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2150 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2154 .cfa: sp 192 + .ra: .cfa -184 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 2158 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2164 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2188 x19: x19 x20: x20
STACK CFI 218c x23: x23 x24: x24
STACK CFI 2190 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2350 x19: x19 x20: x20
STACK CFI 2354 x23: x23 x24: x24
STACK CFI 2358 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 23e4 x19: x19 x20: x20
STACK CFI 23e8 x23: x23 x24: x24
STACK CFI 23ec x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2444 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 2448 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 244c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI INIT 2450 7c .cfa: sp 0 + .ra: x30
STACK CFI 2454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 245c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 246c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 24d0 30 .cfa: sp 0 + .ra: x30
STACK CFI 24d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2500 fc .cfa: sp 0 + .ra: x30
STACK CFI 2504 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2514 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2564 x23: .cfa -16 + ^
STACK CFI 2570 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25d4 x19: x19 x20: x20
STACK CFI 25d8 x23: x23
STACK CFI 25f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2600 54 .cfa: sp 0 + .ra: x30
STACK CFI 2604 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 260c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2658 a4 .cfa: sp 0 + .ra: x30
STACK CFI 265c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2664 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2674 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 26f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2700 84 .cfa: sp 0 + .ra: x30
STACK CFI 2704 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 270c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 271c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 277c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2780 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2788 60 .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27a0 x21: .cfa -16 + ^
STACK CFI 27e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 27e8 508 .cfa: sp 0 + .ra: x30
STACK CFI 27ec .cfa: sp 224 +
STACK CFI 27f4 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2814 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2820 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2828 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 283c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 284c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 28f8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 28fc x19: x19 x20: x20
STACK CFI 2900 x27: x27 x28: x28
STACK CFI 2928 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 292c .cfa: sp 224 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 2adc x19: x19 x20: x20
STACK CFI 2ae0 x21: x21 x22: x22
STACK CFI 2ae4 x25: x25 x26: x26
STACK CFI 2ae8 x27: x27 x28: x28
STACK CFI 2aec x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 2bac x19: x19 x20: x20
STACK CFI 2bb0 x21: x21 x22: x22
STACK CFI 2bb4 x25: x25 x26: x26
STACK CFI 2bb8 x27: x27 x28: x28
STACK CFI 2bbc x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 2cdc x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2ce0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2ce4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2ce8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 2cec x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 2cf0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2d38 44 .cfa: sp 0 + .ra: x30
STACK CFI 2d3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d80 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2d84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2dc4 x23: .cfa -16 + ^
STACK CFI 2dd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e34 x19: x19 x20: x20
STACK CFI 2e38 x23: x23
STACK CFI 2e50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2e58 4c .cfa: sp 0 + .ra: x30
STACK CFI 2e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ea8 78 .cfa: sp 0 + .ra: x30
STACK CFI 2eac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2ed0 x19: .cfa -64 + ^
STACK CFI 2f18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2f20 58 .cfa: sp 0 + .ra: x30
STACK CFI 2f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f40 x21: .cfa -16 + ^
STACK CFI 2f6c x21: x21
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2f78 398 .cfa: sp 0 + .ra: x30
STACK CFI 2f7c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2f84 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2fac x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2ff8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 2ffc .cfa: sp 192 + .ra: .cfa -184 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 3004 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3010 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3018 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3030 x19: x19 x20: x20
STACK CFI 3034 x23: x23 x24: x24
STACK CFI 3038 x25: x25 x26: x26
STACK CFI 303c x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3200 x19: x19 x20: x20
STACK CFI 3204 x23: x23 x24: x24
STACK CFI 3208 x25: x25 x26: x26
STACK CFI 320c x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 329c x19: x19 x20: x20
STACK CFI 32a0 x23: x23 x24: x24
STACK CFI 32a4 x25: x25 x26: x26
STACK CFI 32a8 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3300 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3304 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3308 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 330c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 3310 7c .cfa: sp 0 + .ra: x30
STACK CFI 3314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 331c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 332c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3390 2c .cfa: sp 0 + .ra: x30
STACK CFI 3394 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 33b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 33c0 fc .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 33d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3424 x23: .cfa -16 + ^
STACK CFI 3430 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3494 x19: x19 x20: x20
STACK CFI 3498 x23: x23
STACK CFI 34b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 34c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 34c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3518 88 .cfa: sp 0 + .ra: x30
STACK CFI 351c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3524 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3534 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 359c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 35a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 35a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 35ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 35d0 x21: .cfa -48 + ^
STACK CFI 3614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3618 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3620 60 .cfa: sp 0 + .ra: x30
STACK CFI 3624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 362c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3638 x21: .cfa -16 + ^
STACK CFI 367c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3680 470 .cfa: sp 0 + .ra: x30
STACK CFI 3684 .cfa: sp 192 +
STACK CFI 368c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 36ac x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 36b4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 36c0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 36d4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 36dc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 379c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 37a0 x21: x21 x22: x22
STACK CFI 37a4 x27: x27 x28: x28
STACK CFI 37cc .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 37d0 .cfa: sp 192 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 38d4 x19: x19 x20: x20
STACK CFI 38d8 x21: x21 x22: x22
STACK CFI 38dc x23: x23 x24: x24
STACK CFI 38e0 x27: x27 x28: x28
STACK CFI 38e4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3a2c x19: x19 x20: x20
STACK CFI 3a30 x21: x21 x22: x22
STACK CFI 3a34 x23: x23 x24: x24
STACK CFI 3a38 x27: x27 x28: x28
STACK CFI 3a3c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3adc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 3ae0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3ae4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3ae8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3aec x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 3af0 30 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b20 60 .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b80 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3bc4 x23: .cfa -16 + ^
STACK CFI 3bd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3c34 x19: x19 x20: x20
STACK CFI 3c38 x23: x23
STACK CFI 3c50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 3c58 4c .cfa: sp 0 + .ra: x30
STACK CFI 3c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ca8 78 .cfa: sp 0 + .ra: x30
STACK CFI 3cac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3cd0 x19: .cfa -80 + ^
STACK CFI 3d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d1c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3d20 58 .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d40 x21: .cfa -16 + ^
STACK CFI 3d6c x21: x21
STACK CFI 3d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d78 38c .cfa: sp 0 + .ra: x30
STACK CFI 3d7c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3d84 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3dbc x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3dc8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3dd8 x23: x23 x24: x24
STACK CFI 3e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3e0c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 3e40 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3fd4 x21: x21 x22: x22
STACK CFI 3fd8 x23: x23 x24: x24
STACK CFI 3fdc x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4008 x21: x21 x22: x22
STACK CFI 400c x23: x23 x24: x24
STACK CFI 4010 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 409c x21: x21 x22: x22
STACK CFI 40a0 x23: x23 x24: x24
STACK CFI 40a4 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 40f8 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 40fc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4100 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI INIT 4108 7c .cfa: sp 0 + .ra: x30
STACK CFI 410c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4114 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4124 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4188 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4190 24 .cfa: sp 0 + .ra: x30
STACK CFI 4194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 419c x19: .cfa -16 + ^
STACK CFI 41b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d0 4 .cfa: sp 0 + .ra: x30
