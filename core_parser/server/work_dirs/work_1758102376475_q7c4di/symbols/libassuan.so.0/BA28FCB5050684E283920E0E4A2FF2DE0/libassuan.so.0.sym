MODULE Linux arm64 BA28FCB5050684E283920E0E4A2FF2DE0 libassuan.so.0
INFO CODE_ID B5FC28BA0605E28483920E0E4A2FF2DE1F1DC3D1
PUBLIC 37c0 0 assuan_set_gpg_err_source
PUBLIC 37d0 0 assuan_get_gpg_err_source
PUBLIC 37e0 0 assuan_set_malloc_hooks
PUBLIC 3800 0 assuan_get_malloc_hooks
PUBLIC 3810 0 assuan_set_log_cb
PUBLIC 3828 0 assuan_get_log_cb
PUBLIC 3848 0 assuan_set_system_hooks
PUBLIC 3858 0 assuan_new_ext
PUBLIC 3a80 0 assuan_new
PUBLIC 3ad0 0 assuan_release
PUBLIC 3b58 0 assuan_check_version
PUBLIC 3c50 0 assuan_set_pointer
PUBLIC 3ca0 0 assuan_get_pointer
PUBLIC 3cb8 0 assuan_set_flag
PUBLIC 3de0 0 assuan_get_flag
PUBLIC 3ed8 0 assuan_begin_confidential
PUBLIC 3ee8 0 assuan_end_confidential
PUBLIC 3ef8 0 assuan_ctx_set_system_hooks
PUBLIC 3f50 0 assuan_set_io_monitor
PUBLIC 3fb0 0 assuan_set_error
PUBLIC 4048 0 assuan_get_pid
PUBLIC 40d0 0 assuan_get_peercred
PUBLIC 4210 0 assuan_free
PUBLIC 53a0 0 assuan_client_read_response
PUBLIC 54e0 0 assuan_client_parse_response
PUBLIC 5850 0 assuan_transact
PUBLIC 60f0 0 assuan_read_line
PUBLIC 6168 0 assuan_pending_line
PUBLIC 6340 0 assuan_write_line
PUBLIC 6698 0 assuan_send_data
PUBLIC 6740 0 assuan_sendfd
PUBLIC 6790 0 assuan_receivefd
PUBLIC 6ae0 0 assuan_command_parse_fd
PUBLIC 6cc8 0 assuan_register_command
PUBLIC 6f28 0 assuan_get_command_name
PUBLIC 6f40 0 assuan_register_pre_cmd_notify
PUBLIC 6f60 0 assuan_register_post_cmd_notify
PUBLIC 6f80 0 assuan_register_bye_notify
PUBLIC 6fa0 0 assuan_register_reset_notify
PUBLIC 6fc0 0 assuan_register_cancel_notify
PUBLIC 6fe0 0 assuan_register_option_handler
PUBLIC 7000 0 assuan_register_input_notify
PUBLIC 7020 0 assuan_register_output_notify
PUBLIC 70b0 0 assuan_process_done
PUBLIC 7c48 0 assuan_process_next
PUBLIC 7d90 0 assuan_process
PUBLIC 7e90 0 assuan_get_active_fds
PUBLIC 7f38 0 assuan_get_data_fp
PUBLIC 7f88 0 assuan_set_okay_line
PUBLIC 8050 0 assuan_write_status
PUBLIC 8328 0 assuan_inquire
PUBLIC 8b00 0 assuan_inquire_ext
PUBLIC 8d00 0 assuan_set_hello_line
PUBLIC 8df0 0 assuan_accept
PUBLIC 8f90 0 assuan_get_input_fd
PUBLIC 8fa8 0 assuan_get_output_fd
PUBLIC 8fc0 0 assuan_close_input_fd
PUBLIC 9028 0 assuan_close_output_fd
PUBLIC 9090 0 assuan_init_pipe_server
PUBLIC 95f0 0 assuan_init_socket_server
PUBLIC 9870 0 assuan_set_sock_nonce
PUBLIC 9b88 0 assuan_pipe_connect
PUBLIC a3e8 0 assuan_socket_connect_fd
PUBLIC a440 0 assuan_socket_connect
PUBLIC ae88 0 assuan_set_assuan_log_stream
PUBLIC ae98 0 assuan_set_log_stream
PUBLIC aef0 0 assuan_set_assuan_log_prefix
PUBLIC af30 0 assuan_get_assuan_log_prefix
PUBLIC c260 0 assuan_sock_init
PUBLIC c280 0 assuan_sock_deinit
PUBLIC c2b0 0 assuan_sock_close
PUBLIC c408 0 assuan_sock_new
PUBLIC c428 0 assuan_sock_set_flag
PUBLIC c448 0 assuan_sock_get_flag
PUBLIC c468 0 assuan_sock_connect
PUBLIC c488 0 assuan_sock_connect_byname
PUBLIC c4b0 0 assuan_sock_bind
PUBLIC c4d0 0 assuan_sock_set_sockaddr_un
PUBLIC c4d8 0 assuan_sock_get_nonce
PUBLIC c4f8 0 assuan_sock_check_nonce
PUBLIC c510 0 assuan_sock_set_system_hooks
PUBLIC c530 0 __assuan_pipe
PUBLIC c538 0 __assuan_close
PUBLIC c540 0 __assuan_read
PUBLIC c550 0 __assuan_write
PUBLIC c560 0 __assuan_recvmsg
PUBLIC c5c0 0 __assuan_sendmsg
PUBLIC c620 0 __assuan_socketpair
PUBLIC c638 0 __assuan_socket
PUBLIC c648 0 __assuan_connect
PUBLIC c658 0 __assuan_usleep
PUBLIC c710 0 __assuan_waitpid
PUBLIC cb20 0 __assuan_spawn
PUBLIC cbf0 0 assuan_fdopen
STACK CFI INIT 3598 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35c8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3608 48 .cfa: sp 0 + .ra: x30
STACK CFI 360c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3614 x19: .cfa -16 + ^
STACK CFI 364c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3650 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3658 168 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 37d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 37e0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3800 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3810 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3828 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3848 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3858 228 .cfa: sp 0 + .ra: x30
STACK CFI 385c .cfa: sp 3936 +
STACK CFI 3860 .ra: .cfa -3880 + ^ x29: .cfa -3888 + ^
STACK CFI 3868 x23: .cfa -3840 + ^ x24: .cfa -3832 + ^
STACK CFI 3878 x19: .cfa -3872 + ^ x20: .cfa -3864 + ^
STACK CFI 3890 x21: .cfa -3856 + ^ x22: .cfa -3848 + ^
STACK CFI 38a8 x25: .cfa -3824 + ^ x26: .cfa -3816 + ^
STACK CFI 38b0 x27: .cfa -3808 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 39fc .cfa: sp 3936 + .ra: .cfa -3880 + ^ x19: .cfa -3872 + ^ x20: .cfa -3864 + ^ x21: .cfa -3856 + ^ x22: .cfa -3848 + ^ x23: .cfa -3840 + ^ x24: .cfa -3832 + ^ x25: .cfa -3824 + ^ x26: .cfa -3816 + ^ x27: .cfa -3808 + ^ x29: .cfa -3888 + ^
STACK CFI INIT 3a80 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3aa0 30 .cfa: sp 0 + .ra: x30
STACK CFI 3aac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ab4 x19: .cfa -16 + ^
STACK CFI 3ac8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ad0 84 .cfa: sp 0 + .ra: x30
STACK CFI 3ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b00 x19: .cfa -16 + ^
STACK CFI 3b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b58 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b68 x19: .cfa -48 + ^
STACK CFI 3bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3bf8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c50 50 .cfa: sp 0 + .ra: x30
STACK CFI 3c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ca0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cb8 128 .cfa: sp 0 + .ra: x30
STACK CFI 3cbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3cf0 x21: .cfa -16 + ^
STACK CFI 3d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3de0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3de4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3dfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3ed8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ee8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ef8 54 .cfa: sp 0 + .ra: x30
STACK CFI 3efc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f50 60 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f8c x21: .cfa -16 + ^
STACK CFI 3fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3fb0 98 .cfa: sp 0 + .ra: x30
STACK CFI 3fb4 .cfa: sp 64 +
STACK CFI 3fb8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4048 84 .cfa: sp 0 + .ra: x30
STACK CFI 404c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4060 x19: .cfa -16 + ^
STACK CFI 408c x19: x19
STACK CFI 4098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 409c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 40d0 8c .cfa: sp 0 + .ra: x30
STACK CFI 40d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 412c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4160 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4178 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4190 60 .cfa: sp 0 + .ra: x30
STACK CFI 4194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 419c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 41e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4210 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4218 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 42d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4328 14c .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 433c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4354 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4384 x23: .cfa -16 + ^
STACK CFI 43ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 43f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4478 58 .cfa: sp 0 + .ra: x30
STACK CFI 447c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 448c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 44d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4528 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4538 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4548 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4558 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4568 354 .cfa: sp 0 + .ra: x30
STACK CFI 456c .cfa: sp 160 +
STACK CFI 4570 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4578 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4584 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4594 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 473c .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 48c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48d0 140 .cfa: sp 0 + .ra: x30
STACK CFI 48d4 .cfa: sp 96 +
STACK CFI 48e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 48f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4900 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4918 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4930 x25: .cfa -16 + ^
STACK CFI 49a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 49a8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 4a10 10c .cfa: sp 0 + .ra: x30
STACK CFI 4a14 .cfa: sp 80 +
STACK CFI 4a24 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4a30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4a3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a58 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ad0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4b20 10c .cfa: sp 0 + .ra: x30
STACK CFI 4b24 .cfa: sp 80 +
STACK CFI 4b34 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b40 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b68 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4be0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4c30 120 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 4c3c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 4c90 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 4cb0 x21: x21 x22: x22
STACK CFI 4ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cd0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x29: .cfa -304 + ^
STACK CFI 4d44 x21: x21 x22: x22
STACK CFI 4d4c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI INIT 4d50 ec .cfa: sp 0 + .ra: x30
STACK CFI 4d54 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 4d5c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4db4 x21: .cfa -256 + ^
STACK CFI 4dc8 x21: x21
STACK CFI 4de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4de8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x29: .cfa -288 + ^
STACK CFI 4e2c x21: x21
STACK CFI 4e38 x21: .cfa -256 + ^
STACK CFI INIT 4e40 118 .cfa: sp 0 + .ra: x30
STACK CFI 4e44 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 4e4c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 4ed0 x21: .cfa -288 + ^
STACK CFI 4f14 x21: x21
STACK CFI 4f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f3c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI 4f50 x21: x21
STACK CFI 4f54 x21: .cfa -288 + ^
STACK CFI INIT 4f58 44 .cfa: sp 0 + .ra: x30
STACK CFI 4f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f74 x19: .cfa -16 + ^
STACK CFI 4f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4fa0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4fe0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4fec x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5008 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 500c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5010 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5110 x19: x19 x20: x20
STACK CFI 5114 x21: x21 x22: x22
STACK CFI 5118 x23: x23 x24: x24
STACK CFI 511c x25: x25 x26: x26
STACK CFI 5120 x27: x27 x28: x28
STACK CFI 513c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5140 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 5144 x19: x19 x20: x20
STACK CFI 5148 x21: x21 x22: x22
STACK CFI 5150 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 5154 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5158 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 515c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5160 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 5168 160 .cfa: sp 0 + .ra: x30
STACK CFI 516c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5174 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5180 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5188 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 51c8 x25: .cfa -16 + ^
STACK CFI 5244 x25: x25
STACK CFI 525c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5260 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 52a4 x25: x25
STACK CFI 52bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 52c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 52c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 52d8 98 .cfa: sp 0 + .ra: x30
STACK CFI 52dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52e8 x19: .cfa -16 + ^
STACK CFI 534c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5350 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5370 2c .cfa: sp 0 + .ra: x30
STACK CFI 5374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5384 x19: .cfa -16 + ^
STACK CFI 5398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53a0 140 .cfa: sp 0 + .ra: x30
STACK CFI 53a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5424 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 54e0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5788 c4 .cfa: sp 0 + .ra: x30
STACK CFI 578c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5794 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 57a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 57b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 57c8 x25: .cfa -48 + ^
STACK CFI 5814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5818 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5850 268 .cfa: sp 0 + .ra: x30
STACK CFI 5854 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 585c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 586c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5884 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5890 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5984 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5ab8 88 .cfa: sp 0 + .ra: x30
STACK CFI 5abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ac8 x19: .cfa -16 + ^
STACK CFI 5b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5b20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b40 50 .cfa: sp 0 + .ra: x30
STACK CFI 5b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b4c x19: .cfa -16 + ^
STACK CFI 5b8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b90 34 .cfa: sp 0 + .ra: x30
STACK CFI 5ba8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bc8 7c .cfa: sp 0 + .ra: x30
STACK CFI 5bcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5bd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5be0 x21: .cfa -16 + ^
STACK CFI 5c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5c20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5c48 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5c4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c70 x23: .cfa -16 + ^
STACK CFI 5ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5ce4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5d0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5d30 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 5d34 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 5d3c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 5d4c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 5d64 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5d74 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5e18 x21: x21 x22: x22
STACK CFI 5e1c x25: x25 x26: x26
STACK CFI 5e20 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5e74 x21: x21 x22: x22
STACK CFI 5e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5ea0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 5eb0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5f1c x21: x21 x22: x22
STACK CFI 5f24 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5f30 x27: .cfa -144 + ^
STACK CFI 5f60 x27: x27
STACK CFI 5f94 x21: x21 x22: x22
STACK CFI 5f98 x25: x25 x26: x26
STACK CFI 5f9c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6014 x21: x21 x22: x22
STACK CFI 6024 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6050 x25: x25 x26: x26
STACK CFI 6054 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6068 x25: x25 x26: x26
STACK CFI 606c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 6070 x25: x25 x26: x26
STACK CFI 6094 x21: x21 x22: x22
STACK CFI 6098 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 609c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 60a0 x27: .cfa -144 + ^
STACK CFI 60a4 x25: x25 x26: x26 x27: x27
STACK CFI 60c4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 60c8 x27: .cfa -144 + ^
STACK CFI 60cc x27: x27
STACK CFI 60ec x27: .cfa -144 + ^
STACK CFI INIT 60f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 60f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6108 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6148 x21: x21 x22: x22
STACK CFI 614c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6150 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6168 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6188 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 618c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6194 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 61a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 61a8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6218 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 633c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6340 ac .cfa: sp 0 + .ra: x30
STACK CFI 6348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6350 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 635c x21: .cfa -16 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 63b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 63dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 63f0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 63f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 63fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 640c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6418 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6424 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6434 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6534 x19: x19 x20: x20
STACK CFI 6538 x23: x23 x24: x24
STACK CFI 653c x27: x27 x28: x28
STACK CFI 6544 x21: x21 x22: x22
STACK CFI 6548 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6554 x27: x27 x28: x28
STACK CFI 6558 x19: x19 x20: x20
STACK CFI 655c x23: x23 x24: x24
STACK CFI 6564 x21: x21 x22: x22
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 6578 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 65a8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 65b8 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 65bc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 65c8 cc .cfa: sp 0 + .ra: x30
STACK CFI 65d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6640 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6698 a8 .cfa: sp 0 + .ra: x30
STACK CFI 66a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 66d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 66f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6740 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6790 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 67d0 18 .cfa: sp 0 + .ra: x30
STACK CFI 67d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67e8 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6860 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6890 250 .cfa: sp 0 + .ra: x30
STACK CFI 6894 .cfa: sp 448 +
STACK CFI 6898 .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 68a0 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 68ac x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 6974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6978 .cfa: sp 448 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x29: .cfa -432 + ^
STACK CFI 699c x23: .cfa -384 + ^
STACK CFI 6a30 x23: x23
STACK CFI 6a34 x23: .cfa -384 + ^
STACK CFI 6a38 x23: x23
STACK CFI 6a3c x23: .cfa -384 + ^
STACK CFI 6aa4 x23: x23
STACK CFI 6acc x23: .cfa -384 + ^
STACK CFI INIT 6ae0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 6ae4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6aec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6afc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6b70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6cc8 260 .cfa: sp 0 + .ra: x30
STACK CFI 6ccc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6cd4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6ce0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6d78 x19: x19 x20: x20
STACK CFI 6d84 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6d88 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6d8c x19: x19 x20: x20
STACK CFI 6da8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6dac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6db4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6dc0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6e0c x23: x23 x24: x24
STACK CFI 6e18 x25: x25 x26: x26
STACK CFI 6e80 x19: x19 x20: x20
STACK CFI 6e90 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6e94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6ed4 x23: x23 x24: x24
STACK CFI 6ed8 x25: x25 x26: x26
STACK CFI 6f00 x19: x19 x20: x20
STACK CFI 6f04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6f10 x19: x19 x20: x20
STACK CFI 6f1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 6f28 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f40 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f60 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f80 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fa0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fe0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7000 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7020 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7040 70 .cfa: sp 0 + .ra: x30
STACK CFI 7044 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7054 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 709c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 70ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70b0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70d0 54 .cfa: sp 0 + .ra: x30
STACK CFI 70ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70fc x19: .cfa -16 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7128 a8 .cfa: sp 0 + .ra: x30
STACK CFI 712c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7134 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7148 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 71b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 71b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 71d0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 71d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 71dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 71f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 725c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7278 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 727c .cfa: sp 1120 +
STACK CFI 7280 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 7288 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 7294 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 72c4 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 72dc x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 72e8 x27: .cfa -1040 + ^
STACK CFI 7384 x25: x25 x26: x26
STACK CFI 7388 x27: x27
STACK CFI 7394 x23: x23 x24: x24
STACK CFI 73b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73bc .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x29: .cfa -1120 + ^
STACK CFI 7434 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 74ac x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^
STACK CFI 74d4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7534 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 7538 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 753c x27: .cfa -1040 + ^
STACK CFI INIT 7540 3c .cfa: sp 0 + .ra: x30
STACK CFI 7544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7558 x19: .cfa -16 + ^
STACK CFI 7578 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7580 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75a8 58 .cfa: sp 0 + .ra: x30
STACK CFI 75ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75b8 x19: .cfa -16 + ^
STACK CFI 75d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 75dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 75f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7600 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7628 6c .cfa: sp 0 + .ra: x30
STACK CFI 762c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7638 x19: .cfa -16 + ^
STACK CFI 7658 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 765c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7690 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7698 60 .cfa: sp 0 + .ra: x30
STACK CFI 769c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76a8 x19: .cfa -16 + ^
STACK CFI 76e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 76f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 76f8 2dc .cfa: sp 0 + .ra: x30
STACK CFI 76fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7704 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 782c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7830 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 786c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7898 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7904 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7978 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 798c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 79b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 79d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 79d8 258 .cfa: sp 0 + .ra: x30
STACK CFI 79dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 79e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7a28 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7a2c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7a30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7aec x19: x19 x20: x20
STACK CFI 7af4 x23: x23 x24: x24
STACK CFI 7af8 x25: x25 x26: x26
STACK CFI 7afc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7b00 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7b0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7b10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7b14 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7b74 x19: x19 x20: x20
STACK CFI 7b7c x23: x23 x24: x24
STACK CFI 7b80 x25: x25 x26: x26
STACK CFI 7b84 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7b88 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7bdc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7be0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7bec x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7c00 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7c10 x19: x19 x20: x20
STACK CFI 7c18 x23: x23 x24: x24
STACK CFI 7c1c x25: x25 x26: x26
STACK CFI 7c20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7c24 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7c2c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 7c30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c48 144 .cfa: sp 0 + .ra: x30
STACK CFI 7c4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7c54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7c5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7c64 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7d90 fc .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7da4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7db0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7e60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7e84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7e90 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7ea8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7eb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ee0 x21: .cfa -16 + ^
STACK CFI 7f00 x21: x21
STACK CFI 7f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f38 4c .cfa: sp 0 + .ra: x30
STACK CFI 7f4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f60 x19: .cfa -16 + ^
STACK CFI 7f80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7f88 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7f90 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7f98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7fac x21: .cfa -16 + ^
STACK CFI 7ff0 x21: x21
STACK CFI 7ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8000 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 801c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8030 x21: x21
STACK CFI 803c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 804c x21: x21
STACK CFI INIT 8050 210 .cfa: sp 0 + .ra: x30
STACK CFI 8054 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 805c x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 8064 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 8094 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 80c4 x25: .cfa -288 + ^
STACK CFI 80fc x23: x23 x24: x24
STACK CFI 8100 x25: x25
STACK CFI 8124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8128 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI 8140 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 819c x23: x23 x24: x24
STACK CFI 81a0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 81b4 x25: .cfa -288 + ^
STACK CFI 8214 x25: x25
STACK CFI 821c x23: x23 x24: x24
STACK CFI 822c x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 8254 x23: x23 x24: x24
STACK CFI 8258 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 825c x25: .cfa -288 + ^
STACK CFI INIT 8260 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 826c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8288 x21: .cfa -16 + ^
STACK CFI 82e8 x21: x21
STACK CFI 82f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8304 x21: x21
STACK CFI 8310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8314 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 831c x21: x21
STACK CFI INIT 8328 4a4 .cfa: sp 0 + .ra: x30
STACK CFI 832c .cfa: sp 1168 +
STACK CFI 8330 .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI 8338 x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI 8344 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 8358 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 8364 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 83e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 83ec .cfa: sp 1168 + .ra: .cfa -1160 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^ x29: .cfa -1168 + ^
STACK CFI 8414 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 8508 x25: x25 x26: x26
STACK CFI 852c x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 86d0 x25: x25 x26: x26
STACK CFI 86e4 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 86f4 x25: x25 x26: x26
STACK CFI 8708 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 87b0 x25: x25 x26: x26
STACK CFI 87b4 x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI INIT 87d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 87dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8818 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 881c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8824 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8834 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 891c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8b00 200 .cfa: sp 0 + .ra: x30
STACK CFI 8b04 .cfa: sp 1088 +
STACK CFI 8b08 .ra: .cfa -1080 + ^ x29: .cfa -1088 + ^
STACK CFI 8b10 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^
STACK CFI 8b18 x19: .cfa -1072 + ^ x20: .cfa -1064 + ^
STACK CFI 8b48 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 8b7c x23: x23 x24: x24
STACK CFI 8b88 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 8b8c x23: x23 x24: x24
STACK CFI 8bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8bc8 .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x29: .cfa -1088 + ^
STACK CFI 8bd8 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 8be0 x25: .cfa -1024 + ^
STACK CFI 8c68 x23: x23 x24: x24
STACK CFI 8c6c x25: x25
STACK CFI 8c70 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 8c7c x23: x23 x24: x24
STACK CFI 8c88 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^
STACK CFI 8cb8 x23: x23 x24: x24
STACK CFI 8cbc x25: x25
STACK CFI 8cc4 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 8cc8 x25: .cfa -1024 + ^
STACK CFI 8ce4 x23: x23 x24: x24
STACK CFI 8cf0 x25: x25
STACK CFI 8cf4 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^
STACK CFI 8cf8 x23: x23 x24: x24
STACK CFI 8cfc x25: x25
STACK CFI INIT 8d00 ec .cfa: sp 0 + .ra: x30
STACK CFI 8d08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d24 x21: .cfa -16 + ^
STACK CFI 8d70 x21: x21
STACK CFI 8d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8d80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8dd8 x21: x21
STACK CFI 8de4 x21: .cfa -16 + ^
STACK CFI 8de8 x21: x21
STACK CFI INIT 8df0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 8df4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8dfc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8e04 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8e7c x23: .cfa -80 + ^
STACK CFI 8ebc x23: x23
STACK CFI 8ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8ee4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 8f40 x23: .cfa -80 + ^
STACK CFI 8f64 x23: x23
STACK CFI 8f8c x23: .cfa -80 + ^
STACK CFI INIT 8f90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8fa8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8fc0 64 .cfa: sp 0 + .ra: x30
STACK CFI 8fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fd4 x19: .cfa -16 + ^
STACK CFI 8ff8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9028 64 .cfa: sp 0 + .ra: x30
STACK CFI 9030 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 903c x19: .cfa -16 + ^
STACK CFI 9060 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9064 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 907c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9090 330 .cfa: sp 0 + .ra: x30
STACK CFI 9094 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 90a0 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 90b0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 90b8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 90cc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 9228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 922c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 93c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 93c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 93d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9408 x21: .cfa -48 + ^
STACK CFI 9490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9494 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 94c0 12c .cfa: sp 0 + .ra: x30
STACK CFI 94c4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 94d4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 94e4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 9500 x23: .cfa -144 + ^
STACK CFI 95a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 95ac .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT 95f0 27c .cfa: sp 0 + .ra: x30
STACK CFI 95f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9604 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9610 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9628 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9640 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 970c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 97c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 97cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9838 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9870 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9888 d0 .cfa: sp 0 + .ra: x30
STACK CFI 988c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9898 x21: .cfa -80 + ^
STACK CFI 98a0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 994c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9958 98 .cfa: sp 0 + .ra: x30
STACK CFI 995c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9964 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 99e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 99ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI INIT 99f0 94 .cfa: sp 0 + .ra: x30
STACK CFI 99f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 9a04 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 9a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9a58 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI INIT 9a88 fc .cfa: sp 0 + .ra: x30
STACK CFI 9a8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a98 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9ab8 x21: .cfa -32 + ^
STACK CFI 9af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9afc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9b88 6fc .cfa: sp 0 + .ra: x30
STACK CFI 9b8c .cfa: sp 256 +
STACK CFI 9b90 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 9b98 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 9ba4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 9bb4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 9bd0 x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 9fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9fcc .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT a288 15c .cfa: sp 0 + .ra: x30
STACK CFI a28c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a29c x21: .cfa -32 + ^
STACK CFI a2b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a344 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT a3e8 54 .cfa: sp 0 + .ra: x30
STACK CFI a3ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a424 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a440 528 .cfa: sp 0 + .ra: x30
STACK CFI a444 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI a44c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI a45c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI a490 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI a4bc x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI a4c0 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a5a0 x23: x23 x24: x24
STACK CFI a5a4 x25: x25 x26: x26
STACK CFI a5a8 x27: x27 x28: x28
STACK CFI a5ac x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a664 x23: x23 x24: x24
STACK CFI a668 x25: x25 x26: x26
STACK CFI a66c x27: x27 x28: x28
STACK CFI a6d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a6dc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI a6f0 x23: x23 x24: x24
STACK CFI a6f4 x25: x25 x26: x26
STACK CFI a6f8 x27: x27 x28: x28
STACK CFI a6fc x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a7a8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a7ac x23: x23 x24: x24
STACK CFI a7b8 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a80c x23: x23 x24: x24
STACK CFI a810 x25: x25 x26: x26
STACK CFI a814 x27: x27 x28: x28
STACK CFI a818 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a824 x23: x23 x24: x24
STACK CFI a828 x25: x25 x26: x26
STACK CFI a82c x27: x27 x28: x28
STACK CFI a830 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a914 x23: x23 x24: x24
STACK CFI a924 x25: x25 x26: x26
STACK CFI a928 x27: x27 x28: x28
STACK CFI a92c x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a958 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a95c x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI a960 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI a964 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT a968 cc .cfa: sp 0 + .ra: x30
STACK CFI a96c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a978 x19: .cfa -16 + ^
STACK CFI a9d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aa10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT aa38 80 .cfa: sp 0 + .ra: x30
STACK CFI aa3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI aa54 x19: .cfa -96 + ^
STACK CFI aab0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aab4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT aab8 158 .cfa: sp 0 + .ra: x30
STACK CFI aabc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI aacc x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI aadc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI ab08 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI aba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI abac .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI INIT ac10 174 .cfa: sp 0 + .ra: x30
STACK CFI ac14 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI ac24 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI ac40 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI acd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI acd4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI INIT ad88 58 .cfa: sp 0 + .ra: x30
STACK CFI ad8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ada8 x21: .cfa -16 + ^
STACK CFI add0 x21: x21
STACK CFI addc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ade0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ade8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae18 6c .cfa: sp 0 + .ra: x30
STACK CFI ae1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae2c x19: .cfa -16 + ^
STACK CFI ae6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ae70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ae80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae88 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ae98 54 .cfa: sp 0 + .ra: x30
STACK CFI aea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aea8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aedc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI aee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aef0 40 .cfa: sp 0 + .ra: x30
STACK CFI af04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI af20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT af30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT af40 ec .cfa: sp 0 + .ra: x30
STACK CFI af68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI af70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af80 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI afc0 x21: x21 x22: x22
STACK CFI afc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI afcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aff0 x21: x21 x22: x22
STACK CFI b004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b008 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b030 4b0 .cfa: sp 0 + .ra: x30
STACK CFI b034 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b03c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b058 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b06c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b080 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b088 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b148 x21: x21 x22: x22
STACK CFI b14c x23: x23 x24: x24
STACK CFI b150 x25: x25 x26: x26
STACK CFI b154 x27: x27 x28: x28
STACK CFI b170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b174 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI b178 x21: x21 x22: x22
STACK CFI b17c x27: x27 x28: x28
STACK CFI b180 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b384 x21: x21 x22: x22
STACK CFI b388 x23: x23 x24: x24
STACK CFI b38c x25: x25 x26: x26
STACK CFI b390 x27: x27 x28: x28
STACK CFI b394 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b398 x21: x21 x22: x22
STACK CFI b39c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b4cc x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b4d0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b4d4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b4d8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b4dc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT b4e0 270 .cfa: sp 0 + .ra: x30
STACK CFI b4e4 .cfa: sp 608 +
STACK CFI b4e8 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI b4f0 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI b4fc x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI b530 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI b594 x19: x19 x20: x20
STACK CFI b5c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b5c4 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x29: .cfa -608 + ^
STACK CFI b5e8 x25: .cfa -544 + ^
STACK CFI b6e0 x19: x19 x20: x20
STACK CFI b6e4 x25: x25
STACK CFI b6e8 x19: .cfa -592 + ^ x20: .cfa -584 + ^ x25: .cfa -544 + ^
STACK CFI b6ec x19: x19 x20: x20
STACK CFI b6f0 x25: x25
STACK CFI b6f4 x19: .cfa -592 + ^ x20: .cfa -584 + ^ x25: .cfa -544 + ^
STACK CFI b6f8 x19: x19 x20: x20
STACK CFI b6fc x25: x25
STACK CFI b708 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI b718 x19: x19 x20: x20
STACK CFI b71c x19: .cfa -592 + ^ x20: .cfa -584 + ^ x25: .cfa -544 + ^
STACK CFI b720 x25: x25
STACK CFI b724 x25: .cfa -544 + ^
STACK CFI b73c x19: x19 x20: x20
STACK CFI b740 x25: x25
STACK CFI b748 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI b74c x25: .cfa -544 + ^
STACK CFI INIT b750 44 .cfa: sp 0 + .ra: x30
STACK CFI b754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b75c x19: .cfa -16 + ^
STACK CFI b780 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b784 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b798 d4 .cfa: sp 0 + .ra: x30
STACK CFI b79c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b7a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b7b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b7bc x23: .cfa -16 + ^
STACK CFI b808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b80c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b83c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT b870 508 .cfa: sp 0 + .ra: x30
STACK CFI b874 .cfa: sp 832 +
STACK CFI b878 .ra: .cfa -824 + ^ x29: .cfa -832 + ^
STACK CFI b880 x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI b890 x21: .cfa -800 + ^ x22: .cfa -792 + ^
STACK CFI b8b4 x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI b8c4 x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI b8cc x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI b9f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b9f4 .cfa: sp 832 + .ra: .cfa -824 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^ x29: .cfa -832 + ^
STACK CFI INIT bd78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT bd80 e0 .cfa: sp 0 + .ra: x30
STACK CFI bd84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bd90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bd9c x21: .cfa -16 + ^
STACK CFI bde8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI bdec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT be60 b4 .cfa: sp 0 + .ra: x30
STACK CFI be64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI be78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI be94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bedc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bee0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bf00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bf18 19c .cfa: sp 0 + .ra: x30
STACK CFI bf1c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI bf24 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI bf34 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI bfc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bfc8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI c014 x23: .cfa -176 + ^
STACK CFI c048 x23: x23
STACK CFI c04c x23: .cfa -176 + ^
STACK CFI c078 x23: x23
STACK CFI c07c x23: .cfa -176 + ^
STACK CFI c0a8 x23: x23
STACK CFI c0b0 x23: .cfa -176 + ^
STACK CFI INIT c0b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c0c8 17c .cfa: sp 0 + .ra: x30
STACK CFI c0cc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI c0d4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI c0e0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI c0fc x23: .cfa -160 + ^
STACK CFI c11c x23: x23
STACK CFI c198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c19c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI c1d4 x23: x23
STACK CFI c1d8 x23: .cfa -160 + ^
STACK CFI c228 x23: x23
STACK CFI c240 x23: .cfa -160 + ^
STACK CFI INIT c248 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c258 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c260 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c280 30 .cfa: sp 0 + .ra: x30
STACK CFI c284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c28c x19: .cfa -16 + ^
STACK CFI c2ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c2b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c2c0 144 .cfa: sp 0 + .ra: x30
STACK CFI c2c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c2cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c2dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c2e4 x23: .cfa -32 + ^
STACK CFI c35c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c360 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI c3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c3b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT c408 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c428 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c448 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c468 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c488 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT c4b0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c4d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c4d8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT c4f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c510 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT c530 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c538 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c540 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c550 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c560 60 .cfa: sp 0 + .ra: x30
STACK CFI c564 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c56c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c574 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c5bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT c5c0 60 .cfa: sp 0 + .ra: x30
STACK CFI c5c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c5cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c5d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c61c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT c620 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c638 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c648 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c658 b4 .cfa: sp 0 + .ra: x30
STACK CFI c65c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c664 x21: .cfa -64 + ^
STACK CFI c694 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c6e4 x19: x19 x20: x20
STACK CFI c700 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI c704 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI c708 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT c710 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c728 3f8 .cfa: sp 0 + .ra: x30
STACK CFI c72c .cfa: sp 624 +
STACK CFI c730 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI c738 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI c748 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI c75c x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI c764 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI c8e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c8e8 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x29: .cfa -624 + ^
STACK CFI INIT cb20 d0 .cfa: sp 0 + .ra: x30
STACK CFI cb24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cb2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cb38 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cb44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cb50 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb5c x27: .cfa -16 + ^
STACK CFI cb98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI cb9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI cbd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI cbd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT cbf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cbf8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc18 80 .cfa: sp 0 + .ra: x30
STACK CFI cc1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cc60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cc64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cc94 .cfa: sp 0 + .ra: .ra x29: x29
