MODULE Linux arm64 6F7C469E67D180A91B11DF3F4FBC5CE10 libimage_interface.so
INFO CODE_ID 9E467C6FD167A9801B11DF3F4FBC5CE1
PUBLIC 2078 0 _init
PUBLIC 2330 0 _GLOBAL__sub_I_image_nvmedia.cpp
PUBLIC 23b0 0 call_weak_fn
PUBLIC 23c4 0 deregister_tm_clones
PUBLIC 23f4 0 register_tm_clones
PUBLIC 2430 0 __do_global_dtors_aux
PUBLIC 2480 0 frame_dummy
PUBLIC 2490 0 lios::image::GetImageBufAttrList(linvs::buf::BufAttrList*, lios::image::ImageType, int, int)
PUBLIC 28b0 0 lios::image::AllocImages(std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >*, int, lios::image::ImageType, int, int, signed char)
PUBLIC 2ff0 0 std::unordered_map<lios::image::ImageSupportFeature, linvs::buf::BufAttrList, std::hash<lios::image::ImageSupportFeature>, std::equal_to<lios::image::ImageSupportFeature>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> > >::~unordered_map()
PUBLIC 3070 0 std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >::_M_default_append(unsigned long)
PUBLIC 3240 0 void std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >::_M_realloc_insert<linvs::buf::BufAttrList const*>(__gnu_cxx::__normal_iterator<linvs::buf::BufAttrList const**, std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > >, linvs::buf::BufAttrList const*&&)
PUBLIC 3370 0 std::_Hashtable<lios::image::ImageSupportFeature, std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> >, std::__detail::_Select1st, std::equal_to<lios::image::ImageSupportFeature>, std::hash<lios::image::ImageSupportFeature>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 34a0 0 std::__detail::_Map_base<lios::image::ImageSupportFeature, std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> >, std::__detail::_Select1st, std::equal_to<lios::image::ImageSupportFeature>, std::hash<lios::image::ImageSupportFeature>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](lios::image::ImageSupportFeature&&)
PUBLIC 362c 0 _fini
STACK CFI INIT 23c4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23f4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2430 50 .cfa: sp 0 + .ra: x30
STACK CFI 2440 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2448 x19: .cfa -16 + ^
STACK CFI 2478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2480 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2ff4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ffc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 300c x21: .cfa -16 + ^
STACK CFI 3030 x21: x21
STACK CFI 305c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2490 41c .cfa: sp 0 + .ra: x30
STACK CFI 2494 .cfa: sp 560 +
STACK CFI 24ac .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 24b4 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 24c0 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 24cc x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 252c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2530 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI 2544 x25: .cfa -496 + ^
STACK CFI 26e0 x25: x25
STACK CFI 26e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26e8 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2898 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI INIT 3070 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 3078 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3080 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3088 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 30a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 30e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 30ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 30f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 31a0 x27: x27 x28: x28
STACK CFI 31c0 x25: x25 x26: x26
STACK CFI 31c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 31c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3240 128 .cfa: sp 0 + .ra: x30
STACK CFI 3244 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3254 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3268 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 32f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 32f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3370 124 .cfa: sp 0 + .ra: x30
STACK CFI 3374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 338c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 342c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 34a0 18c .cfa: sp 0 + .ra: x30
STACK CFI 34a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 34b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3524 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 352c x23: .cfa -32 + ^
STACK CFI 35b8 x23: x23
STACK CFI 35d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 35d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 28b0 740 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 28bc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 28c8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 28d4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 28e0 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 28e8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2a50 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2330 80 .cfa: sp 0 + .ra: x30
STACK CFI 2334 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 233c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2348 x21: .cfa -16 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
