MODULE Linux arm64 F0C5CB701CF9DEB70B9CFE0444B2DC2C0 libresolv.so.2
INFO CODE_ID 70CBC5F0F91CB7DE0B9CFE0444B2DC2CC6EAD4DF
PUBLIC 3a98 0 __dn_expand
PUBLIC 3ae0 0 __dn_comp
PUBLIC 3ae8 0 __dn_skipname
PUBLIC 3b20 0 __res_hnok
PUBLIC 3bd0 0 __res_ownok
PUBLIC 3ca0 0 __res_mailok
PUBLIC 3d58 0 __res_dnok
PUBLIC 3de8 0 __putlong
PUBLIC 3df0 0 __putshort
PUBLIC 3df8 0 _getlong
PUBLIC 3e10 0 _getshort
PUBLIC 47a0 0 __fp_nquery
PUBLIC 4d08 0 __fp_query
PUBLIC 4d18 0 __p_query
PUBLIC 4d30 0 __p_cdnname
PUBLIC 4de8 0 __p_cdname
PUBLIC 4df8 0 __p_fqnname
PUBLIC 4e90 0 __p_fqname
PUBLIC 4f18 0 __sym_ston
PUBLIC 4f98 0 __sym_ntos
PUBLIC 5020 0 __sym_ntop
PUBLIC 50a8 0 __p_type
PUBLIC 5120 0 __p_class
PUBLIC 5198 0 __p_option
PUBLIC 5348 0 __fp_resstat
PUBLIC 53e8 0 __p_time
PUBLIC 5460 0 __p_rcode
PUBLIC 54d8 0 __loc_aton
PUBLIC 58d8 0 __loc_ntoa
PUBLIC 5bd0 0 __dn_count_labels
PUBLIC 5c60 0 __p_secstodate
PUBLIC 5d40 0 __res_close
PUBLIC 5fe0 0 __res_nmkquery
PUBLIC 6080 0 __res_mkquery
PUBLIC 61b8 0 __res_context_query
PUBLIC 6980 0 __res_nquery
PUBLIC 6a38 0 __res_query
PUBLIC 6af0 0 __res_nquerydomain
PUBLIC 6bc0 0 __res_querydomain
PUBLIC 6c80 0 __res_context_hostalias
PUBLIC 6e50 0 __res_context_search
PUBLIC 74d0 0 __res_nsearch
PUBLIC 7588 0 __res_search
PUBLIC 7640 0 __res_hostalias
PUBLIC 76d8 0 __hostalias
PUBLIC 8240 0 __res_isourserver
PUBLIC 8258 0 __res_nameinquery
PUBLIC 8390 0 __res_queriesmatch
PUBLIC 9588 0 __res_nsend
PUBLIC 95d0 0 __res_send
PUBLIC 9618 0 inet_net_ntop
PUBLIC 9800 0 inet_net_pton
PUBLIC 9c20 0 inet_neta
PUBLIC 9d20 0 __b64_ntop
PUBLIC 9e90 0 __b64_pton
PUBLIC a1c0 0 ns_msg_getflag
PUBLIC a1e8 0 ns_skiprr
PUBLIC a2b0 0 ns_initparse
PUBLIC a3e8 0 ns_parserr
PUBLIC a668 0 __ns_name_ntop
PUBLIC a828 0 ns_name_pton
PUBLIC aa30 0 ns_name_ntol
PUBLIC ab68 0 ns_name_unpack
PUBLIC acf0 0 ns_name_pack
PUBLIC b060 0 ns_name_uncompress
PUBLIC b0f8 0 ns_name_compress
PUBLIC b190 0 ns_name_rollback
PUBLIC b1c8 0 ns_name_skip
PUBLIC b240 0 __ns_get16
PUBLIC b250 0 ns_get32
PUBLIC b260 0 ns_put16
PUBLIC b270 0 ns_put32
PUBLIC b340 0 ns_format_ttl
PUBLIC b520 0 ns_parse_ttl
PUBLIC bbc0 0 ns_sprintrrf
PUBLIC cfb0 0 ns_sprintrr
PUBLIC d020 0 ns_samedomain
PUBLIC d1b0 0 ns_makecanon
PUBLIC d2b8 0 ns_samename
PUBLIC d360 0 ns_subdomain
PUBLIC d458 0 ns_datetosecs
PUBLIC d788 0 res_send_setqhook
PUBLIC d7a0 0 res_send_setrhook
PUBLIC d800 0 _sethtent
PUBLIC d860 0 _gethtent
PUBLIC dac8 0 _gethtbyname2
PUBLIC db50 0 _gethtbyname
PUBLIC db58 0 _gethtbyaddr
PUBLIC e6b8 0 res_gethostbyaddr
PUBLIC e9f0 0 res_gethostbyname2
PUBLIC ea58 0 res_gethostbyname
STACK CFI INIT 3968 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3998 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 39dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39e4 x19: .cfa -16 + ^
STACK CFI 3a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a28 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a98 48 .cfa: sp 0 + .ra: x30
STACK CFI 3a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3aa8 x19: .cfa -16 + ^
STACK CFI 3acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3adc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ae8 38 .cfa: sp 0 + .ra: x30
STACK CFI 3aec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3af4 x19: .cfa -32 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b20 ac .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3b2c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b90 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3bd0 cc .cfa: sp 0 + .ra: x30
STACK CFI 3bd4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3bdc x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c40 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3ca0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3ca4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3cac x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d10 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3d58 8c .cfa: sp 0 + .ra: x30
STACK CFI 3d5c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3d64 x19: .cfa -288 + ^
STACK CFI 3dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dc8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3de8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3df0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3df8 14 .cfa: sp 0 + .ra: x30
STACK CFI 3dfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e10 14 .cfa: sp 0 + .ra: x30
STACK CFI 3e14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e28 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3e48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e80 x19: .cfa -16 + ^
STACK CFI 3ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ed0 378 .cfa: sp 0 + .ra: x30
STACK CFI 3ed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3edc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4248 118 .cfa: sp 0 + .ra: x30
STACK CFI 424c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4254 x19: .cfa -16 + ^
STACK CFI 42f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4300 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4360 90 .cfa: sp 0 + .ra: x30
STACK CFI 43a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43b8 x19: .cfa -16 + ^
STACK CFI 43d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43f0 3ac .cfa: sp 0 + .ra: x30
STACK CFI 43f4 .cfa: sp 1200 +
STACK CFI 43fc .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 4404 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4454 .cfa: sp 1200 + .ra: .cfa -1192 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x29: .cfa -1200 + ^
STACK CFI 4458 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 446c x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 4470 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 44b4 x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 456c x27: x27 x28: x28
STACK CFI 4588 x19: x19 x20: x20
STACK CFI 458c x21: x21 x22: x22
STACK CFI 4590 x25: x25 x26: x26
STACK CFI 4594 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 46b4 x19: x19 x20: x20
STACK CFI 46b8 x21: x21 x22: x22
STACK CFI 46c0 x25: x25 x26: x26
STACK CFI 46c4 x27: x27 x28: x28
STACK CFI 46c8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 46cc .cfa: sp 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI 4788 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 478c x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 4790 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 4794 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 4798 x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI INIT 47a0 564 .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 47b4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 47bc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 47cc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4800 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 480c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 4b34 x25: x25 x26: x26
STACK CFI 4b38 x27: x27 x28: x28
STACK CFI 4b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4b64 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 4bcc x25: x25 x26: x26
STACK CFI 4bd0 x27: x27 x28: x28
STACK CFI 4bd4 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 4cd0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4cfc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4d00 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 4d08 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d18 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d30 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4d34 .cfa: sp 1104 +
STACK CFI 4d38 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 4d40 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 4d4c x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 4d64 x23: .cfa -1056 + ^
STACK CFI 4dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4dd0 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x29: .cfa -1104 + ^
STACK CFI INIT 4de8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4df8 98 .cfa: sp 0 + .ra: x30
STACK CFI 4dfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4e90 88 .cfa: sp 0 + .ra: x30
STACK CFI 4e94 .cfa: sp 1088 +
STACK CFI 4e9c .ra: .cfa -1080 + ^ x29: .cfa -1088 + ^
STACK CFI 4ea4 x19: .cfa -1072 + ^ x20: .cfa -1064 + ^
STACK CFI 4eb0 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^
STACK CFI 4f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f14 .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x29: .cfa -1088 + ^
STACK CFI INIT 4f18 7c .cfa: sp 0 + .ra: x30
STACK CFI 4f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f34 x21: .cfa -16 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4f98 88 .cfa: sp 0 + .ra: x30
STACK CFI 4f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 501c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5020 88 .cfa: sp 0 + .ra: x30
STACK CFI 5024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 502c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 506c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5070 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 50a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 50a8 74 .cfa: sp 0 + .ra: x30
STACK CFI 50e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50f8 x19: .cfa -16 + ^
STACK CFI 5118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5120 74 .cfa: sp 0 + .ra: x30
STACK CFI 5160 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5170 x19: .cfa -16 + ^
STACK CFI 5190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5198 1ac .cfa: sp 0 + .ra: x30
STACK CFI 5288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5298 x19: .cfa -16 + ^
STACK CFI 52b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5348 a0 .cfa: sp 0 + .ra: x30
STACK CFI 534c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 535c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5368 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5380 x23: .cfa -16 + ^
STACK CFI 53e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 53e8 78 .cfa: sp 0 + .ra: x30
STACK CFI 53ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 540c x21: .cfa -16 + ^
STACK CFI 5430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5434 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 545c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5460 74 .cfa: sp 0 + .ra: x30
STACK CFI 54a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54b0 x19: .cfa -16 + ^
STACK CFI 54d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 54d8 400 .cfa: sp 0 + .ra: x30
STACK CFI 54dc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 54e4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 54ec x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 54f8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5518 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5584 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 559c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 56f8 x27: x27 x28: x28
STACK CFI 571c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 58d0 x27: x27 x28: x28
STACK CFI 58d4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 58d8 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 58dc .cfa: sp 208 +
STACK CFI 58f0 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 58fc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5914 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 591c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5928 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5934 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5b34 x21: x21 x22: x22
STACK CFI 5b38 x23: x23 x24: x24
STACK CFI 5b3c x25: x25 x26: x26
STACK CFI 5b40 x27: x27 x28: x28
STACK CFI 5b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b48 .cfa: sp 208 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5b6c x21: x21 x22: x22
STACK CFI 5b70 x23: x23 x24: x24
STACK CFI 5b74 x25: x25 x26: x26
STACK CFI 5b78 x27: x27 x28: x28
STACK CFI 5b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b80 .cfa: sp 208 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 5ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bac .cfa: sp 208 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5bd0 90 .cfa: sp 0 + .ra: x30
STACK CFI 5bd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bdc x19: .cfa -16 + ^
STACK CFI 5c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c60 dc .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5c74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d38 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5d40 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d68 278 .cfa: sp 0 + .ra: x30
STACK CFI 5d6c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5d74 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 5d7c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5da8 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5dcc x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5de0 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5ee8 x19: x19 x20: x20
STACK CFI 5eec x25: x25 x26: x26
STACK CFI 5ef0 x27: x27 x28: x28
STACK CFI 5ef4 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5ef8 x19: x19 x20: x20
STACK CFI 5efc x25: x25 x26: x26
STACK CFI 5f00 x27: x27 x28: x28
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5f28 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 5fa4 x19: x19 x20: x20
STACK CFI 5fa8 x25: x25 x26: x26
STACK CFI 5fac x27: x27 x28: x28
STACK CFI 5fb0 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5fb4 x19: x19 x20: x20
STACK CFI 5fbc x19: .cfa -272 + ^ x20: .cfa -264 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5fc4 x19: x19 x20: x20
STACK CFI 5fc8 x25: x25 x26: x26
STACK CFI 5fcc x27: x27 x28: x28
STACK CFI 5fd4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5fd8 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5fdc x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 5fe0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5fe4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5fec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ff4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6000 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 600c x25: .cfa -16 + ^
STACK CFI 6074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6078 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6080 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6084 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 608c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6094 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 60a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 60ac x25: .cfa -16 + ^
STACK CFI 6114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6118 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6120 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61b8 654 .cfa: sp 0 + .ra: x30
STACK CFI 61bc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 61c4 .cfa: x29 192 +
STACK CFI 61c8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 61d4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 61e0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 620c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 622c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 6440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6444 .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 6810 170 .cfa: sp 0 + .ra: x30
STACK CFI 6814 .cfa: sp 1216 +
STACK CFI 6818 .ra: .cfa -1176 + ^ x29: .cfa -1184 + ^
STACK CFI 6820 x19: .cfa -1168 + ^ x20: .cfa -1160 + ^
STACK CFI 6830 x25: .cfa -1120 + ^ x26: .cfa -1112 + ^
STACK CFI 684c x21: .cfa -1152 + ^ x22: .cfa -1144 + ^
STACK CFI 6858 x23: .cfa -1136 + ^ x24: .cfa -1128 + ^
STACK CFI 6864 x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 6944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6948 .cfa: sp 1216 + .ra: .cfa -1176 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^ x29: .cfa -1184 + ^
STACK CFI INIT 6980 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6984 .cfa: sp 96 +
STACK CFI 6988 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6990 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6998 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 69a4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6a08 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a38 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6a3c .cfa: sp 96 +
STACK CFI 6a40 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6a48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6a50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a5c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ac0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6af0 cc .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 128 +
STACK CFI 6af8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6b00 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6b08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6b14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6b20 x25: .cfa -32 + ^
STACK CFI 6b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6b90 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6bc0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6bc4 .cfa: sp 112 +
STACK CFI 6bc8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6bd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6bd8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6be4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6bf0 x25: .cfa -16 + ^
STACK CFI 6c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6c54 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6c80 1cc .cfa: sp 0 + .ra: x30
STACK CFI 6c88 .cfa: sp 8304 +
STACK CFI 6c8c .ra: .cfa -8296 + ^ x29: .cfa -8304 + ^
STACK CFI 6c98 x25: .cfa -8240 + ^ x26: .cfa -8232 + ^
STACK CFI 6cc4 x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI 6ccc x27: .cfa -8224 + ^
STACK CFI 6cf4 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 6cf8 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 6dd0 x19: x19 x20: x20
STACK CFI 6dd4 x21: x21 x22: x22
STACK CFI 6dd8 x23: x23 x24: x24
STACK CFI 6ddc x27: x27
STACK CFI 6de0 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^ x21: .cfa -8272 + ^ x22: .cfa -8264 + ^ x23: .cfa -8256 + ^ x24: .cfa -8248 + ^ x27: .cfa -8224 + ^
STACK CFI 6dec x19: x19 x20: x20
STACK CFI 6df4 x21: x21 x22: x22
STACK CFI 6df8 x23: x23 x24: x24
STACK CFI 6dfc x27: x27
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 6e28 .cfa: sp 8304 + .ra: .cfa -8296 + ^ x23: .cfa -8256 + ^ x24: .cfa -8248 + ^ x25: .cfa -8240 + ^ x26: .cfa -8232 + ^ x27: .cfa -8224 + ^ x29: .cfa -8304 + ^
STACK CFI 6e2c x23: x23 x24: x24
STACK CFI 6e30 x27: x27
STACK CFI 6e3c x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 6e40 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 6e44 x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI 6e48 x27: .cfa -8224 + ^
STACK CFI INIT 6e50 680 .cfa: sp 0 + .ra: x30
STACK CFI 6e54 .cfa: sp 1264 +
STACK CFI 6e6c .ra: .cfa -1224 + ^ x29: .cfa -1232 + ^
STACK CFI 6e74 x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 6e80 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 6e8c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^
STACK CFI 6ea0 x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^
STACK CFI 71d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 71dc .cfa: sp 1264 + .ra: .cfa -1224 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^ x29: .cfa -1232 + ^
STACK CFI INIT 74d0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 96 +
STACK CFI 74d8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 74e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 74e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7558 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7588 b4 .cfa: sp 0 + .ra: x30
STACK CFI 758c .cfa: sp 96 +
STACK CFI 7590 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7598 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 760c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7610 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7640 94 .cfa: sp 0 + .ra: x30
STACK CFI 7644 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 764c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7654 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 769c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 76d8 84 .cfa: sp 0 + .ra: x30
STACK CFI 76dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7724 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7760 28 .cfa: sp 0 + .ra: x30
STACK CFI 7764 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7788 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 778c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7794 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 779c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 77bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 77c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 77cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 781c x23: x23 x24: x24
STACK CFI 7830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7834 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 78dc x23: x23 x24: x24
STACK CFI 78e0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 790c x23: x23 x24: x24
STACK CFI 7910 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7924 x23: x23 x24: x24
STACK CFI 7928 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 7930 100 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a30 690 .cfa: sp 0 + .ra: x30
STACK CFI 7a34 .cfa: sp 880 +
STACK CFI 7a3c .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 7a80 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 7a8c x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 7ab4 x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI 7c00 x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 7c14 x23: .cfa -832 + ^ x24: .cfa -824 + ^
STACK CFI 7d44 x23: x23 x24: x24
STACK CFI 7d48 x25: x25 x26: x26
STACK CFI 7d7c x21: x21 x22: x22
STACK CFI 7d80 x27: x27 x28: x28
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d88 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x27: .cfa -800 + ^ x28: .cfa -792 + ^ x29: .cfa -880 + ^
STACK CFI 7d98 x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 7ddc x23: x23 x24: x24
STACK CFI 7de0 x25: x25 x26: x26
STACK CFI 7de4 x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 7e2c x23: x23 x24: x24
STACK CFI 7e30 x25: x25 x26: x26
STACK CFI 7e34 x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 7f4c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7fa4 x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 7fe4 x23: x23 x24: x24
STACK CFI 7fe8 x25: x25 x26: x26
STACK CFI 8074 x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 807c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8080 x23: .cfa -832 + ^ x24: .cfa -824 + ^
STACK CFI 8084 x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI 8088 x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI 80ac x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 80b0 x23: .cfa -832 + ^ x24: .cfa -824 + ^
STACK CFI 80b4 x25: .cfa -816 + ^ x26: .cfa -808 + ^
STACK CFI INIT 80c0 17c .cfa: sp 0 + .ra: x30
STACK CFI 8234 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8240 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8258 134 .cfa: sp 0 + .ra: x30
STACK CFI 825c .cfa: sp 1136 +
STACK CFI 8260 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 8268 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 8274 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 828c x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 82ac x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 82b8 x27: .cfa -1056 + ^
STACK CFI 8334 x25: x25 x26: x26
STACK CFI 8338 x27: x27
STACK CFI 8364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8368 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x29: .cfa -1136 + ^
STACK CFI 8374 x25: x25 x26: x26
STACK CFI 8378 x27: x27
STACK CFI 8384 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 8388 x27: .cfa -1056 + ^
STACK CFI INIT 8390 164 .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 1120 +
STACK CFI 8398 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 83a0 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 83a8 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 83b8 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 83d8 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 8494 x21: x21 x22: x22
STACK CFI 84c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 84c8 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x29: .cfa -1120 + ^
STACK CFI 84cc x21: x21 x22: x22
STACK CFI 84d0 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 84e8 x21: x21 x22: x22
STACK CFI 84f0 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI INIT 84f8 1004 .cfa: sp 0 + .ra: x30
STACK CFI 84fc .cfa: sp 592 +
STACK CFI 8500 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 850c x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 8520 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 856c x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 858c x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 8594 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 8940 x19: x19 x20: x20
STACK CFI 8944 x23: x23 x24: x24
STACK CFI 8948 x25: x25 x26: x26
STACK CFI 894c x19: .cfa -528 + ^ x20: .cfa -520 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 89cc x19: x19 x20: x20
STACK CFI 89d4 x23: x23 x24: x24
STACK CFI 89d8 x25: x25 x26: x26
STACK CFI 8a08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8a0c .cfa: sp 592 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI 8bd8 x19: x19 x20: x20
STACK CFI 8bdc x23: x23 x24: x24
STACK CFI 8be0 x25: x25 x26: x26
STACK CFI 8be4 x19: .cfa -528 + ^ x20: .cfa -520 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 8bf8 x19: x19 x20: x20
STACK CFI 8bfc x23: x23 x24: x24
STACK CFI 8c00 x25: x25 x26: x26
STACK CFI 8c04 x19: .cfa -528 + ^ x20: .cfa -520 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 8d28 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8d44 x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 8d5c x25: x25 x26: x26
STACK CFI 8d64 x19: .cfa -528 + ^ x20: .cfa -520 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 8e10 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8e14 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 8e18 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 8e1c x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI INIT 9500 84 .cfa: sp 0 + .ra: x30
STACK CFI 9504 .cfa: sp 64 +
STACK CFI 9508 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9510 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9558 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9588 44 .cfa: sp 0 + .ra: x30
STACK CFI 958c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9594 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 95a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 95c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 95d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 95d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 95dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 95e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9618 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 9640 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 964c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9658 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9660 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 96a0 x19: x19 x20: x20
STACK CFI 96a4 x23: x23 x24: x24
STACK CFI 96ac .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 96b0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 96b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 96d8 x27: .cfa -16 + ^
STACK CFI 9724 x27: x27
STACK CFI 9790 x21: x21 x22: x22
STACK CFI 9794 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 9798 x21: x21 x22: x22
STACK CFI 979c x27: x27
STACK CFI 97b4 x19: x19 x20: x20
STACK CFI 97bc x23: x23 x24: x24
STACK CFI 97c4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 97c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 97e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 97e8 x21: x21 x22: x22
STACK CFI 97ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 97f4 x21: x21 x22: x22
STACK CFI 97f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 9800 420 .cfa: sp 0 + .ra: x30
STACK CFI 9804 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9818 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9820 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 982c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9834 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 983c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 98e0 x21: x21 x22: x22
STACK CFI 98e8 x23: x23 x24: x24
STACK CFI 98f0 x25: x25 x26: x26
STACK CFI 98f4 x27: x27 x28: x28
STACK CFI 9900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9904 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 9a20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9a3c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9af8 x21: x21 x22: x22
STACK CFI 9afc x23: x23 x24: x24
STACK CFI 9b00 x25: x25 x26: x26
STACK CFI 9b04 x27: x27 x28: x28
STACK CFI 9b08 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 9c20 fc .cfa: sp 0 + .ra: x30
STACK CFI 9c24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9c30 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9c48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9ca0 x23: x23 x24: x24
STACK CFI 9cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9cb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9cec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9cf0 x23: x23 x24: x24
STACK CFI 9d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9d20 170 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e90 330 .cfa: sp 0 + .ra: x30
STACK CFI 9e94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9e9c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9eac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9eb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9ec0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9ec8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a09c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a0dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a0e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a1c0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT a1e8 c8 .cfa: sp 0 + .ra: x30
STACK CFI a1ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a1fc x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a204 x23: .cfa -16 + ^
STACK CFI a288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a28c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a2ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT a2b0 134 .cfa: sp 0 + .ra: x30
STACK CFI a2b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a2bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a2c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a310 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a37c x23: x23 x24: x24
STACK CFI a388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a38c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a3b0 x23: x23 x24: x24
STACK CFI a3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a3d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a3e0 x23: x23 x24: x24
STACK CFI INIT a3e8 27c .cfa: sp 0 + .ra: x30
STACK CFI a3f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a3fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a40c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a43c x23: .cfa -16 + ^
STACK CFI a520 x23: x23
STACK CFI a538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a53c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a554 x23: x23
STACK CFI a55c x23: .cfa -16 + ^
STACK CFI a5cc x23: x23
STACK CFI a5e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a5e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a5f4 x23: x23
STACK CFI a5f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a5fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a600 x23: x23
STACK CFI a61c x23: .cfa -16 + ^
STACK CFI a634 x23: x23
STACK CFI a63c x23: .cfa -16 + ^
STACK CFI a644 x23: x23
STACK CFI a648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a668 1bc .cfa: sp 0 + .ra: x30
STACK CFI INIT a828 204 .cfa: sp 0 + .ra: x30
STACK CFI a82c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a834 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a83c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a844 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a854 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a860 x27: .cfa -16 + ^
STACK CFI a94c x23: x23 x24: x24
STACK CFI a950 x27: x27
STACK CFI a954 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI a9a8 x23: x23 x24: x24
STACK CFI a9ac x27: x27
STACK CFI a9d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI a9dc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI aa14 x23: x23 x24: x24
STACK CFI aa18 x27: x27
STACK CFI INIT aa30 134 .cfa: sp 0 + .ra: x30
STACK CFI aa34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI aa3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI aa4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI aa68 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI aa6c x27: .cfa -16 + ^
STACK CFI ab08 x19: x19 x20: x20
STACK CFI ab0c x25: x25 x26: x26
STACK CFI ab10 x27: x27
STACK CFI ab20 x23: x23 x24: x24
STACK CFI ab24 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI ab28 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI ab34 x19: x19 x20: x20
STACK CFI ab38 x23: x23 x24: x24
STACK CFI ab3c x25: x25 x26: x26
STACK CFI ab40 x27: x27
STACK CFI ab60 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT ab68 188 .cfa: sp 0 + .ra: x30
STACK CFI ab6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ab7c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI ab8c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ab98 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI aba8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI abb4 x27: .cfa -32 + ^
STACK CFI abec x19: x19 x20: x20
STACK CFI abf4 x21: x21 x22: x22
STACK CFI abfc x23: x23 x24: x24
STACK CFI ac04 x27: x27
STACK CFI ac10 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI ac14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI ac64 x19: x19 x20: x20
STACK CFI ac68 x21: x21 x22: x22
STACK CFI ac6c x23: x23 x24: x24
STACK CFI ac70 x27: x27
STACK CFI ac74 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI acb4 x19: x19 x20: x20
STACK CFI acb8 x21: x21 x22: x22
STACK CFI acbc x23: x23 x24: x24
STACK CFI acc0 x27: x27
STACK CFI acdc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI ace8 x19: x19 x20: x20
STACK CFI acec x21: x21 x22: x22
STACK CFI INIT acf0 370 .cfa: sp 0 + .ra: x30
STACK CFI acf4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI acfc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ad04 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI ad0c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ada4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ada8 .cfa: sp 128 + .ra: .cfa -120 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI adb0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI adc0 x19: x19 x20: x20
STACK CFI adc4 x23: x23 x24: x24
STACK CFI adf0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI adf4 .cfa: sp 128 + .ra: .cfa -120 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI ae08 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ae10 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b028 x19: x19 x20: x20
STACK CFI b02c x23: x23 x24: x24
STACK CFI b030 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b048 x19: x19 x20: x20
STACK CFI b050 x23: x23 x24: x24
STACK CFI b054 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b058 x19: x19 x20: x20
STACK CFI b05c x23: x23 x24: x24
STACK CFI INIT b060 98 .cfa: sp 0 + .ra: x30
STACK CFI b064 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI b06c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI b07c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI b0e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b0ec .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT b0f8 98 .cfa: sp 0 + .ra: x30
STACK CFI b0fc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI b104 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI b114 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI b130 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI b188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b18c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x29: .cfa -336 + ^
STACK CFI INIT b190 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT b1c8 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT b240 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b250 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b260 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b280 c0 .cfa: sp 0 + .ra: x30
STACK CFI b284 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b28c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b29c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b2b4 x23: .cfa -80 + ^
STACK CFI b330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b334 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT b340 1dc .cfa: sp 0 + .ra: x30
STACK CFI b344 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b354 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b364 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b37c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b394 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI b3a4 x27: .cfa -32 + ^
STACK CFI b488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b48c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT b520 168 .cfa: sp 0 + .ra: x30
STACK CFI b524 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b52c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b538 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b554 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b62c x25: x25 x26: x26
STACK CFI b658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b65c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b66c x25: x25 x26: x26
STACK CFI b684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT b688 d0 .cfa: sp 0 + .ra: x30
STACK CFI b68c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b694 x21: .cfa -16 + ^
STACK CFI b6a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b710 x19: x19 x20: x20
STACK CFI b714 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b72c x19: x19 x20: x20
STACK CFI b734 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI b738 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b744 x19: x19 x20: x20
STACK CFI b74c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI b750 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b758 28 .cfa: sp 0 + .ra: x30
STACK CFI b75c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b780 90 .cfa: sp 0 + .ra: x30
STACK CFI b784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b78c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b7a4 x21: .cfa -16 + ^
STACK CFI b7e4 x21: x21
STACK CFI b7ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b7f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b80c x21: .cfa -16 + ^
STACK CFI INIT b810 15c .cfa: sp 0 + .ra: x30
STACK CFI b814 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b81c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b82c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b834 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b840 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b928 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b964 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT b970 168 .cfa: sp 0 + .ra: x30
STACK CFI b974 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b984 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b98c x25: .cfa -32 + ^
STACK CFI b994 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b9a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ba44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ba48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT bad8 e4 .cfa: sp 0 + .ra: x30
STACK CFI badc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bae4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI baec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bb20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bb68 x23: x23 x24: x24
STACK CFI bb6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bb70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI bba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bba4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI bba8 x23: x23 x24: x24
STACK CFI bbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT bbc0 13f0 .cfa: sp 0 + .ra: x30
STACK CFI bbc8 .cfa: sp 8512 +
STACK CFI bbd0 .ra: .cfa -8504 + ^ x29: .cfa -8512 + ^
STACK CFI bbdc x19: .cfa -8496 + ^ x20: .cfa -8488 + ^
STACK CFI bc00 x21: .cfa -8480 + ^ x22: .cfa -8472 + ^ x23: .cfa -8464 + ^ x24: .cfa -8456 + ^
STACK CFI bc0c x25: .cfa -8448 + ^ x26: .cfa -8440 + ^
STACK CFI bc14 x27: .cfa -8432 + ^ x28: .cfa -8424 + ^
STACK CFI bf04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf08 .cfa: sp 8512 + .ra: .cfa -8504 + ^ x19: .cfa -8496 + ^ x20: .cfa -8488 + ^ x21: .cfa -8480 + ^ x22: .cfa -8472 + ^ x23: .cfa -8464 + ^ x24: .cfa -8456 + ^ x25: .cfa -8448 + ^ x26: .cfa -8440 + ^ x27: .cfa -8432 + ^ x28: .cfa -8424 + ^ x29: .cfa -8512 + ^
STACK CFI INIT cfb0 70 .cfa: sp 0 + .ra: x30
STACK CFI cfb4 .cfa: sp 48 +
STACK CFI cfcc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d01c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d020 190 .cfa: sp 0 + .ra: x30
STACK CFI d024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d02c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d034 x21: .cfa -16 + ^
STACK CFI d0fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d100 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT d1b0 104 .cfa: sp 0 + .ra: x30
STACK CFI d1b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d1bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d1c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d258 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d2b8 a4 .cfa: sp 0 + .ra: x30
STACK CFI d2bc .cfa: sp 2128 +
STACK CFI d2c4 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI d2cc x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI d2dc x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI d354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d358 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x29: .cfa -2128 + ^
STACK CFI INIT d360 50 .cfa: sp 0 + .ra: x30
STACK CFI d364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d36c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d3a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d3ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d3b0 a8 .cfa: sp 0 + .ra: x30
STACK CFI d3b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d3bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d3c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d3d4 x23: .cfa -16 + ^
STACK CFI d454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT d458 27c .cfa: sp 0 + .ra: x30
STACK CFI d45c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d464 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d48c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d490 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI d494 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d4a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d4ac x25: .cfa -16 + ^
STACK CFI d668 x21: x21 x22: x22
STACK CFI d66c x23: x23 x24: x24
STACK CFI d670 x25: x25
STACK CFI d674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d678 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT d6d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI d6dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d6ec x19: .cfa -32 + ^
STACK CFI d750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d754 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT d788 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT d7b8 44 .cfa: sp 0 + .ra: x30
STACK CFI d7bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d7c4 x19: .cfa -16 + ^
STACK CFI d7e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d7e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI d7f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d800 60 .cfa: sp 0 + .ra: x30
STACK CFI d804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d80c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d834 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d85c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d860 268 .cfa: sp 0 + .ra: x30
STACK CFI d864 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d86c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d874 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d880 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d8a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d958 x23: x23 x24: x24
STACK CFI d960 x25: x25 x26: x26
STACK CFI d974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d978 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI da58 x23: x23 x24: x24
STACK CFI da68 x25: x25 x26: x26
STACK CFI da78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI da7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI da90 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT dac8 84 .cfa: sp 0 + .ra: x30
STACK CFI dacc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI db48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT db50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT db58 70 .cfa: sp 0 + .ra: x30
STACK CFI db5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI db64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI db6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI dbc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT dbc8 164 .cfa: sp 0 + .ra: x30
STACK CFI dbcc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI dd1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI dd20 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT dd30 6b4 .cfa: sp 0 + .ra: x30
STACK CFI dd34 .cfa: sp 1232 +
STACK CFI dd44 .ra: .cfa -1224 + ^ x29: .cfa -1232 + ^
STACK CFI dd50 x19: .cfa -1216 + ^ x20: .cfa -1208 + ^
STACK CFI dd58 x21: .cfa -1200 + ^ x22: .cfa -1192 + ^
STACK CFI dd7c x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI ddc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ddcc .cfa: sp 1232 + .ra: .cfa -1224 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x29: .cfa -1232 + ^
STACK CFI ddd8 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI dfc0 x27: x27 x28: x28
STACK CFI dfd4 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI e148 x27: x27 x28: x28
STACK CFI e150 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI e324 x27: x27 x28: x28
STACK CFI e32c x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI e3b0 x27: x27 x28: x28
STACK CFI e3b4 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI INIT e3e8 2cc .cfa: sp 0 + .ra: x30
STACK CFI e3ec .cfa: sp 1168 +
STACK CFI e3f8 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI e3fc .cfa: x29 1136 +
STACK CFI e400 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI e420 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI e428 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI e594 .cfa: sp 1168 +
STACK CFI e5b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e5b4 .cfa: x29 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x29: .cfa -1136 + ^
STACK CFI INIT e6b8 84 .cfa: sp 0 + .ra: x30
STACK CFI e6bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e6c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e6cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e714 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI e738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT e740 2ac .cfa: sp 0 + .ra: x30
STACK CFI e744 .cfa: sp 1168 +
STACK CFI e750 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI e754 .cfa: x29 1136 +
STACK CFI e758 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI e768 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI e778 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x25: .cfa -1072 + ^
STACK CFI e8a4 .cfa: sp 1168 +
STACK CFI e8c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI e8c4 .cfa: x29 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x29: .cfa -1136 + ^
STACK CFI INIT e9f0 68 .cfa: sp 0 + .ra: x30
STACK CFI e9f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e9fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ea30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ea34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ea54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ea58 68 .cfa: sp 0 + .ra: x30
STACK CFI ea5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ea64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ea98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ea9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI eabc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT eac0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3930 24 .cfa: sp 0 + .ra: x30
STACK CFI 3934 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 394c .cfa: sp 0 + .ra: .ra x29: x29
