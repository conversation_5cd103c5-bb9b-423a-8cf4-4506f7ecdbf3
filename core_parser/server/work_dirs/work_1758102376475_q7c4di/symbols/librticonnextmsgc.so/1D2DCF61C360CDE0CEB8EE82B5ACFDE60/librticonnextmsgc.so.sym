MODULE Linux arm64 1D2DCF61C360CDE0CEB8EE82B5ACFDE60 librticonnextmsgc.so
INFO CODE_ID 61CF2D1D60C3E0CDCEB8EE82B5ACFDE6D0904EF8
PUBLIC 36d8 0 _init
PUBLIC 3cc0 0 call_weak_fn
PUBLIC 3cd8 0 deregister_tm_clones
PUBLIC 3d10 0 register_tm_clones
PUBLIC 3d50 0 __do_global_dtors_aux
PUBLIC 3d98 0 frame_dummy
PUBLIC 3dd0 0 RTI_Connext_Messaging_get_api_version
PUBLIC 3e0c 0 RTI_Connext_Messaging_Library_get_api_version_string
PUBLIC 3e64 0 RTI_Connext_Messaging_get_api_build_number_string
PUBLIC 3e70 0 RTI_Connext_EntityParams_delete
PUBLIC 3e98 0 RTI_Connext_EntityParams_validate
PUBLIC 41d4 0 RTI_Connext_Replier_on_data_available
PUBLIC 41f4 0 RTI_Connext_ReplierParams_toEntityParams
PUBLIC 424c 0 RTI_Connext_Replier_delete
PUBLIC 4358 0 RTI_Connext_Replier_wait_for_requests
PUBLIC 44c8 0 RTI_Connext_ReplierUntypedImpl_create_writer_topic
PUBLIC 4538 0 RTI_Connext_ReplierUntypedImpl_create_reader_topic
PUBLIC 45a4 0 RTI_Connext_ReplierUntypedImpl_create
PUBLIC 45b8 0 RTI_Connext_ReplierUntypedImpl_initialize
PUBLIC 46b0 0 RTI_Connext_ReplierUntypedImpl_configure_params_for_reply
PUBLIC 4764 0 RTI_Connext_ReplierUntypedImpl_send_sample
PUBLIC 4884 0 RTI_Connext_Requester_delete
PUBLIC 4990 0 RTI_Connext_Requester_wait_for_replies_for_related_request
PUBLIC 4a40 0 RTI_Connext_Requester_wait_for_replies
PUBLIC 4a58 0 RTI_Connext_RequesterUntypedImpl_create_writer_topic
PUBLIC 4b84 0 RTI_Connext_RequesterUntypedImpl_initializeWaitSet
PUBLIC 4b9c 0 RTI_Connext_RequesterUntypedImpl_finalizeWaitSet
PUBLIC 4bb0 0 RTI_Connext_CorrelationCFTBuilder_create_correlation_cft
PUBLIC 4f54 0 RTI_Connext_RequesterUntypedImpl_create_reader_topic
PUBLIC 5124 0 RTI_Connext_RequesterParams_to_RTI_Connext_EntityParams
PUBLIC 517c 0 RTI_Connext_RequesterUntypedImpl_create
PUBLIC 5460 0 RTI_Connext_RequesterUntypedImpl_create_query_expression_for_correlation_sequence_number
PUBLIC 553c 0 RTI_Connext_RequesterUntypedImpl_create_correlation_condition
PUBLIC 572c 0 RTI_Connext_RequesterUntypedImpl_wait_for_replies
PUBLIC 59ac 0 RTI_Connext_RequesterUntypedImpl_get_reply_loaned
PUBLIC 5b94 0 DebugDataWriterListener_on_offered_incompatible_qos
PUBLIC 5bb0 0 DebugDataWriterListener_on_publication_matched
PUBLIC 5bcc 0 RTI_Connext_get_default_request_reply_writer_qos
PUBLIC 5bf8 0 RTI_Connext_get_default_request_reply_reader_qos
PUBLIC 5c28 0 RTI_Connext_get_or_create_topic
PUBLIC 5d6c 0 RTI_Connext_create_request_topic_name_from_service_name
PUBLIC 5eac 0 RTI_Connext_create_reply_topic_name_from_service_name
PUBLIC 5fec 0 RTI_Connext_EntityUntypedImpl_create
PUBLIC 6104 0 RTI_Connext_EntityUntypedImpl_get_datawriter_qos
PUBLIC 6318 0 RTI_Connext_EntityUntypedImpl_get_datareader_qos
PUBLIC 64d0 0 RTI_Connext_EntityUntypedImpl_delete
PUBLIC 6720 0 RTI_Connext_EntityUntypedImpl_initialize
PUBLIC 6ed8 0 RTI_Connext_EntityUntypedImpl_get_sample_loaned_w_len
PUBLIC 6fb4 0 RTI_Connext_EntityUntypedImpl_validate
PUBLIC 7308 0 RTI_Connext_EntityUntypedImpl_get_sample_loaned
PUBLIC 73cc 0 RTI_Connext_EntityUntypedImpl_return_loan
PUBLIC 7474 0 RTI_Connext_EntityUntypedImpl_touch_samples
PUBLIC 7634 0 RTI_Connext_EntityUntypedImpl_wait_for_samples
PUBLIC 79ec 0 RTI_Connext_EntityUntypedImpl_wait_for_any_sample
PUBLIC 7a7c 0 RTI_Connext_EntityUntypedImpl_send_sample
PUBLIC 7b2c 0 RTI_Connext_EntityUntypedImpl_get_datawriter
PUBLIC 7b34 0 RTI_Connext_EntityUntypedImpl_get_datareader
PUBLIC 7b3c 0 RTI_Connext_EntityUntypedImpl_validate_receive_params
PUBLIC 7d6c 0 RTI_Connext_SimpleReplierParams_to_entityparams
PUBLIC 7dc8 0 e843419@000d_0000007b_46c
PUBLIC 8dc4 0 XMQLog_getBitmaps
PUBLIC 8de8 0 XMQLog_setBitmaps
PUBLIC 8e04 0 XMQLog_setVerbosity
PUBLIC 8e34 0 _fini
STACK CFI INIT 3dd0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e0c 58 .cfa: sp 0 + .ra: x30
STACK CFI 3e1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e20 .cfa: x29 16 +
STACK CFI 3e54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e64 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e70 28 .cfa: sp 0 + .ra: x30
STACK CFI 3e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e78 .cfa: x29 16 +
STACK CFI 3e94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e98 33c .cfa: sp 0 + .ra: x30
STACK CFI 3e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ea0 .cfa: x29 32 +
STACK CFI 3ea4 x19: .cfa -16 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41d4 20 .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41dc .cfa: x29 16 +
STACK CFI 41f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41f4 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 424c 10c .cfa: sp 0 + .ra: x30
STACK CFI 4250 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4254 .cfa: x29 32 +
STACK CFI 4258 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4358 170 .cfa: sp 0 + .ra: x30
STACK CFI 435c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4360 .cfa: x29 32 +
STACK CFI 4364 x19: .cfa -16 + ^
STACK CFI 44c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44c8 70 .cfa: sp 0 + .ra: x30
STACK CFI 44cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44d0 .cfa: x29 48 +
STACK CFI 44d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4538 6c .cfa: sp 0 + .ra: x30
STACK CFI 453c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4540 .cfa: x29 48 +
STACK CFI 4548 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 45a4 14 .cfa: sp 0 + .ra: x30
STACK CFI 45a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45ac .cfa: x29 16 +
STACK CFI 45b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45b8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 45bc .cfa: sp 80 +
STACK CFI 45c0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45c4 .cfa: x29 64 +
STACK CFI 45c8 x19: .cfa -48 + ^
STACK CFI 46ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46b0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 46b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46b8 .cfa: x29 32 +
STACK CFI 46bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4764 120 .cfa: sp 0 + .ra: x30
STACK CFI 4768 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 476c .cfa: x29 48 +
STACK CFI 4774 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4884 10c .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 488c .cfa: x29 32 +
STACK CFI 4890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 498c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4990 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4998 .cfa: x29 32 +
STACK CFI 499c x19: .cfa -16 + ^
STACK CFI 4a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a40 18 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a48 .cfa: x29 16 +
STACK CFI 4a54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a58 12c .cfa: sp 0 + .ra: x30
STACK CFI 4a5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a60 .cfa: x29 48 +
STACK CFI 4a68 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4b84 18 .cfa: sp 0 + .ra: x30
STACK CFI 4b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b8c .cfa: x29 16 +
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b9c 14 .cfa: sp 0 + .ra: x30
STACK CFI 4ba0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ba4 .cfa: x29 16 +
STACK CFI 4bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4bb0 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 4bb4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4bb8 .cfa: x29 176 +
STACK CFI 4bcc x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 4f54 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 4f58 .cfa: sp 1264 +
STACK CFI 4f5c .cfa: sp 1328 + .ra: .cfa -1320 + ^ x29: .cfa -1328 + ^
STACK CFI 4f60 .cfa: x29 1328 +
STACK CFI 4f6c x19: .cfa -1312 + ^ x20: .cfa -1304 + ^ x21: .cfa -1296 + ^ x22: .cfa -1288 + ^ x23: .cfa -1280 + ^
STACK CFI 511c .cfa: sp 1264 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5120 .cfa: sp 0 +
STACK CFI INIT 5124 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 517c 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 5180 .cfa: sp 240 +
STACK CFI 5184 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 5188 .cfa: x29 224 +
STACK CFI 5198 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 545c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 5460 dc .cfa: sp 0 + .ra: x30
STACK CFI 5464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5468 .cfa: x29 32 +
STACK CFI 546c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 553c 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 5540 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 5544 .cfa: x29 336 +
STACK CFI 5728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 572c 280 .cfa: sp 0 + .ra: x30
STACK CFI 5730 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5734 .cfa: x29 80 +
STACK CFI 5744 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 59a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 59ac 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 59b0 .cfa: sp 128 +
STACK CFI 59b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 59b8 .cfa: x29 96 +
STACK CFI 59cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 5b94 1c .cfa: sp 0 + .ra: x30
STACK CFI 5b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b9c .cfa: x29 16 +
STACK CFI 5bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bb8 .cfa: x29 16 +
STACK CFI 5bc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bcc 2c .cfa: sp 0 + .ra: x30
STACK CFI 5bd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bd4 .cfa: x29 32 +
STACK CFI 5bd8 x19: .cfa -16 + ^
STACK CFI 5bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5bf8 30 .cfa: sp 0 + .ra: x30
STACK CFI 5bfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c00 .cfa: x29 32 +
STACK CFI 5c04 x19: .cfa -16 + ^
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c28 144 .cfa: sp 0 + .ra: x30
STACK CFI 5c2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c30 .cfa: x29 64 +
STACK CFI 5c3c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5d6c 140 .cfa: sp 0 + .ra: x30
STACK CFI 5d70 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d74 .cfa: x29 48 +
STACK CFI 5d7c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5eac 140 .cfa: sp 0 + .ra: x30
STACK CFI 5eb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5eb4 .cfa: x29 48 +
STACK CFI 5ebc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5fec 118 .cfa: sp 0 + .ra: x30
STACK CFI 5ff0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ff4 .cfa: x29 48 +
STACK CFI 5ff8 x19: .cfa -32 + ^
STACK CFI 6100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6104 214 .cfa: sp 0 + .ra: x30
STACK CFI 6108 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 610c .cfa: x29 64 +
STACK CFI 6118 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6318 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 631c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6320 .cfa: x29 64 +
STACK CFI 632c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 64cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 64d0 250 .cfa: sp 0 + .ra: x30
STACK CFI 64d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64dc .cfa: x29 32 +
STACK CFI 64e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 670c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6714 .cfa: x29 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 671c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6720 7b8 .cfa: sp 0 + .ra: x30
STACK CFI 6724 .cfa: sp 2384 +
STACK CFI 6728 .cfa: sp 2480 + .ra: .cfa -2472 + ^ x29: .cfa -2480 + ^
STACK CFI 672c .cfa: x29 2480 +
STACK CFI 6740 x19: .cfa -2464 + ^ x20: .cfa -2456 + ^ x21: .cfa -2448 + ^ x22: .cfa -2440 + ^ x23: .cfa -2432 + ^ x24: .cfa -2424 + ^ x25: .cfa -2416 + ^ x26: .cfa -2408 + ^ x27: .cfa -2400 + ^ x28: .cfa -2392 + ^
STACK CFI 6ed0 .cfa: sp 2384 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6ed4 .cfa: sp 0 +
STACK CFI INIT 6ed8 dc .cfa: sp 0 + .ra: x30
STACK CFI 6edc .cfa: sp 80 +
STACK CFI 6ee0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ee4 .cfa: x29 32 +
STACK CFI 6ee8 x19: .cfa -16 + ^
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6fb4 354 .cfa: sp 0 + .ra: x30
STACK CFI 6fb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fbc .cfa: x29 32 +
STACK CFI 6fc0 x19: .cfa -16 + ^
STACK CFI 7304 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7308 c4 .cfa: sp 0 + .ra: x30
STACK CFI 730c .cfa: sp 64 +
STACK CFI 7310 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7314 .cfa: x29 32 +
STACK CFI 7318 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 73c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 73cc a8 .cfa: sp 0 + .ra: x30
STACK CFI 73d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 73d4 .cfa: x29 48 +
STACK CFI 73dc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7474 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 7478 .cfa: sp 160 +
STACK CFI 747c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7480 .cfa: x29 128 +
STACK CFI 7484 x19: .cfa -112 + ^
STACK CFI 7630 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7634 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 7638 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 763c .cfa: x29 240 +
STACK CFI 7650 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 79e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 79ec 90 .cfa: sp 0 + .ra: x30
STACK CFI 79f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79f4 .cfa: x29 32 +
STACK CFI 79f8 x19: .cfa -16 + ^
STACK CFI 7a78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7a7c b0 .cfa: sp 0 + .ra: x30
STACK CFI 7a80 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a84 .cfa: x29 48 +
STACK CFI 7a8c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7b2c 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b34 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b3c 230 .cfa: sp 0 + .ra: x30
STACK CFI 7b40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b44 .cfa: x29 16 +
STACK CFI 7d68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d6c 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8dc4 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8de8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e04 30 .cfa: sp 0 + .ra: x30
STACK CFI 8e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8e0c .cfa: x29 16 +
STACK CFI 8e30 .cfa: sp 0 + .ra: .ra x29: x29
