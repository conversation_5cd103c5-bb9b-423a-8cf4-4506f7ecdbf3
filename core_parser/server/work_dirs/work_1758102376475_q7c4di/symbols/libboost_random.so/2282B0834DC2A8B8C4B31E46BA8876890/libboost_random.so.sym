MODULE Linux arm64 2282B0834DC2A8B8C4B31E46BA8876890 libboost_random.so.1.77.0
INFO CODE_ID 83B08222C24DB8A8C4B31E46BA887689
PUBLIC 3220 0 _init
PUBLIC 34d0 0 void boost::throw_exception<boost::system::system_error>(boost::system::system_error const&)
PUBLIC 3610 0 boost::wrapexcept<boost::system::system_error>::rethrow() const
PUBLIC 3664 0 call_weak_fn
PUBLIC 3678 0 deregister_tm_clones
PUBLIC 36a8 0 register_tm_clones
PUBLIC 36e4 0 __do_global_dtors_aux
PUBLIC 3734 0 frame_dummy
PUBLIC 3740 0 boost::random::random_device::entropy() const
PUBLIC 3750 0 boost::random::random_device::random_device()
PUBLIC 38d0 0 boost::random::random_device::~random_device()
PUBLIC 3940 0 boost::random::random_device::operator()()
PUBLIC 39d0 0 boost::random::random_device::random_device(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3af0 0 boost::system::error_category::failed(int) const
PUBLIC 3b00 0 boost::system::detail::generic_error_category::name() const
PUBLIC 3b10 0 boost::system::detail::system_error_category::name() const
PUBLIC 3b20 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 3b40 0 boost::system::detail::interop_error_category::name() const
PUBLIC 3b50 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 3bf0 0 boost::system::detail::std_category::name() const
PUBLIC 3c10 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 3c40 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 3c50 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 3c60 0 boost::system::system_error::~system_error()
PUBLIC 3cb0 0 boost::system::detail::std_category::~std_category()
PUBLIC 3cd0 0 boost::system::detail::std_category::~std_category()
PUBLIC 3d10 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 3da0 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 3e40 0 boost::system::system_error::~system_error()
PUBLIC 3e90 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 3f10 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 3fa0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4020 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 40c0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4150 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 41e0 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 42e0 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 43e0 0 boost::wrapexcept<boost::system::system_error>::clone() const
PUBLIC 4700 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 48a0 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 4e90 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 5320 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 5390 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 53d0 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 54a0 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 55f0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, char const*)
PUBLIC 56a0 0 boost::random::random_device::impl::error(char const*)
PUBLIC 5880 0 boost::wrapexcept<boost::system::system_error>::wrapexcept(boost::wrapexcept<boost::system::system_error> const&)
PUBLIC 59f0 0 boost::system::system_error::what() const
PUBLIC 5b80 0 _fini
STACK CFI INIT 3678 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36a8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 36e4 50 .cfa: sp 0 + .ra: x30
STACK CFI 36f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36fc x19: .cfa -16 + ^
STACK CFI 372c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3734 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3af0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b20 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b50 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c10 30 .cfa: sp 0 + .ra: x30
STACK CFI 3c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c28 x19: .cfa -16 + ^
STACK CFI 3c3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c60 44 .cfa: sp 0 + .ra: x30
STACK CFI 3c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c78 x19: .cfa -16 + ^
STACK CFI 3ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3cb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd0 38 .cfa: sp 0 + .ra: x30
STACK CFI 3cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ce4 x19: .cfa -16 + ^
STACK CFI 3d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d10 88 .cfa: sp 0 + .ra: x30
STACK CFI 3d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d1c x19: .cfa -16 + ^
STACK CFI 3d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3d7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3d94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3da0 94 .cfa: sp 0 + .ra: x30
STACK CFI 3da4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3db8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3e40 50 .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e58 x19: .cfa -16 + ^
STACK CFI 3e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e90 80 .cfa: sp 0 + .ra: x30
STACK CFI 3e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ea8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4020 9c .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4038 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 404c x21: .cfa -16 + ^
STACK CFI 40b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 40c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 40c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 414c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4150 8c .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4168 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f10 84 .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3fa0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fb8 x19: .cfa -16 + ^
STACK CFI 4018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41e0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 41e4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 41f4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4200 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4254 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4274 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 42c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42c8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 42e0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 42e4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 42f4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4300 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4354 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4374 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 43c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43c8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 43e0 320 .cfa: sp 0 + .ra: x30
STACK CFI 43e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43fc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 457c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4580 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 4618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 461c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4700 194 .cfa: sp 0 + .ra: x30
STACK CFI 4704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 471c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4788 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 47f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 47fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4828 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48a0 5e8 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 48ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 48b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 48c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 48d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 49f4 x25: x25 x26: x26
STACK CFI 4a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4ad4 x25: x25 x26: x26
STACK CFI 4ae0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4b5c x25: x25 x26: x26
STACK CFI 4b60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4bc0 x25: x25 x26: x26
STACK CFI 4c84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4c98 x25: x25 x26: x26
STACK CFI 4c9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4cc4 x25: x25 x26: x26
STACK CFI 4cd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4da4 x25: x25 x26: x26
STACK CFI 4db8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4dcc x25: x25 x26: x26
STACK CFI 4df8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 4e90 48c .cfa: sp 0 + .ra: x30
STACK CFI 4e94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4e9c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4ea4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4eb0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4ec4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4ff4 x23: x23 x24: x24
STACK CFI 4ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 5000 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5044 x23: x23 x24: x24
STACK CFI 504c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 5050 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 50a8 x23: x23 x24: x24
STACK CFI 50c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 50c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5144 x23: x23 x24: x24
STACK CFI 51f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 51fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5200 x23: x23 x24: x24
STACK CFI 5204 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5218 x23: x23 x24: x24
STACK CFI 522c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 52c4 x23: x23 x24: x24
STACK CFI 52d8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 52ec x23: x23 x24: x24
STACK CFI 5308 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 5320 70 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 538c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5390 3c .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53ac x19: .cfa -16 + ^
STACK CFI 53c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53d0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 53d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 53dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5440 x21: x21 x22: x22
STACK CFI 544c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5450 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 545c x21: x21 x22: x22
STACK CFI 5460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5464 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5478 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 549c x21: x21 x22: x22
STACK CFI INIT 54a0 144 .cfa: sp 0 + .ra: x30
STACK CFI 54a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 54b4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 54c0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 553c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5540 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 555c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5560 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 55b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 55b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3740 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 55f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 560c x21: .cfa -16 + ^
STACK CFI 5680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5684 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 34d0 140 .cfa: sp 0 + .ra: x30
STACK CFI 34d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 34dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 56a0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 56a4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 56ac x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 56b8 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 56c4 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI INIT 3750 180 .cfa: sp 0 + .ra: x30
STACK CFI 3754 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3764 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 377c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 381c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3820 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 38d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 38d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38dc x19: .cfa -16 + ^
STACK CFI 3914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3918 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3920 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3924 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3940 88 .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 394c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3958 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 399c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 39d0 120 .cfa: sp 0 + .ra: x30
STACK CFI 39d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 39dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 39e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5880 16c .cfa: sp 0 + .ra: x30
STACK CFI 5884 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5894 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 58a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 58a8 x23: .cfa -32 + ^
STACK CFI 5974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5978 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3610 54 .cfa: sp 0 + .ra: x30
STACK CFI 3614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 361c x19: .cfa -16 + ^
STACK CFI INIT 59f0 190 .cfa: sp 0 + .ra: x30
STACK CFI 59f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a00 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a18 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 5a1c x21: .cfa -64 + ^
STACK CFI 5aa8 x21: x21
STACK CFI 5aac x21: .cfa -64 + ^
STACK CFI 5b1c x21: x21
STACK CFI 5b20 x21: .cfa -64 + ^
STACK CFI 5b74 x21: x21
STACK CFI 5b7c x21: .cfa -64 + ^
