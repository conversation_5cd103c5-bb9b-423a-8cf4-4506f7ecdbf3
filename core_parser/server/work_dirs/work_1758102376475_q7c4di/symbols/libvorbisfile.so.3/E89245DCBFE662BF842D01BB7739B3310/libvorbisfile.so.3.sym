MODULE Linux arm64 E89245DCBFE662BF842D01BB7739B3310 libvorbisfile.so.3
INFO CODE_ID DC4592E8E6BFBF62842D01BB7739B331E6B31D6B
PUBLIC 32a0 0 ov_clear
PUBLIC 35d8 0 ov_halfrate_p
PUBLIC 35f0 0 ov_test_callbacks
PUBLIC 3620 0 ov_test
PUBLIC 3650 0 ov_streams
PUBLIC 3658 0 ov_seekable
PUBLIC 3660 0 ov_bitrate_instant
PUBLIC 36c8 0 ov_serialnumber
PUBLIC 3720 0 ov_raw_total
PUBLIC 37e0 0 ov_pcm_total
PUBLIC 3890 0 ov_raw_seek
PUBLIC 3e50 0 ov_open_callbacks
PUBLIC 3ee0 0 ov_open
PUBLIC 3f10 0 ov_fopen
PUBLIC 3f80 0 ov_test_open
PUBLIC 3fe8 0 ov_pcm_seek_page
PUBLIC 46a0 0 ov_pcm_seek
PUBLIC 4a00 0 ov_halfrate
PUBLIC 4af0 0 ov_time_total
PUBLIC 4bd0 0 ov_bitrate
PUBLIC 4cf8 0 ov_time_seek
PUBLIC 4e20 0 ov_time_seek_page
PUBLIC 4f48 0 ov_raw_tell
PUBLIC 4f68 0 ov_pcm_tell
PUBLIC 4f88 0 ov_time_tell
PUBLIC 50b0 0 ov_info
PUBLIC 5570 0 ov_comment
PUBLIC 55c8 0 ov_read_filter
PUBLIC 5a10 0 ov_read
PUBLIC 5a38 0 ov_read_float
PUBLIC 5ba8 0 ov_crosslap
PUBLIC 5e00 0 ov_raw_seek_lap
PUBLIC 5e10 0 ov_pcm_seek_lap
PUBLIC 5e20 0 ov_pcm_seek_page_lap
PUBLIC 5e30 0 ov_time_seek_lap
PUBLIC 5e40 0 ov_time_seek_page_lap
STACK CFI INIT 1db8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e28 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e34 x19: .cfa -16 + ^
STACK CFI 1e6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e78 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f48 150 .cfa: sp 0 + .ra: x30
STACK CFI 1f4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f68 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2024 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2058 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2074 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2098 134 .cfa: sp 0 + .ra: x30
STACK CFI 209c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 20a4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 20b0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 20c0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 20d8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 20e4 x27: .cfa -112 + ^
STACK CFI 2194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2198 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x29: .cfa -192 + ^
STACK CFI INIT 21d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 21d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21e8 x21: .cfa -16 + ^
STACK CFI 2234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2238 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2248 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2258 9c .cfa: sp 0 + .ra: x30
STACK CFI 225c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2264 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 22b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 22e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 22f8 1ac .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2304 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 2324 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2330 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2348 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2354 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 245c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2460 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 24a8 354 .cfa: sp 0 + .ra: x30
STACK CFI 24ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 24bc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 24d0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 24dc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 24e4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 24f4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 26b0 x23: x23 x24: x24
STACK CFI 26b4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2718 x23: x23 x24: x24
STACK CFI 274c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2750 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 2770 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 27e4 x23: x23 x24: x24
STACK CFI 27f8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 2800 474 .cfa: sp 0 + .ra: x30
STACK CFI 2804 .cfa: sp 320 +
STACK CFI 280c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 2818 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2830 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 283c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2848 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 29f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 29f8 .cfa: sp 320 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x29: .cfa -304 + ^
STACK CFI 2a1c x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2a8c x27: x27 x28: x28
STACK CFI 2c04 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2c50 x27: x27 x28: x28
STACK CFI 2c54 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2c5c x27: x27 x28: x28
STACK CFI 2c70 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 2c78 38c .cfa: sp 0 + .ra: x30
STACK CFI 2c7c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c84 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2c8c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2c9c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2ca8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2e48 x27: .cfa -112 + ^
STACK CFI 2e88 x27: x27
STACK CFI 2eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2eb8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 2ee4 x27: .cfa -112 + ^
STACK CFI 2eec x27: x27
STACK CFI 2ef0 x27: .cfa -112 + ^
STACK CFI 2f10 x27: x27
STACK CFI 2f14 x27: .cfa -112 + ^
STACK CFI 2f94 x27: x27
STACK CFI 2fa4 x27: .cfa -112 + ^
STACK CFI 2ffc x27: x27
STACK CFI 3000 x27: .cfa -112 + ^
STACK CFI INIT 3008 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 300c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3014 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3038 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3044 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3050 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3054 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 30f4 x19: x19 x20: x20
STACK CFI 30f8 x21: x21 x22: x22
STACK CFI 30fc x25: x25 x26: x26
STACK CFI 3120 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3124 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3180 x19: x19 x20: x20
STACK CFI 3184 x21: x21 x22: x22
STACK CFI 3188 x25: x25 x26: x26
STACK CFI 318c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 31dc x19: x19 x20: x20
STACK CFI 31e0 x21: x21 x22: x22
STACK CFI 31e4 x25: x25 x26: x26
STACK CFI 31ec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 31f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 31f4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 31f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 31fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3204 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3258 48 .cfa: sp 0 + .ra: x30
STACK CFI 325c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3264 x19: .cfa -16 + ^
STACK CFI 329c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32a0 100 .cfa: sp 0 + .ra: x30
STACK CFI 32a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32ec x21: .cfa -16 + ^
STACK CFI 3320 x21: x21
STACK CFI 3394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 33a0 234 .cfa: sp 0 + .ra: x30
STACK CFI 33a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 33ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 33bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 33e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3568 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 35d8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 35f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3618 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3620 30 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 364c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3650 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3658 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3660 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36c8 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3720 bc .cfa: sp 0 + .ra: x30
STACK CFI 3724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 372c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3748 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3768 x21: x21 x22: x22
STACK CFI 3784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3788 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 37c0 x21: x21 x22: x22
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 37d0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 37d8 x21: x21 x22: x22
STACK CFI INIT 37e0 ac .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37f8 x21: .cfa -16 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3840 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3890 450 .cfa: sp 0 + .ra: x30
STACK CFI 3894 .cfa: sp 624 +
STACK CFI 3898 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 38a0 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 38ac x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 38e8 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 38f0 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 3978 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 3b6c x21: x21 x22: x22
STACK CFI 3b74 x23: x23 x24: x24
STACK CFI 3b78 x27: x27 x28: x28
STACK CFI 3ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 3ba8 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 3c84 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 3c8c x23: .cfa -576 + ^ x24: .cfa -568 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 3cb4 x23: x23 x24: x24
STACK CFI 3cb8 x27: x27 x28: x28
STACK CFI 3cc8 x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 3cd0 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 3cd4 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 3cd8 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 3cdc x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI INIT 3ce0 16c .cfa: sp 0 + .ra: x30
STACK CFI 3ce4 .cfa: sp 112 +
STACK CFI 3cec .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3cf8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3d08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3d20 x23: .cfa -48 + ^
STACK CFI 3e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3e28 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3e50 90 .cfa: sp 0 + .ra: x30
STACK CFI 3e58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e70 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3eb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ee0 30 .cfa: sp 0 + .ra: x30
STACK CFI 3eec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f10 6c .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f80 68 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3fe8 6b8 .cfa: sp 0 + .ra: x30
STACK CFI 3fec .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 3ffc x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 4010 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4030 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 4058 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 4060 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4398 x21: x21 x22: x22
STACK CFI 439c x23: x23 x24: x24
STACK CFI 43a0 x27: x27 x28: x28
STACK CFI 43cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 43d0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 4560 x21: x21 x22: x22
STACK CFI 4564 x23: x23 x24: x24
STACK CFI 4568 x27: x27 x28: x28
STACK CFI 456c x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 45c0 x21: x21 x22: x22
STACK CFI 45c4 x23: x23 x24: x24
STACK CFI 45c8 x27: x27 x28: x28
STACK CFI 45cc x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 45d8 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 45e0 x23: x23 x24: x24
STACK CFI 45e4 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 45f4 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 45fc x23: x23 x24: x24
STACK CFI 4608 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4658 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 465c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 4660 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 4664 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 46a0 35c .cfa: sp 0 + .ra: x30
STACK CFI 46a4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 46b4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 46c8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 46d0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 46f4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4700 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 48c0 x21: x21 x22: x22
STACK CFI 48c4 x27: x27 x28: x28
STACK CFI 48f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 48f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 4994 x21: x21 x22: x22
STACK CFI 4998 x27: x27 x28: x28
STACK CFI 499c x21: .cfa -160 + ^ x22: .cfa -152 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 49f0 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 49f4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 49f8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 4a00 ec .cfa: sp 0 + .ra: x30
STACK CFI 4a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a74 x19: x19 x20: x20
STACK CFI 4a80 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4a8c x19: x19 x20: x20
STACK CFI 4a94 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4ae0 x19: x19 x20: x20
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 4af0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4afc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b08 v8: .cfa -16 + ^
STACK CFI 4b68 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 4b6c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4b88 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 4b8c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4bcc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 4bd0 128 .cfa: sp 0 + .ra: x30
STACK CFI 4c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c48 v8: .cfa -16 + ^
STACK CFI 4c9c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 4cac .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4cec .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 4cf8 124 .cfa: sp 0 + .ra: x30
STACK CFI 4cfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4d24 v10: .cfa -24 + ^
STACK CFI 4d34 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4d4c x21: .cfa -32 + ^
STACK CFI 4da0 x21: x21
STACK CFI 4dc0 v10: v10
STACK CFI 4ddc v8: v8 v9: v9
STACK CFI 4de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4de4 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4de8 x21: x21
STACK CFI 4e00 v8: v8 v9: v9
STACK CFI 4e04 v10: v10
STACK CFI 4e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4e20 124 .cfa: sp 0 + .ra: x30
STACK CFI 4e24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e4c v10: .cfa -24 + ^
STACK CFI 4e5c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4e74 x21: .cfa -32 + ^
STACK CFI 4ec8 x21: x21
STACK CFI 4ee8 v10: v10
STACK CFI 4f04 v8: v8 v9: v9
STACK CFI 4f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f0c .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 4f10 x21: x21
STACK CFI 4f28 v8: v8 v9: v9
STACK CFI 4f2c v10: v10
STACK CFI 4f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4f48 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f68 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f88 128 .cfa: sp 0 + .ra: x30
STACK CFI 4f98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fb0 v8: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4fec .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4ff0 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5078 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5088 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 50b0 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5108 22c .cfa: sp 0 + .ra: x30
STACK CFI 510c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5110 .cfa: x29 128 +
STACK CFI 5114 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5124 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5148 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5198 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5338 238 .cfa: sp 0 + .ra: x30
STACK CFI 533c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5340 .cfa: x29 128 +
STACK CFI 5344 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5354 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 537c v8: .cfa -32 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 53cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 53d0 .cfa: x29 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5570 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55c8 444 .cfa: sp 0 + .ra: x30
STACK CFI 55cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 55d4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 55e0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5610 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 561c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5628 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5764 x19: x19 x20: x20
STACK CFI 576c x23: x23 x24: x24
STACK CFI 5770 x25: x25 x26: x26
STACK CFI 5794 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5798 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 57a0 x19: x19 x20: x20
STACK CFI 57a4 x23: x23 x24: x24
STACK CFI 57a8 x25: x25 x26: x26
STACK CFI 57ac x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 57b0 x19: x19 x20: x20
STACK CFI 57b4 x23: x23 x24: x24
STACK CFI 57b8 x25: x25 x26: x26
STACK CFI 57bc x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 58a0 x19: x19 x20: x20
STACK CFI 58a4 x23: x23 x24: x24
STACK CFI 58a8 x25: x25 x26: x26
STACK CFI 58ac x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 59e0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 59e8 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 59f0 x19: x19 x20: x20
STACK CFI 59f4 x23: x23 x24: x24
STACK CFI 59f8 x25: x25 x26: x26
STACK CFI 5a00 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5a04 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5a08 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT 5a10 24 .cfa: sp 0 + .ra: x30
STACK CFI 5a14 .cfa: sp 32 +
STACK CFI 5a1c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a38 16c .cfa: sp 0 + .ra: x30
STACK CFI 5a3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5a44 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5a50 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5a74 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5a80 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5a8c x27: .cfa -32 + ^
STACK CFI 5b20 x21: x21 x22: x22
STACK CFI 5b24 x27: x27
STACK CFI 5b2c x25: x25 x26: x26
STACK CFI 5b30 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 5b34 x21: x21 x22: x22
STACK CFI 5b3c x25: x25 x26: x26
STACK CFI 5b40 x27: x27
STACK CFI 5b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 5b68 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 5b70 x21: x21 x22: x22
STACK CFI 5b74 x25: x25 x26: x26
STACK CFI 5b78 x27: x27
STACK CFI 5b7c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 5b80 x21: x21 x22: x22
STACK CFI 5b84 x25: x25 x26: x26
STACK CFI 5b88 x27: x27
STACK CFI 5b98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5b9c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5ba0 x27: .cfa -32 + ^
STACK CFI INIT 5ba8 258 .cfa: sp 0 + .ra: x30
STACK CFI 5bac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5bb0 .cfa: x29 128 +
STACK CFI 5bb4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5bbc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5be4 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5c48 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5e00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e30 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e40 c .cfa: sp 0 + .ra: x30
