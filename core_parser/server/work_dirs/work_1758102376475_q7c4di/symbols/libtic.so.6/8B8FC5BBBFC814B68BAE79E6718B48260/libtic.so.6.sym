MODULE Linux arm64 8B8FC5BBBFC814B68BAE79E6718B48260 libtic.so.6
INFO CODE_ID BBC58F8BC8BFB6148BAE79E6718B48264D55135A
PUBLIC 4400 0 _nc_infotocap
PUBLIC 5328 0 _nc_tic_expand
PUBLIC 6810 0 _nc_entry_match
PUBLIC 6818 0 _nc_read_entry_source
PUBLIC 69c0 0 _nc_resolve_uses2
PUBLIC 7570 0 _nc_reset_input
PUBLIC 75c8 0 _nc_trans_string
PUBLIC 87b0 0 _nc_capcmp
PUBLIC a878 0 _nc_set_writedir
PUBLIC a988 0 _nc_write_object
PUBLIC b410 0 _nc_write_entry
PUBLIC b980 0 _nc_tic_written
STACK CFI INIT 2c58 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cc8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cd4 x19: .cfa -16 + ^
STACK CFI 2d0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d18 50 .cfa: sp 0 + .ra: x30
STACK CFI 2d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d68 4c .cfa: sp 0 + .ra: x30
STACK CFI 2d6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2db8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2dbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2dcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ddc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e10 x23: .cfa -16 + ^
STACK CFI 2e40 x23: x23
STACK CFI 2e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2e78 x23: x23
STACK CFI 2e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2eb0 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 2eb4 .cfa: sp 65536 +
STACK CFI 2ebc .cfa: sp 65792 +
STACK CFI 2ec8 .ra: .cfa -65784 + ^ x29: .cfa -65792 + ^
STACK CFI 2ed0 x21: .cfa -65760 + ^ x22: .cfa -65752 + ^
STACK CFI 2ed8 x23: .cfa -65744 + ^ x24: .cfa -65736 + ^
STACK CFI 2ee8 x19: .cfa -65776 + ^ x20: .cfa -65768 + ^
STACK CFI 2ef8 x25: .cfa -65728 + ^ x26: .cfa -65720 + ^ x27: .cfa -65712 + ^ x28: .cfa -65704 + ^
STACK CFI 30d8 .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 30dc .cfa: sp 65536 +
STACK CFI 30e0 .cfa: sp 0 +
STACK CFI 30e4 .cfa: sp 65792 + .ra: .cfa -65784 + ^ x19: .cfa -65776 + ^ x20: .cfa -65768 + ^ x21: .cfa -65760 + ^ x22: .cfa -65752 + ^ x23: .cfa -65744 + ^ x24: .cfa -65736 + ^ x25: .cfa -65728 + ^ x26: .cfa -65720 + ^ x27: .cfa -65712 + ^ x28: .cfa -65704 + ^ x29: .cfa -65792 + ^
STACK CFI INIT 32a0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 32a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 32b4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 32fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3300 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 3304 x21: .cfa -96 + ^
STACK CFI 343c x21: x21
STACK CFI 3444 x21: .cfa -96 + ^
STACK CFI INIT 3448 9c .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3454 x19: .cfa -16 + ^
STACK CFI 3488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 348c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 34b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 34bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 34e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 34e8 4c .cfa: sp 0 + .ra: x30
STACK CFI 34ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34f4 x19: .cfa -16 + ^
STACK CFI 3510 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3514 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3538 88 .cfa: sp 0 + .ra: x30
STACK CFI 353c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3544 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3558 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3560 x23: .cfa -16 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 35b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 35c0 344 .cfa: sp 0 + .ra: x30
STACK CFI 35c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 35cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 36c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 38dc x23: .cfa -16 + ^
STACK CFI 3900 x23: x23
STACK CFI INIT 3908 1ec .cfa: sp 0 + .ra: x30
STACK CFI 390c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3914 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3920 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3930 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 39f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 39f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3a80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3af8 110 .cfa: sp 0 + .ra: x30
STACK CFI 3afc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3b04 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3b14 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3bc0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3c08 38 .cfa: sp 0 + .ra: x30
STACK CFI 3c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c40 7c0 .cfa: sp 0 + .ra: x30
STACK CFI 3c44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3c4c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3c58 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3c64 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c6c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3c98 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3dd4 x27: x27 x28: x28
STACK CFI 3e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3e20 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3e88 x27: x27 x28: x28
STACK CFI 3ebc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4400 f28 .cfa: sp 0 + .ra: x30
STACK CFI 4404 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 440c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 4418 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 442c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4438 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 456c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 5328 6e0 .cfa: sp 0 + .ra: x30
STACK CFI 532c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 533c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 534c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5364 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 536c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 53a8 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 5518 x23: x23 x24: x24
STACK CFI 551c x25: x25 x26: x26
STACK CFI 5548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 554c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 556c x23: x23 x24: x24
STACK CFI 5574 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 571c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5730 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 5734 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 596c x25: x25 x26: x26
STACK CFI 5974 x23: x23 x24: x24
STACK CFI 5978 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 59fc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5a00 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 5a04 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI INIT 5a08 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a60 204 .cfa: sp 0 + .ra: x30
STACK CFI 5a64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5a6c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5a78 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5aac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 5af0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5b04 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5b08 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c30 x23: x23 x24: x24
STACK CFI 5c34 x25: x25 x26: x26
STACK CFI 5c38 x27: x27 x28: x28
STACK CFI 5c44 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 5c68 58 .cfa: sp 0 + .ra: x30
STACK CFI 5c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c74 x19: .cfa -16 + ^
STACK CFI 5c88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5cc0 958 .cfa: sp 0 + .ra: x30
STACK CFI 5cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 65f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6600 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6618 78 .cfa: sp 0 + .ra: x30
STACK CFI 661c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6624 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 664c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 668c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6690 180 .cfa: sp 0 + .ra: x30
STACK CFI 6694 .cfa: sp 1136 +
STACK CFI 6698 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 66a0 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 66b0 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 66cc x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 678c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6790 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x29: .cfa -1136 + ^
STACK CFI INIT 6810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6818 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 681c .cfa: sp 1120 +
STACK CFI 6820 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 6828 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 6838 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 684c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 6858 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 699c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 69a0 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x29: .cfa -1120 + ^
STACK CFI INIT 69c0 79c .cfa: sp 0 + .ra: x30
STACK CFI 69c8 .cfa: sp 6224 +
STACK CFI 69d0 .ra: .cfa -6216 + ^ x29: .cfa -6224 + ^
STACK CFI 69f4 x27: .cfa -6144 + ^ x28: .cfa -6136 + ^
STACK CFI 6a0c x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 6a1c x19: .cfa -6208 + ^ x20: .cfa -6200 + ^
STACK CFI 6a20 x21: .cfa -6192 + ^ x22: .cfa -6184 + ^
STACK CFI 6a24 x23: .cfa -6176 + ^ x24: .cfa -6168 + ^
STACK CFI 6e48 x19: x19 x20: x20
STACK CFI 6e4c x21: x21 x22: x22
STACK CFI 6e50 x23: x23 x24: x24
STACK CFI 6e54 x25: x25 x26: x26
STACK CFI 6e58 x19: .cfa -6208 + ^ x20: .cfa -6200 + ^ x21: .cfa -6192 + ^ x22: .cfa -6184 + ^ x23: .cfa -6176 + ^ x24: .cfa -6168 + ^ x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 6ea4 x19: x19 x20: x20
STACK CFI 6eac x21: x21 x22: x22
STACK CFI 6eb0 x23: x23 x24: x24
STACK CFI 6eb4 x25: x25 x26: x26
STACK CFI 6ee4 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 6ee8 .cfa: sp 6224 + .ra: .cfa -6216 + ^ x19: .cfa -6208 + ^ x20: .cfa -6200 + ^ x21: .cfa -6192 + ^ x22: .cfa -6184 + ^ x23: .cfa -6176 + ^ x24: .cfa -6168 + ^ x25: .cfa -6160 + ^ x26: .cfa -6152 + ^ x27: .cfa -6144 + ^ x28: .cfa -6136 + ^ x29: .cfa -6224 + ^
STACK CFI 6eec x19: x19 x20: x20
STACK CFI 6ef0 x21: x21 x22: x22
STACK CFI 6ef4 x23: x23 x24: x24
STACK CFI 6ef8 x25: x25 x26: x26
STACK CFI 6f04 x19: .cfa -6208 + ^ x20: .cfa -6200 + ^ x21: .cfa -6192 + ^ x22: .cfa -6184 + ^ x23: .cfa -6176 + ^ x24: .cfa -6168 + ^ x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 6fa4 x19: x19 x20: x20
STACK CFI 6fa8 x21: x21 x22: x22
STACK CFI 6fac x23: x23 x24: x24
STACK CFI 6fb0 x25: x25 x26: x26
STACK CFI 6fb4 x19: .cfa -6208 + ^ x20: .cfa -6200 + ^ x21: .cfa -6192 + ^ x22: .cfa -6184 + ^ x23: .cfa -6176 + ^ x24: .cfa -6168 + ^ x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 70d4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 70d8 x19: .cfa -6208 + ^ x20: .cfa -6200 + ^
STACK CFI 70dc x21: .cfa -6192 + ^ x22: .cfa -6184 + ^
STACK CFI 70e0 x23: .cfa -6176 + ^ x24: .cfa -6168 + ^
STACK CFI 70e4 x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 70fc x19: x19 x20: x20
STACK CFI 7100 x21: x21 x22: x22
STACK CFI 7104 x23: x23 x24: x24
STACK CFI 7108 x25: x25 x26: x26
STACK CFI 713c x19: .cfa -6208 + ^ x20: .cfa -6200 + ^ x21: .cfa -6192 + ^ x22: .cfa -6184 + ^ x23: .cfa -6176 + ^ x24: .cfa -6168 + ^ x25: .cfa -6160 + ^ x26: .cfa -6152 + ^
STACK CFI 714c x19: x19 x20: x20
STACK CFI 7150 x21: x21 x22: x22
STACK CFI 7154 x23: x23 x24: x24
STACK CFI 7158 x25: x25 x26: x26
STACK CFI INIT 7160 84 .cfa: sp 0 + .ra: x30
STACK CFI 7164 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7170 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7178 x21: .cfa -16 + ^
STACK CFI 71bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 71c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 71e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 71e8 334 .cfa: sp 0 + .ra: x30
STACK CFI 71ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 71f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7204 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 7274 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7278 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7284 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7398 x21: x21 x22: x22
STACK CFI 73a0 x23: x23 x24: x24
STACK CFI 73a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7444 x21: x21 x22: x22
STACK CFI 7448 x23: x23 x24: x24
STACK CFI 7458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 745c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7488 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 74bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 74c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7508 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7520 50 .cfa: sp 0 + .ra: x30
STACK CFI 7560 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7570 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75c8 4fc .cfa: sp 0 + .ra: x30
STACK CFI 75cc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 75d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 75e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 75f0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 75fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7608 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 76e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 76ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7794 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7998 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7ac8 48 .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ad8 x19: .cfa -16 + ^
STACK CFI 7af4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7b10 2c .cfa: sp 0 + .ra: x30
STACK CFI 7b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b1c x19: .cfa -16 + ^
STACK CFI 7b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7b40 990 .cfa: sp 0 + .ra: x30
STACK CFI 7b44 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 7b4c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7b54 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7b64 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7b7c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7bec .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 84d0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 84dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 84e8 x23: .cfa -32 + ^
STACK CFI 8500 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8520 x21: x21 x22: x22
STACK CFI 8544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 8548 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 8568 x21: x21 x22: x22
STACK CFI 8570 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 8578 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 857c .cfa: sp 1120 +
STACK CFI 8580 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 8588 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 8590 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 85e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85e4 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x29: .cfa -1120 + ^
STACK CFI INIT 8750 5c .cfa: sp 0 + .ra: x30
STACK CFI 8754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 875c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 876c x21: .cfa -16 + ^
STACK CFI 8788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 878c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 87a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 87b0 194 .cfa: sp 0 + .ra: x30
STACK CFI 87b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 87bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 87ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 87f0 x23: .cfa -16 + ^
STACK CFI 8820 x19: x19 x20: x20
STACK CFI 8828 x23: x23
STACK CFI 882c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8830 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 88cc x19: x19 x20: x20 x23: x23
STACK CFI 88e0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 88e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8934 x19: x19 x20: x20
STACK CFI 893c x23: x23
STACK CFI 8940 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 8948 c1c .cfa: sp 0 + .ra: x30
STACK CFI 8950 .cfa: sp 4528 +
STACK CFI 8954 .ra: .cfa -4520 + ^ x29: .cfa -4528 + ^
STACK CFI 895c x25: .cfa -4464 + ^ x26: .cfa -4456 + ^
STACK CFI 8964 x23: .cfa -4480 + ^ x24: .cfa -4472 + ^
STACK CFI 8988 x19: .cfa -4512 + ^ x20: .cfa -4504 + ^ x21: .cfa -4496 + ^ x22: .cfa -4488 + ^
STACK CFI 8a34 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI 8b58 x27: x27 x28: x28
STACK CFI 8d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8d3c .cfa: sp 4528 + .ra: .cfa -4520 + ^ x19: .cfa -4512 + ^ x20: .cfa -4504 + ^ x21: .cfa -4496 + ^ x22: .cfa -4488 + ^ x23: .cfa -4480 + ^ x24: .cfa -4472 + ^ x25: .cfa -4464 + ^ x26: .cfa -4456 + ^ x27: .cfa -4448 + ^ x28: .cfa -4440 + ^ x29: .cfa -4528 + ^
STACK CFI 8e20 x27: x27 x28: x28
STACK CFI 8fa0 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI 8fb8 x27: x27 x28: x28
STACK CFI 91b4 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI 91c8 x27: x27 x28: x28
STACK CFI 9414 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI 9420 x27: x27 x28: x28
STACK CFI 9550 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI 955c x27: x27 x28: x28
STACK CFI 9560 x27: .cfa -4448 + ^ x28: .cfa -4440 + ^
STACK CFI INIT 9568 f0c .cfa: sp 0 + .ra: x30
STACK CFI 956c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 9574 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 957c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 9598 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 960c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 9610 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 987c x23: x23 x24: x24
STACK CFI 9884 x25: x25 x26: x26
STACK CFI 9888 x27: x27 x28: x28
STACK CFI 9894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9898 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 9edc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9f3c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a3d0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a3d4 x23: x23 x24: x24
STACK CFI a3dc x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a41c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a428 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a42c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT a478 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT a508 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a538 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT a580 b0 .cfa: sp 0 + .ra: x30
STACK CFI a584 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a58c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a59c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a5a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a5b4 x25: .cfa -16 + ^
STACK CFI a604 x21: x21 x22: x22
STACK CFI a608 x23: x23 x24: x24
STACK CFI a60c x25: x25
STACK CFI a610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a614 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a61c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a62c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a630 160 .cfa: sp 0 + .ra: x30
STACK CFI a638 .cfa: sp 4288 +
STACK CFI a63c .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI a644 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI a658 x21: .cfa -4256 + ^ x22: .cfa -4248 + ^
STACK CFI a710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a714 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x29: .cfa -4288 + ^
STACK CFI INIT a790 e8 .cfa: sp 0 + .ra: x30
STACK CFI a794 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a79c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a7a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a80c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a810 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a878 10c .cfa: sp 0 + .ra: x30
STACK CFI a880 .cfa: sp 4160 +
STACK CFI a884 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI a88c x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI a8a4 x21: .cfa -4128 + ^
STACK CFI a91c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a920 .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x29: .cfa -4160 + ^
STACK CFI INIT a988 900 .cfa: sp 0 + .ra: x30
STACK CFI a98c .cfa: sp 65536 +
STACK CFI a994 .cfa: sp 65696 +
STACK CFI a9a4 .ra: .cfa -65688 + ^ x29: .cfa -65696 + ^
STACK CFI a9b0 x21: .cfa -65664 + ^ x22: .cfa -65656 + ^
STACK CFI a9bc x23: .cfa -65648 + ^ x24: .cfa -65640 + ^
STACK CFI a9cc x27: .cfa -65616 + ^ x28: .cfa -65608 + ^
STACK CFI a9e0 x19: .cfa -65680 + ^ x20: .cfa -65672 + ^
STACK CFI a9f8 x25: .cfa -65632 + ^ x26: .cfa -65624 + ^
STACK CFI ab90 .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ab94 .cfa: sp 65536 +
STACK CFI ab98 .cfa: sp 0 +
STACK CFI ab9c .cfa: sp 65696 + .ra: .cfa -65688 + ^ x19: .cfa -65680 + ^ x20: .cfa -65672 + ^ x21: .cfa -65664 + ^ x22: .cfa -65656 + ^ x23: .cfa -65648 + ^ x24: .cfa -65640 + ^ x25: .cfa -65632 + ^ x26: .cfa -65624 + ^ x27: .cfa -65616 + ^ x28: .cfa -65608 + ^ x29: .cfa -65696 + ^
STACK CFI INIT b288 184 .cfa: sp 0 + .ra: x30
STACK CFI b290 .cfa: sp 32832 +
STACK CFI b298 .ra: .cfa -32824 + ^ x29: .cfa -32832 + ^
STACK CFI b2a0 x21: .cfa -32800 + ^ x22: .cfa -32792 + ^
STACK CFI b2b0 x19: .cfa -32816 + ^ x20: .cfa -32808 + ^
STACK CFI b39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b3a0 .cfa: sp 32832 + .ra: .cfa -32824 + ^ x19: .cfa -32816 + ^ x20: .cfa -32808 + ^ x21: .cfa -32800 + ^ x22: .cfa -32792 + ^ x29: .cfa -32832 + ^
STACK CFI INIT b410 56c .cfa: sp 0 + .ra: x30
STACK CFI b418 .cfa: sp 16640 +
STACK CFI b41c .ra: .cfa -16632 + ^ x29: .cfa -16640 + ^
STACK CFI b424 x25: .cfa -16576 + ^ x26: .cfa -16568 + ^
STACK CFI b448 x19: .cfa -16624 + ^ x20: .cfa -16616 + ^ x21: .cfa -16608 + ^ x22: .cfa -16600 + ^ x23: .cfa -16592 + ^ x24: .cfa -16584 + ^
STACK CFI b450 x27: .cfa -16560 + ^ x28: .cfa -16552 + ^
STACK CFI b698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b69c .cfa: sp 16640 + .ra: .cfa -16632 + ^ x19: .cfa -16624 + ^ x20: .cfa -16616 + ^ x21: .cfa -16608 + ^ x22: .cfa -16600 + ^ x23: .cfa -16592 + ^ x24: .cfa -16584 + ^ x25: .cfa -16576 + ^ x26: .cfa -16568 + ^ x27: .cfa -16560 + ^ x28: .cfa -16552 + ^ x29: .cfa -16640 + ^
STACK CFI INIT b980 c .cfa: sp 0 + .ra: x30
