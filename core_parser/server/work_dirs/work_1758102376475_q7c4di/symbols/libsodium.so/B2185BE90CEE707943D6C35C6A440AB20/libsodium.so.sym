MODULE Linux arm64 B2185BE90CEE707943D6C35C6A440AB20 libsodium.so.23
INFO CODE_ID E95B18B2EE0C797043D6C35C6A440AB2
PUBLIC ce38 0 _init
PUBLIC d990 0 call_weak_fn
PUBLIC d9a4 0 deregister_tm_clones
PUBLIC d9d4 0 register_tm_clones
PUBLIC da10 0 __do_global_dtors_aux
PUBLIC da60 0 frame_dummy
PUBLIC da70 0 crypto_aead_chacha20poly1305_encrypt_detached
PUBLIC dbc0 0 crypto_aead_chacha20poly1305_encrypt
PUBLIC dc30 0 crypto_aead_chacha20poly1305_ietf_encrypt_detached
PUBLIC ddb0 0 crypto_aead_chacha20poly1305_ietf_encrypt
PUBLIC de30 0 crypto_aead_chacha20poly1305_decrypt_detached
PUBLIC dfc0 0 crypto_aead_chacha20poly1305_decrypt
PUBLIC e030 0 crypto_aead_chacha20poly1305_ietf_decrypt_detached
PUBLIC e1f0 0 crypto_aead_chacha20poly1305_ietf_decrypt
PUBLIC e260 0 crypto_aead_chacha20poly1305_ietf_keybytes
PUBLIC e270 0 crypto_aead_chacha20poly1305_ietf_npubbytes
PUBLIC e280 0 crypto_aead_chacha20poly1305_ietf_nsecbytes
PUBLIC e290 0 crypto_aead_chacha20poly1305_ietf_abytes
PUBLIC e2a0 0 crypto_aead_chacha20poly1305_ietf_messagebytes_max
PUBLIC e2b0 0 crypto_aead_chacha20poly1305_ietf_keygen
PUBLIC e2c0 0 crypto_aead_chacha20poly1305_keybytes
PUBLIC e2d0 0 crypto_aead_chacha20poly1305_npubbytes
PUBLIC e2e0 0 crypto_aead_chacha20poly1305_nsecbytes
PUBLIC e2f0 0 crypto_aead_chacha20poly1305_abytes
PUBLIC e300 0 crypto_aead_chacha20poly1305_messagebytes_max
PUBLIC e310 0 crypto_aead_chacha20poly1305_keygen
PUBLIC e320 0 crypto_aead_xchacha20poly1305_ietf_encrypt_detached
PUBLIC e4f0 0 crypto_aead_xchacha20poly1305_ietf_encrypt
PUBLIC e560 0 crypto_aead_xchacha20poly1305_ietf_decrypt_detached
PUBLIC e760 0 crypto_aead_xchacha20poly1305_ietf_decrypt
PUBLIC e7d0 0 crypto_aead_xchacha20poly1305_ietf_keybytes
PUBLIC e7e0 0 crypto_aead_xchacha20poly1305_ietf_npubbytes
PUBLIC e7f0 0 crypto_aead_xchacha20poly1305_ietf_nsecbytes
PUBLIC e800 0 crypto_aead_xchacha20poly1305_ietf_abytes
PUBLIC e810 0 crypto_aead_xchacha20poly1305_ietf_messagebytes_max
PUBLIC e820 0 crypto_aead_xchacha20poly1305_ietf_keygen
PUBLIC e830 0 crypto_auth_bytes
PUBLIC e840 0 crypto_auth_keybytes
PUBLIC e850 0 crypto_auth_primitive
PUBLIC e860 0 crypto_auth
PUBLIC e870 0 crypto_auth_verify
PUBLIC e880 0 crypto_auth_keygen
PUBLIC e890 0 crypto_auth_hmacsha256_bytes
PUBLIC e8a0 0 crypto_auth_hmacsha256_keybytes
PUBLIC e8b0 0 crypto_auth_hmacsha256_statebytes
PUBLIC e8c0 0 crypto_auth_hmacsha256_keygen
PUBLIC e8d0 0 crypto_auth_hmacsha256_init
PUBLIC eea0 0 crypto_auth_hmacsha256_update
PUBLIC eec0 0 crypto_auth_hmacsha256_final
PUBLIC ef50 0 crypto_auth_hmacsha256
PUBLIC eff0 0 crypto_auth_hmacsha256_verify
PUBLIC f070 0 crypto_auth_hmacsha512_bytes
PUBLIC f080 0 crypto_auth_hmacsha512_keybytes
PUBLIC f090 0 crypto_auth_hmacsha512_statebytes
PUBLIC f0a0 0 crypto_auth_hmacsha512_keygen
PUBLIC f0b0 0 crypto_auth_hmacsha512_init
PUBLIC f740 0 crypto_auth_hmacsha512_update
PUBLIC f760 0 crypto_auth_hmacsha512_final
PUBLIC f7f0 0 crypto_auth_hmacsha512
PUBLIC f890 0 crypto_auth_hmacsha512_verify
PUBLIC f910 0 crypto_auth_hmacsha512256_bytes
PUBLIC f920 0 crypto_auth_hmacsha512256_keybytes
PUBLIC f930 0 crypto_auth_hmacsha512256_statebytes
PUBLIC f940 0 crypto_auth_hmacsha512256_keygen
PUBLIC f950 0 crypto_auth_hmacsha512256_init
PUBLIC f960 0 crypto_auth_hmacsha512256_update
PUBLIC f970 0 crypto_auth_hmacsha512256_final
PUBLIC f9d0 0 crypto_auth_hmacsha512256
PUBLIC fa60 0 crypto_auth_hmacsha512256_verify
PUBLIC fae0 0 crypto_box_seedbytes
PUBLIC faf0 0 crypto_box_publickeybytes
PUBLIC fb00 0 crypto_box_secretkeybytes
PUBLIC fb10 0 crypto_box_beforenmbytes
PUBLIC fb20 0 crypto_box_noncebytes
PUBLIC fb30 0 crypto_box_zerobytes
PUBLIC fb40 0 crypto_box_boxzerobytes
PUBLIC fb50 0 crypto_box_macbytes
PUBLIC fb60 0 crypto_box_messagebytes_max
PUBLIC fb70 0 crypto_box_primitive
PUBLIC fb80 0 crypto_box_seed_keypair
PUBLIC fb90 0 crypto_box_keypair
PUBLIC fba0 0 crypto_box_beforenm
PUBLIC fbb0 0 crypto_box_afternm
PUBLIC fbc0 0 crypto_box_open_afternm
PUBLIC fbd0 0 crypto_box
PUBLIC fbe0 0 crypto_box_open
PUBLIC fbf0 0 crypto_box_detached_afternm
PUBLIC fc00 0 crypto_box_detached
PUBLIC fcc0 0 crypto_box_easy_afternm
PUBLIC fd00 0 crypto_box_easy
PUBLIC fd40 0 crypto_box_open_detached_afternm
PUBLIC fd50 0 crypto_box_open_detached
PUBLIC fe10 0 crypto_box_open_easy_afternm
PUBLIC fe40 0 crypto_box_open_easy
PUBLIC fe70 0 _crypto_box_seal_nonce
PUBLIC ff30 0 crypto_box_seal
PUBLIC 10080 0 crypto_box_seal_open
PUBLIC 10130 0 crypto_box_sealbytes
PUBLIC 10140 0 crypto_box_curve25519xsalsa20poly1305_seed_keypair
PUBLIC 101d0 0 crypto_box_curve25519xsalsa20poly1305_keypair
PUBLIC 10210 0 crypto_box_curve25519xsalsa20poly1305_beforenm
PUBLIC 102a0 0 crypto_box_curve25519xsalsa20poly1305_afternm
PUBLIC 102b0 0 crypto_box_curve25519xsalsa20poly1305_open_afternm
PUBLIC 102c0 0 crypto_box_curve25519xsalsa20poly1305
PUBLIC 10370 0 crypto_box_curve25519xsalsa20poly1305_open
PUBLIC 10420 0 crypto_box_curve25519xsalsa20poly1305_seedbytes
PUBLIC 10430 0 crypto_box_curve25519xsalsa20poly1305_publickeybytes
PUBLIC 10440 0 crypto_box_curve25519xsalsa20poly1305_secretkeybytes
PUBLIC 10450 0 crypto_box_curve25519xsalsa20poly1305_beforenmbytes
PUBLIC 10460 0 crypto_box_curve25519xsalsa20poly1305_noncebytes
PUBLIC 10470 0 crypto_box_curve25519xsalsa20poly1305_zerobytes
PUBLIC 10480 0 crypto_box_curve25519xsalsa20poly1305_boxzerobytes
PUBLIC 10490 0 crypto_box_curve25519xsalsa20poly1305_macbytes
PUBLIC 104a0 0 crypto_box_curve25519xsalsa20poly1305_messagebytes_max
PUBLIC 104b0 0 fe25519_sub
PUBLIC 10540 0 fe25519_mul
PUBLIC 10780 0 fe25519_sq
PUBLIC 10910 0 fe25519_sq2
PUBLIC 10ab0 0 fe25519_reduce
PUBLIC 10c60 0 slide_vartime
PUBLIC 10d60 0 ge25519_madd
PUBLIC 10ea0 0 ge25519_p2_dbl
PUBLIC 10f90 0 fe25519_pow22523
PUBLIC 11310 0 ge25519_cmov8
PUBLIC 11f30 0 ge25519_cmov8_cached
PUBLIC 12f70 0 fe25519_frombytes
PUBLIC 12fb0 0 fe25519_tobytes
PUBLIC 13000 0 ristretto255_sqrt_ratio_m1
PUBLIC 13290 0 ristretto255_elligator
PUBLIC 136f0 0 fe25519_invert
PUBLIC 13a90 0 ge25519_add
PUBLIC 13bd0 0 ge25519_frombytes
PUBLIC 13ee0 0 ge25519_elligator2
PUBLIC 14660 0 ge25519_frombytes_negate_vartime
PUBLIC 148e0 0 ge25519_p1p1_to_p2
PUBLIC 14b50 0 ge25519_p1p1_to_p3
PUBLIC 14dd0 0 ge25519_p3_to_cached
PUBLIC 14ec0 0 ge25519_p3_tobytes
PUBLIC 14f80 0 ge25519_sub
PUBLIC 150d0 0 ge25519_tobytes
PUBLIC 15190 0 ge25519_double_scalarmult_vartime
PUBLIC 159f0 0 ge25519_scalarmult
PUBLIC 161c0 0 ge25519_scalarmult_base
PUBLIC 165a0 0 ge25519_is_on_curve
PUBLIC 166d0 0 ge25519_is_on_main_subgroup
PUBLIC 16ff0 0 ge25519_is_canonical
PUBLIC 170e0 0 ge25519_has_small_order
PUBLIC 17520 0 sc25519_mul
PUBLIC 18530 0 sc25519_muladd
PUBLIC 19780 0 sc25519_invert
PUBLIC 19ce0 0 sc25519_reduce
PUBLIC 1a470 0 sc25519_is_canonical
PUBLIC 1a4d0 0 ge25519_from_uniform
PUBLIC 1a550 0 ge25519_from_hash
PUBLIC 1a680 0 ristretto255_frombytes
PUBLIC 1aa70 0 ristretto255_p3_tobytes
PUBLIC 1ae80 0 ristretto255_from_hash
PUBLIC 1b030 0 crypto_core_hchacha20
PUBLIC 1b240 0 crypto_core_hchacha20_outputbytes
PUBLIC 1b250 0 crypto_core_hchacha20_inputbytes
PUBLIC 1b260 0 crypto_core_hchacha20_keybytes
PUBLIC 1b270 0 crypto_core_hchacha20_constbytes
PUBLIC 1b280 0 crypto_core_hsalsa20
PUBLIC 1b420 0 crypto_core_hsalsa20_outputbytes
PUBLIC 1b430 0 crypto_core_hsalsa20_inputbytes
PUBLIC 1b440 0 crypto_core_hsalsa20_keybytes
PUBLIC 1b450 0 crypto_core_hsalsa20_constbytes
PUBLIC 1b460 0 crypto_core_salsa
PUBLIC 1b6d0 0 crypto_core_salsa20
PUBLIC 1b6f0 0 crypto_core_salsa20_outputbytes
PUBLIC 1b700 0 crypto_core_salsa20_inputbytes
PUBLIC 1b710 0 crypto_core_salsa20_keybytes
PUBLIC 1b720 0 crypto_core_salsa20_constbytes
PUBLIC 1b730 0 crypto_core_salsa2012
PUBLIC 1b750 0 crypto_core_salsa2012_outputbytes
PUBLIC 1b760 0 crypto_core_salsa2012_inputbytes
PUBLIC 1b770 0 crypto_core_salsa2012_keybytes
PUBLIC 1b780 0 crypto_core_salsa2012_constbytes
PUBLIC 1b790 0 crypto_core_salsa208
PUBLIC 1b7b0 0 crypto_core_salsa208_outputbytes
PUBLIC 1b7c0 0 crypto_core_salsa208_inputbytes
PUBLIC 1b7d0 0 crypto_core_salsa208_keybytes
PUBLIC 1b7e0 0 crypto_core_salsa208_constbytes
PUBLIC 1b7f0 0 crypto_generichash_bytes_min
PUBLIC 1b800 0 crypto_generichash_bytes_max
PUBLIC 1b810 0 crypto_generichash_bytes
PUBLIC 1b820 0 crypto_generichash_keybytes_min
PUBLIC 1b830 0 crypto_generichash_keybytes_max
PUBLIC 1b840 0 crypto_generichash_keybytes
PUBLIC 1b850 0 crypto_generichash_primitive
PUBLIC 1b860 0 crypto_generichash_statebytes
PUBLIC 1b870 0 crypto_generichash
PUBLIC 1b880 0 crypto_generichash_init
PUBLIC 1b890 0 crypto_generichash_update
PUBLIC 1b8a0 0 crypto_generichash_final
PUBLIC 1b8b0 0 crypto_generichash_keygen
PUBLIC 1b8c0 0 crypto_generichash_blake2b_bytes_min
PUBLIC 1b8d0 0 crypto_generichash_blake2b_bytes_max
PUBLIC 1b8e0 0 crypto_generichash_blake2b_bytes
PUBLIC 1b8f0 0 crypto_generichash_blake2b_keybytes_min
PUBLIC 1b900 0 crypto_generichash_blake2b_keybytes_max
PUBLIC 1b910 0 crypto_generichash_blake2b_keybytes
PUBLIC 1b920 0 crypto_generichash_blake2b_saltbytes
PUBLIC 1b930 0 crypto_generichash_blake2b_personalbytes
PUBLIC 1b940 0 crypto_generichash_blake2b_statebytes
PUBLIC 1b950 0 crypto_generichash_blake2b_keygen
PUBLIC 1b960 0 blake2b_compress_ref
PUBLIC 1d110 0 crypto_generichash_blake2b__init_param
PUBLIC 1d250 0 crypto_generichash_blake2b__init
PUBLIC 1d300 0 crypto_generichash_blake2b__init_salt_personal
PUBLIC 1d3f0 0 crypto_generichash_blake2b__update
PUBLIC 1d510 0 crypto_generichash_blake2b__init_key
PUBLIC 1d670 0 crypto_generichash_blake2b__init_key_salt_personal
PUBLIC 1d800 0 crypto_generichash_blake2b__final
PUBLIC 1d970 0 crypto_generichash_blake2b__blake2b
PUBLIC 1db40 0 crypto_generichash_blake2b__blake2b_salt_personal
PUBLIC 1dd30 0 crypto_generichash_blake2b__pick_best_implementation
PUBLIC 1dd50 0 crypto_generichash_blake2b
PUBLIC 1dd90 0 crypto_generichash_blake2b_salt_personal
PUBLIC 1ddd0 0 crypto_generichash_blake2b_init
PUBLIC 1de40 0 crypto_generichash_blake2b_init_salt_personal
PUBLIC 1dec0 0 crypto_generichash_blake2b_update
PUBLIC 1ded0 0 crypto_generichash_blake2b_final
PUBLIC 1dee0 0 _crypto_generichash_blake2b_pick_best_implementation
PUBLIC 1def0 0 crypto_hash_bytes
PUBLIC 1df00 0 crypto_hash
PUBLIC 1df10 0 crypto_hash_primitive
PUBLIC 1df20 0 crypto_hash_sha256_bytes
PUBLIC 1df30 0 crypto_hash_sha256_statebytes
PUBLIC 1df40 0 SHA256_Transform
PUBLIC 1ea00 0 crypto_hash_sha256_update.part.0
PUBLIC 1f070 0 crypto_hash_sha256_init
PUBLIC 1f0a0 0 crypto_hash_sha256_update
PUBLIC 1f0d0 0 crypto_hash_sha256_final
PUBLIC 1f320 0 crypto_hash_sha256
PUBLIC 1f3b0 0 crypto_hash_sha512_bytes
PUBLIC 1f3c0 0 crypto_hash_sha512_statebytes
PUBLIC 1f3d0 0 SHA512_Transform
PUBLIC 1fee0 0 crypto_hash_sha512_update.part.0
PUBLIC 20630 0 crypto_hash_sha512_init
PUBLIC 20670 0 crypto_hash_sha512_update
PUBLIC 206a0 0 crypto_hash_sha512_final
PUBLIC 20820 0 crypto_hash_sha512
PUBLIC 208b0 0 crypto_kdf_blake2b_bytes_min
PUBLIC 208c0 0 crypto_kdf_blake2b_bytes_max
PUBLIC 208d0 0 crypto_kdf_blake2b_contextbytes
PUBLIC 208e0 0 crypto_kdf_blake2b_keybytes
PUBLIC 208f0 0 crypto_kdf_blake2b_derive_from_key
PUBLIC 20980 0 crypto_kdf_primitive
PUBLIC 20990 0 crypto_kdf_bytes_min
PUBLIC 209a0 0 crypto_kdf_bytes_max
PUBLIC 209b0 0 crypto_kdf_contextbytes
PUBLIC 209c0 0 crypto_kdf_keybytes
PUBLIC 209d0 0 crypto_kdf_derive_from_key
PUBLIC 209e0 0 crypto_kdf_keygen
PUBLIC 209f0 0 crypto_kx_seed_keypair
PUBLIC 20a30 0 crypto_kx_keypair
PUBLIC 20a70 0 crypto_kx_client_session_keys
PUBLIC 20c30 0 crypto_kx_server_session_keys
PUBLIC 20df0 0 crypto_kx_publickeybytes
PUBLIC 20e00 0 crypto_kx_secretkeybytes
PUBLIC 20e10 0 crypto_kx_seedbytes
PUBLIC 20e20 0 crypto_kx_sessionkeybytes
PUBLIC 20e30 0 crypto_kx_primitive
PUBLIC 20e40 0 crypto_onetimeauth_statebytes
PUBLIC 20e50 0 crypto_onetimeauth_bytes
PUBLIC 20e60 0 crypto_onetimeauth_keybytes
PUBLIC 20e70 0 crypto_onetimeauth
PUBLIC 20e80 0 crypto_onetimeauth_verify
PUBLIC 20e90 0 crypto_onetimeauth_init
PUBLIC 20ea0 0 crypto_onetimeauth_update
PUBLIC 20eb0 0 crypto_onetimeauth_final
PUBLIC 20ec0 0 crypto_onetimeauth_primitive
PUBLIC 20ed0 0 crypto_onetimeauth_keygen
PUBLIC 20ee0 0 crypto_onetimeauth_poly1305
PUBLIC 20f00 0 crypto_onetimeauth_poly1305_verify
PUBLIC 20f20 0 crypto_onetimeauth_poly1305_init
PUBLIC 20f40 0 crypto_onetimeauth_poly1305_update
PUBLIC 20f60 0 crypto_onetimeauth_poly1305_final
PUBLIC 20f80 0 crypto_onetimeauth_poly1305_bytes
PUBLIC 20f90 0 crypto_onetimeauth_poly1305_keybytes
PUBLIC 20fa0 0 crypto_onetimeauth_poly1305_statebytes
PUBLIC 20fb0 0 crypto_onetimeauth_poly1305_keygen
PUBLIC 20fc0 0 _crypto_onetimeauth_poly1305_pick_best_implementation
PUBLIC 20fe0 0 poly1305_blocks
PUBLIC 21110 0 crypto_onetimeauth_poly1305_donna_init
PUBLIC 21160 0 poly1305_blocks.constprop.0
PUBLIC 21270 0 poly1305_finish
PUBLIC 213b0 0 crypto_onetimeauth_poly1305_donna_final
PUBLIC 213d0 0 crypto_onetimeauth_poly1305_donna
PUBLIC 214e0 0 crypto_onetimeauth_poly1305_donna_update
PUBLIC 217f0 0 crypto_onetimeauth_poly1305_donna_verify
PUBLIC 21900 0 initial_hash.part.0
PUBLIC 21b10 0 free_instance
PUBLIC 21b90 0 finalize
PUBLIC 21d20 0 fill_memory_blocks
PUBLIC 21dd0 0 validate_inputs
PUBLIC 21ef0 0 fill_first_blocks
PUBLIC 220c0 0 initial_hash
PUBLIC 220e0 0 initialize
PUBLIC 22250 0 argon2_pick_best_implementation
PUBLIC 22270 0 _crypto_pwhash_argon2_pick_best_implementation
PUBLIC 22290 0 decode_string
PUBLIC 227a0 0 encode_string
PUBLIC 22f70 0 fill_block
PUBLIC 23600 0 fill_block_with_xor
PUBLIC 23cc0 0 generate_addresses
PUBLIC 23e20 0 fill_segment_ref
PUBLIC 240a0 0 argon2_ctx
PUBLIC 24180 0 argon2_hash
PUBLIC 242f0 0 argon2i_hash_encoded
PUBLIC 24450 0 argon2i_hash_raw
PUBLIC 24570 0 argon2id_hash_encoded
PUBLIC 246d0 0 argon2id_hash_raw
PUBLIC 247f0 0 argon2_verify
PUBLIC 249d0 0 argon2i_verify
PUBLIC 249e0 0 argon2id_verify
PUBLIC 249f0 0 blake2b_long
PUBLIC 24c70 0 crypto_pwhash_argon2i_alg_argon2i13
PUBLIC 24c80 0 crypto_pwhash_argon2i_bytes_min
PUBLIC 24c90 0 crypto_pwhash_argon2i_bytes_max
PUBLIC 24ca0 0 crypto_pwhash_argon2i_passwd_min
PUBLIC 24cb0 0 crypto_pwhash_argon2i_passwd_max
PUBLIC 24cc0 0 crypto_pwhash_argon2i_saltbytes
PUBLIC 24cd0 0 crypto_pwhash_argon2i_strbytes
PUBLIC 24ce0 0 crypto_pwhash_argon2i_strprefix
PUBLIC 24cf0 0 crypto_pwhash_argon2i_opslimit_min
PUBLIC 24d00 0 crypto_pwhash_argon2i_opslimit_max
PUBLIC 24d10 0 crypto_pwhash_argon2i_memlimit_min
PUBLIC 24d20 0 crypto_pwhash_argon2i_memlimit_max
PUBLIC 24d30 0 crypto_pwhash_argon2i_opslimit_interactive
PUBLIC 24d40 0 crypto_pwhash_argon2i_memlimit_interactive
PUBLIC 24d50 0 crypto_pwhash_argon2i_opslimit_moderate
PUBLIC 24d60 0 crypto_pwhash_argon2i_memlimit_moderate
PUBLIC 24d70 0 crypto_pwhash_argon2i_opslimit_sensitive
PUBLIC 24d80 0 crypto_pwhash_argon2i_memlimit_sensitive
PUBLIC 24d90 0 crypto_pwhash_argon2i
PUBLIC 24ea0 0 crypto_pwhash_argon2i_str
PUBLIC 24fe0 0 crypto_pwhash_argon2i_str_verify
PUBLIC 25050 0 crypto_pwhash_argon2i_str_needs_rehash
PUBLIC 25180 0 crypto_pwhash_argon2id_str_needs_rehash
PUBLIC 252b0 0 crypto_pwhash_argon2id_alg_argon2id13
PUBLIC 252c0 0 crypto_pwhash_argon2id_bytes_min
PUBLIC 252d0 0 crypto_pwhash_argon2id_bytes_max
PUBLIC 252e0 0 crypto_pwhash_argon2id_passwd_min
PUBLIC 252f0 0 crypto_pwhash_argon2id_passwd_max
PUBLIC 25300 0 crypto_pwhash_argon2id_saltbytes
PUBLIC 25310 0 crypto_pwhash_argon2id_strbytes
PUBLIC 25320 0 crypto_pwhash_argon2id_strprefix
PUBLIC 25330 0 crypto_pwhash_argon2id_opslimit_min
PUBLIC 25340 0 crypto_pwhash_argon2id_opslimit_max
PUBLIC 25350 0 crypto_pwhash_argon2id_memlimit_min
PUBLIC 25360 0 crypto_pwhash_argon2id_memlimit_max
PUBLIC 25370 0 crypto_pwhash_argon2id_opslimit_interactive
PUBLIC 25380 0 crypto_pwhash_argon2id_memlimit_interactive
PUBLIC 25390 0 crypto_pwhash_argon2id_opslimit_moderate
PUBLIC 253a0 0 crypto_pwhash_argon2id_memlimit_moderate
PUBLIC 253b0 0 crypto_pwhash_argon2id_opslimit_sensitive
PUBLIC 253c0 0 crypto_pwhash_argon2id_memlimit_sensitive
PUBLIC 253d0 0 crypto_pwhash_argon2id
PUBLIC 254e0 0 crypto_pwhash_argon2id_str
PUBLIC 25620 0 crypto_pwhash_argon2id_str_verify
PUBLIC 25690 0 crypto_pwhash_alg_argon2i13
PUBLIC 256a0 0 crypto_pwhash_alg_argon2id13
PUBLIC 256b0 0 crypto_pwhash_alg_default
PUBLIC 256c0 0 crypto_pwhash_bytes_min
PUBLIC 256d0 0 crypto_pwhash_bytes_max
PUBLIC 256e0 0 crypto_pwhash_passwd_min
PUBLIC 256f0 0 crypto_pwhash_passwd_max
PUBLIC 25700 0 crypto_pwhash_saltbytes
PUBLIC 25710 0 crypto_pwhash_strbytes
PUBLIC 25720 0 crypto_pwhash_strprefix
PUBLIC 25730 0 crypto_pwhash_opslimit_min
PUBLIC 25740 0 crypto_pwhash_opslimit_max
PUBLIC 25750 0 crypto_pwhash_memlimit_min
PUBLIC 25760 0 crypto_pwhash_memlimit_max
PUBLIC 25770 0 crypto_pwhash_opslimit_interactive
PUBLIC 25780 0 crypto_pwhash_memlimit_interactive
PUBLIC 25790 0 crypto_pwhash_opslimit_moderate
PUBLIC 257a0 0 crypto_pwhash_memlimit_moderate
PUBLIC 257b0 0 crypto_pwhash_opslimit_sensitive
PUBLIC 257c0 0 crypto_pwhash_memlimit_sensitive
PUBLIC 257d0 0 crypto_pwhash
PUBLIC 25810 0 crypto_pwhash_str
PUBLIC 25820 0 crypto_pwhash_str_alg
PUBLIC 25850 0 crypto_pwhash_str_verify
PUBLIC 25900 0 crypto_pwhash_str_needs_rehash
PUBLIC 259b0 0 crypto_pwhash_primitive
PUBLIC 259c0 0 crypto_scalarmult_primitive
PUBLIC 259d0 0 crypto_scalarmult_base
PUBLIC 259e0 0 crypto_scalarmult
PUBLIC 259f0 0 crypto_scalarmult_bytes
PUBLIC 25a00 0 crypto_scalarmult_scalarbytes
PUBLIC 25a10 0 fe25519_sub
PUBLIC 25aa0 0 fe25519_mul
PUBLIC 25ce0 0 fe25519_sq
PUBLIC 25e70 0 crypto_scalarmult_curve25519_ref10_base
PUBLIC 25f70 0 crypto_scalarmult_curve25519_ref10.part.0
PUBLIC 264d0 0 crypto_scalarmult_curve25519_ref10
PUBLIC 26940 0 crypto_scalarmult_curve25519
PUBLIC 269c0 0 crypto_scalarmult_curve25519_base
PUBLIC 269e0 0 crypto_scalarmult_curve25519_bytes
PUBLIC 269f0 0 crypto_scalarmult_curve25519_scalarbytes
PUBLIC 26a00 0 _crypto_scalarmult_curve25519_pick_best_implementation
PUBLIC 26a20 0 crypto_secretbox_keybytes
PUBLIC 26a30 0 crypto_secretbox_noncebytes
PUBLIC 26a40 0 crypto_secretbox_zerobytes
PUBLIC 26a50 0 crypto_secretbox_boxzerobytes
PUBLIC 26a60 0 crypto_secretbox_macbytes
PUBLIC 26a70 0 crypto_secretbox_messagebytes_max
PUBLIC 26a80 0 crypto_secretbox_primitive
PUBLIC 26a90 0 crypto_secretbox
PUBLIC 26aa0 0 crypto_secretbox_open
PUBLIC 26ab0 0 crypto_secretbox_keygen
PUBLIC 26ac0 0 crypto_secretbox_detached
PUBLIC 26cc0 0 crypto_secretbox_easy
PUBLIC 26d00 0 crypto_secretbox_open_detached
PUBLIC 26ed0 0 crypto_secretbox_open_easy
PUBLIC 26f00 0 crypto_secretbox_xsalsa20poly1305
PUBLIC 26f50 0 crypto_secretbox_xsalsa20poly1305_open
PUBLIC 27030 0 crypto_secretbox_xsalsa20poly1305_keybytes
PUBLIC 27040 0 crypto_secretbox_xsalsa20poly1305_noncebytes
PUBLIC 27050 0 crypto_secretbox_xsalsa20poly1305_zerobytes
PUBLIC 27060 0 crypto_secretbox_xsalsa20poly1305_boxzerobytes
PUBLIC 27070 0 crypto_secretbox_xsalsa20poly1305_macbytes
PUBLIC 27080 0 crypto_secretbox_xsalsa20poly1305_messagebytes_max
PUBLIC 27090 0 crypto_secretbox_xsalsa20poly1305_keygen
PUBLIC 270a0 0 crypto_secretstream_xchacha20poly1305_keygen
PUBLIC 270b0 0 crypto_secretstream_xchacha20poly1305_init_push
PUBLIC 27120 0 crypto_secretstream_xchacha20poly1305_init_pull
PUBLIC 27170 0 crypto_secretstream_xchacha20poly1305_rekey
PUBLIC 27210 0 crypto_secretstream_xchacha20poly1305_push
PUBLIC 27510 0 crypto_secretstream_xchacha20poly1305_pull
PUBLIC 277c0 0 crypto_secretstream_xchacha20poly1305_statebytes
PUBLIC 277d0 0 crypto_secretstream_xchacha20poly1305_abytes
PUBLIC 277e0 0 crypto_secretstream_xchacha20poly1305_headerbytes
PUBLIC 277f0 0 crypto_secretstream_xchacha20poly1305_keybytes
PUBLIC 27800 0 crypto_secretstream_xchacha20poly1305_messagebytes_max
PUBLIC 27810 0 crypto_secretstream_xchacha20poly1305_tag_message
PUBLIC 27820 0 crypto_secretstream_xchacha20poly1305_tag_push
PUBLIC 27830 0 crypto_secretstream_xchacha20poly1305_tag_rekey
PUBLIC 27840 0 crypto_secretstream_xchacha20poly1305_tag_final
PUBLIC 27850 0 crypto_shorthash_bytes
PUBLIC 27860 0 crypto_shorthash_keybytes
PUBLIC 27870 0 crypto_shorthash_primitive
PUBLIC 27880 0 crypto_shorthash
PUBLIC 27890 0 crypto_shorthash_keygen
PUBLIC 278a0 0 crypto_shorthash_siphash24_bytes
PUBLIC 278b0 0 crypto_shorthash_siphash24_keybytes
PUBLIC 278c0 0 crypto_shorthash_siphash24
PUBLIC 27b30 0 crypto_sign_statebytes
PUBLIC 27b40 0 crypto_sign_bytes
PUBLIC 27b50 0 crypto_sign_seedbytes
PUBLIC 27b60 0 crypto_sign_publickeybytes
PUBLIC 27b70 0 crypto_sign_secretkeybytes
PUBLIC 27b80 0 crypto_sign_messagebytes_max
PUBLIC 27b90 0 crypto_sign_primitive
PUBLIC 27ba0 0 crypto_sign_seed_keypair
PUBLIC 27bb0 0 crypto_sign_keypair
PUBLIC 27bc0 0 crypto_sign
PUBLIC 27bd0 0 crypto_sign_open
PUBLIC 27be0 0 crypto_sign_detached
PUBLIC 27bf0 0 crypto_sign_verify_detached
PUBLIC 27c00 0 crypto_sign_init
PUBLIC 27c10 0 crypto_sign_update
PUBLIC 27c20 0 crypto_sign_final_create
PUBLIC 27c30 0 crypto_sign_final_verify
PUBLIC 27c40 0 crypto_sign_ed25519ph_statebytes
PUBLIC 27c50 0 crypto_sign_ed25519_bytes
PUBLIC 27c60 0 crypto_sign_ed25519_seedbytes
PUBLIC 27c70 0 crypto_sign_ed25519_publickeybytes
PUBLIC 27c80 0 crypto_sign_ed25519_secretkeybytes
PUBLIC 27c90 0 crypto_sign_ed25519_messagebytes_max
PUBLIC 27ca0 0 crypto_sign_ed25519_sk_to_seed
PUBLIC 27cc0 0 crypto_sign_ed25519_sk_to_pk
PUBLIC 27ce0 0 crypto_sign_ed25519ph_init
PUBLIC 27d00 0 crypto_sign_ed25519ph_update
PUBLIC 27d10 0 crypto_sign_ed25519ph_final_create
PUBLIC 27da0 0 crypto_sign_ed25519ph_final_verify
PUBLIC 27e20 0 crypto_sign_ed25519_seed_keypair
PUBLIC 27ec0 0 crypto_sign_ed25519_keypair
PUBLIC 27f50 0 crypto_sign_ed25519_pk_to_curve25519
PUBLIC 28260 0 crypto_sign_ed25519_sk_to_curve25519
PUBLIC 28300 0 _crypto_sign_ed25519_verify_detached
PUBLIC 28480 0 crypto_sign_ed25519_verify_detached
PUBLIC 28490 0 crypto_sign_ed25519_open
PUBLIC 28570 0 _crypto_sign_ed25519_ref10_hinit
PUBLIC 285c0 0 _crypto_sign_ed25519_detached
PUBLIC 28840 0 crypto_sign_ed25519_detached
PUBLIC 28850 0 crypto_sign_ed25519
PUBLIC 288f0 0 crypto_stream_chacha20_keybytes
PUBLIC 28900 0 crypto_stream_chacha20_noncebytes
PUBLIC 28910 0 crypto_stream_chacha20_messagebytes_max
PUBLIC 28920 0 crypto_stream_chacha20_ietf_keybytes
PUBLIC 28930 0 crypto_stream_chacha20_ietf_noncebytes
PUBLIC 28940 0 crypto_stream_chacha20_ietf_messagebytes_max
PUBLIC 28950 0 crypto_stream_chacha20
PUBLIC 28970 0 crypto_stream_chacha20_xor_ic
PUBLIC 28990 0 crypto_stream_chacha20_xor
PUBLIC 289b0 0 crypto_stream_chacha20_ietf_ext
PUBLIC 289d0 0 crypto_stream_chacha20_ietf_ext_xor_ic
PUBLIC 289f0 0 crypto_stream_chacha20_ietf
PUBLIC 28a20 0 crypto_stream_chacha20_ietf_xor_ic
PUBLIC 28a60 0 crypto_stream_chacha20_ietf_xor
PUBLIC 28aa0 0 crypto_stream_chacha20_ietf_keygen
PUBLIC 28ab0 0 crypto_stream_chacha20_keygen
PUBLIC 28ac0 0 _crypto_stream_chacha20_pick_best_implementation
PUBLIC 28ae0 0 chacha20_encrypt_bytes.part.0
PUBLIC 293a0 0 stream_ietf_ext_ref_xor_ic
PUBLIC 29420 0 stream_ref_xor_ic
PUBLIC 29490 0 stream_ietf_ext_ref
PUBLIC 29520 0 stream_ref
PUBLIC 295b0 0 crypto_stream_keybytes
PUBLIC 295c0 0 crypto_stream_noncebytes
PUBLIC 295d0 0 crypto_stream_messagebytes_max
PUBLIC 295e0 0 crypto_stream_primitive
PUBLIC 295f0 0 crypto_stream
PUBLIC 29600 0 crypto_stream_xor
PUBLIC 29610 0 crypto_stream_keygen
PUBLIC 29620 0 crypto_stream_salsa20_keybytes
PUBLIC 29630 0 crypto_stream_salsa20_noncebytes
PUBLIC 29640 0 crypto_stream_salsa20_messagebytes_max
PUBLIC 29650 0 crypto_stream_salsa20
PUBLIC 29670 0 crypto_stream_salsa20_xor_ic
PUBLIC 29690 0 crypto_stream_salsa20_xor
PUBLIC 296b0 0 crypto_stream_salsa20_keygen
PUBLIC 296c0 0 _crypto_stream_salsa20_pick_best_implementation
PUBLIC 296e0 0 crypto_stream_xsalsa20
PUBLIC 29780 0 crypto_stream_xsalsa20_xor_ic
PUBLIC 29840 0 crypto_stream_xsalsa20_xor
PUBLIC 29850 0 crypto_stream_xsalsa20_keybytes
PUBLIC 29860 0 crypto_stream_xsalsa20_noncebytes
PUBLIC 29870 0 crypto_stream_xsalsa20_messagebytes_max
PUBLIC 29880 0 crypto_stream_xsalsa20_keygen
PUBLIC 29890 0 crypto_verify_16_bytes
PUBLIC 298a0 0 crypto_verify_32_bytes
PUBLIC 298b0 0 crypto_verify_64_bytes
PUBLIC 298c0 0 crypto_verify_16
PUBLIC 29b30 0 crypto_verify_32
PUBLIC 29b90 0 crypto_verify_64
PUBLIC 29bf0 0 randombytes_set_implementation
PUBLIC 29c10 0 randombytes_stir
PUBLIC 29c70 0 randombytes_implementation_name
PUBLIC 29cd0 0 randombytes_random
PUBLIC 29d30 0 randombytes_uniform
PUBLIC 29dc0 0 randombytes_buf
PUBLIC 29e20 0 randombytes_buf_deterministic
PUBLIC 29e50 0 randombytes_seedbytes
PUBLIC 29e60 0 randombytes_close
PUBLIC 29e90 0 randombytes
PUBLIC 29ea0 0 sodium_bin2hex
PUBLIC 2a080 0 sodium_hex2bin
PUBLIC 2a250 0 sodium_base64_encoded_len
PUBLIC 2a2c0 0 sodium_bin2base64
PUBLIC 2a6a0 0 sodium_base642bin
PUBLIC 2aa00 0 sodium_init
PUBLIC 2aab0 0 sodium_crit_enter
PUBLIC 2aaf0 0 sodium_crit_leave
PUBLIC 2ab10 0 sodium_misuse
PUBLIC 2ab60 0 sodium_set_misuse_handler
PUBLIC 2abd0 0 _sodium_runtime_get_cpu_features
PUBLIC 2abf0 0 sodium_runtime_has_neon
PUBLIC 2ac00 0 sodium_runtime_has_sse2
PUBLIC 2ac10 0 sodium_runtime_has_sse3
PUBLIC 2ac20 0 sodium_runtime_has_ssse3
PUBLIC 2ac30 0 sodium_runtime_has_sse41
PUBLIC 2ac40 0 sodium_runtime_has_avx
PUBLIC 2ac50 0 sodium_runtime_has_avx2
PUBLIC 2ac60 0 sodium_runtime_has_avx512f
PUBLIC 2ac70 0 sodium_runtime_has_pclmul
PUBLIC 2ac80 0 sodium_runtime_has_aesni
PUBLIC 2ac90 0 sodium_runtime_has_rdrand
PUBLIC 2aca0 0 _sodium_dummy_symbol_to_prevent_memzero_lto
PUBLIC 2acb0 0 sodium_memzero
PUBLIC 2acc0 0 sodium_stackzero
PUBLIC 2ad20 0 _sodium_dummy_symbol_to_prevent_memcmp_lto
PUBLIC 2ad30 0 sodium_memcmp
PUBLIC 2adb0 0 _sodium_dummy_symbol_to_prevent_compare_lto
PUBLIC 2adc0 0 sodium_compare
PUBLIC 2ae70 0 sodium_is_zero
PUBLIC 2aec0 0 sodium_increment
PUBLIC 2aef0 0 sodium_add
PUBLIC 2af30 0 sodium_sub
PUBLIC 2af70 0 _sodium_alloc_init
PUBLIC 2afc0 0 sodium_mlock
PUBLIC 2aff0 0 sodium_munlock
PUBLIC 2b030 0 sodium_malloc
PUBLIC 2b1a0 0 sodium_allocarray
PUBLIC 2b1e0 0 sodium_free
PUBLIC 2b290 0 sodium_mprotect_noaccess
PUBLIC 2b2d0 0 sodium_mprotect_readonly
PUBLIC 2b310 0 sodium_mprotect_readwrite
PUBLIC 2b350 0 sodium_pad
PUBLIC 2b420 0 sodium_unpad
PUBLIC 2b4d0 0 sodium_version_string
PUBLIC 2b4e0 0 sodium_library_version_major
PUBLIC 2b4f0 0 sodium_library_version_minor
PUBLIC 2b500 0 sodium_library_minimal
PUBLIC 2b510 0 stream_ref_xor_ic
PUBLIC 2b980 0 stream_ref
PUBLIC 2bb10 0 crypto_box_curve25519xchacha20poly1305_seed_keypair
PUBLIC 2bba0 0 crypto_box_curve25519xchacha20poly1305_keypair
PUBLIC 2bbe0 0 crypto_box_curve25519xchacha20poly1305_beforenm
PUBLIC 2bc70 0 crypto_box_curve25519xchacha20poly1305_detached_afternm
PUBLIC 2bc80 0 crypto_box_curve25519xchacha20poly1305_detached
PUBLIC 2bd40 0 crypto_box_curve25519xchacha20poly1305_easy_afternm
PUBLIC 2bd80 0 crypto_box_curve25519xchacha20poly1305_easy
PUBLIC 2bdc0 0 crypto_box_curve25519xchacha20poly1305_open_detached_afternm
PUBLIC 2bdd0 0 crypto_box_curve25519xchacha20poly1305_open_detached
PUBLIC 2be90 0 crypto_box_curve25519xchacha20poly1305_open_easy_afternm
PUBLIC 2bec0 0 crypto_box_curve25519xchacha20poly1305_open_easy
PUBLIC 2bef0 0 crypto_box_curve25519xchacha20poly1305_seedbytes
PUBLIC 2bf00 0 crypto_box_curve25519xchacha20poly1305_publickeybytes
PUBLIC 2bf10 0 crypto_box_curve25519xchacha20poly1305_secretkeybytes
PUBLIC 2bf20 0 crypto_box_curve25519xchacha20poly1305_beforenmbytes
PUBLIC 2bf30 0 crypto_box_curve25519xchacha20poly1305_noncebytes
PUBLIC 2bf40 0 crypto_box_curve25519xchacha20poly1305_macbytes
PUBLIC 2bf50 0 crypto_box_curve25519xchacha20poly1305_messagebytes_max
PUBLIC 2bf60 0 _crypto_box_curve25519xchacha20poly1305_seal_nonce
PUBLIC 2c020 0 crypto_box_curve25519xchacha20poly1305_seal
PUBLIC 2c170 0 crypto_box_curve25519xchacha20poly1305_seal_open
PUBLIC 2c220 0 crypto_box_curve25519xchacha20poly1305_sealbytes
PUBLIC 2c230 0 crypto_core_ed25519_is_valid_point
PUBLIC 2c2a0 0 crypto_core_ed25519_add
PUBLIC 2c370 0 crypto_core_ed25519_sub
PUBLIC 2c440 0 crypto_core_ed25519_from_uniform
PUBLIC 2c460 0 crypto_core_ed25519_from_hash
PUBLIC 2c480 0 crypto_core_ed25519_random
PUBLIC 2c4f0 0 crypto_core_ed25519_scalar_random
PUBLIC 2c550 0 crypto_core_ed25519_scalar_invert
PUBLIC 2c580 0 crypto_core_ed25519_scalar_negate
PUBLIC 2c640 0 crypto_core_ed25519_scalar_complement
PUBLIC 2c700 0 crypto_core_ed25519_scalar_mul
PUBLIC 2c710 0 crypto_core_ed25519_scalar_reduce
PUBLIC 2c7b0 0 crypto_core_ed25519_scalar_add
PUBLIC 2c860 0 crypto_core_ed25519_scalar_sub
PUBLIC 2c8d0 0 crypto_core_ed25519_bytes
PUBLIC 2c8e0 0 crypto_core_ed25519_nonreducedscalarbytes
PUBLIC 2c8f0 0 crypto_core_ed25519_uniformbytes
PUBLIC 2c900 0 crypto_core_ed25519_hashbytes
PUBLIC 2c910 0 crypto_core_ed25519_scalarbytes
PUBLIC 2c920 0 crypto_core_ristretto255_is_valid_point
PUBLIC 2c950 0 crypto_core_ristretto255_add
PUBLIC 2ca00 0 crypto_core_ristretto255_sub
PUBLIC 2cab0 0 crypto_core_ristretto255_from_hash
PUBLIC 2cad0 0 crypto_core_ristretto255_random
PUBLIC 2cb40 0 crypto_core_ristretto255_scalar_random
PUBLIC 2cb50 0 crypto_core_ristretto255_scalar_invert
PUBLIC 2cb60 0 crypto_core_ristretto255_scalar_negate
PUBLIC 2cb70 0 crypto_core_ristretto255_scalar_complement
PUBLIC 2cb80 0 crypto_core_ristretto255_scalar_add
PUBLIC 2cb90 0 crypto_core_ristretto255_scalar_sub
PUBLIC 2cba0 0 crypto_core_ristretto255_scalar_mul
PUBLIC 2cbb0 0 crypto_core_ristretto255_scalar_reduce
PUBLIC 2cbc0 0 crypto_core_ristretto255_bytes
PUBLIC 2cbd0 0 crypto_core_ristretto255_nonreducedscalarbytes
PUBLIC 2cbe0 0 crypto_core_ristretto255_hashbytes
PUBLIC 2cbf0 0 crypto_core_ristretto255_scalarbytes
PUBLIC 2cc00 0 escrypt_parse_setting
PUBLIC 2cdf0 0 escrypt_r
PUBLIC 2d010 0 escrypt_gensalt_r
PUBLIC 2d230 0 crypto_pwhash_scryptsalsa208sha256_ll
PUBLIC 2d2f0 0 alloc_region
PUBLIC 2d360 0 free_region
PUBLIC 2d3b0 0 escrypt_init_local
PUBLIC 2d3d0 0 escrypt_free_local
PUBLIC 2d420 0 PBKDF2_SHA256
PUBLIC 2d610 0 crypto_pwhash_scryptsalsa208sha256_bytes_min
PUBLIC 2d620 0 crypto_pwhash_scryptsalsa208sha256_bytes_max
PUBLIC 2d630 0 crypto_pwhash_scryptsalsa208sha256_passwd_min
PUBLIC 2d640 0 crypto_pwhash_scryptsalsa208sha256_passwd_max
PUBLIC 2d650 0 crypto_pwhash_scryptsalsa208sha256_saltbytes
PUBLIC 2d660 0 crypto_pwhash_scryptsalsa208sha256_strbytes
PUBLIC 2d670 0 crypto_pwhash_scryptsalsa208sha256_strprefix
PUBLIC 2d680 0 crypto_pwhash_scryptsalsa208sha256_opslimit_min
PUBLIC 2d690 0 crypto_pwhash_scryptsalsa208sha256_opslimit_max
PUBLIC 2d6a0 0 crypto_pwhash_scryptsalsa208sha256_memlimit_min
PUBLIC 2d6b0 0 crypto_pwhash_scryptsalsa208sha256_memlimit_max
PUBLIC 2d6c0 0 crypto_pwhash_scryptsalsa208sha256_opslimit_interactive
PUBLIC 2d6d0 0 crypto_pwhash_scryptsalsa208sha256_memlimit_interactive
PUBLIC 2d6e0 0 crypto_pwhash_scryptsalsa208sha256_opslimit_sensitive
PUBLIC 2d6f0 0 crypto_pwhash_scryptsalsa208sha256_memlimit_sensitive
PUBLIC 2d700 0 crypto_pwhash_scryptsalsa208sha256
PUBLIC 2d860 0 crypto_pwhash_scryptsalsa208sha256_str
PUBLIC 2da10 0 crypto_pwhash_scryptsalsa208sha256_str_verify
PUBLIC 2db40 0 crypto_pwhash_scryptsalsa208sha256_str_needs_rehash
PUBLIC 2dc60 0 salsa20_8
PUBLIC 2de60 0 blockmix_salsa8
PUBLIC 2e0c0 0 escrypt_kdf_nosse
PUBLIC 2e9d0 0 _crypto_scalarmult_ed25519
PUBLIC 2ebb0 0 _crypto_scalarmult_ed25519_base
PUBLIC 2ed40 0 crypto_scalarmult_ed25519
PUBLIC 2ed50 0 crypto_scalarmult_ed25519_noclamp
PUBLIC 2ed60 0 crypto_scalarmult_ed25519_base
PUBLIC 2ed70 0 crypto_scalarmult_ed25519_base_noclamp
PUBLIC 2ed80 0 crypto_scalarmult_ed25519_bytes
PUBLIC 2ed90 0 crypto_scalarmult_ed25519_scalarbytes
PUBLIC 2eda0 0 crypto_scalarmult_ristretto255
PUBLIC 2ee70 0 crypto_scalarmult_ristretto255_base
PUBLIC 2ef10 0 crypto_scalarmult_ristretto255_bytes
PUBLIC 2ef20 0 crypto_scalarmult_ristretto255_scalarbytes
PUBLIC 2ef30 0 crypto_secretbox_xchacha20poly1305_detached
PUBLIC 2f130 0 crypto_secretbox_xchacha20poly1305_easy
PUBLIC 2f170 0 crypto_secretbox_xchacha20poly1305_open_detached
PUBLIC 2f340 0 crypto_secretbox_xchacha20poly1305_open_easy
PUBLIC 2f370 0 crypto_secretbox_xchacha20poly1305_keybytes
PUBLIC 2f380 0 crypto_secretbox_xchacha20poly1305_noncebytes
PUBLIC 2f390 0 crypto_secretbox_xchacha20poly1305_macbytes
PUBLIC 2f3a0 0 crypto_secretbox_xchacha20poly1305_messagebytes_max
PUBLIC 2f3b0 0 crypto_shorthash_siphashx24_bytes
PUBLIC 2f3c0 0 crypto_shorthash_siphashx24_keybytes
PUBLIC 2f3d0 0 crypto_shorthash_siphashx24
PUBLIC 2f700 0 crypto_sign_edwards25519sha512batch_keypair
PUBLIC 2f780 0 crypto_sign_edwards25519sha512batch
PUBLIC 2f940 0 crypto_sign_edwards25519sha512batch_open
PUBLIC 2fb10 0 crypto_stream_salsa2012
PUBLIC 2fca0 0 crypto_stream_salsa2012_xor
PUBLIC 30110 0 crypto_stream_salsa2012_keybytes
PUBLIC 30120 0 crypto_stream_salsa2012_noncebytes
PUBLIC 30130 0 crypto_stream_salsa2012_messagebytes_max
PUBLIC 30140 0 crypto_stream_salsa2012_keygen
PUBLIC 30150 0 crypto_stream_salsa208
PUBLIC 302e0 0 crypto_stream_salsa208_xor
PUBLIC 30750 0 crypto_stream_salsa208_keybytes
PUBLIC 30760 0 crypto_stream_salsa208_noncebytes
PUBLIC 30770 0 crypto_stream_salsa208_messagebytes_max
PUBLIC 30780 0 crypto_stream_salsa208_keygen
PUBLIC 30790 0 crypto_stream_xchacha20_keybytes
PUBLIC 307a0 0 crypto_stream_xchacha20_noncebytes
PUBLIC 307b0 0 crypto_stream_xchacha20_messagebytes_max
PUBLIC 307c0 0 crypto_stream_xchacha20
PUBLIC 30850 0 crypto_stream_xchacha20_xor_ic
PUBLIC 30900 0 crypto_stream_xchacha20_xor
PUBLIC 30910 0 crypto_stream_xchacha20_keygen
PUBLIC 30920 0 randombytes_sysrandom_implementation_name
PUBLIC 30930 0 randombytes_sysrandom_close
PUBLIC 309a0 0 randombytes_sysrandom_init
PUBLIC 30b90 0 randombytes_sysrandom_stir
PUBLIC 30bd0 0 randombytes_sysrandom_buf
PUBLIC 30cf0 0 randombytes_sysrandom
PUBLIC 30d10 0 crypto_aead_aes256gcm_encrypt_detached
PUBLIC 30d40 0 crypto_aead_aes256gcm_encrypt
PUBLIC 30d70 0 crypto_aead_aes256gcm_decrypt_detached
PUBLIC 30da0 0 crypto_aead_aes256gcm_decrypt
PUBLIC 30dd0 0 crypto_aead_aes256gcm_beforenm
PUBLIC 30e00 0 crypto_aead_aes256gcm_encrypt_detached_afternm
PUBLIC 30e30 0 crypto_aead_aes256gcm_encrypt_afternm
PUBLIC 30e60 0 crypto_aead_aes256gcm_decrypt_detached_afternm
PUBLIC 30e90 0 crypto_aead_aes256gcm_decrypt_afternm
PUBLIC 30ec0 0 crypto_aead_aes256gcm_is_available
PUBLIC 30ed0 0 crypto_aead_aes256gcm_keybytes
PUBLIC 30ee0 0 crypto_aead_aes256gcm_nsecbytes
PUBLIC 30ef0 0 crypto_aead_aes256gcm_npubbytes
PUBLIC 30f00 0 crypto_aead_aes256gcm_abytes
PUBLIC 30f10 0 crypto_aead_aes256gcm_statebytes
PUBLIC 30f20 0 crypto_aead_aes256gcm_messagebytes_max
PUBLIC 30f30 0 crypto_aead_aes256gcm_keygen
PUBLIC 30f40 0 randombytes_internal_implementation_name
PUBLIC 30f50 0 randombytes_internal_random_close
PUBLIC 30fa0 0 randombytes_internal_random_stir
PUBLIC 31240 0 randombytes_internal_random
PUBLIC 31330 0 randombytes_internal_random_buf
PUBLIC 313f4 0 _fini
STACK CFI INIT d9a4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d9d4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT da10 50 .cfa: sp 0 + .ra: x30
STACK CFI da20 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da28 x19: .cfa -16 + ^
STACK CFI da58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT da60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT da70 150 .cfa: sp 0 + .ra: x30
STACK CFI da74 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI da7c x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI da8c x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI da98 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI daa4 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI daac x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI dbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI dbbc .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT dbc0 70 .cfa: sp 0 + .ra: x30
STACK CFI dbc4 .cfa: sp 48 +
STACK CFI dbcc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dbd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI dc28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc2c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT dc30 180 .cfa: sp 0 + .ra: x30
STACK CFI dc34 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI dc3c x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI dc4c x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI dc58 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI dc64 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI dc6c x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI dda8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ddac .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT ddb0 74 .cfa: sp 0 + .ra: x30
STACK CFI ddb4 .cfa: sp 48 +
STACK CFI ddb8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ddc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI de1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI de20 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT de30 188 .cfa: sp 0 + .ra: x30
STACK CFI de34 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI de3c x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI de4c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI de58 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI de6c x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI df98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI df9c .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT dfc0 6c .cfa: sp 0 + .ra: x30
STACK CFI dfc4 .cfa: sp 48 +
STACK CFI dfcc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dfd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e01c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e030 1b8 .cfa: sp 0 + .ra: x30
STACK CFI e034 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI e03c x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI e04c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI e060 x21: .cfa -464 + ^ x22: .cfa -456 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI e06c x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI e1c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e1cc .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT e1f0 6c .cfa: sp 0 + .ra: x30
STACK CFI e1f4 .cfa: sp 48 +
STACK CFI e1fc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e204 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e24c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e2f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e300 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e320 1cc .cfa: sp 0 + .ra: x30
STACK CFI e324 .cfa: sp 528 +
STACK CFI e328 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI e330 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI e340 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI e350 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI e364 x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI e4e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e4e8 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT e4f0 70 .cfa: sp 0 + .ra: x30
STACK CFI e4f4 .cfa: sp 48 +
STACK CFI e4fc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e504 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e55c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e560 1fc .cfa: sp 0 + .ra: x30
STACK CFI e564 .cfa: sp 528 +
STACK CFI e56c .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI e57c x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI e584 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI e594 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI e5ac x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI e73c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e740 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT e760 6c .cfa: sp 0 + .ra: x30
STACK CFI e764 .cfa: sp 48 +
STACK CFI e76c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e7bc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e7d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e7e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e7f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e850 c .cfa: sp 0 + .ra: x30
STACK CFI INIT e860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e8a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e8b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e8c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e8d0 5cc .cfa: sp 0 + .ra: x30
STACK CFI e8d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI e8dc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI e8e8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI e8f8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI e900 x25: .cfa -128 + ^
STACK CFI ea84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ea88 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI INIT eea0 18 .cfa: sp 0 + .ra: x30
STACK CFI eea4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI eeb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT eec0 8c .cfa: sp 0 + .ra: x30
STACK CFI eec4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI eecc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI eedc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ef44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ef48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT ef50 94 .cfa: sp 0 + .ra: x30
STACK CFI ef54 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI ef5c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI ef6c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI ef74 x23: .cfa -256 + ^
STACK CFI efdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI efe0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x29: .cfa -304 + ^
STACK CFI INIT eff0 7c .cfa: sp 0 + .ra: x30
STACK CFI eff4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI effc x21: .cfa -64 + ^
STACK CFI f004 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f068 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT f070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f090 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f0a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f0b0 68c .cfa: sp 0 + .ra: x30
STACK CFI f0b4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f0bc x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI f0c8 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI f0d8 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI f0e0 x25: .cfa -224 + ^
STACK CFI f32c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI f330 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x29: .cfa -288 + ^
STACK CFI INIT f740 18 .cfa: sp 0 + .ra: x30
STACK CFI f744 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f754 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f760 8c .cfa: sp 0 + .ra: x30
STACK CFI f764 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI f76c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI f77c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI f7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f7e8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT f7f0 9c .cfa: sp 0 + .ra: x30
STACK CFI f7f4 .cfa: sp 512 +
STACK CFI f7f8 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI f800 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI f810 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI f818 x23: .cfa -464 + ^
STACK CFI f884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f888 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x29: .cfa -512 + ^
STACK CFI INIT f890 7c .cfa: sp 0 + .ra: x30
STACK CFI f894 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI f89c x21: .cfa -96 + ^
STACK CFI f8a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI f904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f908 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT f910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f950 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f960 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f970 60 .cfa: sp 0 + .ra: x30
STACK CFI f974 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f97c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f9c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f9cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT f9d0 90 .cfa: sp 0 + .ra: x30
STACK CFI f9d4 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI f9dc x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI f9ec x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI f9f4 x23: .cfa -448 + ^
STACK CFI fa58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI fa5c .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x29: .cfa -496 + ^
STACK CFI INIT fa60 7c .cfa: sp 0 + .ra: x30
STACK CFI fa64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fa6c x21: .cfa -64 + ^
STACK CFI fa74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI fad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI fad8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT fae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT faf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT fb80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fb90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fba0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbe0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc00 c0 .cfa: sp 0 + .ra: x30
STACK CFI fc04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI fc0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI fc1c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI fc28 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI fc30 x25: .cfa -64 + ^
STACK CFI fcb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI fcb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT fcc0 34 .cfa: sp 0 + .ra: x30
STACK CFI fcec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT fd00 38 .cfa: sp 0 + .ra: x30
STACK CFI fd30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT fd40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd50 c0 .cfa: sp 0 + .ra: x30
STACK CFI fd54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI fd5c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI fd6c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI fd78 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI fd80 x25: .cfa -64 + ^
STACK CFI fe00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI fe04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT fe10 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT fe40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT fe70 b4 .cfa: sp 0 + .ra: x30
STACK CFI fe74 .cfa: sp 512 +
STACK CFI fe7c .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI fe84 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI fe9c x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^
STACK CFI ff1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ff20 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x29: .cfa -512 + ^
STACK CFI INIT ff30 150 .cfa: sp 0 + .ra: x30
STACK CFI ff34 .cfa: sp 624 +
STACK CFI ff38 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI ff40 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI ff54 x19: .cfa -608 + ^ x20: .cfa -600 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI ff60 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI ff6c x27: .cfa -544 + ^
STACK CFI 10070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 10074 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x29: .cfa -624 + ^
STACK CFI INIT 10080 a8 .cfa: sp 0 + .ra: x30
STACK CFI 10084 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1008c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 100b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 100c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 100f0 x21: x21 x22: x22
STACK CFI 100f4 x23: x23 x24: x24
STACK CFI 10110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10114 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 10120 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10124 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 10130 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10140 8c .cfa: sp 0 + .ra: x30
STACK CFI 10144 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1014c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1015c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 101c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 101c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 101d0 34 .cfa: sp 0 + .ra: x30
STACK CFI 101d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10210 8c .cfa: sp 0 + .ra: x30
STACK CFI 10214 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10224 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10234 x21: .cfa -64 + ^
STACK CFI 1028c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10290 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 102a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 102b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 102c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 102c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 102cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 102dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 102e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10364 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10370 b0 .cfa: sp 0 + .ra: x30
STACK CFI 10374 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1037c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1038c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10394 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10414 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10430 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104b0 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10540 234 .cfa: sp 0 + .ra: x30
STACK CFI 10544 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10558 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10780 190 .cfa: sp 0 + .ra: x30
STACK CFI 10784 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10798 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 10900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 10910 1a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10ab0 1ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c60 f8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d60 134 .cfa: sp 0 + .ra: x30
STACK CFI 10d64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10d6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10d78 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10d84 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10ea0 ec .cfa: sp 0 + .ra: x30
STACK CFI 10ea4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10eac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10eb8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10ec4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10f90 37c .cfa: sp 0 + .ra: x30
STACK CFI 10f94 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 10f9c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 10fa8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 10fb8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 10fc0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 11308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 11310 c14 .cfa: sp 0 + .ra: x30
STACK CFI 11314 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 11360 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^
STACK CFI 11f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 11f30 103c .cfa: sp 0 + .ra: x30
STACK CFI 11f34 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 11f64 x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 12f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 12f70 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fb0 4c .cfa: sp 0 + .ra: x30
STACK CFI 12fb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12ff8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13000 288 .cfa: sp 0 + .ra: x30
STACK CFI 13004 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 1300c x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 1301c x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 13028 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 13030 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 13280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13284 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x29: .cfa -480 + ^
STACK CFI INIT 13290 458 .cfa: sp 0 + .ra: x30
STACK CFI 13294 .cfa: sp 832 +
STACK CFI 132a0 .ra: .cfa -824 + ^ x29: .cfa -832 + ^
STACK CFI 132ac x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 132c0 x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 132d0 x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI 132d8 x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 136e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 136e4 .cfa: sp 832 + .ra: .cfa -824 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^ x29: .cfa -832 + ^
STACK CFI INIT 136f0 39c .cfa: sp 0 + .ra: x30
STACK CFI 136f4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 136fc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 1370c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 13718 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 13720 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 13a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 13a90 140 .cfa: sp 0 + .ra: x30
STACK CFI 13a94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 13a9c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13aa8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 13ab4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 13bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 13bd0 304 .cfa: sp 0 + .ra: x30
STACK CFI 13bd4 .cfa: sp 560 +
STACK CFI 13bdc .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 13be4 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 13bf0 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 13c00 x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 13c08 x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI 13c14 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 13ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13ed0 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI INIT 13ee0 774 .cfa: sp 0 + .ra: x30
STACK CFI 13ee4 .cfa: sp 1024 +
STACK CFI 13ee8 .ra: .cfa -1016 + ^ x29: .cfa -1024 + ^
STACK CFI 13ef0 x21: .cfa -992 + ^ x22: .cfa -984 + ^
STACK CFI 13efc x19: .cfa -1008 + ^ x20: .cfa -1000 + ^
STACK CFI 13f14 x23: .cfa -976 + ^ x24: .cfa -968 + ^ x25: .cfa -960 + ^ x26: .cfa -952 + ^
STACK CFI 13f20 x27: .cfa -944 + ^ x28: .cfa -936 + ^
STACK CFI 14648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1464c .cfa: sp 1024 + .ra: .cfa -1016 + ^ x19: .cfa -1008 + ^ x20: .cfa -1000 + ^ x21: .cfa -992 + ^ x22: .cfa -984 + ^ x23: .cfa -976 + ^ x24: .cfa -968 + ^ x25: .cfa -960 + ^ x26: .cfa -952 + ^ x27: .cfa -944 + ^ x28: .cfa -936 + ^ x29: .cfa -1024 + ^
STACK CFI INIT 14660 278 .cfa: sp 0 + .ra: x30
STACK CFI 14664 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 14670 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 1467c x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 14688 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 14694 x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 146a0 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 148a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 148ac .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 148e0 264 .cfa: sp 0 + .ra: x30
STACK CFI 148e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 148f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 148fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14908 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 14b50 278 .cfa: sp 0 + .ra: x30
STACK CFI 14b54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14b60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14b6c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14b78 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 14dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 14dd0 e4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14ec0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 14ec4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 14ecc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 14edc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 14ee4 x23: .cfa -176 + ^
STACK CFI 14f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 14f74 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 14f80 148 .cfa: sp 0 + .ra: x30
STACK CFI 14f84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 14f8c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 14f98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 14fa4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 150c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 150d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 150d4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 150dc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 150ec x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 150f4 x23: .cfa -176 + ^
STACK CFI 15180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 15184 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 15190 85c .cfa: sp 0 + .ra: x30
STACK CFI 15194 .cfa: sp 2480 +
STACK CFI 151a0 .ra: .cfa -2472 + ^ x29: .cfa -2480 + ^
STACK CFI 151b0 x19: .cfa -2464 + ^ x20: .cfa -2456 + ^
STACK CFI 151c0 x21: .cfa -2448 + ^ x22: .cfa -2440 + ^
STACK CFI 151cc x23: .cfa -2432 + ^ x24: .cfa -2424 + ^
STACK CFI 151d4 x25: .cfa -2416 + ^ x26: .cfa -2408 + ^
STACK CFI 151e0 x27: .cfa -2400 + ^ x28: .cfa -2392 + ^
STACK CFI 159e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 159e8 .cfa: sp 2480 + .ra: .cfa -2472 + ^ x19: .cfa -2464 + ^ x20: .cfa -2456 + ^ x21: .cfa -2448 + ^ x22: .cfa -2440 + ^ x23: .cfa -2432 + ^ x24: .cfa -2424 + ^ x25: .cfa -2416 + ^ x26: .cfa -2408 + ^ x27: .cfa -2400 + ^ x28: .cfa -2392 + ^ x29: .cfa -2480 + ^
STACK CFI INIT 159f0 7d0 .cfa: sp 0 + .ra: x30
STACK CFI 159f8 .cfa: sp 4208 +
STACK CFI 15a00 .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI 15a08 x19: .cfa -4192 + ^ x20: .cfa -4184 + ^
STACK CFI 15a24 x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI 15a2c x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 15a38 x27: .cfa -4128 + ^ x28: .cfa -4120 + ^
STACK CFI 161b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 161bc .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x25: .cfa -4144 + ^ x26: .cfa -4136 + ^ x27: .cfa -4128 + ^ x28: .cfa -4120 + ^ x29: .cfa -4208 + ^
STACK CFI INIT 161c0 3e0 .cfa: sp 0 + .ra: x30
STACK CFI 161c4 .cfa: sp 640 +
STACK CFI 161dc .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 161e8 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 161fc x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 16598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1659c .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^ x29: .cfa -640 + ^
STACK CFI INIT 165a0 130 .cfa: sp 0 + .ra: x30
STACK CFI 165a4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 165b0 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 165c0 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 165c8 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 165d4 x25: .cfa -304 + ^
STACK CFI 166c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 166cc .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x29: .cfa -368 + ^
STACK CFI INIT 166d0 91c .cfa: sp 0 + .ra: x30
STACK CFI 166d4 .cfa: sp 2272 +
STACK CFI 166e8 .ra: .cfa -2264 + ^ x29: .cfa -2272 + ^
STACK CFI 166f4 x19: .cfa -2256 + ^ x20: .cfa -2248 + ^
STACK CFI 16708 x21: .cfa -2240 + ^ x22: .cfa -2232 + ^ x23: .cfa -2224 + ^ x24: .cfa -2216 + ^
STACK CFI 16720 x25: .cfa -2208 + ^ x26: .cfa -2200 + ^ x27: .cfa -2192 + ^ x28: .cfa -2184 + ^
STACK CFI 16f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16f80 .cfa: sp 2272 + .ra: .cfa -2264 + ^ x19: .cfa -2256 + ^ x20: .cfa -2248 + ^ x21: .cfa -2240 + ^ x22: .cfa -2232 + ^ x23: .cfa -2224 + ^ x24: .cfa -2216 + ^ x25: .cfa -2208 + ^ x26: .cfa -2200 + ^ x27: .cfa -2192 + ^ x28: .cfa -2184 + ^ x29: .cfa -2272 + ^
STACK CFI INIT 16ff0 e4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 170e0 434 .cfa: sp 0 + .ra: x30
STACK CFI 170e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 17138 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 17510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 17520 1008 .cfa: sp 0 + .ra: x30
STACK CFI 17524 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1753c x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 18524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 18530 1248 .cfa: sp 0 + .ra: x30
STACK CFI 18534 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1854c x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 19774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 19780 560 .cfa: sp 0 + .ra: x30
STACK CFI 19784 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 19790 x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 197a0 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 197ac x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 197b8 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 197c0 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 19cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19cdc .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^ x29: .cfa -384 + ^
STACK CFI INIT 19ce0 784 .cfa: sp 0 + .ra: x30
STACK CFI 19ce4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 19d30 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19d48 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 19dbc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1a460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 1a470 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a4d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 1a4d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a550 130 .cfa: sp 0 + .ra: x30
STACK CFI 1a554 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1a564 x19: .cfa -144 + ^
STACK CFI 1a678 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a67c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1a680 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 1a684 .cfa: sp 656 +
STACK CFI 1a690 .ra: .cfa -648 + ^ x29: .cfa -656 + ^
STACK CFI 1a6a0 x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 1a6a8 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 1a78c x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 1a798 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 1a7a4 x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 1aa14 x21: x21 x22: x22
STACK CFI 1aa1c x25: x25 x26: x26
STACK CFI 1aa24 x27: x27 x28: x28
STACK CFI 1aa4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1aa50 .cfa: sp 656 + .ra: .cfa -648 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x29: .cfa -656 + ^
STACK CFI 1aa5c x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 1aa60 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 1aa64 x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI INIT 1aa70 408 .cfa: sp 0 + .ra: x30
STACK CFI 1aa74 .cfa: sp 992 +
STACK CFI 1aa7c .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI 1aa84 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI 1aa94 x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI 1aaa0 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI 1aaa8 x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI 1aab4 x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI 1ae70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ae74 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^ x29: .cfa -992 + ^
STACK CFI INIT 1ae80 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 1ae84 .cfa: sp 976 +
STACK CFI 1ae90 .ra: .cfa -968 + ^ x29: .cfa -976 + ^
STACK CFI 1ae98 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI 1aea4 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 1aeb0 x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 1aec0 x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 1aec8 x27: .cfa -896 + ^
STACK CFI 1b024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 1b030 210 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b280 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 1b284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b28c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b3f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b3fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b430 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b460 264 .cfa: sp 0 + .ra: x30
STACK CFI 1b464 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b470 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1b688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1b6d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b6d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b6e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b6f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b720 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b730 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b734 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b748 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b790 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b794 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b7a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b7b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b850 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b860 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b890 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b960 17a4 .cfa: sp 0 + .ra: x30
STACK CFI 1b964 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1b9b0 x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 1d100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 1d110 134 .cfa: sp 0 + .ra: x30
STACK CFI 1d114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d11c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d1b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d1bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1d240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d250 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1d254 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d264 x19: .cfa -80 + ^
STACK CFI 1d2f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d2f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d300 ec .cfa: sp 0 + .ra: x30
STACK CFI 1d304 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d314 x19: .cfa -80 + ^
STACK CFI 1d3d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d3d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d3f0 118 .cfa: sp 0 + .ra: x30
STACK CFI 1d3f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1d400 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1d408 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1d414 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d420 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1d4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1d510 154 .cfa: sp 0 + .ra: x30
STACK CFI 1d514 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1d524 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1d534 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 1d53c x23: .cfa -224 + ^
STACK CFI 1d658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1d65c .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x29: .cfa -272 + ^
STACK CFI INIT 1d670 190 .cfa: sp 0 + .ra: x30
STACK CFI 1d674 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1d684 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1d69c x19: .cfa -256 + ^ x20: .cfa -248 + ^ x23: .cfa -224 + ^
STACK CFI 1d7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1d7e8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x29: .cfa -272 + ^
STACK CFI INIT 1d800 16c .cfa: sp 0 + .ra: x30
STACK CFI 1d804 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1d80c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1d818 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1d820 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1d850 x25: .cfa -96 + ^
STACK CFI 1d91c x25: x25
STACK CFI 1d944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1d948 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 1d954 x25: x25
STACK CFI 1d960 x25: .cfa -96 + ^
STACK CFI 1d964 x25: x25
STACK CFI 1d968 x25: .cfa -96 + ^
STACK CFI INIT 1d970 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1d974 .cfa: sp 576 +
STACK CFI 1d978 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 1d980 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1d988 x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 1d9ac x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI 1da1c x25: .cfa -512 + ^
STACK CFI 1daf8 x25: x25
STACK CFI 1db24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1db28 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x29: .cfa -576 + ^
STACK CFI 1db34 x25: .cfa -512 + ^
STACK CFI 1db38 x25: x25
STACK CFI 1db3c x25: .cfa -512 + ^
STACK CFI INIT 1db40 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 1db44 .cfa: sp 592 +
STACK CFI 1db48 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 1db50 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 1db58 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 1db80 x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 1dbf8 x25: .cfa -528 + ^
STACK CFI 1dcd4 x25: x25
STACK CFI 1dd00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1dd04 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x29: .cfa -592 + ^
STACK CFI 1dd24 x25: .cfa -528 + ^
STACK CFI 1dd28 x25: x25
STACK CFI 1dd2c x25: .cfa -528 + ^
STACK CFI INIT 1dd30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dd50 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dd90 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ddd0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1ddf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1de18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1de1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1de2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1de40 74 .cfa: sp 0 + .ra: x30
STACK CFI 1de60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1de8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1de90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1dea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1dec0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ded0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dee0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1def0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df40 ab4 .cfa: sp 0 + .ra: x30
STACK CFI 1df44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1df64 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1e89c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1e8a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1e900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1e930 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1ea00 670 .cfa: sp 0 + .ra: x30
STACK CFI 1ea04 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1ea0c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1ea18 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 1ea20 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 1eccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ecd0 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x29: .cfa -352 + ^
STACK CFI INIT 1f070 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f0a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1f0a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f0b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f0d0 250 .cfa: sp 0 + .ra: x30
STACK CFI 1f0d4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1f0dc x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1f0ec x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^
STACK CFI 1f284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1f288 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x29: .cfa -352 + ^
STACK CFI INIT 1f320 88 .cfa: sp 0 + .ra: x30
STACK CFI 1f324 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1f32c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1f33c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1f344 x23: .cfa -128 + ^
STACK CFI 1f3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1f3a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1f3b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f3c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f3d0 b0c .cfa: sp 0 + .ra: x30
STACK CFI 1f3d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f3f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1fd70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1fd90 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1fde4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1fe18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1fee0 748 .cfa: sp 0 + .ra: x30
STACK CFI 1fee4 .cfa: sp 768 +
STACK CFI 1feec .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 1fef4 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 1ff04 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 20248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2024c .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x29: .cfa -768 + ^
STACK CFI INIT 20630 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20670 24 .cfa: sp 0 + .ra: x30
STACK CFI 20678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20688 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 206a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 206a4 .cfa: sp 752 +
STACK CFI 206a8 .ra: .cfa -744 + ^ x29: .cfa -752 + ^
STACK CFI 206b0 x19: .cfa -736 + ^ x20: .cfa -728 + ^
STACK CFI 206bc x21: .cfa -720 + ^ x22: .cfa -712 + ^
STACK CFI 207dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 207e0 .cfa: sp 752 + .ra: .cfa -744 + ^ x19: .cfa -736 + ^ x20: .cfa -728 + ^ x21: .cfa -720 + ^ x22: .cfa -712 + ^ x29: .cfa -752 + ^
STACK CFI INIT 20820 88 .cfa: sp 0 + .ra: x30
STACK CFI 20824 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 2082c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 2083c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 20844 x23: .cfa -240 + ^
STACK CFI 208a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 208a4 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x29: .cfa -288 + ^
STACK CFI INIT 208b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 208f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 208fc x19: .cfa -64 + ^
STACK CFI 20960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 20964 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 20980 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209f0 40 .cfa: sp 0 + .ra: x30
STACK CFI 209f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20a08 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20a30 34 .cfa: sp 0 + .ra: x30
STACK CFI 20a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20a3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20a70 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 20a74 .cfa: sp 640 +
STACK CFI 20a7c .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 20a84 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 20a90 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 20a9c x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 20aa4 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI 20aac x27: .cfa -560 + ^
STACK CFI 20bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 20bd8 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x29: .cfa -640 + ^
STACK CFI INIT 20c30 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 20c34 .cfa: sp 640 +
STACK CFI 20c3c .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 20c44 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 20c50 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 20c5c x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 20c64 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI 20c6c x27: .cfa -560 + ^
STACK CFI 20d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 20d98 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x29: .cfa -640 + ^
STACK CFI INIT 20df0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e30 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ec0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ed0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ee0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20fa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20fb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20fc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20fe0 12c .cfa: sp 0 + .ra: x30
STACK CFI 20fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21100 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21110 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21160 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21270 140 .cfa: sp 0 + .ra: x30
STACK CFI 21274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2127c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 213ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 213b0 18 .cfa: sp 0 + .ra: x30
STACK CFI 213b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 213c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 213d0 104 .cfa: sp 0 + .ra: x30
STACK CFI 213d4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 213e4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 213f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 213fc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2148c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 21490 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI INIT 214e0 308 .cfa: sp 0 + .ra: x30
STACK CFI 214e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 214ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2155c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21560 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 215c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 215cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 217f0 110 .cfa: sp 0 + .ra: x30
STACK CFI 217f4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 21804 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 21814 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2181c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 218b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 218bc .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI INIT 21900 20c .cfa: sp 0 + .ra: x30
STACK CFI 21904 .cfa: sp 528 +
STACK CFI 2190c .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 21914 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 21920 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 21930 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 21adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 21ae0 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x29: .cfa -528 + ^
STACK CFI INIT 21b10 80 .cfa: sp 0 + .ra: x30
STACK CFI 21b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21b1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 21b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 21b90 190 .cfa: sp 0 + .ra: x30
STACK CFI 21b94 .cfa: sp 2128 +
STACK CFI 21b98 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 21ba0 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 21bb0 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 21bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 21bf4 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x29: .cfa -2128 + ^
STACK CFI 21bf8 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^
STACK CFI 21cdc x23: x23 x24: x24
STACK CFI 21ce0 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^
STACK CFI 21d18 x23: x23 x24: x24
STACK CFI 21d1c x23: .cfa -2080 + ^ x24: .cfa -2072 + ^
STACK CFI INIT 21d20 b0 .cfa: sp 0 + .ra: x30
STACK CFI 21d28 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 21d30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21d44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 21d4c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 21d9c x19: x19 x20: x20
STACK CFI 21da0 x23: x23 x24: x24
STACK CFI 21da8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 21dac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 21dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 21dd0 118 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21ef0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 21ef4 .cfa: sp 1120 +
STACK CFI 21ef8 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 21f00 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 21f0c x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 21f14 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 21f38 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 22014 x19: x19 x20: x20
STACK CFI 22048 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2204c .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x29: .cfa -1120 + ^
STACK CFI 220ac x19: x19 x20: x20
STACK CFI 220b0 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI INIT 220c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 220e0 168 .cfa: sp 0 + .ra: x30
STACK CFI 220e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 220ec x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 220f8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 22130 x23: .cfa -96 + ^
STACK CFI 221e4 x23: x23
STACK CFI 22208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2220c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 2222c x23: x23
STACK CFI 22244 x23: .cfa -96 + ^
STACK CFI INIT 22250 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22270 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22290 50c .cfa: sp 0 + .ra: x30
STACK CFI 22294 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 222a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 222a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 22300 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 22304 x25: .cfa -32 + ^
STACK CFI 22374 x23: x23 x24: x24
STACK CFI 22378 x25: x25
STACK CFI 22388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2238c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 223ac x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 22704 x23: x23 x24: x24
STACK CFI 22708 x25: x25
STACK CFI 22718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2271c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 22728 x23: x23 x24: x24
STACK CFI 2272c x25: x25
STACK CFI 22730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22734 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 2277c x23: x23 x24: x24
STACK CFI 22780 x25: x25
STACK CFI 22784 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 2278c x23: x23 x24: x24
STACK CFI 22790 x25: x25
STACK CFI INIT 227a0 7c8 .cfa: sp 0 + .ra: x30
STACK CFI 227a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 227b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 227bc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 227d0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 22820 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 22868 x27: .cfa -64 + ^
STACK CFI 22dec x23: x23 x24: x24
STACK CFI 22df0 x27: x27
STACK CFI 22e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 22e20 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 22e4c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^
STACK CFI 22e54 x27: x27
STACK CFI 22e58 x23: x23 x24: x24
STACK CFI 22e60 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^
STACK CFI 22ec0 x23: x23 x24: x24
STACK CFI 22ec4 x27: x27
STACK CFI 22ec8 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^
STACK CFI 22ef0 x23: x23 x24: x24
STACK CFI 22ef4 x27: x27
STACK CFI 22ef8 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^
STACK CFI 22f50 x27: x27
STACK CFI 22f58 x23: x23 x24: x24
STACK CFI 22f60 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 22f64 x27: .cfa -64 + ^
STACK CFI INIT 22f70 68c .cfa: sp 0 + .ra: x30
STACK CFI 22f74 .cfa: sp 2128 +
STACK CFI 22f78 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 22f80 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 22f90 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 22fa4 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x25: .cfa -2064 + ^ x26: .cfa -2056 + ^
STACK CFI 235f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 23600 6bc .cfa: sp 0 + .ra: x30
STACK CFI 23604 .cfa: sp 2128 +
STACK CFI 23608 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 23610 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 23620 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 23634 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x25: .cfa -2064 + ^ x26: .cfa -2056 + ^
STACK CFI 23cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 23cc0 158 .cfa: sp 0 + .ra: x30
STACK CFI 23cc8 .cfa: sp 4176 +
STACK CFI 23ccc .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 23cd4 x23: .cfa -4128 + ^ x24: .cfa -4120 + ^
STACK CFI 23cdc x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 23cf0 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 23cfc x25: .cfa -4112 + ^
STACK CFI 23d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 23d94 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x25: .cfa -4112 + ^ x29: .cfa -4176 + ^
STACK CFI 23e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 23e20 274 .cfa: sp 0 + .ra: x30
STACK CFI 23e24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23e34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23e38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23e3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24004 x19: x19 x20: x20
STACK CFI 24008 x21: x21 x22: x22
STACK CFI 2400c x23: x23 x24: x24
STACK CFI 24010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24014 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 240a0 dc .cfa: sp 0 + .ra: x30
STACK CFI 240a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 240ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 240b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 24170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24174 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 24180 16c .cfa: sp 0 + .ra: x30
STACK CFI 24184 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 24194 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 241cc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 241d4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 24278 x25: x25 x26: x26
STACK CFI 2427c x27: x27 x28: x28
STACK CFI 24290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24294 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 242c4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 242dc x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 242e4 x25: x25 x26: x26
STACK CFI 242e8 x27: x27 x28: x28
STACK CFI INIT 242f0 15c .cfa: sp 0 + .ra: x30
STACK CFI 242f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 24300 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 24314 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 24334 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 24340 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 243cc x19: x19 x20: x20
STACK CFI 243d0 x25: x25 x26: x26
STACK CFI 243d4 x27: x27 x28: x28
STACK CFI 243e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 243e8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 24418 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 24420 x19: x19 x20: x20
STACK CFI 2442c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 24434 x19: x19 x20: x20
STACK CFI 24438 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 24440 x19: x19 x20: x20
STACK CFI 24444 x25: x25 x26: x26
STACK CFI 24448 x27: x27 x28: x28
STACK CFI INIT 24450 120 .cfa: sp 0 + .ra: x30
STACK CFI 24454 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 24464 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 24498 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 244a8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2452c x25: x25 x26: x26
STACK CFI 24530 x27: x27 x28: x28
STACK CFI 24544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24548 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 24560 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 24568 x25: x25 x26: x26
STACK CFI 2456c x27: x27 x28: x28
STACK CFI INIT 24570 15c .cfa: sp 0 + .ra: x30
STACK CFI 24574 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 24580 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 24594 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 245b4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 245c0 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2464c x19: x19 x20: x20
STACK CFI 24650 x25: x25 x26: x26
STACK CFI 24654 x27: x27 x28: x28
STACK CFI 24664 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24668 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 24698 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 246a0 x19: x19 x20: x20
STACK CFI 246ac x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 246b4 x19: x19 x20: x20
STACK CFI 246b8 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 246c0 x19: x19 x20: x20
STACK CFI 246c4 x25: x25 x26: x26
STACK CFI 246c8 x27: x27 x28: x28
STACK CFI INIT 246d0 120 .cfa: sp 0 + .ra: x30
STACK CFI 246d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 246e4 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 24718 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 24728 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 247ac x25: x25 x26: x26
STACK CFI 247b0 x27: x27 x28: x28
STACK CFI 247c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 247c8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 247e0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 247e8 x25: x25 x26: x26
STACK CFI 247ec x27: x27 x28: x28
STACK CFI INIT 247f0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 247f4 .cfa: sp 224 +
STACK CFI 247f8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 24804 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2480c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 24818 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2484c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 24920 x27: x27 x28: x28
STACK CFI 2493c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24940 .cfa: sp 224 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 24998 x27: x27 x28: x28
STACK CFI 2499c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 249a0 .cfa: sp 224 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 249a8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 249c8 x27: x27 x28: x28
STACK CFI INIT 249d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 249e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 249f0 280 .cfa: sp 0 + .ra: x30
STACK CFI 249f4 .cfa: sp 672 +
STACK CFI 249f8 .ra: .cfa -664 + ^ x29: .cfa -672 + ^
STACK CFI 24a00 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 24a08 x25: .cfa -608 + ^
STACK CFI 24a14 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 24a3c x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 24ab4 x23: x23 x24: x24
STACK CFI 24ab8 x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 24bbc x23: x23 x24: x24
STACK CFI 24bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 24bf8 .cfa: sp 672 + .ra: .cfa -664 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x29: .cfa -672 + ^
STACK CFI 24c58 x23: x23 x24: x24
STACK CFI 24c6c x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI INIT 24c70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24ca0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24ce0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d90 104 .cfa: sp 0 + .ra: x30
STACK CFI 24d94 .cfa: sp 96 +
STACK CFI 24d98 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 24da0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 24dac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24db8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24dcc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 24e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24e64 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 24ea0 138 .cfa: sp 0 + .ra: x30
STACK CFI 24ea4 .cfa: sp 128 +
STACK CFI 24ea8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 24eb0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 24eb8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 24ec8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 24f2c x25: .cfa -48 + ^
STACK CFI 24f70 x25: x25
STACK CFI 24f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24fa0 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 24fd4 x25: .cfa -48 + ^
STACK CFI INIT 24fe0 64 .cfa: sp 0 + .ra: x30
STACK CFI 24fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2500c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25010 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25028 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2502c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 25050 130 .cfa: sp 0 + .ra: x30
STACK CFI 25054 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2505c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 25064 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2506c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 250b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 250b8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 25160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 25164 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 25180 130 .cfa: sp 0 + .ra: x30
STACK CFI 25184 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2518c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 25194 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2519c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 251e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 251e8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 25290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 25294 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 252b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25300 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25320 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25330 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25360 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 253a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 253b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 253c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 253d0 104 .cfa: sp 0 + .ra: x30
STACK CFI 253d4 .cfa: sp 96 +
STACK CFI 253d8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 253e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 253ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 253f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2540c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 254a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 254a4 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 254e0 138 .cfa: sp 0 + .ra: x30
STACK CFI 254e4 .cfa: sp 128 +
STACK CFI 254e8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 254f0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 254f8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 25508 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2556c x25: .cfa -48 + ^
STACK CFI 255b0 x25: x25
STACK CFI 255dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 255e0 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 25614 x25: .cfa -48 + ^
STACK CFI INIT 25620 64 .cfa: sp 0 + .ra: x30
STACK CFI 25624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2564c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25650 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2566c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 25690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 256f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25720 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25730 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25740 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 257a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 257b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 257c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 257d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 257e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25804 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 25810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25820 24 .cfa: sp 0 + .ra: x30
STACK CFI 2583c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 25850 a4 .cfa: sp 0 + .ra: x30
STACK CFI 25854 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2585c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25870 x21: .cfa -16 + ^
STACK CFI 258b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 258b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 258cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 258d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 258f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 25900 a4 .cfa: sp 0 + .ra: x30
STACK CFI 25904 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2590c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25920 x21: .cfa -16 + ^
STACK CFI 25960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2597c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25980 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 259a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 259b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 259c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 259d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 259e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 259f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25a00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25a10 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25aa0 234 .cfa: sp 0 + .ra: x30
STACK CFI 25aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25ab8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 25cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 25ce0 190 .cfa: sp 0 + .ra: x30
STACK CFI 25ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25cf8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 25e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 25e70 f8 .cfa: sp 0 + .ra: x30
STACK CFI 25e74 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 25e80 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 25e8c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 25f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 25f4c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT 25f70 560 .cfa: sp 0 + .ra: x30
STACK CFI 25f74 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 25f94 x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 264b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 264b4 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 264d0 46c .cfa: sp 0 + .ra: x30
STACK CFI 264d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 26524 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 26660 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 266c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26700 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2675c x27: .cfa -16 + ^
STACK CFI 26918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2691c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 26938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 26940 74 .cfa: sp 0 + .ra: x30
STACK CFI 26944 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26954 x19: .cfa -32 + ^
STACK CFI 269a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 269ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 269c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 269e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 269f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a00 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26aa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26ab0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26ac0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 26ac4 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 26acc x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 26ad8 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 26ae8 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 26af4 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 26afc x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 26c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 26c54 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT 26cc0 34 .cfa: sp 0 + .ra: x30
STACK CFI 26cec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 26d00 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 26d04 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 26d0c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 26d1c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 26d24 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 26d3c x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 26e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 26e6c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 26ed0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f00 50 .cfa: sp 0 + .ra: x30
STACK CFI 26f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26f14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26f50 e0 .cfa: sp 0 + .ra: x30
STACK CFI 26f54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 26f5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 26f64 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 26f80 x25: .cfa -64 + ^
STACK CFI 26f8c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 26ff0 x23: x23 x24: x24
STACK CFI 27018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 2701c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 27020 x23: x23 x24: x24
STACK CFI 2702c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 27030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27050 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27060 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27090 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 270a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 270b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 270b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 270bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 270d0 x21: .cfa -16 + ^
STACK CFI 27118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 27120 48 .cfa: sp 0 + .ra: x30
STACK CFI 27124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27130 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 27170 9c .cfa: sp 0 + .ra: x30
STACK CFI 27174 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27184 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 27204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27208 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 27210 2fc .cfa: sp 0 + .ra: x30
STACK CFI 27214 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 2721c x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 2722c x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 27240 x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 2724c x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 27460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 27464 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT 27510 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 27514 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 27528 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 27530 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 27538 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 27548 x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 27770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 27774 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT 277c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 277d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 277e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 277f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27860 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27870 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 27880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 278a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 278b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 278c0 270 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 27ba0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27bb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27bc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27bd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27be0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27bf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27ca0 1c .cfa: sp 0 + .ra: x30
STACK CFI 27ca4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27cb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27cc0 20 .cfa: sp 0 + .ra: x30
STACK CFI 27cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27cdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27ce0 18 .cfa: sp 0 + .ra: x30
STACK CFI 27ce4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27d00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27d10 84 .cfa: sp 0 + .ra: x30
STACK CFI 27d14 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 27d1c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 27d2c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 27d34 x23: .cfa -96 + ^
STACK CFI 27d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 27d90 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 27da0 74 .cfa: sp 0 + .ra: x30
STACK CFI 27da4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 27dac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 27dbc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 27e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27e10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 27e20 98 .cfa: sp 0 + .ra: x30
STACK CFI 27e24 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 27e2c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 27e40 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 27eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 27ec0 84 .cfa: sp 0 + .ra: x30
STACK CFI 27ec4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 27ecc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27edc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27f40 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 27f50 304 .cfa: sp 0 + .ra: x30
STACK CFI 27f54 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 27f5c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 27f64 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 28000 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 28008 x25: .cfa -256 + ^
STACK CFI 28234 x23: x23 x24: x24
STACK CFI 28238 x25: x25
STACK CFI 28248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2824c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT 28260 98 .cfa: sp 0 + .ra: x30
STACK CFI 28264 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 28270 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 28280 x21: .cfa -96 + ^
STACK CFI 282f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 282f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 28300 180 .cfa: sp 0 + .ra: x30
STACK CFI 28304 .cfa: sp 688 +
STACK CFI 28308 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 28310 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 28320 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 28328 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 28338 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 2837c x27: .cfa -608 + ^
STACK CFI 28438 x27: x27
STACK CFI 28464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 28468 .cfa: sp 688 + .ra: .cfa -680 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x29: .cfa -688 + ^
STACK CFI 2846c x27: x27
STACK CFI 2847c x27: .cfa -608 + ^
STACK CFI INIT 28480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28490 d8 .cfa: sp 0 + .ra: x30
STACK CFI 28494 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 284a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 284a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 284c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 284cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 284d4 x23: .cfa -16 + ^
STACK CFI 28524 x23: x23
STACK CFI 28528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2852c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2853c x23: x23
STACK CFI 28540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28544 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2855c x23: x23
STACK CFI 28560 x23: .cfa -16 + ^
STACK CFI 28564 x23: x23
STACK CFI INIT 28570 44 .cfa: sp 0 + .ra: x30
STACK CFI 28574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2857c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 28594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 285ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 285c0 280 .cfa: sp 0 + .ra: x30
STACK CFI 285c4 .cfa: sp 688 +
STACK CFI 285c8 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 285d0 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 285dc x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 285ec x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 285f4 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 28600 x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI 28774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 28778 .cfa: sp 688 + .ra: .cfa -680 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^ x29: .cfa -688 + ^
STACK CFI INIT 28840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28850 98 .cfa: sp 0 + .ra: x30
STACK CFI 28854 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 28860 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28870 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 288c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 288c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 288f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28950 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28970 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28990 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 289b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 289d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 289f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 28a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28a20 34 .cfa: sp 0 + .ra: x30
STACK CFI 28a4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28a60 34 .cfa: sp 0 + .ra: x30
STACK CFI 28a8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28aa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ab0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ac0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ae0 8b4 .cfa: sp 0 + .ra: x30
STACK CFI 28ae4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 28af4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 28b00 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 28b1c x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2931c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 29320 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 293a0 74 .cfa: sp 0 + .ra: x30
STACK CFI 293b0 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 293bc x19: .cfa -80 + ^
STACK CFI 29410 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29420 6c .cfa: sp 0 + .ra: x30
STACK CFI 29430 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2943c x19: .cfa -80 + ^
STACK CFI 29488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29490 90 .cfa: sp 0 + .ra: x30
STACK CFI 29494 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 294a8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 294b0 x21: .cfa -96 + ^
STACK CFI 29510 x19: x19 x20: x20
STACK CFI 29514 x21: x21
STACK CFI 2951c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29520 8c .cfa: sp 0 + .ra: x30
STACK CFI 29530 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2953c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2954c x21: .cfa -80 + ^
STACK CFI 295a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 295b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 295c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 295d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 295e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 295f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29600 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29620 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29650 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29670 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29690 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 296b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 296c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 296e0 9c .cfa: sp 0 + .ra: x30
STACK CFI 296e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 296ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 296fc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 29704 x23: .cfa -64 + ^
STACK CFI 29774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 29778 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 29780 b8 .cfa: sp 0 + .ra: x30
STACK CFI 29784 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2978c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2979c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 297a8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 297b0 x25: .cfa -80 + ^
STACK CFI 29830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 29834 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 29840 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29860 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 298a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 298b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 298c0 268 .cfa: sp 0 + .ra: x30
STACK CFI 298c4 .cfa: sp 32 +
STACK CFI 29b18 .cfa: sp 0 +
STACK CFI INIT 29b30 60 .cfa: sp 0 + .ra: x30
STACK CFI 29b34 .cfa: sp 32 +
STACK CFI 29b80 .cfa: sp 0 +
STACK CFI INIT 29b90 60 .cfa: sp 0 + .ra: x30
STACK CFI 29b94 .cfa: sp 32 +
STACK CFI 29be0 .cfa: sp 0 +
STACK CFI INIT 29bf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29c10 54 .cfa: sp 0 + .ra: x30
STACK CFI 29c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29c1c x19: .cfa -16 + ^
STACK CFI 29c3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29c48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 29c70 54 .cfa: sp 0 + .ra: x30
STACK CFI 29c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29c7c x19: .cfa -16 + ^
STACK CFI 29c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29cd0 54 .cfa: sp 0 + .ra: x30
STACK CFI 29cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29cdc x19: .cfa -16 + ^
STACK CFI 29cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29cfc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29d30 88 .cfa: sp 0 + .ra: x30
STACK CFI 29d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 29d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 29d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29da0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 29dc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 29dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29dcc x19: .cfa -32 + ^
STACK CFI 29dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 29dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29e00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29e20 28 .cfa: sp 0 + .ra: x30
STACK CFI 29e40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 29e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29e60 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29e90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29ea0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 2a078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2a080 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 2a084 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2a08c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2a094 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2a09c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2a0a8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2a0c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2a170 x23: x23 x24: x24
STACK CFI 2a190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2a194 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2a1ec x23: x23 x24: x24
STACK CFI 2a1f0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2a218 x23: x23 x24: x24
STACK CFI 2a22c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2a24c x23: x23 x24: x24
STACK CFI INIT 2a250 64 .cfa: sp 0 + .ra: x30
STACK CFI 2a2ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2a2c0 3dc .cfa: sp 0 + .ra: x30
STACK CFI 2a2c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a2d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2a674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a678 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2a6a0 360 .cfa: sp 0 + .ra: x30
STACK CFI 2a6a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2a6ac x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2a6d0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2a6d8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2a6e0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2a718 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a860 x21: x21 x22: x22
STACK CFI 2a884 x19: x19 x20: x20
STACK CFI 2a888 x23: x23 x24: x24
STACK CFI 2a88c x25: x25 x26: x26
STACK CFI 2a890 x27: x27 x28: x28
STACK CFI 2a894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a898 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2a8b0 x21: x21 x22: x22
STACK CFI 2a918 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a934 x21: x21 x22: x22
STACK CFI 2a938 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a988 x21: x21 x22: x22
STACK CFI 2a998 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a9dc x21: x21 x22: x22
STACK CFI 2a9e0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2a9e4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a9e8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2a9ec x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2a9f0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2a9fc x21: x21 x22: x22
STACK CFI INIT 2aa00 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2aa04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aa0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2aa5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aa60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2aaac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2aab0 34 .cfa: sp 0 + .ra: x30
STACK CFI 2aab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aabc x19: .cfa -16 + ^
STACK CFI 2aae0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2aaf0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ab10 4c .cfa: sp 0 + .ra: x30
STACK CFI 2ab14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ab1c x19: .cfa -16 + ^
STACK CFI INIT 2ab60 68 .cfa: sp 0 + .ra: x30
STACK CFI 2ab64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ab6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2abb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2abb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2abc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2abd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2abf0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac30 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ac90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2aca0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2acb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2acc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2acc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2acd0 .cfa: x29 48 +
STACK CFI 2acd4 x19: .cfa -32 + ^
STACK CFI 2ad14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ad18 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ad20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ad30 7c .cfa: sp 0 + .ra: x30
STACK CFI 2ad34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ad3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ad48 x21: .cfa -32 + ^
STACK CFI 2ada8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2adb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2adc0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2adc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2add4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ade0 x21: .cfa -32 + ^
STACK CFI 2ae64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2ae70 44 .cfa: sp 0 + .ra: x30
STACK CFI 2ae74 .cfa: sp 16 +
STACK CFI 2aea4 .cfa: sp 0 +
STACK CFI INIT 2aec0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2aef0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af30 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af70 50 .cfa: sp 0 + .ra: x30
STACK CFI 2af74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2afb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2afb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2afc0 30 .cfa: sp 0 + .ra: x30
STACK CFI 2afc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2afd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2afec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2aff0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2aff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2affc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b030 16c .cfa: sp 0 + .ra: x30
STACK CFI 2b034 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2b040 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2b05c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2b064 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2b130 x21: x21 x22: x22
STACK CFI 2b134 x25: x25 x26: x26
STACK CFI 2b144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2b148 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2b158 x21: x21 x22: x22
STACK CFI 2b160 x25: x25 x26: x26
STACK CFI 2b164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2b168 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2b188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2b18c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2b194 x21: x21 x22: x22
STACK CFI 2b198 x25: x25 x26: x26
STACK CFI INIT 2b1a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2b1c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b1d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b1e0 ac .cfa: sp 0 + .ra: x30
STACK CFI 2b1e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b1f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2b200 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b208 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b27c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2b290 3c .cfa: sp 0 + .ra: x30
STACK CFI 2b2c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b2d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2b304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b310 3c .cfa: sp 0 + .ra: x30
STACK CFI 2b344 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b350 cc .cfa: sp 0 + .ra: x30
STACK CFI 2b358 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b3fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b400 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b418 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2b420 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2b424 .cfa: sp 16 +
STACK CFI 2b4b8 .cfa: sp 0 +
STACK CFI 2b4bc .cfa: sp 16 +
STACK CFI INIT 2b4d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b4e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b4f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b500 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b510 46c .cfa: sp 0 + .ra: x30
STACK CFI 2b514 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 2b530 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2b538 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2b544 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2b550 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2b55c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 2b90c x19: x19 x20: x20
STACK CFI 2b910 x21: x21 x22: x22
STACK CFI 2b914 x25: x25 x26: x26
STACK CFI 2b918 x27: x27 x28: x28
STACK CFI 2b93c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 2b940 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 2b968 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2b96c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2b970 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2b974 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2b978 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 2b980 18c .cfa: sp 0 + .ra: x30
STACK CFI 2b984 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2b98c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2b998 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2b9b0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2ba98 x19: x19 x20: x20
STACK CFI 2babc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2bac0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 2bb04 x19: x19 x20: x20
STACK CFI 2bb08 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI INIT 2bb10 8c .cfa: sp 0 + .ra: x30
STACK CFI 2bb14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2bb1c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2bb2c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2bb94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2bb98 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2bba0 34 .cfa: sp 0 + .ra: x30
STACK CFI 2bba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bbac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2bbd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2bbe0 8c .cfa: sp 0 + .ra: x30
STACK CFI 2bbe4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2bbf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2bc04 x21: .cfa -64 + ^
STACK CFI 2bc5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2bc60 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2bc70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bc80 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2bc84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2bc8c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2bc9c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2bca8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2bcb0 x25: .cfa -64 + ^
STACK CFI 2bd30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2bd34 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2bd40 34 .cfa: sp 0 + .ra: x30
STACK CFI 2bd6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2bd80 38 .cfa: sp 0 + .ra: x30
STACK CFI 2bdb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2bdc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bdd0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2bdd4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2bddc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2bdec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2bdf8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2be00 x25: .cfa -64 + ^
STACK CFI 2be80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2be84 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2be90 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bec0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf60 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2bf64 .cfa: sp 512 +
STACK CFI 2bf6c .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 2bf74 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 2bf8c x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^
STACK CFI 2c00c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c010 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x29: .cfa -512 + ^
STACK CFI INIT 2c020 150 .cfa: sp 0 + .ra: x30
STACK CFI 2c024 .cfa: sp 624 +
STACK CFI 2c028 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 2c030 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 2c044 x19: .cfa -608 + ^ x20: .cfa -600 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 2c050 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 2c05c x27: .cfa -544 + ^
STACK CFI 2c160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2c164 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x29: .cfa -624 + ^
STACK CFI INIT 2c170 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2c174 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c17c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c1a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c1b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c1e0 x21: x21 x22: x22
STACK CFI 2c1e4 x23: x23 x24: x24
STACK CFI 2c200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c204 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 2c210 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c214 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 2c220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c230 68 .cfa: sp 0 + .ra: x30
STACK CFI 2c234 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c23c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2c254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c258 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2c2a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2c2a4 .cfa: sp 864 +
STACK CFI 2c2a8 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2c2b0 x23: .cfa -816 + ^
STACK CFI 2c2b8 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2c2c0 x21: .cfa -832 + ^ x22: .cfa -824 + ^
STACK CFI 2c358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c35c .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x29: .cfa -864 + ^
STACK CFI INIT 2c370 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2c374 .cfa: sp 864 +
STACK CFI 2c378 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2c380 x23: .cfa -816 + ^
STACK CFI 2c388 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2c390 x21: .cfa -832 + ^ x22: .cfa -824 + ^
STACK CFI 2c428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c42c .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x29: .cfa -864 + ^
STACK CFI INIT 2c440 18 .cfa: sp 0 + .ra: x30
STACK CFI 2c444 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c460 18 .cfa: sp 0 + .ra: x30
STACK CFI 2c464 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c474 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c480 68 .cfa: sp 0 + .ra: x30
STACK CFI 2c484 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2c490 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2c4a0 x21: .cfa -96 + ^
STACK CFI 2c4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c4e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2c4f0 58 .cfa: sp 0 + .ra: x30
STACK CFI 2c4f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c4fc x19: .cfa -32 + ^
STACK CFI 2c544 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c550 30 .cfa: sp 0 + .ra: x30
STACK CFI 2c554 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c55c x19: .cfa -16 + ^
STACK CFI 2c57c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c580 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2c584 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c59c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2c5a8 x21: .cfa -160 + ^
STACK CFI 2c62c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c630 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2c640 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2c644 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2c65c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2c668 x21: .cfa -176 + ^
STACK CFI 2c6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c6fc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2c700 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c710 94 .cfa: sp 0 + .ra: x30
STACK CFI 2c714 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2c71c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2c728 x21: .cfa -96 + ^
STACK CFI 2c79c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c7a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2c7b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2c7b4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c7c4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2c7d0 x21: .cfa -160 + ^
STACK CFI 2c84c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c850 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2c860 70 .cfa: sp 0 + .ra: x30
STACK CFI 2c864 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c86c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c87c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c8c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c8cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2c8d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c8e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c8f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c920 24 .cfa: sp 0 + .ra: x30
STACK CFI 2c924 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2c940 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c950 ac .cfa: sp 0 + .ra: x30
STACK CFI 2c954 .cfa: sp 864 +
STACK CFI 2c958 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2c960 x23: .cfa -816 + ^
STACK CFI 2c968 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2c970 x21: .cfa -832 + ^ x22: .cfa -824 + ^
STACK CFI 2c9f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c9f4 .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x29: .cfa -864 + ^
STACK CFI INIT 2ca00 ac .cfa: sp 0 + .ra: x30
STACK CFI 2ca04 .cfa: sp 864 +
STACK CFI 2ca08 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2ca10 x23: .cfa -816 + ^
STACK CFI 2ca18 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2ca20 x21: .cfa -832 + ^ x22: .cfa -824 + ^
STACK CFI 2caa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2caa4 .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x29: .cfa -864 + ^
STACK CFI INIT 2cab0 18 .cfa: sp 0 + .ra: x30
STACK CFI 2cab4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2cac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cad0 68 .cfa: sp 0 + .ra: x30
STACK CFI 2cad4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2cae0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2caf0 x21: .cfa -96 + ^
STACK CFI 2cb30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2cb34 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2cb40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cb90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cba0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbe0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cc00 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2cc04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2cc0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2cc38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2cc44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2cc7c x25: .cfa -16 + ^
STACK CFI 2cd80 x21: x21 x22: x22
STACK CFI 2cd84 x23: x23 x24: x24
STACK CFI 2cd88 x25: x25
STACK CFI 2cd8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cd90 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2cd94 x21: x21 x22: x22
STACK CFI 2cd98 x23: x23 x24: x24
STACK CFI 2cd9c x25: x25
STACK CFI 2cdac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cdb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2cdb4 x25: x25
STACK CFI 2cdc0 x21: x21 x22: x22
STACK CFI 2cdc4 x23: x23 x24: x24
STACK CFI 2cdc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2cdd4 x21: x21 x22: x22
STACK CFI 2cdd8 x23: x23 x24: x24
STACK CFI 2cddc x25: x25
STACK CFI 2cde0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2cde8 x21: x21 x22: x22
STACK CFI 2cdec x23: x23 x24: x24
STACK CFI INIT 2cdf0 220 .cfa: sp 0 + .ra: x30
STACK CFI 2cdf4 .cfa: sp 176 +
STACK CFI 2cdf8 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2ce00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2ce10 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2ce18 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2ce28 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2ce5c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2cf6c x27: x27 x28: x28
STACK CFI 2cf9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2cfa0 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2cfb4 x27: x27 x28: x28
STACK CFI 2cfb8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2cff4 x27: x27 x28: x28
STACK CFI 2cff8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2d008 x27: x27 x28: x28
STACK CFI 2d00c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2d010 218 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d230 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2d234 .cfa: sp 144 +
STACK CFI 2d238 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2d240 x27: .cfa -48 + ^
STACK CFI 2d248 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2d258 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2d264 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2d270 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2d2dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2d2e0 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2d2f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 2d2f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d30c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d344 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d35c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2d360 44 .cfa: sp 0 + .ra: x30
STACK CFI 2d364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d36c x19: .cfa -16 + ^
STACK CFI 2d398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d39c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d3b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d3d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 2d3d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d3dc x19: .cfa -16 + ^
STACK CFI 2d408 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d40c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d420 1ec .cfa: sp 0 + .ra: x30
STACK CFI 2d424 .cfa: sp 640 +
STACK CFI 2d428 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 2d430 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 2d458 x19: .cfa -624 + ^ x20: .cfa -616 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 2d600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d604 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^ x29: .cfa -640 + ^
STACK CFI INIT 2d610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d620 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d650 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d670 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d6f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d700 160 .cfa: sp 0 + .ra: x30
STACK CFI 2d704 .cfa: sp 96 +
STACK CFI 2d708 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d710 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d71c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2d730 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2d73c x25: .cfa -16 + ^
STACK CFI 2d7d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2d7d8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2d860 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2d864 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2d870 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2d878 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2d884 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2d88c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2d998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d99c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2da10 12c .cfa: sp 0 + .ra: x30
STACK CFI 2da14 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2da20 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2da30 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2da84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2da88 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 2da94 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2db14 x23: x23 x24: x24
STACK CFI 2db18 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2db1c x23: x23 x24: x24
STACK CFI 2db20 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2db30 x23: x23 x24: x24
STACK CFI 2db38 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 2db40 114 .cfa: sp 0 + .ra: x30
STACK CFI 2db44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2db54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2dbc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2dbc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2dc60 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 2dc64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2dcc8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^
STACK CFI 2de54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2de60 25c .cfa: sp 0 + .ra: x30
STACK CFI 2de64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2de74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2dec4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e06c x21: x21 x22: x22
STACK CFI 2e074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e078 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e0c0 904 .cfa: sp 0 + .ra: x30
STACK CFI 2e0c4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 2e0cc x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2e0f4 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2e168 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 2e198 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2e1b4 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 2e714 x19: x19 x20: x20
STACK CFI 2e71c x21: x21 x22: x22
STACK CFI 2e720 x23: x23 x24: x24
STACK CFI 2e724 x25: x25 x26: x26
STACK CFI 2e72c .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 2e730 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 2e908 x25: x25 x26: x26
STACK CFI 2e92c x19: x19 x20: x20
STACK CFI 2e930 x21: x21 x22: x22
STACK CFI 2e934 x23: x23 x24: x24
STACK CFI 2e938 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2e950 x21: x21 x22: x22
STACK CFI 2e954 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2e96c x21: x21 x22: x22
STACK CFI 2e970 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2e988 x21: x21 x22: x22
STACK CFI 2e9a4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2e9bc x19: x19 x20: x20
STACK CFI 2e9c0 x21: x21 x22: x22
STACK CFI INIT 2e9d0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2e9d4 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 2e9dc x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 2e9ec x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 2e9f4 x23: .cfa -336 + ^
STACK CFI 2eb68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2eb6c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x29: .cfa -384 + ^
STACK CFI INIT 2ebb0 188 .cfa: sp 0 + .ra: x30
STACK CFI 2ebb4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2ebbc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2ebc8 x21: .cfa -176 + ^
STACK CFI 2ed0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2ed10 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2ed40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eda0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2eda4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 2edac x21: .cfa -336 + ^
STACK CFI 2edb4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 2ee3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2ee40 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x29: .cfa -368 + ^
STACK CFI INIT 2ee70 98 .cfa: sp 0 + .ra: x30
STACK CFI 2ee74 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2ee80 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2eee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2eeec .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2ef10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef30 1fc .cfa: sp 0 + .ra: x30
STACK CFI 2ef34 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 2ef3c x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 2ef48 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 2ef58 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 2ef64 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 2ef6c x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 2f0c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f0c4 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT 2f130 34 .cfa: sp 0 + .ra: x30
STACK CFI 2f15c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2f170 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 2f174 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2f17c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2f18c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2f194 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f1ac x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2f2d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f2dc .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2f340 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f3a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f3b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f3c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f3d0 324 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f700 80 .cfa: sp 0 + .ra: x30
STACK CFI 2f704 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2f70c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2f718 x21: .cfa -176 + ^
STACK CFI 2f77c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2f780 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 2f784 .cfa: sp 848 +
STACK CFI 2f788 .ra: .cfa -840 + ^ x29: .cfa -848 + ^
STACK CFI 2f790 x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 2f7a0 x19: .cfa -832 + ^ x20: .cfa -824 + ^
STACK CFI 2f7a8 x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 2f7b4 x23: .cfa -800 + ^ x24: .cfa -792 + ^
STACK CFI 2f7c0 x25: .cfa -784 + ^ x26: .cfa -776 + ^
STACK CFI 2f92c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f930 .cfa: sp 848 + .ra: .cfa -840 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^ x29: .cfa -848 + ^
STACK CFI INIT 2f940 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 2f944 .cfa: sp 1168 +
STACK CFI 2f948 .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI 2f950 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 2f958 x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI 2f964 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 2f96c x25: .cfa -1104 + ^ x26: .cfa -1096 + ^
STACK CFI 2f9b4 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI 2fac0 x27: x27 x28: x28
STACK CFI 2faf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2faf4 .cfa: sp 1168 + .ra: .cfa -1160 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^ x29: .cfa -1168 + ^
STACK CFI 2faf8 x27: x27 x28: x28
STACK CFI 2fb04 x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI INIT 2fb10 18c .cfa: sp 0 + .ra: x30
STACK CFI 2fb14 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2fb1c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2fb28 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2fb40 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2fc28 x19: x19 x20: x20
STACK CFI 2fc4c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fc50 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 2fc94 x19: x19 x20: x20
STACK CFI 2fc98 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI INIT 2fca0 468 .cfa: sp 0 + .ra: x30
STACK CFI 2fca4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 2fcb4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2fcc8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2fcd0 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2fcdc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2fce8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 3009c x21: x21 x22: x22
STACK CFI 300a0 x25: x25 x26: x26
STACK CFI 300a4 x27: x27 x28: x28
STACK CFI 300cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 300d0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 300f8 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 300fc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 30100 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 30104 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 30110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30120 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30130 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30140 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30150 18c .cfa: sp 0 + .ra: x30
STACK CFI 30154 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3015c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 30168 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 30180 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 30268 x19: x19 x20: x20
STACK CFI 3028c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30290 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 302d4 x19: x19 x20: x20
STACK CFI 302d8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI INIT 302e0 468 .cfa: sp 0 + .ra: x30
STACK CFI 302e4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 302f4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 30308 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 30310 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 3031c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 30328 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 306dc x21: x21 x22: x22
STACK CFI 306e0 x25: x25 x26: x26
STACK CFI 306e4 x27: x27 x28: x28
STACK CFI 3070c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 30710 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 30738 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3073c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 30740 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 30744 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 30750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 307a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 307b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 307c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 307c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 307cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 307dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 307e4 x23: .cfa -64 + ^
STACK CFI 30840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 30844 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 30850 a4 .cfa: sp 0 + .ra: x30
STACK CFI 30854 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3085c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 30870 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 30880 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 30888 x25: .cfa -80 + ^
STACK CFI 308ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 308f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 30900 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30920 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30930 68 .cfa: sp 0 + .ra: x30
STACK CFI 30934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3093c x19: .cfa -16 + ^
STACK CFI 30968 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3096c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30994 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 309a0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 309a4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 309ac x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 309b8 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 309c4 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^
STACK CFI 30a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 30a48 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x29: .cfa -240 + ^
STACK CFI INIT 30b90 40 .cfa: sp 0 + .ra: x30
STACK CFI 30b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30b9c x19: .cfa -16 + ^
STACK CFI 30bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30bcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30bd0 114 .cfa: sp 0 + .ra: x30
STACK CFI 30bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 30bdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30be8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30bf0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 30c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30c6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 30cf0 20 .cfa: sp 0 + .ra: x30
STACK CFI 30cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30d0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30d10 24 .cfa: sp 0 + .ra: x30
STACK CFI 30d14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30d30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30d40 24 .cfa: sp 0 + .ra: x30
STACK CFI 30d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30d70 24 .cfa: sp 0 + .ra: x30
STACK CFI 30d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30d90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30da0 24 .cfa: sp 0 + .ra: x30
STACK CFI 30da4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30dd0 24 .cfa: sp 0 + .ra: x30
STACK CFI 30dd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e00 24 .cfa: sp 0 + .ra: x30
STACK CFI 30e04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30e20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e30 24 .cfa: sp 0 + .ra: x30
STACK CFI 30e34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30e50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e60 24 .cfa: sp 0 + .ra: x30
STACK CFI 30e64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30e80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e90 24 .cfa: sp 0 + .ra: x30
STACK CFI 30e94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30eb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30ec0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30ed0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30ee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30ef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f50 4c .cfa: sp 0 + .ra: x30
STACK CFI 30f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30f6c x19: .cfa -16 + ^
STACK CFI 30f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30fa0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 30fa4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 30fb0 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 30fc0 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 3107c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 31080 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI 31084 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 310cc x25: x25 x26: x26
STACK CFI 310f4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 31214 x25: x25 x26: x26
STACK CFI 3121c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 31240 e4 .cfa: sp 0 + .ra: x30
STACK CFI 31244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3125c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31318 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 31330 c4 .cfa: sp 0 + .ra: x30
STACK CFI 31334 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3133c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 31348 x21: .cfa -32 + ^
STACK CFI 313d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 313dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
