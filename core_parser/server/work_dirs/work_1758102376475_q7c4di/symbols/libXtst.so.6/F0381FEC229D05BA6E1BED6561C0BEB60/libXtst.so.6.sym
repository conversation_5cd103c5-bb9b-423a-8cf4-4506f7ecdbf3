MODULE Linux arm64 F0381FEC229D05BA6E1BED6561C0BEB60 libXtst.so.6
INFO CODE_ID EC1F38F09D22BA056E1BED6561C0BEB65B0026D9
PUBLIC 1388 0 _init
PUBLIC 20e8 0 XRecordIdBaseMask
PUBLIC 20f8 0 XRecordQueryVersion
PUBLIC 2258 0 XRecordCreateContext
PUBLIC 23a8 0 XRecordAllocRange
PUBLIC 23b8 0 XRecordRegisterClients
PUBLIC 2520 0 XRecordUnregisterClients
PUBLIC 2638 0 XRecordFreeState
PUBLIC 26d0 0 XRecordGetContext
PUBLIC 2ab8 0 XRecordFreeData
PUBLIC 2c28 0 XRecordEnableContext
PUBLIC 2e28 0 XRecordEnableContextAsync
PUBLIC 3040 0 XRecordProcessReplies
PUBLIC 3048 0 XRecordDisableContext
PUBLIC 3128 0 XRecordFreeContext
PUBLIC 3490 0 XTestQueryExtension
PUBLIC 3600 0 XTestCompareCursorWithWindow
PUBLIC 3740 0 XTestCompareCurrentCursorWithWindow
PUBLIC 3748 0 XTestFakeKeyEvent
PUBLIC 3850 0 XTestFakeButtonEvent
PUBLIC 3958 0 XTestFakeMotionEvent
PUBLIC 3a88 0 XTestFakeRelativeMotionEvent
PUBLIC 3b90 0 XTestFakeDeviceKeyEvent
PUBLIC 3d08 0 XTestFakeDeviceButtonEvent
PUBLIC 3e80 0 XTestFakeProximityEvent
PUBLIC 3ff0 0 XTestFakeDeviceMotionEvent
PUBLIC 4158 0 XTestGrabControl
PUBLIC 4238 0 XTestSetGContextOfGC
PUBLIC 4240 0 XTestSetVisualIDOfVisual
PUBLIC 4248 0 XTestDiscard
PUBLIC 4304 0 _fini
