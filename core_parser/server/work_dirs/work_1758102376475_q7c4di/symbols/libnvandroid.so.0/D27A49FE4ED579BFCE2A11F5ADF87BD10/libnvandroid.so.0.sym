MODULE Linux arm64 D27A49FE4ED579BFCE2A11F5ADF87BD10 libnvandroid.so.unstripped.0
INFO CODE_ID FE497AD2D54EBF79CE2A11F5ADF87BD1
PUBLIC 5598 0 nvandroid::NvAndroid::Impl::getAllocationAttr(void*, NvFbStreamsMessageGetAllocationAttr_t const*)
PUBLIC 5718 0 nvandroid::NvAndroid::Impl::getSyncAttr(void*, NvFbStreamsMessageGetSyncAttr_t const*)
PUBLIC 5898 0 nvandroid::NvAndroid::Impl::getSyncObj(void*, NvFbStreamsMessageGetSyncObj_t const*)
PUBLIC 5ae0 0 nvandroid::NvAndroid::Impl::registerSyncObj(void*, NvFbStreamsMessageRegisterSyncObj_t const*)
PUBLIC 5d40 0 nvandroid::NvAndroid::Impl::unRegisterBuffer(void*, NvFbStreamsMessageUnRegisterBuffer_t const*)
PUBLIC 5e60 0 nvandroid::NvAndroid::Impl::postBuffer(void*, NvFbStreamsMessagePostBuffer_t const*)
PUBLIC 6050 0 nvandroid::NvImageBuffer::NvImageBuffer(nvandroid::INvImageBuffer const&)
PUBLIC 6068 0 nvandroid::NvAndroid::Impl::registerBuffer(void*, NvFbStreamsMessageRegisterBuffer_t const*)
PUBLIC 6508 0 nvandroid::NvImageBuffer::~NvImageBuffer()
PUBLIC 6520 0 nvandroid::NvAndroid::Impl::fillInternalAllocAttr()
PUBLIC 6650 0 nvandroid::NvAndroid::Impl::fillAllocAttr()
PUBLIC 6738 0 nvandroid::NvAndroid::Impl::fillSignalAttr()
PUBLIC 67f8 0 nvandroid::NvAndroid::Impl::fillWaitAttr()
PUBLIC 68b8 0 nvandroid::NvAndroid::Impl::~Impl()
PUBLIC 6908 0 nvandroid::NvAndroid::Impl::init()
PUBLIC 6910 0 nvandroid::NvAndroid::Impl::shutdown(bool)
PUBLIC 6a40 0 nvandroid::NvAndroid::Impl::disconnectCallback(void*, NvFbStreamsMessageDisconnect_t const*)
PUBLIC 6a90 0 nvandroid::NvAndroid::Impl::run()
PUBLIC 6ae8 0 nvandroid::NvAndroid::Impl::hotplugDisplay(int, nvandroid::NvDisplayInfo const*)
PUBLIC 6af0 0 nvandroid::NvAndroid::Impl::releaseBuffer(unsigned int, unsigned long, int)
PUBLIC 6cc8 0 nvandroid::NvAndroid::Impl::registerBufferObserver(nvandroid::IBufferObserver*)
PUBLIC 6cd0 0 nvandroid::NvAndroid::Impl::unregisterBufferObserver()
PUBLIC 6cd8 0 nvandroid::NvAndroid::Impl::setInputMode(nvandroid::InputMode)
PUBLIC 6ce0 0 nvandroid::NvAndroid::Impl::sendKey(int, int, long)
PUBLIC 6ce8 0 nvandroid::NvAndroid::Impl::sendButton(int, int, int, int, long)
PUBLIC 6cf0 0 nvandroid::NvAndroid::Impl::sendMotion(int, int, long)
PUBLIC 6cf8 0 nvandroid::NvAndroid::Impl::sendRawInput(int, int, int, long)
PUBLIC 6d00 0 nvandroid::NvAndroid::~NvAndroid()
PUBLIC 6d60 0 nvandroid::NvAndroid::init()
PUBLIC 6d68 0 nvandroid::NvAndroid::shutdown()
PUBLIC 6d78 0 nvandroid::NvAndroid::run()
PUBLIC 6d80 0 nvandroid::NvAndroid::hotplugDisplay(int, nvandroid::NvDisplayInfo const*)
PUBLIC 6d88 0 nvandroid::NvAndroid::registerBufferObserver(nvandroid::IBufferObserver*)
PUBLIC 6d90 0 nvandroid::NvAndroid::unregisterBufferObserver()
PUBLIC 6d98 0 nvandroid::NvAndroid::releaseBuffer(unsigned int, unsigned long, int)
PUBLIC 6da0 0 nvandroid::NvAndroid::setInputMode(nvandroid::InputMode)
PUBLIC 6da8 0 nvandroid::NvAndroid::sendKey(int, int, long)
PUBLIC 6db8 0 nvandroid::NvAndroid::sendButton(int, int, int, int, long)
PUBLIC 6dc8 0 nvandroid::NvAndroid::sendMotion(int, int, long)
PUBLIC 6dd8 0 nvandroid::NvAndroid::sendRawInput(int, int, int, long)
PUBLIC 6de8 0 nvandroid::NvAndroid::Impl::Impl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, bool, bool)
PUBLIC 7100 0 nvandroid::NvAndroid::NvAndroid(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, bool, bool)
PUBLIC 7248 0 std::_Sp_counted_ptr<nvandroid::NvImageBuffer*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 7250 0 std::_Sp_counted_ptr<nvandroid::NvImageBuffer*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 7258 0 std::_Sp_counted_ptr<nvandroid::NvImageBuffer*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 7260 0 std::_Sp_counted_ptr<nvandroid::NvImageBuffer*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 7268 0 std::_Sp_counted_ptr<nvandroid::NvImageBuffer*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 7298 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag)
PUBLIC 7370 0 NVInput::NVInput()
PUBLIC 7380 0 NVInput::NVInput(nvandroid::InputMode, char const*)
PUBLIC 7548 0 NVInput::~NVInput()
PUBLIC 7580 0 NVInput::setMode(nvandroid::InputMode)
PUBLIC 7588 0 NVInput::emit(NVInputChannel, int, int, int, long)
PUBLIC 7690 0 NVInput::emitKey(int, int, long)
PUBLIC 76e8 0 NVInput::emitButton(int, int, int, int, long)
PUBLIC 78f8 0 NVInput::emitMotion(int, int, long)
PUBLIC 7a78 0 NVInput::emitRawInput(int, int, int, long)
PUBLIC 7ad8 0 NVLooper::~NVLooper()
PUBLIC 7af8 0 NVLooper::~NVLooper()
PUBLIC 7b18 0 NVLooper::stop()
PUBLIC 7b70 0 NVLooper::addFd(int, void (*)(void*), void*)
PUBLIC 7bb8 0 NVLooper::removeFd(int)
PUBLIC 7c30 0 NVLooper::setupFds(pollfd*)
PUBLIC 7c70 0 NVLooper::processFds(pollfd const*)
PUBLIC 7d08 0 NVLooper::run()
PUBLIC 7e48 0 NVLooper::addTimeout(timespec*, void (*)(void*), void*)
PUBLIC 7e68 0 NVPipe::NVPipe()
PUBLIC 7ef8 0 NVPipe::NVPipe(char const*, bool)
PUBLIC 81e0 0 NVPipe::NVPipe(int)
PUBLIC 8218 0 NVPipe::~NVPipe()
PUBLIC 8240 0 NVPipe::waitConnection()
PUBLIC 8320 0 NVPipe::waitForClient()
PUBLIC 8350 0 NVPipe::acceptClient()
PUBLIC 8390 0 NVPipe::releaseClient()
PUBLIC 83b8 0 NVPipe::readBytes(void*, unsigned long) const
PUBLIC 84c8 0 NVPipe::sendBytes(void const*, unsigned long) const
PUBLIC 8550 0 NVPipe::sendInt(int) const
PUBLIC 8570 0 NVPipe::sendUInt64(unsigned long) const
PUBLIC 8590 0 NVPipe::sendFloat(float) const
PUBLIC 85b0 0 NVPipe::sendPointer(void*) const
PUBLIC 85d0 0 NVPipe::sendFile(int) const
PUBLIC 86c0 0 NVPipe::readInt(int*) const
PUBLIC 86c8 0 NVPipe::readUInt64(unsigned long*) const
PUBLIC 86d0 0 NVPipe::readFloat(float*) const
PUBLIC 86d8 0 NVPipe::readPointer(void**) const
PUBLIC 86e0 0 NVPipe::readInt() const
PUBLIC 8788 0 NVPipe::readPointer() const
PUBLIC 8830 0 NVPipe::readFile() const
PUBLIC 8920 0 NVPipe::closeRead()
PUBLIC 8948 0 NVPipe::closeWrite()
PUBLIC 8970 0 NVPipe::lock() const
PUBLIC 89a0 0 NVPipe::unlock() const
STACK CFI INIT 54d4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5504 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 50 .cfa: sp 0 + .ra: x30
STACK CFI 5550 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5558 x19: .cfa -16 + ^
STACK CFI 5588 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5590 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7248 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5598 17c .cfa: sp 0 + .ra: x30
STACK CFI 55a0 .cfa: sp 5872 +
STACK CFI 55a8 x19: .cfa -5872 + ^ x20: .cfa -5864 + ^
STACK CFI 55b8 x21: .cfa -5856 + ^ x22: .cfa -5848 + ^
STACK CFI 55e8 .ra: .cfa -5832 + ^
STACK CFI 5618 x23: .cfa -5840 + ^
STACK CFI 566c x23: x23
STACK CFI 5698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 569c .cfa: sp 5872 + .ra: .cfa -5832 + ^ x19: .cfa -5872 + ^ x20: .cfa -5864 + ^ x21: .cfa -5856 + ^ x22: .cfa -5848 + ^ x23: .cfa -5840 + ^
STACK CFI 56c4 x23: x23
STACK CFI 56ec x23: .cfa -5840 + ^
STACK CFI 570c x23: x23
STACK CFI 5710 x23: .cfa -5840 + ^
STACK CFI INIT 5718 17c .cfa: sp 0 + .ra: x30
STACK CFI 5720 .cfa: sp 5872 +
STACK CFI 5728 x19: .cfa -5872 + ^ x20: .cfa -5864 + ^
STACK CFI 5738 x21: .cfa -5856 + ^ x22: .cfa -5848 + ^
STACK CFI 5768 .ra: .cfa -5832 + ^
STACK CFI 5798 x23: .cfa -5840 + ^
STACK CFI 57ec x23: x23
STACK CFI 5818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 581c .cfa: sp 5872 + .ra: .cfa -5832 + ^ x19: .cfa -5872 + ^ x20: .cfa -5864 + ^ x21: .cfa -5856 + ^ x22: .cfa -5848 + ^ x23: .cfa -5840 + ^
STACK CFI 5844 x23: x23
STACK CFI 586c x23: .cfa -5840 + ^
STACK CFI 588c x23: x23
STACK CFI 5890 x23: .cfa -5840 + ^
STACK CFI INIT 5898 244 .cfa: sp 0 + .ra: x30
STACK CFI 58a0 .cfa: sp 5904 +
STACK CFI 58a8 x19: .cfa -5904 + ^ x20: .cfa -5896 + ^
STACK CFI 58b8 x21: .cfa -5888 + ^ x22: .cfa -5880 + ^
STACK CFI 58f8 .ra: .cfa -5864 + ^
STACK CFI 5980 x23: .cfa -5872 + ^
STACK CFI 59c8 x23: x23
STACK CFI 5a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5a3c .cfa: sp 5904 + .ra: .cfa -5864 + ^ x19: .cfa -5904 + ^ x20: .cfa -5896 + ^ x21: .cfa -5888 + ^ x22: .cfa -5880 + ^
STACK CFI 5aa0 x23: .cfa -5872 + ^
STACK CFI 5ac0 x23: x23
STACK CFI 5ac4 x23: .cfa -5872 + ^
STACK CFI 5ad4 x23: x23
STACK CFI 5ad8 x23: .cfa -5872 + ^
STACK CFI INIT 5ae0 160 .cfa: sp 0 + .ra: x30
STACK CFI 5ae4 .cfa: sp 144 +
STACK CFI 5aec x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5af8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5b04 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5b0c .ra: .cfa -80 + ^
STACK CFI 5bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5bd8 .cfa: sp 144 + .ra: .cfa -80 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 7258 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c40 100 .cfa: sp 0 + .ra: x30
STACK CFI 5c44 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5c54 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5c60 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5c68 .ra: .cfa -64 + ^
STACK CFI 5cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5cf8 .cfa: sp 112 + .ra: .cfa -64 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 5d40 11c .cfa: sp 0 + .ra: x30
STACK CFI 5d44 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5d50 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5d5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5d6c .ra: .cfa -64 + ^
STACK CFI 5e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5e0c .cfa: sp 112 + .ra: .cfa -64 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 5e60 1ec .cfa: sp 0 + .ra: x30
STACK CFI 5e64 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5e70 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5e7c .ra: .cfa -208 + ^
STACK CFI 5ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5ee4 .cfa: sp 240 + .ra: .cfa -208 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI INIT 6050 14 .cfa: sp 0 + .ra: x30
STACK CFI 6054 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 6060 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 6068 4a0 .cfa: sp 0 + .ra: x30
STACK CFI 606c .cfa: sp 352 +
STACK CFI 6074 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 607c x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6088 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 6090 .ra: .cfa -288 + ^
STACK CFI 635c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6360 .cfa: sp 352 + .ra: .cfa -288 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI INIT 6508 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7268 2c .cfa: sp 0 + .ra: x30
STACK CFI 726c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7288 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 728c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7290 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6520 130 .cfa: sp 0 + .ra: x30
STACK CFI 6524 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6564 .ra: .cfa -120 + ^
STACK CFI 65e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 65e8 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 65ec x21: .cfa -128 + ^
STACK CFI 6618 x21: x21
STACK CFI 664c x21: .cfa -128 + ^
STACK CFI INIT 6650 e8 .cfa: sp 0 + .ra: x30
STACK CFI 6654 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 6660 .ra: .cfa -144 + ^
STACK CFI 6704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6708 .cfa: sp 160 + .ra: .cfa -144 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI INIT 6738 c0 .cfa: sp 0 + .ra: x30
STACK CFI 673c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6748 .ra: .cfa -96 + ^
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 67c8 .cfa: sp 112 + .ra: .cfa -96 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI INIT 67f8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 67fc .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6808 .ra: .cfa -96 + ^
STACK CFI 6884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6888 .cfa: sp 112 + .ra: .cfa -96 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI INIT 68b8 4c .cfa: sp 0 + .ra: x30
STACK CFI 68bc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 68f8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 68fc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6908 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6910 130 .cfa: sp 0 + .ra: x30
STACK CFI 6914 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6920 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6930 .ra: .cfa -48 + ^
STACK CFI 69d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 69d8 .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 6a40 4c .cfa: sp 0 + .ra: x30
STACK CFI 6a44 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a54 .ra: .cfa -16 + ^
STACK CFI 6a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6a90 54 .cfa: sp 0 + .ra: x30
STACK CFI 6a94 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6aa0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6ae8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6af0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 6b00 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 6b0c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 6b14 .ra: .cfa -192 + ^
STACK CFI 6c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6c0c .cfa: sp 240 + .ra: .cfa -192 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT 6cc8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ce0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ce8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cf8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d00 5c .cfa: sp 0 + .ra: x30
STACK CFI 6d04 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d08 .ra: .cfa -16 + ^
STACK CFI 6d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6d50 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6d60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d68 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6da0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6da8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6db8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6dc8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6dd8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7298 d8 .cfa: sp 0 + .ra: x30
STACK CFI 729c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 72a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 72b8 .ra: .cfa -32 + ^
STACK CFI 7324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7328 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 6de8 318 .cfa: sp 0 + .ra: x30
STACK CFI 6dec .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6df8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e08 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6e10 .ra: .cfa -16 + ^
STACK CFI 6fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6fe0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7018 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 705c .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 709c .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 70d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 70d8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7100 148 .cfa: sp 0 + .ra: x30
STACK CFI 7104 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 710c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 711c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 7124 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7130 .ra: .cfa -120 + ^ x27: .cfa -128 + ^
STACK CFI 7240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7244 .cfa: sp 192 + .ra: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^
STACK CFI INIT 7370 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7380 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 400 + x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 7390 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 73a0 x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 73a8 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 73b8 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 73c0 .ra: .cfa -320 + ^
STACK CFI 750c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7510 .cfa: sp 400 + .ra: .cfa -320 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT 7548 34 .cfa: sp 0 + .ra: x30
STACK CFI 754c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7558 .ra: .cfa -16 + ^
STACK CFI 7578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7588 104 .cfa: sp 0 + .ra: x30
STACK CFI 758c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 75ac .ra: .cfa -40 + ^
STACK CFI 7634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7638 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7640 x21: .cfa -48 + ^
STACK CFI 7680 x21: x21
STACK CFI 7688 x21: .cfa -48 + ^
STACK CFI INIT 7690 54 .cfa: sp 0 + .ra: x30
STACK CFI 769c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 76bc .ra: .cfa -16 + ^
STACK CFI 76d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 76e8 210 .cfa: sp 0 + .ra: x30
STACK CFI 76ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 76fc .ra: .cfa -16 + ^
STACK CFI 7738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7748 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 775c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7760 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7798 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 779c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 77b4 x21: x21 x22: x22
STACK CFI 77b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 77e8 x21: x21 x22: x22
STACK CFI 77f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7804 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7838 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7854 x21: x21 x22: x22
STACK CFI 7864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7870 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 78f8 17c .cfa: sp 0 + .ra: x30
STACK CFI 78fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7908 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7914 .ra: .cfa -16 + ^
STACK CFI 7970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7974 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7984 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 79e0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 7a78 58 .cfa: sp 0 + .ra: x30
STACK CFI 7a88 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7ab4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7ad0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ad8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7af8 1c .cfa: sp 0 + .ra: x30
STACK CFI 7afc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7b10 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7b18 58 .cfa: sp 0 + .ra: x30
STACK CFI 7b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 7b68 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 7b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 7b70 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7bb8 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c30 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c70 94 .cfa: sp 0 + .ra: x30
STACK CFI 7c7c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7c88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7c94 .ra: .cfa -16 + ^
STACK CFI 7cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 7d08 13c .cfa: sp 0 + .ra: x30
STACK CFI 7d0c .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 7d18 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7d28 .ra: .cfa -192 + ^
STACK CFI 7e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7e30 .cfa: sp 224 + .ra: .cfa -192 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI INIT 7e48 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e68 8c .cfa: sp 0 + .ra: x30
STACK CFI 7e6c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e78 .ra: .cfa -16 + ^
STACK CFI 7ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7eac .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7ef8 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 7efc .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7f08 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 7f18 .ra: .cfa -136 + ^ x23: .cfa -144 + ^
STACK CFI 8018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 801c .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^
STACK CFI INIT 81e0 34 .cfa: sp 0 + .ra: x30
STACK CFI 81e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8210 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8218 24 .cfa: sp 0 + .ra: x30
STACK CFI 821c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8238 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8240 dc .cfa: sp 0 + .ra: x30
STACK CFI 8244 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8250 .ra: .cfa -8 + ^
STACK CFI 82c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 82cc .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 82d0 x21: .cfa -16 + ^
STACK CFI 8308 x21: x21
STACK CFI 8310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8314 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 8320 30 .cfa: sp 0 + .ra: x30
STACK CFI 8324 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 834c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8350 40 .cfa: sp 0 + .ra: x30
STACK CFI 8354 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 835c .ra: .cfa -16 + ^
STACK CFI 838c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8390 28 .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 83b4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 83b8 110 .cfa: sp 0 + .ra: x30
STACK CFI 83bc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 83c4 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 83d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 83ec x25: .cfa -16 + ^
STACK CFI 8414 x23: x23 x24: x24
STACK CFI 8418 x25: x25
STACK CFI 8428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 842c .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 84b8 x23: x23 x24: x24
STACK CFI 84bc x25: x25
STACK CFI 84c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 84c8 88 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84d8 .ra: .cfa -8 + ^
STACK CFI 84f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 84f8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8500 x21: .cfa -16 + ^
STACK CFI 8548 x21: x21
STACK CFI INIT 8550 1c .cfa: sp 0 + .ra: x30
STACK CFI 8554 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 8568 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 8570 1c .cfa: sp 0 + .ra: x30
STACK CFI 8574 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 8588 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 8590 1c .cfa: sp 0 + .ra: x30
STACK CFI 8594 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 85a8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 85b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 85b4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 85c8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 85d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 85d4 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 85f4 .ra: .cfa -136 + ^
STACK CFI 8628 x21: .cfa -144 + ^
STACK CFI 8670 x21: x21
STACK CFI 8698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 869c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 86a8 x21: .cfa -144 + ^
STACK CFI 86b0 x21: x21
STACK CFI 86b8 x21: .cfa -144 + ^
STACK CFI INIT 86c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86e0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 86e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8700 .ra: .cfa -24 + ^
STACK CFI 872c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8730 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8738 x21: .cfa -32 + ^
STACK CFI 877c x21: x21
STACK CFI 8784 x21: .cfa -32 + ^
STACK CFI INIT 8788 a8 .cfa: sp 0 + .ra: x30
STACK CFI 878c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 87a8 .ra: .cfa -24 + ^
STACK CFI 87d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 87d8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 87e0 x21: .cfa -32 + ^
STACK CFI 8824 x21: x21
STACK CFI 882c x21: .cfa -32 + ^
STACK CFI INIT 8830 f0 .cfa: sp 0 + .ra: x30
STACK CFI 8834 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 8838 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8844 .ra: .cfa -128 + ^
STACK CFI 88f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 88fc .cfa: sp 160 + .ra: .cfa -128 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT 8920 24 .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8940 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8948 24 .cfa: sp 0 + .ra: x30
STACK CFI 894c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8968 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8970 2c .cfa: sp 0 + .ra: x30
STACK CFI 8980 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 8990 .cfa: sp 0 + .ra: .ra
STACK CFI 8998 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 89a0 18 .cfa: sp 0 + .ra: x30
