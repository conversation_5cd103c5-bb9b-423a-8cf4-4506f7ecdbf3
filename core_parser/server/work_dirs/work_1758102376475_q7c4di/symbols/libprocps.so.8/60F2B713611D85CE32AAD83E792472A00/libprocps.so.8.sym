MODULE Linux arm64 60F2B713611D85CE32AAD83E792472A00 libprocps.so.8
INFO CODE_ID 13B7F2601D61CE8532AAD83E792472A09DA06B57
PUBLIC 5428 0 dev_to_tty
PUBLIC 5cd8 0 tty_to_dev
PUBLIC 5e10 0 escape_str
PUBLIC 61b8 0 escape_strlist
PUBLIC 6298 0 escape_command
PUBLIC 63e0 0 escaped_copy
PUBLIC 64a8 0 numa_init
PUBLIC 65a0 0 numa_uninit
PUBLIC 65a8 0 pwcache_get_user
PUBLIC 8b40 0 get_ns_name
PUBLIC 8b68 0 get_ns_id
PUBLIC 8ca0 0 readproc
PUBLIC 8de0 0 readtask
PUBLIC 8ff8 0 readeither
PUBLIC 9210 0 openproc
PUBLIC 93c8 0 closeproc
PUBLIC 9408 0 freeproc
PUBLIC 94d8 0 look_up_our_self
PUBLIC 9588 0 readproctab
PUBLIC 96d0 0 readproctab2
PUBLIC 9a08 0 readproctab3
PUBLIC 9ce0 0 signal_name_to_number
PUBLIC 9f00 0 signal_number_to_name
PUBLIC a200 0 strtosig
PUBLIC a400 0 pretty_print_signals
PUBLIC a4b0 0 unix_print_signals
PUBLIC aaf8 0 put_slabinfo
PUBLIC ab08 0 free_slabinfo
PUBLIC ab40 0 get_slabinfo
PUBLIC ad20 0 uptime
PUBLIC aee0 0 getbtime
PUBLIC aff0 0 loadavg
PUBLIC b1c0 0 meminfo
PUBLIC b908 0 getstat
PUBLIC bd98 0 getpartitions_num
PUBLIC bdd8 0 getdiskstat
PUBLIC c0e8 0 getslabinfo
PUBLIC c268 0 get_pid_digits
PUBLIC c3a8 0 cpuinfo
PUBLIC c3f0 0 procps_linux_version
PUBLIC c500 0 lookup_wchan
PUBLIC c5e8 0 sprint_uptime
PUBLIC cc88 0 print_uptime
STACK CFI INIT 4da8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dd8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e18 48 .cfa: sp 0 + .ra: x30
STACK CFI 4e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e24 x19: .cfa -16 + ^
STACK CFI 4e5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e68 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4e6c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 4e7c x19: .cfa -320 + ^
STACK CFI 4f28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4f2c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 4f30 64 .cfa: sp 0 + .ra: x30
STACK CFI 4f38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f98 60 .cfa: sp 0 + .ra: x30
STACK CFI 4f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fa8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ff8 60 .cfa: sp 0 + .ra: x30
STACK CFI 4ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5008 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5058 94 .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5064 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5070 x21: .cfa -16 + ^
STACK CFI 50a0 x21: x21
STACK CFI 50a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 50b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 50f0 230 .cfa: sp 0 + .ra: x30
STACK CFI 50f8 .cfa: sp 10112 +
STACK CFI 5108 .ra: .cfa -10104 + ^ x29: .cfa -10112 + ^
STACK CFI 5110 x23: .cfa -10064 + ^ x24: .cfa -10056 + ^
STACK CFI 511c x21: .cfa -10080 + ^ x22: .cfa -10072 + ^
STACK CFI 513c x19: .cfa -10096 + ^ x20: .cfa -10088 + ^
STACK CFI 5168 x25: .cfa -10048 + ^ x26: .cfa -10040 + ^
STACK CFI 517c x27: .cfa -10032 + ^
STACK CFI 5294 x25: x25 x26: x26
STACK CFI 5298 x27: x27
STACK CFI 52a4 x19: x19 x20: x20
STACK CFI 52d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 52dc .cfa: sp 10112 + .ra: .cfa -10104 + ^ x19: .cfa -10096 + ^ x20: .cfa -10088 + ^ x21: .cfa -10080 + ^ x22: .cfa -10072 + ^ x23: .cfa -10064 + ^ x24: .cfa -10056 + ^ x25: .cfa -10048 + ^ x26: .cfa -10040 + ^ x27: .cfa -10032 + ^ x29: .cfa -10112 + ^
STACK CFI 5304 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27
STACK CFI 5314 x19: .cfa -10096 + ^ x20: .cfa -10088 + ^
STACK CFI 5318 x25: .cfa -10048 + ^ x26: .cfa -10040 + ^
STACK CFI 531c x27: .cfa -10032 + ^
STACK CFI INIT 5320 108 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 5334 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 5344 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 5358 x23: .cfa -192 + ^
STACK CFI 53dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 53e0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x29: .cfa -240 + ^
STACK CFI INIT 5428 8b0 .cfa: sp 0 + .ra: x30
STACK CFI 542c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 5434 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5440 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 54a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 54a4 .cfa: sp 256 + .ra: .cfa -248 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 54ac x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 54b8 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 54c0 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 55e8 x19: x19 x20: x20
STACK CFI 55ec x25: x25 x26: x26
STACK CFI 55f0 x27: x27 x28: x28
STACK CFI 55f4 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 5730 x19: x19 x20: x20
STACK CFI 5734 x25: x25 x26: x26
STACK CFI 5738 x27: x27 x28: x28
STACK CFI 573c x19: .cfa -240 + ^ x20: .cfa -232 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 5cc8 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5ccc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5cd0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 5cd4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 5cd8 138 .cfa: sp 0 + .ra: x30
STACK CFI 5cdc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5ce4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5cf4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d7c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5e10 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 5e14 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 5e1c x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 5e2c x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 5e38 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 5e50 x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 5f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5f78 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^ x29: .cfa -416 + ^
STACK CFI 5fcc x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 6150 x25: x25 x26: x26
STACK CFI 6168 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 61a0 x25: x25 x26: x26
STACK CFI 61a8 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 61ac x25: x25 x26: x26
STACK CFI INIT 61b8 dc .cfa: sp 0 + .ra: x30
STACK CFI 61c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 61c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 61dc x25: .cfa -16 + ^
STACK CFI 61f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6208 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6260 x19: x19 x20: x20
STACK CFI 6268 x23: x23 x24: x24
STACK CFI 6270 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 6274 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6278 x19: x19 x20: x20
STACK CFI 6288 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI INIT 6298 144 .cfa: sp 0 + .ra: x30
STACK CFI 62a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6334 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6384 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 63ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 63e0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 63ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 642c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6430 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6438 x21: .cfa -16 + ^
STACK CFI 6470 x21: x21
STACK CFI 647c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 648c x21: x21
STACK CFI 6490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6498 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64a8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 64ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 64d0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6574 x21: x21 x22: x22
STACK CFI 6578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 657c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 65a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65a8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 65ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6620 x21: .cfa -16 + ^
STACK CFI 6644 x21: x21
STACK CFI 6674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6678 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6688 x21: x21
STACK CFI INIT 66a0 fc .cfa: sp 0 + .ra: x30
STACK CFI 66a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 671c x21: .cfa -16 + ^
STACK CFI 6740 x21: x21
STACK CFI 6770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6774 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6784 x21: x21
STACK CFI INIT 67a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 67a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6800 80 .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 680c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6814 x21: .cfa -16 + ^
STACK CFI 686c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6880 ac .cfa: sp 0 + .ra: x30
STACK CFI 6884 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 688c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6898 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6918 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6930 18c .cfa: sp 0 + .ra: x30
STACK CFI 6934 .cfa: sp 272 +
STACK CFI 693c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6944 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6970 x21: .cfa -16 + ^
STACK CFI 6a90 x21: x21
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6aa0 .cfa: sp 272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6aac x21: x21
STACK CFI 6ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6ac0 650 .cfa: sp 0 + .ra: x30
STACK CFI 6ac4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6acc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6ae8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6af4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6c48 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7110 360 .cfa: sp 0 + .ra: x30
STACK CFI 7114 .cfa: sp 2144 +
STACK CFI 712c .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 7134 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 7178 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI 7190 x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 71a8 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 7310 x19: x19 x20: x20
STACK CFI 7314 x23: x23 x24: x24
STACK CFI 7318 x25: x25 x26: x26
STACK CFI 7340 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7344 .cfa: sp 2144 + .ra: .cfa -2136 + ^ x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x21: .cfa -2112 + ^ x22: .cfa -2104 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x26: .cfa -2072 + ^ x29: .cfa -2144 + ^
STACK CFI 73d8 x19: x19 x20: x20
STACK CFI 73dc x23: x23 x24: x24
STACK CFI 73e0 x25: x25 x26: x26
STACK CFI 73e4 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 7400 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 7404 x23: x23 x24: x24
STACK CFI 7410 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 7444 x19: x19 x20: x20
STACK CFI 7448 x23: x23 x24: x24
STACK CFI 744c x25: x25 x26: x26
STACK CFI 7450 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI 7460 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7464 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 7468 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI 746c x25: .cfa -2080 + ^ x26: .cfa -2072 + ^
STACK CFI INIT 7470 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 7474 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7480 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 74a0 x21: .cfa -96 + ^
STACK CFI 755c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7560 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7628 e0 .cfa: sp 0 + .ra: x30
STACK CFI 762c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 763c x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 764c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 766c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 7678 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 7700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7704 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7708 1dc .cfa: sp 0 + .ra: x30
STACK CFI 770c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 771c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 7728 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 774c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 77a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 77a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 77b0 x27: .cfa -96 + ^
STACK CFI 77d0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 788c x25: x25 x26: x26
STACK CFI 7890 x27: x27
STACK CFI 7894 x27: .cfa -96 + ^
STACK CFI 7898 x27: x27
STACK CFI 789c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI 78d8 x25: x25 x26: x26 x27: x27
STACK CFI 78dc x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 78e0 x27: .cfa -96 + ^
STACK CFI INIT 78e8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 78ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 78fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7910 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7924 x23: .cfa -32 + ^
STACK CFI 798c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7990 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 79b0 194 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 79bc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 79c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 79d0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7a00 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7a18 x27: .cfa -32 + ^
STACK CFI 7acc x23: x23 x24: x24
STACK CFI 7ad0 x27: x27
STACK CFI 7af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 7afc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 7b08 x23: x23 x24: x24 x27: x27
STACK CFI 7b1c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI 7b2c x23: x23 x24: x24
STACK CFI 7b30 x27: x27
STACK CFI 7b3c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7b40 x27: .cfa -32 + ^
STACK CFI INIT 7b48 180 .cfa: sp 0 + .ra: x30
STACK CFI 7b4c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7b58 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7b64 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 7b80 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7be8 x25: .cfa -112 + ^
STACK CFI 7c64 x25: x25
STACK CFI 7c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7c98 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 7cc4 x25: .cfa -112 + ^
STACK CFI INIT 7cc8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7ccc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7cd8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d04 x23: .cfa -32 + ^
STACK CFI 7d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7d78 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7d90 184 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7da8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7dcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7e7c x23: x23 x24: x24
STACK CFI 7e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7e94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 7eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7eb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7ec0 x23: x23 x24: x24
STACK CFI 7ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7ec8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7f10 x23: x23 x24: x24
STACK CFI INIT 7f18 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 7f1c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7f34 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7f44 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7f5c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 80e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 80e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 80e8 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 80ec .cfa: sp 80 +
STACK CFI 80f0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 80f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8100 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 811c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 820c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8210 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 835c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8360 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8598 47c .cfa: sp 0 + .ra: x30
STACK CFI 859c .cfa: sp 112 +
STACK CFI 85ac .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 85b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 85cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 866c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8670 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 8704 x25: .cfa -32 + ^
STACK CFI 8748 x25: x25
STACK CFI 87d4 x25: .cfa -32 + ^
STACK CFI 8878 x25: x25
STACK CFI 890c x25: .cfa -32 + ^
STACK CFI 89b4 x25: x25
STACK CFI 89d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 89d8 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8a18 124 .cfa: sp 0 + .ra: x30
STACK CFI 8a1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8a24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8a38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8a8c x23: .cfa -16 + ^
STACK CFI 8b0c x23: x23
STACK CFI 8b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8b18 x23: .cfa -16 + ^
STACK CFI 8b20 x23: x23
STACK CFI 8b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8b40 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b68 98 .cfa: sp 0 + .ra: x30
STACK CFI 8b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8b74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8bd4 x19: x19 x20: x20
STACK CFI 8bdc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8be0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8be4 x19: x19 x20: x20
STACK CFI 8bf4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8bf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8c00 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8c04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8c14 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8c24 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8ca0 140 .cfa: sp 0 + .ra: x30
STACK CFI 8ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8cb8 x21: .cfa -16 + ^
STACK CFI 8dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8de0 214 .cfa: sp 0 + .ra: x30
STACK CFI 8de4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8dec x25: .cfa -96 + ^
STACK CFI 8df4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 8e14 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8f2c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8ff8 214 .cfa: sp 0 + .ra: x30
STACK CFI 8ffc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9004 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 900c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 902c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 9184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9188 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9210 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 9214 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 921c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 922c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 9244 x23: .cfa -256 + ^
STACK CFI 9304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9308 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x29: .cfa -304 + ^
STACK CFI INIT 93c8 40 .cfa: sp 0 + .ra: x30
STACK CFI 93d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 93d8 x19: .cfa -16 + ^
STACK CFI 9400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9408 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9410 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9418 x19: .cfa -16 + ^
STACK CFI 94d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 94d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 94dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 94ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 955c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9588 148 .cfa: sp 0 + .ra: x30
STACK CFI 958c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9598 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 95c8 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9668 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 96d0 334 .cfa: sp 0 + .ra: x30
STACK CFI 96d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 96dc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 96e8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 96f8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9700 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 970c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9978 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9a08 10c .cfa: sp 0 + .ra: x30
STACK CFI 9a0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a14 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9a20 x25: .cfa -16 + ^
STACK CFI 9a2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9a38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9adc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9b18 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 9b1c .cfa: sp 240 +
STACK CFI 9b30 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9b38 x23: .cfa -176 + ^
STACK CFI 9b40 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 9b4c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 9c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9c2c .cfa: sp 240 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 9ce0 220 .cfa: sp 0 + .ra: x30
STACK CFI 9ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9cf4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9d04 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9d78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9d84 x25: .cfa -32 + ^
STACK CFI 9e68 x21: x21 x22: x22
STACK CFI 9e6c x25: x25
STACK CFI 9e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 9e9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 9eac x21: x21 x22: x22
STACK CFI 9eb0 x25: x25
STACK CFI 9eb4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^
STACK CFI 9eb8 x21: x21 x22: x22
STACK CFI 9ebc x25: x25
STACK CFI 9ec0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^
STACK CFI 9ecc x21: x21 x22: x22
STACK CFI 9ed0 x25: x25
STACK CFI 9ed4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^
STACK CFI 9eec x21: x21 x22: x22
STACK CFI 9ef0 x25: x25
STACK CFI 9ef8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9efc x25: .cfa -32 + ^
STACK CFI INIT 9f00 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9fd0 230 .cfa: sp 0 + .ra: x30
STACK CFI 9fd4 .cfa: sp 2192 +
STACK CFI 9fe0 .ra: .cfa -2184 + ^ x29: .cfa -2192 + ^
STACK CFI 9fec x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI a004 x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI a014 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI a03c x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI a130 x21: x21 x22: x22
STACK CFI a134 x27: x27 x28: x28
STACK CFI a168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a16c .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^ x27: .cfa -2112 + ^ x28: .cfa -2104 + ^ x29: .cfa -2192 + ^
STACK CFI a1e4 x21: x21 x22: x22
STACK CFI a1e8 x27: x27 x28: x28
STACK CFI a1f8 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI a1fc x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI INIT a200 200 .cfa: sp 0 + .ra: x30
STACK CFI a204 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a20c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a228 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a334 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT a400 b0 .cfa: sp 0 + .ra: x30
STACK CFI a404 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a40c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a418 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a424 x23: .cfa -16 + ^
STACK CFI a494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a498 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a4b0 ac .cfa: sp 0 + .ra: x30
STACK CFI a4b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a560 60 .cfa: sp 0 + .ra: x30
STACK CFI a564 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a5bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a5c0 264 .cfa: sp 0 + .ra: x30
STACK CFI a5c4 .cfa: sp 2192 +
STACK CFI a5c8 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI a5d0 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI a5dc x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI a5e8 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI a5fc x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI a604 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI a610 v8: .cfa -2080 + ^
STACK CFI a7a8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a7ac .cfa: sp 2192 + .ra: .cfa -2168 + ^ v8: .cfa -2080 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x28: .cfa -2088 + ^ x29: .cfa -2176 + ^
STACK CFI INIT a828 2cc .cfa: sp 0 + .ra: x30
STACK CFI a82c .cfa: sp 2192 +
STACK CFI a834 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI a840 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI a858 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI a860 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI a86c x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI a880 v8: .cfa -2080 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI aa18 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI aa1c .cfa: sp 2192 + .ra: .cfa -2168 + ^ v8: .cfa -2080 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x28: .cfa -2088 + ^ x29: .cfa -2176 + ^
STACK CFI INIT aaf8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ab08 38 .cfa: sp 0 + .ra: x30
STACK CFI ab10 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab18 x19: .cfa -16 + ^
STACK CFI ab38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab40 1c4 .cfa: sp 0 + .ra: x30
STACK CFI ab44 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI ab4c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI ab74 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^
STACK CFI ac30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ac34 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT ad08 14 .cfa: sp 0 + .ra: x30
STACK CFI ad0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4620 190 .cfa: sp 0 + .ra: x30
STACK CFI 4624 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 462c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 463c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 46b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 46d8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 46ec x25: .cfa -48 + ^
STACK CFI 474c x23: x23 x24: x24
STACK CFI 4750 x25: x25
STACK CFI 4754 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4780 x23: x23 x24: x24
STACK CFI 4784 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 478c x23: x23 x24: x24
STACK CFI 4790 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 47a4 x23: x23 x24: x24 x25: x25
STACK CFI 47a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 47ac x25: .cfa -48 + ^
STACK CFI INIT ad20 1c0 .cfa: sp 0 + .ra: x30
STACK CFI ad24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ad2c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ad3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ad50 x23: .cfa -48 + ^
STACK CFI ae38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ae3c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT aee0 110 .cfa: sp 0 + .ra: x30
STACK CFI aee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI aeec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI af04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI af08 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI af1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI af24 x23: .cfa -16 + ^
STACK CFI afb0 x19: x19 x20: x20
STACK CFI afb8 x23: x23
STACK CFI afbc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI afc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT aff0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI aff4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI affc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b00c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b024 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b11c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b120 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT b1c0 448 .cfa: sp 0 + .ra: x30
STACK CFI b1c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI b1d0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI b1d8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI b1f8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI b498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b49c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT b608 2fc .cfa: sp 0 + .ra: x30
STACK CFI b60c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI b614 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI b624 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI b62c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b63c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI b644 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI b870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b874 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT b908 48c .cfa: sp 0 + .ra: x30
STACK CFI b90c .cfa: sp 256 +
STACK CFI b910 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI b918 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI b938 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI b95c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI b968 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI bbb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bbb8 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT bd98 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT bdd8 310 .cfa: sp 0 + .ra: x30
STACK CFI bddc .cfa: sp 288 +
STACK CFI bde4 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI bdf4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI be04 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI be24 x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI bfa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bfa4 .cfa: sp 288 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT c0e8 17c .cfa: sp 0 + .ra: x30
STACK CFI c0ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c0fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c10c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c128 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI c214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI c218 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT c268 140 .cfa: sp 0 + .ra: x30
STACK CFI c26c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c274 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c27c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c2c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI c2ec x23: .cfa -64 + ^
STACK CFI c31c x23: x23
STACK CFI c320 x23: .cfa -64 + ^
STACK CFI c380 x23: x23
STACK CFI c390 x23: .cfa -64 + ^
STACK CFI c398 x23: x23
STACK CFI c3a4 x23: .cfa -64 + ^
STACK CFI INIT c3a8 44 .cfa: sp 0 + .ra: x30
STACK CFI c3ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c3d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 47b0 5e0 .cfa: sp 0 + .ra: x30
STACK CFI 47b4 .cfa: sp 208 +
STACK CFI 47bc .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 47c4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 47dc x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4874 .cfa: sp 208 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 48a0 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 48ac x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 48b8 v8: .cfa -112 + ^
STACK CFI 4b64 x23: x23 x24: x24
STACK CFI 4b68 x25: x25 x26: x26
STACK CFI 4b6c v8: v8
STACK CFI 4b74 v8: .cfa -112 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4bac x23: x23 x24: x24
STACK CFI 4bb0 x25: x25 x26: x26
STACK CFI 4bb4 v8: v8
STACK CFI 4bb8 v8: .cfa -112 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4cbc v8: v8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4cdc v8: .cfa -112 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4d00 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4d1c v8: .cfa -112 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4d80 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4d84 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4d88 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4d8c v8: .cfa -112 + ^
STACK CFI INIT c3f0 10c .cfa: sp 0 + .ra: x30
STACK CFI c3f4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI c404 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI c434 x21: .cfa -304 + ^
STACK CFI c490 x21: x21
STACK CFI c4b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c4bc .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI c4cc x21: x21
STACK CFI c4e0 x21: .cfa -304 + ^
STACK CFI c4f0 x21: x21
STACK CFI c4f8 x21: .cfa -304 + ^
STACK CFI INIT c500 e8 .cfa: sp 0 + .ra: x30
STACK CFI c504 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c528 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c5a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c5ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c5c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c5c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c5e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT c5e8 6a0 .cfa: sp 0 + .ra: x30
STACK CFI c5ec .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c5f4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c5fc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI c618 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI c62c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c63c v8: .cfa -48 + ^
STACK CFI c80c v8: v8 x27: x27 x28: x28
STACK CFI ca34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ca38 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI ca58 v8: v8 x27: x27 x28: x28
STACK CFI ca74 v8: .cfa -48 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cb4c x27: x27 x28: x28
STACK CFI cb50 v8: v8
STACK CFI cb54 v8: .cfa -48 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cc24 v8: v8 x27: x27 x28: x28
STACK CFI cc28 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cc2c v8: .cfa -48 + ^
STACK CFI INIT cc88 14 .cfa: sp 0 + .ra: x30
STACK CFI cc8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc98 .cfa: sp 0 + .ra: .ra x29: x29
