MODULE Linux arm64 8A8F3D448C4D4575377DE952B5968CF40 libefiboot.so.1
INFO CODE_ID 443D8F8A4D8C7545377DE952B5968CF4F95B13C5
PUBLIC fcb0 0 efi_loadopt_desc
PUBLIC fe00 0 efi_loadopt_args_as_ucs2
PUBLIC ffd0 0 efi_loadopt_args_as_utf8
PUBLIC 10078 0 efi_loadopt_args_from_file
PUBLIC 101e8 0 efi_loadopt_optional_data
PUBLIC 102e0 0 efi_loadopt_path
PUBLIC 10658 0 efi_loadopt_pathlen
PUBLIC 10688 0 efi_loadopt_attr_clear
PUBLIC 106a0 0 efi_loadopt_attr_set
PUBLIC 106b8 0 efi_loadopt_attrs
PUBLIC 106c0 0 efi_loadopt_optional_data_size
PUBLIC 10cd8 0 efi_loadopt_is_valid
PUBLIC 10cf8 0 efi_loadopt_create
PUBLIC 11758 0 efi_generate_ipv4_device_path
PUBLIC 118e0 0 efi_generate_file_device_path
PUBLIC 11dd0 0 efi_generate_file_device_path_from_esp
STACK CFI INIT 2b78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ba8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2be8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2bec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bf4 x19: .cfa -16 + ^
STACK CFI 2c2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c38 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2c3c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 2c4c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d08 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 2d30 108 .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 2d3c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 2e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e0c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 2e38 290 .cfa: sp 0 + .ra: x30
STACK CFI 2e3c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2e40 .cfa: x29 128 +
STACK CFI 2e44 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2e50 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2e5c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2e68 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f60 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 30c8 450 .cfa: sp 0 + .ra: x30
STACK CFI 30cc .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 30d8 .cfa: x29 400 +
STACK CFI 30dc x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 313c x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 33c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 33c4 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 3518 450 .cfa: sp 0 + .ra: x30
STACK CFI 351c .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 3528 .cfa: x29 400 +
STACK CFI 352c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 3590 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 3810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3814 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 3968 450 .cfa: sp 0 + .ra: x30
STACK CFI 396c .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 3978 .cfa: x29 400 +
STACK CFI 397c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 39e0 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 3c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3c64 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 3db8 450 .cfa: sp 0 + .ra: x30
STACK CFI 3dbc .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 3dc8 .cfa: x29 400 +
STACK CFI 3dcc x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 3e30 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 40b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 40b4 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 4208 450 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 4218 .cfa: x29 400 +
STACK CFI 421c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 427c x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 4500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4504 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 4658 450 .cfa: sp 0 + .ra: x30
STACK CFI 465c .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 4668 .cfa: x29 400 +
STACK CFI 466c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 46cc x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4954 .cfa: x29 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 4aa8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ab4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4abc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4bc4 x23: .cfa -32 + ^
STACK CFI 4c18 x23: x23
STACK CFI 4c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4c5c x23: .cfa -32 + ^
STACK CFI INIT 4c60 150 .cfa: sp 0 + .ra: x30
STACK CFI 4c64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4c78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4db0 9bc .cfa: sp 0 + .ra: x30
STACK CFI 4db4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 4dbc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 4dc8 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 4df8 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 4f44 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 4f48 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 52a0 x25: x25 x26: x26
STACK CFI 52a8 x27: x27 x28: x28
STACK CFI 52d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 52d4 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 56f0 x25: x25 x26: x26
STACK CFI 56f4 x27: x27 x28: x28
STACK CFI 56fc x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 5730 x25: x25 x26: x26
STACK CFI 5734 x27: x27 x28: x28
STACK CFI 5738 x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 5760 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5764 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 5768 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 5770 21c .cfa: sp 0 + .ra: x30
STACK CFI 5774 .cfa: sp 128 +
STACK CFI 5778 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5780 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5788 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 58ac x23: .cfa -64 + ^
STACK CFI 5900 x23: x23
STACK CFI 5958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 595c .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 5988 x23: .cfa -64 + ^
STACK CFI INIT 5990 294 .cfa: sp 0 + .ra: x30
STACK CFI 5994 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 59ac x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 59e8 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5a54 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5a74 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5b70 x23: x23 x24: x24
STACK CFI 5b74 x25: x25 x26: x26
STACK CFI 5b78 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5b7c x23: x23 x24: x24
STACK CFI 5b84 x25: x25 x26: x26
STACK CFI 5c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5c10 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 5c1c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5c20 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 5c28 7c0 .cfa: sp 0 + .ra: x30
STACK CFI 5c2c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 5c30 .cfa: x29 384 +
STACK CFI 5c34 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 5c48 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 5c64 x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 609c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 60a0 .cfa: x29 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x29: .cfa -384 + ^
STACK CFI INIT 63e8 128 .cfa: sp 0 + .ra: x30
STACK CFI 63ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6400 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 64c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 64c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6510 140 .cfa: sp 0 + .ra: x30
STACK CFI 6514 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6520 .cfa: x29 96 +
STACK CFI 6524 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6534 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6554 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 65a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 65ac .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6650 278 .cfa: sp 0 + .ra: x30
STACK CFI 6654 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6658 .cfa: x29 96 +
STACK CFI 665c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 666c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6684 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6694 x25: .cfa -32 + ^
STACK CFI 67d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 67d8 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 68c8 100 .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68f8 x23: .cfa -16 + ^
STACK CFI 6950 x23: x23
STACK CFI 6984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6988 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 69c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 69c8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 69cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6a78 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b28 6bc .cfa: sp 0 + .ra: x30
STACK CFI 6b2c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 6b30 .cfa: x29 272 +
STACK CFI 6b34 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 6b3c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 6b60 x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^
STACK CFI 6f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6f44 .cfa: x29 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x29: .cfa -272 + ^
STACK CFI INIT 71e8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 71ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 71f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7200 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7218 x23: .cfa -16 + ^
STACK CFI 7270 x23: x23
STACK CFI 72a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 72a8 d00 .cfa: sp 0 + .ra: x30
STACK CFI 72ac .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 72b8 .cfa: x29 336 +
STACK CFI 72bc x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 7308 x19: .cfa -320 + ^ x20: .cfa -312 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 7310 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 731c x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 7500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7504 .cfa: x29 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI INIT 7fa8 324 .cfa: sp 0 + .ra: x30
STACK CFI 7fac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7fb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7fcc x23: .cfa -16 + ^
STACK CFI 8100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8104 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 816c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8170 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 82c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 82d0 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 82d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 82dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 82e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 82f0 x23: .cfa -16 + ^
STACK CFI 841c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8420 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 848c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8538 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8598 290 .cfa: sp 0 + .ra: x30
STACK CFI 859c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 85a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 85b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 85bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 86a8 x27: .cfa -16 + ^
STACK CFI 8784 x27: x27
STACK CFI 879c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 87a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 87a4 x27: x27
STACK CFI 881c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8820 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8828 7e4 .cfa: sp 0 + .ra: x30
STACK CFI 882c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 8834 .cfa: x29 320 +
STACK CFI 883c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 8860 x19: .cfa -304 + ^ x20: .cfa -296 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 8d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d54 .cfa: x29 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI INIT 9010 ac .cfa: sp 0 + .ra: x30
STACK CFI 9014 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 901c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9028 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9030 x23: .cfa -16 + ^
STACK CFI 90b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 90c0 194 .cfa: sp 0 + .ra: x30
STACK CFI 90c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 90cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 90d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 90f0 x23: .cfa -16 + ^
STACK CFI 9150 x23: x23
STACK CFI 91a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 920c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 924c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9258 108 .cfa: sp 0 + .ra: x30
STACK CFI 925c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9264 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9270 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9288 x23: .cfa -16 + ^
STACK CFI 92e0 x23: x23
STACK CFI 931c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9320 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 935c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9360 a64 .cfa: sp 0 + .ra: x30
STACK CFI 9364 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9368 .cfa: x29 144 +
STACK CFI 936c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9378 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9394 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 93a8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 93b4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 982c .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9dc8 35c .cfa: sp 0 + .ra: x30
STACK CFI 9dcc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9dd0 .cfa: x29 128 +
STACK CFI 9dd4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9de0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9df4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9e18 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 9f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9f98 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT a128 11c .cfa: sp 0 + .ra: x30
STACK CFI a12c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a134 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a13c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a148 x23: .cfa -16 + ^
STACK CFI a1fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a200 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT a248 45c .cfa: sp 0 + .ra: x30
STACK CFI a24c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a250 .cfa: x29 144 +
STACK CFI a254 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a264 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a280 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a334 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI INIT a6a8 d0 .cfa: sp 0 + .ra: x30
STACK CFI a6ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a6bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a764 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a778 598 .cfa: sp 0 + .ra: x30
STACK CFI a77c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a784 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a794 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a81c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a860 x23: x23 x24: x24
STACK CFI a888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a88c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI aa7c x23: x23 x24: x24
STACK CFI aae4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI aae8 x25: .cfa -80 + ^
STACK CFI ab3c x25: x25
STACK CFI ab9c x25: .cfa -80 + ^
STACK CFI abf0 x25: x25
STACK CFI ad00 x23: x23 x24: x24
STACK CFI ad08 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ad0c x25: .cfa -80 + ^
STACK CFI INIT ad10 ac .cfa: sp 0 + .ra: x30
STACK CFI ad14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ad1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ad28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ad30 x23: .cfa -16 + ^
STACK CFI adb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT adc0 664 .cfa: sp 0 + .ra: x30
STACK CFI adc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI adc8 .cfa: x29 112 +
STACK CFI adcc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI addc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI adfc x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI af08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI af0c .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2980 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 2984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 298c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b428 404 .cfa: sp 0 + .ra: x30
STACK CFI b430 .cfa: sp 8464 +
STACK CFI b434 .ra: .cfa -8456 + ^ x29: .cfa -8464 + ^
STACK CFI b43c x23: .cfa -8416 + ^ x24: .cfa -8408 + ^
STACK CFI b448 x19: .cfa -8448 + ^ x20: .cfa -8440 + ^
STACK CFI b45c x25: .cfa -8400 + ^ x26: .cfa -8392 + ^
STACK CFI b4b8 x21: .cfa -8432 + ^ x22: .cfa -8424 + ^
STACK CFI b4d8 x27: .cfa -8384 + ^ x28: .cfa -8376 + ^
STACK CFI b558 x21: x21 x22: x22
STACK CFI b55c x27: x27 x28: x28
STACK CFI b590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b594 .cfa: sp 8464 + .ra: .cfa -8456 + ^ x19: .cfa -8448 + ^ x20: .cfa -8440 + ^ x21: .cfa -8432 + ^ x22: .cfa -8424 + ^ x23: .cfa -8416 + ^ x24: .cfa -8408 + ^ x25: .cfa -8400 + ^ x26: .cfa -8392 + ^ x27: .cfa -8384 + ^ x28: .cfa -8376 + ^ x29: .cfa -8464 + ^
STACK CFI b6e0 x21: x21 x22: x22
STACK CFI b6e4 x27: x27 x28: x28
STACK CFI b6e8 x21: .cfa -8432 + ^ x22: .cfa -8424 + ^ x27: .cfa -8384 + ^ x28: .cfa -8376 + ^
STACK CFI b750 x21: x21 x22: x22
STACK CFI b754 x27: x27 x28: x28
STACK CFI b758 x21: .cfa -8432 + ^ x22: .cfa -8424 + ^ x27: .cfa -8384 + ^ x28: .cfa -8376 + ^
STACK CFI b794 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI b7cc x21: .cfa -8432 + ^ x22: .cfa -8424 + ^ x27: .cfa -8384 + ^ x28: .cfa -8376 + ^
STACK CFI b7f0 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI b7f4 x21: .cfa -8432 + ^ x22: .cfa -8424 + ^
STACK CFI b7f8 x27: .cfa -8384 + ^ x28: .cfa -8376 + ^
STACK CFI INIT b830 64 .cfa: sp 0 + .ra: x30
STACK CFI b834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b840 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b88c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT b898 364 .cfa: sp 0 + .ra: x30
STACK CFI b8a0 .cfa: sp 4784 +
STACK CFI b8a4 .ra: .cfa -4760 + ^ x29: .cfa -4768 + ^
STACK CFI b8a8 .cfa: x29 4768 +
STACK CFI b8ac x19: .cfa -4752 + ^ x20: .cfa -4744 + ^
STACK CFI b8bc x21: .cfa -4736 + ^ x22: .cfa -4728 + ^
STACK CFI b8e0 x23: .cfa -4720 + ^ x24: .cfa -4712 + ^ x25: .cfa -4704 + ^
STACK CFI bb88 .cfa: sp 4784 +
STACK CFI bba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI bbac .cfa: x29 4768 + .ra: .cfa -4760 + ^ x19: .cfa -4752 + ^ x20: .cfa -4744 + ^ x21: .cfa -4736 + ^ x22: .cfa -4728 + ^ x23: .cfa -4720 + ^ x24: .cfa -4712 + ^ x25: .cfa -4704 + ^ x29: .cfa -4768 + ^
STACK CFI INIT bc00 f8 .cfa: sp 0 + .ra: x30
STACK CFI bc08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bcf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bcf8 1210 .cfa: sp 0 + .ra: x30
STACK CFI bcfc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI bd04 .cfa: x29 176 +
STACK CFI bd0c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI bd24 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI bd3c x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c52c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c530 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT cf08 124 .cfa: sp 0 + .ra: x30
STACK CFI cf0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cf14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cf8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cf90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cfa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cfa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cfc8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d024 x21: x21 x22: x22
STACK CFI d028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d030 f0 .cfa: sp 0 + .ra: x30
STACK CFI d034 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d03c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d054 x21: .cfa -16 + ^
STACK CFI d0ac x21: x21
STACK CFI d0c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d0cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d118 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT d120 2c0 .cfa: sp 0 + .ra: x30
STACK CFI d124 .cfa: sp 128 +
STACK CFI d128 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI d130 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI d140 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI d248 x23: .cfa -64 + ^
STACK CFI d29c x23: x23
STACK CFI d2d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d2d8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI d3dc x23: .cfa -64 + ^
STACK CFI INIT d3e0 290 .cfa: sp 0 + .ra: x30
STACK CFI d3e4 .cfa: sp 96 +
STACK CFI d3e8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d3f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d400 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d5b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d5bc .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT d670 64 .cfa: sp 0 + .ra: x30
STACK CFI d674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d67c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d688 x21: .cfa -16 + ^
STACK CFI d6d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT d6d8 1f4 .cfa: sp 0 + .ra: x30
STACK CFI d6dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d6e0 .cfa: x29 80 +
STACK CFI d6e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d6f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d70c x23: .cfa -32 + ^
STACK CFI d888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d88c .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT d8d0 3b8 .cfa: sp 0 + .ra: x30
STACK CFI d8d4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI d8e8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI d900 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI d914 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI d91c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI d92c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI db78 x19: x19 x20: x20
STACK CFI db7c x23: x23 x24: x24
STACK CFI db80 x27: x27 x28: x28
STACK CFI dba8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI dbac .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI dc78 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI dc7c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI dc80 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI dc84 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT dc88 220 .cfa: sp 0 + .ra: x30
STACK CFI dc8c .cfa: sp 608 +
STACK CFI dc98 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI dca0 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI dcec x21: .cfa -576 + ^
STACK CFI dd30 x21: x21
STACK CFI dd58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dd5c .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x29: .cfa -608 + ^
STACK CFI ddac x21: x21
STACK CFI ddb4 x21: .cfa -576 + ^
STACK CFI de10 x21: x21
STACK CFI de1c x21: .cfa -576 + ^
STACK CFI de20 x21: x21
STACK CFI de68 x21: .cfa -576 + ^
STACK CFI dea0 x21: x21
STACK CFI dea4 x21: .cfa -576 + ^
STACK CFI INIT dea8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI deac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI deb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI dec4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI dedc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI dee4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI df30 x27: .cfa -64 + ^
STACK CFI df90 x27: x27
STACK CFI dfc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI dfc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI e010 x27: x27
STACK CFI e014 x27: .cfa -64 + ^
STACK CFI e024 x27: x27
STACK CFI e028 x27: .cfa -64 + ^
STACK CFI e058 x27: x27
STACK CFI e05c x27: .cfa -64 + ^
STACK CFI INIT e060 59c .cfa: sp 0 + .ra: x30
STACK CFI e064 .cfa: sp 144 +
STACK CFI e068 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI e070 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI e078 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI e084 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e08c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e0c8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e298 x27: x27 x28: x28
STACK CFI e29c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e2e4 x27: x27 x28: x28
STACK CFI e300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e304 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI e374 x27: x27 x28: x28
STACK CFI e388 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e5b4 x27: x27 x28: x28
STACK CFI e5bc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT e600 a5c .cfa: sp 0 + .ra: x30
STACK CFI e604 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI e60c x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI e61c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI e630 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI e640 x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI e6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e6fc .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT f060 16c .cfa: sp 0 + .ra: x30
STACK CFI f064 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f070 .cfa: x29 64 +
STACK CFI f074 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f0a4 x21: .cfa -32 + ^
STACK CFI f188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f18c .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT f1d0 ae0 .cfa: sp 0 + .ra: x30
STACK CFI f1d4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI f1dc .cfa: x29 208 +
STACK CFI f1e4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI f1fc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI f208 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI f218 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI f7a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f7a8 .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT fcb0 150 .cfa: sp 0 + .ra: x30
STACK CFI fcb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fcbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI fcc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fdac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI fdb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT fe00 1cc .cfa: sp 0 + .ra: x30
STACK CFI fe04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ff6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ff70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT ffd0 a4 .cfa: sp 0 + .ra: x30
STACK CFI ffd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ffe4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fff0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10024 x21: x21 x22: x22
STACK CFI 10030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10034 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 10040 x21: x21 x22: x22
STACK CFI 10044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 10058 x21: x21 x22: x22
STACK CFI INIT 10078 16c .cfa: sp 0 + .ra: x30
STACK CFI 1007c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 10084 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1008c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1009c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 10194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10198 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 101e8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 102bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 102dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 102e0 378 .cfa: sp 0 + .ra: x30
STACK CFI 102e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 102f0 x19: .cfa -16 + ^
STACK CFI 1040c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10410 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10574 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 105cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 105d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10658 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10688 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 106a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 106b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 106c0 618 .cfa: sp 0 + .ra: x30
STACK CFI 106c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 106d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 106d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 106e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10814 x19: x19 x20: x20
STACK CFI 10818 x23: x23 x24: x24
STACK CFI 10824 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 10828 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 109a0 x19: x19 x20: x20
STACK CFI 109a4 x23: x23 x24: x24
STACK CFI 109a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 109c4 x19: x19 x20: x20
STACK CFI 109cc x23: x23 x24: x24
STACK CFI 109d0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 109d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 10bec x19: x19 x20: x20
STACK CFI 10bf0 x23: x23 x24: x24
STACK CFI 10bf4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10c38 x19: x19 x20: x20
STACK CFI 10c3c x23: x23 x24: x24
STACK CFI 10c84 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10cbc x19: x19 x20: x20
STACK CFI 10cc0 x23: x23 x24: x24
STACK CFI 10cc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 10cd8 1c .cfa: sp 0 + .ra: x30
STACK CFI 10cdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10cec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10cf8 a60 .cfa: sp 0 + .ra: x30
STACK CFI 10cfc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 10d04 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 10d10 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 10d1c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 10d2c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 10d34 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 10f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10f70 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 11758 184 .cfa: sp 0 + .ra: x30
STACK CFI 1175c .cfa: sp 64 +
STACK CFI 11760 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11768 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11770 x21: .cfa -16 + ^
STACK CFI 117ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 117f0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 11860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11864 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 118e0 4ec .cfa: sp 0 + .ra: x30
STACK CFI 118e4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 118f0 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 11900 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 11954 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 11980 x25: .cfa -272 + ^
STACK CFI 11c0c x25: x25
STACK CFI 11c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11c64 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x29: .cfa -336 + ^
STACK CFI 11cc8 x25: x25
STACK CFI 11ccc x25: .cfa -272 + ^
STACK CFI 11d2c x25: x25
STACK CFI 11d98 x25: .cfa -272 + ^
STACK CFI 11dc4 x25: x25
STACK CFI 11dc8 x25: .cfa -272 + ^
STACK CFI INIT 11dd0 dc .cfa: sp 0 + .ra: x30
STACK CFI 11dd4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 11de4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 11e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11e80 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x29: .cfa -256 + ^
