MODULE Linux arm64 A0037F4058CE951940F8D31B581E17880 libXss.so.1
INFO CODE_ID 407F03A0CE58199540F8D31B581E1788A0AA1DE8
PUBLIC 1160 0 XScreenSaverQueryExtension
PUBLIC 11c0 0 XScreenSaverQueryVersion
PUBLIC 1308 0 XScreenSaverAllocInfo
PUBLIC 1310 0 XScreenSaverQueryInfo
PUBLIC 1468 0 XScreenSaverSelectInput
PUBLIC 1530 0 XScreenSaverSetAttributes
PUBLIC 1768 0 XScreenSaverUnsetAttributes
PUBLIC 1830 0 XScreenSaverRegister
PUBLIC 18e0 0 XScreenSaverUnregister
PUBLIC 1948 0 XScreenSaverGetRegistered
PUBLIC 1a60 0 XScreenSaverSuspend
STACK CFI INIT e48 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT e78 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb0 48 .cfa: sp 0 + .ra: x30
STACK CFI eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ebc x19: .cfa -16 + ^
STACK CFI ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ef8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT f10 74 .cfa: sp 0 + .ra: x30
STACK CFI f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT f88 ec .cfa: sp 0 + .ra: x30
STACK CFI f8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fa0 x21: .cfa -16 + ^
STACK CFI fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI fdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1000 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1078 e8 .cfa: sp 0 + .ra: x30
STACK CFI 107c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1084 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1090 x21: .cfa -16 + ^
STACK CFI 10c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 113c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 115c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1160 60 .cfa: sp 0 + .ra: x30
STACK CFI 1164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 116c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 11bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11c0 144 .cfa: sp 0 + .ra: x30
STACK CFI 11c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11d8 x23: .cfa -64 + ^
STACK CFI 11e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1308 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1310 158 .cfa: sp 0 + .ra: x30
STACK CFI 1314 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 131c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1328 x23: .cfa -64 + ^
STACK CFI 1334 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1428 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1468 c8 .cfa: sp 0 + .ra: x30
STACK CFI 146c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1474 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 147c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 151c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1520 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 152c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1530 238 .cfa: sp 0 + .ra: x30
STACK CFI 1534 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 153c x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 1544 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 1554 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 1560 x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 1668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 166c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^ x29: .cfa -384 + ^
STACK CFI INIT 1768 c4 .cfa: sp 0 + .ra: x30
STACK CFI 176c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1774 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 177c x21: .cfa -16 + ^
STACK CFI 17fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1800 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 181c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1830 ac .cfa: sp 0 + .ra: x30
STACK CFI 1834 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 183c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1848 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 18e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1918 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1948 118 .cfa: sp 0 + .ra: x30
STACK CFI 194c .cfa: sp 144 +
STACK CFI 1950 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1958 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1964 x23: .cfa -64 + ^
STACK CFI 1970 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 19d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 19d4 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1a60 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a74 x21: .cfa -16 + ^
STACK CFI 1af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
