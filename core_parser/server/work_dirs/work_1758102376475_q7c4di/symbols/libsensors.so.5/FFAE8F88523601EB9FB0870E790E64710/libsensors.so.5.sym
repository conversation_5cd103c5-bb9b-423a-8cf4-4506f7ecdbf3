MODULE Linux arm64 FFAE8F88523601EB9FB0870E790E64710 libsensors.so.5
INFO CODE_ID 888FAEFF3652EB019FB0870E790E6471C5ECE319
PUBLIC 2958 0 sensors_free_chip_name
PUBLIC 2960 0 sensors_parse_chip_name
PUBLIC 2ca0 0 sensors_snprintf_chip_name
PUBLIC 3490 0 sensors_strerror
PUBLIC 3ba0 0 sensors_get_label
PUBLIC 3d40 0 sensors_get_value
PUBLIC 3d50 0 sensors_set_value
PUBLIC 3f18 0 sensors_get_detected_chips
PUBLIC 3fa0 0 sensors_get_adapter_name
PUBLIC 40b0 0 sensors_get_features
PUBLIC 41d0 0 sensors_get_all_subfeatures
PUBLIC 4248 0 sensors_get_subfeature
PUBLIC 42d8 0 sensors_do_chip_sets
PUBLIC 4978 0 sensors_cleanup
PUBLIC 4d38 0 sensors_init
STACK CFI INIT 2898 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2908 48 .cfa: sp 0 + .ra: x30
STACK CFI 290c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2914 x19: .cfa -16 + ^
STACK CFI 294c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2950 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2958 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2960 340 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 296c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 297c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2a0c x23: .cfa -32 + ^
STACK CFI 2a98 x23: x23
STACK CFI 2ad0 x23: .cfa -32 + ^
STACK CFI 2b30 x23: x23
STACK CFI 2b38 x23: .cfa -32 + ^
STACK CFI 2bc4 x23: x23
STACK CFI 2bf4 x23: .cfa -32 + ^
STACK CFI 2c1c x23: x23
STACK CFI 2c2c x23: .cfa -32 + ^
STACK CFI 2c98 x23: x23
STACK CFI 2c9c x23: .cfa -32 + ^
STACK CFI INIT 2ca0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cbc x21: .cfa -16 + ^
STACK CFI 2d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2dc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e90 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2e94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ea0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2eac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2f48 208 .cfa: sp 0 + .ra: x30
STACK CFI 2f4c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2f54 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2f64 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2f78 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2f90 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2f94 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 30cc x19: x19 x20: x20
STACK CFI 30d4 x23: x23 x24: x24
STACK CFI 30d8 x25: x25 x26: x26
STACK CFI 30e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 30ec .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 3130 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 314c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI INIT 3150 70 .cfa: sp 0 + .ra: x30
STACK CFI 3154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 315c x21: .cfa -16 + ^
STACK CFI 3168 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 319c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31c0 40 .cfa: sp 0 + .ra: x30
STACK CFI 31c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31dc x21: .cfa -16 + ^
STACK CFI 31fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3200 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3204 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 320c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 321c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3228 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 326c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 32c0 ec .cfa: sp 0 + .ra: x30
STACK CFI 32c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 32d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3334 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3344 x25: .cfa -16 + ^
STACK CFI 3378 x25: x25
STACK CFI 3380 x25: .cfa -16 + ^
STACK CFI INIT 33b0 34 .cfa: sp 0 + .ra: x30
STACK CFI 33b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 33e8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3428 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3490 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 34c0 98 .cfa: sp 0 + .ra: x30
STACK CFI 34c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3558 ec .cfa: sp 0 + .ra: x30
STACK CFI 355c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3568 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 35a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 35b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3600 x19: x19 x20: x20
STACK CFI 3604 x23: x23 x24: x24
STACK CFI 3614 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 3618 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 362c x19: x19 x20: x20
STACK CFI 3630 x23: x23 x24: x24
STACK CFI INIT 3648 90 .cfa: sp 0 + .ra: x30
STACK CFI 364c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3658 x21: .cfa -16 + ^
STACK CFI 3664 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 36c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 36d8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3718 228 .cfa: sp 0 + .ra: x30
STACK CFI 371c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3728 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3738 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3750 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 37a8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 37b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3870 x19: x19 x20: x20
STACK CFI 3874 x27: x27 x28: x28
STACK CFI 38c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 38c8 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 38d0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 38ec x19: x19 x20: x20
STACK CFI 38f0 x27: x27 x28: x28
STACK CFI 38fc x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3908 x19: x19 x20: x20
STACK CFI 390c x27: x27 x28: x28
STACK CFI 3928 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 392c x19: x19 x20: x20
STACK CFI 3930 x27: x27 x28: x28
STACK CFI 3938 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 393c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 3940 25c .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 394c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3970 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 397c v8: .cfa -40 + ^
STACK CFI 39a0 x25: .cfa -48 + ^
STACK CFI 39fc x25: x25
STACK CFI 3a00 v8: v8
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a30 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 3a8c v8: v8
STACK CFI 3aa8 v8: .cfa -40 + ^
STACK CFI 3aac v8: v8
STACK CFI 3ab0 v8: .cfa -40 + ^
STACK CFI 3abc v8: v8
STACK CFI 3ac0 v8: .cfa -40 + ^
STACK CFI 3acc v8: v8
STACK CFI 3ad8 v8: .cfa -40 + ^
STACK CFI 3afc v8: v8
STACK CFI 3b04 v8: .cfa -40 + ^
STACK CFI 3b14 v8: v8
STACK CFI 3b20 v8: .cfa -40 + ^ x25: .cfa -48 + ^
STACK CFI 3b28 v8: v8
STACK CFI 3b2c x25: x25
STACK CFI 3b30 v8: .cfa -40 + ^
STACK CFI 3b38 v8: v8
STACK CFI 3b3c v8: .cfa -40 + ^
STACK CFI 3b44 v8: v8
STACK CFI 3b48 v8: .cfa -40 + ^
STACK CFI 3b54 v8: v8
STACK CFI 3b5c v8: .cfa -40 + ^
STACK CFI 3b64 v8: v8
STACK CFI 3b70 v8: .cfa -40 + ^
STACK CFI 3b84 v8: v8
STACK CFI 3b94 x25: .cfa -48 + ^
STACK CFI 3b98 v8: .cfa -40 + ^
STACK CFI INIT 3ba0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 3ba8 .cfa: sp 4192 +
STACK CFI 3bac .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 3bb4 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 3bc4 x25: .cfa -4128 + ^ x26: .cfa -4120 + ^
STACK CFI 3bd8 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 3be4 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 3c54 x21: x21 x22: x22
STACK CFI 3c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3c8c .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x25: .cfa -4128 + ^ x26: .cfa -4120 + ^ x29: .cfa -4192 + ^
STACK CFI 3c94 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 3d14 x21: x21 x22: x22
STACK CFI 3d18 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 3d3c x21: x21 x22: x22
STACK CFI INIT 3d40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d50 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d5c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3d68 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3d7c v8: .cfa -32 + ^
STACK CFI 3dd0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3dd8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3ddc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3e7c x19: x19 x20: x20
STACK CFI 3e80 x21: x21 x22: x22
STACK CFI 3e84 x27: x27 x28: x28
STACK CFI 3e8c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3e90 x19: x19 x20: x20
STACK CFI 3e94 x21: x21 x22: x22
STACK CFI 3e98 x27: x27 x28: x28
STACK CFI 3ed4 .cfa: sp 0 + .ra: .ra v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3ed8 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 3ee0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3eec x19: x19 x20: x20
STACK CFI 3ef0 x21: x21 x22: x22
STACK CFI 3ef4 x27: x27 x28: x28
STACK CFI 3f0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3f10 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3f14 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3f18 88 .cfa: sp 0 + .ra: x30
STACK CFI 3f1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f38 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3fa0 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40b0 11c .cfa: sp 0 + .ra: x30
STACK CFI 40b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 40bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 40c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 40f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 417c x19: x19 x20: x20
STACK CFI 4180 x21: x21 x22: x22
STACK CFI 4184 x27: x27 x28: x28
STACK CFI 4198 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 419c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 41a8 x19: x19 x20: x20
STACK CFI 41ac x27: x27 x28: x28
STACK CFI 41b4 x21: x21 x22: x22
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 41c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 41c8 x21: x21 x22: x22
STACK CFI INIT 41d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 41d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 423c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4248 90 .cfa: sp 0 + .ra: x30
STACK CFI 424c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4254 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42d8 234 .cfa: sp 0 + .ra: x30
STACK CFI 42dc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 42e4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 42f8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4330 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4508 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4510 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4520 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 4524 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 452c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 454c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 466c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4670 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 46e0 21c .cfa: sp 0 + .ra: x30
STACK CFI 46e8 .cfa: sp 4336 +
STACK CFI 46f8 .ra: .cfa -4328 + ^ x29: .cfa -4336 + ^
STACK CFI 4700 x21: .cfa -4304 + ^ x22: .cfa -4296 + ^
STACK CFI 4728 x23: .cfa -4288 + ^ x24: .cfa -4280 + ^
STACK CFI 4744 x19: .cfa -4320 + ^ x20: .cfa -4312 + ^
STACK CFI 474c x25: .cfa -4272 + ^ x26: .cfa -4264 + ^
STACK CFI 475c x27: .cfa -4256 + ^ x28: .cfa -4248 + ^
STACK CFI 4804 x19: x19 x20: x20
STACK CFI 4808 x25: x25 x26: x26
STACK CFI 480c x27: x27 x28: x28
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4848 .cfa: sp 4336 + .ra: .cfa -4328 + ^ x19: .cfa -4320 + ^ x20: .cfa -4312 + ^ x21: .cfa -4304 + ^ x22: .cfa -4296 + ^ x23: .cfa -4288 + ^ x24: .cfa -4280 + ^ x25: .cfa -4272 + ^ x26: .cfa -4264 + ^ x27: .cfa -4256 + ^ x28: .cfa -4248 + ^ x29: .cfa -4336 + ^
STACK CFI 4888 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 48a4 x19: .cfa -4320 + ^ x20: .cfa -4312 + ^
STACK CFI 48c0 x19: x19 x20: x20
STACK CFI 48c4 x19: .cfa -4320 + ^ x20: .cfa -4312 + ^ x25: .cfa -4272 + ^ x26: .cfa -4264 + ^ x27: .cfa -4256 + ^ x28: .cfa -4248 + ^
STACK CFI 48ec x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 48f0 x19: .cfa -4320 + ^ x20: .cfa -4312 + ^
STACK CFI 48f4 x25: .cfa -4272 + ^ x26: .cfa -4264 + ^
STACK CFI 48f8 x27: .cfa -4256 + ^ x28: .cfa -4248 + ^
STACK CFI INIT 4900 74 .cfa: sp 0 + .ra: x30
STACK CFI 4904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 490c x19: .cfa -16 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4934 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 495c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4978 3bc .cfa: sp 0 + .ra: x30
STACK CFI 497c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4984 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 498c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 49a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4aa4 x27: .cfa -16 + ^
STACK CFI 4c44 x27: x27
STACK CFI 4d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 4d38 150 .cfa: sp 0 + .ra: x30
STACK CFI 4d3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4da4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4de8 x21: x21 x22: x22
STACK CFI 4dec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4df4 x21: x21 x22: x22
STACK CFI 4df8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e00 x21: x21 x22: x22
STACK CFI 4e04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e08 x23: .cfa -16 + ^
STACK CFI 4e40 x21: x21 x22: x22
STACK CFI 4e44 x23: x23
STACK CFI 4e50 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4e7c x23: x23
STACK CFI 4e80 x23: .cfa -16 + ^
STACK CFI 4e84 x23: x23
STACK CFI INIT 4e88 10c .cfa: sp 0 + .ra: x30
STACK CFI 4e8c .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 4e9c x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 4ef0 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 4f34 x21: x21 x22: x22
STACK CFI 4f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f5c .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x29: .cfa -448 + ^
STACK CFI 4f60 x21: x21 x22: x22
STACK CFI 4f6c x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 4f90 x21: x21 x22: x22
STACK CFI INIT 4f98 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4f9c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4fa4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4fc4 x21: .cfa -64 + ^
STACK CFI 504c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5050 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5070 550 .cfa: sp 0 + .ra: x30
STACK CFI 5074 .cfa: sp 928 +
STACK CFI 5078 .ra: .cfa -920 + ^ x29: .cfa -928 + ^
STACK CFI 5080 x27: .cfa -848 + ^ x28: .cfa -840 + ^
STACK CFI 508c x21: .cfa -896 + ^ x22: .cfa -888 + ^
STACK CFI 50a0 x19: .cfa -912 + ^ x20: .cfa -904 + ^
STACK CFI 50b8 x23: .cfa -880 + ^ x24: .cfa -872 + ^
STACK CFI 50c4 x25: .cfa -864 + ^ x26: .cfa -856 + ^
STACK CFI 5218 x23: x23 x24: x24
STACK CFI 521c x25: x25 x26: x26
STACK CFI 524c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5250 .cfa: sp 928 + .ra: .cfa -920 + ^ x19: .cfa -912 + ^ x20: .cfa -904 + ^ x21: .cfa -896 + ^ x22: .cfa -888 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x28: .cfa -840 + ^ x29: .cfa -928 + ^
STACK CFI 5498 x23: x23 x24: x24
STACK CFI 549c x25: x25 x26: x26
STACK CFI 54a0 x23: .cfa -880 + ^ x24: .cfa -872 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^
STACK CFI 5584 x23: x23 x24: x24
STACK CFI 5588 x25: x25 x26: x26
STACK CFI 55b8 x23: .cfa -880 + ^ x24: .cfa -872 + ^
STACK CFI 55bc x25: .cfa -864 + ^ x26: .cfa -856 + ^
STACK CFI INIT 55c0 120 .cfa: sp 0 + .ra: x30
STACK CFI 55c4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 55d4 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 55f8 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 5608 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 5614 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 56c8 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x29: .cfa -352 + ^
STACK CFI INIT 56e0 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5768 7d8 .cfa: sp 0 + .ra: x30
STACK CFI 576c .cfa: sp 976 +
STACK CFI 5774 .ra: .cfa -968 + ^ x29: .cfa -976 + ^
STACK CFI 577c x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 579c x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 57a8 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI 57b4 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 57b8 x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 5c8c x19: x19 x20: x20
STACK CFI 5c94 x21: x21 x22: x22
STACK CFI 5c98 x23: x23 x24: x24
STACK CFI 5cc4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5cc8 .cfa: sp 976 + .ra: .cfa -968 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^ x29: .cfa -976 + ^
STACK CFI 5e40 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5e50 x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 5ea0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5ea4 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI 5ea8 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 5eac x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI INIT 5f40 158 .cfa: sp 0 + .ra: x30
STACK CFI 5f44 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5f4c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5f5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5f90 x23: .cfa -80 + ^
STACK CFI 6000 x23: x23
STACK CFI 6028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 602c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 6060 x23: x23
STACK CFI 6068 x23: .cfa -80 + ^
STACK CFI 6090 x23: x23
STACK CFI INIT 6098 128 .cfa: sp 0 + .ra: x30
STACK CFI 609c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 60ac x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 60b8 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 6150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6154 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT 61c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 61c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 61e0 128 .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 61f4 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 6218 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 6220 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 6238 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 62ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 62f0 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x29: .cfa -352 + ^
STACK CFI INIT 6308 8c .cfa: sp 0 + .ra: x30
STACK CFI 630c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 631c x19: .cfa -144 + ^
STACK CFI 638c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6390 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6398 50 .cfa: sp 0 + .ra: x30
STACK CFI 639c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 63c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 63e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 63ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63fc x19: .cfa -16 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 642c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6448 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6450 14c .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 6464 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 647c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 648c x23: .cfa -288 + ^
STACK CFI 6524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6528 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x29: .cfa -336 + ^
STACK CFI INIT 65a0 12c .cfa: sp 0 + .ra: x30
STACK CFI 65a4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 65b4 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 65d0 v8: .cfa -280 + ^
STACK CFI 65dc x21: .cfa -288 + ^
STACK CFI 6684 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6688 .cfa: sp 320 + .ra: .cfa -312 + ^ v8: .cfa -280 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 66d0 50 .cfa: sp 0 + .ra: x30
STACK CFI 66d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66e0 x19: .cfa -16 + ^
STACK CFI 66f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 66fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6720 5c .cfa: sp 0 + .ra: x30
STACK CFI 6724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6740 x19: .cfa -16 + ^
STACK CFI 6768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 676c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6780 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6828 308 .cfa: sp 0 + .ra: x30
STACK CFI 682c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6838 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6844 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 685c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6918 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 6920 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6928 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6a28 x25: x25 x26: x26
STACK CFI 6a2c x27: x27 x28: x28
STACK CFI 6a30 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6a80 x25: x25 x26: x26
STACK CFI 6a84 x27: x27 x28: x28
STACK CFI 6a8c x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6ac4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6ad4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6ae4 x25: x25 x26: x26
STACK CFI 6ae8 x27: x27 x28: x28
STACK CFI 6af0 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6b10 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6b14 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6b18 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6b24 x25: x25 x26: x26
STACK CFI 6b28 x27: x27 x28: x28
STACK CFI INIT 6b30 ae0 .cfa: sp 0 + .ra: x30
STACK CFI 6b38 .cfa: sp 5440 +
STACK CFI 6b4c .ra: .cfa -5432 + ^ x29: .cfa -5440 + ^
STACK CFI 6b5c x21: .cfa -5408 + ^ x22: .cfa -5400 + ^
STACK CFI 6b6c x19: .cfa -5424 + ^ x20: .cfa -5416 + ^
STACK CFI 6b90 x23: .cfa -5392 + ^ x24: .cfa -5384 + ^
STACK CFI 6b9c x25: .cfa -5376 + ^ x26: .cfa -5368 + ^
STACK CFI 6ba4 x27: .cfa -5360 + ^ x28: .cfa -5352 + ^
STACK CFI 6f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6f9c .cfa: sp 5440 + .ra: .cfa -5432 + ^ x19: .cfa -5424 + ^ x20: .cfa -5416 + ^ x21: .cfa -5408 + ^ x22: .cfa -5400 + ^ x23: .cfa -5392 + ^ x24: .cfa -5384 + ^ x25: .cfa -5376 + ^ x26: .cfa -5368 + ^ x27: .cfa -5360 + ^ x28: .cfa -5352 + ^ x29: .cfa -5440 + ^
STACK CFI INIT 7610 30 .cfa: sp 0 + .ra: x30
STACK CFI 7614 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7640 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 76c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 76cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 76d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7758 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7768 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7778 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7788 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7798 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77e8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7800 74 .cfa: sp 0 + .ra: x30
STACK CFI 7804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 780c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7814 x21: .cfa -16 + ^
STACK CFI 7864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7868 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7878 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7880 bc .cfa: sp 0 + .ra: x30
STACK CFI 7884 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 788c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 78c0 x21: .cfa -16 + ^
STACK CFI 78f8 x21: x21
STACK CFI 78fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7900 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 792c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7930 x21: .cfa -16 + ^
STACK CFI INIT 7940 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7944 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 794c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 795c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 79c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 79d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7a04 x23: x23 x24: x24
STACK CFI 7a08 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7a10 x23: x23 x24: x24
STACK CFI INIT 7a18 ac .cfa: sp 0 + .ra: x30
STACK CFI 7a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a24 x19: .cfa -16 + ^
STACK CFI 7ab0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ac8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ae0 x21: .cfa -16 + ^
STACK CFI 7b3c x21: x21
STACK CFI 7b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7b54 x21: x21
STACK CFI 7b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7b80 94 .cfa: sp 0 + .ra: x30
STACK CFI 7b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b8c x21: .cfa -16 + ^
STACK CFI 7b94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7bfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7c18 28 .cfa: sp 0 + .ra: x30
STACK CFI 7c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c24 x19: .cfa -16 + ^
STACK CFI 7c3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c40 bc .cfa: sp 0 + .ra: x30
STACK CFI 7c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c50 x19: .cfa -16 + ^
STACK CFI 7ce8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7cec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7cf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d00 e18 .cfa: sp 0 + .ra: x30
STACK CFI 7d04 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7d0c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7d28 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7f04 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8b18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b20 74 .cfa: sp 0 + .ra: x30
STACK CFI 8b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b30 x19: .cfa -16 + ^
STACK CFI 8b68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b98 94 .cfa: sp 0 + .ra: x30
STACK CFI 8b9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ba4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8c30 90 .cfa: sp 0 + .ra: x30
STACK CFI 8c34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8cc0 68 .cfa: sp 0 + .ra: x30
STACK CFI 8cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ccc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ce0 x21: .cfa -16 + ^
STACK CFI 8d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8d28 2c .cfa: sp 0 + .ra: x30
STACK CFI 8d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d34 x19: .cfa -16 + ^
STACK CFI 8d50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
