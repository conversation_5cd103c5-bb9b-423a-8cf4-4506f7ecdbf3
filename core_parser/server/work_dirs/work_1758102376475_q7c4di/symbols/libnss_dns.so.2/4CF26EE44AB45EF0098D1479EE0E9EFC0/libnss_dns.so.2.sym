MODULE Linux arm64 4CF26EE44AB45EF0098D1479EE0E9EFC0 libnss_dns.so.2
INFO CODE_ID E46EF24CB44AF05E098D1479EE0E9EFC49848284
PUBLIC 2300 0 _nss_dns_gethostbyname3_r
PUBLIC 23c8 0 _nss_dns_gethostbyname2_r
PUBLIC 2470 0 _nss_dns_gethostbyname_r
PUBLIC 2558 0 _nss_dns_gethostbyname4_r
PUBLIC 28b0 0 _nss_dns_gethostbyaddr2_r
PUBLIC 2ca8 0 _nss_dns_gethostbyaddr_r
PUBLIC 3220 0 _nss_dns_getnetbyname_r
PUBLIC 33a0 0 _nss_dns_getnetbyaddr_r
PUBLIC 3640 0 _nss_dns_getcanonname_r
STACK CFI INIT 1058 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1088 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 10cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10d4 x19: .cfa -16 + ^
STACK CFI 110c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1110 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1118 56c .cfa: sp 0 + .ra: x30
STACK CFI 111c .cfa: sp 1504 +
STACK CFI 1124 .ra: .cfa -1496 + ^ x29: .cfa -1504 + ^
STACK CFI 1134 x19: .cfa -1488 + ^ x20: .cfa -1480 + ^
STACK CFI 1154 x21: .cfa -1472 + ^ x22: .cfa -1464 + ^
STACK CFI 1174 x27: .cfa -1424 + ^ x28: .cfa -1416 + ^
STACK CFI 1188 x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI 11a0 x23: .cfa -1456 + ^ x24: .cfa -1448 + ^
STACK CFI 11cc x23: x23 x24: x24
STACK CFI 11e4 x25: x25 x26: x26
STACK CFI 11ec x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI 1294 x23: x23 x24: x24
STACK CFI 1298 x25: x25 x26: x26
STACK CFI 12d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 12d4 .cfa: sp 1504 + .ra: .cfa -1496 + ^ x19: .cfa -1488 + ^ x20: .cfa -1480 + ^ x21: .cfa -1472 + ^ x22: .cfa -1464 + ^ x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^ x27: .cfa -1424 + ^ x28: .cfa -1416 + ^ x29: .cfa -1504 + ^
STACK CFI 146c x23: x23 x24: x24
STACK CFI 1470 x25: x25 x26: x26
STACK CFI 1478 x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI 15a0 x23: x23 x24: x24
STACK CFI 15a4 x25: x25 x26: x26
STACK CFI 15c4 x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI 161c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1630 x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI 1654 x23: x23 x24: x24
STACK CFI 1668 x25: x25 x26: x26
STACK CFI 167c x23: .cfa -1456 + ^ x24: .cfa -1448 + ^
STACK CFI 1680 x25: .cfa -1440 + ^ x26: .cfa -1432 + ^
STACK CFI INIT 1688 a24 .cfa: sp 0 + .ra: x30
STACK CFI 168c .cfa: sp 1648 +
STACK CFI 1690 .ra: .cfa -1640 + ^ x29: .cfa -1648 + ^
STACK CFI 1698 x21: .cfa -1616 + ^ x22: .cfa -1608 + ^
STACK CFI 16ac x27: .cfa -1568 + ^ x28: .cfa -1560 + ^
STACK CFI 16f8 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^
STACK CFI 1704 x23: .cfa -1600 + ^ x24: .cfa -1592 + ^
STACK CFI 1708 x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 1758 x19: x19 x20: x20
STACK CFI 175c x23: x23 x24: x24
STACK CFI 1760 x25: x25 x26: x26
STACK CFI 1768 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 1784 x19: x19 x20: x20
STACK CFI 1788 x23: x23 x24: x24
STACK CFI 178c x25: x25 x26: x26
STACK CFI 17d0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 17d4 .cfa: sp 1648 + .ra: .cfa -1640 + ^ x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x21: .cfa -1616 + ^ x22: .cfa -1608 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^ x27: .cfa -1568 + ^ x28: .cfa -1560 + ^ x29: .cfa -1648 + ^
STACK CFI 1aa8 x19: x19 x20: x20
STACK CFI 1aac x23: x23 x24: x24
STACK CFI 1ab0 x25: x25 x26: x26
STACK CFI 1ab8 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 1e70 x19: x19 x20: x20
STACK CFI 1e74 x23: x23 x24: x24
STACK CFI 1e78 x25: x25 x26: x26
STACK CFI 1e80 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 1ebc x19: x19 x20: x20
STACK CFI 1ee4 x23: x23 x24: x24
STACK CFI 1ee8 x25: x25 x26: x26
STACK CFI 1ef0 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 1f8c x19: x19 x20: x20
STACK CFI 1f9c x23: x23 x24: x24
STACK CFI 1fa0 x25: x25 x26: x26
STACK CFI 1fb0 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 209c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 20a0 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^
STACK CFI 20a4 x23: .cfa -1600 + ^ x24: .cfa -1592 + ^
STACK CFI 20a8 x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI INIT 20b0 250 .cfa: sp 0 + .ra: x30
STACK CFI 20b4 .cfa: sp 1232 +
STACK CFI 20bc .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 20c0 .cfa: x29 1200 +
STACK CFI 20c4 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 20d4 x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 20f4 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 2100 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 2108 x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 214c .cfa: sp 1232 +
STACK CFI 216c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2170 .cfa: x29 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI INIT 2300 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2304 .cfa: sp 112 +
STACK CFI 2308 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2310 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2318 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2324 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2330 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 233c x27: .cfa -16 + ^
STACK CFI 23a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 23a4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 23c8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 23cc .cfa: sp 96 +
STACK CFI 23d0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 23fc x25: .cfa -16 + ^
STACK CFI 2428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 242c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 246c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2470 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2474 .cfa: sp 96 +
STACK CFI 2478 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2480 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2488 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2494 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 24d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24d8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2534 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2558 354 .cfa: sp 0 + .ra: x30
STACK CFI 255c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2560 .cfa: x29 192 +
STACK CFI 2564 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2570 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2588 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2594 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 25a4 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 25f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 25f8 .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 28b0 3f4 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 1248 +
STACK CFI 28b8 .ra: .cfa -1208 + ^ x29: .cfa -1216 + ^
STACK CFI 28bc .cfa: x29 1216 +
STACK CFI 28c0 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 28c8 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 28d4 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 28dc x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 28ec x19: .cfa -1200 + ^ x20: .cfa -1192 + ^
STACK CFI 2954 .cfa: sp 1248 +
STACK CFI 2974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2978 .cfa: x29 1216 + .ra: .cfa -1208 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^ x29: .cfa -1216 + ^
STACK CFI INIT 2ca8 20 .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 32 +
STACK CFI 2cb0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cc8 558 .cfa: sp 0 + .ra: x30
STACK CFI 2ccc .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 2cd4 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 2cdc x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 2d10 x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 2d20 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 2d5c x21: x21 x22: x22
STACK CFI 2d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d94 .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x29: .cfa -432 + ^
STACK CFI 2da4 x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 2dfc x21: x21 x22: x22
STACK CFI 2e04 x27: x27 x28: x28
STACK CFI 2e08 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 2fc0 x21: x21 x22: x22
STACK CFI 2fc8 x27: x27 x28: x28
STACK CFI 2fcc x21: .cfa -400 + ^ x22: .cfa -392 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 312c x21: x21 x22: x22
STACK CFI 3130 x27: x27 x28: x28
STACK CFI 3134 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 3150 x21: x21 x22: x22
STACK CFI 3154 x27: x27 x28: x28
STACK CFI 3174 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 3188 x21: x21 x22: x22
STACK CFI 318c x27: x27 x28: x28
STACK CFI 3198 x21: .cfa -400 + ^ x22: .cfa -392 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 31e8 x21: x21 x22: x22
STACK CFI 31ec x27: x27 x28: x28
STACK CFI 31f4 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 320c x21: x21 x22: x22
STACK CFI 3218 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 321c x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI INIT 3220 180 .cfa: sp 0 + .ra: x30
STACK CFI 3224 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3228 .cfa: x29 112 +
STACK CFI 322c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3238 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3250 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 325c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 326c x27: .cfa -32 + ^
STACK CFI 3330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3334 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 33a0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 33a4 .cfa: sp 1232 +
STACK CFI 33ac .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 33b0 .cfa: x29 1200 +
STACK CFI 33b4 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 33bc x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 33cc x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 33ec x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 3528 .cfa: sp 1232 +
STACK CFI 3548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 354c .cfa: x29 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^ x29: .cfa -1200 + ^
STACK CFI INIT 3640 2cc .cfa: sp 0 + .ra: x30
STACK CFI 3644 .cfa: sp 224 +
STACK CFI 3648 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3650 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3670 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3694 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 36a0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3754 x25: x25 x26: x26
STACK CFI 3758 x27: x27 x28: x28
STACK CFI 3788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 378c .cfa: sp 224 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 38d4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3904 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3908 x27: .cfa -112 + ^ x28: .cfa -104 + ^
