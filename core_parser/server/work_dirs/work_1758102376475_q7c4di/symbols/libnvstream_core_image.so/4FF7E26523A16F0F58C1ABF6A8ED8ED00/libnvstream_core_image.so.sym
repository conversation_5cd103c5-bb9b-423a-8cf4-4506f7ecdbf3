MODULE Linux arm64 4FF7E26523A16F0F58C1ABF6A8ED8ED00 libnvstream_core_image.so
INFO CODE_ID 65E2F74FA1230F6F58C1ABF6A8ED8ED0
PUBLIC b70 0 _init
PUBLIC c00 0 call_weak_fn
PUBLIC c14 0 deregister_tm_clones
PUBLIC c44 0 register_tm_clones
PUBLIC c80 0 __do_global_dtors_aux
PUBLIC cd0 0 frame_dummy
PUBLIC ce0 0 linvs::image::GetImageBufAttrList(linvs::buf::BufAttrList&, linvs::image::ImageType, int, int)
PUBLIC 10fc 0 _fini
STACK CFI INIT c14 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT c44 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT c80 50 .cfa: sp 0 + .ra: x30
STACK CFI c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c98 x19: .cfa -16 + ^
STACK CFI cc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ce0 41c .cfa: sp 0 + .ra: x30
STACK CFI ce4 .cfa: sp 560 +
STACK CFI cfc .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI d04 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI d10 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI d1c x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d80 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI d94 x25: .cfa -496 + ^
STACK CFI f30 x25: x25
STACK CFI f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f38 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI 10e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10e8 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
