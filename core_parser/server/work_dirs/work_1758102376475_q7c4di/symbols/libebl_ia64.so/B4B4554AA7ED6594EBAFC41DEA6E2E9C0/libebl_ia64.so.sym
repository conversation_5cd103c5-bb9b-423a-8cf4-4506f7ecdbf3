MODULE Linux arm64 B4B4554AA7ED6594EBAFC41DEA6E2E9C0 libebl_ia64.so
INFO CODE_ID 4A55B4B4EDA79465EBAFC41DEA6E2E9C66BD5EAB
PUBLIC db0 0 ia64_init
STACK CFI INIT bd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT c08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT c48 48 .cfa: sp 0 + .ra: x30
STACK CFI c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c54 x19: .cfa -16 + ^
STACK CFI c8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c98 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT cf0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d10 a0 .cfa: sp 0 + .ra: x30
STACK CFI d14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI d1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d88 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT db0 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT e70 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT f00 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT f18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT f28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f48 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT f80 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1030 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1048 694 .cfa: sp 0 + .ra: x30
STACK CFI 105c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1074 x19: .cfa -16 + ^
STACK CFI 1128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 112c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1394 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1398 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 141c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1424 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 16d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16e0 48c .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 16ec x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 16f8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1714 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 174c x21: x21 x22: x22
STACK CFI 1750 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1764 x21: x21 x22: x22
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1788 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI 17b8 x21: x21 x22: x22
STACK CFI 17bc x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 187c x21: x21 x22: x22
STACK CFI 1880 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1898 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1918 x25: x25 x26: x26
STACK CFI 1934 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^
STACK CFI 1938 x27: x27
STACK CFI 1940 x21: x21 x22: x22
STACK CFI 1944 x25: x25 x26: x26
STACK CFI 1948 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 194c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 198c x27: .cfa -128 + ^
STACK CFI 19cc x27: x27
STACK CFI 19e8 x25: x25 x26: x26
STACK CFI 1a1c x21: x21 x22: x22
STACK CFI 1a20 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1a68 x21: x21 x22: x22
STACK CFI 1a70 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1a78 x21: x21 x22: x22
STACK CFI 1a7c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1b34 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1b38 x21: x21 x22: x22
STACK CFI 1b3c x25: x25 x26: x26
STACK CFI 1b40 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^
STACK CFI 1b44 x21: x21 x22: x22
STACK CFI 1b48 x25: x25 x26: x26
STACK CFI 1b4c x27: x27
STACK CFI 1b54 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1b58 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1b5c x27: .cfa -128 + ^
STACK CFI 1b60 x25: x25 x26: x26 x27: x27
STACK CFI 1b68 x21: x21 x22: x22
STACK CFI INIT 1b70 36c .cfa: sp 0 + .ra: x30
STACK CFI 1b74 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1b7c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1b90 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1ba4 x23: .cfa -96 + ^
STACK CFI 1c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1c50 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
