MODULE Linux arm64 EDB3A097BC1EA42BC64B8D895C0C10470 libibnetdisc.so.5
INFO CODE_ID 97A0B3ED1EBC2BA4C64B8D895C0C10475AD70DDE
PUBLIC 21b0 0 ibnd_get_chassis_type
PUBLIC 2288 0 ibnd_get_chassis_slot_str
PUBLIC 2378 0 ibnd_is_xsigo_guid
PUBLIC 2390 0 ibnd_is_xsigo_hca
PUBLIC 23a8 0 ibnd_is_xsigo_tca
PUBLIC 24f8 0 ibnd_get_chassis_guid
PUBLIC 4160 0 ibnd_find_node_guid
PUBLIC 5398 0 ibnd_destroy_fabric
PUBLIC 5400 0 ibnd_discover_fabric
PUBLIC 57a0 0 ibnd_iter_nodes
PUBLIC 5850 0 ibnd_iter_nodes_type
PUBLIC 5968 0 ibnd_find_port_lid
PUBLIC 59a8 0 ibnd_find_port_guid
PUBLIC 5a50 0 ibnd_find_port_dr
PUBLIC 5b80 0 ibnd_find_node_dr
PUBLIC 5b98 0 ibnd_iter_ports
PUBLIC 6a50 0 ibnd_load_fabric
PUBLIC 7108 0 ibnd_cache_fabric
STACK CFI INIT 18d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1908 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1948 48 .cfa: sp 0 + .ra: x30
STACK CFI 194c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1954 x19: .cfa -16 + ^
STACK CFI 198c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1998 f8 .cfa: sp 0 + .ra: x30
STACK CFI 199c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19ac x19: .cfa -16 + ^
STACK CFI 19d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a90 104 .cfa: sp 0 + .ra: x30
STACK CFI 1a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1aa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b98 14c .cfa: sp 0 + .ra: x30
STACK CFI 1b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ba4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bb8 x21: .cfa -16 + ^
STACK CFI 1c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1ce8 20c .cfa: sp 0 + .ra: x30
STACK CFI 1cec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1cf4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1cfc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1d08 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1d14 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1d1c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1e00 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 1ef8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1efc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f0c x19: .cfa -16 + ^
STACK CFI 1f40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1fa0 20c .cfa: sp 0 + .ra: x30
STACK CFI 1fa4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1fac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1fb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1fc4 x27: .cfa -16 + ^
STACK CFI 1fd0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2004 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 21b0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 21b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21c0 x19: .cfa -16 + ^
STACK CFI 21f4 x19: x19
STACK CFI 21f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 221c x19: x19
STACK CFI 2220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2228 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2238 x19: x19
STACK CFI 223c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2240 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2244 x19: x19
STACK CFI 224c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2250 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2288 f0 .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2298 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22ac x21: .cfa -16 + ^
STACK CFI 2324 x19: x19 x20: x20
STACK CFI 2328 x21: x21
STACK CFI 232c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2330 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2334 x19: x19 x20: x20
STACK CFI 2338 x21: x21
STACK CFI 2340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2344 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2378 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2390 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c0 134 .cfa: sp 0 + .ra: x30
STACK CFI 23c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 23e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 249c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 24b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 24f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 24f8 90 .cfa: sp 0 + .ra: x30
STACK CFI 24fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2504 x19: .cfa -16 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2540 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2588 1254 .cfa: sp 0 + .ra: x30
STACK CFI 258c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2598 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 25bc x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 25d0 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2ba4 x27: x27 x28: x28
STACK CFI 2cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2cb8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 2dec x27: x27 x28: x28
STACK CFI 2e10 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2e50 x27: x27 x28: x28
STACK CFI 2eb4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3080 x27: x27 x28: x28
STACK CFI 30c8 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 313c x27: x27 x28: x28
STACK CFI 3174 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3194 x27: x27 x28: x28
STACK CFI 319c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3220 x27: x27 x28: x28
STACK CFI 3230 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 333c x27: x27 x28: x28
STACK CFI 3358 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3698 x27: x27 x28: x28
STACK CFI 369c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 36cc x27: x27 x28: x28
STACK CFI 36d4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3734 x27: x27 x28: x28
STACK CFI 3764 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 37e0 208 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 304 +
STACK CFI 37e8 .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 37f0 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 3800 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 381c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 38cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 38d0 .cfa: sp 304 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 38fc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 3900 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 39d4 x25: x25 x26: x26
STACK CFI 39d8 x27: x27 x28: x28
STACK CFI 39e0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 39e4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 39e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a18 4c .cfa: sp 0 + .ra: x30
STACK CFI 3a20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3a68 394 .cfa: sp 0 + .ra: x30
STACK CFI 3a6c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 3a78 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 3a80 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 3a98 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 3aa4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 3adc x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 3b30 x27: x27 x28: x28
STACK CFI 3b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3b60 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 3d50 x27: x27 x28: x28
STACK CFI 3d88 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 3db4 x27: x27 x28: x28
STACK CFI 3db8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 3e00 35c .cfa: sp 0 + .ra: x30
STACK CFI 3e04 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3e0c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3e18 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3e30 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3e44 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 3e6c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 3e98 x25: x25 x26: x26
STACK CFI 3ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3ec8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 40b0 x25: x25 x26: x26
STACK CFI 40e8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4114 x25: x25 x26: x26
STACK CFI 4118 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 4160 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4178 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4190 x19: .cfa -16 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4208 ac .cfa: sp 0 + .ra: x30
STACK CFI 427c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42b8 ac .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4370 58 .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 437c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4384 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 43c8 80 .cfa: sp 0 + .ra: x30
STACK CFI 43cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 443c x21: x21 x22: x22
STACK CFI 4444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4448 70c .cfa: sp 0 + .ra: x30
STACK CFI 444c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 4454 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 4460 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 4468 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 4470 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 4478 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 4764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4768 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 4b58 10c .cfa: sp 0 + .ra: x30
STACK CFI 4b5c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b6c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4b78 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4b90 x27: .cfa -16 + ^
STACK CFI 4ba8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4bf0 x23: x23 x24: x24
STACK CFI 4bf4 x27: x27
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4c0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4c68 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4cb8 64c .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 240 +
STACK CFI 4cc0 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4cc8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4cd8 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4ce0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4cfc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4d04 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 4f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4f20 .cfa: sp 240 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 5308 34 .cfa: sp 0 + .ra: x30
STACK CFI 530c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 531c x19: .cfa -16 + ^
STACK CFI 5338 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5340 58 .cfa: sp 0 + .ra: x30
STACK CFI 5344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 534c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5398 64 .cfa: sp 0 + .ra: x30
STACK CFI 53a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5400 39c .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 624 +
STACK CFI 540c .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 5418 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 5424 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 5434 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 543c x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 550c x27: .cfa -544 + ^
STACK CFI 5618 x27: x27
STACK CFI 5648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 564c .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x29: .cfa -624 + ^
STACK CFI 5664 x27: .cfa -544 + ^
STACK CFI 5690 x27: x27
STACK CFI 5694 x27: .cfa -544 + ^
STACK CFI 5724 x27: x27
STACK CFI 5750 x27: .cfa -544 + ^
STACK CFI 5784 x27: x27
STACK CFI 5798 x27: .cfa -544 + ^
STACK CFI INIT 57a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 57a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57c0 x21: .cfa -16 + ^
STACK CFI 57e0 x21: x21
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5800 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 581c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5850 118 .cfa: sp 0 + .ra: x30
STACK CFI 5858 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5860 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 586c x21: .cfa -16 + ^
STACK CFI 589c x21: x21
STACK CFI 58a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 58dc x21: x21
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5910 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5918 x21: x21
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5968 40 .cfa: sp 0 + .ra: x30
STACK CFI 596c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5978 x19: .cfa -16 + ^
STACK CFI 599c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 59a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 59a8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 59c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59d8 x19: .cfa -16 + ^
STACK CFI 5a18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a50 130 .cfa: sp 0 + .ra: x30
STACK CFI 5a54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5a60 x21: .cfa -96 + ^
STACK CFI 5a80 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5ae4 x19: x19 x20: x20
STACK CFI 5b00 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5b04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI 5b08 x19: x19 x20: x20
STACK CFI 5b7c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI INIT 5b80 18 .cfa: sp 0 + .ra: x30
STACK CFI 5b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b98 d8 .cfa: sp 0 + .ra: x30
STACK CFI 5ba0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ba8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5bb0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5bbc x23: .cfa -16 + ^
STACK CFI 5c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5c18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5c70 e8 .cfa: sp 0 + .ra: x30
STACK CFI 5c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d58 170 .cfa: sp 0 + .ra: x30
STACK CFI 5d60 .cfa: sp 4176 +
STACK CFI 5d68 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 5d74 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 5d80 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 5da8 x23: .cfa -4128 + ^
STACK CFI 5eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5eb8 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 5ec8 98 .cfa: sp 0 + .ra: x30
STACK CFI 5ed0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ed8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ee0 x21: .cfa -16 + ^
STACK CFI 5f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5f60 348 .cfa: sp 0 + .ra: x30
STACK CFI 5f68 .cfa: sp 4176 +
STACK CFI 5f6c .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 5f74 x23: .cfa -4128 + ^ x24: .cfa -4120 + ^
STACK CFI 5f88 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 5fac x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 60ec x19: x19 x20: x20
STACK CFI 6114 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6118 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x29: .cfa -4176 + ^
STACK CFI 6230 x19: x19 x20: x20
STACK CFI 6234 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6264 x19: x19 x20: x20
STACK CFI 6280 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6284 x19: x19 x20: x20
STACK CFI INIT 62a8 15c .cfa: sp 0 + .ra: x30
STACK CFI 62b0 .cfa: sp 4176 +
STACK CFI 62c4 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 62cc x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 62d8 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 6300 x23: .cfa -4128 + ^
STACK CFI 634c x23: x23
STACK CFI 6354 x23: .cfa -4128 + ^
STACK CFI 6358 x23: x23
STACK CFI 6384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6388 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI 63b4 x23: x23
STACK CFI 6400 x23: .cfa -4128 + ^
STACK CFI INIT 6408 104 .cfa: sp 0 + .ra: x30
STACK CFI 6410 .cfa: sp 4176 +
STACK CFI 6414 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 641c x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6428 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 6450 x23: .cfa -4128 + ^
STACK CFI 6504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6508 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 6510 13c .cfa: sp 0 + .ra: x30
STACK CFI 6518 .cfa: sp 4176 +
STACK CFI 651c .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 6524 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6534 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 6554 x23: .cfa -4128 + ^
STACK CFI 65d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 65d8 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 6650 208 .cfa: sp 0 + .ra: x30
STACK CFI 6658 .cfa: sp 4176 +
STACK CFI 665c .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 6664 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 6678 x23: .cfa -4128 + ^ x24: .cfa -4120 + ^
STACK CFI 669c x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 679c x19: x19 x20: x20
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 67c8 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x29: .cfa -4176 + ^
STACK CFI 67e0 x19: x19 x20: x20
STACK CFI 67e4 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6814 x19: x19 x20: x20
STACK CFI 6830 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 6834 x19: x19 x20: x20
STACK CFI INIT 6858 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 6860 .cfa: sp 4176 +
STACK CFI 6864 .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 6870 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 6894 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x23: .cfa -4128 + ^
STACK CFI 6a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6a38 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 6a50 6b4 .cfa: sp 0 + .ra: x30
STACK CFI 6a54 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6a5c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6a78 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6a84 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6b18 x19: x19 x20: x20
STACK CFI 6b40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b44 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 6b8c x19: x19 x20: x20
STACK CFI 6bc8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6c28 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6c30 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6de8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6e00 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6e98 x25: x25 x26: x26
STACK CFI 6e9c x27: x27 x28: x28
STACK CFI 6ea0 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6ee0 x19: x19 x20: x20
STACK CFI 6ee4 x25: x25 x26: x26
STACK CFI 6ee8 x27: x27 x28: x28
STACK CFI 6eec x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6ef0 x25: x25 x26: x26
STACK CFI 6ef4 x27: x27 x28: x28
STACK CFI 6ef8 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6f58 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6f8c x19: x19 x20: x20
STACK CFI 6f90 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6fb0 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6fe0 x25: x25 x26: x26
STACK CFI 6fe4 x27: x27 x28: x28
STACK CFI 7018 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7048 x25: x25 x26: x26
STACK CFI 704c x27: x27 x28: x28
STACK CFI 7050 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7080 x25: x25 x26: x26
STACK CFI 7084 x27: x27 x28: x28
STACK CFI 7088 x19: x19 x20: x20
STACK CFI 708c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 7090 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7094 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7098 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 70cc x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 70fc x25: x25 x26: x26
STACK CFI 7100 x27: x27 x28: x28
STACK CFI INIT 7108 30c .cfa: sp 0 + .ra: x30
STACK CFI 710c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 7114 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7120 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7160 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7184 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 71b0 x21: x21 x22: x22
STACK CFI 71c4 x19: x19 x20: x20
STACK CFI 71e8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 71ec .cfa: sp 224 + .ra: .cfa -216 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 7220 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7290 x19: x19 x20: x20
STACK CFI 7294 x21: x21 x22: x22
STACK CFI 7298 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 72b0 x19: x19 x20: x20
STACK CFI 7354 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7388 x19: x19 x20: x20
STACK CFI 73c8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 73cc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7410 x21: x21 x22: x22
STACK CFI INIT 7418 198 .cfa: sp 0 + .ra: x30
STACK CFI 741c .cfa: sp 1104 +
STACK CFI 7420 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 7428 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 7434 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 7450 x23: .cfa -1056 + ^
STACK CFI 74f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 74f8 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x29: .cfa -1104 + ^
STACK CFI INIT 75b0 33c .cfa: sp 0 + .ra: x30
STACK CFI 75b4 .cfa: sp 448 +
STACK CFI 75b8 .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 75c0 x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 75cc x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 75e4 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 7620 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 76e4 x27: .cfa -352 + ^
STACK CFI 7748 x27: x27
STACK CFI 7784 x25: x25 x26: x26
STACK CFI 77b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 77b4 .cfa: sp 448 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x29: .cfa -432 + ^
STACK CFI 7854 x27: .cfa -352 + ^
STACK CFI 7870 x27: x27
STACK CFI 7874 x25: x25 x26: x26
STACK CFI 78a8 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 78dc x25: x25 x26: x26
STACK CFI 78e4 x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 78e8 x27: .cfa -352 + ^
STACK CFI INIT 78f0 214 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 78fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7908 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7918 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7924 x25: .cfa -16 + ^
STACK CFI 7a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7a84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7ac8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7b08 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 7b0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7b14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7b20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7b2c x23: .cfa -16 + ^
STACK CFI 7bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7bdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7cb8 10c .cfa: sp 0 + .ra: x30
STACK CFI 7cbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7cc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7cd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7db4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7dc8 38 .cfa: sp 0 + .ra: x30
STACK CFI 7dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7dd4 x19: .cfa -16 + ^
STACK CFI 7dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e00 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e40 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e90 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ef8 50 .cfa: sp 0 + .ra: x30
STACK CFI 7efc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7f04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7f18 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7f3c x21: x21 x22: x22
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f48 2c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8208 3e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85e8 34 .cfa: sp 0 + .ra: x30
STACK CFI 85ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8620 88 .cfa: sp 0 + .ra: x30
STACK CFI 8624 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 862c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8634 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8648 x23: .cfa -16 + ^
STACK CFI 8698 x23: x23
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 86a8 154 .cfa: sp 0 + .ra: x30
STACK CFI 86ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 86b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 86bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 86c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 86d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 86dc x27: .cfa -16 + ^
STACK CFI 87d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 87d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
