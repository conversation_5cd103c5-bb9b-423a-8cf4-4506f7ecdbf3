MODULE Linux arm64 EED95526D812C488ADF3BDF89D1E6FDC0 libgdbm.so.6
INFO CODE_ID 2655D9EE12D888C4ADF3BDF89D1E6FDC0C3E0F92
PUBLIC 2c98 0 gdbm_close
PUBLIC 2de0 0 gdbm_count
PUBLIC 2ea8 0 gdbm_delete
PUBLIC 3180 0 _gdbm_dump_ascii
PUBLIC 3540 0 gdbm_dump_to_file
PUBLIC 35e8 0 gdbm_dump
PUBLIC 36f8 0 gdbm_errno_location
PUBLIC 3720 0 gdbm_set_errno
PUBLIC 3798 0 gdbm_last_errno
PUBLIC 37c8 0 gdbm_last_syserr
PUBLIC 37f8 0 gdbm_needs_recovery
PUBLIC 3810 0 gdbm_clear_error
PUBLIC 3848 0 gdbm_strerror
PUBLIC 3888 0 gdbm_db_strerror
PUBLIC 3938 0 gdbm_check_syserr
PUBLIC 3958 0 gdbm_exists
PUBLIC 39e0 0 gdbm_export_to_file
PUBLIC 3c40 0 gdbm_export
PUBLIC 3d28 0 gdbm_fdesc
PUBLIC 3d30 0 gdbm_fetch
PUBLIC 42b0 0 get_len
PUBLIC 4368 0 read_record
PUBLIC 4548 0 _gdbm_load_file
PUBLIC 49f8 0 gdbm_load_bdb_dump
PUBLIC 4cb8 0 gdbm_load_from_file
PUBLIC 4e50 0 gdbm_load
PUBLIC 5068 0 gdbm_avail_table_valid_p
PUBLIC 5138 0 gdbm_avail_block_validate
PUBLIC 51a0 0 gdbm_bucket_avail_table_validate
PUBLIC 51f0 0 _gdbm_validate_header
PUBLIC 5270 0 gdbm_fd_open
PUBLIC 5a18 0 gdbm_open
PUBLIC 5ad8 0 _gdbm_cache_entry_invalidate
PUBLIC 5b00 0 _gdbm_init_cache
PUBLIC 5be0 0 gdbm_import_from_file
PUBLIC 5f18 0 gdbm_import
PUBLIC 5f90 0 gdbm_reorganize
PUBLIC 6188 0 gdbm_firstkey
PUBLIC 6238 0 gdbm_nextkey
PUBLIC 6810 0 gdbm_setopt
PUBLIC 6880 0 gdbm_store
PUBLIC 6c20 0 gdbm_sync
PUBLIC 6c70 0 _gdbm_base64_encode
PUBLIC 6df0 0 _gdbm_base64_decode
PUBLIC 6f88 0 _gdbm_new_bucket
PUBLIC 6fc8 0 gdbm_dir_entry_valid_p
PUBLIC 7008 0 _gdbm_read_bucket_at
PUBLIC 7100 0 _gdbm_write_bucket
PUBLIC 71c0 0 _gdbm_get_bucket
PUBLIC 7438 0 _gdbm_split_bucket
PUBLIC 7be0 0 _gdbm_put_av_elem
PUBLIC 7dc0 0 _gdbm_free
PUBLIC 8208 0 _gdbm_alloc
PUBLIC 8520 0 gdbm_bucket_element_valid_p
PUBLIC 8598 0 _gdbm_read_entry
PUBLIC 8778 0 _gdbm_findkey
PUBLIC 89f0 0 _gdbm_full_read
PUBLIC 8ab0 0 _gdbm_full_write
PUBLIC 8b80 0 _gdbm_file_extend
PUBLIC 8c98 0 _gdbm_hash
PUBLIC 8d08 0 _gdbm_bucket_dir
PUBLIC 8d20 0 _gdbm_hash_key
PUBLIC 8d90 0 _gdbm_unlock_file
PUBLIC 8e40 0 _gdbm_lock_file
PUBLIC 8f68 0 _gdbm_file_size
PUBLIC 8ff0 0 _gdbm_mapped_unmap
PUBLIC 9028 0 _gdbm_internal_remap
PUBLIC 90f0 0 _gdbm_mapped_remap
PUBLIC 92b0 0 _gdbm_mapped_init
PUBLIC 92d0 0 _gdbm_mapped_read
PUBLIC 9458 0 _gdbm_mapped_write
PUBLIC 95e8 0 _gdbm_mapped_lseek
PUBLIC 96e8 0 _gdbm_mapped_sync
PUBLIC 9758 0 gdbm_copy_meta
PUBLIC 9838 0 _gdbm_next_bucket_dir
PUBLIC 9890 0 gdbm_recover
PUBLIC a1e8 0 _gdbm_fatal
PUBLIC a210 0 _gdbm_end_update
PUBLIC a3f8 0 gdbm_version_cmp
STACK CFI INIT 2bd8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c48 48 .cfa: sp 0 + .ra: x30
STACK CFI 2c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c54 x19: .cfa -16 + ^
STACK CFI 2c8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c98 148 .cfa: sp 0 + .ra: x30
STACK CFI 2c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cb4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2de0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2de4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2df4 x23: .cfa -16 + ^
STACK CFI 2e04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e68 x19: x19 x20: x20
STACK CFI 2e6c x21: x21 x22: x22
STACK CFI 2e74 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2e78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2e7c x19: x19 x20: x20
STACK CFI 2e84 x21: x21 x22: x22
STACK CFI 2e90 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2e94 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ea8 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2eac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2eb4 x21: .cfa -16 + ^
STACK CFI 2ec8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3034 x19: x19 x20: x20
STACK CFI 3044 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 304c x19: x19 x20: x20
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 305c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 306c x19: x19 x20: x20
STACK CFI INIT 3080 100 .cfa: sp 0 + .ra: x30
STACK CFI 3084 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 308c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3098 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 30a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 317c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3180 3bc .cfa: sp 0 + .ra: x30
STACK CFI 3184 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 318c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 31b0 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 31d0 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 32fc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3314 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 33e8 x23: x23 x24: x24
STACK CFI 33ec x25: x25 x26: x26
STACK CFI 33f0 x21: x21 x22: x22
STACK CFI 3420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 3424 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 348c x21: x21 x22: x22
STACK CFI 3490 x23: x23 x24: x24
STACK CFI 3494 x25: x25 x26: x26
STACK CFI 3498 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 349c x23: x23 x24: x24
STACK CFI 34a0 x25: x25 x26: x26
STACK CFI 34fc x21: x21 x22: x22
STACK CFI 3500 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3520 x21: x21 x22: x22
STACK CFI 3524 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 352c x21: x21 x22: x22
STACK CFI 3530 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3534 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3538 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI INIT 3540 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3554 x19: .cfa -16 + ^
STACK CFI 3578 x19: x19
STACK CFI 3580 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3584 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3590 x19: x19
STACK CFI 3594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 35bc x19: x19
STACK CFI 35c0 x19: .cfa -16 + ^
STACK CFI 35d4 x19: x19
STACK CFI INIT 35e8 110 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3600 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 366c x21: x21 x22: x22
STACK CFI 3678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 367c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 369c x21: x21 x22: x22
STACK CFI 36a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 36d4 x21: x21 x22: x22
STACK CFI 36ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 36f8 28 .cfa: sp 0 + .ra: x30
STACK CFI 36fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 371c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3720 74 .cfa: sp 0 + .ra: x30
STACK CFI 3724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 372c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3740 x21: .cfa -16 + ^
STACK CFI 3770 x21: x21
STACK CFI 3784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3788 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3798 30 .cfa: sp 0 + .ra: x30
STACK CFI 37a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37c8 30 .cfa: sp 0 + .ra: x30
STACK CFI 37d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37f8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3810 34 .cfa: sp 0 + .ra: x30
STACK CFI 3818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3820 x19: .cfa -16 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3848 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3888 ac .cfa: sp 0 + .ra: x30
STACK CFI 388c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3894 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 38cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3928 x21: x21 x22: x22
STACK CFI 392c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3930 x21: x21 x22: x22
STACK CFI INIT 3938 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3958 84 .cfa: sp 0 + .ra: x30
STACK CFI 395c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3968 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3994 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 39b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 39e0 260 .cfa: sp 0 + .ra: x30
STACK CFI 39e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 39ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 39f8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3a10 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3a30 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3a9c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3ba4 x19: x19 x20: x20
STACK CFI 3ba8 x27: x27 x28: x28
STACK CFI 3bac x19: .cfa -96 + ^ x20: .cfa -88 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3bb0 x27: x27 x28: x28
STACK CFI 3bc8 x19: x19 x20: x20
STACK CFI 3bf0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3bf4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3c14 x19: x19 x20: x20
STACK CFI 3c18 x27: x27 x28: x28
STACK CFI 3c38 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c3c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 3c40 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3c44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c60 x21: .cfa -16 + ^
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3c98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3cf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3d28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d30 124 .cfa: sp 0 + .ra: x30
STACK CFI 3d34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e58 14c .cfa: sp 0 + .ra: x30
STACK CFI 3e5c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3e64 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3e70 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3e90 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3f54 x23: x23 x24: x24
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f7c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 3f8c x23: x23 x24: x24
STACK CFI 3f90 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3f98 x23: x23 x24: x24
STACK CFI 3fa0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI INIT 3fa8 264 .cfa: sp 0 + .ra: x30
STACK CFI 3fac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4204 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4210 9c .cfa: sp 0 + .ra: x30
STACK CFI 4214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 421c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 422c x21: .cfa -16 + ^
STACK CFI 4280 x21: x21
STACK CFI 428c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4290 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 429c x21: x21
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 42b0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 42b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4350 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4368 1dc .cfa: sp 0 + .ra: x30
STACK CFI 436c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4374 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4384 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4398 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 43d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43d8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 43e0 x25: .cfa -48 + ^
STACK CFI 44f4 x25: x25
STACK CFI 451c x25: .cfa -48 + ^
STACK CFI 4520 x25: x25
STACK CFI 4524 x25: .cfa -48 + ^
STACK CFI 452c x25: x25
STACK CFI 4530 x25: .cfa -48 + ^
STACK CFI 4538 x25: x25
STACK CFI 4540 x25: .cfa -48 + ^
STACK CFI INIT 4548 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 454c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 4554 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 4560 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4578 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 4580 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 4588 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4674 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 49f8 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 4a0c x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 4a44 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 4a8c x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 4a98 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 4aa0 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 4be0 x23: x23 x24: x24
STACK CFI 4be4 x25: x25 x26: x26
STACK CFI 4be8 x27: x27 x28: x28
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c14 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI 4c80 x23: x23 x24: x24
STACK CFI 4c84 x25: x25 x26: x26
STACK CFI 4c88 x27: x27 x28: x28
STACK CFI 4c8c x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 4c94 x23: x23 x24: x24
STACK CFI 4c98 x25: x25 x26: x26
STACK CFI 4c9c x27: x27 x28: x28
STACK CFI 4ca0 x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 4ca8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4cac x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 4cb0 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 4cb4 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 4cb8 198 .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4cc4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4cd0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4cf0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4d08 x25: .cfa -144 + ^
STACK CFI 4d48 x25: x25
STACK CFI 4d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d78 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x29: .cfa -208 + ^
STACK CFI 4de8 x25: x25
STACK CFI 4dec x25: .cfa -144 + ^
STACK CFI 4e28 x25: x25
STACK CFI 4e2c x25: .cfa -144 + ^
STACK CFI 4e44 x25: x25
STACK CFI 4e4c x25: .cfa -144 + ^
STACK CFI INIT 4e50 90 .cfa: sp 0 + .ra: x30
STACK CFI 4e54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e7c x23: .cfa -16 + ^
STACK CFI 4ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4ecc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ee0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ef0 178 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5068 cc .cfa: sp 0 + .ra: x30
STACK CFI 506c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5078 x19: .cfa -16 + ^
STACK CFI 50f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 50f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5130 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5138 68 .cfa: sp 0 + .ra: x30
STACK CFI 513c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5148 x19: .cfa -16 + ^
STACK CFI 5180 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5184 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 519c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 51a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51b0 x19: .cfa -16 + ^
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 51f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 51f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 51fc x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5220 x21: .cfa -160 + ^
STACK CFI 5264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5268 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5270 7a4 .cfa: sp 0 + .ra: x30
STACK CFI 5274 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 527c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 528c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 52a0 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 52b0 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 56c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 56cc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x29: .cfa -288 + ^
STACK CFI INIT 5a18 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a7c x21: x21 x22: x22
STACK CFI 5a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5ad0 x21: x21 x22: x22
STACK CFI 5ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5ad8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b00 dc .cfa: sp 0 + .ra: x30
STACK CFI 5b04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 5b2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5b3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ba4 x23: x23 x24: x24
STACK CFI 5bb4 x21: x21 x22: x22
STACK CFI 5bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5bd4 x21: x21 x22: x22
STACK CFI 5bd8 x23: x23 x24: x24
STACK CFI INIT 5be0 334 .cfa: sp 0 + .ra: x30
STACK CFI 5be4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5bf8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5c10 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5c18 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5c84 x21: x21 x22: x22
STACK CFI 5cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 5cb0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 5ce4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5ce8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5e44 x21: x21 x22: x22
STACK CFI 5e48 x23: x23 x24: x24
STACK CFI 5e4c x25: x25 x26: x26
STACK CFI 5e50 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5e7c x21: x21 x22: x22
STACK CFI 5e80 x23: x23 x24: x24
STACK CFI 5e84 x25: x25 x26: x26
STACK CFI 5e88 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5e94 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5ea8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5ebc x21: x21 x22: x22
STACK CFI 5ec0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5ee0 x21: x21 x22: x22
STACK CFI 5ee8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5eec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5ef0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5f08 x21: x21 x22: x22
STACK CFI 5f0c x23: x23 x24: x24
STACK CFI 5f10 x25: x25 x26: x26
STACK CFI INIT 5f18 78 .cfa: sp 0 + .ra: x30
STACK CFI 5f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f38 x21: .cfa -16 + ^
STACK CFI 5f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5f90 70 .cfa: sp 0 + .ra: x30
STACK CFI 5f94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5fa0 x19: .cfa -112 + ^
STACK CFI 5fe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5fe8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6000 188 .cfa: sp 0 + .ra: x30
STACK CFI 6004 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 600c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6014 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60d8 x23: .cfa -16 + ^
STACK CFI 611c x23: x23
STACK CFI 6120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6124 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 613c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6140 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 616c x23: x23
STACK CFI 6170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6174 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6178 x23: x23
STACK CFI 6184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6188 ac .cfa: sp 0 + .ra: x30
STACK CFI 618c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6194 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6204 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6238 ec .cfa: sp 0 + .ra: x30
STACK CFI 623c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6244 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6274 x21: .cfa -64 + ^
STACK CFI 62c4 x21: x21
STACK CFI 62e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 6300 x21: .cfa -64 + ^
STACK CFI 6314 x21: x21
STACK CFI 6320 x21: .cfa -64 + ^
STACK CFI INIT 6328 44 .cfa: sp 0 + .ra: x30
STACK CFI 6350 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6368 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6370 20 .cfa: sp 0 + .ra: x30
STACK CFI 6374 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 638c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6390 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63b8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63e0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6408 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6430 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6458 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6480 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64d0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6508 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6540 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6580 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65b8 74 .cfa: sp 0 + .ra: x30
STACK CFI 65bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65f4 x19: x19 x20: x20
STACK CFI 65f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 65fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6610 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6628 x19: x19 x20: x20
STACK CFI INIT 6630 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6644 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 668c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 669c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 66d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 66e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66f0 8c .cfa: sp 0 + .ra: x30
STACK CFI 66f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 670c x21: .cfa -16 + ^
STACK CFI 675c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6760 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6780 8c .cfa: sp 0 + .ra: x30
STACK CFI 6784 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6810 70 .cfa: sp 0 + .ra: x30
STACK CFI 6814 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 684c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6854 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 686c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6870 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6880 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 6884 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 688c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 68a8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 68b0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 68bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 68f0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 69dc x19: x19 x20: x20
STACK CFI 69e0 x23: x23 x24: x24
STACK CFI 69e4 x27: x27 x28: x28
STACK CFI 6a08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6a0c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6a38 x19: x19 x20: x20
STACK CFI 6a3c x23: x23 x24: x24
STACK CFI 6a40 x27: x27 x28: x28
STACK CFI 6a44 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6a5c x19: x19 x20: x20
STACK CFI 6a60 x23: x23 x24: x24
STACK CFI 6a64 x27: x27 x28: x28
STACK CFI 6a68 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6b40 x19: x19 x20: x20
STACK CFI 6b44 x23: x23 x24: x24
STACK CFI 6b48 x27: x27 x28: x28
STACK CFI 6b4c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6b6c x27: x27 x28: x28
STACK CFI 6b80 x19: x19 x20: x20
STACK CFI 6b84 x23: x23 x24: x24
STACK CFI 6b88 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6bc4 x19: x19 x20: x20
STACK CFI 6bc8 x23: x23 x24: x24
STACK CFI 6bcc x27: x27 x28: x28
STACK CFI 6bd0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6be0 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 6bf4 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6c08 x19: x19 x20: x20
STACK CFI 6c0c x23: x23 x24: x24
STACK CFI 6c14 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6c18 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6c1c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 6c20 4c .cfa: sp 0 + .ra: x30
STACK CFI 6c24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c3c x19: .cfa -16 + ^
STACK CFI 6c4c x19: x19
STACK CFI 6c50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6c70 17c .cfa: sp 0 + .ra: x30
STACK CFI 6c74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6c7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6c88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ca4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6df0 198 .cfa: sp 0 + .ra: x30
STACK CFI 6df4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6dfc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6e18 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6f44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6f48 x25: .cfa -16 + ^
STACK CFI 6f70 x25: x25
STACK CFI 6f7c x25: .cfa -16 + ^
STACK CFI 6f84 x25: x25
STACK CFI INIT 6f88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7008 f8 .cfa: sp 0 + .ra: x30
STACK CFI 700c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7014 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7024 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 708c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 70cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 70d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7100 bc .cfa: sp 0 + .ra: x30
STACK CFI 7104 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7110 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7168 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71c0 278 .cfa: sp 0 + .ra: x30
STACK CFI 71c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 71cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 71d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7258 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 727c x23: .cfa -16 + ^
STACK CFI 732c x23: x23
STACK CFI 7340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7344 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7358 x23: x23
STACK CFI 739c x23: .cfa -16 + ^
STACK CFI 73b4 x23: x23
STACK CFI 740c x23: .cfa -16 + ^
STACK CFI 7434 x23: x23
STACK CFI INIT 7438 6c8 .cfa: sp 0 + .ra: x30
STACK CFI 743c .cfa: sp 544 +
STACK CFI 7444 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 7468 x19: .cfa -528 + ^ x20: .cfa -520 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 748c x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 7490 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 7494 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 78b0 x21: x21 x22: x22
STACK CFI 78b8 x23: x23 x24: x24
STACK CFI 78bc x27: x27 x28: x28
STACK CFI 78e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 78ec .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI 7a08 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 7a38 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 7a3c x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 7a40 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 7a4c x21: x21 x22: x22
STACK CFI 7a50 x23: x23 x24: x24
STACK CFI 7a54 x27: x27 x28: x28
STACK CFI 7a88 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 7a8c x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 7a90 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 7ad0 x21: x21 x22: x22
STACK CFI 7ad4 x23: x23 x24: x24
STACK CFI 7ad8 x27: x27 x28: x28
STACK CFI 7adc x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI INIT 7b00 e0 .cfa: sp 0 + .ra: x30
STACK CFI 7b10 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b7c x21: .cfa -16 + ^
STACK CFI 7ba4 x21: x21
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7be0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 7bec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bf8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7c1c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7cb8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7cd4 x27: .cfa -16 + ^
STACK CFI 7d98 x27: x27
STACK CFI 7d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7da0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7dc0 224 .cfa: sp 0 + .ra: x30
STACK CFI 7dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7dd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7dec x21: .cfa -16 + ^
STACK CFI 7e40 x21: x21
STACK CFI 7e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7ed8 x21: x21
STACK CFI 7edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ee0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7f78 x21: x21
STACK CFI 7f7c x21: .cfa -16 + ^
STACK CFI 7fb8 x21: x21
STACK CFI 7fc4 x21: .cfa -16 + ^
STACK CFI 7fe0 x21: x21
STACK CFI INIT 7fe8 21c .cfa: sp 0 + .ra: x30
STACK CFI 7fec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ff4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8010 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8208 314 .cfa: sp 0 + .ra: x30
STACK CFI 820c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8214 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8220 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8244 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8278 x23: x23 x24: x24
STACK CFI 82ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 82b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8470 x23: x23 x24: x24
STACK CFI 8474 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8484 x23: x23 x24: x24
STACK CFI 8488 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 84cc x23: x23 x24: x24
STACK CFI 84d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 84f4 x23: x23 x24: x24
STACK CFI 84f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 8520 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8598 1dc .cfa: sp 0 + .ra: x30
STACK CFI 859c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 85b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 85c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 85d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 865c x21: x21 x22: x22
STACK CFI 8660 x23: x23 x24: x24
STACK CFI 866c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8670 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8684 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 86e8 x21: x21 x22: x22
STACK CFI 86ec x23: x23 x24: x24
STACK CFI 86f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 86f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8714 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 872c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 8778 274 .cfa: sp 0 + .ra: x30
STACK CFI 877c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 87a4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 87ac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 87bc x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 87ec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8934 x23: x23 x24: x24
STACK CFI 8968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 896c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 89ac x23: x23 x24: x24
STACK CFI 89b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 89b8 x23: x23 x24: x24
STACK CFI 89c0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 89d0 x23: x23 x24: x24
STACK CFI 89dc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 89e0 x23: x23 x24: x24
STACK CFI 89e8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 89f0 bc .cfa: sp 0 + .ra: x30
STACK CFI 89f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 89fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8a54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8ab0 cc .cfa: sp 0 + .ra: x30
STACK CFI 8ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8abc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ac4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8b80 114 .cfa: sp 0 + .ra: x30
STACK CFI 8b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8b8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8bd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8c98 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d08 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d20 70 .cfa: sp 0 + .ra: x30
STACK CFI 8d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8d2c x23: .cfa -16 + ^
STACK CFI 8d34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8d44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8d90 ac .cfa: sp 0 + .ra: x30
STACK CFI 8d94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8d9c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8df4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8e40 124 .cfa: sp 0 + .ra: x30
STACK CFI 8e44 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8e4c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8e54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8ebc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8f68 88 .cfa: sp 0 + .ra: x30
STACK CFI 8f6c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8f74 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 8f94 x21: .cfa -160 + ^
STACK CFI 8fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8fd4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8ff0 34 .cfa: sp 0 + .ra: x30
STACK CFI 8ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ffc x19: .cfa -16 + ^
STACK CFI 9020 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9028 c8 .cfa: sp 0 + .ra: x30
STACK CFI 902c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9034 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9044 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 90d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 90d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 90f0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 90f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 90fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9108 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 915c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 92b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 92d0 184 .cfa: sp 0 + .ra: x30
STACK CFI 92d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 92dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 92ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 92f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9304 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 93b4 x25: x25 x26: x26
STACK CFI 93c0 x21: x21 x22: x22
STACK CFI 93c4 x23: x23 x24: x24
STACK CFI 93c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 93cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 93d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 93dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 942c x25: x25 x26: x26
STACK CFI 9430 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9440 x25: x25 x26: x26
STACK CFI 9444 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 944c x25: x25 x26: x26
STACK CFI INIT 9458 18c .cfa: sp 0 + .ra: x30
STACK CFI 945c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9464 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9474 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9488 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 948c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 953c x19: x19 x20: x20
STACK CFI 9540 x25: x25 x26: x26
STACK CFI 954c x23: x23 x24: x24
STACK CFI 9550 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9554 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9560 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9564 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 95a4 x19: x19 x20: x20
STACK CFI 95a8 x25: x25 x26: x26
STACK CFI 95ac x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 95d4 x19: x19 x20: x20
STACK CFI 95d8 x25: x25 x26: x26
STACK CFI INIT 95e8 fc .cfa: sp 0 + .ra: x30
STACK CFI 95ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 95f8 x21: .cfa -32 + ^
STACK CFI 9600 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 96a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 96ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 96e8 6c .cfa: sp 0 + .ra: x30
STACK CFI 96ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9724 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9744 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9758 dc .cfa: sp 0 + .ra: x30
STACK CFI 975c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 9764 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 9784 x21: .cfa -160 + ^
STACK CFI 97d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 97dc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 9838 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9890 954 .cfa: sp 0 + .ra: x30
STACK CFI 9894 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 98a8 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 98c4 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 98d0 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 98d4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9c58 x19: x19 x20: x20
STACK CFI 9c5c x23: x23 x24: x24
STACK CFI 9c60 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9cd8 x19: x19 x20: x20
STACK CFI 9cdc x23: x23 x24: x24
STACK CFI 9d08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9d0c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI a0c0 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI a0d8 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a0f4 x19: x19 x20: x20
STACK CFI a0f8 x23: x23 x24: x24
STACK CFI a0fc x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a10c x19: x19 x20: x20
STACK CFI a110 x23: x23 x24: x24
STACK CFI a114 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a12c x19: x19 x20: x20
STACK CFI a130 x23: x23 x24: x24
STACK CFI a134 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a154 x19: x19 x20: x20
STACK CFI a158 x23: x23 x24: x24
STACK CFI a15c x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a1bc x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI a1c0 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI a1c4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI a1dc x19: x19 x20: x20
STACK CFI a1e0 x23: x23 x24: x24
STACK CFI INIT a1e8 28 .cfa: sp 0 + .ra: x30
STACK CFI a1fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a210 1e4 .cfa: sp 0 + .ra: x30
STACK CFI a214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a220 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a2c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a36c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a3f8 5c .cfa: sp 0 + .ra: x30
