MODULE Linux arm64 1D37A1C30CD50728D11D0D9E444B03250 libpanel.so.6
INFO CODE_ID C3A1371DD50C2807D11D0D9E444B0325562EB038
PUBLIC 1678 0 ground_panel
PUBLIC 16c8 0 panel_above
PUBLIC 16e8 0 ceiling_panel
PUBLIC 1738 0 panel_below
PUBLIC 1790 0 bottom_panel
PUBLIC 1a30 0 del_panel
PUBLIC 1cb0 0 hide_panel
PUBLIC 1f28 0 panel_hidden
PUBLIC 1f88 0 move_panel
PUBLIC 21a8 0 new_panel
PUBLIC 2260 0 replace_panel
PUBLIC 2478 0 show_panel
PUBLIC 2710 0 top_panel
PUBLIC 2718 0 update_panels_sp
PUBLIC 28f0 0 update_panels
PUBLIC 2900 0 set_panel_userptr
PUBLIC 2920 0 panel_userptr
PUBLIC 2938 0 panel_window
STACK CFI INIT 15b8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1628 48 .cfa: sp 0 + .ra: x30
STACK CFI 162c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1634 x19: .cfa -16 + ^
STACK CFI 166c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1678 4c .cfa: sp 0 + .ra: x30
STACK CFI 16a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16c8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 16e8 4c .cfa: sp 0 + .ra: x30
STACK CFI 1710 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 172c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1738 54 .cfa: sp 0 + .ra: x30
STACK CFI 1740 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1748 x19: .cfa -16 + ^
STACK CFI 1778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1790 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 1798 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1808 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 188c x23: x23 x24: x24
STACK CFI 18e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 192c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1958 x25: .cfa -16 + ^
STACK CFI 1a08 x25: x25
STACK CFI 1a10 x25: .cfa -16 + ^
STACK CFI 1a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 1a30 280 .cfa: sp 0 + .ra: x30
STACK CFI 1a34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1a3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1a48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1b14 x23: x23 x24: x24
STACK CFI 1b58 x21: x21 x22: x22
STACK CFI 1b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1bac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1bd8 x25: .cfa -16 + ^
STACK CFI 1c88 x25: x25
STACK CFI 1c90 x25: .cfa -16 + ^
STACK CFI 1ca8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 1cb0 278 .cfa: sp 0 + .ra: x30
STACK CFI 1cb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ccc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1d0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d94 x23: x23 x24: x24
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1de0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1e24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1e50 x25: .cfa -16 + ^
STACK CFI 1f00 x25: x25
STACK CFI 1f08 x25: .cfa -16 + ^
STACK CFI 1f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 1f28 5c .cfa: sp 0 + .ra: x30
STACK CFI 1f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f38 x19: .cfa -16 + ^
STACK CFI 1f60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f88 21c .cfa: sp 0 + .ra: x30
STACK CFI 1f90 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f98 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1fa8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ff0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 206c x23: x23 x24: x24
STACK CFI 2080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2084 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 209c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20b0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2164 x25: x25 x26: x26
STACK CFI 216c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2194 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 219c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 21a8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 21ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2204 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2218 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2220 x21: .cfa -16 + ^
STACK CFI 2234 x21: x21
STACK CFI 2238 x21: .cfa -16 + ^
STACK CFI 225c x21: x21
STACK CFI INIT 2260 218 .cfa: sp 0 + .ra: x30
STACK CFI 2268 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2270 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 227c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 22c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2344 x23: x23 x24: x24
STACK CFI 2358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 235c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2374 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 23a0 x25: .cfa -16 + ^
STACK CFI 2450 x25: x25
STACK CFI 2458 x25: .cfa -16 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2478 294 .cfa: sp 0 + .ra: x30
STACK CFI 2480 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2488 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2494 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 256c x23: x23 x24: x24
STACK CFI 25bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 25c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 25f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2620 x25: .cfa -16 + ^
STACK CFI 26d0 x25: x25
STACK CFI 26d8 x25: .cfa -16 + ^
STACK CFI 26f0 x23: x23 x24: x24 x25: x25
STACK CFI 2700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2718 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2720 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 272c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2740 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2744 x25: .cfa -16 + ^
STACK CFI 27f4 x23: x23 x24: x24
STACK CFI 27f8 x25: x25
STACK CFI 2804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2808 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 28e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 28f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2900 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2920 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2938 14 .cfa: sp 0 + .ra: x30
