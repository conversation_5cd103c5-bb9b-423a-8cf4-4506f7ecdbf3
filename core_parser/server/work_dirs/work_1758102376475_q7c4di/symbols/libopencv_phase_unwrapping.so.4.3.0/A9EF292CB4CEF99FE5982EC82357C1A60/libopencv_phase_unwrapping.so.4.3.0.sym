MODULE Linux arm64 A9EF292CB4CEF99FE5982EC82357C1A60 libopencv_phase_unwrapping.so.4.3
INFO CODE_ID 2C29EFA9CEB49FF9E5982EC82357C1A604BA180F
PUBLIC 3dc8 0 _init
PUBLIC 4240 0 call_weak_fn
PUBLIC 4258 0 deregister_tm_clones
PUBLIC 4290 0 register_tm_clones
PUBLIC 42d0 0 __do_global_dtors_aux
PUBLIC 4318 0 frame_dummy
PUBLIC 4350 0 cv::Algorithm::clear()
PUBLIC 4358 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 4360 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 4368 0 cv::Algorithm::empty() const
PUBLIC 4370 0 std::_Sp_counted_ptr_inplace<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4378 0 std::_Sp_counted_ptr_inplace<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4380 0 std::_Sp_counted_ptr_inplace<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4388 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::~HistogramPhaseUnwrapping_Impl()
PUBLIC 4410 0 virtual thunk to cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::~HistogramPhaseUnwrapping_Impl()
PUBLIC 4420 0 std::_Sp_counted_ptr_inplace<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4470 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::~HistogramPhaseUnwrapping_Impl()
PUBLIC 4500 0 virtual thunk to cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::~HistogramPhaseUnwrapping_Impl()
PUBLIC 4510 0 std::_Sp_counted_ptr_inplace<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 45c8 0 cv::Mat::~Mat()
PUBLIC 4658 0 cv::phase_unwrapping::HistogramPhaseUnwrapping::Params::Params()
PUBLIC 4690 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::Pixel()
PUBLIC 4698 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::Pixel(float, int, bool, float, int)
PUBLIC 46c0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getPhaseValue()
PUBLIC 46c8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getIndex()
PUBLIC 46d0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getValidity()
PUBLIC 46d8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getInverseReliability()
PUBLIC 46e0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::getInverseReliabilityMap(cv::_OutputArray const&)
PUBLIC 4860 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getIncrement()
PUBLIC 4868 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getNbrOfPixelsInGroup()
PUBLIC 4870 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getGroupId()
PUBLIC 4878 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::getSinglePixelGroup()
PUBLIC 4880 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::setIncrement(int)
PUBLIC 4888 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::changeIncrement(int)
PUBLIC 4898 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::setNbrOfPixelsInGroup(int)
PUBLIC 48a0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::setGroupId(int)
PUBLIC 48a8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel::setSinglePixelGroup(bool)
PUBLIC 48b0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge::Edge()
PUBLIC 48b8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge::Edge(int, int, int)
PUBLIC 48c8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge::getPixOneId()
PUBLIC 48d0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge::getPixTwoId()
PUBLIC 48d8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge::getIncrement()
PUBLIC 48e0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin::HistogramBin()
PUBLIC 48f0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin::HistogramBin(float, float)
PUBLIC 4900 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin::getEdges()
PUBLIC 49e0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::Histogram()
PUBLIC 49f0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramPhaseUnwrapping_Impl(cv::phase_unwrapping::HistogramPhaseUnwrapping::Params const&)
PUBLIC 4ab0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramPhaseUnwrapping_Impl(cv::phase_unwrapping::HistogramPhaseUnwrapping::Params const&)
PUBLIC 4b38 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::getThresh()
PUBLIC 4b40 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::getSmallWidth()
PUBLIC 4b48 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::getLargeWidth()
PUBLIC 4b50 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::getNbrOfBins()
PUBLIC 4b58 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::getEdgesFromBin(int)
PUBLIC 4bd8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::unwrapHistogram()
PUBLIC 52e0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::addIncrement(cv::_OutputArray const&)
PUBLIC 54c8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::wrap(float, float)
PUBLIC 5508 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::findInc(float, float)
PUBLIC 5538 0 cv::phase_unwrapping::HistogramPhaseUnwrapping::create(cv::phase_unwrapping::HistogramPhaseUnwrapping::Params const&)
PUBLIC 55f0 0 void std::vector<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge> >::_M_emplace_back_aux<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge const&>(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge const&)
PUBLIC 5728 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin::addEdge(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge)
PUBLIC 5778 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::addEdgeInBin(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Edge, int)
PUBLIC 5798 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::createAndSortEdge(int, int)
PUBLIC 5910 0 void std::vector<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin> >::_M_emplace_back_aux<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin const&>(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin const&)
PUBLIC 5b78 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::addBin(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::HistogramBin)
PUBLIC 5cb0 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Histogram::createBins(float, int, int)
PUBLIC 5de8 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::computeEdgesReliabilityAndCreateHistogram()
PUBLIC 5f20 0 void std::vector<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel, std::allocator<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel> >::_M_emplace_back_aux<cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel const&>(cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::Pixel const&)
PUBLIC 6028 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::computePixelsReliability(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 6570 0 cv::phase_unwrapping::HistogramPhaseUnwrapping_Impl::unwrapPhaseMap(cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC 6790 0 _fini
STACK CFI INIT 4350 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4358 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4378 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4388 84 .cfa: sp 0 + .ra: x30
STACK CFI 438c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4390 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 4408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 4410 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4420 50 .cfa: sp 0 + .ra: x30
STACK CFI 4424 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4430 .ra: .cfa -16 + ^
STACK CFI 446c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 4470 8c .cfa: sp 0 + .ra: x30
STACK CFI 4474 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4478 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 44f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 4500 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4514 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4520 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4528 .ra: .cfa -16 + ^
STACK CFI 45ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 45b0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 45c8 90 .cfa: sp 0 + .ra: x30
STACK CFI 45cc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4648 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4654 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 4658 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4698 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46e0 17c .cfa: sp 0 + .ra: x30
STACK CFI 46e4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 46e8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 46fc .ra: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4804 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 4860 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4868 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4878 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4888 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4898 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4900 dc .cfa: sp 0 + .ra: x30
STACK CFI 4904 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4914 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 49d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 49d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 49e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 49f0 bc .cfa: sp 0 + .ra: x30
STACK CFI 49f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a04 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 4a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4a68 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 4ab0 84 .cfa: sp 0 + .ra: x30
STACK CFI 4ab4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ac0 .ra: .cfa -16 + ^
STACK CFI 4b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4b04 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 4b38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b58 7c .cfa: sp 0 + .ra: x30
STACK CFI 4b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI 4bb4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4bb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI INIT 4bd8 704 .cfa: sp 0 + .ra: x30
STACK CFI 4bdc .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4bfc .ra: .cfa -128 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 4f1c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4f20 .cfa: sp 208 + .ra: .cfa -128 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 52e0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 52e4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 52fc .ra: .cfa -64 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5418 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5420 .cfa: sp 128 + .ra: .cfa -64 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 54c8 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5508 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5538 b8 .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5540 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5550 .ra: .cfa -16 + ^
STACK CFI 55ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 55b0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 55d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 55dc .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 55f0 138 .cfa: sp 0 + .ra: x30
STACK CFI 55f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5600 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5608 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 56ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 56f0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 5728 4c .cfa: sp 0 + .ra: x30
STACK CFI 5730 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 5760 .cfa: sp 0 + .ra: .ra
STACK CFI 5768 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 5778 1c .cfa: sp 0 + .ra: x30
STACK CFI 5780 .cfa: sp 16 +
STACK CFI 5790 .cfa: sp 0 +
STACK CFI INIT 5798 174 .cfa: sp 0 + .ra: x30
STACK CFI 579c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 57a8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 57bc .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 57e4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 57e8 .cfa: sp 96 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 58ac .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 58b0 .cfa: sp 96 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5908 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 5910 264 .cfa: sp 0 + .ra: x30
STACK CFI 5914 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 591c .ra: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 5928 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 5b00 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 5b78 138 .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b84 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5c80 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5c9c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5cac .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 5cb0 12c .cfa: sp 0 + .ra: x30
STACK CFI 5cc4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5ce0 .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x21: .cfa -64 + ^
STACK CFI 5db8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI 5dbc .cfa: sp 80 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5de8 134 .cfa: sp 0 + .ra: x30
STACK CFI 5dec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5dfc .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5ee0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 5f20 108 .cfa: sp 0 + .ra: x30
STACK CFI 5f24 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f2c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 5f34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5ff8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 6028 53c .cfa: sp 0 + .ra: x30
STACK CFI 602c .cfa: sp 368 + x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 6050 .ra: .cfa -288 + ^ v10: .cfa -256 + ^ v11: .cfa -248 + ^ v12: .cfa -240 + ^ v13: .cfa -232 + ^ v14: .cfa -224 + ^ v15: .cfa -216 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 62b4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 62b8 .cfa: sp 368 + .ra: .cfa -288 + ^ v10: .cfa -256 + ^ v11: .cfa -248 + ^ v12: .cfa -240 + ^ v13: .cfa -232 + ^ v14: .cfa -224 + ^ v15: .cfa -216 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI INIT 6570 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 6578 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 6588 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 659c .ra: .cfa -168 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^
STACK CFI 6708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 6710 .cfa: sp 224 + .ra: .cfa -168 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^
