MODULE Linux arm64 62EDA176AAC8E0C9532BECE194647B660 libxtables.so.12
INFO CODE_ID 76A1ED62C8AAC9E0532BECE194647B66F902D4C2
PUBLIC 32f8 0 basic_exit_err
PUBLIC 34f0 0 xtables_free_opts
PUBLIC 3530 0 xtables_merge_options
PUBLIC 3700 0 xtables_init
PUBLIC 37a8 0 xtables_set_nfproto
PUBLIC 3860 0 xtables_set_params
PUBLIC 38d0 0 xtables_init_all
PUBLIC 3900 0 xtables_calloc
PUBLIC 3930 0 xtables_malloc
PUBLIC 3960 0 xtables_realloc
PUBLIC 3990 0 xtables_insmod
PUBLIC 3b60 0 xtables_load_ko
PUBLIC 3c68 0 xtables_strtoul
PUBLIC 3d88 0 xtables_strtoui
PUBLIC 3f78 0 xtables_service_to_port
PUBLIC 3fa8 0 xtables_parse_port
PUBLIC 4060 0 xtables_parse_interface
PUBLIC 4168 0 xtables_find_target
PUBLIC 4770 0 xtables_find_match
PUBLIC 4bf0 0 xtables_find_match_revision
PUBLIC 4c78 0 xtables_find_target_revision
PUBLIC 4d00 0 xtables_compatible_revision
PUBLIC 4f18 0 xtables_register_match
PUBLIC 5110 0 xtables_register_matches
PUBLIC 5150 0 xtables_register_target
PUBLIC 5368 0 xtables_register_targets
PUBLIC 53a8 0 xtables_rule_matches_free
PUBLIC 5428 0 xtables_param_act
PUBLIC 55d8 0 xtables_ipaddr_to_numeric
PUBLIC 5630 0 xtables_ipaddr_to_anyname
PUBLIC 56f8 0 xtables_ipmask_to_cidr
PUBLIC 5748 0 xtables_ipmask_to_numeric
PUBLIC 57f8 0 xtables_numeric_to_ipaddr
PUBLIC 5960 0 xtables_numeric_to_ipmask
PUBLIC 5a40 0 xtables_ipparse_multiple
PUBLIC 5d50 0 xtables_ipparse_any
PUBLIC 5ec8 0 xtables_ip6addr_to_numeric
PUBLIC 5ee8 0 xtables_ip6addr_to_anyname
PUBLIC 5f98 0 xtables_ip6mask_to_cidr
PUBLIC 5fe8 0 xtables_ip6mask_to_numeric
PUBLIC 6098 0 xtables_numeric_to_ip6addr
PUBLIC 6378 0 xtables_ip6parse_multiple
PUBLIC 66c8 0 xtables_ip6parse_any
PUBLIC 68b0 0 xtables_save_string
PUBLIC 69d0 0 xtables_parse_protocol
PUBLIC 6b00 0 xtables_print_num
PUBLIC 6c50 0 xtables_print_mac
PUBLIC 6cb0 0 xtables_print_mac_and_mask
PUBLIC 6d18 0 xtables_parse_val_mask
PUBLIC 6e88 0 xtables_print_val_mask
PUBLIC 6f10 0 get_kernel_version
PUBLIC 6fe0 0 xt_xlate_alloc
PUBLIC 7058 0 xt_xlate_free
PUBLIC 7080 0 xt_xlate_add
PUBLIC 7198 0 xt_xlate_add_comment
PUBLIC 71c8 0 xt_xlate_get_comment
PUBLIC 71d8 0 xt_xlate_get
PUBLIC 8568 0 xtables_options_xfrm
PUBLIC 8740 0 xtables_option_parse
PUBLIC 8880 0 xtables_option_metavalidate
PUBLIC 8990 0 xtables_option_tpcall
PUBLIC 8ab0 0 xtables_option_mpcall
PUBLIC 8bd0 0 xtables_options_fcheck
PUBLIC 8d00 0 xtables_option_tfcall
PUBLIC 8da8 0 xtables_option_mfcall
PUBLIC 8e50 0 xtables_lmap_free
PUBLIC 8e98 0 xtables_lmap_init
PUBLIC 90f8 0 xtables_lmap_name2id
PUBLIC 9158 0 xtables_lmap_id2name
PUBLIC 9418 0 xtables_getethertypebyname
PUBLIC 94b8 0 xtables_getethertypebynumber
STACK CFI INIT 3238 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3268 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 32ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32b4 x19: .cfa -16 + ^
STACK CFI 32ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32f8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 32fc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3328 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 3378 x21: .cfa -304 + ^
STACK CFI INIT 33e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 3404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3440 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 34a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34c0 2c .cfa: sp 0 + .ra: x30
STACK CFI 34c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 34f0 3c .cfa: sp 0 + .ra: x30
STACK CFI 34f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34fc x19: .cfa -16 + ^
STACK CFI 3528 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3530 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 3534 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3540 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 354c x27: .cfa -16 + ^
STACK CFI 3558 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3560 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 368c x19: x19 x20: x20
STACK CFI 3690 x23: x23 x24: x24
STACK CFI 3694 x27: x27
STACK CFI 36a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 36a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 36bc x19: x19 x20: x20 x23: x23 x24: x24 x27: x27
STACK CFI 36d0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 36d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 36f4 x19: x19 x20: x20
STACK CFI 36f8 x23: x23 x24: x24
STACK CFI 36fc x27: x27
STACK CFI INIT 3700 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3714 x19: .cfa -16 + ^
STACK CFI 372c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3764 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 376c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 37a8 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3860 70 .cfa: sp 0 + .ra: x30
STACK CFI 3898 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 38d0 30 .cfa: sp 0 + .ra: x30
STACK CFI 38d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3900 2c .cfa: sp 0 + .ra: x30
STACK CFI 3904 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3930 2c .cfa: sp 0 + .ra: x30
STACK CFI 3934 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3944 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3960 2c .cfa: sp 0 + .ra: x30
STACK CFI 3964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3974 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3990 1cc .cfa: sp 0 + .ra: x30
STACK CFI 3994 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 399c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 39a8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 39c4 x23: .cfa -64 + ^
STACK CFI 3a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3a70 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3b60 108 .cfa: sp 0 + .ra: x30
STACK CFI 3b64 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3b6c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 3b78 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 3b80 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 3bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3bc8 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x29: .cfa -336 + ^
STACK CFI 3be0 x25: .cfa -272 + ^
STACK CFI 3c2c x25: x25
STACK CFI 3c30 x25: .cfa -272 + ^
STACK CFI 3c5c x25: x25
STACK CFI 3c64 x25: .cfa -272 + ^
STACK CFI INIT 3c68 120 .cfa: sp 0 + .ra: x30
STACK CFI 3c6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3c74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3c80 x25: .cfa -32 + ^
STACK CFI 3c94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3ca0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3d50 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3d88 74 .cfa: sp 0 + .ra: x30
STACK CFI 3d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3df8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e00 174 .cfa: sp 0 + .ra: x30
STACK CFI 3e04 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3e10 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3e24 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3e38 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3e44 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3e58 x27: .cfa -64 + ^
STACK CFI 3f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3f14 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3f78 2c .cfa: sp 0 + .ra: x30
STACK CFI 3f7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3fa8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3fac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3fc8 x21: .cfa -32 + ^
STACK CFI 4018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 401c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4060 104 .cfa: sp 0 + .ra: x30
STACK CFI 4064 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 406c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4078 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4080 x23: .cfa -16 + ^
STACK CFI 4120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4128 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 413c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4168 400 .cfa: sp 0 + .ra: x30
STACK CFI 416c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 417c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4188 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 41cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4208 x27: x27 x28: x28
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 42a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 43cc x27: x27 x28: x28
STACK CFI 4418 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4420 x27: x27 x28: x28
STACK CFI 4454 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 44a8 x27: x27 x28: x28
STACK CFI 44c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 44c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 44ec x27: x27 x28: x28
STACK CFI 44f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4568 204 .cfa: sp 0 + .ra: x30
STACK CFI 456c .cfa: sp 544 +
STACK CFI 4570 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 4578 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 4584 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 4594 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 459c x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 45ac x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 46c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46c8 .cfa: sp 544 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT 4770 480 .cfa: sp 0 + .ra: x30
STACK CFI 4774 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 477c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4788 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4794 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4928 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4a8c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4bf0 84 .cfa: sp 0 + .ra: x30
STACK CFI 4bf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4bfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c08 x21: .cfa -16 + ^
STACK CFI 4c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c78 84 .cfa: sp 0 + .ra: x30
STACK CFI 4c7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c90 x21: .cfa -16 + ^
STACK CFI 4ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4d00 214 .cfa: sp 0 + .ra: x30
STACK CFI 4d04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4d0c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4d1c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4d24 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4da0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 4db0 x25: .cfa -64 + ^
STACK CFI 4e38 x25: x25
STACK CFI 4e64 x25: .cfa -64 + ^
STACK CFI 4e74 x25: x25
STACK CFI 4e78 x25: .cfa -64 + ^
STACK CFI 4e88 x25: x25
STACK CFI 4e90 x25: .cfa -64 + ^
STACK CFI 4ecc x25: x25
STACK CFI 4ee0 x25: .cfa -64 + ^
STACK CFI INIT 4f18 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 4f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4fe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5110 40 .cfa: sp 0 + .ra: x30
STACK CFI 5114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5120 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 514c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5150 214 .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 515c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5168 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 523c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5368 40 .cfa: sp 0 + .ra: x30
STACK CFI 536c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5378 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 53a8 80 .cfa: sp 0 + .ra: x30
STACK CFI 53ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53bc x21: .cfa -16 + ^
STACK CFI 5424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5428 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 542c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 54d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 54d8 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 55d8 54 .cfa: sp 0 + .ra: x30
STACK CFI 55dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55ec x19: .cfa -16 + ^
STACK CFI 5628 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5630 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5634 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5644 x21: .cfa -48 + ^
STACK CFI 564c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 56c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 56f8 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5748 b0 .cfa: sp 0 + .ra: x30
STACK CFI 574c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5754 x19: .cfa -16 + ^
STACK CFI 57a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 57f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5800 15c .cfa: sp 0 + .ra: x30
STACK CFI 5804 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 580c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5814 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 587c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5880 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5968 d8 .cfa: sp 0 + .ra: x30
STACK CFI 596c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5974 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 59b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5a40 310 .cfa: sp 0 + .ra: x30
STACK CFI 5a44 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 5a4c x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 5a64 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 5a7c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 5ac4 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 5be8 x25: x25 x26: x26
STACK CFI 5c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5c1c .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI 5d1c x25: x25 x26: x26
STACK CFI 5d2c x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI INIT 5d50 174 .cfa: sp 0 + .ra: x30
STACK CFI 5d54 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 5d5c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 5d6c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 5d8c x23: .cfa -288 + ^
STACK CFI 5e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5e9c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x29: .cfa -336 + ^
STACK CFI INIT 5ec8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ee8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5eec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5efc x21: .cfa -64 + ^
STACK CFI 5f04 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5f98 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5fe8 ac .cfa: sp 0 + .ra: x30
STACK CFI 5fec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ff4 x19: .cfa -16 + ^
STACK CFI 6040 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6044 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6054 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6058 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6098 3c .cfa: sp 0 + .ra: x30
STACK CFI 609c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 60ac x19: .cfa -16 + ^
STACK CFI 60d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 60d8 154 .cfa: sp 0 + .ra: x30
STACK CFI 60dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 60e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6128 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6144 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6170 x21: x21 x22: x22
STACK CFI 6174 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6184 x23: .cfa -32 + ^
STACK CFI 61ec x23: x23
STACK CFI 61f0 x23: .cfa -32 + ^
STACK CFI 61f4 x23: x23
STACK CFI 61f8 x21: x21 x22: x22
STACK CFI 61fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6200 x23: .cfa -32 + ^
STACK CFI 6204 x23: x23
STACK CFI 6228 x23: .cfa -32 + ^
STACK CFI INIT 6230 144 .cfa: sp 0 + .ra: x30
STACK CFI 6234 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 623c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6244 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6268 x23: .cfa -80 + ^
STACK CFI 6288 x23: x23
STACK CFI 62b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 6368 x23: .cfa -80 + ^
STACK CFI 636c x23: x23
STACK CFI 6370 x23: .cfa -80 + ^
STACK CFI INIT 6378 350 .cfa: sp 0 + .ra: x30
STACK CFI 637c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 6384 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 639c x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 63b4 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 6400 x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 6660 x25: x25 x26: x26
STACK CFI 6690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6694 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^ x29: .cfa -384 + ^
STACK CFI 66a4 x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI INIT 66c8 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 66cc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 66d4 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 66e4 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 6704 x23: .cfa -288 + ^
STACK CFI 6854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6858 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x29: .cfa -336 + ^
STACK CFI INIT 68b0 11c .cfa: sp 0 + .ra: x30
STACK CFI 68b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6998 x23: x23 x24: x24
STACK CFI 699c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 69a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 69c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 69d0 130 .cfa: sp 0 + .ra: x30
STACK CFI 69d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 69e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 69f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6a8c x23: .cfa -32 + ^
STACK CFI 6af4 x23: x23
STACK CFI 6afc x23: .cfa -32 + ^
STACK CFI INIT 6b00 14c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c50 5c .cfa: sp 0 + .ra: x30
STACK CFI 6c54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c70 x21: .cfa -16 + ^
STACK CFI 6ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6cb0 64 .cfa: sp 0 + .ra: x30
STACK CFI 6cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cbc x19: .cfa -16 + ^
STACK CFI 6cf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6cf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6d10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d18 16c .cfa: sp 0 + .ra: x30
STACK CFI 6d1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6d24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6d30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6d40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6d58 x25: .cfa -32 + ^
STACK CFI 6dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6dcc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6e88 84 .cfa: sp 0 + .ra: x30
STACK CFI 6e8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e98 x19: .cfa -16 + ^
STACK CFI 6ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6f10 cc .cfa: sp 0 + .ra: x30
STACK CFI 6f14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fa8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6fe0 74 .cfa: sp 0 + .ra: x30
STACK CFI 6fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7034 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7058 28 .cfa: sp 0 + .ra: x30
STACK CFI 705c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7064 x19: .cfa -16 + ^
STACK CFI 707c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7080 114 .cfa: sp 0 + .ra: x30
STACK CFI 7084 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 7094 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 716c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7170 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 7198 2c .cfa: sp 0 + .ra: x30
STACK CFI 719c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71a8 x19: .cfa -16 + ^
STACK CFI 71c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 71c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 71d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 71e0 130 .cfa: sp 0 + .ra: x30
STACK CFI 71e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 71ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 71fc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 721c x25: .cfa -32 + ^
STACK CFI 72c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 72cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7310 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 7314 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7320 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 732c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 73ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73b0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 74b8 128 .cfa: sp 0 + .ra: x30
STACK CFI 74bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 75e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 75e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 75f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 767c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7690 1fc .cfa: sp 0 + .ra: x30
STACK CFI 7694 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 769c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 76a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 77c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 77c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7890 44 .cfa: sp 0 + .ra: x30
STACK CFI 7894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 789c x19: .cfa -16 + ^
STACK CFI 78d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78d8 1cc .cfa: sp 0 + .ra: x30
STACK CFI 78dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 78e8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7910 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 7a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7a48 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7aa8 140 .cfa: sp 0 + .ra: x30
STACK CFI 7aac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7abc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ac8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7af8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7b14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7b80 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7bb8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7bbc .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7bcc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7bd4 x19: x19 x20: x20
STACK CFI 7bd8 x21: x21 x22: x22
STACK CFI 7be0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7be4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 7be8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7bf8 cc .cfa: sp 0 + .ra: x30
STACK CFI 7bfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c10 x21: .cfa -16 + ^
STACK CFI 7c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7c7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7cc8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7cd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7cf8 x21: .cfa -32 + ^
STACK CFI 7d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7d70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7db0 188 .cfa: sp 0 + .ra: x30
STACK CFI 7db4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7dbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7dc8 x23: .cfa -32 + ^
STACK CFI 7dd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7e84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7f38 374 .cfa: sp 0 + .ra: x30
STACK CFI 7f3c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7f44 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7f58 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7f78 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8208 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 82b0 7c .cfa: sp 0 + .ra: x30
STACK CFI 82b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8308 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8330 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8334 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8340 x21: .cfa -16 + ^
STACK CFI 8348 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 83a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 83b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 83b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 83d8 18c .cfa: sp 0 + .ra: x30
STACK CFI 83dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 83ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8410 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8488 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 8494 x23: .cfa -48 + ^
STACK CFI 84d8 x23: x23
STACK CFI 84dc x23: .cfa -48 + ^
STACK CFI 8510 x23: x23
STACK CFI 8534 x23: .cfa -48 + ^
STACK CFI 855c x23: x23
STACK CFI 8560 x23: .cfa -48 + ^
STACK CFI INIT 8568 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 856c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8574 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 857c x27: .cfa -16 + ^
STACK CFI 8588 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8594 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8598 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 86d8 x19: x19 x20: x20
STACK CFI 86dc x21: x21 x22: x22
STACK CFI 86e0 x23: x23 x24: x24
STACK CFI 86f0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 86f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8704 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8718 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 871c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8730 x19: x19 x20: x20
STACK CFI 8734 x21: x21 x22: x22
STACK CFI 8738 x23: x23 x24: x24
STACK CFI INIT 8740 13c .cfa: sp 0 + .ra: x30
STACK CFI 8744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8750 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 87c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 87e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 882c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8880 10c .cfa: sp 0 + .ra: x30
STACK CFI 888c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8990 11c .cfa: sp 0 + .ra: x30
STACK CFI 8994 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 89a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a60 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8ab0 11c .cfa: sp 0 + .ra: x30
STACK CFI 8ab4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8ac0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b80 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8bd0 130 .cfa: sp 0 + .ra: x30
STACK CFI 8bec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8ca0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8d00 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8d04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8d0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8d8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8da8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8dac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8db4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e34 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8e50 44 .cfa: sp 0 + .ra: x30
STACK CFI 8e58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e98 260 .cfa: sp 0 + .ra: x30
STACK CFI 8e9c .cfa: sp 640 +
STACK CFI 8ea8 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 8eb0 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 8ec8 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI 8ed8 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 8eec x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 8ef0 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 9058 x19: x19 x20: x20
STACK CFI 905c x21: x21 x22: x22
STACK CFI 9060 x27: x27 x28: x28
STACK CFI 908c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9090 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^ x29: .cfa -640 + ^
STACK CFI 90a4 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 90b0 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 90b4 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 90b8 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 90e0 x19: x19 x20: x20
STACK CFI 90e4 x21: x21 x22: x22
STACK CFI 90e8 x27: x27 x28: x28
STACK CFI 90ec x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 90f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 9100 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9108 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 913c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9158 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9188 70 .cfa: sp 0 + .ra: x30
STACK CFI 918c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9194 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 91c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 91c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 91f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 91f8 220 .cfa: sp 0 + .ra: x30
STACK CFI 91fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9204 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9210 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 923c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9248 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9258 x27: .cfa -32 + ^
STACK CFI 9338 x21: x21 x22: x22
STACK CFI 933c x25: x25 x26: x26
STACK CFI 9340 x27: x27
STACK CFI 9368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 936c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 93cc x25: x25 x26: x26
STACK CFI 93d0 x27: x27
STACK CFI 93d8 x21: x21 x22: x22
STACK CFI 93dc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 93e4 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 940c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9410 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9414 x27: .cfa -32 + ^
STACK CFI INIT 9418 9c .cfa: sp 0 + .ra: x30
STACK CFI 941c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9424 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9430 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 94b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 94b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 94bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94c4 x21: .cfa -16 + ^
STACK CFI 94d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 952c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
