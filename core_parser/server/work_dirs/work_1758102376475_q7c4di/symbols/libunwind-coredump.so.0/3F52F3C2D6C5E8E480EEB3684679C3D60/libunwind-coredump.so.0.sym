MODULE Linux arm64 3F52F3C2D6C5E8E480EEB3684679C3D60 libunwind-coredump.so.0
INFO CODE_ID C2F3523FC5D6E4E880EEB3684679C3D6E68F9D03
PUBLIC f90 0 _UCD_create
PUBLIC 1388 0 _UCD_get_num_threads
PUBLIC 1390 0 _UCD_select_thread
PUBLIC 13b0 0 _UCD_get_pid
PUBLIC 13c0 0 _UCD_get_cursig
PUBLIC 13d0 0 _UCD_add_backing_file_at_segment
PUBLIC 1580 0 _UCD_add_backing_file_at_vaddr
PUBLIC 15c0 0 _UCD_destroy
PUBLIC 16b0 0 _UCD_access_mem
PUBLIC 18f8 0 _UCD_find_proc_info
PUBLIC 1af0 0 _UCD_get_proc_name
PUBLIC 2210 0 _UCD_access_fpreg
PUBLIC 2250 0 _UCD_get_dyn_info_list_addr
PUBLIC 2258 0 _UCD_put_unwind_info
PUBLIC 2288 0 _UCD_resume
PUBLIC 22c8 0 _UCD_access_reg
STACK CFI INIT f90 3f4 .cfa: sp 0 + .ra: x30
STACK CFI f94 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI fa0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI fc4 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x27: .cfa -144 + ^
STACK CFI 1088 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 10a8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1110 x23: x23 x24: x24
STACK CFI 1114 x25: x25 x26: x26
STACK CFI 1150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x29: x29
STACK CFI 1154 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x27: .cfa -144 + ^ x29: .cfa -224 + ^
STACK CFI 117c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 119c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1214 x25: x25 x26: x26
STACK CFI 1224 x23: x23 x24: x24
STACK CFI 1230 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 135c x25: x25 x26: x26
STACK CFI 1360 x23: x23 x24: x24
STACK CFI 1364 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 1378 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 137c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1380 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 1388 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1390 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13d0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 13d4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 13dc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1400 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1408 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1470 x19: x19 x20: x20
STACK CFI 1498 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 149c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 14a4 x25: .cfa -160 + ^
STACK CFI 1520 x19: x19 x20: x20
STACK CFI 1524 x25: x25
STACK CFI 1528 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: x25
STACK CFI 152c x19: x19 x20: x20
STACK CFI 1534 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x25: .cfa -160 + ^
STACK CFI 1548 x25: x25
STACK CFI 1570 x19: x19 x20: x20
STACK CFI 1578 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 157c x25: .cfa -160 + ^
STACK CFI INIT 1580 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15c0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 15c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15d0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 168c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 16ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16b0 fc .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 171c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1720 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1728 x21: .cfa -16 + ^
STACK CFI 177c x21: x21
STACK CFI 1788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 178c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 17a8 x21: x21
STACK CFI INIT 17b0 144 .cfa: sp 0 + .ra: x30
STACK CFI 17b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 181c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1820 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18f8 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 18fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1904 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 190c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1918 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1abc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ae0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1af0 84 .cfa: sp 0 + .ra: x30
STACK CFI 1af4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1afc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1b0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1b18 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1b78 208 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1b98 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1ba8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1bb0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1bdc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1be8 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 1bec x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1bf0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1c00 x19: x19 x20: x20
STACK CFI 1c08 x23: x23 x24: x24
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1c18 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1d80 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 1d84 .cfa: sp 272 +
STACK CFI 1d8c .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1d94 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1db0 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1dd4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ec4 .cfa: sp 272 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 1ed4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1ee4 x23: x23 x24: x24
STACK CFI 1ee8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 2040 x23: x23 x24: x24
STACK CFI 2044 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 204c x23: x23 x24: x24
STACK CFI 2058 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 2124 x23: x23 x24: x24
STACK CFI 2128 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 2138 x23: x23 x24: x24
STACK CFI 2140 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT 2148 c8 .cfa: sp 0 + .ra: x30
STACK CFI 214c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2154 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2160 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2174 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2190 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 220c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2210 3c .cfa: sp 0 + .ra: x30
STACK CFI 2214 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2258 30 .cfa: sp 0 + .ra: x30
STACK CFI 2264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 226c x19: .cfa -16 + ^
STACK CFI 2280 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2288 3c .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 22c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22c8 2c .cfa: sp 0 + .ra: x30
