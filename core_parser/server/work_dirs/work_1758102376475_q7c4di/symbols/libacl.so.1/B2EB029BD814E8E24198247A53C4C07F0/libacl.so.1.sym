MODULE Linux arm64 B2EB029BD814E8E24198247A53C4C07F0 libacl.so.1
INFO CODE_ID 9B02EBB214D8E2E84198247A53C4C07F4B695FF3
PUBLIC 1a28 0 acl_add_perm
PUBLIC 1a78 0 acl_calc_mask
PUBLIC 1b78 0 acl_clear_perms
PUBLIC 1ba8 0 acl_copy_entry
PUBLIC 1c28 0 acl_copy_ext
PUBLIC 1cd8 0 acl_copy_int
PUBLIC 1ea8 0 acl_create_entry
PUBLIC 1f20 0 acl_delete_def_file
PUBLIC 1f60 0 acl_delete_entry
PUBLIC 1fe8 0 acl_delete_perm
PUBLIC 2038 0 acl_dup
PUBLIC 2148 0 acl_free
PUBLIC 24f8 0 acl_from_text
PUBLIC 2ab0 0 acl_get_entry
PUBLIC 2bb0 0 acl_get_fd
PUBLIC 2d18 0 acl_get_file
PUBLIC 2ef8 0 acl_get_perm
PUBLIC 2f50 0 acl_get_permset
PUBLIC 2fb0 0 acl_get_qualifier
PUBLIC 3038 0 acl_get_tag_type
PUBLIC 3110 0 acl_init
PUBLIC 3150 0 acl_set_fd
PUBLIC 31f8 0 acl_set_file
PUBLIC 32d8 0 acl_set_permset
PUBLIC 3330 0 acl_set_qualifier
PUBLIC 33a8 0 acl_set_tag_type
PUBLIC 3428 0 acl_size
PUBLIC 3460 0 acl_to_text
PUBLIC 3478 0 acl_valid
PUBLIC 34b8 0 acl_check
PUBLIC 3628 0 acl_cmp
PUBLIC 3700 0 acl_entries
PUBLIC 3728 0 acl_equiv_mode
PUBLIC 3840 0 acl_error
PUBLIC 38c0 0 acl_extended_fd
PUBLIC 3960 0 acl_extended_file
PUBLIC 3970 0 acl_extended_file_nofollow
PUBLIC 3980 0 acl_from_mode
PUBLIC 3a40 0 acl_to_any_text
PUBLIC 4b58 0 perm_copy_fd
PUBLIC 4e98 0 perm_copy_file
STACK CFI INIT 1968 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1998 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 19dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19e4 x19: .cfa -16 + ^
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a28 4c .cfa: sp 0 + .ra: x30
STACK CFI 1a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a34 x19: .cfa -16 + ^
STACK CFI 1a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a78 fc .cfa: sp 0 + .ra: x30
STACK CFI 1a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1af4 x19: x19 x20: x20
STACK CFI 1afc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1b20 x19: x19 x20: x20
STACK CFI 1b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1b50 x19: x19 x20: x20
STACK CFI 1b68 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b6c x19: x19 x20: x20
STACK CFI INIT 1b78 30 .cfa: sp 0 + .ra: x30
STACK CFI 1b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ba0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1ba8 7c .cfa: sp 0 + .ra: x30
STACK CFI 1bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bbc x21: .cfa -16 + ^
STACK CFI 1c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c28 ac .cfa: sp 0 + .ra: x30
STACK CFI 1c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1cd8 128 .cfa: sp 0 + .ra: x30
STACK CFI 1cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ce8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d24 x21: .cfa -16 + ^
STACK CFI 1d94 x19: x19 x20: x20
STACK CFI 1d98 x21: x21
STACK CFI 1d9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1da0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1db4 x19: x19 x20: x20
STACK CFI 1db8 x21: x21
STACK CFI 1dbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1dc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1dc4 x19: x19 x20: x20
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1de0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1df4 x19: x19 x20: x20
STACK CFI 1dfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e00 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ea8 74 .cfa: sp 0 + .ra: x30
STACK CFI 1eac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ebc x19: .cfa -16 + ^
STACK CFI 1ef0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f20 3c .cfa: sp 0 + .ra: x30
STACK CFI 1f24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f60 84 .cfa: sp 0 + .ra: x30
STACK CFI 1f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1fe8 4c .cfa: sp 0 + .ra: x30
STACK CFI 1fec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ff4 x19: .cfa -16 + ^
STACK CFI 2028 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 202c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2038 c0 .cfa: sp 0 + .ra: x30
STACK CFI 203c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2048 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2054 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 20c0 x21: x21 x22: x22
STACK CFI 20c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 20d8 x21: x21 x22: x22
STACK CFI 20dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 20e4 x21: x21 x22: x22
STACK CFI 20f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20f8 50 .cfa: sp 0 + .ra: x30
STACK CFI 20fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2104 x19: .cfa -16 + ^
STACK CFI 2144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2148 78 .cfa: sp 0 + .ra: x30
STACK CFI 214c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 219c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21c0 80 .cfa: sp 0 + .ra: x30
STACK CFI 21c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2234 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2240 144 .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 224c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2254 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2304 x23: .cfa -16 + ^
STACK CFI 232c x23: x23
STACK CFI 234c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2350 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 237c x23: .cfa -16 + ^
STACK CFI 2380 x23: x23
STACK CFI INIT 2388 170 .cfa: sp 0 + .ra: x30
STACK CFI 238c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2394 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 242c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 24c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 24f8 5b4 .cfa: sp 0 + .ra: x30
STACK CFI 24fc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2504 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2524 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2540 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 254c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2550 x27: .cfa -128 + ^
STACK CFI 28d8 x23: x23 x24: x24
STACK CFI 28dc x25: x25 x26: x26
STACK CFI 28e0 x27: x27
STACK CFI 2904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2908 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x29: .cfa -208 + ^
STACK CFI 2a7c x23: x23 x24: x24
STACK CFI 2a80 x25: x25 x26: x26
STACK CFI 2a84 x27: x27
STACK CFI 2aa0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2aa4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2aa8 x27: .cfa -128 + ^
STACK CFI INIT 2ab0 fc .cfa: sp 0 + .ra: x30
STACK CFI 2ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2abc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ac4 x21: .cfa -16 + ^
STACK CFI 2b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2bb0 164 .cfa: sp 0 + .ra: x30
STACK CFI 2bb4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2bb8 .cfa: x29 208 +
STACK CFI 2bbc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2bdc x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^
STACK CFI 2c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c6c .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2d18 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 2d1c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2d20 .cfa: x29 208 +
STACK CFI 2d24 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2d34 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2d4c x23: .cfa -160 + ^
STACK CFI 2da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2da8 .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2ef8 54 .cfa: sp 0 + .ra: x30
STACK CFI 2efc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f04 x19: .cfa -16 + ^
STACK CFI 2f38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f50 60 .cfa: sp 0 + .ra: x30
STACK CFI 2f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f5c x19: .cfa -16 + ^
STACK CFI 2f84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2fb0 84 .cfa: sp 0 + .ra: x30
STACK CFI 2fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fc8 x19: .cfa -16 + ^
STACK CFI 2ff4 x19: x19
STACK CFI 2ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3000 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 301c x19: x19
STACK CFI 3020 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3024 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3028 x19: x19
STACK CFI 3030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3038 58 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3044 x19: .cfa -16 + ^
STACK CFI 306c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3070 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3090 80 .cfa: sp 0 + .ra: x30
STACK CFI 3094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 310c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3110 40 .cfa: sp 0 + .ra: x30
STACK CFI 3114 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 314c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3150 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3154 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 315c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3174 x21: .cfa -32 + ^
STACK CFI 31e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 31f8 dc .cfa: sp 0 + .ra: x30
STACK CFI 31fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3204 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3214 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 32a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 32a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 32d8 54 .cfa: sp 0 + .ra: x30
STACK CFI 32dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3330 74 .cfa: sp 0 + .ra: x30
STACK CFI 3334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 333c x19: .cfa -16 + ^
STACK CFI 3378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 337c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 339c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 33a8 80 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33b4 x19: .cfa -16 + ^
STACK CFI 3404 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3408 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 341c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3420 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3428 34 .cfa: sp 0 + .ra: x30
STACK CFI 342c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3450 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3454 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3460 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3478 40 .cfa: sp 0 + .ra: x30
STACK CFI 347c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3490 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3494 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 34b8 170 .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34c4 x19: .cfa -16 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 35ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3628 d8 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3634 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 36e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 36f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3700 28 .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 371c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3720 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3728 114 .cfa: sp 0 + .ra: x30
STACK CFI 372c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3734 x19: .cfa -16 + ^
STACK CFI 37b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3840 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38c0 9c .cfa: sp 0 + .ra: x30
STACK CFI 38c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38dc x19: .cfa -16 + ^
STACK CFI 38fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3900 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3960 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3970 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3980 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 398c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39b4 x21: .cfa -16 + ^
STACK CFI 3a1c x21: x21
STACK CFI 3a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3a28 x21: x21
STACK CFI 3a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a40 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a58 ac .cfa: sp 0 + .ra: x30
STACK CFI 3a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ae0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b08 150 .cfa: sp 0 + .ra: x30
STACK CFI 3b0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3b18 x25: .cfa -16 + ^
STACK CFI 3b38 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3bc0 x21: x21 x22: x22
STACK CFI 3bc4 x23: x23 x24: x24
STACK CFI 3bdc x19: x19 x20: x20
STACK CFI 3be4 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 3be8 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3c04 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 3c08 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3c20 x21: x21 x22: x22
STACK CFI 3c24 x23: x23 x24: x24
STACK CFI 3c38 x19: x19 x20: x20
STACK CFI 3c3c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c48 x21: x21 x22: x22
STACK CFI 3c4c x23: x23 x24: x24
STACK CFI 3c54 x19: x19 x20: x20
STACK CFI INIT 3c58 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ca0 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d50 15c .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d58 .cfa: x29 64 +
STACK CFI 3d60 x21: .cfa -32 + ^
STACK CFI 3d68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3ea0 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3eb0 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f68 930 .cfa: sp 0 + .ra: x30
STACK CFI 3f6c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3f7c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3f88 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 3f9c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3fcc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 401c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4398 x25: x25 x26: x26
STACK CFI 43a0 x27: x27 x28: x28
STACK CFI 43b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43b8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 4800 x27: x27 x28: x28
STACK CFI 480c x25: x25 x26: x26
STACK CFI 4814 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 4844 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4848 x27: x27 x28: x28
STACK CFI 484c x25: x25 x26: x26
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4868 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 4880 x25: x25 x26: x26
STACK CFI 4884 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4888 x27: x27 x28: x28
STACK CFI 4890 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 4898 a0 .cfa: sp 0 + .ra: x30
STACK CFI 489c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48a4 x19: .cfa -16 + ^
STACK CFI 4934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4938 144 .cfa: sp 0 + .ra: x30
STACK CFI 493c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4944 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4950 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4984 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 49d0 x21: x21 x22: x22
STACK CFI 49f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 49fc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 4a70 x21: x21 x22: x22
STACK CFI 4a78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 4a80 34 .cfa: sp 0 + .ra: x30
STACK CFI 4a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a8c x19: .cfa -16 + ^
STACK CFI 4ab0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ab8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ac8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b18 40 .cfa: sp 0 + .ra: x30
STACK CFI 4b1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b58 33c .cfa: sp 0 + .ra: x30
STACK CFI 4b5c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4b64 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4b74 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4b7c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4b94 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4c04 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4e98 47c .cfa: sp 0 + .ra: x30
STACK CFI 4e9c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 4ea4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 4eb4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 4ec4 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 4f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4f58 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 4f5c x27: .cfa -160 + ^
STACK CFI 5008 x27: x27
STACK CFI 5124 x27: .cfa -160 + ^
STACK CFI 5148 x27: x27
STACK CFI 5154 x27: .cfa -160 + ^
STACK CFI 5168 x27: x27
STACK CFI 52e0 x27: .cfa -160 + ^
STACK CFI 52e4 x27: x27
STACK CFI 5310 x27: .cfa -160 + ^
STACK CFI INIT 5318 74 .cfa: sp 0 + .ra: x30
STACK CFI 531c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5324 x21: .cfa -16 + ^
STACK CFI 5340 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5344 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5350 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5374 x19: x19 x20: x20
STACK CFI 537c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5380 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5388 x19: x19 x20: x20
STACK CFI INIT 5390 140 .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 539c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 53a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 53ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 548c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 54a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 54ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54d0 15c .cfa: sp 0 + .ra: x30
STACK CFI 54d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 54e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 54e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 553c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 555c x25: .cfa -16 + ^
STACK CFI 55ec x23: x23 x24: x24
STACK CFI 55f4 x25: x25
STACK CFI 5600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5604 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 560c x25: x25
STACK CFI 561c x23: x23 x24: x24
STACK CFI 5620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5630 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5708 520 .cfa: sp 0 + .ra: x30
STACK CFI 570c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5714 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 5720 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5734 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 5750 x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5864 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI INIT 5c28 118 .cfa: sp 0 + .ra: x30
STACK CFI 5c30 .cfa: sp 4192 +
STACK CFI 5c34 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 5c3c x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 5c4c x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 5c60 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 5cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5cd8 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x21: .cfa -4160 + ^ x22: .cfa -4152 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x29: .cfa -4192 + ^
