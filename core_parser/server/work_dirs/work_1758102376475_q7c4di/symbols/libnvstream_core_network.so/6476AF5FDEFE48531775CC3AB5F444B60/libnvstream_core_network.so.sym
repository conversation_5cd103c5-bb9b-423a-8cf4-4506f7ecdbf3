MODULE Linux arm64 6476AF5FDEFE48531775CC3AB5F444B60 libnvstream_core_network.so
INFO CODE_ID 5FAF7664FEDE53481775CC3AB5F444B6
PUBLIC 46b0 0 _init
PUBLIC 4ab0 0 call_weak_fn
PUBLIC 4ac4 0 deregister_tm_clones
PUBLIC 4af4 0 register_tm_clones
PUBLIC 4b30 0 __do_global_dtors_aux
PUBLIC 4b80 0 frame_dummy
PUBLIC 4b90 0 linvs::network::DgramSocket::DgramSocket(int)
PUBLIC 4bc0 0 linvs::network::DgramSocket::Open(int, int)
PUBLIC 4bd0 0 linvs::network::DgramSocket::SendTo(void const*, long, sockaddr const*, unsigned int, int)
PUBLIC 4c30 0 linvs::network::DgramSocket::RecvFrom(void*, unsigned long, sockaddr*, unsigned int*, int)
PUBLIC 4cb0 0 linvs::network::DgramSocket::SendToTimeout(void const*, long, long, sockaddr const*, unsigned int, int)
PUBLIC 4d60 0 linvs::network::DgramSocket::RecvFromTimeout(void*, unsigned long, long, sockaddr*, unsigned int*, int)
PUBLIC 4e10 0 linvs::network::Socket::~Socket()
PUBLIC 4e40 0 linvs::network::Socket::~Socket()
PUBLIC 4e80 0 linvs::network::DgramSocket::~DgramSocket()
PUBLIC 4eb0 0 linvs::network::DgramSocket::~DgramSocket()
PUBLIC 4ef0 0 linvs::network::Socket::Socket(int)
PUBLIC 4f10 0 linvs::network::Socket::Open(int, int, int)
PUBLIC 4f80 0 linvs::network::Socket::Close()
PUBLIC 4fc0 0 linvs::network::Socket::WaitEvent(pollfd*, unsigned long, long)
PUBLIC 5090 0 linvs::network::StreamSocket::StreamSocket(int)
PUBLIC 50c0 0 linvs::network::StreamSocket::Open(int, int)
PUBLIC 50d0 0 linvs::network::StreamSocket::Send(void const*, long, int)
PUBLIC 50e0 0 linvs::network::StreamSocket::Recv(void*, unsigned long, int)
PUBLIC 50f0 0 linvs::network::StreamSocket::SendTimeout(void const*, long, long, int)
PUBLIC 5180 0 linvs::network::StreamSocket::RecvTimeout(void*, unsigned long, long, int)
PUBLIC 5210 0 linvs::network::StreamSocket::~StreamSocket()
PUBLIC 5240 0 linvs::network::StreamSocket::~StreamSocket()
PUBLIC 5280 0 linvs::network::StreamSocketServer::Listen(int)
PUBLIC 5290 0 linvs::network::StreamSocketServer::Accept()
PUBLIC 5360 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<linvs::network::StreamSocket>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5370 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<linvs::network::StreamSocket>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 5390 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<linvs::network::StreamSocket>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 53f0 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<linvs::network::StreamSocket>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5400 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<linvs::network::StreamSocket>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5410 0 linvs::network::UdpDgramSocketClient::Open()
PUBLIC 5420 0 linvs::network::UdpDgramSocketClient::Bind(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short)
PUBLIC 54a0 0 linvs::network::UdpDgramSocketClient::SendTo(void const*, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short, int)
PUBLIC 5530 0 linvs::network::UdpDgramSocketClient::RecvFrom(void*, unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, unsigned short&, int)
PUBLIC 55d0 0 linvs::network::UdpDgramSocketClient::SendToTimeout(void const*, long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short, int)
PUBLIC 5670 0 linvs::network::UdpDgramSocketClient::RecvFromTimeout(void*, unsigned long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, unsigned short&, int)
PUBLIC 5710 0 linvs::network::UdpDgramSocketClient::CreateInstance(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short)
PUBLIC 5820 0 linvs::network::UdpDgramSocketClient::~UdpDgramSocketClient()
PUBLIC 5850 0 linvs::network::UdpDgramSocketClient::~UdpDgramSocketClient()
PUBLIC 5890 0 linvs::network::UnixDgramSocketClient::~UnixDgramSocketClient()
PUBLIC 5a00 0 linvs::network::UnixDgramSocketClient::~UnixDgramSocketClient() [clone .localalias]
PUBLIC 5a30 0 linvs::network::UnixDgramSocketClient::UnixDgramSocketClient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5b50 0 linvs::network::UnixDgramSocketClient::Open()
PUBLIC 5c60 0 linvs::network::UnixDgramSocketClient::SendTo(void const*, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 5d40 0 linvs::network::UnixDgramSocketClient::RecvFrom(void*, unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, int)
PUBLIC 5dc0 0 linvs::network::UnixDgramSocketClient::SendToTimeout(void const*, long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 5eb0 0 linvs::network::UnixDgramSocketClient::RecvFromTimeout(void*, unsigned long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, int)
PUBLIC 5f30 0 linvs::network::UnixDgramSocketClient::CreateInstance(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6020 0 linvs::network::UnixStreamSocketClient::Connect(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6110 0 linvs::network::UnixStreamSocketClient::Open()
PUBLIC 6120 0 linvs::network::UnixStreamSocketClient::CreateInstance()
PUBLIC 61b0 0 linvs::network::UnixStreamSocketClient::~UnixStreamSocketClient()
PUBLIC 61e0 0 linvs::network::UnixStreamSocketClient::~UnixStreamSocketClient()
PUBLIC 6220 0 linvs::network::UnixStreamSocketServer::~UnixStreamSocketServer()
PUBLIC 6370 0 linvs::network::UnixStreamSocketServer::~UnixStreamSocketServer() [clone .localalias]
PUBLIC 63a0 0 linvs::network::UnixStreamSocketServer::Bind(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 64a0 0 linvs::network::UnixStreamSocketServer::Open()
PUBLIC 64b0 0 linvs::network::UnixStreamSocketServer::CreateInstance()
PUBLIC 6588 0 _fini
STACK CFI INIT 4ac4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4af4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b30 50 .cfa: sp 0 + .ra: x30
STACK CFI 4b40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b48 x19: .cfa -16 + ^
STACK CFI 4b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e10 24 .cfa: sp 0 + .ra: x30
STACK CFI 4e14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e40 38 .cfa: sp 0 + .ra: x30
STACK CFI 4e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e54 x19: .cfa -16 + ^
STACK CFI 4e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e80 24 .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ea0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4eb0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ec4 x19: .cfa -16 + ^
STACK CFI 4ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b90 30 .cfa: sp 0 + .ra: x30
STACK CFI 4b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b9c x19: .cfa -16 + ^
STACK CFI 4bbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4bc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bd0 60 .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4c30 74 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c54 x19: .cfa -16 + ^
STACK CFI 4c6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4cb0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4cb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4cc0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4cd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ce4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d24 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4d60 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4d64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d70 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d84 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d94 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4dd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4ef0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f10 64 .cfa: sp 0 + .ra: x30
STACK CFI 4f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f1c x19: .cfa -16 + ^
STACK CFI 4f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4f48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f80 34 .cfa: sp 0 + .ra: x30
STACK CFI 4f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f8c x19: .cfa -16 + ^
STACK CFI 4fb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4fc0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4fc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4fe0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5054 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5210 24 .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5230 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5240 38 .cfa: sp 0 + .ra: x30
STACK CFI 5244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5254 x19: .cfa -16 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5090 30 .cfa: sp 0 + .ra: x30
STACK CFI 5094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 509c x19: .cfa -16 + ^
STACK CFI 50bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 50c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 50d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 50f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5100 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5114 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5154 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5180 88 .cfa: sp 0 + .ra: x30
STACK CFI 5184 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5190 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 51e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5370 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 60 .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 53f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5290 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5294 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 52ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 52c0 x21: .cfa -48 + ^
STACK CFI 530c x21: x21
STACK CFI 5310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5314 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 534c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5820 24 .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5840 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5850 38 .cfa: sp 0 + .ra: x30
STACK CFI 5854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5864 x19: .cfa -16 + ^
STACK CFI 5884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5410 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5420 80 .cfa: sp 0 + .ra: x30
STACK CFI 5424 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5434 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5478 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 549c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 54a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 54b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 54c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 54d8 x23: .cfa -32 + ^
STACK CFI 551c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5520 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5530 9c .cfa: sp 0 + .ra: x30
STACK CFI 5534 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5540 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5550 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 55c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 55c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 55d0 98 .cfa: sp 0 + .ra: x30
STACK CFI 55d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 55e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 55f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5608 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5658 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5670 98 .cfa: sp 0 + .ra: x30
STACK CFI 5674 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5680 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5690 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5710 104 .cfa: sp 0 + .ra: x30
STACK CFI 5714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 571c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5724 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5774 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5890 168 .cfa: sp 0 + .ra: x30
STACK CFI 5894 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 58a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 58f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 58f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5980 x21: x21 x22: x22
STACK CFI 5984 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 5a00 28 .cfa: sp 0 + .ra: x30
STACK CFI 5a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a0c x19: .cfa -16 + ^
STACK CFI 5a24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a30 120 .cfa: sp 0 + .ra: x30
STACK CFI 5a34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5a40 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a58 x21: .cfa -32 + ^
STACK CFI 5ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ab4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 5ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 5b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5b50 10c .cfa: sp 0 + .ra: x30
STACK CFI 5b54 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5b64 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b90 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5c60 dc .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5c78 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5c84 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5c8c x23: .cfa -128 + ^
STACK CFI 5cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5cf8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5d40 78 .cfa: sp 0 + .ra: x30
STACK CFI 5d44 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5d54 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5d60 x21: .cfa -144 + ^
STACK CFI 5dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5db0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5dc0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5dc4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5dd8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5de4 x21: .cfa -160 + ^ x23: .cfa -152 + ^
STACK CFI 5dec x24: .cfa -144 + ^
STACK CFI 5e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x23: x23 x24: x24 x29: x29
STACK CFI 5e58 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x23: .cfa -152 + ^ x24: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5eb0 74 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5ec4 x21: .cfa -144 + ^
STACK CFI 5ed0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5f30 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5f34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f44 x21: .cfa -16 + ^
STACK CFI 5f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5fc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5fe0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 61b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 61b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 61e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61f4 x19: .cfa -16 + ^
STACK CFI 6214 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6020 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6038 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6098 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6110 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6120 90 .cfa: sp 0 + .ra: x30
STACK CFI 6124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6130 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 616c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6194 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6220 148 .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6230 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6238 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6288 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6370 28 .cfa: sp 0 + .ra: x30
STACK CFI 6374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 637c x19: .cfa -16 + ^
STACK CFI 6394 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 63a0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 63a4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 63b8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 63c0 x21: .cfa -128 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 642c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x29: .cfa -160 + ^
STACK CFI INIT 64a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 64b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6514 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 656c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
