MODULE Linux arm64 B9741D7DA96674C1B52F21FDCA75725F0 libdebian-installer.so.4
INFO CODE_ID 7D1D74B966A9C174B52F21FDCA75725F2234DF48
PUBLIC 53cc 0 di_exec_full
PUBLIC 540c 0 di_exec_env_full
PUBLIC 5454 0 di_exec_path_full
PUBLIC 5494 0 di_exec_shell_full
PUBLIC 551c 0 di_exec_prepare_chdir
PUBLIC 5538 0 di_exec_prepare_chroot
PUBLIC 556c 0 di_exec_io_log
PUBLIC 5590 0 di_exec_mangle_status
PUBLIC 5668 0 di_hash_table_new_full
PUBLIC 56e8 0 di_hash_table_new
PUBLIC 56f4 0 di_hash_table_destroy
PUBLIC 5790 0 di_hash_table_lookup
PUBLIC 57b4 0 di_hash_table_insert
PUBLIC 5940 0 di_hash_table_foreach
PUBLIC 59a0 0 di_hash_table_size
PUBLIC 5af8 0 di_log_handler_default
PUBLIC 5bfc 0 di_log_handler_syslog
PUBLIC 5d80 0 di_log_set_handler
PUBLIC 5dd4 0 di_vlog
PUBLIC 5eb4 0 di_log_real_4_0
PUBLIC 5f4c 0 di_malloc
PUBLIC 5f88 0 di_malloc0
PUBLIC 5fcc 0 di_realloc
PUBLIC 6008 0 di_free
PUBLIC 600c 0 di_mem_chunk_new
PUBLIC 60b0 0 di_mem_chunk_alloc
PUBLIC 6150 0 di_mem_chunk_alloc0
PUBLIC 6184 0 di_mem_chunk_destroy
PUBLIC 61b8 0 di_mem_chunk_size
PUBLIC 635c 0 di_package_destroy
PUBLIC 63c4 0 di_package_version_free
PUBLIC 63c8 0 di_package_version_compare
PUBLIC 642c 0 di_package_version_parse
PUBLIC 658c 0 di_package_array_text_from
PUBLIC 6610 0 di_package_parser_read_dependency
PUBLIC 6788 0 di_package_parser_write_priority
PUBLIC 6800 0 di_package_parser_write_dependency
PUBLIC 693c 0 di_package_parser_read_description
PUBLIC 69c4 0 di_package_parser_write_status
PUBLIC 6a6c 0 di_package_parser_write_description
PUBLIC 6b60 0 di_package_parser_read_priority
PUBLIC 6b84 0 di_package_parser_read_status
PUBLIC 6c5c 0 di_package_parser_info
PUBLIC 6c80 0 di_package_special_read_file
PUBLIC 6cfc 0 di_package_parser_read_name
PUBLIC 6de8 0 di_packages_alloc
PUBLIC 6e64 0 di_packages_allocator_alloc
PUBLIC 6e8c 0 di_packages_free
PUBLIC 6eb0 0 di_packages_allocator_free
PUBLIC 6edc 0 di_packages_get_package
PUBLIC 6f50 0 di_packages_append_package
PUBLIC 6f9c 0 di_packages_get_package_new
PUBLIC 7304 0 di_packages_resolve_dependencies
PUBLIC 7430 0 di_packages_resolve_dependencies_array
PUBLIC 7508 0 di_packages_resolve_dependencies_mark
PUBLIC 7570 0 di_packages_parser_read_name
PUBLIC 75f8 0 di_packages_parser_info
PUBLIC 761c 0 di_packages_minimal_parser_info
PUBLIC 7640 0 di_packages_status_parser_info
PUBLIC 7664 0 di_packages_special_read_file
PUBLIC 76fc 0 di_packages_special_write_file
PUBLIC 7748 0 di_parser_info_alloc
PUBLIC 777c 0 di_parser_info_free
PUBLIC 77b0 0 di_parser_info_add
PUBLIC 77f4 0 di_parser_read_boolean
PUBLIC 7838 0 di_parser_write_boolean
PUBLIC 78b0 0 di_parser_read_int
PUBLIC 78e0 0 di_parser_write_int
PUBLIC 79f0 0 di_parser_read_string
PUBLIC 7a2c 0 di_parser_write_string
PUBLIC 7b04 0 di_parser_rfc822_read
PUBLIC 7d90 0 di_parser_rfc822_read_file
PUBLIC 7e98 0 di_parser_rfc822_write_file
PUBLIC 8008 0 di_slist_alloc
PUBLIC 8010 0 di_slist_destroy
PUBLIC 8050 0 di_slist_free
PUBLIC 8054 0 di_slist_append
PUBLIC 8090 0 di_slist_append_chunk
PUBLIC 80cc 0 di_slist_prepend
PUBLIC 8104 0 di_slist_prepend_chunk
PUBLIC 816c 0 di_snprintfcat
PUBLIC 823c 0 di_stradup
PUBLIC 826c 0 di_rstring_equal
PUBLIC 82a8 0 di_rstring_hash
PUBLIC 85b4 0 di_tree_new_full
PUBLIC 85e0 0 di_tree_new
PUBLIC 85ec 0 di_tree_destroy
PUBLIC 8608 0 di_tree_insert
PUBLIC 8658 0 di_tree_foreach
PUBLIC 8660 0 di_tree_size
PUBLIC 8668 0 di_tree_lookup
PUBLIC 86bc 0 di_init
PUBLIC 86d4 0 di_progname_get
PUBLIC 86e0 0 di_system_devfs_map_from
PUBLIC 8964 0 di_system_dpkg_package_control_file_exec
PUBLIC 8b1c 0 di_system_package_destroy
PUBLIC 8b3c 0 di_system_packages_allocator_alloc
PUBLIC 8b64 0 di_system_packages_alloc
PUBLIC 8ba4 0 di_system_package_check_subarchitecture
PUBLIC 8cf0 0 di_system_package_parser_info
PUBLIC 8d14 0 di_system_packages_parser_info
PUBLIC 8d38 0 di_system_packages_status_parser_info
PUBLIC 8d5c 0 di_system_packages_resolve_dependencies_array_permissive
PUBLIC 8ddc 0 di_system_packages_resolve_dependencies_mark_anna
PUBLIC 8e5c 0 di_system_packages_resolve_dependencies_mark_kernel
PUBLIC 8ebc 0 di_prebaseconfig_append
PUBLIC 8ff0 0 di_system_init
PUBLIC 9010 0 di_system_subarch_analyze_guess
PUBLIC 9014 0 di_system_subarch_analyze
STACK CFI INIT 4e08 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e38 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e70 48 .cfa: sp 0 + .ra: x30
STACK CFI 4e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e7c x19: .cfa -16 + ^
STACK CFI 4eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4eb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ebc 510 .cfa: sp 0 + .ra: x30
STACK CFI 4ec0 .cfa: sp 1248 +
STACK CFI 4ec8 .ra: .cfa -1240 + ^ x29: .cfa -1248 + ^
STACK CFI 4ecc .cfa: x29 1248 +
STACK CFI 4ed0 v8: .cfa -1152 + ^ v9: .cfa -1144 + ^
STACK CFI 4edc x27: .cfa -1168 + ^ x28: .cfa -1160 + ^
STACK CFI 4ef4 x19: .cfa -1232 + ^ x20: .cfa -1224 + ^ x21: .cfa -1216 + ^ x22: .cfa -1208 + ^
STACK CFI 4efc x23: .cfa -1200 + ^ x24: .cfa -1192 + ^
STACK CFI 4f08 x25: .cfa -1184 + ^ x26: .cfa -1176 + ^
STACK CFI 4f10 v10: .cfa -1136 + ^ v11: .cfa -1128 + ^
STACK CFI 4f20 v12: .cfa -1120 + ^
STACK CFI 53a0 .cfa: sp 1248 +
STACK CFI 53c8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 53cc 40 .cfa: sp 0 + .ra: x30
STACK CFI 53d0 .cfa: sp 48 +
STACK CFI 53fc .ra: .cfa -16 + ^
STACK CFI 5408 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 540c 48 .cfa: sp 0 + .ra: x30
STACK CFI 5410 .cfa: sp 48 +
STACK CFI 5444 .ra: .cfa -16 + ^
STACK CFI 5450 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 5454 40 .cfa: sp 0 + .ra: x30
STACK CFI 5458 .cfa: sp 48 +
STACK CFI 5484 .ra: .cfa -16 + ^
STACK CFI 5490 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 5494 88 .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 96 +
STACK CFI 549c .ra: .cfa -56 + ^ x19: .cfa -64 + ^
STACK CFI 5518 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 551c 1c .cfa: sp 0 + .ra: x30
STACK CFI 5520 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 5534 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 5538 34 .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 5560 .cfa: sp 0 + .ra: .ra
STACK CFI 5564 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 556c 24 .cfa: sp 0 + .ra: x30
STACK CFI 5570 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 558c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 5590 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55d0 98 .cfa: sp 0 + .ra: x30
STACK CFI 55d4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 55ec .ra: .cfa -16 + ^
STACK CFI 5664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 5668 80 .cfa: sp 0 + .ra: x30
STACK CFI 566c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5674 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 56d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 56dc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 56e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 56f4 9c .cfa: sp 0 + .ra: x30
STACK CFI 56f8 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5700 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5708 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 5734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5738 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 5790 24 .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 57a8 .cfa: sp 0 + .ra: .ra
STACK CFI 57ac .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 57b4 18c .cfa: sp 0 + .ra: x30
STACK CFI 57b8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 57c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 57d0 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 581c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5820 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 5940 60 .cfa: sp 0 + .ra: x30
STACK CFI 5944 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 594c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5958 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 5974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5978 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 59a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 59a8 150 .cfa: sp 0 + .ra: x30
STACK CFI 59ac .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 59b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 59d8 .ra: .cfa -48 + ^
STACK CFI 5ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5ad8 .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 5af8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5afc .cfa: sp 1344 +
STACK CFI 5b00 x21: .cfa -1328 + ^ x22: .cfa -1320 + ^
STACK CFI 5b10 .ra: .cfa -1304 + ^ x23: .cfa -1312 + ^
STACK CFI 5b24 x19: .cfa -1344 + ^ x20: .cfa -1336 + ^
STACK CFI 5be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 5bec 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bfc 184 .cfa: sp 0 + .ra: x30
STACK CFI 5c00 .cfa: sp 1344 +
STACK CFI 5c04 x21: .cfa -1328 + ^ x22: .cfa -1320 + ^
STACK CFI 5c0c x19: .cfa -1344 + ^ x20: .cfa -1336 + ^
STACK CFI 5c1c .ra: .cfa -1304 + ^ x23: .cfa -1312 + ^
STACK CFI 5d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 5d80 54 .cfa: sp 0 + .ra: x30
STACK CFI 5d84 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d98 .ra: .cfa -32 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 5dd4 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5dd8 .cfa: sp 1152 +
STACK CFI 5de0 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI 5df0 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 5e0c .ra: .cfa -1120 + ^
STACK CFI 5eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 5eb4 98 .cfa: sp 0 + .ra: x30
STACK CFI 5eb8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -272 + ^
STACK CFI 5f48 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5f4c 3c .cfa: sp 0 + .ra: x30
STACK CFI 5f50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 5f84 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5f88 44 .cfa: sp 0 + .ra: x30
STACK CFI 5f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 5fc8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5fcc 3c .cfa: sp 0 + .ra: x30
STACK CFI 5fd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 6004 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6008 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 600c a4 .cfa: sp 0 + .ra: x30
STACK CFI 6018 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6028 .ra: .cfa -16 + ^
STACK CFI 609c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 60a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 60b0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 60b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 614c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6150 34 .cfa: sp 0 + .ra: x30
STACK CFI 6154 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 615c .ra: .cfa -16 + ^
STACK CFI 6180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6184 34 .cfa: sp 0 + .ra: x30
STACK CFI 6188 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6194 .ra: .cfa -16 + ^
STACK CFI 61a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 61a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 61b8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61e0 17c .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 61ec .ra: .cfa -16 + ^
STACK CFI 6288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 628c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 635c 64 .cfa: sp 0 + .ra: x30
STACK CFI 6360 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 63b8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 63c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c4 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c8 64 .cfa: sp 0 + .ra: x30
STACK CFI 63e0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63f4 .ra: .cfa -16 + ^
STACK CFI 640c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6420 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 642c 10c .cfa: sp 0 + .ra: x30
STACK CFI 6434 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6450 .ra: .cfa -24 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 6534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 6538 54 .cfa: sp 0 + .ra: x30
STACK CFI 653c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6544 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6550 .ra: .cfa -16 + ^
STACK CFI 6588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 658c 5c .cfa: sp 0 + .ra: x30
STACK CFI 6590 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 65b4 .ra: .cfa -48 + ^
STACK CFI 65e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 65e8 28 .cfa: sp 0 + .ra: x30
STACK CFI 65ec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 660c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6610 178 .cfa: sp 0 + .ra: x30
STACK CFI 6614 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 661c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 662c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6634 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6648 .ra: .cfa -24 + ^ x27: .cfa -32 + ^
STACK CFI 6668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 666c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI INIT 6788 78 .cfa: sp 0 + .ra: x30
STACK CFI 678c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 679c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 67fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6800 13c .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6810 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 681c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6828 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 684c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6854 .ra: .cfa -48 + ^
STACK CFI 6938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 693c 88 .cfa: sp 0 + .ra: x30
STACK CFI 6940 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 694c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 6954 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 69ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 69b0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 69c4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 69c8 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 69d8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 6a0c .ra: .cfa -176 + ^
STACK CFI 6a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 6a6c f4 .cfa: sp 0 + .ra: x30
STACK CFI 6a70 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6a74 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6a7c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6a94 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6aac .ra: .cfa -48 + ^
STACK CFI 6b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT 6b60 24 .cfa: sp 0 + .ra: x30
STACK CFI 6b64 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6b80 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6b84 d8 .cfa: sp 0 + .ra: x30
STACK CFI 6b88 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6b98 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6bb4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6bc4 .ra: .cfa -48 + ^
STACK CFI 6c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 6c5c 24 .cfa: sp 0 + .ra: x30
STACK CFI 6c60 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6c7c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6c80 7c .cfa: sp 0 + .ra: x30
STACK CFI 6c84 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c90 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 6cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6cfc 34 .cfa: sp 0 + .ra: x30
STACK CFI 6d00 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d10 .ra: .cfa -16 + ^
STACK CFI 6d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6d30 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d74 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d88 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6da0 24 .cfa: sp 0 + .ra: x30
STACK CFI 6da8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 6dc0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 6dc4 24 .cfa: sp 0 + .ra: x30
STACK CFI 6dcc .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 6de4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 6de8 40 .cfa: sp 0 + .ra: x30
STACK CFI 6dec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6e24 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6e28 3c .cfa: sp 0 + .ra: x30
STACK CFI 6e2c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6e60 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6e64 28 .cfa: sp 0 + .ra: x30
STACK CFI 6e68 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6e88 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6e8c 24 .cfa: sp 0 + .ra: x30
STACK CFI 6e94 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6ea8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6eb0 2c .cfa: sp 0 + .ra: x30
STACK CFI 6eb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6ed8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6edc 74 .cfa: sp 0 + .ra: x30
STACK CFI 6ee4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f04 .ra: .cfa -64 + ^
STACK CFI 6f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6f50 4c .cfa: sp 0 + .ra: x30
STACK CFI 6f54 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6f64 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6f9c 78 .cfa: sp 0 + .ra: x30
STACK CFI 6fa0 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fa8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fb8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 7008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 700c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 7014 1ac .cfa: sp 0 + .ra: x30
STACK CFI 7038 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7044 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7050 .ra: .cfa -16 + ^
STACK CFI 70e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 70e8 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 71c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 71d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 71d8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 71f0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 71f4 54 .cfa: sp 0 + .ra: x30
STACK CFI 7218 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 723c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7248 bc .cfa: sp 0 + .ra: x30
STACK CFI 724c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 725c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7264 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 727c .ra: .cfa -48 + ^
STACK CFI 7300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 7304 74 .cfa: sp 0 + .ra: x30
STACK CFI 7308 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 731c .ra: .cfa -88 + ^ x21: .cfa -96 + ^
STACK CFI 7374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 7378 b8 .cfa: sp 0 + .ra: x30
STACK CFI 737c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7388 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7398 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 73ac .ra: .cfa -48 + ^
STACK CFI 742c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 7430 74 .cfa: sp 0 + .ra: x30
STACK CFI 7434 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7448 .ra: .cfa -88 + ^ x21: .cfa -96 + ^
STACK CFI 74a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 74a4 64 .cfa: sp 0 + .ra: x30
STACK CFI 74a8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 74b0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 74d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 74d4 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 7508 68 .cfa: sp 0 + .ra: x30
STACK CFI 750c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7540 .ra: .cfa -80 + ^
STACK CFI 756c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7570 58 .cfa: sp 0 + .ra: x30
STACK CFI 7574 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 757c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 75c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 75c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f8 24 .cfa: sp 0 + .ra: x30
STACK CFI 75fc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7618 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 761c 24 .cfa: sp 0 + .ra: x30
STACK CFI 7620 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 763c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7640 24 .cfa: sp 0 + .ra: x30
STACK CFI 7644 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7660 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7664 98 .cfa: sp 0 + .ra: x30
STACK CFI 7668 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7670 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 76f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 76fc 4c .cfa: sp 0 + .ra: x30
STACK CFI 7700 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7708 .ra: .cfa -32 + ^
STACK CFI 7744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7748 34 .cfa: sp 0 + .ra: x30
STACK CFI 774c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7778 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 777c 34 .cfa: sp 0 + .ra: x30
STACK CFI 7780 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7790 .ra: .cfa -16 + ^
STACK CFI 77ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 77b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 77b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 77c0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 77d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 77d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 77f4 44 .cfa: sp 0 + .ra: x30
STACK CFI 77f8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7814 .ra: .cfa -16 + ^
STACK CFI 7834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7838 78 .cfa: sp 0 + .ra: x30
STACK CFI 7850 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 78b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 78b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78c8 .ra: .cfa -16 + ^
STACK CFI 78dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 78e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 78e4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 78f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7920 .ra: .cfa -80 + ^
STACK CFI 797c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 7980 50 .cfa: sp 0 + .ra: x30
STACK CFI 7984 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7990 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79a0 .ra: .cfa -16 + ^
STACK CFI 79cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 79d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 79f0 3c .cfa: sp 0 + .ra: x30
STACK CFI 79f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a00 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 7a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 7a2c 7c .cfa: sp 0 + .ra: x30
STACK CFI 7a30 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7a48 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 7aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 7aa8 5c .cfa: sp 0 + .ra: x30
STACK CFI 7aac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ac8 .ra: .cfa -16 + ^
STACK CFI 7b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7b04 28c .cfa: sp 0 + .ra: x30
STACK CFI 7b08 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 7b10 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7b20 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7b2c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7b54 .ra: .cfa -128 + ^ v8: .cfa -120 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7d8c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 7d90 108 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 7d9c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 7dc0 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 7dcc .ra: .cfa -160 + ^
STACK CFI 7e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT 7e98 170 .cfa: sp 0 + .ra: x30
STACK CFI 7ea0 .cfa: sp 4224 +
STACK CFI 7ea4 x19: .cfa -4224 + ^ x20: .cfa -4216 + ^
STACK CFI 7eac x23: .cfa -4192 + ^ x24: .cfa -4184 + ^
STACK CFI 7ebc x21: .cfa -4208 + ^ x22: .cfa -4200 + ^ x25: .cfa -4176 + ^ x26: .cfa -4168 + ^
STACK CFI 7ed4 x27: .cfa -4160 + ^ x28: .cfa -4152 + ^
STACK CFI 7ee0 .ra: .cfa -4144 + ^
STACK CFI 8004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 8008 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8010 40 .cfa: sp 0 + .ra: x30
STACK CFI 8014 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8020 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 802c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8030 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 8050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8054 3c .cfa: sp 0 + .ra: x30
STACK CFI 8058 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 8084 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 8088 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 8090 3c .cfa: sp 0 + .ra: x30
STACK CFI 8094 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 80c0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 80c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 80cc 38 .cfa: sp 0 + .ra: x30
STACK CFI 80d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 8100 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8104 38 .cfa: sp 0 + .ra: x30
STACK CFI 8108 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 8138 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 813c 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 816c d0 .cfa: sp 0 + .ra: x30
STACK CFI 8170 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 81ac .ra: .cfa -312 + ^ x21: .cfa -320 + ^
STACK CFI 8238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 823c 30 .cfa: sp 0 + .ra: x30
STACK CFI 8240 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8250 .ra: .cfa -16 + ^
STACK CFI 8268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 826c 3c .cfa: sp 0 + .ra: x30
STACK CFI 8284 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 829c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 82a8 58 .cfa: sp 0 + .ra: x30
STACK CFI 82ac .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 82b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 82c4 .ra: .cfa -16 + ^
STACK CFI 82d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 82dc .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 8300 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8340 48 .cfa: sp 0 + .ra: x30
STACK CFI 8344 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8350 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 8384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 8388 30 .cfa: sp 0 + .ra: x30
STACK CFI 838c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 83b4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 83b8 5c .cfa: sp 0 + .ra: x30
STACK CFI 83c0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83d0 .ra: .cfa -16 + ^
STACK CFI 840c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8414 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 8418 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8430 .ra: .cfa -32 + ^
STACK CFI 856c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8570 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 85b4 2c .cfa: sp 0 + .ra: x30
STACK CFI 85b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 85dc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 85e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 85ec 1c .cfa: sp 0 + .ra: x30
STACK CFI 85f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8604 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8608 50 .cfa: sp 0 + .ra: x30
STACK CFI 860c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 863c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 8640 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 8658 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8668 54 .cfa: sp 0 + .ra: x30
STACK CFI 866c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8678 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 86a8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 86bc 18 .cfa: sp 0 + .ra: x30
STACK CFI 86c0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 86cc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 86d4 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 86e0 284 .cfa: sp 0 + .ra: x30
STACK CFI 86e4 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 86ec x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 86fc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 871c .ra: .cfa -152 + ^ x25: .cfa -160 + ^
STACK CFI 8960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 8964 138 .cfa: sp 0 + .ra: x30
STACK CFI 896c .cfa: sp 4320 +
STACK CFI 8980 .ra: .cfa -4296 + ^ x29: .cfa -4304 + ^
STACK CFI 8984 .cfa: x29 4304 +
STACK CFI 8990 x19: .cfa -4288 + ^ x20: .cfa -4280 + ^
STACK CFI 89a0 x23: .cfa -4256 + ^
STACK CFI 89a8 x21: .cfa -4272 + ^ x22: .cfa -4264 + ^
STACK CFI 8a80 .cfa: sp 4320 +
STACK CFI 8a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8a9c 5c .cfa: sp 0 + .ra: x30
STACK CFI 8aa0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8aec .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 8af0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 8af8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b1c 1c .cfa: sp 0 + .ra: x30
STACK CFI 8b20 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8b34 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8b38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b3c 28 .cfa: sp 0 + .ra: x30
STACK CFI 8b40 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8b60 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8b64 40 .cfa: sp 0 + .ra: x30
STACK CFI 8b68 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8ba0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8ba4 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8ba8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8bb8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8c00 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 8c44 ac .cfa: sp 0 + .ra: x30
STACK CFI 8c48 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8c64 .ra: .cfa -16 + ^
STACK CFI 8c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8c8c .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 8cf0 24 .cfa: sp 0 + .ra: x30
STACK CFI 8cf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8d10 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8d14 24 .cfa: sp 0 + .ra: x30
STACK CFI 8d18 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8d34 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8d38 24 .cfa: sp 0 + .ra: x30
STACK CFI 8d3c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8d58 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8d5c 80 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -112 + ^
STACK CFI 8dd8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8ddc 80 .cfa: sp 0 + .ra: x30
STACK CFI 8de0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -96 + ^
STACK CFI 8e58 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8e5c 60 .cfa: sp 0 + .ra: x30
STACK CFI 8e60 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 8e70 .ra: .cfa -408 + ^ x21: .cfa -416 + ^
STACK CFI 8eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 8ebc 134 .cfa: sp 0 + .ra: x30
STACK CFI 8ec0 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 8ec8 .ra: .cfa -424 + ^ x21: .cfa -432 + ^
STACK CFI 8fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 8ff0 20 .cfa: sp 0 + .ra: x30
STACK CFI 8ff4 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 9004 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 9010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9014 28 .cfa: sp 0 + .ra: x30
STACK CFI 9018 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 9038 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 9040 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9050 10 .cfa: sp 0 + .ra: x30
