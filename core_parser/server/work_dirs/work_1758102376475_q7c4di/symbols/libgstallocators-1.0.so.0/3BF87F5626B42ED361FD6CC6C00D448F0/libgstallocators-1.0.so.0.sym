MODULE Linux arm64 3BF87F5626B42ED361FD6CC6C00D448F0 libgstallocators-1.0.so.0
INFO CODE_ID 567FF83BB426D32E61FD6CC6C00D448FFBBB21AC
PUBLIC 19e8 0 gst_fd_allocator_get_type
PUBLIC 1a58 0 gst_fd_allocator_new
PUBLIC 1a88 0 gst_fd_allocator_alloc
PUBLIC 1bc8 0 gst_is_fd_memory
PUBLIC 1c50 0 gst_fd_memory_get_fd
PUBLIC 1d00 0 gst_phys_memory_allocator_get_type
PUBLIC 1da8 0 gst_is_phys_memory
PUBLIC 1e00 0 gst_phys_memory_get_phys_addr
PUBLIC 21a8 0 gst_dmabuf_allocator_get_type
PUBLIC 2218 0 gst_dmabuf_allocator_new
PUBLIC 2278 0 gst_dmabuf_allocator_alloc
PUBLIC 2310 0 gst_dmabuf_allocator_alloc_with_flags
PUBLIC 23b0 0 gst_is_dmabuf_memory
PUBLIC 2438 0 gst_dmabuf_memory_get_fd
STACK CFI INIT 1368 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1398 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 13dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13e4 x19: .cfa -16 + ^
STACK CFI 141c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1420 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1428 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1468 50 .cfa: sp 0 + .ra: x30
STACK CFI 146c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1474 x19: .cfa -16 + ^
STACK CFI 149c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14b8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 14bc .cfa: sp 64 +
STACK CFI 14c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1598 d4 .cfa: sp 0 + .ra: x30
STACK CFI 159c .cfa: sp 48 +
STACK CFI 15a0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15cc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1604 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1670 204 .cfa: sp 0 + .ra: x30
STACK CFI 1674 .cfa: sp 80 +
STACK CFI 1678 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1684 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16dc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 175c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1788 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1820 x23: .cfa -16 + ^
STACK CFI 1870 x23: x23
STACK CFI INIT 1878 88 .cfa: sp 0 + .ra: x30
STACK CFI 187c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1884 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 18fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1900 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 190c x19: .cfa -16 + ^
STACK CFI 1974 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 19d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19e8 6c .cfa: sp 0 + .ra: x30
STACK CFI 19ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a58 30 .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a64 x19: .cfa -16 + ^
STACK CFI 1a84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a88 140 .cfa: sp 0 + .ra: x30
STACK CFI 1a8c .cfa: sp 80 +
STACK CFI 1a90 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a98 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1aa0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1aac x23: .cfa -16 + ^
STACK CFI 1b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1b8c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1bc8 88 .cfa: sp 0 + .ra: x30
STACK CFI 1bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bd8 x19: .cfa -16 + ^
STACK CFI 1c04 x19: x19
STACK CFI 1c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c38 x19: x19
STACK CFI 1c3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c48 x19: x19
STACK CFI 1c4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c50 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c9c x19: x19 x20: x20
STACK CFI 1ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1cc8 x19: x19 x20: x20
STACK CFI 1ccc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1cf8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d00 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1da8 54 .cfa: sp 0 + .ra: x30
STACK CFI 1dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dc4 x19: .cfa -16 + ^
STACK CFI 1dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e00 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1ea0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ec8 50 .cfa: sp 0 + .ra: x30
STACK CFI 1ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ed4 x19: .cfa -16 + ^
STACK CFI 1efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f18 11c .cfa: sp 0 + .ra: x30
STACK CFI 1f1c .cfa: sp 96 +
STACK CFI 1f24 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1f50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1fc0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1fdc x23: .cfa -32 + ^
STACK CFI 2028 x23: x23
STACK CFI 2030 x23: .cfa -32 + ^
STACK CFI INIT 2038 128 .cfa: sp 0 + .ra: x30
STACK CFI 203c .cfa: sp 96 +
STACK CFI 2040 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2048 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2074 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 20e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20e8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2104 x23: .cfa -32 + ^
STACK CFI 2154 x23: x23
STACK CFI 215c x23: .cfa -32 + ^
STACK CFI INIT 2160 44 .cfa: sp 0 + .ra: x30
STACK CFI 2164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 216c x19: .cfa -16 + ^
STACK CFI 218c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2190 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 21a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21a8 6c .cfa: sp 0 + .ra: x30
STACK CFI 21ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2218 5c .cfa: sp 0 + .ra: x30
STACK CFI 221c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2224 x19: .cfa -16 + ^
STACK CFI 2250 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2254 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2278 98 .cfa: sp 0 + .ra: x30
STACK CFI 227c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2284 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2290 x21: .cfa -16 + ^
STACK CFI 22dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 230c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2310 9c .cfa: sp 0 + .ra: x30
STACK CFI 2314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 231c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2328 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 237c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 23b0 88 .cfa: sp 0 + .ra: x30
STACK CFI 23b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23c0 x19: .cfa -16 + ^
STACK CFI 23ec x19: x19
STACK CFI 23f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2414 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2418 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2420 x19: x19
STACK CFI 2424 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2428 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2430 x19: x19
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2438 54 .cfa: sp 0 + .ra: x30
STACK CFI 243c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2444 x19: .cfa -16 + ^
STACK CFI 2478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 247c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
