MODULE Linux arm64 979E84AA6E30F4E22BE3F50623AC3A4B0 libnvidia-container-go.so.1
INFO CODE_ID AA849E97306EE2F42BE3F50623AC3A4B415555E9
PUBLIC 8f038 0 _init
PUBLIC f08e0 0 _cgo_topofstack
PUBLIC f5f60 0 _cgo_panic
PUBLIC f5fb0 0 crosscall2
PUBLIC 19ce50 0 _cgoexp_b57e814d6d16_GetDeviceCGroupVersion
PUBLIC 19cec0 0 _cgoexp_b57e814d6d16_GetDeviceCGroupMountPath
PUBLIC 19cf40 0 _cgoexp_b57e814d6d16_GetDeviceCGroupRootPath
PUBLIC 19cfc0 0 _cgoexp_b57e814d6d16_AddDeviceRules
PUBLIC 19d040 0 GetDeviceCGroupVersion
PUBLIC 19d0f0 0 GetDeviceCGroupMountPath
PUBLIC 19d1c0 0 GetDeviceCGroupRootPath
PUBLIC 19d290 0 AddDeviceRules
PUBLIC 19d350 0 _cgo_b57e814d6d16_Cfunc__Cmalloc
PUBLIC 19d3a0 0 _cgo_release_context
PUBLIC 19d408 0 fatalf
PUBLIC 19d4d8 0 _cgo_wait_runtime_init_done
PUBLIC 19d590 0 x_cgo_notify_runtime_init_done
PUBLIC 19d5d0 0 x_cgo_set_context_function
PUBLIC 19d608 0 _cgo_get_context_function
PUBLIC 19d640 0 _cgo_try_pthread_create
PUBLIC 19d720 0 x_cgo_sys_thread_create
PUBLIC 19d7e8 0 _cgo_sys_thread_start
PUBLIC 19d8e0 0 x_cgo_init
PUBLIC 19d9d0 0 x_cgo_mmap
PUBLIC 19da00 0 x_cgo_munmap
PUBLIC 19da20 0 x_cgo_setenv
PUBLIC 19da30 0 x_cgo_unsetenv
PUBLIC 19da38 0 x_cgo_sigaction
PUBLIC 19dbf8 0 x_cgo_callers
PUBLIC 19dc80 0 x_cgo_thread_start
PUBLIC 19dce0 0 _cgo_libc_setegid
PUBLIC 19dd30 0 _cgo_libc_seteuid
PUBLIC 19dd80 0 _cgo_libc_setgid
PUBLIC 19ddd0 0 _cgo_libc_setgroups
PUBLIC 19de20 0 _cgo_libc_setregid
PUBLIC 19de78 0 _cgo_libc_setresgid
PUBLIC 19ded0 0 _cgo_libc_setresuid
PUBLIC 19df28 0 _cgo_libc_setreuid
PUBLIC 19df80 0 _cgo_libc_setuid
PUBLIC 19e020 0 _cgo_3c1cec0c9a4e_C2func_getnameinfo
PUBLIC 19e090 0 _cgo_3c1cec0c9a4e_Cfunc_getnameinfo
PUBLIC 19e0f0 0 _cgo_3c1cec0c9a4e_C2func_getaddrinfo
PUBLIC 19e150 0 _cgo_3c1cec0c9a4e_Cfunc_freeaddrinfo
PUBLIC 19e158 0 _cgo_3c1cec0c9a4e_Cfunc_gai_strerror
PUBLIC 19e1a0 0 _cgo_3c1cec0c9a4e_Cfunc_getaddrinfo
PUBLIC 19e1ec 0 _fini
STACK CFI INIT 19dfd0 4c .cfa: sp 0 + .ra: x30
STACK CFI 19dfd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19dfd8 .cfa: x29 96 +
STACK CFI 19dfdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19dfe0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19dfe4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19dfe8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 19dfec x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 19e004 x27: x27 x28: x28
STACK CFI 19e008 x25: x25 x26: x26
STACK CFI 19e00c x23: x23 x24: x24
STACK CFI 19e010 x21: x21 x22: x22
STACK CFI 19e014 x19: x19 x20: x20
STACK CFI 19e018 .cfa: sp 0 + .ra: .ra x29: x29
