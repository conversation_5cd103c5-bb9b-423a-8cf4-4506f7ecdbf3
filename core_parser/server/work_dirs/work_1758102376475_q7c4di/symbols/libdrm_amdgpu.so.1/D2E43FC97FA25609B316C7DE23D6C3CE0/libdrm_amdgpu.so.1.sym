MODULE Linux arm64 D2E43FC97FA25609B316C7DE23D6C3CE0 libdrm_amdgpu.so.1
INFO CODE_ID C93FE4D2A27F0956B316C7DE23D6C3CE314D8D05
PUBLIC 2d18 0 amdgpu_bo_alloc
PUBLIC 2e00 0 amdgpu_bo_set_metadata
PUBLIC 2ed0 0 amdgpu_bo_query_info
PUBLIC 3028 0 amdgpu_bo_export
PUBLIC 31e0 0 amdgpu_bo_inc_ref
PUBLIC 31f8 0 amdgpu_bo_cpu_map
PUBLIC 3360 0 amdgpu_bo_cpu_unmap
PUBLIC 3438 0 amdgpu_bo_free
PUBLIC 3588 0 amdgpu_bo_import
PUBLIC 3868 0 amdgpu_query_buffer_size_alignment
PUBLIC 3880 0 amdgpu_bo_wait_for_idle
PUBLIC 3948 0 amdgpu_find_bo_by_cpu_mapping
PUBLIC 3a58 0 amdgpu_create_bo_from_user_mem
PUBLIC 3b40 0 amdgpu_bo_list_create_raw
PUBLIC 3bc8 0 amdgpu_bo_list_destroy_raw
PUBLIC 3c30 0 amdgpu_bo_list_create
PUBLIC 3d98 0 amdgpu_bo_list_destroy
PUBLIC 3e30 0 amdgpu_bo_list_update
PUBLIC 3f40 0 amdgpu_bo_va_op_raw
PUBLIC 3fd8 0 amdgpu_bo_va_op
PUBLIC 40c0 0 amdgpu_cs_ctx_create2
PUBLIC 4220 0 amdgpu_cs_ctx_create
PUBLIC 4230 0 amdgpu_cs_ctx_free
PUBLIC 4380 0 amdgpu_cs_ctx_override_priority
PUBLIC 4410 0 amdgpu_cs_query_reset_state
PUBLIC 44b8 0 amdgpu_cs_query_reset_state2
PUBLIC 4780 0 amdgpu_cs_query_fence_status
PUBLIC 4890 0 amdgpu_cs_wait_fences
PUBLIC 48f0 0 amdgpu_cs_create_semaphore
PUBLIC 4948 0 amdgpu_cs_signal_semaphore
PUBLIC 4a40 0 amdgpu_cs_wait_semaphore
PUBLIC 4af8 0 amdgpu_cs_destroy_semaphore
PUBLIC 4b00 0 amdgpu_cs_create_syncobj2
PUBLIC 4b18 0 amdgpu_cs_create_syncobj
PUBLIC 4b38 0 amdgpu_cs_destroy_syncobj
PUBLIC 4b50 0 amdgpu_cs_syncobj_reset
PUBLIC 4b68 0 amdgpu_cs_syncobj_signal
PUBLIC 4b80 0 amdgpu_cs_syncobj_timeline_signal
PUBLIC 4b98 0 amdgpu_cs_syncobj_wait
PUBLIC 4bb0 0 amdgpu_cs_syncobj_timeline_wait
PUBLIC 4bc8 0 amdgpu_cs_syncobj_query
PUBLIC 4be0 0 amdgpu_cs_syncobj_query2
PUBLIC 4bf8 0 amdgpu_cs_export_syncobj
PUBLIC 4c10 0 amdgpu_cs_import_syncobj
PUBLIC 4c28 0 amdgpu_cs_syncobj_export_sync_file
PUBLIC 4c40 0 amdgpu_cs_syncobj_import_sync_file
PUBLIC 4c58 0 amdgpu_cs_syncobj_export_sync_file2
PUBLIC 4d80 0 amdgpu_cs_syncobj_import_sync_file2
PUBLIC 4e90 0 amdgpu_cs_syncobj_transfer
PUBLIC 4ea8 0 amdgpu_cs_submit_raw
PUBLIC 4fc0 0 amdgpu_cs_submit_raw2
PUBLIC 5590 0 amdgpu_cs_submit
PUBLIC 5628 0 amdgpu_cs_chunk_fence_info_to_data
PUBLIC 5640 0 amdgpu_cs_chunk_fence_to_dep
PUBLIC 5668 0 amdgpu_cs_fence_to_handle
PUBLIC 5880 0 amdgpu_device_initialize
PUBLIC 5d90 0 amdgpu_device_deinitialize
PUBLIC 5e00 0 amdgpu_get_marketing_name
PUBLIC 5e08 0 amdgpu_query_sw_info
PUBLIC 5e48 0 amdgpu_query_info
PUBLIC 5eb8 0 amdgpu_query_crtc_from_id
PUBLIC 5f30 0 amdgpu_read_mm_registers
PUBLIC 5fa8 0 amdgpu_query_hw_ip_count
PUBLIC 6020 0 amdgpu_query_hw_ip_info
PUBLIC 6098 0 amdgpu_query_firmware_version
PUBLIC 63c8 0 amdgpu_query_gpu_info
PUBLIC 6408 0 amdgpu_query_heap_info
PUBLIC 6510 0 amdgpu_query_gds_info
PUBLIC 65a8 0 amdgpu_query_sensor_info
PUBLIC 6620 0 amdgpu_query_video_caps_info
PUBLIC 6ae8 0 amdgpu_va_range_query
PUBLIC 6bf0 0 amdgpu_va_range_alloc
PUBLIC 6db8 0 amdgpu_va_range_free
PUBLIC 6e08 0 amdgpu_vm_reserve_vmid
PUBLIC 6e68 0 amdgpu_vm_unreserve_vmid
STACK CFI INIT 2868 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2898 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28e4 x19: .cfa -16 + ^
STACK CFI 291c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2928 33c .cfa: sp 0 + .ra: x30
STACK CFI 292c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2938 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2944 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2960 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2978 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2984 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2af4 x21: x21 x22: x22
STACK CFI 2af8 x23: x23 x24: x24
STACK CFI 2b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2b24 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2c10 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2c48 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2c58 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2c5c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2c60 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 2c68 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2c6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c78 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c88 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2cf0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d18 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2d1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2d28 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2d30 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2d48 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2dac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2e00 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2e04 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 2e10 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 2e20 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ea8 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI INIT 2ed0 154 .cfa: sp 0 + .ra: x30
STACK CFI 2ed4 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 2edc x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 2eec x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 2f00 x23: .cfa -368 + ^
STACK CFI 2fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2fb8 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x29: .cfa -416 + ^
STACK CFI INIT 3028 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 302c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3038 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3048 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 30a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 31e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31f8 168 .cfa: sp 0 + .ra: x30
STACK CFI 31fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3204 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3214 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3228 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 328c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3360 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 336c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3374 x21: .cfa -16 + ^
STACK CFI 33cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 33d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 33fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3400 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3438 14c .cfa: sp 0 + .ra: x30
STACK CFI 343c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3444 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3454 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3518 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3588 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 358c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3594 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 35a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 35b4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 35c0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3638 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3868 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3880 c8 .cfa: sp 0 + .ra: x30
STACK CFI 3884 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 388c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 38a4 x21: .cfa -48 + ^
STACK CFI 391c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3920 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3948 10c .cfa: sp 0 + .ra: x30
STACK CFI 394c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 395c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3964 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3978 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3980 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3a34 x21: x21 x22: x22
STACK CFI 3a38 x23: x23 x24: x24
STACK CFI 3a3c x25: x25 x26: x26
STACK CFI 3a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a4c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3a58 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3a5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3a68 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3a78 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3a9c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3ae8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3b40 84 .cfa: sp 0 + .ra: x30
STACK CFI 3b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3bc8 64 .cfa: sp 0 + .ra: x30
STACK CFI 3bcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3be4 x19: .cfa -48 + ^
STACK CFI 3c24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c30 168 .cfa: sp 0 + .ra: x30
STACK CFI 3c34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3c3c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3c48 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c78 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3c84 x25: .cfa -48 + ^
STACK CFI 3d14 x23: x23 x24: x24
STACK CFI 3d18 x25: x25
STACK CFI 3d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d4c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 3d54 x23: x23 x24: x24
STACK CFI 3d58 x25: x25
STACK CFI 3d64 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 3d74 x23: x23 x24: x24
STACK CFI 3d78 x25: x25
STACK CFI 3d7c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 3d84 x23: x23 x24: x24
STACK CFI 3d88 x25: x25
STACK CFI 3d90 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3d94 x25: .cfa -48 + ^
STACK CFI INIT 3d98 98 .cfa: sp 0 + .ra: x30
STACK CFI 3d9c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3dac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3de4 x21: .cfa -48 + ^
STACK CFI 3e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3e30 10c .cfa: sp 0 + .ra: x30
STACK CFI 3e34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3e3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3e48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3e5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3f28 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3f40 98 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f54 x19: .cfa -64 + ^
STACK CFI 3fc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3fcc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3fd8 6c .cfa: sp 0 + .ra: x30
STACK CFI 3fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fe4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ff0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ffc x23: .cfa -16 + ^
STACK CFI 4040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4048 74 .cfa: sp 0 + .ra: x30
STACK CFI 404c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4054 x19: .cfa -16 + ^
STACK CFI 4090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4094 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 40c0 160 .cfa: sp 0 + .ra: x30
STACK CFI 40c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 40cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 40d4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 40e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4104 x25: .cfa -48 + ^
STACK CFI 4148 x25: x25
STACK CFI 4174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4178 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 41fc x25: x25
STACK CFI 420c x25: .cfa -48 + ^
STACK CFI 4214 x25: x25
STACK CFI 421c x25: .cfa -48 + ^
STACK CFI INIT 4220 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4230 150 .cfa: sp 0 + .ra: x30
STACK CFI 4234 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 423c x27: .cfa -48 + ^
STACK CFI 4244 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4264 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4268 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4274 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4334 x19: x19 x20: x20
STACK CFI 4338 x21: x21 x22: x22
STACK CFI 433c x23: x23 x24: x24
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 4368 .cfa: sp 128 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 4374 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4378 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 437c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 4380 8c .cfa: sp 0 + .ra: x30
STACK CFI 4384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4394 x19: .cfa -48 + ^
STACK CFI 43fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4400 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4410 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4414 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 441c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4428 x21: .cfa -48 + ^
STACK CFI 4494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4498 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 44b8 94 .cfa: sp 0 + .ra: x30
STACK CFI 44bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4534 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4550 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4554 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 455c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 45a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 45d8 x21: .cfa -48 + ^
STACK CFI 4608 x21: x21
STACK CFI 4610 x21: .cfa -48 + ^
STACK CFI INIT 4618 168 .cfa: sp 0 + .ra: x30
STACK CFI 461c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4628 .cfa: x29 96 +
STACK CFI 462c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 465c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4668 x23: .cfa -48 + ^
STACK CFI 4760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4764 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4780 110 .cfa: sp 0 + .ra: x30
STACK CFI 4784 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4790 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4808 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 480c x21: .cfa -64 + ^
STACK CFI 4854 x21: x21
STACK CFI 485c x21: .cfa -64 + ^
STACK CFI 4868 x21: x21
STACK CFI 4870 x21: .cfa -64 + ^
STACK CFI 4880 x21: x21
STACK CFI 488c x21: .cfa -64 + ^
STACK CFI INIT 4890 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48f0 54 .cfa: sp 0 + .ra: x30
STACK CFI 48f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4904 x19: .cfa -16 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 493c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4948 f4 .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 495c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 497c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4988 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 49f4 x19: x19 x20: x20
STACK CFI 49fc x23: x23 x24: x24
STACK CFI 4a04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4a08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4a10 x19: x19 x20: x20
STACK CFI 4a1c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 4a40 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4a50 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4a5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4adc x23: x23 x24: x24
STACK CFI 4aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4af0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4af8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b18 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b38 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b68 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b98 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bc8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4be0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bf8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c28 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c58 124 .cfa: sp 0 + .ra: x30
STACK CFI 4c5c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4c64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c70 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4c90 x25: .cfa -32 + ^
STACK CFI 4ca0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4cbc x23: x23 x24: x24
STACK CFI 4cc0 x25: x25
STACK CFI 4ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 4d20 x23: x23 x24: x24
STACK CFI 4d24 x25: x25
STACK CFI 4d28 x25: .cfa -32 + ^
STACK CFI 4d38 x25: x25
STACK CFI 4d3c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4d60 x23: x23 x24: x24
STACK CFI 4d64 x25: x25
STACK CFI 4d74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4d78 x25: .cfa -32 + ^
STACK CFI INIT 4d80 10c .cfa: sp 0 + .ra: x30
STACK CFI 4d84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4db0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ddc x23: x23 x24: x24
STACK CFI 4e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e08 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e30 x23: x23 x24: x24
STACK CFI 4e34 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4e44 x23: x23 x24: x24
STACK CFI 4e48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4e78 x23: x23 x24: x24
STACK CFI 4e88 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 4e90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ea8 118 .cfa: sp 0 + .ra: x30
STACK CFI 4eac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4eb0 .cfa: x29 64 +
STACK CFI 4eb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fac .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4fc0 108 .cfa: sp 0 + .ra: x30
STACK CFI 4fc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4fd0 .cfa: x29 80 +
STACK CFI 4fd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 50b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50bc .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 50c8 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 50cc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 50d0 .cfa: x29 144 +
STACK CFI 50d4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 50e0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 510c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 551c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5520 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5590 94 .cfa: sp 0 + .ra: x30
STACK CFI 55a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55b8 x21: .cfa -16 + ^
STACK CFI 55fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5600 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5628 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5640 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5668 94 .cfa: sp 0 + .ra: x30
STACK CFI 566c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 567c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5700 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5704 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 570c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 572c x21: .cfa -64 + ^
STACK CFI 5770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5774 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 57a0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 57a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57b4 x21: .cfa -16 + ^
STACK CFI 586c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5880 50c .cfa: sp 0 + .ra: x30
STACK CFI 5884 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 588c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 58ac x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 58c4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 58f0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 59c8 x27: x27 x28: x28
STACK CFI 59f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 59fc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5a90 x27: x27 x28: x28
STACK CFI 5acc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5b04 x27: x27 x28: x28
STACK CFI 5b08 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5c7c x27: x27 x28: x28
STACK CFI 5c80 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5d00 x27: x27 x28: x28
STACK CFI 5d04 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5d40 x27: x27 x28: x28
STACK CFI 5d44 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 5d90 6c .cfa: sp 0 + .ra: x30
STACK CFI 5d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5dcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5dd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5e00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e08 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e48 6c .cfa: sp 0 + .ra: x30
STACK CFI 5e4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5e64 x19: .cfa -64 + ^
STACK CFI 5eac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5eb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5eb8 74 .cfa: sp 0 + .ra: x30
STACK CFI 5ebc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5ed4 x19: .cfa -64 + ^
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5f30 78 .cfa: sp 0 + .ra: x30
STACK CFI 5f34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5f4c x19: .cfa -64 + ^
STACK CFI 5fa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5fa4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5fa8 74 .cfa: sp 0 + .ra: x30
STACK CFI 5fac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5fc4 x19: .cfa -64 + ^
STACK CFI 6014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6018 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6020 74 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 603c x19: .cfa -64 + ^
STACK CFI 608c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6090 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6098 ac .cfa: sp 0 + .ra: x30
STACK CFI 609c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 60ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 60d8 x21: .cfa -64 + ^
STACK CFI 613c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6140 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6148 27c .cfa: sp 0 + .ra: x30
STACK CFI 614c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6160 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 618c x23: .cfa -16 + ^
STACK CFI 61fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6298 x21: x21 x22: x22
STACK CFI 62a0 x23: x23
STACK CFI 62ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 62b4 x21: x21 x22: x22
STACK CFI 6388 x23: x23
STACK CFI 638c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6390 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 63a0 x23: x23
STACK CFI 63a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 63b8 x21: x21 x22: x22
STACK CFI 63bc x23: x23
STACK CFI 63c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 63c8 3c .cfa: sp 0 + .ra: x30
STACK CFI 63dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 63f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6408 108 .cfa: sp 0 + .ra: x30
STACK CFI 640c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6414 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6424 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6444 x23: .cfa -48 + ^
STACK CFI 6498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 649c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6510 94 .cfa: sp 0 + .ra: x30
STACK CFI 6514 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 651c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6598 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 65a8 74 .cfa: sp 0 + .ra: x30
STACK CFI 65ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 65c4 x19: .cfa -64 + ^
STACK CFI 6614 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6618 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6620 74 .cfa: sp 0 + .ra: x30
STACK CFI 6624 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 663c x19: .cfa -64 + ^
STACK CFI 668c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6690 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6698 108 .cfa: sp 0 + .ra: x30
STACK CFI 669c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 66a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 66b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 66b8 x23: .cfa -16 + ^
STACK CFI 66f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 66f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 671c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6720 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 676c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6770 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6798 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 67a0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 67a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 67ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 67cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 67e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 67f4 x25: .cfa -16 + ^
STACK CFI 685c x23: x23 x24: x24
STACK CFI 6860 x25: x25
STACK CFI 6864 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 68fc x25: x25
STACK CFI 6904 x23: x23 x24: x24
STACK CFI 6914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6918 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6958 x23: x23 x24: x24 x25: x25
STACK CFI INIT 6960 188 .cfa: sp 0 + .ra: x30
STACK CFI 6964 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 696c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6978 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6988 x23: .cfa -16 + ^
STACK CFI 6a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6a60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ae0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6ae8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b10 80 .cfa: sp 0 + .ra: x30
STACK CFI 6b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6b90 5c .cfa: sp 0 + .ra: x30
STACK CFI 6b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ba4 x21: .cfa -16 + ^
STACK CFI 6be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6bf0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 6bf4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6bfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6c14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6c24 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6ccc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6d28 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6db8 50 .cfa: sp 0 + .ra: x30
STACK CFI 6dc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6dcc x19: .cfa -16 + ^
STACK CFI 6dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e08 60 .cfa: sp 0 + .ra: x30
STACK CFI 6e0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e24 x19: .cfa -32 + ^
STACK CFI 6e60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e68 60 .cfa: sp 0 + .ra: x30
STACK CFI 6e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e84 x19: .cfa -32 + ^
STACK CFI 6ec0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6ec8 ac .cfa: sp 0 + .ra: x30
STACK CFI 6ecc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6ed4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ee4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6f0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6f14 x23: .cfa -16 + ^
STACK CFI 6f64 x23: x23
STACK CFI 6f68 x23: .cfa -16 + ^
STACK CFI 6f70 x23: x23
STACK CFI INIT 6f78 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f90 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fb0 2c .cfa: sp 0 + .ra: x30
STACK CFI 6fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fbc x19: .cfa -16 + ^
STACK CFI 6fd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
