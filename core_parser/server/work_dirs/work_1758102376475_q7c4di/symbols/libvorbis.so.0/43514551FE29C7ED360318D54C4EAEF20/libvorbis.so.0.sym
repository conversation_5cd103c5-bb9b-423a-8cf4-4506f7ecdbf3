MODULE Linux arm64 43514551FE29C7ED360318D54C4EAEF20 libvorbis.so.0
INFO CODE_ID 5145514329FEEDC7360318D54C4EAEF240FE247B
PUBLIC 35c8 0 mdct_init
PUBLIC 3830 0 mdct_clear
PUBLIC 3878 0 mdct_backward
PUBLIC 3c48 0 mdct_forward
PUBLIC 6240 0 drft_forward
PUBLIC 6428 0 drft_backward
PUBLIC 6650 0 drft_init
PUBLIC 68b8 0 drft_clear
PUBLIC 6ad0 0 vorbis_block_init
PUBLIC 6bc8 0 _vorbis_block_alloc
PUBLIC 6c58 0 _vorbis_block_ripcord
PUBLIC 6cf8 0 vorbis_block_clear
PUBLIC 6da8 0 vorbis_dsp_clear
PUBLIC 74c0 0 vorbis_analysis_init
PUBLIC 7548 0 vorbis_analysis_buffer
PUBLIC 7638 0 vorbis_analysis_wrote
PUBLIC 77e0 0 vorbis_analysis_blockout
PUBLIC 7b88 0 vorbis_synthesis_restart
PUBLIC 7bf0 0 vorbis_synthesis_init
PUBLIC 7c48 0 vorbis_synthesis_blockin
PUBLIC 82c8 0 vorbis_synthesis_pcmout
PUBLIC 8338 0 vorbis_synthesis_read
PUBLIC 8368 0 vorbis_synthesis_lapout
PUBLIC 8578 0 vorbis_window
PUBLIC 8968 0 _ve_envelope_init
PUBLIC 8b50 0 _ve_envelope_clear
PUBLIC 8bc0 0 _ve_envelope_search
PUBLIC 8e60 0 _ve_envelope_mark
PUBLIC 8f30 0 _ve_envelope_shift
PUBLIC 8fa0 0 _vorbis_window_get
PUBLIC 8fb0 0 _vorbis_apply_window
PUBLIC 94f0 0 vorbis_lsp_to_curve
PUBLIC 9710 0 vorbis_lpc_to_lsp
PUBLIC 9b10 0 vorbis_lpc_from_data
PUBLIC 9de8 0 vorbis_lpc_predict
PUBLIC 9f20 0 vorbis_analysis
PUBLIC 9fe0 0 vorbis_synthesis
PUBLIC a198 0 vorbis_synthesis_trackonly
PUBLIC a290 0 vorbis_packet_blocksize
PUBLIC a370 0 vorbis_synthesis_halfrate
PUBLIC a3a0 0 vorbis_synthesis_halfrate_p
PUBLIC b838 0 _vp_global_look
PUBLIC b888 0 _vp_global_free
PUBLIC b898 0 _vi_gpsy_free
PUBLIC b8a8 0 _vi_psy_free
PUBLIC b8b8 0 _vp_psy_init
PUBLIC bf78 0 _vp_psy_clear
PUBLIC c068 0 _vp_noisemask
PUBLIC c228 0 _vp_tonemask
PUBLIC c628 0 _vp_offset_and_mix
PUBLIC c728 0 _vp_ampmax_decay
PUBLIC c770 0 _vp_couple_quantize_normalize
PUBLIC d390 0 vorbis_comment_init
PUBLIC d3a0 0 vorbis_comment_add
PUBLIC d460 0 vorbis_comment_add_tag
PUBLIC d4f8 0 vorbis_comment_query
PUBLIC d5e0 0 vorbis_comment_query_count
PUBLIC d6b0 0 vorbis_comment_clear
PUBLIC d738 0 vorbis_info_blocksize
PUBLIC d750 0 vorbis_info_init
PUBLIC d790 0 vorbis_info_clear
PUBLIC d978 0 vorbis_synthesis_idheader
PUBLIC da80 0 vorbis_synthesis_headerin
PUBLIC e178 0 vorbis_commentheader_out
PUBLIC e260 0 vorbis_analysis_headerout
PUBLIC e8f8 0 vorbis_granule_time
PUBLIC e938 0 vorbis_version_string
PUBLIC fa80 0 floor1_fit
PUBLIC 102a8 0 floor1_interpolate_fit
PUBLIC 10350 0 floor1_encode
PUBLIC 11010 0 res0_pack
PUBLIC 11180 0 res0_look
PUBLIC 113b8 0 res1_class
PUBLIC 115c8 0 res2_class
PUBLIC 11b70 0 res0_inverse
PUBLIC 11bc0 0 res1_inverse
PUBLIC 11f78 0 res2_inverse
PUBLIC 121c0 0 res0_free_info
PUBLIC 121d0 0 res0_unpack
PUBLIC 123f8 0 res0_free_look
PUBLIC 128b8 0 res2_forward
PUBLIC 129d8 0 res1_forward
PUBLIC 13ef0 0 vorbis_staticbook_pack
PUBLIC 14270 0 vorbis_staticbook_unpack
PUBLIC 14680 0 vorbis_book_encode
PUBLIC 146f8 0 vorbis_book_decode
PUBLIC 14860 0 vorbis_book_decodevs_add
PUBLIC 14b68 0 vorbis_book_decodev_add
PUBLIC 14d48 0 vorbis_book_decodev_set
PUBLIC 14f40 0 vorbis_book_decodevv_add
PUBLIC 151c0 0 ov_ilog
PUBLIC 151e0 0 _float32_pack
PUBLIC 15260 0 _float32_unpack
PUBLIC 15298 0 _make_words
PUBLIC 154f8 0 _book_maptype1_quantvals
PUBLIC 155d8 0 _book_unquantize
PUBLIC 15838 0 vorbis_staticbook_destroy
PUBLIC 15888 0 vorbis_book_clear
PUBLIC 158f8 0 vorbis_book_init_encode
PUBLIC 15988 0 vorbis_book_init_decode
PUBLIC 15ea8 0 vorbis_book_codeword
PUBLIC 15ec8 0 vorbis_book_codelen
PUBLIC 15ee8 0 vorbis_bitrate_init
PUBLIC 15fa8 0 vorbis_bitrate_clear
PUBLIC 15fc0 0 vorbis_bitrate_managed
PUBLIC 15fd8 0 vorbis_bitrate_addblock
PUBLIC 16498 0 vorbis_bitrate_flushpacket
STACK CFI INIT 2d18 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d48 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d88 48 .cfa: sp 0 + .ra: x30
STACK CFI 2d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d94 x19: .cfa -16 + ^
STACK CFI 2dcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2dd8 7f0 .cfa: sp 0 + .ra: x30
STACK CFI 2ddc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2e00 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2e0c x21: .cfa -128 + ^
STACK CFI 2fc8 x19: x19 x20: x20
STACK CFI 2fcc x21: x21
STACK CFI 3010 v8: .cfa -112 + ^ v9: .cfa -104 + ^
STACK CFI 3014 v10: .cfa -96 + ^ v11: .cfa -88 + ^
STACK CFI 3018 v12: .cfa -80 + ^ v13: .cfa -72 + ^
STACK CFI 301c v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI 3470 v8: v8 v9: v9
STACK CFI 3474 v10: v10 v11: v11
STACK CFI 3478 v12: v12 v13: v13
STACK CFI 347c v14: v14 v15: v15
STACK CFI 3480 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3484 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI INIT 35c8 264 .cfa: sp 0 + .ra: x30
STACK CFI 35cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 35d8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 35e4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 35f4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3608 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3664 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 3688 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 37f4 x23: x23 x24: x24
STACK CFI 37f8 v8: v8 v9: v9
STACK CFI 381c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3820 .cfa: sp 160 + .ra: .cfa -152 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 3830 44 .cfa: sp 0 + .ra: x30
STACK CFI 3838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3840 x19: .cfa -16 + ^
STACK CFI 386c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3878 3cc .cfa: sp 0 + .ra: x30
STACK CFI 387c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 388c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3898 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 38c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3c48 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 3c4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3c50 .cfa: x29 80 +
STACK CFI 3c54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3c60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4024 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4038 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40b8 x21: .cfa -16 + ^
STACK CFI 40e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41a4 x19: x19 x20: x20
STACK CFI 41ac x21: x21
STACK CFI 41f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4200 x19: x19 x20: x20
STACK CFI 4204 x21: x21
STACK CFI 4208 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 420c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4224 x21: .cfa -16 + ^
STACK CFI 4228 x21: x21
STACK CFI INIT 4230 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 4234 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4240 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 424c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4388 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4398 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 44cc x19: x19 x20: x20
STACK CFI 44d4 x25: x25 x26: x26
STACK CFI 458c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4590 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4594 x19: x19 x20: x20
STACK CFI 45a0 x25: x25 x26: x26
STACK CFI 45a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 45ac .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 45d8 1c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4798 230 .cfa: sp 0 + .ra: x30
STACK CFI 479c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4854 x21: .cfa -16 + ^
STACK CFI 49b0 x19: x19 x20: x20
STACK CFI 49b4 x21: x21
STACK CFI 49b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 49bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 49c0 x19: x19 x20: x20
STACK CFI 49c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49c8 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 49cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 49d8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 49f8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4aac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4ad8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4ae8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4c64 x19: x19 x20: x20
STACK CFI 4c68 x21: x21 x22: x22
STACK CFI 4c6c x25: x25 x26: x26
STACK CFI 4d2c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4d30 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4d34 x19: x19 x20: x20
STACK CFI 4d38 x21: x21 x22: x22
STACK CFI 4d40 x25: x25 x26: x26
STACK CFI 4d48 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4d4c .cfa: sp 128 + .ra: .cfa -120 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4d70 abc .cfa: sp 0 + .ra: x30
STACK CFI 4d84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4d90 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4d9c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4da8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4db4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4dc4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 549c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 57fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5830 a0c .cfa: sp 0 + .ra: x30
STACK CFI 5844 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5850 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 585c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5868 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5874 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5880 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5e3c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 614c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6150 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 6240 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 6244 .cfa: sp 112 +
STACK CFI 6248 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6250 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6260 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6268 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 627c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 62ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6398 x19: x19 x20: x20
STACK CFI 639c x23: x23 x24: x24
STACK CFI 63a0 x25: x25 x26: x26
STACK CFI 63a4 x27: x27 x28: x28
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 63b4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6418 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 641c x23: x23 x24: x24
STACK CFI 6420 x27: x27 x28: x28
STACK CFI INIT 6428 228 .cfa: sp 0 + .ra: x30
STACK CFI 642c .cfa: sp 128 +
STACK CFI 6430 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6438 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6448 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 644c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6454 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6484 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6590 x19: x19 x20: x20
STACK CFI 6594 x21: x21 x22: x22
STACK CFI 6598 x25: x25 x26: x26
STACK CFI 659c x27: x27 x28: x28
STACK CFI 65a8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 65ac .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6650 264 .cfa: sp 0 + .ra: x30
STACK CFI 6654 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 665c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 6678 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 66ac x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 66c4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 66c8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 66cc v8: .cfa -128 + ^ v9: .cfa -120 + ^
STACK CFI 66d0 v10: .cfa -112 + ^ v11: .cfa -104 + ^
STACK CFI 66d4 v12: .cfa -96 + ^
STACK CFI 6878 x21: x21 x22: x22
STACK CFI 687c x25: x25 x26: x26
STACK CFI 6880 x27: x27 x28: x28
STACK CFI 6884 v8: v8 v9: v9
STACK CFI 6888 v10: v10 v11: v11
STACK CFI 688c v12: v12
STACK CFI 6898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 689c .cfa: sp 224 + .ra: .cfa -216 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 68b8 44 .cfa: sp 0 + .ra: x30
STACK CFI 68c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68c8 x19: .cfa -16 + ^
STACK CFI 68f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6900 1cc .cfa: sp 0 + .ra: x30
STACK CFI 6904 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6908 .cfa: x29 80 +
STACK CFI 690c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6924 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6aa4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6ad0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 6ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6adc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 6b38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b40 x23: .cfa -16 + ^
STACK CFI 6bb8 x21: x21 x22: x22
STACK CFI 6bbc x23: x23
STACK CFI 6bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6bc8 8c .cfa: sp 0 + .ra: x30
STACK CFI 6bcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6bd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6be4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6c58 9c .cfa: sp 0 + .ra: x30
STACK CFI 6c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c6c x21: .cfa -16 + ^
STACK CFI 6cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6cf8 ac .cfa: sp 0 + .ra: x30
STACK CFI 6cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6d24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d64 x19: x19 x20: x20
STACK CFI 6da0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6da8 284 .cfa: sp 0 + .ra: x30
STACK CFI 6db0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6db8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6dc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6dc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e58 x25: .cfa -16 + ^
STACK CFI 6e94 x25: x25
STACK CFI 6eb8 x25: .cfa -16 + ^
STACK CFI 6ef4 x25: x25
STACK CFI 7018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 701c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7030 48c .cfa: sp 0 + .ra: x30
STACK CFI 7034 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 703c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7048 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7070 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7084 x21: x21 x22: x22
STACK CFI 7088 x23: x23 x24: x24
STACK CFI 7090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7094 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 709c x21: x21 x22: x22
STACK CFI 70a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 70a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 70b4 x27: .cfa -16 + ^
STACK CFI 72ec x21: x21 x22: x22
STACK CFI 72f0 x23: x23 x24: x24
STACK CFI 72f4 x25: x25 x26: x26
STACK CFI 72f8 x27: x27
STACK CFI 72fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7300 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 730c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7310 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 7458 x21: x21 x22: x22
STACK CFI 745c x23: x23 x24: x24
STACK CFI 7460 x25: x25 x26: x26
STACK CFI 7464 x27: x27
STACK CFI 7468 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 74c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 74c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 74d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 74dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74ec x23: .cfa -16 + ^
STACK CFI 7530 x23: x23
STACK CFI 7544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7548 f0 .cfa: sp 0 + .ra: x30
STACK CFI 754c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7554 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7560 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 762c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7630 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7638 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 763c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7644 .cfa: x29 80 +
STACK CFI 7648 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7654 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 766c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 76c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 76c8 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 77e0 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 77e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 77ec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7800 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7810 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7818 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 781c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 79ec x19: x19 x20: x20
STACK CFI 79f0 x23: x23 x24: x24
STACK CFI 79f4 x25: x25 x26: x26
STACK CFI 79f8 x27: x27 x28: x28
STACK CFI 79fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7a00 x19: x19 x20: x20
STACK CFI 7a0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7a10 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7a1c x19: x19 x20: x20
STACK CFI 7a20 x23: x23 x24: x24
STACK CFI 7a24 x25: x25 x26: x26
STACK CFI 7a28 x27: x27 x28: x28
STACK CFI 7a2c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7af0 x23: x23 x24: x24
STACK CFI 7af4 x25: x25 x26: x26
STACK CFI 7af8 x27: x27 x28: x28
STACK CFI 7b00 x19: x19 x20: x20
STACK CFI 7b04 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7b28 x23: x23 x24: x24
STACK CFI 7b2c x25: x25 x26: x26
STACK CFI 7b30 x27: x27 x28: x28
STACK CFI 7b38 x19: x19 x20: x20
STACK CFI 7b3c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7b58 x23: x23 x24: x24
STACK CFI 7b5c x25: x25 x26: x26
STACK CFI 7b60 x27: x27 x28: x28
STACK CFI 7b6c x19: x19 x20: x20
STACK CFI 7b74 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 7b88 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7bf0 54 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7c48 67c .cfa: sp 0 + .ra: x30
STACK CFI 7c4c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 7c54 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 7c5c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7c8c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7ce0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7d54 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 7f94 x23: x23 x24: x24
STACK CFI 7fc8 x21: x21 x22: x22
STACK CFI 80a8 x25: x25 x26: x26
STACK CFI 80b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 80bc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 80e8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8108 x21: x21 x22: x22
STACK CFI 8110 x25: x25 x26: x26
STACK CFI 8124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 8128 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 8140 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8150 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8170 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8198 x21: x21 x22: x22
STACK CFI 81a4 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8278 x23: x23 x24: x24
STACK CFI 8280 x21: x21 x22: x22
STACK CFI 82a4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 82bc x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI INIT 82c8 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8338 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8368 20c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8578 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 85a8 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 85ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 85b0 .cfa: x29 112 +
STACK CFI 85b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 85c4 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 85d8 x25: .cfa -48 + ^
STACK CFI 85e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 85f4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8914 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8918 .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8968 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 8970 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8980 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8990 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 89a0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 89a8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 89c4 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ x25: .cfa -48 + ^
STACK CFI 8b4c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 8b50 6c .cfa: sp 0 + .ra: x30
STACK CFI 8b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8b5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8b6c x21: .cfa -16 + ^
STACK CFI 8bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8bc0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 8bc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8bd4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8bdc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8bf4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8c40 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8c50 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8d34 x23: x23 x24: x24
STACK CFI 8d38 x25: x25 x26: x26
STACK CFI 8e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8e04 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8e10 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8e28 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI INIT 8e60 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f30 6c .cfa: sp 0 + .ra: x30
STACK CFI 8f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8fa0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8fb0 160 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9110 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9178 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9198 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 919c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91a8 .cfa: x29 32 +
STACK CFI 9348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 934c .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9360 18c .cfa: sp 0 + .ra: x30
STACK CFI 9364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9370 .cfa: x29 32 +
STACK CFI 94d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 94d8 .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 94f0 220 .cfa: sp 0 + .ra: x30
STACK CFI 94f4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9504 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 951c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9528 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9534 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 953c v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 958c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 95b8 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 96bc x25: x25 x26: x26
STACK CFI 96c0 v12: v12 v13: v13
STACK CFI 96dc .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 96e0 .cfa: sp 160 + .ra: .cfa -152 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9710 400 .cfa: sp 0 + .ra: x30
STACK CFI 9714 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9718 .cfa: x29 128 +
STACK CFI 971c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 9724 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9738 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9760 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9ab4 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9b10 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 9b14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9b1c .cfa: x29 80 +
STACK CFI 9b20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9b34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9b54 v8: .cfa -32 + ^
STACK CFI 9dac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9db0 .cfa: x29 80 + .ra: .cfa -72 + ^ v8: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9de8 138 .cfa: sp 0 + .ra: x30
STACK CFI 9dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9df8 .cfa: x29 32 +
STACK CFI 9ef0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9ef4 .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9f20 c0 .cfa: sp 0 + .ra: x30
STACK CFI 9f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9f34 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9fd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9fe0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 9fe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ff0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a004 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a00c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a114 x21: x21 x22: x22
STACK CFI a120 x23: x23 x24: x24
STACK CFI a128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a134 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a138 x21: x21 x22: x22
STACK CFI a13c x23: x23 x24: x24
STACK CFI a148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a14c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a17c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a184 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a18c x21: x21 x22: x22
STACK CFI a190 x23: x23 x24: x24
STACK CFI INIT a198 f8 .cfa: sp 0 + .ra: x30
STACK CFI a19c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a1a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a1b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a1c4 x23: .cfa -16 + ^
STACK CFI a254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a258 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a290 e0 .cfa: sp 0 + .ra: x30
STACK CFI a294 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a29c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a2d4 x21: .cfa -64 + ^
STACK CFI a324 x21: x21
STACK CFI a344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a348 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI a350 x21: x21
STACK CFI a35c x21: .cfa -64 + ^
STACK CFI a364 x21: x21
STACK CFI a36c x21: .cfa -64 + ^
STACK CFI INIT a370 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a3a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a3b0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT a3d8 5f4 .cfa: sp 0 + .ra: x30
STACK CFI a3dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3e8 .cfa: x29 32 +
STACK CFI a98c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a990 .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT a9d0 250 .cfa: sp 0 + .ra: x30
STACK CFI a9d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a9e0 .cfa: x29 32 +
STACK CFI abb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI abbc .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT ac20 934 .cfa: sp 0 + .ra: x30
STACK CFI ac28 .cfa: sp 32800 +
STACK CFI ac30 .ra: .cfa -32776 + ^ x29: .cfa -32784 + ^
STACK CFI ac34 .cfa: x29 32784 +
STACK CFI ac40 x19: .cfa -32768 + ^ x20: .cfa -32760 + ^
STACK CFI ac70 v10: .cfa -32672 + ^ v11: .cfa -32664 + ^ v8: .cfa -32688 + ^ v9: .cfa -32680 + ^ x21: .cfa -32752 + ^ x22: .cfa -32744 + ^ x23: .cfa -32736 + ^ x24: .cfa -32728 + ^ x25: .cfa -32720 + ^ x26: .cfa -32712 + ^ x27: .cfa -32704 + ^ x28: .cfa -32696 + ^
STACK CFI ac80 v12: .cfa -32656 + ^ v13: .cfa -32648 + ^ v14: .cfa -32640 + ^ v15: .cfa -32632 + ^
STACK CFI b50c .cfa: sp 32800 +
STACK CFI b53c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b540 .cfa: x29 32784 + .ra: .cfa -32776 + ^ v10: .cfa -32672 + ^ v11: .cfa -32664 + ^ v12: .cfa -32656 + ^ v13: .cfa -32648 + ^ v14: .cfa -32640 + ^ v15: .cfa -32632 + ^ v8: .cfa -32688 + ^ v9: .cfa -32680 + ^ x19: .cfa -32768 + ^ x20: .cfa -32760 + ^ x21: .cfa -32752 + ^ x22: .cfa -32744 + ^ x23: .cfa -32736 + ^ x24: .cfa -32728 + ^ x25: .cfa -32720 + ^ x26: .cfa -32712 + ^ x27: .cfa -32704 + ^ x28: .cfa -32696 + ^ x29: .cfa -32784 + ^
STACK CFI INIT b558 2dc .cfa: sp 0 + .ra: x30
STACK CFI b55c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b568 .cfa: x29 112 +
STACK CFI b56c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b57c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI b59c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b73c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b740 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT b838 50 .cfa: sp 0 + .ra: x30
STACK CFI b83c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b84c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b888 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b898 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b8a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b8b8 6c0 .cfa: sp 0 + .ra: x30
STACK CFI b8bc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI b8c8 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI b8d8 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI b8e4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI b8ec x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI b8f4 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI b904 v10: .cfa -144 + ^ v11: .cfa -136 + ^ v12: .cfa -128 + ^ v13: .cfa -120 + ^
STACK CFI b910 v14: .cfa -112 + ^ v15: .cfa -104 + ^
STACK CFI bf14 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf18 .cfa: sp 256 + .ra: .cfa -248 + ^ v10: .cfa -144 + ^ v11: .cfa -136 + ^ v12: .cfa -128 + ^ v13: .cfa -120 + ^ v14: .cfa -112 + ^ v15: .cfa -104 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT bf78 f0 .cfa: sp 0 + .ra: x30
STACK CFI bf80 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bf88 x21: .cfa -16 + ^
STACK CFI bf94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c068 1bc .cfa: sp 0 + .ra: x30
STACK CFI c06c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c070 .cfa: x29 80 +
STACK CFI c074 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c080 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c0a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c1f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c1f4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT c228 3fc .cfa: sp 0 + .ra: x30
STACK CFI c22c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c234 .cfa: x29 80 +
STACK CFI c238 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c244 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c278 x23: .cfa -32 + ^
STACK CFI c524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c528 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT c628 100 .cfa: sp 0 + .ra: x30
STACK CFI INIT c728 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT c770 a20 .cfa: sp 0 + .ra: x30
STACK CFI c774 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI c77c .cfa: x29 272 +
STACK CFI c7b0 x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI c7c0 x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI c7dc v10: .cfa -160 + ^ v8: .cfa -176 + ^ v9: .cfa -168 + ^
STACK CFI d128 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d12c .cfa: x29 272 + .ra: .cfa -264 + ^ v10: .cfa -160 + ^ v8: .cfa -176 + ^ v9: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI INIT d190 88 .cfa: sp 0 + .ra: x30
STACK CFI d19c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d1a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d1b0 x21: .cfa -16 + ^
STACK CFI d1f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d1fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI d20c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT d218 178 .cfa: sp 0 + .ra: x30
STACK CFI d21c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d228 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d234 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d240 x23: .cfa -16 + ^
STACK CFI d38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT d390 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d3a0 bc .cfa: sp 0 + .ra: x30
STACK CFI d3a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d3ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d3b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d3d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI d458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT d460 94 .cfa: sp 0 + .ra: x30
STACK CFI d464 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d470 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d478 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d4f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d4f8 e8 .cfa: sp 0 + .ra: x30
STACK CFI d4fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d504 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d50c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d51c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d5b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d5bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d5dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT d5e0 d0 .cfa: sp 0 + .ra: x30
STACK CFI d5e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d5ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d5f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d68c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI d6ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d6b0 88 .cfa: sp 0 + .ra: x30
STACK CFI d6b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d6c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d738 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT d750 3c .cfa: sp 0 + .ra: x30
STACK CFI d754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d760 x19: .cfa -16 + ^
STACK CFI d788 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d790 1e4 .cfa: sp 0 + .ra: x30
STACK CFI d794 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d79c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d7a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d7b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d954 x23: x23 x24: x24
STACK CFI d970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT d978 104 .cfa: sp 0 + .ra: x30
STACK CFI d97c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d984 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d9ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI d9c4 x21: x21 x22: x22
STACK CFI d9e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d9e8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI da00 x23: .cfa -80 + ^
STACK CFI da50 x21: x21 x22: x22
STACK CFI da54 x23: x23
STACK CFI da58 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI da70 x21: x21 x22: x22 x23: x23
STACK CFI da74 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI da78 x23: .cfa -80 + ^
STACK CFI INIT da80 6f8 .cfa: sp 0 + .ra: x30
STACK CFI da84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI da8c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI da98 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI daac x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI dabc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI dacc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI db28 x19: x19 x20: x20
STACK CFI db30 x25: x25 x26: x26
STACK CFI db58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI db5c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI db90 x19: x19 x20: x20
STACK CFI db94 x25: x25 x26: x26
STACK CFI db98 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI dd24 x19: x19 x20: x20
STACK CFI dd28 x25: x25 x26: x26
STACK CFI dd2c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI de3c x19: x19 x20: x20
STACK CFI de40 x25: x25 x26: x26
STACK CFI de44 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI dec0 x19: x19 x20: x20
STACK CFI dec4 x25: x25 x26: x26
STACK CFI ded0 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI ded8 x19: x19 x20: x20
STACK CFI dedc x25: x25 x26: x26
STACK CFI dee0 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI e018 x19: x19 x20: x20
STACK CFI e01c x25: x25 x26: x26
STACK CFI e020 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI e09c x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI e0a0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI e0a4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT e178 e4 .cfa: sp 0 + .ra: x30
STACK CFI e17c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e184 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e190 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e1cc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e21c x23: x23 x24: x24
STACK CFI e240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e244 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI e258 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT e260 694 .cfa: sp 0 + .ra: x30
STACK CFI e264 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI e26c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI e27c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI e290 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI e29c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI e2a4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI e2bc x25: x25 x26: x26
STACK CFI e310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI e314 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI e62c x25: x25 x26: x26
STACK CFI e638 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI e8e8 x25: x25 x26: x26
STACK CFI e8f0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT e8f8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT e938 c .cfa: sp 0 + .ra: x30
STACK CFI INIT e948 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT e960 1e0 .cfa: sp 0 + .ra: x30
STACK CFI e964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI eb18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI eb1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI eb34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI eb38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT eb40 208 .cfa: sp 0 + .ra: x30
STACK CFI eb44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI eb50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI eb5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI eb6c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ed10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ed14 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT ed48 30c .cfa: sp 0 + .ra: x30
STACK CFI ed4c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ed54 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ed5c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ed78 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI edb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI edb4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI ee38 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI eedc x27: x27 x28: x28
STACK CFI eee0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ef10 x27: x27 x28: x28
STACK CFI f024 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI f044 x27: x27 x28: x28
STACK CFI INIT f058 2a8 .cfa: sp 0 + .ra: x30
STACK CFI f05c .cfa: sp 608 +
STACK CFI f064 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI f06c x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI f078 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI f090 x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI f0f4 x25: .cfa -544 + ^
STACK CFI f1a8 x25: x25
STACK CFI f294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f298 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x29: .cfa -608 + ^
STACK CFI f2f0 x25: .cfa -544 + ^
STACK CFI f2f8 x25: x25
STACK CFI f2fc x25: .cfa -544 + ^
STACK CFI INIT f300 18c .cfa: sp 0 + .ra: x30
STACK CFI f304 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f32c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f34c x21: .cfa -16 + ^
STACK CFI f424 x21: x21
STACK CFI f444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f448 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f458 x21: x21
STACK CFI INIT f490 220 .cfa: sp 0 + .ra: x30
STACK CFI INIT f6b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f6c0 3b0 .cfa: sp 0 + .ra: x30
STACK CFI f6c4 .cfa: sp 640 +
STACK CFI f6d0 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI f6d8 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI f6e4 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI f700 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI f728 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI f764 x19: x19 x20: x20
STACK CFI f79c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI f7a0 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x29: .cfa -640 + ^
STACK CFI f7a8 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI f850 x19: x19 x20: x20
STACK CFI f854 x27: x27 x28: x28
STACK CFI f858 x19: .cfa -624 + ^ x20: .cfa -616 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI f870 x19: x19 x20: x20
STACK CFI f874 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI f888 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI f88c x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI f8dc x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI f94c x19: x19 x20: x20
STACK CFI f950 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI f980 x19: x19 x20: x20
STACK CFI f984 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI fa10 x19: x19 x20: x20
STACK CFI fa14 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI fa34 x19: x19 x20: x20
STACK CFI fa38 x27: x27 x28: x28
STACK CFI fa3c x19: .cfa -624 + ^ x20: .cfa -616 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI fa40 x19: x19 x20: x20
STACK CFI fa44 x27: x27 x28: x28
STACK CFI fa4c x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI fa50 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI fa54 x19: x19 x20: x20
STACK CFI fa58 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI INIT fa70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT fa80 828 .cfa: sp 0 + .ra: x30
STACK CFI fa88 .cfa: sp 5152 +
STACK CFI fa8c .ra: .cfa -5144 + ^ x29: .cfa -5152 + ^
STACK CFI fa9c x19: .cfa -5136 + ^ x20: .cfa -5128 + ^ x23: .cfa -5104 + ^ x24: .cfa -5096 + ^
STACK CFI fac0 x21: .cfa -5120 + ^ x22: .cfa -5112 + ^ x25: .cfa -5088 + ^ x26: .cfa -5080 + ^
STACK CFI faf0 x27: .cfa -5072 + ^ x28: .cfa -5064 + ^
STACK CFI ffd0 x27: x27 x28: x28
STACK CFI 1012c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10130 .cfa: sp 5152 + .ra: .cfa -5144 + ^ x19: .cfa -5136 + ^ x20: .cfa -5128 + ^ x21: .cfa -5120 + ^ x22: .cfa -5112 + ^ x23: .cfa -5104 + ^ x24: .cfa -5096 + ^ x25: .cfa -5088 + ^ x26: .cfa -5080 + ^ x27: .cfa -5072 + ^ x28: .cfa -5064 + ^ x29: .cfa -5152 + ^
STACK CFI 10204 x27: x27 x28: x28
STACK CFI 10234 x27: .cfa -5072 + ^ x28: .cfa -5064 + ^
STACK CFI 1023c x27: x27 x28: x28
STACK CFI 10240 x27: .cfa -5072 + ^ x28: .cfa -5064 + ^
STACK CFI 1024c x27: x27 x28: x28
STACK CFI 10288 x27: .cfa -5072 + ^ x28: .cfa -5064 + ^
STACK CFI 10290 x27: x27 x28: x28
STACK CFI 10294 x27: .cfa -5072 + ^ x28: .cfa -5064 + ^
STACK CFI 102a4 x27: x27 x28: x28
STACK CFI INIT 102a8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 102b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 102c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 102cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 10350 698 .cfa: sp 0 + .ra: x30
STACK CFI 10354 .cfa: sp 512 +
STACK CFI 1035c .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 10370 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 1039c x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 103ac x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 103b8 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 10934 x19: x19 x20: x20
STACK CFI 1093c x21: x21 x22: x22
STACK CFI 10968 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1096c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI 10994 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 109c8 x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 109dc x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 109e0 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 109e4 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI INIT 109e8 19c .cfa: sp 0 + .ra: x30
STACK CFI 109ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 109f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 109fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10a04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10a30 v8: .cfa -16 + ^
STACK CFI 10a6c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10b34 x25: x25 x26: x26
STACK CFI 10b3c v8: v8
STACK CFI 10b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10b4c .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 10b50 x25: x25 x26: x26
STACK CFI 10b54 v8: v8
STACK CFI 10b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10b70 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10b88 54 .cfa: sp 0 + .ra: x30
STACK CFI 10b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10b98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10be0 278 .cfa: sp 0 + .ra: x30
STACK CFI 10be4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 10bec x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 10bf4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 10c00 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 10c08 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 10c14 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 10c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10c68 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 10c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10c98 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 10ca0 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 10cc0 v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 10ccc v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 10d94 v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI 10e2c v14: v14 v15: v15
STACK CFI 10e3c v8: v8 v9: v9
STACK CFI 10e40 v10: v10 v11: v11
STACK CFI 10e4c v12: v12 v13: v13
STACK CFI INIT 10e58 58 .cfa: sp 0 + .ra: x30
STACK CFI 10e60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10e6c x19: .cfa -16 + ^
STACK CFI 10ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10eb0 150 .cfa: sp 0 + .ra: x30
STACK CFI 10eb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10ec8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10f70 x23: .cfa -16 + ^
STACK CFI 10fc8 x23: x23
STACK CFI 10fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10fe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 10ff8 x23: x23
STACK CFI 10ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 11000 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11010 16c .cfa: sp 0 + .ra: x30
STACK CFI 11014 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11020 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1102c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 110a0 x23: .cfa -16 + ^
STACK CFI 1115c x23: x23
STACK CFI 11168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1116c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11180 238 .cfa: sp 0 + .ra: x30
STACK CFI 11184 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1118c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11198 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 111a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 111b0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1137c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11380 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 113b8 20c .cfa: sp 0 + .ra: x30
STACK CFI 113bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 113c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 113d0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 113dc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11410 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1141c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11434 v8: .cfa -32 + ^
STACK CFI 11580 x21: x21 x22: x22
STACK CFI 11588 x23: x23 x24: x24
STACK CFI 1158c x25: x25 x26: x26
STACK CFI 11590 x27: x27 x28: x28
STACK CFI 11594 v8: v8
STACK CFI 115a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 115a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 115ac x21: x21 x22: x22
STACK CFI 115b0 x27: x27 x28: x28
STACK CFI 115c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 115c8 224 .cfa: sp 0 + .ra: x30
STACK CFI 115cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 115d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 115e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 115ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11624 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11634 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1176c x19: x19 x20: x20
STACK CFI 11774 x25: x25 x26: x26
STACK CFI 11778 x27: x27 x28: x28
STACK CFI 11784 x21: x21 x22: x22
STACK CFI 1178c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 11790 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 117a8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 117b0 x19: x19 x20: x20
STACK CFI 117b4 x21: x21 x22: x22
STACK CFI 117bc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 117c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 117d8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 117e8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 117f0 37c .cfa: sp 0 + .ra: x30
STACK CFI 117f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 117f8 .cfa: x29 208 +
STACK CFI 117fc x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 11804 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1180c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1181c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 11848 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 11b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11b14 .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 11b70 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11bc0 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11c10 368 .cfa: sp 0 + .ra: x30
STACK CFI 11c14 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 11c1c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 11c24 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 11c38 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 11c60 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 11f38 x25: x25 x26: x26
STACK CFI 11f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 11f68 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 11f74 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 11f78 244 .cfa: sp 0 + .ra: x30
STACK CFI 11f7c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 11f84 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 11f90 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 11f9c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 11fa4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 11fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11fe4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 11ff4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1214c x23: x23 x24: x24
STACK CFI 12164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12168 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 121ac x23: x23 x24: x24
STACK CFI 121b0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 121b8 x23: x23 x24: x24
STACK CFI INIT 121c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 121d0 224 .cfa: sp 0 + .ra: x30
STACK CFI 121d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 121dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 121f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12278 x25: .cfa -16 + ^
STACK CFI 1231c x25: x25
STACK CFI 1233c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12340 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 12398 x25: x25
STACK CFI 1239c x25: .cfa -16 + ^
STACK CFI 123a0 x25: x25
STACK CFI 123dc x25: .cfa -16 + ^
STACK CFI 123e4 x25: x25
STACK CFI INIT 123f8 98 .cfa: sp 0 + .ra: x30
STACK CFI 12400 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1240c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12490 424 .cfa: sp 0 + .ra: x30
STACK CFI 12494 .cfa: sp 2304 +
STACK CFI 12498 .ra: .cfa -2296 + ^ x29: .cfa -2304 + ^
STACK CFI 124a0 x21: .cfa -2272 + ^ x22: .cfa -2264 + ^
STACK CFI 124ac x25: .cfa -2240 + ^ x26: .cfa -2232 + ^
STACK CFI 124c4 x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^
STACK CFI 124f4 x27: .cfa -2224 + ^ x28: .cfa -2216 + ^
STACK CFI 127d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 127d4 .cfa: sp 2304 + .ra: .cfa -2296 + ^ x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^ x25: .cfa -2240 + ^ x26: .cfa -2232 + ^ x27: .cfa -2224 + ^ x28: .cfa -2216 + ^ x29: .cfa -2304 + ^
STACK CFI INIT 128b8 11c .cfa: sp 0 + .ra: x30
STACK CFI 128bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 128c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 128d0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 128e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12908 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12910 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 129b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 129b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 129d8 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12a30 4f0 .cfa: sp 0 + .ra: x30
STACK CFI 12a34 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 12a38 .cfa: x29 160 +
STACK CFI 12a3c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 12a48 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 12a78 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 12ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12edc .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 12f20 af8 .cfa: sp 0 + .ra: x30
STACK CFI 12f24 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 12f28 .cfa: x29 320 +
STACK CFI 12f2c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 12f7c v10: .cfa -208 + ^ v11: .cfa -200 + ^ v12: .cfa -192 + ^ v13: .cfa -184 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 12f98 v14: .cfa -176 + ^
STACK CFI 139c8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 139cc .cfa: x29 320 + .ra: .cfa -312 + ^ v10: .cfa -208 + ^ v11: .cfa -200 + ^ v12: .cfa -192 + ^ v13: .cfa -184 + ^ v14: .cfa -176 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI INIT 13a18 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 13a1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13a24 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13a30 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13a44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13a88 x25: .cfa -16 + ^
STACK CFI 13b14 x25: x25
STACK CFI 13bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13bd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13c00 298 .cfa: sp 0 + .ra: x30
STACK CFI 13c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13c0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13c18 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13c40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13c9c x25: .cfa -16 + ^
STACK CFI 13d18 x23: x23 x24: x24
STACK CFI 13d1c x25: x25
STACK CFI 13d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13d3c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 13d40 x23: x23 x24: x24
STACK CFI 13d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13d58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 13d7c x23: x23 x24: x24
STACK CFI 13d8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13df4 x23: x23 x24: x24
STACK CFI 13df8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13e74 x23: x23 x24: x24
STACK CFI 13e78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13e7c x23: x23 x24: x24
STACK CFI 13e80 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 13e94 x25: x25
STACK CFI INIT 13e98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13ea8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13ef0 37c .cfa: sp 0 + .ra: x30
STACK CFI 13ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13f00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13f18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1411c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 14164 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 141dc x23: x23 x24: x24
STACK CFI INIT 14270 410 .cfa: sp 0 + .ra: x30
STACK CFI 14274 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14284 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14324 x23: .cfa -16 + ^
STACK CFI 143d0 x23: x23
STACK CFI 14458 x23: .cfa -16 + ^
STACK CFI 144d4 x23: x23
STACK CFI 14504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14508 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1450c x23: .cfa -16 + ^
STACK CFI 14534 x23: x23
STACK CFI 145d8 x23: .cfa -16 + ^
STACK CFI 14628 x23: x23
STACK CFI 14660 x23: .cfa -16 + ^
STACK CFI 14674 x23: x23
STACK CFI 14678 x23: .cfa -16 + ^
STACK CFI INIT 14680 74 .cfa: sp 0 + .ra: x30
STACK CFI 14688 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14694 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 146b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 146bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 146e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 146f8 164 .cfa: sp 0 + .ra: x30
STACK CFI 146fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14704 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1471c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14728 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 147f4 x21: x21 x22: x22
STACK CFI 147f8 x23: x23 x24: x24
STACK CFI 14804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14808 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 14834 x21: x21 x22: x22
STACK CFI 14838 x23: x23 x24: x24
STACK CFI 14840 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14854 x21: x21 x22: x22
STACK CFI 14858 x23: x23 x24: x24
STACK CFI INIT 14860 304 .cfa: sp 0 + .ra: x30
STACK CFI 14864 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14868 .cfa: x29 128 +
STACK CFI 1486c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1489c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 14b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14b0c .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 14b68 1dc .cfa: sp 0 + .ra: x30
STACK CFI 14b6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14b74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14b88 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14b94 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14ba0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14cc4 x19: x19 x20: x20
STACK CFI 14cc8 x23: x23 x24: x24
STACK CFI 14ccc x25: x25 x26: x26
STACK CFI 14cd8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 14cdc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 14d30 x19: x19 x20: x20
STACK CFI 14d38 x23: x23 x24: x24
STACK CFI 14d3c x25: x25 x26: x26
STACK CFI 14d40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 14d48 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 14d4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14d54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14d64 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14d74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14d80 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14e9c x19: x19 x20: x20
STACK CFI 14ea0 x25: x25 x26: x26
STACK CFI 14eb0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14eb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 14ef4 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 14f14 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14f28 x19: x19 x20: x20
STACK CFI 14f34 x25: x25 x26: x26
STACK CFI 14f38 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 14f40 218 .cfa: sp 0 + .ra: x30
STACK CFI 14f44 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 14f4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 14f64 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 14f6c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 14f7c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14f88 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 150b8 x19: x19 x20: x20
STACK CFI 150bc x21: x21 x22: x22
STACK CFI 150c0 x25: x25 x26: x26
STACK CFI 150c4 x27: x27 x28: x28
STACK CFI 150d0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 150d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15118 x19: x19 x20: x20
STACK CFI 15120 x21: x21 x22: x22
STACK CFI 15124 x25: x25 x26: x26
STACK CFI 15128 x27: x27 x28: x28
STACK CFI 15130 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 15134 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 15148 x19: x19 x20: x20
STACK CFI 1514c x21: x21 x22: x22
STACK CFI 15150 x25: x25 x26: x26
STACK CFI 15154 x27: x27 x28: x28
STACK CFI INIT 15158 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 151a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 151c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 151e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 151e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 151f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 151f8 v8: .cfa -16 + ^
STACK CFI 1525c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 15260 34 .cfa: sp 0 + .ra: x30
STACK CFI 1526c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15290 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15298 260 .cfa: sp 0 + .ra: x30
STACK CFI 1529c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 152a8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 152b4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 15438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1543c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 154f8 dc .cfa: sp 0 + .ra: x30
STACK CFI 154fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15504 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 155d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 155d8 260 .cfa: sp 0 + .ra: x30
STACK CFI 155dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 155e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 155f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15614 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 15624 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 156b8 v8: v8 v9: v9
STACK CFI 156bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 156c0 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15838 4c .cfa: sp 0 + .ra: x30
STACK CFI 1583c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15844 x19: .cfa -16 + ^
STACK CFI 15874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 15878 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 15880 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15888 70 .cfa: sp 0 + .ra: x30
STACK CFI 1588c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15894 x19: .cfa -16 + ^
STACK CFI 158f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 158f8 90 .cfa: sp 0 + .ra: x30
STACK CFI 158fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15908 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15988 51c .cfa: sp 0 + .ra: x30
STACK CFI 1598c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15990 .cfa: x29 96 +
STACK CFI 15994 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 159a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 159c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 15a54 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 15ea8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ec8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ee8 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15fa8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15fc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15fd8 4c0 .cfa: sp 0 + .ra: x30
STACK CFI 15fdc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 15fe4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 15ff4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 16008 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 16074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16078 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 160c0 v8: .cfa -48 + ^
STACK CFI 161a8 v8: v8
STACK CFI 162ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 162b0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 162f8 v8: .cfa -48 + ^
STACK CFI 16354 v8: v8
STACK CFI INIT 16498 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1649c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 164a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 164b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 164cc x23: .cfa -16 + ^
STACK CFI 16500 x23: x23
STACK CFI 16528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1652c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1653c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
