MODULE Linux arm64 7BDA08A8FB791695F93BFC40468F4B9C0 libmtdev.so.1
INFO CODE_ID A808DA7B79FB9516F93BFC40468F4B9C565CA0C6
PUBLIC e48 0 mtdev_has_mt_event
PUBLIC f20 0 mtdev_get_abs_minimum
PUBLIC f88 0 mtdev_get_abs_maximum
PUBLIC ff0 0 mtdev_set_slots
PUBLIC 1130 0 mtdev_get_abs_fuzz
PUBLIC 1198 0 mtdev_get_abs_resolution
PUBLIC 1200 0 mtdev_set_abs_minimum
PUBLIC 1268 0 mtdev_set_mt_event
PUBLIC 1378 0 mtdev_set_abs_maximum
PUBLIC 13e0 0 mtdev_configure
PUBLIC 1658 0 mtdev_set_abs_fuzz
PUBLIC 16c0 0 mtdev_set_abs_resolution
PUBLIC 20c0 0 mtdev_new
PUBLIC 20d0 0 mtdev_init
PUBLIC 2140 0 mtdev_put_event
PUBLIC 22b8 0 mtdev_close
PUBLIC 22f0 0 mtdev_open
PUBLIC 2370 0 mtdev_delete
PUBLIC 2378 0 mtdev_new_open
PUBLIC 23d0 0 mtdev_close_delete
PUBLIC 23f8 0 mtdev_idle
PUBLIC 2470 0 mtdev_fetch_event
PUBLIC 2598 0 mtdev_empty
PUBLIC 25b8 0 mtdev_get_event
PUBLIC 25f8 0 mtdev_get
PUBLIC 2fa8 0 mtdev_match
PUBLIC 2fb0 0 mtdev_match_four
STACK CFI INIT d08 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d38 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT d78 48 .cfa: sp 0 + .ra: x30
STACK CFI d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d84 x19: .cfa -16 + ^
STACK CFI dbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dc8 80 .cfa: sp 0 + .ra: x30
STACK CFI dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dec x21: .cfa -16 + ^
STACK CFI e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e48 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT e98 88 .cfa: sp 0 + .ra: x30
STACK CFI ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI eb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f20 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT f88 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff0 140 .cfa: sp 0 + .ra: x30
STACK CFI ff4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI ffc x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1008 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 101c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 1028 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1030 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 10bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10c0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 1130 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1198 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1200 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1268 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 12c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 134c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1350 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1378 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e0 274 .cfa: sp 0 + .ra: x30
STACK CFI 13e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13ec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 140c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 158c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1590 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1658 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16c0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1728 154 .cfa: sp 0 + .ra: x30
STACK CFI 1790 .cfa: sp 32 +
STACK CFI 1874 .cfa: sp 0 +
STACK CFI INIT 1880 83c .cfa: sp 0 + .ra: x30
STACK CFI 1888 .cfa: sp 6864 +
STACK CFI 188c .ra: .cfa -6856 + ^ x29: .cfa -6864 + ^
STACK CFI 1894 x25: .cfa -6800 + ^ x26: .cfa -6792 + ^
STACK CFI 18cc x21: .cfa -6832 + ^ x22: .cfa -6824 + ^
STACK CFI 1904 x19: .cfa -6848 + ^ x20: .cfa -6840 + ^
STACK CFI 190c x23: .cfa -6816 + ^ x24: .cfa -6808 + ^
STACK CFI 1914 x27: .cfa -6784 + ^ x28: .cfa -6776 + ^
STACK CFI 1b04 x19: x19 x20: x20
STACK CFI 1b08 x21: x21 x22: x22
STACK CFI 1b0c x23: x23 x24: x24
STACK CFI 1b10 x27: x27 x28: x28
STACK CFI 1b38 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 1b3c .cfa: sp 6864 + .ra: .cfa -6856 + ^ x19: .cfa -6848 + ^ x20: .cfa -6840 + ^ x21: .cfa -6832 + ^ x22: .cfa -6824 + ^ x23: .cfa -6816 + ^ x24: .cfa -6808 + ^ x25: .cfa -6800 + ^ x26: .cfa -6792 + ^ x27: .cfa -6784 + ^ x28: .cfa -6776 + ^ x29: .cfa -6864 + ^
STACK CFI 1ce4 x23: x23 x24: x24
STACK CFI 1ce8 x27: x27 x28: x28
STACK CFI 1cf0 x19: x19 x20: x20
STACK CFI 1cf4 x21: x21 x22: x22
STACK CFI 1cf8 x19: .cfa -6848 + ^ x20: .cfa -6840 + ^ x21: .cfa -6832 + ^ x22: .cfa -6824 + ^ x23: .cfa -6816 + ^ x24: .cfa -6808 + ^ x27: .cfa -6784 + ^ x28: .cfa -6776 + ^
STACK CFI 20a8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 20ac x19: .cfa -6848 + ^ x20: .cfa -6840 + ^
STACK CFI 20b0 x21: .cfa -6832 + ^ x22: .cfa -6824 + ^
STACK CFI 20b4 x23: .cfa -6816 + ^ x24: .cfa -6808 + ^
STACK CFI 20b8 x27: .cfa -6784 + ^ x28: .cfa -6776 + ^
STACK CFI INIT 20c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d0 6c .cfa: sp 0 + .ra: x30
STACK CFI 20d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20e4 x19: .cfa -16 + ^
STACK CFI 2130 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2134 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2140 174 .cfa: sp 0 + .ra: x30
STACK CFI 2144 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 214c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2158 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 21a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 21a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 21a8 x23: .cfa -48 + ^
STACK CFI 224c x23: x23
STACK CFI 2250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2254 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 2290 x23: x23
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 22b8 38 .cfa: sp 0 + .ra: x30
STACK CFI 22c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22c8 x19: .cfa -16 + ^
STACK CFI 22e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 22f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2304 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2328 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 232c x21: .cfa -16 + ^
STACK CFI 2350 x21: x21
STACK CFI 2360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2364 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2368 x21: x21
STACK CFI INIT 2370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2378 54 .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2384 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 23c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 23d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23dc x19: .cfa -16 + ^
STACK CFI 23f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23f8 74 .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2408 x19: .cfa -32 + ^
STACK CFI 244c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2470 128 .cfa: sp 0 + .ra: x30
STACK CFI 2474 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 247c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2484 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2490 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24a0 x25: .cfa -16 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 24f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2584 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2598 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25b8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f8 fc .cfa: sp 0 + .ra: x30
STACK CFI 25fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2604 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2628 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2634 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2640 x25: .cfa -48 + ^
STACK CFI 269c x19: x19 x20: x20
STACK CFI 26a0 x21: x21 x22: x22
STACK CFI 26a4 x25: x25
STACK CFI 26a8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^
STACK CFI 26ac x19: x19 x20: x20
STACK CFI 26b4 x21: x21 x22: x22
STACK CFI 26bc x25: x25
STACK CFI 26d8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 26dc .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 26e8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 26ec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 26f0 x25: .cfa -48 + ^
STACK CFI INIT 26f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27a0 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 27a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2818 x21: .cfa -16 + ^
STACK CFI 2968 x21: x21
STACK CFI 2b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2c38 x21: .cfa -16 + ^
STACK CFI 2c3c x21: x21
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c48 35c .cfa: sp 0 + .ra: x30
STACK CFI 2c4c .cfa: sp 464 +
STACK CFI 2c58 .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 2c60 x19: .cfa -432 + ^
STACK CFI 2f10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f14 .cfa: sp 464 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x29: .cfa -448 + ^
STACK CFI INIT 2fa8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb0 26c .cfa: sp 0 + .ra: x30
STACK CFI 2fb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 310c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
