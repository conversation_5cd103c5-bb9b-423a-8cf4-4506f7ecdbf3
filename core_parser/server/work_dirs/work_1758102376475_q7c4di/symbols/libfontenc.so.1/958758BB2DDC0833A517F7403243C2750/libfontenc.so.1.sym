MODULE Linux arm64 958758BB2DDC0833A517F7403243C2750 libfontenc.so.1
INFO CODE_ID BB588795DC2D3308A517F7403243C275A7E4AC41
PUBLIC 2c78 0 FontEncDirectory
PUBLIC 2cd0 0 FontEncReallyLoad
PUBLIC 2e50 0 FontEncIdentify
PUBLIC 3048 0 FontEncSimpleRecode
PUBLIC 34e8 0 FontEncFromXLFD
PUBLIC 35c0 0 FontEncRecode
PUBLIC 3648 0 FontEncName
PUBLIC 3690 0 FontEncFind
PUBLIC 38c0 0 FontMapFind
PUBLIC 3918 0 FontEncMapFind
PUBLIC 3970 0 FontEncSimpleName
PUBLIC 39b0 0 FontEncUndefinedRecode
PUBLIC 39b8 0 FontEncUndefinedName
PUBLIC 39c0 0 FontMapReverse
PUBLIC 3b18 0 FontMapReverseFree
STACK CFI INIT 14c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14f8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1538 48 .cfa: sp 0 + .ra: x30
STACK CFI 153c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1544 x19: .cfa -16 + ^
STACK CFI 157c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1588 a8 .cfa: sp 0 + .ra: x30
STACK CFI 158c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1594 x19: .cfa -16 + ^
STACK CFI 1620 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1624 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1630 310 .cfa: sp 0 + .ra: x30
STACK CFI 1634 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1640 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 164c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16e4 x23: .cfa -16 + ^
STACK CFI 1760 x23: x23
STACK CFI 1770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1774 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 187c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1880 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1940 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1944 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 194c x19: .cfa -16 + ^
STACK CFI 19d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a20 540 .cfa: sp 0 + .ra: x30
STACK CFI 1a24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a2c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a38 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1aec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b30 x23: x23 x24: x24
STACK CFI 1b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b58 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 1b64 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b7c x25: .cfa -32 + ^
STACK CFI 1c2c x25: x25
STACK CFI 1c34 x23: x23 x24: x24
STACK CFI 1c4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1c64 x23: x23 x24: x24
STACK CFI 1c68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1ca0 x23: x23 x24: x24
STACK CFI 1ca4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1cd4 x23: x23 x24: x24
STACK CFI 1cd8 x25: x25
STACK CFI 1cf0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1d20 x25: x25
STACK CFI 1d24 x25: .cfa -32 + ^
STACK CFI 1d7c x23: x23 x24: x24
STACK CFI 1d80 x25: x25
STACK CFI 1d84 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1dc4 x25: x25
STACK CFI 1dc8 x25: .cfa -32 + ^
STACK CFI 1e14 x23: x23 x24: x24
STACK CFI 1e18 x25: x25
STACK CFI 1e1c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1e64 x25: x25
STACK CFI 1e70 x23: x23 x24: x24
STACK CFI 1e74 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1e80 x23: x23 x24: x24
STACK CFI 1e84 x25: x25
STACK CFI 1e88 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1e9c x23: x23 x24: x24
STACK CFI 1ea0 x25: x25
STACK CFI 1ea8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1f08 x25: x25
STACK CFI 1f0c x25: .cfa -32 + ^
STACK CFI 1f18 x23: x23 x24: x24
STACK CFI 1f1c x25: x25
STACK CFI 1f20 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1f30 x23: x23 x24: x24
STACK CFI 1f34 x25: x25
STACK CFI 1f3c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1f40 x25: .cfa -32 + ^
STACK CFI 1f58 x23: x23 x24: x24
STACK CFI 1f5c x25: x25
STACK CFI INIT 1f60 17c .cfa: sp 0 + .ra: x30
STACK CFI 1f64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 200c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 20e0 9a0 .cfa: sp 0 + .ra: x30
STACK CFI 20e4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 20f0 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 2104 x19: .cfa -336 + ^ x20: .cfa -328 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2154 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 2220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2224 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^ x29: .cfa -352 + ^
STACK CFI 223c x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2268 x25: x25 x26: x26
STACK CFI 2284 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2394 x25: x25 x26: x26
STACK CFI 239c x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 23f8 x25: x25 x26: x26
STACK CFI 23fc x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2484 x25: x25 x26: x26
STACK CFI 2488 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2660 x25: x25 x26: x26
STACK CFI 2664 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2a3c x25: x25 x26: x26
STACK CFI 2a40 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2a54 x25: x25 x26: x26
STACK CFI 2a58 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2a5c x25: x25 x26: x26
STACK CFI 2a60 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2a6c x25: x25 x26: x26
STACK CFI 2a70 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI INIT 2a80 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 2a84 .cfa: sp 3168 +
STACK CFI 2a88 .ra: .cfa -3160 + ^ x29: .cfa -3168 + ^
STACK CFI 2a90 x23: .cfa -3120 + ^ x24: .cfa -3112 + ^
STACK CFI 2aa4 x19: .cfa -3152 + ^ x20: .cfa -3144 + ^ x21: .cfa -3136 + ^ x22: .cfa -3128 + ^
STACK CFI 2af4 x25: .cfa -3104 + ^
STACK CFI 2b50 x25: x25
STACK CFI 2b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b80 .cfa: sp 3168 + .ra: .cfa -3160 + ^ x19: .cfa -3152 + ^ x20: .cfa -3144 + ^ x21: .cfa -3136 + ^ x22: .cfa -3128 + ^ x23: .cfa -3120 + ^ x24: .cfa -3112 + ^ x25: .cfa -3104 + ^ x29: .cfa -3168 + ^
STACK CFI 2c24 x25: x25
STACK CFI 2c3c x25: .cfa -3104 + ^
STACK CFI 2c6c x25: x25
STACK CFI 2c74 x25: .cfa -3104 + ^
STACK CFI INIT 2c78 58 .cfa: sp 0 + .ra: x30
STACK CFI 2c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c84 x19: .cfa -16 + ^
STACK CFI 2c9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ca0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2cd0 17c .cfa: sp 0 + .ra: x30
STACK CFI 2cd4 .cfa: sp 2112 +
STACK CFI 2cd8 .ra: .cfa -2104 + ^ x29: .cfa -2112 + ^
STACK CFI 2ce0 x19: .cfa -2096 + ^ x20: .cfa -2088 + ^
STACK CFI 2ce8 x21: .cfa -2080 + ^ x22: .cfa -2072 + ^
STACK CFI 2d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d88 .cfa: sp 2112 + .ra: .cfa -2104 + ^ x19: .cfa -2096 + ^ x20: .cfa -2088 + ^ x21: .cfa -2080 + ^ x22: .cfa -2072 + ^ x29: .cfa -2112 + ^
STACK CFI INIT 2e50 118 .cfa: sp 0 + .ra: x30
STACK CFI 2e54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e64 x21: .cfa -16 + ^
STACK CFI 2e74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f04 x19: x19 x20: x20
STACK CFI 2f10 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2f38 x19: x19 x20: x20
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2f4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2f64 x19: x19 x20: x20
STACK CFI INIT 2f68 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fc8 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3048 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30c8 c8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3190 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 31c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3220 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3280 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3308 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3348 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33e0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3418 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3450 60 .cfa: sp 0 + .ra: x30
STACK CFI 3454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 345c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3464 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 348c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 34b0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34e8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3550 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3560 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35c0 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3648 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3690 22c .cfa: sp 0 + .ra: x30
STACK CFI 3694 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 369c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 36a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3704 x25: .cfa -16 + ^
STACK CFI 3734 x25: x25
STACK CFI 375c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3760 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3798 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 37ec x25: .cfa -16 + ^
STACK CFI 385c x25: x25
STACK CFI 3860 x25: .cfa -16 + ^
STACK CFI 38a8 x25: x25
STACK CFI 38ac x25: .cfa -16 + ^
STACK CFI 38b8 x25: x25
STACK CFI INIT 38c0 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3918 54 .cfa: sp 0 + .ra: x30
STACK CFI 391c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3924 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3934 x21: .cfa -16 + ^
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 395c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3970 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39c0 154 .cfa: sp 0 + .ra: x30
STACK CFI 39c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 39d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3a0c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a74 x25: x25 x26: x26
STACK CFI 3a94 x19: x19 x20: x20
STACK CFI 3a98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3ae0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3ae4 x19: x19 x20: x20
STACK CFI 3ae8 x25: x25 x26: x26
STACK CFI 3b00 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3b04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3b08 x19: x19 x20: x20
STACK CFI INIT 3b18 48 .cfa: sp 0 + .ra: x30
STACK CFI 3b20 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3b34 x21: .cfa -16 + ^
STACK CFI 3b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
