MODULE Linux arm64 96F0E668B9F6E3446CCD31A076EE2F500 libss.so.2
INFO CODE_ID 68E6F096F6B944E36CCD31A076EE2F5018F9BB2A
PUBLIC 2348 0 initialize_ss_error_table_r
PUBLIC 23d8 0 initialize_ss_error_table
PUBLIC 23e8 0 ss_create_invocation
PUBLIC 2598 0 ss_delete_invocation
PUBLIC 2650 0 ss_help
PUBLIC 2938 0 ss_add_info_dir
PUBLIC 2a68 0 ss_delete_info_dir
PUBLIC 2c08 0 ss_execute_command
PUBLIC 2ce8 0 ss_execute_line
PUBLIC 2fc8 0 ss_listen
PUBLIC 32a0 0 ss_abort_subsystem
PUBLIC 32c0 0 ss_quit
PUBLIC 32d0 0 ss_rl_completion
PUBLIC 3300 0 ss_parse
PUBLIC 34c8 0 ss_name
PUBLIC 35a8 0 ss_error
PUBLIC 3678 0 ss_perror
PUBLIC 3688 0 ss_set_prompt
PUBLIC 36a0 0 ss_get_prompt
PUBLIC 36b8 0 ss_add_request_table
PUBLIC 37b0 0 ss_delete_request_table
PUBLIC 3808 0 ss_list_requests
PUBLIC 3a48 0 ss_safe_getenv
PUBLIC 3ac0 0 ss_page_stdin
PUBLIC 3c18 0 ss_pager_create
PUBLIC 3cb8 0 ss_self_identify
PUBLIC 3ce0 0 ss_subsystem_name
PUBLIC 3cf8 0 ss_subsystem_version
PUBLIC 3d10 0 ss_unimplemented
PUBLIC 3d60 0 ss_get_readline
STACK CFI INIT 2288 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2304 x19: .cfa -16 + ^
STACK CFI 233c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2340 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2348 90 .cfa: sp 0 + .ra: x30
STACK CFI 234c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2358 x19: .cfa -16 + ^
STACK CFI 238c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2390 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 23b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 23bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23e8 1ac .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 23f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2400 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2418 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2424 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2548 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2598 b8 .cfa: sp 0 + .ra: x30
STACK CFI 259c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 264c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2650 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 2654 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2668 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2674 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2690 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 26a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26b4 x27: .cfa -16 + ^
STACK CFI 2774 x19: x19 x20: x20
STACK CFI 2778 x21: x21 x22: x22
STACK CFI 2784 x27: x27
STACK CFI 2788 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 278c .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2790 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27e4 x19: x19 x20: x20
STACK CFI 27f0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 27f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 282c x19: x19 x20: x20
STACK CFI 2830 x21: x21 x22: x22
STACK CFI 283c x27: x27
STACK CFI 2840 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2844 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2850 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2854 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 28a8 x19: x19 x20: x20
STACK CFI 28ac x21: x21 x22: x22
STACK CFI 28b8 x27: x27
STACK CFI 28bc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 28c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 28c4 x19: x19 x20: x20
STACK CFI 28dc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 28e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 28f0 x19: x19 x20: x20
STACK CFI 28f8 x21: x21 x22: x22
STACK CFI 2908 x27: x27
STACK CFI 290c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2910 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 291c x19: x19 x20: x20
STACK CFI 2930 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2938 130 .cfa: sp 0 + .ra: x30
STACK CFI 293c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2944 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2950 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2964 x19: x19 x20: x20
STACK CFI 2978 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 297c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 299c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2a0c x19: x19 x20: x20
STACK CFI 2a10 x23: x23 x24: x24
STACK CFI 2a1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2a20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2a2c x19: x19 x20: x20
STACK CFI 2a38 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2a3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2a5c x19: x19 x20: x20
STACK CFI 2a60 x23: x23 x24: x24
STACK CFI INIT 2a68 94 .cfa: sp 0 + .ra: x30
STACK CFI 2a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a94 x21: .cfa -16 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2ae0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2b00 108 .cfa: sp 0 + .ra: x30
STACK CFI 2b04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2b14 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2b28 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2b44 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2bb0 x23: x23 x24: x24
STACK CFI 2bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2bd0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2be4 x23: x23 x24: x24
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 2c08 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2c0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c14 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c24 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c2c x23: .cfa -32 + ^
STACK CFI 2cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2cd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2ce8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2cec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2cf4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d10 x21: .cfa -48 + ^
STACK CFI 2d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2de0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2de4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2e20 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e48 x19: .cfa -16 + ^
STACK CFI 2e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e68 15c .cfa: sp 0 + .ra: x30
STACK CFI 2e6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e78 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2fc8 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 2fd0 .cfa: sp 8864 +
STACK CFI 2fdc .ra: .cfa -8856 + ^ x29: .cfa -8864 + ^
STACK CFI 2ff0 x19: .cfa -8848 + ^ x20: .cfa -8840 + ^
STACK CFI 3020 x21: .cfa -8832 + ^ x22: .cfa -8824 + ^ x23: .cfa -8816 + ^
STACK CFI 3228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 322c .cfa: sp 8864 + .ra: .cfa -8856 + ^ x19: .cfa -8848 + ^ x20: .cfa -8840 + ^ x21: .cfa -8832 + ^ x22: .cfa -8824 + ^ x23: .cfa -8816 + ^ x29: .cfa -8864 + ^
STACK CFI INIT 32a0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 32c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 32d0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3300 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 3304 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 330c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3314 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3324 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 33d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 33dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 34a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 34a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 34c8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 34cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3578 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 35a8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 35ac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 35b4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 35e4 x21: .cfa -272 + ^
STACK CFI 3670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3674 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3678 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3688 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36b8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 36bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 36cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 36d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 377c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 37b0 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3808 23c .cfa: sp 0 + .ra: x30
STACK CFI 380c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 3814 x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 3820 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 383c x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 3884 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 38e8 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 39b4 x27: x27 x28: x28
STACK CFI 39d4 x23: x23 x24: x24
STACK CFI 39fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3a00 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^ x29: .cfa -384 + ^
STACK CFI 3a1c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 3a3c x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 3a40 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI INIT 3a48 74 .cfa: sp 0 + .ra: x30
STACK CFI 3a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3aac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ac0 158 .cfa: sp 0 + .ra: x30
STACK CFI 3ac4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 3ad4 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 3ae8 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI INIT 3c18 9c .cfa: sp 0 + .ra: x30
STACK CFI 3c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c24 x19: .cfa -32 + ^
STACK CFI 3c84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3cb8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ce0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cf8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d28 34 .cfa: sp 0 + .ra: x30
STACK CFI 3d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d34 x19: .cfa -16 + ^
STACK CFI 3d58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d60 1cc .cfa: sp 0 + .ra: x30
STACK CFI 3d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3d8c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3d90 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3d9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3dc4 x19: x19 x20: x20
STACK CFI 3dcc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3dd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e50 x19: x19 x20: x20
STACK CFI 3e58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f14 x19: x19 x20: x20
STACK CFI 3f1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
