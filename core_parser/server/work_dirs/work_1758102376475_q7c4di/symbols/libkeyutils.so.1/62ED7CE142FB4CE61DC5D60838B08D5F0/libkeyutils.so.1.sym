MODULE Linux arm64 62ED7CE142FB4CE61DC5D60838B08D5F0 libkeyutils.so.1
INFO CODE_ID E17CED62FB42E64C1DC5D60838B08D5FFF9D5ED3
PUBLIC 1658 0 add_key
PUBLIC 1690 0 request_key
PUBLIC 16c0 0 keyctl
PUBLIC 1750 0 keyctl_get_keyring_ID
PUBLIC 1770 0 keyctl_join_session_keyring
PUBLIC 1790 0 keyctl_update
PUBLIC 17a8 0 keyctl_revoke
PUBLIC 17b8 0 keyctl_chown
PUBLIC 17d0 0 keyctl_setperm
PUBLIC 17e0 0 keyctl_describe
PUBLIC 17f8 0 keyctl_clear
PUBLIC 1808 0 keyctl_link
PUBLIC 1818 0 keyctl_unlink
PUBLIC 1828 0 keyctl_search
PUBLIC 1848 0 keyctl_read
PUBLIC 1860 0 keyctl_instantiate
PUBLIC 1880 0 keyctl_negate
PUBLIC 1898 0 keyctl_set_reqkey_keyring
PUBLIC 18a8 0 keyctl_set_timeout
PUBLIC 18b8 0 keyctl_assume_authority
PUBLIC 18c8 0 keyctl_get_security
PUBLIC 18e0 0 keyctl_session_to_parent
PUBLIC 18e8 0 keyctl_reject
PUBLIC 1968 0 keyctl_instantiate_iov
PUBLIC 1ab8 0 keyctl_invalidate
PUBLIC 1ac8 0 keyctl_get_persistent
PUBLIC 1ad8 0 keyctl_dh_compute
PUBLIC 1b48 0 keyctl_dh_compute_kdf
PUBLIC 1bd0 0 keyctl_restrict_keyring
PUBLIC 1be8 0 keyctl_pkey_query
PUBLIC 1c00 0 keyctl_pkey_encrypt
PUBLIC 1c70 0 keyctl_pkey_decrypt
PUBLIC 1ce0 0 keyctl_pkey_sign
PUBLIC 1d50 0 keyctl_pkey_verify
PUBLIC 1dc0 0 keyctl_describe_alloc
PUBLIC 1e68 0 keyctl_read_alloc
PUBLIC 20d8 0 keyctl_get_security_alloc
PUBLIC 2180 0 keyctl_dh_compute_alloc
PUBLIC 2228 0 recursive_key_scan
PUBLIC 2240 0 recursive_session_key_scan
PUBLIC 2290 0 find_key_by_type_and_desc
STACK CFI INIT 1598 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15c8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1608 48 .cfa: sp 0 + .ra: x30
STACK CFI 160c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1614 x19: .cfa -16 + ^
STACK CFI 164c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1650 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1658 38 .cfa: sp 0 + .ra: x30
STACK CFI 1664 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 168c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1690 30 .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 16d4 x19: .cfa -96 + ^
STACK CFI 1748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 174c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1750 20 .cfa: sp 0 + .ra: x30
STACK CFI 1754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 176c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1770 1c .cfa: sp 0 + .ra: x30
STACK CFI 1774 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1788 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1790 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1808 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1818 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1828 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1848 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1860 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1880 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1898 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18c8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18e8 80 .cfa: sp 0 + .ra: x30
STACK CFI 18ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1900 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 193c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1964 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1968 14c .cfa: sp 0 + .ra: x30
STACK CFI 196c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1978 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1984 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 198c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 19c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 19c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 19f0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1a7c x25: x25 x26: x26
STACK CFI 1a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1a88 x25: x25 x26: x26
STACK CFI 1aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1aac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1ab0 x25: x25 x26: x26
STACK CFI INIT 1ab8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad8 70 .cfa: sp 0 + .ra: x30
STACK CFI 1adc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1aec x19: .cfa -48 + ^
STACK CFI 1b40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1b48 88 .cfa: sp 0 + .ra: x30
STACK CFI 1b4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1b5c x19: .cfa -96 + ^
STACK CFI 1bc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1bcc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1bd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 70 .cfa: sp 0 + .ra: x30
STACK CFI 1c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1c14 x19: .cfa -64 + ^
STACK CFI 1c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1c70 70 .cfa: sp 0 + .ra: x30
STACK CFI 1c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1c84 x19: .cfa -64 + ^
STACK CFI 1cd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cdc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1ce0 70 .cfa: sp 0 + .ra: x30
STACK CFI 1ce4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cf4 x19: .cfa -64 + ^
STACK CFI 1d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d4c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1d50 70 .cfa: sp 0 + .ra: x30
STACK CFI 1d54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1d64 x19: .cfa -64 + ^
STACK CFI 1db8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1dbc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1dc0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1dd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1de8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e34 x19: x19 x20: x20
STACK CFI 1e40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1e44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1e50 x19: x19 x20: x20
STACK CFI 1e58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1e60 x19: x19 x20: x20
STACK CFI INIT 1e68 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1edc x19: x19 x20: x20
STACK CFI 1eec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1ef0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1f04 x19: x19 x20: x20
STACK CFI 1f0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1f10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1f14 x19: x19 x20: x20
STACK CFI INIT 1f20 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 1f24 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 1f2c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 1f34 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 1f44 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 1f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 1f88 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI 1f94 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 1fa0 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 2098 x23: x23 x24: x24
STACK CFI 209c x25: x25 x26: x26
STACK CFI 20a0 x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 20c8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 20cc x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 20d0 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI INIT 20d8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 20dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2100 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 214c x19: x19 x20: x20
STACK CFI 2158 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 215c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2168 x19: x19 x20: x20
STACK CFI 2170 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2174 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2178 x19: x19 x20: x20
STACK CFI INIT 2180 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2190 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 21b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 21f0 x19: x19 x20: x20
STACK CFI 21fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2200 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2204 x19: x19 x20: x20
STACK CFI 2210 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2220 x19: x19 x20: x20
STACK CFI INIT 2228 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2240 4c .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 224c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2290 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 2294 .cfa: sp 2240 +
STACK CFI 2298 .ra: .cfa -2232 + ^ x29: .cfa -2240 + ^
STACK CFI 22a0 x25: .cfa -2176 + ^ x26: .cfa -2168 + ^
STACK CFI 22ac x27: .cfa -2160 + ^ x28: .cfa -2152 + ^
STACK CFI 22d0 x19: .cfa -2224 + ^ x20: .cfa -2216 + ^
STACK CFI 2328 x21: .cfa -2208 + ^ x22: .cfa -2200 + ^
STACK CFI 2338 x23: .cfa -2192 + ^ x24: .cfa -2184 + ^
STACK CFI 2468 x21: x21 x22: x22
STACK CFI 246c x23: x23 x24: x24
STACK CFI 24a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 24a4 .cfa: sp 2240 + .ra: .cfa -2232 + ^ x19: .cfa -2224 + ^ x20: .cfa -2216 + ^ x21: .cfa -2208 + ^ x22: .cfa -2200 + ^ x23: .cfa -2192 + ^ x24: .cfa -2184 + ^ x25: .cfa -2176 + ^ x26: .cfa -2168 + ^ x27: .cfa -2160 + ^ x28: .cfa -2152 + ^ x29: .cfa -2240 + ^
STACK CFI 24dc x21: x21 x22: x22
STACK CFI 24e0 x23: x23 x24: x24
STACK CFI 24e4 x21: .cfa -2208 + ^ x22: .cfa -2200 + ^ x23: .cfa -2192 + ^ x24: .cfa -2184 + ^
STACK CFI 2500 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2528 x21: .cfa -2208 + ^ x22: .cfa -2200 + ^
STACK CFI 252c x23: .cfa -2192 + ^ x24: .cfa -2184 + ^
