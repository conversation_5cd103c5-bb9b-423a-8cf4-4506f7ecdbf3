MODULE Linux arm64 1C164308FEF9E5748B3A526B492C22C80 libip6tc.so.2
INFO CODE_ID 0843161CF9FE74E58B3A526B492C22C8F991F1C1
PUBLIC 1ba0 0 ip6tc_first_chain
PUBLIC 1be0 0 ip6tc_next_chain
PUBLIC 1c18 0 ip6tc_next_rule
PUBLIC 1c60 0 ip6tc_strerror
PUBLIC 1d90 0 ip6tc_free
PUBLIC 2410 0 ip6tc_is_chain
PUBLIC 2440 0 ip6tc_delete_num_entry
PUBLIC 25e0 0 ip6tc_set_policy
PUBLIC 27d0 0 dump_entries6
PUBLIC 2d70 0 ip6tc_get_target
PUBLIC 2f00 0 ip6tc_init
PUBLIC 3488 0 ip6tc_first_rule
PUBLIC 34f0 0 ip6tc_set_counter
PUBLIC 35d8 0 ip6tc_get_policy
PUBLIC 3640 0 ip6tc_read_counter
PUBLIC 36e0 0 ip6tc_zero_counter
PUBLIC 37b8 0 ip6tc_create_chain
PUBLIC 38f8 0 ip6tc_commit
PUBLIC 3ec8 0 ip6tc_builtin
PUBLIC 40f8 0 ip6tc_insert_entry
PUBLIC 42f8 0 ip6tc_replace_entry
PUBLIC 44c0 0 ip6tc_append_entry
PUBLIC 4ac8 0 ip6tc_delete_entry
PUBLIC 4ad0 0 ip6tc_rename_chain
PUBLIC 4c18 0 ip6tc_get_references
PUBLIC 4c80 0 ip6tc_delete_chain
PUBLIC 4dd8 0 ip6tc_flush_entries
PUBLIC 4e78 0 ip6tc_zero_entries
PUBLIC 4f20 0 ip6tc_check_entry
STACK CFI INIT 1a58 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac8 48 .cfa: sp 0 + .ra: x30
STACK CFI 1acc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ad4 x19: .cfa -16 + ^
STACK CFI 1b0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b18 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ba0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c18 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c60 ec .cfa: sp 0 + .ra: x30
STACK CFI 1c64 .cfa: sp 576 +
STACK CFI 1c70 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 1c78 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d1c .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x29: .cfa -576 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d48 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x29: .cfa -576 + ^
STACK CFI INIT 1d50 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d90 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1da0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1dbc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1dd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1e20 x19: x19 x20: x20
STACK CFI 1e40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1e48 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1e90 70 .cfa: sp 0 + .ra: x30
STACK CFI 1e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ea0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f00 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f0c x19: .cfa -16 + ^
STACK CFI 1f3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f48 150 .cfa: sp 0 + .ra: x30
STACK CFI 1f4c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1f54 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1f60 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1f70 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1f80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 202c x23: x23 x24: x24
STACK CFI 2030 x27: x27 x28: x28
STACK CFI 2044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2048 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2068 x23: x23 x24: x24
STACK CFI 2070 x27: x27 x28: x28
STACK CFI 2074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2078 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2098 100 .cfa: sp 0 + .ra: x30
STACK CFI 209c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 20ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 20c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 20d8 x23: .cfa -32 + ^
STACK CFI 2134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2138 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2198 154 .cfa: sp 0 + .ra: x30
STACK CFI 219c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 21ac x25: .cfa -32 + ^
STACK CFI 21b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 21c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 21cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 227c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2280 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 22f0 11c .cfa: sp 0 + .ra: x30
STACK CFI 22f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 22fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2304 x23: .cfa -32 + ^
STACK CFI 230c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2400 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2410 2c .cfa: sp 0 + .ra: x30
STACK CFI 2414 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2438 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2440 140 .cfa: sp 0 + .ra: x30
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 245c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 24f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2574 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2580 5c .cfa: sp 0 + .ra: x30
STACK CFI 2584 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 258c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2598 x21: .cfa -16 + ^
STACK CFI 25d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 25e0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 25e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2604 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 266c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2670 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 26b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26d8 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2768 68 .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27d0 51c .cfa: sp 0 + .ra: x30
STACK CFI 27d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 27e4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2884 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2898 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 28a4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 28a8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2c14 x19: x19 x20: x20
STACK CFI 2c18 x21: x21 x22: x22
STACK CFI 2c1c x25: x25 x26: x26
STACK CFI 2c20 x27: x27 x28: x28
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 2c48 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2cd8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2cdc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2ce0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2ce4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2ce8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 2cf0 80 .cfa: sp 0 + .ra: x30
STACK CFI 2d48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2d70 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2de0 78 .cfa: sp 0 + .ra: x30
STACK CFI 2df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e04 x19: .cfa -16 + ^
STACK CFI 2e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e58 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2e5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e84 x23: .cfa -16 + ^
STACK CFI 2edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2ee0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2f00 584 .cfa: sp 0 + .ra: x30
STACK CFI 2f04 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2f0c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2f18 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2f34 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 309c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 31c0 x25: x25 x26: x26
STACK CFI 32b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 32b8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 32f8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3354 x25: x25 x26: x26
STACK CFI 335c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 33d0 x25: x25 x26: x26
STACK CFI 33f0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 33fc x25: x25 x26: x26
STACK CFI 3418 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3434 x25: x25 x26: x26
STACK CFI 3440 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3470 x25: x25 x26: x26
STACK CFI 3480 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 3488 68 .cfa: sp 0 + .ra: x30
STACK CFI 348c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 34f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 34f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 350c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 351c x21: .cfa -16 + ^
STACK CFI 357c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3580 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 35ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 35b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 35d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 35d8 68 .cfa: sp 0 + .ra: x30
STACK CFI 35dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35f4 x19: .cfa -16 + ^
STACK CFI 3620 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3624 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 363c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3640 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 365c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 36cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 36e0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 36e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3764 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 378c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37b8 13c .cfa: sp 0 + .ra: x30
STACK CFI 37bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 38f8 5cc .cfa: sp 0 + .ra: x30
STACK CFI 38fc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3908 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 392c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3930 .cfa: sp 128 + .ra: .cfa -120 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 3934 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 393c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3944 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3948 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3cf0 x19: x19 x20: x20
STACK CFI 3cf4 x21: x21 x22: x22
STACK CFI 3cf8 x25: x25 x26: x26
STACK CFI 3cfc x27: x27 x28: x28
STACK CFI 3d00 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3d78 x19: x19 x20: x20
STACK CFI 3d7c x21: x21 x22: x22
STACK CFI 3d84 x25: x25 x26: x26
STACK CFI 3d88 x27: x27 x28: x28
STACK CFI 3d8c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3d90 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3da4 x19: x19 x20: x20
STACK CFI 3da8 x21: x21 x22: x22
STACK CFI 3dac x25: x25 x26: x26
STACK CFI 3db0 x27: x27 x28: x28
STACK CFI 3dbc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3dc0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3ec8 50 .cfa: sp 0 + .ra: x30
STACK CFI 3ecc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ef0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3efc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f18 1dc .cfa: sp 0 + .ra: x30
STACK CFI 3f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4018 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4038 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 408c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 40bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40f8 1fc .cfa: sp 0 + .ra: x30
STACK CFI 40fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4114 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 411c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4124 x23: .cfa -16 + ^
STACK CFI 4210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4214 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 423c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 42c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 42f8 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 42fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4314 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 431c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 445c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 447c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44c0 10c .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4564 x19: x19 x20: x20
STACK CFI 4574 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4578 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4588 x19: x19 x20: x20
STACK CFI 4598 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 459c .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 45b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 45bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 45c8 x19: x19 x20: x20
STACK CFI INIT 45d0 4f8 .cfa: sp 0 + .ra: x30
STACK CFI 45d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 45ec x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 45f4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4600 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4630 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4660 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 46b8 x25: x25 x26: x26
STACK CFI 46bc x27: x27 x28: x28
STACK CFI 46e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 46ec .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 4710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4714 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4a14 x25: x25 x26: x26
STACK CFI 4a18 x27: x27 x28: x28
STACK CFI 4a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a20 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4a38 x27: x27 x28: x28
STACK CFI 4a44 x25: x25 x26: x26
STACK CFI 4a48 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4a84 x25: x25 x26: x26
STACK CFI 4a88 x27: x27 x28: x28
STACK CFI 4a8c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 4ac8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ad0 148 .cfa: sp 0 + .ra: x30
STACK CFI 4ad4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ae8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4afc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c18 68 .cfa: sp 0 + .ra: x30
STACK CFI 4c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c34 x19: .cfa -16 + ^
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c80 158 .cfa: sp 0 + .ra: x30
STACK CFI 4c84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ca4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4dd8 9c .cfa: sp 0 + .ra: x30
STACK CFI 4ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4df4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e38 x19: x19 x20: x20
STACK CFI 4e50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4e54 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4e70 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 4e78 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4e7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e94 x19: .cfa -16 + ^
STACK CFI 4ef8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4efc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4f18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4f20 8 .cfa: sp 0 + .ra: x30
