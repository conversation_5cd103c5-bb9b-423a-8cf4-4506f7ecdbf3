MODULE Linux arm64 6DB7782BD39DCC13CB3C56DFE89FEF480 libidn2.so.0
INFO CODE_ID 2B78B76D9DD313CCCB3C56DFE89FEF48C66AF7CA
PUBLIC 1df0 0 idn2_lookup_u8
PUBLIC 2778 0 idn2_lookup_ul
PUBLIC 2818 0 idn2_to_ascii_4i2
PUBLIC 2958 0 idn2_to_ascii_4i
PUBLIC 2a10 0 idn2_to_ascii_4z
PUBLIC 2ae0 0 idn2_to_ascii_8z
PUBLIC 2ae8 0 idn2_to_ascii_lz
PUBLIC 2d98 0 idn2_to_unicode_8z4z
PUBLIC 2db0 0 idn2_to_unicode_4z4z
PUBLIC 2ea8 0 idn2_to_unicode_44i
PUBLIC 3000 0 idn2_to_unicode_8z8z
PUBLIC 30d8 0 idn2_to_unicode_8zlz
PUBLIC 31a0 0 idn2_to_unicode_lzlz
PUBLIC 3250 0 idn2_register_u8
PUBLIC 3538 0 idn2_register_ul
PUBLIC 3768 0 idn2_check_version
PUBLIC 37a8 0 idn2_strerror
PUBLIC 3948 0 idn2_strerror_name
PUBLIC 3af8 0 _idn2_punycode_encode
PUBLIC 3e00 0 _idn2_punycode_decode
PUBLIC 4140 0 idn2_free
STACK CFI INIT 1728 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1758 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1798 48 .cfa: sp 0 + .ra: x30
STACK CFI 179c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17a4 x19: .cfa -16 + ^
STACK CFI 17dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e8 16c .cfa: sp 0 + .ra: x30
STACK CFI 17ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 17f4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1800 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1808 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 182c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1848 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 18d0 x27: x27 x28: x28
STACK CFI 18e0 x25: x25 x26: x26
STACK CFI 1904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1908 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 1924 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1940 x25: x25 x26: x26
STACK CFI 1944 x27: x27 x28: x28
STACK CFI 194c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1950 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 1958 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1988 468 .cfa: sp 0 + .ra: x30
STACK CFI 198c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1994 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 19a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 19b8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1a40 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1a44 x27: .cfa -48 + ^
STACK CFI 1aa4 x25: x25 x26: x26
STACK CFI 1aa8 x27: x27
STACK CFI 1b7c x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 1b80 x25: x25 x26: x26
STACK CFI 1b88 x27: x27
STACK CFI 1bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1bb0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 1bdc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1be0 x27: .cfa -48 + ^
STACK CFI 1be4 x25: x25 x26: x26 x27: x27
STACK CFI 1cf8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1d48 x25: x25 x26: x26
STACK CFI 1da4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1dd8 x25: x25 x26: x26
STACK CFI 1de8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1dec x27: .cfa -48 + ^
STACK CFI INIT 1df0 988 .cfa: sp 0 + .ra: x30
STACK CFI 1df4 .cfa: sp 1040 +
STACK CFI 1dfc .ra: .cfa -1032 + ^ x29: .cfa -1040 + ^
STACK CFI 1e08 x25: .cfa -976 + ^ x26: .cfa -968 + ^
STACK CFI 1e20 x19: .cfa -1024 + ^ x20: .cfa -1016 + ^
STACK CFI 1e28 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^
STACK CFI 1e5c x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 1e6c x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 2094 x21: x21 x22: x22
STACK CFI 2098 x23: x23 x24: x24
STACK CFI 209c x27: x27 x28: x28
STACK CFI 20a0 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 20b4 x21: x21 x22: x22
STACK CFI 20b8 x23: x23 x24: x24
STACK CFI 20bc x27: x27 x28: x28
STACK CFI 20e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 20ec .cfa: sp 1040 + .ra: .cfa -1032 + ^ x19: .cfa -1024 + ^ x20: .cfa -1016 + ^ x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x25: .cfa -976 + ^ x26: .cfa -968 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^ x29: .cfa -1040 + ^
STACK CFI 20f8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2104 x21: x21 x22: x22
STACK CFI 2108 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 212c x21: x21 x22: x22
STACK CFI 2130 x23: x23 x24: x24
STACK CFI 2134 x27: x27 x28: x28
STACK CFI 2138 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 21d4 x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 2388 x21: x21 x22: x22
STACK CFI 238c x23: x23 x24: x24
STACK CFI 2390 x27: x27 x28: x28
STACK CFI 2394 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 23c4 x23: x23 x24: x24
STACK CFI 23c8 x27: x27 x28: x28
STACK CFI 23d0 x21: x21 x22: x22
STACK CFI 23d4 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 2428 x21: x21 x22: x22
STACK CFI 242c x23: x23 x24: x24
STACK CFI 2430 x27: x27 x28: x28
STACK CFI 2438 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 2450 x21: x21 x22: x22
STACK CFI 2454 x23: x23 x24: x24
STACK CFI 2458 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 2468 x21: x21 x22: x22
STACK CFI 246c x23: x23 x24: x24
STACK CFI 2470 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 24b0 x27: x27 x28: x28
STACK CFI 2508 x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 25a0 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 25b4 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 25bc x21: x21 x22: x22
STACK CFI 25c0 x23: x23 x24: x24
STACK CFI 25c4 x27: x27 x28: x28
STACK CFI 25c8 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 25f0 x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 25fc x27: x27 x28: x28
STACK CFI 260c x21: x21 x22: x22
STACK CFI 2610 x23: x23 x24: x24
STACK CFI 2614 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 2630 x21: x21 x22: x22
STACK CFI 2634 x23: x23 x24: x24
STACK CFI 2638 x27: x27 x28: x28
STACK CFI 263c x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 26a8 x21: x21 x22: x22
STACK CFI 26ac x23: x23 x24: x24
STACK CFI 26b0 x27: x27 x28: x28
STACK CFI 26b4 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 26bc x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 26c8 x21: x21 x22: x22
STACK CFI 26cc x23: x23 x24: x24
STACK CFI 26d0 x27: x27 x28: x28
STACK CFI 26d4 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 26e4 x21: x21 x22: x22
STACK CFI 26e8 x23: x23 x24: x24
STACK CFI 26f0 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^
STACK CFI 26f4 x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 26f8 x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 270c x21: x21 x22: x22
STACK CFI 2710 x23: x23 x24: x24
STACK CFI 2714 x27: x27 x28: x28
STACK CFI 2718 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 273c x21: x21 x22: x22
STACK CFI 2740 x23: x23 x24: x24
STACK CFI 2744 x27: x27 x28: x28
STACK CFI 2748 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 2758 x21: x21 x22: x22
STACK CFI 275c x23: x23 x24: x24
STACK CFI 2760 x27: x27 x28: x28
STACK CFI 2764 x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 276c x21: x21 x22: x22
STACK CFI 2770 x23: x23 x24: x24
STACK CFI 2774 x27: x27 x28: x28
STACK CFI INIT 2778 9c .cfa: sp 0 + .ra: x30
STACK CFI 277c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2790 x21: .cfa -16 + ^
STACK CFI 27e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2818 140 .cfa: sp 0 + .ra: x30
STACK CFI 281c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2824 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2830 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2844 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2850 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 28d4 x25: x25 x26: x26
STACK CFI 28fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2900 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 291c x25: x25 x26: x26
STACK CFI 2930 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 293c x25: x25 x26: x26
STACK CFI 2944 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 294c x25: x25 x26: x26
STACK CFI 2954 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 2958 b4 .cfa: sp 0 + .ra: x30
STACK CFI 295c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2964 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 296c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2a10 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2a14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2a1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2a2c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2a48 x23: .cfa -32 + ^
STACK CFI 2a84 x23: x23
STACK CFI 2aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2aac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 2ac8 x23: x23
STACK CFI 2adc x23: .cfa -32 + ^
STACK CFI INIT 2ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ae8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af0 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 2af4 .cfa: sp 1392 +
STACK CFI 2af8 .ra: .cfa -1384 + ^ x29: .cfa -1392 + ^
STACK CFI 2b00 x25: .cfa -1328 + ^ x26: .cfa -1320 + ^
STACK CFI 2b20 x27: .cfa -1312 + ^ x28: .cfa -1304 + ^
STACK CFI 2b2c x21: .cfa -1360 + ^ x22: .cfa -1352 + ^
STACK CFI 2b34 x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2b44 x19: .cfa -1376 + ^ x20: .cfa -1368 + ^
STACK CFI 2c00 x19: x19 x20: x20
STACK CFI 2c04 x21: x21 x22: x22
STACK CFI 2c08 x23: x23 x24: x24
STACK CFI 2c50 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2c54 .cfa: sp 1392 + .ra: .cfa -1384 + ^ x19: .cfa -1376 + ^ x20: .cfa -1368 + ^ x21: .cfa -1360 + ^ x22: .cfa -1352 + ^ x23: .cfa -1344 + ^ x24: .cfa -1336 + ^ x25: .cfa -1328 + ^ x26: .cfa -1320 + ^ x27: .cfa -1312 + ^ x28: .cfa -1304 + ^ x29: .cfa -1392 + ^
STACK CFI 2c70 x19: x19 x20: x20
STACK CFI 2c74 x21: x21 x22: x22
STACK CFI 2c78 x23: x23 x24: x24
STACK CFI 2c7c x19: .cfa -1376 + ^ x20: .cfa -1368 + ^ x21: .cfa -1360 + ^ x22: .cfa -1352 + ^ x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2cb0 x19: x19 x20: x20
STACK CFI 2cb4 x21: x21 x22: x22
STACK CFI 2cb8 x23: x23 x24: x24
STACK CFI 2cbc x19: .cfa -1376 + ^ x20: .cfa -1368 + ^ x21: .cfa -1360 + ^ x22: .cfa -1352 + ^ x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2d1c x19: x19 x20: x20
STACK CFI 2d20 x21: x21 x22: x22
STACK CFI 2d24 x23: x23 x24: x24
STACK CFI 2d28 x19: .cfa -1376 + ^ x20: .cfa -1368 + ^ x21: .cfa -1360 + ^ x22: .cfa -1352 + ^ x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2d34 x19: x19 x20: x20
STACK CFI 2d38 x21: x21 x22: x22
STACK CFI 2d3c x23: x23 x24: x24
STACK CFI 2d64 x19: .cfa -1376 + ^ x20: .cfa -1368 + ^ x21: .cfa -1360 + ^ x22: .cfa -1352 + ^ x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2d6c x19: x19 x20: x20
STACK CFI 2d70 x21: x21 x22: x22
STACK CFI 2d74 x23: x23 x24: x24
STACK CFI 2d7c x19: .cfa -1376 + ^ x20: .cfa -1368 + ^
STACK CFI 2d80 x21: .cfa -1360 + ^ x22: .cfa -1352 + ^
STACK CFI 2d84 x23: .cfa -1344 + ^ x24: .cfa -1336 + ^
STACK CFI 2d8c x19: x19 x20: x20
STACK CFI 2d90 x21: x21 x22: x22
STACK CFI 2d94 x23: x23 x24: x24
STACK CFI INIT 2d98 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2db0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2db4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2dbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2dcc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2de8 x23: .cfa -48 + ^
STACK CFI 2e30 x23: x23
STACK CFI 2e38 x23: .cfa -48 + ^
STACK CFI 2e44 x23: x23
STACK CFI 2e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e7c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 2e88 x23: .cfa -48 + ^
STACK CFI 2e8c x23: x23
STACK CFI 2e90 x23: .cfa -48 + ^
STACK CFI 2e98 x23: x23
STACK CFI 2ea0 x23: .cfa -48 + ^
STACK CFI INIT 2ea8 154 .cfa: sp 0 + .ra: x30
STACK CFI 2eac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2eb4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ec0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2ed4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ee8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2f68 x25: x25 x26: x26
STACK CFI 2f70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2f74 x25: x25 x26: x26
STACK CFI 2f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fa0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 2fb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fb4 x25: x25 x26: x26
STACK CFI 2fbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fe0 x25: x25 x26: x26
STACK CFI 2fec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ff8 x25: x25 x26: x26
STACK CFI INIT 3000 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3004 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 300c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3028 x21: .cfa -48 + ^
STACK CFI 3060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3064 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 30d8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 30dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 30e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3144 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 31a0 ac .cfa: sp 0 + .ra: x30
STACK CFI 31a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 31f8 x21: x21 x22: x22
STACK CFI 3204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3208 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 322c x21: x21 x22: x22
STACK CFI 3230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3234 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3250 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3270 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 3278 .cfa: sp 4448 +
STACK CFI 327c .ra: .cfa -4440 + ^ x29: .cfa -4448 + ^
STACK CFI 3284 x23: .cfa -4400 + ^ x24: .cfa -4392 + ^
STACK CFI 3294 x19: .cfa -4432 + ^ x20: .cfa -4424 + ^
STACK CFI 32a8 x21: .cfa -4416 + ^ x22: .cfa -4408 + ^
STACK CFI 3318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 331c .cfa: sp 4448 + .ra: .cfa -4440 + ^ x19: .cfa -4432 + ^ x20: .cfa -4424 + ^ x21: .cfa -4416 + ^ x22: .cfa -4408 + ^ x23: .cfa -4400 + ^ x24: .cfa -4392 + ^ x29: .cfa -4448 + ^
STACK CFI 33a0 x25: .cfa -4384 + ^
STACK CFI 3454 x25: x25
STACK CFI 3500 x25: .cfa -4384 + ^
STACK CFI 3504 x25: x25
STACK CFI 3508 x25: .cfa -4384 + ^
STACK CFI 3510 x25: x25
STACK CFI 3514 x25: .cfa -4384 + ^
STACK CFI 351c x25: x25
STACK CFI 3524 x25: .cfa -4384 + ^
STACK CFI 3530 x25: x25
STACK CFI INIT 3538 a0 .cfa: sp 0 + .ra: x30
STACK CFI 353c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3550 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 35ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 35d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 35d8 190 .cfa: sp 0 + .ra: x30
STACK CFI 35e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 35f8 x21: .cfa -16 + ^
STACK CFI 36bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 36c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3744 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 375c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3768 40 .cfa: sp 0 + .ra: x30
STACK CFI 3770 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3778 x19: .cfa -16 + ^
STACK CFI 3798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 37a8 1a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3948 1ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 3af8 308 .cfa: sp 0 + .ra: x30
STACK CFI 3b78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3b88 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3b94 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3de4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3df8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3e00 33c .cfa: sp 0 + .ra: x30
STACK CFI 3e08 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e14 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3e1c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3e7c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3e84 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3e90 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3f58 x21: x21 x22: x22
STACK CFI 3f5c x25: x25 x26: x26
STACK CFI 3f60 x27: x27 x28: x28
STACK CFI 3f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3f74 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3ff4 x21: x21 x22: x22
STACK CFI 3ffc x25: x25 x26: x26
STACK CFI 4000 x27: x27 x28: x28
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4008 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4110 x21: x21 x22: x22
STACK CFI 4114 x25: x25 x26: x26
STACK CFI 4118 x27: x27 x28: x28
STACK CFI 4120 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4128 x21: x21 x22: x22
STACK CFI 412c x25: x25 x26: x26
STACK CFI 4130 x27: x27 x28: x28
STACK CFI 4134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4140 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4148 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4220 70 .cfa: sp 0 + .ra: x30
STACK CFI 4224 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4288 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 428c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4290 19c .cfa: sp 0 + .ra: x30
STACK CFI 4294 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 42a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 42b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 42c4 x25: .cfa -32 + ^
STACK CFI 4314 x23: x23 x24: x24
STACK CFI 4318 x25: x25
STACK CFI 4330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4334 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 4354 x23: x23 x24: x24
STACK CFI 4358 x25: x25
STACK CFI 435c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4360 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4380 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 438c x25: .cfa -32 + ^
STACK CFI 43d0 x23: x23 x24: x24 x25: x25
STACK CFI 43d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 43e4 x25: .cfa -32 + ^
STACK CFI INIT 4430 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4458 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44d0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4540 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45b8 1c .cfa: sp 0 + .ra: x30
STACK CFI 45bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45d8 1c .cfa: sp 0 + .ra: x30
STACK CFI 45dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45f8 1c .cfa: sp 0 + .ra: x30
STACK CFI 45fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4618 1c .cfa: sp 0 + .ra: x30
STACK CFI 461c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4630 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4638 14c .cfa: sp 0 + .ra: x30
STACK CFI 4640 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4648 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4654 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4664 x23: .cfa -16 + ^
STACK CFI 46b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 46c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 46d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 46d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4744 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4788 270 .cfa: sp 0 + .ra: x30
STACK CFI 478c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4794 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 47a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 47b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4850 x25: .cfa -16 + ^
STACK CFI 4880 x25: x25
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4888 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 48a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 492c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4968 x25: .cfa -16 + ^
STACK CFI 49b0 x25: x25
STACK CFI 49b4 x25: .cfa -16 + ^
STACK CFI 49c0 x25: x25
STACK CFI INIT 49f8 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a60 50 .cfa: sp 0 + .ra: x30
STACK CFI 4a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ab0 6c .cfa: sp 0 + .ra: x30
STACK CFI 4ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ac0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b20 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b78 3c .cfa: sp 0 + .ra: x30
STACK CFI 4b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b84 x19: .cfa -16 + ^
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4bb8 44 .cfa: sp 0 + .ra: x30
STACK CFI 4bbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bc4 x19: .cfa -16 + ^
STACK CFI 4bf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c10 11c .cfa: sp 0 + .ra: x30
STACK CFI 4c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c38 x21: .cfa -16 + ^
STACK CFI 4c90 x21: x21
STACK CFI 4cb0 x19: x19 x20: x20
STACK CFI 4cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4cb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4cc4 x21: x21
STACK CFI 4cd4 x19: x19 x20: x20
STACK CFI 4cd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4cf4 x21: x21
STACK CFI 4cfc x19: x19 x20: x20
STACK CFI 4d00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4d24 x21: .cfa -16 + ^
STACK CFI INIT 4d30 3bc .cfa: sp 0 + .ra: x30
STACK CFI 4d34 .cfa: sp 80 +
STACK CFI 4d38 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d4c x21: .cfa -16 + ^
STACK CFI 4e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e74 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ee8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f2c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f8c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4fe8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 501c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5020 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50a4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 50f0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5118 b8 .cfa: sp 0 + .ra: x30
STACK CFI 511c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5134 x19: .cfa -112 + ^
STACK CFI 519c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 51d0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 51d4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 51e4 x19: .cfa -304 + ^
STACK CFI 5298 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 529c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x29: .cfa -320 + ^
STACK CFI INIT 52c0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 52c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x29: x29
