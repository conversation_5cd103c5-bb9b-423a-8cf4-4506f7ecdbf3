MODULE Linux arm64 73E1253A02105C034BD25AA2C00CDD350 libboost_atomic.so.1.77.0
INFO CODE_ID 3A25E1731002035C4BD25AA2C00CDD35
PUBLIC bb0 0 _init
PUBLIC cc0 0 call_weak_fn
PUBLIC cd4 0 deregister_tm_clones
PUBLIC d04 0 register_tm_clones
PUBLIC d40 0 __do_global_dtors_aux
PUBLIC d90 0 frame_dummy
PUBLIC da0 0 boost::atomics::detail::lock_pool::(anonymous namespace)::wait_state_list::allocate_buffer(unsigned long, boost::atomics::detail::lock_pool::(anonymous namespace)::wait_state_list::header*)
PUBLIC ee0 0 boost::atomics::detail::lock_pool::(anonymous namespace)::cleanup_lock_pool()
PUBLIC 10a0 0 boost::atomics::detail::find_address_generic(void const volatile*, void const volatile* const*, unsigned long)
PUBLIC 10e0 0 boost::atomics::detail::lock_pool::short_lock(unsigned long)
PUBLIC 11c0 0 boost::atomics::detail::lock_pool::long_lock(unsigned long)
PUBLIC 12a0 0 boost::atomics::detail::lock_pool::unlock(void*)
PUBLIC 1330 0 boost::atomics::detail::lock_pool::allocate_wait_state(void*, void const volatile*)
PUBLIC 1490 0 boost::atomics::detail::lock_pool::free_wait_state(void*, void*)
PUBLIC 1590 0 boost::atomics::detail::lock_pool::wait(void*, void*)
PUBLIC 1840 0 boost::atomics::detail::lock_pool::notify_one(void*, void const volatile*)
PUBLIC 18f0 0 boost::atomics::detail::lock_pool::notify_all(void*, void const volatile*)
PUBLIC 19a0 0 boost::atomics::detail::lock_pool::thread_fence()
PUBLIC 19b0 0 boost::atomics::detail::lock_pool::signal_fence()
PUBLIC 19c0 0 atexit
PUBLIC 19d0 0 _fini
STACK CFI INIT cd4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d04 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d40 50 .cfa: sp 0 + .ra: x30
STACK CFI d50 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d58 x19: .cfa -16 + ^
STACK CFI d88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT da0 140 .cfa: sp 0 + .ra: x30
STACK CFI da4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dbc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI e08 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e80 x23: x23 x24: x24
STACK CFI e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI ec0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ed8 x23: x23 x24: x24
STACK CFI edc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT ee0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI ee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI eec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ef8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI f00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fd0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10a0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 10e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10f4 x19: .cfa -16 + ^
STACK CFI 1130 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1134 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11c0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 11c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11d4 x19: .cfa -16 + ^
STACK CFI 1210 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1214 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 12a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 12a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 132c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1330 154 .cfa: sp 0 + .ra: x30
STACK CFI 1334 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 133c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1348 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 13d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13f8 x23: x23 x24: x24
STACK CFI 141c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1450 x23: x23 x24: x24
STACK CFI 1474 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 1490 100 .cfa: sp 0 + .ra: x30
STACK CFI 14ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1524 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1530 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 157c x21: x21 x22: x22
STACK CFI 1580 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1588 x21: x21 x22: x22
STACK CFI INIT 1590 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 1594 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 159c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1640 x21: x21 x22: x22
STACK CFI 1650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1654 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 16cc x21: x21 x22: x22
STACK CFI 17b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17f0 x21: x21 x22: x22
STACK CFI 1830 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1838 x21: x21 x22: x22
STACK CFI INIT 1840 ac .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 184c x19: .cfa -16 + ^
STACK CFI 18e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 18f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18fc x19: .cfa -16 + ^
STACK CFI 1998 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 19a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c0 10 .cfa: sp 0 + .ra: x30
