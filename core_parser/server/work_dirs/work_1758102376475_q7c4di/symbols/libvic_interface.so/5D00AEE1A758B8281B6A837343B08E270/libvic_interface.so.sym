MODULE Linux arm64 5D00AEE1A758B8281B6A837343B08E270 libvic_interface.so
INFO CODE_ID E1AE005D58A728B81B6A837343B08E27
PUBLIC 19a8 0 _init
PUBLIC 1c60 0 call_weak_fn
PUBLIC 1c74 0 deregister_tm_clones
PUBLIC 1ca4 0 register_tm_clones
PUBLIC 1ce0 0 __do_global_dtors_aux
PUBLIC 1d30 0 frame_dummy
PUBLIC 1d40 0 lios::vic::VicNvMedia::VicNvMedia()
PUBLIC 1f40 0 lios::vic::VicNvMedia::~VicNvMedia()
PUBLIC 1f80 0 lios::vic::VicNvMedia::transform_surface(NvSciBufObjRefRec*, NvSciBufObjRefRec*, NvMediaRect const*, NvMediaRect const*)
PUBLIC 21e0 0 lios::vic::VicNvMedia::register_bufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2270 0 lios::vic::VicNvMedia::GetVicBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 2334 0 _fini
STACK CFI INIT 1c74 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ca4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ce0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1cf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cf8 x19: .cfa -16 + ^
STACK CFI 1d28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d40 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 1d44 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 1d4c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 1d5c x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ecc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 1f40 3c .cfa: sp 0 + .ra: x30
STACK CFI 1f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f4c x19: .cfa -16 + ^
STACK CFI 1f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f80 254 .cfa: sp 0 + .ra: x30
STACK CFI 1f84 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1f8c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1f9c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1fa8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1fc4 x25: .cfa -80 + ^
STACK CFI 2098 x25: x25
STACK CFI 20ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 20b0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 20d4 x25: x25
STACK CFI 20f8 x25: .cfa -80 + ^
STACK CFI 211c x25: x25
STACK CFI 2120 x25: .cfa -80 + ^
STACK CFI 2144 x25: x25
STACK CFI 2148 x25: .cfa -80 + ^
STACK CFI 216c x25: x25
STACK CFI 2170 x25: .cfa -80 + ^
STACK CFI 2194 x25: x25
STACK CFI 2198 x25: .cfa -80 + ^
STACK CFI 21bc x25: x25
STACK CFI 21c0 x25: .cfa -80 + ^
STACK CFI INIT 21e0 88 .cfa: sp 0 + .ra: x30
STACK CFI 21e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21f4 x21: .cfa -16 + ^
STACK CFI 2250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2254 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2270 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2274 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 227c x19: .cfa -128 + ^
STACK CFI 230c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2310 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
