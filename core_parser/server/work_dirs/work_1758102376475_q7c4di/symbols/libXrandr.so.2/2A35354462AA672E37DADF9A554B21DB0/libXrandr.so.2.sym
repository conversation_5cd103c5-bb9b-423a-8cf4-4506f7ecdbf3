MODULE Linux arm64 2A35354462AA672E37DADF9A554B21DB0 libXrandr.so.2
INFO CODE_ID 4435352AAA622E6737DADF9A554B21DBD34F9F66
PUBLIC 24d8 0 XRRRootToScreen
PUBLIC 2520 0 XRRQueryExtension
PUBLIC 25a0 0 XRRQueryVersion
PUBLIC 27d8 0 XRRSelectInput
PUBLIC 28b0 0 XRRUpdateConfiguration
PUBLIC 2e98 0 XRRConfigRotations
PUBLIC 2ea8 0 XRRConfigSizes
PUBLIC 2eb8 0 XRRConfigRates
PUBLIC 2f10 0 XRRConfigTimes
PUBLIC 2f20 0 XRRConfigCurrentConfiguration
PUBLIC 2f30 0 XRRConfigCurrentRate
PUBLIC 2f38 0 XRRRotations
PUBLIC 2ff0 0 XRRSizes
PUBLIC 30a0 0 XRRRates
PUBLIC 3168 0 XRRTimes
PUBLIC 3218 0 XRRGetScreenInfo
PUBLIC 32a8 0 XRRFreeScreenConfigInfo
PUBLIC 32b0 0 XRRSetScreenConfigAndRate
PUBLIC 3498 0 XRRSetScreenConfig
PUBLIC 34a8 0 XRRGetCrtcInfo
PUBLIC 3720 0 XRRFreeCrtcInfo
PUBLIC 3728 0 XRRSetCrtcConfig
PUBLIC 38a8 0 XRRGetCrtcGammaSize
PUBLIC 39b0 0 XRRAllocGamma
PUBLIC 3a00 0 XRRGetCrtcGamma
PUBLIC 3bb8 0 XRRSetCrtcGamma
PUBLIC 3d00 0 XRRFreeGamma
PUBLIC 3d08 0 XRRSetCrtcTransform
PUBLIC 3eb8 0 XRRGetCrtcTransform
PUBLIC 4318 0 XRRGetPanning
PUBLIC 44d8 0 XRRFreePanning
PUBLIC 44e0 0 XRRSetPanning
PUBLIC 4660 0 XRRAllocModeInfo
PUBLIC 46d8 0 XRRCreateMode
PUBLIC 48e0 0 XRRDestroyMode
PUBLIC 49a8 0 XRRAddOutputMode
PUBLIC 4a78 0 XRRDeleteOutputMode
PUBLIC 4b48 0 XRRFreeModeInfo
PUBLIC 4b50 0 XRRGetOutputInfo
PUBLIC 4e78 0 XRRFreeOutputInfo
PUBLIC 4e80 0 XRRSetOutputPrimary
PUBLIC 4fa0 0 XRRGetOutputPrimary
PUBLIC 50e0 0 XRRListOutputProperties
PUBLIC 5278 0 XRRQueryOutputProperty
PUBLIC 5428 0 XRRConfigureOutputProperty
PUBLIC 55d0 0 XRRChangeOutputProperty
PUBLIC 5928 0 XRRDeleteOutputProperty
PUBLIC 59f8 0 XRRGetOutputProperty
PUBLIC 62e8 0 XRRGetScreenResources
PUBLIC 62f0 0 XRRGetScreenResourcesCurrent
PUBLIC 62f8 0 XRRFreeScreenResources
PUBLIC 6300 0 XRRGetScreenSizeRange
PUBLIC 6470 0 XRRSetScreenSize
PUBLIC 6578 0 XRRGetProviderResources
PUBLIC 6790 0 XRRFreeProviderResources
PUBLIC 6798 0 XRRGetProviderInfo
PUBLIC 6a68 0 XRRFreeProviderInfo
PUBLIC 6a70 0 XRRSetProviderOutputSource
PUBLIC 6b38 0 XRRSetProviderOffloadSink
PUBLIC 6c00 0 XRRListProviderProperties
PUBLIC 6d98 0 XRRQueryProviderProperty
PUBLIC 6f48 0 XRRConfigureProviderProperty
PUBLIC 70f0 0 XRRChangeProviderProperty
PUBLIC 7448 0 XRRDeleteProviderProperty
PUBLIC 7518 0 XRRGetProviderProperty
PUBLIC 7828 0 XRRGetMonitors
PUBLIC 7b98 0 XRRSetMonitor
PUBLIC 7cd8 0 XRRDeleteMonitor
PUBLIC 7da8 0 XRRAllocateMonitor
PUBLIC 7de8 0 XRRFreeMonitors
STACK CFI INIT 1d28 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d58 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d98 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1da4 x19: .cfa -16 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1de0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1df8 x21: .cfa -16 + ^
STACK CFI 1e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1ec8 23c .cfa: sp 0 + .ra: x30
STACK CFI 1ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ed4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ee0 x21: .cfa -16 + ^
STACK CFI 1f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2024 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 20b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 20b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2108 308 .cfa: sp 0 + .ra: x30
STACK CFI 210c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2114 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 211c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2168 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2274 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 235c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2360 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2410 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 241c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 244c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2490 x21: x21 x22: x22
STACK CFI 24b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 24cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 24d0 x21: x21 x22: x22
STACK CFI INIT 24d8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 60 .cfa: sp 0 + .ra: x30
STACK CFI 2524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 252c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 256c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 257c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2580 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25a0 190 .cfa: sp 0 + .ra: x30
STACK CFI 25a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 25ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 25bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 25c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2634 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2730 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2734 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 273c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 279c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 27d8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 27dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 28ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 28b0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 28bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2914 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2964 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 298c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 29a4 x23: .cfa -16 + ^
STACK CFI 2a2c x23: x23
STACK CFI 2a30 x23: .cfa -16 + ^
STACK CFI 2a70 x23: x23
STACK CFI INIT 2a78 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 2a7c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2a84 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2a90 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2aa4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2b04 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2b30 x25: x25 x26: x26
STACK CFI 2b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b64 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 2b8c x27: .cfa -112 + ^
STACK CFI 2c6c x25: x25 x26: x26
STACK CFI 2c70 x27: x27
STACK CFI 2d4c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2dc4 x25: x25 x26: x26
STACK CFI 2dcc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2dd8 x25: x25 x26: x26
STACK CFI 2de8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2dec x27: .cfa -112 + ^
STACK CFI 2df8 x27: x27
STACK CFI 2dfc x27: .cfa -112 + ^
STACK CFI 2e10 x25: x25 x26: x26
STACK CFI 2e14 x27: x27
STACK CFI INIT 2e18 80 .cfa: sp 0 + .ra: x30
STACK CFI 2e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e98 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ea8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb8 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f20 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f38 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f50 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2fb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ff0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2ff4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ffc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3004 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3074 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 309c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 30a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 30a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 30ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30c0 x23: .cfa -16 + ^
STACK CFI 3130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3134 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3168 ac .cfa: sp 0 + .ra: x30
STACK CFI 316c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3174 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 317c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 31e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3218 8c .cfa: sp 0 + .ra: x30
STACK CFI 321c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3224 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 322c x21: .cfa -16 + ^
STACK CFI 32a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 32a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32b0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 32b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 32bc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 32cc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 32d4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 32e0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 32ec x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 33f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 33f8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3498 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 34a8 274 .cfa: sp 0 + .ra: x30
STACK CFI 34ac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 34b4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 34c4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 34cc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 355c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 35a0 x25: x25 x26: x26
STACK CFI 35a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3678 x25: x25 x26: x26
STACK CFI 36a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 36a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 36a8 x25: x25 x26: x26
STACK CFI 36f0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 36f4 x25: x25 x26: x26
STACK CFI 3704 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 3720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3728 180 .cfa: sp 0 + .ra: x30
STACK CFI 372c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3734 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3740 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3748 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3758 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3760 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 387c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3880 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 38a8 108 .cfa: sp 0 + .ra: x30
STACK CFI 38ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 38b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 38c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 398c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 39b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 39b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39c8 x19: .cfa -16 + ^
STACK CFI 39fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a00 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 3a04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3a0c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3a1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3aa0 x23: .cfa -64 + ^
STACK CFI 3ac4 x23: x23
STACK CFI 3b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 3b78 x23: x23
STACK CFI 3b98 x23: .cfa -64 + ^
STACK CFI 3bac x23: x23
STACK CFI 3bb0 x23: .cfa -64 + ^
STACK CFI INIT 3bb8 144 .cfa: sp 0 + .ra: x30
STACK CFI 3bbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3bc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3bd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3cec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3d00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d08 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3d0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3d14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3d20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3d28 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3d34 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3e60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3e9c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 3eb8 45c .cfa: sp 0 + .ra: x30
STACK CFI 3ebc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3ec4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3ed4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3edc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3f14 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 40fc x25: x25 x26: x26
STACK CFI 4100 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4210 x25: x25 x26: x26
STACK CFI 4238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 423c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 4254 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 42ac x25: x25 x26: x26
STACK CFI 42b0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 42f4 x25: x25 x26: x26
STACK CFI 42f8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4310 x25: x25 x26: x26
STACK CFI INIT 4318 1bc .cfa: sp 0 + .ra: x30
STACK CFI 431c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4324 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4334 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 445c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 44d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44e0 180 .cfa: sp 0 + .ra: x30
STACK CFI 44e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 44ec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 44fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4504 x23: .cfa -64 + ^
STACK CFI 4634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4638 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4660 74 .cfa: sp 0 + .ra: x30
STACK CFI 4664 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 466c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 467c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 46d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 46d8 208 .cfa: sp 0 + .ra: x30
STACK CFI 46dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 46e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 46f4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 46fc x23: .cfa -64 + ^
STACK CFI 485c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4860 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 48e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 48e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48f4 x21: .cfa -16 + ^
STACK CFI 4978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 497c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4998 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 49a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 49a8 cc .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 49b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4a78 cc .cfa: sp 0 + .ra: x30
STACK CFI 4a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4b48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b50 324 .cfa: sp 0 + .ra: x30
STACK CFI 4b54 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4b5c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4b6c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4b74 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4c04 x27: .cfa -64 + ^
STACK CFI 4c1c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4d44 x25: x25 x26: x26
STACK CFI 4d48 x27: x27
STACK CFI 4d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d74 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI 4d78 x25: x25 x26: x26
STACK CFI 4d7c x27: x27
STACK CFI 4dc4 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 4df0 x25: x25 x26: x26
STACK CFI 4df4 x27: x27
STACK CFI 4e04 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4e08 x27: .cfa -64 + ^
STACK CFI 4e0c x25: x25 x26: x26
STACK CFI 4e4c x27: x27
STACK CFI 4e50 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 4e64 x25: x25 x26: x26
STACK CFI INIT 4e78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4e80 11c .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4e8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4e9c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ea4 x23: .cfa -32 + ^
STACK CFI 4f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4f70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4fa0 13c .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4fac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4fbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 50a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 50ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 50e0 194 .cfa: sp 0 + .ra: x30
STACK CFI 50e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 50ec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 50fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5104 x23: .cfa -64 + ^
STACK CFI 51e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 51e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5278 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 527c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5284 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5294 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 529c x23: .cfa -64 + ^
STACK CFI 53a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 53ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5428 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 542c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5434 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5440 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5448 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5454 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5460 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 555c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 55d0 358 .cfa: sp 0 + .ra: x30
STACK CFI 55d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 55dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 55ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 55f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5600 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 560c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5714 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5928 cc .cfa: sp 0 + .ra: x30
STACK CFI 592c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5934 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 593c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 59c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 59dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 59f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 59f8 310 .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5a04 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5a10 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5a1c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5a28 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5bac .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 5d08 5e0 .cfa: sp 0 + .ra: x30
STACK CFI 5d0c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 5d1c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 5d24 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 5d3c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5d54 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 5eac x25: x25 x26: x26
STACK CFI 5eb0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 5f20 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6150 x25: x25 x26: x26
STACK CFI 6158 x27: x27 x28: x28
STACK CFI 6184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6188 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 61e4 x25: x25 x26: x26
STACK CFI 61e8 x27: x27 x28: x28
STACK CFI 61ec x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 627c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6280 x27: x27 x28: x28
STACK CFI 6288 x25: x25 x26: x26
STACK CFI 62a4 x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 62a8 x25: x25 x26: x26
STACK CFI 62ac x27: x27 x28: x28
STACK CFI 62b0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 62c8 x25: x25 x26: x26
STACK CFI 62cc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 62d0 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 62e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 62f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 62f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6300 16c .cfa: sp 0 + .ra: x30
STACK CFI 6304 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 630c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 631c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6324 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6330 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 642c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6470 104 .cfa: sp 0 + .ra: x30
STACK CFI 6474 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 647c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6484 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6490 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 649c x25: .cfa -16 + ^
STACK CFI 6534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6538 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 655c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 6578 214 .cfa: sp 0 + .ra: x30
STACK CFI 657c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6584 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6594 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 661c x25: .cfa -64 + ^
STACK CFI 6660 x25: x25
STACK CFI 6664 x25: .cfa -64 + ^
STACK CFI 6668 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 66dc x23: x23 x24: x24
STACK CFI 66e0 x25: x25
STACK CFI 6704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6708 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 670c x23: x23 x24: x24
STACK CFI 6710 x25: x25
STACK CFI 6758 x25: .cfa -64 + ^
STACK CFI 675c x25: x25
STACK CFI 676c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6770 x25: .cfa -64 + ^
STACK CFI 6778 x23: x23 x24: x24
STACK CFI 677c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 6790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6798 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 679c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 67a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 67b4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 67bc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6854 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6868 x27: .cfa -64 + ^
STACK CFI 6980 x25: x25 x26: x26
STACK CFI 6984 x27: x27
STACK CFI 69ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 69b0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI 69b4 x25: x25 x26: x26
STACK CFI 69b8 x27: x27
STACK CFI 6a0c x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 6a38 x25: x25 x26: x26
STACK CFI 6a3c x27: x27
STACK CFI 6a4c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6a50 x27: .cfa -64 + ^
STACK CFI INIT 6a68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a70 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a84 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6b38 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6b3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6bdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6c00 194 .cfa: sp 0 + .ra: x30
STACK CFI 6c04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c0c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6c1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6c24 x23: .cfa -64 + ^
STACK CFI 6d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6d98 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 6d9c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6da4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6db4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6dbc x23: .cfa -64 + ^
STACK CFI 6ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ecc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6f48 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 6f4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6f54 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6f60 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6f68 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6f74 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6f80 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 707c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 70f0 358 .cfa: sp 0 + .ra: x30
STACK CFI 70f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 70fc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 710c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7114 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7120 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 712c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7234 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7448 cc .cfa: sp 0 + .ra: x30
STACK CFI 744c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7454 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 745c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 74e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 74e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 74fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7518 310 .cfa: sp 0 + .ra: x30
STACK CFI 751c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7524 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 7530 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 753c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7548 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 76c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 76cc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7828 36c .cfa: sp 0 + .ra: x30
STACK CFI 782c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7834 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 7844 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 784c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7864 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7928 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7970 x27: x27 x28: x28
STACK CFI 799c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 79a0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 7af0 x27: x27 x28: x28
STACK CFI 7b44 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7b48 x27: x27 x28: x28
STACK CFI 7b54 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 7b98 140 .cfa: sp 0 + .ra: x30
STACK CFI 7b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ba4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7cac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7cc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7cd8 cc .cfa: sp 0 + .ra: x30
STACK CFI 7cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ce4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7cec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7d78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7d94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7da8 40 .cfa: sp 0 + .ra: x30
STACK CFI 7dac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7dbc x19: .cfa -16 + ^
STACK CFI 7de4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7de8 4 .cfa: sp 0 + .ra: x30
