MODULE Linux arm64 68A33B08FFEC5E389F2D951970FA8D270 libboost_context.so.1.77.0
INFO CODE_ID 083BA368ECFF385E9F2D951970FA8D27
PUBLIC 840 0 _init
PUBLIC 8f0 0 call_weak_fn
PUBLIC 904 0 deregister_tm_clones
PUBLIC 934 0 register_tm_clones
PUBLIC 970 0 __do_global_dtors_aux
PUBLIC 9c0 0 frame_dummy
PUBLIC 9c4 0 make_fcontext
PUBLIC 9e4 0 jump_fcontext
PUBLIC a54 0 ontop_fcontext
PUBLIC ac0 0 boost::context::stack_traits::is_unbounded()
PUBLIC b40 0 boost::context::stack_traits::page_size()
PUBLIC bc0 0 boost::context::stack_traits::default_size()
PUBLIC bd0 0 boost::context::stack_traits::minimum_size()
PUBLIC be0 0 boost::context::stack_traits::maximum_size()
PUBLIC c4c 0 _fini
STACK CFI INIT 904 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 934 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 970 50 .cfa: sp 0 + .ra: x30
STACK CFI 980 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 988 x19: .cfa -16 + ^
STACK CFI 9b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac0 7c .cfa: sp 0 + .ra: x30
STACK CFI ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI acc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b40 78 .cfa: sp 0 + .ra: x30
STACK CFI b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b50 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT be0 6c .cfa: sp 0 + .ra: x30
STACK CFI be4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
