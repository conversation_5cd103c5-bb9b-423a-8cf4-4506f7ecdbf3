MODULE Linux arm64 F42CDE3D3F4BDCAEE4C0179E6BE141540 libebl_ppc.so
INFO CODE_ID 3DDE2CF44B3FAEDCE4C0179E6BE141549D7FAE47
PUBLIC 17c0 0 ppc_init
STACK CFI INIT 15d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1608 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1648 48 .cfa: sp 0 + .ra: x30
STACK CFI 164c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1654 x19: .cfa -16 + ^
STACK CFI 168c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1698 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16c8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1710 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1720 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1724 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 172c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1798 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 17c0 124 .cfa: sp 0 + .ra: x30
STACK CFI 17cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1800 x19: .cfa -16 + ^
STACK CFI 18d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18e8 188 .cfa: sp 0 + .ra: x30
STACK CFI 18ec .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 18f4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 1908 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 1928 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1968 x23: x23 x24: x24
STACK CFI 1970 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 19bc x23: x23 x24: x24
STACK CFI 19e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19e8 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI 1a00 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1a4c x25: x25 x26: x26
STACK CFI 1a50 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1a58 x23: x23 x24: x24
STACK CFI 1a5c x25: x25 x26: x26
STACK CFI 1a68 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1a6c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 1a70 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1aa0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ae8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b00 194 .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1b0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1b18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1b3c x23: .cfa -32 + ^
STACK CFI 1b74 x23: x23
STACK CFI 1b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ba0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 1bf4 x23: x23
STACK CFI 1bfc x23: .cfa -32 + ^
STACK CFI 1c44 x23: x23
STACK CFI 1c48 x23: .cfa -32 + ^
STACK CFI 1c80 x23: x23
STACK CFI 1c90 x23: .cfa -32 + ^
STACK CFI INIT 1c98 50 .cfa: sp 0 + .ra: x30
STACK CFI 1c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ca4 x19: .cfa -32 + ^
STACK CFI 1ce0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ce8 280 .cfa: sp 0 + .ra: x30
STACK CFI 1cec .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1cf4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1d08 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1d1c x23: .cfa -96 + ^
STACK CFI 1dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1dd4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1f68 53c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24a8 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 24ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 24b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 24c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 24d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2564 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 25ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 25b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2790 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c0 108 .cfa: sp 0 + .ra: x30
STACK CFI 27c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2824 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 28c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 28c8 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2900 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2938 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2980 8 .cfa: sp 0 + .ra: x30
