MODULE Linux arm64 A9A1D92B1C8E021CBD10843BEA7A36D60 libmulti_scale_deformable_attn.so
INFO CODE_ID 2BD9A1A98E1C1C02BD10843BEA7A36D6
PUBLIC 7cb8 0 _init
PUBLIC 8100 0 _GLOBAL__sub_I_multiscaleDeformableAttnPlugin.cpp
PUBLIC 8180 0 _GLOBAL__sub_I_checkMacrosPlugin.cpp
PUBLIC 8520 0 call_weak_fn
PUBLIC 8534 0 deregister_tm_clones
PUBLIC 8564 0 register_tm_clones
PUBLIC 85a0 0 __do_global_dtors_aux
PUBLIC 85f0 0 frame_dummy
PUBLIC 8600 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 8630 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 8640 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 8650 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::detachFromContext()
PUBLIC 8660 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 8670 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::getPluginName() const
PUBLIC 8680 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::getPluginVersion() const
PUBLIC 8690 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getNbOutputs() const
PUBLIC 86a0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::initialize()
PUBLIC 86b0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getSerializationSize() const
PUBLIC 86c0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::serialize(void*) const
PUBLIC 86d0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::destroy()
PUBLIC 86e0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::getPluginNamespace() const [clone .localalias]
PUBLIC 86f0 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::getFieldNames()
PUBLIC 8700 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::getPluginNamespace() const [clone .localalias]
PUBLIC 8710 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 87e0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 89e0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 8ab0 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::setPluginNamespace(char const*)
PUBLIC 8af0 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::setPluginNamespace(char const*)
PUBLIC 8b30 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::MultiscaleDeformableAttnPlugin()
PUBLIC 8b50 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::clone() const
PUBLIC 8c00 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 8c80 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::MultiscaleDeformableAttnPlugin(void const*, unsigned long)
PUBLIC 8ca0 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 8d60 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::MultiscaleDeformableAttnPluginCreator()
PUBLIC 8d90 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 8da0 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 8db0 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 8dc0 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 8dd0 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 8de0 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 8df0 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 8e10 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 8e20 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 8e30 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 8e40 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 8e50 0 std::ctype<char>::do_widen(char) const
PUBLIC 8e60 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::~MultiscaleDeformableAttnPlugin()
PUBLIC 8e90 0 nvinfer1::plugin::MultiscaleDeformableAttnPlugin::~MultiscaleDeformableAttnPlugin()
PUBLIC 8ee0 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::~MultiscaleDeformableAttnPluginCreator()
PUBLIC 8f40 0 nvinfer1::PluginRegistrar<nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator>::~PluginRegistrar()
PUBLIC 8fa0 0 nvinfer1::plugin::MultiscaleDeformableAttnPluginCreator::~MultiscaleDeformableAttnPluginCreator()
PUBLIC 9000 0 nvinfer1::plugin::caughtError(std::exception const&)
PUBLIC 9170 0 nvinfer1::plugin::TRTException::log(std::ostream&) const
PUBLIC 9390 0 nvinfer1::plugin::throwCudaError(char const*, char const*, int, int, char const*)
PUBLIC 9440 0 nvinfer1::plugin::throwCublasError(char const*, char const*, int, int, char const*)
PUBLIC 95d0 0 nvinfer1::plugin::throwCudnnError(char const*, char const*, int, int, char const*)
PUBLIC 9680 0 nvinfer1::plugin::logError(char const*, char const*, char const*, int)
PUBLIC 9a20 0 nvinfer1::plugin::reportValidationFailure(char const*, char const*, int)
PUBLIC 9e10 0 nvinfer1::plugin::throwPluginError(char const*, char const*, int, int, char const*)
PUBLIC 9ec0 0 nvinfer1::plugin::reportAssertion(char const*, char const*, int)
PUBLIC a2e0 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC a300 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC a340 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC a360 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC a3a0 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC a3c0 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC a400 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC a420 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC a460 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC a480 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC a4c0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC a570 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC a610 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC a6c0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC a750 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC a800 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC a8a0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC a950 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC a9f0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC aaa0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC ab30 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC abe0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC ac80 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC ad30 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC adc0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC ae70 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC af00 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC af60 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC afc0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b020 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC b080 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC b0e0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b140 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC b1a0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC b200 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::sync()
PUBLIC b3b0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::sync()
PUBLIC b560 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::sync()
PUBLIC b710 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::sync()
PUBLIC b8c0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC b920 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC b980 0 _fini
STACK CFI INIT 8534 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8564 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 85a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 85b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85b8 x19: .cfa -16 + ^
STACK CFI 85e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 85f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8da0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8db0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8dc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8de0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8df0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8600 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8650 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8670 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8680 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8710 cc .cfa: sp 0 + .ra: x30
STACK CFI 8714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8720 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 872c x21: .cfa -16 + ^
STACK CFI 87a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 87ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 87c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 87c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 87d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 87e0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 87e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 87f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 87fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 89d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 89e0 cc .cfa: sp 0 + .ra: x30
STACK CFI 89e4 .cfa: sp 64 +
STACK CFI 89ec .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8a24 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8a68 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8aa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8e60 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e90 48 .cfa: sp 0 + .ra: x30
STACK CFI 8e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ea8 x19: .cfa -16 + ^
STACK CFI 8ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ee0 54 .cfa: sp 0 + .ra: x30
STACK CFI 8ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ef8 x19: .cfa -16 + ^
STACK CFI 8f24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8f30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ab0 40 .cfa: sp 0 + .ra: x30
STACK CFI 8ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8abc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8f40 54 .cfa: sp 0 + .ra: x30
STACK CFI 8f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f58 x19: .cfa -16 + ^
STACK CFI 8f84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8f90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8af0 40 .cfa: sp 0 + .ra: x30
STACK CFI 8af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8fa0 54 .cfa: sp 0 + .ra: x30
STACK CFI 8fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fb8 x19: .cfa -16 + ^
STACK CFI 8ff0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9000 168 .cfa: sp 0 + .ra: x30
STACK CFI 9004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9010 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9020 x21: .cfa -16 + ^
STACK CFI 90d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 90d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 911c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b30 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b50 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8b5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8bc0 x21: .cfa -16 + ^
STACK CFI 8bd8 x21: x21
STACK CFI 8bfc x21: .cfa -16 + ^
STACK CFI INIT 8c00 78 .cfa: sp 0 + .ra: x30
STACK CFI 8c04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8c10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8c54 x21: .cfa -16 + ^
STACK CFI 8c6c x21: x21
STACK CFI 8c74 x21: .cfa -16 + ^
STACK CFI INIT 8c80 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ca0 bc .cfa: sp 0 + .ra: x30
STACK CFI 8ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8cb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8d0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8d60 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8100 7c .cfa: sp 0 + .ra: x30
STACK CFI 8104 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 810c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a2e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a300 38 .cfa: sp 0 + .ra: x30
STACK CFI a304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a314 x19: .cfa -16 + ^
STACK CFI a334 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a340 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a360 38 .cfa: sp 0 + .ra: x30
STACK CFI a364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a374 x19: .cfa -16 + ^
STACK CFI a394 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a3a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a3c0 38 .cfa: sp 0 + .ra: x30
STACK CFI a3c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3d4 x19: .cfa -16 + ^
STACK CFI a3f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a400 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a420 38 .cfa: sp 0 + .ra: x30
STACK CFI a424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a434 x19: .cfa -16 + ^
STACK CFI a454 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a460 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a480 38 .cfa: sp 0 + .ra: x30
STACK CFI a484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a494 x19: .cfa -16 + ^
STACK CFI a4b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a4c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI a4c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4e4 x21: .cfa -16 + ^
STACK CFI a56c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a610 a4 .cfa: sp 0 + .ra: x30
STACK CFI a614 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a624 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a634 x21: .cfa -16 + ^
STACK CFI a6b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a750 b0 .cfa: sp 0 + .ra: x30
STACK CFI a754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a774 x21: .cfa -16 + ^
STACK CFI a7fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a8a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI a8a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a8b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a8c4 x21: .cfa -16 + ^
STACK CFI a94c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a9f0 a4 .cfa: sp 0 + .ra: x30
STACK CFI a9f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aa04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aa14 x21: .cfa -16 + ^
STACK CFI aa90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT ab30 b0 .cfa: sp 0 + .ra: x30
STACK CFI ab34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ab54 x21: .cfa -16 + ^
STACK CFI abdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT ac80 a4 .cfa: sp 0 + .ra: x30
STACK CFI ac84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ac94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aca4 x21: .cfa -16 + ^
STACK CFI ad20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT adc0 a4 .cfa: sp 0 + .ra: x30
STACK CFI adc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI add4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ade4 x21: .cfa -16 + ^
STACK CFI ae60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT af00 54 .cfa: sp 0 + .ra: x30
STACK CFI af04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af18 x19: .cfa -16 + ^
STACK CFI af50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af60 54 .cfa: sp 0 + .ra: x30
STACK CFI af64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af78 x19: .cfa -16 + ^
STACK CFI afb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT afc0 54 .cfa: sp 0 + .ra: x30
STACK CFI afc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI afd8 x19: .cfa -16 + ^
STACK CFI b010 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b020 54 .cfa: sp 0 + .ra: x30
STACK CFI b024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b038 x19: .cfa -16 + ^
STACK CFI b070 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a800 98 .cfa: sp 0 + .ra: x30
STACK CFI a804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a818 x19: .cfa -16 + ^
STACK CFI a894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a570 98 .cfa: sp 0 + .ra: x30
STACK CFI a574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a588 x19: .cfa -16 + ^
STACK CFI a604 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT abe0 98 .cfa: sp 0 + .ra: x30
STACK CFI abe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abf8 x19: .cfa -16 + ^
STACK CFI ac74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a950 98 .cfa: sp 0 + .ra: x30
STACK CFI a954 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a968 x19: .cfa -16 + ^
STACK CFI a9e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b080 60 .cfa: sp 0 + .ra: x30
STACK CFI b084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b098 x19: .cfa -16 + ^
STACK CFI b0dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b0e0 60 .cfa: sp 0 + .ra: x30
STACK CFI b0e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b0f8 x19: .cfa -16 + ^
STACK CFI b13c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b140 60 .cfa: sp 0 + .ra: x30
STACK CFI b144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b158 x19: .cfa -16 + ^
STACK CFI b19c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b1a0 60 .cfa: sp 0 + .ra: x30
STACK CFI b1a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b1b8 x19: .cfa -16 + ^
STACK CFI b1fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a6c0 8c .cfa: sp 0 + .ra: x30
STACK CFI a6c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a6d8 x19: .cfa -16 + ^
STACK CFI a748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aaa0 8c .cfa: sp 0 + .ra: x30
STACK CFI aaa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aab8 x19: .cfa -16 + ^
STACK CFI ab28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae70 8c .cfa: sp 0 + .ra: x30
STACK CFI ae74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae88 x19: .cfa -16 + ^
STACK CFI aef8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad30 8c .cfa: sp 0 + .ra: x30
STACK CFI ad34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad48 x19: .cfa -16 + ^
STACK CFI adb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9170 214 .cfa: sp 0 + .ra: x30
STACK CFI 9174 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 917c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9184 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 918c x23: .cfa -16 + ^
STACK CFI 92d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 92dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b200 1a4 .cfa: sp 0 + .ra: x30
STACK CFI b204 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b20c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b224 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b324 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT b3b0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI b3b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b3bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b3d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b4d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b4d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT b560 1a4 .cfa: sp 0 + .ra: x30
STACK CFI b564 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b56c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b584 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b684 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT b710 1a4 .cfa: sp 0 + .ra: x30
STACK CFI b714 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b71c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b734 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b834 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9390 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9394 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 93a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9440 184 .cfa: sp 0 + .ra: x30
STACK CFI 9444 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9454 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 95d0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 95d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 95e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9680 394 .cfa: sp 0 + .ra: x30
STACK CFI 9684 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 968c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 969c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 96a4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9924 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 996c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT b8c0 54 .cfa: sp 0 + .ra: x30
STACK CFI b8c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b8d8 x19: .cfa -16 + ^
STACK CFI b910 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b920 60 .cfa: sp 0 + .ra: x30
STACK CFI b924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b938 x19: .cfa -16 + ^
STACK CFI b97c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9a20 3ec .cfa: sp 0 + .ra: x30
STACK CFI 9a24 .cfa: sp 528 +
STACK CFI 9a28 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 9a30 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 9a38 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 9a44 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 9a50 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 9cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9ccc .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT 9e10 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9e14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9e24 x19: .cfa -64 + ^
STACK CFI INIT 9ec0 414 .cfa: sp 0 + .ra: x30
STACK CFI 9ec4 .cfa: sp 512 +
STACK CFI 9ec8 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 9ed0 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 9ee0 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 9eec x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 9ef4 x25: .cfa -448 + ^
STACK CFI INIT 8180 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 8184 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 818c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8198 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 84bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 84c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
