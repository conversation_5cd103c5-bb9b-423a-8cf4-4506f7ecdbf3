MODULE Linux arm64 57D5291EC73AE4BC076C2582B4FA35FE0 libgpiod.so.2
INFO CODE_ID 1E29D5573AC7BCE4076C2582B4FA35FE8E77B2D0
PUBLIC 2238 0 gpiod_chip_open
PUBLIC 2510 0 gpiod_chip_name
PUBLIC 2518 0 gpiod_chip_label
PUBLIC 2520 0 gpiod_chip_num_lines
PUBLIC 2528 0 gpiod_line_get_chip
PUBLIC 25c8 0 gpiod_line_offset
PUBLIC 25d0 0 gpiod_line_name
PUBLIC 25e0 0 gpiod_line_consumer
PUBLIC 25f0 0 gpiod_line_direction
PUBLIC 25f8 0 gpiod_line_active_state
PUBLIC 2600 0 gpiod_line_is_used
PUBLIC 2608 0 gpiod_line_is_open_drain
PUBLIC 2610 0 gpiod_line_is_open_source
PUBLIC 2618 0 gpiod_line_needs_update
PUBLIC 2628 0 gpiod_line_update
PUBLIC 2718 0 gpiod_chip_get_line
PUBLIC 2a58 0 gpiod_line_release_bulk
PUBLIC 2b08 0 gpiod_line_release
PUBLIC 2b68 0 gpiod_chip_close
PUBLIC 2be0 0 gpiod_line_is_requested
PUBLIC 2c88 0 gpiod_line_is_free
PUBLIC 2c98 0 gpiod_line_request_bulk
PUBLIC 2f38 0 gpiod_line_request
PUBLIC 2fa0 0 gpiod_line_get_value_bulk
PUBLIC 3088 0 gpiod_line_get_value
PUBLIC 3100 0 gpiod_line_set_value_bulk
PUBLIC 31e8 0 gpiod_line_set_value
PUBLIC 3250 0 gpiod_line_event_wait_bulk
PUBLIC 32b8 0 gpiod_line_event_wait
PUBLIC 3320 0 gpiod_line_event_get_fd
PUBLIC 3360 0 gpiod_line_event_read_fd
PUBLIC 3430 0 gpiod_line_event_read
PUBLIC 3590 0 gpiod_ctxless_get_value_multiple
PUBLIC 36f0 0 gpiod_ctxless_get_value
PUBLIC 3758 0 gpiod_ctxless_set_value_multiple
PUBLIC 38c0 0 gpiod_ctxless_set_value
PUBLIC 38f8 0 gpiod_ctxless_event_monitor_multiple
PUBLIC 3c48 0 gpiod_ctxless_event_loop_multiple
PUBLIC 3c90 0 gpiod_ctxless_event_monitor
PUBLIC 3cd8 0 gpiod_ctxless_event_loop
PUBLIC 3d20 0 gpiod_ctxless_find_line
PUBLIC 3dc0 0 gpiod_chip_open_by_name
PUBLIC 3e40 0 gpiod_chip_open_by_number
PUBLIC 3ec0 0 gpiod_chip_open_by_label
PUBLIC 3f70 0 gpiod_chip_open_lookup
PUBLIC 4018 0 gpiod_chip_get_lines
PUBLIC 40a8 0 gpiod_chip_get_all_lines
PUBLIC 4118 0 gpiod_chip_find_line
PUBLIC 41d8 0 gpiod_chip_find_lines
PUBLIC 4258 0 gpiod_line_request_input
PUBLIC 42b8 0 gpiod_line_request_output
PUBLIC 4310 0 gpiod_line_request_input_flags
PUBLIC 4370 0 gpiod_line_request_output_flags
PUBLIC 43d0 0 gpiod_line_request_rising_edge_events
PUBLIC 4428 0 gpiod_line_request_falling_edge_events
PUBLIC 4480 0 gpiod_line_request_both_edges_events
PUBLIC 44d8 0 gpiod_line_request_rising_edge_events_flags
PUBLIC 4538 0 gpiod_line_request_falling_edge_events_flags
PUBLIC 4598 0 gpiod_line_request_both_edges_events_flags
PUBLIC 45f8 0 gpiod_line_request_bulk_input
PUBLIC 4658 0 gpiod_line_request_bulk_output
PUBLIC 46b0 0 gpiod_line_request_bulk_rising_edge_events
PUBLIC 4708 0 gpiod_line_request_bulk_falling_edge_events
PUBLIC 4760 0 gpiod_line_request_bulk_both_edges_events
PUBLIC 47b8 0 gpiod_line_request_bulk_input_flags
PUBLIC 4818 0 gpiod_line_request_bulk_output_flags
PUBLIC 4878 0 gpiod_line_request_bulk_rising_edge_events_flags
PUBLIC 48d8 0 gpiod_line_request_bulk_falling_edge_events_flags
PUBLIC 4938 0 gpiod_line_request_bulk_both_edges_events_flags
PUBLIC 4998 0 gpiod_line_get
PUBLIC 49f8 0 gpiod_line_find
PUBLIC 4aa8 0 gpiod_line_close_chip
PUBLIC 4b38 0 gpiod_chip_iter_new
PUBLIC 4c88 0 gpiod_chip_iter_free_noclose
PUBLIC 4ce8 0 gpiod_chip_iter_free
PUBLIC 4d40 0 gpiod_chip_iter_next_noclose
PUBLIC 4d68 0 gpiod_chip_iter_next
PUBLIC 4dc0 0 gpiod_line_iter_new
PUBLIC 4e90 0 gpiod_line_iter_free
PUBLIC 4eb8 0 gpiod_line_iter_next
PUBLIC 4ee0 0 gpiod_version_string
STACK CFI INIT 2018 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2048 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2088 48 .cfa: sp 0 + .ra: x30
STACK CFI 208c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2094 x19: .cfa -16 + ^
STACK CFI 20cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 20d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d8 160 .cfa: sp 0 + .ra: x30
STACK CFI 20dc .cfa: sp 592 +
STACK CFI 20e0 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 20e8 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 20f4 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 2104 x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 2210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2214 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x29: .cfa -592 + ^
STACK CFI INIT 2238 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 223c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 224c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 225c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 2278 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 22bc x23: x23 x24: x24
STACK CFI 22e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22e8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 23b8 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2484 x23: x23 x24: x24
STACK CFI 2488 x25: x25 x26: x26
STACK CFI 248c x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 249c x23: x23 x24: x24
STACK CFI 24a0 x25: x25 x26: x26
STACK CFI 24a4 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 24bc x23: x23 x24: x24
STACK CFI 24c0 x25: x25 x26: x26
STACK CFI 24c4 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 24d8 x25: x25 x26: x26
STACK CFI 24dc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2500 x25: x25 x26: x26
STACK CFI 2504 x23: x23 x24: x24
STACK CFI 2508 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 250c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI INIT 2510 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2518 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2528 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2530 98 .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 253c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2554 x21: .cfa -16 + ^
STACK CFI 25a4 x21: x21
STACK CFI 25b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 25b8 x21: x21
STACK CFI 25c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2608 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2618 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2628 f0 .cfa: sp 0 + .ra: x30
STACK CFI 262c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2640 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 267c x21: .cfa -96 + ^
STACK CFI 2708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 270c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2718 d4 .cfa: sp 0 + .ra: x30
STACK CFI 271c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2724 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2738 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2768 x21: x21 x22: x22
STACK CFI 276c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2770 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 27ac x21: x21 x22: x22
STACK CFI 27b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 27d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 27f0 264 .cfa: sp 0 + .ra: x30
STACK CFI 27f4 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 27fc x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 2804 x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 2814 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 283c x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 29bc x23: x23 x24: x24
STACK CFI 29e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 29e8 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x29: .cfa -464 + ^
STACK CFI 2a1c x23: x23 x24: x24
STACK CFI 2a40 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 2a48 x23: x23 x24: x24
STACK CFI 2a50 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI INIT 2a58 ac .cfa: sp 0 + .ra: x30
STACK CFI 2a5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a6c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2b08 60 .cfa: sp 0 + .ra: x30
STACK CFI 2b0c .cfa: sp 560 +
STACK CFI 2b1c .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 2b24 x19: .cfa -544 + ^
STACK CFI 2b60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b64 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x29: .cfa -560 + ^
STACK CFI INIT 2b68 78 .cfa: sp 0 + .ra: x30
STACK CFI 2b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b78 x21: .cfa -16 + ^
STACK CFI 2b8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2bc0 x19: x19 x20: x20
STACK CFI 2bdc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 2be0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bf8 90 .cfa: sp 0 + .ra: x30
STACK CFI 2bfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c10 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2c88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c98 29c .cfa: sp 0 + .ra: x30
STACK CFI 2c9c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2ca4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2cb0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2cc8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d58 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 2d78 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2d90 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2ea0 x25: x25 x26: x26
STACK CFI 2ea8 x27: x27 x28: x28
STACK CFI 2eac x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2ef0 x25: x25 x26: x26
STACK CFI 2ef4 x27: x27 x28: x28
STACK CFI 2f2c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2f30 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2f38 68 .cfa: sp 0 + .ra: x30
STACK CFI 2f3c .cfa: sp 576 +
STACK CFI 2f4c .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 2f54 x19: .cfa -560 + ^
STACK CFI 2f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f9c .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x29: .cfa -576 + ^
STACK CFI INIT 2fa0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2fa4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2fac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2fbc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3078 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3088 78 .cfa: sp 0 + .ra: x30
STACK CFI 308c .cfa: sp 576 +
STACK CFI 30a0 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 30a8 x19: .cfa -560 + ^
STACK CFI 30f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30f4 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x29: .cfa -576 + ^
STACK CFI INIT 3100 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3104 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 310c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3128 x21: .cfa -96 + ^
STACK CFI 31d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31d8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 31e8 68 .cfa: sp 0 + .ra: x30
STACK CFI 31ec .cfa: sp 576 +
STACK CFI 31fc .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 3204 x19: .cfa -560 + ^
STACK CFI 3248 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 324c .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x29: .cfa -576 + ^
STACK CFI INIT 3250 68 .cfa: sp 0 + .ra: x30
STACK CFI 3254 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 325c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3268 x21: .cfa -16 + ^
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 328c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 32b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 32b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 32bc .cfa: sp 560 +
STACK CFI 32d0 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 32d8 x19: .cfa -544 + ^
STACK CFI 3314 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3318 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x29: .cfa -560 + ^
STACK CFI INIT 3320 3c .cfa: sp 0 + .ra: x30
STACK CFI 333c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3358 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3360 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3370 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 340c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3430 38 .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3464 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3468 128 .cfa: sp 0 + .ra: x30
STACK CFI 346c .cfa: sp 592 +
STACK CFI 3470 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 3478 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 3480 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 348c x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 356c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3570 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x29: .cfa -592 + ^
STACK CFI INIT 3590 160 .cfa: sp 0 + .ra: x30
STACK CFI 3594 .cfa: sp 624 +
STACK CFI 3598 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 35a0 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 35a8 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 35b4 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 35d4 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 35e0 x27: .cfa -544 + ^
STACK CFI 3674 x25: x25 x26: x26
STACK CFI 3678 x27: x27
STACK CFI 36a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 36a8 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x29: .cfa -624 + ^
STACK CFI 36b8 x25: x25 x26: x26
STACK CFI 36bc x27: x27
STACK CFI 36d4 x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^
STACK CFI 36dc x25: x25 x26: x26
STACK CFI 36e0 x27: x27
STACK CFI 36e8 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 36ec x27: .cfa -544 + ^
STACK CFI INIT 36f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 36f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3704 x19: .cfa -48 + ^
STACK CFI 3750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3754 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3758 168 .cfa: sp 0 + .ra: x30
STACK CFI 375c .cfa: sp 624 +
STACK CFI 3760 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 3768 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 3770 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 3790 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 379c x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 37a8 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 3834 x23: x23 x24: x24
STACK CFI 383c x25: x25 x26: x26
STACK CFI 3840 x27: x27 x28: x28
STACK CFI 3864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3868 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 3878 x23: x23 x24: x24
STACK CFI 387c x25: x25 x26: x26
STACK CFI 3880 x27: x27 x28: x28
STACK CFI 389c x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 38a4 x23: x23 x24: x24
STACK CFI 38a8 x25: x25 x26: x26
STACK CFI 38ac x27: x27 x28: x28
STACK CFI 38b4 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 38b8 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 38bc x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI INIT 38c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 38c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 38f8 34c .cfa: sp 0 + .ra: x30
STACK CFI 38fc .cfa: sp 1232 +
STACK CFI 3900 .ra: .cfa -1224 + ^ x29: .cfa -1232 + ^
STACK CFI 3908 x21: .cfa -1200 + ^ x22: .cfa -1192 + ^
STACK CFI 391c x23: .cfa -1184 + ^ x24: .cfa -1176 + ^
STACK CFI 3940 x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 394c x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 396c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^
STACK CFI 3a14 x19: x19 x20: x20
STACK CFI 3a18 x25: x25 x26: x26
STACK CFI 3a1c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 3a30 x19: x19 x20: x20
STACK CFI 3a34 x25: x25 x26: x26
STACK CFI 3a68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3a6c .cfa: sp 1232 + .ra: .cfa -1224 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^ x29: .cfa -1232 + ^
STACK CFI 3c14 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 3c28 x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI 3c30 x19: x19 x20: x20
STACK CFI 3c34 x25: x25 x26: x26
STACK CFI 3c3c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^
STACK CFI 3c40 x25: .cfa -1168 + ^ x26: .cfa -1160 + ^
STACK CFI INIT 3c48 48 .cfa: sp 0 + .ra: x30
STACK CFI 3c4c .cfa: sp 32 +
STACK CFI 3c5c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c90 44 .cfa: sp 0 + .ra: x30
STACK CFI 3c94 .cfa: sp 48 +
STACK CFI 3c98 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cd8 48 .cfa: sp 0 + .ra: x30
STACK CFI 3cdc .cfa: sp 32 +
STACK CFI 3ce8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d20 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d3c x23: .cfa -16 + ^
STACK CFI 3d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3d9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3dc0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3dd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e40 7c .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3eb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3ec0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ed0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f70 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fe0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4008 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4018 90 .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4030 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4040 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 408c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 409c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 40a8 70 .cfa: sp 0 + .ra: x30
STACK CFI 40ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 410c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4110 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4118 bc .cfa: sp 0 + .ra: x30
STACK CFI 411c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4128 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 41b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 41d8 7c .cfa: sp 0 + .ra: x30
STACK CFI 41dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4200 x21: .cfa -16 + ^
STACK CFI 4238 x21: x21
STACK CFI 423c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4240 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4244 x21: x21
STACK CFI 4250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4258 5c .cfa: sp 0 + .ra: x30
STACK CFI 425c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 426c x19: .cfa -48 + ^
STACK CFI 42ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 42b8 58 .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42cc x19: .cfa -48 + ^
STACK CFI 4308 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 430c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4310 60 .cfa: sp 0 + .ra: x30
STACK CFI 4314 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4324 x19: .cfa -48 + ^
STACK CFI 4368 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 436c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4370 60 .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4384 x19: .cfa -48 + ^
STACK CFI 43c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 43d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43e4 x19: .cfa -48 + ^
STACK CFI 4420 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4424 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4428 58 .cfa: sp 0 + .ra: x30
STACK CFI 442c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 443c x19: .cfa -48 + ^
STACK CFI 4478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 447c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4480 58 .cfa: sp 0 + .ra: x30
STACK CFI 4484 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4494 x19: .cfa -48 + ^
STACK CFI 44d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 44d8 60 .cfa: sp 0 + .ra: x30
STACK CFI 44dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44ec x19: .cfa -48 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4534 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4538 60 .cfa: sp 0 + .ra: x30
STACK CFI 453c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 454c x19: .cfa -48 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4594 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4598 60 .cfa: sp 0 + .ra: x30
STACK CFI 459c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45ac x19: .cfa -48 + ^
STACK CFI 45f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 45f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 45fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 460c x19: .cfa -48 + ^
STACK CFI 464c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4650 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4658 58 .cfa: sp 0 + .ra: x30
STACK CFI 465c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 466c x19: .cfa -48 + ^
STACK CFI 46a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 46b0 58 .cfa: sp 0 + .ra: x30
STACK CFI 46b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 46c4 x19: .cfa -48 + ^
STACK CFI 4700 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4704 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4708 58 .cfa: sp 0 + .ra: x30
STACK CFI 470c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 471c x19: .cfa -48 + ^
STACK CFI 4758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 475c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4760 58 .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4774 x19: .cfa -48 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 47b8 60 .cfa: sp 0 + .ra: x30
STACK CFI 47bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 47cc x19: .cfa -48 + ^
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4814 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4818 60 .cfa: sp 0 + .ra: x30
STACK CFI 481c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 482c x19: .cfa -48 + ^
STACK CFI 4870 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4874 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4878 60 .cfa: sp 0 + .ra: x30
STACK CFI 487c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 488c x19: .cfa -48 + ^
STACK CFI 48d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 48d8 60 .cfa: sp 0 + .ra: x30
STACK CFI 48dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 48ec x19: .cfa -48 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4934 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4938 60 .cfa: sp 0 + .ra: x30
STACK CFI 493c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 494c x19: .cfa -48 + ^
STACK CFI 4990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4994 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4998 5c .cfa: sp 0 + .ra: x30
STACK CFI 499c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 49e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49f8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a08 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4aa8 14 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ab8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ac0 54 .cfa: sp 0 + .ra: x30
STACK CFI 4ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4acc x21: .cfa -16 + ^
STACK CFI 4ad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b04 x19: x19 x20: x20
STACK CFI 4b10 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 4b18 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b38 150 .cfa: sp 0 + .ra: x30
STACK CFI 4b3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4b4c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4b7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4bcc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4bd0 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4bdc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c58 x19: x19 x20: x20
STACK CFI 4c6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c7c x19: x19 x20: x20
STACK CFI 4c84 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 4c88 5c .cfa: sp 0 + .ra: x30
STACK CFI 4c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ce8 54 .cfa: sp 0 + .ra: x30
STACK CFI 4cec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cf4 x19: .cfa -16 + ^
STACK CFI 4d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4d38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d40 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d68 58 .cfa: sp 0 + .ra: x30
STACK CFI 4d6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d74 x19: .cfa -16 + ^
STACK CFI 4d8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4dbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4dc0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4dd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4dec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e60 x19: x19 x20: x20
STACK CFI 4e74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4e78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4e80 x19: x19 x20: x20
STACK CFI 4e8c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4e90 28 .cfa: sp 0 + .ra: x30
STACK CFI 4e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e9c x19: .cfa -16 + ^
STACK CFI 4eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4eb8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ee0 c .cfa: sp 0 + .ra: x30
