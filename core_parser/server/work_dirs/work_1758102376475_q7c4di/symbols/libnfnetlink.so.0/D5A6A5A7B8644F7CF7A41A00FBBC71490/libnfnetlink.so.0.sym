MODULE Linux arm64 D5A6A5A7B8644F7CF7A41A00FBBC71490 libnfnetlink.so.0
INFO CODE_ID A7A5A6D564B87C4FF7A41A00FBBC71494DC54F7A
PUBLIC 1610 0 nfnl_dump_packet
PUBLIC 1740 0 nfnl_fd
PUBLIC 1778 0 nfnl_portid
PUBLIC 17b0 0 nfnl_open
PUBLIC 18f8 0 nfnl_set_sequence_tracking
PUBLIC 1908 0 nfnl_unset_sequence_tracking
PUBLIC 1918 0 nfnl_set_rcv_buffer_size
PUBLIC 1920 0 nfnl_subsys_open
PUBLIC 1a58 0 nfnl_subsys_close
PUBLIC 1ab8 0 nfnl_close
PUBLIC 1b38 0 nfnl_join
PUBLIC 1b90 0 nfnl_send
PUBLIC 1c08 0 nfnl_sendmsg
PUBLIC 1c70 0 nfnl_sendiov
PUBLIC 1d08 0 nfnl_fill_hdr
PUBLIC 1e18 0 nfnl_parse_hdr
PUBLIC 1e60 0 nfnl_recv
PUBLIC 1fb0 0 nfnl_listen
PUBLIC 2318 0 nfnl_talk
PUBLIC 27c0 0 nfnl_addattr_l
PUBLIC 28d8 0 nfnl_nfa_addattr_l
PUBLIC 29e0 0 nfnl_addattr8
PUBLIC 2a80 0 nfnl_nfa_addattr16
PUBLIC 2b20 0 nfnl_addattr16
PUBLIC 2bc0 0 nfnl_nfa_addattr32
PUBLIC 2c60 0 nfnl_addattr32
PUBLIC 2d00 0 nfnl_parse_attr
PUBLIC 2ff8 0 nfnl_build_nfa_iovec
PUBLIC 3080 0 nfnl_rcvbufsiz
PUBLIC 3158 0 nfnl_get_msg_first
PUBLIC 3218 0 nfnl_get_msg_next
PUBLIC 3320 0 nfnl_callback_register
PUBLIC 33c8 0 nfnl_callback_unregister
PUBLIC 3440 0 nfnl_check_attributes
PUBLIC 35b8 0 nfnl_handle_packet
PUBLIC 37a0 0 nfnl_process
PUBLIC 38c8 0 nfnl_iterator_create
PUBLIC 39e0 0 nfnl_iterator_destroy
PUBLIC 3a18 0 nfnl_iterator_process
PUBLIC 3ad8 0 nfnl_iterator_next
PUBLIC 3b60 0 nfnl_catch
PUBLIC 3c88 0 nfnl_query
PUBLIC 3fb8 0 nlif_index2name
PUBLIC 4088 0 nlif_get_ifflags
PUBLIC 4148 0 nlif_open
PUBLIC 4228 0 nlif_close
PUBLIC 42e0 0 nlif_catch
PUBLIC 4328 0 nlif_query
PUBLIC 4398 0 nlif_fd
PUBLIC 43e0 0 rtnl_handler_register
PUBLIC 43f8 0 rtnl_handler_unregister
PUBLIC 4450 0 rtnl_parse_rtattr
PUBLIC 44f0 0 rtnl_dump_type
PUBLIC 4598 0 rtnl_receive
PUBLIC 4720 0 rtnl_receive_multi
PUBLIC 4750 0 rtnl_open
PUBLIC 4860 0 rtnl_close
STACK CFI INIT 15b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 15b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 160c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1610 12c .cfa: sp 0 + .ra: x30
STACK CFI 1614 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1624 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1630 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1740 38 .cfa: sp 0 + .ra: x30
STACK CFI 1750 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1778 38 .cfa: sp 0 + .ra: x30
STACK CFI 1788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 17b0 148 .cfa: sp 0 + .ra: x30
STACK CFI 17b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1804 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1810 x23: .cfa -32 + ^
STACK CFI 1890 x21: x21 x22: x22
STACK CFI 1898 x23: x23
STACK CFI 18a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 18b8 x21: x21 x22: x22
STACK CFI 18bc x23: x23
STACK CFI 18e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 18f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18f4 x23: .cfa -32 + ^
STACK CFI INIT 18f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1908 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1918 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1920 134 .cfa: sp 0 + .ra: x30
STACK CFI 1924 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1930 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1940 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 195c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 19a0 x23: x23 x24: x24
STACK CFI 19ac x19: x19 x20: x20
STACK CFI 19b4 x21: x21 x22: x22
STACK CFI 19b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 19cc x19: x19 x20: x20
STACK CFI 19d8 x21: x21 x22: x22
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 19f8 x21: x21 x22: x22
STACK CFI 19fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1a00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1a0c x23: x23 x24: x24
STACK CFI 1a14 x19: x19 x20: x20
STACK CFI 1a18 x21: x21 x22: x22
STACK CFI 1a3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a40 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1a4c x19: x19 x20: x20
STACK CFI 1a50 x23: x23 x24: x24
STACK CFI INIT 1a58 5c .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a64 x19: .cfa -16 + ^
STACK CFI 1a8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ab8 80 .cfa: sp 0 + .ra: x30
STACK CFI 1abc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ac8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1b38 54 .cfa: sp 0 + .ra: x30
STACK CFI 1b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b90 78 .cfa: sp 0 + .ra: x30
STACK CFI 1b94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bc0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c08 68 .cfa: sp 0 + .ra: x30
STACK CFI 1c0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c70 98 .cfa: sp 0 + .ra: x30
STACK CFI 1c74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1c80 x19: .cfa -80 + ^
STACK CFI 1cdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ce0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d08 10c .cfa: sp 0 + .ra: x30
STACK CFI 1d0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d18 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1e18 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e60 150 .cfa: sp 0 + .ra: x30
STACK CFI 1e64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1e6c x19: .cfa -48 + ^
STACK CFI 1eec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ef0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1fb0 364 .cfa: sp 0 + .ra: x30
STACK CFI 1fb8 .cfa: sp 8416 +
STACK CFI 1fc0 .ra: .cfa -8408 + ^ x29: .cfa -8416 + ^
STACK CFI 1fc8 x23: .cfa -8368 + ^ x24: .cfa -8360 + ^
STACK CFI 1fdc x25: .cfa -8352 + ^ x26: .cfa -8344 + ^
STACK CFI 2008 x19: .cfa -8400 + ^ x20: .cfa -8392 + ^
STACK CFI 2014 x21: .cfa -8384 + ^ x22: .cfa -8376 + ^
STACK CFI 20c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 20c4 .cfa: sp 8416 + .ra: .cfa -8408 + ^ x19: .cfa -8400 + ^ x20: .cfa -8392 + ^ x21: .cfa -8384 + ^ x22: .cfa -8376 + ^ x23: .cfa -8368 + ^ x24: .cfa -8360 + ^ x25: .cfa -8352 + ^ x26: .cfa -8344 + ^ x29: .cfa -8416 + ^
STACK CFI 20d8 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 215c x27: x27 x28: x28
STACK CFI 2188 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 21c0 x27: x27 x28: x28
STACK CFI 21c4 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 21cc x27: x27 x28: x28
STACK CFI 21d0 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 21d4 x27: x27 x28: x28
STACK CFI 21d8 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 21ec x27: x27 x28: x28
STACK CFI 21f0 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 2210 x27: x27 x28: x28
STACK CFI 2214 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 2240 x27: x27 x28: x28
STACK CFI 2244 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 224c x27: x27 x28: x28
STACK CFI 2250 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 2274 x27: x27 x28: x28
STACK CFI 22d4 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI 2308 x27: x27 x28: x28
STACK CFI 2310 x27: .cfa -8336 + ^ x28: .cfa -8328 + ^
STACK CFI INIT 2318 4a4 .cfa: sp 0 + .ra: x30
STACK CFI 2320 .cfa: sp 8432 +
STACK CFI 232c .ra: .cfa -8424 + ^ x29: .cfa -8432 + ^
STACK CFI 2338 x23: .cfa -8384 + ^ x24: .cfa -8376 + ^
STACK CFI 2344 x21: .cfa -8400 + ^ x22: .cfa -8392 + ^
STACK CFI 2370 x19: .cfa -8416 + ^ x20: .cfa -8408 + ^ x25: .cfa -8368 + ^ x26: .cfa -8360 + ^ x27: .cfa -8352 + ^ x28: .cfa -8344 + ^
STACK CFI 2520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2524 .cfa: sp 8432 + .ra: .cfa -8424 + ^ x19: .cfa -8416 + ^ x20: .cfa -8408 + ^ x21: .cfa -8400 + ^ x22: .cfa -8392 + ^ x23: .cfa -8384 + ^ x24: .cfa -8376 + ^ x25: .cfa -8368 + ^ x26: .cfa -8360 + ^ x27: .cfa -8352 + ^ x28: .cfa -8344 + ^ x29: .cfa -8432 + ^
STACK CFI INIT 27c0 114 .cfa: sp 0 + .ra: x30
STACK CFI 27c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 284c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2850 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 28d8 108 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 295c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 29e0 9c .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a10 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a80 9c .cfa: sp 0 + .ra: x30
STACK CFI 2a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ab0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2b20 9c .cfa: sp 0 + .ra: x30
STACK CFI 2b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b50 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2bc0 9c .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2c60 9c .cfa: sp 0 + .ra: x30
STACK CFI 2c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d00 110 .cfa: sp 0 + .ra: x30
STACK CFI 2d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d10 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2da4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e10 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2e14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2e18 .cfa: x29 96 +
STACK CFI 2e1c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e28 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2e40 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2edc .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2ff8 84 .cfa: sp 0 + .ra: x30
STACK CFI 2ffc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3080 d8 .cfa: sp 0 + .ra: x30
STACK CFI 3084 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3090 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30a4 x21: .cfa -48 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3114 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3158 bc .cfa: sp 0 + .ra: x30
STACK CFI 315c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3198 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 319c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3218 108 .cfa: sp 0 + .ra: x30
STACK CFI 321c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 32a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 32b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3320 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3324 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3364 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3368 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 33c8 78 .cfa: sp 0 + .ra: x30
STACK CFI 33cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3400 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3440 174 .cfa: sp 0 + .ra: x30
STACK CFI 3444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 344c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 353c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3540 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 35b8 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 35bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 35c0 .cfa: x29 112 +
STACK CFI 35c4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 35e0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 35ec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3758 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 37a0 124 .cfa: sp 0 + .ra: x30
STACK CFI 37a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3834 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 38c8 114 .cfa: sp 0 + .ra: x30
STACK CFI 38cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3934 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 395c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3960 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39e0 34 .cfa: sp 0 + .ra: x30
STACK CFI 39ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3a18 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3a1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ad8 88 .cfa: sp 0 + .ra: x30
STACK CFI 3adc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3b60 128 .cfa: sp 0 + .ra: x30
STACK CFI 3b64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3b68 .cfa: x29 80 +
STACK CFI 3b6c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b84 x23: .cfa -32 + ^
STACK CFI 3c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3c60 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3c88 88 .cfa: sp 0 + .ra: x30
STACK CFI 3c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c94 x19: .cfa -16 + ^
STACK CFI 3cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3cc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d10 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3d14 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 3d1c x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 3d2c x21: .cfa -448 + ^
STACK CFI 3d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d70 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x29: .cfa -480 + ^
STACK CFI INIT 3de8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e00 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 3e04 .cfa: sp 528 +
STACK CFI 3e08 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 3e10 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 3e18 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 3e24 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 3e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e70 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x29: .cfa -528 + ^
STACK CFI 3e84 x27: .cfa -448 + ^
STACK CFI 3e98 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3f20 x27: x27
STACK CFI 3f30 x25: x25 x26: x26
STACK CFI 3f34 x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^
STACK CFI 3f38 x25: x25 x26: x26
STACK CFI 3f3c x27: x27
STACK CFI 3f40 x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^
STACK CFI 3f80 x25: x25 x26: x26
STACK CFI 3f84 x27: x27
STACK CFI 3f8c x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3f90 x27: .cfa -448 + ^
STACK CFI 3f98 x25: x25 x26: x26
STACK CFI 3f9c x27: x27
STACK CFI INIT 3fa0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fb8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3fbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4044 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4088 bc .cfa: sp 0 + .ra: x30
STACK CFI 408c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4148 e0 .cfa: sp 0 + .ra: x30
STACK CFI 414c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 415c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 41e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4214 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4228 b4 .cfa: sp 0 + .ra: x30
STACK CFI 422c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4238 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 42e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 42fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4328 6c .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4334 x19: .cfa -16 + ^
STACK CFI 435c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4360 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 436c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4370 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4398 48 .cfa: sp 0 + .ra: x30
STACK CFI 43b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 43e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f8 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4450 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 445c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 446c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 44f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4514 x19: .cfa -64 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4594 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4598 184 .cfa: sp 0 + .ra: x30
STACK CFI 45a0 .cfa: sp 8336 +
STACK CFI 45b4 .ra: .cfa -8328 + ^ x29: .cfa -8336 + ^
STACK CFI 45c0 x21: .cfa -8304 + ^ x22: .cfa -8296 + ^
STACK CFI 45c8 x19: .cfa -8320 + ^ x20: .cfa -8312 + ^
STACK CFI 46d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46d8 .cfa: sp 8336 + .ra: .cfa -8328 + ^ x19: .cfa -8320 + ^ x20: .cfa -8312 + ^ x21: .cfa -8304 + ^ x22: .cfa -8296 + ^ x29: .cfa -8336 + ^
STACK CFI INIT 4720 30 .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 472c x19: .cfa -16 + ^
STACK CFI 474c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4750 110 .cfa: sp 0 + .ra: x30
STACK CFI 4754 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4764 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47b8 x21: .cfa -32 + ^
STACK CFI 4814 x21: x21
STACK CFI 481c x21: .cfa -32 + ^
STACK CFI 4828 x21: x21
STACK CFI 4854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4858 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 485c x21: .cfa -32 + ^
STACK CFI INIT 4860 28 .cfa: sp 0 + .ra: x30
STACK CFI 4864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 486c x19: .cfa -16 + ^
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
