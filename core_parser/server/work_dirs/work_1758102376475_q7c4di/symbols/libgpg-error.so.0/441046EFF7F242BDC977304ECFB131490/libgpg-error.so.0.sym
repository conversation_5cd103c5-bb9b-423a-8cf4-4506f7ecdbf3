MODULE Linux arm64 441046EFF7F242BDC977304ECFB131490 libgpg-error.so.0
INFO CODE_ID EF461044F2F7BD42C977304ECFB13149884C4195
PUBLIC 4220 0 gpg_err_init
PUBLIC e900 0 gpg_strerror
PUBLIC e908 0 gpg_strerror_r
PUBLIC e910 0 gpg_strsource
PUBLIC e918 0 gpg_err_code_from_errno
PUBLIC e920 0 gpg_err_code_to_errno
PUBLIC e928 0 gpg_err_code_from_syserror
PUBLIC e930 0 gpg_err_set_errno
PUBLIC e938 0 gpg_err_deinit
PUBLIC e940 0 gpgrt_add_emergency_cleanup
PUBLIC e948 0 gpgrt_abort
PUBLIC e958 0 gpg_error_check_version
PUBLIC e960 0 gpgrt_check_version
PUBLIC e968 0 gpgrt_set_syscall_clamp
PUBLIC e970 0 gpgrt_get_syscall_clamp
PUBLIC e978 0 gpgrt_set_alloc_func
PUBLIC e980 0 gpgrt_lock_init
PUBLIC e988 0 gpgrt_lock_lock
PUBLIC e990 0 gpgrt_lock_trylock
PUBLIC e998 0 gpgrt_lock_unlock
PUBLIC e9a0 0 gpgrt_lock_destroy
PUBLIC e9a8 0 gpgrt_yield
PUBLIC e9b0 0 gpgrt_fopen
PUBLIC e9b8 0 gpgrt_mopen
PUBLIC e9c0 0 gpgrt_fopenmem
PUBLIC e9c8 0 gpgrt_fopenmem_init
PUBLIC e9d0 0 gpgrt_fdopen
PUBLIC e9d8 0 gpgrt_fdopen_nc
PUBLIC e9e0 0 gpgrt_sysopen
PUBLIC e9e8 0 gpgrt_sysopen_nc
PUBLIC e9f0 0 gpgrt_fpopen
PUBLIC e9f8 0 gpgrt_fpopen_nc
PUBLIC ea00 0 gpgrt_freopen
PUBLIC ea08 0 gpgrt_fopencookie
PUBLIC ea38 0 gpgrt_fclose
PUBLIC ea40 0 gpgrt_fclose_snatch
PUBLIC ea48 0 gpgrt_onclose
PUBLIC ea50 0 gpgrt_fileno
PUBLIC ea58 0 gpgrt_fileno_unlocked
PUBLIC ea60 0 gpgrt_syshd
PUBLIC ea68 0 gpgrt_syshd_unlocked
PUBLIC ea70 0 _gpgrt_set_std_fd
PUBLIC ea78 0 _gpgrt_get_std_stream
PUBLIC ea80 0 gpgrt_flockfile
PUBLIC ea88 0 gpgrt_ftrylockfile
PUBLIC ea90 0 gpgrt_funlockfile
PUBLIC ea98 0 _gpgrt_pending
PUBLIC eaa0 0 _gpgrt_pending_unlocked
PUBLIC eaa8 0 gpgrt_feof
PUBLIC eab0 0 gpgrt_feof_unlocked
PUBLIC eab8 0 gpgrt_ferror
PUBLIC eac0 0 gpgrt_ferror_unlocked
PUBLIC eac8 0 gpgrt_clearerr
PUBLIC ead0 0 gpgrt_clearerr_unlocked
PUBLIC ead8 0 gpgrt_fflush
PUBLIC eae0 0 gpgrt_fseek
PUBLIC eae8 0 gpgrt_fseeko
PUBLIC eaf0 0 gpgrt_ftell
PUBLIC eaf8 0 gpgrt_ftello
PUBLIC eb00 0 gpgrt_rewind
PUBLIC eb08 0 gpgrt_ftruncate
PUBLIC eb10 0 gpgrt_fgetc
PUBLIC eb18 0 _gpgrt_getc_underflow
PUBLIC eb20 0 gpgrt_fputc
PUBLIC eb28 0 _gpgrt_putc_overflow
PUBLIC eb30 0 gpgrt_ungetc
PUBLIC eb38 0 gpgrt_read
PUBLIC eb40 0 gpgrt_write
PUBLIC eb48 0 gpgrt_write_sanitized
PUBLIC eb50 0 gpgrt_write_hexstring
PUBLIC eb58 0 gpgrt_fread
PUBLIC eb60 0 gpgrt_fwrite
PUBLIC eb68 0 gpgrt_fgets
PUBLIC eb70 0 gpgrt_fputs
PUBLIC eb78 0 gpgrt_fputs_unlocked
PUBLIC eb80 0 gpgrt_getline
PUBLIC eb88 0 gpgrt_read_line
PUBLIC eb90 0 gpgrt_vfprintf
PUBLIC ebc8 0 gpgrt_vfprintf_unlocked
PUBLIC ec00 0 gpgrt_printf
PUBLIC ecc0 0 gpgrt_printf_unlocked
PUBLIC ed80 0 gpgrt_fprintf
PUBLIC ee30 0 gpgrt_fprintf_unlocked
PUBLIC eee0 0 gpgrt_fprintf_sf
PUBLIC ef80 0 gpgrt_fprintf_sf_unlocked
PUBLIC f020 0 gpgrt_setvbuf
PUBLIC f028 0 gpgrt_setbuf
PUBLIC f040 0 gpgrt_set_binary
PUBLIC f048 0 gpgrt_set_nonblock
PUBLIC f050 0 gpgrt_get_nonblock
PUBLIC f058 0 gpgrt_poll
PUBLIC f060 0 gpgrt_tmpfile
PUBLIC f068 0 gpgrt_opaque_set
PUBLIC f070 0 gpgrt_opaque_get
PUBLIC f078 0 gpgrt_fname_set
PUBLIC f080 0 gpgrt_fname_get
PUBLIC f088 0 gpgrt_asprintf
PUBLIC f128 0 gpgrt_vasprintf
PUBLIC f158 0 gpgrt_bsprintf
PUBLIC f218 0 gpgrt_vbsprintf
PUBLIC f288 0 gpgrt_snprintf
PUBLIC f328 0 gpgrt_vsnprintf
PUBLIC f358 0 gpgrt_realloc
PUBLIC f360 0 gpgrt_malloc
PUBLIC f368 0 gpgrt_calloc
PUBLIC f370 0 gpgrt_strdup
PUBLIC f378 0 gpgrt_strconcat
PUBLIC f438 0 gpgrt_free
PUBLIC f448 0 gpgrt_getenv
PUBLIC f450 0 gpgrt_setenv
PUBLIC f458 0 gpgrt_mkdir
PUBLIC f460 0 gpgrt_chdir
PUBLIC f468 0 gpgrt_getcwd
PUBLIC f470 0 gpgrt_b64enc_start
PUBLIC f478 0 gpgrt_b64enc_write
PUBLIC f480 0 gpgrt_b64enc_finish
PUBLIC f488 0 gpgrt_b64dec_start
PUBLIC f490 0 gpgrt_b64dec_proc
PUBLIC f498 0 gpgrt_b64dec_finish
PUBLIC f4a0 0 gpgrt_get_errorcount
PUBLIC f4a8 0 gpgrt_inc_errorcount
PUBLIC f4b0 0 gpgrt_log_set_sink
PUBLIC f4b8 0 gpgrt_log_set_socket_dir_cb
PUBLIC f4c0 0 gpgrt_log_set_pid_suffix_cb
PUBLIC f4c8 0 gpgrt_log_set_prefix
PUBLIC f4d0 0 gpgrt_log_get_prefix
PUBLIC f4d8 0 gpgrt_log_test_fd
PUBLIC f4e0 0 gpgrt_log_get_fd
PUBLIC f4e8 0 gpgrt_log_get_stream
PUBLIC f4f0 0 gpgrt_log
PUBLIC f590 0 gpgrt_logv
PUBLIC f5c0 0 gpgrt_logv_prefix
PUBLIC f5f0 0 gpgrt_log_string
PUBLIC f5f8 0 gpgrt_log_info
PUBLIC f6a8 0 gpgrt_log_error
PUBLIC f758 0 gpgrt_log_fatal
PUBLIC f7e8 0 gpgrt_log_bug
PUBLIC f878 0 gpgrt_log_debug
PUBLIC f928 0 gpgrt_log_debug_string
PUBLIC f9e8 0 gpgrt_log_printf
PUBLIC faa0 0 gpgrt_log_flush
PUBLIC faa8 0 gpgrt_log_printhex
PUBLIC fb48 0 gpgrt_log_clock
PUBLIC fbf0 0 _gpgrt_log_assert
PUBLIC fc00 0 gpgrt_argparse
PUBLIC fc08 0 gpgrt_usage
PUBLIC fc10 0 gpgrt_strusage
PUBLIC fc18 0 gpgrt_set_strusage
PUBLIC fc20 0 gpgrt_set_usage_outfnc
PUBLIC fc28 0 gpgrt_set_fixed_string_mapper
PUBLIC fc30 0 gpgrt_cmp_version
STACK CFI INIT 4238 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4268 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 42ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42b4 x19: .cfa -16 + ^
STACK CFI 42ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42f8 2c .cfa: sp 0 + .ra: x30
STACK CFI 42fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4328 54 .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4368 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4378 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4380 60 .cfa: sp 0 + .ra: x30
STACK CFI 4384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 438c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 43e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4414 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4418 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 441c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4420 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4428 44 .cfa: sp 0 + .ra: x30
STACK CFI 442c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 445c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4460 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4464 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4468 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4470 cc .cfa: sp 0 + .ra: x30
STACK CFI 4474 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 447c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4488 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 44d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4534 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4540 20 .cfa: sp 0 + .ra: x30
STACK CFI 4544 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 455c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4560 148 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 456c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4578 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4658 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 46a8 2c .cfa: sp 0 + .ra: x30
STACK CFI 46ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46b4 x19: .cfa -16 + ^
STACK CFI INIT 46d8 388 .cfa: sp 0 + .ra: x30
STACK CFI 46dc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 46ec x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4704 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 470c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 471c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4780 x27: .cfa -64 + ^
STACK CFI 49e8 x27: x27
STACK CFI 49f0 x27: .cfa -64 + ^
STACK CFI 49f4 x27: x27
STACK CFI 4a0c x27: .cfa -64 + ^
STACK CFI INIT 4a60 78 .cfa: sp 0 + .ra: x30
STACK CFI 4a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ad8 32c .cfa: sp 0 + .ra: x30
STACK CFI 4adc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4ae8 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4af4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4b08 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4b14 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4b1c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4c68 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4e08 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4e0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ebc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ee0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 4ee4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4eec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4efc x25: .cfa -48 + ^
STACK CFI 4f14 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4fe0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 50a0 340 .cfa: sp 0 + .ra: x30
STACK CFI 50a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 50ac x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 50c8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 50d0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 50d8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 50e0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 519c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 53e0 19c .cfa: sp 0 + .ra: x30
STACK CFI 53e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5428 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 54f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 54f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5508 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5580 38 .cfa: sp 0 + .ra: x30
STACK CFI 5590 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5598 x19: .cfa -16 + ^
STACK CFI 55b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55c0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55e8 2c .cfa: sp 0 + .ra: x30
STACK CFI 55ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5618 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5620 84 .cfa: sp 0 + .ra: x30
STACK CFI 5624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 562c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5638 x21: .cfa -16 + ^
STACK CFI 566c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5670 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5698 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 56a8 54 .cfa: sp 0 + .ra: x30
STACK CFI 56ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 5700 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5710 40 .cfa: sp 0 + .ra: x30
STACK CFI 573c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 574c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5750 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5768 70 .cfa: sp 0 + .ra: x30
STACK CFI 576c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 57d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 57d8 44 .cfa: sp 0 + .ra: x30
STACK CFI 57dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5820 12c .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 582c x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI 5838 x27: .cfa -416 + ^
STACK CFI 5844 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 585c x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 58e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 58ec .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x29: .cfa -496 + ^
STACK CFI INIT 5950 bc .cfa: sp 0 + .ra: x30
STACK CFI 5954 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 595c x19: .cfa -288 + ^
STACK CFI 59f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 59f8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 5a10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a18 24 .cfa: sp 0 + .ra: x30
STACK CFI 5a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a24 x19: .cfa -16 + ^
STACK CFI 5a38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a40 ac .cfa: sp 0 + .ra: x30
STACK CFI 5a44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5a4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5a64 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ab0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5af0 110 .cfa: sp 0 + .ra: x30
STACK CFI 5af4 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 5afc x21: .cfa -320 + ^
STACK CFI 5b04 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 5bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5bf4 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x29: .cfa -352 + ^
STACK CFI INIT 5c00 154 .cfa: sp 0 + .ra: x30
STACK CFI 5c04 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 5c14 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 5c28 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 5d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d0c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x29: .cfa -384 + ^
STACK CFI INIT 5d58 64 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5dc0 180 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f40 280 .cfa: sp 0 + .ra: x30
STACK CFI 5f44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5f54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5f70 x21: .cfa -48 + ^
STACK CFI 60cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 61c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61c8 74 .cfa: sp 0 + .ra: x30
STACK CFI 61cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61d4 x19: .cfa -16 + ^
STACK CFI 6208 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 620c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6238 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6240 124 .cfa: sp 0 + .ra: x30
STACK CFI 6244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 624c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 629c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 62e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6328 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6368 d0 .cfa: sp 0 + .ra: x30
STACK CFI 636c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6378 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 638c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 640c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6410 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 642c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6438 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6448 54 .cfa: sp 0 + .ra: x30
STACK CFI 644c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6454 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6490 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 64a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 64a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64b0 x19: .cfa -16 + ^
STACK CFI 64d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 64e0 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 64ec x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6534 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 653c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 655c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6560 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6564 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6590 x21: x21 x22: x22
STACK CFI 6594 x23: x23 x24: x24
STACK CFI 6598 x27: x27 x28: x28
STACK CFI 65a0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 66d8 x21: x21 x22: x22
STACK CFI 66e0 x23: x23 x24: x24
STACK CFI 66e8 x27: x27 x28: x28
STACK CFI 670c x25: x25 x26: x26
STACK CFI 6718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 671c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 683c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6844 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6854 x25: x25 x26: x26
STACK CFI 6858 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6864 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6878 x21: x21 x22: x22
STACK CFI 687c x23: x23 x24: x24
STACK CFI 6880 x25: x25 x26: x26
STACK CFI 6884 x27: x27 x28: x28
STACK CFI 6890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6894 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 689c x25: x25 x26: x26
STACK CFI 68a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 68a8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 68ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6924 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6944 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 695c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6960 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 6964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6970 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6978 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b00 bc .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b20 x21: .cfa -16 + ^
STACK CFI 6b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6bc0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 6bc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6bcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6c90 f0 .cfa: sp 0 + .ra: x30
STACK CFI 6c94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6ca8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6d80 17c .cfa: sp 0 + .ra: x30
STACK CFI 6d84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6da0 x23: .cfa -16 + ^
STACK CFI 6dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6dd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6e88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6f00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6f04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6f0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6f14 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6f20 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6f2c x25: .cfa -16 + ^
STACK CFI 6fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 6fc0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6fc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6fcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fe0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7088 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7090 64 .cfa: sp 0 + .ra: x30
STACK CFI 7094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 709c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 70f8 90 .cfa: sp 0 + .ra: x30
STACK CFI 70fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7104 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7118 x21: .cfa -16 + ^
STACK CFI 7158 x21: x21
STACK CFI 7164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7168 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7178 x21: x21
STACK CFI 717c x21: .cfa -16 + ^
STACK CFI 7184 x21: x21
STACK CFI INIT 7188 8c .cfa: sp 0 + .ra: x30
STACK CFI 718c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7194 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 71ac x21: .cfa -16 + ^
STACK CFI 71dc x21: x21
STACK CFI 71f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7204 x21: x21
STACK CFI 7208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 720c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7218 74 .cfa: sp 0 + .ra: x30
STACK CFI 7224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7234 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 724c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 727c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7290 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 729c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 72c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 72c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 73b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 73bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7478 b4 .cfa: sp 0 + .ra: x30
STACK CFI 747c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7488 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7494 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 74a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74f8 x21: x21 x22: x22
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 7514 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7530 84 .cfa: sp 0 + .ra: x30
STACK CFI 7534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 753c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 754c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7588 x19: x19 x20: x20
STACK CFI 7594 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7598 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 75a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 75b0 x19: x19 x20: x20
STACK CFI INIT 75b8 9c .cfa: sp 0 + .ra: x30
STACK CFI 75bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 75c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 75cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7620 x21: x21 x22: x22
STACK CFI 762c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7630 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7644 x21: x21 x22: x22
STACK CFI 7648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 764c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7658 98 .cfa: sp 0 + .ra: x30
STACK CFI 765c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7664 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7670 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 76ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 76f0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 76f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7700 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 770c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 77f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 77f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 780c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7814 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 78e0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 78e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7924 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7974 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 79b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 79c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 79c8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 79cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79e4 x21: .cfa -16 + ^
STACK CFI 7a28 x21: x21
STACK CFI 7a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7a44 x21: x21
STACK CFI 7a5c x21: .cfa -16 + ^
STACK CFI 7a64 x21: x21
STACK CFI INIT 7a68 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7abc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7b10 19c .cfa: sp 0 + .ra: x30
STACK CFI 7b14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7b20 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 7b2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x24: x24 x25: x25 x29: x29
STACK CFI 7bd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7cb0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 7cb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7cbc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7ce0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d60 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 7dd4 x25: .cfa -48 + ^
STACK CFI 7dfc x25: x25
STACK CFI 7e08 x25: .cfa -48 + ^
STACK CFI 7e50 x25: x25
STACK CFI 7e54 x25: .cfa -48 + ^
STACK CFI INIT 7e58 64 .cfa: sp 0 + .ra: x30
STACK CFI 7e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7eb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ec0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ecc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f68 120 .cfa: sp 0 + .ra: x30
STACK CFI 7f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 805c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8060 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8088 b4 .cfa: sp 0 + .ra: x30
STACK CFI 808c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8098 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 80fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8100 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8140 24c .cfa: sp 0 + .ra: x30
STACK CFI 8144 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 814c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8154 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8160 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8170 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 817c x27: .cfa -16 + ^
STACK CFI 8294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8298 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 82e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 82ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 830c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8390 148 .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 839c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 83a8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 83c4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 84d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 84d8 14c .cfa: sp 0 + .ra: x30
STACK CFI 84dc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 84e4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 84f8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 8514 x23: .cfa -112 + ^
STACK CFI 85e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 85e8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8628 40 .cfa: sp 0 + .ra: x30
STACK CFI 8644 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8664 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8668 124 .cfa: sp 0 + .ra: x30
STACK CFI 866c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8674 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 86c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 86cc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 8714 x21: .cfa -128 + ^
STACK CFI 8750 x21: x21
STACK CFI 8754 x21: .cfa -128 + ^
STACK CFI 8760 x21: x21
STACK CFI 8768 x21: .cfa -128 + ^
STACK CFI 877c x21: x21
STACK CFI 8788 x21: .cfa -128 + ^
STACK CFI INIT 8790 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 8794 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 879c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 87a8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 87c4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 87d8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 881c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8820 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 8824 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 891c x27: x27 x28: x28
STACK CFI 8920 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8924 x27: x27 x28: x28
STACK CFI 8928 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 893c x27: x27 x28: x28
STACK CFI 8944 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8960 x27: x27 x28: x28
STACK CFI 8964 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 8968 130 .cfa: sp 0 + .ra: x30
STACK CFI 896c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8978 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8988 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8a80 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8a98 bc .cfa: sp 0 + .ra: x30
STACK CFI 8aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ab0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ab8 x21: .cfa -16 + ^
STACK CFI 8ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8ae8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8b34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b58 bc .cfa: sp 0 + .ra: x30
STACK CFI 8b5c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8b64 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 8c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c10 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8c18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c48 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c58 44 .cfa: sp 0 + .ra: x30
STACK CFI 8c6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ca0 44 .cfa: sp 0 + .ra: x30
STACK CFI 8cb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8cd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ce8 68 .cfa: sp 0 + .ra: x30
STACK CFI 8cec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8cf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d00 x21: .cfa -16 + ^
STACK CFI 8d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8d50 310 .cfa: sp 0 + .ra: x30
STACK CFI 8d58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d68 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d70 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8f2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9060 1fc .cfa: sp 0 + .ra: x30
STACK CFI 9064 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 906c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 907c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 909c x23: .cfa -48 + ^
STACK CFI 90d8 x23: x23
STACK CFI 9100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9104 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 91fc x23: x23
STACK CFI 9200 x23: .cfa -48 + ^
STACK CFI 9224 x23: x23
STACK CFI 9258 x23: .cfa -48 + ^
STACK CFI INIT 9260 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9270 140 .cfa: sp 0 + .ra: x30
STACK CFI 9274 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 927c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9288 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 92a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9300 x23: x23 x24: x24
STACK CFI 9304 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9314 x23: x23 x24: x24
STACK CFI 933c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9340 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 934c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 939c x23: x23 x24: x24
STACK CFI 93ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 93b0 104 .cfa: sp 0 + .ra: x30
STACK CFI 93b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 93bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 93cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9448 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 94a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 94a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 94b8 58 .cfa: sp 0 + .ra: x30
STACK CFI 94f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 950c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9510 88 .cfa: sp 0 + .ra: x30
STACK CFI 9514 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 951c x19: .cfa -48 + ^
STACK CFI 9570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9574 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9598 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95b8 3c .cfa: sp 0 + .ra: x30
STACK CFI 95d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9618 90 .cfa: sp 0 + .ra: x30
STACK CFI 961c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9624 x19: .cfa -16 + ^
STACK CFI 9660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9664 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 96a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96a8 9c .cfa: sp 0 + .ra: x30
STACK CFI 96ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 96f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 96fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9748 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9750 90 .cfa: sp 0 + .ra: x30
STACK CFI 9754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 975c x19: .cfa -16 + ^
STACK CFI 9798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 979c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 97dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 97e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 97f8 74 .cfa: sp 0 + .ra: x30
STACK CFI 97fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9804 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 982c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9830 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9870 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9888 74 .cfa: sp 0 + .ra: x30
STACK CFI 988c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9894 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 98bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 98c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 98f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9900 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9918 70 .cfa: sp 0 + .ra: x30
STACK CFI 991c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9924 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 994c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9950 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9988 170 .cfa: sp 0 + .ra: x30
STACK CFI 998c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9994 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 99dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 99e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9a30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9af0 x21: x21 x22: x22
STACK CFI 9af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9af8 20 .cfa: sp 0 + .ra: x30
STACK CFI 9afc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9b0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9b18 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9b1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9b24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9bc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bc8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9bd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9c70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c78 7c .cfa: sp 0 + .ra: x30
STACK CFI 9c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c84 x19: .cfa -16 + ^
STACK CFI 9ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9cec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9cf8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9da0 6c .cfa: sp 0 + .ra: x30
STACK CFI 9da4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9db0 x19: .cfa -48 + ^
STACK CFI 9dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9e00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9e10 68 .cfa: sp 0 + .ra: x30
STACK CFI 9e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9e78 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9e7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9e84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9f30 fc .cfa: sp 0 + .ra: x30
STACK CFI 9f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9f4c x21: .cfa -32 + ^
STACK CFI 9fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9fe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT a030 ec .cfa: sp 0 + .ra: x30
STACK CFI a034 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a03c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a050 x21: .cfa -32 + ^
STACK CFI a0f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a0f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT a120 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a130 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a140 d8 .cfa: sp 0 + .ra: x30
STACK CFI a144 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a14c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a15c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a17c x23: .cfa -32 + ^
STACK CFI a1c0 x23: x23
STACK CFI a1e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a1e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI a1f0 x23: .cfa -32 + ^
STACK CFI a210 x23: x23
STACK CFI a214 x23: .cfa -32 + ^
STACK CFI INIT a218 d8 .cfa: sp 0 + .ra: x30
STACK CFI a21c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a224 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a234 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a254 x23: .cfa -32 + ^
STACK CFI a298 x23: x23
STACK CFI a2bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI a2c8 x23: .cfa -32 + ^
STACK CFI a2e8 x23: x23
STACK CFI a2ec x23: .cfa -32 + ^
STACK CFI INIT a2f0 188 .cfa: sp 0 + .ra: x30
STACK CFI a2f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a300 x23: .cfa -16 + ^
STACK CFI a30c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a318 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a3d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a3d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a434 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT a478 40 .cfa: sp 0 + .ra: x30
STACK CFI a47c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a484 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a4b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a4b8 a4 .cfa: sp 0 + .ra: x30
STACK CFI a4bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4d0 x21: .cfa -16 + ^
STACK CFI a524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a528 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a560 458 .cfa: sp 0 + .ra: x30
STACK CFI a564 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a56c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI a578 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI a580 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI a5a4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI a5d4 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a70c x27: x27 x28: x28
STACK CFI a740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a744 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI a75c x27: x27 x28: x28
STACK CFI a790 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a858 x27: x27 x28: x28
STACK CFI a86c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a940 x27: x27 x28: x28
STACK CFI a944 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a950 x27: x27 x28: x28
STACK CFI a954 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a964 x27: x27 x28: x28
STACK CFI a974 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a994 x27: x27 x28: x28
STACK CFI a998 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a9ac x27: x27 x28: x28
STACK CFI INIT a9b8 2a4 .cfa: sp 0 + .ra: x30
STACK CFI a9bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a9c8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a9d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a9dc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI aa08 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI aaa0 x25: x25 x26: x26
STACK CFI aab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI aabc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI ab24 x25: x25 x26: x26
STACK CFI ab3c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI abfc x25: x25 x26: x26
STACK CFI ac18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI ac1c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI ac2c x25: x25 x26: x26
STACK CFI INIT ac60 78 .cfa: sp 0 + .ra: x30
STACK CFI ac64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ac74 x19: .cfa -80 + ^
STACK CFI accc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI acd0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT acd8 e8 .cfa: sp 0 + .ra: x30
STACK CFI acdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ace4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI acf4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ad08 x23: .cfa -80 + ^
STACK CFI ad78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ad7c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI adb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI adb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT adc0 e8 .cfa: sp 0 + .ra: x30
STACK CFI adc4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI add4 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI ae98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ae9c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT aea8 138 .cfa: sp 0 + .ra: x30
STACK CFI aeac .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI aeb8 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI af18 x21: .cfa -304 + ^
STACK CFI afa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI afac .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI INIT afe0 11c .cfa: sp 0 + .ra: x30
STACK CFI afe4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI afec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b010 x21: .cfa -96 + ^
STACK CFI b09c x21: x21
STACK CFI b0bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b0c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI b0cc x21: x21
STACK CFI b0d4 x21: .cfa -96 + ^
STACK CFI b0dc x21: x21
STACK CFI b0e0 x21: .cfa -96 + ^
STACK CFI b0f0 x21: x21
STACK CFI b0f8 x21: .cfa -96 + ^
STACK CFI INIT b100 e8 .cfa: sp 0 + .ra: x30
STACK CFI b104 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b110 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b11c x21: .cfa -32 + ^
STACK CFI b164 x21: x21
STACK CFI b170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b174 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI b1c8 x21: x21
STACK CFI b1cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b1d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI b1d4 x21: x21
STACK CFI INIT b1e8 58 .cfa: sp 0 + .ra: x30
STACK CFI b1ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b1f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b214 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b240 e0 .cfa: sp 0 + .ra: x30
STACK CFI b244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b24c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b258 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b2c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b2f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b2f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b320 74 .cfa: sp 0 + .ra: x30
STACK CFI b324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b32c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b398 300 .cfa: sp 0 + .ra: x30
STACK CFI b39c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b3a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b3b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b3b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b448 x21: x21 x22: x22
STACK CFI b44c x23: x23 x24: x24
STACK CFI b464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI b468 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b658 x21: x21 x22: x22
STACK CFI b65c x23: x23 x24: x24
STACK CFI b660 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b668 x21: x21 x22: x22
STACK CFI b66c x23: x23 x24: x24
STACK CFI b688 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b690 x21: x21 x22: x22
STACK CFI b694 x23: x23 x24: x24
STACK CFI INIT b698 70 .cfa: sp 0 + .ra: x30
STACK CFI b69c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b6a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b6e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b708 6c .cfa: sp 0 + .ra: x30
STACK CFI b70c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b714 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b73c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b778 98 .cfa: sp 0 + .ra: x30
STACK CFI b780 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b788 x19: .cfa -32 + ^
STACK CFI b7c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b7c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI b7d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b7d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI b80c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b810 98 .cfa: sp 0 + .ra: x30
STACK CFI b814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b81c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b864 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b88c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b890 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b8a8 390 .cfa: sp 0 + .ra: x30
STACK CFI b8ac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b8b4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b8c4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b8e8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b90c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b9e8 x27: x27 x28: x28
STACK CFI ba34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ba38 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI bba0 x27: x27 x28: x28
STACK CFI bbb0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bc0c x27: x27 x28: x28
STACK CFI bc1c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bc30 x27: x27 x28: x28
STACK CFI bc34 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT bc38 1d8 .cfa: sp 0 + .ra: x30
STACK CFI bc3c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bc44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bc50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bc64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI bc74 x25: .cfa -32 + ^
STACK CFI bdb8 x25: x25
STACK CFI bde0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bde4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI bdf4 x25: x25
STACK CFI bdf8 x25: .cfa -32 + ^
STACK CFI be08 x25: x25
STACK CFI be0c x25: .cfa -32 + ^
STACK CFI INIT be10 f8 .cfa: sp 0 + .ra: x30
STACK CFI be14 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI be1c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI be24 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI be48 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI be54 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI be60 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI beb8 x21: x21 x22: x22
STACK CFI bebc x23: x23 x24: x24
STACK CFI bec0 x27: x27 x28: x28
STACK CFI bec4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bec8 x21: x21 x22: x22
STACK CFI becc x23: x23 x24: x24
STACK CFI bed0 x27: x27 x28: x28
STACK CFI bef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI bef8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI befc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI bf00 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI bf04 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT bf08 2c .cfa: sp 0 + .ra: x30
STACK CFI bf0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bf38 b4 .cfa: sp 0 + .ra: x30
STACK CFI bf50 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bfbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bfc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bfe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bff0 1ac .cfa: sp 0 + .ra: x30
STACK CFI bff4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bffc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c00c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c014 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c020 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c02c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c0ec x19: x19 x20: x20
STACK CFI c0f0 x21: x21 x22: x22
STACK CFI c0f4 x23: x23 x24: x24
STACK CFI c104 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c108 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI c140 x19: x19 x20: x20
STACK CFI c144 x21: x21 x22: x22
STACK CFI c148 x23: x23 x24: x24
STACK CFI c154 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c158 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI c194 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT c1a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI c1a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c1ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c1b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c214 x19: x19 x20: x20
STACK CFI c220 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c224 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c234 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c260 x19: x19 x20: x20
STACK CFI INIT c268 19f4 .cfa: sp 0 + .ra: x30
STACK CFI c26c .cfa: sp 1120 +
STACK CFI c274 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI c290 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI c2a4 x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI c2bc x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI c2c4 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI c2c8 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI c568 x19: x19 x20: x20
STACK CFI c570 x23: x23 x24: x24
STACK CFI c574 x25: x25 x26: x26
STACK CFI c57c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI c798 x19: x19 x20: x20
STACK CFI c79c x23: x23 x24: x24
STACK CFI c7a0 x25: x25 x26: x26
STACK CFI c7fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI c800 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^ x29: .cfa -1120 + ^
STACK CFI db44 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI db54 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI db90 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI db94 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI db98 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI db9c x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI dba8 x19: x19 x20: x20
STACK CFI dbac x23: x23 x24: x24
STACK CFI dbb0 x25: x25 x26: x26
STACK CFI dbb4 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI INIT dc60 c8 .cfa: sp 0 + .ra: x30
STACK CFI dc64 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI dc94 x19: .cfa -288 + ^
STACK CFI dd20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dd24 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT dd28 bc .cfa: sp 0 + .ra: x30
STACK CFI dd2c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI dd58 x19: .cfa -272 + ^
STACK CFI dddc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dde0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT dde8 40 .cfa: sp 0 + .ra: x30
STACK CFI ddec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI de24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT de28 ec .cfa: sp 0 + .ra: x30
STACK CFI de2c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI de3c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI de4c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ded8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dedc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT df18 a0 .cfa: sp 0 + .ra: x30
STACK CFI df1c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI df2c x19: .cfa -272 + ^
STACK CFI dfb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dfb4 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT dfb8 160 .cfa: sp 0 + .ra: x30
STACK CFI dfbc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI dfc4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI dfd4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI e010 x23: .cfa -96 + ^
STACK CFI e060 x23: x23
STACK CFI e084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e088 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI e0cc x23: x23
STACK CFI e0d8 x23: .cfa -96 + ^
STACK CFI e0e8 x23: x23
STACK CFI e0f0 x23: .cfa -96 + ^
STACK CFI INIT e118 a0 .cfa: sp 0 + .ra: x30
STACK CFI e11c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI e12c x19: .cfa -272 + ^
STACK CFI e1b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e1b4 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT e1b8 bc .cfa: sp 0 + .ra: x30
STACK CFI e1bc .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI e1e4 x19: .cfa -288 + ^
STACK CFI e26c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e270 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT e278 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT e338 1dc .cfa: sp 0 + .ra: x30
STACK CFI e33c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e378 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e3bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e3c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT e518 2a8 .cfa: sp 0 + .ra: x30
STACK CFI e51c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e530 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e5cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e5d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI e628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e62c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT e7c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e7f0 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT e870 90 .cfa: sp 0 + .ra: x30
STACK CFI e874 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e8b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e8b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT e900 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e908 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e910 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e918 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e928 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e930 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e938 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e940 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e948 c .cfa: sp 0 + .ra: x30
STACK CFI e94c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT e958 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e960 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e968 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e970 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e978 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e988 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e998 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e9f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea08 2c .cfa: sp 0 + .ra: x30
STACK CFI ea10 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ea30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ea38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eaa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eaa8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eab8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eac8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ead0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ead8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eae8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eaf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eaf8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eb90 38 .cfa: sp 0 + .ra: x30
STACK CFI eb98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ebc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ebc8 38 .cfa: sp 0 + .ra: x30
STACK CFI ebd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ebfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ec00 bc .cfa: sp 0 + .ra: x30
STACK CFI ec04 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI ec14 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI ecb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ecb8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT ecc0 bc .cfa: sp 0 + .ra: x30
STACK CFI ecc4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI ecd4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI ed74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ed78 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT ed80 ac .cfa: sp 0 + .ra: x30
STACK CFI ed84 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI edb0 x19: .cfa -272 + ^
STACK CFI ee24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ee28 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT ee30 ac .cfa: sp 0 + .ra: x30
STACK CFI ee34 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI ee60 x19: .cfa -272 + ^
STACK CFI eed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI eed8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT eee0 9c .cfa: sp 0 + .ra: x30
STACK CFI eee4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI eef4 x19: .cfa -256 + ^
STACK CFI ef74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ef78 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT ef80 9c .cfa: sp 0 + .ra: x30
STACK CFI ef84 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI ef94 x19: .cfa -256 + ^
STACK CFI f014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f018 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT f020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f028 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT f040 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f048 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f058 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f068 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f070 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f078 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f080 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f088 a0 .cfa: sp 0 + .ra: x30
STACK CFI f08c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f09c x19: .cfa -272 + ^
STACK CFI f120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f124 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT f128 2c .cfa: sp 0 + .ra: x30
STACK CFI f130 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f150 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f158 bc .cfa: sp 0 + .ra: x30
STACK CFI f15c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f184 x19: .cfa -288 + ^
STACK CFI f20c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f210 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT f218 70 .cfa: sp 0 + .ra: x30
STACK CFI f21c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f234 x19: .cfa -64 + ^
STACK CFI f280 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f284 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT f288 a0 .cfa: sp 0 + .ra: x30
STACK CFI f28c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f29c x19: .cfa -272 + ^
STACK CFI f320 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f324 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT f328 2c .cfa: sp 0 + .ra: x30
STACK CFI f330 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f350 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f358 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f368 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f378 bc .cfa: sp 0 + .ra: x30
STACK CFI f37c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f384 x19: .cfa -288 + ^
STACK CFI f41c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f420 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT f438 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f448 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f450 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f458 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f460 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f468 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f478 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f480 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f488 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f490 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f498 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f4f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI f4f4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f504 x19: .cfa -272 + ^
STACK CFI f588 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f58c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT f590 2c .cfa: sp 0 + .ra: x30
STACK CFI f598 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f5b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f5c0 2c .cfa: sp 0 + .ra: x30
STACK CFI f5c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f5e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f5f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f5f8 b0 .cfa: sp 0 + .ra: x30
STACK CFI f5fc .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f624 x19: .cfa -288 + ^
STACK CFI f6a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f6a4 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT f6a8 b0 .cfa: sp 0 + .ra: x30
STACK CFI f6ac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f6d4 x19: .cfa -288 + ^
STACK CFI f750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f754 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT f758 90 .cfa: sp 0 + .ra: x30
STACK CFI f75c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT f7e8 90 .cfa: sp 0 + .ra: x30
STACK CFI f7ec .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT f878 b0 .cfa: sp 0 + .ra: x30
STACK CFI f87c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f8a4 x19: .cfa -288 + ^
STACK CFI f920 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f924 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT f928 bc .cfa: sp 0 + .ra: x30
STACK CFI f92c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI f95c x19: .cfa -272 + ^
STACK CFI f9dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f9e0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT f9e8 b4 .cfa: sp 0 + .ra: x30
STACK CFI f9ec .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI f9fc x19: .cfa -288 + ^
STACK CFI fa94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fa98 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT faa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT faa8 a0 .cfa: sp 0 + .ra: x30
STACK CFI faac .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI fabc x19: .cfa -272 + ^
STACK CFI fb40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fb44 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT fb48 a4 .cfa: sp 0 + .ra: x30
STACK CFI fb4c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI fb5c x19: .cfa -288 + ^
STACK CFI fbe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fbe8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT fbf0 c .cfa: sp 0 + .ra: x30
STACK CFI fbf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT fc00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc38 2c .cfa: sp 0 + .ra: x30
STACK CFI fc3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fc54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI fc58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fc60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fc68 64 .cfa: sp 0 + .ra: x30
STACK CFI fc6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fc78 x19: .cfa -16 + ^
STACK CFI fca0 x19: x19
STACK CFI fca4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI fca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI fcac x19: x19
STACK CFI fcbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI fcc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI fcc8 x19: x19
STACK CFI INIT fcd0 bc .cfa: sp 0 + .ra: x30
STACK CFI fcd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fcf8 x21: .cfa -16 + ^
STACK CFI fd3c x21: x21
STACK CFI fd40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI fd50 x21: x21
STACK CFI fd54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI fd64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI fd7c x21: x21
STACK CFI fd80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT fd90 ec .cfa: sp 0 + .ra: x30
STACK CFI fd94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fdb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI fdbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fe78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fe80 20 .cfa: sp 0 + .ra: x30
STACK CFI fe84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fe94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI fe98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fe9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fea0 68 .cfa: sp 0 + .ra: x30
STACK CFI fea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI feac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ff04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ff08 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff20 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT ff40 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff58 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff70 4c .cfa: sp 0 + .ra: x30
STACK CFI ff74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ff7c x19: .cfa -16 + ^
STACK CFI ffb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ffb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT ffc0 218 .cfa: sp 0 + .ra: x30
STACK CFI ffc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ffcc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ffdc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ffe4 x23: .cfa -80 + ^
STACK CFI 10044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10048 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 101d8 354 .cfa: sp 0 + .ra: x30
STACK CFI 101dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 101e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 101ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 101fc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10228 x25: .cfa -32 + ^
STACK CFI 102a8 x25: x25
STACK CFI 102d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 102d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 10414 x25: x25
STACK CFI 10418 x25: .cfa -32 + ^
STACK CFI 10478 x25: x25
STACK CFI 1047c x25: .cfa -32 + ^
STACK CFI 1049c x25: x25
STACK CFI 104a0 x25: .cfa -32 + ^
STACK CFI 10524 x25: x25
STACK CFI 10528 x25: .cfa -32 + ^
STACK CFI INIT 10530 5e8 .cfa: sp 0 + .ra: x30
STACK CFI 10534 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1053c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 10548 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 10564 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1056c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 10684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10688 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI INIT 10b18 208 .cfa: sp 0 + .ra: x30
STACK CFI 10b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10b28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10b34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10bcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10c2c x23: x23 x24: x24
STACK CFI 10c38 x19: x19 x20: x20
STACK CFI 10c40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 10c44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 10c54 x19: x19 x20: x20
STACK CFI 10c5c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 10c60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 10ce4 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 10cfc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10d00 x19: x19 x20: x20
STACK CFI 10d04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10d0c x19: x19 x20: x20
STACK CFI 10d10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10d1c x19: x19 x20: x20
STACK CFI INIT 10d20 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d40 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d68 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d78 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d88 60 .cfa: sp 0 + .ra: x30
STACK CFI 10d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10d94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10de8 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e50 5c .cfa: sp 0 + .ra: x30
STACK CFI 10e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10e60 x19: .cfa -16 + ^
STACK CFI 10e98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10eb0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10ec8 9c .cfa: sp 0 + .ra: x30
STACK CFI 10ecc .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT 10f68 d0 .cfa: sp 0 + .ra: x30
STACK CFI 10f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10f7c x19: .cfa -16 + ^
STACK CFI 10f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10fdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10fe0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10fec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 11010 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1101c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11038 5c .cfa: sp 0 + .ra: x30
STACK CFI 1103c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11044 x19: .cfa -16 + ^
STACK CFI 11058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1105c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11098 47c .cfa: sp 0 + .ra: x30
STACK CFI 1109c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 110bc x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 110c4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 110d0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 110d8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 113dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 113e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 11518 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1151c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 11548 x19: .cfa -272 + ^
STACK CFI 115c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 115c4 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 115c8 3c .cfa: sp 0 + .ra: x30
STACK CFI 115d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11608 3c .cfa: sp 0 + .ra: x30
STACK CFI 11610 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11648 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1164c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 11674 x19: .cfa -272 + ^
STACK CFI 116f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 116f8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 11700 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11708 bc .cfa: sp 0 + .ra: x30
STACK CFI 1170c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 11734 x19: .cfa -288 + ^
STACK CFI 117bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 117c0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 117c8 bc .cfa: sp 0 + .ra: x30
STACK CFI 117cc .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 117f4 x19: .cfa -288 + ^
STACK CFI 1187c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11880 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 11888 9c .cfa: sp 0 + .ra: x30
STACK CFI 1188c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI INIT 11928 bc .cfa: sp 0 + .ra: x30
STACK CFI 1192c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 11954 x19: .cfa -288 + ^
STACK CFI 119dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 119e0 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 119e8 bc .cfa: sp 0 + .ra: x30
STACK CFI 119ec .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 11a1c x19: .cfa -272 + ^
STACK CFI 11a9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11aa0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 11aa8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 11aac .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 11abc x19: .cfa -288 + ^
STACK CFI 11b60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11b64 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 11b68 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b78 164 .cfa: sp 0 + .ra: x30
STACK CFI 11b7c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11b84 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11b90 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11bac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11bb0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 11c04 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 11c44 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11c48 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 11c50 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 11c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 11c68 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 11c74 x21: x21 x22: x22
STACK CFI 11c78 x25: x25 x26: x26
STACK CFI 11c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 11c8c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 11c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 11ca0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11ce0 cc .cfa: sp 0 + .ra: x30
STACK CFI 11ce4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 11cec x19: .cfa -272 + ^
STACK CFI 11d80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11d84 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 11db0 44 .cfa: sp 0 + .ra: x30
STACK CFI 11db8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11df8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 11dfc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 11e28 x19: .cfa -320 + ^
STACK CFI 11eb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11ebc .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 11ec0 28 .cfa: sp 0 + .ra: x30
STACK CFI 11ed8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11ee8 90 .cfa: sp 0 + .ra: x30
STACK CFI 11eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11ef8 x19: .cfa -16 + ^
STACK CFI 11f1c x19: x19
STACK CFI 11f30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11f50 x19: .cfa -16 + ^
STACK CFI 11f58 x19: x19
STACK CFI 11f60 x19: .cfa -16 + ^
STACK CFI 11f74 x19: x19
STACK CFI INIT 11f78 55c .cfa: sp 0 + .ra: x30
STACK CFI 11f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11f84 x19: .cfa -16 + ^
STACK CFI 121c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 121cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 12318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1231c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 124d8 70 .cfa: sp 0 + .ra: x30
STACK CFI 124dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 124e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1250c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12510 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12548 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1254c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12554 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12560 x21: .cfa -16 + ^
STACK CFI 125d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 125d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 125f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 125f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12608 300 .cfa: sp 0 + .ra: x30
STACK CFI 1260c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 12614 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1261c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 12628 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12644 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12680 x21: x21 x22: x22
STACK CFI 126b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 126b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 126c0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 12824 x21: x21 x22: x22
STACK CFI 1282c x23: x23 x24: x24
STACK CFI 12830 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12834 x21: x21 x22: x22
STACK CFI 12838 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 128a8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 128c8 x23: x23 x24: x24
STACK CFI 128d0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 128dc x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 128e0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 128e4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 12908 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 1290c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 12914 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 12930 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12950 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12a64 x23: x23 x24: x24
STACK CFI 12a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12aa0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 12aac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12b68 x23: x23 x24: x24
STACK CFI 12b74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12bcc x23: x23 x24: x24
STACK CFI 12bd0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 12bf8 3c .cfa: sp 0 + .ra: x30
STACK CFI 12bfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12c04 x19: .cfa -16 + ^
STACK CFI 12c30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c38 b4 .cfa: sp 0 + .ra: x30
STACK CFI 12c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12c44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12cd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12cf0 134 .cfa: sp 0 + .ra: x30
STACK CFI 12cf4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 12cfc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 12d1c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 12d2c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 12d4c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 12dd4 x23: x23 x24: x24
STACK CFI 12dd8 x25: x25 x26: x26
STACK CFI 12dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12e00 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 12e18 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 12e1c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 12e20 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 12e28 198 .cfa: sp 0 + .ra: x30
STACK CFI 12e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12e38 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12e44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12e84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 12ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 12f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12f68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 12f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12fa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 12fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 12fc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 12fe0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12ff4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13004 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13008 59c .cfa: sp 0 + .ra: x30
STACK CFI 1300c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 13014 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 13020 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 13028 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 13110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13114 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 1315c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 13170 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 13214 x23: x23 x24: x24
STACK CFI 132f4 x21: x21 x22: x22
STACK CFI 132fc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 13314 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 133f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 13408 x21: x21 x22: x22
STACK CFI 13440 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 13444 x21: x21 x22: x22
STACK CFI 13454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13458 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 13580 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 13584 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 13590 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1359c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 135a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 135a8 1cc .cfa: sp 0 + .ra: x30
STACK CFI 135ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 135b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 135e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 135ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 13620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13624 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 13690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13694 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 136b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 136b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13778 16c .cfa: sp 0 + .ra: x30
STACK CFI 1377c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1378c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 138e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 138e8 618 .cfa: sp 0 + .ra: x30
STACK CFI 138ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 138fc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 13918 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 13924 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 13f00 1084 .cfa: sp 0 + .ra: x30
STACK CFI 13f04 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 13f0c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 13f38 x23: .cfa -352 + ^ x24: .cfa -344 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 13f60 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 13f64 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14278 x21: x21 x22: x22
STACK CFI 1427c x25: x25 x26: x26
STACK CFI 14284 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14310 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 14348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 1434c .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI 14380 x21: x21 x22: x22
STACK CFI 14384 x25: x25 x26: x26
STACK CFI 14388 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 1439c x21: x21 x22: x22
STACK CFI 143a0 x25: x25 x26: x26
STACK CFI 143bc x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 143c4 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 14430 x25: x25 x26: x26
STACK CFI 1443c x21: x21 x22: x22
STACK CFI 14448 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 145e8 x21: x21 x22: x22
STACK CFI 145ec x25: x25 x26: x26
STACK CFI 145f4 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14644 x21: x21 x22: x22
STACK CFI 14648 x25: x25 x26: x26
STACK CFI 14650 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14694 x21: x21 x22: x22
STACK CFI 14698 x25: x25 x26: x26
STACK CFI 146a0 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 146b0 x21: x21 x22: x22
STACK CFI 146b4 x25: x25 x26: x26
STACK CFI 146bc x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14754 x21: x21 x22: x22
STACK CFI 14758 x25: x25 x26: x26
STACK CFI 1475c x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14760 x21: x21 x22: x22
STACK CFI 14764 x25: x25 x26: x26
STACK CFI 1476c x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14780 x21: x21 x22: x22
STACK CFI 14784 x25: x25 x26: x26
STACK CFI 1478c x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14898 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 148a8 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14918 x21: x21 x22: x22
STACK CFI 1491c x25: x25 x26: x26
STACK CFI 14924 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 14ea4 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 14ea8 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 14eac x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI INIT 14f88 14c .cfa: sp 0 + .ra: x30
STACK CFI 14f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14f94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14fb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15018 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 150d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 150e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 150f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15108 10 .cfa: sp 0 + .ra: x30
