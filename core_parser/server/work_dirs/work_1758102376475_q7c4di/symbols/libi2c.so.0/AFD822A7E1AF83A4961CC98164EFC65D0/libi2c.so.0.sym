MODULE Linux arm64 AFD822A7E1AF83A4961CC98164EFC65D0 libi2c.so.0
INFO CODE_ID A722D8AFAFE1A483961CC98164EFC65D8287AD6E
PUBLIC 9e8 0 i2c_smbus_access
PUBLIC a60 0 i2c_smbus_write_quick
PUBLIC a70 0 i2c_smbus_read_byte
PUBLIC ad0 0 i2c_smbus_write_byte
PUBLIC ae8 0 i2c_smbus_read_byte_data
PUBLIC b48 0 i2c_smbus_write_byte_data
PUBLIC ba0 0 i2c_smbus_read_word_data
PUBLIC c00 0 i2c_smbus_write_word_data
PUBLIC c58 0 i2c_smbus_process_call
PUBLIC cc8 0 i2c_smbus_read_block_data
PUBLIC d60 0 i2c_smbus_write_block_data
PUBLIC e00 0 i2c_smbus_read_i2c_block_data
PUBLIC ec8 0 i2c_smbus_write_i2c_block_data
PUBLIC f68 0 i2c_smbus_block_process_call
STACK CFI INIT 928 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 958 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 998 48 .cfa: sp 0 + .ra: x30
STACK CFI 99c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9a4 x19: .cfa -16 + ^
STACK CFI 9dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e8 74 .cfa: sp 0 + .ra: x30
STACK CFI 9ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f4 x19: .cfa -48 + ^
STACK CFI a54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a70 60 .cfa: sp 0 + .ra: x30
STACK CFI a74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a84 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI acc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT ad0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae8 60 .cfa: sp 0 + .ra: x30
STACK CFI aec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI afc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT b48 58 .cfa: sp 0 + .ra: x30
STACK CFI b4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b58 x19: .cfa -64 + ^
STACK CFI b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b9c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT ba0 60 .cfa: sp 0 + .ra: x30
STACK CFI ba4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bfc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT c00 58 .cfa: sp 0 + .ra: x30
STACK CFI c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c10 x19: .cfa -64 + ^
STACK CFI c50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c58 6c .cfa: sp 0 + .ra: x30
STACK CFI c5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c68 x19: .cfa -64 + ^
STACK CFI cb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI cb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT cc8 98 .cfa: sp 0 + .ra: x30
STACK CFI ccc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cd8 x21: .cfa -64 + ^
STACK CFI ce0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT d60 9c .cfa: sp 0 + .ra: x30
STACK CFI d64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d74 x19: .cfa -64 + ^
STACK CFI dec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI df0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT e00 c4 .cfa: sp 0 + .ra: x30
STACK CFI e04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e14 x21: .cfa -80 + ^
STACK CFI e28 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI eac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT ec8 9c .cfa: sp 0 + .ra: x30
STACK CFI ecc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI edc x19: .cfa -64 + ^
STACK CFI f54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT f68 d8 .cfa: sp 0 + .ra: x30
STACK CFI f6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f7c x21: .cfa -64 + ^
STACK CFI f84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 103c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
