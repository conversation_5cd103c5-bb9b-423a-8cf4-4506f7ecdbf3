MODULE Linux arm64 7B09FAE328D7C76B937086FC30EFBA760 libnss_compat.so.2
INFO CODE_ID E3FA097BD7286BC7937086FC30EFBA767450259C
PUBLIC 1938 0 _nss_compat_setgrent
PUBLIC 19b8 0 _nss_compat_endgrent
PUBLIC 1a28 0 _nss_compat_getgrent_r
PUBLIC 1dc0 0 _nss_compat_getgrnam_r
PUBLIC 2050 0 _nss_compat_getgrgid_r
PUBLIC 34f8 0 _nss_compat_setpwent
PUBLIC 3578 0 _nss_compat_endpwent
PUBLIC 35e8 0 _nss_compat_getpwent_r
PUBLIC 3738 0 _nss_compat_getpwnam_r
PUBLIC 3ab8 0 _nss_compat_getpwuid_r
PUBLIC 4ed8 0 _nss_compat_setspent
PUBLIC 4f58 0 _nss_compat_endspent
PUBLIC 4fc8 0 _nss_compat_getspent_r
PUBLIC 5108 0 _nss_compat_getspnam_r
PUBLIC 5b70 0 _nss_compat_initgroups_dyn
STACK CFI INIT 1358 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1388 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 13cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13d4 x19: .cfa -16 + ^
STACK CFI 140c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1410 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1418 64 .cfa: sp 0 + .ra: x30
STACK CFI 141c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1424 x19: .cfa -16 + ^
STACK CFI 1464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1468 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1480 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1494 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1538 dc .cfa: sp 0 + .ra: x30
STACK CFI 153c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1548 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1558 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1618 f0 .cfa: sp 0 + .ra: x30
STACK CFI 161c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1628 .cfa: x29 80 +
STACK CFI 162c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1658 x21: .cfa -48 + ^
STACK CFI 16f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16fc .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1708 108 .cfa: sp 0 + .ra: x30
STACK CFI 170c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1714 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1720 x21: .cfa -16 + ^
STACK CFI 1798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 179c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1810 7c .cfa: sp 0 + .ra: x30
STACK CFI 1820 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1828 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1854 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1890 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 189c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 18c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1938 7c .cfa: sp 0 + .ra: x30
STACK CFI 193c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 194c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 19ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 19bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a28 398 .cfa: sp 0 + .ra: x30
STACK CFI 1a2c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1a34 .cfa: x29 160 +
STACK CFI 1a38 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1a48 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1a5c x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1a68 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1b20 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1dc0 290 .cfa: sp 0 + .ra: x30
STACK CFI 1dc4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1dcc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1dd8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1df4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 1e24 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1e30 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 1e9c x23: x23 x24: x24
STACK CFI 1ea0 x25: x25 x26: x26
STACK CFI 1ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 1ed0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 1ff0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1ff4 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1ff8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 2050 354 .cfa: sp 0 + .ra: x30
STACK CFI 2054 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2060 .cfa: x29 208 +
STACK CFI 2064 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 207c x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2094 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 214c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2150 .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 23a8 50 .cfa: sp 0 + .ra: x30
STACK CFI 23ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23b4 x19: .cfa -16 + ^
STACK CFI 23f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23f8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 240c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 241c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 248c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2490 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 24f0 94 .cfa: sp 0 + .ra: x30
STACK CFI 24f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24fc x19: .cfa -16 + ^
STACK CFI 2550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2588 70 .cfa: sp 0 + .ra: x30
STACK CFI 258c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2594 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 25f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25f8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 25fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 260c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26a8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 26ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26b8 .cfa: x29 64 +
STACK CFI 26bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26e8 x21: .cfa -32 + ^
STACK CFI 2780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2784 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2790 250 .cfa: sp 0 + .ra: x30
STACK CFI 2794 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 279c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 27a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 27b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 290c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2910 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2984 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 29a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 29a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 29e0 164 .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 29f0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 29fc x27: .cfa -80 + ^
STACK CFI 2a10 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2a18 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2a24 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2a8c x23: x23 x24: x24
STACK CFI 2a90 x25: x25 x26: x26
STACK CFI 2abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x29: x29
STACK CFI 2ac0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI 2aec x23: x23 x24: x24
STACK CFI 2af0 x25: x25 x26: x26
STACK CFI 2af4 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2b10 x23: x23 x24: x24
STACK CFI 2b14 x25: x25 x26: x26
STACK CFI 2b18 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2b24 x23: x23 x24: x24
STACK CFI 2b2c x25: x25 x26: x26
STACK CFI 2b3c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2b40 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT 2b48 124 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2b58 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2b64 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2b78 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2b80 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2bf4 x23: x23 x24: x24
STACK CFI 2c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2c28 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 2c44 x23: x23 x24: x24
STACK CFI 2c48 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2c54 x23: x23 x24: x24
STACK CFI 2c68 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 2c70 114 .cfa: sp 0 + .ra: x30
STACK CFI 2c74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c80 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c8c x27: .cfa -16 + ^
STACK CFI 2c9c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2cbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d20 x25: x25 x26: x26
STACK CFI 2d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 2d3c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2d68 x25: x25 x26: x26
STACK CFI 2d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 2d74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2d88 110 .cfa: sp 0 + .ra: x30
STACK CFI 2d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e98 270 .cfa: sp 0 + .ra: x30
STACK CFI 2e9c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2ea8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2ed0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2edc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2ee4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2ef0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 304c x19: x19 x20: x20
STACK CFI 3050 x21: x21 x22: x22
STACK CFI 3054 x23: x23 x24: x24
STACK CFI 3058 x25: x25 x26: x26
STACK CFI 3084 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 3088 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 30c4 x21: x21 x22: x22
STACK CFI 30c8 x23: x23 x24: x24
STACK CFI 30cc x25: x25 x26: x26
STACK CFI 30d4 x19: x19 x20: x20
STACK CFI 30d8 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 30ec x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 30f8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 30fc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3100 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3104 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 3108 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 310c .cfa: sp 1312 +
STACK CFI 3110 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI 3114 .cfa: x29 1296 +
STACK CFI 3118 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI 3128 x19: .cfa -1280 + ^ x20: .cfa -1272 + ^
STACK CFI 3134 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI 313c x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 3148 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI 32c4 .cfa: sp 1312 +
STACK CFI 32e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 32e8 .cfa: x29 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x28: .cfa -1208 + ^ x29: .cfa -1296 + ^
STACK CFI INIT 34f8 80 .cfa: sp 0 + .ra: x30
STACK CFI 34fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 350c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 356c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3578 70 .cfa: sp 0 + .ra: x30
STACK CFI 357c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 358c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35e8 14c .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3600 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 360c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3618 x25: .cfa -16 + ^
STACK CFI 36a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 36ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3738 380 .cfa: sp 0 + .ra: x30
STACK CFI 373c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 374c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3758 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 376c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3778 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 37c8 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 383c x25: x25 x26: x26
STACK CFI 386c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3870 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 399c x25: x25 x26: x26
STACK CFI 39a0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI INIT 3ab8 57c .cfa: sp 0 + .ra: x30
STACK CFI 3abc .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3ac8 .cfa: x29 336 +
STACK CFI 3acc x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 3ae4 x19: .cfa -320 + ^ x20: .cfa -312 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 3afc x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 3bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3be0 .cfa: x29 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI INIT 4038 44 .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4044 x19: .cfa -16 + ^
STACK CFI 4078 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4080 f0 .cfa: sp 0 + .ra: x30
STACK CFI 4084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4090 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4114 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4170 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 417c x19: .cfa -16 + ^
STACK CFI 41d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4210 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4218 9c .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 422c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42b8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42c8 .cfa: x29 64 +
STACK CFI 42cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42f8 x21: .cfa -32 + ^
STACK CFI 4390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4394 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43a0 10c .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 443c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4448 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4480 x23: x23 x24: x24
STACK CFI 4484 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4498 x23: x23 x24: x24
STACK CFI INIT 44b0 17c .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 44c0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 44cc x27: .cfa -96 + ^
STACK CFI 44e0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 44f0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 450c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4574 x23: x23 x24: x24
STACK CFI 4578 x25: x25 x26: x26
STACK CFI 45a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x29: x29
STACK CFI 45a8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI 45d4 x23: x23 x24: x24
STACK CFI 45d8 x25: x25 x26: x26
STACK CFI 45dc x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 45f8 x23: x23 x24: x24
STACK CFI 45fc x25: x25 x26: x26
STACK CFI 4600 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 460c x25: x25 x26: x26
STACK CFI 4614 x23: x23 x24: x24
STACK CFI 4624 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4628 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 4630 130 .cfa: sp 0 + .ra: x30
STACK CFI 4634 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 463c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4648 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4654 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4660 x27: .cfa -16 + ^
STACK CFI 466c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 46f0 x21: x21 x22: x22
STACK CFI 46f4 x23: x23 x24: x24
STACK CFI 46f8 x27: x27
STACK CFI 4708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 470c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 4728 x21: x21 x22: x22
STACK CFI 472c x23: x23 x24: x24
STACK CFI 4734 x27: x27
STACK CFI 4738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 473c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 4748 x21: x21 x22: x22
STACK CFI 474c x27: x27
STACK CFI 4754 x23: x23 x24: x24
STACK CFI INIT 4760 110 .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 476c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4774 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 47f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4870 298 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4880 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 48a8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 48ac x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 48b8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 48d4 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 49f8 x23: x23 x24: x24
STACK CFI 49fc x19: x19 x20: x20
STACK CFI 4a00 x21: x21 x22: x22
STACK CFI 4a04 x25: x25 x26: x26
STACK CFI 4a2c .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 4a30 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 4a6c x19: x19 x20: x20
STACK CFI 4a70 x21: x21 x22: x22
STACK CFI 4a74 x23: x23 x24: x24
STACK CFI 4a78 x25: x25 x26: x26
STACK CFI 4a7c x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4ab8 x21: x21 x22: x22
STACK CFI 4abc x23: x23 x24: x24
STACK CFI 4ac0 x25: x25 x26: x26
STACK CFI 4ac8 x19: x19 x20: x20
STACK CFI 4acc x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4aec x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4af8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4afc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4b00 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4b04 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 4b08 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 4b0c .cfa: sp 1312 +
STACK CFI 4b10 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI 4b14 .cfa: x29 1296 +
STACK CFI 4b18 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI 4b28 x19: .cfa -1280 + ^ x20: .cfa -1272 + ^
STACK CFI 4b34 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI 4b3c x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 4b48 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI 4ca8 .cfa: sp 1312 +
STACK CFI 4cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4ccc .cfa: x29 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x28: .cfa -1208 + ^ x29: .cfa -1296 + ^
STACK CFI INIT 4ed8 80 .cfa: sp 0 + .ra: x30
STACK CFI 4edc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4eec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f58 70 .cfa: sp 0 + .ra: x30
STACK CFI 4f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4fc8 140 .cfa: sp 0 + .ra: x30
STACK CFI 4fcc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4fe0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4fec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ff8 x25: .cfa -16 + ^
STACK CFI 5084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5088 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5108 394 .cfa: sp 0 + .ra: x30
STACK CFI 510c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 511c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 5124 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 512c x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 51a0 x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 5214 x27: x27 x28: x28
STACK CFI 5240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5244 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI 524c x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5310 x25: x25 x26: x26
STACK CFI 5314 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5334 x25: x25 x26: x26
STACK CFI 5344 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5378 x25: x25 x26: x26
STACK CFI 537c x27: x27 x28: x28
STACK CFI 5380 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5384 x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 53e4 x25: x25 x26: x26
STACK CFI 53e8 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5424 x25: x25 x26: x26
STACK CFI 5428 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 5474 x25: x25 x26: x26
STACK CFI 5478 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI INIT 54a0 98 .cfa: sp 0 + .ra: x30
STACK CFI 54a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 54ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 54bc x21: .cfa -32 + ^
STACK CFI 54e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 54e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5538 e8 .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5548 .cfa: x29 64 +
STACK CFI 554c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5578 x21: .cfa -32 + ^
STACK CFI 5610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5614 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5620 108 .cfa: sp 0 + .ra: x30
STACK CFI 5624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 562c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5638 x21: .cfa -16 + ^
STACK CFI 56b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 56b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5728 b8 .cfa: sp 0 + .ra: x30
STACK CFI 572c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5738 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5748 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5750 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5760 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 57a8 x21: x21 x22: x22
STACK CFI 57ac x23: x23 x24: x24
STACK CFI 57b0 x25: x25 x26: x26
STACK CFI 57bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 57d0 x21: x21 x22: x22
STACK CFI 57d4 x23: x23 x24: x24
STACK CFI 57d8 x25: x25 x26: x26
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 57e0 38c .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 57ec x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 57fc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5818 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 5824 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 58c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 58cc .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 5b70 570 .cfa: sp 0 + .ra: x30
STACK CFI 5b74 .cfa: sp 1312 +
STACK CFI 5b80 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI 5b8c x19: .cfa -1280 + ^ x20: .cfa -1272 + ^
STACK CFI 5ba8 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI 5bb4 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI 5c08 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI 5c10 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 5e54 x23: x23 x24: x24
STACK CFI 5e58 x25: x25 x26: x26
STACK CFI 5e5c x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 5e60 x23: x23 x24: x24
STACK CFI 5e64 x25: x25 x26: x26
STACK CFI 5e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5e9c .cfa: sp 1312 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x27: .cfa -1216 + ^ x28: .cfa -1208 + ^ x29: .cfa -1296 + ^
STACK CFI 5f18 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 5f20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5fd8 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 60d4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 60d8 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI 60dc x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI INIT 60e0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 60e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6158 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
