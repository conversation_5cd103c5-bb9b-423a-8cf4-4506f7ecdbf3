MODULE Linux arm64 0692B348B0C31401DB37808E518DB0970 libopencv_intensity_transform.so.4.3
INFO CODE_ID 48B39206C3B00114DB37808E518DB0975ACEF369
PUBLIC db8 0 _init
PUBLIC f20 0 call_weak_fn
PUBLIC f38 0 deregister_tm_clones
PUBLIC f70 0 register_tm_clones
PUBLIC fb0 0 __do_global_dtors_aux
PUBLIC ff8 0 frame_dummy
PUBLIC 1030 0 cv::Mat::~Mat()
PUBLIC 10c0 0 cv::intensity_transform::logTransform(cv::Mat, cv::Mat&)
PUBLIC 1360 0 cv::intensity_transform::gammaCorrection(cv::Mat, cv::Mat&, float)
PUBLIC 1448 0 cv::MatExpr::~MatExpr()
PUBLIC 15f8 0 cv::intensity_transform::autoscaling(cv::Mat, cv::Mat&)
PUBLIC 1b88 0 cv::intensity_transform::contrastStretching(cv::Mat, cv::Mat&, int, int, int, int)
PUBLIC 1cf0 0 _fini
STACK CFI INIT 1030 90 .cfa: sp 0 + .ra: x30
STACK CFI 1034 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10a8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 10b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10bc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 10c0 27c .cfa: sp 0 + .ra: x30
STACK CFI 10c4 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 10cc v8: .cfa -272 + ^ v9: .cfa -264 + ^
STACK CFI 10d8 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 10e4 .ra: .cfa -288 + ^
STACK CFI 12f0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 12f8 .cfa: sp 320 + .ra: .cfa -288 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI INIT 1360 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1364 .cfa: sp 400 + x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 136c v8: .cfa -352 + ^ v9: .cfa -344 + ^
STACK CFI 137c x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 1388 .ra: .cfa -368 + ^
STACK CFI 1418 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1420 .cfa: sp 400 + .ra: .cfa -368 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI INIT 1448 1ac .cfa: sp 0 + .ra: x30
STACK CFI 144c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1458 .ra: .cfa -16 + ^
STACK CFI 15b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15b8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 15f8 584 .cfa: sp 0 + .ra: x30
STACK CFI 15fc .cfa: sp 1152 +
STACK CFI 1604 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^
STACK CFI 1614 .ra: .cfa -1112 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x23: .cfa -1120 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1ab8 .cfa: sp 1152 + .ra: .cfa -1112 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^
STACK CFI INIT 1b88 15c .cfa: sp 0 + .ra: x30
STACK CFI 1b8c .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 1b98 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 1ba8 x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 1bb4 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 1bcc .ra: .cfa -368 + ^
STACK CFI 1ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
