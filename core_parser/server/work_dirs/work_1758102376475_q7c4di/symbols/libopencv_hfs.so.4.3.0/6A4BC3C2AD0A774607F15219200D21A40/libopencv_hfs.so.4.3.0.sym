MODULE Linux arm64 6A4BC3C2AD0A774607F15219200D21A40 libopencv_hfs.so.4.3
INFO CODE_ID C2C34B6A0AAD467707F15219200D21A443142A4B
PUBLIC 28d8 0 _init
PUBLIC 2c30 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.21]
PUBLIC 2cd0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.108]
PUBLIC 2d70 0 _GLOBAL__sub_I_hfs_core.cpp
PUBLIC 2de4 0 call_weak_fn
PUBLIC 2df8 0 deregister_tm_clones
PUBLIC 2e30 0 register_tm_clones
PUBLIC 2e70 0 __do_global_dtors_aux
PUBLIC 2eb8 0 frame_dummy
PUBLIC 2ef0 0 cv::Algorithm::clear()
PUBLIC 2ef8 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 2f00 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 2f08 0 cv::Algorithm::empty() const
PUBLIC 2f10 0 cv::hfs::HfsSegmentImpl::setSegEgbThresholdI(float)
PUBLIC 2f20 0 cv::hfs::HfsSegmentImpl::getSegEgbThresholdI()
PUBLIC 2f30 0 cv::hfs::HfsSegmentImpl::setMinRegionSizeI(int)
PUBLIC 2f40 0 cv::hfs::HfsSegmentImpl::getMinRegionSizeI()
PUBLIC 2f50 0 cv::hfs::HfsSegmentImpl::setSegEgbThresholdII(float)
PUBLIC 2f60 0 cv::hfs::HfsSegmentImpl::getSegEgbThresholdII()
PUBLIC 2f70 0 cv::hfs::HfsSegmentImpl::setMinRegionSizeII(int)
PUBLIC 2f80 0 cv::hfs::HfsSegmentImpl::getMinRegionSizeII()
PUBLIC 2f90 0 cv::hfs::HfsSegmentImpl::getSpatialWeight()
PUBLIC 2fa0 0 cv::hfs::HfsSegmentImpl::getSlicSpixelSize()
PUBLIC 2fb0 0 cv::hfs::HfsSegmentImpl::getNumSlicIter()
PUBLIC 2fc0 0 std::_Sp_counted_ptr<cv::hfs::HfsSegmentImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 2fc8 0 std::_Sp_counted_ptr<cv::hfs::HfsCore*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 2fd0 0 std::_Sp_counted_ptr<cv::hfs::HfsSegmentImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 2fd8 0 std::_Sp_counted_ptr<cv::hfs::HfsCore*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 2fe0 0 std::_Sp_counted_ptr<cv::hfs::HfsCore*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 2fe8 0 std::_Sp_counted_ptr<cv::hfs::HfsCore*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 2ff0 0 std::_Sp_counted_ptr<cv::hfs::HfsSegmentImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 2ff8 0 std::_Sp_counted_ptr<cv::hfs::HfsSegmentImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 3000 0 std::_Sp_counted_ptr<cv::hfs::HfsCore*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 3028 0 cv::hfs::HfsSegmentImpl::setNumSlicIter(int)
PUBLIC 3038 0 cv::hfs::HfsSegmentImpl::setSlicSpixelSize(int)
PUBLIC 3048 0 cv::hfs::HfsSegmentImpl::setSpatialWeight(float)
PUBLIC 3058 0 cv::hfs::HfsSegmentImpl::~HfsSegmentImpl()
PUBLIC 3140 0 cv::hfs::HfsSegmentImpl::~HfsSegmentImpl()
PUBLIC 3220 0 std::_Sp_counted_ptr<cv::hfs::HfsSegmentImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 3320 0 cv::Mat::~Mat()
PUBLIC 33b0 0 cv::hfs::HfsSegmentImpl::performSegmentGpu(cv::_InputArray const&, bool)
PUBLIC 3840 0 cv::hfs::HfsSegmentImpl::performSegmentCpu(cv::_InputArray const&, bool)
PUBLIC 3cd0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 3d88 0 cv::hfs::HfsSegment::create(int, int, float, int, float, int, float, int, int)
PUBLIC 40a0 0 std::_Sp_counted_ptr<cv::hfs::Magnitude*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 40a8 0 std::_Sp_counted_ptr<cv::hfs::Magnitude*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 40b0 0 std::_Sp_counted_ptr<cv::hfs::Magnitude*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 40b8 0 std::_Sp_counted_ptr<cv::hfs::Magnitude*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 40c0 0 std::_Sp_counted_ptr<cv::hfs::Magnitude*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 40e8 0 cv::hfs::HfsCore::getColorFeature(cv::Vec<float, 3> const&, cv::Vec<float, 3> const&) [clone .constprop.176]
PUBLIC 41a0 0 cv::hfs::slic::cSLIC::~cSLIC()
PUBLIC 42e0 0 cv::hfs::HfsCore::constructEngine()
PUBLIC 44f0 0 cv::hfs::HfsCore::reconstructEngine()
PUBLIC 44f8 0 cv::hfs::HfsCore::~HfsCore()
PUBLIC 45e0 0 cv::hfs::HfsCore::getSLICIdxCpu(cv::Mat const&, int&)
PUBLIC 4b40 0 cv::hfs::HfsCore::drawSegmentationRes(cv::Mat const&, cv::Mat const&, int, cv::Mat&)
PUBLIC 5048 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::~vector()
PUBLIC 50a8 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 5168 0 std::vector<float, std::allocator<float> >::_M_default_append(unsigned long)
PUBLIC 52b8 0 cv::hfs::HfsCore::HfsCore(int, int, float, int, float, int, float, int, int)
PUBLIC 5408 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_default_append(unsigned long)
PUBLIC 5790 0 cv::hfs::HfsCore::getAvgGradientBdry(cv::Mat const&, std::vector<cv::Mat, std::allocator<cv::Mat> > const&, int, cv::Mat&, std::vector<cv::Mat, std::allocator<cv::Mat> >&)
PUBLIC 6140 0 void std::vector<float, std::allocator<float> >::_M_emplace_back_aux<float const&>(float const&)
PUBLIC 6228 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 6570 0 cv::hfs::HfsCore::getSegmentationII(cv::Mat const&, cv::Mat const&, cv::Mat const&, float, int, cv::Mat&, int&)
PUBLIC 7440 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int>(int&&)
PUBLIC 7528 0 cv::hfs::HfsCore::getSegmentationI(cv::Mat const&, cv::Mat const&, cv::Mat const&, float, int, cv::Mat&, int&)
PUBLIC 87c0 0 cv::hfs::HfsCore::processImageCpu(cv::Mat const&, cv::Mat&)
PUBLIC 8ca0 0 cv::hfs::HfsCore::processImageGpu(cv::Mat const&, cv::Mat&)
PUBLIC 8ca8 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<unsigned char>*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8cb0 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<int>*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8cb8 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<unsigned char>*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8cd8 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<unsigned char>*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8ce0 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<int>*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8d00 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<int>*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8d08 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<int>*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8d10 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<int>*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8d18 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<unsigned char>*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8d20 0 std::_Sp_counted_ptr<cv::hfs::orutils::Image<unsigned char>*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8d28 0 cv::hfs::orutils::MemoryBlock<int>::~MemoryBlock()
PUBLIC 8d50 0 cv::hfs::orutils::MemoryBlock<int>::~MemoryBlock()
PUBLIC 8d80 0 cv::hfs::orutils::Image<int>::~Image()
PUBLIC 8da8 0 cv::hfs::orutils::Image<int>::~Image()
PUBLIC 8dd8 0 cv::hfs::orutils::MemoryBlock<unsigned char>::~MemoryBlock()
PUBLIC 8e00 0 cv::hfs::orutils::MemoryBlock<unsigned char>::~MemoryBlock()
PUBLIC 8e30 0 cv::hfs::orutils::Image<unsigned char>::~Image()
PUBLIC 8e58 0 cv::hfs::orutils::Image<unsigned char>::~Image()
PUBLIC 8e88 0 cv::hfs::Magnitude::~Magnitude()
PUBLIC 91f0 0 cv::hfs::Magnitude::derrivativeXYCpu()
PUBLIC 93c8 0 cv::hfs::Magnitude::nonMaxSuppCpu()
PUBLIC 9810 0 cv::hfs::Magnitude::processImgCpu(cv::Mat const&, cv::Mat&)
PUBLIC a0f0 0 cv::hfs::Magnitude::Magnitude(int, int)
PUBLIC abe8 0 std::_Sp_counted_ptr<cv::hfs::RegionSet*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC abf0 0 std::_Sp_counted_ptr<cv::hfs::RegionSet*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC abf8 0 std::_Sp_counted_ptr<cv::hfs::RegionSet*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC ac00 0 std::_Sp_counted_ptr<cv::hfs::RegionSet*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC ac08 0 std::_Sp_counted_ptr<cv::hfs::RegionSet*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ac38 0 void std::__insertion_sort<__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, __gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC ad48 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, long, cv::hfs::Edge, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, long, long, cv::hfs::Edge, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC aec0 0 void std::__introsort_loop<__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, long, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, __gnu_cxx::__normal_iterator<cv::hfs::Edge*, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> > >, long, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.71]
PUBLIC b140 0 cv::hfs::egb_merge(int, int, std::vector<cv::hfs::Edge, std::allocator<cv::hfs::Edge> >&, float, std::vector<int, std::allocator<int> >)
PUBLIC b7c0 0 cv::hfs::slic::cSLIC::enforce_connect(int, int) [clone .constprop.48]
PUBLIC bb10 0 cv::hfs::slic::cSLIC::enforce_connect(int, int) [clone .constprop.49]
PUBLIC bd40 0 cv::hfs::slic::cSLIC::cvt_img_space()
PUBLIC c0b0 0 cv::hfs::slic::cSLIC::init_data(cv::Mat)
PUBLIC c5f8 0 cv::hfs::slic::cSLIC::find_association()
PUBLIC c9d0 0 cv::hfs::slic::cSLIC::update_cluster_center()
PUBLIC cbc0 0 cv::hfs::slic::cSLIC::generate_superpixels(cv::Mat, int, float)
PUBLIC cdc8 0 _fini
STACK CFI INIT 2ef0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f30 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fa0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fc8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fe0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fe8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3000 28 .cfa: sp 0 + .ra: x30
STACK CFI 3004 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 301c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3020 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3024 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3028 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3038 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3048 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c30 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2c34 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c40 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2cc4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 3058 e8 .cfa: sp 0 + .ra: x30
STACK CFI 305c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 306c .ra: .cfa -16 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 30c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3140 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3144 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3154 .ra: .cfa -16 + ^
STACK CFI 31a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 31a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3220 100 .cfa: sp 0 + .ra: x30
STACK CFI 3224 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3228 .ra: .cfa -16 + ^
STACK CFI 3290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3298 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 32a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3320 90 .cfa: sp 0 + .ra: x30
STACK CFI 3324 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 33a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 33ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 33b0 478 .cfa: sp 0 + .ra: x30
STACK CFI 33b4 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 33c0 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 33c8 .ra: .cfa -296 + ^ x23: .cfa -304 + ^
STACK CFI 3664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3668 .cfa: sp 336 + .ra: .cfa -296 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^
STACK CFI INIT 3840 478 .cfa: sp 0 + .ra: x30
STACK CFI 3844 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 3850 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 3858 .ra: .cfa -296 + ^ x23: .cfa -304 + ^
STACK CFI 3af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3af8 .cfa: sp 336 + .ra: .cfa -296 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^
STACK CFI INIT 3cd0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3cd8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ce4 .ra: .cfa -16 + ^
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3d10 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3d60 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3d88 314 .cfa: sp 0 + .ra: x30
STACK CFI 3d8c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3d98 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 3da4 v10: .cfa -16 + ^
STACK CFI 3dac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3dbc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3dc4 .ra: .cfa -40 + ^ x27: .cfa -48 + ^
STACK CFI 3f00 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3f08 .cfa: sp 112 + .ra: .cfa -40 + ^ v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI INIT 40a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 40c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 40e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 40e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2cd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2cd4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ce0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2d64 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 40e8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 40f0 .cfa: sp 96 + .ra: .cfa -96 + ^
STACK CFI 40f8 v8: .cfa -88 + ^
STACK CFI 418c .cfa: sp 0 + .ra: .ra v8: v8
STACK CFI 4190 .cfa: sp 96 + .ra: .cfa -96 + ^ v8: .cfa -88 + ^
STACK CFI INIT 41a0 13c .cfa: sp 0 + .ra: x30
STACK CFI 41a4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41ac .ra: .cfa -16 + ^
STACK CFI 42ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 42b0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 42e0 20c .cfa: sp 0 + .ra: x30
STACK CFI 42e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42f4 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 43ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 43b0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 446c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 4470 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 44f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44f8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 44fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4504 .ra: .cfa -16 + ^
STACK CFI 4550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4558 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 45a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 45e0 548 .cfa: sp 0 + .ra: x30
STACK CFI 45e8 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 45f0 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 460c .ra: .cfa -416 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 4a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4a08 .cfa: sp 496 + .ra: .cfa -416 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 4b40 508 .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4b4c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4b64 .ra: .cfa -112 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4f98 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 5048 5c .cfa: sp 0 + .ra: x30
STACK CFI 504c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5050 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 5090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5098 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 50a8 bc .cfa: sp 0 + .ra: x30
STACK CFI 50ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50b0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 5154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5158 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 5168 14c .cfa: sp 0 + .ra: x30
STACK CFI 5170 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5188 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 51c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 51d8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 5278 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 52b8 14c .cfa: sp 0 + .ra: x30
STACK CFI 52bc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 52c8 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 539c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 53a0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5408 384 .cfa: sp 0 + .ra: x30
STACK CFI 5410 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5428 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 5664 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 56c8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 56e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 56e4 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 5790 9a8 .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 57ac .ra: .cfa -304 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 60e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 60e8 .cfa: sp 384 + .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 6140 e8 .cfa: sp 0 + .ra: x30
STACK CFI 6144 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 614c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6158 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 61d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 61e0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 6228 344 .cfa: sp 0 + .ra: x30
STACK CFI 622c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6238 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6248 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 64a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 64a8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 6570 eb8 .cfa: sp 0 + .ra: x30
STACK CFI 6578 .cfa: sp 560 +
STACK CFI 6584 x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI 6594 x19: .cfa -560 + ^ x20: .cfa -552 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 659c x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 65b4 .ra: .cfa -480 + ^ v8: .cfa -472 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 6fa0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6fa8 .cfa: sp 560 + .ra: .cfa -480 + ^ v8: .cfa -472 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI INIT 7440 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7444 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 744c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7458 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 74d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 74e0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7528 128c .cfa: sp 0 + .ra: x30
STACK CFI 752c .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 7530 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 7538 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 754c .ra: .cfa -304 + ^ v8: .cfa -296 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 85a0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 85a4 .cfa: sp 384 + .ra: .cfa -304 + ^ v8: .cfa -296 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 87c0 4c8 .cfa: sp 0 + .ra: x30
STACK CFI 87c8 .cfa: sp 592 +
STACK CFI 87d0 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI 87d8 .ra: .cfa -520 + ^ x27: .cfa -528 + ^
STACK CFI 87e8 x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 87f0 x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI 8bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8bf0 .cfa: sp 592 + .ra: .cfa -520 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^
STACK CFI INIT 8ca0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d70 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ca8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cb8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8cd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ce0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d28 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d50 2c .cfa: sp 0 + .ra: x30
STACK CFI 8d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8d78 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8d80 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8da8 2c .cfa: sp 0 + .ra: x30
STACK CFI 8dac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8dd0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8dd8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e00 2c .cfa: sp 0 + .ra: x30
STACK CFI 8e04 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8e28 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8e30 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e58 2c .cfa: sp 0 + .ra: x30
STACK CFI 8e5c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8e80 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8e88 368 .cfa: sp 0 + .ra: x30
STACK CFI 8e8c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8e94 .ra: .cfa -16 + ^
STACK CFI 8f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8f90 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 90e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 90f0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 91f0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 91f4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9204 .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 92f0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 92f8 .cfa: sp 96 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI INIT 93c8 448 .cfa: sp 0 + .ra: x30
STACK CFI 943c .cfa: sp 16 +
STACK CFI 9578 .cfa: sp 0 +
STACK CFI 95a0 .cfa: sp 16 +
STACK CFI 96bc .cfa: sp 0 +
STACK CFI 96c0 .cfa: sp 16 +
STACK CFI 97c4 .cfa: sp 0 +
STACK CFI 97c8 .cfa: sp 16 +
STACK CFI INIT 9810 8c8 .cfa: sp 0 + .ra: x30
STACK CFI 9814 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 982c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 9838 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 985c .ra: .cfa -264 + ^ x25: .cfa -272 + ^
STACK CFI 9f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 9f10 .cfa: sp 320 + .ra: .cfa -264 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^
STACK CFI INIT a0f0 af8 .cfa: sp 0 + .ra: x30
STACK CFI a0f4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a100 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a108 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI a118 .ra: .cfa -32 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI a594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a598 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT abe8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT abf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT abf8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac08 30 .cfa: sp 0 + .ra: x30
STACK CFI ac0c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ac28 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI ac30 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ac34 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ac38 10c .cfa: sp 0 + .ra: x30
STACK CFI ac50 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ac5c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ac6c .ra: .cfa -24 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^
STACK CFI ac80 v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI ad04 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI ad08 .cfa: sp 96 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI ad40 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI INIT ad48 178 .cfa: sp 0 + .ra: x30
STACK CFI ad4c .cfa: sp 16 +
STACK CFI ae74 .cfa: sp 0 +
STACK CFI ae78 .cfa: sp 16 +
STACK CFI INIT aec0 278 .cfa: sp 0 + .ra: x30
STACK CFI aec4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI aec8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI aed8 .ra: .cfa -32 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b0cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b0d0 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT b140 668 .cfa: sp 0 + .ra: x30
STACK CFI b144 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI b150 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI b160 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI b16c .ra: .cfa -80 + ^ v8: .cfa -72 + ^
STACK CFI b664 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b668 .cfa: sp 144 + .ra: .cfa -80 + ^ v8: .cfa -72 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT b7c0 350 .cfa: sp 0 + .ra: x30
STACK CFI b7c4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b7d8 .ra: .cfa -16 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI baec .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bb00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI bb04 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT bb10 22c .cfa: sp 0 + .ra: x30
STACK CFI bb14 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bb20 .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bcd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI bce0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bd2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI bd30 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT bd40 2f8 .cfa: sp 0 + .ra: x30
STACK CFI bd44 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI bd54 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI bd64 .ra: .cfa -104 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x23: .cfa -112 + ^
STACK CFI bd88 v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI c034 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT c0b0 524 .cfa: sp 0 + .ra: x30
STACK CFI c0b4 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI c0c4 .ra: .cfa -112 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI c4c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI c4c8 .cfa: sp 144 + .ra: .cfa -112 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT c5f8 3d4 .cfa: sp 0 + .ra: x30
STACK CFI c608 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI c62c .ra: .cfa -80 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c634 v10: .cfa -72 + ^
STACK CFI c934 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c938 .cfa: sp 160 + .ra: .cfa -80 + ^ v10: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c9c8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT c9d0 1ec .cfa: sp 0 + .ra: x30
STACK CFI INIT cbc0 208 .cfa: sp 0 + .ra: x30
STACK CFI cbc8 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI cbd0 .ra: .cfa -104 + ^ x21: .cfa -112 + ^
STACK CFI cd88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI cd90 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^
