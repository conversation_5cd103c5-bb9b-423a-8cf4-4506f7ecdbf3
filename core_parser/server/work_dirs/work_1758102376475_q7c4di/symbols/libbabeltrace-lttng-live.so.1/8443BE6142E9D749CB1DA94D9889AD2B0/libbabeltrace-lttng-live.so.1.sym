MODULE Linux arm64 8443BE6142E9D749CB1DA94D9889AD2B0 libbabeltrace-lttng-live.so.1
INFO CODE_ID 61BE4384E94249D7CB1DA94D9889AD2BA3BF31C6
PUBLIC 23a8 0 bt_lttng_live_hook
PUBLIC 23b0 0 lttng_live_should_quit
PUBLIC 2f80 0 lttng_live_connect_viewer
PUBLIC 30d0 0 lttng_live_establish_connection
PUBLIC 3328 0 lttng_live_list_sessions
PUBLIC 3808 0 lttng_live_ctf_trace_assign
PUBLIC 3910 0 lttng_live_attach_session
PUBLIC 3d50 0 lttng_live_create_viewer_session
PUBLIC 3ec0 0 lttng_live_get_new_streams
PUBLIC 5948 0 lttng_live_read
STACK CFI INIT 1e90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ec0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f00 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f0c x19: .cfa -16 + ^
STACK CFI 1f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f50 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f78 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f90 1c .cfa: sp 0 + .ra: x30
STACK CFI 1f94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1fb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fb8 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 1fbc .cfa: sp 848 +
STACK CFI 1fc4 .ra: .cfa -840 + ^ x29: .cfa -848 + ^
STACK CFI 1fcc x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 1fd8 x19: .cfa -832 + ^ x20: .cfa -824 + ^
STACK CFI 1fec x23: .cfa -800 + ^ x24: .cfa -792 + ^
STACK CFI 20c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 20cc .cfa: sp 848 + .ra: .cfa -840 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x29: .cfa -848 + ^
STACK CFI INIT 2290 114 .cfa: sp 0 + .ra: x30
STACK CFI 2294 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 229c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 22ec x21: .cfa -304 + ^
STACK CFI 234c x21: x21
STACK CFI 2370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2374 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI 2384 x21: x21
STACK CFI 23a0 x21: .cfa -304 + ^
STACK CFI INIT 23a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c0 27c .cfa: sp 0 + .ra: x30
STACK CFI 23c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 23d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 240c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2410 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2414 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2418 x23: .cfa -16 + ^
STACK CFI 25b4 x19: x19 x20: x20
STACK CFI 25bc x23: x23
STACK CFI 25c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 25c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 25d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1e20 58 .cfa: sp 0 + .ra: x30
STACK CFI 1e24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1e54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1e10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2640 60 .cfa: sp 0 + .ra: x30
STACK CFI 2644 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 264c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2654 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 269c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 26a0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 26a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 26c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 274c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2768 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2788 2c .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 27b8 68 .cfa: sp 0 + .ra: x30
STACK CFI 27f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2820 9c .cfa: sp 0 + .ra: x30
STACK CFI 287c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2898 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28c0 534 .cfa: sp 0 + .ra: x30
STACK CFI 28c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 28cc .cfa: x29 160 +
STACK CFI 28dc x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2914 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2920 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 29ec x25: x25 x26: x26
STACK CFI 29f0 x27: x27 x28: x28
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2a24 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2ad8 x25: x25 x26: x26
STACK CFI 2adc x27: x27 x28: x28
STACK CFI 2ae0 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2cf0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2d2c x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2dcc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2dd0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2dd4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2df8 188 .cfa: sp 0 + .ra: x30
STACK CFI 2dfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2f80 150 .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2f94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2fb4 x23: .cfa -48 + ^
STACK CFI 3028 x23: x23
STACK CFI 304c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3050 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 3058 x23: .cfa -48 + ^
STACK CFI 308c x23: x23
STACK CFI 3090 x23: .cfa -48 + ^
STACK CFI 30c8 x23: x23
STACK CFI 30cc x23: .cfa -48 + ^
STACK CFI INIT 30d0 258 .cfa: sp 0 + .ra: x30
STACK CFI 30d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 30dc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 30e4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 31d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31d8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 31dc x23: .cfa -96 + ^
STACK CFI 322c x23: x23
STACK CFI 32ec x23: .cfa -96 + ^
STACK CFI 32f0 x23: x23
STACK CFI 3314 x23: .cfa -96 + ^
STACK CFI 3318 x23: x23
STACK CFI 331c x23: .cfa -96 + ^
STACK CFI 3324 x23: x23
STACK CFI INIT 3328 4dc .cfa: sp 0 + .ra: x30
STACK CFI 332c .cfa: sp 544 +
STACK CFI 3330 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 3338 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 3360 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 3364 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 33f0 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 33f4 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3568 x23: x23 x24: x24
STACK CFI 356c x25: x25 x26: x26
STACK CFI 3578 x19: x19 x20: x20
STACK CFI 357c x27: x27 x28: x28
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 35a8 .cfa: sp 544 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI 35ec x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3600 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3624 x23: x23 x24: x24
STACK CFI 3628 x25: x25 x26: x26
STACK CFI 3650 x19: x19 x20: x20
STACK CFI 3654 x27: x27 x28: x28
STACK CFI 3658 x19: .cfa -512 + ^ x20: .cfa -504 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 366c x23: x23 x24: x24
STACK CFI 3670 x25: x25 x26: x26
STACK CFI 3714 x19: x19 x20: x20
STACK CFI 3718 x27: x27 x28: x28
STACK CFI 3728 x19: .cfa -512 + ^ x20: .cfa -504 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 374c x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3770 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 37bc x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 37c0 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 37c4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 37e8 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 37ec x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 37f0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37f4 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 37f8 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 37fc x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 3800 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI INIT 3808 104 .cfa: sp 0 + .ra: x30
STACK CFI 380c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3814 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 387c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3910 43c .cfa: sp 0 + .ra: x30
STACK CFI 3918 .cfa: sp 4560 +
STACK CFI 391c .ra: .cfa -4552 + ^ x29: .cfa -4560 + ^
STACK CFI 3924 x21: .cfa -4528 + ^ x22: .cfa -4520 + ^
STACK CFI 392c x19: .cfa -4544 + ^ x20: .cfa -4536 + ^
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a30 .cfa: sp 4560 + .ra: .cfa -4552 + ^ x19: .cfa -4544 + ^ x20: .cfa -4536 + ^ x21: .cfa -4528 + ^ x22: .cfa -4520 + ^ x29: .cfa -4560 + ^
STACK CFI 3ab8 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3adc x23: .cfa -4512 + ^ x24: .cfa -4504 + ^
STACK CFI 3ae8 x27: .cfa -4480 + ^
STACK CFI 3c08 x23: x23 x24: x24
STACK CFI 3c0c x25: x25 x26: x26
STACK CFI 3c10 x27: x27
STACK CFI 3c14 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3c3c x23: .cfa -4512 + ^ x24: .cfa -4504 + ^ x27: .cfa -4480 + ^
STACK CFI 3c60 x23: x23 x24: x24
STACK CFI 3c64 x25: x25 x26: x26
STACK CFI 3c68 x27: x27
STACK CFI 3cb0 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3cb4 x25: x25 x26: x26
STACK CFI 3cb8 x23: .cfa -4512 + ^ x24: .cfa -4504 + ^ x25: .cfa -4496 + ^ x26: .cfa -4488 + ^ x27: .cfa -4480 + ^
STACK CFI 3cdc x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3d00 x23: .cfa -4512 + ^ x24: .cfa -4504 + ^
STACK CFI 3d04 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3d08 x27: .cfa -4480 + ^
STACK CFI 3d0c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3d30 x23: .cfa -4512 + ^ x24: .cfa -4504 + ^
STACK CFI 3d34 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3d38 x27: .cfa -4480 + ^
STACK CFI 3d3c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3d40 x23: .cfa -4512 + ^ x24: .cfa -4504 + ^
STACK CFI 3d44 x25: .cfa -4496 + ^ x26: .cfa -4488 + ^
STACK CFI 3d48 x27: .cfa -4480 + ^
STACK CFI INIT 3d50 16c .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3d5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3d64 x21: .cfa -48 + ^
STACK CFI 3e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3ec0 3f4 .cfa: sp 0 + .ra: x30
STACK CFI 3ec8 .cfa: sp 4512 +
STACK CFI 3ecc .ra: .cfa -4504 + ^ x29: .cfa -4512 + ^
STACK CFI 3ed4 x21: .cfa -4480 + ^ x22: .cfa -4472 + ^
STACK CFI 3ee4 x19: .cfa -4496 + ^ x20: .cfa -4488 + ^
STACK CFI 3fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fa8 .cfa: sp 4512 + .ra: .cfa -4504 + ^ x19: .cfa -4496 + ^ x20: .cfa -4488 + ^ x21: .cfa -4480 + ^ x22: .cfa -4472 + ^ x29: .cfa -4512 + ^
STACK CFI 4008 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^
STACK CFI 4028 x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 4050 x27: .cfa -4432 + ^
STACK CFI 4168 x23: x23 x24: x24
STACK CFI 416c x25: x25 x26: x26
STACK CFI 4170 x27: x27
STACK CFI 4174 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^
STACK CFI 4178 x23: x23 x24: x24
STACK CFI 417c x23: .cfa -4464 + ^ x24: .cfa -4456 + ^ x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 41a0 x27: .cfa -4432 + ^
STACK CFI 41c4 x23: x23 x24: x24
STACK CFI 41c8 x25: x25 x26: x26
STACK CFI 41cc x27: x27
STACK CFI 4214 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^ x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 4218 x23: x23 x24: x24
STACK CFI 421c x25: x25 x26: x26
STACK CFI 4220 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^ x25: .cfa -4448 + ^ x26: .cfa -4440 + ^ x27: .cfa -4432 + ^
STACK CFI 4244 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 4248 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^
STACK CFI 424c x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 4250 x27: .cfa -4432 + ^
STACK CFI 4254 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 4278 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^
STACK CFI 427c x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 4280 x27: .cfa -4432 + ^
STACK CFI 4284 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 42a8 x23: .cfa -4464 + ^ x24: .cfa -4456 + ^
STACK CFI 42ac x25: .cfa -4448 + ^ x26: .cfa -4440 + ^
STACK CFI 42b0 x27: .cfa -4432 + ^
STACK CFI INIT 42b8 144 .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 42c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 42cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 42d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 42dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 42e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 438c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4390 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 43b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 43b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4400 590 .cfa: sp 0 + .ra: x30
STACK CFI 4404 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 440c x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 4418 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4440 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 46d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46d8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 4990 fb8 .cfa: sp 0 + .ra: x30
STACK CFI 4994 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4998 .cfa: x29 192 +
STACK CFI 499c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 49ac x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 49c4 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e18 .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5948 398 .cfa: sp 0 + .ra: x30
STACK CFI 594c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5954 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 595c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5988 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 59d8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 59e0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5b20 x25: x25 x26: x26
STACK CFI 5b24 x27: x27 x28: x28
STACK CFI 5b28 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5b58 x25: x25 x26: x26
STACK CFI 5b5c x27: x27 x28: x28
STACK CFI 5b88 x23: x23 x24: x24
STACK CFI 5bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bbc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5bd4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5bfc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5c3c x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5c60 x25: x25 x26: x26
STACK CFI 5c64 x27: x27 x28: x28
STACK CFI 5c68 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5cd0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5cd4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5cd8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5cdc x27: .cfa -48 + ^ x28: .cfa -40 + ^
