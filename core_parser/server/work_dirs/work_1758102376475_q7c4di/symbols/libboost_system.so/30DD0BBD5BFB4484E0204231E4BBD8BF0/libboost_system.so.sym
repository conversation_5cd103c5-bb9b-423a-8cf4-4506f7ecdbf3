MODULE Linux arm64 30DD0BBD5BFB4484E0204231E4BBD8BF0 libboost_system.so.1.77.0
INFO CODE_ID BD0BDD30FB5B8444E0204231E4BBD8BF
PUBLIC 4b0 0 _init
PUBLIC 510 0 call_weak_fn
PUBLIC 524 0 deregister_tm_clones
PUBLIC 554 0 register_tm_clones
PUBLIC 590 0 __do_global_dtors_aux
PUBLIC 5e0 0 frame_dummy
PUBLIC 5f0 0 boost::system::dummy_exported_function()
PUBLIC 5f4 0 _fini
STACK CFI INIT 524 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 554 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 590 50 .cfa: sp 0 + .ra: x30
STACK CFI 5a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a8 x19: .cfa -16 + ^
STACK CFI 5d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f0 4 .cfa: sp 0 + .ra: x30
