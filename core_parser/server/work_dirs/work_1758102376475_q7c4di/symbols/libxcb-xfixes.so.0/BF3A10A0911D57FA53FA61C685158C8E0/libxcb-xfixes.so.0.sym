MODULE Linux arm64 BF3A10A0911D57FA53FA61C685158C8E0 libxcb-xfixes.so.0
INFO CODE_ID A0103ABF1D91FA5753FA61C685158C8E32F528E5
PUBLIC 2c78 0 xcb_xfixes_query_version
PUBLIC 2ce8 0 xcb_xfixes_query_version_unchecked
PUBLIC 2d58 0 xcb_xfixes_query_version_reply
PUBLIC 2d60 0 xcb_xfixes_change_save_set_checked
PUBLIC 2de0 0 xcb_xfixes_change_save_set
PUBLIC 2e58 0 xcb_xfixes_select_selection_input_checked
PUBLIC 2ed0 0 xcb_xfixes_select_selection_input
PUBLIC 2f48 0 xcb_xfixes_select_cursor_input_checked
PUBLIC 2fb8 0 xcb_xfixes_select_cursor_input
PUBLIC 3028 0 xcb_xfixes_get_cursor_image_sizeof
PUBLIC 3040 0 xcb_xfixes_get_cursor_image
PUBLIC 30a8 0 xcb_xfixes_get_cursor_image_unchecked
PUBLIC 3110 0 xcb_xfixes_get_cursor_image_cursor_image
PUBLIC 3118 0 xcb_xfixes_get_cursor_image_cursor_image_length
PUBLIC 3128 0 xcb_xfixes_get_cursor_image_cursor_image_end
PUBLIC 3148 0 xcb_xfixes_get_cursor_image_reply
PUBLIC 3150 0 xcb_xfixes_region_next
PUBLIC 3170 0 xcb_xfixes_region_end
PUBLIC 3188 0 xcb_xfixes_create_region_sizeof
PUBLIC 3198 0 xcb_xfixes_create_region_checked
PUBLIC 3218 0 xcb_xfixes_create_region
PUBLIC 3290 0 xcb_xfixes_create_region_rectangles
PUBLIC 3298 0 xcb_xfixes_create_region_rectangles_length
PUBLIC 32b0 0 xcb_xfixes_create_region_rectangles_iterator
PUBLIC 32e0 0 xcb_xfixes_create_region_from_bitmap_checked
PUBLIC 3350 0 xcb_xfixes_create_region_from_bitmap
PUBLIC 33c0 0 xcb_xfixes_create_region_from_window_checked
PUBLIC 3440 0 xcb_xfixes_create_region_from_window
PUBLIC 34b8 0 xcb_xfixes_create_region_from_gc_checked
PUBLIC 3528 0 xcb_xfixes_create_region_from_gc
PUBLIC 3598 0 xcb_xfixes_create_region_from_picture_checked
PUBLIC 3608 0 xcb_xfixes_create_region_from_picture
PUBLIC 3678 0 xcb_xfixes_destroy_region_checked
PUBLIC 36e8 0 xcb_xfixes_destroy_region
PUBLIC 3750 0 xcb_xfixes_set_region_sizeof
PUBLIC 3760 0 xcb_xfixes_set_region_checked
PUBLIC 37e0 0 xcb_xfixes_set_region
PUBLIC 3858 0 xcb_xfixes_set_region_rectangles
PUBLIC 3860 0 xcb_xfixes_set_region_rectangles_length
PUBLIC 3878 0 xcb_xfixes_set_region_rectangles_iterator
PUBLIC 38a8 0 xcb_xfixes_copy_region_checked
PUBLIC 3918 0 xcb_xfixes_copy_region
PUBLIC 3988 0 xcb_xfixes_union_region_checked
PUBLIC 3a00 0 xcb_xfixes_union_region
PUBLIC 3a78 0 xcb_xfixes_intersect_region_checked
PUBLIC 3af0 0 xcb_xfixes_intersect_region
PUBLIC 3b68 0 xcb_xfixes_subtract_region_checked
PUBLIC 3be0 0 xcb_xfixes_subtract_region
PUBLIC 3c58 0 xcb_xfixes_invert_region_checked
PUBLIC 3cd8 0 xcb_xfixes_invert_region
PUBLIC 3d50 0 xcb_xfixes_translate_region_checked
PUBLIC 3dc8 0 xcb_xfixes_translate_region
PUBLIC 3e40 0 xcb_xfixes_region_extents_checked
PUBLIC 3eb0 0 xcb_xfixes_region_extents
PUBLIC 3f20 0 xcb_xfixes_fetch_region_sizeof
PUBLIC 3f38 0 xcb_xfixes_fetch_region
PUBLIC 3fa8 0 xcb_xfixes_fetch_region_unchecked
PUBLIC 4010 0 xcb_xfixes_fetch_region_rectangles
PUBLIC 4018 0 xcb_xfixes_fetch_region_rectangles_length
PUBLIC 4028 0 xcb_xfixes_fetch_region_rectangles_iterator
PUBLIC 4050 0 xcb_xfixes_fetch_region_reply
PUBLIC 4058 0 xcb_xfixes_set_gc_clip_region_checked
PUBLIC 40d0 0 xcb_xfixes_set_gc_clip_region
PUBLIC 4148 0 xcb_xfixes_set_window_shape_region_checked
PUBLIC 41d0 0 xcb_xfixes_set_window_shape_region
PUBLIC 4250 0 xcb_xfixes_set_picture_clip_region_checked
PUBLIC 42c8 0 xcb_xfixes_set_picture_clip_region
PUBLIC 4340 0 xcb_xfixes_set_cursor_name_sizeof
PUBLIC 4350 0 xcb_xfixes_set_cursor_name_checked
PUBLIC 43e0 0 xcb_xfixes_set_cursor_name
PUBLIC 4470 0 xcb_xfixes_set_cursor_name_name
PUBLIC 4478 0 xcb_xfixes_set_cursor_name_name_length
PUBLIC 4480 0 xcb_xfixes_set_cursor_name_name_end
PUBLIC 4498 0 xcb_xfixes_get_cursor_name_sizeof
PUBLIC 44a8 0 xcb_xfixes_get_cursor_name
PUBLIC 4518 0 xcb_xfixes_get_cursor_name_unchecked
PUBLIC 4580 0 xcb_xfixes_get_cursor_name_name
PUBLIC 4588 0 xcb_xfixes_get_cursor_name_name_length
PUBLIC 4590 0 xcb_xfixes_get_cursor_name_name_end
PUBLIC 45a8 0 xcb_xfixes_get_cursor_name_reply
PUBLIC 45b0 0 xcb_xfixes_get_cursor_image_and_name_sizeof
PUBLIC 45d0 0 xcb_xfixes_get_cursor_image_and_name
PUBLIC 4638 0 xcb_xfixes_get_cursor_image_and_name_unchecked
PUBLIC 46a0 0 xcb_xfixes_get_cursor_image_and_name_cursor_image
PUBLIC 46a8 0 xcb_xfixes_get_cursor_image_and_name_cursor_image_length
PUBLIC 46b8 0 xcb_xfixes_get_cursor_image_and_name_cursor_image_end
PUBLIC 46d8 0 xcb_xfixes_get_cursor_image_and_name_name
PUBLIC 46f0 0 xcb_xfixes_get_cursor_image_and_name_name_length
PUBLIC 46f8 0 xcb_xfixes_get_cursor_image_and_name_name_end
PUBLIC 4728 0 xcb_xfixes_get_cursor_image_and_name_reply
PUBLIC 4730 0 xcb_xfixes_change_cursor_checked
PUBLIC 47a0 0 xcb_xfixes_change_cursor
PUBLIC 4810 0 xcb_xfixes_change_cursor_by_name_sizeof
PUBLIC 4820 0 xcb_xfixes_change_cursor_by_name_checked
PUBLIC 48b0 0 xcb_xfixes_change_cursor_by_name
PUBLIC 4940 0 xcb_xfixes_change_cursor_by_name_name
PUBLIC 4948 0 xcb_xfixes_change_cursor_by_name_name_length
PUBLIC 4950 0 xcb_xfixes_change_cursor_by_name_name_end
PUBLIC 4968 0 xcb_xfixes_expand_region_checked
PUBLIC 49e8 0 xcb_xfixes_expand_region
PUBLIC 4a68 0 xcb_xfixes_hide_cursor_checked
PUBLIC 4ad8 0 xcb_xfixes_hide_cursor
PUBLIC 4b40 0 xcb_xfixes_show_cursor_checked
PUBLIC 4bb0 0 xcb_xfixes_show_cursor
PUBLIC 4c18 0 xcb_xfixes_barrier_next
PUBLIC 4c38 0 xcb_xfixes_barrier_end
PUBLIC 4c50 0 xcb_xfixes_create_pointer_barrier_sizeof
PUBLIC 4c60 0 xcb_xfixes_create_pointer_barrier_checked
PUBLIC 4d08 0 xcb_xfixes_create_pointer_barrier
PUBLIC 4db0 0 xcb_xfixes_create_pointer_barrier_devices
PUBLIC 4db8 0 xcb_xfixes_create_pointer_barrier_devices_length
PUBLIC 4dc0 0 xcb_xfixes_create_pointer_barrier_devices_end
PUBLIC 4dd8 0 xcb_xfixes_delete_pointer_barrier_checked
PUBLIC 4e48 0 xcb_xfixes_delete_pointer_barrier
STACK CFI INIT 2bb8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2be8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c28 48 .cfa: sp 0 + .ra: x30
STACK CFI 2c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c34 x19: .cfa -16 + ^
STACK CFI 2c6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c78 6c .cfa: sp 0 + .ra: x30
STACK CFI 2c7c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2c8c x19: .cfa -112 + ^
STACK CFI 2cdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ce0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2ce8 6c .cfa: sp 0 + .ra: x30
STACK CFI 2cec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2cfc x19: .cfa -112 + ^
STACK CFI 2d4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d50 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2d58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d60 7c .cfa: sp 0 + .ra: x30
STACK CFI 2d64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2d74 x19: .cfa -112 + ^
STACK CFI 2dd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2dd8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2de0 78 .cfa: sp 0 + .ra: x30
STACK CFI 2de4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2df4 x19: .cfa -112 + ^
STACK CFI 2e50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2e54 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2e58 78 .cfa: sp 0 + .ra: x30
STACK CFI 2e5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2e6c x19: .cfa -112 + ^
STACK CFI 2ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ecc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2ed0 74 .cfa: sp 0 + .ra: x30
STACK CFI 2ed4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2ee4 x19: .cfa -112 + ^
STACK CFI 2f3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f40 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2f48 70 .cfa: sp 0 + .ra: x30
STACK CFI 2f4c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2f5c x19: .cfa -112 + ^
STACK CFI 2fb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2fb8 6c .cfa: sp 0 + .ra: x30
STACK CFI 2fbc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2fcc x19: .cfa -112 + ^
STACK CFI 301c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3020 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3028 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3040 68 .cfa: sp 0 + .ra: x30
STACK CFI 3044 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3054 x19: .cfa -96 + ^
STACK CFI 30a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 30a8 64 .cfa: sp 0 + .ra: x30
STACK CFI 30ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 30bc x19: .cfa -96 + ^
STACK CFI 3104 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3108 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3118 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3128 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3148 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3150 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3170 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3188 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3198 7c .cfa: sp 0 + .ra: x30
STACK CFI 319c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 31ac x19: .cfa -128 + ^
STACK CFI 320c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3210 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3218 78 .cfa: sp 0 + .ra: x30
STACK CFI 321c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 322c x19: .cfa -128 + ^
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 328c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3298 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32b0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 32e0 70 .cfa: sp 0 + .ra: x30
STACK CFI 32e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 32f4 x19: .cfa -112 + ^
STACK CFI 3348 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 334c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3350 6c .cfa: sp 0 + .ra: x30
STACK CFI 3354 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3364 x19: .cfa -112 + ^
STACK CFI 33b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 33b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 33c0 7c .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 33d4 x19: .cfa -112 + ^
STACK CFI 3434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3438 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3440 78 .cfa: sp 0 + .ra: x30
STACK CFI 3444 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3454 x19: .cfa -112 + ^
STACK CFI 34b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 34b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 34b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 34cc x19: .cfa -112 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3524 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3528 6c .cfa: sp 0 + .ra: x30
STACK CFI 352c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 353c x19: .cfa -112 + ^
STACK CFI 358c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3590 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3598 70 .cfa: sp 0 + .ra: x30
STACK CFI 359c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 35ac x19: .cfa -112 + ^
STACK CFI 3600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3604 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3608 6c .cfa: sp 0 + .ra: x30
STACK CFI 360c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 361c x19: .cfa -112 + ^
STACK CFI 366c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3670 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3678 6c .cfa: sp 0 + .ra: x30
STACK CFI 367c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 368c x19: .cfa -96 + ^
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 36e0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 36e8 68 .cfa: sp 0 + .ra: x30
STACK CFI 36ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 36fc x19: .cfa -96 + ^
STACK CFI 3748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 374c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3750 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3760 7c .cfa: sp 0 + .ra: x30
STACK CFI 3764 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3774 x19: .cfa -128 + ^
STACK CFI 37d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37d8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 37e0 78 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 37f4 x19: .cfa -128 + ^
STACK CFI 3850 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3854 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3858 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3860 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3878 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38a8 70 .cfa: sp 0 + .ra: x30
STACK CFI 38ac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 38bc x19: .cfa -112 + ^
STACK CFI 3910 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3914 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3918 6c .cfa: sp 0 + .ra: x30
STACK CFI 391c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 392c x19: .cfa -112 + ^
STACK CFI 397c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3980 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3988 78 .cfa: sp 0 + .ra: x30
STACK CFI 398c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 399c x19: .cfa -112 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 39fc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3a00 74 .cfa: sp 0 + .ra: x30
STACK CFI 3a04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3a14 x19: .cfa -112 + ^
STACK CFI 3a6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3a70 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3a78 78 .cfa: sp 0 + .ra: x30
STACK CFI 3a7c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3a8c x19: .cfa -112 + ^
STACK CFI 3ae8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3aec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3af0 74 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3b04 x19: .cfa -112 + ^
STACK CFI 3b5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b60 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3b68 78 .cfa: sp 0 + .ra: x30
STACK CFI 3b6c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3b7c x19: .cfa -112 + ^
STACK CFI 3bd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3bdc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3be0 74 .cfa: sp 0 + .ra: x30
STACK CFI 3be4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3bf4 x19: .cfa -112 + ^
STACK CFI 3c4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c50 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c58 7c .cfa: sp 0 + .ra: x30
STACK CFI 3c5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3c6c x19: .cfa -112 + ^
STACK CFI 3ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cd0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3cd8 78 .cfa: sp 0 + .ra: x30
STACK CFI 3cdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3cec x19: .cfa -112 + ^
STACK CFI 3d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d4c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3d50 78 .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d64 x19: .cfa -112 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3dc8 78 .cfa: sp 0 + .ra: x30
STACK CFI 3dcc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3ddc x19: .cfa -112 + ^
STACK CFI 3e38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e3c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3e40 70 .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3e54 x19: .cfa -112 + ^
STACK CFI 3ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3eac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3eb0 6c .cfa: sp 0 + .ra: x30
STACK CFI 3eb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3ec4 x19: .cfa -112 + ^
STACK CFI 3f14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3f18 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3f20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f38 6c .cfa: sp 0 + .ra: x30
STACK CFI 3f3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3f4c x19: .cfa -96 + ^
STACK CFI 3f9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3fa0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3fa8 68 .cfa: sp 0 + .ra: x30
STACK CFI 3fac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3fbc x19: .cfa -96 + ^
STACK CFI 4008 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 400c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4010 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4018 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4028 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4058 78 .cfa: sp 0 + .ra: x30
STACK CFI 405c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 406c x19: .cfa -112 + ^
STACK CFI 40c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40cc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 40d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 40d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 40e4 x19: .cfa -112 + ^
STACK CFI 413c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4140 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4148 84 .cfa: sp 0 + .ra: x30
STACK CFI 414c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 415c x19: .cfa -112 + ^
STACK CFI 41c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 41d0 80 .cfa: sp 0 + .ra: x30
STACK CFI 41d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 41e8 x19: .cfa -112 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 424c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4250 78 .cfa: sp 0 + .ra: x30
STACK CFI 4254 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4264 x19: .cfa -112 + ^
STACK CFI 42c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 42c8 74 .cfa: sp 0 + .ra: x30
STACK CFI 42cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 42dc x19: .cfa -112 + ^
STACK CFI 4334 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4338 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4340 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4350 90 .cfa: sp 0 + .ra: x30
STACK CFI 4354 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4364 x19: .cfa -144 + ^
STACK CFI 43d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43dc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 43e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 43e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 43f4 x19: .cfa -144 + ^
STACK CFI 4464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4468 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4478 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4480 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4498 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 44a8 6c .cfa: sp 0 + .ra: x30
STACK CFI 44ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 44bc x19: .cfa -96 + ^
STACK CFI 450c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4510 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4518 68 .cfa: sp 0 + .ra: x30
STACK CFI 451c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 452c x19: .cfa -96 + ^
STACK CFI 4578 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 457c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4588 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4590 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 45d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 45e4 x19: .cfa -96 + ^
STACK CFI 4630 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4634 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4638 64 .cfa: sp 0 + .ra: x30
STACK CFI 463c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 464c x19: .cfa -96 + ^
STACK CFI 4694 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4698 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 46a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46d8 14 .cfa: sp 0 + .ra: x30
STACK CFI 46dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46f8 30 .cfa: sp 0 + .ra: x30
STACK CFI 46fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4704 x19: .cfa -16 + ^
STACK CFI 4724 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4728 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4730 70 .cfa: sp 0 + .ra: x30
STACK CFI 4734 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4744 x19: .cfa -112 + ^
STACK CFI 4798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 479c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 47a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 47b4 x19: .cfa -112 + ^
STACK CFI 4804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4808 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4810 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4820 90 .cfa: sp 0 + .ra: x30
STACK CFI 4824 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4834 x19: .cfa -144 + ^
STACK CFI 48a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48ac .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 48b0 8c .cfa: sp 0 + .ra: x30
STACK CFI 48b4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 48c4 x19: .cfa -144 + ^
STACK CFI 4934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4938 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4948 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4950 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4968 80 .cfa: sp 0 + .ra: x30
STACK CFI 496c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 497c x19: .cfa -112 + ^
STACK CFI 49e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 49e8 7c .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 49fc x19: .cfa -112 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a60 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4a68 6c .cfa: sp 0 + .ra: x30
STACK CFI 4a6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4a7c x19: .cfa -96 + ^
STACK CFI 4acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ad0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4ad8 68 .cfa: sp 0 + .ra: x30
STACK CFI 4adc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4aec x19: .cfa -96 + ^
STACK CFI 4b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b3c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4b40 6c .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4b54 x19: .cfa -96 + ^
STACK CFI 4ba4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ba8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4bb0 68 .cfa: sp 0 + .ra: x30
STACK CFI 4bb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4bc4 x19: .cfa -96 + ^
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4c18 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c38 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c60 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4c64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4c7c x19: .cfa -160 + ^
STACK CFI 4d00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d04 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4d08 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4d0c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4d24 x19: .cfa -160 + ^
STACK CFI 4da4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4da8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4db0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4db8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dd8 6c .cfa: sp 0 + .ra: x30
STACK CFI 4ddc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4dec x19: .cfa -96 + ^
STACK CFI 4e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e40 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4e48 68 .cfa: sp 0 + .ra: x30
STACK CFI 4e4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4e5c x19: .cfa -96 + ^
STACK CFI 4ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4eac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
