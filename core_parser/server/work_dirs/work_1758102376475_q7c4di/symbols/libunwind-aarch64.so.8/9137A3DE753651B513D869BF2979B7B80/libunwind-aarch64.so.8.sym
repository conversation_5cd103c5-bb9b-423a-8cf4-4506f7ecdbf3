MODULE Linux arm64 9137A3DE753651B513D869BF2979B7B80 libunwind-aarch64.so.8
INFO CODE_ID DEA337913675B55113D869BF2979B7B8B725343D
PUBLIC 1f60 0 _Uaarch64_get_elf_image
PUBLIC 26b0 0 _Uaarch64_flush_cache
PUBLIC 2ad0 0 _Uaarch64_strerror
PUBLIC 2bc0 0 _Uaarch64_is_fpreg
PUBLIC 2bd0 0 _Uaarch64_regname
PUBLIC 37e0 0 _Uaarch64_get_accessors
PUBLIC 3828 0 _Uaarch64_get_proc_info_by_ip
PUBLIC 38b8 0 _Uaarch64_get_proc_name
PUBLIC 3af8 0 _Uaarch64_destroy_addr_space
PUBLIC 3b00 0 _Uaarch64_get_reg
PUBLIC 3b20 0 _Uaarch64_set_reg
PUBLIC 3b40 0 _Uaarch64_get_fpreg
PUBLIC 3b48 0 _Uaarch64_set_fpreg
PUBLIC 3b68 0 _Uaarch64_set_caching_policy
PUBLIC 3bd0 0 _Uaarch64_create_addr_space
PUBLIC 3c58 0 _Uaarch64_get_proc_info
PUBLIC 3ca8 0 _Uaarch64_get_save_loc
PUBLIC 4038 0 _Uaarch64_init_local
PUBLIC 43d8 0 _Uaarch64_init_remote
PUBLIC 4780 0 _Uaarch64_is_signal_frame
PUBLIC 4b28 0 _Uaarch64_resume
PUBLIC 4db0 0 _Uaarch64_handle_signal_frame
PUBLIC 5008 0 _Uaarch64_step
PUBLIC b618 0 _Uaarch64_dwarf_find_debug_frame
PUBLIC c450 0 _Uaarch64_dwarf_search_unwind_table
PUBLIC cbd8 0 _Uaarch64_dwarf_find_unwind_table
STACK CFI INIT 1f60 744 .cfa: sp 0 + .ra: x30
STACK CFI 1f64 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 1f7c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 1f9c x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 1fc4 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 2064 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 209c x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2578 x21: x21 x22: x22
STACK CFI 257c x27: x27 x28: x28
STACK CFI 25ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 25b0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 25fc x21: x21 x22: x22
STACK CFI 2600 x27: x27 x28: x28
STACK CFI 2604 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 265c x21: x21 x22: x22
STACK CFI 2660 x27: x27 x28: x28
STACK CFI 266c x21: .cfa -256 + ^ x22: .cfa -248 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2684 x27: x27 x28: x28
STACK CFI 2694 x21: x21 x22: x22
STACK CFI 269c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 26a0 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 26a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 26b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26bc x21: .cfa -16 + ^
STACK CFI 26c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2720 114 .cfa: sp 0 + .ra: x30
STACK CFI 2724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2734 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 27b8 x21: .cfa -16 + ^
STACK CFI 27f8 x21: x21
STACK CFI 27fc x21: .cfa -16 + ^
STACK CFI 2830 x21: x21
STACK CFI INIT 2838 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2870 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2874 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 287c x23: .cfa -16 + ^
STACK CFI 2884 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2890 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2938 d8 .cfa: sp 0 + .ra: x30
STACK CFI 293c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2948 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2958 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 29f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29fc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2a10 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2a14 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2a20 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2a30 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2acc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2ad0 f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bc0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bd0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c08 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c78 2c .cfa: sp 0 + .ra: x30
STACK CFI 2c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c84 x19: .cfa -16 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ca8 374 .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2cb4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2cc4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2ce4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2cf0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2d00 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2f68 x19: x19 x20: x20
STACK CFI 2f6c x21: x21 x22: x22
STACK CFI 2f70 x25: x25 x26: x26
STACK CFI 2fa0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2fa4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2fa8 x19: x19 x20: x20
STACK CFI 2fac x21: x21 x22: x22
STACK CFI 2fb0 x25: x25 x26: x26
STACK CFI 2fb4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2fc8 x19: x19 x20: x20
STACK CFI 2fcc x21: x21 x22: x22
STACK CFI 2fd0 x25: x25 x26: x26
STACK CFI 2fd8 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2ff8 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 2ffc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3000 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3004 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3010 x19: x19 x20: x20
STACK CFI 3014 x21: x21 x22: x22
STACK CFI 3018 x25: x25 x26: x26
STACK CFI INIT 3020 54 .cfa: sp 0 + .ra: x30
STACK CFI 3024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 302c x19: .cfa -16 + ^
STACK CFI 3054 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3058 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3070 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3078 5b0 .cfa: sp 0 + .ra: x30
STACK CFI 307c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 308c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3094 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 30a8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 30b8 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 30cc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 32ac x25: x25 x26: x26
STACK CFI 32b0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 32b4 x25: x25 x26: x26
STACK CFI 32ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 32f0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 3338 x25: x25 x26: x26
STACK CFI 3358 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3380 x25: x25 x26: x26
STACK CFI 3384 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3620 x25: x25 x26: x26
STACK CFI 3624 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 3628 3c .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3634 x19: .cfa -16 + ^
STACK CFI 3660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3668 bc .cfa: sp 0 + .ra: x30
STACK CFI 366c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3674 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3684 x21: .cfa -32 + ^
STACK CFI 36c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 36c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3728 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3744 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 374c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3764 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3770 x23: .cfa -16 + ^
STACK CFI 37a4 x21: x21 x22: x22
STACK CFI 37a8 x23: x23
STACK CFI 37b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 37d4 x21: x21 x22: x22
STACK CFI 37d8 x23: x23
STACK CFI 37dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37f4 x19: .cfa -16 + ^
STACK CFI 380c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3810 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3820 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3828 90 .cfa: sp 0 + .ra: x30
STACK CFI 382c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3834 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 384c x23: .cfa -16 + ^
STACK CFI 3884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3888 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 38b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 38b8 1fc .cfa: sp 0 + .ra: x30
STACK CFI 38bc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 38c4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 38d0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 38d8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 38f0 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 39c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 39c8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3ab8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3af8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b00 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b20 20 .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b48 20 .cfa: sp 0 + .ra: x30
STACK CFI 3b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b68 64 .cfa: sp 0 + .ra: x30
STACK CFI 3b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3bd0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3bd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3be4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c58 50 .cfa: sp 0 + .ra: x30
STACK CFI 3c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ca8 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d18 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3d1c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3d24 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3d44 x21: .cfa -160 + ^
STACK CFI 3de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3de8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3df0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3df8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e10 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e38 58 .cfa: sp 0 + .ra: x30
STACK CFI 3e3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e5c x23: .cfa -16 + ^
STACK CFI 3e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3e90 84 .cfa: sp 0 + .ra: x30
STACK CFI 3e94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ea0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3eac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ef0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f18 80 .cfa: sp 0 + .ra: x30
STACK CFI 3f1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f34 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3f98 9c .cfa: sp 0 + .ra: x30
STACK CFI 3f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fac x19: .cfa -16 + ^
STACK CFI 4030 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4038 39c .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 404c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43d8 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 43dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43fc x21: .cfa -16 + ^
STACK CFI 4758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 475c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4780 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4784 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 478c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47a8 x21: .cfa -32 + ^
STACK CFI 4818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 481c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4820 17c .cfa: sp 0 + .ra: x30
STACK CFI 4824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4830 x19: .cfa -32 + ^
STACK CFI 48c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 49a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49a8 17c .cfa: sp 0 + .ra: x30
STACK CFI 49b0 .cfa: sp 128 +
STACK CFI INIT 4b28 12c .cfa: sp 0 + .ra: x30
STACK CFI 4b2c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4b34 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4b40 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4b60 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4b70 x25: .cfa -64 + ^
STACK CFI 4c14 x23: x23 x24: x24
STACK CFI 4c18 x25: x25
STACK CFI 4c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c40 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 4c4c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4c50 x25: .cfa -64 + ^
STACK CFI INIT 4c58 154 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4db0 258 .cfa: sp 0 + .ra: x30
STACK CFI 4db4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4dbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4dc8 x21: .cfa -32 + ^
STACK CFI 4e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5008 5c .cfa: sp 0 + .ra: x30
STACK CFI 500c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5014 x19: .cfa -16 + ^
STACK CFI 5048 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 504c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 505c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5068 48 .cfa: sp 0 + .ra: x30
STACK CFI 506c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 507c x19: .cfa -16 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 50b0 90 .cfa: sp 0 + .ra: x30
STACK CFI 50b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 512c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5130 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 513c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5140 ac .cfa: sp 0 + .ra: x30
STACK CFI 5144 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5160 x21: .cfa -16 + ^
STACK CFI 516c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 51bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 51f0 dc .cfa: sp 0 + .ra: x30
STACK CFI 51f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5204 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5210 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 52a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52a8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 52d0 90 .cfa: sp 0 + .ra: x30
STACK CFI 52d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52dc x19: .cfa -16 + ^
STACK CFI 5340 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5348 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5358 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5360 638 .cfa: sp 0 + .ra: x30
STACK CFI 5364 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 536c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 5378 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 539c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 53a4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 53bc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 54d8 x25: x25 x26: x26
STACK CFI 54e0 x23: x23 x24: x24
STACK CFI 5510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5514 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 5550 x23: x23 x24: x24
STACK CFI 5558 x25: x25 x26: x26
STACK CFI 556c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 5884 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 588c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 58b4 x23: x23 x24: x24
STACK CFI 58bc x25: x25 x26: x26
STACK CFI 58c0 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 598c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5990 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5994 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 5998 354 .cfa: sp 0 + .ra: x30
STACK CFI 599c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 59a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 59ac x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 59bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 59dc x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5cc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5cf0 488 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5d04 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5d14 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5d2c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5d38 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5d44 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5ea4 x21: x21 x22: x22
STACK CFI 5ea8 x25: x25 x26: x26
STACK CFI 5eac x27: x27 x28: x28
STACK CFI 5eb4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5f2c x21: x21 x22: x22
STACK CFI 5f30 x25: x25 x26: x26
STACK CFI 5f34 x27: x27 x28: x28
STACK CFI 5f38 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5f74 x21: x21 x22: x22
STACK CFI 5f7c x25: x25 x26: x26
STACK CFI 5f88 x27: x27 x28: x28
STACK CFI 5f90 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5ff4 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 6038 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 6040 x21: x21 x22: x22
STACK CFI 6044 x25: x25 x26: x26
STACK CFI 6048 x27: x27 x28: x28
STACK CFI 604c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 60fc x21: x21 x22: x22
STACK CFI 6100 x25: x25 x26: x26
STACK CFI 6104 x27: x27 x28: x28
STACK CFI 610c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6130 x21: x21 x22: x22
STACK CFI 6134 x25: x25 x26: x26
STACK CFI 613c x27: x27 x28: x28
STACK CFI 6148 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6150 x21: x21 x22: x22
STACK CFI 6154 x25: x25 x26: x26
STACK CFI 6158 x27: x27 x28: x28
STACK CFI 615c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6168 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 616c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6170 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6174 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 6178 c3c .cfa: sp 0 + .ra: x30
STACK CFI 617c .cfa: sp 752 +
STACK CFI 6180 .ra: .cfa -744 + ^ x29: .cfa -752 + ^
STACK CFI 6188 x19: .cfa -736 + ^ x20: .cfa -728 + ^
STACK CFI 6198 x21: .cfa -720 + ^ x22: .cfa -712 + ^
STACK CFI 61a4 x23: .cfa -704 + ^ x24: .cfa -696 + ^
STACK CFI 61d0 x27: .cfa -672 + ^ x28: .cfa -664 + ^
STACK CFI 6208 x25: .cfa -688 + ^ x26: .cfa -680 + ^
STACK CFI 627c x25: x25 x26: x26
STACK CFI 62b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 62b8 .cfa: sp 752 + .ra: .cfa -744 + ^ x19: .cfa -736 + ^ x20: .cfa -728 + ^ x21: .cfa -720 + ^ x22: .cfa -712 + ^ x23: .cfa -704 + ^ x24: .cfa -696 + ^ x25: .cfa -688 + ^ x26: .cfa -680 + ^ x27: .cfa -672 + ^ x28: .cfa -664 + ^ x29: .cfa -752 + ^
STACK CFI 6308 x25: x25 x26: x26
STACK CFI 631c x25: .cfa -688 + ^ x26: .cfa -680 + ^
STACK CFI 6374 x25: x25 x26: x26
STACK CFI 6378 x25: .cfa -688 + ^ x26: .cfa -680 + ^
STACK CFI 63c8 x25: x25 x26: x26
STACK CFI 63d8 x25: .cfa -688 + ^ x26: .cfa -680 + ^
STACK CFI 6dac x25: x25 x26: x26
STACK CFI 6db0 x25: .cfa -688 + ^ x26: .cfa -680 + ^
STACK CFI INIT 6db8 354 .cfa: sp 0 + .ra: x30
STACK CFI 6dbc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6dc4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6dcc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6ddc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6dfc x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 70e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 70e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7110 ed8 .cfa: sp 0 + .ra: x30
STACK CFI 7114 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 711c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 7128 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 7134 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 7140 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 714c x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 7810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7814 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7fe8 fc .cfa: sp 0 + .ra: x30
STACK CFI 7fec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ff4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8004 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 80a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 80d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 80e8 484 .cfa: sp 0 + .ra: x30
STACK CFI 80ec .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8100 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 810c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 813c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8180 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8270 x27: x27 x28: x28
STACK CFI 82a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 82a8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 8358 x27: x27 x28: x28
STACK CFI 835c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 842c x27: x27 x28: x28
STACK CFI 84cc x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8508 x27: x27 x28: x28
STACK CFI 8524 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8560 x27: x27 x28: x28
STACK CFI 8568 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 8570 13d8 .cfa: sp 0 + .ra: x30
STACK CFI 8574 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 857c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 858c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 85ac x21: .cfa -176 + ^ x22: .cfa -168 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 8608 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 86b8 x23: x23 x24: x24
STACK CFI 86f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 86f8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 8728 x23: x23 x24: x24
STACK CFI 872c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8b14 x23: x23 x24: x24
STACK CFI 8b18 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 9940 x23: x23 x24: x24
STACK CFI 9944 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 9948 120 .cfa: sp 0 + .ra: x30
STACK CFI 994c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9954 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9964 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9980 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9a64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9a68 524 .cfa: sp 0 + .ra: x30
STACK CFI 9a70 .cfa: sp 5088 +
STACK CFI 9a74 .ra: .cfa -5080 + ^ x29: .cfa -5088 + ^
STACK CFI 9a7c x19: .cfa -5072 + ^ x20: .cfa -5064 + ^
STACK CFI 9a84 x23: .cfa -5040 + ^ x24: .cfa -5032 + ^
STACK CFI 9a8c x21: .cfa -5056 + ^ x22: .cfa -5048 + ^
STACK CFI 9cdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9ce0 .cfa: sp 5088 + .ra: .cfa -5080 + ^ x19: .cfa -5072 + ^ x20: .cfa -5064 + ^ x21: .cfa -5056 + ^ x22: .cfa -5048 + ^ x23: .cfa -5040 + ^ x24: .cfa -5032 + ^ x29: .cfa -5088 + ^
STACK CFI 9d78 x25: .cfa -5024 + ^
STACK CFI 9ec8 x25: x25
STACK CFI 9ecc x25: .cfa -5024 + ^
STACK CFI 9ed0 x25: x25
STACK CFI 9f2c x25: .cfa -5024 + ^
STACK CFI 9f3c x25: x25
STACK CFI 9f58 x25: .cfa -5024 + ^
STACK CFI 9f68 x25: x25
STACK CFI 9f6c x25: .cfa -5024 + ^
STACK CFI 9f84 x25: x25
STACK CFI 9f88 x25: .cfa -5024 + ^
STACK CFI INIT 9f90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9fa8 354 .cfa: sp 0 + .ra: x30
STACK CFI 9fac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9fb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9fbc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9fcc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9fec x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a2d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT a300 718 .cfa: sp 0 + .ra: x30
STACK CFI a304 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a30c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a314 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a33c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a348 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a34c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a3cc x25: x25 x26: x26
STACK CFI a3d0 x27: x27 x28: x28
STACK CFI a3d8 x19: x19 x20: x20
STACK CFI a3dc x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a54c x19: x19 x20: x20
STACK CFI a550 x25: x25 x26: x26
STACK CFI a554 x27: x27 x28: x28
STACK CFI a564 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a5e4 x19: x19 x20: x20
STACK CFI a5e8 x25: x25 x26: x26
STACK CFI a5ec x27: x27 x28: x28
STACK CFI a610 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a614 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI a784 x19: x19 x20: x20
STACK CFI a788 x25: x25 x26: x26
STACK CFI a78c x27: x27 x28: x28
STACK CFI a790 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a7b8 x19: x19 x20: x20
STACK CFI a7bc x25: x25 x26: x26
STACK CFI a7c0 x27: x27 x28: x28
STACK CFI a7c8 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI aa08 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI aa0c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI aa10 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI aa14 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT aa18 34 .cfa: sp 0 + .ra: x30
STACK CFI aa1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa24 x19: .cfa -16 + ^
STACK CFI aa48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aa50 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT aa70 498 .cfa: sp 0 + .ra: x30
STACK CFI aa74 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI aa7c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI aa84 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI aa94 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI aadc x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI aae0 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ab20 x25: x25 x26: x26
STACK CFI ab28 x27: x27 x28: x28
STACK CFI ab54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ab58 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI ad4c x25: x25 x26: x26
STACK CFI ad50 x27: x27 x28: x28
STACK CFI ad54 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ad64 x25: x25 x26: x26
STACK CFI ad68 x27: x27 x28: x28
STACK CFI ad6c x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI aefc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI af00 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI af04 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT af08 710 .cfa: sp 0 + .ra: x30
STACK CFI af10 .cfa: sp 4272 +
STACK CFI af14 .ra: .cfa -4264 + ^ x29: .cfa -4272 + ^
STACK CFI af24 x23: .cfa -4224 + ^ x24: .cfa -4216 + ^
STACK CFI af34 x19: .cfa -4256 + ^ x20: .cfa -4248 + ^
STACK CFI af84 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^
STACK CFI af88 x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI af90 x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI b18c x21: x21 x22: x22
STACK CFI b190 x25: x25 x26: x26
STACK CFI b194 x27: x27 x28: x28
STACK CFI b220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI b224 .cfa: sp 4272 + .ra: .cfa -4264 + ^ x19: .cfa -4256 + ^ x20: .cfa -4248 + ^ x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x23: .cfa -4224 + ^ x24: .cfa -4216 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^ x29: .cfa -4272 + ^
STACK CFI b474 x21: x21 x22: x22
STACK CFI b478 x25: x25 x26: x26
STACK CFI b47c x27: x27 x28: x28
STACK CFI b484 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI b5bc x21: x21 x22: x22
STACK CFI b5c0 x25: x25 x26: x26
STACK CFI b5c4 x27: x27 x28: x28
STACK CFI b5c8 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI b5e8 x21: x21 x22: x22
STACK CFI b5ec x25: x25 x26: x26
STACK CFI b5f0 x27: x27 x28: x28
STACK CFI b5f4 x21: .cfa -4240 + ^ x22: .cfa -4232 + ^ x25: .cfa -4208 + ^ x26: .cfa -4200 + ^ x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI b608 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b60c x21: .cfa -4240 + ^ x22: .cfa -4232 + ^
STACK CFI b610 x25: .cfa -4208 + ^ x26: .cfa -4200 + ^
STACK CFI b614 x27: .cfa -4192 + ^ x28: .cfa -4184 + ^
STACK CFI INIT b618 9ec .cfa: sp 0 + .ra: x30
STACK CFI b61c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI b628 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI b634 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI b6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b6e0 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI b6f0 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI b740 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI b744 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bf34 x25: x25 x26: x26
STACK CFI bf38 x27: x27 x28: x28
STACK CFI bf58 x23: x23 x24: x24
STACK CFI bf60 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bfa4 x25: x25 x26: x26
STACK CFI bfac x27: x27 x28: x28
STACK CFI bfbc x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bfe8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bff4 x23: x23 x24: x24
STACK CFI bff8 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI bffc x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI c000 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT c008 444 .cfa: sp 0 + .ra: x30
STACK CFI c00c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI c018 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI c028 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI c03c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI c07c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI c0f8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c2ac x27: x27 x28: x28
STACK CFI c2b8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c2dc x25: x25 x26: x26
STACK CFI c2e0 x27: x27 x28: x28
STACK CFI c30c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c310 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI c378 x25: x25 x26: x26
STACK CFI c37c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c390 x25: x25 x26: x26
STACK CFI c394 x27: x27 x28: x28
STACK CFI c398 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI c39c x25: x25 x26: x26
STACK CFI c3a0 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c3b8 x27: x27 x28: x28
STACK CFI c3bc x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c3f8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c400 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c43c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c440 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI c444 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT c450 608 .cfa: sp 0 + .ra: x30
STACK CFI c454 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI c478 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI c488 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI c49c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI c9e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c9e8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT ca58 174 .cfa: sp 0 + .ra: x30
STACK CFI ca5c .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI ca68 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI ca74 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI ca80 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI ca90 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI cb8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI cb90 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x29: .cfa -400 + ^
STACK CFI INIT cbd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cbd8 2f4 .cfa: sp 0 + .ra: x30
STACK CFI cbdc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI cbe4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI cbf0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI cc4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cc50 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI cc60 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI cc64 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI cc68 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI cc78 x23: x23 x24: x24
STACK CFI cc7c x25: x25 x26: x26
STACK CFI cc80 x27: x27 x28: x28
STACK CFI cc84 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ce58 x23: x23 x24: x24
STACK CFI ce5c x25: x25 x26: x26
STACK CFI ce60 x27: x27 x28: x28
STACK CFI ce64 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI cea4 x23: x23 x24: x24
STACK CFI cea8 x25: x25 x26: x26
STACK CFI ceac x27: x27 x28: x28
STACK CFI ceb0 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ceb8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI cebc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI cec0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI cec4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT ced0 3c .cfa: sp 0 + .ra: x30
STACK CFI ced4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cf08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cf10 208 .cfa: sp 0 + .ra: x30
STACK CFI cf20 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI cf30 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cf40 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI cf48 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI cf74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cf80 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI cf84 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI cf88 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI cf98 x19: x19 x20: x20
STACK CFI cfa0 x23: x23 x24: x24
STACK CFI cfac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cfb0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT d118 3c4 .cfa: sp 0 + .ra: x30
STACK CFI d11c .cfa: sp 272 +
STACK CFI d124 .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI d12c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI d148 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI d16c x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI d258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d25c .cfa: sp 272 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI d26c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI d27c x23: x23 x24: x24
STACK CFI d280 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI d3d8 x23: x23 x24: x24
STACK CFI d3dc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI d3e4 x23: x23 x24: x24
STACK CFI d3f0 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI d4bc x23: x23 x24: x24
STACK CFI d4c0 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI d4d0 x23: x23 x24: x24
STACK CFI d4d8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT d4e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI d4e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d4ec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d4f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI d50c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI d528 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI d5a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d5a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
