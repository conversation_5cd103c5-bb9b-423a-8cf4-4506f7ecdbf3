MODULE Linux arm64 BEB3AB9CB670B4EF89A2AEEAA31513BF0 libSM.so.6
INFO CODE_ID 9CABB3BE70B6EFB489A2AEEAA31513BF9F70A3EC
PUBLIC 1de8 0 SmcOpenConnection
PUBLIC 2390 0 SmcCloseConnection
PUBLIC 25f8 0 SmcModifyCallbacks
PUBLIC 2600 0 SmcSetProperties
PUBLIC 2988 0 SmcDeleteProperties
PUBLIC 2b58 0 SmcGetProperties
PUBLIC 2c38 0 SmcInteractRequest
PUBLIC 2d30 0 SmcInteractDone
PUBLIC 2db0 0 SmcRequestSaveYourself
PUBLIC 2e60 0 SmcRequestSaveYourselfPhase2
PUBLIC 2f10 0 SmcSaveYourselfDone
PUBLIC 2f90 0 _SmsDefaultErrorHandler
PUBLIC 32c8 0 _SmcDefaultErrorHandler
PUBLIC 3670 0 SmcSetErrorHandler
PUBLIC 3698 0 SmsSetErrorHandler
PUBLIC 36c0 0 SmsGenerateClientID
PUBLIC 3868 0 SmsInitialize
PUBLIC 39e8 0 SmsClientHostName
PUBLIC 39f0 0 SmsRegisterClientReply
PUBLIC 3b50 0 SmsSaveYourself
PUBLIC 3c50 0 SmsSaveYourselfPhase2
PUBLIC 3cc8 0 SmsInteract
PUBLIC 3d50 0 SmsDie
PUBLIC 3dc8 0 SmsSaveComplete
PUBLIC 3e40 0 SmsShutdownCancelled
PUBLIC 3ec8 0 SmsReturnProperties
PUBLIC 4240 0 SmsCleanUp
PUBLIC 4280 0 SmFreeProperty
PUBLIC 4310 0 SmFreeReasons
PUBLIC 4368 0 SmcProtocolVersion
PUBLIC 4370 0 SmcProtocolRevision
PUBLIC 4378 0 SmcVendor
PUBLIC 4380 0 SmcRelease
PUBLIC 4388 0 SmcClientID
PUBLIC 4390 0 SmcGetIceConnection
PUBLIC 4398 0 SmsProtocolVersion
PUBLIC 43a0 0 SmsProtocolRevision
PUBLIC 43a8 0 SmsClientID
PUBLIC 43b0 0 SmsGetIceConnection
PUBLIC 4760 0 _SmcProcessMessage
PUBLIC 4ed0 0 _SmsProcessMessage
STACK CFI INIT 1cd8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d08 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d40 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d4c x19: .cfa -16 + ^
STACK CFI 1d84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d90 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de8 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 272 +
STACK CFI 1dfc .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1e08 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1e10 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1e18 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1e30 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1e38 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 2124 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2128 .cfa: sp 272 + .ra: .cfa -248 + ^ v8: .cfa -160 + ^ v9: .cfa -152 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2390 268 .cfa: sp 0 + .ra: x30
STACK CFI 2394 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2568 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 258c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2590 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 25f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2600 384 .cfa: sp 0 + .ra: x30
STACK CFI 2604 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2614 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2674 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2688 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2888 x25: x25 x26: x26
STACK CFI 288c x27: x27 x28: x28
STACK CFI 28c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2914 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2944 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2988 1cc .cfa: sp 0 + .ra: x30
STACK CFI 298c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2998 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29bc x25: .cfa -16 + ^
STACK CFI 2a08 x25: x25
STACK CFI 2ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ae8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2b08 x25: .cfa -16 + ^
STACK CFI 2b44 x25: x25
STACK CFI INIT 2b58 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2c38 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2c3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c44 x21: .cfa -32 + ^
STACK CFI 2c4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2cf8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 2d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d30 7c .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2db0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2db4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2dbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2dc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2dd0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2e60 ac .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ee0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2f10 7c .cfa: sp 0 + .ra: x30
STACK CFI 2f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f90 334 .cfa: sp 0 + .ra: x30
STACK CFI 2f94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2fa0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2fa8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2fb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2fbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fc4 x27: .cfa -16 + ^
STACK CFI 31dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 31e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 32c8 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 32cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 32d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 32e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 32ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 32f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32fc x27: .cfa -16 + ^
STACK CFI 34b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 34b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 354c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3550 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3670 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3698 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36c0 74 .cfa: sp 0 + .ra: x30
STACK CFI 36c4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 36cc x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 36dc x21: .cfa -304 + ^
STACK CFI 372c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3730 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x29: .cfa -336 + ^
STACK CFI INIT 3738 12c .cfa: sp 0 + .ra: x30
STACK CFI 373c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3744 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3750 x25: .cfa -32 + ^
STACK CFI 375c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3764 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3848 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3868 17c .cfa: sp 0 + .ra: x30
STACK CFI 386c .cfa: sp 160 +
STACK CFI 3878 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3884 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 388c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 38a4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 394c .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 39e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39f0 15c .cfa: sp 0 + .ra: x30
STACK CFI 39f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a24 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3abc x23: x23 x24: x24
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3b38 x23: x23 x24: x24
STACK CFI 3b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3b50 100 .cfa: sp 0 + .ra: x30
STACK CFI 3b54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3b5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3b74 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3c28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3c50 74 .cfa: sp 0 + .ra: x30
STACK CFI 3c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c5c x19: .cfa -16 + ^
STACK CFI 3cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3cc8 88 .cfa: sp 0 + .ra: x30
STACK CFI 3ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d50 74 .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d5c x19: .cfa -16 + ^
STACK CFI 3db0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3dc8 74 .cfa: sp 0 + .ra: x30
STACK CFI 3dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3dd4 x19: .cfa -16 + ^
STACK CFI 3e28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3e40 88 .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3eb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ec8 374 .cfa: sp 0 + .ra: x30
STACK CFI 3ecc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3edc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3f3c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3f48 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4140 x25: x25 x26: x26
STACK CFI 4144 x27: x27 x28: x28
STACK CFI 4180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4184 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 41cc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 41f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 41fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4240 40 .cfa: sp 0 + .ra: x30
STACK CFI 4244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4254 x19: .cfa -16 + ^
STACK CFI 427c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4280 8c .cfa: sp 0 + .ra: x30
STACK CFI 4288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4290 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4310 58 .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4324 x21: .cfa -16 + ^
STACK CFI 4334 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4354 x19: x19 x20: x20
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 4368 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4378 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4388 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4398 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43b8 dc .cfa: sp 0 + .ra: x30
STACK CFI 43bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 43fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4454 x21: x21 x22: x22
STACK CFI 445c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 4460 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4464 x21: x21 x22: x22
STACK CFI 4478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 447c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4488 x21: x21 x22: x22
STACK CFI 4490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 4498 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 449c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 44b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 44bc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 44e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4510 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4518 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 46b0 x19: x19 x20: x20
STACK CFI 46b8 x23: x23 x24: x24
STACK CFI 46bc x25: x25 x26: x26
STACK CFI 46c0 x27: x27 x28: x28
STACK CFI 46cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 46d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 46e4 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI 46e8 x23: x23 x24: x24
STACK CFI 46ec x25: x25 x26: x26
STACK CFI 46fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4700 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4714 x19: x19 x20: x20
STACK CFI 4718 x27: x27 x28: x28
STACK CFI 4724 x23: x23 x24: x24
STACK CFI 4728 x25: x25 x26: x26
STACK CFI 472c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4738 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4760 770 .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 476c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4778 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4788 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 485c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4860 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 4898 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4978 x25: x25 x26: x26
STACK CFI 4a10 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4acc x25: x25 x26: x26
STACK CFI 4bb8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4bbc x25: x25 x26: x26
STACK CFI 4bc0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4bdc x25: x25 x26: x26
STACK CFI 4bfc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4c1c x25: x25 x26: x26
STACK CFI 4c68 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4d1c x25: x25 x26: x26
STACK CFI 4d38 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4e14 x25: x25 x26: x26
STACK CFI 4e2c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4e3c x25: x25 x26: x26
STACK CFI 4e54 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4e6c x25: x25 x26: x26
STACK CFI 4e9c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4ec8 x25: x25 x26: x26
STACK CFI 4ecc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 4ed0 ac8 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4edc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4ee8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4ef8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4f3c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4fdc x25: x25 x26: x26
STACK CFI 5028 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 50d0 x25: x25 x26: x26
STACK CFI 517c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5180 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 5204 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5260 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 532c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5370 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 53cc x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 54c0 x25: x25 x26: x26
STACK CFI 54c4 x27: x27 x28: x28
STACK CFI 54d0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5598 x25: x25 x26: x26
STACK CFI 5600 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 561c x25: x25 x26: x26
STACK CFI 5678 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 569c x25: x25 x26: x26
STACK CFI 56bc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5710 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 571c x25: x25 x26: x26
STACK CFI 5720 x27: x27 x28: x28
STACK CFI 57c0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5860 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 58a0 x25: x25 x26: x26
STACK CFI 58a4 x27: x27 x28: x28
STACK CFI 58a8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 58cc x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5900 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5918 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5954 x25: x25 x26: x26
STACK CFI 5990 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5994 x27: .cfa -64 + ^ x28: .cfa -56 + ^
