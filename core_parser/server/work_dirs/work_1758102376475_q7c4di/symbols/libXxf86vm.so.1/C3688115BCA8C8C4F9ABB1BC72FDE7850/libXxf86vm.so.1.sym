MODULE Linux arm64 C3688115BCA8C8C4F9ABB1BC72FDE7850 libXxf86vm.so.1
INFO CODE_ID 158168C3A8BCC4C8F9ABB1BC72FDE78569B7EC98
PUBLIC 11a0 0 XF86VidModeQueryExtension
PUBLIC 11f8 0 XF86VidModeSetClientVersion
PUBLIC 12b8 0 XF86VidModeQueryVersion
PUBLIC 1410 0 XF86VidModeSetGamma
PUBLIC 1520 0 XF86VidModeGetGamma
PUBLIC 1688 0 XF86VidModeGetModeLine
PUBLIC 18a8 0 XF86VidModeGetAllModeLines
PUBLIC 1c30 0 XF86VidModeAddModeLine
PUBLIC 1f60 0 XF86VidModeDeleteModeLine
PUBLIC 21b8 0 XF86VidModeModModeLine
PUBLIC 2408 0 XF86VidModeValidateModeLine
PUBLIC 26b0 0 XF86VidModeSwitchMode
PUBLIC 2788 0 XF86VidModeSwitchToMode
PUBLIC 2a18 0 XF86VidModeLockModeSwitch
PUBLIC 2af0 0 XF86VidModeGetMonitor
PUBLIC 2e60 0 XF86VidModeGetViewPort
PUBLIC 3028 0 XF86VidModeSetViewPort
PUBLIC 3110 0 XF86VidModeGetDotClocks
PUBLIC 32c0 0 XF86VidModeSetGammaRamp
PUBLIC 3428 0 XF86VidModeGetGammaRamp
PUBLIC 35b8 0 XF86VidModeGetGammaRampSize
PUBLIC 36f8 0 XF86VidModeGetPermissions
STACK CFI INIT 1058 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1088 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 10cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10d4 x19: .cfa -16 + ^
STACK CFI 110c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1110 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1118 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1128 78 .cfa: sp 0 + .ra: x30
STACK CFI 112c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1134 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 115c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11a0 58 .cfa: sp 0 + .ra: x30
STACK CFI 11a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 11f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11f8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 11fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1204 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1294 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12b8 158 .cfa: sp 0 + .ra: x30
STACK CFI 12bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 12c4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12e8 x23: .cfa -64 + ^
STACK CFI 13c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 13cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1410 110 .cfa: sp 0 + .ra: x30
STACK CFI 1414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 141c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1424 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 151c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1520 168 .cfa: sp 0 + .ra: x30
STACK CFI 1524 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 152c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 153c x23: .cfa -64 + ^
STACK CFI 1544 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 164c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1688 220 .cfa: sp 0 + .ra: x30
STACK CFI 168c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1694 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 16a4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 16bc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 17f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 17f8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 18a8 388 .cfa: sp 0 + .ra: x30
STACK CFI 18ac .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 18b4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 18c0 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 18d8 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 18e0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 19cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19d0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 19f4 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1b00 x27: x27 x28: x28
STACK CFI 1b04 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1b30 x27: x27 x28: x28
STACK CFI 1b48 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1bcc x27: x27 x28: x28
STACK CFI 1c00 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1c04 x27: x27 x28: x28
STACK CFI 1c0c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1c20 x27: x27 x28: x28
STACK CFI INIT 1c30 32c .cfa: sp 0 + .ra: x30
STACK CFI 1c34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1c3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1c48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1c60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1de0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1f60 258 .cfa: sp 0 + .ra: x30
STACK CFI 1f64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f6c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1f90 x23: .cfa -32 + ^
STACK CFI 20ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 21b8 250 .cfa: sp 0 + .ra: x30
STACK CFI 21bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 21c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 21d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 21e8 x23: .cfa -32 + ^
STACK CFI 2300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2304 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2408 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 240c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2414 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2424 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2438 x23: .cfa -64 + ^
STACK CFI 256c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2570 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 26b0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 26b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 275c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2788 28c .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2794 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 27b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 28e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2a18 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2af0 36c .cfa: sp 0 + .ra: x30
STACK CFI 2af4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2afc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2b0c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2b20 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2ba0 x25: .cfa -64 + ^
STACK CFI 2c1c v8: .cfa -56 + ^
STACK CFI 2d0c v8: v8
STACK CFI 2d34 x25: x25
STACK CFI 2d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d60 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2da0 v8: .cfa -56 + ^ x25: .cfa -64 + ^
STACK CFI 2db4 v8: v8
STACK CFI 2db8 v8: .cfa -56 + ^
STACK CFI 2dcc v8: v8
STACK CFI 2df8 x25: x25
STACK CFI 2dfc x25: .cfa -64 + ^
STACK CFI 2e04 x25: x25
STACK CFI 2e08 x25: .cfa -64 + ^
STACK CFI 2e0c v8: .cfa -56 + ^
STACK CFI 2e10 v8: v8
STACK CFI INIT 2e60 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2e6c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2e7c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2e94 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2ebc x25: .cfa -64 + ^
STACK CFI 2f44 x25: x25
STACK CFI 2f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f70 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 2f88 x25: x25
STACK CFI 2fa0 x25: .cfa -64 + ^
STACK CFI 301c x25: x25
STACK CFI 3024 x25: .cfa -64 + ^
STACK CFI INIT 3028 e4 .cfa: sp 0 + .ra: x30
STACK CFI 302c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3034 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 303c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3048 x23: .cfa -16 + ^
STACK CFI 30dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 30e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3110 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3114 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 311c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 312c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3144 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 314c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3218 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 32c0 168 .cfa: sp 0 + .ra: x30
STACK CFI 32c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 32cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 32f8 x25: .cfa -16 + ^
STACK CFI 33d4 x25: x25
STACK CFI 33d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 33dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 33e0 x25: x25
STACK CFI 3408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 340c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3420 x25: x25
STACK CFI 3424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3428 18c .cfa: sp 0 + .ra: x30
STACK CFI 342c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3434 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3444 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 345c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3464 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3534 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 35b8 13c .cfa: sp 0 + .ra: x30
STACK CFI 35bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 35c4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 35d4 x23: .cfa -64 + ^
STACK CFI 35dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 36b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 36bc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 36f8 13c .cfa: sp 0 + .ra: x30
STACK CFI 36fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3704 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3714 x23: .cfa -64 + ^
STACK CFI 371c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 37f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 37fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
