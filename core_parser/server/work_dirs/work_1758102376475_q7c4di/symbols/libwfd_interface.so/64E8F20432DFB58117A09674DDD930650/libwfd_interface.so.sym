MODULE Linux arm64 64E8F20432DFB58117A09674DDD930650 libwfd_interface.so
INFO CODE_ID 04F2E864DF3281B517A09674DDD93065
PUBLIC 1968 0 _init
PUBLIC 1c10 0 call_weak_fn
PUBLIC 1c24 0 deregister_tm_clones
PUBLIC 1c54 0 register_tm_clones
PUBLIC 1c90 0 __do_global_dtors_aux
PUBLIC 1ce0 0 frame_dummy
PUBLIC 1cf0 0 lios::wfd::WfdNvMedia::GetWfdBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 1df0 0 lios::wfd::WfdNvMedia::WfdFlip(NvSciBufObjRefRec*&)
PUBLIC 1f20 0 lios::wfd::WfdNvMedia::WfdClearDisplay()
PUBLIC 2040 0 lios::wfd::WfdNvMedia::~WfdNvMedia()
PUBLIC 2190 0 lios::wfd::WfdNvMedia::Init()
PUBLIC 26a0 0 lios::wfd::WfdNvMedia::GetBuffers(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&)
PUBLIC 2720 0 lios::wfd::WfdNvMedia::RegisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 29f0 0 void std::vector<int, std::allocator<int> >::_M_realloc_insert<int const&>(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, int const&)
PUBLIC 2b20 0 void std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >::_M_realloc_insert<NvSciBufObjRefRec* const&>(__gnu_cxx::__normal_iterator<NvSciBufObjRefRec**, std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > >, NvSciBufObjRefRec* const&)
PUBLIC 2c50 0 void std::vector<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >, std::allocator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > > >::_M_realloc_insert<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > >(__gnu_cxx::__normal_iterator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >*, std::vector<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >, std::allocator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > > > >, std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >&&)
PUBLIC 2dac 0 _fini
STACK CFI INIT 1c24 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c54 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c90 50 .cfa: sp 0 + .ra: x30
STACK CFI 1ca0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ca8 x19: .cfa -16 + ^
STACK CFI 1cd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ce0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cf0 100 .cfa: sp 0 + .ra: x30
STACK CFI 1cf4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1d40 x19: .cfa -160 + ^
STACK CFI 1db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1db8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI 1de4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1de8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1df0 12c .cfa: sp 0 + .ra: x30
STACK CFI 1df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1dfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e18 x21: .cfa -16 + ^
STACK CFI 1e9c x21: x21
STACK CFI 1ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1eac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ed0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1ef4 x21: x21
STACK CFI 1ef8 x21: .cfa -16 + ^
STACK CFI 1f18 x21: x21
STACK CFI INIT 1f20 118 .cfa: sp 0 + .ra: x30
STACK CFI 1f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f2c x19: .cfa -16 + ^
STACK CFI 1f58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2040 148 .cfa: sp 0 + .ra: x30
STACK CFI 2044 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2054 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 205c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2068 x23: .cfa -16 + ^
STACK CFI 20c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 20d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 29f0 128 .cfa: sp 0 + .ra: x30
STACK CFI 29f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2a04 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2a18 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2aa8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2190 50c .cfa: sp 0 + .ra: x30
STACK CFI 2194 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 21a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2238 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 22b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 22bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2408 x21: x21 x22: x22
STACK CFI 240c x23: x23 x24: x24
STACK CFI 2410 x25: x25 x26: x26
STACK CFI 2438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 243c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 2468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 246c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 2490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2494 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 24b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 24c8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 24ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 250c x23: x23 x24: x24
STACK CFI 2510 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2530 x23: x23 x24: x24
STACK CFI 2534 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 254c x23: x23 x24: x24
STACK CFI 2550 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25f8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 2618 x23: x23 x24: x24
STACK CFI 261c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2b20 128 .cfa: sp 0 + .ra: x30
STACK CFI 2b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b34 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2b48 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2bd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 26a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 26a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26b4 x21: .cfa -16 + ^
STACK CFI 2714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2c50 15c .cfa: sp 0 + .ra: x30
STACK CFI 2c54 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c60 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c78 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2d74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2720 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 2724 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 272c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2734 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 273c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 27b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 27c0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 290c x23: x23 x24: x24
STACK CFI 2910 x25: x25 x26: x26
STACK CFI 292c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 2930 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2984 x23: x23 x24: x24
STACK CFI 2988 x25: x25 x26: x26
STACK CFI 2990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 2994 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
