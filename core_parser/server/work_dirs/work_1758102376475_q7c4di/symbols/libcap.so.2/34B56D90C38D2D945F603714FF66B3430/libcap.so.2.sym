MODULE Linux arm64 34B56D90C38D2D945F603714FF66B3430 libcap.so.2
INFO CODE_ID 906DB5348DC3942D5F603714FF66B343A255452D
PUBLIC 1dd8 0 cap_max_bits
PUBLIC 1de8 0 _libcap_strdup
PUBLIC 1e98 0 cap_free
PUBLIC 1f40 0 cap_init
PUBLIC 1fe8 0 cap_dup
PUBLIC 2128 0 psx_load_syscalls
PUBLIC 2138 0 cap_set_syscall
PUBLIC 2160 0 cap_get_proc
PUBLIC 21a8 0 cap_set_proc
PUBLIC 2220 0 capgetp
PUBLIC 2288 0 cap_get_pid
PUBLIC 22f0 0 capsetp
PUBLIC 2360 0 cap_get_bound
PUBLIC 23a8 0 cap_drop_bound
PUBLIC 23f0 0 cap_get_ambient
PUBLIC 2440 0 cap_set_ambient
PUBLIC 24b0 0 cap_reset_ambient
PUBLIC 2530 0 cap_get_secbits
PUBLIC 2540 0 cap_set_secbits
PUBLIC 2550 0 cap_set_mode
PUBLIC 26b0 0 cap_get_mode
PUBLIC 27a8 0 cap_setuid
PUBLIC 28d8 0 cap_setgroups
PUBLIC 2a38 0 cap_size
PUBLIC 2a40 0 cap_copy_ext
PUBLIC 2b00 0 cap_copy_int
PUBLIC 2c38 0 cap_get_flag
PUBLIC 2cc0 0 cap_set_flag
PUBLIC 2d78 0 cap_clear
PUBLIC 2dc8 0 cap_clear_flag
PUBLIC 2e20 0 cap_compare
PUBLIC 3100 0 cap_from_text
PUBLIC 36d8 0 cap_from_name
PUBLIC 3718 0 cap_to_name
PUBLIC 37b0 0 cap_to_text
PUBLIC 3da0 0 cap_mode_name
PUBLIC 4020 0 cap_get_fd
PUBLIC 40d0 0 cap_get_file
PUBLIC 4180 0 cap_get_nsowner
PUBLIC 4188 0 cap_set_fd
PUBLIC 4270 0 cap_set_file
PUBLIC 4358 0 cap_set_nsowner
STACK CFI INIT 1d18 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d48 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d88 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d94 x19: .cfa -16 + ^
STACK CFI 1dcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c80 80 .cfa: sp 0 + .ra: x30
STACK CFI 1c84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1cac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1dd8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1df8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e3c x19: x19 x20: x20
STACK CFI 1e48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e5c x19: x19 x20: x20
STACK CFI 1e68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e90 x19: x19 x20: x20
STACK CFI INIT 1e98 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1eb4 x19: .cfa -16 + ^
STACK CFI 1efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f40 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1fe8 7c .cfa: sp 0 + .ra: x30
STACK CFI 1fec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ff8 x19: .cfa -16 + ^
STACK CFI 2034 x19: x19
STACK CFI 2038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 203c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2040 x19: x19
STACK CFI 2058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 205c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2060 x19: x19
STACK CFI INIT 2068 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2070 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2078 60 .cfa: sp 0 + .ra: x30
STACK CFI 208c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20d8 4c .cfa: sp 0 + .ra: x30
STACK CFI 20e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 210c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2128 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2138 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2160 44 .cfa: sp 0 + .ra: x30
STACK CFI 2164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 216c x19: .cfa -16 + ^
STACK CFI 2190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2194 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 21a8 74 .cfa: sp 0 + .ra: x30
STACK CFI 21ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2204 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2220 64 .cfa: sp 0 + .ra: x30
STACK CFI 2224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2230 x19: .cfa -16 + ^
STACK CFI 2260 x19: x19
STACK CFI 2264 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2268 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 226c x19: x19
STACK CFI INIT 2288 64 .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2294 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 22c8 x21: .cfa -16 + ^
STACK CFI 22e8 x21: x21
STACK CFI INIT 22f0 6c .cfa: sp 0 + .ra: x30
STACK CFI 22f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2300 x19: .cfa -16 + ^
STACK CFI 2338 x19: x19
STACK CFI 233c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2340 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2344 x19: x19
STACK CFI INIT 2360 48 .cfa: sp 0 + .ra: x30
STACK CFI 2364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2378 x19: .cfa -16 + ^
STACK CFI 2390 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2394 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23a8 44 .cfa: sp 0 + .ra: x30
STACK CFI 23ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23bc x19: .cfa -16 + ^
STACK CFI 23d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 23d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 23f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2410 x19: .cfa -16 + ^
STACK CFI 2428 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 242c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2440 6c .cfa: sp 0 + .ra: x30
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2450 x19: .cfa -16 + ^
STACK CFI 2478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 247c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 24b0 80 .cfa: sp 0 + .ra: x30
STACK CFI 24b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24c4 x21: .cfa -16 + ^
STACK CFI 2504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2508 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 251c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2520 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2530 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2540 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2550 160 .cfa: sp 0 + .ra: x30
STACK CFI 2554 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2560 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2624 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 26b0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 26b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2778 x21: x21 x22: x22
STACK CFI 277c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2780 x21: x21 x22: x22
STACK CFI 278c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2790 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2798 x21: x21 x22: x22
STACK CFI 279c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 27a0 x21: x21 x22: x22
STACK CFI INIT 27a8 12c .cfa: sp 0 + .ra: x30
STACK CFI 27ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27c4 x23: .cfa -32 + ^
STACK CFI 27cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 28a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 28ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 28d8 160 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 28e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 28f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 290c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 29f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 29f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2a38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a40 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2ae0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2afc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b00 134 .cfa: sp 0 + .ra: x30
STACK CFI 2b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b10 x19: .cfa -16 + ^
STACK CFI 2bfc x19: x19
STACK CFI 2c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2c10 x19: x19
STACK CFI 2c28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2c38 88 .cfa: sp 0 + .ra: x30
STACK CFI 2ca0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2cbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cc0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d78 50 .cfa: sp 0 + .ra: x30
STACK CFI 2da8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2dc8 58 .cfa: sp 0 + .ra: x30
STACK CFI 2e00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e20 bc .cfa: sp 0 + .ra: x30
STACK CFI 2ebc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ed8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ee0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2ee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2eec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ef8 x21: .cfa -32 + ^
STACK CFI 3034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3038 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3090 70 .cfa: sp 0 + .ra: x30
STACK CFI 3094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 309c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3100 5d8 .cfa: sp 0 + .ra: x30
STACK CFI 3104 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 310c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3118 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3148 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3184 x23: x23 x24: x24
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 31bc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 31c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3250 x23: x23 x24: x24
STACK CFI 3254 x25: x25 x26: x26
STACK CFI 3258 x27: x27 x28: x28
STACK CFI 3260 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3268 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 326c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 352c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 353c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 36bc x23: x23 x24: x24
STACK CFI 36c0 x25: x25 x26: x26
STACK CFI 36c4 x27: x27 x28: x28
STACK CFI 36cc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 36d0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 36d4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 36d8 3c .cfa: sp 0 + .ra: x30
STACK CFI 36dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 36e4 x19: .cfa -32 + ^
STACK CFI 3710 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3718 98 .cfa: sp 0 + .ra: x30
STACK CFI 371c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 372c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3794 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 37b0 5ec .cfa: sp 0 + .ra: x30
STACK CFI 37b4 .cfa: sp 1616 +
STACK CFI 37b8 .ra: .cfa -1608 + ^ x29: .cfa -1616 + ^
STACK CFI 37c0 x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI 37e0 x23: .cfa -1568 + ^ x24: .cfa -1560 + ^
STACK CFI 37fc x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 3800 x21: .cfa -1584 + ^ x22: .cfa -1576 + ^
STACK CFI 3804 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 3aec x19: x19 x20: x20
STACK CFI 3af0 x21: x21 x22: x22
STACK CFI 3af4 x25: x25 x26: x26
STACK CFI 3b24 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3b28 .cfa: sp 1616 + .ra: .cfa -1608 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^ x29: .cfa -1616 + ^
STACK CFI 3b40 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 3d28 x19: x19 x20: x20
STACK CFI 3d2c x21: x21 x22: x22
STACK CFI 3d30 x25: x25 x26: x26
STACK CFI 3d34 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 3d8c x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 3d90 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 3d94 x21: .cfa -1584 + ^ x22: .cfa -1576 + ^
STACK CFI 3d98 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI INIT 3da0 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3df0 cc .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ec0 15c .cfa: sp 0 + .ra: x30
STACK CFI 3ec4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4020 ac .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 402c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4034 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 40ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 40d0 ac .cfa: sp 0 + .ra: x30
STACK CFI 40d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 40dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 40e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 415c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4160 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4180 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4188 e8 .cfa: sp 0 + .ra: x30
STACK CFI 418c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 4194 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 41a8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 4234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4238 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4270 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4274 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 427c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4290 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 431c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4320 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4358 10 .cfa: sp 0 + .ra: x30
