MODULE Linux arm64 14A05B099581760C52CB19EC8342F9210 libffi.so.7
INFO CODE_ID 095BA01481950C7652CB19EC8342F921B2EB8246
PUBLIC 1ca0 0 ffi_prep_cif
PUBLIC 1cb8 0 ffi_prep_cif_var
PUBLIC 1cd8 0 ffi_prep_closure
PUBLIC 1ce0 0 ffi_get_struct_offsets
PUBLIC 1d10 0 ffi_raw_size
PUBLIC 1d68 0 ffi_raw_to_ptrarray
PUBLIC 1dd8 0 ffi_ptrarray_to_raw
PUBLIC 1ff0 0 ffi_raw_call
PUBLIC 20d0 0 ffi_prep_raw_closure_loc
PUBLIC 2118 0 ffi_prep_raw_closure
PUBLIC 2120 0 ffi_java_raw_size
PUBLIC 2198 0 ffi_java_raw_to_ptrarray
PUBLIC 2208 0 ffi_java_ptrarray_to_raw
PUBLIC 2400 0 ffi_java_raw_call
PUBLIC 24e0 0 ffi_prep_java_raw_closure_loc
PUBLIC 2528 0 ffi_prep_java_raw_closure
PUBLIC 3020 0 ffi_closure_alloc
PUBLIC 45d8 0 ffi_closure_free
PUBLIC 5b68 0 ffi_call
PUBLIC 5b70 0 ffi_call_go
PUBLIC 5b78 0 ffi_prep_closure_loc
PUBLIC 5c18 0 ffi_prep_go_closure
STACK CFI INIT 19f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a28 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a68 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a74 x19: .cfa -16 + ^
STACK CFI 1aac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab8 104 .cfa: sp 0 + .ra: x30
STACK CFI 1ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ac8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ad4 x21: .cfa -16 + ^
STACK CFI 1b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1bac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1bc0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ca0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ce0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d10 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d68 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dd8 138 .cfa: sp 0 + .ra: x30
STACK CFI 1ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1de8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1dfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e6c x19: x19 x20: x20
STACK CFI 1e74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f10 dc .cfa: sp 0 + .ra: x30
STACK CFI 1f14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f18 .cfa: x29 80 +
STACK CFI 1f1c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1f28 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f44 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1fe0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1ff0 dc .cfa: sp 0 + .ra: x30
STACK CFI 1ff4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1ff8 .cfa: x29 80 +
STACK CFI 1ffc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2008 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2038 x23: .cfa -32 + ^
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20c0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 20d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 20d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20ec x21: .cfa -16 + ^
STACK CFI 2110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2118 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2120 74 .cfa: sp 0 + .ra: x30
STACK CFI 218c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2198 6c .cfa: sp 0 + .ra: x30
STACK CFI 21ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2208 118 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2320 dc .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2328 .cfa: x29 80 +
STACK CFI 232c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2338 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2354 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 23f0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2400 dc .cfa: sp 0 + .ra: x30
STACK CFI 2404 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2408 .cfa: x29 80 +
STACK CFI 240c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2418 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2448 x23: .cfa -32 + ^
STACK CFI 24cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 24d0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 24e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24fc x21: .cfa -16 + ^
STACK CFI 2520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2528 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2530 80 .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2548 x19: .cfa -32 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2584 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 25ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25b0 134 .cfa: sp 0 + .ra: x30
STACK CFI 25b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25c0 .cfa: x29 64 +
STACK CFI 25c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 263c .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 26e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 26ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2700 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2708 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2710 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2724 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2734 x21: .cfa -16 + ^
STACK CFI 27a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 27e8 10c .cfa: sp 0 + .ra: x30
STACK CFI 27f0 .cfa: sp 12416 +
STACK CFI 27f4 .ra: .cfa -12408 + ^ x29: .cfa -12416 + ^
STACK CFI 27fc x23: .cfa -12368 + ^ x24: .cfa -12360 + ^
STACK CFI 280c x25: .cfa -12352 + ^
STACK CFI 281c x19: .cfa -12400 + ^ x20: .cfa -12392 + ^
STACK CFI 282c x21: .cfa -12384 + ^ x22: .cfa -12376 + ^
STACK CFI 28ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 28f0 .cfa: sp 12416 + .ra: .cfa -12408 + ^ x19: .cfa -12400 + ^ x20: .cfa -12392 + ^ x21: .cfa -12384 + ^ x22: .cfa -12376 + ^ x23: .cfa -12368 + ^ x24: .cfa -12360 + ^ x25: .cfa -12352 + ^ x29: .cfa -12416 + ^
STACK CFI INIT 28f8 74 .cfa: sp 0 + .ra: x30
STACK CFI 28fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2904 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2970 348 .cfa: sp 0 + .ra: x30
STACK CFI 2974 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2978 .cfa: x29 128 +
STACK CFI 297c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 298c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2994 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 29a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2afc .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2cb8 368 .cfa: sp 0 + .ra: x30
STACK CFI 2cbc .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 2cc4 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2cd0 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d34 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x29: .cfa -240 + ^
STACK CFI 2e2c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2e4c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2ea0 x23: x23 x24: x24
STACK CFI 2eac x25: x25 x26: x26
STACK CFI 2ecc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2ee8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2f58 x23: x23 x24: x24
STACK CFI 2f5c x25: x25 x26: x26
STACK CFI 2f64 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2fa4 x23: x23 x24: x24
STACK CFI 2fb0 x25: x25 x26: x26
STACK CFI 2fd4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2fdc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 2ff8 x23: x23 x24: x24
STACK CFI 2ffc x25: x25 x26: x26
STACK CFI 3004 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 300c x25: x25 x26: x26
STACK CFI 3018 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 301c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 3020 156c .cfa: sp 0 + .ra: x30
STACK CFI 3024 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 302c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3040 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3134 x21: x21 x22: x22
STACK CFI 3138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 313c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3278 x21: x21 x22: x22
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 328c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 331c x21: x21 x22: x22
STACK CFI 3320 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3568 x23: .cfa -16 + ^
STACK CFI 3608 x23: x23
STACK CFI 3f18 x23: .cfa -16 + ^
STACK CFI 3f1c x23: x23
STACK CFI 4048 x23: .cfa -16 + ^
STACK CFI 404c x23: x23
STACK CFI 4154 x23: .cfa -16 + ^
STACK CFI 4158 x23: x23
STACK CFI 4164 x23: .cfa -16 + ^
STACK CFI 4174 x23: x23
STACK CFI 4184 x23: .cfa -16 + ^
STACK CFI 418c x23: x23
STACK CFI 457c x23: .cfa -16 + ^
STACK CFI 4580 x23: x23
STACK CFI 4588 x23: .cfa -16 + ^
STACK CFI INIT 4590 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45d8 b9c .cfa: sp 0 + .ra: x30
STACK CFI 45e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 45e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 45f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4868 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4880 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 48d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 48d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4914 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4918 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 491c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4920 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4b00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b30 x23: x23 x24: x24
STACK CFI 4be0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4bec x23: x23 x24: x24
STACK CFI 4bfc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4c00 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4ca8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4dd0 x23: x23 x24: x24
STACK CFI 4dd4 x25: x25 x26: x26
STACK CFI 4dd8 x27: x27 x28: x28
STACK CFI 4ddc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e98 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4eb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4ec4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4eec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f00 x27: x27 x28: x28
STACK CFI 4f0c x23: x23 x24: x24
STACK CFI 4f10 x25: x25 x26: x26
STACK CFI 4f18 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5018 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5090 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 50d8 x27: x27 x28: x28
STACK CFI 5150 x23: x23 x24: x24
STACK CFI 5154 x25: x25 x26: x26
STACK CFI 5158 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 515c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5164 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5168 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 516c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5170 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5178 58 .cfa: sp 0 + .ra: x30
STACK CFI 5188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5190 x19: .cfa -16 + ^
STACK CFI 51b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 51e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 522c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5238 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5248 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5258 180 .cfa: sp 0 + .ra: x30
STACK CFI 52e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5344 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 53d8 644 .cfa: sp 0 + .ra: x30
STACK CFI 53dc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 53e0 .cfa: x29 192 +
STACK CFI 53e4 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5410 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 5420 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 5814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5818 .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5a20 148 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5aac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ab0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5b68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b78 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b98 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c00 x21: x21 x22: x22
STACK CFI 5c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5c18 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c60 344 .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5c70 .cfa: x29 144 +
STACK CFI 5c74 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5c84 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5cb0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5de4 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5fb0 170 .cfa: x1 32 + .ra: x30
STACK CFI 5fbc .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6000 .cfa: sp 32 +
STACK CFI INIT 6120 18 .cfa: sp 0 + .ra: x30
STACK CFI 6124 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 6140 150 .cfa: sp 0 + .ra: x30
STACK CFI 6144 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 628c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6290 18 .cfa: sp 0 + .ra: x30
STACK CFI 6294 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 62b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 62b4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 62d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 62e0 84 .cfa: sp 0 + .ra: x30
