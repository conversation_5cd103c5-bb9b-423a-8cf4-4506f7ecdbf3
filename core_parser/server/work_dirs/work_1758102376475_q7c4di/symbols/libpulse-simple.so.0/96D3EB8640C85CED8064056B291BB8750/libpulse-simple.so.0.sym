MODULE Linux arm64 96D3EB8640C85CED8064056B291BB8750 libpulse-simple.so.0
INFO CODE_ID 86EBD396C840ED5C8064056B291BB875DC4B2E8F
PUBLIC 1a58 0 pa_simple_free
PUBLIC 1b00 0 pa_simple_new
PUBLIC 1db0 0 pa_simple_write
PUBLIC 1f98 0 pa_simple_read
PUBLIC 2220 0 pa_simple_drain
PUBLIC 2450 0 pa_simple_flush
PUBLIC 2660 0 pa_simple_get_latency
STACK CFI INIT 16a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1718 48 .cfa: sp 0 + .ra: x30
STACK CFI 171c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1724 x19: .cfa -16 + ^
STACK CFI 175c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1768 5c .cfa: sp 0 + .ra: x30
STACK CFI 177c .cfa: sp 32 +
STACK CFI 1798 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 17c8 60 .cfa: sp 0 + .ra: x30
STACK CFI 17dc .cfa: sp 32 +
STACK CFI 17f8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1828 c8 .cfa: sp 0 + .ra: x30
STACK CFI 182c .cfa: sp 48 +
STACK CFI 1830 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1838 x19: .cfa -16 + ^
STACK CFI 1860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1864 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1878 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 187c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18f0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 18f4 .cfa: sp 48 +
STACK CFI 18f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1900 x19: .cfa -16 + ^
STACK CFI 1928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 192c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1940 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1944 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19b8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 19bc .cfa: sp 32 +
STACK CFI 19c0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19e4 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1a58 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 48 +
STACK CFI 1a60 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a68 x19: .cfa -16 + ^
STACK CFI 1ab8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1abc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b00 2ac .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1b0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b14 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1b20 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1b34 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1d00 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1d30 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1d78 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1db0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 1db4 .cfa: sp 80 +
STACK CFI 1db8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1dc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1dd0 x23: .cfa -16 + ^
STACK CFI 1de0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e94 x21: x21 x22: x22
STACK CFI 1e98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ed8 x21: x21 x22: x22
STACK CFI 1ee4 x19: x19 x20: x20
STACK CFI 1ee8 x23: x23
STACK CFI 1eec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ef0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1f14 x21: x21 x22: x22 x23: x23
STACK CFI 1f54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f58 x23: .cfa -16 + ^
STACK CFI 1f70 x21: x21 x22: x22
STACK CFI 1f8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f90 x21: x21 x22: x22
STACK CFI INIT 1f98 288 .cfa: sp 0 + .ra: x30
STACK CFI 1f9c .cfa: sp 96 +
STACK CFI 1fa0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1fa8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1fb8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1fd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2048 x25: .cfa -16 + ^
STACK CFI 2084 x25: x25
STACK CFI 2100 x21: x21 x22: x22
STACK CFI 210c x19: x19 x20: x20
STACK CFI 2110 x23: x23 x24: x24
STACK CFI 2114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2118 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 213c x25: .cfa -16 + ^
STACK CFI 2160 x25: x25
STACK CFI 2174 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 21b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 21b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 21bc x25: .cfa -16 + ^
STACK CFI 21d4 x21: x21 x22: x22
STACK CFI 21d8 x25: x25
STACK CFI 21dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 21e4 x25: .cfa -16 + ^
STACK CFI 21ec x25: x25
STACK CFI 21f0 x21: x21 x22: x22
STACK CFI 2218 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 221c x25: x25
STACK CFI INIT 2220 230 .cfa: sp 0 + .ra: x30
STACK CFI 2224 .cfa: sp 64 +
STACK CFI 2228 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2234 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 231c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 237c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2380 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2450 210 .cfa: sp 0 + .ra: x30
STACK CFI 2454 .cfa: sp 64 +
STACK CFI 2458 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2464 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2540 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 25a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25a4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2660 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 2664 .cfa: sp 112 +
STACK CFI 2668 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2670 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2678 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2684 x23: .cfa -48 + ^
STACK CFI 277c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2780 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
