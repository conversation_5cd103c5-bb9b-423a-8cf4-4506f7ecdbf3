MODULE Linux arm64 BFCEDEF63C231C0974686626AC6222090 libnfsidmap.so.0
INFO CODE_ID F6DECEBF233C091C74686626AC622209AAA03B01
PUBLIC 1dd8 0 nfs4_cleanup_name_mapping
PUBLIC 1e20 0 get_default_domain
PUBLIC 1ea0 0 nfs4_init_name_mapping
PUBLIC 2318 0 get_local_realms
PUBLIC 2328 0 nfs4_get_default_domain
PUBLIC 2378 0 nfs4_uid_to_name
PUBLIC 24e0 0 nfs4_gid_to_name
PUBLIC 2678 0 nfs4_uid_to_owner
PUBLIC 26d0 0 nfs4_gid_to_group_owner
PUBLIC 2728 0 nfs4_name_to_uid
PUBLIC 28a8 0 nfs4_name_to_gid
PUBLIC 2ae0 0 nfs4_owner_to_uid
PUBLIC 2b48 0 nfs4_group_owner_to_gid
PUBLIC 2bb0 0 nfs4_gss_princ_to_ids
PUBLIC 2d60 0 nfs4_gss_princ_to_grouplist
PUBLIC 2f10 0 nfs4_gss_princ_to_ids_ex
PUBLIC 30c8 0 nfs4_gss_princ_to_grouplist_ex
PUBLIC 3280 0 nfs4_set_debug
PUBLIC 3498 0 conf_get_str
PUBLIC 3558 0 conf_get_num
PUBLIC 3590 0 conf_match_num
PUBLIC 36a8 0 conf_get_str_with_def
PUBLIC 3770 0 conf_decode_base64
PUBLIC 3928 0 conf_free_list
PUBLIC 3990 0 conf_get_list
PUBLIC 3b88 0 conf_get_tag_list
PUBLIC 3ca0 0 conf_begin
PUBLIC 3cb8 0 conf_set
PUBLIC 3dc8 0 conf_remove
PUBLIC 3e78 0 conf_remove_section
PUBLIC 3ef8 0 conf_end
PUBLIC 42e8 0 conf_reinit
PUBLIC 4750 0 conf_init
PUBLIC 4780 0 conf_report
PUBLIC 4990 0 strlcpy
STACK CFI INIT 1848 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1878 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 18b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18bc x19: .cfa -16 + ^
STACK CFI 18f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1900 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1904 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 1914 x19: .cfa -320 + ^
STACK CFI 19b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19b4 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x29: .cfa -336 + ^
STACK CFI INIT 19b8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 19bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 19ec x23: .cfa -16 + ^
STACK CFI 1a48 x19: x19 x20: x20
STACK CFI 1a4c x23: x23
STACK CFI 1a58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 1a60 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 1a64 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1a6c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 1a98 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 1aa8 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1abc x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 1ac4 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 1ad8 v8: .cfa -176 + ^
STACK CFI 1bac x21: x21 x22: x22
STACK CFI 1bb4 x23: x23 x24: x24
STACK CFI 1bb8 x25: x25 x26: x26
STACK CFI 1bbc v8: v8
STACK CFI 1be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 1be4 .cfa: sp 272 + .ra: .cfa -264 + ^ v8: .cfa -176 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 1be8 x23: x23 x24: x24
STACK CFI 1bec x25: x25 x26: x26
STACK CFI 1bf0 v8: v8
STACK CFI 1bfc x21: x21 x22: x22
STACK CFI 1c04 v8: .cfa -176 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 1d1c v8: v8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1d28 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1d2c x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 1d30 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 1d34 v8: .cfa -176 + ^
STACK CFI INIT 1d38 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1d3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1d48 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dcc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1dd8 44 .cfa: sp 0 + .ra: x30
STACK CFI 1ddc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1de4 x19: .cfa -16 + ^
STACK CFI 1e18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e20 80 .cfa: sp 0 + .ra: x30
STACK CFI 1e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e2c x19: .cfa -16 + ^
STACK CFI 1e40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ea0 474 .cfa: sp 0 + .ra: x30
STACK CFI 1ea4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1eac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1eb4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1ef8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1efc .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 1f08 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1f1c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 206c x19: x19 x20: x20
STACK CFI 2070 x25: x25 x26: x26
STACK CFI 2074 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 226c x19: x19 x20: x20
STACK CFI 2270 x25: x25 x26: x26
STACK CFI 2274 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 22c8 x19: x19 x20: x20
STACK CFI 22d0 x25: x25 x26: x26
STACK CFI 22d4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2300 x19: x19 x20: x20
STACK CFI 2304 x25: x25 x26: x26
STACK CFI 230c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2310 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 2318 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2328 50 .cfa: sp 0 + .ra: x30
STACK CFI 232c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2334 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 236c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2378 168 .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2388 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2394 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23a0 x27: .cfa -16 + ^
STACK CFI 23d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 248c x21: x21 x22: x22
STACK CFI 24d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 24d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 24e0 198 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 24fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2508 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2520 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 260c x21: x21 x22: x22
STACK CFI 2624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2628 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2664 x21: x21 x22: x22
STACK CFI 2674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 2678 58 .cfa: sp 0 + .ra: x30
STACK CFI 267c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2684 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 26cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 26d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2728 17c .cfa: sp 0 + .ra: x30
STACK CFI 272c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2738 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2758 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2760 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2840 x21: x21 x22: x22
STACK CFI 2844 x23: x23 x24: x24
STACK CFI 2854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 2858 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2894 x21: x21 x22: x22
STACK CFI 2898 x23: x23 x24: x24
STACK CFI 28a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 28a8 17c .cfa: sp 0 + .ra: x30
STACK CFI 28ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 28b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 28d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 28e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29c0 x21: x21 x22: x22
STACK CFI 29c4 x23: x23 x24: x24
STACK CFI 29d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 29d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2a14 x21: x21 x22: x22
STACK CFI 2a18 x23: x23 x24: x24
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2a28 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2a2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a38 .cfa: x29 64 +
STACK CFI 2a3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ad0 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ae0 68 .cfa: sp 0 + .ra: x30
STACK CFI 2ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b48 68 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2bb0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2bb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2bc0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2bcc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2bd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2bf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ce8 x21: x21 x22: x22
STACK CFI 2d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d04 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2d40 x21: x21 x22: x22
STACK CFI 2d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d54 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2d60 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2d64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2d70 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d7c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d88 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2da4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e98 x21: x21 x22: x22
STACK CFI 2eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2eb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2ef0 x21: x21 x22: x22
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f04 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2f10 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 2f14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2f20 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2f2c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2f38 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2f58 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3050 x21: x21 x22: x22
STACK CFI 3068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 306c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 30ac x21: x21 x22: x22
STACK CFI 30bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 30c0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 30c8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 30cc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 30d8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 30e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 30f0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3110 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3208 x21: x21 x22: x22
STACK CFI 3220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3224 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3264 x21: x21 x22: x22
STACK CFI 3274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3278 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3280 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32a0 64 .cfa: sp 0 + .ra: x30
STACK CFI 32a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32ac x19: .cfa -16 + ^
STACK CFI 32dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 32e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3308 70 .cfa: sp 0 + .ra: x30
STACK CFI 330c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3314 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3320 x21: .cfa -16 + ^
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3364 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3378 120 .cfa: sp 0 + .ra: x30
STACK CFI 337c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3388 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3390 x23: .cfa -16 + ^
STACK CFI 3428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 342c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 348c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3490 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3498 bc .cfa: sp 0 + .ra: x30
STACK CFI 349c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34ac x21: .cfa -16 + ^
STACK CFI 3530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3534 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 354c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3558 34 .cfa: sp 0 + .ra: x30
STACK CFI 355c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3590 118 .cfa: sp 0 + .ra: x30
STACK CFI 3594 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 359c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 35a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35c0 x23: .cfa -48 + ^
STACK CFI 3638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 363c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 36a8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 36ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 36b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 374c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3764 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3770 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 3774 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 377c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 378c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3798 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 37b8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 37dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 38d0 x19: x19 x20: x20
STACK CFI 38d4 x23: x23 x24: x24
STACK CFI 38d8 x27: x27 x28: x28
STACK CFI 38e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 38ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 38f0 x19: x19 x20: x20
STACK CFI 38f4 x23: x23 x24: x24
STACK CFI 38f8 x27: x27 x28: x28
STACK CFI 3910 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3914 .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 391c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3920 x19: x19 x20: x20
STACK CFI 3924 x27: x27 x28: x28
STACK CFI INIT 3928 68 .cfa: sp 0 + .ra: x30
STACK CFI 392c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3934 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 398c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3990 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 3994 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 399c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 39ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 39f0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3a08 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3ab8 x21: x21 x22: x22
STACK CFI 3abc x25: x25 x26: x26
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3ae8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 3b34 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 3b44 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3b5c x21: x21 x22: x22
STACK CFI 3b60 x25: x25 x26: x26
STACK CFI 3b64 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3b74 x25: x25 x26: x26
STACK CFI 3b7c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3b80 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 3b88 118 .cfa: sp 0 + .ra: x30
STACK CFI 3b8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3bac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c68 x19: x19 x20: x20
STACK CFI 3c74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3c78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3c8c x19: x19 x20: x20
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3c98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3ca0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cb8 10c .cfa: sp 0 + .ra: x30
STACK CFI 3cbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3cdc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3dc8 ac .cfa: sp 0 + .ra: x30
STACK CFI 3dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3dd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3de0 x21: .cfa -16 + ^
STACK CFI 3e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e78 7c .cfa: sp 0 + .ra: x30
STACK CFI 3e7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ef8 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 3efc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3f04 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3f10 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3f1c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3f28 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3f40 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3fdc x21: x21 x22: x22
STACK CFI 3fe0 x25: x25 x26: x26
STACK CFI 3fe4 x27: x27 x28: x28
STACK CFI 3ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3ff8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 40ec v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 4258 v8: v8 v9: v9
STACK CFI 4264 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 4280 v8: v8 v9: v9
STACK CFI 4284 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 429c v8: v8 v9: v9
STACK CFI 42b0 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI INIT 42e8 464 .cfa: sp 0 + .ra: x30
STACK CFI 42ec .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 42f8 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 4308 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 4324 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 43d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43d8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x29: .cfa -288 + ^
STACK CFI 43e0 v8: .cfa -192 + ^
STACK CFI 43fc x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 4420 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 44f8 x25: x25 x26: x26
STACK CFI 44fc x27: x27 x28: x28
STACK CFI 4554 v8: v8
STACK CFI 455c v8: .cfa -192 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4570 x25: x25 x26: x26
STACK CFI 4574 x27: x27 x28: x28
STACK CFI 4578 v8: v8
STACK CFI 4594 v8: .cfa -192 + ^
STACK CFI 45a4 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 46bc v8: v8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 46d0 v8: .cfa -192 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 472c v8: v8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4730 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 4734 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4738 v8: .cfa -192 + ^
STACK CFI INIT 4750 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4780 210 .cfa: sp 0 + .ra: x30
STACK CFI 4784 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 479c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 47a8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 47b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 47b8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 47c8 x27: .cfa -32 + ^
STACK CFI 4918 x19: x19 x20: x20
STACK CFI 491c x21: x21 x22: x22
STACK CFI 4920 x23: x23 x24: x24
STACK CFI 4924 x25: x25 x26: x26
STACK CFI 4928 x27: x27
STACK CFI 492c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4930 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 4968 x19: x19 x20: x20
STACK CFI 496c x21: x21 x22: x22
STACK CFI 4970 x23: x23 x24: x24
STACK CFI 4974 x25: x25 x26: x26
STACK CFI 4978 x27: x27
STACK CFI 497c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4980 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4984 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4990 54 .cfa: sp 0 + .ra: x30
