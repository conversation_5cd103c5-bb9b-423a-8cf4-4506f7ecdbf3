MODULE Linux arm64 2497D429B892BCA66F67FFCD0E9C696B0 libxcb-sync.so.1
INFO CODE_ID 29D4972492B8A6BC6F67FFCD0E9C696B0287C15C
PUBLIC 21c8 0 xcb_sync_alarm_next
PUBLIC 21e8 0 xcb_sync_alarm_end
PUBLIC 2200 0 xcb_sync_counter_next
PUBLIC 2220 0 xcb_sync_counter_end
PUBLIC 2238 0 xcb_sync_fence_next
PUBLIC 2258 0 xcb_sync_fence_end
PUBLIC 2270 0 xcb_sync_int64_next
PUBLIC 2290 0 xcb_sync_int64_end
PUBLIC 22a8 0 xcb_sync_systemcounter_sizeof
PUBLIC 22c0 0 xcb_sync_systemcounter_name
PUBLIC 22c8 0 xcb_sync_systemcounter_name_length
PUBLIC 22d0 0 xcb_sync_systemcounter_name_end
PUBLIC 22e8 0 xcb_sync_systemcounter_next
PUBLIC 2330 0 xcb_sync_systemcounter_end
PUBLIC 2388 0 xcb_sync_trigger_next
PUBLIC 23a8 0 xcb_sync_trigger_end
PUBLIC 23c8 0 xcb_sync_waitcondition_next
PUBLIC 23e8 0 xcb_sync_waitcondition_end
PUBLIC 2408 0 xcb_sync_initialize
PUBLIC 2478 0 xcb_sync_initialize_unchecked
PUBLIC 24f0 0 xcb_sync_initialize_reply
PUBLIC 24f8 0 xcb_sync_list_system_counters_sizeof
PUBLIC 2578 0 xcb_sync_list_system_counters
PUBLIC 25e0 0 xcb_sync_list_system_counters_unchecked
PUBLIC 2648 0 xcb_sync_list_system_counters_counters_length
PUBLIC 2650 0 xcb_sync_list_system_counters_counters_iterator
PUBLIC 2670 0 xcb_sync_list_system_counters_reply
PUBLIC 2678 0 xcb_sync_create_counter_checked
PUBLIC 26f0 0 xcb_sync_create_counter
PUBLIC 2760 0 xcb_sync_destroy_counter_checked
PUBLIC 27d0 0 xcb_sync_destroy_counter
PUBLIC 2838 0 xcb_sync_query_counter
PUBLIC 28a8 0 xcb_sync_query_counter_unchecked
PUBLIC 2910 0 xcb_sync_query_counter_reply
PUBLIC 2918 0 xcb_sync_await_sizeof
PUBLIC 2930 0 xcb_sync_await_checked
PUBLIC 29b0 0 xcb_sync_await
PUBLIC 2a28 0 xcb_sync_await_wait_list
PUBLIC 2a30 0 xcb_sync_await_wait_list_length
PUBLIC 2a60 0 xcb_sync_await_wait_list_iterator
PUBLIC 2aa8 0 xcb_sync_change_counter_checked
PUBLIC 2b20 0 xcb_sync_change_counter
PUBLIC 2b90 0 xcb_sync_set_counter_checked
PUBLIC 2c08 0 xcb_sync_set_counter
PUBLIC 2c78 0 xcb_sync_create_alarm_value_list_serialize
PUBLIC 2e98 0 xcb_sync_create_alarm_value_list_unpack
PUBLIC 2f38 0 xcb_sync_create_alarm_value_list_sizeof
PUBLIC 2f80 0 xcb_sync_create_alarm_sizeof
PUBLIC 2fa8 0 xcb_sync_create_alarm_checked
PUBLIC 3038 0 xcb_sync_create_alarm
PUBLIC 30c8 0 xcb_sync_create_alarm_aux_checked
PUBLIC 3178 0 xcb_sync_create_alarm_aux
PUBLIC 3228 0 xcb_sync_create_alarm_value_list
PUBLIC 3230 0 xcb_sync_change_alarm_value_list_serialize
PUBLIC 3450 0 xcb_sync_change_alarm_value_list_unpack
PUBLIC 34f0 0 xcb_sync_change_alarm_value_list_sizeof
PUBLIC 3538 0 xcb_sync_change_alarm_sizeof
PUBLIC 3560 0 xcb_sync_change_alarm_checked
PUBLIC 35f0 0 xcb_sync_change_alarm
PUBLIC 3680 0 xcb_sync_change_alarm_aux_checked
PUBLIC 3730 0 xcb_sync_change_alarm_aux
PUBLIC 37e0 0 xcb_sync_change_alarm_value_list
PUBLIC 37e8 0 xcb_sync_destroy_alarm_checked
PUBLIC 3858 0 xcb_sync_destroy_alarm
PUBLIC 38c0 0 xcb_sync_query_alarm
PUBLIC 3930 0 xcb_sync_query_alarm_unchecked
PUBLIC 3998 0 xcb_sync_query_alarm_reply
PUBLIC 39a0 0 xcb_sync_set_priority_checked
PUBLIC 3a10 0 xcb_sync_set_priority
PUBLIC 3a80 0 xcb_sync_get_priority
PUBLIC 3af0 0 xcb_sync_get_priority_unchecked
PUBLIC 3b58 0 xcb_sync_get_priority_reply
PUBLIC 3b60 0 xcb_sync_create_fence_checked
PUBLIC 3bd8 0 xcb_sync_create_fence
PUBLIC 3c48 0 xcb_sync_trigger_fence_checked
PUBLIC 3cb8 0 xcb_sync_trigger_fence
PUBLIC 3d20 0 xcb_sync_reset_fence_checked
PUBLIC 3d90 0 xcb_sync_reset_fence
PUBLIC 3df8 0 xcb_sync_destroy_fence_checked
PUBLIC 3e68 0 xcb_sync_destroy_fence
PUBLIC 3ed0 0 xcb_sync_query_fence
PUBLIC 3f40 0 xcb_sync_query_fence_unchecked
PUBLIC 3fa8 0 xcb_sync_query_fence_reply
PUBLIC 3fb0 0 xcb_sync_await_fence_sizeof
PUBLIC 3fc0 0 xcb_sync_await_fence_checked
PUBLIC 4038 0 xcb_sync_await_fence
PUBLIC 40b0 0 xcb_sync_await_fence_fence_list
PUBLIC 40b8 0 xcb_sync_await_fence_fence_list_length
PUBLIC 40c8 0 xcb_sync_await_fence_fence_list_end
STACK CFI INIT 2108 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2138 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2178 48 .cfa: sp 0 + .ra: x30
STACK CFI 217c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2184 x19: .cfa -16 + ^
STACK CFI 21bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 21c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2200 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2220 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2238 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2258 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2270 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2290 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22e8 48 .cfa: sp 0 + .ra: x30
STACK CFI 22ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 232c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2330 54 .cfa: sp 0 + .ra: x30
STACK CFI 2334 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 234c x19: .cfa -32 + ^
STACK CFI 2368 x19: x19
STACK CFI 2380 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2388 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2408 70 .cfa: sp 0 + .ra: x30
STACK CFI 240c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 241c x19: .cfa -96 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2474 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2478 74 .cfa: sp 0 + .ra: x30
STACK CFI 247c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 248c x19: .cfa -96 + ^
STACK CFI 24e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 24e8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 24f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f8 7c .cfa: sp 0 + .ra: x30
STACK CFI 24fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2504 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 250c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 255c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2560 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2578 68 .cfa: sp 0 + .ra: x30
STACK CFI 257c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 258c x19: .cfa -96 + ^
STACK CFI 25d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 25e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 25e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 25f4 x19: .cfa -96 + ^
STACK CFI 263c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2640 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2648 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2650 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2678 74 .cfa: sp 0 + .ra: x30
STACK CFI 267c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 268c x19: .cfa -112 + ^
STACK CFI 26e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26e8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 26f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 26f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2704 x19: .cfa -112 + ^
STACK CFI 2758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 275c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2760 6c .cfa: sp 0 + .ra: x30
STACK CFI 2764 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2774 x19: .cfa -96 + ^
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 27c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 27d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 27d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 27e4 x19: .cfa -96 + ^
STACK CFI 2830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2834 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2838 6c .cfa: sp 0 + .ra: x30
STACK CFI 283c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 284c x19: .cfa -96 + ^
STACK CFI 289c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 28a0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 28a8 68 .cfa: sp 0 + .ra: x30
STACK CFI 28ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28bc x19: .cfa -96 + ^
STACK CFI 2908 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 290c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2910 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2918 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2930 7c .cfa: sp 0 + .ra: x30
STACK CFI 2934 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2944 x19: .cfa -128 + ^
STACK CFI 29a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29a8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 29b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 29b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 29c4 x19: .cfa -128 + ^
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a24 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2a28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a30 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a60 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2aa8 74 .cfa: sp 0 + .ra: x30
STACK CFI 2aac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2abc x19: .cfa -112 + ^
STACK CFI 2b14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b18 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2b20 70 .cfa: sp 0 + .ra: x30
STACK CFI 2b24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2b34 x19: .cfa -112 + ^
STACK CFI 2b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b8c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2b90 74 .cfa: sp 0 + .ra: x30
STACK CFI 2b94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2ba4 x19: .cfa -112 + ^
STACK CFI 2bfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c00 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2c08 70 .cfa: sp 0 + .ra: x30
STACK CFI 2c0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2c1c x19: .cfa -112 + ^
STACK CFI 2c70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c74 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2c78 220 .cfa: sp 0 + .ra: x30
STACK CFI 2c7c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c8c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2c98 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2cb8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e3c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2e98 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f38 48 .cfa: sp 0 + .ra: x30
STACK CFI 2f3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f44 x19: .cfa -64 + ^
STACK CFI 2f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2f80 24 .cfa: sp 0 + .ra: x30
STACK CFI 2f88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fa8 90 .cfa: sp 0 + .ra: x30
STACK CFI 2fac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2fbc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3034 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3038 90 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 304c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 30c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 30c8 ac .cfa: sp 0 + .ra: x30
STACK CFI 30cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 30dc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 316c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3170 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 3178 ac .cfa: sp 0 + .ra: x30
STACK CFI 317c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 318c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 321c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3220 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3228 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3230 220 .cfa: sp 0 + .ra: x30
STACK CFI 3234 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3244 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3250 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3270 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 33f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 33f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3450 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 34f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 34fc x19: .cfa -64 + ^
STACK CFI 3530 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3534 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3538 24 .cfa: sp 0 + .ra: x30
STACK CFI 3540 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3558 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3560 90 .cfa: sp 0 + .ra: x30
STACK CFI 3564 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3574 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 35e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35ec .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 35f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 35f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3604 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 367c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3680 ac .cfa: sp 0 + .ra: x30
STACK CFI 3684 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3694 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3728 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3730 ac .cfa: sp 0 + .ra: x30
STACK CFI 3734 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3744 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 37d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37d8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT 37e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37e8 6c .cfa: sp 0 + .ra: x30
STACK CFI 37ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 37fc x19: .cfa -96 + ^
STACK CFI 384c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3850 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3858 68 .cfa: sp 0 + .ra: x30
STACK CFI 385c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 386c x19: .cfa -96 + ^
STACK CFI 38b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 38bc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 38c0 6c .cfa: sp 0 + .ra: x30
STACK CFI 38c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 38d4 x19: .cfa -96 + ^
STACK CFI 3924 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3928 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3930 68 .cfa: sp 0 + .ra: x30
STACK CFI 3934 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3944 x19: .cfa -96 + ^
STACK CFI 3990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3994 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3998 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39a0 70 .cfa: sp 0 + .ra: x30
STACK CFI 39a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 39b4 x19: .cfa -112 + ^
STACK CFI 3a08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3a0c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3a10 6c .cfa: sp 0 + .ra: x30
STACK CFI 3a14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3a24 x19: .cfa -112 + ^
STACK CFI 3a74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3a78 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3a80 6c .cfa: sp 0 + .ra: x30
STACK CFI 3a84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3a94 x19: .cfa -96 + ^
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ae8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3af0 68 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3b04 x19: .cfa -96 + ^
STACK CFI 3b50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b54 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3b58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b60 74 .cfa: sp 0 + .ra: x30
STACK CFI 3b64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3b74 x19: .cfa -112 + ^
STACK CFI 3bcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3bd0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3bd8 70 .cfa: sp 0 + .ra: x30
STACK CFI 3bdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3bec x19: .cfa -112 + ^
STACK CFI 3c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c44 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c48 6c .cfa: sp 0 + .ra: x30
STACK CFI 3c4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3c5c x19: .cfa -96 + ^
STACK CFI 3cac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cb0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3cb8 68 .cfa: sp 0 + .ra: x30
STACK CFI 3cbc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ccc x19: .cfa -96 + ^
STACK CFI 3d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d1c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3d20 6c .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3d34 x19: .cfa -96 + ^
STACK CFI 3d84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d88 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3d90 68 .cfa: sp 0 + .ra: x30
STACK CFI 3d94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3da4 x19: .cfa -96 + ^
STACK CFI 3df0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3df4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3df8 6c .cfa: sp 0 + .ra: x30
STACK CFI 3dfc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e0c x19: .cfa -96 + ^
STACK CFI 3e5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e60 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3e68 68 .cfa: sp 0 + .ra: x30
STACK CFI 3e6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e7c x19: .cfa -96 + ^
STACK CFI 3ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ecc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ed0 6c .cfa: sp 0 + .ra: x30
STACK CFI 3ed4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ee4 x19: .cfa -96 + ^
STACK CFI 3f34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3f38 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3f40 68 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3f54 x19: .cfa -96 + ^
STACK CFI 3fa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3fa4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3fa8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fc0 78 .cfa: sp 0 + .ra: x30
STACK CFI 3fc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3fd4 x19: .cfa -128 + ^
STACK CFI 4030 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4034 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4038 74 .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 404c x19: .cfa -128 + ^
STACK CFI 40a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40a8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 40b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 40c8 14 .cfa: sp 0 + .ra: x30
