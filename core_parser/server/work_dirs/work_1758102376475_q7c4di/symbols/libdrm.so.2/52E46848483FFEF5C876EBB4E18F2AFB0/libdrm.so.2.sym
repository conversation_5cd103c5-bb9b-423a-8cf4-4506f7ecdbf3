MODULE Linux arm64 52E46848483FFEF5C876EBB4E18F2AFB0 libdrm.so.2
INFO CODE_ID 4868E4523F48F5FEC876EBB4E18F2AFB24AF9A60
PUBLIC 5fe8 0 drmSetServerInfo
PUBLIC 5ff8 0 drmMsg
PUBLIC 63e0 0 drmGetHashTable
PUBLIC 63f0 0 drmMalloc
PUBLIC 6400 0 drmFree
PUBLIC 6440 0 drmIoctl
PUBLIC 64a8 0 drmGetEntry
PUBLIC 6580 0 drmOpenControl
PUBLIC 6590 0 drmOpenRender
PUBLIC 65a0 0 drmFreeVersion
PUBLIC 65e0 0 drmGetVersion
PUBLIC 6750 0 drmAvailable
PUBLIC 67c8 0 drmGetLibVersion
PUBLIC 67f0 0 drmGetCap
PUBLIC 6860 0 drmSetClientCap
PUBLIC 68c0 0 drmFreeBusid
PUBLIC 68c8 0 drmGetBusid
PUBLIC 6988 0 drmSetBusid
PUBLIC 6a10 0 drmGetMagic
PUBLIC 6a90 0 drmAuthMagic
PUBLIC 6b00 0 drmAddMap
PUBLIC 6b90 0 drmRmMap
PUBLIC 6c08 0 drmAddBufs
PUBLIC 6c90 0 drmMarkBufs
PUBLIC 6e08 0 drmFreeBufs
PUBLIC 6e80 0 drmClose
PUBLIC 6f28 0 drmMap
PUBLIC 6fe8 0 drmUnmap
PUBLIC 6ff0 0 drmGetBufInfo
PUBLIC 7138 0 drmMapBufs
PUBLIC 7280 0 drmUnmapBufs
PUBLIC 72f8 0 drmDMA
PUBLIC 73d0 0 drmGetLock
PUBLIC 7498 0 drmUnlock
PUBLIC 74f8 0 drmGetReservedContextList
PUBLIC 7620 0 drmFreeReservedContextList
PUBLIC 7628 0 drmCreateContext
PUBLIC 76a8 0 drmSwitchToContext
PUBLIC 7718 0 drmSetContextFlags
PUBLIC 77a0 0 drmGetContextFlags
PUBLIC 7830 0 drmDestroyContext
PUBLIC 78a0 0 drmCreateDrawable
PUBLIC 7920 0 drmDestroyDrawable
PUBLIC 7990 0 drmUpdateDrawableInfo
PUBLIC 7a08 0 drmCrtcGetSequence
PUBLIC 7aa0 0 drmCrtcQueueSequence
PUBLIC 7b20 0 drmAgpAcquire
PUBLIC 7b50 0 drmAgpRelease
PUBLIC 7b80 0 drmAgpEnable
PUBLIC 7bf0 0 drmAgpAlloc
PUBLIC 7ca0 0 drmAgpFree
PUBLIC 7d18 0 drmAgpBind
PUBLIC 7d90 0 drmAgpUnbind
PUBLIC 7e00 0 drmAgpVersionMajor
PUBLIC 7e80 0 drmAgpVersionMinor
PUBLIC 7f00 0 drmAgpGetMode
PUBLIC 7f70 0 drmAgpBase
PUBLIC 7fe0 0 drmAgpSize
PUBLIC 8050 0 drmAgpMemoryUsed
PUBLIC 80c0 0 drmAgpMemoryAvail
PUBLIC 8130 0 drmAgpVendorId
PUBLIC 81a0 0 drmAgpDeviceId
PUBLIC 8210 0 drmScatterGatherAlloc
PUBLIC 8298 0 drmScatterGatherFree
PUBLIC 8308 0 drmWaitVBlank
PUBLIC 8470 0 drmError
PUBLIC 8588 0 drmCtlInstHandler
PUBLIC 8600 0 drmCtlUninstHandler
PUBLIC 8670 0 drmFinish
PUBLIC 8738 0 drmGetInterruptFromBusID
PUBLIC 87b8 0 drmAddContextTag
PUBLIC 8830 0 drmDelContextTag
PUBLIC 8858 0 drmGetContextTag
PUBLIC 88c0 0 drmAddContextPrivateMapping
PUBLIC 8938 0 drmGetContextPrivateMapping
PUBLIC 89c0 0 drmGetMap
PUBLIC 8aa0 0 drmGetClient
PUBLIC 8b70 0 drmGetStats
PUBLIC 8f18 0 drmSetInterfaceVersion
PUBLIC 94f0 0 drmOpenWithType
PUBLIC 95b0 0 drmOpen
PUBLIC 95b8 0 drmCommandNone
PUBLIC 95f0 0 drmCommandRead
PUBLIC 9628 0 drmCommandWrite
PUBLIC 9660 0 drmCommandWriteRead
PUBLIC 9698 0 drmOpenOnceWithType
PUBLIC 97c8 0 drmOpenOnce
PUBLIC 97d8 0 drmCloseOnce
PUBLIC 98a8 0 drmSetMaster
PUBLIC 98b8 0 drmDropMaster
PUBLIC 98c8 0 drmIsMaster
PUBLIC 98e8 0 drmGetDeviceNameFromFd
PUBLIC 99d8 0 drmGetNodeTypeFromFd
PUBLIC 9ab8 0 drmPrimeHandleToFD
PUBLIC 9b38 0 drmPrimeFDToHandle
PUBLIC 9bb0 0 drmGetPrimaryDeviceNameFromFd
PUBLIC 9bb8 0 drmGetRenderDeviceNameFromFd
PUBLIC 9bc0 0 drmDevicesEqual
PUBLIC 9c98 0 drmFreeDevice
PUBLIC 9e80 0 drmFreeDevices
PUBLIC 9ed8 0 drmGetDevice2
PUBLIC a158 0 drmGetDevice
PUBLIC a168 0 drmGetDevices2
PUBLIC a320 0 drmGetDevices
PUBLIC a330 0 drmGetDeviceNameFromFd2
PUBLIC a468 0 drmSyncobjCreate
PUBLIC a4d8 0 drmSyncobjDestroy
PUBLIC a538 0 drmSyncobjHandleToFD
PUBLIC a5b8 0 drmSyncobjFDToHandle
PUBLIC a630 0 drmSyncobjImportSyncFile
PUBLIC a698 0 drmSyncobjExportSyncFile
PUBLIC a718 0 drmSyncobjWait
PUBLIC a7a8 0 drmSyncobjReset
PUBLIC a810 0 drmSyncobjSignal
PUBLIC a878 0 drmSyncobjTimelineSignal
PUBLIC a8e0 0 drmSyncobjTimelineWait
PUBLIC a978 0 drmSyncobjQuery
PUBLIC a9e0 0 drmSyncobjQuery2
PUBLIC aa40 0 drmSyncobjTransfer
PUBLIC aaa8 0 drmGetFormatModifierVendor
PUBLIC ab08 0 drmGetFormatModifierName
PUBLIC ad48 0 drmHashCreate
PUBLIC ad70 0 drmHashDestroy
PUBLIC adf0 0 drmHashLookup
PUBLIC ae68 0 drmHashInsert
PUBLIC af20 0 drmHashDelete
PUBLIC afb8 0 drmHashNext
PUBLIC b038 0 drmHashFirst
PUBLIC b068 0 drmRandomCreate
PUBLIC b0e8 0 drmRandomDestroy
PUBLIC b100 0 drmRandom
PUBLIC b140 0 drmRandomDouble
PUBLIC b170 0 drmSLCreate
PUBLIC b1f0 0 drmSLDestroy
PUBLIC b298 0 drmSLInsert
PUBLIC b4a0 0 drmSLDelete
PUBLIC b638 0 drmSLLookup
PUBLIC b708 0 drmSLLookupNeighbors
PUBLIC b840 0 drmSLNext
PUBLIC b890 0 drmSLFirst
PUBLIC b8c0 0 drmSLDump
PUBLIC be28 0 drmModeFreeModeInfo
PUBLIC be38 0 drmModeFreeResources
PUBLIC be80 0 drmModeFreeFB
PUBLIC be90 0 drmModeFreeCrtc
PUBLIC bea0 0 drmModeFreeConnector
PUBLIC bee8 0 drmModeFreeEncoder
PUBLIC bef0 0 drmIsKMS
PUBLIC bf88 0 drmModeGetResources
PUBLIC c2a8 0 drmModeAddFB
PUBLIC c348 0 drmModeAddFB2WithModifiers
PUBLIC c410 0 drmModeAddFB2
PUBLIC c440 0 drmModeRmFB
PUBLIC c478 0 drmModeGetFB
PUBLIC c520 0 drmModeDirtyFB
PUBLIC c5a0 0 drmModeGetCrtc
PUBLIC c6a8 0 drmModeSetCrtc
PUBLIC c770 0 drmModeSetCursor
PUBLIC c7f8 0 drmModeSetCursor2
PUBLIC c880 0 drmModeMoveCursor
PUBLIC c910 0 drmModeGetEncoder
PUBLIC c9b0 0 drmModeGetConnector
PUBLIC c9b8 0 drmModeGetConnectorCurrent
PUBLIC c9c0 0 drmModeAttachMode
PUBLIC ca60 0 drmModeDetachMode
PUBLIC cb00 0 drmModeGetProperty
PUBLIC cd70 0 drmModeFreeProperty
PUBLIC cdb0 0 drmModeGetPropertyBlob
PUBLIC ceb0 0 drmModeFreePropertyBlob
PUBLIC cee0 0 drmModeConnectorSetProperty
PUBLIC cf58 0 drmCheckModesettingSupported
PUBLIC d0f8 0 drmModeCrtcGetGamma
PUBLIC d170 0 drmModeCrtcSetGamma
PUBLIC d1e8 0 drmHandleEvent
PUBLIC d378 0 drmModePageFlip
PUBLIC d3f0 0 drmModePageFlipTarget
PUBLIC d468 0 drmModeSetPlane
PUBLIC d500 0 drmModeGetPlane
PUBLIC d690 0 drmModeFreePlane
PUBLIC d6c0 0 drmModeGetPlaneResources
PUBLIC d820 0 drmModeFreePlaneResources
PUBLIC d850 0 drmModeObjectGetProperties
PUBLIC da30 0 drmModeFreeObjectProperties
PUBLIC da68 0 drmModeObjectSetProperty
PUBLIC dae0 0 drmModeAtomicAlloc
PUBLIC db00 0 drmModeAtomicDuplicate
PUBLIC dba0 0 drmModeAtomicMerge
PUBLIC dc60 0 drmModeAtomicGetCursor
PUBLIC dc78 0 drmModeAtomicSetCursor
PUBLIC dc88 0 drmModeAtomicAddProperty
PUBLIC dd68 0 drmModeAtomicFree
PUBLIC dda0 0 drmModeAtomicCommit
PUBLIC e0b0 0 drmModeCreatePropertyBlob
PUBLIC e158 0 drmModeDestroyPropertyBlob
PUBLIC e1c8 0 drmModeCreateLease
PUBLIC e270 0 drmModeListLessees
PUBLIC e380 0 drmModeGetLease
PUBLIC e490 0 drmModeRevokeLease
PUBLIC e518 0 drmModeGetFB2
PUBLIC e608 0 drmModeFreeFB2
STACK CFI INIT 4598 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4608 48 .cfa: sp 0 + .ra: x30
STACK CFI 460c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4614 x19: .cfa -16 + ^
STACK CFI 464c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4650 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4658 84 .cfa: sp 0 + .ra: x30
STACK CFI 465c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 466c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 46d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 46d8 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x29: .cfa -240 + ^
STACK CFI INIT 46e0 190 .cfa: sp 0 + .ra: x30
STACK CFI 46e4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 46f0 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 4708 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 4734 x23: .cfa -288 + ^
STACK CFI 47e0 x23: x23
STACK CFI 47e4 x23: .cfa -288 + ^
STACK CFI 47e8 x23: x23
STACK CFI 4814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4818 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI 4828 x23: .cfa -288 + ^
STACK CFI 4864 x23: x23
STACK CFI 486c x23: .cfa -288 + ^
STACK CFI INIT 4870 84 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4880 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4894 x21: .cfa -16 + ^
STACK CFI 48f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 48f8 dc .cfa: sp 0 + .ra: x30
STACK CFI 4900 .cfa: sp 4160 +
STACK CFI 4914 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 4920 x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 4940 x21: .cfa -4128 + ^
STACK CFI 49b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 49b8 .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 49d8 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 49e0 .cfa: sp 4496 +
STACK CFI 49f4 .ra: .cfa -4488 + ^ x29: .cfa -4496 + ^
STACK CFI 4a04 x23: .cfa -4448 + ^ x24: .cfa -4440 + ^
STACK CFI 4a20 x19: .cfa -4480 + ^ x20: .cfa -4472 + ^
STACK CFI 4aa4 x21: .cfa -4464 + ^ x22: .cfa -4456 + ^
STACK CFI 4aec x25: .cfa -4432 + ^
STACK CFI 4b80 x25: x25
STACK CFI 4bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4bb8 .cfa: sp 4496 + .ra: .cfa -4488 + ^ x19: .cfa -4480 + ^ x20: .cfa -4472 + ^ x21: .cfa -4464 + ^ x22: .cfa -4456 + ^ x23: .cfa -4448 + ^ x24: .cfa -4440 + ^ x29: .cfa -4496 + ^
STACK CFI 4bcc x25: .cfa -4432 + ^
STACK CFI INIT 4bd0 104 .cfa: sp 0 + .ra: x30
STACK CFI 4bd8 .cfa: sp 4160 +
STACK CFI 4bec .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 4bf8 x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 4c04 x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI 4c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c90 .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 4cd8 1cc .cfa: sp 0 + .ra: x30
STACK CFI 4ce0 .cfa: sp 4208 +
STACK CFI 4cf4 .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI 4d00 x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI 4d0c x21: .cfa -4176 + ^ x22: .cfa -4168 + ^
STACK CFI 4d30 x19: .cfa -4192 + ^ x20: .cfa -4184 + ^
STACK CFI 4d8c x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 4dcc x25: x25 x26: x26
STACK CFI 4e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e04 .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x25: .cfa -4144 + ^ x26: .cfa -4136 + ^ x29: .cfa -4208 + ^
STACK CFI 4e3c x25: x25 x26: x26
STACK CFI 4e60 x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 4e70 x25: x25 x26: x26
STACK CFI 4e74 x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 4e98 x25: x25 x26: x26
STACK CFI 4ea0 x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI INIT 4ea8 178 .cfa: sp 0 + .ra: x30
STACK CFI 4eb0 .cfa: sp 8352 +
STACK CFI 4eb4 .ra: .cfa -8344 + ^ x29: .cfa -8352 + ^
STACK CFI 4ebc x21: .cfa -8320 + ^ x22: .cfa -8312 + ^
STACK CFI 4ec8 x25: .cfa -8288 + ^ x26: .cfa -8280 + ^
STACK CFI 4ed4 x19: .cfa -8336 + ^ x20: .cfa -8328 + ^
STACK CFI 4ef8 x23: .cfa -8304 + ^ x24: .cfa -8296 + ^ x27: .cfa -8272 + ^ x28: .cfa -8264 + ^
STACK CFI 5018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 501c .cfa: sp 8352 + .ra: .cfa -8344 + ^ x19: .cfa -8336 + ^ x20: .cfa -8328 + ^ x21: .cfa -8320 + ^ x22: .cfa -8312 + ^ x23: .cfa -8304 + ^ x24: .cfa -8296 + ^ x25: .cfa -8288 + ^ x26: .cfa -8280 + ^ x27: .cfa -8272 + ^ x28: .cfa -8264 + ^ x29: .cfa -8352 + ^
STACK CFI INIT 5020 80 .cfa: sp 0 + .ra: x30
STACK CFI 5024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5034 x19: .cfa -32 + ^
STACK CFI 5098 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 509c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 50a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 50a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50b4 x19: .cfa -32 + ^
STACK CFI 513c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5140 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5150 348 .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 515c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 516c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 51e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 51e8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 51f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 529c x23: x23 x24: x24
STACK CFI 52a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 52bc x25: .cfa -48 + ^
STACK CFI 52f8 x25: x25
STACK CFI 5318 x23: x23 x24: x24
STACK CFI 5328 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 535c x23: x23 x24: x24
STACK CFI 5368 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 536c x25: .cfa -48 + ^
STACK CFI 53e4 x25: x25
STACK CFI 53e8 x25: .cfa -48 + ^
STACK CFI 5410 x25: x25
STACK CFI 546c x25: .cfa -48 + ^
STACK CFI 548c x23: x23 x24: x24 x25: x25
STACK CFI 5490 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5494 x25: .cfa -48 + ^
STACK CFI INIT 5498 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 549c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 54a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 54ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 54d8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5514 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 55b4 x21: x21 x22: x22
STACK CFI 55b8 x25: x25 x26: x26
STACK CFI 55e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 55e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 55f8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 55fc x25: x25 x26: x26
STACK CFI 5608 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 560c x25: x25 x26: x26
STACK CFI 5610 x21: x21 x22: x22
STACK CFI 5618 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5630 x21: x21 x22: x22
STACK CFI 5634 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5640 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5644 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 5648 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 564c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 5650 188 .cfa: sp 0 + .ra: x30
STACK CFI 5658 .cfa: sp 8384 +
STACK CFI 5664 .ra: .cfa -8376 + ^ x29: .cfa -8384 + ^
STACK CFI 566c x21: .cfa -8352 + ^ x22: .cfa -8344 + ^
STACK CFI 5678 x19: .cfa -8368 + ^ x20: .cfa -8360 + ^
STACK CFI 5730 x23: .cfa -8336 + ^
STACK CFI 5778 x23: x23
STACK CFI 57a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57ac .cfa: sp 8384 + .ra: .cfa -8376 + ^ x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x29: .cfa -8384 + ^
STACK CFI 57bc x23: .cfa -8336 + ^
STACK CFI 57c8 x23: x23
STACK CFI 57d4 x23: .cfa -8336 + ^
STACK CFI INIT 57d8 118 .cfa: sp 0 + .ra: x30
STACK CFI 57e0 .cfa: sp 8272 +
STACK CFI 57e8 .ra: .cfa -8264 + ^ x29: .cfa -8272 + ^
STACK CFI 57f0 x19: .cfa -8256 + ^ x20: .cfa -8248 + ^
STACK CFI 5814 x21: .cfa -8240 + ^
STACK CFI 58a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 58ac .cfa: sp 8272 + .ra: .cfa -8264 + ^ x19: .cfa -8256 + ^ x20: .cfa -8248 + ^ x21: .cfa -8240 + ^ x29: .cfa -8272 + ^
STACK CFI INIT 58f0 148 .cfa: sp 0 + .ra: x30
STACK CFI 58f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5910 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5944 x21: .cfa -16 + ^
STACK CFI 59bc x21: x21
STACK CFI 59c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 59d8 x21: x21
STACK CFI 59dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 59ec x21: x21
STACK CFI 59fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5a08 x21: x21
STACK CFI 5a0c x21: .cfa -16 + ^
STACK CFI 5a18 x21: x21
STACK CFI 5a1c x21: .cfa -16 + ^
STACK CFI 5a2c x21: x21
STACK CFI INIT 5a38 5b0 .cfa: sp 0 + .ra: x30
STACK CFI 5a40 .cfa: sp 12656 +
STACK CFI 5a44 .ra: .cfa -12648 + ^ x29: .cfa -12656 + ^
STACK CFI 5a4c x21: .cfa -12624 + ^ x22: .cfa -12616 + ^
STACK CFI 5a54 x19: .cfa -12640 + ^ x20: .cfa -12632 + ^
STACK CFI 5a60 x25: .cfa -12592 + ^ x26: .cfa -12584 + ^
STACK CFI 5a80 x23: .cfa -12608 + ^ x24: .cfa -12600 + ^
STACK CFI 5a8c x27: .cfa -12576 + ^ x28: .cfa -12568 + ^
STACK CFI 5cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ccc .cfa: sp 12656 + .ra: .cfa -12648 + ^ x19: .cfa -12640 + ^ x20: .cfa -12632 + ^ x21: .cfa -12624 + ^ x22: .cfa -12616 + ^ x23: .cfa -12608 + ^ x24: .cfa -12600 + ^ x25: .cfa -12592 + ^ x26: .cfa -12584 + ^ x27: .cfa -12576 + ^ x28: .cfa -12568 + ^ x29: .cfa -12656 + ^
STACK CFI INIT 5fe8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ff8 158 .cfa: sp 0 + .ra: x30
STACK CFI 5ffc .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 6004 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 6020 x21: .cfa -352 + ^
STACK CFI 60dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60e0 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x29: .cfa -384 + ^
STACK CFI INIT 6150 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 6154 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 6164 x23: .cfa -192 + ^
STACK CFI 617c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 61b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 61bc .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x23: .cfa -192 + ^ x29: .cfa -240 + ^
STACK CFI 61c8 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 6264 x21: x21 x22: x22
STACK CFI 6274 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 62b0 x21: x21 x22: x22
STACK CFI 62c0 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 62f0 x21: x21 x22: x22
STACK CFI 62fc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI INIT 6300 e0 .cfa: sp 0 + .ra: x30
STACK CFI 6304 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6314 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6364 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 63e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6408 38 .cfa: sp 0 + .ra: x30
STACK CFI 640c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6414 x19: .cfa -16 + ^
STACK CFI 643c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6440 64 .cfa: sp 0 + .ra: x30
STACK CFI 6444 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 644c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6454 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 64a8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 64ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 64b8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 64c8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 64e4 x23: .cfa -160 + ^
STACK CFI 6538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 653c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI INIT 6580 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6590 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 65a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 65a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65b0 x19: .cfa -16 + ^
STACK CFI 65d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 65e0 170 .cfa: sp 0 + .ra: x30
STACK CFI 65e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 66d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6750 74 .cfa: sp 0 + .ra: x30
STACK CFI 6754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6764 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 679c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67c8 28 .cfa: sp 0 + .ra: x30
STACK CFI 67cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 67f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6804 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 685c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6860 60 .cfa: sp 0 + .ra: x30
STACK CFI 6864 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6874 x19: .cfa -48 + ^
STACK CFI 68b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 68bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 68c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68c8 bc .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 68dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 68ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6974 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6988 84 .cfa: sp 0 + .ra: x30
STACK CFI 698c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6994 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a10 80 .cfa: sp 0 + .ra: x30
STACK CFI 6a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6a90 70 .cfa: sp 0 + .ra: x30
STACK CFI 6a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6aa4 x19: .cfa -32 + ^
STACK CFI 6af8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6afc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b00 90 .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6b14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6b90 78 .cfa: sp 0 + .ra: x30
STACK CFI 6b94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6ba4 x19: .cfa -64 + ^
STACK CFI 6c00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6c08 88 .cfa: sp 0 + .ra: x30
STACK CFI 6c0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6c1c x19: .cfa -64 + ^
STACK CFI 6c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6c90 174 .cfa: sp 0 + .ra: x30
STACK CFI 6c94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6ca4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6cb0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 6cc8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6d20 x23: .cfa -64 + ^
STACK CFI 6d94 x23: x23
STACK CFI 6dc0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6dc4 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 6dd0 x23: x23
STACK CFI 6e00 x23: .cfa -64 + ^
STACK CFI INIT 6e08 78 .cfa: sp 0 + .ra: x30
STACK CFI 6e0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e1c x19: .cfa -48 + ^
STACK CFI 6e78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6e80 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6e84 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 6e90 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 6ea0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 6f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6f20 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 6f28 bc .cfa: sp 0 + .ra: x30
STACK CFI 6f30 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f54 x23: .cfa -16 + ^
STACK CFI 6fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6fa8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6fe8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ff0 144 .cfa: sp 0 + .ra: x30
STACK CFI 6ff4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7004 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7024 x21: .cfa -48 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7118 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7138 148 .cfa: sp 0 + .ra: x30
STACK CFI 713c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 714c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 716c x21: .cfa -48 + ^
STACK CFI 725c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7260 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7280 74 .cfa: sp 0 + .ra: x30
STACK CFI 7284 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 728c x21: .cfa -16 + ^
STACK CFI 72a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72d4 x19: x19 x20: x20
STACK CFI 72f0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 72f8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 72fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7304 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7310 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7320 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 73b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 73bc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 73d0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 73d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 73e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 73f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7494 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7498 5c .cfa: sp 0 + .ra: x30
STACK CFI 749c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74ac x19: .cfa -32 + ^
STACK CFI 74ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 74f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 74f8 128 .cfa: sp 0 + .ra: x30
STACK CFI 74fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7504 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7518 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7538 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7604 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7628 7c .cfa: sp 0 + .ra: x30
STACK CFI 762c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 768c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 76a8 70 .cfa: sp 0 + .ra: x30
STACK CFI 76ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 76bc x19: .cfa -32 + ^
STACK CFI 7710 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7714 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7718 88 .cfa: sp 0 + .ra: x30
STACK CFI 771c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7724 x19: .cfa -32 + ^
STACK CFI 7798 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 779c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 77a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 77a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7814 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7830 70 .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7844 x19: .cfa -32 + ^
STACK CFI 7898 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 789c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 78a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 78a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7920 70 .cfa: sp 0 + .ra: x30
STACK CFI 7924 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7934 x19: .cfa -32 + ^
STACK CFI 7988 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 798c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7990 78 .cfa: sp 0 + .ra: x30
STACK CFI 7994 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 79a4 x19: .cfa -48 + ^
STACK CFI 7a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7a08 94 .cfa: sp 0 + .ra: x30
STACK CFI 7a0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7a1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7a2c x21: .cfa -48 + ^
STACK CFI 7a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7aa0 80 .cfa: sp 0 + .ra: x30
STACK CFI 7aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ab4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7b20 2c .cfa: sp 0 + .ra: x30
STACK CFI 7b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b50 2c .cfa: sp 0 + .ra: x30
STACK CFI 7b54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b80 70 .cfa: sp 0 + .ra: x30
STACK CFI 7b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b94 x19: .cfa -32 + ^
STACK CFI 7be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7bf0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c24 x21: .cfa -64 + ^
STACK CFI 7c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7c8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7ca0 78 .cfa: sp 0 + .ra: x30
STACK CFI 7ca4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7cb4 x19: .cfa -64 + ^
STACK CFI 7d10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d14 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7d18 74 .cfa: sp 0 + .ra: x30
STACK CFI 7d1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d2c x19: .cfa -48 + ^
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7d90 70 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7da4 x19: .cfa -48 + ^
STACK CFI 7df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7dfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7e00 80 .cfa: sp 0 + .ra: x30
STACK CFI 7e04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e14 x19: .cfa -80 + ^
STACK CFI 7e68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e6c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e80 80 .cfa: sp 0 + .ra: x30
STACK CFI 7e84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e94 x19: .cfa -80 + ^
STACK CFI 7ee8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7eec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7f00 70 .cfa: sp 0 + .ra: x30
STACK CFI 7f04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f14 x19: .cfa -80 + ^
STACK CFI 7f68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f6c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7f70 70 .cfa: sp 0 + .ra: x30
STACK CFI 7f74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f84 x19: .cfa -80 + ^
STACK CFI 7fd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7fdc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7fe0 70 .cfa: sp 0 + .ra: x30
STACK CFI 7fe4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7ff4 x19: .cfa -80 + ^
STACK CFI 8048 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 804c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8050 70 .cfa: sp 0 + .ra: x30
STACK CFI 8054 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8064 x19: .cfa -80 + ^
STACK CFI 80b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 80bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 80c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 80c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 80d4 x19: .cfa -80 + ^
STACK CFI 8128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 812c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8130 70 .cfa: sp 0 + .ra: x30
STACK CFI 8134 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8144 x19: .cfa -80 + ^
STACK CFI 8198 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 819c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 81a0 70 .cfa: sp 0 + .ra: x30
STACK CFI 81a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 81b4 x19: .cfa -80 + ^
STACK CFI 8208 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 820c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8210 84 .cfa: sp 0 + .ra: x30
STACK CFI 8214 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8224 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 827c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8280 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8298 70 .cfa: sp 0 + .ra: x30
STACK CFI 829c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 82ac x19: .cfa -48 + ^
STACK CFI 8300 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8304 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8308 168 .cfa: sp 0 + .ra: x30
STACK CFI 830c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8314 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8320 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8338 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8354 x25: .cfa -64 + ^
STACK CFI 83a4 x25: x25
STACK CFI 83d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 83d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 8428 x25: x25
STACK CFI 846c x25: .cfa -64 + ^
STACK CFI INIT 8470 114 .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8488 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 84f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 851c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 853c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8540 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8588 74 .cfa: sp 0 + .ra: x30
STACK CFI 858c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 859c x19: .cfa -32 + ^
STACK CFI 85f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8600 70 .cfa: sp 0 + .ra: x30
STACK CFI 8604 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8614 x19: .cfa -32 + ^
STACK CFI 8668 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 866c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8670 c8 .cfa: sp 0 + .ra: x30
STACK CFI 8674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 867c x19: .cfa -32 + ^
STACK CFI 8730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8734 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8738 80 .cfa: sp 0 + .ra: x30
STACK CFI 873c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 874c x19: .cfa -48 + ^
STACK CFI 87a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 87a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 87b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 87bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 87c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 87cc x21: .cfa -16 + ^
STACK CFI 87fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8800 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 882c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8830 28 .cfa: sp 0 + .ra: x30
STACK CFI 8834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 883c x19: .cfa -16 + ^
STACK CFI 8850 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8858 68 .cfa: sp 0 + .ra: x30
STACK CFI 885c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8864 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 88b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 88bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 88c0 78 .cfa: sp 0 + .ra: x30
STACK CFI 88c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 88d4 x19: .cfa -48 + ^
STACK CFI 8930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8934 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8938 88 .cfa: sp 0 + .ra: x30
STACK CFI 893c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 894c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 89a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 89ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 89c0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 89d4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 89ec x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 89f8 x25: .cfa -64 + ^
STACK CFI 8a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8a8c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8aa0 cc .cfa: sp 0 + .ra: x30
STACK CFI 8aa4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8ab4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8ac4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8ae8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8b58 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8b70 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 8b74 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 8b7c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 8b8c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8bfc .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x29: .cfa -304 + ^
STACK CFI INIT 8f18 84 .cfa: sp 0 + .ra: x30
STACK CFI 8f1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8fa0 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 8fa4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8fac x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 8fb8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8fd4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 8fe8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8ff0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 90d0 x21: x21 x22: x22
STACK CFI 90d4 x25: x25 x26: x26
STACK CFI 9108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 910c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 9214 x21: x21 x22: x22
STACK CFI 9218 x25: x25 x26: x26
STACK CFI 921c x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9264 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 9268 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 926c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 9270 280 .cfa: sp 0 + .ra: x30
STACK CFI 9274 .cfa: sp 688 +
STACK CFI 9278 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 9280 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 9288 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 92a8 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 92b4 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 9364 x27: .cfa -608 + ^
STACK CFI 9434 x25: x25 x26: x26
STACK CFI 9438 x27: x27
STACK CFI 943c x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 9448 x27: .cfa -608 + ^
STACK CFI 944c x25: x25 x26: x26
STACK CFI 9450 x27: x27
STACK CFI 9484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9488 .cfa: sp 688 + .ra: .cfa -680 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x29: .cfa -688 + ^
STACK CFI 9494 x25: x25 x26: x26
STACK CFI 9498 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 94b0 x25: x25 x26: x26
STACK CFI 94b4 x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^
STACK CFI 94d4 x25: x25 x26: x26
STACK CFI 94d8 x27: x27
STACK CFI 94dc x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^
STACK CFI 94e4 x25: x25 x26: x26 x27: x27
STACK CFI 94e8 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI 94ec x27: .cfa -608 + ^
STACK CFI INIT 94f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 94f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9504 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 955c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 957c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 95b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95b8 34 .cfa: sp 0 + .ra: x30
STACK CFI 95bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 95f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9628 38 .cfa: sp 0 + .ra: x30
STACK CFI 9630 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 965c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9660 38 .cfa: sp 0 + .ra: x30
STACK CFI 9668 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9698 12c .cfa: sp 0 + .ra: x30
STACK CFI 969c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 96a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 96b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 96bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 977c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9780 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 97c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 97c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 97d8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 97dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 97e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9834 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 988c x21: x21 x22: x22
STACK CFI 9894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9898 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 98a0 x21: x21 x22: x22
STACK CFI 98a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 98a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98c8 20 .cfa: sp 0 + .ra: x30
STACK CFI 98cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 98e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 98e8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 98ec .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 98fc x25: .cfa -288 + ^
STACK CFI 9904 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 9914 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 9928 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 99c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 99c8 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x29: .cfa -352 + ^
STACK CFI INIT 99d8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 99dc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 99ec x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 9a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9a7c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9ab8 7c .cfa: sp 0 + .ra: x30
STACK CFI 9abc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9acc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9b38 74 .cfa: sp 0 + .ra: x30
STACK CFI 9b3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9b4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9ba8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bc0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9c98 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9ca0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9ca8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cd4 x21: .cfa -16 + ^
STACK CFI 9d0c x21: x21
STACK CFI 9d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9d30 x21: .cfa -16 + ^
STACK CFI 9d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9d54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9d58 x21: x21
STACK CFI INIT 9d60 120 .cfa: sp 0 + .ra: x30
STACK CFI 9d6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9d74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9d7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9d84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9d90 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9e74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 9e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 9e80 54 .cfa: sp 0 + .ra: x30
STACK CFI 9e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9e9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9ed8 27c .cfa: sp 0 + .ra: x30
STACK CFI 9edc .cfa: sp 2416 +
STACK CFI 9ee4 .ra: .cfa -2408 + ^ x29: .cfa -2416 + ^
STACK CFI 9eec x23: .cfa -2368 + ^ x24: .cfa -2360 + ^
STACK CFI 9ef8 x21: .cfa -2384 + ^ x22: .cfa -2376 + ^
STACK CFI 9f64 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9f68 .cfa: sp 2416 + .ra: .cfa -2408 + ^ x21: .cfa -2384 + ^ x22: .cfa -2376 + ^ x23: .cfa -2368 + ^ x24: .cfa -2360 + ^ x29: .cfa -2416 + ^
STACK CFI 9f6c x25: .cfa -2352 + ^ x26: .cfa -2344 + ^
STACK CFI 9f74 x19: .cfa -2400 + ^ x20: .cfa -2392 + ^
STACK CFI 9fe0 x27: .cfa -2336 + ^ x28: .cfa -2328 + ^
STACK CFI a0cc x19: x19 x20: x20
STACK CFI a0d8 x25: x25 x26: x26
STACK CFI a0dc x27: x27 x28: x28
STACK CFI a0e0 x19: .cfa -2400 + ^ x20: .cfa -2392 + ^ x25: .cfa -2352 + ^ x26: .cfa -2344 + ^ x27: .cfa -2336 + ^ x28: .cfa -2328 + ^
STACK CFI a110 x27: x27 x28: x28
STACK CFI a114 x19: x19 x20: x20
STACK CFI a118 x25: x25 x26: x26
STACK CFI a11c x19: .cfa -2400 + ^ x20: .cfa -2392 + ^ x25: .cfa -2352 + ^ x26: .cfa -2344 + ^
STACK CFI a120 x19: x19 x20: x20
STACK CFI a124 x25: x25 x26: x26
STACK CFI a12c x19: .cfa -2400 + ^ x20: .cfa -2392 + ^ x25: .cfa -2352 + ^ x26: .cfa -2344 + ^
STACK CFI a138 x19: x19 x20: x20
STACK CFI a140 x25: x25 x26: x26
STACK CFI a148 x19: .cfa -2400 + ^ x20: .cfa -2392 + ^
STACK CFI a14c x25: .cfa -2352 + ^ x26: .cfa -2344 + ^
STACK CFI a150 x27: .cfa -2336 + ^ x28: .cfa -2328 + ^
STACK CFI INIT a158 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a168 1b8 .cfa: sp 0 + .ra: x30
STACK CFI a16c .cfa: sp 2160 +
STACK CFI a170 .ra: .cfa -2152 + ^ x29: .cfa -2160 + ^
STACK CFI a178 x27: .cfa -2080 + ^ x28: .cfa -2072 + ^
STACK CFI a180 x23: .cfa -2112 + ^ x24: .cfa -2104 + ^
STACK CFI a18c x21: .cfa -2128 + ^ x22: .cfa -2120 + ^
STACK CFI a1b0 x25: .cfa -2096 + ^ x26: .cfa -2088 + ^
STACK CFI a1c8 x19: .cfa -2144 + ^ x20: .cfa -2136 + ^
STACK CFI a2a0 x19: x19 x20: x20
STACK CFI a2a4 x25: x25 x26: x26
STACK CFI a2d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI a2d8 .cfa: sp 2160 + .ra: .cfa -2152 + ^ x19: .cfa -2144 + ^ x20: .cfa -2136 + ^ x21: .cfa -2128 + ^ x22: .cfa -2120 + ^ x23: .cfa -2112 + ^ x24: .cfa -2104 + ^ x25: .cfa -2096 + ^ x26: .cfa -2088 + ^ x27: .cfa -2080 + ^ x28: .cfa -2072 + ^ x29: .cfa -2160 + ^
STACK CFI a2e8 x19: x19 x20: x20
STACK CFI a2ec x25: x25 x26: x26
STACK CFI a2f0 x25: .cfa -2096 + ^ x26: .cfa -2088 + ^
STACK CFI a2fc x25: x25 x26: x26
STACK CFI a304 x19: .cfa -2144 + ^ x20: .cfa -2136 + ^ x25: .cfa -2096 + ^ x26: .cfa -2088 + ^
STACK CFI a30c x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI a318 x19: .cfa -2144 + ^ x20: .cfa -2136 + ^
STACK CFI a31c x25: .cfa -2096 + ^ x26: .cfa -2088 + ^
STACK CFI INIT a320 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a330 134 .cfa: sp 0 + .ra: x30
STACK CFI a338 .cfa: sp 4288 +
STACK CFI a348 .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI a350 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI a374 x21: .cfa -4256 + ^
STACK CFI a424 x21: x21
STACK CFI a428 x21: .cfa -4256 + ^
STACK CFI a42c x21: x21
STACK CFI a458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a45c .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x29: .cfa -4288 + ^
STACK CFI a460 x21: .cfa -4256 + ^
STACK CFI INIT a468 70 .cfa: sp 0 + .ra: x30
STACK CFI a46c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a47c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a4d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT a4d8 5c .cfa: sp 0 + .ra: x30
STACK CFI a4dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4ec x19: .cfa -32 + ^
STACK CFI a52c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a530 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT a538 7c .cfa: sp 0 + .ra: x30
STACK CFI a53c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a54c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a5ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a5b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT a5b8 74 .cfa: sp 0 + .ra: x30
STACK CFI a5bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a5cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a628 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT a630 68 .cfa: sp 0 + .ra: x30
STACK CFI a634 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a644 x19: .cfa -48 + ^
STACK CFI a690 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a694 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a698 7c .cfa: sp 0 + .ra: x30
STACK CFI a69c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a6ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a70c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a710 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT a718 90 .cfa: sp 0 + .ra: x30
STACK CFI a71c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a72c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a794 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT a7a8 64 .cfa: sp 0 + .ra: x30
STACK CFI a7ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a7bc x19: .cfa -48 + ^
STACK CFI a804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a808 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a810 64 .cfa: sp 0 + .ra: x30
STACK CFI a814 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a824 x19: .cfa -48 + ^
STACK CFI a86c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a870 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a878 64 .cfa: sp 0 + .ra: x30
STACK CFI a87c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a88c x19: .cfa -48 + ^
STACK CFI a8d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a8d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a8e0 94 .cfa: sp 0 + .ra: x30
STACK CFI a8e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a8f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a95c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a960 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT a978 64 .cfa: sp 0 + .ra: x30
STACK CFI a97c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a98c x19: .cfa -48 + ^
STACK CFI a9d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT a9e0 60 .cfa: sp 0 + .ra: x30
STACK CFI a9e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a9f4 x19: .cfa -48 + ^
STACK CFI aa38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT aa40 64 .cfa: sp 0 + .ra: x30
STACK CFI aa44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aa54 x19: .cfa -64 + ^
STACK CFI aa9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aaa0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT aaa8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT ab08 dc .cfa: sp 0 + .ra: x30
STACK CFI ab0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ab18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ab24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ab30 x23: .cfa -16 + ^
STACK CFI ab88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ab8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI abdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT abe8 160 .cfa: sp 0 + .ra: x30
STACK CFI abec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI abf4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ac00 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ac08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI acac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI acb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI acd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI acd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI ace8 x25: .cfa -16 + ^
STACK CFI ad14 x25: x25
STACK CFI ad44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT ad48 28 .cfa: sp 0 + .ra: x30
STACK CFI ad4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ad6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad70 80 .cfa: sp 0 + .ra: x30
STACK CFI ad74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad84 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ada0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI add8 x19: x19 x20: x20
STACK CFI ade4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI ade8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT adf0 78 .cfa: sp 0 + .ra: x30
STACK CFI adf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae0c x19: .cfa -16 + ^
STACK CFI ae28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ae2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ae50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ae60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT ae68 b8 .cfa: sp 0 + .ra: x30
STACK CFI ae6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ae74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ae80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI aecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aed0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT af20 94 .cfa: sp 0 + .ra: x30
STACK CFI af24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI af34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI af80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT afb8 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT b038 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT b068 7c .cfa: sp 0 + .ra: x30
STACK CFI b06c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b074 x19: .cfa -16 + ^
STACK CFI b0c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b0c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b0e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b0e8 18 .cfa: sp 0 + .ra: x30
STACK CFI b0ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b0fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b100 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b140 30 .cfa: sp 0 + .ra: x30
STACK CFI b144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b14c x19: .cfa -16 + ^
STACK CFI b168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b170 7c .cfa: sp 0 + .ra: x30
STACK CFI b174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b180 x19: .cfa -16 + ^
STACK CFI b1e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b1f0 a4 .cfa: sp 0 + .ra: x30
STACK CFI b1f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b204 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b224 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b25c x19: x19 x20: x20
STACK CFI b268 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b26c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b270 x19: x19 x20: x20
STACK CFI b290 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT b298 208 .cfa: sp 0 + .ra: x30
STACK CFI b29c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI b2a4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI b2ac x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI b2e8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI b3a4 x25: .cfa -160 + ^
STACK CFI b404 x25: x25
STACK CFI b410 x23: x23 x24: x24
STACK CFI b43c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b440 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI b488 x23: x23 x24: x24
STACK CFI b490 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI b494 x25: .cfa -160 + ^
STACK CFI b498 x25: x25
STACK CFI b49c x25: .cfa -160 + ^
STACK CFI INIT b4a0 194 .cfa: sp 0 + .ra: x30
STACK CFI b4a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI b4ac x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI b624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b628 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT b638 d0 .cfa: sp 0 + .ra: x30
STACK CFI b63c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI b6f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b6f8 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI INIT b708 134 .cfa: sp 0 + .ra: x30
STACK CFI b70c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI b814 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b818 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI INIT b840 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT b890 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT b8c0 13c .cfa: sp 0 + .ra: x30
STACK CFI b8e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b8f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b908 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b920 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b930 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b9e8 x21: x21 x22: x22
STACK CFI b9ec x25: x25 x26: x26
STACK CFI b9f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT ba00 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba38 7c .cfa: sp 0 + .ra: x30
STACK CFI ba3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ba5c x23: .cfa -16 + ^
STACK CFI bab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT bab8 36c .cfa: sp 0 + .ra: x30
STACK CFI babc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI bac4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI bae8 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI baf0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI bc9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bca0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT be28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT be38 48 .cfa: sp 0 + .ra: x30
STACK CFI be40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI be48 x19: .cfa -16 + ^
STACK CFI be78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT be80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT be90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT bea0 48 .cfa: sp 0 + .ra: x30
STACK CFI bea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI beb0 x19: .cfa -16 + ^
STACK CFI bee0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bee8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT bef0 94 .cfa: sp 0 + .ra: x30
STACK CFI bef4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bf04 x19: .cfa -96 + ^
STACK CFI bf5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bf60 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT bf88 320 .cfa: sp 0 + .ra: x30
STACK CFI bf8c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI bf94 x27: .cfa -96 + ^
STACK CFI bf9c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI bfac x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI bfc8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI c114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI c118 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI INIT c2a8 9c .cfa: sp 0 + .ra: x30
STACK CFI c2ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c2bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c32c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c330 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT c348 c8 .cfa: sp 0 + .ra: x30
STACK CFI c34c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c354 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c3f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c3fc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT c410 2c .cfa: sp 0 + .ra: x30
STACK CFI c414 .cfa: sp 32 +
STACK CFI c418 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c438 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c440 34 .cfa: sp 0 + .ra: x30
STACK CFI c444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c478 a4 .cfa: sp 0 + .ra: x30
STACK CFI c47c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c48c x19: .cfa -64 + ^
STACK CFI c514 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c518 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c520 80 .cfa: sp 0 + .ra: x30
STACK CFI c524 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c534 x19: .cfa -48 + ^
STACK CFI c598 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c59c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT c5a0 104 .cfa: sp 0 + .ra: x30
STACK CFI c5a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c5b4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c658 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT c6a8 c8 .cfa: sp 0 + .ra: x30
STACK CFI c6ac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c6b4 x19: .cfa -128 + ^
STACK CFI c768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c76c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT c770 84 .cfa: sp 0 + .ra: x30
STACK CFI c774 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c784 x19: .cfa -64 + ^
STACK CFI c7ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c7f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c7f8 88 .cfa: sp 0 + .ra: x30
STACK CFI c7fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c80c x19: .cfa -64 + ^
STACK CFI c878 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c87c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c880 8c .cfa: sp 0 + .ra: x30
STACK CFI c884 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c894 x19: .cfa -64 + ^
STACK CFI c904 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c908 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c910 a0 .cfa: sp 0 + .ra: x30
STACK CFI c914 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c924 x19: .cfa -64 + ^
STACK CFI c9a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c9ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT c9b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9c0 a0 .cfa: sp 0 + .ra: x30
STACK CFI c9c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c9d4 x19: .cfa -96 + ^
STACK CFI ca58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ca5c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT ca60 a0 .cfa: sp 0 + .ra: x30
STACK CFI ca64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ca74 x19: .cfa -96 + ^
STACK CFI caf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI cafc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT cb00 26c .cfa: sp 0 + .ra: x30
STACK CFI cb04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI cb14 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI cb24 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI cbd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cbd8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT cd70 40 .cfa: sp 0 + .ra: x30
STACK CFI cd78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cd80 x19: .cfa -16 + ^
STACK CFI cda8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cdb0 100 .cfa: sp 0 + .ra: x30
STACK CFI cdb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cdc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cde8 x21: .cfa -48 + ^
STACK CFI ce50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ce54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT ceb0 30 .cfa: sp 0 + .ra: x30
STACK CFI ceb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cec0 x19: .cfa -16 + ^
STACK CFI ced8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cee0 78 .cfa: sp 0 + .ra: x30
STACK CFI cee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cef4 x19: .cfa -48 + ^
STACK CFI cf50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI cf54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT cf58 1a0 .cfa: sp 0 + .ra: x30
STACK CFI cf5c .cfa: sp 1104 +
STACK CFI cf74 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI cf80 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI cfbc x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI d018 x21: x21 x22: x22
STACK CFI d040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d044 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x29: .cfa -1104 + ^
STACK CFI d0e0 x21: x21 x22: x22
STACK CFI d0e4 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI d0e8 x21: x21 x22: x22
STACK CFI d0f4 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI INIT d0f8 78 .cfa: sp 0 + .ra: x30
STACK CFI d0fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d10c x19: .cfa -64 + ^
STACK CFI d168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d16c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT d170 78 .cfa: sp 0 + .ra: x30
STACK CFI d174 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d184 x19: .cfa -64 + ^
STACK CFI d1e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d1e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT d1e8 18c .cfa: sp 0 + .ra: x30
STACK CFI d1ec .cfa: sp 1120 +
STACK CFI d1f0 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI d1f8 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI d204 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI d224 x25: .cfa -1056 + ^
STACK CFI d240 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI d2b4 x19: x19 x20: x20
STACK CFI d2e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d2e8 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x29: .cfa -1120 + ^
STACK CFI d364 x19: x19 x20: x20
STACK CFI d370 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI INIT d378 78 .cfa: sp 0 + .ra: x30
STACK CFI d37c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d38c x19: .cfa -48 + ^
STACK CFI d3e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d3ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT d3f0 78 .cfa: sp 0 + .ra: x30
STACK CFI d3f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d404 x19: .cfa -48 + ^
STACK CFI d460 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d464 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT d468 98 .cfa: sp 0 + .ra: x30
STACK CFI d46c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d47c x19: .cfa -80 + ^
STACK CFI d4f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d4fc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT d500 18c .cfa: sp 0 + .ra: x30
STACK CFI d504 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d50c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI d518 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI d528 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d530 x25: .cfa -64 + ^
STACK CFI d5dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d5e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT d690 30 .cfa: sp 0 + .ra: x30
STACK CFI d698 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d6a0 x19: .cfa -16 + ^
STACK CFI d6b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d6c0 160 .cfa: sp 0 + .ra: x30
STACK CFI d6c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d6cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d6d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d6e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d78c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT d820 30 .cfa: sp 0 + .ra: x30
STACK CFI d828 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d830 x19: .cfa -16 + ^
STACK CFI d848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d850 1dc .cfa: sp 0 + .ra: x30
STACK CFI d854 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI d85c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d86c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI d87c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI d884 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d898 x27: .cfa -64 + ^
STACK CFI d954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d958 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI INIT da30 38 .cfa: sp 0 + .ra: x30
STACK CFI da38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da40 x19: .cfa -16 + ^
STACK CFI da60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT da68 78 .cfa: sp 0 + .ra: x30
STACK CFI da6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI da7c x19: .cfa -48 + ^
STACK CFI dad8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dadc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT dae0 20 .cfa: sp 0 + .ra: x30
STACK CFI dae4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dafc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT db00 9c .cfa: sp 0 + .ra: x30
STACK CFI db04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI db0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI db44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI db74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI db88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT dba0 c0 .cfa: sp 0 + .ra: x30
STACK CFI dba8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dbb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dbd4 x21: .cfa -16 + ^
STACK CFI dc24 x21: x21
STACK CFI dc34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI dc44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI dc5c x21: x21
STACK CFI INIT dc60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT dc78 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dc88 e0 .cfa: sp 0 + .ra: x30
STACK CFI dc90 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dca0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dca8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI dcb4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dcec x23: x23 x24: x24
STACK CFI dcfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dd00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI dd04 x25: .cfa -16 + ^
STACK CFI dd34 x25: x25
STACK CFI dd3c x23: x23 x24: x24
STACK CFI dd44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dd4c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI dd58 x23: x23 x24: x24
STACK CFI dd64 x25: x25
STACK CFI INIT dd68 34 .cfa: sp 0 + .ra: x30
STACK CFI dd70 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dd78 x19: .cfa -16 + ^
STACK CFI dd94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dda0 30c .cfa: sp 0 + .ra: x30
STACK CFI dda4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI ddac x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI ddc8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI de00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI de04 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI de0c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI de18 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI de48 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI dfec x21: x21 x22: x22
STACK CFI dff0 x23: x23 x24: x24
STACK CFI dff4 x27: x27 x28: x28
STACK CFI dff8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI e084 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI e08c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI e094 x21: x21 x22: x22
STACK CFI e098 x23: x23 x24: x24
STACK CFI e0a0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI e0a4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI e0a8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT e0b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI e0b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e0c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e138 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT e158 70 .cfa: sp 0 + .ra: x30
STACK CFI e15c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e16c x19: .cfa -32 + ^
STACK CFI e1c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e1c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT e1c8 a4 .cfa: sp 0 + .ra: x30
STACK CFI e1cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e1dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e248 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT e270 10c .cfa: sp 0 + .ra: x30
STACK CFI e274 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e284 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e290 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e2bc x23: .cfa -48 + ^
STACK CFI e300 x23: x23
STACK CFI e328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e32c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI e344 x23: .cfa -48 + ^
STACK CFI e35c x23: x23
STACK CFI e360 x23: .cfa -48 + ^
STACK CFI e370 x23: x23
STACK CFI e378 x23: .cfa -48 + ^
STACK CFI INIT e380 10c .cfa: sp 0 + .ra: x30
STACK CFI e384 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e394 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e3a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e3cc x23: .cfa -48 + ^
STACK CFI e410 x23: x23
STACK CFI e438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e43c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI e454 x23: .cfa -48 + ^
STACK CFI e46c x23: x23
STACK CFI e470 x23: .cfa -48 + ^
STACK CFI e480 x23: x23
STACK CFI e488 x23: .cfa -48 + ^
STACK CFI INIT e490 84 .cfa: sp 0 + .ra: x30
STACK CFI e494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e4a4 x19: .cfa -32 + ^
STACK CFI e4fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e500 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT e518 ec .cfa: sp 0 + .ra: x30
STACK CFI e51c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI e524 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e5fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e600 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI INIT e608 4 .cfa: sp 0 + .ra: x30
