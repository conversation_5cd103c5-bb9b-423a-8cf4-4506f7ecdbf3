MODULE Linux arm64 EB1FD48A470297A4890257E71DED56E80 libwacom.so.2
INFO CODE_ID 8AD41FEB0247A497890257E71DED56E8B4C9D247
PUBLIC 3200 0 libwacom_new_from_usbid
PUBLIC 3278 0 libwacom_new_from_name
PUBLIC 3538 0 libwacom_destroy
PUBLIC 35e8 0 libwacom_get_vendor_id
PUBLIC 3670 0 libwacom_get_name
PUBLIC 3678 0 libwacom_get_model_name
PUBLIC 3680 0 libwacom_get_layout_filename
PUBLIC 3688 0 libwacom_get_product_id
PUBLIC 3710 0 libwacom_get_match
PUBLIC 3798 0 libwacom_get_matches
PUBLIC 37a0 0 libwacom_compare
PUBLIC 3a40 0 libwacom_get_paired_device
PUBLIC 3a48 0 libwacom_get_width
PUBLIC 3a50 0 libwacom_get_height
PUBLIC 3a58 0 libwacom_get_class
PUBLIC 3a60 0 libwacom_has_stylus
PUBLIC 3a70 0 libwacom_has_touch
PUBLIC 3a80 0 libwacom_get_num_buttons
PUBLIC 3a88 0 libwacom_get_supported_styli
PUBLIC 3be8 0 libwacom_has_ring
PUBLIC 3bf8 0 libwacom_has_ring2
PUBLIC 3c08 0 libwacom_get_ring_num_modes
PUBLIC 3c10 0 libwacom_get_ring2_num_modes
PUBLIC 3c18 0 libwacom_get_num_strips
PUBLIC 3c20 0 libwacom_get_strips_num_modes
PUBLIC 3c28 0 libwacom_get_status_leds
PUBLIC 3db0 0 libwacom_get_button_led_group
PUBLIC 3ee8 0 libwacom_is_reversible
PUBLIC 3ef8 0 libwacom_has_touchswitch
PUBLIC 3f08 0 libwacom_get_integration_flags
PUBLIC 3f18 0 libwacom_is_builtin
PUBLIC 3f30 0 libwacom_get_bustype
PUBLIC 3fb8 0 libwacom_get_button_flag
PUBLIC 41f0 0 libwacom_get_button_evdev_code
PUBLIC 43f8 0 libwacom_stylus_get_for_id
PUBLIC 4408 0 libwacom_stylus_get_id
PUBLIC 4410 0 libwacom_stylus_get_name
PUBLIC 4418 0 libwacom_stylus_get_num_buttons
PUBLIC 4460 0 libwacom_stylus_has_eraser
PUBLIC 4468 0 libwacom_stylus_is_eraser
PUBLIC 4470 0 libwacom_stylus_has_lens
PUBLIC 4478 0 libwacom_stylus_has_wheel
PUBLIC 4480 0 libwacom_stylus_get_axes
PUBLIC 4488 0 libwacom_stylus_get_type
PUBLIC 44c8 0 libwacom_print_stylus_description
PUBLIC 4870 0 libwacom_match_get_name
PUBLIC 4878 0 libwacom_match_get_bustype
PUBLIC 4880 0 libwacom_match_get_product_id
PUBLIC 4888 0 libwacom_match_get_vendor_id
PUBLIC 49b0 0 libwacom_print_device_description
PUBLIC 4ff8 0 libwacom_match_get_match_string
PUBLIC 50c8 0 libwacom_new_from_path
PUBLIC 57d0 0 libwacom_error_new
PUBLIC 57f0 0 libwacom_error_free
PUBLIC 5828 0 libwacom_error_get_code
PUBLIC 5830 0 libwacom_error_get_message
PUBLIC 5e60 0 libwacom_database_destroy
PUBLIC 5e98 0 libwacom_database_new_for_path
PUBLIC 74d8 0 libwacom_database_new
PUBLIC 74e8 0 libwacom_list_devices_from_database
PUBLIC 75a0 0 libwacom_match_destroy
PUBLIC 75a8 0 libwacom_match_new
PUBLIC 75b0 0 libwacom_error_set
PUBLIC 75b8 0 libwacom_stylus_destroy
PUBLIC 75c0 0 libwacom_update_match
STACK CFI INIT 2ef8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f28 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f68 48 .cfa: sp 0 + .ra: x30
STACK CFI 2f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f74 x19: .cfa -16 + ^
STACK CFI 2fac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2fb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb8 188 .cfa: sp 0 + .ra: x30
STACK CFI 2fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fc4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 313c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3140 54 .cfa: sp 0 + .ra: x30
STACK CFI 3144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3154 x19: .cfa -16 + ^
STACK CFI 316c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3198 68 .cfa: sp 0 + .ra: x30
STACK CFI 319c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3200 74 .cfa: sp 0 + .ra: x30
STACK CFI 3204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 320c x19: .cfa -16 + ^
STACK CFI 3238 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 323c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3258 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 325c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3278 f0 .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3284 x23: .cfa -16 + ^
STACK CFI 3290 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 32a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 32e0 x19: x19 x20: x20
STACK CFI 32e4 x21: x21 x22: x22
STACK CFI 32ec .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 32f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3310 x21: x21 x22: x22
STACK CFI 331c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 3320 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3340 x19: x19 x20: x20
STACK CFI 3344 x21: x21 x22: x22
STACK CFI 334c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 3350 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3368 50 .cfa: sp 0 + .ra: x30
STACK CFI 3390 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 33b8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 33d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33dc x19: .cfa -16 + ^
STACK CFI 3404 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3408 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 342c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3430 108 .cfa: sp 0 + .ra: x30
STACK CFI 3438 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3440 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 347c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3514 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3538 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3540 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3544 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 354c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3558 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3564 x23: .cfa -16 + ^
STACK CFI 35d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 35d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 35e8 88 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 360c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3618 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3644 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 366c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3678 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3688 88 .cfa: sp 0 + .ra: x30
STACK CFI 368c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 370c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3710 88 .cfa: sp 0 + .ra: x30
STACK CFI 3714 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3740 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3768 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 376c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3794 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3798 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37a0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 37a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 37ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 37e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3800 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3824 x21: x21 x22: x22
STACK CFI 3830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3834 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3838 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 39d8 x21: x21 x22: x22
STACK CFI 39dc x23: x23 x24: x24
STACK CFI 39e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 39f0 x21: x21 x22: x22
STACK CFI 39f4 x23: x23 x24: x24
STACK CFI 39f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 3a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a88 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a98 14c .cfa: sp 0 + .ra: x30
STACK CFI 3a9c .cfa: sp 1120 +
STACK CFI 3aa4 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 3aac x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 3abc x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 3b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3b14 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x29: .cfa -1120 + ^
STACK CFI 3b20 x25: .cfa -1056 + ^
STACK CFI 3b40 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 3b8c x21: x21 x22: x22
STACK CFI 3ba8 x25: x25
STACK CFI 3bac x25: .cfa -1056 + ^
STACK CFI 3bb4 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 3bd8 x21: x21 x22: x22 x25: x25
STACK CFI 3bdc x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 3be0 x25: .cfa -1056 + ^
STACK CFI INIT 3be8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bf8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c28 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c38 178 .cfa: sp 0 + .ra: x30
STACK CFI 3c3c .cfa: sp 368 +
STACK CFI 3c48 .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 3c54 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 3c60 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 3d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d64 .cfa: sp 368 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI INIT 3db0 138 .cfa: sp 0 + .ra: x30
STACK CFI 3db4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3eb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ebc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ee4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ee8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ef8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f18 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f30 88 .cfa: sp 0 + .ra: x30
STACK CFI 3f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fb8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3fbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4024 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 404c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4050 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4078 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4080 170 .cfa: sp 0 + .ra: x30
STACK CFI 4084 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4088 .cfa: x29 128 +
STACK CFI 408c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4094 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 40a4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 40c0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 41d4 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 41f0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 41f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4228 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4230 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 425c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4288 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42b8 13c .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 1120 +
STACK CFI 42c0 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 42c8 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 42d4 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 42f0 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 4324 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 4378 x19: x19 x20: x20
STACK CFI 43bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 43c0 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x29: .cfa -1120 + ^
STACK CFI 43c8 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 43ec x19: x19 x20: x20
STACK CFI 43f0 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI INIT 43f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4408 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4410 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4418 44 .cfa: sp 0 + .ra: x30
STACK CFI 4430 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4468 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4478 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4488 40 .cfa: sp 0 + .ra: x30
STACK CFI 449c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44c8 32c .cfa: sp 0 + .ra: x30
STACK CFI 44cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 467c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 46b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 46e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 47f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4810 60 .cfa: sp 0 + .ra: x30
STACK CFI 4814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 481c x19: .cfa -16 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4848 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 486c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4878 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4888 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4890 120 .cfa: sp 0 + .ra: x30
STACK CFI 4894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 489c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4954 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 49b0 648 .cfa: sp 0 + .ra: x30
STACK CFI 49b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 49d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4d5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4f94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ff8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5000 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5010 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 501c x21: .cfa -16 + ^
STACK CFI 5064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5068 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 50c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 50c8 708 .cfa: sp 0 + .ra: x30
STACK CFI 50cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 50d4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 50dc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 50ec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 513c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5140 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 53c4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 541c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5420 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 54cc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 54e8 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 55d4 x25: x25 x26: x26
STACK CFI 55d8 x27: x27 x28: x28
STACK CFI 55dc x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5668 x25: x25 x26: x26
STACK CFI 566c x27: x27 x28: x28
STACK CFI 5670 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 56a8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 56c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 56cc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 56e8 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5700 x25: x25 x26: x26
STACK CFI 5704 x27: x27 x28: x28
STACK CFI 570c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5710 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 57d0 20 .cfa: sp 0 + .ra: x30
STACK CFI 57d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 57ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 57f0 34 .cfa: sp 0 + .ra: x30
STACK CFI 57f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57fc x19: .cfa -16 + ^
STACK CFI 5820 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5828 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5838 d8 .cfa: sp 0 + .ra: x30
STACK CFI 583c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5844 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5904 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 5910 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5930 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5938 6c .cfa: sp 0 + .ra: x30
STACK CFI 593c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5944 x19: .cfa -16 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5994 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 59a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 59a8 6c .cfa: sp 0 + .ra: x30
STACK CFI 59ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59b4 x19: .cfa -16 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5a10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a18 78 .cfa: sp 0 + .ra: x30
STACK CFI 5a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a3c x21: .cfa -16 + ^
STACK CFI 5a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5a90 68 .cfa: sp 0 + .ra: x30
STACK CFI 5a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5aa4 x21: .cfa -16 + ^
STACK CFI 5acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ad0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5af8 80 .cfa: sp 0 + .ra: x30
STACK CFI 5afc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b0c x19: .cfa -16 + ^
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b78 cc .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5b8c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5b9c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5bb0 x23: .cfa -96 + ^
STACK CFI 5c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5c30 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5c48 108 .cfa: sp 0 + .ra: x30
STACK CFI 5c4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5c54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cfc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d50 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5db0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5df8 68 .cfa: sp 0 + .ra: x30
STACK CFI 5dfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e14 x21: .cfa -16 + ^
STACK CFI 5e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e60 38 .cfa: sp 0 + .ra: x30
STACK CFI 5e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e6c x19: .cfa -16 + ^
STACK CFI 5e94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e98 1640 .cfa: sp 0 + .ra: x30
STACK CFI 5e9c .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5ea4 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5ed0 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 5f28 x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 5f30 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 5f34 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 6fcc x23: x23 x24: x24
STACK CFI 6fd0 x25: x25 x26: x26
STACK CFI 6fd4 x27: x27 x28: x28
STACK CFI 7004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7008 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI 71b8 x23: x23 x24: x24
STACK CFI 71bc x25: x25 x26: x26
STACK CFI 71c0 x27: x27 x28: x28
STACK CFI 71d0 x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 74c8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 74cc x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 74d0 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 74d4 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 74d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 74e8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 74ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7564 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 75a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75c0 4 .cfa: sp 0 + .ra: x30
