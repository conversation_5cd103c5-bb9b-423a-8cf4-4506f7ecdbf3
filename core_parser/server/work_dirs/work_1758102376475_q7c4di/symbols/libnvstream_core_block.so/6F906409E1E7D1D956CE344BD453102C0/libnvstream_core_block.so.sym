MODULE Linux arm64 6F906409E1E7D1D956CE344BD453102C0 libnvstream_core_block.so
INFO CODE_ID 0964906FE7E1D9D156CE344BD453102C
PUBLIC 66b8 0 _init
PUBLIC 6bf0 0 call_weak_fn
PUBLIC 6c04 0 deregister_tm_clones
PUBLIC 6c34 0 register_tm_clones
PUBLIC 6c70 0 __do_global_dtors_aux
PUBLIC 6cc0 0 frame_dummy
PUBLIC 6cd0 0 linvs::block::C2cDst::C2cDst(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, linvs::block::IBlock const&)
PUBLIC 6de0 0 linvs::block::C2cDst::~C2cDst()
PUBLIC 6e00 0 linvs::block::C2cDst::~C2cDst()
PUBLIC 6e40 0 linvs::block::C2cSrc::C2cSrc(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, linvs::block::IBlock const&)
PUBLIC 6f50 0 linvs::block::C2cSrc::~C2cSrc()
PUBLIC 6f70 0 linvs::block::C2cSrc::~C2cSrc()
PUBLIC 6fb0 0 linvs::block::FifoQueue::FifoQueue()
PUBLIC 7040 0 linvs::block::StreamQueue::~StreamQueue()
PUBLIC 7060 0 linvs::block::StreamQueue::~StreamQueue()
PUBLIC 70a0 0 linvs::block::FifoQueue::~FifoQueue()
PUBLIC 70c0 0 linvs::block::FifoQueue::~FifoQueue()
PUBLIC 7100 0 linvs::block::IBlock::operator*()
PUBLIC 7110 0 linvs::block::IBlock::operator*() const
PUBLIC 7120 0 linvs::block::IBlock::operator bool()
PUBLIC 7130 0 linvs::block::IBlock::Reset()
PUBLIC 71a0 0 linvs::block::IBlock::~IBlock()
PUBLIC 71d0 0 linvs::block::IBlock::~IBlock()
PUBLIC 7200 0 linvs::block::IBlock::ConnectUp(linvs::block::IBlock const&)
PUBLIC 7260 0 linvs::block::IBlock::ConnectDown(linvs::block::IBlock const&)
PUBLIC 72b0 0 linvs::block::IBlock::Disconnect()
PUBLIC 7300 0 linvs::block::IBlock::QueryEvent(NvSciStreamEventType&, long)
PUBLIC 7360 0 linvs::block::IBlock::GetSetupStatus(NvSciError&)
PUBLIC 73b0 0 linvs::block::IBlock::SetSetupStatus(NvSciStreamSetup, bool)
PUBLIC 7400 0 linvs::block::IBlock::QueryConsumerCount(unsigned int&)
PUBLIC 7450 0 linvs::block::IBlock::QueryUserInfo(NvSciStreamBlockType, unsigned int, unsigned int, unsigned int&, void*)
PUBLIC 74a0 0 linvs::block::IBlock::SetElementAttr(unsigned int, linvs::buf::BufAttrList const&)
PUBLIC 7520 0 linvs::block::IBlock::GetElementCount(NvSciStreamBlockType, unsigned int&)
PUBLIC 7570 0 linvs::block::IBlock::GetElementAttr(NvSciStreamBlockType, unsigned int, unsigned int&, linvs::buf::BufAttrList&)
PUBLIC 7600 0 linvs::block::IBlock::SetElementWaiterAttr(unsigned int, linvs::sync::SyncAttrList const&)
PUBLIC 7680 0 linvs::block::IBlock::GetPacketNewHandle(unsigned long&)
PUBLIC 76d0 0 linvs::block::IBlock::SetPacketStatus(unsigned long, unsigned long, NvSciError)
PUBLIC 7720 0 linvs::block::IBlock::GetPacketBuf(unsigned long, unsigned int, linvs::buf::BufObj&)
PUBLIC 77a0 0 linvs::block::IBlock::GetPacketOldCookie(unsigned long&)
PUBLIC 77f0 0 linvs::block::IBlock::GetElementWaiterAttr(unsigned int, linvs::sync::SyncAttrList&)
PUBLIC 7860 0 linvs::block::IBlock::SetElementSignalObj(unsigned int, linvs::sync::SyncObj const&)
PUBLIC 78e0 0 linvs::block::IBlock::GetElementSignalObj(unsigned int, unsigned int, linvs::sync::SyncObj&)
PUBLIC 7960 0 linvs::block::IBlock::GetProducerPacket(unsigned long&)
PUBLIC 79b0 0 linvs::block::IBlock::GetPacketFence(unsigned long, unsigned int, unsigned int, linvs::sync::SyncFence&)
PUBLIC 7a40 0 linvs::block::IBlock::SetPacketFence(unsigned long, unsigned int, linvs::sync::SyncFence const&)
PUBLIC 7ac0 0 linvs::block::IBlock::PresentProducerPacket(unsigned long)
PUBLIC 7b10 0 linvs::block::IBlock::SetElementUsage(unsigned int, bool)
PUBLIC 7b60 0 linvs::block::IBlock::AcquireConsumerPacket(unsigned long&)
PUBLIC 7bb0 0 linvs::block::IBlock::ReleaseConsumerPacket(unsigned long)
PUBLIC 7c00 0 linvs::block::IBlock::CreatePacket(unsigned long, unsigned long&)
PUBLIC 7c50 0 linvs::block::IBlock::InsertBuffer(unsigned long, unsigned int, linvs::buf::BufObj&)
PUBLIC 7ce0 0 linvs::block::IBlock::CompletePacket(unsigned long)
PUBLIC 7d30 0 linvs::block::IBlock::GetAcceptPacketStatus(unsigned long, bool&)
PUBLIC 7d80 0 linvs::block::IBlock::GetPacketStatusValue(unsigned long, NvSciStreamBlockType, unsigned int, NvSciError&)
PUBLIC 7dd0 0 linvs::block::Ipc::~Ipc()
PUBLIC 7e10 0 linvs::block::Ipc::~Ipc()
PUBLIC 7e40 0 linvs::block::Ipc::Ipc(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 7ea0 0 linvs::block::Ipc::operator bool()
PUBLIC 7eb0 0 linvs::block::IpcDst::IpcDst(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 7fa0 0 linvs::block::IpcDst::~IpcDst()
PUBLIC 7fc0 0 linvs::block::IpcDst::~IpcDst()
PUBLIC 8000 0 linvs::block::IpcSrc::IpcSrc(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 80f0 0 linvs::block::IpcSrc::~IpcSrc()
PUBLIC 8110 0 linvs::block::IpcSrc::~IpcSrc()
PUBLIC 8150 0 linvs::block::BlockLimit::BlockLimit(int)
PUBLIC 81e0 0 linvs::block::BlockLimit::~BlockLimit()
PUBLIC 8200 0 linvs::block::BlockLimit::~BlockLimit()
PUBLIC 8240 0 linvs::block::MailboxQueue::MailboxQueue()
PUBLIC 82d0 0 linvs::block::MailboxQueue::~MailboxQueue()
PUBLIC 82f0 0 linvs::block::MailboxQueue::~MailboxQueue()
PUBLIC 8330 0 linvs::block::BlockMulticast::BlockMulticast(unsigned int)
PUBLIC 83c0 0 linvs::block::BlockMulticast::~BlockMulticast()
PUBLIC 83e0 0 linvs::block::BlockMulticast::~BlockMulticast()
PUBLIC 8420 0 linvs::block::PacketPool::~PacketPool()
PUBLIC 8440 0 linvs::block::PacketPool::~PacketPool()
PUBLIC 8470 0 linvs::block::PacketPool::Init(unsigned int)
PUBLIC 84d0 0 linvs::block::PacketPool::PacketPool(unsigned int)
PUBLIC 8510 0 linvs::block::PacketPool::CreateProducer(bool)
PUBLIC 8690 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<linvs::block::IBlock>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 86a0 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<linvs::block::IBlock>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 86c0 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<linvs::block::IBlock>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 86d0 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<linvs::block::IBlock>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8730 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<linvs::block::IBlock>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8740 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 8800 0 linvs::block::PresentSync::PresentSync(linvs::sync::SyncModule const&)
PUBLIC 88a0 0 linvs::block::PresentSync::~PresentSync()
PUBLIC 88c0 0 linvs::block::PresentSync::~PresentSync()
PUBLIC 8900 0 linvs::block::ReturnSync::ReturnSync(linvs::sync::SyncModule const&)
PUBLIC 89a0 0 linvs::block::ReturnSync::~ReturnSync()
PUBLIC 89c0 0 linvs::block::ReturnSync::~ReturnSync()
PUBLIC 8a00 0 linvs::block::StreamQueue::CreateConsumer(bool)
PUBLIC 8b74 0 _fini
STACK CFI INIT 6c04 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c34 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c70 50 .cfa: sp 0 + .ra: x30
STACK CFI 6c80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c88 x19: .cfa -16 + ^
STACK CFI 6cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6de0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e00 38 .cfa: sp 0 + .ra: x30
STACK CFI 6e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e14 x19: .cfa -16 + ^
STACK CFI 6e34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cd0 10c .cfa: sp 0 + .ra: x30
STACK CFI 6cd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ce8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6cf8 x23: .cfa -16 + ^
STACK CFI 6d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6f50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f70 38 .cfa: sp 0 + .ra: x30
STACK CFI 6f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f84 x19: .cfa -16 + ^
STACK CFI 6fa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e40 10c .cfa: sp 0 + .ra: x30
STACK CFI 6e44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e68 x23: .cfa -16 + ^
STACK CFI 6ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7040 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7060 38 .cfa: sp 0 + .ra: x30
STACK CFI 7064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7074 x19: .cfa -16 + ^
STACK CFI 7094 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 70a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 70c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70d4 x19: .cfa -16 + ^
STACK CFI 70f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6fb0 88 .cfa: sp 0 + .ra: x30
STACK CFI 6fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fc4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7014 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7100 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7120 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7130 68 .cfa: sp 0 + .ra: x30
STACK CFI 7134 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 713c x19: .cfa -16 + ^
STACK CFI 7150 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7154 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 716c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 71a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 71c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 71d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 71d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71dc x19: .cfa -16 + ^
STACK CFI 71f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7200 54 .cfa: sp 0 + .ra: x30
STACK CFI 7208 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7260 50 .cfa: sp 0 + .ra: x30
STACK CFI 7264 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7280 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7284 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 72b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 72b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7300 58 .cfa: sp 0 + .ra: x30
STACK CFI 7304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7328 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 732c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7360 4c .cfa: sp 0 + .ra: x30
STACK CFI 7364 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 737c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7380 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 73b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 73b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 73d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7400 4c .cfa: sp 0 + .ra: x30
STACK CFI 7404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 741c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7420 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7450 4c .cfa: sp 0 + .ra: x30
STACK CFI 7454 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 746c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7470 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 74a0 74 .cfa: sp 0 + .ra: x30
STACK CFI 74a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7520 4c .cfa: sp 0 + .ra: x30
STACK CFI 7524 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 753c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7540 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7570 84 .cfa: sp 0 + .ra: x30
STACK CFI 7574 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 757c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 758c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 75c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 75c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7600 74 .cfa: sp 0 + .ra: x30
STACK CFI 7604 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7614 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7648 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7680 4c .cfa: sp 0 + .ra: x30
STACK CFI 7684 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 769c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 76d0 4c .cfa: sp 0 + .ra: x30
STACK CFI 76d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7720 7c .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 772c x21: .cfa -16 + ^
STACK CFI 7738 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 776c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7770 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 77a0 4c .cfa: sp 0 + .ra: x30
STACK CFI 77a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 77c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 77f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 77f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7804 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7834 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7860 74 .cfa: sp 0 + .ra: x30
STACK CFI 7864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7874 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 78e0 7c .cfa: sp 0 + .ra: x30
STACK CFI 78e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78ec x21: .cfa -16 + ^
STACK CFI 78f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 792c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7930 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7960 4c .cfa: sp 0 + .ra: x30
STACK CFI 7964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 797c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7980 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 79b0 84 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 79cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7a40 7c .cfa: sp 0 + .ra: x30
STACK CFI 7a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a4c x21: .cfa -16 + ^
STACK CFI 7a58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ac0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7adc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ae0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7b10 4c .cfa: sp 0 + .ra: x30
STACK CFI 7b14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7b60 4c .cfa: sp 0 + .ra: x30
STACK CFI 7b64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7bb0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7bcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7bd0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7c00 4c .cfa: sp 0 + .ra: x30
STACK CFI 7c04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7c20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7c50 84 .cfa: sp 0 + .ra: x30
STACK CFI 7c54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c70 x21: .cfa -16 + ^
STACK CFI 7ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ce0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7ce4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7cfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d30 4c .cfa: sp 0 + .ra: x30
STACK CFI 7d34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d80 4c .cfa: sp 0 + .ra: x30
STACK CFI 7d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7da0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7dd0 40 .cfa: sp 0 + .ra: x30
STACK CFI 7dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7de4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7e10 28 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e1c x19: .cfa -16 + ^
STACK CFI 7e34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e40 58 .cfa: sp 0 + .ra: x30
STACK CFI 7e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ea0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7fa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7fc0 38 .cfa: sp 0 + .ra: x30
STACK CFI 7fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fd4 x19: .cfa -16 + ^
STACK CFI 7ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7eb0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7eb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ebc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ec8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 80f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8110 38 .cfa: sp 0 + .ra: x30
STACK CFI 8114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8124 x19: .cfa -16 + ^
STACK CFI 8144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8000 e8 .cfa: sp 0 + .ra: x30
STACK CFI 8004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 800c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8018 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8094 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 80cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 81e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8200 38 .cfa: sp 0 + .ra: x30
STACK CFI 8204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8214 x19: .cfa -16 + ^
STACK CFI 8234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8150 88 .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8164 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8194 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 81a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 81c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 82d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 82f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 82f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8304 x19: .cfa -16 + ^
STACK CFI 8324 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8240 88 .cfa: sp 0 + .ra: x30
STACK CFI 8244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8254 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 83c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 83e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 83e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83f4 x19: .cfa -16 + ^
STACK CFI 8414 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8330 88 .cfa: sp 0 + .ra: x30
STACK CFI 8334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8374 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 83a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8420 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8440 28 .cfa: sp 0 + .ra: x30
STACK CFI 8444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 844c x19: .cfa -16 + ^
STACK CFI 8464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 86d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 872c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8730 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8470 60 .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 847c x19: .cfa -16 + ^
STACK CFI 84a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 84a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 84d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 84f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 84f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 84fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8740 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 874c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8784 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8510 174 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 851c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8524 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 852c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 85b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 85b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 88a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 88c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88d4 x19: .cfa -16 + ^
STACK CFI 88f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8800 98 .cfa: sp 0 + .ra: x30
STACK CFI 8804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8854 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8880 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 89a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 89d4 x19: .cfa -16 + ^
STACK CFI 89f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8900 98 .cfa: sp 0 + .ra: x30
STACK CFI 8904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8914 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8954 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8964 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8980 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8a00 174 .cfa: sp 0 + .ra: x30
STACK CFI 8a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8a0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8a14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8a1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
