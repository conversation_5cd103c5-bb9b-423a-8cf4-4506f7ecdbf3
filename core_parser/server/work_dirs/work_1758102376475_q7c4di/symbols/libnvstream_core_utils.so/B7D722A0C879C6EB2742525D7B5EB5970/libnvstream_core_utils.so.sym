MODULE Linux arm64 B7D722A0C879C6EB2742525D7B5EB5970 libnvstream_core_utils.so
INFO CODE_ID A022D7B779C8EBC62742525D7B5EB597
PUBLIC 41c0 0 _init
PUBLIC 4590 0 call_weak_fn
PUBLIC 45a4 0 deregister_tm_clones
PUBLIC 45d4 0 register_tm_clones
PUBLIC 4610 0 __do_global_dtors_aux
PUBLIC 4660 0 frame_dummy
PUBLIC 4670 0 linvs::utils::CallbackWatchDogImpl::Start()
PUBLIC 4690 0 std::_Function_base::_Base_manager<linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}> const&, std::_Manager_operation)
PUBLIC 46d0 0 linvs::utils::CallbackWatchDogImpl::BeforeFeed()
PUBLIC 4720 0 linvs::utils::CallbackWatchDogImpl::Stop()
PUBLIC 4760 0 linvs::utils::CallbackWatchDogImpl::~CallbackWatchDogImpl()
PUBLIC 47e0 0 linvs::utils::CallbackWatchDogImpl::~CallbackWatchDogImpl()
PUBLIC 4810 0 linvs::utils::CallbackWatchDogImpl::AfterFeed()
PUBLIC 48b0 0 linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)
PUBLIC 4a80 0 linvs::utils::CallbackWatchDogImpl::OnTimer()
PUBLIC 4b50 0 std::_Function_handler<void (), linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}>::_M_invoke(std::_Any_data const&)
PUBLIC 4b60 0 linvs::utils::CallbackWatchDog::CreateUnique(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)
PUBLIC 4be0 0 linvs::utils::FrameAlignImpl::flush_frames(std::function<void (int, unsigned long, long)>&&)
PUBLIC 4c70 0 std::_Deque_base<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> >::_M_initialize_map(unsigned long) [clone .constprop.0]
PUBLIC 4d20 0 linvs::utils::FrameAlignImpl::push_frame_into_queue(int, unsigned long, long)
PUBLIC 4f90 0 linvs::utils::FrameAlignImpl::FrameAlignImpl(long, int, int, int, std::unordered_set<int, std::hash<int>, std::equal_to<int>, std::allocator<int> > const&)
PUBLIC 5180 0 linvs::utils::FrameAlign::create_unique_instance(long, int, int, int, std::unordered_set<int, std::hash<int>, std::equal_to<int>, std::allocator<int> > const&)
PUBLIC 5210 0 linvs::utils::FrameAlignImpl::get_pacesetter_frame_info()
PUBLIC 5310 0 linvs::utils::FrameAlignImpl::pop_pacesetter_queue()
PUBLIC 5390 0 linvs::utils::FrameAlignImpl::align_frames(int, unsigned long, long)
PUBLIC 5730 0 linvs::utils::FrameAlignImpl::~FrameAlignImpl()
PUBLIC 5810 0 linvs::utils::FrameAlignImpl::~FrameAlignImpl()
PUBLIC 58f0 0 std::_Hashtable<int, std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > >, std::allocator<std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > > >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::~_Hashtable()
PUBLIC 59a0 0 std::_Deque_base<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> >::~_Deque_base()
PUBLIC 5a00 0 std::_Hashtable<int, std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > >, std::allocator<std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > > >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 5b30 0 std::__detail::_Map_base<int, std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > >, std::allocator<std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > > >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&)
PUBLIC 5cf0 0 std::_Hashtable<int, std::pair<int const, unsigned long>, std::allocator<std::pair<int const, unsigned long> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 5e20 0 std::__detail::_Map_base<int, std::pair<int const, unsigned long>, std::allocator<std::pair<int const, unsigned long> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&)
PUBLIC 5fa0 0 linvs::utils::FrameSelectorImpl::expect_frame_id() const
PUBLIC 5fb0 0 linvs::utils::FrameSelectorImpl::fetch_expect_frame_id()
PUBLIC 5fe0 0 linvs::utils::FrameSelectorImpl::expect_frame(unsigned long)
PUBLIC 60b0 0 linvs::utils::FrameSelectorImpl::FrameSelectorImpl(double, double)
PUBLIC 60e0 0 linvs::utils::FrameSelector::create_unique_instance(double, double)
PUBLIC 6150 0 linvs::utils::FrameSelectorImpl::~FrameSelectorImpl()
PUBLIC 6160 0 linvs::utils::FrameSelectorImpl::~FrameSelectorImpl()
PUBLIC 6170 0 linvs::utils::FrequencyCal::CreateUnique(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)
PUBLIC 6360 0 std::_Function_base::_Base_manager<linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}> const&, std::_Manager_operation)
PUBLIC 63a0 0 linvs::utils::FrequencyCalImpl::FetchAdd(unsigned long)
PUBLIC 63c0 0 linvs::utils::FrequencyCalImpl::~FrequencyCalImpl()
PUBLIC 6420 0 linvs::utils::FrequencyCalImpl::CurrentFrequency()
PUBLIC 6490 0 linvs::utils::FrequencyCalImpl::Reset()
PUBLIC 64e0 0 std::_Function_handler<void (), linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}>::_M_invoke(std::_Any_data const&)
PUBLIC 6590 0 linvs::utils::FrequencyCalImpl::~FrequencyCalImpl()
PUBLIC 65f0 0 linvs::utils::GetSocId()
PUBLIC 6a14 0 _fini
STACK CFI INIT 45a4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45d4 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4610 50 .cfa: sp 0 + .ra: x30
STACK CFI 4620 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4628 x19: .cfa -16 + ^
STACK CFI 4658 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4660 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4670 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4690 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 46d0 4c .cfa: sp 0 + .ra: x30
STACK CFI 46d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46dc x19: .cfa -16 + ^
STACK CFI 4718 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4720 3c .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 472c x19: .cfa -16 + ^
STACK CFI 4758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4760 7c .cfa: sp 0 + .ra: x30
STACK CFI 4764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4774 x19: .cfa -16 + ^
STACK CFI 47cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 47d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47e0 28 .cfa: sp 0 + .ra: x30
STACK CFI 47e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47ec x19: .cfa -16 + ^
STACK CFI 4804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4810 98 .cfa: sp 0 + .ra: x30
STACK CFI 4814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 481c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4888 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48b0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 48b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 48c4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 48cc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 48e0 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4a24 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4a80 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a94 x21: .cfa -16 + ^
STACK CFI 4aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4af0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4b50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b60 7c .cfa: sp 0 + .ra: x30
STACK CFI 4b64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b88 x23: .cfa -16 + ^
STACK CFI 4bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4bc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4be0 84 .cfa: sp 0 + .ra: x30
STACK CFI 4be4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4bec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4bfc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c08 x23: .cfa -48 + ^
STACK CFI 4c50 x21: x21 x22: x22
STACK CFI 4c54 x23: x23
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c60 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4c70 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4cdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5730 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5734 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5744 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 575c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5760 x23: .cfa -16 + ^
STACK CFI 57b0 x21: x21 x22: x22
STACK CFI 57b4 x23: x23
STACK CFI 5800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5810 d8 .cfa: sp 0 + .ra: x30
STACK CFI 5814 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5824 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 583c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5840 x23: .cfa -16 + ^
STACK CFI 5890 x21: x21 x22: x22
STACK CFI 5894 x23: x23
STACK CFI 58d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4d20 264 .cfa: sp 0 + .ra: x30
STACK CFI 4d3c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4d44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d4c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d64 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4dd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4df4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e88 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 58f0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 58f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 58fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 590c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5910 x23: .cfa -16 + ^
STACK CFI 5960 x21: x21 x22: x22
STACK CFI 5964 x23: x23
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5994 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 599c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59a0 60 .cfa: sp 0 + .ra: x30
STACK CFI 59a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59ac x21: .cfa -16 + ^
STACK CFI 59bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 59e8 x19: x19 x20: x20
STACK CFI 59f0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 59f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 59fc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 5a00 124 .cfa: sp 0 + .ra: x30
STACK CFI 5a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5abc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b30 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5b34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5b40 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5b50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5bbc x23: .cfa -32 + ^
STACK CFI 5c58 x23: x23
STACK CFI 5c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4f90 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 4f94 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4fa8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4fb4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 501c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 5024 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 5028 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 512c x23: x23 x24: x24
STACK CFI 5130 x25: x25 x26: x26
STACK CFI 5134 x27: x27 x28: x28
STACK CFI 5140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5144 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 5180 84 .cfa: sp 0 + .ra: x30
STACK CFI 5184 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 518c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 519c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51a8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 51ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5210 fc .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5220 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 522c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5278 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 52d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5310 74 .cfa: sp 0 + .ra: x30
STACK CFI 5314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5324 x19: .cfa -16 + ^
STACK CFI 5350 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5354 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5cf0 124 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e20 178 .cfa: sp 0 + .ra: x30
STACK CFI 5e24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5e30 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5e40 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5e54 x23: .cfa -32 + ^
STACK CFI 5ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5ea8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 5f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5f48 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5390 394 .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 53a8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 53b0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 53c0 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 53fc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 55f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 55fc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5fa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6150 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5fb0 30 .cfa: sp 0 + .ra: x30
STACK CFI 5fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fc0 x19: .cfa -16 + ^
STACK CFI 5fdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5fe0 cc .cfa: sp 0 + .ra: x30
STACK CFI 5fe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ff0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ff8 x21: .cfa -16 + ^
STACK CFI 6024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6028 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6074 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6160 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 60b0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 60e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 60e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60f0 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 60fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6128 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 612c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6360 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63a0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63d4 x19: .cfa -16 + ^
STACK CFI 640c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6410 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6418 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6420 68 .cfa: sp 0 + .ra: x30
STACK CFI 6424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 642c x19: .cfa -16 + ^
STACK CFI 6480 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6490 48 .cfa: sp 0 + .ra: x30
STACK CFI 6494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 649c x19: .cfa -16 + ^
STACK CFI 64d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 64e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6504 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 658c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6590 5c .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65a4 x19: .cfa -16 + ^
STACK CFI 65e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6170 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 6174 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 617c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6190 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 623c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 62fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6300 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT 65f0 424 .cfa: sp 0 + .ra: x30
STACK CFI 65f4 .cfa: sp 656 +
STACK CFI 6600 .ra: .cfa -648 + ^ x29: .cfa -656 + ^
STACK CFI 6608 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 6614 x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 66c0 x25: .cfa -592 + ^
STACK CFI 66f8 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 684c x23: x23 x24: x24
STACK CFI 6874 x25: x25
STACK CFI 6878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 687c .cfa: sp 656 + .ra: .cfa -648 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x29: .cfa -656 + ^
STACK CFI 6888 x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^
STACK CFI 68a8 x23: x23 x24: x24 x25: x25
STACK CFI 68dc x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^
STACK CFI 6910 x23: x23 x24: x24
STACK CFI 692c x25: x25
STACK CFI 6954 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 6958 x25: .cfa -592 + ^
STACK CFI 695c x23: x23 x24: x24 x25: x25
STACK CFI 6974 x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^
STACK CFI 69d0 x23: x23 x24: x24 x25: x25
STACK CFI 69d4 x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^
STACK CFI 69e8 x23: x23 x24: x24 x25: x25
STACK CFI 69ec x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 69f0 x25: .cfa -592 + ^
STACK CFI 6a04 x23: x23 x24: x24 x25: x25
STACK CFI 6a0c x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 6a10 x25: .cfa -592 + ^
