MODULE Linux arm64 63C74D043F9734C8E243E661814502850 libseccomp.so.2
INFO CODE_ID 044DC763973FC834E243E66181450285A3E3D988
PUBLIC 1b60 0 seccomp_version
PUBLIC 1b70 0 seccomp_api_get
PUBLIC 1b88 0 seccomp_api_set
PUBLIC 1dc8 0 seccomp_init
PUBLIC 1e18 0 seccomp_reset
PUBLIC 1e88 0 seccomp_release
PUBLIC 1e90 0 seccomp_merge
PUBLIC 1f20 0 seccomp_arch_resolve_name
PUBLIC 1f58 0 seccomp_arch_native
PUBLIC 1f70 0 seccomp_arch_exist
PUBLIC 1fd0 0 seccomp_arch_add
PUBLIC 2070 0 seccomp_arch_remove
PUBLIC 20f0 0 seccomp_load
PUBLIC 2150 0 seccomp_attr_get
PUBLIC 21b8 0 seccomp_attr_set
PUBLIC 2220 0 seccomp_syscall_resolve_num_arch
PUBLIC 2288 0 seccomp_syscall_resolve_name_arch
PUBLIC 22f8 0 seccomp_syscall_resolve_name_rewrite
PUBLIC 23c0 0 seccomp_syscall_resolve_name
PUBLIC 23d0 0 seccomp_syscall_priority
PUBLIC 2450 0 seccomp_rule_add_array
PUBLIC 2558 0 seccomp_rule_add
PUBLIC 2658 0 seccomp_rule_add_exact_array
PUBLIC 2770 0 seccomp_rule_add_exact
PUBLIC 2870 0 seccomp_notify_alloc
PUBLIC 28c0 0 seccomp_notify_free
PUBLIC 28f8 0 seccomp_notify_receive
PUBLIC 2918 0 seccomp_notify_respond
PUBLIC 2938 0 seccomp_notify_id_valid
PUBLIC 2988 0 seccomp_notify_fd
PUBLIC 29e0 0 seccomp_export_pfc
PUBLIC 2a50 0 seccomp_export_bpf
STACK CFI INIT 1988 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 19fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a04 x19: .cfa -16 + ^
STACK CFI 1a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a48 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a98 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1abc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1b60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b88 240 .cfa: sp 0 + .ra: x30
STACK CFI 1b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b98 x19: .cfa -16 + ^
STACK CFI 1cac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1cf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cfc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1dc8 50 .cfa: sp 0 + .ra: x30
STACK CFI 1dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dd8 x19: .cfa -16 + ^
STACK CFI 1e04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e18 70 .cfa: sp 0 + .ra: x30
STACK CFI 1e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e90 8c .cfa: sp 0 + .ra: x30
STACK CFI 1e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ec0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1f20 34 .cfa: sp 0 + .ra: x30
STACK CFI 1f28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f58 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f70 60 .cfa: sp 0 + .ra: x30
STACK CFI 1f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1fd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1fd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1fdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fe4 x21: .cfa -16 + ^
STACK CFI 2030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2034 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2058 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 206c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2070 80 .cfa: sp 0 + .ra: x30
STACK CFI 2074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 207c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 20ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20f0 5c .cfa: sp 0 + .ra: x30
STACK CFI 20f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20fc x19: .cfa -16 + ^
STACK CFI 2118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 211c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2148 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2150 64 .cfa: sp 0 + .ra: x30
STACK CFI 2154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 215c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2168 x21: .cfa -16 + ^
STACK CFI 2188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 218c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 21b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 21b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 21bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21d0 x21: .cfa -16 + ^
STACK CFI 21f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 21f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2220 68 .cfa: sp 0 + .ra: x30
STACK CFI 2224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 222c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2288 70 .cfa: sp 0 + .ra: x30
STACK CFI 2290 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2298 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 22c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 22ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 22f8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2304 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2314 x21: .cfa -32 + ^
STACK CFI 239c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23d0 80 .cfa: sp 0 + .ra: x30
STACK CFI 23d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23e8 x21: .cfa -16 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2438 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 244c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2450 104 .cfa: sp 0 + .ra: x30
STACK CFI 245c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 246c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 248c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2490 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 249c x23: .cfa -16 + ^
STACK CFI 2504 x21: x21 x22: x22
STACK CFI 2508 x23: x23
STACK CFI 2514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2524 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 252c x21: x21 x22: x22
STACK CFI 2530 x23: x23
STACK CFI 2534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2538 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 253c x21: x21 x22: x22
STACK CFI 2540 x23: x23
STACK CFI 2544 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 254c x21: x21 x22: x22
STACK CFI 2550 x23: x23
STACK CFI INIT 2558 100 .cfa: sp 0 + .ra: x30
STACK CFI 255c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 2568 x19: .cfa -240 + ^
STACK CFI 25b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25b8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2658 118 .cfa: sp 0 + .ra: x30
STACK CFI 2664 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2674 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 267c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 269c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 26a8 x23: .cfa -16 + ^
STACK CFI 2700 x23: x23
STACK CFI 2704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2710 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 271c x23: x23
STACK CFI 2720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2728 x23: x23
STACK CFI 272c x23: .cfa -16 + ^
STACK CFI 2750 x23: x23
STACK CFI 2760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2764 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 276c x23: x23
STACK CFI INIT 2770 100 .cfa: sp 0 + .ra: x30
STACK CFI 2774 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 2780 x19: .cfa -240 + ^
STACK CFI 27cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 27d0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2870 4c .cfa: sp 0 + .ra: x30
STACK CFI 2874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2880 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 28ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 28b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 28c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 28c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28cc x19: .cfa -16 + ^
STACK CFI 28e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 28ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 28f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 28f8 20 .cfa: sp 0 + .ra: x30
STACK CFI 28fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 290c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2910 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2918 20 .cfa: sp 0 + .ra: x30
STACK CFI 291c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 292c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2930 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2938 4c .cfa: sp 0 + .ra: x30
STACK CFI 293c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2948 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2978 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2988 58 .cfa: sp 0 + .ra: x30
STACK CFI 298c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2998 x19: .cfa -16 + ^
STACK CFI 29c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2a50 104 .cfa: sp 0 + .ra: x30
STACK CFI 2a54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2a5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2a68 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2a80 x23: .cfa -32 + ^
STACK CFI 2ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2abc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2b50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2b58 8c .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b8c x19: .cfa -16 + ^
STACK CFI 2ba0 x19: x19
STACK CFI 2bb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2bdc x19: x19
STACK CFI INIT 2be8 6c .cfa: sp 0 + .ra: x30
STACK CFI 2bec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bf4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2c58 50 .cfa: sp 0 + .ra: x30
STACK CFI 2c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c68 x19: .cfa -16 + ^
STACK CFI 2ca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ca8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cc0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cd0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 2cd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2ce4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d88 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2d8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2da0 x21: x21 x22: x22
STACK CFI 2da4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2dcc x21: x21 x22: x22
STACK CFI 2dd4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e0c x21: x21 x22: x22
STACK CFI 2e14 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e48 x21: x21 x22: x22
STACK CFI 2e4c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e70 x21: x21 x22: x22
STACK CFI 2e74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e98 x21: x21 x22: x22
STACK CFI 2e9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2ebc x21: x21 x22: x22
STACK CFI 2ec0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 2ec8 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f18 114 .cfa: sp 0 + .ra: x30
STACK CFI 2f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f40 x19: .cfa -16 + ^
STACK CFI 2f54 x19: x19
STACK CFI 2f5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f74 x19: .cfa -16 + ^
STACK CFI 2f94 x19: x19
STACK CFI 2f9c x19: .cfa -16 + ^
STACK CFI 2fbc x19: x19
STACK CFI 2fc4 x19: .cfa -16 + ^
STACK CFI 2fe4 x19: x19
STACK CFI 2fec x19: .cfa -16 + ^
STACK CFI 300c x19: x19
STACK CFI 3010 x19: .cfa -16 + ^
STACK CFI 3020 x19: x19
STACK CFI INIT 3030 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30a0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 30a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 30ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 30c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 30f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 30fc x25: .cfa -32 + ^
STACK CFI 31c0 x23: x23 x24: x24
STACK CFI 31c4 x25: x25
STACK CFI 31f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 3228 x23: x23 x24: x24 x25: x25
STACK CFI 3270 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 3298 x23: x23 x24: x24
STACK CFI 329c x25: x25
STACK CFI 32a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 32ec x23: x23 x24: x24
STACK CFI 32f0 x25: x25
STACK CFI 32f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 3318 x23: x23 x24: x24
STACK CFI 331c x25: x25
STACK CFI 3324 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 3330 x23: x23 x24: x24
STACK CFI 3334 x25: x25
STACK CFI 333c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3340 x25: .cfa -32 + ^
STACK CFI INIT 3348 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3358 ec .cfa: sp 0 + .ra: x30
STACK CFI 335c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3368 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3380 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 33c8 x21: x21 x22: x22
STACK CFI 33d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 33e8 x21: x21 x22: x22
STACK CFI 33ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 342c x21: x21 x22: x22
STACK CFI 3438 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3440 x21: x21 x22: x22
STACK CFI INIT 3448 44 .cfa: sp 0 + .ra: x30
STACK CFI 3460 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3480 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3490 44 .cfa: sp 0 + .ra: x30
STACK CFI 34a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 34dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3514 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3520 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3530 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3638 38c .cfa: sp 0 + .ra: x30
STACK CFI 363c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3644 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 364c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3660 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3784 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 39c8 948 .cfa: sp 0 + .ra: x30
STACK CFI 39cc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 39d4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 39f0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3a60 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3a64 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3a68 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3c58 x21: x21 x22: x22
STACK CFI 3c5c x23: x23 x24: x24
STACK CFI 3c60 x25: x25 x26: x26
STACK CFI 3ce4 x19: x19 x20: x20
STACK CFI 3cec .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 3cf0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 4244 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4258 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 425c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 428c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 42a0 x19: x19 x20: x20
STACK CFI 42ac .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 42b0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4310 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43b8 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4460 2c .cfa: sp 0 + .ra: x30
STACK CFI 4464 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4488 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4490 280 .cfa: sp 0 + .ra: x30
STACK CFI 4494 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 449c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 44a8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 44b0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 44fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4500 .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 450c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4524 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 45fc x19: x19 x20: x20
STACK CFI 4600 x25: x25 x26: x26
STACK CFI 4604 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4670 x19: x19 x20: x20
STACK CFI 4674 x25: x25 x26: x26
STACK CFI 4678 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4684 x19: x19 x20: x20
STACK CFI 4688 x25: x25 x26: x26
STACK CFI 468c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 46ac x19: x19 x20: x20
STACK CFI 46b0 x25: x25 x26: x26
STACK CFI 46b4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 46dc x19: x19 x20: x20
STACK CFI 46e4 x25: x25 x26: x26
STACK CFI 46f8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4700 x25: x25 x26: x26
STACK CFI 4708 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 470c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 4710 70 .cfa: sp 0 + .ra: x30
STACK CFI 4720 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4730 x19: .cfa -16 + ^
STACK CFI 476c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4770 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4780 34 .cfa: sp 0 + .ra: x30
STACK CFI 4788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4790 x19: .cfa -16 + ^
STACK CFI 47ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 47bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47c4 x19: .cfa -16 + ^
STACK CFI 4804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4808 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4828 11c .cfa: sp 0 + .ra: x30
STACK CFI 482c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4834 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4840 x23: .cfa -16 + ^
STACK CFI 4854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 490c x21: x21 x22: x22
STACK CFI 491c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 4920 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4948 7c .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4954 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 497c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 49ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49c8 9c .cfa: sp 0 + .ra: x30
STACK CFI 49cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 49d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49e0 x21: .cfa -16 + ^
STACK CFI 4a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4a68 7c .cfa: sp 0 + .ra: x30
STACK CFI 4a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a88 x21: .cfa -16 + ^
STACK CFI 4acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ad0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4ae8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b20 x19: .cfa -80 + ^
STACK CFI 4b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4b90 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 4b94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4b9c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4bc4 x21: .cfa -80 + ^
STACK CFI 4c60 x21: x21
STACK CFI 4c64 x21: .cfa -80 + ^
STACK CFI 4d3c x21: x21
STACK CFI 4d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d64 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI 4e18 x21: x21
STACK CFI 4e28 x21: .cfa -80 + ^
STACK CFI 4e64 x21: x21
STACK CFI 4e6c x21: .cfa -80 + ^
STACK CFI INIT 4e70 88 .cfa: sp 0 + .ra: x30
STACK CFI 4e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4edc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ef8 534 .cfa: sp 0 + .ra: x30
STACK CFI 4efc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4f0c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4f28 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f38 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4f40 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 51cc x25: x25 x26: x26
STACK CFI 51e4 x21: x21 x22: x22
STACK CFI 5214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5218 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 521c x21: x21 x22: x22
STACK CFI 5220 x25: x25 x26: x26
STACK CFI 5224 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 52e8 x25: x25 x26: x26
STACK CFI 52ec x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 5380 x25: x25 x26: x26
STACK CFI 5384 x21: x21 x22: x22
STACK CFI 539c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5408 x21: x21 x22: x22
STACK CFI 5418 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 541c x21: x21 x22: x22
STACK CFI 5424 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5428 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 5430 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 5434 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5440 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 545c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5468 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5474 x25: .cfa -32 + ^
STACK CFI 5524 x21: x21 x22: x22
STACK CFI 5528 x23: x23 x24: x24
STACK CFI 552c x25: x25
STACK CFI 5548 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 5554 x21: x21 x22: x22
STACK CFI 555c x23: x23 x24: x24
STACK CFI 5560 x25: x25
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5570 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 55ac x21: x21 x22: x22
STACK CFI 55b0 x23: x23 x24: x24
STACK CFI 55b4 x25: x25
STACK CFI 55bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 5608 98 .cfa: sp 0 + .ra: x30
STACK CFI 560c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5618 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5644 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 569c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 56a0 14c4 .cfa: sp 0 + .ra: x30
STACK CFI 56a4 .cfa: sp 2576 +
STACK CFI 56a8 .ra: .cfa -2568 + ^ x29: .cfa -2576 + ^
STACK CFI 56b0 x27: .cfa -2496 + ^ x28: .cfa -2488 + ^
STACK CFI 56d4 x19: .cfa -2560 + ^ x20: .cfa -2552 + ^
STACK CFI 570c x21: .cfa -2544 + ^ x22: .cfa -2536 + ^
STACK CFI 585c x23: .cfa -2528 + ^ x24: .cfa -2520 + ^
STACK CFI 5884 x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 5cbc x25: x25 x26: x26
STACK CFI 5dc4 x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 5fbc x23: x23 x24: x24
STACK CFI 5fc0 x25: x25 x26: x26
STACK CFI 6000 x21: x21 x22: x22
STACK CFI 6030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 6034 .cfa: sp 2576 + .ra: .cfa -2568 + ^ x19: .cfa -2560 + ^ x20: .cfa -2552 + ^ x21: .cfa -2544 + ^ x22: .cfa -2536 + ^ x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^ x27: .cfa -2496 + ^ x28: .cfa -2488 + ^ x29: .cfa -2576 + ^
STACK CFI 6070 x23: x23 x24: x24
STACK CFI 6074 x25: x25 x26: x26
STACK CFI 6078 x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 65c8 x25: x25 x26: x26
STACK CFI 65d0 x23: x23 x24: x24
STACK CFI 65d4 x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 66cc x25: x25 x26: x26
STACK CFI 66d4 x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 674c x23: x23 x24: x24
STACK CFI 6750 x25: x25 x26: x26
STACK CFI 6754 x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 6904 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 690c x21: .cfa -2544 + ^ x22: .cfa -2536 + ^
STACK CFI 6914 x21: x21 x22: x22
STACK CFI 691c x21: .cfa -2544 + ^ x22: .cfa -2536 + ^ x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 6990 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6994 x21: .cfa -2544 + ^ x22: .cfa -2536 + ^
STACK CFI 6998 x23: .cfa -2528 + ^ x24: .cfa -2520 + ^
STACK CFI 699c x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI INIT 6b68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6b70 fc .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c70 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd8 154 .cfa: sp 0 + .ra: x30
STACK CFI 6cdc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6ce4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6cf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6dc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6e30 100 .cfa: sp 0 + .ra: x30
STACK CFI 6e34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ed4 x21: x21 x22: x22
STACK CFI 6ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6efc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6f24 x21: x21 x22: x22
STACK CFI 6f2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 6f30 168 .cfa: sp 0 + .ra: x30
STACK CFI 6f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 700c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7098 7c .cfa: sp 0 + .ra: x30
STACK CFI 70a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 710c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7118 38 .cfa: sp 0 + .ra: x30
STACK CFI 711c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7124 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 714c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7150 16c .cfa: sp 0 + .ra: x30
STACK CFI INIT 72c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 72c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72cc x19: .cfa -16 + ^
STACK CFI 72e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 72f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7350 6c .cfa: sp 0 + .ra: x30
STACK CFI 7354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 735c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 73a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 73b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 73c0 7c .cfa: sp 0 + .ra: x30
STACK CFI 73c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7440 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 744c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 74d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 74e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 74e8 480 .cfa: sp 0 + .ra: x30
STACK CFI 74ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 74f8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 7500 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 750c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7528 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 76f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 76fc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7968 32c .cfa: sp 0 + .ra: x30
STACK CFI 796c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7978 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7998 x21: .cfa -32 + ^
STACK CFI 7b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7b38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7c98 40 .cfa: sp 0 + .ra: x30
STACK CFI 7c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7cd8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cf0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d28 68 .cfa: sp 0 + .ra: x30
STACK CFI 7d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7d90 110 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7da0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ea0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ee8 e8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7fd0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8018 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8020 1ac .cfa: sp 0 + .ra: x30
STACK CFI 8024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8030 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8074 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8138 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8158 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8178 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 81d0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 81d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 81dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8244 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 828c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 829c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 82b8 13c .cfa: sp 0 + .ra: x30
STACK CFI 82bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 82d4 x21: .cfa -16 + ^
STACK CFI 83d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 83dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 83f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8408 58 .cfa: sp 0 + .ra: x30
STACK CFI 840c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8414 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8444 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8460 64 .cfa: sp 0 + .ra: x30
STACK CFI 8464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 846c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 84b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 84c8 134 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 84e0 x21: .cfa -16 + ^
STACK CFI 8514 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8568 x19: x19 x20: x20
STACK CFI 8574 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 8578 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 85c4 x19: x19 x20: x20
STACK CFI 85d8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 85dc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 85e8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 85f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 85f8 x19: x19 x20: x20
STACK CFI INIT 8600 964 .cfa: sp 0 + .ra: x30
STACK CFI 8604 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 860c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 8618 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 8630 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 8634 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 866c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8764 x25: x25 x26: x26
STACK CFI 8768 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 88c4 x25: x25 x26: x26
STACK CFI 88cc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8928 x25: x25 x26: x26
STACK CFI 8954 x19: x19 x20: x20
STACK CFI 895c x23: x23 x24: x24
STACK CFI 8964 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8968 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 8974 x25: x25 x26: x26
STACK CFI 8994 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8b64 x25: x25 x26: x26
STACK CFI 8b6c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8db0 x25: x25 x26: x26
STACK CFI 8db4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8ed0 x25: x25 x26: x26
STACK CFI 8ed8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8f38 x25: x25 x26: x26
STACK CFI 8f58 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 8f5c x25: x25 x26: x26
STACK CFI 8f60 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI INIT 8f68 1cc .cfa: sp 0 + .ra: x30
STACK CFI 8f6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8f74 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8f80 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8fa0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8fb4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9054 x25: x25 x26: x26
STACK CFI 9088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 908c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 9124 x25: x25 x26: x26
STACK CFI 9130 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 9138 16c .cfa: sp 0 + .ra: x30
STACK CFI 913c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9148 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9190 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9194 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9198 x25: .cfa -16 + ^
STACK CFI 921c x19: x19 x20: x20
STACK CFI 9224 x21: x21 x22: x22
STACK CFI 9228 x25: x25
STACK CFI 9230 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9234 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9248 x19: x19 x20: x20
STACK CFI 924c x21: x21 x22: x22
STACK CFI 9250 x25: x25
STACK CFI 9268 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 926c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9278 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25
STACK CFI 9288 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 928c .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 92a8 78 .cfa: sp 0 + .ra: x30
STACK CFI 92b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92d4 x21: .cfa -16 + ^
STACK CFI 9318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9320 1fc .cfa: sp 0 + .ra: x30
STACK CFI 9324 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 932c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 934c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9358 x25: .cfa -16 + ^
STACK CFI 9368 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 93e8 x23: x23 x24: x24
STACK CFI 93ec x25: x25
STACK CFI 93f4 x19: x19 x20: x20
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9408 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9438 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 944c x23: x23 x24: x24
STACK CFI 9454 x19: x19 x20: x20
STACK CFI 945c x25: x25
STACK CFI 9464 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9468 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9480 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9484 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 949c x19: x19 x20: x20
STACK CFI 94a0 x23: x23 x24: x24
STACK CFI 94a4 x25: x25
STACK CFI 94b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 94b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 9518 x25: .cfa -16 + ^
STACK CFI INIT 9520 1bc .cfa: sp 0 + .ra: x30
STACK CFI 9524 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 952c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9534 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9544 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9550 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 95dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 95e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 960c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9680 x27: x27 x28: x28
STACK CFI 9690 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96b0 x27: x27 x28: x28
STACK CFI 96b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96d0 x27: x27 x28: x28
STACK CFI INIT 96e0 20c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 98f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9904 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9910 288 .cfa: sp 0 + .ra: x30
STACK CFI 9914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9924 x19: .cfa -16 + ^
STACK CFI 9ab8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9abc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9aec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9b98 5c .cfa: sp 0 + .ra: x30
STACK CFI 9b9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9bd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9bdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9be4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9bec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9bf8 68 .cfa: sp 0 + .ra: x30
STACK CFI 9bfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9c60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c68 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c88 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ca8 ac .cfa: sp 0 + .ra: x30
STACK CFI 9cac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9cb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cc0 x21: .cfa -16 + ^
STACK CFI 9d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9d3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9d58 70 .cfa: sp 0 + .ra: x30
STACK CFI 9d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d64 x19: .cfa -16 + ^
STACK CFI 9dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9dbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9dc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9dc8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9dd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9e44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9e70 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ec0 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 9ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ed4 x19: .cfa -16 + ^
STACK CFI a158 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a15c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a16c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a268 37c .cfa: sp 0 + .ra: x30
STACK CFI a26c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a274 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a280 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a30c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a390 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a41c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a420 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a458 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a45c x23: .cfa -16 + ^
STACK CFI a4f8 x23: x23
STACK CFI a4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a500 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a564 x23: .cfa -16 + ^
STACK CFI a56c x23: x23
STACK CFI a580 x23: .cfa -16 + ^
STACK CFI a584 x23: x23
STACK CFI a588 x23: .cfa -16 + ^
STACK CFI a5d0 x23: x23
STACK CFI a5e0 x23: .cfa -16 + ^
STACK CFI INIT a5e8 284 .cfa: sp 0 + .ra: x30
STACK CFI INIT a870 20 .cfa: sp 0 + .ra: x30
STACK CFI a874 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a88c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a890 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8b0 20 .cfa: sp 0 + .ra: x30
STACK CFI a8b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a8cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a8d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a8e0 24 .cfa: sp 0 + .ra: x30
STACK CFI a8e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a900 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a908 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a920 24 .cfa: sp 0 + .ra: x30
STACK CFI a924 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a940 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a948 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a960 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT a9b0 388 .cfa: sp 0 + .ra: x30
STACK CFI a9b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a9c4 x19: .cfa -16 + ^
STACK CFI ac34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ac38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ac44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ac48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT ad38 380 .cfa: sp 0 + .ra: x30
STACK CFI ad3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ad44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ad50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI add8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI addc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI ae5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ae60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI aec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aec8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI aedc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aee0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI aee4 x23: .cfa -16 + ^
STACK CFI af80 x23: x23
STACK CFI af84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI af88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b03c x23: .cfa -16 + ^
STACK CFI b048 x23: x23
STACK CFI b050 x23: .cfa -16 + ^
STACK CFI b098 x23: x23
STACK CFI b0a8 x23: .cfa -16 + ^
STACK CFI b0b4 x23: x23
STACK CFI INIT b0b8 270 .cfa: sp 0 + .ra: x30
STACK CFI INIT b328 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT b378 388 .cfa: sp 0 + .ra: x30
STACK CFI b37c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b38c x19: .cfa -16 + ^
STACK CFI b5fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b600 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b60c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b610 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b700 384 .cfa: sp 0 + .ra: x30
STACK CFI b704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b70c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b718 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b7a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b828 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b8b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b8b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b8f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b8f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b8f8 x23: .cfa -16 + ^
STACK CFI b994 x23: x23
STACK CFI b998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b99c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI ba08 x23: .cfa -16 + ^
STACK CFI ba0c x23: x23
STACK CFI ba10 x23: .cfa -16 + ^
STACK CFI ba60 x23: x23
STACK CFI ba74 x23: .cfa -16 + ^
STACK CFI ba80 x23: x23
STACK CFI INIT ba88 270 .cfa: sp 0 + .ra: x30
STACK CFI INIT bcf8 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT bd48 388 .cfa: sp 0 + .ra: x30
STACK CFI bd4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd5c x19: .cfa -16 + ^
STACK CFI bfcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bfd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bfdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bfe0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c0d0 384 .cfa: sp 0 + .ra: x30
STACK CFI c0d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c0dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c0e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c174 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c1f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c1f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c288 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c2c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c2c8 x23: .cfa -16 + ^
STACK CFI c364 x23: x23
STACK CFI c368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c36c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c3d8 x23: .cfa -16 + ^
STACK CFI c3dc x23: x23
STACK CFI c3e0 x23: .cfa -16 + ^
STACK CFI c430 x23: x23
STACK CFI c444 x23: .cfa -16 + ^
STACK CFI c450 x23: x23
STACK CFI INIT c458 270 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c708 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c718 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c720 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c728 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c730 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c738 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c740 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c748 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c758 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c768 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c778 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c788 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c798 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c808 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c818 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c828 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c830 2d64 .cfa: sp 0 + .ra: x30
STACK CFI c834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c844 x19: .cfa -16 + ^
STACK CFI c928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c92c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c95c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT f598 40 .cfa: sp 0 + .ra: x30
STACK CFI f59c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f5a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f5cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f5d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f5d8 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT f630 84 .cfa: sp 0 + .ra: x30
