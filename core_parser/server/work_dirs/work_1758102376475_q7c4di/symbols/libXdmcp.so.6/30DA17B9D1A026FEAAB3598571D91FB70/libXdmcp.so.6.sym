MODULE Linux arm64 30DA17B9D1A026FEAAB3598571D91FB70 libXdmcp.so.6
INFO CODE_ID B917DA30A0D1FE26AAB3598571D91FB780505181
PUBLIC 11d8 0 XdmcpAllocARRAY8
PUBLIC 1260 0 XdmcpAllocARRAY16
PUBLIC 12e8 0 XdmcpAllocARRAY32
PUBLIC 1370 0 XdmcpAllocARRAYofARRAY8
PUBLIC 13f8 0 XdmcpARRAY8Equal
PUBLIC 1438 0 XdmcpCopyARRAY8
PUBLIC 1480 0 XdmcpReallocARRAY8
PUBLIC 14f8 0 XdmcpReallocARRAYofARRAY8
PUBLIC 1598 0 XdmcpReallocARRAY16
PUBLIC 1610 0 XdmcpReallocARRAY32
PUBLIC 1688 0 XdmcpDisposeARRAY8
PUBLIC 16b8 0 XdmcpDisposeARRAY16
PUBLIC 16e8 0 XdmcpDisposeARRAY32
PUBLIC 1718 0 XdmcpDisposeARRAYofARRAY8
PUBLIC 1778 0 XdmcpFill
PUBLIC 1858 0 XdmcpFlush
PUBLIC 1898 0 XdmcpGenerateKey
PUBLIC 18a0 0 XdmcpCompareKeys
PUBLIC 18d8 0 XdmcpIncrementKey
PUBLIC 1908 0 XdmcpDecrementKey
PUBLIC 1938 0 XdmcpReadRemaining
PUBLIC 1948 0 XdmcpReadCARD8
PUBLIC 1978 0 XdmcpReadCARD16
PUBLIC 1a08 0 XdmcpReadHeader
PUBLIC 1a68 0 XdmcpReadARRAY8
PUBLIC 1b50 0 XdmcpReadARRAYofARRAY8
PUBLIC 1c40 0 XdmcpReadARRAY16
PUBLIC 1d28 0 XdmcpReadCARD32
PUBLIC 1de8 0 XdmcpReadARRAY32
PUBLIC 1ed0 0 XdmcpUnwrap
PUBLIC 2030 0 _XdmcpWrapperToOddParity
PUBLIC 20a0 0 XdmcpWrap
PUBLIC 2218 0 XdmcpWriteCARD8
PUBLIC 2248 0 XdmcpWriteCARD16
PUBLIC 2288 0 XdmcpWriteHeader
PUBLIC 2338 0 XdmcpWriteARRAY8
PUBLIC 23b8 0 XdmcpWriteARRAYofARRAY8
PUBLIC 2438 0 XdmcpWriteARRAY16
PUBLIC 24b8 0 XdmcpWriteCARD32
PUBLIC 2528 0 XdmcpWriteARRAY32
PUBLIC 25a8 0 _XdmcpAuthSetup
PUBLIC 27a8 0 _XdmcpAuthDoIt
STACK CFI INIT 1118 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1148 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1188 48 .cfa: sp 0 + .ra: x30
STACK CFI 118c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1194 x19: .cfa -16 + ^
STACK CFI 11cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11d8 88 .cfa: sp 0 + .ra: x30
STACK CFI 11dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 120c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1210 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1220 x21: .cfa -16 + ^
STACK CFI 1240 x21: x21
STACK CFI 124c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1250 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 125c x21: x21
STACK CFI INIT 1260 84 .cfa: sp 0 + .ra: x30
STACK CFI 1264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1270 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1294 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12a4 x21: .cfa -16 + ^
STACK CFI 12c4 x21: x21
STACK CFI 12d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 12e0 x21: x21
STACK CFI INIT 12e8 84 .cfa: sp 0 + .ra: x30
STACK CFI 12ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 131c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 132c x21: .cfa -16 + ^
STACK CFI 134c x21: x21
STACK CFI 1358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 135c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1368 x21: x21
STACK CFI INIT 1370 88 .cfa: sp 0 + .ra: x30
STACK CFI 1374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 13b8 x21: .cfa -16 + ^
STACK CFI 13d8 x21: x21
STACK CFI 13e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 13f4 x21: x21
STACK CFI INIT 13f8 40 .cfa: sp 0 + .ra: x30
STACK CFI 140c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 142c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1438 44 .cfa: sp 0 + .ra: x30
STACK CFI 143c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1444 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1480 78 .cfa: sp 0 + .ra: x30
STACK CFI 1484 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 148c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 14b0 x21: .cfa -16 + ^
STACK CFI 14e8 x21: x21
STACK CFI 14ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 14f4 x21: x21
STACK CFI INIT 14f8 9c .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1514 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1520 x21: .cfa -16 + ^
STACK CFI 1560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1564 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1578 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1598 78 .cfa: sp 0 + .ra: x30
STACK CFI 15ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15c0 x21: .cfa -16 + ^
STACK CFI 15f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 15fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 160c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1610 78 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 162c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1638 x21: .cfa -16 + ^
STACK CFI 1670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1674 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1688 2c .cfa: sp 0 + .ra: x30
STACK CFI 168c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1694 x19: .cfa -16 + ^
STACK CFI 16b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16b8 2c .cfa: sp 0 + .ra: x30
STACK CFI 16bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16c4 x19: .cfa -16 + ^
STACK CFI 16e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16e8 2c .cfa: sp 0 + .ra: x30
STACK CFI 16ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16f4 x19: .cfa -16 + ^
STACK CFI 1710 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1718 5c .cfa: sp 0 + .ra: x30
STACK CFI 171c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1724 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1778 e0 .cfa: sp 0 + .ra: x30
STACK CFI 177c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1784 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 178c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 179c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 17f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1814 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 181c x25: .cfa -16 + ^
STACK CFI 1848 x25: x25
STACK CFI 184c x25: .cfa -16 + ^
STACK CFI 1854 x25: x25
STACK CFI INIT 1858 40 .cfa: sp 0 + .ra: x30
STACK CFI 185c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 186c x19: .cfa -16 + ^
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1898 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1908 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1938 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1948 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1978 8c .cfa: sp 0 + .ra: x30
STACK CFI 197c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1984 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1994 x21: .cfa -32 + ^
STACK CFI 19d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 19d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a08 5c .cfa: sp 0 + .ra: x30
STACK CFI 1a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a68 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a78 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1aac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1b50 ec .cfa: sp 0 + .ra: x30
STACK CFI 1b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c40 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1c44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c50 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1cec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d28 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d44 x21: .cfa -32 + ^
STACK CFI 1d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1de8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1df8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1eb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ec8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ed0 15c .cfa: sp 0 + .ra: x30
STACK CFI 1ed4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1edc x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1eec x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 1ef4 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 1f04 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 1f4c x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 1fec x27: x27 x28: x28
STACK CFI 2014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2018 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 2024 x27: x27 x28: x28
STACK CFI 2028 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 2030 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 20a4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 20b4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 20c4 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 20cc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 20d8 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 2204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2208 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2218 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2248 40 .cfa: sp 0 + .ra: x30
STACK CFI 224c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2254 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2288 b0 .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2294 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22b8 x21: .cfa -16 + ^
STACK CFI 22dc x21: x21
STACK CFI 22fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2300 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 232c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2330 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2334 x21: x21
STACK CFI INIT 2338 80 .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2344 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 234c x21: .cfa -16 + ^
STACK CFI 23a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 23b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 23b8 80 .cfa: sp 0 + .ra: x30
STACK CFI 23bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23cc x21: .cfa -16 + ^
STACK CFI 2420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2424 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2438 80 .cfa: sp 0 + .ra: x30
STACK CFI 243c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2444 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 244c x21: .cfa -16 + ^
STACK CFI 24a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 24a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 24b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 24b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 24bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 24e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2528 80 .cfa: sp 0 + .ra: x30
STACK CFI 252c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2534 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 253c x21: .cfa -16 + ^
STACK CFI 2590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2594 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 25a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 25a8 1fc .cfa: sp 0 + .ra: x30
STACK CFI INIT 27a8 328 .cfa: sp 0 + .ra: x30
