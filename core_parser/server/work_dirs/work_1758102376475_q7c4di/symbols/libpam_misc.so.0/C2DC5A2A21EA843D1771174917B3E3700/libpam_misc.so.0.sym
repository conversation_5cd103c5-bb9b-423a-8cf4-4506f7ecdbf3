MODULE Linux arm64 C2DC5A2A21EA843D1771174917B3E3700 libpam_misc.so.0
INFO CODE_ID 2A5ADCC2EA213D841771174917B3E3705EFC40BC
PUBLIC 11a8 0 pam_misc_drop_env
PUBLIC 1208 0 pam_misc_paste_env
PUBLIC 1258 0 pam_misc_setenv
PUBLIC 1930 0 misc_conv
STACK CFI INIT 10e8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1118 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1158 48 .cfa: sp 0 + .ra: x30
STACK CFI 115c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1164 x19: .cfa -16 + ^
STACK CFI 119c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a8 5c .cfa: sp 0 + .ra: x30
STACK CFI 11ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1208 4c .cfa: sp 0 + .ra: x30
STACK CFI 1210 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1218 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1258 d8 .cfa: sp 0 + .ra: x30
STACK CFI 125c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1264 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1274 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1288 x23: .cfa -32 + ^
STACK CFI 1328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 132c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1330 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1340 80 .cfa: sp 0 + .ra: x30
STACK CFI 1344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 134c x19: .cfa -16 + ^
STACK CFI 1398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 139c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13c0 138 .cfa: sp 0 + .ra: x30
STACK CFI 13c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13f0 x21: .cfa -32 + ^
STACK CFI 1450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1454 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 14f8 438 .cfa: sp 0 + .ra: x30
STACK CFI 14fc .cfa: sp 1344 +
STACK CFI 1504 .ra: .cfa -1336 + ^ x29: .cfa -1344 + ^
STACK CFI 1514 x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x23: .cfa -1296 + ^ x24: .cfa -1288 + ^
STACK CFI 1530 x27: .cfa -1264 + ^ x28: .cfa -1256 + ^
STACK CFI 15cc x21: .cfa -1312 + ^ x22: .cfa -1304 + ^
STACK CFI 15d4 x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 16bc x21: x21 x22: x22
STACK CFI 16c0 x25: x25 x26: x26
STACK CFI 16fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 1700 .cfa: sp 1344 + .ra: .cfa -1336 + ^ x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x23: .cfa -1296 + ^ x24: .cfa -1288 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^ x27: .cfa -1264 + ^ x28: .cfa -1256 + ^ x29: .cfa -1344 + ^
STACK CFI 17a4 x21: x21 x22: x22
STACK CFI 17a8 x25: x25 x26: x26
STACK CFI 17f4 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 183c x21: x21 x22: x22
STACK CFI 1840 x25: x25 x26: x26
STACK CFI 1844 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 1898 x21: x21 x22: x22
STACK CFI 189c x25: x25 x26: x26
STACK CFI 18a0 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 18c0 x21: x21 x22: x22
STACK CFI 18c4 x25: x25 x26: x26
STACK CFI 18c8 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 18dc x21: x21 x22: x22
STACK CFI 18e0 x25: x25 x26: x26
STACK CFI 18e4 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI 190c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 1928 x21: .cfa -1312 + ^ x22: .cfa -1304 + ^
STACK CFI 192c x25: .cfa -1280 + ^ x26: .cfa -1272 + ^
STACK CFI INIT 1930 3fc .cfa: sp 0 + .ra: x30
STACK CFI 1934 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 193c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1944 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 196c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1974 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1990 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1a24 x23: x23 x24: x24
STACK CFI 1a28 x25: x25 x26: x26
STACK CFI 1a2c x27: x27 x28: x28
STACK CFI 1a34 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1b6c x23: x23 x24: x24
STACK CFI 1b74 x25: x25 x26: x26
STACK CFI 1b78 x27: x27 x28: x28
STACK CFI 1b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ba0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 1c58 x25: x25 x26: x26
STACK CFI 1c5c x23: x23 x24: x24
STACK CFI 1c60 x27: x27 x28: x28
STACK CFI 1c68 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1cf8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1cfc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1d00 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1d04 x27: .cfa -80 + ^ x28: .cfa -72 + ^
