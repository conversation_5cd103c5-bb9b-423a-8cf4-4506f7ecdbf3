MODULE Linux arm64 1FEBC7CB057EFA9B4B563B340FE0FD870 libXinerama.so.1
INFO CODE_ID CBC7EB1F7E059BFA4B563B340FE0FD875BC098D2
PUBLIC d78 0 XPanoramiXQueryExtension
PUBLIC dd8 0 XPanoramiXQueryVersion
PUBLIC f20 0 XPanoramiXAllocInfo
PUBLIC f28 0 XPanoramiXGetState
PUBLIC 1070 0 XPanoramiXGetScreenCount
PUBLIC 11b8 0 XPanoramiXGetScreenSize
PUBLIC 1310 0 XineramaQueryExtension
PUBLIC 1318 0 XineramaQueryVersion
PUBLIC 1320 0 XineramaIsActive
PUBLIC 1438 0 XineramaQueryScreens
STACK CFI INIT c38 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT c68 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca0 48 .cfa: sp 0 + .ra: x30
STACK CFI ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cac x19: .cfa -16 + ^
STACK CFI ce4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ce8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cf0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT d00 78 .cfa: sp 0 + .ra: x30
STACK CFI d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d78 60 .cfa: sp 0 + .ra: x30
STACK CFI d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT dd8 144 .cfa: sp 0 + .ra: x30
STACK CFI ddc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI de4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI df0 x23: .cfa -64 + ^
STACK CFI dfc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ee0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT f20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f28 148 .cfa: sp 0 + .ra: x30
STACK CFI f2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f34 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f40 x23: .cfa -64 + ^
STACK CFI f4c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 102c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1030 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1070 148 .cfa: sp 0 + .ra: x30
STACK CFI 1074 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 107c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1088 x23: .cfa -64 + ^
STACK CFI 1094 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1178 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11b8 154 .cfa: sp 0 + .ra: x30
STACK CFI 11bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1318 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1320 114 .cfa: sp 0 + .ra: x30
STACK CFI 1324 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 132c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 133c x21: .cfa -64 + ^
STACK CFI 1400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1404 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1438 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 143c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1444 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 144c x25: .cfa -64 + ^
STACK CFI 1458 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1474 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 157c x19: x19 x20: x20
STACK CFI 15a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 15a8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 15ac x19: x19 x20: x20
STACK CFI 15b0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15e0 x19: x19 x20: x20
STACK CFI 15e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15e8 x19: x19 x20: x20
STACK CFI 1604 x19: .cfa -112 + ^ x20: .cfa -104 + ^
