MODULE Linux arm64 23718C8F9335EDD269E7D3AD5E4A1EB70 libutil.so.1
INFO CODE_ID 8F8C71233593D2ED69E7D3AD5E4A1EB70F2EB348
PUBLIC 1208 0 login
PUBLIC 1390 0 login_tty
PUBLIC 1490 0 logout
PUBLIC 15b0 0 logwtmp
PUBLIC 17b8 0 openpty
PUBLIC 1970 0 forkpty
STACK CFI INIT 1048 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1078 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 10bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10c4 x19: .cfa -16 + ^
STACK CFI 10fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1100 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1108 100 .cfa: sp 0 + .ra: x30
STACK CFI 110c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1114 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 111c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1128 x23: .cfa -16 + ^
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1208 184 .cfa: sp 0 + .ra: x30
STACK CFI 1210 .cfa: sp 4592 +
STACK CFI 121c .ra: .cfa -4584 + ^ x29: .cfa -4592 + ^
STACK CFI 1224 x19: .cfa -4576 + ^ x20: .cfa -4568 + ^
STACK CFI 1238 x21: .cfa -4560 + ^ x22: .cfa -4552 + ^
STACK CFI 1300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1304 .cfa: sp 4592 + .ra: .cfa -4584 + ^ x19: .cfa -4576 + ^ x20: .cfa -4568 + ^ x21: .cfa -4560 + ^ x22: .cfa -4552 + ^ x29: .cfa -4592 + ^
STACK CFI INIT 1390 100 .cfa: sp 0 + .ra: x30
STACK CFI 1394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 139c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1478 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 148c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1490 120 .cfa: sp 0 + .ra: x30
STACK CFI 1494 .cfa: sp 880 +
STACK CFI 1498 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 14a0 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 14c4 x21: .cfa -848 + ^
STACK CFI 153c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1540 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x29: .cfa -880 + ^
STACK CFI INIT 15b0 104 .cfa: sp 0 + .ra: x30
STACK CFI 15b4 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 15bc x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 15cc x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 15e0 x23: .cfa -448 + ^
STACK CFI 16ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 16b0 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x29: .cfa -496 + ^
STACK CFI INIT 16b8 100 .cfa: sp 0 + .ra: x30
STACK CFI 16bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16d8 x23: .cfa -16 + ^
STACK CFI 1798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 179c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 17b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 17b8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 17c0 .cfa: sp 4208 +
STACK CFI 17c4 .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI 17cc x19: .cfa -4192 + ^ x20: .cfa -4184 + ^
STACK CFI 17d8 x27: .cfa -4128 + ^ x28: .cfa -4120 + ^
STACK CFI 17ec x21: .cfa -4176 + ^ x22: .cfa -4168 + ^
STACK CFI 17f4 x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI 1800 x25: .cfa -4144 + ^ x26: .cfa -4136 + ^
STACK CFI 1884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1888 .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x25: .cfa -4144 + ^ x26: .cfa -4136 + ^ x27: .cfa -4128 + ^ x28: .cfa -4120 + ^ x29: .cfa -4208 + ^
STACK CFI INIT 1970 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1984 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1994 x21: .cfa -32 + ^
STACK CFI 1a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1a0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
