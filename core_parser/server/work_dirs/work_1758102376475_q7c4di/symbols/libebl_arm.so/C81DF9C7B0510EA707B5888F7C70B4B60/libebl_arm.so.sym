MODULE Linux arm64 C81DF9C7B0510EA707B5888F7C70B4B60 libebl_arm.so
INFO CODE_ID C7F91DC851B0A70E07B5888F7C70B4B6D0B2EAD7
PUBLIC 1d90 0 arm_init
STACK CFI INIT 1ba8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bd8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c18 48 .cfa: sp 0 + .ra: x30
STACK CFI 1c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c24 x19: .cfa -16 + ^
STACK CFI 1c5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c68 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c98 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ce0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cf0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1cf4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1cfc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d68 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1d90 240 .cfa: sp 0 + .ra: x30
STACK CFI 1d94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1da0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1fd0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ff0 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2048 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20d8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f0 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2170 524 .cfa: sp 0 + .ra: x30
STACK CFI 21c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 222c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2628 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 268c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2698 26c .cfa: sp 0 + .ra: x30
STACK CFI 269c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 26c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2754 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 279c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 27a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2908 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2938 700 .cfa: sp 0 + .ra: x30
STACK CFI 293c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2950 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 295c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2984 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 29bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 29e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3038 230 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3044 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3054 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3070 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3118 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3268 25c .cfa: sp 0 + .ra: x30
STACK CFI 326c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3274 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3284 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 32a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3348 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 34c8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34f0 cc .cfa: sp 0 + .ra: x30
STACK CFI 34f4 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 3500 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 3530 x21: .cfa -448 + ^
STACK CFI 35b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 35b8 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x29: .cfa -480 + ^
