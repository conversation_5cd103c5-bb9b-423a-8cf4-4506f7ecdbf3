MODULE Linux arm64 131EDC16BD01A50B56AA6F603D7ED2C80 libhs_routine.so
INFO CODE_ID 16DC1E1301BD0BA556AA6F603D7ED2C8
PUBLIC 63e8 0 _init
PUBLIC 6bb0 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag) [clone .constprop.0]
PUBLIC 6c60 0 __static_initialization_and_destruction_0(int, int) [clone .constprop.0]
PUBLIC 7f30 0 _GLOBAL__sub_I_summary.cc
PUBLIC 7f40 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 8000 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag) [clone .constprop.0]
PUBLIC 80b0 0 __static_initialization_and_destruction_0(int, int) [clone .constprop.0]
PUBLIC 96e0 0 _GLOBAL__sub_I_condition.cc
PUBLIC 96e4 0 call_weak_fn
PUBLIC 96f8 0 deregister_tm_clones
PUBLIC 9728 0 register_tm_clones
PUBLIC 9764 0 __do_global_dtors_aux
PUBLIC 97b4 0 frame_dummy
PUBLIC 97c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 9880 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > __gnu_cxx::__to_xstring<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, char>(int (*)(char*, unsigned long, char const*, std::__va_list), unsigned long, char const*, ...) [clone .constprop.0]
PUBLIC 9980 0 hesai::routine::CalibSummary::CalibSummary(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 9ab0 0 hesai::solver::FitGaussian(std::vector<double, std::allocator<double> >*, double)
PUBLIC 9d80 0 hesai::routine::CalibSummary::CalStatistic(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC c050 0 hesai::routine::CalibSummary::Init()
PUBLIC ecf0 0 hesai::routine::CalibSummary::Submit()
PUBLIC 10070 0 std::ctype<char>::do_widen(char) const
PUBLIC 10080 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 10090 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 100a0 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC 10120 0 std::vector<int, std::allocator<int> >::~vector()
PUBLIC 10130 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 10140 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 10150 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 101c0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 10220 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 10280 0 FormatLiLog::LogError(char const*)
PUBLIC 106b0 0 hesai::LiLogger::LiLogger(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, hesai::log_rank_t)
PUBLIC 108d0 0 hesai::Logger::log(hesai::log_rank_t, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 12770 0 hesai::LiLogger::~LiLogger()
PUBLIC 131c0 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::vector(std::initializer_list<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&)
PUBLIC 13390 0 std::basic_ostream<char, std::char_traits<char> >& std::operator<< <char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&, std::_Put_time<char>)
PUBLIC 13610 0 std::vector<std::experimental::filesystem::v1::__cxx11::path::_Cmpt, std::allocator<std::experimental::filesystem::v1::__cxx11::path::_Cmpt> >::~vector()
PUBLIC 136a0 0 std::_Rb_tree<hesai::sys::StatusRank, std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*)
PUBLIC 13720 0 std::map<hesai::sys::StatusRank, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::map(std::initializer_list<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<hesai::sys::StatusRank> const&, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 13940 0 std::map<hesai::sys::StatusRank, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::~map()
PUBLIC 139c0 0 std::_Rb_tree<int, std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*)
PUBLIC 13a40 0 std::map<int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::map(std::initializer_list<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<int> const&, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 13c60 0 std::map<int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::~map()
PUBLIC 13ce0 0 void std::vector<double, std::allocator<double> >::_M_realloc_insert<double const&>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, double const&)
PUBLIC 13e10 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, double, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, long, double, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 13f20 0 void std::__introselect<__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, __gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, __gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 14190 0 hesai::io::ConvertInputInsData(hesai::io::InputInsData const&, double, int)
PUBLIC 14430 0 hesai::routine::VehicleCondition::VehicleCondition()
PUBLIC 14450 0 hesai::routine::VehicleCondition::Reset()
PUBLIC 14470 0 hesai::routine::VehicleCondition::AddInsData(hesai::io::InputInsData const&, double)
PUBLIC 14b70 0 hesai::ds::InsKR::Info[abi:cxx11]() const
PUBLIC 15c90 0 hesai::ds::PoseStamped::Info[abi:cxx11]() const
PUBLIC 16d80 0 std::vector<int, std::allocator<int> >::vector(std::initializer_list<int>, std::allocator<int> const&)
PUBLIC 16e20 0 Eigen::Matrix<double, 3, 1, 0, 3, 1> hesai::ds::RPYfromQuaternion<double>(Eigen::Quaternion<double, 0> const&)
PUBLIC 171c0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >*)
PUBLIC 17240 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::Type, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::~map()
PUBLIC 172c0 0 std::_Rb_tree<hesai::io::ply::Type, std::pair<hesai::io::ply::Type const, int>, std::_Select1st<std::pair<hesai::io::ply::Type const, int> >, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::_M_erase(std::_Rb_tree_node<std::pair<hesai::io::ply::Type const, int> >*)
PUBLIC 17310 0 std::map<hesai::io::ply::Type, int, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::map(std::initializer_list<std::pair<hesai::io::ply::Type const, int> >, std::less<hesai::io::ply::Type> const&, std::allocator<std::pair<hesai::io::ply::Type const, int> > const&)
PUBLIC 17470 0 std::map<hesai::io::ply::Type, int, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::~map()
PUBLIC 174b0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >*)
PUBLIC 17530 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::FieldName, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::~map()
PUBLIC 175b0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 17730 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::Type, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > const&)
PUBLIC 17980 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 17b00 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::FieldName, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > const&)
PUBLIC 17d48 0 _fini
STACK CFI INIT 96f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9728 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9764 50 .cfa: sp 0 + .ra: x30
STACK CFI 9774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 977c x19: .cfa -16 + ^
STACK CFI 97ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 97b4 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10080 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10090 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 100a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 100a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 100ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 100b4 x21: .cfa -16 + ^
STACK CFI 100f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 100fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 10120 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10130 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10140 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6bb0 ac .cfa: sp 0 + .ra: x30
STACK CFI 6bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6bc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 6c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10150 70 .cfa: sp 0 + .ra: x30
STACK CFI 10154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1015c x19: .cfa -16 + ^
STACK CFI 101b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 101b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 101bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 97c0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 97c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 97d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 982c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9880 100 .cfa: sp 0 + .ra: x30
STACK CFI 9884 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9890 .cfa: x29 288 +
STACK CFI 989c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 98bc x21: .cfa -256 + ^
STACK CFI 9944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9948 .cfa: x29 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x29: .cfa -288 + ^
STACK CFI 997c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9980 124 .cfa: sp 0 + .ra: x30
STACK CFI 9984 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9994 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 999c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 99ac x23: .cfa -32 + ^
STACK CFI 9a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9a20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 101c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 101c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101d8 x19: .cfa -16 + ^
STACK CFI 10210 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10220 60 .cfa: sp 0 + .ra: x30
STACK CFI 10224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10238 x19: .cfa -16 + ^
STACK CFI 1027c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10280 428 .cfa: sp 0 + .ra: x30
STACK CFI 10284 .cfa: sp 544 +
STACK CFI 10288 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 10290 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 1029c x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 102a4 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 102b4 x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 10564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10568 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI INIT 106b0 220 .cfa: sp 0 + .ra: x30
STACK CFI 106b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 106bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 106c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 106d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 106dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 107f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 107f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 10818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1081c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 10868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1086c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 108d0 1e94 .cfa: sp 0 + .ra: x30
STACK CFI 108d4 .cfa: sp 1136 +
STACK CFI 108dc .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 108e8 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 10928 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 10930 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 10938 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 1093c x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 11074 x21: x21 x22: x22
STACK CFI 11078 x23: x23 x24: x24
STACK CFI 1107c x25: x25 x26: x26
STACK CFI 11080 x27: x27 x28: x28
STACK CFI 110a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 110a4 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x29: .cfa -1136 + ^
STACK CFI 110b4 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 110c0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 110c8 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 110d0 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 110d8 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 110dc x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 11794 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1179c x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 117a4 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 117ac x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 117b0 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 11c2c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11c34 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 11c3c x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 11c44 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 11c48 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 12300 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12304 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 12308 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 1230c x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 12310 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI INIT 12770 a4c .cfa: sp 0 + .ra: x30
STACK CFI 12774 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 1277c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 12824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12828 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI 12838 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 129d4 x21: x21 x22: x22
STACK CFI 129d8 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 12c2c x21: x21 x22: x22
STACK CFI 12c30 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI INIT 131c0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 131c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 131cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 131d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 131e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1322c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 132d0 x25: x25 x26: x26
STACK CFI 132e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 132e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 132ec x25: x25 x26: x26
STACK CFI 1331c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13320 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1332c x25: x25 x26: x26
STACK CFI 13338 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 13348 x25: x25 x26: x26
STACK CFI 13350 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 13390 278 .cfa: sp 0 + .ra: x30
STACK CFI 13394 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1339c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 133a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 133b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 133c0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 133d4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 13464 x25: x25 x26: x26
STACK CFI 134bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 134c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 13500 x25: x25 x26: x26
STACK CFI 13504 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1353c x25: x25 x26: x26
STACK CFI 1355c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 135f8 x25: x25 x26: x26
STACK CFI 135fc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 13610 84 .cfa: sp 0 + .ra: x30
STACK CFI 13614 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1361c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13624 x21: .cfa -16 + ^
STACK CFI 13670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 13674 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 13690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 136a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 136a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 136b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 136b8 x21: .cfa -16 + ^
STACK CFI 13710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 13720 218 .cfa: sp 0 + .ra: x30
STACK CFI 13724 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1372c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 13740 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1375c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13764 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1376c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 13864 x19: x19 x20: x20
STACK CFI 13868 x21: x21 x22: x22
STACK CFI 1386c x27: x27 x28: x28
STACK CFI 13878 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1387c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 13940 74 .cfa: sp 0 + .ra: x30
STACK CFI 13944 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1394c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1395c x21: .cfa -16 + ^
STACK CFI 139a8 x21: x21
STACK CFI 139b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 139c0 78 .cfa: sp 0 + .ra: x30
STACK CFI 139c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 139d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 139d8 x21: .cfa -16 + ^
STACK CFI 13a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 13a40 218 .cfa: sp 0 + .ra: x30
STACK CFI 13a44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 13a4c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 13a60 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 13a7c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13a84 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 13a8c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 13b84 x19: x19 x20: x20
STACK CFI 13b88 x21: x21 x22: x22
STACK CFI 13b8c x27: x27 x28: x28
STACK CFI 13b98 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13b9c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6c60 12c8 .cfa: sp 0 + .ra: x30
STACK CFI 6c64 .cfa: sp 2576 +
STACK CFI 6c68 .ra: .cfa -2568 + ^ x29: .cfa -2576 + ^
STACK CFI 6c74 x19: .cfa -2560 + ^ x20: .cfa -2552 + ^ x21: .cfa -2544 + ^ x22: .cfa -2536 + ^
STACK CFI 6c84 x23: .cfa -2528 + ^ x24: .cfa -2520 + ^
STACK CFI 6c90 x25: .cfa -2512 + ^ x26: .cfa -2504 + ^
STACK CFI 6c98 x27: .cfa -2496 + ^ x28: .cfa -2488 + ^
STACK CFI 7dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7dd8 .cfa: sp 2576 + .ra: .cfa -2568 + ^ x19: .cfa -2560 + ^ x20: .cfa -2552 + ^ x21: .cfa -2544 + ^ x22: .cfa -2536 + ^ x23: .cfa -2528 + ^ x24: .cfa -2520 + ^ x25: .cfa -2512 + ^ x26: .cfa -2504 + ^ x27: .cfa -2496 + ^ x28: .cfa -2488 + ^ x29: .cfa -2576 + ^
STACK CFI INIT 13c60 74 .cfa: sp 0 + .ra: x30
STACK CFI 13c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13c7c x21: .cfa -16 + ^
STACK CFI 13cc8 x21: x21
STACK CFI 13cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13ce0 128 .cfa: sp 0 + .ra: x30
STACK CFI 13ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 13cf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 13d08 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 13d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 13d98 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 13e10 104 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13f20 268 .cfa: sp 0 + .ra: x30
STACK CFI 13f24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13f2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13f40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14004 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1409c x23: x23 x24: x24
STACK CFI 140b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 140b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 14100 v8: .cfa -16 + ^
STACK CFI 14144 v8: v8
STACK CFI 14148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1414c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9ab0 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 9ab4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9ac0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9acc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9ad4 v10: .cfa -56 + ^
STACK CFI 9ae4 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 9c04 v10: v10
STACK CFI 9c08 v8: v8 v9: v9
STACK CFI 9c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9c10 .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 9c18 x23: .cfa -64 + ^
STACK CFI 9ce8 x23: x23
STACK CFI 9cec v10: v10 v8: v8 v9: v9
STACK CFI 9d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d0c .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 9d2c x23: .cfa -64 + ^
STACK CFI 9d3c x23: x23
STACK CFI 9d50 x23: .cfa -64 + ^
STACK CFI INIT 9d80 22c4 .cfa: sp 0 + .ra: x30
STACK CFI 9d84 .cfa: sp 1328 +
STACK CFI 9d88 .ra: .cfa -1320 + ^ x29: .cfa -1328 + ^
STACK CFI 9d90 x19: .cfa -1312 + ^ x20: .cfa -1304 + ^
STACK CFI 9da8 v8: .cfa -1232 + ^ v9: .cfa -1224 + ^ x21: .cfa -1296 + ^ x22: .cfa -1288 + ^ x23: .cfa -1280 + ^ x24: .cfa -1272 + ^ x25: .cfa -1264 + ^ x26: .cfa -1256 + ^ x27: .cfa -1248 + ^ x28: .cfa -1240 + ^
STACK CFI a8f4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a8f8 .cfa: sp 1328 + .ra: .cfa -1320 + ^ v8: .cfa -1232 + ^ v9: .cfa -1224 + ^ x19: .cfa -1312 + ^ x20: .cfa -1304 + ^ x21: .cfa -1296 + ^ x22: .cfa -1288 + ^ x23: .cfa -1280 + ^ x24: .cfa -1272 + ^ x25: .cfa -1264 + ^ x26: .cfa -1256 + ^ x27: .cfa -1248 + ^ x28: .cfa -1240 + ^ x29: .cfa -1328 + ^
STACK CFI INIT 7f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c050 2c94 .cfa: sp 0 + .ra: x30
STACK CFI c054 .cfa: sp 1088 +
STACK CFI c058 .ra: .cfa -1080 + ^ x29: .cfa -1088 + ^
STACK CFI c060 x19: .cfa -1072 + ^ x20: .cfa -1064 + ^
STACK CFI c074 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI c080 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^
STACK CFI cfa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cfac .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^ x29: .cfa -1088 + ^
STACK CFI da84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI da88 .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^ x29: .cfa -1088 + ^
STACK CFI e084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e088 .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^ x29: .cfa -1088 + ^
STACK CFI INIT ecf0 1378 .cfa: sp 0 + .ra: x30
STACK CFI ecf4 .cfa: sp 1440 +
STACK CFI ecf8 .ra: .cfa -1432 + ^ x29: .cfa -1440 + ^
STACK CFI ed00 x19: .cfa -1424 + ^ x20: .cfa -1416 + ^
STACK CFI ed08 x21: .cfa -1408 + ^ x22: .cfa -1400 + ^
STACK CFI ed10 x23: .cfa -1392 + ^ x24: .cfa -1384 + ^
STACK CFI ed20 x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI eebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI eec0 .cfa: sp 1440 + .ra: .cfa -1432 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^ x29: .cfa -1440 + ^
STACK CFI INIT 7f40 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7f44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7f50 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7fac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8000 ac .cfa: sp 0 + .ra: x30
STACK CFI 8004 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8018 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 8068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 806c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 14190 29c .cfa: sp 0 + .ra: x30
STACK CFI 14198 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 141a8 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 141cc v8: .cfa -296 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^
STACK CFI 1436c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 14370 .cfa: sp 368 + .ra: .cfa -360 + ^ v8: .cfa -296 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x29: .cfa -368 + ^
STACK CFI INIT 14430 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14450 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14b70 1118 .cfa: sp 0 + .ra: x30
STACK CFI 14b74 .cfa: sp 1440 +
STACK CFI 14b78 .ra: .cfa -1432 + ^ x29: .cfa -1440 + ^
STACK CFI 14b80 x19: .cfa -1424 + ^ x20: .cfa -1416 + ^
STACK CFI 14b88 x21: .cfa -1408 + ^ x22: .cfa -1400 + ^
STACK CFI 14b90 x23: .cfa -1392 + ^ x24: .cfa -1384 + ^
STACK CFI 14b98 x25: .cfa -1376 + ^ x26: .cfa -1368 + ^
STACK CFI 14ba8 v8: .cfa -1344 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI 157cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 157d0 .cfa: sp 1440 + .ra: .cfa -1432 + ^ v8: .cfa -1344 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^ x29: .cfa -1440 + ^
STACK CFI INIT 15c90 10e4 .cfa: sp 0 + .ra: x30
STACK CFI 15c94 .cfa: sp 1440 +
STACK CFI 15c98 .ra: .cfa -1432 + ^ x29: .cfa -1440 + ^
STACK CFI 15ca0 x19: .cfa -1424 + ^ x20: .cfa -1416 + ^
STACK CFI 15ca8 x23: .cfa -1392 + ^ x24: .cfa -1384 + ^
STACK CFI 15cb8 x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^
STACK CFI 15cc8 v8: .cfa -1344 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI 168b8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 168bc .cfa: sp 1440 + .ra: .cfa -1432 + ^ v8: .cfa -1344 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^ x29: .cfa -1440 + ^
STACK CFI INIT 16d80 a0 .cfa: sp 0 + .ra: x30
STACK CFI 16d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16d98 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16df0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16e20 394 .cfa: sp 0 + .ra: x30
STACK CFI 16e24 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 16e3c v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 16e44 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 16e50 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1702c v14: .cfa -48 + ^
STACK CFI 1709c v14: v14
STACK CFI 170c8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 170cc .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 171a0 v14: .cfa -48 + ^
STACK CFI 171a8 v14: v14
STACK CFI 171ac v14: .cfa -48 + ^
STACK CFI 171b0 v14: v14
STACK CFI INIT 14470 700 .cfa: sp 0 + .ra: x30
STACK CFI 14474 .cfa: sp 688 +
STACK CFI 14478 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 14484 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 14490 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 14498 v8: .cfa -624 + ^ v9: .cfa -616 + ^
STACK CFI 144b8 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 1453c x23: x23 x24: x24
STACK CFI 14550 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14554 .cfa: sp 688 + .ra: .cfa -680 + ^ v8: .cfa -624 + ^ v9: .cfa -616 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x29: .cfa -688 + ^
STACK CFI 14584 v10: .cfa -608 + ^ v11: .cfa -600 + ^
STACK CFI 1458c v12: .cfa -592 + ^ v13: .cfa -584 + ^
STACK CFI 145bc v10: v10 v11: v11
STACK CFI 145c0 v12: v12 v13: v13
STACK CFI 145c4 v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -592 + ^ v13: .cfa -584 + ^
STACK CFI 14994 x23: x23 x24: x24
STACK CFI 14998 v10: v10 v11: v11
STACK CFI 1499c v12: v12 v13: v13
STACK CFI 149a0 v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -592 + ^ v13: .cfa -584 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 14a40 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 14a50 x23: x23 x24: x24
STACK CFI 14a58 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14a5c .cfa: sp 688 + .ra: .cfa -680 + ^ v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -592 + ^ v13: .cfa -584 + ^ v8: .cfa -624 + ^ v9: .cfa -616 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x29: .cfa -688 + ^
STACK CFI 14a6c x23: x23 x24: x24
STACK CFI 14a74 v10: v10 v11: v11
STACK CFI 14a78 v12: v12 v13: v13
STACK CFI 14a7c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14a80 .cfa: sp 688 + .ra: .cfa -680 + ^ v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -592 + ^ v13: .cfa -584 + ^ v8: .cfa -624 + ^ v9: .cfa -616 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x29: .cfa -688 + ^
STACK CFI 14a8c v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 14a90 v10: .cfa -608 + ^ v11: .cfa -600 + ^
STACK CFI 14a94 v12: .cfa -592 + ^ v13: .cfa -584 + ^
STACK CFI 14a98 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 14aa0 v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -592 + ^ v13: .cfa -584 + ^
STACK CFI 14af4 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 14afc v10: .cfa -608 + ^ v11: .cfa -600 + ^
STACK CFI 14b00 v12: .cfa -592 + ^ v13: .cfa -584 + ^
STACK CFI INIT 171c0 78 .cfa: sp 0 + .ra: x30
STACK CFI 171c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 171d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 171d8 x21: .cfa -16 + ^
STACK CFI 17230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 17240 74 .cfa: sp 0 + .ra: x30
STACK CFI 17244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1724c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1725c x21: .cfa -16 + ^
STACK CFI 172a8 x21: x21
STACK CFI 172b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 172c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 172c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 172d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 172fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 17310 160 .cfa: sp 0 + .ra: x30
STACK CFI 17314 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1731c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1733c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17344 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17408 x19: x19 x20: x20
STACK CFI 1740c x21: x21 x22: x22
STACK CFI 17414 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 17418 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 17470 40 .cfa: sp 0 + .ra: x30
STACK CFI 17474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1747c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 174ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 174b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 174b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 174c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 174c8 x21: .cfa -16 + ^
STACK CFI 17520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 17530 74 .cfa: sp 0 + .ra: x30
STACK CFI 17534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1753c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1754c x21: .cfa -16 + ^
STACK CFI 17598 x21: x21
STACK CFI 175a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 175b0 178 .cfa: sp 0 + .ra: x30
STACK CFI 175b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 175bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 175c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 175d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 175d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 176a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 176ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 17700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17704 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 17724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 17730 248 .cfa: sp 0 + .ra: x30
STACK CFI 17734 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1773c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 17744 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 17750 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 17770 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 17778 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 17918 x19: x19 x20: x20
STACK CFI 1791c x21: x21 x22: x22
STACK CFI 1792c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17930 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 17980 178 .cfa: sp 0 + .ra: x30
STACK CFI 17984 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1798c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 17998 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 179a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 179a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 17a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17a7c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 17ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17ad4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 17af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 17b00 248 .cfa: sp 0 + .ra: x30
STACK CFI 17b04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 17b0c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 17b14 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 17b20 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 17b40 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 17b48 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 17ce8 x19: x19 x20: x20
STACK CFI 17cec x21: x21 x22: x22
STACK CFI 17cfc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17d00 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 80b0 1630 .cfa: sp 0 + .ra: x30
STACK CFI 80b4 .cfa: sp 2592 +
STACK CFI 80b8 .ra: .cfa -2584 + ^ x29: .cfa -2592 + ^
STACK CFI 80c4 x19: .cfa -2576 + ^ x20: .cfa -2568 + ^
STACK CFI 80d4 x23: .cfa -2544 + ^ x24: .cfa -2536 + ^
STACK CFI 80e0 x21: .cfa -2560 + ^ x22: .cfa -2552 + ^
STACK CFI 80f0 x25: .cfa -2528 + ^ x26: .cfa -2520 + ^
STACK CFI 80fc x27: .cfa -2512 + ^ x28: .cfa -2504 + ^
STACK CFI 8104 v8: .cfa -2496 + ^
STACK CFI 9528 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 952c .cfa: sp 2592 + .ra: .cfa -2584 + ^ v8: .cfa -2496 + ^ x19: .cfa -2576 + ^ x20: .cfa -2568 + ^ x21: .cfa -2560 + ^ x22: .cfa -2552 + ^ x23: .cfa -2544 + ^ x24: .cfa -2536 + ^ x25: .cfa -2528 + ^ x26: .cfa -2520 + ^ x27: .cfa -2512 + ^ x28: .cfa -2504 + ^ x29: .cfa -2592 + ^
STACK CFI INIT 96e0 4 .cfa: sp 0 + .ra: x30
