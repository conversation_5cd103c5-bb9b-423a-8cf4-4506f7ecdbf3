MODULE Linux arm64 95F2B9D7690264AE814D39B10F644E1E0 libXfixes.so.3
INFO CODE_ID D7B9F2950269AE64814D39B10F644E1E2F9E0D1B
PUBLIC 15d8 0 XFixesSelectCursorInput
PUBLIC 1688 0 XFixesGetCursorImage
PUBLIC 1938 0 XFixesSetCursorName
PUBLIC 1a58 0 XFixesGetCursorName
PUBLIC 1bd8 0 XFixesChangeCursor
PUBLIC 1c98 0 XFixesChangeCursorByName
PUBLIC 1db8 0 XFixesHideCursor
PUBLIC 1e70 0 XFixesShowCursor
PUBLIC 1f28 0 XFixesCreatePointerBarrier
PUBLIC 20c8 0 XFixesDestroyPointerBarrier
PUBLIC 2180 0 XFixesCreateRegion
PUBLIC 2338 0 XFixesCreateRegionFromBitmap
PUBLIC 2408 0 XFixesCreateRegionFromWindow
PUBLIC 24e0 0 XFixesCreateRegionFromGC
PUBLIC 25b8 0 XFixesCreateRegionFromPicture
PUBLIC 2688 0 XFixesDestroyRegion
PUBLIC 2738 0 XFixesSetRegion
PUBLIC 28d8 0 XFixesCopyRegion
PUBLIC 2988 0 XFixesUnionRegion
PUBLIC 2a50 0 XFixesIntersectRegion
PUBLIC 2b18 0 XFixesSubtractRegion
PUBLIC 2be0 0 XFixesInvertRegion
PUBLIC 2cc8 0 XFixesTranslateRegion
PUBLIC 2d90 0 XFixesRegionExtents
PUBLIC 2e40 0 XFixesFetchRegionAndBounds
PUBLIC 3000 0 XFixesFetchRegion
PUBLIC 3048 0 XFixesSetGCClipRegion
PUBLIC 3118 0 XFixesSetWindowShapeRegion
PUBLIC 3200 0 XFixesSetPictureClipRegion
PUBLIC 32d0 0 XFixesExpandRegion
PUBLIC 33b8 0 XFixesChangeSaveSet
PUBLIC 3488 0 XFixesSelectSelectionInput
PUBLIC 3670 0 XFixesFindDisplay
PUBLIC 3ba8 0 XFixesQueryExtension
PUBLIC 3c08 0 XFixesQueryVersion
PUBLIC 3c60 0 XFixesVersion
STACK CFI INIT 1518 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1548 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1588 48 .cfa: sp 0 + .ra: x30
STACK CFI 158c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1594 x19: .cfa -16 + ^
STACK CFI 15cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 15dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1678 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1688 2ac .cfa: sp 0 + .ra: x30
STACK CFI 168c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1694 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 16a0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 172c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1788 x25: x25 x26: x26
STACK CFI 178c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 17b4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 17dc x27: x27 x28: x28
STACK CFI 17e4 x25: x25 x26: x26
STACK CFI 1810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1814 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 1840 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 184c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 18ec x23: x23 x24: x24
STACK CFI 18f0 x25: x25 x26: x26
STACK CFI 18f4 x27: x27 x28: x28
STACK CFI 18f8 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 18fc x23: x23 x24: x24
STACK CFI 1900 x25: x25 x26: x26
STACK CFI 1904 x27: x27 x28: x28
STACK CFI 190c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1910 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1914 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 192c x23: x23 x24: x24
STACK CFI 1930 x27: x27 x28: x28
STACK CFI INIT 1938 120 .cfa: sp 0 + .ra: x30
STACK CFI 193c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1944 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 194c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1954 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a4c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a58 17c .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1a64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1a74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1a7c x23: .cfa -64 + ^
STACK CFI 1b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1b8c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1bd8 bc .cfa: sp 0 + .ra: x30
STACK CFI 1bdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1be4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1c98 120 .cfa: sp 0 + .ra: x30
STACK CFI 1c9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ca4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1cac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1cb4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1d98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1dac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1db8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1dc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1dcc x21: .cfa -16 + ^
STACK CFI 1e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1e70 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1e74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e84 x21: .cfa -16 + ^
STACK CFI 1f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1f18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1f28 19c .cfa: sp 0 + .ra: x30
STACK CFI 1f2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1f34 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1f3c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1f48 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1f54 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1f5c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 209c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 20a0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 20c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 20c8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 20cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20dc x21: .cfa -16 + ^
STACK CFI 216c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2170 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 217c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2180 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 218c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 219c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 21a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 21cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 228c x25: x25 x26: x26
STACK CFI 22b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 22b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2320 x25: x25 x26: x26
STACK CFI 2328 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 232c x25: x25 x26: x26
STACK CFI 2334 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2338 cc .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2344 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 234c x21: .cfa -16 + ^
STACK CFI 23e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2408 d4 .cfa: sp 0 + .ra: x30
STACK CFI 240c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2414 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 241c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 24c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 24d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 24e0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 259c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 25b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 25b8 cc .cfa: sp 0 + .ra: x30
STACK CFI 25bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25cc x21: .cfa -16 + ^
STACK CFI 2668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 266c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2688 ac .cfa: sp 0 + .ra: x30
STACK CFI 268c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2694 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 269c x21: .cfa -16 + ^
STACK CFI 2720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2724 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2738 19c .cfa: sp 0 + .ra: x30
STACK CFI 273c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2744 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2754 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 275c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 277c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2834 x21: x21 x22: x22
STACK CFI 2858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 285c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 28c8 x21: x21 x22: x22
STACK CFI 28d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 28d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2978 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2988 c4 .cfa: sp 0 + .ra: x30
STACK CFI 298c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2994 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 299c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29a8 x23: .cfa -16 + ^
STACK CFI 2a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2a38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2a50 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a70 x23: .cfa -16 + ^
STACK CFI 2afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2b00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2b18 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b38 x23: .cfa -16 + ^
STACK CFI 2bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2bc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2be0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2be4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2bf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c00 x23: .cfa -16 + ^
STACK CFI 2cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2cb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2cc8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2cd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2cdc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ce8 x23: .cfa -16 + ^
STACK CFI 2d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2d7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2d90 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2d94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2da4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2e40 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 2e44 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2e4c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2e5c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2e64 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f4c x25: .cfa -64 + ^
STACK CFI 2fac x25: x25
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fd8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2fe4 x25: .cfa -64 + ^
STACK CFI 2ffc x25: x25
STACK CFI INIT 3000 48 .cfa: sp 0 + .ra: x30
STACK CFI 3004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 300c x19: .cfa -32 + ^
STACK CFI 3040 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3044 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3048 d0 .cfa: sp 0 + .ra: x30
STACK CFI 304c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3054 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 305c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3068 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3104 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3118 e4 .cfa: sp 0 + .ra: x30
STACK CFI 311c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3124 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 312c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3138 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3144 x25: .cfa -16 + ^
STACK CFI 31e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 31e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 31f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 3200 cc .cfa: sp 0 + .ra: x30
STACK CFI 3204 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 320c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3214 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3220 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 32b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 32b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 32c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 32d0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 32d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 32dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3300 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 339c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 33a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 33b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 33b8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 33bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 33c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 33cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 33d8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3474 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3488 c4 .cfa: sp 0 + .ra: x30
STACK CFI 348c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3494 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 349c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 34a8 x23: .cfa -16 + ^
STACK CFI 3534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3538 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3550 11c .cfa: sp 0 + .ra: x30
STACK CFI 3554 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3564 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 360c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3670 30c .cfa: sp 0 + .ra: x30
STACK CFI 3674 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 367c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3688 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3690 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 374c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3750 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 3754 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 37a4 x27: .cfa -64 + ^
STACK CFI 3890 x27: x27
STACK CFI 38e8 x25: x25 x26: x26
STACK CFI 38ec x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3920 x25: x25 x26: x26
STACK CFI 3924 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3928 x25: x25 x26: x26
STACK CFI 392c x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 3930 x27: x27
STACK CFI 3934 x27: .cfa -64 + ^
STACK CFI 3968 x25: x25 x26: x26
STACK CFI 396c x27: x27
STACK CFI 3974 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3978 x27: .cfa -64 + ^
STACK CFI INIT 3980 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 398c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a78 12c .cfa: sp 0 + .ra: x30
STACK CFI 3a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a90 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ad8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3ba8 60 .cfa: sp 0 + .ra: x30
STACK CFI 3bac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c08 58 .cfa: sp 0 + .ra: x30
STACK CFI 3c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c60 8 .cfa: sp 0 + .ra: x30
