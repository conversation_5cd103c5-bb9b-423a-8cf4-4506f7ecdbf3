MODULE Linux arm64 631DE5F442A75E3E17C162A867C821F40 libxcb-render-util.so.0
INFO CODE_ID F4E51D63A7423E5E17C162A867C821F4FB6688D9
PUBLIC 1b58 0 xcb_render_util_query_version
PUBLIC 1b78 0 xcb_render_util_query_formats
PUBLIC 1b98 0 xcb_render_util_disconnect
PUBLIC 1c60 0 xcb_render_util_find_visual_format
PUBLIC 1d48 0 xcb_render_util_find_format
PUBLIC 1f60 0 xcb_render_util_find_standard_format
PUBLIC 1ff8 0 xcb_render_util_composite_text_stream
PUBLIC 2068 0 xcb_render_util_glyphs_8
PUBLIC 2158 0 xcb_render_util_glyphs_16
PUBLIC 2240 0 xcb_render_util_glyphs_32
PUBLIC 2320 0 xcb_render_util_change_glyphset
PUBLIC 2378 0 xcb_render_util_composite_text
PUBLIC 2408 0 xcb_render_util_composite_text_checked
PUBLIC 2498 0 xcb_render_util_composite_text_free
STACK CFI INIT 16c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1738 48 .cfa: sp 0 + .ra: x30
STACK CFI 173c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1744 x19: .cfa -16 + ^
STACK CFI 177c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1780 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1788 278 .cfa: sp 0 + .ra: x30
STACK CFI 178c .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 179c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 17b0 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 17c8 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 17d4 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 17e0 x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 1990 x21: x21 x22: x22
STACK CFI 1994 x23: x23 x24: x24
STACK CFI 1998 x25: x25 x26: x26
STACK CFI 19c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 19c8 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^ x29: .cfa -416 + ^
STACK CFI 19e4 x21: x21 x22: x22
STACK CFI 19e8 x23: x23 x24: x24
STACK CFI 19ec x25: x25 x26: x26
STACK CFI 19f4 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 19f8 x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 19fc x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI INIT 1a00 154 .cfa: sp 0 + .ra: x30
STACK CFI 1a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1a94 x23: .cfa -16 + ^
STACK CFI 1b28 x23: x23
STACK CFI 1b38 x23: .cfa -16 + ^
STACK CFI INIT 1b58 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b78 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b98 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ba4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bac x21: .cfa -16 + ^
STACK CFI 1c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c60 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1c64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1c6c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1c74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1d48 218 .cfa: sp 0 + .ra: x30
STACK CFI 1d4c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1d54 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1d60 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1d7c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1d9c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1da8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1edc x21: x21 x22: x22
STACK CFI 1ee0 x23: x23 x24: x24
STACK CFI 1ee4 x27: x27 x28: x28
STACK CFI 1f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1f0c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 1f34 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1f38 x27: x27 x28: x28
STACK CFI 1f3c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1f40 x21: x21 x22: x22
STACK CFI 1f44 x23: x23 x24: x24
STACK CFI 1f48 x27: x27 x28: x28
STACK CFI 1f54 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1f58 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1f5c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 1f60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f90 68 .cfa: sp 0 + .ra: x30
STACK CFI 1f94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1ff8 70 .cfa: sp 0 + .ra: x30
STACK CFI 2008 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 201c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2028 x21: .cfa -16 + ^
STACK CFI 2064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2068 f0 .cfa: sp 0 + .ra: x30
STACK CFI 206c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 207c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 208c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2094 x23: .cfa -32 + ^
STACK CFI 2150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2154 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2158 e8 .cfa: sp 0 + .ra: x30
STACK CFI 215c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 216c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 217c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 223c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2240 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2254 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2264 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 231c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2320 58 .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 232c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2378 90 .cfa: sp 0 + .ra: x30
STACK CFI 23b8 .cfa: sp 32 +
STACK CFI 23c0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2408 90 .cfa: sp 0 + .ra: x30
STACK CFI 2448 .cfa: sp 32 +
STACK CFI 2450 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 247c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2498 28 .cfa: sp 0 + .ra: x30
STACK CFI 249c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24a4 x19: .cfa -16 + ^
STACK CFI 24bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
