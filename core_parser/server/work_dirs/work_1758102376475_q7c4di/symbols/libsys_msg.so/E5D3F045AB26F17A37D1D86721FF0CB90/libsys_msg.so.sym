MODULE Linux arm64 E5D3F045AB26F17A37D1D86721FF0CB90 libsys_msg.so
INFO CODE_ID 45F0D3E526AB7AF137D1D86721FF0CB9
PUBLIC 8f38 0 _init
PUBLIC 9550 0 rti::core::memory::OsapiAllocator<ota2powerMngr>::allocate() [clone .part.0]
PUBLIC 9584 0 rti::core::memory::OsapiAllocator<dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> >::allocate() [clone .part.0]
PUBLIC 95c0 0 _GLOBAL__sub_I_ota2powerMngr.cxx
PUBLIC 9600 0 _GLOBAL__sub_I_ota2powerMngrPlugin.cxx
PUBLIC 9640 0 _GLOBAL__sub_I_power.cxx
PUBLIC 9680 0 _GLOBAL__sub_I_powerPlugin.cxx
PUBLIC 96bc 0 call_weak_fn
PUBLIC 96d0 0 deregister_tm_clones
PUBLIC 9700 0 register_tm_clones
PUBLIC 973c 0 __do_global_dtors_aux
PUBLIC 978c 0 frame_dummy
PUBLIC 9790 0 ota2powerMngr::ota2powerMngr()
PUBLIC 97a0 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<ota2powerMngr>(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC 98e0 0 ota2powerMngr::ota2powerMngr(unsigned short, unsigned long long, unsigned char, unsigned char)
PUBLIC 9900 0 ota2powerMngr::swap(ota2powerMngr&)
PUBLIC 9950 0 ota2powerMngr::operator==(ota2powerMngr const&) const
PUBLIC 99b0 0 ota2powerMngr::operator!=(ota2powerMngr const&) const
PUBLIC 99d0 0 operator<<(std::ostream&, ota2powerMngr const&)
PUBLIC 9b40 0 powerMngr2ota::powerMngr2ota()
PUBLIC 9b50 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<powerMngr2ota>(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC 9c90 0 powerMngr2ota::powerMngr2ota(unsigned short, unsigned long long, unsigned char, unsigned char)
PUBLIC 9cb0 0 powerMngr2ota::swap(powerMngr2ota&)
PUBLIC 9d00 0 powerMngr2ota::operator==(powerMngr2ota const&) const
PUBLIC 9d60 0 powerMngr2ota::operator!=(powerMngr2ota const&) const
PUBLIC 9d80 0 operator<<(std::ostream&, powerMngr2ota const&)
PUBLIC 9ef0 0 rti::topic::dynamic_type<ota2powerMngr>::get()
PUBLIC a0c0 0 rti::topic::dynamic_type<powerMngr2ota>::get()
PUBLIC a290 0 dds::topic::topic_type_support<ota2powerMngr>::register_type(dds::domain::TDomainParticipant<rti::domain::DomainParticipantImpl>&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC a2b0 0 dds::topic::topic_type_support<ota2powerMngr>::from_cdr_buffer(ota2powerMngr&, std::vector<char, std::allocator<char> > const&)
PUBLIC a2f0 0 dds::topic::topic_type_support<ota2powerMngr>::reset_sample(ota2powerMngr&)
PUBLIC a300 0 dds::topic::topic_type_support<ota2powerMngr>::allocate_sample(ota2powerMngr&, int, int)
PUBLIC a310 0 dds::topic::topic_type_support<powerMngr2ota>::register_type(dds::domain::TDomainParticipant<rti::domain::DomainParticipantImpl>&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC a330 0 dds::topic::topic_type_support<powerMngr2ota>::from_cdr_buffer(powerMngr2ota&, std::vector<char, std::allocator<char> > const&)
PUBLIC a370 0 dds::topic::topic_type_support<powerMngr2ota>::reset_sample(powerMngr2ota&)
PUBLIC a380 0 dds::topic::topic_type_support<powerMngr2ota>::allocate_sample(powerMngr2ota&, int, int)
PUBLIC a390 0 dds::topic::topic_type_support<powerMngr2ota>::to_cdr_buffer(std::vector<char, std::allocator<char> >&, powerMngr2ota const&, short)
PUBLIC a460 0 dds::topic::topic_type_support<ota2powerMngr>::to_cdr_buffer(std::vector<char, std::allocator<char> >&, ota2powerMngr const&, short)
PUBLIC a530 0 std::vector<char, std::allocator<char> >::_M_default_append(unsigned long)
PUBLIC a670 0 ota2powerMngrPlugin_get_key_kind()
PUBLIC a680 0 ota2powerMngrPluginSupport_destroy_data(ota2powerMngr*)
PUBLIC a690 0 powerMngr2otaPluginSupport_destroy_data(powerMngr2ota*)
PUBLIC a6a0 0 ota2powerMngrPluginSupport_create_data()
PUBLIC a710 0 ota2powerMngrPlugin_on_participant_attached(void*, PRESTypePluginParticipantInfo const*, int, void*, RTICdrTypeCode*)
PUBLIC a7c0 0 ota2powerMngrPlugin_on_participant_detached(void*)
PUBLIC a800 0 ota2powerMngrPlugin_on_endpoint_detached(void*)
PUBLIC a810 0 ota2powerMngrPlugin_return_sample(void*, ota2powerMngr*, void*)
PUBLIC a8e0 0 ota2powerMngrPlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC a930 0 ota2powerMngrPlugin_on_endpoint_attached(void*, PRESTypePluginEndpointInfo const*, int, void*)
PUBLIC a9f0 0 powerMngr2otaPluginSupport_create_data()
PUBLIC aa60 0 powerMngr2otaPlugin_on_participant_attached(void*, PRESTypePluginParticipantInfo const*, int, void*, RTICdrTypeCode*)
PUBLIC ab10 0 powerMngr2otaPlugin_return_sample(void*, powerMngr2ota*, void*)
PUBLIC abe0 0 powerMngr2otaPlugin_get_key_kind()
PUBLIC abf0 0 powerMngr2otaPlugin_on_endpoint_detached(void*)
PUBLIC ac00 0 powerMngr2otaPlugin_on_participant_detached(void*)
PUBLIC ac40 0 powerMngr2otaPlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC ac90 0 powerMngr2otaPlugin_on_endpoint_attached(void*, PRESTypePluginEndpointInfo const*, int, void*)
PUBLIC ad50 0 ota2powerMngrPluginSupport_copy_data(ota2powerMngr*, ota2powerMngr const*)
PUBLIC ad70 0 ota2powerMngrPlugin_copy_sample(void*, ota2powerMngr*, ota2powerMngr const*)
PUBLIC ad80 0 ota2powerMngrPlugin_serialize_to_cdr_buffer(char*, unsigned int*, ota2powerMngr const*, short)
PUBLIC b040 0 ota2powerMngrPlugin_deserialize_from_cdr_buffer(ota2powerMngr*, char const*, unsigned int)
PUBLIC b220 0 ota2powerMngrPlugin_deserialize_key(void*, ota2powerMngr**, int*, RTICdrStream*, int, int, void*)
PUBLIC b280 0 ota2powerMngrPlugin_get_serialized_key_max_size(void*, int, unsigned short, unsigned int)
PUBLIC b2d0 0 ota2powerMngrPlugin_get_serialized_key_max_size_for_keyhash(void*, unsigned short, unsigned int)
PUBLIC b310 0 ota2powerMngrPlugin_new()
PUBLIC b470 0 ota2powerMngrPlugin_delete(PRESTypePlugin*)
PUBLIC b490 0 powerMngr2otaPluginSupport_copy_data(powerMngr2ota*, powerMngr2ota const*)
PUBLIC b4b0 0 powerMngr2otaPlugin_copy_sample(void*, powerMngr2ota*, powerMngr2ota const*)
PUBLIC b4c0 0 powerMngr2otaPlugin_serialize_to_cdr_buffer(char*, unsigned int*, powerMngr2ota const*, short)
PUBLIC b780 0 powerMngr2otaPlugin_deserialize_from_cdr_buffer(powerMngr2ota*, char const*, unsigned int)
PUBLIC b960 0 powerMngr2otaPlugin_deserialize_key(void*, powerMngr2ota**, int*, RTICdrStream*, int, int, void*)
PUBLIC b9c0 0 powerMngr2otaPlugin_get_serialized_key_max_size(void*, int, unsigned short, unsigned int)
PUBLIC ba10 0 powerMngr2otaPlugin_get_serialized_key_max_size_for_keyhash(void*, unsigned short, unsigned int)
PUBLIC ba50 0 powerMngr2otaPlugin_new()
PUBLIC bbb0 0 powerMngr2otaPlugin_delete(PRESTypePlugin*)
PUBLIC bbd0 0 rti::topic::interpreter::get_external_value_pointer(void*)
PUBLIC bbe0 0 rti::xcdr::ProgramsSingleton<ota2powerMngr, 27, true, true, true, false, rti::topic::interpreter::detail::PropertyConfigurator>::~ProgramsSingleton()
PUBLIC bc00 0 rti::xcdr::ProgramsSingleton<powerMngr2ota, 27, true, true, true, false, rti::topic::interpreter::detail::PropertyConfigurator>::~ProgramsSingleton()
PUBLIC bc20 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> >(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC bd20 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<dds::core::safe_enum<lios::internal::power::Status_def, lios::internal::power::Status_def::type> >(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC be20 0 lios::internal::power::operator<<(std::ostream&, dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> const&)
PUBLIC bef0 0 lios::internal::power::operator<<(std::ostream&, dds::core::safe_enum<lios::internal::power::Status_def, lios::internal::power::Status_def::type> const&)
PUBLIC bfc0 0 lios::internal::power::request::request()
PUBLIC bfd0 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<lios::internal::power::request>(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC c110 0 lios::internal::power::request::request(dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> const&, unsigned int)
PUBLIC c120 0 lios::internal::power::request::swap(lios::internal::power::request&)
PUBLIC c150 0 lios::internal::power::request::operator==(lios::internal::power::request const&) const
PUBLIC c180 0 lios::internal::power::request::operator!=(lios::internal::power::request const&) const
PUBLIC c1a0 0 lios::internal::power::operator<<(std::ostream&, lios::internal::power::request const&)
PUBLIC c280 0 lios::internal::power::response::response()
PUBLIC c290 0 RTIXCdrMemberValue rti::topic::interpreter::get_aggregation_value_pointer<lios::internal::power::response>(void*, unsigned int*, unsigned long long, unsigned int, RTIXCdrTypeCode const*, RTIXCdrTypeCodeMember const*, unsigned char, void*)
PUBLIC c3d0 0 lios::internal::power::response::response(dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> const&, dds::core::safe_enum<lios::internal::power::Status_def, lios::internal::power::Status_def::type> const&, short)
PUBLIC c3f0 0 lios::internal::power::response::swap(lios::internal::power::response&)
PUBLIC c430 0 lios::internal::power::response::operator==(lios::internal::power::response const&) const
PUBLIC c480 0 lios::internal::power::response::operator!=(lios::internal::power::response const&) const
PUBLIC c4a0 0 lios::internal::power::operator<<(std::ostream&, lios::internal::power::response const&)
PUBLIC c5a0 0 rti::topic::dynamic_type<dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> >::get()
PUBLIC c650 0 rti::topic::dynamic_type<dds::core::safe_enum<lios::internal::power::Status_def, lios::internal::power::Status_def::type> >::get()
PUBLIC c700 0 rti::topic::dynamic_type<lios::internal::power::request>::get()
PUBLIC c880 0 rti::topic::dynamic_type<lios::internal::power::response>::get()
PUBLIC ca10 0 dds::topic::topic_type_support<lios::internal::power::request>::register_type(dds::domain::TDomainParticipant<rti::domain::DomainParticipantImpl>&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC ca30 0 dds::topic::topic_type_support<lios::internal::power::request>::from_cdr_buffer(lios::internal::power::request&, std::vector<char, std::allocator<char> > const&)
PUBLIC ca70 0 dds::topic::topic_type_support<lios::internal::power::request>::reset_sample(lios::internal::power::request&)
PUBLIC ca80 0 dds::topic::topic_type_support<lios::internal::power::request>::allocate_sample(lios::internal::power::request&, int, int)
PUBLIC ca90 0 dds::topic::topic_type_support<lios::internal::power::response>::register_type(dds::domain::TDomainParticipant<rti::domain::DomainParticipantImpl>&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC cab0 0 dds::topic::topic_type_support<lios::internal::power::response>::from_cdr_buffer(lios::internal::power::response&, std::vector<char, std::allocator<char> > const&)
PUBLIC caf0 0 dds::topic::topic_type_support<lios::internal::power::response>::reset_sample(lios::internal::power::response&)
PUBLIC cb00 0 dds::topic::topic_type_support<lios::internal::power::response>::allocate_sample(lios::internal::power::response&, int, int)
PUBLIC cb10 0 dds::topic::topic_type_support<lios::internal::power::response>::to_cdr_buffer(std::vector<char, std::allocator<char> >&, lios::internal::power::response const&, short)
PUBLIC cbe0 0 dds::topic::topic_type_support<lios::internal::power::request>::to_cdr_buffer(std::vector<char, std::allocator<char> >&, lios::internal::power::request const&, short)
PUBLIC ccb0 0 lios::internal::power::requestPlugin_get_key_kind()
PUBLIC ccc0 0 lios::internal::power::requestPlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC cd10 0 lios::internal::power::requestPluginSupport_destroy_data(lios::internal::power::request*)
PUBLIC cd20 0 lios::internal::power::responsePluginSupport_destroy_data(lios::internal::power::response*)
PUBLIC cd30 0 lios::internal::power::requestPluginSupport_create_data()
PUBLIC cda0 0 lios::internal::power::requestPlugin_on_participant_attached(void*, PRESTypePluginParticipantInfo const*, int, void*, RTICdrTypeCode*)
PUBLIC ce50 0 lios::internal::power::requestPlugin_on_participant_detached(void*)
PUBLIC ce90 0 lios::internal::power::requestPlugin_on_endpoint_attached(void*, PRESTypePluginEndpointInfo const*, int, void*)
PUBLIC cf50 0 lios::internal::power::requestPlugin_on_endpoint_detached(void*)
PUBLIC cf60 0 lios::internal::power::requestPlugin_return_sample(void*, lios::internal::power::request*, void*)
PUBLIC d030 0 lios::internal::power::responsePluginSupport_create_data()
PUBLIC d0a0 0 lios::internal::power::responsePlugin_on_participant_attached(void*, PRESTypePluginParticipantInfo const*, int, void*, RTICdrTypeCode*)
PUBLIC d150 0 lios::internal::power::responsePlugin_return_sample(void*, lios::internal::power::response*, void*)
PUBLIC d220 0 lios::internal::power::responsePlugin_get_key_kind()
PUBLIC d230 0 lios::internal::power::responsePlugin_on_endpoint_detached(void*)
PUBLIC d240 0 lios::internal::power::responsePlugin_on_participant_detached(void*)
PUBLIC d280 0 lios::internal::power::responsePlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC d2d0 0 lios::internal::power::responsePlugin_on_endpoint_attached(void*, PRESTypePluginEndpointInfo const*, int, void*)
PUBLIC d390 0 lios::internal::power::EventPlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC d3d0 0 lios::internal::power::EventPluginSupport_print_data(dds::core::safe_enum<lios::internal::power::Event_def, lios::internal::power::Event_def::type> const*, char const*, int)
PUBLIC d450 0 lios::internal::power::StatusPlugin_get_serialized_sample_max_size(void*, int, unsigned short, unsigned int)
PUBLIC d490 0 lios::internal::power::StatusPluginSupport_print_data(dds::core::safe_enum<lios::internal::power::Status_def, lios::internal::power::Status_def::type> const*, char const*, int)
PUBLIC d510 0 lios::internal::power::requestPluginSupport_copy_data(lios::internal::power::request*, lios::internal::power::request const*)
PUBLIC d530 0 lios::internal::power::requestPlugin_copy_sample(void*, lios::internal::power::request*, lios::internal::power::request const*)
PUBLIC d540 0 lios::internal::power::requestPlugin_serialize_to_cdr_buffer(char*, unsigned int*, lios::internal::power::request const*, short)
PUBLIC d800 0 lios::internal::power::requestPlugin_deserialize_from_cdr_buffer(lios::internal::power::request*, char const*, unsigned int)
PUBLIC d9e0 0 lios::internal::power::requestPlugin_deserialize_key(void*, lios::internal::power::request**, int*, RTICdrStream*, int, int, void*)
PUBLIC da40 0 lios::internal::power::requestPlugin_get_serialized_key_max_size(void*, int, unsigned short, unsigned int)
PUBLIC da90 0 lios::internal::power::requestPlugin_get_serialized_key_max_size_for_keyhash(void*, unsigned short, unsigned int)
PUBLIC dad0 0 lios::internal::power::requestPlugin_new()
PUBLIC dc30 0 lios::internal::power::requestPlugin_delete(PRESTypePlugin*)
PUBLIC dc50 0 lios::internal::power::responsePluginSupport_copy_data(lios::internal::power::response*, lios::internal::power::response const*)
PUBLIC dc70 0 lios::internal::power::responsePlugin_copy_sample(void*, lios::internal::power::response*, lios::internal::power::response const*)
PUBLIC dc80 0 lios::internal::power::responsePlugin_serialize_to_cdr_buffer(char*, unsigned int*, lios::internal::power::response const*, short)
PUBLIC df40 0 lios::internal::power::responsePlugin_deserialize_from_cdr_buffer(lios::internal::power::response*, char const*, unsigned int)
PUBLIC e120 0 lios::internal::power::responsePlugin_deserialize_key(void*, lios::internal::power::response**, int*, RTICdrStream*, int, int, void*)
PUBLIC e180 0 lios::internal::power::responsePlugin_get_serialized_key_max_size(void*, int, unsigned short, unsigned int)
PUBLIC e1d0 0 lios::internal::power::responsePlugin_get_serialized_key_max_size_for_keyhash(void*, unsigned short, unsigned int)
PUBLIC e210 0 lios::internal::power::responsePlugin_new()
PUBLIC e370 0 lios::internal::power::responsePlugin_delete(PRESTypePlugin*)
PUBLIC e390 0 rti::xcdr::ProgramsSingleton<lios::internal::power::request, 27, true, true, true, false, rti::topic::interpreter::detail::PropertyConfigurator>::~ProgramsSingleton()
PUBLIC e3b0 0 rti::xcdr::ProgramsSingleton<lios::internal::power::response, 27, true, true, true, false, rti::topic::interpreter::detail::PropertyConfigurator>::~ProgramsSingleton()
PUBLIC e3d0 0 _fini
STACK CFI INIT 96d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9700 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 973c 50 .cfa: sp 0 + .ra: x30
STACK CFI 974c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9754 x19: .cfa -16 + ^
STACK CFI 9784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 978c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9550 34 .cfa: sp 0 + .ra: x30
STACK CFI 9554 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9790 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 97a0 140 .cfa: sp 0 + .ra: x30
STACK CFI 97ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 97b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 97bc x21: .cfa -48 + ^
STACK CFI 9800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9804 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 9818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 983c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 98e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9900 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9950 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 99b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 99c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 99d0 16c .cfa: sp 0 + .ra: x30
STACK CFI 99d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 99e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 99fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9b28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9b40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b50 140 .cfa: sp 0 + .ra: x30
STACK CFI 9b5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9b64 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9b6c x21: .cfa -48 + ^
STACK CFI 9bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9bb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 9bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9bec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9c90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9cb0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9d00 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9d60 1c .cfa: sp 0 + .ra: x30
STACK CFI 9d64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d80 16c .cfa: sp 0 + .ra: x30
STACK CFI 9d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9dac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9ed8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ef0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 9ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9efc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9f08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a00c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a0c0 1cc .cfa: sp 0 + .ra: x30
STACK CFI a0c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a0cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a0d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a1d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a1dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a290 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a2b0 3c .cfa: sp 0 + .ra: x30
STACK CFI a2b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a2d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a300 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a310 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a330 3c .cfa: sp 0 + .ra: x30
STACK CFI a334 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a354 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a35c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a370 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a530 13c .cfa: sp 0 + .ra: x30
STACK CFI a538 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a540 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a548 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a594 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a598 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a608 x23: x23 x24: x24
STACK CFI a60c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a614 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a390 c4 .cfa: sp 0 + .ra: x30
STACK CFI a394 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a39c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a3b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a42c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT a460 c4 .cfa: sp 0 + .ra: x30
STACK CFI a464 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a46c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a480 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a4f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a4fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 95c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 95c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 95cc x19: .cfa -16 + ^
STACK CFI 95f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bbd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a680 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a690 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a6a0 64 .cfa: sp 0 + .ra: x30
STACK CFI a6a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a6b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a6d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a710 ac .cfa: sp 0 + .ra: x30
STACK CFI a71c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a734 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI a7a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a7ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT a7c0 40 .cfa: sp 0 + .ra: x30
STACK CFI a7c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a7d0 x19: .cfa -16 + ^
STACK CFI a7f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a800 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a810 c8 .cfa: sp 0 + .ra: x30
STACK CFI a814 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a81c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a82c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a84c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a850 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT bbe0 20 .cfa: sp 0 + .ra: x30
STACK CFI bbec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bbf8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bc00 20 .cfa: sp 0 + .ra: x30
STACK CFI bc0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bc18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a8e0 50 .cfa: sp 0 + .ra: x30
STACK CFI a8e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a91c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a920 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT a930 bc .cfa: sp 0 + .ra: x30
STACK CFI a934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a93c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a984 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a9d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a9dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a9f0 64 .cfa: sp 0 + .ra: x30
STACK CFI a9f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aa28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aa2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT aa60 ac .cfa: sp 0 + .ra: x30
STACK CFI aa6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aa84 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI aaf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aafc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT ab10 c8 .cfa: sp 0 + .ra: x30
STACK CFI ab14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ab2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ab4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT abe0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT abf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac00 40 .cfa: sp 0 + .ra: x30
STACK CFI ac08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac10 x19: .cfa -16 + ^
STACK CFI ac38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac40 50 .cfa: sp 0 + .ra: x30
STACK CFI ac44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ac80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT ac90 bc .cfa: sp 0 + .ra: x30
STACK CFI ac94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ace0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ace4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ad38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ad3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ad50 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT ad70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ad80 2b4 .cfa: sp 0 + .ra: x30
STACK CFI ad84 .cfa: sp 992 +
STACK CFI ad88 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI ad90 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI ad9c x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI adac x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI add0 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI af38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI af3c .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI af60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI af64 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI b01c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b020 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI INIT b040 1d8 .cfa: sp 0 + .ra: x30
STACK CFI b044 .cfa: sp 1024 +
STACK CFI b048 .ra: .cfa -1016 + ^ x29: .cfa -1024 + ^
STACK CFI b050 x23: .cfa -976 + ^ x24: .cfa -968 + ^
STACK CFI b060 v8: .cfa -960 + ^
STACK CFI b068 x21: .cfa -992 + ^ x22: .cfa -984 + ^
STACK CFI b074 x19: .cfa -1008 + ^ x20: .cfa -1000 + ^
STACK CFI b180 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b184 .cfa: sp 1024 + .ra: .cfa -1016 + ^ v8: .cfa -960 + ^ x19: .cfa -1008 + ^ x20: .cfa -1000 + ^ x21: .cfa -992 + ^ x22: .cfa -984 + ^ x23: .cfa -976 + ^ x24: .cfa -968 + ^ x29: .cfa -1024 + ^
STACK CFI INIT b220 5c .cfa: sp 0 + .ra: x30
STACK CFI b224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b22c x19: .cfa -16 + ^
STACK CFI b268 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b26c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b280 50 .cfa: sp 0 + .ra: x30
STACK CFI b284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b2c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT b2d0 38 .cfa: sp 0 + .ra: x30
STACK CFI b2d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b310 154 .cfa: sp 0 + .ra: x30
STACK CFI b314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b460 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b470 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT b490 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT b4b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b4c0 2b4 .cfa: sp 0 + .ra: x30
STACK CFI b4c4 .cfa: sp 992 +
STACK CFI b4c8 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI b4d0 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI b4dc x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI b4ec x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI b510 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI b678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b67c .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI b6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b6a4 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI b75c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b760 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI INIT b780 1d8 .cfa: sp 0 + .ra: x30
STACK CFI b784 .cfa: sp 1024 +
STACK CFI b788 .ra: .cfa -1016 + ^ x29: .cfa -1024 + ^
STACK CFI b790 x23: .cfa -976 + ^ x24: .cfa -968 + ^
STACK CFI b7a0 v8: .cfa -960 + ^
STACK CFI b7a8 x21: .cfa -992 + ^ x22: .cfa -984 + ^
STACK CFI b7b4 x19: .cfa -1008 + ^ x20: .cfa -1000 + ^
STACK CFI b8c0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b8c4 .cfa: sp 1024 + .ra: .cfa -1016 + ^ v8: .cfa -960 + ^ x19: .cfa -1008 + ^ x20: .cfa -1000 + ^ x21: .cfa -992 + ^ x22: .cfa -984 + ^ x23: .cfa -976 + ^ x24: .cfa -968 + ^ x29: .cfa -1024 + ^
STACK CFI INIT b960 5c .cfa: sp 0 + .ra: x30
STACK CFI b964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b96c x19: .cfa -16 + ^
STACK CFI b9a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b9ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b9c0 50 .cfa: sp 0 + .ra: x30
STACK CFI b9c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b9fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ba00 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT ba10 38 .cfa: sp 0 + .ra: x30
STACK CFI ba14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ba50 154 .cfa: sp 0 + .ra: x30
STACK CFI ba54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bbb0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9600 3c .cfa: sp 0 + .ra: x30
STACK CFI 9604 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 960c x19: .cfa -16 + ^
STACK CFI 9634 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9584 34 .cfa: sp 0 + .ra: x30
STACK CFI 9588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT bc20 fc .cfa: sp 0 + .ra: x30
STACK CFI bc2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bc34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bc7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI bc8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bcb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT bd20 fc .cfa: sp 0 + .ra: x30
STACK CFI bd2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bd34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bd78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bd7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI bd8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bdb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT be20 d0 .cfa: sp 0 + .ra: x30
STACK CFI be24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI be34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI be94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bef0 cc .cfa: sp 0 + .ra: x30
STACK CFI bef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bf38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bf7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bfc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bfd0 140 .cfa: sp 0 + .ra: x30
STACK CFI bfdc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bfe4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bfec x21: .cfa -48 + ^
STACK CFI c030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c034 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI c048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c06c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT c110 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c120 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c150 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT c180 1c .cfa: sp 0 + .ra: x30
STACK CFI c184 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c198 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c1a0 d4 .cfa: sp 0 + .ra: x30
STACK CFI c1a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c1b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c1cc x21: .cfa -16 + ^
STACK CFI c25c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c260 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c280 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c290 140 .cfa: sp 0 + .ra: x30
STACK CFI c29c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c2a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c2ac x21: .cfa -48 + ^
STACK CFI c2f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c2f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI c308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c32c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT c3d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3f0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT c430 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT c480 1c .cfa: sp 0 + .ra: x30
STACK CFI c484 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c498 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c4a0 100 .cfa: sp 0 + .ra: x30
STACK CFI c4a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c4b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c4cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c58c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT c5a0 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c650 a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c700 174 .cfa: sp 0 + .ra: x30
STACK CFI c704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c70c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c718 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c738 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c868 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT c880 190 .cfa: sp 0 + .ra: x30
STACK CFI c884 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c88c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c898 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c8b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c8b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI c8c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c9e8 x23: x23 x24: x24
STACK CFI ca00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ca04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT ca10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca30 3c .cfa: sp 0 + .ra: x30
STACK CFI ca34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ca54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ca58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ca5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ca70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT cab0 3c .cfa: sp 0 + .ra: x30
STACK CFI cab4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cadc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT caf0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT cb00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cb10 c4 .cfa: sp 0 + .ra: x30
STACK CFI cb14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cb1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cb30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cbac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT cbe0 c4 .cfa: sp 0 + .ra: x30
STACK CFI cbe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cbec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cc00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cc7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9640 3c .cfa: sp 0 + .ra: x30
STACK CFI 9644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 964c x19: .cfa -16 + ^
STACK CFI 9674 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ccb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ccc0 50 .cfa: sp 0 + .ra: x30
STACK CFI ccc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ccfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cd00 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT cd10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT cd20 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT cd30 64 .cfa: sp 0 + .ra: x30
STACK CFI cd34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cd40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cd68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cd6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT cda0 ac .cfa: sp 0 + .ra: x30
STACK CFI cdac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cdc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI ce38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ce3c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT ce50 40 .cfa: sp 0 + .ra: x30
STACK CFI ce58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ce60 x19: .cfa -16 + ^
STACK CFI ce88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ce90 bc .cfa: sp 0 + .ra: x30
STACK CFI ce94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ce9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI cf38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cf3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT cf50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cf60 c8 .cfa: sp 0 + .ra: x30
STACK CFI cf64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cf6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cf7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cf9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cfa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT e390 20 .cfa: sp 0 + .ra: x30
STACK CFI e39c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e3a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e3b0 20 .cfa: sp 0 + .ra: x30
STACK CFI e3bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e3c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d030 64 .cfa: sp 0 + .ra: x30
STACK CFI d034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d040 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d06c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT d0a0 ac .cfa: sp 0 + .ra: x30
STACK CFI d0ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d0c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI d138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d13c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT d150 c8 .cfa: sp 0 + .ra: x30
STACK CFI d154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d15c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d16c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d18c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d190 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d230 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d240 40 .cfa: sp 0 + .ra: x30
STACK CFI d248 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d250 x19: .cfa -16 + ^
STACK CFI d278 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d280 50 .cfa: sp 0 + .ra: x30
STACK CFI d284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d2bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d2c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT d2d0 bc .cfa: sp 0 + .ra: x30
STACK CFI d2d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d2dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d37c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT d390 38 .cfa: sp 0 + .ra: x30
STACK CFI d398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d3b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d3b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT d3d0 78 .cfa: sp 0 + .ra: x30
STACK CFI d3d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d3dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d3f0 x21: .cfa -16 + ^
STACK CFI d40c x21: x21
STACK CFI d428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d42c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d450 38 .cfa: sp 0 + .ra: x30
STACK CFI d458 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d474 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d478 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT d490 78 .cfa: sp 0 + .ra: x30
STACK CFI d494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d49c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d4b0 x21: .cfa -16 + ^
STACK CFI d4cc x21: x21
STACK CFI d4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d4ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d510 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d530 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d540 2b4 .cfa: sp 0 + .ra: x30
STACK CFI d544 .cfa: sp 992 +
STACK CFI d548 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI d550 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI d55c x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI d56c x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI d590 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI d6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d6fc .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI d720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d724 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI d7dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d7e0 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI INIT d800 1d8 .cfa: sp 0 + .ra: x30
STACK CFI d804 .cfa: sp 1024 +
STACK CFI d808 .ra: .cfa -1016 + ^ x29: .cfa -1024 + ^
STACK CFI d810 x23: .cfa -976 + ^ x24: .cfa -968 + ^
STACK CFI d820 v8: .cfa -960 + ^
STACK CFI d828 x21: .cfa -992 + ^ x22: .cfa -984 + ^
STACK CFI d834 x19: .cfa -1008 + ^ x20: .cfa -1000 + ^
STACK CFI d940 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d944 .cfa: sp 1024 + .ra: .cfa -1016 + ^ v8: .cfa -960 + ^ x19: .cfa -1008 + ^ x20: .cfa -1000 + ^ x21: .cfa -992 + ^ x22: .cfa -984 + ^ x23: .cfa -976 + ^ x24: .cfa -968 + ^ x29: .cfa -1024 + ^
STACK CFI INIT d9e0 5c .cfa: sp 0 + .ra: x30
STACK CFI d9e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d9ec x19: .cfa -16 + ^
STACK CFI da28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI da2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT da40 50 .cfa: sp 0 + .ra: x30
STACK CFI da44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI da80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT da90 38 .cfa: sp 0 + .ra: x30
STACK CFI da94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT dad0 154 .cfa: sp 0 + .ra: x30
STACK CFI dad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dc20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT dc30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT dc50 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT dc70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dc80 2b4 .cfa: sp 0 + .ra: x30
STACK CFI dc84 .cfa: sp 992 +
STACK CFI dc88 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI dc90 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI dc9c x25: .cfa -928 + ^ x26: .cfa -920 + ^
STACK CFI dcac x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI dcd0 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI de38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI de3c .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI de60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI de64 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI df1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI df20 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x29: .cfa -992 + ^
STACK CFI INIT df40 1d8 .cfa: sp 0 + .ra: x30
STACK CFI df44 .cfa: sp 1024 +
STACK CFI df48 .ra: .cfa -1016 + ^ x29: .cfa -1024 + ^
STACK CFI df50 x23: .cfa -976 + ^ x24: .cfa -968 + ^
STACK CFI df60 v8: .cfa -960 + ^
STACK CFI df68 x21: .cfa -992 + ^ x22: .cfa -984 + ^
STACK CFI df74 x19: .cfa -1008 + ^ x20: .cfa -1000 + ^
STACK CFI e080 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e084 .cfa: sp 1024 + .ra: .cfa -1016 + ^ v8: .cfa -960 + ^ x19: .cfa -1008 + ^ x20: .cfa -1000 + ^ x21: .cfa -992 + ^ x22: .cfa -984 + ^ x23: .cfa -976 + ^ x24: .cfa -968 + ^ x29: .cfa -1024 + ^
STACK CFI INIT e120 5c .cfa: sp 0 + .ra: x30
STACK CFI e124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e12c x19: .cfa -16 + ^
STACK CFI e168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e16c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT e180 50 .cfa: sp 0 + .ra: x30
STACK CFI e184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e1bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e1c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT e1d0 38 .cfa: sp 0 + .ra: x30
STACK CFI e1d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e204 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e210 154 .cfa: sp 0 + .ra: x30
STACK CFI e214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e370 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9680 3c .cfa: sp 0 + .ra: x30
STACK CFI 9684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 968c x19: .cfa -16 + ^
STACK CFI 96b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
