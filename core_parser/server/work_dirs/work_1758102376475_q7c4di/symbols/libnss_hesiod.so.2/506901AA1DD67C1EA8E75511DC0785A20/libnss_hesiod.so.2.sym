MODULE Linux arm64 506901AA1DD67C1EA8E75511DC0785A20 libnss_hesiod.so.2
INFO CODE_ID AA016950D61D1E7CA8E75511DC0785A272B3C432
PUBLIC 1f18 0 _nss_hesiod_setgrent
PUBLIC 1f20 0 _nss_hesiod_endgrent
PUBLIC 1f28 0 _nss_hesiod_getgrnam_r
PUBLIC 1f50 0 _nss_hesiod_getgrgid_r
PUBLIC 1ff0 0 _nss_hesiod_initgroups_dyn
PUBLIC 27e0 0 _nss_hesiod_setprotoent
PUBLIC 27e8 0 _nss_hesiod_endprotoent
PUBLIC 27f0 0 _nss_hesiod_getprotobyname_r
PUBLIC 2818 0 _nss_hesiod_getprotobynumber_r
PUBLIC 2a38 0 _nss_hesiod_setpwent
PUBLIC 2a40 0 _nss_hesiod_endpwent
PUBLIC 2a48 0 _nss_hesiod_getpwnam_r
PUBLIC 2a70 0 _nss_hesiod_getpwuid_r
PUBLIC 2ff8 0 _nss_hesiod_setservent
PUBLIC 3000 0 _nss_hesiod_endservent
PUBLIC 3008 0 _nss_hesiod_getservbyname_r
PUBLIC 3038 0 _nss_hesiod_getservbyport_r
STACK CFI INIT 12a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1318 48 .cfa: sp 0 + .ra: x30
STACK CFI 131c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1324 x19: .cfa -16 + ^
STACK CFI 135c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1368 314 .cfa: sp 0 + .ra: x30
STACK CFI 136c .cfa: sp 1136 +
STACK CFI 1370 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 1378 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 1384 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 1390 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 13e4 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 13f0 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 150c x25: x25 x26: x26
STACK CFI 1510 x27: x27 x28: x28
STACK CFI 1518 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI 15c8 x25: x25 x26: x26
STACK CFI 15d0 x27: x27 x28: x28
STACK CFI 15fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1600 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x28: .cfa -1048 + ^ x29: .cfa -1136 + ^
STACK CFI 1668 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1674 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 1678 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI INIT 1680 324 .cfa: sp 0 + .ra: x30
STACK CFI 1684 .cfa: sp 2192 +
STACK CFI 1698 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI 16a0 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI 16b0 x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI 16bc x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI 16d8 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI 170c x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI 1760 x27: x27 x28: x28
STACK CFI 17ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 17b0 .cfa: sp 2192 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x28: .cfa -2088 + ^ x29: .cfa -2176 + ^
STACK CFI 1930 x27: x27 x28: x28
STACK CFI 1950 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI 1978 x27: x27 x28: x28
STACK CFI 197c x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI 199c x27: x27 x28: x28
STACK CFI 19a0 x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI INIT 19a8 50 .cfa: sp 0 + .ra: x30
STACK CFI 19ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 19f8 108 .cfa: sp 0 + .ra: x30
STACK CFI 19fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a04 x21: .cfa -16 + ^
STACK CFI 1a18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ab4 x19: x19 x20: x20
STACK CFI 1abc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1ae8 x19: x19 x20: x20
STACK CFI 1aec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1af8 x19: x19 x20: x20
STACK CFI INIT 1b00 3c .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1b40 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 1b44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1b4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1b5c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1b68 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1c50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1cf8 9c .cfa: sp 0 + .ra: x30
STACK CFI 1cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d08 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1d98 180 .cfa: sp 0 + .ra: x30
STACK CFI 1d9c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1da4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1db4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1dc4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1de0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1dec x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1eb0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1f18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f28 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f50 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1f54 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1f5c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1f6c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1f8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1fec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1ff0 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 1ff4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1ffc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 2008 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 202c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2054 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2064 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2238 x25: x25 x26: x26
STACK CFI 2240 x27: x27 x28: x28
STACK CFI 2268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 226c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 22fc x25: x25 x26: x26
STACK CFI 2314 x27: x27 x28: x28
STACK CFI 2324 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2388 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 238c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 2390 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 2398 284 .cfa: sp 0 + .ra: x30
STACK CFI 239c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 23a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 23ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 23d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 25ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2620 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 2624 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 262c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 263c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2648 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2658 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2674 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2738 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 27e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27f0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2818 a0 .cfa: sp 0 + .ra: x30
STACK CFI 281c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2824 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2834 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2854 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 28b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 28b8 180 .cfa: sp 0 + .ra: x30
STACK CFI 28bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 28c4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 28d4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 28e4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2900 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 290c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 29cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 29d0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2a38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a48 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a70 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2a74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2a7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2a8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2aac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2b10 2ec .cfa: sp 0 + .ra: x30
STACK CFI 2b14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b1c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2b28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b4c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d98 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2e00 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 2e04 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2e18 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2e24 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2e30 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2e4c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2e58 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f68 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2ff8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3000 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3008 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3038 b4 .cfa: sp 0 + .ra: x30
STACK CFI 303c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3048 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3058 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3078 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3090 x25: .cfa -32 + ^
STACK CFI 30e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 30e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
