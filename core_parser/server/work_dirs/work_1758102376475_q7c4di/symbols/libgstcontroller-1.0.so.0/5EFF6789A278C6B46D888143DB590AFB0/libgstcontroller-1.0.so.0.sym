MODULE Linux arm64 5EFF6789A278C6B46D888143DB590AFB0 libgstcontroller-1.0.so.0
INFO CODE_ID 8967FF5E78A2B4C66D888143DB590AFB90B14866
PUBLIC 36f8 0 gst_argb_control_binding_get_type
PUBLIC 48d8 0 gst_argb_control_binding_new
PUBLIC 5958 0 gst_direct_control_binding_get_type
PUBLIC 66a8 0 gst_direct_control_binding_new
PUBLIC 6700 0 gst_direct_control_binding_new_absolute
PUBLIC 68b8 0 gst_control_point_copy
PUBLIC 6a48 0 gst_control_point_free
PUBLIC 6a70 0 gst_timed_value_control_source_get_type
PUBLIC 6ae0 0 gst_control_point_get_type
PUBLIC 6c98 0 gst_timed_value_control_source_find_control_point_iter
PUBLIC 6cf8 0 gst_timed_value_control_source_set
PUBLIC 6dd0 0 gst_timed_value_control_source_set_from_list
PUBLIC 6ef0 0 gst_timed_value_control_source_unset
PUBLIC 7060 0 gst_timed_value_control_source_unset_all
PUBLIC 70f0 0 gst_timed_value_control_source_get_all
PUBLIC 71d8 0 gst_timed_value_control_source_get_count
PUBLIC 7250 0 gst_timed_value_control_invalidate_cache
PUBLIC 9070 0 gst_interpolation_control_source_get_type
PUBLIC 90e0 0 gst_interpolation_control_source_new
PUBLIC 94a8 0 gst_proxy_control_binding_get_type
PUBLIC 9518 0 gst_proxy_control_binding_new
PUBLIC 9ca0 0 gst_trigger_control_source_get_type
PUBLIC 9d10 0 gst_trigger_control_source_new
PUBLIC abc0 0 gst_lfo_control_source_get_type
PUBLIC ac30 0 gst_lfo_control_source_new
PUBLIC ac60 0 gst_interpolation_mode_get_type
PUBLIC acd0 0 gst_lfo_waveform_get_type
STACK CFI INIT 30d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3108 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3148 48 .cfa: sp 0 + .ra: x30
STACK CFI 314c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3154 x19: .cfa -16 + ^
STACK CFI 318c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3190 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3198 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 31a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3208 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 322c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3230 168 .cfa: sp 0 + .ra: x30
STACK CFI 3234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 323c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3398 38 .cfa: sp 0 + .ra: x30
STACK CFI 339c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33a4 x19: .cfa -16 + ^
STACK CFI 33c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 33d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33e0 x19: .cfa -16 + ^
STACK CFI 3424 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3430 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 343c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 34e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 34e8 118 .cfa: sp 0 + .ra: x30
STACK CFI 34ec .cfa: sp 64 +
STACK CFI 34f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 353c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 355c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3560 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3564 x21: .cfa -16 + ^
STACK CFI 35c0 x21: x21
STACK CFI 35c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35c8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 35e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35e4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 35fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3600 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3604 .cfa: sp 64 +
STACK CFI 360c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3614 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 364c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3668 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 366c x21: .cfa -16 + ^
STACK CFI 36c8 x21: x21
STACK CFI 36cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36d0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 36e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36e4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 36f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 36f8 6c .cfa: sp 0 + .ra: x30
STACK CFI 36fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3704 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3734 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3768 4d0 .cfa: sp 0 + .ra: x30
STACK CFI 376c .cfa: sp 160 +
STACK CFI 3770 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3778 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3784 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 378c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 37e0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 37e8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 38d8 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 38e8 v10: .cfa -16 + ^
STACK CFI 3a08 v10: v10 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3a44 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3b5c x21: x21 x22: x22
STACK CFI 3b60 x23: x23 x24: x24
STACK CFI 3b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3b70 .cfa: sp 160 + .ra: .cfa -120 + ^ v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3b74 v8: v8 v9: v9
STACK CFI 3b78 v10: v10
STACK CFI 3b7c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3bb8 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3bf4 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c38 4b4 .cfa: sp 0 + .ra: x30
STACK CFI 3c3c .cfa: sp 128 +
STACK CFI 3c40 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3c48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3c50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3c5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3c68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3cb4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3ef0 x25: x25 x26: x26
STACK CFI 3ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3efc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3f3c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3f7c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3fbc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4000 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 40f0 39c .cfa: sp 0 + .ra: x30
STACK CFI 40f4 .cfa: sp 128 +
STACK CFI 40fc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4104 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4114 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4298 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4490 448 .cfa: sp 0 + .ra: x30
STACK CFI 4494 .cfa: sp 160 +
STACK CFI 449c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 44a4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 44b4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 44bc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 44c4 x25: .cfa -64 + ^
STACK CFI 4560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4564 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 48d8 98 .cfa: sp 0 + .ra: x30
STACK CFI 48dc .cfa: sp 112 +
STACK CFI 48e0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 48e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 48f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4900 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 496c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4970 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 49d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 49e0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a50 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a88 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae8 90 .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4af4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b78 110 .cfa: sp 0 + .ra: x30
STACK CFI 4b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4c88 68 .cfa: sp 0 + .ra: x30
STACK CFI 4c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4cf0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4cfc x19: .cfa -32 + ^
STACK CFI 4d30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d38 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d70 394 .cfa: sp 0 + .ra: x30
STACK CFI 4d74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4da0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ddc x23: .cfa -16 + ^
STACK CFI 4e1c x23: x23
STACK CFI 4ea4 x19: x19 x20: x20
STACK CFI 4eb0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4eb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4ee4 x19: x19 x20: x20
STACK CFI 4ee8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f98 x19: x19 x20: x20
STACK CFI 4fa0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4fa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5000 x19: x19 x20: x20
STACK CFI 5004 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 5108 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5160 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51d0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5230 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5240 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52a8 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5310 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5378 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5388 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5408 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5410 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5478 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5488 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5500 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5568 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5578 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55f0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5658 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5668 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56e0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5748 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5758 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57d0 bc .cfa: sp 0 + .ra: x30
STACK CFI 57d4 .cfa: sp 64 +
STACK CFI 57dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 580c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5810 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5814 x21: .cfa -16 + ^
STACK CFI 5870 x21: x21
STACK CFI 5874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5878 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5890 c4 .cfa: sp 0 + .ra: x30
STACK CFI 5894 .cfa: sp 64 +
STACK CFI 589c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58d4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 58d8 x21: .cfa -16 + ^
STACK CFI 5934 x21: x21
STACK CFI 5938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 593c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5958 6c .cfa: sp 0 + .ra: x30
STACK CFI 595c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5964 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5994 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 59c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59c8 38c .cfa: sp 0 + .ra: x30
STACK CFI 59cc .cfa: sp 128 +
STACK CFI 59d0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 59d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 59e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 59ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 59f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5a44 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5b34 x25: x25 x26: x26
STACK CFI 5b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5b54 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5b94 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5c88 x25: x25 x26: x26
STACK CFI 5cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5ccc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 5d0c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5d58 364 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 128 +
STACK CFI 5d60 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d68 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5d74 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5dd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5ddc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5e64 x25: x25 x26: x26
STACK CFI 5e68 x27: x27 x28: x28
STACK CFI 5e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5e84 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 5ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ec0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5fb4 x25: x25 x26: x26
STACK CFI 5fb8 x27: x27 x28: x28
STACK CFI 5ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ff8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6034 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6078 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 60c0 238 .cfa: sp 0 + .ra: x30
STACK CFI 60c4 .cfa: sp 96 +
STACK CFI 60c8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 627c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6280 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 62f8 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 62fc .cfa: sp 144 +
STACK CFI 6300 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6308 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6314 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6320 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6340 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 64c4 x25: x25 x26: x26
STACK CFI 64c8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 64e4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6578 x27: x27 x28: x28
STACK CFI 65a8 x25: x25 x26: x26
STACK CFI 65ac x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 65b0 x25: x25 x26: x26
STACK CFI 6600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6604 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 6628 x25: x25 x26: x26
STACK CFI 662c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 664c x25: x25 x26: x26
STACK CFI 6650 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6664 x25: x25 x26: x26
STACK CFI 6668 x27: x27 x28: x28
STACK CFI 666c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 668c x25: x25 x26: x26
STACK CFI 6690 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6694 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 66a0 x27: x27 x28: x28
STACK CFI INIT 66a8 58 .cfa: sp 0 + .ra: x30
STACK CFI 66ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66c0 x21: .cfa -16 + ^
STACK CFI 66ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6700 74 .cfa: sp 0 + .ra: x30
STACK CFI 6704 .cfa: sp 64 +
STACK CFI 6708 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6710 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 671c x21: .cfa -16 + ^
STACK CFI 6770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6778 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6798 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 67b8 90 .cfa: sp 0 + .ra: x30
STACK CFI 67bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 681c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6820 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6848 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6850 64 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 685c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 68a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 68b8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 68c8 16c .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 68d4 v8: .cfa -24 + ^
STACK CFI 68dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 68e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 691c x23: .cfa -32 + ^
STACK CFI 697c x23: x23
STACK CFI 6990 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6994 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 69d4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 69d8 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 69e0 x23: .cfa -32 + ^
STACK CFI INIT 6a38 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a48 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6a70 6c .cfa: sp 0 + .ra: x30
STACK CFI 6a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6aac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6ae0 88 .cfa: sp 0 + .ra: x30
STACK CFI 6ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6aec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6b68 130 .cfa: sp 0 + .ra: x30
STACK CFI 6b6c .cfa: sp 80 +
STACK CFI 6b70 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b78 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b88 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6c88 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c98 5c .cfa: sp 0 + .ra: x30
STACK CFI 6c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cc0 x19: .cfa -32 + ^
STACK CFI 6cdc x19: x19
STACK CFI 6ce0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 6ce8 x19: x19
STACK CFI 6cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6cf8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d04 v8: .cfa -16 + ^
STACK CFI 6d0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d68 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 6d6c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6d98 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 6d9c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6dc8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 6dd0 120 .cfa: sp 0 + .ra: x30
STACK CFI 6dd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6ddc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6de8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e20 x23: .cfa -16 + ^
STACK CFI 6eb4 x23: x23
STACK CFI 6eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ebc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6ef0 16c .cfa: sp 0 + .ra: x30
STACK CFI 6ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6efc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fd4 x21: x21 x22: x22
STACK CFI 6fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6fe8 x21: x21 x22: x22
STACK CFI 6ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ff8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6ffc x21: x21 x22: x22
STACK CFI 7024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7028 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 704c x21: x21 x22: x22
STACK CFI 7050 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7058 x21: x21 x22: x22
STACK CFI INIT 7060 90 .cfa: sp 0 + .ra: x30
STACK CFI 7064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 706c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 70d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 70f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 70fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7128 x21: .cfa -48 + ^
STACK CFI 717c x21: x21
STACK CFI 71a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 71a8 x21: x21
STACK CFI 71d0 x21: .cfa -48 + ^
STACK CFI INIT 71d8 78 .cfa: sp 0 + .ra: x30
STACK CFI 71dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71e4 x19: .cfa -16 + ^
STACK CFI 7220 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7224 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 724c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7250 70 .cfa: sp 0 + .ra: x30
STACK CFI 7254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 725c x19: .cfa -16 + ^
STACK CFI 7298 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 729c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 72a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 72c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7338 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7368 68 .cfa: sp 0 + .ra: x30
STACK CFI 736c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7374 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 73cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 73d0 250 .cfa: sp 0 + .ra: x30
STACK CFI 73d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 73e0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 73f0 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 73fc v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 761c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 7620 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 7624 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7634 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 764c v10: .cfa -48 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 78d4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 78d8 .cfa: sp 160 + .ra: .cfa -152 + ^ v10: .cfa -48 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 78e8 88 .cfa: sp 0 + .ra: x30
STACK CFI 78ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78f4 x19: .cfa -16 + ^
STACK CFI 7960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7964 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7970 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7974 .cfa: sp 64 +
STACK CFI 797c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7984 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7990 x21: .cfa -16 + ^
STACK CFI 79f0 x21: x21
STACK CFI 79f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79f8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7a18 148 .cfa: sp 0 + .ra: x30
STACK CFI 7a1c .cfa: sp 64 +
STACK CFI 7a24 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a38 x21: .cfa -16 + ^
STACK CFI 7a90 x21: x21
STACK CFI 7a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7aa0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b08 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7b0c x21: .cfa -16 + ^
STACK CFI 7b58 x21: x21
STACK CFI 7b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7b60 60 .cfa: sp 0 + .ra: x30
STACK CFI 7b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7bc0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 7bc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7bcc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7bd8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7c18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 7c20 x23: .cfa -16 + ^
STACK CFI 7c98 x23: x23
STACK CFI 7cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7cb0 178 .cfa: sp 0 + .ra: x30
STACK CFI 7cc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7cc8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7cd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7d24 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 7d5c v8: v8 v9: v9
STACK CFI 7d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d80 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7e10 v8: v8 v9: v9
STACK CFI 7e14 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 7e28 148 .cfa: sp 0 + .ra: x30
STACK CFI 7e38 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7e40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7e58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7e98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7e9c v8: .cfa -16 + ^
STACK CFI 7ee4 v8: v8
STACK CFI 7f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7f08 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7f58 v8: v8
STACK CFI 7f5c v8: .cfa -16 + ^
STACK CFI INIT 7f70 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 7f74 .cfa: sp 192 +
STACK CFI 7f78 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7f80 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7f88 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7f98 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7fcc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 829c x27: x27 x28: x28
STACK CFI 82c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 82c4 .cfa: sp 192 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8330 x27: x27 x28: x28
STACK CFI INIT 8338 444 .cfa: sp 0 + .ra: x30
STACK CFI 833c .cfa: sp 208 +
STACK CFI 8340 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8348 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8350 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 835c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8384 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 83a0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 86cc x25: x25 x26: x26
STACK CFI 86d0 x27: x27 x28: x28
STACK CFI 86f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 86f4 .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8774 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 8780 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 8784 .cfa: sp 208 +
STACK CFI 8788 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 87a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 87ac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 87b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 87d0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 87e8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 87fc v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 8b64 x25: x25 x26: x26
STACK CFI 8b68 x27: x27 x28: x28
STACK CFI 8b6c v8: v8 v9: v9
STACK CFI 8b80 x19: x19 x20: x20
STACK CFI 8b84 x21: x21 x22: x22
STACK CFI 8b88 x23: x23 x24: x24
STACK CFI 8b8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8b90 .cfa: sp 208 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8be8 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8bf4 .cfa: sp 208 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8c20 v8: v8 v9: v9 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 8c28 444 .cfa: sp 0 + .ra: x30
STACK CFI 8c38 .cfa: sp 208 +
STACK CFI 8c3c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8c44 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8c50 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8c58 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8c64 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 8c88 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8ca8 v8: .cfa -48 + ^
STACK CFI 8f9c x27: x27 x28: x28
STACK CFI 8fa0 v8: v8
STACK CFI 8fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8fc8 .cfa: sp 208 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9020 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9024 .cfa: sp 208 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9064 v8: v8 x27: x27 x28: x28
STACK CFI INIT 9070 6c .cfa: sp 0 + .ra: x30
STACK CFI 9074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 907c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 90a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 90d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 90e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 90e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90ec x19: .cfa -16 + ^
STACK CFI 910c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9110 50 .cfa: sp 0 + .ra: x30
STACK CFI 9114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 911c x19: .cfa -16 + ^
STACK CFI 9144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9160 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9170 78 .cfa: sp 0 + .ra: x30
STACK CFI 9174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 917c x19: .cfa -16 + ^
STACK CFI 91d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 91d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 91e8 40 .cfa: sp 0 + .ra: x30
STACK CFI 91ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91f4 x19: .cfa -16 + ^
STACK CFI 921c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9228 b0 .cfa: sp 0 + .ra: x30
STACK CFI 922c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9234 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9240 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 924c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9260 x25: .cfa -16 + ^
STACK CFI 92b4 x25: x25
STACK CFI 92b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 92bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 92d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 92d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 92dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 92e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 92f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 92fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9310 x25: .cfa -16 + ^
STACK CFI 9364 x25: x25
STACK CFI 9368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 936c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9388 84 .cfa: sp 0 + .ra: x30
STACK CFI 938c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9394 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 93a0 x21: .cfa -16 + ^
STACK CFI 93f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 93f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9410 98 .cfa: sp 0 + .ra: x30
STACK CFI 9414 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 941c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9428 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 943c x23: .cfa -16 + ^
STACK CFI 9488 x23: x23
STACK CFI 948c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9490 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 94a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 94a8 6c .cfa: sp 0 + .ra: x30
STACK CFI 94ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 94e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 94e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9518 174 .cfa: sp 0 + .ra: x30
STACK CFI 951c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9530 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 95c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 95f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 964c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9690 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96b8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 96bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 972c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9760 f0 .cfa: sp 0 + .ra: x30
STACK CFI 9764 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 976c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9778 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9788 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 97ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 97f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9834 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9850 88 .cfa: sp 0 + .ra: x30
STACK CFI 9854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 985c x19: .cfa -16 + ^
STACK CFI 98c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 98cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 98d8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 98dc .cfa: sp 64 +
STACK CFI 98e4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 98ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 98f8 x21: .cfa -16 + ^
STACK CFI 9958 x21: x21
STACK CFI 995c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9960 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9980 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9984 .cfa: sp 64 +
STACK CFI 998c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9994 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 999c x21: .cfa -16 + ^
STACK CFI 9a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9a08 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9a48 258 .cfa: sp 0 + .ra: x30
STACK CFI 9a4c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9a54 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9a60 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9a68 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9a74 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 9aa4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9aac v8: .cfa -32 + ^
STACK CFI 9b18 x27: x27 x28: x28
STACK CFI 9b1c v8: v8
STACK CFI 9b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9b40 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 9c48 v8: v8
STACK CFI 9c5c x27: x27 x28: x28
STACK CFI 9c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9c64 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 9c88 v8: v8 x27: x27 x28: x28
STACK CFI 9c90 v8: .cfa -32 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 9ca0 6c .cfa: sp 0 + .ra: x30
STACK CFI 9ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9d10 30 .cfa: sp 0 + .ra: x30
STACK CFI 9d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d1c x19: .cfa -16 + ^
STACK CFI 9d3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9d40 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9d44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9de8 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e68 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9e6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9e74 x25: .cfa -16 + ^
STACK CFI 9e80 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9e90 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ea0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9eac v8: .cfa -8 + ^
STACK CFI 9f24 x19: x19 x20: x20
STACK CFI 9f28 x21: x21 x22: x22
STACK CFI 9f2c x23: x23 x24: x24
STACK CFI 9f30 v8: v8
STACK CFI 9f3c .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI INIT 9f40 94 .cfa: sp 0 + .ra: x30
STACK CFI 9f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9f54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9fd8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9fdc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9fe4 x25: .cfa -16 + ^
STACK CFI 9ff0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a000 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a010 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a01c v8: .cfa -8 + ^
STACK CFI a094 x19: x19 x20: x20
STACK CFI a098 x21: x21 x22: x22
STACK CFI a09c x23: x23 x24: x24
STACK CFI a0a0 v8: v8
STACK CFI a0ac .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI INIT a0b0 94 .cfa: sp 0 + .ra: x30
STACK CFI a0b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a0bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a0c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a148 c4 .cfa: sp 0 + .ra: x30
STACK CFI a14c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a154 x25: .cfa -16 + ^
STACK CFI a160 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a170 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a180 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a1f4 x19: x19 x20: x20
STACK CFI a1f8 x21: x21 x22: x22
STACK CFI a1fc x23: x23 x24: x24
STACK CFI a208 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI INIT a210 88 .cfa: sp 0 + .ra: x30
STACK CFI a214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a21c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a224 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a298 fc .cfa: sp 0 + .ra: x30
STACK CFI a29c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a2a4 x25: .cfa -48 + ^
STACK CFI a2b0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a2bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a2d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a2e4 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI a2f0 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI a374 x19: x19 x20: x20
STACK CFI a378 x21: x21 x22: x22
STACK CFI a37c x23: x23 x24: x24
STACK CFI a380 v8: v8 v9: v9
STACK CFI a384 v10: v10 v11: v11
STACK CFI a390 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI INIT a398 b0 .cfa: sp 0 + .ra: x30
STACK CFI a39c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a3a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a3ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a3b8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI a444 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a448 18c .cfa: sp 0 + .ra: x30
STACK CFI a44c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a454 x19: .cfa -16 + ^
STACK CFI a5c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a5c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a5d8 128 .cfa: sp 0 + .ra: x30
STACK CFI a5dc .cfa: sp 64 +
STACK CFI a5e4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a5ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a628 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a650 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a668 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a680 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a698 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a69c x21: .cfa -16 + ^
STACK CFI a6f8 x21: x21
STACK CFI a6fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a700 3c .cfa: sp 0 + .ra: x30
STACK CFI a704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a70c x19: .cfa -16 + ^
STACK CFI a730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a740 d0 .cfa: sp 0 + .ra: x30
STACK CFI a744 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a74c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a754 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a7e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a7e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a810 28c .cfa: sp 0 + .ra: x30
STACK CFI a814 .cfa: sp 64 +
STACK CFI a81c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a82c x21: .cfa -16 + ^
STACK CFI a87c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a880 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a898 v8: .cfa -8 + ^
STACK CFI a8e8 v8: v8
STACK CFI a8ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a8f0 .cfa: sp 64 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a90c v8: v8
STACK CFI a914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a920 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a954 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a9ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a9b0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI aa10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aa14 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI aa48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aa4c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT aaa0 11c .cfa: sp 0 + .ra: x30
STACK CFI aaa4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI aaac x25: .cfa -48 + ^
STACK CFI aab8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI aac8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI aad8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI aae4 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI aaf0 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI ab78 x19: x19 x20: x20
STACK CFI ab7c x21: x21 x22: x22
STACK CFI ab80 x23: x23 x24: x24
STACK CFI ab84 v8: v8 v9: v9
STACK CFI ab88 v10: v10 v11: v11
STACK CFI ab94 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI ab98 .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT abc0 6c .cfa: sp 0 + .ra: x30
STACK CFI abc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI abcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI abf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI abfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ac28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ac30 30 .cfa: sp 0 + .ra: x30
STACK CFI ac34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac3c x19: .cfa -16 + ^
STACK CFI ac5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac60 70 .cfa: sp 0 + .ra: x30
STACK CFI ac64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ac6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ac90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI accc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT acd0 80 .cfa: sp 0 + .ra: x30
STACK CFI acd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI acdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ad08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ad0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ad4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
