MODULE Linux arm64 8C2CE600E7BD809C705EA843CB2BDB220 libthread_db.so.1
INFO CODE_ID 00E62C8CBDE79C80705EA843CB2BDB22A662AE58
PUBLIC 1a18 0 td_init
PUBLIC 1a58 0 td_log
PUBLIC 1a98 0 td_ta_new
PUBLIC 1bd8 0 td_ta_delete
PUBLIC 1c70 0 td_ta_get_nthreads
PUBLIC 1d80 0 td_ta_get_ph
PUBLIC 1e10 0 td_ta_map_id2thr
PUBLIC 2160 0 td_ta_map_lwp2thr
PUBLIC 2530 0 td_ta_thr_iter
PUBLIC 2680 0 td_ta_tsd_iter
PUBLIC 28c0 0 td_thr_get_info
PUBLIC 2c90 0 td_thr_getfpregs
PUBLIC 2db8 0 td_thr_getgregs
PUBLIC 2ec8 0 td_thr_getxregs
PUBLIC 2f08 0 td_thr_getxregsize
PUBLIC 2f48 0 td_thr_setfpregs
PUBLIC 3058 0 td_thr_setgregs
PUBLIC 3168 0 td_thr_setprio
PUBLIC 31a8 0 td_thr_setsigpending
PUBLIC 31e8 0 td_thr_setxregs
PUBLIC 3228 0 td_thr_sigsetmask
PUBLIC 3268 0 td_thr_tsd
PUBLIC 3638 0 td_thr_validate
PUBLIC 3790 0 td_thr_dbsuspend
PUBLIC 37d0 0 td_thr_dbresume
PUBLIC 3810 0 td_ta_setconcurrency
PUBLIC 3890 0 td_ta_enable_stats
PUBLIC 3910 0 td_ta_reset_stats
PUBLIC 3990 0 td_ta_get_stats
PUBLIC 3a10 0 td_ta_event_addr
PUBLIC 3b30 0 td_thr_event_enable
PUBLIC 3bf8 0 td_thr_set_event
PUBLIC 3e08 0 td_thr_clear_event
PUBLIC 4018 0 td_thr_event_getmsg
PUBLIC 43b0 0 td_ta_set_event
PUBLIC 4600 0 td_ta_event_getmsg
PUBLIC 4930 0 td_ta_clear_event
PUBLIC 4b80 0 td_symbol_list
PUBLIC 4bd0 0 td_thr_tlsbase
PUBLIC 4fd8 0 td_thr_tls_get_addr
STACK CFI INIT 1958 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1988 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 19cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19d4 x19: .cfa -16 + ^
STACK CFI 1a0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a18 3c .cfa: sp 0 + .ra: x30
STACK CFI 1a30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a58 3c .cfa: sp 0 + .ra: x30
STACK CFI 1a70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a98 140 .cfa: sp 0 + .ra: x30
STACK CFI 1a9c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1aa8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1ab4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1af8 x23: .cfa -48 + ^
STACK CFI 1b2c x23: x23
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 1b64 x23: x23
STACK CFI 1b80 x23: .cfa -48 + ^
STACK CFI 1bc0 x23: x23
STACK CFI 1bc8 x23: .cfa -48 + ^
STACK CFI 1bd4 x23: x23
STACK CFI INIT 1bd8 94 .cfa: sp 0 + .ra: x30
STACK CFI 1bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1be8 x19: .cfa -16 + ^
STACK CFI 1c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1c70 10c .cfa: sp 0 + .ra: x30
STACK CFI 1c74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1c90 x21: .cfa -32 + ^
STACK CFI 1d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1d80 8c .cfa: sp 0 + .ra: x30
STACK CFI 1d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e10 98 .cfa: sp 0 + .ra: x30
STACK CFI 1e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e30 x21: .cfa -16 + ^
STACK CFI 1e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ea8 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 1eac .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1eb4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1ec8 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 1f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f24 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI 1f2c x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 1fac x23: x23 x24: x24
STACK CFI 1fc0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 1fd8 x23: x23 x24: x24
STACK CFI 1fe0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2014 x23: x23 x24: x24
STACK CFI 205c x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2094 x23: x23 x24: x24
STACK CFI 2098 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 20c4 x23: x23 x24: x24
STACK CFI 20d8 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 20e8 x23: x23 x24: x24
STACK CFI 20ec x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 20f4 x23: x23 x24: x24
STACK CFI 20f8 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 20fc x23: x23 x24: x24
STACK CFI 2100 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2124 x23: x23 x24: x24
STACK CFI 2128 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2150 x23: x23 x24: x24
STACK CFI 2158 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI INIT 2160 14c .cfa: sp 0 + .ra: x30
STACK CFI 2164 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2170 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2180 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2194 x23: .cfa -32 + ^
STACK CFI 2240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2244 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 22b0 27c .cfa: sp 0 + .ra: x30
STACK CFI 22b4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 22b8 .cfa: x29 192 +
STACK CFI 22bc x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 22e8 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 2324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2328 .cfa: x29 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2530 14c .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2544 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2550 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 255c x23: .cfa -16 + ^
STACK CFI 25e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 25ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 261c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2620 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2638 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2680 240 .cfa: sp 0 + .ra: x30
STACK CFI 2684 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 268c .cfa: x29 144 +
STACK CFI 2690 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 26a0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 26b4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 26c4 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2868 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 28c0 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 28c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 28cc .cfa: x29 144 +
STACK CFI 28d0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 28e0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2900 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 2a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2a18 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2c90 128 .cfa: sp 0 + .ra: x30
STACK CFI 2c94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2ca0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2cb0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2db8 110 .cfa: sp 0 + .ra: x30
STACK CFI 2dbc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2dc8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2dd8 x21: .cfa -48 + ^
STACK CFI 2e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2ec8 3c .cfa: sp 0 + .ra: x30
STACK CFI 2ee0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f08 3c .cfa: sp 0 + .ra: x30
STACK CFI 2f20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f48 10c .cfa: sp 0 + .ra: x30
STACK CFI 2f4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f58 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f68 x21: .cfa -48 + ^
STACK CFI 2fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fd0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3058 10c .cfa: sp 0 + .ra: x30
STACK CFI 305c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3068 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3078 x21: .cfa -48 + ^
STACK CFI 30dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 30e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3168 3c .cfa: sp 0 + .ra: x30
STACK CFI 3180 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 31a8 3c .cfa: sp 0 + .ra: x30
STACK CFI 31c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 31e8 3c .cfa: sp 0 + .ra: x30
STACK CFI 3200 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3228 3c .cfa: sp 0 + .ra: x30
STACK CFI 3240 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3260 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3268 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 326c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3274 .cfa: x29 128 +
STACK CFI 3278 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3288 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 32ac x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI 3340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3344 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3510 124 .cfa: sp 0 + .ra: x30
STACK CFI 3514 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3520 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 352c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 353c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3560 x25: .cfa -48 + ^
STACK CFI 3614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3618 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3638 158 .cfa: sp 0 + .ra: x30
STACK CFI 363c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3648 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 367c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36a0 x21: x21 x22: x22
STACK CFI 36c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 36f8 x21: x21 x22: x22
STACK CFI 374c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3758 x21: x21 x22: x22
STACK CFI 3760 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3788 x21: x21 x22: x22
STACK CFI 378c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 3790 3c .cfa: sp 0 + .ra: x30
STACK CFI 37a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 37e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3808 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3810 80 .cfa: sp 0 + .ra: x30
STACK CFI 3814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3820 x19: .cfa -16 + ^
STACK CFI 3864 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3868 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3878 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3890 80 .cfa: sp 0 + .ra: x30
STACK CFI 3894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38a0 x19: .cfa -16 + ^
STACK CFI 38e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 38e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 38f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 38f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3910 80 .cfa: sp 0 + .ra: x30
STACK CFI 3914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3920 x19: .cfa -16 + ^
STACK CFI 3964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3968 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3974 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3978 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3990 80 .cfa: sp 0 + .ra: x30
STACK CFI 3994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39a0 x19: .cfa -16 + ^
STACK CFI 39e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 39e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 39f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 39f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3a10 120 .cfa: sp 0 + .ra: x30
STACK CFI 3a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a30 x21: .cfa -16 + ^
STACK CFI 3a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3aac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3b30 c8 .cfa: sp 0 + .ra: x30
STACK CFI 3b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ba8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3bf8 210 .cfa: sp 0 + .ra: x30
STACK CFI 3bfc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3c04 .cfa: x29 96 +
STACK CFI 3c08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3c18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3c34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d74 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3e08 210 .cfa: sp 0 + .ra: x30
STACK CFI 3e0c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3e14 .cfa: x29 96 +
STACK CFI 3e18 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3e28 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3e44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3f84 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4018 394 .cfa: sp 0 + .ra: x30
STACK CFI 401c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4024 .cfa: x29 128 +
STACK CFI 4028 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 403c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4054 x23: .cfa -80 + ^
STACK CFI 423c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4240 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 43b0 250 .cfa: sp 0 + .ra: x30
STACK CFI 43b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 43bc .cfa: x29 96 +
STACK CFI 43c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 43cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 43f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 454c .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4600 32c .cfa: sp 0 + .ra: x30
STACK CFI 4604 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 460c .cfa: x29 112 +
STACK CFI 4610 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4624 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 463c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4708 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4930 250 .cfa: sp 0 + .ra: x30
STACK CFI 4934 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 493c .cfa: x29 96 +
STACK CFI 4940 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 494c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4970 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4acc .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4b80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b90 40 .cfa: sp 0 + .ra: x30
STACK CFI 4bac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4bd0 404 .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4bd8 .cfa: x29 176 +
STACK CFI 4bdc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4be8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4c0c x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4d00 .cfa: x29 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4fd8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fe8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ff8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5058 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5088 c4 .cfa: sp 0 + .ra: x30
STACK CFI 508c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5094 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 50b4 x21: .cfa -32 + ^
STACK CFI 50e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5150 13c .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 515c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 516c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 51e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 51e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 51e8 x23: .cfa -32 + ^
STACK CFI 521c x23: x23
STACK CFI 5230 x23: .cfa -32 + ^
STACK CFI 5264 x23: x23
STACK CFI 5274 x23: .cfa -32 + ^
STACK CFI 5278 x23: x23
STACK CFI 527c x23: .cfa -32 + ^
STACK CFI 5280 x23: x23
STACK CFI 5288 x23: .cfa -32 + ^
STACK CFI INIT 5290 158 .cfa: sp 0 + .ra: x30
STACK CFI 5294 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 529c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 52ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 537c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5380 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 53e8 144 .cfa: sp 0 + .ra: x30
STACK CFI 53ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 53f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5404 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 54cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 54d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5530 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 553c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 557c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5580 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 55f8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 55fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5604 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5644 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
