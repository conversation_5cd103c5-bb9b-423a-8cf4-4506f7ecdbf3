MODULE Linux arm64 CCFE72FBF7C048FA488EA0E9B09FDAE90 libdebconfclient.so.0
INFO CODE_ID FB72FECCC0F7FA48488EA0E9B09FDAE989640A28
PUBLIC ad0 0 debconfclient_init
PUBLIC ea8 0 debconfclient_new
PUBLIC f68 0 debconfclient_delete
STACK CFI INIT ae8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT b18 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT b58 48 .cfa: sp 0 + .ra: x30
STACK CFI b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b64 x19: .cfa -16 + ^
STACK CFI b9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb0 11c .cfa: sp 0 + .ra: x30
STACK CFI bb4 .cfa: sp 2112 +
STACK CFI bbc .ra: .cfa -2104 + ^ x29: .cfa -2112 + ^
STACK CFI bc8 x19: .cfa -2096 + ^ x20: .cfa -2088 + ^
STACK CFI be8 x21: .cfa -2080 + ^
STACK CFI c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c88 .cfa: sp 2112 + .ra: .cfa -2104 + ^ x19: .cfa -2096 + ^ x20: .cfa -2088 + ^ x21: .cfa -2080 + ^ x29: .cfa -2112 + ^
STACK CFI INIT cd0 dc .cfa: sp 0 + .ra: x30
STACK CFI cd4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI cec x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI da8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT db0 f8 .cfa: sp 0 + .ra: x30
STACK CFI db4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI dbc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI dcc x21: .cfa -112 + ^
STACK CFI ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ea4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x29: .cfa -144 + ^
STACK CFI INIT ad0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ea8 bc .cfa: sp 0 + .ra: x30
STACK CFI eac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ebc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f68 28 .cfa: sp 0 + .ra: x30
STACK CFI f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f74 x19: .cfa -16 + ^
STACK CFI f8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
