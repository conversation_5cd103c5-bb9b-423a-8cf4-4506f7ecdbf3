MODULE Linux arm64 19EA664D2798C13007EB607FC1543B6E0 libnss_files.so.2
INFO CODE_ID 4D66EA19982730C107EB607FC1543B6ECBBC3294
PUBLIC 2598 0 _nss_files_parse_protoent
PUBLIC 29d8 0 _nss_files_setprotoent
PUBLIC 2a30 0 _nss_files_endprotoent
PUBLIC 2a90 0 _nss_files_getprotoent_r
PUBLIC 2b68 0 _nss_files_getprotobyname_r
PUBLIC 2c60 0 _nss_files_getprotobynumber_r
PUBLIC 2da0 0 _nss_files_parse_servent
PUBLIC 3218 0 _nss_files_setservent
PUBLIC 3270 0 _nss_files_endservent
PUBLIC 32d0 0 _nss_files_getservent_r
PUBLIC 33a8 0 _nss_files_getservbyname_r
PUBLIC 34c0 0 _nss_files_getservbyport_r
PUBLIC 41b0 0 _nss_files_sethostent
PUBLIC 4208 0 _nss_files_endhostent
PUBLIC 4268 0 _nss_files_gethostent_r
PUBLIC 4348 0 _nss_files_gethostbyaddr_r
PUBLIC 4460 0 _nss_files_gethostbyname3_r
PUBLIC 4608 0 _nss_files_gethostbyname_r
PUBLIC 4650 0 _nss_files_gethostbyname2_r
PUBLIC 4678 0 _nss_files_gethostbyname4_r
PUBLIC 49e8 0 _nss_files_parse_netent
PUBLIC 4f20 0 _nss_files_setnetent
PUBLIC 4f78 0 _nss_files_endnetent
PUBLIC 4fd8 0 _nss_files_getnetent_r
PUBLIC 50b8 0 _nss_files_getnetbyname_r
PUBLIC 51c0 0 _nss_files_getnetbyaddr_r
PUBLIC 54e0 0 _nss_files_setgrent
PUBLIC 5538 0 _nss_files_endgrent
PUBLIC 5598 0 _nss_files_getgrent_r
PUBLIC 5670 0 _nss_files_getgrnam_r
PUBLIC 5760 0 _nss_files_getgrgid_r
PUBLIC 5a78 0 _nss_files_setpwent
PUBLIC 5ad0 0 _nss_files_endpwent
PUBLIC 5b30 0 _nss_files_getpwent_r
PUBLIC 5c08 0 _nss_files_getpwnam_r
PUBLIC 5cf8 0 _nss_files_getpwuid_r
PUBLIC 5e58 0 _nss_files_parse_etherent
PUBLIC 61c8 0 _nss_files_setetherent
PUBLIC 6220 0 _nss_files_endetherent
PUBLIC 6280 0 _nss_files_getetherent_r
PUBLIC 6358 0 _nss_files_gethostton_r
PUBLIC 6430 0 _nss_files_getntohost_r
PUBLIC 6748 0 _nss_files_setspent
PUBLIC 67a0 0 _nss_files_endspent
PUBLIC 6800 0 _nss_files_getspent_r
PUBLIC 68d8 0 _nss_files_getspnam_r
PUBLIC 6a30 0 _nss_files_setnetgrent
PUBLIC 6da8 0 _nss_files_endnetgrent
PUBLIC 6dd8 0 _nss_netgroup_parseline
PUBLIC 6fe8 0 _nss_files_getnetgrent_r
PUBLIC 75b0 0 _nss_files_setaliasent
PUBLIC 7608 0 _nss_files_endaliasent
PUBLIC 7668 0 _nss_files_getaliasent_r
PUBLIC 7738 0 _nss_files_getaliasbyname_r
PUBLIC 7a68 0 _nss_files_setsgent
PUBLIC 7ac0 0 _nss_files_endsgent
PUBLIC 7b20 0 _nss_files_getsgent_r
PUBLIC 7bf8 0 _nss_files_getsgnam_r
PUBLIC 7ee0 0 _nss_files_getpublickey
PUBLIC 7ee8 0 _nss_files_getsecretkey
PUBLIC 8028 0 _nss_files_parse_rpcent
PUBLIC 8468 0 _nss_files_setrpcent
PUBLIC 84c0 0 _nss_files_endrpcent
PUBLIC 8520 0 _nss_files_getrpcent_r
PUBLIC 85f8 0 _nss_files_getrpcbyname_r
PUBLIC 86f0 0 _nss_files_getrpcbynumber_r
PUBLIC 87c0 0 _nss_files_initgroups_dyn
PUBLIC 8a98 0 _nss_files_init
STACK CFI INIT 2468 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2498 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 24dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24e4 x19: .cfa -16 + ^
STACK CFI 251c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2520 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2528 70 .cfa: sp 0 + .ra: x30
STACK CFI 252c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2534 x19: .cfa -16 + ^
STACK CFI 2550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2598 284 .cfa: sp 0 + .ra: x30
STACK CFI 259c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 25a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 25d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 27ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2820 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 2824 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2830 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2838 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2844 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2850 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 285c x27: .cfa -16 + ^
STACK CFI 28fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2900 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 293c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2940 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2964 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2988 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 29d8 58 .cfa: sp 0 + .ra: x30
STACK CFI 29dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2a30 5c .cfa: sp 0 + .ra: x30
STACK CFI 2a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2a90 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2a94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2aa4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2aac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ab8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2ac4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2b34 x27: .cfa -16 + ^
STACK CFI 2b54 x27: x27
STACK CFI 2b58 x27: .cfa -16 + ^
STACK CFI 2b60 x27: x27
STACK CFI INIT 2b68 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2b6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ba4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2bf4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2c60 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2c64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c6c x25: .cfa -32 + ^
STACK CFI 2c74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2cec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2d30 70 .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d3c x19: .cfa -16 + ^
STACK CFI 2d58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2da0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 2da4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2dac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2db8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2dc8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ddc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2ff8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3060 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 3064 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3070 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3078 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3084 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3090 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 309c x27: .cfa -16 + ^
STACK CFI 313c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3140 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 317c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3180 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 31a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 31a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 31c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 31c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3218 58 .cfa: sp 0 + .ra: x30
STACK CFI 321c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 322c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 326c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3270 5c .cfa: sp 0 + .ra: x30
STACK CFI 3274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3284 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 32c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 32d0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 32d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 32e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 32ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 32f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3304 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3364 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 3374 x27: .cfa -16 + ^
STACK CFI 3394 x27: x27
STACK CFI 3398 x27: .cfa -16 + ^
STACK CFI 33a0 x27: x27
STACK CFI INIT 33a8 118 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 33b4 x27: .cfa -32 + ^
STACK CFI 33bc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 33e0 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 33ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 343c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3440 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 34c0 ec .cfa: sp 0 + .ra: x30
STACK CFI 34c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 34cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 34dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 34f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3500 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 354c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3550 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 35b0 4ec .cfa: sp 0 + .ra: x30
STACK CFI 35b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 35c0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 35cc x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 35ec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3600 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3864 x19: x19 x20: x20
STACK CFI 387c x23: x23 x24: x24
STACK CFI 388c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 38f0 x19: x19 x20: x20
STACK CFI 38f8 x23: x23 x24: x24
STACK CFI 390c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3910 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 39cc x19: x19 x20: x20
STACK CFI 39d4 x23: x23 x24: x24
STACK CFI 39fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3a0c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 3a18 x19: x19 x20: x20
STACK CFI 3a20 x23: x23 x24: x24
STACK CFI 3a34 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3a38 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 3a58 x19: x19 x20: x20
STACK CFI 3a5c x23: x23 x24: x24
STACK CFI 3a64 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3a7c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 3aa0 80 .cfa: sp 0 + .ra: x30
STACK CFI 3aa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ab0 x21: .cfa -16 + ^
STACK CFI 3ab8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3b20 70 .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b2c x19: .cfa -16 + ^
STACK CFI 3b48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b90 61c .cfa: sp 0 + .ra: x30
STACK CFI 3b94 .cfa: sp 1536 +
STACK CFI 3b98 .ra: .cfa -1528 + ^ x29: .cfa -1536 + ^
STACK CFI 3ba0 x19: .cfa -1520 + ^ x20: .cfa -1512 + ^
STACK CFI 3bcc x21: .cfa -1504 + ^ x22: .cfa -1496 + ^ x23: .cfa -1488 + ^ x24: .cfa -1480 + ^ x25: .cfa -1472 + ^ x26: .cfa -1464 + ^ x27: .cfa -1456 + ^ x28: .cfa -1448 + ^
STACK CFI 3f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3f70 .cfa: sp 1536 + .ra: .cfa -1528 + ^ x19: .cfa -1520 + ^ x20: .cfa -1512 + ^ x21: .cfa -1504 + ^ x22: .cfa -1496 + ^ x23: .cfa -1488 + ^ x24: .cfa -1480 + ^ x25: .cfa -1472 + ^ x26: .cfa -1464 + ^ x27: .cfa -1456 + ^ x28: .cfa -1448 + ^ x29: .cfa -1536 + ^
STACK CFI INIT 41b0 58 .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4208 5c .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 421c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4268 e0 .cfa: sp 0 + .ra: x30
STACK CFI 426c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 427c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4284 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4290 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 429c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4308 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4318 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4338 x27: x27 x28: x28
STACK CFI 433c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4344 x27: x27 x28: x28
STACK CFI INIT 4348 114 .cfa: sp 0 + .ra: x30
STACK CFI 434c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4354 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4364 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4380 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4388 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4394 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 43e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 43ec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4460 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 4464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 446c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4474 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4494 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 44ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 44b8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4514 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4608 48 .cfa: sp 0 + .ra: x30
STACK CFI 460c .cfa: sp 32 +
STACK CFI 4624 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 464c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4650 24 .cfa: sp 0 + .ra: x30
STACK CFI 4654 .cfa: sp 32 +
STACK CFI 465c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4678 300 .cfa: sp 0 + .ra: x30
STACK CFI 467c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 468c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 46a8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 46b0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 46b8 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 46c0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 473c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4978 70 .cfa: sp 0 + .ra: x30
STACK CFI 497c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4984 x19: .cfa -16 + ^
STACK CFI 49a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49e8 364 .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 49f0 .cfa: x29 112 +
STACK CFI 49f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4a00 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4a0c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4a30 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4c24 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4d50 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 4d54 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4d60 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d68 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4d80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4d8c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e38 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4eb0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4f20 58 .cfa: sp 0 + .ra: x30
STACK CFI 4f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4f78 5c .cfa: sp 0 + .ra: x30
STACK CFI 4f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4fd8 dc .cfa: sp 0 + .ra: x30
STACK CFI 4fdc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4fec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4ff4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5000 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 500c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5074 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5084 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 50a4 x27: x27 x28: x28
STACK CFI 50a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 50b0 x27: x27 x28: x28
STACK CFI INIT 50b8 108 .cfa: sp 0 + .ra: x30
STACK CFI 50bc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 50c4 x27: .cfa -32 + ^
STACK CFI 50cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 50d8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 50f0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 50fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 514c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5150 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 51c0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 51c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 51cc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 51f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5200 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 520c x27: .cfa -32 + ^
STACK CFI 5258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 525c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 52b8 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 52bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 52c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 52d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 52dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 52e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 52f4 x27: .cfa -16 + ^
STACK CFI 5394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5398 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 53d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 53d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 53fc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 541c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5420 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5470 70 .cfa: sp 0 + .ra: x30
STACK CFI 5474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 547c x19: .cfa -16 + ^
STACK CFI 5498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 549c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 54e0 58 .cfa: sp 0 + .ra: x30
STACK CFI 54e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5538 5c .cfa: sp 0 + .ra: x30
STACK CFI 553c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 554c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5598 d4 .cfa: sp 0 + .ra: x30
STACK CFI 559c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 55ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 55b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 55c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 55cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 562c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 563c x27: .cfa -16 + ^
STACK CFI 565c x27: x27
STACK CFI 5660 x27: .cfa -16 + ^
STACK CFI 5668 x27: x27
STACK CFI INIT 5670 ec .cfa: sp 0 + .ra: x30
STACK CFI 5674 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 567c x25: .cfa -48 + ^
STACK CFI 5684 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5694 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 56a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 56f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 56fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5760 ec .cfa: sp 0 + .ra: x30
STACK CFI 5764 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 576c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 577c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5794 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 57a0 x25: .cfa -32 + ^
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 57ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5850 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 5854 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5860 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5868 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5874 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5880 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 588c x27: .cfa -16 + ^
STACK CFI 592c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5930 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 596c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5970 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5994 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 59b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 59b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5a08 70 .cfa: sp 0 + .ra: x30
STACK CFI 5a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a14 x19: .cfa -16 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a78 58 .cfa: sp 0 + .ra: x30
STACK CFI 5a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5ad0 5c .cfa: sp 0 + .ra: x30
STACK CFI 5ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ae4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5b30 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5b34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5b44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5b4c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5b58 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5b64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5bc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5bd4 x27: .cfa -16 + ^
STACK CFI 5bf4 x27: x27
STACK CFI 5bf8 x27: .cfa -16 + ^
STACK CFI 5c00 x27: x27
STACK CFI INIT 5c08 ec .cfa: sp 0 + .ra: x30
STACK CFI 5c0c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5c14 x25: .cfa -48 + ^
STACK CFI 5c1c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5c2c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5c40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5c94 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5cf8 ec .cfa: sp 0 + .ra: x30
STACK CFI 5cfc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5d14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5d2c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d38 x25: .cfa -32 + ^
STACK CFI 5d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5d84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5de8 70 .cfa: sp 0 + .ra: x30
STACK CFI 5dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5df4 x19: .cfa -16 + ^
STACK CFI 5e10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e58 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 5e5c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5e64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5e88 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5e94 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 5fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5fe0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6010 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 6014 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6020 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6028 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6034 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6040 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 604c x27: .cfa -16 + ^
STACK CFI 60ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 60f0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 612c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6130 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6154 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6178 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 61c8 58 .cfa: sp 0 + .ra: x30
STACK CFI 61cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 621c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6220 5c .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6234 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6280 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6284 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6294 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 629c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 62a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 62b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6314 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6324 x27: .cfa -16 + ^
STACK CFI 6344 x27: x27
STACK CFI 6348 x27: .cfa -16 + ^
STACK CFI 6350 x27: x27
STACK CFI INIT 6358 d4 .cfa: sp 0 + .ra: x30
STACK CFI 635c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6364 x25: .cfa -32 + ^
STACK CFI 636c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 637c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6390 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 63e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6430 ec .cfa: sp 0 + .ra: x30
STACK CFI 6434 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 643c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6448 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 645c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6470 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 64bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 64c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6520 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 6524 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6530 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6538 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6544 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6550 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 655c x27: .cfa -16 + ^
STACK CFI 65fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6600 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 663c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6640 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6664 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6688 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 66d8 70 .cfa: sp 0 + .ra: x30
STACK CFI 66dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66e4 x19: .cfa -16 + ^
STACK CFI 6700 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6704 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6748 58 .cfa: sp 0 + .ra: x30
STACK CFI 674c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 675c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 679c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 67a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6800 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6814 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 681c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6828 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6834 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6894 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 68a4 x27: .cfa -16 + ^
STACK CFI 68c4 x27: x27
STACK CFI 68c8 x27: .cfa -16 + ^
STACK CFI 68d0 x27: x27
STACK CFI INIT 68d8 ec .cfa: sp 0 + .ra: x30
STACK CFI 68dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 68e4 x25: .cfa -48 + ^
STACK CFI 68ec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 68fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6910 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6964 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 69c8 68 .cfa: sp 0 + .ra: x30
STACK CFI 69cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69d4 x19: .cfa -16 + ^
STACK CFI 6a2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a30 378 .cfa: sp 0 + .ra: x30
STACK CFI 6a34 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6a48 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6a88 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6a90 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6a98 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6c18 x21: x21 x22: x22
STACK CFI 6c1c x23: x23 x24: x24
STACK CFI 6c20 x25: x25 x26: x26
STACK CFI 6c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 6c58 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 6d18 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6d3c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6d64 x21: x21 x22: x22
STACK CFI 6d68 x23: x23 x24: x24
STACK CFI 6d6c x25: x25 x26: x26
STACK CFI 6d70 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6d98 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6d9c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6da0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6da4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 6da8 30 .cfa: sp 0 + .ra: x30
STACK CFI 6dac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6db4 x19: .cfa -16 + ^
STACK CFI 6dd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6dd8 210 .cfa: sp 0 + .ra: x30
STACK CFI 6ddc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6de4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6df4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6e00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6e08 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6e78 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6e7c x27: x27 x28: x28
STACK CFI 6e88 x21: x21 x22: x22
STACK CFI 6e90 x23: x23 x24: x24
STACK CFI 6e98 x25: x25 x26: x26
STACK CFI 6ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ea8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6ee0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6f5c x21: x21 x22: x22
STACK CFI 6f6c x25: x25 x26: x26
STACK CFI 6f70 x27: x27 x28: x28
STACK CFI 6f78 x23: x23 x24: x24
STACK CFI 6f7c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6f98 x21: x21 x22: x22
STACK CFI 6fa0 x25: x25 x26: x26
STACK CFI 6fb0 x23: x23 x24: x24
STACK CFI 6fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fb8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 6fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fc8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6fd4 x23: x23 x24: x24
STACK CFI 6fd8 x25: x25 x26: x26
STACK CFI 6fdc x27: x27 x28: x28
STACK CFI 6fe4 x21: x21 x22: x22
STACK CFI INIT 6fe8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7008 538 .cfa: sp 0 + .ra: x30
STACK CFI 700c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7014 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 702c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7038 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7044 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7060 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 73fc x19: x19 x20: x20
STACK CFI 7400 x21: x21 x22: x22
STACK CFI 7404 x25: x25 x26: x26
STACK CFI 7408 x27: x27 x28: x28
STACK CFI 7420 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7424 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 7450 x19: x19 x20: x20
STACK CFI 7454 x21: x21 x22: x22
STACK CFI 7458 x25: x25 x26: x26
STACK CFI 745c x27: x27 x28: x28
STACK CFI 7460 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7468 x19: x19 x20: x20
STACK CFI 746c x21: x21 x22: x22
STACK CFI 7470 x25: x25 x26: x26
STACK CFI 7474 x27: x27 x28: x28
STACK CFI 7478 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 74c8 x19: x19 x20: x20
STACK CFI 74cc x21: x21 x22: x22
STACK CFI 74d0 x25: x25 x26: x26
STACK CFI 74d4 x27: x27 x28: x28
STACK CFI 74d8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7530 x19: x19 x20: x20
STACK CFI 7534 x21: x21 x22: x22
STACK CFI 7538 x25: x25 x26: x26
STACK CFI 753c x27: x27 x28: x28
STACK CFI INIT 7540 70 .cfa: sp 0 + .ra: x30
STACK CFI 7544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 754c x19: .cfa -16 + ^
STACK CFI 7568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 756c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 75b0 58 .cfa: sp 0 + .ra: x30
STACK CFI 75b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7608 5c .cfa: sp 0 + .ra: x30
STACK CFI 760c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 761c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7668 d0 .cfa: sp 0 + .ra: x30
STACK CFI 766c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 767c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7684 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7690 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7698 x25: .cfa -16 + ^
STACK CFI 7718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 771c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7738 104 .cfa: sp 0 + .ra: x30
STACK CFI 773c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7744 x25: .cfa -32 + ^
STACK CFI 7760 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7770 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 777c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 77a4 x21: x21 x22: x22
STACK CFI 77a8 x23: x23 x24: x24
STACK CFI 77d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI 77d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 77d8 x21: x21 x22: x22
STACK CFI 77dc x23: x23 x24: x24
STACK CFI 77e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7814 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7834 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7838 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 7840 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 7844 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7850 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7858 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7864 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7870 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 787c x27: .cfa -16 + ^
STACK CFI 791c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7920 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 795c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7960 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 7980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7984 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 79a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 79a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 79f8 70 .cfa: sp 0 + .ra: x30
STACK CFI 79fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a04 x19: .cfa -16 + ^
STACK CFI 7a20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7a68 58 .cfa: sp 0 + .ra: x30
STACK CFI 7a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ac0 5c .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ad4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7b20 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7b34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7b3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7b48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7b54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7bb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7bc4 x27: .cfa -16 + ^
STACK CFI 7be4 x27: x27
STACK CFI 7be8 x27: .cfa -16 + ^
STACK CFI 7bf0 x27: x27
STACK CFI INIT 7bf8 ec .cfa: sp 0 + .ra: x30
STACK CFI 7bfc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7c04 x25: .cfa -48 + ^
STACK CFI 7c0c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7c1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7c30 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7c84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7ce8 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 7cec .cfa: sp 512 +
STACK CFI 7cf0 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 7cf8 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 7d08 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 7d20 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 7d48 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 7d54 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 7df4 x19: x19 x20: x20
STACK CFI 7df8 x23: x23 x24: x24
STACK CFI 7dfc x19: .cfa -496 + ^ x20: .cfa -488 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 7e54 x19: x19 x20: x20
STACK CFI 7e5c x23: x23 x24: x24
STACK CFI 7e88 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7e8c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI 7eb4 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 7ed8 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 7edc x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI INIT 7ee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ee8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 7eec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7ef4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 7f00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7f14 x23: .cfa -112 + ^
STACK CFI 7f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7f6c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7fb8 70 .cfa: sp 0 + .ra: x30
STACK CFI 7fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fc4 x19: .cfa -16 + ^
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8028 284 .cfa: sp 0 + .ra: x30
STACK CFI 802c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8034 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 803c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 804c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8060 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 823c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 82b0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 82b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 82c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 82c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 82d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 82e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 82ec x27: .cfa -16 + ^
STACK CFI 838c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8390 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 83cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 83d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 83f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 83f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8418 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8468 58 .cfa: sp 0 + .ra: x30
STACK CFI 846c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 847c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 84c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 84c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8520 d4 .cfa: sp 0 + .ra: x30
STACK CFI 8524 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8534 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 853c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8548 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8554 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 85b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 85b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 85c4 x27: .cfa -16 + ^
STACK CFI 85e4 x27: x27
STACK CFI 85e8 x27: .cfa -16 + ^
STACK CFI 85f0 x27: x27
STACK CFI INIT 85f8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 85fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8604 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8614 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 862c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8634 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8684 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 86f0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 86f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 86fc x25: .cfa -32 + ^
STACK CFI 8704 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8714 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8728 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 877c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 87c0 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 87c4 .cfa: sp 1280 +
STACK CFI 87cc .ra: .cfa -1272 + ^ x29: .cfa -1280 + ^
STACK CFI 87d8 x19: .cfa -1264 + ^ x20: .cfa -1256 + ^
STACK CFI 87f0 x21: .cfa -1248 + ^ x22: .cfa -1240 + ^
STACK CFI 87fc x27: .cfa -1200 + ^ x28: .cfa -1192 + ^
STACK CFI 8830 x23: .cfa -1232 + ^ x24: .cfa -1224 + ^
STACK CFI 883c x25: .cfa -1216 + ^ x26: .cfa -1208 + ^
STACK CFI 8a00 x23: x23 x24: x24
STACK CFI 8a08 x25: x25 x26: x26
STACK CFI 8a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8a44 .cfa: sp 1280 + .ra: .cfa -1272 + ^ x19: .cfa -1264 + ^ x20: .cfa -1256 + ^ x21: .cfa -1248 + ^ x22: .cfa -1240 + ^ x23: .cfa -1232 + ^ x24: .cfa -1224 + ^ x25: .cfa -1216 + ^ x26: .cfa -1208 + ^ x27: .cfa -1200 + ^ x28: .cfa -1192 + ^ x29: .cfa -1280 + ^
STACK CFI 8a5c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8a80 x23: .cfa -1232 + ^ x24: .cfa -1224 + ^ x25: .cfa -1216 + ^ x26: .cfa -1208 + ^
STACK CFI 8a88 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8a8c x23: .cfa -1232 + ^ x24: .cfa -1224 + ^
STACK CFI 8a90 x25: .cfa -1216 + ^ x26: .cfa -1208 + ^
STACK CFI INIT 8a98 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 8aa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ad0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8ad8 x21: .cfa -16 + ^
STACK CFI 8c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
