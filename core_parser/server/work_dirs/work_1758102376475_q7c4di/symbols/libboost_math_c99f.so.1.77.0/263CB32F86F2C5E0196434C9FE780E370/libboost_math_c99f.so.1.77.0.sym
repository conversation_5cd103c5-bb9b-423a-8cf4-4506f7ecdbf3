MODULE Linux arm64 263CB32F86F2C5E0196434C9FE780E370 libboost_math_c99f.so.1.77.0
INFO CODE_ID 2FB33C26F286E0C5196434C9FE780E37
PUBLIC 1550 0 _init
PUBLIC 17a0 0 _GLOBAL__sub_I_acoshf.cpp
PUBLIC 17e0 0 _GLOBAL__sub_I_asinhf.cpp
PUBLIC 1840 0 _GLOBAL__sub_I_atanhf.cpp
PUBLIC 1880 0 _GLOBAL__sub_I_cbrtf.cpp
PUBLIC 18c0 0 _GLOBAL__sub_I_copysignf.cpp
PUBLIC 1900 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::erf<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 1990 0 _GLOBAL__sub_I_erfcf.cpp
PUBLIC 1a20 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::erf<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 1ab0 0 _GLOBAL__sub_I_erff.cpp
PUBLIC 1b40 0 _GLOBAL__sub_I_expm1f.cpp
PUBLIC 1ba0 0 _GLOBAL__sub_I_fmaxf.cpp
PUBLIC 1be0 0 _GLOBAL__sub_I_fminf.cpp
PUBLIC 1c20 0 _GLOBAL__sub_I_fpclassifyf.cpp
PUBLIC 1c60 0 _GLOBAL__sub_I_hypotf.cpp
PUBLIC 1ca0 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::lgamma<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, int*, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0] [clone .constprop.0]
PUBLIC 1d30 0 _GLOBAL__sub_I_lgammaf.cpp
PUBLIC 1db0 0 _GLOBAL__sub_I_llroundf.cpp
PUBLIC 1df0 0 _GLOBAL__sub_I_log1pf.cpp
PUBLIC 1e30 0 _GLOBAL__sub_I_lroundf.cpp
PUBLIC 1e70 0 _GLOBAL__sub_I_nextafterf.cpp
PUBLIC 1f00 0 _GLOBAL__sub_I_nexttowardf.cpp
PUBLIC 1fa0 0 _GLOBAL__sub_I_roundf.cpp
PUBLIC 1fe0 0 _GLOBAL__sub_I_tgammaf.cpp
PUBLIC 2020 0 _GLOBAL__sub_I_truncf.cpp
PUBLIC 205c 0 call_weak_fn
PUBLIC 2070 0 deregister_tm_clones
PUBLIC 20a0 0 register_tm_clones
PUBLIC 20dc 0 __do_global_dtors_aux
PUBLIC 212c 0 frame_dummy
PUBLIC 2130 0 boost_acoshf
PUBLIC 2360 0 boost_asinhf
PUBLIC 2af0 0 double boost::math::detail::asinh_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 2f00 0 boost_atanhf
PUBLIC 3170 0 boost_cbrtf
PUBLIC 3390 0 boost_copysignf
PUBLIC 33c0 0 boost_erfcf
PUBLIC 3920 0 double boost::math::detail::erf_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, bool, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, std::integral_constant<int, 53> const&)
PUBLIC 3e70 0 boost_erff
PUBLIC 4490 0 boost_expm1f
PUBLIC 4660 0 boost_fmaxf
PUBLIC 4690 0 boost_fminf
PUBLIC 46c0 0 bool boost::math::tr1::signbit<float>(float)
PUBLIC 46d0 0 int boost::math::tr1::fpclassify<float>(float)
PUBLIC 4720 0 bool boost::math::tr1::isfinite<float>(float)
PUBLIC 4740 0 bool boost::math::tr1::isinf<float>(float)
PUBLIC 4760 0 bool boost::math::tr1::isnan<float>(float)
PUBLIC 4770 0 bool boost::math::tr1::isnormal<float>(float)
PUBLIC 4790 0 boost_hypotf
PUBLIC 4840 0 boost_lgammaf
PUBLIC 4de0 0 double boost::math::unchecked_factorial<double>(unsigned int)
PUBLIC 4e20 0 double boost::math::detail::lgamma_small_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, double, double, std::integral_constant<int, 64> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&)
PUBLIC 5150 0 double boost::math::detail::gamma_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&)
PUBLIC 5800 0 double boost::math::detail::lgamma_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&, int*)
PUBLIC 5d20 0 boost_llroundf
PUBLIC 5e30 0 boost_log1pf
PUBLIC 5e90 0 boost_lroundf
PUBLIC 5fa0 0 boost_nextafterf
PUBLIC 6280 0 float boost::math::detail::float_next_imp<float, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(float const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 6450 0 float boost::math::detail::float_prior_imp<float, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(float const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 6620 0 boost_nexttowardf
PUBLIC 6bc0 0 long double boost::math::detail::float_next_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 6ee0 0 long double boost::math::detail::float_prior_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&)
PUBLIC 7210 0 boost_roundf
PUBLIC 72d0 0 boost_tgammaf
PUBLIC 73a0 0 boost_truncf
PUBLIC 7410 0 _fini
STACK CFI INIT 2070 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20dc 50 .cfa: sp 0 + .ra: x30
STACK CFI 20ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20f4 x19: .cfa -16 + ^
STACK CFI 2124 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 212c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2130 22c .cfa: sp 0 + .ra: x30
STACK CFI 2138 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2148 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 21d8 v8: v8 v9: v9
STACK CFI 21ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21f0 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 21f4 v8: v8 v9: v9
STACK CFI 223c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2280 v8: v8 v9: v9
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 228c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2300 v8: v8 v9: v9
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 230c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2328 v8: v8 v9: v9
STACK CFI 2330 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2358 v8: v8 v9: v9
STACK CFI INIT 17a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 17a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17ac x19: .cfa -16 + ^
STACK CFI 17d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2af0 404 .cfa: sp 0 + .ra: x30
STACK CFI 2af8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b0c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2b8c v8: v8 v9: v9
STACK CFI 2b94 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2bf4 v8: v8 v9: v9
STACK CFI 2bf8 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2c24 v8: v8 v9: v9
STACK CFI 2c28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c2c .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2c4c v8: v8 v9: v9
STACK CFI 2c50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c54 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2c5c v8: v8 v9: v9
STACK CFI 2c60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c64 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2c74 v8: v8 v9: v9
STACK CFI 2c7c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2ca8 v8: v8 v9: v9
STACK CFI 2cac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2cb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2cc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ccc .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2d08 v8: v8 v9: v9
STACK CFI 2d10 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2e44 v8: v8 v9: v9
STACK CFI 2e4c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2e88 v8: v8 v9: v9
STACK CFI 2e8c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2eb4 v8: v8 v9: v9
STACK CFI 2ebc v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI INIT 2360 78c .cfa: sp 0 + .ra: x30
STACK CFI 2364 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 236c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2410 v10: .cfa -32 + ^
STACK CFI 2490 v10: v10
STACK CFI 24cc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 24d0 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2534 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 2538 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2558 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 255c .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 25dc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 25e0 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2640 v10: .cfa -32 + ^
STACK CFI 26b8 v10: v10
STACK CFI 26bc v10: .cfa -32 + ^
STACK CFI 26dc v10: v10
STACK CFI 2728 v10: .cfa -32 + ^
STACK CFI 279c v10: v10
STACK CFI 2804 v10: .cfa -32 + ^
STACK CFI 2828 v10: v10
STACK CFI 288c v10: .cfa -32 + ^
STACK CFI 28c4 v10: v10
STACK CFI 28e8 v10: .cfa -32 + ^
STACK CFI 2914 v10: v10
STACK CFI 292c v10: .cfa -32 + ^
STACK CFI 2acc v10: v10
STACK CFI 2adc v10: .cfa -32 + ^
STACK CFI INIT 17e0 54 .cfa: sp 0 + .ra: x30
STACK CFI 17e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17ec x19: .cfa -16 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f00 26c .cfa: sp 0 + .ra: x30
STACK CFI 2f08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f18 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 2f24 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2f38 v8: v8 v9: v9
STACK CFI 2f3c v10: v10 v11: v11
STACK CFI 2f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f68 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2fb8 v8: v8 v9: v9
STACK CFI 2fbc v10: v10 v11: v11
STACK CFI 2fc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2fc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2fd8 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 3070 v8: v8 v9: v9
STACK CFI 3078 v10: v10 v11: v11
STACK CFI 30a0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 30e8 v8: v8 v9: v9
STACK CFI 30ec v10: v10 v11: v11
STACK CFI 30f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 30f8 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3154 v8: v8 v9: v9
STACK CFI 3158 v10: v10 v11: v11
STACK CFI 315c v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI INIT 1840 3c .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 184c x19: .cfa -16 + ^
STACK CFI 1874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3170 220 .cfa: sp 0 + .ra: x30
STACK CFI 3174 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3184 v8: .cfa -24 + ^
STACK CFI 31a8 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 31ac .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 31d0 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 31d4 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 31dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 31e4 x21: .cfa -32 + ^
STACK CFI 32dc x19: x19 x20: x20
STACK CFI 32e0 x21: x21
STACK CFI 32f0 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 32f4 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 3380 x19: x19 x20: x20
STACK CFI 3384 x21: x21
STACK CFI INIT 1880 3c .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 188c x19: .cfa -16 + ^
STACK CFI 18b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3390 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 18c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18cc x19: .cfa -16 + ^
STACK CFI 18f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3920 54c .cfa: sp 0 + .ra: x30
STACK CFI 3924 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3930 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 3940 x19: .cfa -64 + ^
STACK CFI 3998 x19: x19
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 39a4 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 39c0 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3a90 v10: v10 v11: v11
STACK CFI 3ab4 x19: x19
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3ac4 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 3af8 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3b3c v10: v10 v11: v11
STACK CFI 3b70 x19: x19
STACK CFI 3b74 x19: .cfa -64 + ^
STACK CFI 3b84 x19: x19
STACK CFI 3b90 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3b94 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 3ba4 x19: x19
STACK CFI 3bac .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3bb0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 3bc8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3bcc .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 3bdc v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3cfc v10: v10 v11: v11
STACK CFI 3d00 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3dc0 v10: v10 v11: v11
STACK CFI 3dc8 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI INIT 1900 84 .cfa: sp 0 + .ra: x30
STACK CFI 1904 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1958 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 33c0 558 .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 33cc v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 3448 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 3514 v10: v10 v11: v11
STACK CFI 3594 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3598 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3658 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 365c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3690 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3694 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 36ac v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 37c4 v10: v10 v11: v11
STACK CFI 37d4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 3894 v10: v10 v11: v11
STACK CFI 38a4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI INIT 1990 90 .cfa: sp 0 + .ra: x30
STACK CFI 1994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 199c x19: .cfa -16 + ^
STACK CFI 19d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a20 84 .cfa: sp 0 + .ra: x30
STACK CFI 1a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e70 618 .cfa: sp 0 + .ra: x30
STACK CFI 3e74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3e7c v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI 3ef4 v10: .cfa -64 + ^ v11: .cfa -56 + ^
STACK CFI 3efc v12: .cfa -48 + ^ v13: .cfa -40 + ^
STACK CFI 401c v10: v10 v11: v11
STACK CFI 4024 v12: v12 v13: v13
STACK CFI 4084 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 4088 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 40bc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 40c0 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 40f4 v10: .cfa -64 + ^ v11: .cfa -56 + ^
STACK CFI 4138 v10: v10 v11: v11
STACK CFI 416c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 4170 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 4180 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 4184 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 419c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 41a0 .cfa: sp 96 + .ra: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 41bc v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^
STACK CFI 437c v10: v10 v11: v11
STACK CFI 4388 v12: v12 v13: v13
STACK CFI 4394 v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^
STACK CFI INIT 1ab0 90 .cfa: sp 0 + .ra: x30
STACK CFI 1ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1abc x19: .cfa -16 + ^
STACK CFI 1af8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1afc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1b3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4490 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 449c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 44ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b40 54 .cfa: sp 0 + .ra: x30
STACK CFI 1b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b4c x19: .cfa -16 + ^
STACK CFI 1b90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4660 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ba0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bac x19: .cfa -16 + ^
STACK CFI 1bd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4690 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bec x19: .cfa -16 + ^
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46d0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4720 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4740 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4760 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4770 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c20 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c2c x19: .cfa -16 + ^
STACK CFI 1c54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4790 a4 .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47dc v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 47f8 v8: v8 v9: v9
STACK CFI 47fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4800 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4820 v8: v8 v9: v9
STACK CFI 4824 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4828 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1c60 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c6c x19: .cfa -16 + ^
STACK CFI 1c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4de0 40 .cfa: sp 0 + .ra: x30
STACK CFI 4de4 .cfa: sp 1408 +
STACK CFI 4df8 .ra: .cfa -1400 + ^ x29: .cfa -1408 + ^
STACK CFI 4e00 x19: .cfa -1392 + ^
STACK CFI 4e1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e20 324 .cfa: sp 0 + .ra: x30
STACK CFI 4e2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e40 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 4e54 v8: v8 v9: v9
STACK CFI 4e5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e60 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e74 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4e7c v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 4f74 v10: v10 v11: v11
STACK CFI 4f90 v8: v8 v9: v9
STACK CFI 4f94 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 5064 v8: v8 v9: v9
STACK CFI 5068 v10: v10 v11: v11
STACK CFI 5070 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI INIT 5150 6ac .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5160 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 516c v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 51ec .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5208 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 52f8 v12: v12 v13: v13
STACK CFI 53c0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 53c4 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 53ec .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 53f0 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5470 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 5474 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 55c0 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 5644 v12: v12 v13: v13
STACK CFI 56c8 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 56ec v12: v12 v13: v13
STACK CFI 575c v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 57a8 v12: v12 v13: v13
STACK CFI 57b0 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 57cc v12: v12 v13: v13
STACK CFI 57d0 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 57e4 v12: v12 v13: v13
STACK CFI 57f0 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI INIT 5800 518 .cfa: sp 0 + .ra: x30
STACK CFI 580c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5818 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5824 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 582c v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 5868 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 586c .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 5874 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 5900 v10: v10 v11: v11
STACK CFI 5924 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5928 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 59e8 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 59fc v12: .cfa -32 + ^
STACK CFI 5aec v12: v12
STACK CFI 5b50 v10: v10 v11: v11
STACK CFI 5b54 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 5b74 v10: v10 v11: v11
STACK CFI 5bd4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI INIT 1ca0 84 .cfa: sp 0 + .ra: x30
STACK CFI 1ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4840 598 .cfa: sp 0 + .ra: x30
STACK CFI 484c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4858 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 4860 v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 48ac .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 48b0 .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 4980 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 4984 .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 4a74 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 4a78 .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x29: x29
STACK CFI 4aec .cfa: sp 112 + .ra: .cfa -104 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 4b28 v12: .cfa -64 + ^
STACK CFI 4c18 v12: v12
STACK CFI INIT 1d30 74 .cfa: sp 0 + .ra: x30
STACK CFI 1d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d3c x19: .cfa -16 + ^
STACK CFI 1d78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1da0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d20 110 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d34 v8: .cfa -16 + ^
STACK CFI 5d6c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 5d70 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5dc4 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 5dc8 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e2c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 1db0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dbc x19: .cfa -16 + ^
STACK CFI 1de4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e30 58 .cfa: sp 0 + .ra: x30
STACK CFI 5e38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1df0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dfc x19: .cfa -16 + ^
STACK CFI 1e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e90 110 .cfa: sp 0 + .ra: x30
STACK CFI 5e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ea4 v8: .cfa -16 + ^
STACK CFI 5edc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 5ee0 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5f34 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 5f38 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5f9c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 1e30 3c .cfa: sp 0 + .ra: x30
STACK CFI 1e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e3c x19: .cfa -16 + ^
STACK CFI 1e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6280 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 6284 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6298 v8: .cfa -40 + ^
STACK CFI 62cc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 62d0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 62d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 632c x19: x19 x20: x20
STACK CFI 6338 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 6340 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 635c x21: .cfa -48 + ^
STACK CFI 63b4 x19: x19 x20: x20
STACK CFI 63b8 x21: x21
STACK CFI 63bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 63cc x19: x19 x20: x20
STACK CFI 63dc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 63e0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 63ec .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 63f0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6404 x19: x19 x20: x20
STACK CFI 6408 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 640c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 6410 x21: x21
STACK CFI 6414 x21: .cfa -48 + ^
STACK CFI INIT 6450 1cc .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6468 v8: .cfa -40 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 6490 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6498 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 64f0 x19: x19 x20: x20
STACK CFI 64fc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 6504 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6524 x21: .cfa -48 + ^
STACK CFI 6578 x19: x19 x20: x20
STACK CFI 657c x21: x21
STACK CFI 6580 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6590 x19: x19 x20: x20
STACK CFI 659c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 65a0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 65bc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 65c0 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 65d8 x19: x19 x20: x20
STACK CFI 65e0 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 65e4 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 65e8 x21: x21
STACK CFI 65ec x21: .cfa -48 + ^
STACK CFI INIT 5fa0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 5fa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5fb0 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 5fc8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 5fcc .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6030 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 6034 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 60bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60e8 x19: x19 x20: x20
STACK CFI 615c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 618c x19: x19 x20: x20
STACK CFI 61d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 624c x19: x19 x20: x20
STACK CFI 6250 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 627c x19: x19 x20: x20
STACK CFI INIT 1e70 8c .cfa: sp 0 + .ra: x30
STACK CFI 1e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e7c x19: .cfa -16 + ^
STACK CFI 1eb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6bc0 320 .cfa: sp 0 + .ra: x30
STACK CFI 6bc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6bcc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6bdc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6be8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6bf4 x25: .cfa -80 + ^
STACK CFI 6c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6c94 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 6d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6d60 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 6e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6e40 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 6e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6e64 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 6ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6ea4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6ee0 330 .cfa: sp 0 + .ra: x30
STACK CFI 6ee4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6eec x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6efc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6f08 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6f14 x25: .cfa -80 + ^
STACK CFI 6fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6fac .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 7078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 707c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 715c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7160 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 718c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7190 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 71d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 71d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6620 594 .cfa: sp 0 + .ra: x30
STACK CFI 6624 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 662c v8: .cfa -72 + ^
STACK CFI 6638 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6674 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 6678 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 667c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6694 x23: .cfa -80 + ^
STACK CFI 66dc x21: x21 x22: x22
STACK CFI 66e0 x23: x23
STACK CFI 66e8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6700 x23: .cfa -80 + ^
STACK CFI 674c x21: x21 x22: x22
STACK CFI 6754 x23: x23
STACK CFI 6764 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 6768 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 67d8 x21: x21 x22: x22
STACK CFI 67dc x23: x23
STACK CFI 67e0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 6854 x21: x21 x22: x22
STACK CFI 6858 x23: x23
STACK CFI 685c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 688c x21: x21 x22: x22
STACK CFI 6890 x23: x23
STACK CFI 6898 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 6988 x21: x21 x22: x22
STACK CFI 698c x23: x23
STACK CFI 6990 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 69c0 x21: x21 x22: x22
STACK CFI 69c4 x23: x23
STACK CFI 69cc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 69d4 x21: x21 x22: x22
STACK CFI 69d8 x23: x23
STACK CFI 69dc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 6ab0 x21: x21 x22: x22
STACK CFI 6ab4 x23: x23
STACK CFI 6ab8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 6b84 x21: x21 x22: x22
STACK CFI 6b88 x23: x23
STACK CFI 6b8c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 1f00 98 .cfa: sp 0 + .ra: x30
STACK CFI 1f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f0c x19: .cfa -16 + ^
STACK CFI 1f48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7210 bc .cfa: sp 0 + .ra: x30
STACK CFI 7214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7220 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 7254 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 7258 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7290 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 7294 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1fa0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fac x19: .cfa -16 + ^
STACK CFI 1fd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 72d0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 72d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 72e8 v8: .cfa -48 + ^
STACK CFI 7334 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 7338 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI 7380 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 7384 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI 739c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 1fe0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fec x19: .cfa -16 + ^
STACK CFI 2014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 73a0 70 .cfa: sp 0 + .ra: x30
STACK CFI 73a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73b0 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 73e4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 73e8 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 73fc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 7400 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 740c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI INIT 2020 3c .cfa: sp 0 + .ra: x30
STACK CFI 2024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 202c x19: .cfa -16 + ^
STACK CFI 2054 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
