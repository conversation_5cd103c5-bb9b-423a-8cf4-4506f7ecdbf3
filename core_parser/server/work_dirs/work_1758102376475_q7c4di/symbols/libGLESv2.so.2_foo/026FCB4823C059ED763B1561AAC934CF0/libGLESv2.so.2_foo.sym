MODULE Linux arm64 026FCB4823C059ED763B1561AAC934CF0 libGLESv2.so.2
INFO CODE_ID 48CB6F02C023ED59763B1561AAC934CF81131F0F
PUBLIC 10000 0 glActiveShaderProgram
PUBLIC 10080 0 glActiveTexture
PUBLIC 10100 0 glAttachShader
PUBLIC 10180 0 glBeginQuery
PUBLIC 10200 0 glBeginTransformFeedback
PUBLIC 10280 0 glBindAttribLocation
PUBLIC 10300 0 glBindBuffer
PUBLIC 10380 0 glBindBufferBase
PUBLIC 10400 0 glBindBufferRange
PUBLIC 10480 0 glBindFramebuffer
PUBLIC 10500 0 glBindImageTexture
PUBLIC 10580 0 glBindProgramPipeline
PUBLIC 10600 0 glBindRenderbuffer
PUBLIC 10680 0 glBindSampler
PUBLIC 10700 0 glBindTexture
PUBLIC 10780 0 glBindTransformFeedback
PUBLIC 10800 0 glBindVertexArray
PUBLIC 10880 0 glBindVertexBuffer
PUBLIC 10900 0 glBlendBarrier
PUBLIC 10980 0 glBlendColor
PUBLIC 10a00 0 glBlendEquation
PUBLIC 10a80 0 glBlendEquationSeparate
PUBLIC 10b00 0 glBlendEquationSeparatei
PUBLIC 10b80 0 glBlendEquationi
PUBLIC 10c00 0 glBlendFunc
PUBLIC 10c80 0 glBlendFuncSeparate
PUBLIC 10d00 0 glBlendFuncSeparatei
PUBLIC 10d80 0 glBlendFunci
PUBLIC 10e00 0 glBlitFramebuffer
PUBLIC 10e80 0 glBufferData
PUBLIC 10f00 0 glBufferSubData
PUBLIC 10f80 0 glCheckFramebufferStatus
PUBLIC 11000 0 glClear
PUBLIC 11080 0 glClearBufferfi
PUBLIC 11100 0 glClearBufferfv
PUBLIC 11180 0 glClearBufferiv
PUBLIC 11200 0 glClearBufferuiv
PUBLIC 11280 0 glClearColor
PUBLIC 11300 0 glClearDepthf
PUBLIC 11380 0 glClearStencil
PUBLIC 11400 0 glClientWaitSync
PUBLIC 11480 0 glColorMask
PUBLIC 11500 0 glColorMaski
PUBLIC 11580 0 glCompileShader
PUBLIC 11600 0 glCompressedTexImage2D
PUBLIC 11680 0 glCompressedTexImage3D
PUBLIC 11700 0 glCompressedTexSubImage2D
PUBLIC 11780 0 glCompressedTexSubImage3D
PUBLIC 11800 0 glCopyBufferSubData
PUBLIC 11880 0 glCopyImageSubData
PUBLIC 11900 0 glCopyTexImage2D
PUBLIC 11980 0 glCopyTexSubImage2D
PUBLIC 11a00 0 glCopyTexSubImage3D
PUBLIC 11a80 0 glCreateProgram
PUBLIC 11b00 0 glCreateShader
PUBLIC 11b80 0 glCreateShaderProgramv
PUBLIC 11c00 0 glCullFace
PUBLIC 11c80 0 glDebugMessageCallback
PUBLIC 11d00 0 glDebugMessageControl
PUBLIC 11d80 0 glDebugMessageInsert
PUBLIC 11e00 0 glDeleteBuffers
PUBLIC 11e80 0 glDeleteFramebuffers
PUBLIC 11f00 0 glDeleteProgram
PUBLIC 11f80 0 glDeleteProgramPipelines
PUBLIC 12000 0 glDeleteQueries
PUBLIC 12080 0 glDeleteRenderbuffers
PUBLIC 12100 0 glDeleteSamplers
PUBLIC 12180 0 glDeleteShader
PUBLIC 12200 0 glDeleteSync
PUBLIC 12280 0 glDeleteTextures
PUBLIC 12300 0 glDeleteTransformFeedbacks
PUBLIC 12380 0 glDeleteVertexArrays
PUBLIC 12400 0 glDepthFunc
PUBLIC 12480 0 glDepthMask
PUBLIC 12500 0 glDepthRangef
PUBLIC 12580 0 glDetachShader
PUBLIC 12600 0 glDisable
PUBLIC 12680 0 glDisableVertexAttribArray
PUBLIC 12700 0 glDisablei
PUBLIC 12780 0 glDispatchCompute
PUBLIC 12800 0 glDispatchComputeIndirect
PUBLIC 12880 0 glDrawArrays
PUBLIC 12900 0 glDrawArraysIndirect
PUBLIC 12980 0 glDrawArraysInstanced
PUBLIC 12a00 0 glDrawBuffers
PUBLIC 12a80 0 glDrawElements
PUBLIC 12b00 0 glDrawElementsBaseVertex
PUBLIC 12b80 0 glDrawElementsIndirect
PUBLIC 12c00 0 glDrawElementsInstanced
PUBLIC 12c80 0 glDrawElementsInstancedBaseVertex
PUBLIC 12d00 0 glDrawRangeElements
PUBLIC 12d80 0 glDrawRangeElementsBaseVertex
PUBLIC 12e00 0 glEnable
PUBLIC 12e80 0 glEnableVertexAttribArray
PUBLIC 12f00 0 glEnablei
PUBLIC 12f80 0 glEndQuery
PUBLIC 13000 0 glEndTransformFeedback
PUBLIC 13080 0 glFenceSync
PUBLIC 13100 0 glFinish
PUBLIC 13180 0 glFlush
PUBLIC 13200 0 glFlushMappedBufferRange
PUBLIC 13280 0 glFramebufferParameteri
PUBLIC 13300 0 glFramebufferRenderbuffer
PUBLIC 13380 0 glFramebufferTexture
PUBLIC 13400 0 glFramebufferTexture2D
PUBLIC 13480 0 glFramebufferTextureLayer
PUBLIC 13500 0 glFrontFace
PUBLIC 13580 0 glGenBuffers
PUBLIC 13600 0 glGenFramebuffers
PUBLIC 13680 0 glGenProgramPipelines
PUBLIC 13700 0 glGenQueries
PUBLIC 13780 0 glGenRenderbuffers
PUBLIC 13800 0 glGenSamplers
PUBLIC 13880 0 glGenTextures
PUBLIC 13900 0 glGenTransformFeedbacks
PUBLIC 13980 0 glGenVertexArrays
PUBLIC 13a00 0 glGenerateMipmap
PUBLIC 13a80 0 glGetActiveAttrib
PUBLIC 13b00 0 glGetActiveUniform
PUBLIC 13b80 0 glGetActiveUniformBlockName
PUBLIC 13c00 0 glGetActiveUniformBlockiv
PUBLIC 13c80 0 glGetActiveUniformsiv
PUBLIC 13d00 0 glGetAttachedShaders
PUBLIC 13d80 0 glGetAttribLocation
PUBLIC 13e00 0 glGetBooleani_v
PUBLIC 13e80 0 glGetBooleanv
PUBLIC 13f00 0 glGetBufferParameteri64v
PUBLIC 13f80 0 glGetBufferParameteriv
PUBLIC 14000 0 glGetBufferPointerv
PUBLIC 14080 0 glGetDebugMessageLog
PUBLIC 14100 0 glGetError
PUBLIC 14180 0 glGetFloatv
PUBLIC 14200 0 glGetFragDataLocation
PUBLIC 14280 0 glGetFramebufferAttachmentParameteriv
PUBLIC 14300 0 glGetFramebufferParameteriv
PUBLIC 14380 0 glGetGraphicsResetStatus
PUBLIC 14400 0 glGetInteger64i_v
PUBLIC 14480 0 glGetInteger64v
PUBLIC 14500 0 glGetIntegeri_v
PUBLIC 14580 0 glGetIntegerv
PUBLIC 14600 0 glGetInternalformativ
PUBLIC 14680 0 glGetMultisamplefv
PUBLIC 14700 0 glGetObjectLabel
PUBLIC 14780 0 glGetObjectPtrLabel
PUBLIC 14800 0 glGetPointerv
PUBLIC 14880 0 glGetProgramBinary
PUBLIC 14900 0 glGetProgramInfoLog
PUBLIC 14980 0 glGetProgramInterfaceiv
PUBLIC 14a00 0 glGetProgramPipelineInfoLog
PUBLIC 14a80 0 glGetProgramPipelineiv
PUBLIC 14b00 0 glGetProgramResourceIndex
PUBLIC 14b80 0 glGetProgramResourceLocation
PUBLIC 14c00 0 glGetProgramResourceName
PUBLIC 14c80 0 glGetProgramResourceiv
PUBLIC 14d00 0 glGetProgramiv
PUBLIC 14d80 0 glGetQueryObjectuiv
PUBLIC 14e00 0 glGetQueryiv
PUBLIC 14e80 0 glGetRenderbufferParameteriv
PUBLIC 14f00 0 glGetSamplerParameterIiv
PUBLIC 14f80 0 glGetSamplerParameterIuiv
PUBLIC 15000 0 glGetSamplerParameterfv
PUBLIC 15080 0 glGetSamplerParameteriv
PUBLIC 15100 0 glGetShaderInfoLog
PUBLIC 15180 0 glGetShaderPrecisionFormat
PUBLIC 15200 0 glGetShaderSource
PUBLIC 15280 0 glGetShaderiv
PUBLIC 15300 0 glGetString
PUBLIC 15380 0 glGetStringi
PUBLIC 15400 0 glGetSynciv
PUBLIC 15480 0 glGetTexLevelParameterfv
PUBLIC 15500 0 glGetTexLevelParameteriv
PUBLIC 15580 0 glGetTexParameterIiv
PUBLIC 15600 0 glGetTexParameterIuiv
PUBLIC 15680 0 glGetTexParameterfv
PUBLIC 15700 0 glGetTexParameteriv
PUBLIC 15780 0 glGetTransformFeedbackVarying
PUBLIC 15800 0 glGetUniformBlockIndex
PUBLIC 15880 0 glGetUniformIndices
PUBLIC 15900 0 glGetUniformLocation
PUBLIC 15980 0 glGetUniformfv
PUBLIC 15a00 0 glGetUniformiv
PUBLIC 15a80 0 glGetUniformuiv
PUBLIC 15b00 0 glGetVertexAttribIiv
PUBLIC 15b80 0 glGetVertexAttribIuiv
PUBLIC 15c00 0 glGetVertexAttribPointerv
PUBLIC 15c80 0 glGetVertexAttribfv
PUBLIC 15d00 0 glGetVertexAttribiv
PUBLIC 15d80 0 glGetnUniformfv
PUBLIC 15e00 0 glGetnUniformiv
PUBLIC 15e80 0 glGetnUniformuiv
PUBLIC 15f00 0 glHint
PUBLIC 15f80 0 glInvalidateFramebuffer
PUBLIC 16000 0 glInvalidateSubFramebuffer
PUBLIC 16080 0 glIsBuffer
PUBLIC 16100 0 glIsEnabled
PUBLIC 16180 0 glIsEnabledi
PUBLIC 16200 0 glIsFramebuffer
PUBLIC 16280 0 glIsProgram
PUBLIC 16300 0 glIsProgramPipeline
PUBLIC 16380 0 glIsQuery
PUBLIC 16400 0 glIsRenderbuffer
PUBLIC 16480 0 glIsSampler
PUBLIC 16500 0 glIsShader
PUBLIC 16580 0 glIsSync
PUBLIC 16600 0 glIsTexture
PUBLIC 16680 0 glIsTransformFeedback
PUBLIC 16700 0 glIsVertexArray
PUBLIC 16780 0 glLineWidth
PUBLIC 16800 0 glLinkProgram
PUBLIC 16880 0 glMapBufferRange
PUBLIC 16900 0 glMemoryBarrier
PUBLIC 16980 0 glMemoryBarrierByRegion
PUBLIC 16a00 0 glMinSampleShading
PUBLIC 16a80 0 glObjectLabel
PUBLIC 16b00 0 glObjectPtrLabel
PUBLIC 16b80 0 glPatchParameteri
PUBLIC 16c00 0 glPauseTransformFeedback
PUBLIC 16c80 0 glPixelStorei
PUBLIC 16d00 0 glPolygonOffset
PUBLIC 16d80 0 glPopDebugGroup
PUBLIC 16e00 0 glPrimitiveBoundingBox
PUBLIC 16e80 0 glProgramBinary
PUBLIC 16f00 0 glProgramParameteri
PUBLIC 16f80 0 glProgramUniform1f
PUBLIC 17000 0 glProgramUniform1fv
PUBLIC 17080 0 glProgramUniform1i
PUBLIC 17100 0 glProgramUniform1iv
PUBLIC 17180 0 glProgramUniform1ui
PUBLIC 17200 0 glProgramUniform1uiv
PUBLIC 17280 0 glProgramUniform2f
PUBLIC 17300 0 glProgramUniform2fv
PUBLIC 17380 0 glProgramUniform2i
PUBLIC 17400 0 glProgramUniform2iv
PUBLIC 17480 0 glProgramUniform2ui
PUBLIC 17500 0 glProgramUniform2uiv
PUBLIC 17580 0 glProgramUniform3f
PUBLIC 17600 0 glProgramUniform3fv
PUBLIC 17680 0 glProgramUniform3i
PUBLIC 17700 0 glProgramUniform3iv
PUBLIC 17780 0 glProgramUniform3ui
PUBLIC 17800 0 glProgramUniform3uiv
PUBLIC 17880 0 glProgramUniform4f
PUBLIC 17900 0 glProgramUniform4fv
PUBLIC 17980 0 glProgramUniform4i
PUBLIC 17a00 0 glProgramUniform4iv
PUBLIC 17a80 0 glProgramUniform4ui
PUBLIC 17b00 0 glProgramUniform4uiv
PUBLIC 17b80 0 glProgramUniformMatrix2fv
PUBLIC 17c00 0 glProgramUniformMatrix2x3fv
PUBLIC 17c80 0 glProgramUniformMatrix2x4fv
PUBLIC 17d00 0 glProgramUniformMatrix3fv
PUBLIC 17d80 0 glProgramUniformMatrix3x2fv
PUBLIC 17e00 0 glProgramUniformMatrix3x4fv
PUBLIC 17e80 0 glProgramUniformMatrix4fv
PUBLIC 17f00 0 glProgramUniformMatrix4x2fv
PUBLIC 17f80 0 glProgramUniformMatrix4x3fv
PUBLIC 18000 0 glPushDebugGroup
PUBLIC 18080 0 glReadBuffer
PUBLIC 18100 0 glReadPixels
PUBLIC 18180 0 glReadnPixels
PUBLIC 18200 0 glReleaseShaderCompiler
PUBLIC 18280 0 glRenderbufferStorage
PUBLIC 18300 0 glRenderbufferStorageMultisample
PUBLIC 18380 0 glResumeTransformFeedback
PUBLIC 18400 0 glSampleCoverage
PUBLIC 18480 0 glSampleMaski
PUBLIC 18500 0 glSamplerParameterIiv
PUBLIC 18580 0 glSamplerParameterIuiv
PUBLIC 18600 0 glSamplerParameterf
PUBLIC 18680 0 glSamplerParameterfv
PUBLIC 18700 0 glSamplerParameteri
PUBLIC 18780 0 glSamplerParameteriv
PUBLIC 18800 0 glScissor
PUBLIC 18880 0 glShaderBinary
PUBLIC 18900 0 glShaderSource
PUBLIC 18980 0 glStencilFunc
PUBLIC 18a00 0 glStencilFuncSeparate
PUBLIC 18a80 0 glStencilMask
PUBLIC 18b00 0 glStencilMaskSeparate
PUBLIC 18b80 0 glStencilOp
PUBLIC 18c00 0 glStencilOpSeparate
PUBLIC 18c80 0 glTexBuffer
PUBLIC 18d00 0 glTexBufferRange
PUBLIC 18d80 0 glTexImage2D
PUBLIC 18e00 0 glTexImage3D
PUBLIC 18e80 0 glTexParameterIiv
PUBLIC 18f00 0 glTexParameterIuiv
PUBLIC 18f80 0 glTexParameterf
PUBLIC 19000 0 glTexParameterfv
PUBLIC 19080 0 glTexParameteri
PUBLIC 19100 0 glTexParameteriv
PUBLIC 19180 0 glTexStorage2D
PUBLIC 19200 0 glTexStorage2DMultisample
PUBLIC 19280 0 glTexStorage3D
PUBLIC 19300 0 glTexStorage3DMultisample
PUBLIC 19380 0 glTexSubImage2D
PUBLIC 19400 0 glTexSubImage3D
PUBLIC 19480 0 glTransformFeedbackVaryings
PUBLIC 19500 0 glUniform1f
PUBLIC 19580 0 glUniform1fv
PUBLIC 19600 0 glUniform1i
PUBLIC 19680 0 glUniform1iv
PUBLIC 19700 0 glUniform1ui
PUBLIC 19780 0 glUniform1uiv
PUBLIC 19800 0 glUniform2f
PUBLIC 19880 0 glUniform2fv
PUBLIC 19900 0 glUniform2i
PUBLIC 19980 0 glUniform2iv
PUBLIC 19a00 0 glUniform2ui
PUBLIC 19a80 0 glUniform2uiv
PUBLIC 19b00 0 glUniform3f
PUBLIC 19b80 0 glUniform3fv
PUBLIC 19c00 0 glUniform3i
PUBLIC 19c80 0 glUniform3iv
PUBLIC 19d00 0 glUniform3ui
PUBLIC 19d80 0 glUniform3uiv
PUBLIC 19e00 0 glUniform4f
PUBLIC 19e80 0 glUniform4fv
PUBLIC 19f00 0 glUniform4i
PUBLIC 19f80 0 glUniform4iv
PUBLIC 1a000 0 glUniform4ui
PUBLIC 1a080 0 glUniform4uiv
PUBLIC 1a100 0 glUniformBlockBinding
PUBLIC 1a180 0 glUniformMatrix2fv
PUBLIC 1a200 0 glUniformMatrix2x3fv
PUBLIC 1a280 0 glUniformMatrix2x4fv
PUBLIC 1a300 0 glUniformMatrix3fv
PUBLIC 1a380 0 glUniformMatrix3x2fv
PUBLIC 1a400 0 glUniformMatrix3x4fv
PUBLIC 1a480 0 glUniformMatrix4fv
PUBLIC 1a500 0 glUniformMatrix4x2fv
PUBLIC 1a580 0 glUniformMatrix4x3fv
PUBLIC 1a600 0 glUnmapBuffer
PUBLIC 1a680 0 glUseProgram
PUBLIC 1a700 0 glUseProgramStages
PUBLIC 1a780 0 glValidateProgram
PUBLIC 1a800 0 glValidateProgramPipeline
PUBLIC 1a880 0 glVertexAttrib1f
PUBLIC 1a900 0 glVertexAttrib1fv
PUBLIC 1a980 0 glVertexAttrib2f
PUBLIC 1aa00 0 glVertexAttrib2fv
PUBLIC 1aa80 0 glVertexAttrib3f
PUBLIC 1ab00 0 glVertexAttrib3fv
PUBLIC 1ab80 0 glVertexAttrib4f
PUBLIC 1ac00 0 glVertexAttrib4fv
PUBLIC 1ac80 0 glVertexAttribBinding
PUBLIC 1ad00 0 glVertexAttribDivisor
PUBLIC 1ad80 0 glVertexAttribFormat
PUBLIC 1ae00 0 glVertexAttribI4i
PUBLIC 1ae80 0 glVertexAttribI4iv
PUBLIC 1af00 0 glVertexAttribI4ui
PUBLIC 1af80 0 glVertexAttribI4uiv
PUBLIC 1b000 0 glVertexAttribIFormat
PUBLIC 1b080 0 glVertexAttribIPointer
PUBLIC 1b100 0 glVertexAttribPointer
PUBLIC 1b180 0 glVertexBindingDivisor
PUBLIC 1b200 0 glViewport
PUBLIC 1b280 0 glWaitSync
STACK CFI INIT 7508 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7538 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7578 48 .cfa: sp 0 + .ra: x30
STACK CFI 757c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7584 x19: .cfa -16 + ^
STACK CFI 75bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 74ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 74b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 74b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 74cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 75c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 75f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75fc x19: .cfa -16 + ^
STACK CFI 7630 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7634 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7678 38 .cfa: sp 0 + .ra: x30
STACK CFI 767c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 76b0 14 .cfa: sp 0 + .ra: x30
STACK CFI 76b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 76c8 88 .cfa: sp 0 + .ra: x30
STACK CFI 76cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76d4 x19: .cfa -16 + ^
STACK CFI 76fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7700 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7750 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7754 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7764 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 77f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 77f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7818 58 .cfa: sp 0 + .ra: x30
STACK CFI 781c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7824 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 785c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7870 28 .cfa: sp 0 + .ra: x30
STACK CFI 7874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 787c x19: .cfa -16 + ^
STACK CFI 7894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7898 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78b8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 78bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78c4 x19: .cfa -16 + ^
STACK CFI 7958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7968 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7970 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7990 54 .cfa: sp 0 + .ra: x30
STACK CFI 7994 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 799c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79a4 x21: .cfa -16 + ^
STACK CFI 79e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 79e8 3c .cfa: sp 0 + .ra: x30
STACK CFI 79ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79fc x19: .cfa -16 + ^
STACK CFI 7a20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7a28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a30 84 .cfa: sp 0 + .ra: x30
