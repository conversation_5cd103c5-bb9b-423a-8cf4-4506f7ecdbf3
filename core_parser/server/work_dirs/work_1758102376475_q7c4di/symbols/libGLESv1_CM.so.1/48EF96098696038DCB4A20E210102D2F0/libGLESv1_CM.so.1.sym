MODULE Linux arm64 48EF96098696038DCB4A20E210102D2F0 libGLESv1_CM.so.1
INFO CODE_ID 0996EF4896868D03CB4A20E210102D2F059EB175
PUBLIC 10000 0 glActiveTexture
PUBLIC 10080 0 glAlphaFunc
PUBLIC 10100 0 glAlphaFuncx
PUBLIC 10180 0 glBindBuffer
PUBLIC 10200 0 glBindTexture
PUBLIC 10280 0 glBlendFunc
PUBLIC 10300 0 glBufferData
PUBLIC 10380 0 glBufferSubData
PUBLIC 10400 0 glClear
PUBLIC 10480 0 glClearColor
PUBLIC 10500 0 glClearColorx
PUBLIC 10580 0 glClearDepthf
PUBLIC 10600 0 glClearDepthx
PUBLIC 10680 0 glClearStencil
PUBLIC 10700 0 glClientActiveTexture
PUBLIC 10780 0 glClipPlanef
PUBLIC 10800 0 glClipPlanex
PUBLIC 10880 0 glColor4f
PUBLIC 10900 0 glColor4ub
PUBLIC 10980 0 glColor4x
PUBLIC 10a00 0 glColorMask
PUBLIC 10a80 0 glColorPointer
PUBLIC 10b00 0 glCompressedTexImage2D
PUBLIC 10b80 0 glCompressedTexSubImage2D
PUBLIC 10c00 0 glCopyTexImage2D
PUBLIC 10c80 0 glCopyTexSubImage2D
PUBLIC 10d00 0 glCullFace
PUBLIC 10d80 0 glDeleteBuffers
PUBLIC 10e00 0 glDeleteTextures
PUBLIC 10e80 0 glDepthFunc
PUBLIC 10f00 0 glDepthMask
PUBLIC 10f80 0 glDepthRangef
PUBLIC 11000 0 glDepthRangex
PUBLIC 11080 0 glDisable
PUBLIC 11100 0 glDisableClientState
PUBLIC 11180 0 glDrawArrays
PUBLIC 11200 0 glDrawElements
PUBLIC 11280 0 glEnable
PUBLIC 11300 0 glEnableClientState
PUBLIC 11380 0 glFinish
PUBLIC 11400 0 glFlush
PUBLIC 11480 0 glFogf
PUBLIC 11500 0 glFogfv
PUBLIC 11580 0 glFogx
PUBLIC 11600 0 glFogxv
PUBLIC 11680 0 glFrontFace
PUBLIC 11700 0 glFrustumf
PUBLIC 11780 0 glFrustumx
PUBLIC 11800 0 glGenBuffers
PUBLIC 11880 0 glGenTextures
PUBLIC 11900 0 glGetBooleanv
PUBLIC 11980 0 glGetBufferParameteriv
PUBLIC 11a00 0 glGetClipPlanef
PUBLIC 11a80 0 glGetClipPlanex
PUBLIC 11b00 0 glGetError
PUBLIC 11b80 0 glGetFixedv
PUBLIC 11c00 0 glGetFloatv
PUBLIC 11c80 0 glGetIntegerv
PUBLIC 11d00 0 glGetLightfv
PUBLIC 11d80 0 glGetLightxv
PUBLIC 11e00 0 glGetMaterialfv
PUBLIC 11e80 0 glGetMaterialxv
PUBLIC 11f00 0 glGetPointerv
PUBLIC 11f80 0 glGetString
PUBLIC 12000 0 glGetTexEnvfv
PUBLIC 12080 0 glGetTexEnviv
PUBLIC 12100 0 glGetTexEnvxv
PUBLIC 12180 0 glGetTexParameterfv
PUBLIC 12200 0 glGetTexParameteriv
PUBLIC 12280 0 glGetTexParameterxv
PUBLIC 12300 0 glHint
PUBLIC 12380 0 glIsBuffer
PUBLIC 12400 0 glIsEnabled
PUBLIC 12480 0 glIsTexture
PUBLIC 12500 0 glLightModelf
PUBLIC 12580 0 glLightModelfv
PUBLIC 12600 0 glLightModelx
PUBLIC 12680 0 glLightModelxv
PUBLIC 12700 0 glLightf
PUBLIC 12780 0 glLightfv
PUBLIC 12800 0 glLightx
PUBLIC 12880 0 glLightxv
PUBLIC 12900 0 glLineWidth
PUBLIC 12980 0 glLineWidthx
PUBLIC 12a00 0 glLoadIdentity
PUBLIC 12a80 0 glLoadMatrixf
PUBLIC 12b00 0 glLoadMatrixx
PUBLIC 12b80 0 glLogicOp
PUBLIC 12c00 0 glMaterialf
PUBLIC 12c80 0 glMaterialfv
PUBLIC 12d00 0 glMaterialx
PUBLIC 12d80 0 glMaterialxv
PUBLIC 12e00 0 glMatrixMode
PUBLIC 12e80 0 glMultMatrixf
PUBLIC 12f00 0 glMultMatrixx
PUBLIC 12f80 0 glMultiTexCoord4f
PUBLIC 13000 0 glMultiTexCoord4x
PUBLIC 13080 0 glNormal3f
PUBLIC 13100 0 glNormal3x
PUBLIC 13180 0 glNormalPointer
PUBLIC 13200 0 glOrthof
PUBLIC 13280 0 glOrthox
PUBLIC 13300 0 glPixelStorei
PUBLIC 13380 0 glPointParameterf
PUBLIC 13400 0 glPointParameterfv
PUBLIC 13480 0 glPointParameterx
PUBLIC 13500 0 glPointParameterxv
PUBLIC 13580 0 glPointSize
PUBLIC 13600 0 glPointSizePointerOES
PUBLIC 13680 0 glPointSizex
PUBLIC 13700 0 glPolygonOffset
PUBLIC 13780 0 glPolygonOffsetx
PUBLIC 13800 0 glPopMatrix
PUBLIC 13880 0 glPushMatrix
PUBLIC 13900 0 glReadPixels
PUBLIC 13980 0 glRotatef
PUBLIC 13a00 0 glRotatex
PUBLIC 13a80 0 glSampleCoverage
PUBLIC 13b00 0 glSampleCoveragex
PUBLIC 13b80 0 glScalef
PUBLIC 13c00 0 glScalex
PUBLIC 13c80 0 glScissor
PUBLIC 13d00 0 glShadeModel
PUBLIC 13d80 0 glStencilFunc
PUBLIC 13e00 0 glStencilMask
PUBLIC 13e80 0 glStencilOp
PUBLIC 13f00 0 glTexCoordPointer
PUBLIC 13f80 0 glTexEnvf
PUBLIC 14000 0 glTexEnvfv
PUBLIC 14080 0 glTexEnvi
PUBLIC 14100 0 glTexEnviv
PUBLIC 14180 0 glTexEnvx
PUBLIC 14200 0 glTexEnvxv
PUBLIC 14280 0 glTexImage2D
PUBLIC 14300 0 glTexParameterf
PUBLIC 14380 0 glTexParameterfv
PUBLIC 14400 0 glTexParameteri
PUBLIC 14480 0 glTexParameteriv
PUBLIC 14500 0 glTexParameterx
PUBLIC 14580 0 glTexParameterxv
PUBLIC 14600 0 glTexSubImage2D
PUBLIC 14680 0 glTranslatef
PUBLIC 14700 0 glTranslatex
PUBLIC 14780 0 glVertexPointer
PUBLIC 14800 0 glViewport
STACK CFI INIT 3348 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3378 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 33bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33c4 x19: .cfa -16 + ^
STACK CFI 33fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3400 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3310 24 .cfa: sp 0 + .ra: x30
STACK CFI 3314 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 332c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 32f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 32f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 330c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3408 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3418 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3428 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3430 84 .cfa: sp 0 + .ra: x30
STACK CFI 3434 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 343c x19: .cfa -16 + ^
STACK CFI 3470 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 34b8 38 .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34f0 14 .cfa: sp 0 + .ra: x30
STACK CFI 34f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3500 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3508 88 .cfa: sp 0 + .ra: x30
STACK CFI 350c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3514 x19: .cfa -16 + ^
STACK CFI 353c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3540 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3590 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3594 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 35a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3634 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3658 58 .cfa: sp 0 + .ra: x30
STACK CFI 365c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3664 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 369c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 36b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 36b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36bc x19: .cfa -16 + ^
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 36d8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3700 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 370c x19: .cfa -16 + ^
STACK CFI 37a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 37a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37b8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37d8 54 .cfa: sp 0 + .ra: x30
STACK CFI 37dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37ec x21: .cfa -16 + ^
STACK CFI 3828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3830 3c .cfa: sp 0 + .ra: x30
STACK CFI 3834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3844 x19: .cfa -16 + ^
STACK CFI 3868 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3878 84 .cfa: sp 0 + .ra: x30
