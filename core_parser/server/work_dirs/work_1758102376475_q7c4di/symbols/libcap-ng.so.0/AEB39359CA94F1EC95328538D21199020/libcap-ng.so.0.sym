MODULE Linux arm64 AEB39359CA94F1EC95328538D21199020 libcap-ng.so.0
INFO CODE_ID 5993B3AE94CAECF195328538D2119902823F1292
PUBLIC 1700 0 capng_clear
PUBLIC 17c8 0 capng_fill
PUBLIC 18b8 0 capng_setpid
PUBLIC 1928 0 capng_get_caps_process
PUBLIC 1ae0 0 capng_get_caps_fd
PUBLIC 1c58 0 capng_update
PUBLIC 1fe8 0 capng_updatev
PUBLIC 2130 0 capng_lock
PUBLIC 2188 0 capng_have_capabilities
PUBLIC 2378 0 capng_apply_caps_fd
PUBLIC 24e0 0 capng_have_capability
PUBLIC 26e0 0 capng_print_caps_numeric
PUBLIC 2960 0 capng_print_caps_text
PUBLIC 2b50 0 capng_save_state
PUBLIC 2ba8 0 capng_restore_state
PUBLIC 2c10 0 capng_apply
PUBLIC 2d90 0 capng_change_id
PUBLIC 3090 0 capng_name_to_capability
PUBLIC 3110 0 capng_capability_to_name
STACK CFI INIT 1418 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1448 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1488 48 .cfa: sp 0 + .ra: x30
STACK CFI 148c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1494 x19: .cfa -16 + ^
STACK CFI 14cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14d8 2c .cfa: sp 0 + .ra: x30
STACK CFI 14e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1500 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1508 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1524 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1530 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 156c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1570 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 15ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1618 x23: x23 x24: x24
STACK CFI 1634 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1670 x23: x23 x24: x24
STACK CFI 1690 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16f8 x23: x23 x24: x24
STACK CFI 16fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 13e0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 170c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1728 x21: .cfa -16 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17c8 ec .cfa: sp 0 + .ra: x30
STACK CFI 17cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17f0 x21: .cfa -16 + ^
STACK CFI 186c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 18b8 70 .cfa: sp 0 + .ra: x30
STACK CFI 18bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18e0 x21: .cfa -16 + ^
STACK CFI 1918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 191c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1928 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 192c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1944 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 194c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 19c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 1a1c x23: .cfa -96 + ^
STACK CFI 1a88 x23: x23
STACK CFI 1a8c x23: .cfa -96 + ^
STACK CFI 1a98 x23: x23
STACK CFI 1ad8 x23: .cfa -96 + ^
STACK CFI INIT 1ae0 174 .cfa: sp 0 + .ra: x30
STACK CFI 1ae4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1aec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1af8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1c58 38c .cfa: sp 0 + .ra: x30
STACK CFI 1c60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1fe8 144 .cfa: sp 0 + .ra: x30
STACK CFI 1fec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1ff4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2004 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2018 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 205c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2130 58 .cfa: sp 0 + .ra: x30
STACK CFI 2134 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2150 x19: .cfa -16 + ^
STACK CFI 2184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2188 1ec .cfa: sp 0 + .ra: x30
STACK CFI 218c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2194 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21b0 x21: .cfa -16 + ^
STACK CFI 2264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2268 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 22e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2378 168 .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2384 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 23a0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2494 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI INIT 24e0 200 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 258c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2590 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26e0 27c .cfa: sp 0 + .ra: x30
STACK CFI 26e4 .cfa: sp 96 +
STACK CFI 26e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 26f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2798 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 27a4 x23: .cfa -16 + ^
STACK CFI 2828 x23: x23
STACK CFI 282c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2830 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 288c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2914 x23: x23
STACK CFI 2930 x23: .cfa -16 + ^
STACK CFI 2934 x23: x23
STACK CFI 2938 x23: .cfa -16 + ^
STACK CFI 293c x23: x23
STACK CFI 2940 x23: .cfa -16 + ^
STACK CFI INIT 2960 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2970 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2994 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 29b0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 29bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 29cc x27: .cfa -32 + ^
STACK CFI 2a3c x19: x19 x20: x20
STACK CFI 2a4c x23: x23 x24: x24
STACK CFI 2a54 x27: x27
STACK CFI 2a58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2a5c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 2adc x19: x19 x20: x20
STACK CFI 2ae0 x23: x23 x24: x24
STACK CFI 2ae4 x27: x27
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2b0c .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 2b20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2b24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 2b28 x19: x19 x20: x20
STACK CFI 2b2c x23: x23 x24: x24
STACK CFI 2b30 x27: x27
STACK CFI 2b44 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 2b48 .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2b50 54 .cfa: sp 0 + .ra: x30
STACK CFI 2b54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ba8 68 .cfa: sp 0 + .ra: x30
STACK CFI 2bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bbc x19: .cfa -16 + ^
STACK CFI 2c08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c10 180 .cfa: sp 0 + .ra: x30
STACK CFI 2c14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c38 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2c98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2d90 2fc .cfa: sp 0 + .ra: x30
STACK CFI 2d94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2dbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ddc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e84 x23: x23 x24: x24
STACK CFI 2e88 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f60 x23: x23 x24: x24
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2f94 x23: x23 x24: x24
STACK CFI 2f98 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2fd8 x23: x23 x24: x24
STACK CFI 2fdc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 303c x23: x23 x24: x24
STACK CFI 3044 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 304c x23: x23 x24: x24
STACK CFI 3050 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3058 x23: x23 x24: x24
STACK CFI 305c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3064 x23: x23 x24: x24
STACK CFI 3068 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3070 x23: x23 x24: x24
STACK CFI 3074 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 307c x23: x23 x24: x24
STACK CFI 3080 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3088 x23: x23 x24: x24
STACK CFI INIT 3090 7c .cfa: sp 0 + .ra: x30
STACK CFI 3094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 309c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 30f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3110 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3114 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3120 x19: .cfa -32 + ^
STACK CFI 31d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 31d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
