MODULE Linux arm64 E033EFAA8C9B1F603588EF81367F7B500 libe2p.so.2
INFO CODE_ID AAEF33E09B8C601F3588EF81367F7B5043838E05
PUBLIC 22f8 0 e2p_feature2string
PUBLIC 23b0 0 e2p_string2feature
PUBLIC 2500 0 e2p_jrnl_feature2string
PUBLIC 25c0 0 e2p_jrnl_string2feature
PUBLIC 2718 0 e2p_edit_feature2
PUBLIC 29c8 0 e2p_edit_feature
PUBLIC 29d8 0 fgetflags
PUBLIC 2af8 0 fsetflags
PUBLIC 2bf8 0 fgetversion
PUBLIC 2cc0 0 fsetversion
PUBLIC 2d88 0 getflags
PUBLIC 2e40 0 getversion
PUBLIC 2ea0 0 e2p_hash2string
PUBLIC 2f08 0 e2p_string2hash
PUBLIC 2fe0 0 iterate_on_dir
PUBLIC 30d8 0 list_super2
PUBLIC 4448 0 list_super
PUBLIC 4458 0 e2p_list_journal_super
PUBLIC 4770 0 e2p_mntopt2string
PUBLIC 47f8 0 e2p_string2mntopt
PUBLIC 48f8 0 e2p_edit_mntopts
PUBLIC 4ab8 0 parse_num_blocks2
PUBLIC 4bc8 0 parse_num_blocks
PUBLIC 4bd0 0 print_fs_errors
PUBLIC 4c40 0 print_flags
PUBLIC 4d38 0 print_fs_state
PUBLIC 4db8 0 setflags
PUBLIC 4e68 0 setversion
PUBLIC 4ec8 0 e2p_is_null_uuid
PUBLIC 4ef0 0 e2p_uuid_to_str
PUBLIC 4fa0 0 e2p_uuid2str
PUBLIC 4ff0 0 e2p_os2string
PUBLIC 5050 0 e2p_string2os
PUBLIC 50c8 0 e2p_percent
PUBLIC 5128 0 e2p_encmode2string
PUBLIC 5190 0 e2p_string2encmode
PUBLIC 5268 0 fgetproject
PUBLIC 5320 0 fsetproject
PUBLIC 5400 0 e2p_str2encoding
PUBLIC 5450 0 e2p_encoding2str
PUBLIC 54a8 0 e2p_get_encoding_flags
PUBLIC 54b0 0 e2p_str2encoding_flags
STACK CFI INIT 2238 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2268 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 22ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22b4 x19: .cfa -16 + ^
STACK CFI 22ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 234c x19: .cfa -16 + ^
STACK CFI 2398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 239c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23b0 14c .cfa: sp 0 + .ra: x30
STACK CFI 23b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 23d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2500 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2558 x19: .cfa -16 + ^
STACK CFI 25ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 25c0 158 .cfa: sp 0 + .ra: x30
STACK CFI 25c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 25cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 25d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 25e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2668 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2718 2ac .cfa: sp 0 + .ra: x30
STACK CFI 271c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2724 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2730 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2754 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 27c4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 28a4 x25: x25 x26: x26
STACK CFI 28e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 28e8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2928 x25: x25 x26: x26
STACK CFI 292c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2984 x25: x25 x26: x26
STACK CFI 298c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 29ac x25: x25 x26: x26
STACK CFI 29c0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 29c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29d8 11c .cfa: sp 0 + .ra: x30
STACK CFI 29dc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 29e4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 29f8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a9c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 2aa0 x23: .cfa -160 + ^
STACK CFI 2ac8 x23: x23
STACK CFI 2acc x23: .cfa -160 + ^
STACK CFI 2ad0 x23: x23
STACK CFI 2af0 x23: .cfa -160 + ^
STACK CFI INIT 2af8 100 .cfa: sp 0 + .ra: x30
STACK CFI 2afc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2b04 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2b18 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2bb8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2bf8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2cc0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ccc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d28 x21: x21 x22: x22
STACK CFI 2d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d70 x21: x21 x22: x22
STACK CFI 2d74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d78 x21: x21 x22: x22
STACK CFI 2d84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2d88 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2d8c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2d94 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2db8 x21: .cfa -160 + ^
STACK CFI 2e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e20 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2e40 60 .cfa: sp 0 + .ra: x30
STACK CFI 2e44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ea0 68 .cfa: sp 0 + .ra: x30
STACK CFI 2ecc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2edc x19: .cfa -16 + ^
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f08 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2f0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f14 x21: .cfa -32 + ^
STACK CFI 2f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fd0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2fe0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2fe4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2ff4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3004 x25: .cfa -16 + ^
STACK CFI 30ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 30b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 30d8 1370 .cfa: sp 0 + .ra: x30
STACK CFI 30dc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 30e8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3114 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 311c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 3b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3b78 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4448 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4458 314 .cfa: sp 0 + .ra: x30
STACK CFI 445c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4468 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4474 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4480 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 448c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 44a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 46d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 471c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4720 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4770 88 .cfa: sp 0 + .ra: x30
STACK CFI 479c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47ac x19: .cfa -16 + ^
STACK CFI 47f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47f8 fc .cfa: sp 0 + .ra: x30
STACK CFI 47fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4804 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4814 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 482c x23: .cfa -32 + ^
STACK CFI 488c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4890 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 48f8 1bc .cfa: sp 0 + .ra: x30
STACK CFI 48fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4904 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4910 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4928 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4944 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a40 x23: x23 x24: x24
STACK CFI 4a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4a70 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4aa4 x23: x23 x24: x24
STACK CFI 4aa8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4aac x23: x23 x24: x24
STACK CFI INIT 4ab8 110 .cfa: sp 0 + .ra: x30
STACK CFI 4abc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ac8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4bc8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bd0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c40 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4c44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4c5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c64 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4cdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4d38 80 .cfa: sp 0 + .ra: x30
STACK CFI 4d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4db8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4dbc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4dc4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4de8 x21: .cfa -160 + ^
STACK CFI 4e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e4c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4e68 5c .cfa: sp 0 + .ra: x30
STACK CFI 4e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e7c x19: .cfa -32 + ^
STACK CFI 4ebc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ec0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ec8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ef0 ac .cfa: sp 0 + .ra: x30
STACK CFI 4ef4 .cfa: sp 80 +
STACK CFI 4f0c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4fa0 50 .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ff0 60 .cfa: sp 0 + .ra: x30
STACK CFI 4ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5000 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 503c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5040 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5050 74 .cfa: sp 0 + .ra: x30
STACK CFI 5054 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 505c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5068 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 50a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 50ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 50c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 50c8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5128 68 .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5164 x19: .cfa -16 + ^
STACK CFI 5188 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5190 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5194 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 519c x21: .cfa -32 + ^
STACK CFI 51a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5258 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5268 b4 .cfa: sp 0 + .ra: x30
STACK CFI 526c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5274 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5280 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5308 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5320 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 532c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5338 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 53f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5400 50 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5414 x19: .cfa -16 + ^
STACK CFI 5444 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5448 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5450 54 .cfa: sp 0 + .ra: x30
STACK CFI 545c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 546c x19: .cfa -16 + ^
STACK CFI 5494 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 54a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 54b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 54bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 54d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 54fc x23: .cfa -16 + ^
STACK CFI 5530 x23: x23
STACK CFI 5540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5544 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5570 x23: x23
STACK CFI 5578 x23: .cfa -16 + ^
