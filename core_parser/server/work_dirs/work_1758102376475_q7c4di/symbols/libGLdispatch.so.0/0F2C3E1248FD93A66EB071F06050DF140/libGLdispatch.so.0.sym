MODULE Linux arm64 0F2C3E1248FD93A66EB071F06050DF140 libGLdispatch.so.0
INFO CODE_ID 123E2C0FFD48A6936EB071F06050DF149ACF7BCB
PUBLIC 40570 0 __glDispatchGetABIVersion
PUBLIC 40578 0 __glDispatchInit
PUBLIC 40630 0 __glDispatchNewVendorID
PUBLIC 40690 0 __glDispatchGetProcAddress
PUBLIC 40780 0 __glDispatchCreateTable
PUBLIC 407b8 0 __glDispatchDestroyTable
PUBLIC 40820 0 __glDispatchRegisterStubCallbacks
PUBLIC 40890 0 __glDispatchUnregisterStubCallbacks
PUBLIC 40a08 0 __glDispatchGetCurrentThreadState
PUBLIC 40d88 0 __glDispatchForceUnpatch
PUBLIC 40e30 0 __glDispatchMakeCurrent
PUBLIC 41048 0 __glDispatchLoseCurrent
PUBLIC 41070 0 __glDispatchReset
PUBLIC 41150 0 __glDispatchFini
PUBLIC 41210 0 __glDispatchCheckMultithreaded
PUBLIC 41368 0 _glapi_get_current
STACK CFI INIT 401a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 401d8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40218 48 .cfa: sp 0 + .ra: x30
STACK CFI 4021c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40224 x19: .cfa -16 + ^
STACK CFI 4025c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40260 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40268 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40270 a0 .cfa: sp 0 + .ra: x30
STACK CFI 40278 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40280 x19: .cfa -16 + ^
STACK CFI 402fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40300 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40308 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40310 104 .cfa: sp 0 + .ra: x30
STACK CFI 40314 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40320 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4032c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40348 x23: .cfa -16 + ^
STACK CFI 40394 x23: x23
STACK CFI 403a0 x21: x21 x22: x22
STACK CFI 403a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 403a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 403d0 x23: .cfa -16 + ^
STACK CFI 403f0 x23: x23
STACK CFI 40410 x23: .cfa -16 + ^
STACK CFI INIT 40418 110 .cfa: sp 0 + .ra: x30
STACK CFI 4041c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40424 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40434 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4043c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 404e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 404ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 404fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 40500 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40528 48 .cfa: sp 0 + .ra: x30
STACK CFI 40530 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4053c x19: .cfa -16 + ^
STACK CFI 4055c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40560 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40180 14 .cfa: sp 0 + .ra: x30
STACK CFI 40184 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40190 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40578 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4057c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40584 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4058c x23: .cfa -16 + ^
STACK CFI 40594 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 405ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 405f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40630 60 .cfa: sp 0 + .ra: x30
STACK CFI 40634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4063c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4068c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 40690 ec .cfa: sp 0 + .ra: x30
STACK CFI 40694 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4069c x23: .cfa -16 + ^
STACK CFI 406a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 406b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 40758 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40780 34 .cfa: sp 0 + .ra: x30
STACK CFI 40784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4078c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 407b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 407b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 407bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 407c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 407e0 x21: .cfa -16 + ^
STACK CFI 40818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 40820 70 .cfa: sp 0 + .ra: x30
STACK CFI 40824 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4082c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40848 x21: .cfa -16 + ^
STACK CFI 4088c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 40890 d0 .cfa: sp 0 + .ra: x30
STACK CFI 40898 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 408a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 408b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 408c0 x23: .cfa -16 + ^
STACK CFI 40958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 40960 a4 .cfa: sp 0 + .ra: x30
STACK CFI 40964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40970 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 409dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 409e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40a08 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 40a28 360 .cfa: sp 0 + .ra: x30
STACK CFI 40a2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 40a38 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40a4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 40aec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40af8 x27: .cfa -16 + ^
STACK CFI 40bcc x23: x23 x24: x24
STACK CFI 40bd0 x25: x25 x26: x26
STACK CFI 40bd4 x27: x27
STACK CFI 40bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40bdc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 40be0 x25: x25 x26: x26
STACK CFI 40be4 x27: x27
STACK CFI 40c00 x23: x23 x24: x24
STACK CFI 40c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c08 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 40c18 x25: x25 x26: x26 x27: x27
STACK CFI 40c28 x23: x23 x24: x24
STACK CFI 40c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c30 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 40c88 x23: x23 x24: x24
STACK CFI 40c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c90 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 40d28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40d2c x27: .cfa -16 + ^
STACK CFI 40d30 x25: x25 x26: x26 x27: x27
STACK CFI 40d54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40d58 x27: .cfa -16 + ^
STACK CFI 40d5c x25: x25 x26: x26 x27: x27
STACK CFI 40d80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40d84 x27: .cfa -16 + ^
STACK CFI INIT 40d88 a8 .cfa: sp 0 + .ra: x30
STACK CFI 40d8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40d94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40da4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40dac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 40e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 40e10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 40e30 214 .cfa: sp 0 + .ra: x30
STACK CFI 40e34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 40e3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40e48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 40e58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 40e70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40e7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 40f04 x25: x25 x26: x26
STACK CFI 40f08 x27: x27 x28: x28
STACK CFI 40f10 x19: x19 x20: x20
STACK CFI 40f1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 40f20 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 40f94 x19: x19 x20: x20
STACK CFI 40fa0 x25: x25 x26: x26
STACK CFI 40fa4 x27: x27 x28: x28
STACK CFI 40fa8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 40fac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 40fc8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 40ff4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40ff8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 41048 24 .cfa: sp 0 + .ra: x30
STACK CFI 4104c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4105c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41064 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41070 dc .cfa: sp 0 + .ra: x30
STACK CFI 41074 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 41080 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41088 x23: .cfa -16 + ^
STACK CFI 41090 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 41150 bc .cfa: sp 0 + .ra: x30
STACK CFI 41154 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4115c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41168 x23: .cfa -16 + ^
STACK CFI 41170 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 411cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 411d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 41210 118 .cfa: sp 0 + .ra: x30
STACK CFI 41214 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4121c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 41234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 41238 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4123c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 41240 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4127c x25: .cfa -16 + ^
STACK CFI 412ac x25: x25
STACK CFI 412d4 x21: x21 x22: x22
STACK CFI 412d8 x23: x23 x24: x24
STACK CFI 412ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 412f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4130c x25: x25
STACK CFI 41318 x25: .cfa -16 + ^
STACK CFI 41324 x25: x25
STACK CFI INIT 41328 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41330 14 .cfa: sp 0 + .ra: x30
STACK CFI 41334 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41348 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41350 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41368 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41378 44 .cfa: sp 0 + .ra: x30
STACK CFI 41380 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41388 x19: .cfa -16 + ^
STACK CFI 4139c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 413a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 413b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 413c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 413c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 413d0 x19: .cfa -16 + ^
STACK CFI 413e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 413e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41404 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41410 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41418 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41420 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41430 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41448 84 .cfa: sp 0 + .ra: x30
STACK CFI 4144c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41454 x19: .cfa -16 + ^
STACK CFI 41488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4148c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 414d0 38 .cfa: sp 0 + .ra: x30
STACK CFI 414d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 414ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 414f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41504 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41508 14 .cfa: sp 0 + .ra: x30
STACK CFI 4150c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41518 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41520 88 .cfa: sp 0 + .ra: x30
STACK CFI 41524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4152c x19: .cfa -16 + ^
STACK CFI 41554 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41558 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 415a8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 415ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 415bc x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 41648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4164c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 41670 7c .cfa: sp 0 + .ra: x30
STACK CFI 41674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4167c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 416a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 416d8 x21: x21 x22: x22
STACK CFI 416e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 416f0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 416f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 416fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41710 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4171c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41754 x21: x21 x22: x22
STACK CFI 4176c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 41770 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41774 x21: x21 x22: x22
STACK CFI 41780 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 417a8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 417ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 417b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 417bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 417dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41808 x21: x21 x22: x22
STACK CFI 41818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4181c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41824 x21: x21 x22: x22
STACK CFI 41834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 41838 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41860 x21: x21 x22: x22
STACK CFI 41868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4186c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41898 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 418a0 70 .cfa: sp 0 + .ra: x30
STACK CFI 418a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 418ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 418b8 x21: .cfa -16 + ^
STACK CFI 418f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 418f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 41910 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41940 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41950 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41958 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41978 28 .cfa: sp 0 + .ra: x30
STACK CFI 4197c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4199c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 419a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 419a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 419b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 419b8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 419d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 419e8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 419ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 419f4 x19: .cfa -16 + ^
STACK CFI 41a88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41a90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41a98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41aa0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ac0 54 .cfa: sp 0 + .ra: x30
STACK CFI 41ac4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41acc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41ad4 x21: .cfa -16 + ^
STACK CFI 41b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 41b18 3c .cfa: sp 0 + .ra: x30
STACK CFI 41b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41b2c x19: .cfa -16 + ^
STACK CFI 41b50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41b58 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b78 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b88 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ba8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41bb0 34 .cfa: sp 0 + .ra: x30
STACK CFI 41bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41bbc x19: .cfa -16 + ^
STACK CFI 41be0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41be8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41bf8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c00 34 .cfa: sp 0 + .ra: x30
STACK CFI 41c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41c14 x19: .cfa -16 + ^
STACK CFI 41c30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41c38 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c50 20 .cfa: sp 0 + .ra: x30
STACK CFI 41c54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41c70 48 .cfa: sp 0 + .ra: x30
STACK CFI 41c80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41ca4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41cb8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41cc8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41cd8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ce8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41cf8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d28 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d70 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d80 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41d90 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41da0 20 .cfa: sp 0 + .ra: x30
STACK CFI 41da4 .cfa: sp 16 +
STACK CFI 41dbc .cfa: sp 0 +
STACK CFI INIT 41dc0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41dd0 28 .cfa: sp 0 + .ra: x30
STACK CFI 41dd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 41df8 2c .cfa: sp 0 + .ra: x30
STACK CFI 41dfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 41e28 18 .cfa: sp 0 + .ra: x30
STACK CFI 41e2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41e3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41e40 40 .cfa: sp 0 + .ra: x30
STACK CFI 41e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41e4c x19: .cfa -16 + ^
STACK CFI 41e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41e80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41e88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41e90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41e98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ea0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ea8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41eb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41eb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ec0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ec8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ed0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ed8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41ee0 434 .cfa: sp 0 + .ra: x30
STACK CFI 41ee4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41ef4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41f44 x21: .cfa -16 + ^
STACK CFI 421cc x21: x21
STACK CFI 422e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 422ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 422f0 x21: .cfa -16 + ^
STACK CFI INIT 42318 8c .cfa: sp 0 + .ra: x30
STACK CFI 4231c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42394 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 42398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 423a8 fc .cfa: sp 0 + .ra: x30
STACK CFI 423ac .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 423b4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 42428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4242c .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x29: .cfa -352 + ^
STACK CFI 42430 x21: .cfa -320 + ^
STACK CFI 42494 x21: x21
STACK CFI 4249c x21: .cfa -320 + ^
STACK CFI INIT 424a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 424b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 424c0 84 .cfa: sp 0 + .ra: x30
