MODULE Linux arm64 982F9838EF65EC11A29CC57A726E36110 libopencv_videostab.so.4.3
INFO CODE_ID 38982F9865EF11ECA29CC57A726E3611AD6831C5
PUBLIC f3f0 0 _init
PUBLIC fe90 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.97]
PUBLIC ff30 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.97]
PUBLIC ffd0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.68]
PUBLIC 10070 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.43]
PUBLIC 10110 0 _GLOBAL__sub_I_deblurring.cpp
PUBLIC 10140 0 _GLOBAL__sub_I_fast_marching.cpp
PUBLIC 10170 0 _GLOBAL__sub_I_frame_source.cpp
PUBLIC 101a0 0 _GLOBAL__sub_I_global_motion.cpp
PUBLIC 101d0 0 _GLOBAL__sub_I_inpainting.cpp
PUBLIC 10200 0 _GLOBAL__sub_I_log.cpp
PUBLIC 10230 0 _GLOBAL__sub_I_motion_stabilizing.cpp
PUBLIC 10260 0 _GLOBAL__sub_I_optical_flow.cpp
PUBLIC 10290 0 _GLOBAL__sub_I_outlier_rejection.cpp
PUBLIC 102c0 0 _GLOBAL__sub_I_stabilizer.cpp
PUBLIC 102f0 0 _GLOBAL__sub_I_wobble_suppression.cpp
PUBLIC 10320 0 call_weak_fn
PUBLIC 10338 0 deregister_tm_clones
PUBLIC 10370 0 register_tm_clones
PUBLIC 103b0 0 __do_global_dtors_aux
PUBLIC 103f8 0 frame_dummy
PUBLIC 10430 0 cv::videostab::DeblurerBase::setRadius(int)
PUBLIC 10438 0 cv::videostab::DeblurerBase::radius() const
PUBLIC 10440 0 cv::videostab::DeblurerBase::setFrames(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 10448 0 cv::videostab::DeblurerBase::frames() const
PUBLIC 10450 0 cv::videostab::DeblurerBase::setMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 10458 0 cv::videostab::DeblurerBase::motions() const
PUBLIC 10460 0 cv::videostab::DeblurerBase::setBlurrinessRates(std::vector<float, std::allocator<float> > const&)
PUBLIC 10468 0 cv::videostab::DeblurerBase::blurrinessRates() const
PUBLIC 10470 0 cv::videostab::WeightingDeblurer::~WeightingDeblurer()
PUBLIC 106b0 0 cv::videostab::WeightingDeblurer::~WeightingDeblurer()
PUBLIC 108f8 0 cv::Mat::~Mat()
PUBLIC 10990 0 cv::videostab::WeightingDeblurer::deblur(int, cv::Mat&, cv::Range const&)
PUBLIC 11a60 0 cv::videostab::calcBlurriness(cv::Mat const&)
PUBLIC 11d78 0 cv::videostab::WeightingDeblurer::WeightingDeblurer()
PUBLIC 11e58 0 cv::videostab::FastMarchingMethod::solve(int, int, int, int) const
PUBLIC 12000 0 cv::videostab::FastMarchingMethod::heapUp(int)
PUBLIC 120c0 0 cv::videostab::FastMarchingMethod::heapDown(int)
PUBLIC 121b8 0 cv::videostab::FastMarchingMethod::heapRemoveMin()
PUBLIC 12250 0 std::vector<cv::videostab::FastMarchingMethod::DXY, std::allocator<cv::videostab::FastMarchingMethod::DXY> >::_M_default_append(unsigned long)
PUBLIC 123d8 0 cv::videostab::FastMarchingMethod::heapAdd(cv::videostab::FastMarchingMethod::DXY const&)
PUBLIC 124a8 0 cv::videostab::VideoFileSource::reset()
PUBLIC 124c0 0 cv::videostab::VideoFileSource::nextFrame()
PUBLIC 124e8 0 std::_Sp_counted_ptr<cv::videostab::(anonymous namespace)::VideoFileSourceImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 124f0 0 std::_Sp_counted_ptr<cv::videostab::(anonymous namespace)::VideoFileSourceImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 124f8 0 std::_Sp_counted_ptr<cv::videostab::(anonymous namespace)::VideoFileSourceImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 12500 0 std::_Sp_counted_ptr<cv::videostab::(anonymous namespace)::VideoFileSourceImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 12508 0 cv::videostab::VideoFileSource::~VideoFileSource()
PUBLIC 125e0 0 cv::videostab::VideoFileSource::~VideoFileSource()
PUBLIC 126b0 0 cv::videostab::(anonymous namespace)::VideoFileSourceImpl::nextFrame()
PUBLIC 128c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(char const*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 12970 0 cv::videostab::(anonymous namespace)::VideoFileSourceImpl::~VideoFileSourceImpl()
PUBLIC 129b0 0 cv::videostab::(anonymous namespace)::VideoFileSourceImpl::~VideoFileSourceImpl()
PUBLIC 129f0 0 std::_Sp_counted_ptr<cv::videostab::(anonymous namespace)::VideoFileSourceImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 12a40 0 cv::videostab::(anonymous namespace)::VideoFileSourceImpl::reset()
PUBLIC 12ae0 0 cv::videostab::VideoFileSource::VideoFileSource(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, bool)
PUBLIC 12cf8 0 cv::videostab::VideoFileSource::width()
PUBLIC 12d18 0 cv::videostab::VideoFileSource::height()
PUBLIC 12d38 0 cv::videostab::VideoFileSource::count()
PUBLIC 12d58 0 cv::videostab::VideoFileSource::fps()
PUBLIC 12d68 0 std::ctype<char>::do_widen(char) const
PUBLIC 12d70 0 cv::videostab::MotionEstimatorBase::setMotionModel(cv::videostab::MotionModel)
PUBLIC 12d78 0 cv::videostab::MotionEstimatorBase::motionModel() const
PUBLIC 12d80 0 cv::videostab::ImageMotionEstimatorBase::setMotionModel(cv::videostab::MotionModel)
PUBLIC 12d88 0 cv::videostab::ImageMotionEstimatorBase::motionModel() const
PUBLIC 12d90 0 cv::videostab::ToFileMotionWriter::setMotionModel(cv::videostab::MotionModel)
PUBLIC 12da8 0 cv::videostab::ToFileMotionWriter::motionModel() const
PUBLIC 12dc0 0 cv::videostab::ToFileMotionWriter::setFrameMask(cv::_InputArray const&)
PUBLIC 12dd8 0 cv::videostab::MotionEstimatorRansacL2::~MotionEstimatorRansacL2()
PUBLIC 12de0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullOutlierRejector, std::allocator<cv::videostab::NullOutlierRejector>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 12de8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullOutlierRejector, std::allocator<cv::videostab::NullOutlierRejector>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 12e00 0 std::_Sp_counted_ptr_inplace<cv::videostab::SparsePyrLkOptFlowEstimator, std::allocator<cv::videostab::SparsePyrLkOptFlowEstimator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 12e08 0 std::_Sp_counted_ptr_inplace<cv::videostab::SparsePyrLkOptFlowEstimator, std::allocator<cv::videostab::SparsePyrLkOptFlowEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 12e20 0 cv::videostab::MotionEstimatorRansacL2::~MotionEstimatorRansacL2()
PUBLIC 12e28 0 cv::videostab::MotionEstimatorL1::~MotionEstimatorL1()
PUBLIC 12eb0 0 std::_Sp_counted_ptr_inplace<cv::videostab::SparsePyrLkOptFlowEstimator, std::allocator<cv::videostab::SparsePyrLkOptFlowEstimator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 12eb8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullOutlierRejector, std::allocator<cv::videostab::NullOutlierRejector>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 12ec0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullOutlierRejector, std::allocator<cv::videostab::NullOutlierRejector>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 12ec8 0 std::_Sp_counted_ptr_inplace<cv::videostab::SparsePyrLkOptFlowEstimator, std::allocator<cv::videostab::SparsePyrLkOptFlowEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 12ed0 0 std::_Sp_counted_ptr_inplace<cv::videostab::SparsePyrLkOptFlowEstimator, std::allocator<cv::videostab::SparsePyrLkOptFlowEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 12f20 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullOutlierRejector, std::allocator<cv::videostab::NullOutlierRejector>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 12f70 0 cv::videostab::ImageMotionEstimatorBase::setFrameMask(cv::_InputArray const&)
PUBLIC 12ff0 0 cv::videostab::KeypointBasedMotionEstimator::setMotionModel(cv::videostab::MotionModel)
PUBLIC 13020 0 cv::videostab::KeypointBasedMotionEstimator::motionModel() const
PUBLIC 13050 0 cv::videostab::FromFileMotionReader::~FromFileMotionReader()
PUBLIC 13108 0 cv::videostab::FromFileMotionReader::~FromFileMotionReader()
PUBLIC 131c8 0 cv::videostab::MotionEstimatorL1::~MotionEstimatorL1()
PUBLIC 13250 0 cv::videostab::ToFileMotionWriter::~ToFileMotionWriter()
PUBLIC 133a8 0 cv::videostab::ToFileMotionWriter::~ToFileMotionWriter()
PUBLIC 134f8 0 cv::videostab::KeypointBasedMotionEstimator::~KeypointBasedMotionEstimator()
PUBLIC 13890 0 cv::videostab::KeypointBasedMotionEstimator::~KeypointBasedMotionEstimator()
PUBLIC 13c30 0 cv::videostab::KeypointBasedMotionEstimator::setFrameMask(cv::_InputArray const&)
PUBLIC 13ee0 0 cv::videostab::MotionEstimatorL1::estimate(cv::_InputArray const&, cv::_InputArray const&, bool*)
PUBLIC 14350 0 cv::videostab::FromFileMotionReader::estimate(cv::Mat const&, cv::Mat const&, bool*)
PUBLIC 14550 0 cv::videostab::ToFileMotionWriter::estimate(cv::Mat const&, cv::Mat const&, bool*)
PUBLIC 14ba0 0 cv::MatExpr::~MatExpr()
PUBLIC 14d50 0 cv::videostab::normalizePoints(int, cv::Point_<float>*)
PUBLIC 15580 0 cv::videostab::estimateGlobMotionLeastSquaresRotation(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 15e10 0 cv::videostab::estimateGlobMotionLeastSquaresTranslation(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 16650 0 cv::videostab::estimateGlobalMotionLeastSquares(cv::_InputOutputArray const&, cv::_InputOutputArray const&, int, float*)
PUBLIC 16d10 0 cv::videostab::MotionEstimatorRansacL2::MotionEstimatorRansacL2(cv::videostab::MotionModel)
PUBLIC 16e10 0 cv::videostab::MotionEstimatorL1::MotionEstimatorL1(cv::videostab::MotionModel)
PUBLIC 16e60 0 cv::videostab::FromFileMotionReader::FromFileMotionReader(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 17050 0 cv::videostab::getMotion(int, int, std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 176e0 0 cv::Mat_<float>::operator=(cv::Mat&&)
PUBLIC 17b10 0 cv::videostab::estimateGlobMotionLeastSquaresSimilarity(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 18b80 0 cv::videostab::estimateGlobMotionLeastSquaresRigid(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 19c70 0 cv::videostab::estimateGlobMotionLeastSquaresTranslationAndScale(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 1ae30 0 cv::videostab::estimateGlobMotionLeastSquaresAffine(int, cv::Point_<float>*, cv::Point_<float>*, float*)
PUBLIC 1bea8 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::reserve(unsigned long)
PUBLIC 1bf90 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 1c048 0 cv::videostab::ToFileMotionWriter::ToFileMotionWriter(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::Ptr<cv::videostab::ImageMotionEstimatorBase>)
PUBLIC 1c290 0 cv::videostab::KeypointBasedMotionEstimator::KeypointBasedMotionEstimator(cv::Ptr<cv::videostab::MotionEstimatorBase>)
PUBLIC 1ca40 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_default_append(unsigned long)
PUBLIC 1cb90 0 cv::videostab::estimateGlobalMotionRansac(cv::_InputArray const&, cv::_InputArray const&, int, cv::videostab::RansacParams const&, float*, int*)
PUBLIC 1e5a0 0 cv::videostab::MotionEstimatorRansacL2::estimate(cv::_InputArray const&, cv::_InputArray const&, bool*)
PUBLIC 1ecb0 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> const&>(cv::Point_<float> const&)
PUBLIC 1edb0 0 cv::videostab::KeypointBasedMotionEstimator::estimate(cv::_InputArray const&, cv::_InputArray const&, bool*)
PUBLIC 1f440 0 cv::videostab::KeypointBasedMotionEstimator::estimate(cv::Mat const&, cv::Mat const&, bool*)
PUBLIC 1f480 0 cv::videostab::InpainterBase::setRadius(int)
PUBLIC 1f488 0 cv::videostab::InpainterBase::radius() const
PUBLIC 1f490 0 cv::videostab::InpainterBase::setMotionModel(cv::videostab::MotionModel)
PUBLIC 1f498 0 cv::videostab::InpainterBase::motionModel() const
PUBLIC 1f4a0 0 cv::videostab::InpainterBase::setFrames(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f4a8 0 cv::videostab::InpainterBase::frames() const
PUBLIC 1f4b0 0 cv::videostab::InpainterBase::setMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f4b8 0 cv::videostab::InpainterBase::motions() const
PUBLIC 1f4c0 0 cv::videostab::InpainterBase::setStabilizedFrames(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f4c8 0 cv::videostab::InpainterBase::stabilizedFrames() const
PUBLIC 1f4d0 0 cv::videostab::InpainterBase::setStabilizationMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f4d8 0 cv::videostab::InpainterBase::stabilizationMotions() const
PUBLIC 1f4e0 0 cv::videostab::InpaintingPipeline::setRadius(int)
PUBLIC 1f548 0 cv::videostab::InpaintingPipeline::setFrames(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f5b0 0 cv::videostab::InpaintingPipeline::setMotionModel(cv::videostab::MotionModel)
PUBLIC 1f618 0 cv::videostab::InpaintingPipeline::setMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f680 0 cv::videostab::InpaintingPipeline::setStabilizedFrames(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f6e8 0 cv::videostab::InpaintingPipeline::setStabilizationMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 1f750 0 cv::videostab::ConsistentMosaicInpainter::~ConsistentMosaicInpainter()
PUBLIC 1f758 0 cv::videostab::InpaintingPipeline::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 1f818 0 cv::videostab::ConsistentMosaicInpainter::~ConsistentMosaicInpainter()
PUBLIC 1f820 0 cv::videostab::ColorInpainter::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 1f920 0 cv::videostab::InpaintingPipeline::~InpaintingPipeline()
PUBLIC 1fa70 0 cv::videostab::InpaintingPipeline::~InpaintingPipeline()
PUBLIC 1fbc8 0 cv::videostab::ColorInpainter::~ColorInpainter()
PUBLIC 1fc80 0 cv::videostab::ColorAverageInpainter::~ColorAverageInpainter()
PUBLIC 1fe50 0 cv::videostab::MotionInpainter::~MotionInpainter()
PUBLIC 205f0 0 cv::videostab::ColorAverageInpainter::~ColorAverageInpainter()
PUBLIC 207b8 0 cv::videostab::ColorInpainter::~ColorInpainter()
PUBLIC 20868 0 cv::videostab::MotionInpainter::~MotionInpainter()
PUBLIC 21010 0 cv::Mat::operator=(cv::Mat const&)
PUBLIC 21130 0 cv::Mat::create(int, int, int)
PUBLIC 21190 0 cv::Mat::release()
PUBLIC 21210 0 cv::Mat::operator=(cv::Mat&&)
PUBLIC 21340 0 cv::videostab::ConsistentMosaicInpainter::ConsistentMosaicInpainter()
PUBLIC 21370 0 cv::videostab::FastMarchingMethod::~FastMarchingMethod()
PUBLIC 21530 0 cv::videostab::MotionInpaintBody::~MotionInpaintBody()
PUBLIC 217f0 0 cv::videostab::calcFlowMask(cv::Mat const&, cv::Mat const&, cv::Mat const&, float, cv::Mat const&, cv::Mat const&, cv::Mat&)
PUBLIC 229e0 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 22aa0 0 cv::Mat_<cv::Point3_<unsigned char> >::operator=(cv::Mat const&)
PUBLIC 22d90 0 cv::Mat_<unsigned char>::operator=(cv::Mat const&)
PUBLIC 23080 0 cv::videostab::completeFrameAccordingToFlow(cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, float, cv::Mat&, cv::Mat&)
PUBLIC 23c40 0 cv::videostab::MotionInpainter::MotionInpainter()
PUBLIC 23fe0 0 void std::__insertion_sort<__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 24108 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<std::pair<float, int>*, std::vector<std::pair<float, int>, std::allocator<std::pair<float, int> > > >, long, std::pair<float, int>, __gnu_cxx::__ops::_Iter_comp_iter<std::less<std::pair<float, int> > > >(__gnu_cxx::__normal_iterator<std::pair<float, int>*, std::vector<std::pair<float, int>, std::allocator<std::pair<float, int> > > >, long, long, std::pair<float, int>, __gnu_cxx::__ops::_Iter_comp_iter<std::less<std::pair<float, int> > >)
PUBLIC 242a8 0 void std::vector<std::pair<float, int>, std::allocator<std::pair<float, int> > >::_M_emplace_back_aux<std::pair<float, int> >(std::pair<float, int>&&)
PUBLIC 243a0 0 void std::vector<cv::videostab::FastMarchingMethod::DXY, std::allocator<cv::videostab::FastMarchingMethod::DXY> >::_M_emplace_back_aux<cv::videostab::FastMarchingMethod::DXY>(cv::videostab::FastMarchingMethod::DXY&&)
PUBLIC 244e0 0 cv::videostab::ColorAverageInpaintBody cv::videostab::FastMarchingMethod::run<cv::videostab::ColorAverageInpaintBody>(cv::Mat const&, cv::videostab::ColorAverageInpaintBody)
PUBLIC 24f10 0 cv::videostab::ColorAverageInpainter::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 256a0 0 cv::videostab::MotionInpaintBody cv::videostab::FastMarchingMethod::run<cv::videostab::MotionInpaintBody>(cv::Mat const&, cv::videostab::MotionInpaintBody)
PUBLIC 26d90 0 cv::videostab::MotionInpainter::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 29a00 0 void std::__move_median_to_first<__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 29b10 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, long, cv::videostab::Pixel3, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, long, long, cv::videostab::Pixel3, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 29c90 0 void std::__introsort_loop<__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, long, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, __gnu_cxx::__normal_iterator<cv::videostab::Pixel3*, std::vector<cv::videostab::Pixel3, std::allocator<cv::videostab::Pixel3> > >, long, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.151]
PUBLIC 29ea0 0 cv::videostab::ConsistentMosaicInpainter::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 2b460 0 cv::videostab::LogToStdout::~LogToStdout()
PUBLIC 2b468 0 cv::videostab::LogToStdout::print(char const*, ...)
PUBLIC 2b4f8 0 cv::videostab::LogToStdout::~LogToStdout()
PUBLIC 2b500 0 cv::videostab::LpMotionStabilizer::~LpMotionStabilizer()
PUBLIC 2b588 0 cv::videostab::GaussianMotionFilter::~GaussianMotionFilter()
PUBLIC 2b5b0 0 cv::videostab::GaussianMotionFilter::~GaussianMotionFilter()
PUBLIC 2b5e0 0 cv::videostab::MotionStabilizationPipeline::~MotionStabilizationPipeline()
PUBLIC 2b730 0 cv::videostab::LpMotionStabilizer::stabilize(int, std::vector<cv::Mat, std::allocator<cv::Mat> > const&, cv::Range const&, cv::Mat*)
PUBLIC 2b7d8 0 cv::videostab::MotionStabilizationPipeline::~MotionStabilizationPipeline()
PUBLIC 2b930 0 cv::videostab::LpMotionStabilizer::~LpMotionStabilizer()
PUBLIC 2b9c0 0 cv::videostab::MotionFilterBase::stabilize(int, std::vector<cv::Mat, std::allocator<cv::Mat> > const&, cv::Range const&, cv::Mat*)
PUBLIC 2bc10 0 cv::videostab::GaussianMotionFilter::stabilize(int, std::vector<cv::Mat, std::allocator<cv::Mat> > const&, cv::Range const&)
PUBLIC 2c420 0 cv::videostab::LpMotionStabilizer::LpMotionStabilizer(cv::videostab::MotionModel)
PUBLIC 2c4a0 0 cv::videostab::ensureInclusionConstraint(cv::Mat const&, cv::Size_<int>, float)
PUBLIC 2d960 0 cv::videostab::estimateOptimalTrimRatio(cv::Mat const&, cv::Size_<int>)
PUBLIC 2e2f0 0 cv::videostab::MotionStabilizationPipeline::stabilize(int, std::vector<cv::Mat, std::allocator<cv::Mat> > const&, cv::Range const&, cv::Mat*)
PUBLIC 2f440 0 std::vector<float, std::allocator<float> >::_M_default_append(unsigned long)
PUBLIC 2f590 0 cv::videostab::GaussianMotionFilter::setParams(int, float)
PUBLIC 2f7e8 0 cv::videostab::PyrLkOptFlowEstimatorBase::setWinSize(cv::Size_<int>)
PUBLIC 2f7f8 0 cv::videostab::PyrLkOptFlowEstimatorBase::winSize() const
PUBLIC 2f808 0 cv::videostab::PyrLkOptFlowEstimatorBase::setMaxLevel(int)
PUBLIC 2f810 0 cv::videostab::PyrLkOptFlowEstimatorBase::maxLevel() const
PUBLIC 2f818 0 cv::videostab::SparsePyrLkOptFlowEstimator::~SparsePyrLkOptFlowEstimator()
PUBLIC 2f820 0 non-virtual thunk to cv::videostab::SparsePyrLkOptFlowEstimator::~SparsePyrLkOptFlowEstimator()
PUBLIC 2f828 0 cv::videostab::SparsePyrLkOptFlowEstimator::run(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 2f8a0 0 non-virtual thunk to cv::videostab::SparsePyrLkOptFlowEstimator::run(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 2f8a8 0 cv::videostab::SparsePyrLkOptFlowEstimator::~SparsePyrLkOptFlowEstimator()
PUBLIC 2f8b0 0 non-virtual thunk to cv::videostab::SparsePyrLkOptFlowEstimator::~SparsePyrLkOptFlowEstimator()
PUBLIC 2f8b8 0 cv::videostab::NullOutlierRejector::~NullOutlierRejector()
PUBLIC 2f8c0 0 cv::videostab::NullOutlierRejector::~NullOutlierRejector()
PUBLIC 2f8c8 0 cv::videostab::TranslationBasedLocalOutlierRejector::~TranslationBasedLocalOutlierRejector()
PUBLIC 2f940 0 cv::videostab::TranslationBasedLocalOutlierRejector::~TranslationBasedLocalOutlierRejector()
PUBLIC 2f9b0 0 cv::videostab::NullOutlierRejector::process(cv::Size_<int>, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 30078 0 cv::videostab::TranslationBasedLocalOutlierRejector::TranslationBasedLocalOutlierRejector()
PUBLIC 300c0 0 std::vector<int, std::allocator<int> >::_M_default_append(unsigned long)
PUBLIC 30210 0 std::vector<int, std::allocator<int> >::operator=(std::vector<int, std::allocator<int> > const&)
PUBLIC 30360 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::_M_fill_assign(unsigned long, std::vector<int, std::allocator<int> > const&)
PUBLIC 307b0 0 cv::videostab::TranslationBasedLocalOutlierRejector::process(cv::Size_<int>, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 31638 0 cv::videostab::NullFrameSource::reset()
PUBLIC 31640 0 cv::videostab::NullFrameSource::nextFrame()
PUBLIC 31690 0 cv::videostab::NullInpainter::inpaint(int, cv::Mat&, cv::Mat&)
PUBLIC 31698 0 cv::videostab::NullDeblurer::deblur(int, cv::Mat&, cv::Range const&)
PUBLIC 316a0 0 cv::videostab::WobbleSuppressorBase::setFrameCount(int)
PUBLIC 316a8 0 cv::videostab::WobbleSuppressorBase::setMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 316b0 0 cv::videostab::WobbleSuppressorBase::setMotions2(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 316b8 0 cv::videostab::WobbleSuppressorBase::setStabilizationMotions(std::vector<cv::Mat, std::allocator<cv::Mat> > const&)
PUBLIC 316c0 0 cv::videostab::OnePassStabilizer::estimateStabilizationMotion()
PUBLIC 316f8 0 cv::videostab::NullDeblurer::~NullDeblurer()
PUBLIC 31700 0 cv::videostab::NullInpainter::~NullInpainter()
PUBLIC 31708 0 cv::videostab::NullFrameSource::~NullFrameSource()
PUBLIC 31710 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullWobbleSuppressor, std::allocator<cv::videostab::NullWobbleSuppressor>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31718 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullWobbleSuppressor, std::allocator<cv::videostab::NullWobbleSuppressor>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 31730 0 std::_Sp_counted_ptr_inplace<cv::videostab::GaussianMotionFilter, std::allocator<cv::videostab::GaussianMotionFilter>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31738 0 std::_Sp_counted_ptr_inplace<cv::videostab::GaussianMotionFilter, std::allocator<cv::videostab::GaussianMotionFilter>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 31750 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullInpainter, std::allocator<cv::videostab::NullInpainter>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31758 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullInpainter, std::allocator<cv::videostab::NullInpainter>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 31770 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullDeblurer, std::allocator<cv::videostab::NullDeblurer>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31778 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullDeblurer, std::allocator<cv::videostab::NullDeblurer>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 31790 0 std::_Sp_counted_ptr_inplace<cv::videostab::KeypointBasedMotionEstimator, std::allocator<cv::videostab::KeypointBasedMotionEstimator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31798 0 std::_Sp_counted_ptr_inplace<cv::videostab::KeypointBasedMotionEstimator, std::allocator<cv::videostab::KeypointBasedMotionEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 317b0 0 std::_Sp_counted_ptr_inplace<cv::videostab::MotionEstimatorRansacL2, std::allocator<cv::videostab::MotionEstimatorRansacL2>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 317b8 0 std::_Sp_counted_ptr_inplace<cv::videostab::MotionEstimatorRansacL2, std::allocator<cv::videostab::MotionEstimatorRansacL2>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 317d0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullFrameSource, std::allocator<cv::videostab::NullFrameSource>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 317d8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullFrameSource, std::allocator<cv::videostab::NullFrameSource>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 317f0 0 std::_Sp_counted_ptr_inplace<cv::videostab::LogToStdout, std::allocator<cv::videostab::LogToStdout>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 317f8 0 std::_Sp_counted_ptr_inplace<cv::videostab::LogToStdout, std::allocator<cv::videostab::LogToStdout>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 31810 0 std::_Sp_counted_ptr_inplace<cv::videostab::LogToStdout, std::allocator<cv::videostab::LogToStdout>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 31860 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullFrameSource, std::allocator<cv::videostab::NullFrameSource>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 318b0 0 std::_Sp_counted_ptr_inplace<cv::videostab::MotionEstimatorRansacL2, std::allocator<cv::videostab::MotionEstimatorRansacL2>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 31900 0 std::_Sp_counted_ptr_inplace<cv::videostab::KeypointBasedMotionEstimator, std::allocator<cv::videostab::KeypointBasedMotionEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 31950 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullDeblurer, std::allocator<cv::videostab::NullDeblurer>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 319a0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullInpainter, std::allocator<cv::videostab::NullInpainter>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 319f0 0 std::_Sp_counted_ptr_inplace<cv::videostab::GaussianMotionFilter, std::allocator<cv::videostab::GaussianMotionFilter>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 31a40 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullWobbleSuppressor, std::allocator<cv::videostab::NullWobbleSuppressor>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 31a90 0 cv::videostab::NullFrameSource::~NullFrameSource()
PUBLIC 31a98 0 cv::videostab::NullInpainter::~NullInpainter()
PUBLIC 31aa0 0 cv::videostab::NullDeblurer::~NullDeblurer()
PUBLIC 31aa8 0 std::_Sp_counted_ptr_inplace<cv::videostab::LogToStdout, std::allocator<cv::videostab::LogToStdout>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ab0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullFrameSource, std::allocator<cv::videostab::NullFrameSource>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ab8 0 std::_Sp_counted_ptr_inplace<cv::videostab::MotionEstimatorRansacL2, std::allocator<cv::videostab::MotionEstimatorRansacL2>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ac0 0 std::_Sp_counted_ptr_inplace<cv::videostab::KeypointBasedMotionEstimator, std::allocator<cv::videostab::KeypointBasedMotionEstimator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ac8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullDeblurer, std::allocator<cv::videostab::NullDeblurer>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ad0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullInpainter, std::allocator<cv::videostab::NullInpainter>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ad8 0 std::_Sp_counted_ptr_inplace<cv::videostab::GaussianMotionFilter, std::allocator<cv::videostab::GaussianMotionFilter>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ae0 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullWobbleSuppressor, std::allocator<cv::videostab::NullWobbleSuppressor>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 31ae8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullWobbleSuppressor, std::allocator<cv::videostab::NullWobbleSuppressor>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31af0 0 std::_Sp_counted_ptr_inplace<cv::videostab::GaussianMotionFilter, std::allocator<cv::videostab::GaussianMotionFilter>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31af8 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullInpainter, std::allocator<cv::videostab::NullInpainter>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b00 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullDeblurer, std::allocator<cv::videostab::NullDeblurer>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b08 0 std::_Sp_counted_ptr_inplace<cv::videostab::KeypointBasedMotionEstimator, std::allocator<cv::videostab::KeypointBasedMotionEstimator>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b10 0 std::_Sp_counted_ptr_inplace<cv::videostab::MotionEstimatorRansacL2, std::allocator<cv::videostab::MotionEstimatorRansacL2>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b18 0 std::_Sp_counted_ptr_inplace<cv::videostab::NullFrameSource, std::allocator<cv::videostab::NullFrameSource>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b20 0 std::_Sp_counted_ptr_inplace<cv::videostab::LogToStdout, std::allocator<cv::videostab::LogToStdout>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 31b28 0 cv::videostab::OnePassStabilizer::estimateMotion()
PUBLIC 31bd8 0 cv::videostab::StabilizerBase::postProcessFrame(cv::Mat const&)
PUBLIC 31c28 0 cv::videostab::OnePassStabilizer::postProcessFrame(cv::Mat const&)
PUBLIC 31c40 0 cv::videostab::TwoPassStabilizer::postProcessFrame(cv::Mat const&)
PUBLIC 31ca0 0 cv::videostab::TwoPassStabilizer::estimateMotion()
PUBLIC 31d40 0 cv::videostab::TwoPassStabilizer::estimateStabilizationMotion()
PUBLIC 31de0 0 cv::videostab::StabilizerBase::~StabilizerBase()
PUBLIC 32708 0 cv::videostab::OnePassStabilizer::~OnePassStabilizer()
PUBLIC 327e8 0 non-virtual thunk to cv::videostab::OnePassStabilizer::~OnePassStabilizer()
PUBLIC 327f0 0 cv::videostab::TwoPassStabilizer::~TwoPassStabilizer()
PUBLIC 32a88 0 non-virtual thunk to cv::videostab::TwoPassStabilizer::~TwoPassStabilizer()
PUBLIC 32a90 0 cv::videostab::OnePassStabilizer::~OnePassStabilizer()
PUBLIC 32b78 0 non-virtual thunk to cv::videostab::OnePassStabilizer::~OnePassStabilizer()
PUBLIC 32b80 0 cv::videostab::TwoPassStabilizer::~TwoPassStabilizer()
PUBLIC 32e20 0 non-virtual thunk to cv::videostab::TwoPassStabilizer::~TwoPassStabilizer()
PUBLIC 32e28 0 cv::videostab::StabilizerBase::~StabilizerBase()
PUBLIC 32e40 0 cv::videostab::StabilizerBase::reset()
PUBLIC 33630 0 cv::videostab::OnePassStabilizer::reset()
PUBLIC 33638 0 non-virtual thunk to cv::videostab::OnePassStabilizer::reset()
PUBLIC 33640 0 cv::videostab::TwoPassStabilizer::reset()
PUBLIC 338b0 0 non-virtual thunk to cv::videostab::TwoPassStabilizer::reset()
PUBLIC 338c0 0 cv::videostab::StabilizerBase::stabilizeFrame()
PUBLIC 34440 0 cv::videostab::StabilizerBase::doOneIteration()
PUBLIC 34b70 0 cv::videostab::StabilizerBase::logProcessingTime()
PUBLIC 34bc0 0 cv::videostab::StabilizerBase::nextStabilizedFrame()
PUBLIC 34cc0 0 cv::videostab::OnePassStabilizer::nextFrame()
PUBLIC 34cd8 0 non-virtual thunk to cv::videostab::OnePassStabilizer::nextFrame()
PUBLIC 34ce0 0 cv::videostab::StabilizerBase::StabilizerBase()
PUBLIC 359f0 0 cv::videostab::OnePassStabilizer::OnePassStabilizer()
PUBLIC 35c60 0 cv::videostab::TwoPassStabilizer::TwoPassStabilizer()
PUBLIC 36120 0 cv::videostab::StabilizerBase::setUp(cv::Mat const&)
PUBLIC 363f0 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_default_append(unsigned long)
PUBLIC 36778 0 cv::videostab::TwoPassStabilizer::setUp(cv::Mat const&)
PUBLIC 36c90 0 cv::videostab::OnePassStabilizer::setUp(cv::Mat const&)
PUBLIC 37600 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 37950 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC 37ca0 0 cv::videostab::TwoPassStabilizer::runPrePassIfNecessary()
PUBLIC 38ea0 0 cv::videostab::TwoPassStabilizer::nextFrame()
PUBLIC 38ed0 0 non-virtual thunk to cv::videostab::TwoPassStabilizer::nextFrame()
PUBLIC 38ed8 0 cv::videostab::WobbleSuppressorBase::frameCount() const
PUBLIC 38ee0 0 cv::videostab::WobbleSuppressorBase::motions() const
PUBLIC 38ee8 0 cv::videostab::WobbleSuppressorBase::motions2() const
PUBLIC 38ef0 0 cv::videostab::WobbleSuppressorBase::stabilizationMotions() const
PUBLIC 38ef8 0 cv::videostab::MoreAccurateMotionWobbleSuppressorBase::setPeriod(int)
PUBLIC 38f00 0 cv::videostab::MoreAccurateMotionWobbleSuppressorBase::period() const
PUBLIC 38f08 0 cv::videostab::NullWobbleSuppressor::suppress(int, cv::Mat const&, cv::Mat&)
PUBLIC 39030 0 cv::videostab::MoreAccurateMotionWobbleSuppressor::~MoreAccurateMotionWobbleSuppressor()
PUBLIC 39220 0 cv::videostab::NullWobbleSuppressor::~NullWobbleSuppressor()
PUBLIC 392e8 0 cv::videostab::NullWobbleSuppressor::~NullWobbleSuppressor()
PUBLIC 393c0 0 cv::videostab::MoreAccurateMotionWobbleSuppressor::~MoreAccurateMotionWobbleSuppressor()
PUBLIC 395b0 0 cv::videostab::MoreAccurateMotionWobbleSuppressor::suppress(int, cv::Mat const&, cv::Mat&)
PUBLIC 3ba90 0 cv::videostab::WobbleSuppressorBase::WobbleSuppressorBase()
PUBLIC 3beac 0 _fini
STACK CFI INIT 10430 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10438 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10448 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10458 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10468 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10470 23c .cfa: sp 0 + .ra: x30
STACK CFI 10474 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10484 .ra: .cfa -16 + ^
STACK CFI 10668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 10670 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 106b0 244 .cfa: sp 0 + .ra: x30
STACK CFI 106b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 106c4 .ra: .cfa -16 + ^
STACK CFI 108a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 108a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 108f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 108f8 90 .cfa: sp 0 + .ra: x30
STACK CFI 108fc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10970 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 10978 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10984 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 10990 10a8 .cfa: sp 0 + .ra: x30
STACK CFI 10994 .cfa: sp 624 +
STACK CFI 10998 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 109d8 .ra: .cfa -544 + ^ v10: .cfa -512 + ^ v11: .cfa -504 + ^ v12: .cfa -496 + ^ v13: .cfa -488 + ^ v14: .cfa -536 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 117d0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 117d4 .cfa: sp 624 + .ra: .cfa -544 + ^ v10: .cfa -512 + ^ v11: .cfa -504 + ^ v12: .cfa -496 + ^ v13: .cfa -488 + ^ v14: .cfa -536 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 11a60 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 11a64 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 11a70 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 11a78 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 11a88 .ra: .cfa -272 + ^ v8: .cfa -264 + ^
STACK CFI 11d08 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 11d10 .cfa: sp 320 + .ra: .cfa -272 + ^ v8: .cfa -264 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI INIT 11d78 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT 10110 30 .cfa: sp 0 + .ra: x30
STACK CFI 10114 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10130 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11e58 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 11e5c .cfa: sp 48 + .ra: .cfa -48 + ^
STACK CFI 11e64 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 11ed8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI 11ee0 .cfa: sp 48 + .ra: .cfa -48 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 11f60 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI 11f68 .cfa: sp 48 + .ra: .cfa -48 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 11f94 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI 11f98 .cfa: sp 48 + .ra: .cfa -48 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI INIT 12000 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 120c0 f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121b8 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12250 184 .cfa: sp 0 + .ra: x30
STACK CFI 12258 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1225c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1226c .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 122c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 122d0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 123ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 123b0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 123d8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 123dc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 123ec .ra: .cfa -16 + ^
STACK CFI 1245c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12468 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 10140 30 .cfa: sp 0 + .ra: x30
STACK CFI 10144 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10160 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 124a8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 124c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 124c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 124e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 124e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 124f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 124f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12500 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12508 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1250c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12518 .ra: .cfa -16 + ^
STACK CFI 12560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12568 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 125e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 125e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12628 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 12630 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 126a0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 126b0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 126b8 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 126c8 .ra: .cfa -136 + ^ x21: .cfa -144 + ^
STACK CFI 127e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 127e8 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^
STACK CFI INIT 128c0 ac .cfa: sp 0 + .ra: x30
STACK CFI 128c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 128cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 128d4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1293c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 12940 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 10170 30 .cfa: sp 0 + .ra: x30
STACK CFI 10174 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10190 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 12970 40 .cfa: sp 0 + .ra: x30
STACK CFI 1297c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 129a0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 129a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 129ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 129b0 3c .cfa: sp 0 + .ra: x30
STACK CFI 129bc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 129e8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 129f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 129f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12a30 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 12a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12a3c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 12a40 a0 .cfa: sp 0 + .ra: x30
STACK CFI 12a44 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12a54 .ra: .cfa -48 + ^
STACK CFI 12a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12a84 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 12ae0 214 .cfa: sp 0 + .ra: x30
STACK CFI 12ae4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 12af0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 12b08 .ra: .cfa -56 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI 12be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 12bf0 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI INIT 12cf8 20 .cfa: sp 0 + .ra: x30
STACK CFI 12cfc .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12d10 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12d18 20 .cfa: sp 0 + .ra: x30
STACK CFI 12d1c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12d30 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12d38 20 .cfa: sp 0 + .ra: x30
STACK CFI 12d3c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12d50 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12d58 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12da8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12dc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12dd8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12de0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12de8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e08 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e28 88 .cfa: sp 0 + .ra: x30
STACK CFI 12e2c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12ea0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 12ea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 12eac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 12eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12eb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ec0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ec8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ed0 50 .cfa: sp 0 + .ra: x30
STACK CFI 12ed4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12ee0 .ra: .cfa -16 + ^
STACK CFI 12f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 12f20 50 .cfa: sp 0 + .ra: x30
STACK CFI 12f24 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12f30 .ra: .cfa -16 + ^
STACK CFI 12f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT fe90 a0 .cfa: sp 0 + .ra: x30
STACK CFI fe94 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fea0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI ff20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI ff24 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 12f70 7c .cfa: sp 0 + .ra: x30
STACK CFI 12f74 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12f7c .ra: .cfa -48 + ^
STACK CFI 12f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12f94 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 12ff0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13020 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13050 b4 .cfa: sp 0 + .ra: x30
STACK CFI 13058 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1307c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 13100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 13108 bc .cfa: sp 0 + .ra: x30
STACK CFI 13110 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13134 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 131c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 131c8 84 .cfa: sp 0 + .ra: x30
STACK CFI 131cc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 13248 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 13250 158 .cfa: sp 0 + .ra: x30
STACK CFI 13254 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13260 .ra: .cfa -16 + ^
STACK CFI 1332c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13330 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 133a8 150 .cfa: sp 0 + .ra: x30
STACK CFI 133ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 133b8 .ra: .cfa -16 + ^
STACK CFI 1347c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13480 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 134f8 398 .cfa: sp 0 + .ra: x30
STACK CFI 134fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13508 .ra: .cfa -16 + ^
STACK CFI 1369c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 136a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13890 39c .cfa: sp 0 + .ra: x30
STACK CFI 13894 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 138a0 .ra: .cfa -16 + ^
STACK CFI 13a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13a38 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13b6c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13c30 294 .cfa: sp 0 + .ra: x30
STACK CFI 13c34 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 13c44 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 13c4c .ra: .cfa -112 + ^
STACK CFI 13d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 13d78 .cfa: sp 144 + .ra: .cfa -112 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 13ee0 470 .cfa: sp 0 + .ra: x30
STACK CFI 13ee4 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 13eec .ra: .cfa -120 + ^ x21: .cfa -128 + ^
STACK CFI INIT 14350 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 14354 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 14370 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 14380 .ra: .cfa -120 + ^ x23: .cfa -128 + ^
STACK CFI 144dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 144e0 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^
STACK CFI 14528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1452c .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^
STACK CFI INIT 14550 634 .cfa: sp 0 + .ra: x30
STACK CFI 14554 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 1455c x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 1456c .ra: .cfa -336 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 148f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 148f8 .cfa: sp 384 + .ra: .cfa -336 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 1496c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 14970 .cfa: sp 384 + .ra: .cfa -336 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI INIT 14ba0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 14ba4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14bb0 .ra: .cfa -16 + ^
STACK CFI 14d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14d10 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 14d50 810 .cfa: sp 0 + .ra: x30
STACK CFI 14d54 .cfa: sp 800 +
STACK CFI 14d5c x21: .cfa -784 + ^ x22: .cfa -776 + ^
STACK CFI 14d74 .ra: .cfa -760 + ^ v10: .cfa -736 + ^ v11: .cfa -728 + ^ v12: .cfa -720 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x23: .cfa -768 + ^
STACK CFI 151f0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 151f8 .cfa: sp 800 + .ra: .cfa -760 + ^ v10: .cfa -736 + ^ v11: .cfa -728 + ^ v12: .cfa -720 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^
STACK CFI INIT 15580 874 .cfa: sp 0 + .ra: x30
STACK CFI 15584 .cfa: sp 816 +
STACK CFI 1558c x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 1559c x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 155b8 .ra: .cfa -744 + ^ v10: .cfa -720 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^
STACK CFI 15a7c .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 15a80 .cfa: sp 816 + .ra: .cfa -744 + ^ v10: .cfa -720 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^
STACK CFI INIT 15e10 830 .cfa: sp 0 + .ra: x30
STACK CFI 15e14 .cfa: sp 800 +
STACK CFI 15e18 x25: .cfa -752 + ^ x26: .cfa -744 + ^
STACK CFI 15e20 .ra: .cfa -728 + ^ x27: .cfa -736 + ^
STACK CFI 15e38 x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^
STACK CFI 15e48 x23: .cfa -768 + ^ x24: .cfa -760 + ^
STACK CFI 15e68 v8: .cfa -720 + ^
STACK CFI 162ac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 162b0 .cfa: sp 800 + .ra: .cfa -728 + ^ v8: .cfa -720 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^
STACK CFI INIT 16650 6bc .cfa: sp 0 + .ra: x30
STACK CFI 16654 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 16664 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 16674 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 1667c .ra: .cfa -216 + ^ x27: .cfa -224 + ^
STACK CFI 1698c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 16990 .cfa: sp 288 + .ra: .cfa -216 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^
STACK CFI INIT 16d10 e4 .cfa: sp 0 + .ra: x30
STACK CFI 16d98 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 16da8 .ra: .cfa -48 + ^
STACK CFI INIT 16e10 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16e60 1ec .cfa: sp 0 + .ra: x30
STACK CFI 16e64 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 16e7c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 16e8c .ra: .cfa -48 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 16f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16f78 .cfa: sp 112 + .ra: .cfa -48 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 17050 67c .cfa: sp 0 + .ra: x30
STACK CFI 17054 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 1705c .ra: .cfa -376 + ^ x27: .cfa -384 + ^
STACK CFI 1706c x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 17078 x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 17458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1745c .cfa: sp 448 + .ra: .cfa -376 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^
STACK CFI INIT 176e0 41c .cfa: sp 0 + .ra: x30
STACK CFI 176e4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 176ec .ra: .cfa -104 + ^ x21: .cfa -112 + ^
STACK CFI 17788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 17790 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^
STACK CFI 177f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 17800 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^
STACK CFI 17924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 17928 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^
STACK CFI INIT 17b10 1050 .cfa: sp 0 + .ra: x30
STACK CFI 17b14 .cfa: sp 1760 +
STACK CFI 17b18 x19: .cfa -1760 + ^ x20: .cfa -1752 + ^
STACK CFI 17b20 x21: .cfa -1744 + ^ x22: .cfa -1736 + ^
STACK CFI 17b30 x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI 17b4c .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^
STACK CFI 188fc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18900 .cfa: sp 1760 + .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x19: .cfa -1760 + ^ x20: .cfa -1752 + ^ x21: .cfa -1744 + ^ x22: .cfa -1736 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^ x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI INIT 18b80 10c8 .cfa: sp 0 + .ra: x30
STACK CFI 18b84 .cfa: sp 1216 +
STACK CFI 18b8c x23: .cfa -1184 + ^ x24: .cfa -1176 + ^
STACK CFI 18b9c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^
STACK CFI 18bc0 .ra: .cfa -1136 + ^ v10: .cfa -1104 + ^ v11: .cfa -1096 + ^ v12: .cfa -1128 + ^ v8: .cfa -1120 + ^ v9: .cfa -1112 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI 199c0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 199c8 .cfa: sp 1216 + .ra: .cfa -1136 + ^ v10: .cfa -1104 + ^ v11: .cfa -1096 + ^ v12: .cfa -1128 + ^ v8: .cfa -1120 + ^ v9: .cfa -1112 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI INIT 19c70 11a8 .cfa: sp 0 + .ra: x30
STACK CFI 19c74 .cfa: sp 1760 +
STACK CFI 19c78 x19: .cfa -1760 + ^ x20: .cfa -1752 + ^
STACK CFI 19c80 x21: .cfa -1744 + ^ x22: .cfa -1736 + ^
STACK CFI 19c90 x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI 19cac .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^
STACK CFI 1ab84 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ab88 .cfa: sp 1760 + .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x19: .cfa -1760 + ^ x20: .cfa -1752 + ^ x21: .cfa -1744 + ^ x22: .cfa -1736 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^ x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI INIT 1ae30 1060 .cfa: sp 0 + .ra: x30
STACK CFI 1ae34 .cfa: sp 1760 +
STACK CFI 1ae38 x19: .cfa -1760 + ^ x20: .cfa -1752 + ^
STACK CFI 1ae40 x21: .cfa -1744 + ^ x22: .cfa -1736 + ^
STACK CFI 1ae50 x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI 1ae6c .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^
STACK CFI 1bc2c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1bc30 .cfa: sp 1760 + .ra: .cfa -1680 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x19: .cfa -1760 + ^ x20: .cfa -1752 + ^ x21: .cfa -1744 + ^ x22: .cfa -1736 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^ x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI INIT 1bea8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1beac .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1bec4 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1bef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1bef8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1bf70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1bf74 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 1bf90 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1bf98 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bfa4 .ra: .cfa -16 + ^
STACK CFI 1bfcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1bfd0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1c020 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 1c048 244 .cfa: sp 0 + .ra: x30
STACK CFI 1c04c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1c058 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1c068 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1c070 .ra: .cfa -40 + ^ x27: .cfa -48 + ^
STACK CFI 1c19c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1c1a0 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI INIT 1c290 790 .cfa: sp 0 + .ra: x30
STACK CFI 1c294 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1c2a8 .ra: .cfa -40 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 1c5dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1c5e0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT 1ca40 150 .cfa: sp 0 + .ra: x30
STACK CFI 1ca8c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ca98 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1caa4 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1cb68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1cb70 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 1cb90 19f4 .cfa: sp 0 + .ra: x30
STACK CFI 1cb94 .cfa: sp 1040 +
STACK CFI 1cb9c x19: .cfa -1040 + ^ x20: .cfa -1032 + ^
STACK CFI 1cbac x21: .cfa -1024 + ^ x22: .cfa -1016 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^
STACK CFI 1cbd4 .ra: .cfa -960 + ^ v10: .cfa -952 + ^ v8: .cfa -944 + ^ v9: .cfa -936 + ^ x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
STACK CFI 1ce34 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ce38 .cfa: sp 1040 + .ra: .cfa -960 + ^ v10: .cfa -952 + ^ v8: .cfa -944 + ^ v9: .cfa -936 + ^ x19: .cfa -1040 + ^ x20: .cfa -1032 + ^ x21: .cfa -1024 + ^ x22: .cfa -1016 + ^ x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
STACK CFI INIT 1e5a0 700 .cfa: sp 0 + .ra: x30
STACK CFI 1e5a4 .cfa: sp 592 +
STACK CFI 1e5a8 x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 1e5b8 x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI 1e5c4 x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI 1e5cc .ra: .cfa -520 + ^ x27: .cfa -528 + ^
STACK CFI 1e938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1e940 .cfa: sp 592 + .ra: .cfa -520 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^
STACK CFI 1eac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1eac8 .cfa: sp 592 + .ra: .cfa -520 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^
STACK CFI INIT 1ecb0 100 .cfa: sp 0 + .ra: x30
STACK CFI 1ecb4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ecbc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1ecc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ed78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1ed80 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1edb0 678 .cfa: sp 0 + .ra: x30
STACK CFI 1edb4 .cfa: sp 560 +
STACK CFI 1edc4 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 1eddc x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 1edec .ra: .cfa -480 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 1f0c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1f0c8 .cfa: sp 560 + .ra: .cfa -480 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI INIT 1f440 40 .cfa: sp 0 + .ra: x30
STACK CFI 1f448 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -64 + ^
STACK CFI 1f47c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 101a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 101a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 101c0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1f480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f488 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f498 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f4e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f4e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f4f0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f548 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f54c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f558 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f5a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f5b0 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f5b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f5c0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f618 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f61c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f628 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f680 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f684 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f690 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f6e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f6e8 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f6ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f6f8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1f748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1f750 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f758 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1f75c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1f768 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1f778 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1f788 .ra: .cfa -32 + ^
STACK CFI 1f7f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1f7fc .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 1f818 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f820 fc .cfa: sp 0 + .ra: x30
STACK CFI 1f824 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1f834 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1f844 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1f84c .ra: .cfa -112 + ^
STACK CFI 1f8fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1f900 .cfa: sp 160 + .ra: .cfa -112 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI INIT ff30 a0 .cfa: sp 0 + .ra: x30
STACK CFI ff34 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ff40 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI ffc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI ffc4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 1f920 150 .cfa: sp 0 + .ra: x30
STACK CFI 1f924 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f928 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f938 .ra: .cfa -16 + ^
STACK CFI 1fa00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1fa08 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 1fa70 158 .cfa: sp 0 + .ra: x30
STACK CFI 1fa74 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1fa84 .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fb48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1fb50 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fbc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 1fbc8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1fbcc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fbdc .ra: .cfa -16 + ^
STACK CFI 1fc5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1fc60 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1fc80 1cc .cfa: sp 0 + .ra: x30
STACK CFI 1fc84 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fc90 .ra: .cfa -16 + ^
STACK CFI 1fe0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1fe10 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fe48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1fe50 7a0 .cfa: sp 0 + .ra: x30
STACK CFI 1fe54 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fe64 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 204a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 204a8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 205f0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 205f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20600 .ra: .cfa -16 + ^
STACK CFI 20784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 20788 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 207b8 ac .cfa: sp 0 + .ra: x30
STACK CFI 207bc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 207cc .ra: .cfa -16 + ^
STACK CFI 20854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 20858 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 20868 7a8 .cfa: sp 0 + .ra: x30
STACK CFI 2086c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2087c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 20eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 20eb8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 20fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 20fd0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 21010 120 .cfa: sp 0 + .ra: x30
STACK CFI 21014 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21020 .ra: .cfa -16 + ^
STACK CFI 210ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 210f0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 21130 60 .cfa: sp 0 + .ra: x30
STACK CFI 21150 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 21164 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 21190 78 .cfa: sp 0 + .ra: x30
STACK CFI 21194 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 211f8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 21200 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 21210 120 .cfa: sp 0 + .ra: x30
STACK CFI 21214 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21220 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2130c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 21310 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 21340 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21370 1bc .cfa: sp 0 + .ra: x30
STACK CFI 21374 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2137c .ra: .cfa -16 + ^
STACK CFI 214e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 214f0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 21530 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 21534 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21540 .ra: .cfa -16 + ^
STACK CFI 21780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 21788 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 217e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 217f0 11dc .cfa: sp 0 + .ra: x30
STACK CFI 217f4 .cfa: sp 1936 +
STACK CFI 217f8 v8: .cfa -1848 + ^
STACK CFI 21800 x23: .cfa -1904 + ^ x24: .cfa -1896 + ^
STACK CFI 21818 x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^
STACK CFI 21828 x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 21838 .ra: .cfa -1856 + ^
STACK CFI 21d6c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21d70 .cfa: sp 1936 + .ra: .cfa -1856 + ^ v8: .cfa -1848 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 229e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 229e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 229e8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 22a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22a90 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 22a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 22aa0 2ec .cfa: sp 0 + .ra: x30
STACK CFI 22aa4 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 22aac .ra: .cfa -232 + ^ x21: .cfa -240 + ^
STACK CFI 22bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22bb8 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI 22c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22c08 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI 22c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22c68 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI INIT 22d90 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 22d94 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 22d9c .ra: .cfa -232 + ^ x21: .cfa -240 + ^
STACK CFI 22e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22ea0 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI 22ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22eec .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI 22f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22f58 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI 22f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 22f78 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^
STACK CFI INIT 23080 ba4 .cfa: sp 0 + .ra: x30
STACK CFI 23084 .cfa: sp 1104 +
STACK CFI 23088 v8: .cfa -1016 + ^
STACK CFI 23090 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 230a0 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 230b8 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 230cc .ra: .cfa -1024 + ^
STACK CFI 236e0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 236e4 .cfa: sp 1104 + .ra: .cfa -1024 + ^ v8: .cfa -1016 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI INIT 23c40 380 .cfa: sp 0 + .ra: x30
STACK CFI 23c48 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 23c64 .ra: .cfa -56 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI INIT 23fe0 124 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24108 19c .cfa: sp 0 + .ra: x30
STACK CFI INIT 242a8 f8 .cfa: sp 0 + .ra: x30
STACK CFI 242ac .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 242b4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 242bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 24368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 24370 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 243a0 138 .cfa: sp 0 + .ra: x30
STACK CFI 243a4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 243b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 243b8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2449c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 244a0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 244e0 a20 .cfa: sp 0 + .ra: x30
STACK CFI 244e4 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 24504 .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 24ac4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 24ac8 .cfa: sp 224 + .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 24f10 780 .cfa: sp 0 + .ra: x30
STACK CFI 24f14 .cfa: sp 800 +
STACK CFI 24f20 x19: .cfa -800 + ^ x20: .cfa -792 + ^
STACK CFI 24f28 x23: .cfa -768 + ^ x24: .cfa -760 + ^
STACK CFI 24f38 .ra: .cfa -744 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x25: .cfa -752 + ^
STACK CFI 2546c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 25470 .cfa: sp 800 + .ra: .cfa -744 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^
STACK CFI INIT 256a0 16dc .cfa: sp 0 + .ra: x30
STACK CFI 256a4 .cfa: sp 368 + x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 256a8 x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 256cc .ra: .cfa -288 + ^ v10: .cfa -256 + ^ v11: .cfa -248 + ^ v12: .cfa -240 + ^ v13: .cfa -232 + ^ v14: .cfa -224 + ^ v15: .cfa -216 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 26c14 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 26c18 .cfa: sp 368 + .ra: .cfa -288 + ^ v10: .cfa -256 + ^ v11: .cfa -248 + ^ v12: .cfa -240 + ^ v13: .cfa -232 + ^ v14: .cfa -224 + ^ v15: .cfa -216 + ^ v8: .cfa -272 + ^ v9: .cfa -264 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI INIT 26d90 2c10 .cfa: sp 0 + .ra: x30
STACK CFI 26d94 .cfa: sp 2464 +
STACK CFI 26d98 x19: .cfa -2464 + ^ x20: .cfa -2456 + ^
STACK CFI 26da8 x21: .cfa -2448 + ^ x22: .cfa -2440 + ^
STACK CFI 26dd8 .ra: .cfa -2384 + ^ v10: .cfa -2352 + ^ v11: .cfa -2344 + ^ v12: .cfa -2336 + ^ v13: .cfa -2328 + ^ v14: .cfa -2376 + ^ v8: .cfa -2368 + ^ v9: .cfa -2360 + ^ x23: .cfa -2432 + ^ x24: .cfa -2424 + ^ x25: .cfa -2416 + ^ x26: .cfa -2408 + ^ x27: .cfa -2400 + ^ x28: .cfa -2392 + ^
STACK CFI 28cf4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 28cf8 .cfa: sp 2464 + .ra: .cfa -2384 + ^ v10: .cfa -2352 + ^ v11: .cfa -2344 + ^ v12: .cfa -2336 + ^ v13: .cfa -2328 + ^ v14: .cfa -2376 + ^ v8: .cfa -2368 + ^ v9: .cfa -2360 + ^ x19: .cfa -2464 + ^ x20: .cfa -2456 + ^ x21: .cfa -2448 + ^ x22: .cfa -2440 + ^ x23: .cfa -2432 + ^ x24: .cfa -2424 + ^ x25: .cfa -2416 + ^ x26: .cfa -2408 + ^ x27: .cfa -2400 + ^ x28: .cfa -2392 + ^
STACK CFI INIT 29a00 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29b10 17c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29c90 204 .cfa: sp 0 + .ra: x30
STACK CFI 29c94 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 29c98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 29ca8 .ra: .cfa -32 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 29e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 29ea0 1598 .cfa: sp 0 + .ra: x30
STACK CFI 29ea4 .cfa: sp 1408 +
STACK CFI 29ea8 x21: .cfa -1392 + ^ x22: .cfa -1384 + ^
STACK CFI 29eb8 x23: .cfa -1376 + ^ x24: .cfa -1368 + ^ x27: .cfa -1344 + ^ x28: .cfa -1336 + ^
STACK CFI 29ee4 .ra: .cfa -1328 + ^ v10: .cfa -1296 + ^ v11: .cfa -1288 + ^ v12: .cfa -1280 + ^ v13: .cfa -1272 + ^ v8: .cfa -1312 + ^ v9: .cfa -1304 + ^ x19: .cfa -1408 + ^ x20: .cfa -1400 + ^ x25: .cfa -1360 + ^ x26: .cfa -1352 + ^
STACK CFI 2a964 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2a968 .cfa: sp 1408 + .ra: .cfa -1328 + ^ v10: .cfa -1296 + ^ v11: .cfa -1288 + ^ v12: .cfa -1280 + ^ v13: .cfa -1272 + ^ v8: .cfa -1312 + ^ v9: .cfa -1304 + ^ x19: .cfa -1408 + ^ x20: .cfa -1400 + ^ x21: .cfa -1392 + ^ x22: .cfa -1384 + ^ x23: .cfa -1376 + ^ x24: .cfa -1368 + ^ x25: .cfa -1360 + ^ x26: .cfa -1352 + ^ x27: .cfa -1344 + ^ x28: .cfa -1336 + ^
STACK CFI INIT 101d0 30 .cfa: sp 0 + .ra: x30
STACK CFI 101d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 101f0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2b460 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b468 90 .cfa: sp 0 + .ra: x30
STACK CFI 2b46c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -288 + ^
STACK CFI 2b4f4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2b4f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10200 30 .cfa: sp 0 + .ra: x30
STACK CFI 10204 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10220 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2b500 88 .cfa: sp 0 + .ra: x30
STACK CFI 2b504 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2b578 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2b580 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2b584 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2b588 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b5b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b5b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2b5dc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ffd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI ffd4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ffe0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 10060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10064 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2b5e0 150 .cfa: sp 0 + .ra: x30
STACK CFI 2b5e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b5e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b5f8 .ra: .cfa -16 + ^
STACK CFI 2b6c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2b6c8 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2b730 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2b734 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b744 .ra: .cfa -64 + ^
STACK CFI INIT 2b7d8 158 .cfa: sp 0 + .ra: x30
STACK CFI 2b7dc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b7ec .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b8b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2b8b8 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b92c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 2b930 84 .cfa: sp 0 + .ra: x30
STACK CFI 2b934 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2b9b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2b9c0 23c .cfa: sp 0 + .ra: x30
STACK CFI 2b9cc .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2b9d8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2b9e8 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2b9fc x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2ba04 .ra: .cfa -112 + ^
STACK CFI 2bb3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2bb40 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2bbe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2bbe8 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 2bc10 7fc .cfa: sp 0 + .ra: x30
STACK CFI 2bc14 .cfa: sp 704 +
STACK CFI 2bc20 x25: .cfa -656 + ^ x26: .cfa -648 + ^
STACK CFI 2bc28 x19: .cfa -704 + ^ x20: .cfa -696 + ^
STACK CFI 2bc30 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 2bc38 x23: .cfa -672 + ^ x24: .cfa -664 + ^
STACK CFI 2bc50 .ra: .cfa -624 + ^ v8: .cfa -608 + ^ v9: .cfa -600 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI 2c370 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2c374 .cfa: sp 704 + .ra: .cfa -624 + ^ v8: .cfa -608 + ^ v9: .cfa -600 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI INIT 2c420 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c4a0 148c .cfa: sp 0 + .ra: x30
STACK CFI 2c4a4 .cfa: sp 1584 +
STACK CFI 2c4a8 v8: .cfa -1520 + ^ v9: .cfa -1512 + ^
STACK CFI 2c4b0 x23: .cfa -1552 + ^ x24: .cfa -1544 + ^
STACK CFI 2c4b8 x21: .cfa -1568 + ^ x22: .cfa -1560 + ^
STACK CFI 2c4c8 v10: .cfa -1504 + ^ v11: .cfa -1496 + ^ x19: .cfa -1584 + ^ x20: .cfa -1576 + ^
STACK CFI 2c4e4 .ra: .cfa -1536 + ^ v12: .cfa -1488 + ^ v13: .cfa -1480 + ^ v14: .cfa -1472 + ^ v15: .cfa -1464 + ^
STACK CFI 2d744 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2d748 .cfa: sp 1584 + .ra: .cfa -1536 + ^ v10: .cfa -1504 + ^ v11: .cfa -1496 + ^ v12: .cfa -1488 + ^ v13: .cfa -1480 + ^ v14: .cfa -1472 + ^ v15: .cfa -1464 + ^ v8: .cfa -1520 + ^ v9: .cfa -1512 + ^ x19: .cfa -1584 + ^ x20: .cfa -1576 + ^ x21: .cfa -1568 + ^ x22: .cfa -1560 + ^ x23: .cfa -1552 + ^ x24: .cfa -1544 + ^
STACK CFI INIT 2d960 978 .cfa: sp 0 + .ra: x30
STACK CFI 2d964 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 2d974 .ra: .cfa -248 + ^ x21: .cfa -256 + ^
STACK CFI 2d990 v10: .cfa -224 + ^ v11: .cfa -216 + ^ v12: .cfa -208 + ^ v13: .cfa -200 + ^ v14: .cfa -192 + ^ v15: .cfa -184 + ^ v8: .cfa -240 + ^ v9: .cfa -232 + ^
STACK CFI 2dfb0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21
STACK CFI 2dfb8 .cfa: sp 272 + .ra: .cfa -248 + ^ v10: .cfa -224 + ^ v11: .cfa -216 + ^ v12: .cfa -208 + ^ v13: .cfa -200 + ^ v14: .cfa -192 + ^ v15: .cfa -184 + ^ v8: .cfa -240 + ^ v9: .cfa -232 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^
STACK CFI INIT 2e2f0 1140 .cfa: sp 0 + .ra: x30
STACK CFI 2e2f4 .cfa: sp 1488 +
STACK CFI 2e2f8 x21: .cfa -1472 + ^ x22: .cfa -1464 + ^
STACK CFI 2e318 .ra: .cfa -1408 + ^ x19: .cfa -1488 + ^ x20: .cfa -1480 + ^ x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^ x27: .cfa -1424 + ^ x28: .cfa -1416 + ^
STACK CFI 2f3f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2f3f8 .cfa: sp 1488 + .ra: .cfa -1408 + ^ x19: .cfa -1488 + ^ x20: .cfa -1480 + ^ x21: .cfa -1472 + ^ x22: .cfa -1464 + ^ x23: .cfa -1456 + ^ x24: .cfa -1448 + ^ x25: .cfa -1440 + ^ x26: .cfa -1432 + ^ x27: .cfa -1424 + ^ x28: .cfa -1416 + ^
STACK CFI INIT 2f440 14c .cfa: sp 0 + .ra: x30
STACK CFI 2f448 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f460 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2f4a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2f4b0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2f54c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2f550 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 2f590 254 .cfa: sp 0 + .ra: x30
STACK CFI 2f594 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f59c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f5a4 v8: .cfa -16 + ^
STACK CFI 2f5ac .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 2f7ac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f7b0 .cfa: sp 64 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI INIT 10230 30 .cfa: sp 0 + .ra: x30
STACK CFI 10234 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10250 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2f7e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f7f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f808 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f818 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f828 60 .cfa: sp 0 + .ra: x30
STACK CFI 2f830 .cfa: sp 64 +
STACK CFI 2f83c .ra: .cfa -32 + ^
STACK CFI 2f884 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 2f8a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f8a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f8b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10260 30 .cfa: sp 0 + .ra: x30
STACK CFI 10264 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10280 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2f8b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f8c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10070 a0 .cfa: sp 0 + .ra: x30
STACK CFI 10074 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10080 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 10100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10104 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2f8c8 74 .cfa: sp 0 + .ra: x30
STACK CFI 2f8cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f8d0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2f928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2f930 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2f938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2f940 6c .cfa: sp 0 + .ra: x30
STACK CFI 2f944 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f948 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2f9a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2f9b0 6c8 .cfa: sp 0 + .ra: x30
STACK CFI 2f9b4 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 2f9c4 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2f9d4 .ra: .cfa -232 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x25: .cfa -240 + ^
STACK CFI 2fd14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2fd18 .cfa: sp 288 + .ra: .cfa -232 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI INIT 30078 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 300c0 14c .cfa: sp 0 + .ra: x30
STACK CFI 300c8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 300e0 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 30120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 30130 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 301cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 301d0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 30210 14c .cfa: sp 0 + .ra: x30
STACK CFI 30214 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30228 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 30294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 30298 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 30360 44c .cfa: sp 0 + .ra: x30
STACK CFI 30364 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 30374 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3037c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3038c .ra: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 304dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 304e0 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 30550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 30558 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 30714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 30718 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 307b0 e84 .cfa: sp 0 + .ra: x30
STACK CFI 307b4 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 307c4 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 307dc v8: .cfa -400 + ^ v9: .cfa -392 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 307ec .ra: .cfa -416 + ^
STACK CFI 31180 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 31184 .cfa: sp 496 + .ra: .cfa -416 + ^ v8: .cfa -400 + ^ v9: .cfa -392 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 10290 30 .cfa: sp 0 + .ra: x30
STACK CFI 10294 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 102b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 31638 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31640 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31698 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 316a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 316a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 316b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 316b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 316c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 316c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 316f4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 316f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31700 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31708 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31718 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31730 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31738 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31750 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31758 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31770 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31778 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31798 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317b8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317d8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 317f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31810 50 .cfa: sp 0 + .ra: x30
STACK CFI 31814 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31820 .ra: .cfa -16 + ^
STACK CFI 3185c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 31860 50 .cfa: sp 0 + .ra: x30
STACK CFI 31864 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31870 .ra: .cfa -16 + ^
STACK CFI 318ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 318b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 318b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 318c0 .ra: .cfa -16 + ^
STACK CFI 318fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 31900 50 .cfa: sp 0 + .ra: x30
STACK CFI 31904 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31910 .ra: .cfa -16 + ^
STACK CFI 3194c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 31950 50 .cfa: sp 0 + .ra: x30
STACK CFI 31954 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31960 .ra: .cfa -16 + ^
STACK CFI 3199c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 319a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 319a4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 319b0 .ra: .cfa -16 + ^
STACK CFI 319ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 319f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 319f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31a00 .ra: .cfa -16 + ^
STACK CFI 31a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 31a40 50 .cfa: sp 0 + .ra: x30
STACK CFI 31a44 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31a50 .ra: .cfa -16 + ^
STACK CFI 31a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 31a90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31a98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31aa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31aa8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ab8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ac8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ad0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ad8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31ae8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31af8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b28 ac .cfa: sp 0 + .ra: x30
STACK CFI 31b2c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 31b40 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 31b48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 31bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 31bd8 50 .cfa: sp 0 + .ra: x30
STACK CFI 31be0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 31c24 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 31c28 18 .cfa: sp 0 + .ra: x30
STACK CFI 31c2c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 31c3c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 31c40 54 .cfa: sp 0 + .ra: x30
STACK CFI 31c44 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31c54 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 31c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 31ca0 8c .cfa: sp 0 + .ra: x30
STACK CFI 31ca4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 31cb4 .ra: .cfa -48 + ^
STACK CFI 31d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 31d18 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 31d40 8c .cfa: sp 0 + .ra: x30
STACK CFI 31d44 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 31d54 .ra: .cfa -48 + ^
STACK CFI 31db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 31db8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 31de0 924 .cfa: sp 0 + .ra: x30
STACK CFI 31de4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31df0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 32444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 32448 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 325c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 325c4 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 32708 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3270c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32718 .ra: .cfa -16 + ^
STACK CFI 32768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 32770 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 327e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 327f0 298 .cfa: sp 0 + .ra: x30
STACK CFI 327f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32804 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 3299c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 329a0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 32a88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32a90 e8 .cfa: sp 0 + .ra: x30
STACK CFI 32a94 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32aa0 .ra: .cfa -16 + ^
STACK CFI 32af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 32b00 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 32b78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32b80 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 32b84 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32b94 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 32d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 32d38 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 32e20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 32e28 18 .cfa: sp 0 + .ra: x30
STACK CFI 32e2c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 32e3c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 32e40 7dc .cfa: sp 0 + .ra: x30
STACK CFI 32e48 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 32e5c .ra: .cfa -104 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^
STACK CFI 3353c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 33540 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^
STACK CFI INIT 33630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33638 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33640 260 .cfa: sp 0 + .ra: x30
STACK CFI 33644 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 33650 .ra: .cfa -112 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 33848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3384c .cfa: sp 144 + .ra: .cfa -112 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 338b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 338c0 b4c .cfa: sp 0 + .ra: x30
STACK CFI 338c4 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 338cc x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 338e0 .ra: .cfa -352 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 340bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 340c0 .cfa: sp 416 + .ra: .cfa -352 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI INIT 34440 718 .cfa: sp 0 + .ra: x30
STACK CFI 34444 .cfa: sp 512 +
STACK CFI 34448 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 34460 .ra: .cfa -456 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^
STACK CFI 347e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 347e8 .cfa: sp 512 + .ra: .cfa -456 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^
STACK CFI INIT 34b70 40 .cfa: sp 0 + .ra: x30
STACK CFI 34b74 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 34b98 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 34bc0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 34bc4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34bd0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 34c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 34c60 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 34ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 34cc0 18 .cfa: sp 0 + .ra: x30
STACK CFI 34cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 34cd4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 34cd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34ce0 cfc .cfa: sp 0 + .ra: x30
STACK CFI 34ce4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 34cf4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 34d14 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 34d1c .ra: .cfa -48 + ^
STACK CFI 352d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 352d8 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 359f0 268 .cfa: sp 0 + .ra: x30
STACK CFI 359f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 35a00 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 35b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 35b18 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 35c60 4a4 .cfa: sp 0 + .ra: x30
STACK CFI 35c64 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 35c6c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 35c78 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 35e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 35ea0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 36120 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 36124 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3612c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36138 .ra: .cfa -16 + ^ v8: .cfa -8 + ^
STACK CFI 361c0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 361c8 .cfa: sp 48 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 363f0 384 .cfa: sp 0 + .ra: x30
STACK CFI 363f8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 36410 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 36648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3664c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 366ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 366b0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 366c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 366cc .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 36778 518 .cfa: sp 0 + .ra: x30
STACK CFI 3677c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 36790 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3679c .ra: .cfa -16 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 36b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 36b60 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 36c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 36c18 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 36c90 968 .cfa: sp 0 + .ra: x30
STACK CFI 36c94 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 36ca0 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 36cb0 .ra: .cfa -376 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^
STACK CFI 37508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 37510 .cfa: sp 448 + .ra: .cfa -376 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^
STACK CFI INIT 37600 344 .cfa: sp 0 + .ra: x30
STACK CFI 37604 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37610 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 37620 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3787c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 37880 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 37950 334 .cfa: sp 0 + .ra: x30
STACK CFI 37954 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37960 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 37970 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 37bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 37bc8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 37ca0 11dc .cfa: sp 0 + .ra: x30
STACK CFI 37cac .cfa: sp 816 +
STACK CFI 37cb0 x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 37ccc .ra: .cfa -736 + ^ v8: .cfa -728 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 38c64 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38c68 .cfa: sp 816 + .ra: .cfa -736 + ^ v8: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 38dc8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38dcc .cfa: sp 816 + .ra: .cfa -736 + ^ v8: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI INIT 38ea0 30 .cfa: sp 0 + .ra: x30
STACK CFI 38ea4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38eb0 .ra: .cfa -16 + ^
STACK CFI 38ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 38ed0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 102c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 102c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 102e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 38ed8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38ee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38ee8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38ef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38ef8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38f00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38f08 128 .cfa: sp 0 + .ra: x30
STACK CFI 38f14 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38f18 .ra: .cfa -16 + ^
STACK CFI 38fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 38fe8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 39024 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 39030 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 39034 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39044 .ra: .cfa -16 + ^
STACK CFI 39180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 39188 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 39220 c8 .cfa: sp 0 + .ra: x30
STACK CFI 39224 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 39268 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 39270 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 392e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 392e8 d8 .cfa: sp 0 + .ra: x30
STACK CFI 392ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 392f8 .ra: .cfa -16 + ^
STACK CFI 39340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 39348 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 393c0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 393c4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 393d4 .ra: .cfa -16 + ^
STACK CFI 3950c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 39510 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 395a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 395b0 24d0 .cfa: sp 0 + .ra: x30
STACK CFI 395b4 .cfa: sp 2656 +
STACK CFI 395b8 x21: .cfa -2640 + ^ x22: .cfa -2632 + ^
STACK CFI 395d0 .ra: .cfa -2576 + ^ x19: .cfa -2656 + ^ x20: .cfa -2648 + ^ x23: .cfa -2624 + ^ x24: .cfa -2616 + ^ x25: .cfa -2608 + ^ x26: .cfa -2600 + ^ x27: .cfa -2592 + ^ x28: .cfa -2584 + ^
STACK CFI 3b0a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3b0a4 .cfa: sp 2656 + .ra: .cfa -2576 + ^ x19: .cfa -2656 + ^ x20: .cfa -2648 + ^ x21: .cfa -2640 + ^ x22: .cfa -2632 + ^ x23: .cfa -2624 + ^ x24: .cfa -2616 + ^ x25: .cfa -2608 + ^ x26: .cfa -2600 + ^ x27: .cfa -2592 + ^ x28: .cfa -2584 + ^
STACK CFI 3b21c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3b220 .cfa: sp 2656 + .ra: .cfa -2576 + ^ x19: .cfa -2656 + ^ x20: .cfa -2648 + ^ x21: .cfa -2640 + ^ x22: .cfa -2632 + ^ x23: .cfa -2624 + ^ x24: .cfa -2616 + ^ x25: .cfa -2608 + ^ x26: .cfa -2600 + ^ x27: .cfa -2592 + ^ x28: .cfa -2584 + ^
STACK CFI INIT 3ba90 41c .cfa: sp 0 + .ra: x30
STACK CFI 3ba98 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3bab8 .ra: .cfa -32 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3bc48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3bc50 .cfa: sp 96 + .ra: .cfa -32 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 102f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 102f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 10310 .cfa: sp 0 + .ra: .ra x19: x19
