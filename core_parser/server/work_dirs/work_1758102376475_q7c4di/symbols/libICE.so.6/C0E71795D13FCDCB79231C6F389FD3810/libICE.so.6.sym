MODULE Linux arm64 C0E71795D13FCDCB79231C6F389FD3810 libICE.so.6
INFO CODE_ID 9517E7C03FD1CBCD79231C6F389FD381BD6D2D8A
PUBLIC 4678 0 IceAcceptConnection
PUBLIC 4bc8 0 IceAuthFileName
PUBLIC 4d28 0 IceLockAuthFile
PUBLIC 4ef0 0 IceUnlockAuthFile
PUBLIC 4fb0 0 IceReadAuthFileEntry
PUBLIC 50d0 0 IceFreeAuthFileEntry
PUBLIC 5120 0 IceWriteAuthFileEntry
PUBLIC 51a0 0 IceGetAuthFileEntry
PUBLIC 5278 0 IceOpenConnection
PUBLIC 5c58 0 IceGetConnectionContext
PUBLIC 6418 0 _IceErrorBadMinor
PUBLIC 64a8 0 _IceErrorBadState
PUBLIC 6538 0 _IceErrorBadLength
PUBLIC 65c8 0 _IceErrorBadValue
PUBLIC 67f8 0 _IceErrorNoAuthentication
PUBLIC 6888 0 _IceErrorNoVersion
PUBLIC 6918 0 _IceErrorSetupFailed
PUBLIC 6a98 0 _IceErrorAuthenticationRejected
PUBLIC 6c08 0 _IceErrorAuthenticationFailed
PUBLIC 6d78 0 _IceErrorProtocolDuplicate
PUBLIC 6ed8 0 _IceErrorMajorOpcodeDuplicate
PUBLIC 6fe0 0 _IceErrorUnknownProtocol
PUBLIC 7140 0 _IceErrorBadMajor
PUBLIC 7250 0 IceSetErrorHandler
PUBLIC 7278 0 IceSetIOErrorHandler
PUBLIC 72a0 0 _IceGetPoAuthData
PUBLIC 7318 0 _IceGetPaAuthData
PUBLIC 7428 0 _IceGetPoValidAuthIndices
PUBLIC 7590 0 _IceGetPaValidAuthIndices
PUBLIC 76e0 0 _IcePoMagicCookie1Proc
PUBLIC 77e0 0 _IcePaMagicCookie1Proc
PUBLIC 78f8 0 IceGenerateMagicCookie
PUBLIC 9620 0 _IceTransFreeConnInfo
PUBLIC 9818 0 _IceTransSetOption
PUBLIC 98d0 0 _IceTransCreateListener
PUBLIC 98e0 0 _IceTransReceived
PUBLIC 9990 0 _IceTransNoListen
PUBLIC 9a28 0 _IceTransListen
PUBLIC 9ac0 0 _IceTransIsListening
PUBLIC 9b10 0 _IceTransResetListener
PUBLIC 9b30 0 _IceTransAccept
PUBLIC 9b88 0 _IceTransBytesReadable
PUBLIC 9b98 0 _IceTransRead
PUBLIC 9ba8 0 _IceTransWrite
PUBLIC 9bb8 0 _IceTransReadv
PUBLIC 9bc8 0 _IceTransWritev
PUBLIC 9bd8 0 _IceTransDisconnect
PUBLIC 9be8 0 _IceTransClose
PUBLIC 9c38 0 _IceTransCloseForCloning
PUBLIC 9c88 0 _IceTransIsLocal
PUBLIC 9c98 0 _IceTransGetMyAddr
PUBLIC 9d28 0 _IceTransGetPeerAddr
PUBLIC 9db8 0 _IceTransGetConnectionNumber
PUBLIC 9dc0 0 _IceTransGetHostname
PUBLIC ac98 0 _IceTransOpenCOTSClient
PUBLIC acd0 0 _IceTransOpenCOTSServer
PUBLIC ad08 0 _IceTransMakeAllCOTSServerListeners
PUBLIC b0c0 0 _IceTransOpenCLTSClient
PUBLIC b0f8 0 _IceTransOpenCLTSServer
PUBLIC b130 0 _IceTransMakeAllCLTSServerListeners
PUBLIC b4a8 0 _IceTransConnect
PUBLIC b5d0 0 _IceTransGetMyNetworkId
PUBLIC b758 0 _IceTransGetPeerNetworkId
PUBLIC b908 0 IceListenForConnections
PUBLIC bc50 0 IceGetListenConnectionNumber
PUBLIC bc58 0 IceGetListenConnectionString
PUBLIC bc60 0 IceComposeNetworkIdList
PUBLIC bde0 0 IceFreeListenObjs
PUBLIC be50 0 IceSetHostBasedAuthProc
PUBLIC be58 0 IceListenForWellKnownConnections
PUBLIC c190 0 IceInitThreads
PUBLIC c198 0 IceAppLockConn
PUBLIC c1a0 0 IceAppUnlockConn
PUBLIC c1a8 0 IceAllocScratch
PUBLIC c1f0 0 IceGetOutBufSize
PUBLIC c200 0 IceGetInBufSize
PUBLIC c210 0 IceConnectionStatus
PUBLIC c218 0 IceVendor
PUBLIC c220 0 IceRelease
PUBLIC c228 0 IceProtocolVersion
PUBLIC c240 0 IceProtocolRevision
PUBLIC c258 0 IceConnectionNumber
PUBLIC c260 0 IceConnectionString
PUBLIC c278 0 IceLastSentSequenceNumber
PUBLIC c280 0 IceLastReceivedSequenceNumber
PUBLIC c288 0 IceSwapping
PUBLIC c298 0 _IceRead
PUBLIC c3c8 0 _IceReadSkip
PUBLIC c458 0 _IceWrite
PUBLIC c578 0 IceFlush
PUBLIC c5b0 0 _IceAddOpcodeMapping
PUBLIC c7b8 0 IceGetPeerName
PUBLIC c7c0 0 _IceGetPeerName
PUBLIC c7c8 0 IcePing
PUBLIC fd70 0 IceProcessMessages
PUBLIC 10098 0 IceProtocolSetup
PUBLIC 108a0 0 IceRegisterForProtocolSetup
PUBLIC 10ad8 0 IceRegisterForProtocolReply
PUBLIC 10d10 0 _IceAddReplyWait
PUBLIC 10da0 0 _IceSearchReplyWaits
PUBLIC 10dd8 0 _IceSetReplyReady
PUBLIC 10e08 0 _IceCheckReplyReady
PUBLIC 10ea8 0 IceSetPaAuthData
PUBLIC 11018 0 IceProtocolShutdown
PUBLIC 110b8 0 IceSetShutdownNegotiation
PUBLIC 110d0 0 IceCheckShutdownNegotiation
PUBLIC 110e0 0 _IceFreeConnection
PUBLIC 111e0 0 IceCloseConnection
PUBLIC 113b8 0 IceAddConnectionWatch
PUBLIC 114a8 0 IceRemoveConnectionWatch
PUBLIC 11568 0 _IceConnectionOpened
PUBLIC 11610 0 _IceConnectionClosed
STACK CFI INIT 45b8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4628 48 .cfa: sp 0 + .ra: x30
STACK CFI 462c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4634 x19: .cfa -16 + ^
STACK CFI 466c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4678 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 467c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4684 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 468c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4694 x23: .cfa -32 + ^
STACK CFI 47dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 47e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4870 84 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4884 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48f8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 48fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4904 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4914 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4958 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 495c x23: .cfa -32 + ^
STACK CFI 49a8 x23: x23
STACK CFI 49ac x23: .cfa -32 + ^
STACK CFI 49c0 x23: x23
STACK CFI 49c4 x23: .cfa -32 + ^
STACK CFI 49d4 x23: x23
STACK CFI 49dc x23: .cfa -32 + ^
STACK CFI 49e4 x23: x23
STACK CFI INIT 49e8 ec .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 49f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4a00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4a08 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a68 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 4a70 x25: .cfa -32 + ^
STACK CFI 4aa4 x25: x25
STACK CFI 4aa8 x25: .cfa -32 + ^
STACK CFI 4aac x25: x25
STACK CFI 4ab8 x25: .cfa -32 + ^
STACK CFI 4ac8 x25: x25
STACK CFI 4ad0 x25: .cfa -32 + ^
STACK CFI INIT 4ad8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4adc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4ae8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4af8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4b78 50 .cfa: sp 0 + .ra: x30
STACK CFI 4b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4bc8 15c .cfa: sp 0 + .ra: x30
STACK CFI 4bcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4bdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bf8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4c04 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4c30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4cb4 x21: x21 x22: x22
STACK CFI 4cc0 x23: x23 x24: x24
STACK CFI 4cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4cf4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d00 x21: x21 x22: x22
STACK CFI 4d04 x23: x23 x24: x24
STACK CFI 4d08 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4d10 x23: x23 x24: x24
STACK CFI 4d14 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4d1c x21: x21 x22: x22
STACK CFI 4d20 x23: x23 x24: x24
STACK CFI INIT 4d28 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 4d2c .cfa: sp 2288 +
STACK CFI 4d30 .ra: .cfa -2280 + ^ x29: .cfa -2288 + ^
STACK CFI 4d38 x21: .cfa -2256 + ^ x22: .cfa -2248 + ^
STACK CFI 4d48 x19: .cfa -2272 + ^ x20: .cfa -2264 + ^
STACK CFI 4d54 x25: .cfa -2224 + ^
STACK CFI 4d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 4da0 .cfa: sp 2288 + .ra: .cfa -2280 + ^ x19: .cfa -2272 + ^ x20: .cfa -2264 + ^ x21: .cfa -2256 + ^ x22: .cfa -2248 + ^ x25: .cfa -2224 + ^ x29: .cfa -2288 + ^
STACK CFI 4da4 x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 4eb4 x23: x23 x24: x24
STACK CFI 4eb8 x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 4ed8 x23: x23 x24: x24
STACK CFI 4edc x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI 4ee4 x23: x23 x24: x24
STACK CFI 4eec x23: .cfa -2240 + ^ x24: .cfa -2232 + ^
STACK CFI INIT 4ef0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4ef4 .cfa: sp 2128 +
STACK CFI 4ef8 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 4f00 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 4f40 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 4f84 x21: x21 x22: x22
STACK CFI 4fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fa8 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x29: .cfa -2128 + ^
STACK CFI 4fac x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI INIT 4fb0 11c .cfa: sp 0 + .ra: x30
STACK CFI 4fb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4fbc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4fcc x21: .cfa -80 + ^
STACK CFI 50c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 50d0 50 .cfa: sp 0 + .ra: x30
STACK CFI 50d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50e0 x19: .cfa -16 + ^
STACK CFI 5118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5120 80 .cfa: sp 0 + .ra: x30
STACK CFI 5124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 512c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 514c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5150 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 51a0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 51a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51bc x23: .cfa -16 + ^
STACK CFI 5258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 525c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5278 9dc .cfa: sp 0 + .ra: x30
STACK CFI 527c .cfa: sp 720 +
STACK CFI 5280 .ra: .cfa -712 + ^ x29: .cfa -720 + ^
STACK CFI 5288 x25: .cfa -656 + ^ x26: .cfa -648 + ^
STACK CFI 5298 x19: .cfa -704 + ^ x20: .cfa -696 + ^
STACK CFI 52a4 x23: .cfa -672 + ^ x24: .cfa -664 + ^
STACK CFI 52ac x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI 5324 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 5484 x21: x21 x22: x22
STACK CFI 54c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 54c4 .cfa: sp 720 + .ra: .cfa -712 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^ x29: .cfa -720 + ^
STACK CFI 58c0 x21: x21 x22: x22
STACK CFI 58c4 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 58dc x21: x21 x22: x22
STACK CFI 58e0 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 59fc x21: x21 x22: x22
STACK CFI 5a00 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 5bac x21: x21 x22: x22
STACK CFI 5bb0 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 5be4 x21: x21 x22: x22
STACK CFI 5be8 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 5bfc x21: x21 x22: x22
STACK CFI 5c00 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 5c28 x21: x21 x22: x22
STACK CFI 5c30 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI INIT 5c58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c60 768 .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5c70 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5c78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5c84 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5c90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5c98 x27: .cfa -16 + ^
STACK CFI 5f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5f54 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5fcc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 63c8 4c .cfa: sp 0 + .ra: x30
STACK CFI 63cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 6418 90 .cfa: sp 0 + .ra: x30
STACK CFI 641c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6424 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6430 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 649c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 64a8 90 .cfa: sp 0 + .ra: x30
STACK CFI 64ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 652c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6538 90 .cfa: sp 0 + .ra: x30
STACK CFI 653c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6550 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 65b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 65bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 65c8 22c .cfa: sp 0 + .ra: x30
STACK CFI 65cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 65d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 65e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 65f0 x23: .cfa -48 + ^
STACK CFI 6770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6774 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 67f8 8c .cfa: sp 0 + .ra: x30
STACK CFI 67fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6808 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6814 x21: .cfa -16 + ^
STACK CFI 6874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6878 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6888 90 .cfa: sp 0 + .ra: x30
STACK CFI 688c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6898 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 68a4 x21: .cfa -16 + ^
STACK CFI 6908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 690c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6918 17c .cfa: sp 0 + .ra: x30
STACK CFI 691c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6928 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6930 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 693c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6a40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6a70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a98 170 .cfa: sp 0 + .ra: x30
STACK CFI 6a9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6aa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6aac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ab4 x23: .cfa -16 + ^
STACK CFI 6bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6be4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c08 170 .cfa: sp 0 + .ra: x30
STACK CFI 6c0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6c14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6c1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6c24 x23: .cfa -16 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6d78 160 .cfa: sp 0 + .ra: x30
STACK CFI 6d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6eb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6ed8 104 .cfa: sp 0 + .ra: x30
STACK CFI 6edc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ee4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6fe0 160 .cfa: sp 0 + .ra: x30
STACK CFI 6fe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6fec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ff4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 70ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 70f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 711c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7140 110 .cfa: sp 0 + .ra: x30
STACK CFI 7144 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 714c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 715c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7228 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7250 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7278 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 72a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 72a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 72ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72b4 x21: .cfa -16 + ^
STACK CFI 72f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 72fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7318 10c .cfa: sp 0 + .ra: x30
STACK CFI 731c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7330 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7338 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 734c x27: .cfa -16 + ^
STACK CFI 7354 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 73d0 x19: x19 x20: x20
STACK CFI 73dc x27: x27
STACK CFI 73e0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 73e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 73f8 x19: x19 x20: x20
STACK CFI 73fc x27: x27
STACK CFI 7414 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7418 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 741c x19: x19 x20: x20
STACK CFI 7420 x27: x27
STACK CFI INIT 7428 164 .cfa: sp 0 + .ra: x30
STACK CFI 742c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7438 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7444 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 744c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7488 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 74a4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 74a8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7580 x25: x25 x26: x26
STACK CFI 7584 x27: x27 x28: x28
STACK CFI 7588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7590 14c .cfa: sp 0 + .ra: x30
STACK CFI 7594 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 759c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 75c4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 75d4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 75e0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 75e8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 76c4 x19: x19 x20: x20
STACK CFI 76c8 x23: x23 x24: x24
STACK CFI 76cc x25: x25 x26: x26
STACK CFI 76d0 x27: x27 x28: x28
STACK CFI 76d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 76e0 100 .cfa: sp 0 + .ra: x30
STACK CFI 76e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 76f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76fc x23: .cfa -48 + ^
STACK CFI 7728 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7778 x21: x21 x22: x22
STACK CFI 7798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 779c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 77b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 77d4 x21: x21 x22: x22
STACK CFI 77dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 77e0 114 .cfa: sp 0 + .ra: x30
STACK CFI 77e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 77ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7818 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7820 x23: .cfa -48 + ^
STACK CFI 7894 x23: x23
STACK CFI 78b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 78bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 78d0 x23: .cfa -48 + ^
STACK CFI 78e8 x23: x23
STACK CFI 78f0 x23: .cfa -48 + ^
STACK CFI INIT 78f8 40 .cfa: sp 0 + .ra: x30
STACK CFI 78fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7904 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7938 24 .cfa: sp 0 + .ra: x30
STACK CFI 793c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7960 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7968 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7970 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7980 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 798c x23: .cfa -16 + ^
STACK CFI 79f4 x21: x21 x22: x22
STACK CFI 79fc x23: x23
STACK CFI 7a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7a30 x21: x21 x22: x22
STACK CFI 7a34 x23: x23
STACK CFI 7a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7a48 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7a4c .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 7a5c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 7b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b18 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x29: .cfa -368 + ^
STACK CFI INIT 7b20 120 .cfa: sp 0 + .ra: x30
STACK CFI 7b24 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 7b34 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7bb4 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x29: .cfa -384 + ^
STACK CFI 7bb8 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 7bc0 x23: .cfa -336 + ^
STACK CFI 7c2c x21: x21 x22: x22
STACK CFI 7c30 x23: x23
STACK CFI 7c38 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 7c3c x23: .cfa -336 + ^
STACK CFI INIT 7c40 38 .cfa: sp 0 + .ra: x30
STACK CFI 7c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c54 x19: .cfa -16 + ^
STACK CFI 7c74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c78 58 .cfa: sp 0 + .ra: x30
STACK CFI 7c7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c8c x21: .cfa -16 + ^
STACK CFI 7c94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7cd0 30 .cfa: sp 0 + .ra: x30
STACK CFI 7cd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7cfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d00 38 .cfa: sp 0 + .ra: x30
STACK CFI 7d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d14 x19: .cfa -16 + ^
STACK CFI 7d34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d38 3c .cfa: sp 0 + .ra: x30
STACK CFI 7d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d4c x19: .cfa -16 + ^
STACK CFI 7d70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d78 58 .cfa: sp 0 + .ra: x30
STACK CFI 7d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d98 x21: .cfa -16 + ^
STACK CFI 7dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7dd0 58 .cfa: sp 0 + .ra: x30
STACK CFI 7dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7de4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7df0 x21: .cfa -16 + ^
STACK CFI 7e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7e28 58 .cfa: sp 0 + .ra: x30
STACK CFI 7e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e3c x21: .cfa -16 + ^
STACK CFI 7e44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7e80 48 .cfa: sp 0 + .ra: x30
STACK CFI 7e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ec8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ed8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7ef0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7f68 190 .cfa: sp 0 + .ra: x30
STACK CFI 7f6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7f7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7fa0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7fc8 x23: .cfa -32 + ^
STACK CFI 8010 x23: x23
STACK CFI 8034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8038 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 8054 x23: x23
STACK CFI 8058 x23: .cfa -32 + ^
STACK CFI 80b0 x23: x23
STACK CFI 80b4 x23: .cfa -32 + ^
STACK CFI 80d8 x23: x23
STACK CFI 80f4 x23: .cfa -32 + ^
STACK CFI INIT 80f8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 80fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 811c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8130 x21: .cfa -16 + ^
STACK CFI 81a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 81a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 81c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 81d0 12c .cfa: sp 0 + .ra: x30
STACK CFI 81d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 81e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 81fc x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 82b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 82bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8300 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8304 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8324 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8334 x21: .cfa -16 + ^
STACK CFI 83a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 83a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 83c0 154 .cfa: sp 0 + .ra: x30
STACK CFI 83c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 83d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 83ec x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 84ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8518 110 .cfa: sp 0 + .ra: x30
STACK CFI 851c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 852c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 853c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 85e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85e8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8628 114 .cfa: sp 0 + .ra: x30
STACK CFI 862c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8634 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 8644 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 86e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 86ec .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI INIT 8740 178 .cfa: sp 0 + .ra: x30
STACK CFI 8744 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8750 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 875c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8818 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 88b8 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 88bc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 88c8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 88d4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 88dc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 89b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 89bc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8a60 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 8a64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8a6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8a74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8a80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8a8c x25: .cfa -16 + ^
STACK CFI 8b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8b6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8bf0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8c10 20c .cfa: sp 0 + .ra: x30
STACK CFI 8c14 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 8c1c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8c24 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 8c34 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 8d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8d1c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI INIT 8e20 90 .cfa: sp 0 + .ra: x30
STACK CFI 8e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8e34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8e40 x21: .cfa -16 + ^
STACK CFI 8e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8e80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8eb0 118 .cfa: sp 0 + .ra: x30
STACK CFI 8eb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8ec4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8edc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^
STACK CFI 8fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8fbc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8fc8 344 .cfa: sp 0 + .ra: x30
STACK CFI 8fcc .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 8fd8 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 8fe8 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 9084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9088 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x29: .cfa -368 + ^
STACK CFI 90a0 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 90ac x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 90c4 x23: x23 x24: x24
STACK CFI 90c8 x25: x25 x26: x26
STACK CFI 90cc x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 90d4 x27: .cfa -288 + ^
STACK CFI 9164 x27: x27
STACK CFI 91ac x23: x23 x24: x24
STACK CFI 91b0 x25: x25 x26: x26
STACK CFI 91d0 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 91d8 x27: .cfa -288 + ^
STACK CFI 91e4 x27: x27
STACK CFI 91e8 x27: .cfa -288 + ^
STACK CFI 9208 x27: x27
STACK CFI 920c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9230 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^
STACK CFI 926c x23: x23 x24: x24
STACK CFI 9270 x25: x25 x26: x26
STACK CFI 9274 x27: x27
STACK CFI 9294 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^
STACK CFI 92c0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 92c4 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 92c8 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 92cc x27: .cfa -288 + ^
STACK CFI 92d0 x27: x27
STACK CFI 92f4 x27: .cfa -288 + ^
STACK CFI 9300 x27: x27
STACK CFI INIT 9310 310 .cfa: sp 0 + .ra: x30
STACK CFI 9314 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 931c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 9324 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 932c x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 9388 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 9390 x27: .cfa -256 + ^
STACK CFI 9480 x25: x25 x26: x26
STACK CFI 9484 x27: x27
STACK CFI 94ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 94b0 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x29: .cfa -336 + ^
STACK CFI 94e4 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 94e8 x27: .cfa -256 + ^
STACK CFI 959c x25: x25 x26: x26
STACK CFI 95a0 x27: x27
STACK CFI 95e0 x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^
STACK CFI 95f8 x25: x25 x26: x26
STACK CFI 95fc x27: x27
STACK CFI 9604 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 9608 x27: .cfa -256 + ^
STACK CFI INIT 9620 58 .cfa: sp 0 + .ra: x30
STACK CFI 9624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9634 x19: .cfa -16 + ^
STACK CFI 9674 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9678 19c .cfa: sp 0 + .ra: x30
STACK CFI 967c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 968c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 96b8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 96f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 96f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 9728 x23: .cfa -160 + ^
STACK CFI 9794 x23: x23
STACK CFI 9798 x23: .cfa -160 + ^
STACK CFI 97b8 x23: x23
STACK CFI 97bc x23: .cfa -160 + ^
STACK CFI 97ec x23: x23
STACK CFI 97f0 x23: .cfa -160 + ^
STACK CFI 9808 x23: x23
STACK CFI 9810 x23: .cfa -160 + ^
STACK CFI INIT 9818 b8 .cfa: sp 0 + .ra: x30
STACK CFI 981c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 982c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9838 x21: .cfa -16 + ^
STACK CFI 987c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9880 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 989c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 98cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 98d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 98e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 98e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 98f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9938 x21: .cfa -16 + ^
STACK CFI 995c x21: x21
STACK CFI 9970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9974 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9990 98 .cfa: sp 0 + .ra: x30
STACK CFI 9994 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 999c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 99d0 x21: .cfa -16 + ^
STACK CFI 99f4 x21: x21
STACK CFI 9a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9a0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9a28 98 .cfa: sp 0 + .ra: x30
STACK CFI 9a2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9a34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9a68 x21: .cfa -16 + ^
STACK CFI 9a8c x21: x21
STACK CFI 9aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9aa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ac0 4c .cfa: sp 0 + .ra: x30
STACK CFI 9ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9acc x19: .cfa -16 + ^
STACK CFI 9aec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9b10 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b30 54 .cfa: sp 0 + .ra: x30
STACK CFI 9b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9b3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9b88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b98 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ba8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bb8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bc8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bd8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9be8 50 .cfa: sp 0 + .ra: x30
STACK CFI 9bec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9bfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9c38 50 .cfa: sp 0 + .ra: x30
STACK CFI 9c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9c88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9c98 8c .cfa: sp 0 + .ra: x30
STACK CFI 9c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9ca4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9d28 8c .cfa: sp 0 + .ra: x30
STACK CFI 9d2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d40 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9db8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9dc0 98 .cfa: sp 0 + .ra: x30
STACK CFI 9dc4 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 9dcc x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 9ddc x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 9de4 x23: .cfa -416 + ^
STACK CFI 9e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9e54 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x29: .cfa -464 + ^
STACK CFI INIT 9e58 5f0 .cfa: sp 0 + .ra: x30
STACK CFI 9e5c .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 9e6c x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 9e78 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 9e80 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 9e8c x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI a07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a080 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT a448 3fc .cfa: sp 0 + .ra: x30
STACK CFI a44c .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI a45c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI a468 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI a470 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI a570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a574 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x29: .cfa -496 + ^
STACK CFI a590 x27: .cfa -416 + ^
STACK CFI a59c x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a600 x25: x25 x26: x26
STACK CFI a604 x27: x27
STACK CFI a638 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a65c x25: x25 x26: x26
STACK CFI a660 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a694 x25: x25 x26: x26
STACK CFI a6b0 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a794 x25: x25 x26: x26
STACK CFI a798 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a7c4 x25: x25 x26: x26
STACK CFI a804 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a818 x25: x25 x26: x26
STACK CFI a81c x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI a820 x27: .cfa -416 + ^
STACK CFI a83c x25: x25 x26: x26
STACK CFI a840 x27: x27
STACK CFI INIT a848 29c .cfa: sp 0 + .ra: x30
STACK CFI a84c .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI a854 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI a85c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI a864 x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI a874 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI a8e8 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI a940 x27: x27 x28: x28
STACK CFI a96c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a970 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x29: .cfa -416 + ^
STACK CFI a990 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI a9f8 x27: x27 x28: x28
STACK CFI aa3c x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI aa68 x27: x27 x28: x28
STACK CFI aa78 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI aa88 x27: x27 x28: x28
STACK CFI aa8c x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI aab4 x27: x27 x28: x28
STACK CFI aab8 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI aad4 x27: x27 x28: x28
STACK CFI aad8 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT aae8 1b0 .cfa: sp 0 + .ra: x30
STACK CFI aaec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aafc x21: .cfa -48 + ^
STACK CFI ab04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI abcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI abd0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT ac98 38 .cfa: sp 0 + .ra: x30
STACK CFI ac9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acb0 x19: .cfa -16 + ^
STACK CFI accc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT acd0 38 .cfa: sp 0 + .ra: x30
STACK CFI acd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ace8 x19: .cfa -16 + ^
STACK CFI ad04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad08 3b8 .cfa: sp 0 + .ra: x30
STACK CFI ad0c .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI ad18 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI ad24 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI ad48 x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI afd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI afdc .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI INIT b0c0 38 .cfa: sp 0 + .ra: x30
STACK CFI b0c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b0d8 x19: .cfa -16 + ^
STACK CFI b0f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b0f8 38 .cfa: sp 0 + .ra: x30
STACK CFI b0fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b110 x19: .cfa -16 + ^
STACK CFI b12c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b130 378 .cfa: sp 0 + .ra: x30
STACK CFI b134 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI b140 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI b14c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI b170 x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI b3c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b3cc .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^ x29: .cfa -416 + ^
STACK CFI INIT b4a8 124 .cfa: sp 0 + .ra: x30
STACK CFI b4ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b4b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b4c4 x21: .cfa -48 + ^
STACK CFI b578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b57c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT b5d0 188 .cfa: sp 0 + .ra: x30
STACK CFI b5d4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI b5e0 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI b5ec x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI b610 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI b640 x25: .cfa -304 + ^
STACK CFI b698 x25: x25
STACK CFI b6c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b6c4 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI b754 x25: .cfa -304 + ^
STACK CFI INIT b758 1ac .cfa: sp 0 + .ra: x30
STACK CFI b75c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI b770 x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI b834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b838 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI INIT b908 348 .cfa: sp 0 + .ra: x30
STACK CFI b90c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b914 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b92c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b93c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b984 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ba54 x27: x27 x28: x28
STACK CFI ba80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ba84 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI babc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bbf8 x27: x27 x28: x28
STACK CFI bbfc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bc14 x27: x27 x28: x28
STACK CFI INIT bc50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bc58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bc60 180 .cfa: sp 0 + .ra: x30
STACK CFI bc64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bc74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bc7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bcc4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI bd3c x25: .cfa -16 + ^
STACK CFI bda0 x21: x21 x22: x22
STACK CFI bda4 x23: x23 x24: x24
STACK CFI bda8 x25: x25
STACK CFI bdac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bdb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI bdbc x21: x21 x22: x22
STACK CFI bdc0 x23: x23 x24: x24
STACK CFI bdc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bdc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI bdcc x21: x21 x22: x22
STACK CFI bddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bde0 6c .cfa: sp 0 + .ra: x30
STACK CFI bde4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bdf0 x21: .cfa -16 + ^
STACK CFI be00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI be3c x19: x19 x20: x20
STACK CFI be48 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT be50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT be58 334 .cfa: sp 0 + .ra: x30
STACK CFI be5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI be64 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI be74 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI be80 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI be8c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI bed4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bf8c x27: x27 x28: x28
STACK CFI bfdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bfe0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI c000 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c140 x27: x27 x28: x28
STACK CFI c144 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c15c x27: x27 x28: x28
STACK CFI INIT c190 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c198 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c1a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c1a8 44 .cfa: sp 0 + .ra: x30
STACK CFI c1ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT c1f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c200 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c210 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c218 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c228 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c240 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT c258 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c260 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c278 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c288 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c298 130 .cfa: sp 0 + .ra: x30
STACK CFI c2a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c2a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c2b4 x21: .cfa -16 + ^
STACK CFI c2fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c300 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c3c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c3c8 90 .cfa: sp 0 + .ra: x30
STACK CFI c3cc .cfa: sp 592 +
STACK CFI c3d0 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI c3d8 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI c3e0 x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI c3ec x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI c450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c454 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x29: .cfa -592 + ^
STACK CFI INIT c458 11c .cfa: sp 0 + .ra: x30
STACK CFI c460 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c468 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c474 x21: .cfa -16 + ^
STACK CFI c4b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c4bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c578 34 .cfa: sp 0 + .ra: x30
STACK CFI c57c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c584 x19: .cfa -16 + ^
STACK CFI c5a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c5b0 208 .cfa: sp 0 + .ra: x30
STACK CFI c5b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c5bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c5d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c5dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c5e8 x25: .cfa -16 + ^
STACK CFI c608 x25: x25
STACK CFI c658 x21: x21 x22: x22
STACK CFI c65c x23: x23 x24: x24
STACK CFI c664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c668 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI c6fc x25: x25
STACK CFI c700 x25: .cfa -16 + ^
STACK CFI c78c x25: x25
STACK CFI INIT c7b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c7c8 c8 .cfa: sp 0 + .ra: x30
STACK CFI c7cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c7d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c7e4 x21: .cfa -16 + ^
STACK CFI c884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c888 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c890 f4 .cfa: sp 0 + .ra: x30
STACK CFI c894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c89c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c938 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c988 178 .cfa: sp 0 + .ra: x30
STACK CFI c98c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c994 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c9a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c9ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cab8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT cb00 828 .cfa: sp 0 + .ra: x30
STACK CFI cb04 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI cb0c x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI cb18 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI cb30 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI cb3c x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI cb40 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI cc94 x23: x23 x24: x24
STACK CFI cc98 x25: x25 x26: x26
STACK CFI cc9c x27: x27 x28: x28
STACK CFI ccc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ccc4 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI ccfc x23: x23 x24: x24
STACK CFI cd00 x25: x25 x26: x26
STACK CFI cd04 x27: x27 x28: x28
STACK CFI cd18 x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI d30c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d310 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI d314 x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI d318 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI INIT d328 170 .cfa: sp 0 + .ra: x30
STACK CFI d32c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d334 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d344 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d34c x23: .cfa -32 + ^
STACK CFI d44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d450 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT d498 170 .cfa: sp 0 + .ra: x30
STACK CFI d49c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d4a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d4b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d4bc x23: .cfa -32 + ^
STACK CFI d5bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d5c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT d608 1a8 .cfa: sp 0 + .ra: x30
STACK CFI d60c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI d614 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI d620 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI d62c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI d634 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI d63c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI d74c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d750 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT d7b0 a7c .cfa: sp 0 + .ra: x30
STACK CFI d7b4 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI d7bc x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI d7c8 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI d7e0 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI d7e8 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI d7f0 x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI d990 x23: x23 x24: x24
STACK CFI d994 x25: x25 x26: x26
STACK CFI d998 x27: x27 x28: x28
STACK CFI d9bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d9c0 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI d9e8 x23: x23 x24: x24
STACK CFI d9ec x25: x25 x26: x26
STACK CFI d9f0 x27: x27 x28: x28
STACK CFI da04 x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI e1fc x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e200 x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI e204 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI e208 x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI INIT e230 1b40 .cfa: sp 0 + .ra: x30
STACK CFI e234 .cfa: sp 192 +
STACK CFI e238 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI e240 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI e258 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI e268 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI e304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e308 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI e374 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI e580 x27: x27 x28: x28
STACK CFI e638 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI e848 x27: x27 x28: x28
STACK CFI e854 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI e920 x27: x27 x28: x28
STACK CFI e9c8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ea00 x27: x27 x28: x28
STACK CFI ea04 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI eaec x27: x27 x28: x28
STACK CFI eaf4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI eb90 x27: x27 x28: x28
STACK CFI eb94 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ec68 x27: x27 x28: x28
STACK CFI eccc x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ecf4 x27: x27 x28: x28
STACK CFI ecf8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ed00 x27: x27 x28: x28
STACK CFI ed04 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ed38 x27: x27 x28: x28
STACK CFI ed3c x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI ed64 x27: x27 x28: x28
STACK CFI ed68 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI eeb4 x27: x27 x28: x28
STACK CFI eeb8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI f004 x27: x27 x28: x28
STACK CFI f018 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI f664 x27: x27 x28: x28
STACK CFI f668 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI fa90 x27: x27 x28: x28
STACK CFI fa94 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT fd70 324 .cfa: sp 0 + .ra: x30
STACK CFI fd74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fd7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI fd8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI fd94 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI fe70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fe74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 10098 804 .cfa: sp 0 + .ra: x30
STACK CFI 1009c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 100ac x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 100b4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 100c4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 100fc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 101a4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 10590 x27: x27 x28: x28
STACK CFI 105c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 105c8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI 10630 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1066c x27: x27 x28: x28
STACK CFI 10694 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 10794 x27: x27 x28: x28
STACK CFI 107a0 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 10840 x27: x27 x28: x28
STACK CFI 10844 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 10868 x27: x27 x28: x28
STACK CFI 10898 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 108a0 234 .cfa: sp 0 + .ra: x30
STACK CFI 108a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 108ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 108bc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 108c8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 108d4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 10958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1095c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 10a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10a48 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 10ad8 234 .cfa: sp 0 + .ra: x30
STACK CFI 10adc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 10ae4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 10af4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 10b00 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 10b0c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 10b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10b94 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 10c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10c80 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 10d10 8c .cfa: sp 0 + .ra: x30
STACK CFI 10d14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10d1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10d50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 10d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 10d78 x21: .cfa -16 + ^
STACK CFI 10d98 x21: x21
STACK CFI INIT 10da0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10dd8 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e08 9c .cfa: sp 0 + .ra: x30
STACK CFI 10e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10e1c x19: .cfa -16 + ^
STACK CFI 10e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10e94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10e98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10ea8 170 .cfa: sp 0 + .ra: x30
STACK CFI 10eb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10ec0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 10ecc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 10ed8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10ee8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 10ef0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10fe0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 11014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 11018 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110b8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110e0 100 .cfa: sp 0 + .ra: x30
STACK CFI 110e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 110ec x19: .cfa -16 + ^
STACK CFI 11168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1116c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 111e0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 111e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 111ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1125c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 11274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 11290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11294 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 11310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11314 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 113b8 ec .cfa: sp 0 + .ra: x30
STACK CFI 113bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 113c4 x23: .cfa -16 + ^
STACK CFI 113d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1149c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 114a8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 114ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 114b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 114c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 114fc x23: .cfa -16 + ^
STACK CFI 11530 x23: x23
STACK CFI 11534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11538 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 11544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11548 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 11560 x23: x23
STACK CFI 11564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 11568 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1156c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1157c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 115d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 115dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1160c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11610 a0 .cfa: sp 0 + .ra: x30
STACK CFI 11614 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11624 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11698 x19: x19 x20: x20
STACK CFI 116a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 116a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
