MODULE Linux arm64 00FE62D92BF2DE169350FE3818834B270 libxcb-shape.so.0
INFO CODE_ID D962FE00F22B16DE9350FE3818834B27C6EF15CA
PUBLIC 10f8 0 xcb_shape_op_next
PUBLIC 1118 0 xcb_shape_op_end
PUBLIC 1130 0 xcb_shape_kind_next
PUBLIC 1150 0 xcb_shape_kind_end
PUBLIC 1168 0 xcb_shape_query_version
PUBLIC 11d0 0 xcb_shape_query_version_unchecked
PUBLIC 1238 0 xcb_shape_query_version_reply
PUBLIC 1240 0 xcb_shape_rectangles_sizeof
PUBLIC 1250 0 xcb_shape_rectangles_checked
PUBLIC 12e8 0 xcb_shape_rectangles
PUBLIC 1380 0 xcb_shape_rectangles_rectangles
PUBLIC 1388 0 xcb_shape_rectangles_rectangles_length
PUBLIC 13a0 0 xcb_shape_rectangles_rectangles_iterator
PUBLIC 13d0 0 xcb_shape_mask_checked
PUBLIC 1460 0 xcb_shape_mask
PUBLIC 14f0 0 xcb_shape_combine_checked
PUBLIC 1578 0 xcb_shape_combine
PUBLIC 1600 0 xcb_shape_offset_checked
PUBLIC 1688 0 xcb_shape_offset
PUBLIC 1710 0 xcb_shape_query_extents
PUBLIC 1780 0 xcb_shape_query_extents_unchecked
PUBLIC 17e8 0 xcb_shape_query_extents_reply
PUBLIC 17f0 0 xcb_shape_select_input_checked
PUBLIC 1868 0 xcb_shape_select_input
PUBLIC 18e0 0 xcb_shape_input_selected
PUBLIC 1950 0 xcb_shape_input_selected_unchecked
PUBLIC 19b8 0 xcb_shape_input_selected_reply
PUBLIC 19c0 0 xcb_shape_get_rectangles_sizeof
PUBLIC 19d0 0 xcb_shape_get_rectangles
PUBLIC 1a48 0 xcb_shape_get_rectangles_unchecked
PUBLIC 1ac0 0 xcb_shape_get_rectangles_rectangles
PUBLIC 1ac8 0 xcb_shape_get_rectangles_rectangles_length
PUBLIC 1ad0 0 xcb_shape_get_rectangles_rectangles_iterator
PUBLIC 1af0 0 xcb_shape_get_rectangles_reply
STACK CFI INIT 1038 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1068 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 10ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10b4 x19: .cfa -16 + ^
STACK CFI 10ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1118 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1130 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1150 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1168 64 .cfa: sp 0 + .ra: x30
STACK CFI 116c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 117c x19: .cfa -96 + ^
STACK CFI 11c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 11d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11e4 x19: .cfa -96 + ^
STACK CFI 122c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1230 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1238 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1240 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1250 98 .cfa: sp 0 + .ra: x30
STACK CFI 1254 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1264 x19: .cfa -144 + ^
STACK CFI 12e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 12e8 98 .cfa: sp 0 + .ra: x30
STACK CFI 12ec .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 12fc x19: .cfa -144 + ^
STACK CFI 1378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 137c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1388 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13a0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13d0 8c .cfa: sp 0 + .ra: x30
STACK CFI 13d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 13e4 x19: .cfa -112 + ^
STACK CFI 1454 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1458 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1460 8c .cfa: sp 0 + .ra: x30
STACK CFI 1464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1474 x19: .cfa -112 + ^
STACK CFI 14e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14e8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 14f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 14f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1504 x19: .cfa -112 + ^
STACK CFI 1570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1574 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1578 88 .cfa: sp 0 + .ra: x30
STACK CFI 157c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 158c x19: .cfa -112 + ^
STACK CFI 15f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 15fc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1600 84 .cfa: sp 0 + .ra: x30
STACK CFI 1604 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1614 x19: .cfa -112 + ^
STACK CFI 167c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1680 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1688 84 .cfa: sp 0 + .ra: x30
STACK CFI 168c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 169c x19: .cfa -112 + ^
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1708 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1710 6c .cfa: sp 0 + .ra: x30
STACK CFI 1714 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1724 x19: .cfa -96 + ^
STACK CFI 1774 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1778 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1780 68 .cfa: sp 0 + .ra: x30
STACK CFI 1784 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1794 x19: .cfa -96 + ^
STACK CFI 17e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 17e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 17e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 17f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1804 x19: .cfa -112 + ^
STACK CFI 1860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1864 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1868 74 .cfa: sp 0 + .ra: x30
STACK CFI 186c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 187c x19: .cfa -112 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18d8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 18e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 18e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 18f4 x19: .cfa -96 + ^
STACK CFI 1944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1948 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1950 68 .cfa: sp 0 + .ra: x30
STACK CFI 1954 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1964 x19: .cfa -96 + ^
STACK CFI 19b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 19b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 19d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 19e4 x19: .cfa -112 + ^
STACK CFI 1a40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a44 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a48 74 .cfa: sp 0 + .ra: x30
STACK CFI 1a4c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1a5c x19: .cfa -112 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ab8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1ac0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af0 4 .cfa: sp 0 + .ra: x30
