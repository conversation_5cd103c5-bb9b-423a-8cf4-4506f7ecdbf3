MODULE Linux arm64 9F7B38315D2D1D8FA8EBB7475BBA3E450 libebl_aarch64.so
INFO CODE_ID 31387B9F2D5D8F1DA8EBB7475BBA3E4506555AC9
PUBLIC 23e8 0 aarch64_init
STACK CFI INIT 21f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2228 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2268 48 .cfa: sp 0 + .ra: x30
STACK CFI 226c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2274 x19: .cfa -16 + ^
STACK CFI 22ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22b8 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2318 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2328 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2338 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2348 a0 .cfa: sp 0 + .ra: x30
STACK CFI 234c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2354 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 23bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23c0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 23e8 cc .cfa: sp 0 + .ra: x30
STACK CFI INIT 24b8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 24bc .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 24cc x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 2588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 258c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x29: .cfa -288 + ^
STACK CFI INIT 2598 118 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26d0 178 .cfa: sp 0 + .ra: x30
STACK CFI 26d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 26dc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 26ec x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2714 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2724 x21: x21 x22: x22
STACK CFI 2748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 274c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 277c x25: .cfa -96 + ^
STACK CFI 27f8 x21: x21 x22: x22
STACK CFI 2808 x25: x25
STACK CFI 280c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 2824 x25: x25
STACK CFI 282c x21: x21 x22: x22
STACK CFI 2830 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 2834 x21: x21 x22: x22
STACK CFI 2838 x25: x25
STACK CFI 2840 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2844 x25: .cfa -96 + ^
STACK CFI INIT 2848 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c8 334 .cfa: sp 0 + .ra: x30
STACK CFI 28cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 28d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 28f0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2984 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 29cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 29d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c00 178 .cfa: sp 0 + .ra: x30
STACK CFI 2c04 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2c0c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2c18 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2c34 x23: .cfa -96 + ^
STACK CFI 2ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2cd0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2d78 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2d7c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2d8c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2d9c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2e2c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2e4c x23: x23 x24: x24
STACK CFI 2e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e70 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2ee0 x23: x23 x24: x24
STACK CFI 2ee4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f1c x23: x23 x24: x24
STACK CFI 2f3c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f40 x23: x23 x24: x24
STACK CFI 2f54 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f5c x23: x23 x24: x24
STACK CFI 2f64 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 2f68 46c .cfa: sp 0 + .ra: x30
STACK CFI 2f6c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2f74 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2f80 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2fa0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3008 x25: .cfa -112 + ^
STACK CFI 3060 x25: x25
STACK CFI 308c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3090 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 30ec x25: .cfa -112 + ^
STACK CFI 3180 x25: x25
STACK CFI 3184 x25: .cfa -112 + ^
STACK CFI 3188 x25: x25
STACK CFI 31f8 x25: .cfa -112 + ^
STACK CFI 3200 x25: x25
STACK CFI 3204 x25: .cfa -112 + ^
STACK CFI 3218 x25: x25
STACK CFI 3220 x25: .cfa -112 + ^
STACK CFI 3240 x25: x25
STACK CFI 3248 x25: .cfa -112 + ^
STACK CFI 3284 x25: x25
STACK CFI 328c x25: .cfa -112 + ^
STACK CFI 32bc x25: x25
STACK CFI 3310 x25: .cfa -112 + ^
STACK CFI 3318 x25: x25
STACK CFI 331c x25: .cfa -112 + ^
STACK CFI 334c x25: x25
STACK CFI 3354 x25: .cfa -112 + ^
STACK CFI 335c x25: x25
STACK CFI 3360 x25: .cfa -112 + ^
STACK CFI 3374 x25: x25
STACK CFI 3380 x25: .cfa -112 + ^
STACK CFI 3384 x25: x25
STACK CFI 33a8 x25: .cfa -112 + ^
STACK CFI 33ac x25: x25
STACK CFI 33b0 x25: .cfa -112 + ^
STACK CFI INIT 33d8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3400 12c .cfa: sp 0 + .ra: x30
STACK CFI 3404 .cfa: sp 1152 +
STACK CFI 340c .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 3414 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 3424 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 3438 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 3524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3528 .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x29: .cfa -1152 + ^
STACK CFI INIT 3530 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3534 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 353c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 354c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3564 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 35b0 x25: .cfa -80 + ^
STACK CFI 3688 x25: x25
STACK CFI 36b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 36bc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 36c8 x25: x25
STACK CFI 36d8 x25: .cfa -80 + ^
