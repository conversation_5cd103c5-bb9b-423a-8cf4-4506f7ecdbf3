MODULE Linux arm64 A6820C7D67E49187FF0041A4FB83DB6A0 libslam.so
INFO CODE_ID 7D0C82A6E4678791FF0041A4FB83DB6A
PUBLIC c80 0 _init
PUBLIC ce0 0 call_weak_fn
PUBLIC cf4 0 deregister_tm_clones
PUBLIC d24 0 register_tm_clones
PUBLIC d60 0 __do_global_dtors_aux
PUBLIC db0 0 frame_dummy
PUBLIC dc0 0 fsd::slam::CoreAlgorithm::generateLocalMap()
PUBLIC dd0 0 fsd::slam::CoreAlgorithm::appendToGlobalMap(fsd::slam::LocalMap const&)
PUBLIC de0 0 fsd::slam::CoreAlgorithm::largeScaleOptimize()
PUBLIC df0 0 fsd::slam::CoreAlgorithm::storeGlobalMap(fsd::slam::HdMap const&) const
PUBLIC df4 0 _fini
STACK CFI INIT cf4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d24 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d60 50 .cfa: sp 0 + .ra: x30
STACK CFI d70 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d78 x19: .cfa -16 + ^
STACK CFI da8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT db0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT df0 4 .cfa: sp 0 + .ra: x30
