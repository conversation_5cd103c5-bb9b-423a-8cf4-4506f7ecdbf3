MODULE Linux arm64 2A7C719C5545E580CE385D4996F58AB40 libopencv_shape.so.4.3
INFO CODE_ID 9C717C2A455580E5CE385D4996F58AB47E8BE988
PUBLIC 5660 0 _init
PUBLIC 5c50 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.80]
PUBLIC 5cf0 0 _GLOBAL__sub_I_aff_trans.cpp
PUBLIC 5d20 0 _GLOBAL__sub_I_emdL1.cpp
PUBLIC 5d50 0 _GLOBAL__sub_I_haus_dis.cpp
PUBLIC 5d80 0 _GLOBAL__sub_I_hist_cost.cpp
PUBLIC 5db0 0 _GLOBAL__sub_I_sc_dis.cpp
PUBLIC 5de0 0 _GLOBAL__sub_I_tps_trans.cpp
PUBLIC 5e10 0 call_weak_fn
PUBLIC 5e28 0 deregister_tm_clones
PUBLIC 5e60 0 register_tm_clones
PUBLIC 5ea0 0 __do_global_dtors_aux
PUBLIC 5ee8 0 frame_dummy
PUBLIC 5f20 0 cv::Algorithm::clear()
PUBLIC 5f28 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 5f30 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 5f38 0 cv::Algorithm::empty() const
PUBLIC 5f40 0 cv::AffineTransformerImpl::setFullAffine(bool)
PUBLIC 5f48 0 cv::AffineTransformerImpl::getFullAffine() const
PUBLIC 5f50 0 std::_Sp_counted_ptr<cv::AffineTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 5f58 0 std::_Sp_counted_ptr<cv::AffineTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5f60 0 std::_Sp_counted_ptr<cv::AffineTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 5f68 0 std::_Sp_counted_ptr<cv::AffineTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5f70 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.40]
PUBLIC 6050 0 cv::AffineTransformerImpl::read(cv::FileNode const&)
PUBLIC 6180 0 cv::AffineTransformerImpl::write(cv::FileStorage&) const
PUBLIC 62e0 0 cv::AffineTransformerImpl::~AffineTransformerImpl()
PUBLIC 63b8 0 cv::AffineTransformerImpl::~AffineTransformerImpl()
PUBLIC 6488 0 std::_Sp_counted_ptr<cv::AffineTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 65a0 0 cv::Mat::~Mat()
PUBLIC 6630 0 cv::AffineTransformerImpl::warpImage(cv::_InputArray const&, cv::_OutputArray const&, int, int, cv::Scalar_<double> const&) const
PUBLIC 68e0 0 cv::AffineTransformerImpl::applyTransformation(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 7298 0 cv::MatExpr::~MatExpr()
PUBLIC 7450 0 cv::createAffineTransformer(bool)
PUBLIC 75a0 0 void std::vector<cv::DMatch, std::allocator<cv::DMatch> >::_M_emplace_back_aux<cv::DMatch const&>(cv::DMatch const&)
PUBLIC 7698 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> const&>(cv::Point_<float> const&)
PUBLIC 77a0 0 cv::AffineTransformerImpl::estimateTransformation(cv::_InputArray const&, cv::_InputArray const&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >&)
PUBLIC 9758 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.81]
PUBLIC 9838 0 EmdL1::~EmdL1()
PUBLIC 9ac0 0 EmdL1::fillBaseTrees(float*, float*)
PUBLIC 9e28 0 EmdL1::initBVTree()
PUBLIC a248 0 EmdL1::findLoopFromEnterBV()
PUBLIC a3f8 0 EmdL1::findNewSolution()
PUBLIC a650 0 std::vector<std::vector<cvEMDNode, std::allocator<cvEMDNode> >, std::allocator<std::vector<cvEMDNode, std::allocator<cvEMDNode> > > >::_M_default_append(unsigned long)
PUBLIC a810 0 std::vector<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> >, std::allocator<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> > > >::_M_default_append(unsigned long)
PUBLIC a9d0 0 std::vector<cvEMDNode, std::allocator<cvEMDNode> >::_M_default_append(unsigned long)
PUBLIC ab70 0 std::vector<cvEMDEdge, std::allocator<cvEMDEdge> >::_M_default_append(unsigned long)
PUBLIC acc0 0 std::vector<cvEMDEdge*, std::allocator<cvEMDEdge*> >::_M_default_append(unsigned long)
PUBLIC ae10 0 std::vector<cvEMDNode*, std::allocator<cvEMDNode*> >::_M_default_append(unsigned long)
PUBLIC af60 0 std::vector<std::vector<std::vector<cvEMDNode, std::allocator<cvEMDNode> >, std::allocator<std::vector<cvEMDNode, std::allocator<cvEMDNode> > > >, std::allocator<std::vector<std::vector<cvEMDNode, std::allocator<cvEMDNode> >, std::allocator<std::vector<cvEMDNode, std::allocator<cvEMDNode> > > > > >::_M_default_append(unsigned long)
PUBLIC b150 0 std::vector<std::vector<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> >, std::allocator<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> > > >, std::allocator<std::vector<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> >, std::allocator<std::vector<cvEMDEdge, std::allocator<cvEMDEdge> > > > > >::_M_default_append(unsigned long)
PUBLIC b340 0 EmdL1::initBaseTrees(int, int, int)
PUBLIC bff0 0 std::vector<float, std::allocator<float> >::_M_default_append(unsigned long)
PUBLIC c140 0 EmdL1::greedySolution2()
PUBLIC c750 0 std::vector<std::vector<float, std::allocator<float> >, std::allocator<std::vector<float, std::allocator<float> > > >::_M_default_append(unsigned long)
PUBLIC c910 0 EmdL1::greedySolution3()
PUBLIC d2f8 0 EmdL1::getEMDL1(cv::Mat&, cv::Mat&)
PUBLIC d760 0 cv::EMDL1(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC dd38 0 cv::HausdorffDistanceExtractorImpl::setDistanceFlag(int)
PUBLIC dd40 0 cv::HausdorffDistanceExtractorImpl::getDistanceFlag() const
PUBLIC dd48 0 cv::HausdorffDistanceExtractorImpl::getRankProportion() const
PUBLIC dd50 0 std::_Sp_counted_ptr<cv::HausdorffDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC dd58 0 std::_Sp_counted_ptr<cv::HausdorffDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC dd60 0 std::_Sp_counted_ptr<cv::HausdorffDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC dd68 0 std::_Sp_counted_ptr<cv::HausdorffDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC dd70 0 cv::HausdorffDistanceExtractorImpl::~HausdorffDistanceExtractorImpl()
PUBLIC ddb8 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.26]
PUBLIC de98 0 cv::HausdorffDistanceExtractorImpl::setRankProportion(float)
PUBLIC df18 0 cv::HausdorffDistanceExtractorImpl::read(cv::FileNode const&)
PUBLIC e060 0 cv::HausdorffDistanceExtractorImpl::~HausdorffDistanceExtractorImpl()
PUBLIC e0b0 0 std::_Sp_counted_ptr<cv::HausdorffDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC e110 0 cv::HausdorffDistanceExtractorImpl::write(cv::FileStorage&) const
PUBLIC e328 0 cv::createHausdorffDistanceExtractor(int, float)
PUBLIC e440 0 cv::Mat::Mat<float>(cv::Point_<float> const&, bool)
PUBLIC e520 0 cv::_apply(cv::Mat const&, cv::Mat const&, int, double)
PUBLIC eae0 0 cv::HausdorffDistanceExtractorImpl::computeDistance(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC f1b0 0 cv::NormHistogramCostExtractorImpl::setNDummies(int)
PUBLIC f1b8 0 cv::NormHistogramCostExtractorImpl::getNDummies() const
PUBLIC f1c0 0 cv::NormHistogramCostExtractorImpl::setDefaultCost(float)
PUBLIC f1c8 0 cv::NormHistogramCostExtractorImpl::getDefaultCost() const
PUBLIC f1d0 0 cv::NormHistogramCostExtractorImpl::setNormFlag(int)
PUBLIC f1d8 0 cv::NormHistogramCostExtractorImpl::getNormFlag() const
PUBLIC f1e0 0 cv::EMDHistogramCostExtractorImpl::setNDummies(int)
PUBLIC f1e8 0 cv::EMDHistogramCostExtractorImpl::getNDummies() const
PUBLIC f1f0 0 cv::EMDHistogramCostExtractorImpl::setDefaultCost(float)
PUBLIC f1f8 0 cv::EMDHistogramCostExtractorImpl::getDefaultCost() const
PUBLIC f200 0 cv::EMDHistogramCostExtractorImpl::setNormFlag(int)
PUBLIC f208 0 cv::EMDHistogramCostExtractorImpl::getNormFlag() const
PUBLIC f210 0 cv::ChiHistogramCostExtractorImpl::setNDummies(int)
PUBLIC f218 0 cv::ChiHistogramCostExtractorImpl::getNDummies() const
PUBLIC f220 0 cv::ChiHistogramCostExtractorImpl::setDefaultCost(float)
PUBLIC f228 0 cv::ChiHistogramCostExtractorImpl::getDefaultCost() const
PUBLIC f230 0 cv::EMDL1HistogramCostExtractorImpl::setNDummies(int)
PUBLIC f238 0 cv::EMDL1HistogramCostExtractorImpl::getNDummies() const
PUBLIC f240 0 cv::EMDL1HistogramCostExtractorImpl::setDefaultCost(float)
PUBLIC f248 0 cv::EMDL1HistogramCostExtractorImpl::getDefaultCost() const
PUBLIC f250 0 std::_Sp_counted_ptr<cv::EMDL1HistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f258 0 std::_Sp_counted_ptr<cv::ChiHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f260 0 std::_Sp_counted_ptr<cv::EMDHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f268 0 std::_Sp_counted_ptr<cv::NormHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f270 0 std::_Sp_counted_ptr<cv::EMDL1HistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC f278 0 std::_Sp_counted_ptr<cv::ChiHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC f280 0 std::_Sp_counted_ptr<cv::EMDHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC f288 0 std::_Sp_counted_ptr<cv::NormHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC f290 0 std::_Sp_counted_ptr<cv::NormHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f298 0 std::_Sp_counted_ptr<cv::NormHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC f2a0 0 std::_Sp_counted_ptr<cv::EMDL1HistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f2a8 0 std::_Sp_counted_ptr<cv::EMDL1HistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC f2b0 0 std::_Sp_counted_ptr<cv::ChiHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f2b8 0 std::_Sp_counted_ptr<cv::ChiHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC f2c0 0 std::_Sp_counted_ptr<cv::EMDHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC f2c8 0 std::_Sp_counted_ptr<cv::EMDHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC f2d0 0 cv::EMDL1HistogramCostExtractorImpl::~EMDL1HistogramCostExtractorImpl()
PUBLIC f318 0 cv::ChiHistogramCostExtractorImpl::~ChiHistogramCostExtractorImpl()
PUBLIC f360 0 cv::EMDHistogramCostExtractorImpl::~EMDHistogramCostExtractorImpl()
PUBLIC f3a8 0 cv::NormHistogramCostExtractorImpl::~NormHistogramCostExtractorImpl()
PUBLIC f3f0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.24]
PUBLIC f4d0 0 cv::EMDHistogramCostExtractorImpl::read(cv::FileNode const&)
PUBLIC f638 0 cv::NormHistogramCostExtractorImpl::~NormHistogramCostExtractorImpl()
PUBLIC f688 0 cv::EMDHistogramCostExtractorImpl::~EMDHistogramCostExtractorImpl()
PUBLIC f6d8 0 cv::EMDL1HistogramCostExtractorImpl::~EMDL1HistogramCostExtractorImpl()
PUBLIC f728 0 cv::ChiHistogramCostExtractorImpl::~ChiHistogramCostExtractorImpl()
PUBLIC f778 0 std::_Sp_counted_ptr<cv::EMDL1HistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f7d0 0 std::_Sp_counted_ptr<cv::ChiHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f828 0 std::_Sp_counted_ptr<cv::EMDHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f880 0 std::_Sp_counted_ptr<cv::NormHistogramCostExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f8d8 0 cv::EMDL1HistogramCostExtractorImpl::read(cv::FileNode const&)
PUBLIC fa20 0 cv::ChiHistogramCostExtractorImpl::read(cv::FileNode const&)
PUBLIC fb68 0 cv::NormHistogramCostExtractorImpl::read(cv::FileNode const&)
PUBLIC fcd0 0 cv::EMDL1HistogramCostExtractorImpl::write(cv::FileStorage&) const
PUBLIC fef8 0 cv::ChiHistogramCostExtractorImpl::write(cv::FileStorage&) const
PUBLIC 10120 0 cv::NormHistogramCostExtractorImpl::write(cv::FileStorage&) const
PUBLIC 10408 0 cv::EMDHistogramCostExtractorImpl::write(cv::FileStorage&) const
PUBLIC 106f0 0 cv::ChiHistogramCostExtractorImpl::buildCostMatrix(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 11250 0 cv::NormHistogramCostExtractorImpl::buildCostMatrix(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 121d0 0 cv::EMDHistogramCostExtractorImpl::buildCostMatrix(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 13950 0 cv::EMDL1HistogramCostExtractorImpl::buildCostMatrix(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 14c68 0 cv::createNormHistogramCostExtractor(int, int, float)
PUBLIC 14d88 0 cv::createEMDHistogramCostExtractor(int, int, float)
PUBLIC 14ea8 0 cv::createChiHistogramCostExtractor(int, float)
PUBLIC 14fc0 0 cv::createEMDL1HistogramCostExtractor(int, float)
PUBLIC 150d8 0 cv::ShapeContextDistanceExtractorImpl::getAngularBins() const
PUBLIC 150e0 0 cv::ShapeContextDistanceExtractorImpl::getRadialBins() const
PUBLIC 150e8 0 cv::ShapeContextDistanceExtractorImpl::getInnerRadius() const
PUBLIC 150f0 0 cv::ShapeContextDistanceExtractorImpl::getOuterRadius() const
PUBLIC 150f8 0 cv::ShapeContextDistanceExtractorImpl::setRotationInvariant(bool)
PUBLIC 15100 0 cv::ShapeContextDistanceExtractorImpl::getRotationInvariant() const
PUBLIC 15108 0 cv::ShapeContextDistanceExtractorImpl::setShapeContextWeight(float)
PUBLIC 15110 0 cv::ShapeContextDistanceExtractorImpl::getShapeContextWeight() const
PUBLIC 15118 0 cv::ShapeContextDistanceExtractorImpl::setImageAppearanceWeight(float)
PUBLIC 15120 0 cv::ShapeContextDistanceExtractorImpl::getImageAppearanceWeight() const
PUBLIC 15128 0 cv::ShapeContextDistanceExtractorImpl::setBendingEnergyWeight(float)
PUBLIC 15130 0 cv::ShapeContextDistanceExtractorImpl::getBendingEnergyWeight() const
PUBLIC 15138 0 cv::ShapeContextDistanceExtractorImpl::setStdDev(float)
PUBLIC 15140 0 cv::ShapeContextDistanceExtractorImpl::getStdDev() const
PUBLIC 15148 0 cv::ShapeContextDistanceExtractorImpl::getIterations() const
PUBLIC 15150 0 std::_Sp_counted_ptr<cv::ShapeContextDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15158 0 std::_Sp_counted_ptr<cv::ShapeContextDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 15160 0 cv::ShapeContextDistanceExtractorImpl::getTransformAlgorithm() const
PUBLIC 151b0 0 cv::ShapeContextDistanceExtractorImpl::getCostExtractor() const
PUBLIC 15200 0 std::_Sp_counted_ptr<cv::ShapeContextDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15208 0 std::_Sp_counted_ptr<cv::ShapeContextDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15210 0 cv::ShapeContextDistanceExtractorImpl::setIterations(int)
PUBLIC 15280 0 cv::ShapeContextDistanceExtractorImpl::setOuterRadius(float)
PUBLIC 152f0 0 cv::ShapeContextDistanceExtractorImpl::setInnerRadius(float)
PUBLIC 15360 0 cv::ShapeContextDistanceExtractorImpl::setRadialBins(int)
PUBLIC 153d0 0 cv::ShapeContextDistanceExtractorImpl::setAngularBins(int)
PUBLIC 15440 0 cv::ShapeContextDistanceExtractorImpl::read(cv::FileNode const&)
PUBLIC 15648 0 cv::ShapeContextDistanceExtractorImpl::setCostExtractor(cv::Ptr<cv::HistogramCostExtractor>)
PUBLIC 15760 0 cv::ShapeContextDistanceExtractorImpl::setTransformAlgorithm(cv::Ptr<cv::ShapeTransformer>)
PUBLIC 15878 0 cv::ShapeContextDistanceExtractorImpl::getImages(cv::_OutputArray const&, cv::_OutputArray const&) const
PUBLIC 159a8 0 cv::ShapeContextDistanceExtractorImpl::~ShapeContextDistanceExtractorImpl()
PUBLIC 15c58 0 cv::ShapeContextDistanceExtractorImpl::~ShapeContextDistanceExtractorImpl()
PUBLIC 15f00 0 std::_Sp_counted_ptr<cv::ShapeContextDistanceExtractorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 161e8 0 cv::ShapeContextDistanceExtractorImpl::write(cv::FileStorage&) const
PUBLIC 16b18 0 cv::ShapeContextDistanceExtractorImpl::setImages(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 17050 0 cv::SCD::buildAngleMatrix(cv::Mat&, cv::Mat&) const
PUBLIC 17380 0 cv::SCD::buildNormalizedDistanceMatrix(cv::Mat&, cv::Mat&, std::vector<int, std::allocator<int> > const&, float)
PUBLIC 17858 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 17910 0 cv::createShapeContextDistanceExtractor(int, int, float, float, int, cv::Ptr<cv::HistogramCostExtractor> const&, cv::Ptr<cv::ShapeTransformer> const&)
PUBLIC 17cc0 0 void std::vector<double, std::allocator<double> >::_M_emplace_back_aux<double const&>(double const&)
PUBLIC 17db0 0 cv::SCD::extractSCD(cv::Mat&, cv::Mat&, std::vector<int, std::allocator<int> > const&, float)
PUBLIC 188f0 0 cv::SCDMatcher::hungarian(cv::Mat&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >&, std::vector<int, std::allocator<int> >&, std::vector<int, std::allocator<int> >&, int, int)
PUBLIC 19a70 0 cv::ShapeContextDistanceExtractorImpl::computeDistance(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 1cae0 0 cv::ThinPlateSplineShapeTransformerImpl::setRegularizationParameter(double)
PUBLIC 1cae8 0 cv::ThinPlateSplineShapeTransformerImpl::getRegularizationParameter() const
PUBLIC 1caf0 0 std::_Sp_counted_ptr<cv::ThinPlateSplineShapeTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 1caf8 0 std::_Sp_counted_ptr<cv::ThinPlateSplineShapeTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 1cb00 0 std::_Sp_counted_ptr<cv::ThinPlateSplineShapeTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 1cb08 0 std::_Sp_counted_ptr<cv::ThinPlateSplineShapeTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 1cb10 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.34]
PUBLIC 1cbf0 0 cv::ThinPlateSplineShapeTransformerImpl::read(cv::FileNode const&)
PUBLIC 1cd20 0 cv::_applyTransformation(cv::Mat const&, cv::Point_<float>, cv::Mat const&)
PUBLIC 1ce88 0 cv::ThinPlateSplineShapeTransformerImpl::write(cv::FileStorage&) const
PUBLIC 1cfe8 0 cv::ThinPlateSplineShapeTransformerImpl::~ThinPlateSplineShapeTransformerImpl()
PUBLIC 1d148 0 cv::ThinPlateSplineShapeTransformerImpl::~ThinPlateSplineShapeTransformerImpl()
PUBLIC 1d2a0 0 std::_Sp_counted_ptr<cv::ThinPlateSplineShapeTransformerImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 1d410 0 cv::ThinPlateSplineShapeTransformerImpl::warpImage(cv::_InputArray const&, cv::_OutputArray const&, int, int, cv::Scalar_<double> const&) const
PUBLIC 1d8f0 0 cv::ThinPlateSplineShapeTransformerImpl::applyTransformation(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 1dd90 0 cv::createThinPlateSplineShapeTransformer(double)
PUBLIC 1df10 0 cv::ThinPlateSplineShapeTransformerImpl::estimateTransformation(cv::_InputArray const&, cv::_InputArray const&, std::vector<cv::DMatch, std::allocator<cv::DMatch> >&)
PUBLIC 1fa44 0 _fini
STACK CFI INIT 5f20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f70 dc .cfa: sp 0 + .ra: x30
STACK CFI 5f74 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5f78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5f80 .ra: .cfa -32 + ^
STACK CFI 5fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5fd0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6018 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6040 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 6050 130 .cfa: sp 0 + .ra: x30
STACK CFI 6054 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6058 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 606c .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 60ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 60f0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 6180 160 .cfa: sp 0 + .ra: x30
STACK CFI 6184 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 618c .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 6280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6284 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 62e0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62f4 .ra: .cfa -16 + ^
STACK CFI 63a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 63a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 63b8 cc .cfa: sp 0 + .ra: x30
STACK CFI 63bc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63cc .ra: .cfa -16 + ^
STACK CFI 6474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6478 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6488 118 .cfa: sp 0 + .ra: x30
STACK CFI 648c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6490 .ra: .cfa -16 + ^
STACK CFI 6568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6570 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6580 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 659c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 65a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 65a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6618 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 6620 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 662c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6630 2ac .cfa: sp 0 + .ra: x30
STACK CFI 6634 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 664c x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 6658 .ra: .cfa -152 + ^ x25: .cfa -160 + ^
STACK CFI 67b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 67b8 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI INIT 68e0 9a0 .cfa: sp 0 + .ra: x30
STACK CFI 68e4 .cfa: sp 784 +
STACK CFI 68e8 x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI 68f8 x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI 6900 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 6918 .ra: .cfa -720 + ^ v8: .cfa -712 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7118 .cfa: sp 784 + .ra: .cfa -720 + ^ v8: .cfa -712 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI INIT 7298 1ac .cfa: sp 0 + .ra: x30
STACK CFI 729c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72a8 .ra: .cfa -16 + ^
STACK CFI 7404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7408 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7450 134 .cfa: sp 0 + .ra: x30
STACK CFI 7454 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 745c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7464 .ra: .cfa -16 + ^
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7514 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 75a0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 75a4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75ac .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 75b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 7668 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 7698 100 .cfa: sp 0 + .ra: x30
STACK CFI 769c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 76a4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 76ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 7768 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 77a0 1f88 .cfa: sp 0 + .ra: x30
STACK CFI 77a4 .cfa: sp 1600 +
STACK CFI 77ac x21: .cfa -1584 + ^ x22: .cfa -1576 + ^
STACK CFI 77bc x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 77e4 .ra: .cfa -1520 + ^ v10: .cfa -1512 + ^ v8: .cfa -1504 + ^ v9: .cfa -1496 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI 8914 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8918 .cfa: sp 1600 + .ra: .cfa -1520 + ^ v10: .cfa -1512 + ^ v8: .cfa -1504 + ^ v9: .cfa -1496 + ^ x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI INIT 5cf0 30 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5d10 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9758 dc .cfa: sp 0 + .ra: x30
STACK CFI 975c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9760 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9768 .ra: .cfa -32 + ^
STACK CFI 97b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 97b8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 97fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 9800 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 9828 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 9838 284 .cfa: sp 0 + .ra: x30
STACK CFI 983c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9848 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 9aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 9aac .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 9ac0 364 .cfa: sp 0 + .ra: x30
STACK CFI 9ac4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9ac8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9adc .ra: .cfa -32 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9b14 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9cdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9ce0 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 9e28 41c .cfa: sp 0 + .ra: x30
STACK CFI 9e2c .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9e48 .ra: .cfa -64 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9ff8 .cfa: sp 144 + .ra: .cfa -64 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT a248 1a8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a3f8 258 .cfa: sp 0 + .ra: x30
STACK CFI a400 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a404 .ra: .cfa -48 + ^
STACK CFI a5d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI a5d8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a5f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI a5f8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT a650 1bc .cfa: sp 0 + .ra: x30
STACK CFI a6b4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a6b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a6c8 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI a7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a7e8 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT a810 1bc .cfa: sp 0 + .ra: x30
STACK CFI a874 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a878 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a888 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI a9a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a9a8 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT a9d0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI a9d8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a9dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a9ec .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI aad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI aae0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI ab28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI ab38 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT ab70 14c .cfa: sp 0 + .ra: x30
STACK CFI abc4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI abd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI abdc .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI ac84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI ac88 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT acc0 14c .cfa: sp 0 + .ra: x30
STACK CFI acc8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ace0 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI ad20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI ad30 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI adcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI add0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT ae10 14c .cfa: sp 0 + .ra: x30
STACK CFI ae18 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ae30 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI ae70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI ae80 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI af1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI af20 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT af60 1ec .cfa: sp 0 + .ra: x30
STACK CFI afc0 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI afc4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI afdc .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI b120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI b124 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT b150 1ec .cfa: sp 0 + .ra: x30
STACK CFI b1b0 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b1b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b1cc .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI b310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI b314 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT b340 cac .cfa: sp 0 + .ra: x30
STACK CFI b344 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b348 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b350 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b360 .ra: .cfa -32 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b3a8 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b964 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b968 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bd9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bda0 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT bff0 14c .cfa: sp 0 + .ra: x30
STACK CFI bff8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c010 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI c060 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c0fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI c100 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT c140 610 .cfa: sp 0 + .ra: x30
STACK CFI c144 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c158 .ra: .cfa -32 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c6d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c6d4 .cfa: sp 96 + .ra: .cfa -32 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT c750 1bc .cfa: sp 0 + .ra: x30
STACK CFI c7b4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c7b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c7c8 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI c8e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI c8e8 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT c910 9e4 .cfa: sp 0 + .ra: x30
STACK CFI c914 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI c92c .ra: .cfa -80 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI d228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d22c .cfa: sp 160 + .ra: .cfa -80 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT d2f8 464 .cfa: sp 0 + .ra: x30
STACK CFI d2fc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d300 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d308 .ra: .cfa -40 + ^ x23: .cfa -48 + ^
STACK CFI d6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI d6e0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI d700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI d704 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT d760 5cc .cfa: sp 0 + .ra: x30
STACK CFI d764 .cfa: sp 624 +
STACK CFI d768 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI d778 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI d790 .ra: .cfa -568 + ^ v8: .cfa -560 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^
STACK CFI dbc0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI dbc4 .cfa: sp 624 + .ra: .cfa -568 + ^ v8: .cfa -560 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^
STACK CFI INIT 5d20 30 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5d40 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT dd38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd70 48 .cfa: sp 0 + .ra: x30
STACK CFI dd74 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ddb4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ddb8 dc .cfa: sp 0 + .ra: x30
STACK CFI ddbc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ddc0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ddc8 .ra: .cfa -32 + ^
STACK CFI de14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI de18 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI de5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI de60 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI de80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI de88 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT de98 7c .cfa: sp 0 + .ra: x30
STACK CFI deb8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dec8 .ra: .cfa -48 + ^
STACK CFI INIT df18 148 .cfa: sp 0 + .ra: x30
STACK CFI df1c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI df20 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI df34 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI dfcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI dfd0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT e060 50 .cfa: sp 0 + .ra: x30
STACK CFI e064 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e0ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e0b0 60 .cfa: sp 0 + .ra: x30
STACK CFI e0b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e100 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI e108 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e10c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e110 214 .cfa: sp 0 + .ra: x30
STACK CFI e114 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e11c .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI e28c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e290 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT e328 118 .cfa: sp 0 + .ra: x30
STACK CFI e32c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e334 v8: .cfa -8 + ^
STACK CFI e33c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e344 .ra: .cfa -16 + ^
STACK CFI e3cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI e3d0 .cfa: sp 48 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT e440 a4 .cfa: sp 0 + .ra: x30
STACK CFI e448 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e450 .ra: .cfa -32 + ^
STACK CFI e4a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e4a8 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e520 5a0 .cfa: sp 0 + .ra: x30
STACK CFI e528 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI e52c x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI e534 v8: .cfa -400 + ^ v9: .cfa -392 + ^
STACK CFI e548 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI e558 .ra: .cfa -416 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI ea5c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ea60 .cfa: sp 496 + .ra: .cfa -416 + ^ v8: .cfa -400 + ^ v9: .cfa -392 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT eae0 6b8 .cfa: sp 0 + .ra: x30
STACK CFI eae4 .cfa: sp 368 + x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI eae8 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI eb00 .ra: .cfa -328 + ^ v8: .cfa -320 + ^ v9: .cfa -312 + ^ x23: .cfa -336 + ^
STACK CFI f0b8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI f0c0 .cfa: sp 368 + .ra: .cfa -328 + ^ v8: .cfa -320 + ^ v9: .cfa -312 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^
STACK CFI INIT 5d50 30 .cfa: sp 0 + .ra: x30
STACK CFI 5d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5d70 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f1b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f1f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f200 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f208 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f210 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f218 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f228 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f238 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f248 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f250 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f258 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f260 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f268 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f278 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f288 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f290 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f298 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f2d0 44 .cfa: sp 0 + .ra: x30
STACK CFI f2d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f310 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f318 44 .cfa: sp 0 + .ra: x30
STACK CFI f31c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f358 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f360 44 .cfa: sp 0 + .ra: x30
STACK CFI f364 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f3a0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f3a8 44 .cfa: sp 0 + .ra: x30
STACK CFI f3ac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f3e8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f3f0 dc .cfa: sp 0 + .ra: x30
STACK CFI f3f4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f3f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f400 .ra: .cfa -32 + ^
STACK CFI f44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI f450 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI f498 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f4b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI f4c0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT f4d0 168 .cfa: sp 0 + .ra: x30
STACK CFI f4d4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI f4dc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI f4f0 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI f5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI f5a8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT f638 4c .cfa: sp 0 + .ra: x30
STACK CFI f63c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f680 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f688 4c .cfa: sp 0 + .ra: x30
STACK CFI f68c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f6d0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f6d8 4c .cfa: sp 0 + .ra: x30
STACK CFI f6dc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f720 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f728 4c .cfa: sp 0 + .ra: x30
STACK CFI f72c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f770 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f778 58 .cfa: sp 0 + .ra: x30
STACK CFI f77c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f7c4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI f7c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f7cc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f7d0 58 .cfa: sp 0 + .ra: x30
STACK CFI f7d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f81c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI f820 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f824 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f828 58 .cfa: sp 0 + .ra: x30
STACK CFI f82c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f874 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI f878 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f87c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f880 58 .cfa: sp 0 + .ra: x30
STACK CFI f884 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f8cc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI f8d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI f8d4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT f8d8 148 .cfa: sp 0 + .ra: x30
STACK CFI f8dc .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI f8e0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI f8f4 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI f98c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI f990 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT fa20 148 .cfa: sp 0 + .ra: x30
STACK CFI fa24 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI fa28 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI fa3c .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI fad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI fad8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT fb68 168 .cfa: sp 0 + .ra: x30
STACK CFI fb6c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI fb74 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI fb88 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI fc3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI fc40 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT fcd0 224 .cfa: sp 0 + .ra: x30
STACK CFI fcd4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI fcdc .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI fe5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI fe60 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT fef8 224 .cfa: sp 0 + .ra: x30
STACK CFI fefc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ff04 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 10084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10088 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 10120 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 10124 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1012c .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 10334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10338 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 10408 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 1040c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10414 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 1061c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10620 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 106f0 b28 .cfa: sp 0 + .ra: x30
STACK CFI 106f4 .cfa: sp 992 +
STACK CFI 106fc x21: .cfa -976 + ^ x22: .cfa -968 + ^
STACK CFI 1070c x19: .cfa -992 + ^ x20: .cfa -984 + ^
STACK CFI 1071c x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^
STACK CFI 10730 .ra: .cfa -912 + ^ v8: .cfa -896 + ^ v9: .cfa -888 + ^ x27: .cfa -928 + ^ x28: .cfa -920 + ^
STACK CFI 10fa0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10fa8 .cfa: sp 992 + .ra: .cfa -912 + ^ v8: .cfa -896 + ^ v9: .cfa -888 + ^ x19: .cfa -992 + ^ x20: .cfa -984 + ^ x21: .cfa -976 + ^ x22: .cfa -968 + ^ x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^ x27: .cfa -928 + ^ x28: .cfa -920 + ^
STACK CFI INIT 11250 f60 .cfa: sp 0 + .ra: x30
STACK CFI 11254 .cfa: sp 1472 +
STACK CFI 1125c x21: .cfa -1456 + ^ x22: .cfa -1448 + ^
STACK CFI 1126c x19: .cfa -1472 + ^ x20: .cfa -1464 + ^
STACK CFI 1127c x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI 11290 .ra: .cfa -1392 + ^ v8: .cfa -1376 + ^ v9: .cfa -1368 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^
STACK CFI 11f04 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11f08 .cfa: sp 1472 + .ra: .cfa -1392 + ^ v8: .cfa -1376 + ^ v9: .cfa -1368 + ^ x19: .cfa -1472 + ^ x20: .cfa -1464 + ^ x21: .cfa -1456 + ^ x22: .cfa -1448 + ^ x23: .cfa -1440 + ^ x24: .cfa -1432 + ^ x25: .cfa -1424 + ^ x26: .cfa -1416 + ^ x27: .cfa -1408 + ^ x28: .cfa -1400 + ^
STACK CFI INIT 121d0 173c .cfa: sp 0 + .ra: x30
STACK CFI 121d4 .cfa: sp 1584 +
STACK CFI 121dc x21: .cfa -1568 + ^ x22: .cfa -1560 + ^
STACK CFI 121ec x19: .cfa -1584 + ^ x20: .cfa -1576 + ^
STACK CFI 121fc x23: .cfa -1552 + ^ x24: .cfa -1544 + ^ x27: .cfa -1520 + ^ x28: .cfa -1512 + ^
STACK CFI 12210 .ra: .cfa -1504 + ^ v8: .cfa -1488 + ^ v9: .cfa -1480 + ^ x25: .cfa -1536 + ^ x26: .cfa -1528 + ^
STACK CFI 13638 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13640 .cfa: sp 1584 + .ra: .cfa -1504 + ^ v8: .cfa -1488 + ^ v9: .cfa -1480 + ^ x19: .cfa -1584 + ^ x20: .cfa -1576 + ^ x21: .cfa -1568 + ^ x22: .cfa -1560 + ^ x23: .cfa -1552 + ^ x24: .cfa -1544 + ^ x25: .cfa -1536 + ^ x26: .cfa -1528 + ^ x27: .cfa -1520 + ^ x28: .cfa -1512 + ^
STACK CFI INIT 13950 12f0 .cfa: sp 0 + .ra: x30
STACK CFI 13954 .cfa: sp 1568 +
STACK CFI 1395c x21: .cfa -1552 + ^ x22: .cfa -1544 + ^
STACK CFI 1396c x19: .cfa -1568 + ^ x20: .cfa -1560 + ^
STACK CFI 1397c x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI 13990 .ra: .cfa -1488 + ^ v8: .cfa -1472 + ^ v9: .cfa -1464 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^
STACK CFI 14980 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14984 .cfa: sp 1568 + .ra: .cfa -1488 + ^ v8: .cfa -1472 + ^ v9: .cfa -1464 + ^ x19: .cfa -1568 + ^ x20: .cfa -1560 + ^ x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI INIT 14c68 11c .cfa: sp 0 + .ra: x30
STACK CFI 14c6c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14c74 v8: .cfa -16 + ^
STACK CFI 14c7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14c84 .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 14d10 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 14d14 .cfa: sp 64 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI INIT 14d88 11c .cfa: sp 0 + .ra: x30
STACK CFI 14d8c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14d94 v8: .cfa -16 + ^
STACK CFI 14d9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14da4 .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 14e30 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 14e34 .cfa: sp 64 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI INIT 14ea8 118 .cfa: sp 0 + .ra: x30
STACK CFI 14eac .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14eb4 v8: .cfa -8 + ^
STACK CFI 14ebc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14ec4 .ra: .cfa -16 + ^
STACK CFI 14f4c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 14f50 .cfa: sp 48 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 14fc0 118 .cfa: sp 0 + .ra: x30
STACK CFI 14fc4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14fcc v8: .cfa -8 + ^
STACK CFI 14fd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14fdc .ra: .cfa -16 + ^
STACK CFI 15064 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 15068 .cfa: sp 48 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 5d80 30 .cfa: sp 0 + .ra: x30
STACK CFI 5d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5da0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 150d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 150e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 150e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 150f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 150f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15100 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15108 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15118 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15120 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15128 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15130 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15138 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15140 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15148 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15150 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15158 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15160 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 151b0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15200 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15208 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c50 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5c54 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c60 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5ce4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 15210 70 .cfa: sp 0 + .ra: x30
STACK CFI 15224 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15234 .ra: .cfa -48 + ^
STACK CFI INIT 15280 70 .cfa: sp 0 + .ra: x30
STACK CFI 15294 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 152a4 .ra: .cfa -48 + ^
STACK CFI INIT 152f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 15304 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15314 .ra: .cfa -48 + ^
STACK CFI INIT 15360 70 .cfa: sp 0 + .ra: x30
STACK CFI 15374 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15384 .ra: .cfa -48 + ^
STACK CFI INIT 153d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 153e4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 153f4 .ra: .cfa -48 + ^
STACK CFI INIT 15440 208 .cfa: sp 0 + .ra: x30
STACK CFI 15444 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1544c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15460 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 155b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 155b8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 15648 118 .cfa: sp 0 + .ra: x30
STACK CFI 1564c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15650 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 156bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 156c0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 15760 118 .cfa: sp 0 + .ra: x30
STACK CFI 15764 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15768 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 157d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 157d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 15878 130 .cfa: sp 0 + .ra: x30
STACK CFI 1587c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1588c .ra: .cfa -48 + ^
STACK CFI 15958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15960 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 159a8 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 159ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 159bc .ra: .cfa -16 + ^
STACK CFI 15b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15b48 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 15c58 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 15c5c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15c6c .ra: .cfa -16 + ^
STACK CFI 15de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15df0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 15f00 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 15f04 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15f08 .ra: .cfa -16 + ^
STACK CFI 160b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 160b8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 160c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 160c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 16178 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 161e8 92c .cfa: sp 0 + .ra: x30
STACK CFI 161ec .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 161f4 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 16884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 16888 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 16b18 538 .cfa: sp 0 + .ra: x30
STACK CFI 16b1c .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 16b24 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 16b2c .ra: .cfa -232 + ^ x23: .cfa -240 + ^
STACK CFI 16e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 16e08 .cfa: sp 272 + .ra: .cfa -232 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^
STACK CFI INIT 17050 30c .cfa: sp 0 + .ra: x30
STACK CFI 17058 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 1705c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1707c .ra: .cfa -144 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1708c v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^
STACK CFI 172a8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 172b0 .cfa: sp 224 + .ra: .cfa -144 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 17380 4b4 .cfa: sp 0 + .ra: x30
STACK CFI 17388 .cfa: sp 512 +
STACK CFI 17394 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 173b0 .ra: .cfa -432 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 17738 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17740 .cfa: sp 512 + .ra: .cfa -432 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI INIT 17858 b4 .cfa: sp 0 + .ra: x30
STACK CFI 17860 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1786c .ra: .cfa -16 + ^
STACK CFI 17894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17898 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 178dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 178e8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 17910 390 .cfa: sp 0 + .ra: x30
STACK CFI 17914 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1791c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 17928 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 17938 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1794c .ra: .cfa -32 + ^
STACK CFI 17b00 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 17b08 .cfa: sp 96 + .ra: .cfa -32 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 17cc0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 17cc4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17ccc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17cd8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 17d60 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17db0 b14 .cfa: sp 0 + .ra: x30
STACK CFI 17db8 .cfa: sp 832 +
STACK CFI 17dbc x19: .cfa -832 + ^ x20: .cfa -824 + ^
STACK CFI 17dc4 x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 17dec .ra: .cfa -752 + ^ v10: .cfa -720 + ^ v11: .cfa -712 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 17df4 v8: .cfa -736 + ^ v9: .cfa -728 + ^
STACK CFI 18724 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18728 .cfa: sp 832 + .ra: .cfa -752 + ^ v10: .cfa -720 + ^ v11: .cfa -712 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI INIT 188f0 114c .cfa: sp 0 + .ra: x30
STACK CFI 188f4 .cfa: sp 480 + x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 18910 .ra: .cfa -400 + ^ v10: .cfa -368 + ^ v11: .cfa -360 + ^ v8: .cfa -384 + ^ v9: .cfa -376 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 192ac .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 192b0 .cfa: sp 480 + .ra: .cfa -400 + ^ v10: .cfa -368 + ^ v11: .cfa -360 + ^ v8: .cfa -384 + ^ v9: .cfa -376 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI INIT 19a70 302c .cfa: sp 0 + .ra: x30
STACK CFI 19a74 .cfa: sp 2368 +
STACK CFI 19a7c x19: .cfa -2368 + ^ x20: .cfa -2360 + ^
STACK CFI 19ab4 .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v12: .cfa -2240 + ^ v13: .cfa -2232 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI 1b914 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1b918 .cfa: sp 2368 + .ra: .cfa -2288 + ^ v10: .cfa -2256 + ^ v11: .cfa -2248 + ^ v12: .cfa -2240 + ^ v13: .cfa -2232 + ^ v8: .cfa -2272 + ^ v9: .cfa -2264 + ^ x19: .cfa -2368 + ^ x20: .cfa -2360 + ^ x21: .cfa -2352 + ^ x22: .cfa -2344 + ^ x23: .cfa -2336 + ^ x24: .cfa -2328 + ^ x25: .cfa -2320 + ^ x26: .cfa -2312 + ^ x27: .cfa -2304 + ^ x28: .cfa -2296 + ^
STACK CFI INIT 5db0 30 .cfa: sp 0 + .ra: x30
STACK CFI 5db4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1cae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cae8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1caf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1caf8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cb10 dc .cfa: sp 0 + .ra: x30
STACK CFI 1cb14 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1cb18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cb20 .ra: .cfa -32 + ^
STACK CFI 1cb6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1cb70 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cbb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1cbb8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cbd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1cbe0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 1cbf0 130 .cfa: sp 0 + .ra: x30
STACK CFI 1cbf4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1cbf8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1cc0c .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 1cc88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1cc90 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 1cd20 160 .cfa: sp 0 + .ra: x30
STACK CFI 1cd24 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1cd2c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 1cd34 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1cd3c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1cd4c .ra: .cfa -48 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1cd60 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^
STACK CFI 1ce6c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1ce70 .cfa: sp 112 + .ra: .cfa -48 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 1ce88 15c .cfa: sp 0 + .ra: x30
STACK CFI 1ce8c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1ce94 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 1cf84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1cf88 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 1cfe8 15c .cfa: sp 0 + .ra: x30
STACK CFI 1cfec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cffc .ra: .cfa -16 + ^
STACK CFI 1d120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d128 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 1d148 154 .cfa: sp 0 + .ra: x30
STACK CFI 1d14c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d15c .ra: .cfa -16 + ^
STACK CFI 1d278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d280 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 1d2a0 16c .cfa: sp 0 + .ra: x30
STACK CFI 1d2a4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d2a8 .ra: .cfa -16 + ^
STACK CFI 1d3dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d3e0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1d410 4c8 .cfa: sp 0 + .ra: x30
STACK CFI 1d414 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 1d444 .ra: .cfa -416 + ^ v8: .cfa -408 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 1d79c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d7a0 .cfa: sp 496 + .ra: .cfa -416 + ^ v8: .cfa -408 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 1d8f0 494 .cfa: sp 0 + .ra: x30
STACK CFI 1d8f4 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1d904 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 1d928 .ra: .cfa -256 + ^ v8: .cfa -248 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 1dc28 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1dc30 .cfa: sp 336 + .ra: .cfa -256 + ^ v8: .cfa -248 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 1dd90 168 .cfa: sp 0 + .ra: x30
STACK CFI 1dd94 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1dda0 v8: .cfa -16 + ^
STACK CFI 1dda8 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 1de74 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI 1de78 .cfa: sp 48 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 1df10 1b20 .cfa: sp 0 + .ra: x30
STACK CFI 1df14 .cfa: sp 2432 +
STACK CFI 1df1c x21: .cfa -2416 + ^ x22: .cfa -2408 + ^
STACK CFI 1df2c x19: .cfa -2432 + ^ x20: .cfa -2424 + ^
STACK CFI 1df54 .ra: .cfa -2352 + ^ v10: .cfa -2344 + ^ v8: .cfa -2336 + ^ v9: .cfa -2328 + ^ x23: .cfa -2400 + ^ x24: .cfa -2392 + ^ x25: .cfa -2384 + ^ x26: .cfa -2376 + ^ x27: .cfa -2368 + ^ x28: .cfa -2360 + ^
STACK CFI 1f7a8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1f7ac .cfa: sp 2432 + .ra: .cfa -2352 + ^ v10: .cfa -2344 + ^ v8: .cfa -2336 + ^ v9: .cfa -2328 + ^ x19: .cfa -2432 + ^ x20: .cfa -2424 + ^ x21: .cfa -2416 + ^ x22: .cfa -2408 + ^ x23: .cfa -2400 + ^ x24: .cfa -2392 + ^ x25: .cfa -2384 + ^ x26: .cfa -2376 + ^ x27: .cfa -2368 + ^ x28: .cfa -2360 + ^
STACK CFI INIT 5de0 30 .cfa: sp 0 + .ra: x30
STACK CFI 5de4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5e00 .cfa: sp 0 + .ra: .ra x19: x19
