MODULE Linux arm64 BA3EC58F404D88DC0FE015C8AEDC31630 libXi.so.6
INFO CODE_ID 8FC53EBA4D40DC880FE015C8AEDC316331C8CC7D
PUBLIC 2328 0 XAllowDeviceEvents
PUBLIC 23f8 0 XChangeDeviceProperty
PUBLIC 2738 0 XChangeDeviceControl
PUBLIC 2b58 0 XChangeFeedbackControl
PUBLIC 2f40 0 XChangeKeyboardDevice
PUBLIC 3048 0 XChangeDeviceKeyMapping
PUBLIC 3180 0 XChangePointerDevice
PUBLIC 32a0 0 XChangeDeviceDontPropagateList
PUBLIC 33a0 0 XCloseDevice
PUBLIC 3468 0 XDeleteDeviceProperty
PUBLIC 3530 0 XDeviceBell
PUBLIC 3cc0 0 XGetDeviceButtonMapping
PUBLIC 3e40 0 XIGetClientPointer
PUBLIC 3f80 0 XGetDeviceControl
PUBLIC 42e0 0 XFreeDeviceControl
PUBLIC 42e8 0 XGetDeviceProperty
PUBLIC 4598 0 XGetFeedbackControl
PUBLIC 49c8 0 XFreeFeedbackList
PUBLIC 49d0 0 XGetDeviceKeyMapping
PUBLIC 4b90 0 XGetDeviceModifierMapping
PUBLIC 4d48 0 XGetDeviceDontPropagateList
PUBLIC 4ff0 0 XGetExtensionVersion
PUBLIC 50a8 0 XGetDeviceMotionEvents
PUBLIC 5390 0 XFreeDeviceMotionEvents
PUBLIC 5398 0 XGrabDevice
PUBLIC 5508 0 XGrabDeviceButton
PUBLIC 5648 0 XGrabDeviceKey
PUBLIC 5798 0 XGetDeviceFocus
PUBLIC 58c8 0 XGetSelectedExtensionEvents
PUBLIC 5b68 0 XListInputDevices
PUBLIC 6048 0 XFreeDeviceList
PUBLIC 6058 0 XListDeviceProperties
PUBLIC 61b0 0 XOpenDevice
PUBLIC 6380 0 XQueryDeviceState
PUBLIC 66f8 0 XFreeDeviceState
PUBLIC 6700 0 XSelectExtensionEvent
PUBLIC 67f0 0 XSetDeviceButtonMapping
PUBLIC 6960 0 XSetDeviceValuators
PUBLIC 6ac0 0 XSetDeviceModifierMapping
PUBLIC 6bf8 0 XSetDeviceMode
PUBLIC 6d08 0 XSendExtensionEvent
PUBLIC 6ed0 0 XSetDeviceFocus
PUBLIC 6fb0 0 XUngrabDevice
PUBLIC 7078 0 XUngrabDeviceButton
PUBLIC 7170 0 XUngrabDeviceKey
PUBLIC 85f0 0 _xibaddevice
PUBLIC 8620 0 _xibadclass
PUBLIC 8650 0 _xibadevent
PUBLIC 8680 0 _xibadmode
PUBLIC 86b0 0 _xidevicebusy
PUBLIC 8878 0 _XiGetDevicePresenceNotifyEvent
PUBLIC 9870 0 XIAllowEvents
PUBLIC 9880 0 XIAllowTouchEvents
PUBLIC 9938 0 XIGrabDevice
PUBLIC 9bd0 0 XIUngrabDevice
PUBLIC 9e00 0 XIQueryVersion
PUBLIC 9e60 0 XIQueryDevice
PUBLIC a1b8 0 XIFreeDeviceInfo
PUBLIC a208 0 XISetFocus
PUBLIC a2d0 0 XIGetFocus
PUBLIC a928 0 XIGrabButton
PUBLIC a990 0 XIGrabKeycode
PUBLIC a9e8 0 XIGrabEnter
PUBLIC aa40 0 XIGrabFocusIn
PUBLIC aa90 0 XIGrabTouchBegin
PUBLIC ab68 0 XIUngrabButton
PUBLIC ab88 0 XIUngrabKeycode
PUBLIC aba8 0 XIUngrabEnter
PUBLIC abc0 0 XIUngrabFocusIn
PUBLIC abd8 0 XIUngrabTouchBegin
PUBLIC ac90 0 XIListProperties
PUBLIC ade8 0 XIDeleteProperty
PUBLIC aea8 0 XIChangeProperty
PUBLIC b1a8 0 XIGetProperty
PUBLIC b450 0 XISelectEvents
PUBLIC b7b8 0 XIGetSelectedEvents
PUBLIC ba80 0 XISetClientPointer
PUBLIC bb48 0 XIWarpPointer
PUBLIC bc68 0 XIChangeHierarchy
PUBLIC bf80 0 XIDefineCursor
PUBLIC c048 0 XIUndefineCursor
PUBLIC c050 0 XIQueryPointer
PUBLIC c280 0 XIBarrierReleasePointers
PUBLIC c390 0 XIBarrierReleasePointer
STACK CFI INIT 2268 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2298 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 22dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22e4 x19: .cfa -16 + ^
STACK CFI 231c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2328 d0 .cfa: sp 0 + .ra: x30
STACK CFI 232c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2334 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 233c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2348 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 23f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 23f8 33c .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2404 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2414 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 241c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2428 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2434 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2564 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2738 41c .cfa: sp 0 + .ra: x30
STACK CFI 273c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2744 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2750 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2758 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2764 x25: .cfa -96 + ^
STACK CFI 29dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 29e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2b58 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2b64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2b70 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2b78 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2b84 x25: .cfa -48 + ^
STACK CFI 2d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2d58 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2f40 108 .cfa: sp 0 + .ra: x30
STACK CFI 2f44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2f4c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2f5c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2f64 x23: .cfa -64 + ^
STACK CFI 3040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3044 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3048 134 .cfa: sp 0 + .ra: x30
STACK CFI 304c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3054 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 305c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3068 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3074 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 315c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3160 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3180 120 .cfa: sp 0 + .ra: x30
STACK CFI 3184 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 318c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 319c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 31a4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 31b0 x25: .cfa -64 + ^
STACK CFI 3298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 329c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 32a0 fc .cfa: sp 0 + .ra: x30
STACK CFI 32a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 32ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 32cc x25: .cfa -16 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 33a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 33a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3468 c4 .cfa: sp 0 + .ra: x30
STACK CFI 346c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3474 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 347c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 351c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3530 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3534 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 353c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3548 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3554 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 355c x25: .cfa -16 + ^
STACK CFI 360c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 3610 6b0 .cfa: sp 0 + .ra: x30
STACK CFI 3614 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 361c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3628 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3648 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 36b8 x23: x23 x24: x24
STACK CFI 36c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 37c4 x23: x23 x24: x24
STACK CFI 37c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 37ec x23: x23 x24: x24
STACK CFI 37f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 383c x23: x23 x24: x24
STACK CFI 3840 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 38f8 x23: x23 x24: x24
STACK CFI 38fc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 392c x23: x23 x24: x24
STACK CFI 393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3940 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3a10 x23: x23 x24: x24
STACK CFI 3a14 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3c88 x23: x23 x24: x24
STACK CFI 3c8c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 3cc0 17c .cfa: sp 0 + .ra: x30
STACK CFI 3cc4 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 3ccc x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 3cdc x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 3ce4 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 3cf0 x25: .cfa -320 + ^
STACK CFI 3dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3dd8 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x29: .cfa -384 + ^
STACK CFI INIT 3e40 140 .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e4c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3e5c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3e64 x23: .cfa -64 + ^
STACK CFI 3f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3f4c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3f80 35c .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3f8c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3f9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3fa4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 409c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 42e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42e8 2ac .cfa: sp 0 + .ra: x30
STACK CFI 42ec .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 42f4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4300 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 430c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4314 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 4320 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4478 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4598 430 .cfa: sp 0 + .ra: x30
STACK CFI 459c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 45a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 45b4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 45bc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 47a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 47a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 47e8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4804 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 498c x25: x25 x26: x26
STACK CFI 4990 x27: x27 x28: x28
STACK CFI 4994 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4998 x25: x25 x26: x26
STACK CFI 49a8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 49ac x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 49b0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 49c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49d0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 49d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 49dc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 49ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 49f4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4a00 x25: .cfa -64 + ^
STACK CFI 4b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4b3c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4b90 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 4b94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4b9c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4bac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c98 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 4cb0 x23: .cfa -64 + ^
STACK CFI 4cf0 x23: x23
STACK CFI 4d2c x23: .cfa -64 + ^
STACK CFI 4d34 x23: x23
STACK CFI 4d38 x23: .cfa -64 + ^
STACK CFI INIT 4d48 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 4d4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4d54 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4d64 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4d6c x23: .cfa -64 + ^
STACK CFI 4eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4eb0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4f00 f0 .cfa: sp 0 + .ra: x30
STACK CFI 4f04 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4f0c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4f18 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4f20 x23: .cfa -64 + ^
STACK CFI 4fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4fd8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4ff0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4ff4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ffc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5004 x21: .cfa -16 + ^
STACK CFI 5088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 508c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 50a8 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 50ac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 50b4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 50c4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 50d0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 50dc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 50e4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5328 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5390 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5398 170 .cfa: sp 0 + .ra: x30
STACK CFI 539c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 53a4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 53b4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 53c0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 53c8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 53d4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5504 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5508 140 .cfa: sp 0 + .ra: x30
STACK CFI 550c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5514 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5520 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 552c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5538 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5544 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 5648 14c .cfa: sp 0 + .ra: x30
STACK CFI 564c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5654 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5660 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 566c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5678 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5684 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5778 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5798 130 .cfa: sp 0 + .ra: x30
STACK CFI 579c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 57a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 57b4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 57bc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 57c8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 58c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 58c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 58c8 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 58cc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 58d4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 58e0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 58e8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 58f4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5900 x27: .cfa -64 + ^
STACK CFI 5a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5a28 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5b68 4dc .cfa: sp 0 + .ra: x30
STACK CFI 5b6c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5b74 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5b84 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5b98 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5c10 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5c68 x25: x25 x26: x26
STACK CFI 5c6c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5ca8 x25: x25 x26: x26
STACK CFI 5cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5cd4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 5cd8 x25: x25 x26: x26
STACK CFI 5cdc x27: x27 x28: x28
STACK CFI 5ce8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5cec x25: x25 x26: x26
STACK CFI 5d1c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5d24 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5df0 x27: x27 x28: x28
STACK CFI 5df4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5f3c x27: x27 x28: x28
STACK CFI 5f40 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5fec x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5ff0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5ff4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6028 x25: x25 x26: x26
STACK CFI 602c x27: x27 x28: x28
STACK CFI 6030 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6034 x27: x27 x28: x28
STACK CFI 6040 x25: x25 x26: x26
STACK CFI INIT 6048 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6058 158 .cfa: sp 0 + .ra: x30
STACK CFI 605c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6064 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6074 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 607c x23: .cfa -64 + ^
STACK CFI 6168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 616c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 61b0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 61b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 61bc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 61c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 61d0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 625c x25: .cfa -64 + ^
STACK CFI 62e8 x25: x25
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6314 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 6318 x25: x25
STACK CFI 6354 x25: .cfa -64 + ^
STACK CFI INIT 6380 374 .cfa: sp 0 + .ra: x30
STACK CFI 6384 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 638c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6398 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 63a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 63f4 x25: .cfa -64 + ^
STACK CFI 6470 x25: x25
STACK CFI 6498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 649c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 64c8 x25: x25
STACK CFI 64cc x25: .cfa -64 + ^
STACK CFI 6598 x25: x25
STACK CFI 65a0 x25: .cfa -64 + ^
STACK CFI 66ec x25: x25
STACK CFI 66f0 x25: .cfa -64 + ^
STACK CFI INIT 66f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6700 ec .cfa: sp 0 + .ra: x30
STACK CFI 6704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 670c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6714 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6720 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 67e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 67f0 16c .cfa: sp 0 + .ra: x30
STACK CFI 67f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 67fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 680c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6814 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6934 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6960 160 .cfa: sp 0 + .ra: x30
STACK CFI 6964 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 696c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 697c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6984 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6990 x25: .cfa -64 + ^
STACK CFI 6aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6aa8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6ac0 134 .cfa: sp 0 + .ra: x30
STACK CFI 6ac4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6acc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6ad4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6ae4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6aec x25: .cfa -64 + ^
STACK CFI 6bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6bf0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6bf8 10c .cfa: sp 0 + .ra: x30
STACK CFI 6bfc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c04 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6c14 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6c1c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6d00 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6d08 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 6d0c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6d14 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6d24 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6d2c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6d34 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 6d40 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6e14 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6ed0 dc .cfa: sp 0 + .ra: x30
STACK CFI 6ed4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6edc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ee8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6ef4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6efc x25: .cfa -16 + ^
STACK CFI 6fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 6fb0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6fb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6fbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fcc x23: .cfa -16 + ^
STACK CFI 7074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7078 f4 .cfa: sp 0 + .ra: x30
STACK CFI 707c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7084 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 708c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7098 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 70a4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 7170 f4 .cfa: sp 0 + .ra: x30
STACK CFI 7174 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 717c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7184 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7190 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 719c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 7268 d8 .cfa: sp 0 + .ra: x30
STACK CFI 726c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 7274 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 72b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 72bc .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI 72c0 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 72e4 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 732c x21: x21 x22: x22
STACK CFI 7330 x23: x23 x24: x24
STACK CFI 7338 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 733c x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI INIT 7340 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7380 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 738c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 739c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 73ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73f0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7428 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7504 x23: x23 x24: x24
STACK CFI 7508 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 750c x23: x23 x24: x24
STACK CFI 7510 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7558 x23: x23 x24: x24
STACK CFI 755c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 7560 790 .cfa: sp 0 + .ra: x30
STACK CFI 7564 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 756c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7574 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 765c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7660 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 76ec x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7738 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 774c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7850 x23: x23 x24: x24
STACK CFI 7878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 787c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7880 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7968 x23: x23 x24: x24
STACK CFI 7970 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 79d0 x23: x23 x24: x24
STACK CFI 7a20 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7aa8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c18 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7ca8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7cac x23: x23 x24: x24
STACK CFI 7cb0 x25: x25 x26: x26
STACK CFI 7cb4 x27: x27 x28: x28
STACK CFI 7cb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7cd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7cd4 x23: x23 x24: x24
STACK CFI 7cd8 x25: x25 x26: x26
STACK CFI 7cdc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7ce0 x23: x23 x24: x24
STACK CFI 7ce4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ce8 x23: x23 x24: x24
STACK CFI 7cec x25: x25 x26: x26
STACK CFI INIT 7cf0 870 .cfa: sp 0 + .ra: x30
STACK CFI 7cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7cfc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7d08 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7d10 x25: .cfa -16 + ^
STACK CFI 7dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7dd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7e78 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7fc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 80dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 80e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8560 8c .cfa: sp 0 + .ra: x30
STACK CFI 8564 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 856c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8574 x21: .cfa -16 + ^
STACK CFI 85cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 85d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 85e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 85f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 85f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85fc x19: .cfa -16 + ^
STACK CFI 8618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8620 30 .cfa: sp 0 + .ra: x30
STACK CFI 8624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 862c x19: .cfa -16 + ^
STACK CFI 864c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8650 30 .cfa: sp 0 + .ra: x30
STACK CFI 8654 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 865c x19: .cfa -16 + ^
STACK CFI 867c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8680 30 .cfa: sp 0 + .ra: x30
STACK CFI 8684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 868c x19: .cfa -16 + ^
STACK CFI 86ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 86b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86bc x19: .cfa -16 + ^
STACK CFI 86dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86e0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8750 128 .cfa: sp 0 + .ra: x30
STACK CFI 8754 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 875c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8768 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 879c x19: x19 x20: x20
STACK CFI 87a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 87a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 87ac x19: x19 x20: x20
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 87e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 87e8 x23: .cfa -16 + ^
STACK CFI 8814 x23: x23
STACK CFI 8834 x19: x19 x20: x20
STACK CFI 8838 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8840 x19: x19 x20: x20
STACK CFI 8844 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 8860 x19: x19 x20: x20
STACK CFI 8864 x23: x23
STACK CFI 8868 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 8870 x19: x19 x20: x20
STACK CFI 8874 x23: x23
STACK CFI INIT 8878 20 .cfa: sp 0 + .ra: x30
STACK CFI 887c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 888c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8898 110 .cfa: sp 0 + .ra: x30
STACK CFI 88c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 892c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8960 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8998 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 89a8 330 .cfa: sp 0 + .ra: x30
STACK CFI 89ac .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 89b4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 89c0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 89c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8a04 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8a0c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 8a10 v8: .cfa -32 + ^
STACK CFI 8b34 x21: x21 x22: x22
STACK CFI 8b38 x25: x25 x26: x26
STACK CFI 8b3c v8: v8
STACK CFI 8b44 x19: x19 x20: x20
STACK CFI 8b54 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8b58 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8cc4 v8: v8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 8cd0 x19: x19 x20: x20
STACK CFI INIT 8cd8 a68 .cfa: sp 0 + .ra: x30
STACK CFI 8cdc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8ce4 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 8cf0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 8cf8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8d00 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 8d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8d6c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 9238 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 93fc x27: x27 x28: x28
STACK CFI 96e8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 96f8 x27: x27 x28: x28
STACK CFI 9700 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 971c x27: x27 x28: x28
STACK CFI 9730 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 9734 x27: x27 x28: x28
STACK CFI 9738 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 9740 12c .cfa: sp 0 + .ra: x30
STACK CFI 9744 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 974c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9754 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9760 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 976c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9838 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9870 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9880 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9884 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 988c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9894 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 98a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 991c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9938 294 .cfa: sp 0 + .ra: x30
STACK CFI 993c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9944 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9950 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9958 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9964 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 996c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 9a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9a34 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9bd0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9bdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9be4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9bec x23: .cfa -16 + ^
STACK CFI 9c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9c98 164 .cfa: sp 0 + .ra: x30
STACK CFI 9c9c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9ca4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9cb4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9cbc x23: .cfa -64 + ^
STACK CFI 9d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9d9c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9e00 5c .cfa: sp 0 + .ra: x30
STACK CFI 9e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9e18 x21: .cfa -16 + ^
STACK CFI 9e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9e60 358 .cfa: sp 0 + .ra: x30
STACK CFI 9e64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 9e74 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 9e7c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 9e94 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 9f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 9f9c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI a030 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a03c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a158 x23: x23 x24: x24
STACK CFI a15c x25: x25 x26: x26
STACK CFI a160 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a164 x23: x23 x24: x24
STACK CFI a168 x25: x25 x26: x26
STACK CFI a1a0 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a1ac x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a1b0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a1b4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT a1b8 4c .cfa: sp 0 + .ra: x30
STACK CFI a1bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a1c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a208 c8 .cfa: sp 0 + .ra: x30
STACK CFI a20c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a214 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a21c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a228 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a2cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT a2d0 138 .cfa: sp 0 + .ra: x30
STACK CFI a2d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a2dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a2ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a2f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a3dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a3e0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT a408 1b8 .cfa: sp 0 + .ra: x30
STACK CFI a40c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a414 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a424 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a430 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a438 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI a444 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a56c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a570 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT a5c0 364 .cfa: sp 0 + .ra: x30
STACK CFI a5c4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a5cc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a5e0 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a5ec x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a5f8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI a6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a6c0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT a928 64 .cfa: sp 0 + .ra: x30
STACK CFI a92c .cfa: sp 64 +
STACK CFI a93c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a990 58 .cfa: sp 0 + .ra: x30
STACK CFI a994 .cfa: sp 64 +
STACK CFI a9a0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a9e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a9e8 54 .cfa: sp 0 + .ra: x30
STACK CFI a9ec .cfa: sp 64 +
STACK CFI a9f8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT aa40 50 .cfa: sp 0 + .ra: x30
STACK CFI aa44 .cfa: sp 64 +
STACK CFI aa50 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT aa90 d8 .cfa: sp 0 + .ra: x30
STACK CFI aa94 .cfa: sp 128 +
STACK CFI aa98 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aaa0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aaac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI aab8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aac4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ab64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT ab68 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab88 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT aba8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT abc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT abd8 b4 .cfa: sp 0 + .ra: x30
STACK CFI abdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI abe4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI abec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI abf8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI ac74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ac78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ac88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT ac90 154 .cfa: sp 0 + .ra: x30
STACK CFI ac94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ac9c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI acac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI acb4 x23: .cfa -64 + ^
STACK CFI ad9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ada0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT ade8 c0 .cfa: sp 0 + .ra: x30
STACK CFI adec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI adf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI adfc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ae94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ae98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT aea8 300 .cfa: sp 0 + .ra: x30
STACK CFI aeac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI aeb4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI aec4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI aed0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI aed8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI aee4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b058 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT b1a8 2a8 .cfa: sp 0 + .ra: x30
STACK CFI b1ac .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI b1b4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI b1c0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI b1cc x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI b1d4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI b1e0 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI b3f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b3fc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT b450 364 .cfa: sp 0 + .ra: x30
STACK CFI b454 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b45c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b46c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b474 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b4c4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b4d0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b514 x27: x27 x28: x28
STACK CFI b530 x23: x23 x24: x24
STACK CFI b568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI b56c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI b700 x27: x27 x28: x28
STACK CFI b718 x23: x23 x24: x24
STACK CFI b71c x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b760 x27: x27 x28: x28
STACK CFI b7a8 x23: x23 x24: x24
STACK CFI b7ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b7b0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT b7b8 2c4 .cfa: sp 0 + .ra: x30
STACK CFI b7bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI b7c4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI b7d0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI b7dc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b7f0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI b83c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI b948 x27: x27 x28: x28
STACK CFI b984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b988 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI b9a4 x27: x27 x28: x28
STACK CFI b9a8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI b9b8 x27: x27 x28: x28
STACK CFI b9c0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ba5c x27: x27 x28: x28
STACK CFI ba60 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT ba80 c4 .cfa: sp 0 + .ra: x30
STACK CFI ba84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ba9c x23: .cfa -16 + ^
STACK CFI bb40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT bb48 11c .cfa: sp 0 + .ra: x30
STACK CFI bb4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bb54 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI bb60 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI bb6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bb74 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bb80 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bb8c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bc60 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT bc68 314 .cfa: sp 0 + .ra: x30
STACK CFI bc6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bc74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bc84 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bcc4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bce0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI be10 x25: x25 x26: x26
STACK CFI be4c x27: x27 x28: x28
STACK CFI be60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI be64 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI bed4 x25: x25 x26: x26
STACK CFI bed8 x27: x27 x28: x28
STACK CFI bedc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bee0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI bf40 x25: x25 x26: x26
STACK CFI bf54 x27: x27 x28: x28
STACK CFI bf58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bf5c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI bf6c x25: x25 x26: x26
STACK CFI bf70 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bf78 x25: x25 x26: x26
STACK CFI INIT bf80 c8 .cfa: sp 0 + .ra: x30
STACK CFI bf84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bf8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI bf94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bfa0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI c044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT c048 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c050 22c .cfa: sp 0 + .ra: x30
STACK CFI c054 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI c05c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI c068 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI c070 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI c080 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI c088 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI c178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c17c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT c280 10c .cfa: sp 0 + .ra: x30
STACK CFI c284 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c28c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c298 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c2a8 x23: .cfa -16 + ^
STACK CFI c370 x23: x23
STACK CFI c374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c378 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c37c x23: x23
STACK CFI c388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT c390 60 .cfa: sp 0 + .ra: x30
STACK CFI c394 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c3a4 x19: .cfa -48 + ^
STACK CFI c3e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c3ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
