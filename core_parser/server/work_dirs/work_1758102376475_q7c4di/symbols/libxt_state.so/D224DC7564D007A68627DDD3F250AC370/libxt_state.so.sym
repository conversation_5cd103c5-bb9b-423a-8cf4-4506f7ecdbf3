MODULE Linux arm64 D224DC7564D007A68627DDD3F250AC370 libxt_state.so
INFO CODE_ID 75DC24D2D064A6078627DDD3F250AC37869C6D7A
PUBLIC 17e0 0 libxt_conntrack_init
STACK CFI INIT 1808 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1838 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1878 48 .cfa: sp 0 + .ra: x30
STACK CFI 187c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1884 x19: .cfa -16 + ^
STACK CFI 18bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18c8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18e8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 18ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1924 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 195c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1968 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 19e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a48 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a58 114 .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a6c x19: .cfa -16 + ^
STACK CFI 1a94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ac0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1acc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b70 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c48 cc .cfa: sp 0 + .ra: x30
STACK CFI 1c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c5c x19: .cfa -16 + ^
STACK CFI 1c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ca8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d18 30 .cfa: sp 0 + .ra: x30
STACK CFI 1d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d28 x19: .cfa -16 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d48 30 .cfa: sp 0 + .ra: x30
STACK CFI 1d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d58 x19: .cfa -16 + ^
STACK CFI 1d74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d78 144 .cfa: sp 0 + .ra: x30
STACK CFI 1d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1ec0 34 .cfa: sp 0 + .ra: x30
STACK CFI 1ed0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1ef8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f30 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 1f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f54 x23: .cfa -16 + ^
STACK CFI 2210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2214 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2228 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2268 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23f8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2418 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2438 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2458 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2478 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2498 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24b8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 24fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2508 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 25a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25a8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 25ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25bc x21: .cfa -16 + ^
STACK CFI 2628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 262c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2670 3c .cfa: sp 0 + .ra: x30
STACK CFI 2674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 267c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 26b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2700 4c .cfa: sp 0 + .ra: x30
STACK CFI 2704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 270c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2750 178 .cfa: sp 0 + .ra: x30
STACK CFI 2754 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 275c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2774 x21: .cfa -16 + ^
STACK CFI 279c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 27d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 283c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 28c8 114 .cfa: sp 0 + .ra: x30
STACK CFI 28cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28e0 x21: .cfa -16 + ^
STACK CFI 292c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2930 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2944 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 29a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29e0 178 .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a04 x21: .cfa -16 + ^
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2acc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2b58 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b70 x21: .cfa -16 + ^
STACK CFI 2bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2c40 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 2c44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2cb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2de0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2de8 x23: .cfa -16 + ^
STACK CFI 2e5c x23: x23
STACK CFI 2e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2e98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2ea0 x23: .cfa -16 + ^
STACK CFI 2f2c x23: x23
STACK CFI 2f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2f38 x23: x23
STACK CFI 2f3c x23: .cfa -16 + ^
STACK CFI 2fb8 x23: x23
STACK CFI 2fd8 x23: .cfa -16 + ^
STACK CFI INIT 2fe8 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2fec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ff8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3018 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3050 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3088 5e0 .cfa: sp 0 + .ra: x30
STACK CFI 308c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3098 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 34ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 34f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 35b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3668 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3678 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3688 84 .cfa: sp 0 + .ra: x30
STACK CFI 368c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3698 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3708 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3710 94 .cfa: sp 0 + .ra: x30
STACK CFI 3714 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 371c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3740 x21: .cfa -192 + ^
STACK CFI 379c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 37a0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 37a8 84 .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 37b8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3828 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3830 94 .cfa: sp 0 + .ra: x30
STACK CFI 3834 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 383c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3860 x21: .cfa -192 + ^
STACK CFI 38bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 38c0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 38c8 548 .cfa: sp 0 + .ra: x30
STACK CFI 38cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 38d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 38dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 395c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3a4c x23: .cfa -16 + ^
STACK CFI 3ad0 x23: x23
STACK CFI 3b34 x23: .cfa -16 + ^
STACK CFI 3bb4 x23: x23
STACK CFI 3ce0 x23: .cfa -16 + ^
STACK CFI 3ce4 x23: x23
STACK CFI 3d6c x23: .cfa -16 + ^
STACK CFI 3d74 x23: x23
STACK CFI 3d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3da8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e18 10c .cfa: sp 0 + .ra: x30
STACK CFI 3e1c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3e28 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3e34 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f00 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3f28 12c .cfa: sp 0 + .ra: x30
STACK CFI 3f2c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3f38 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3f40 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 402c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4030 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4058 a4 .cfa: sp 0 + .ra: x30
STACK CFI 405c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4064 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 40d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4100 2dc .cfa: sp 0 + .ra: x30
STACK CFI 4104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 410c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4118 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4298 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 43bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 43e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e0 10 .cfa: sp 0 + .ra: x30
