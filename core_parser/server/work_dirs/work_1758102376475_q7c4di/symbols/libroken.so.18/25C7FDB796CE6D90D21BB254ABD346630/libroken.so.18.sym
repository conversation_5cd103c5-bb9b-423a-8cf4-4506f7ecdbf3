MODULE Linux arm64 25C7FDB796CE6D90D21BB254ABD346630 libroken.so.18
INFO CODE_ID B7FDC725CE96906DD21BB254ABD34663AB42D35E
PUBLIC 50a8 0 rk_base64_encode
PUBLIC 51d0 0 base64_encode
PUBLIC 51d8 0 rk_base64_decode
PUBLIC 5328 0 base64_decode
PUBLIC 5330 0 rk_bswap64
PUBLIC 5338 0 rk_bswap32
PUBLIC 5340 0 rk_bswap16
PUBLIC 5348 0 roken_vconcat
PUBLIC 5420 0 roken_concat
PUBLIC 54c8 0 roken_vmconcat
PUBLIC 5610 0 roken_mconcat
PUBLIC 56b8 0 rk_cloexec
PUBLIC 5700 0 rk_cloexec_file
PUBLIC 5718 0 rk_cloexec_dir
PUBLIC 5730 0 rk_cloexec_socket
PUBLIC 5738 0 ct_memcmp
PUBLIC 5790 0 roken_detach_prep
PUBLIC 5950 0 roken_detach_finish
PUBLIC 5b00 0 rk_dumpdata
PUBLIC 5b60 0 rk_undumpdata
PUBLIC 5eb0 0 rk_read_environment
PUBLIC 5f38 0 rk_free_environment
PUBLIC 5f78 0 rk_eread
PUBLIC 5fa0 0 rk_esetenv
PUBLIC 5fd8 0 rk_ewrite
PUBLIC 6000 0 roken_getaddrinfo_hostspec2
PUBLIC 6180 0 roken_getaddrinfo_hostspec
PUBLIC 6190 0 get_default_username
PUBLIC 6230 0 get_window_size
PUBLIC 64f0 0 arg_printusage_i18n
PUBLIC 71b0 0 arg_printusage
PUBLIC 71d0 0 getarg
PUBLIC 78b8 0 free_getarg_strings
PUBLIC 78c0 0 getnameinfo_verified
PUBLIC 7a90 0 rk_getprogname
PUBLIC 7b08 0 rk_hex_encode
PUBLIC 7bb8 0 rk_hex_decode
PUBLIC 7c60 0 rk_hostent_find_fqdn
PUBLIC 7ce0 0 issuid
PUBLIC 7e00 0 k_getpwnam
PUBLIC 7e48 0 k_getpwuid
PUBLIC 7e88 0 mini_inetd_addrinfo
PUBLIC 81a8 0 mini_inetd
PUBLIC 8288 0 rk_mkdir
PUBLIC 8290 0 net_read
PUBLIC 8328 0 net_write
PUBLIC 83b8 0 parse_bytes
PUBLIC 83c8 0 unparse_bytes
PUBLIC 83e0 0 unparse_bytes_short
PUBLIC 83f8 0 parse_time
PUBLIC 8408 0 unparse_time
PUBLIC 8430 0 unparse_time_approx
PUBLIC 8458 0 print_time_table
PUBLIC 8958 0 parse_units
PUBLIC 8970 0 parse_flags
PUBLIC 8988 0 unparse_units
PUBLIC 89a8 0 unparse_units_approx
PUBLIC 89c8 0 print_units_table
PUBLIC 8b40 0 unparse_flags
PUBLIC 8b60 0 print_flags_table
PUBLIC 8be0 0 rk_random_init
PUBLIC 8bf8 0 rk_realloc
PUBLIC 91d0 0 rk_dns_string_to_type
PUBLIC 9228 0 rk_dns_type_to_string
PUBLIC 9258 0 rk_dns_free_data
PUBLIC 9710 0 rk_dns_lookup
PUBLIC 9790 0 rk_dns_srv_order
PUBLIC 9d30 0 roken_gethostby_setup
PUBLIC 9ed8 0 roken_gethostbyname
PUBLIC 9f10 0 roken_gethostbyaddr
PUBLIC a078 0 rtbl_create
PUBLIC a088 0 rtbl_set_flags
PUBLIC a090 0 rtbl_get_flags
PUBLIC a098 0 rtbl_destroy
PUBLIC a150 0 rtbl_add_column_by_id
PUBLIC a200 0 rtbl_add_column
PUBLIC a210 0 rtbl_new_row
PUBLIC a320 0 rtbl_set_prefix
PUBLIC a368 0 rtbl_set_separator
PUBLIC a3b0 0 rtbl_set_column_prefix
PUBLIC a410 0 rtbl_set_column_affix_by_id
PUBLIC a4f8 0 rtbl_add_column_entry_by_id
PUBLIC a540 0 rtbl_add_column_entryv_by_id
PUBLIC a638 0 rtbl_add_column_entry
PUBLIC a688 0 rtbl_add_column_entryv
PUBLIC ac20 0 rtbl_format
PUBLIC ac70 0 rk_setprogname
PUBLIC ac78 0 signal
PUBLIC ad18 0 rk_wait_for_process_timed
PUBLIC ae88 0 rk_wait_for_process
PUBLIC ae98 0 rk_pipe_execv
PUBLIC b1e0 0 rk_simple_execvp_timed
PUBLIC b278 0 rk_simple_execvp
PUBLIC b288 0 rk_simple_execve_timed
PUBLIC b328 0 rk_simple_execve
PUBLIC b338 0 rk_simple_execlp
PUBLIC b410 0 rk_simple_execle
PUBLIC b9c8 0 rk_vasnprintf
PUBLIC c760 0 rk_asnprintf
PUBLIC c808 0 rk_socket_set_any
PUBLIC c860 0 rk_socket_set_address_and_port
PUBLIC c8c8 0 rk_socket_addr_size
PUBLIC c8f0 0 rk_socket_sockaddr_size
PUBLIC c918 0 rk_socket_get_address
PUBLIC c940 0 rk_socket_get_port
PUBLIC c968 0 rk_socket_set_port
PUBLIC c9a0 0 rk_socket_set_portrange
PUBLIC c9a8 0 rk_socket_set_debug
PUBLIC ca08 0 rk_socket_set_tos
PUBLIC ca30 0 rk_socket_set_nonblocking
PUBLIC ca88 0 rk_socket_set_reuseaddr
PUBLIC cab0 0 rk_socket_set_ipv6only
PUBLIC cae0 0 rk_socket
PUBLIC cc40 0 rk_vstrcollect
PUBLIC cc58 0 rk_strcollect
PUBLIC cd20 0 rk_strerror_r
PUBLIC cd78 0 rk_strpoolfree
PUBLIC cda8 0 rk_strpoolprintf
PUBLIC cf20 0 rk_strpoolcollect
PUBLIC cf58 0 rk_timevalfix
PUBLIC cfa8 0 rk_timevaladd
PUBLIC cfc0 0 rk_timevalsub
PUBLIC cfd8 0 tm2time
PUBLIC d060 0 rk_unvis
PUBLIC d358 0 rk_strunvis
PUBLIC d440 0 unix_verify_user
PUBLIC d7e8 0 rk_svis
PUBLIC d8e0 0 rk_strsvis
PUBLIC da10 0 rk_strsvisx
PUBLIC db68 0 rk_vis
PUBLIC dc40 0 rk_strvis
PUBLIC dcf0 0 rk_strvisx
PUBLIC ddb0 0 rk_warnerr
PUBLIC dea8 0 rk_pid_file_write
PUBLIC dfb8 0 rk_pid_file_delete
PUBLIC e038 0 rk_pidfile
PUBLIC e098 0 rk_xfree
PUBLIC efb0 0 rk_glob
PUBLIC f0c8 0 rk_globfree
PUBLIC f138 0 cgetset
PUBLIC f288 0 cgetcap
PUBLIC f318 0 cgetmatch
PUBLIC f980 0 rk_cgetent
PUBLIC f9e0 0 cgetclose
PUBLIC fa18 0 rk_cgetstr
PUBLIC fd28 0 cgetustr
PUBLIC fe58 0 cgetnum
PUBLIC ff28 0 rk_copyhostent
PUBLIC 100f8 0 rk_closefrom
PUBLIC 10150 0 rk_ecalloc
PUBLIC 10190 0 rk_emalloc
PUBLIC 101d0 0 rk_erealloc
PUBLIC 10210 0 rk_estrdup
PUBLIC 10238 0 rk_freehostent
PUBLIC 102b0 0 rk_getipnodebyaddr
PUBLIC 10338 0 rk_getipnodebyname
PUBLIC 103c0 0 rk_memset_s
PUBLIC 103f0 0 rk_strlcat
PUBLIC 10460 0 rk_strlcpy
PUBLIC 104e0 0 rk_strlwr
PUBLIC 10528 0 rk_strsep_copy
PUBLIC 105b8 0 rk_strupr
STACK CFI INIT 4fe8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5018 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5058 48 .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5064 x19: .cfa -16 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 50a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50a8 128 .cfa: sp 0 + .ra: x30
STACK CFI 50ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50c0 x21: .cfa -16 + ^
STACK CFI 51b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 51d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51d8 150 .cfa: sp 0 + .ra: x30
STACK CFI 51dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5204 x23: .cfa -16 + ^
STACK CFI 5290 x23: x23
STACK CFI 52a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5304 x23: x23
STACK CFI 5308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 530c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5314 x23: .cfa -16 + ^
STACK CFI 5324 x23: x23
STACK CFI INIT 5328 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5330 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5338 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5348 d4 .cfa: sp 0 + .ra: x30
STACK CFI 534c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5354 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 535c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5364 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 53f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5420 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5424 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5434 x19: .cfa -272 + ^
STACK CFI 54bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 54c0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 54c8 144 .cfa: sp 0 + .ra: x30
STACK CFI 54cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 54d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 54e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 54f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5510 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 55a8 x27: x27 x28: x28
STACK CFI 55c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 55c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 55fc x27: x27 x28: x28
STACK CFI 5600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5604 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5610 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5614 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5624 x19: .cfa -272 + ^
STACK CFI 56ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56b0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 56b8 44 .cfa: sp 0 + .ra: x30
STACK CFI 56bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56c8 x19: .cfa -16 + ^
STACK CFI 56ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 56f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5700 14 .cfa: sp 0 + .ra: x30
STACK CFI 5704 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5710 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5718 14 .cfa: sp 0 + .ra: x30
STACK CFI 571c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5730 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5738 58 .cfa: sp 0 + .ra: x30
STACK CFI 573c .cfa: sp 16 +
STACK CFI 5780 .cfa: sp 0 +
STACK CFI 5784 .cfa: sp 16 +
STACK CFI 578c .cfa: sp 0 +
STACK CFI INIT 5790 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 579c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 57ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 57c4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5864 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5950 1ac .cfa: sp 0 + .ra: x30
STACK CFI 5954 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 595c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 596c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5b00 5c .cfa: sp 0 + .ra: x30
STACK CFI 5b04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b18 x21: .cfa -16 + ^
STACK CFI 5b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5b60 124 .cfa: sp 0 + .ra: x30
STACK CFI 5b64 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 5b6c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 5b78 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 5c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c04 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 5c08 x23: .cfa -160 + ^
STACK CFI 5c40 x23: x23
STACK CFI 5c5c x23: .cfa -160 + ^
STACK CFI 5c68 x23: x23
STACK CFI 5c74 x23: .cfa -160 + ^
STACK CFI 5c80 x23: x23
STACK CFI INIT 5c88 224 .cfa: sp 0 + .ra: x30
STACK CFI 5c90 .cfa: sp 8320 +
STACK CFI 5c94 .ra: .cfa -8312 + ^ x29: .cfa -8320 + ^
STACK CFI 5ca0 x21: .cfa -8288 + ^ x22: .cfa -8280 + ^
STACK CFI 5cac x23: .cfa -8272 + ^ x24: .cfa -8264 + ^
STACK CFI 5cd0 x19: .cfa -8304 + ^ x20: .cfa -8296 + ^ x25: .cfa -8256 + ^ x26: .cfa -8248 + ^ x27: .cfa -8240 + ^ x28: .cfa -8232 + ^
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5e60 .cfa: sp 8320 + .ra: .cfa -8312 + ^ x19: .cfa -8304 + ^ x20: .cfa -8296 + ^ x21: .cfa -8288 + ^ x22: .cfa -8280 + ^ x23: .cfa -8272 + ^ x24: .cfa -8264 + ^ x25: .cfa -8256 + ^ x26: .cfa -8248 + ^ x27: .cfa -8240 + ^ x28: .cfa -8232 + ^ x29: .cfa -8320 + ^
STACK CFI INIT 5eb0 88 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ebc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ed4 x21: .cfa -32 + ^
STACK CFI 5f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5f38 40 .cfa: sp 0 + .ra: x30
STACK CFI 5f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5f78 28 .cfa: sp 0 + .ra: x30
STACK CFI 5f7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5fa0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fac x19: .cfa -16 + ^
STACK CFI 5fc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5fd8 28 .cfa: sp 0 + .ra: x30
STACK CFI 5fdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ff0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6000 180 .cfa: sp 0 + .ra: x30
STACK CFI 6004 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 600c x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 601c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 6024 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 6044 x25: .cfa -304 + ^
STACK CFI 6168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 616c .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x29: .cfa -368 + ^
STACK CFI INIT 6180 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6190 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 61ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 61f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6230 e0 .cfa: sp 0 + .ra: x30
STACK CFI 6234 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 623c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6248 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 62b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6318 38 .cfa: sp 0 + .ra: x30
STACK CFI 631c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6324 x19: .cfa -16 + ^
STACK CFI 6348 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6350 128 .cfa: sp 0 + .ra: x30
STACK CFI 6354 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 635c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6368 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6374 x23: .cfa -16 + ^
STACK CFI 63d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 63d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6478 78 .cfa: sp 0 + .ra: x30
STACK CFI 647c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6484 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 649c x21: .cfa -16 + ^
STACK CFI 64d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 64d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 64f0 cbc .cfa: sp 0 + .ra: x30
STACK CFI 64f4 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 6504 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 6520 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 6678 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 669c x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 69d8 x25: x25 x26: x26
STACK CFI 69dc x27: x27 x28: x28
STACK CFI 6a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6a84 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x29: .cfa -400 + ^
STACK CFI 6a94 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 6a98 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 6e4c x25: x25 x26: x26
STACK CFI 6e50 x27: x27 x28: x28
STACK CFI 6e54 x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 705c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 70ac x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 70cc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 70d8 x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 71a0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 71a4 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 71a8 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 71b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 71d0 6e8 .cfa: sp 0 + .ra: x30
STACK CFI 71d4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 71dc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 71f4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 7208 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 7214 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 7244 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 72d0 x27: x27 x28: x28
STACK CFI 730c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7310 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 738c x27: x27 x28: x28
STACK CFI 7390 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7624 x27: x27 x28: x28
STACK CFI 7628 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7714 x27: x27 x28: x28
STACK CFI 7718 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7738 x27: x27 x28: x28
STACK CFI 773c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 77a8 x27: x27 x28: x28
STACK CFI 77ac x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 782c x27: x27 x28: x28
STACK CFI 7830 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 7848 x27: x27 x28: x28
STACK CFI 784c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 787c x27: x27 x28: x28
STACK CFI 7884 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 78ac x27: x27 x28: x28
STACK CFI 78b0 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 78b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78c0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 78c4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 78cc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 78e8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 78f4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7900 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 795c x19: x19 x20: x20
STACK CFI 7964 x21: x21 x22: x22
STACK CFI 7968 x25: x25 x26: x26
STACK CFI 798c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7990 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 79c4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 7a34 x19: x19 x20: x20
STACK CFI 7a38 x21: x21 x22: x22
STACK CFI 7a3c x25: x25 x26: x26
STACK CFI 7a40 x27: x27 x28: x28
STACK CFI 7a44 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7a50 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 7a60 x27: x27 x28: x28
STACK CFI 7a68 x19: x19 x20: x20
STACK CFI 7a6c x21: x21 x22: x22
STACK CFI 7a70 x25: x25 x26: x26
STACK CFI 7a7c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7a80 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7a84 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 7a88 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 7a90 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7aa0 64 .cfa: sp 0 + .ra: x30
STACK CFI 7aa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7aac x19: .cfa -16 + ^
STACK CFI 7af0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7b08 b0 .cfa: sp 0 + .ra: x30
STACK CFI 7b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b18 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b8c x19: x19 x20: x20
STACK CFI 7b98 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7bac x19: x19 x20: x20
STACK CFI INIT 7bb8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7bbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7bc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7bd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7bd8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7c40 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7c60 7c .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c70 x21: .cfa -16 + ^
STACK CFI 7c78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7ce0 11c .cfa: sp 0 + .ra: x30
STACK CFI 7ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7cf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7d4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7da4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e00 44 .cfa: sp 0 + .ra: x30
STACK CFI 7e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7e48 40 .cfa: sp 0 + .ra: x30
STACK CFI 7e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e54 x19: .cfa -16 + ^
STACK CFI 7e84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e88 31c .cfa: sp 0 + .ra: x30
STACK CFI 7e8c .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 7e94 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 7e9c x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 7ec0 x19: .cfa -368 + ^ x20: .cfa -360 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^
STACK CFI 80f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 80fc .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x28: .cfa -296 + ^ x29: .cfa -384 + ^
STACK CFI INIT 81a8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 81ac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 81bc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 81d0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 81fc x23: .cfa -112 + ^
STACK CFI 8268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 826c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x29: .cfa -160 + ^
STACK CFI INIT 8288 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8290 98 .cfa: sp 0 + .ra: x30
STACK CFI 8294 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 82a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 82b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 82e8 x21: x21 x22: x22
STACK CFI 82fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 8300 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 831c x21: x21 x22: x22
STACK CFI 8324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 8328 90 .cfa: sp 0 + .ra: x30
STACK CFI 832c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8338 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 8348 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8378 x21: x21 x22: x22
STACK CFI 838c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 8390 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 83ac x21: x21 x22: x22
STACK CFI 83b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 83b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 83c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 83e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 83f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8408 28 .cfa: sp 0 + .ra: x30
STACK CFI 840c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 842c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8430 28 .cfa: sp 0 + .ra: x30
STACK CFI 8434 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8458 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8468 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8470 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8498 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 84a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 84c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84d0 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 84f4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 84fc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 8504 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8554 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 86bc x21: x21 x22: x22
STACK CFI 86f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 86f4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8758 x21: x21 x22: x22
STACK CFI 8764 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 877c x21: x21 x22: x22
STACK CFI 8790 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 8798 60 .cfa: sp 0 + .ra: x30
STACK CFI 879c .cfa: sp 32 +
STACK CFI 87b8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87f8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8828 12c .cfa: sp 0 + .ra: x30
STACK CFI 882c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8834 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 883c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8848 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8854 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8860 x27: .cfa -16 + ^
STACK CFI 88e0 x19: x19 x20: x20
STACK CFI 88ec x25: x25 x26: x26
STACK CFI 88f0 x27: x27
STACK CFI 88f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 88f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 890c x19: x19 x20: x20 x25: x25 x26: x26 x27: x27
STACK CFI 8928 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8930 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 893c x19: x19 x20: x20
STACK CFI 8948 x25: x25 x26: x26
STACK CFI 894c x27: x27
STACK CFI 8950 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8958 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8970 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8988 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c8 174 .cfa: sp 0 + .ra: x30
STACK CFI 89cc .cfa: sp 1136 +
STACK CFI 89d0 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI 89d8 x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI 89e8 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI 89fc x27: .cfa -1056 + ^
STACK CFI 8a0c x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 8a18 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI 8ad8 x19: x19 x20: x20
STACK CFI 8adc x23: x23 x24: x24
STACK CFI 8b08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 8b0c .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x29: .cfa -1136 + ^
STACK CFI 8b30 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 8b34 x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI 8b38 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI INIT 8b40 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b60 7c .cfa: sp 0 + .ra: x30
STACK CFI 8b6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8b80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8b94 x23: .cfa -16 + ^
STACK CFI 8bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8be0 18 .cfa: sp 0 + .ra: x30
STACK CFI 8be4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8bf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8bf8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c08 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c40 38 .cfa: sp 0 + .ra: x30
STACK CFI 8c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c4c x19: .cfa -16 + ^
STACK CFI 8c74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8c78 558 .cfa: sp 0 + .ra: x30
STACK CFI 8c7c .cfa: sp 1184 +
STACK CFI 8c84 .ra: .cfa -1176 + ^ x29: .cfa -1184 + ^
STACK CFI 8c8c x21: .cfa -1152 + ^ x22: .cfa -1144 + ^
STACK CFI 8c98 x23: .cfa -1136 + ^ x24: .cfa -1128 + ^
STACK CFI 8cb8 x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^
STACK CFI 8cf0 x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 8dec x27: x27 x28: x28
STACK CFI 8e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8e20 .cfa: sp 1184 + .ra: .cfa -1176 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^ x29: .cfa -1184 + ^
STACK CFI 90b8 x27: x27 x28: x28
STACK CFI 90bc x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 91c0 x27: x27 x28: x28
STACK CFI 91cc x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI INIT 91d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 91d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9218 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9228 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9258 48 .cfa: sp 0 + .ra: x30
STACK CFI 925c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9264 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 929c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 92a0 470 .cfa: sp 0 + .ra: x30
STACK CFI 92a4 .cfa: sp 1728 +
STACK CFI 92b0 .ra: .cfa -1720 + ^ x29: .cfa -1728 + ^
STACK CFI 92bc x19: .cfa -1712 + ^ x20: .cfa -1704 + ^
STACK CFI 92d0 x21: .cfa -1696 + ^ x22: .cfa -1688 + ^
STACK CFI 92e8 x23: .cfa -1680 + ^ x24: .cfa -1672 + ^ x27: .cfa -1648 + ^ x28: .cfa -1640 + ^
STACK CFI 9308 x25: .cfa -1664 + ^ x26: .cfa -1656 + ^
STACK CFI 95ec x25: x25 x26: x26
STACK CFI 9624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 9628 .cfa: sp 1728 + .ra: .cfa -1720 + ^ x19: .cfa -1712 + ^ x20: .cfa -1704 + ^ x21: .cfa -1696 + ^ x22: .cfa -1688 + ^ x23: .cfa -1680 + ^ x24: .cfa -1672 + ^ x25: .cfa -1664 + ^ x26: .cfa -1656 + ^ x27: .cfa -1648 + ^ x28: .cfa -1640 + ^ x29: .cfa -1728 + ^
STACK CFI 9640 x25: x25 x26: x26
STACK CFI 964c x25: .cfa -1664 + ^ x26: .cfa -1656 + ^
STACK CFI 9704 x25: x25 x26: x26
STACK CFI 970c x25: .cfa -1664 + ^ x26: .cfa -1656 + ^
STACK CFI INIT 9710 7c .cfa: sp 0 + .ra: x30
STACK CFI 9714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 971c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9748 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9768 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9790 248 .cfa: sp 0 + .ra: x30
STACK CFI 9794 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 979c x25: .cfa -16 + ^
STACK CFI 97a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 97d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 97fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9928 x23: x23 x24: x24
STACK CFI 992c x21: x21 x22: x22
STACK CFI 9938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI 993c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 99a0 x21: x21 x22: x22
STACK CFI 99a4 x23: x23 x24: x24
STACK CFI 99ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI 99b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 99d8 268 .cfa: sp 0 + .ra: x30
STACK CFI 99dc .cfa: sp 1152 +
STACK CFI 99e4 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 99ec x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 9a50 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 9a70 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 9aa8 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 9bac x19: x19 x20: x20
STACK CFI 9bb4 x23: x23 x24: x24
STACK CFI 9bb8 x25: x25 x26: x26
STACK CFI 9bbc x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 9bc0 x19: x19 x20: x20
STACK CFI 9bc4 x23: x23 x24: x24
STACK CFI 9bc8 x25: x25 x26: x26
STACK CFI 9bf0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9bf4 .cfa: sp 1152 + .ra: .cfa -1144 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x29: .cfa -1152 + ^
STACK CFI 9c0c x23: x23 x24: x24
STACK CFI 9c10 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 9c28 x19: x19 x20: x20
STACK CFI 9c2c x23: x23 x24: x24
STACK CFI 9c34 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 9c38 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 9c3c x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI INIT 9c40 ac .cfa: sp 0 + .ra: x30
STACK CFI 9c44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9c4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9c58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9cd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9cf0 3c .cfa: sp 0 + .ra: x30
STACK CFI 9cf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9d24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9d30 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 9d34 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9d3c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9d70 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9d80 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9d84 x25: .cfa -64 + ^
STACK CFI 9e10 x23: x23 x24: x24
STACK CFI 9e18 x25: x25
STACK CFI 9e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e68 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 9e78 x23: x23 x24: x24
STACK CFI 9e7c x25: x25
STACK CFI 9e84 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI 9ecc x23: x23 x24: x24 x25: x25
STACK CFI 9ed0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9ed4 x25: .cfa -64 + ^
STACK CFI INIT 9ed8 34 .cfa: sp 0 + .ra: x30
STACK CFI 9edc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ee4 x19: .cfa -16 + ^
STACK CFI 9ef8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9efc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9f10 58 .cfa: sp 0 + .ra: x30
STACK CFI 9f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9f28 x21: .cfa -16 + ^
STACK CFI 9f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9f68 84 .cfa: sp 0 + .ra: x30
STACK CFI 9f6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9f80 x21: .cfa -16 + ^
STACK CFI 9fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9fd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ff0 88 .cfa: sp 0 + .ra: x30
STACK CFI 9ff4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ffc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a008 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a014 x23: .cfa -16 + ^
STACK CFI a040 x21: x21 x22: x22
STACK CFI a044 x23: x23
STACK CFI a050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a054 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a064 x21: x21 x22: x22
STACK CFI a068 x23: x23
STACK CFI a06c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a070 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT a078 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a088 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a090 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a098 b8 .cfa: sp 0 + .ra: x30
STACK CFI a09c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a0a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a0b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a128 x19: x19 x20: x20
STACK CFI a14c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT a150 ac .cfa: sp 0 + .ra: x30
STACK CFI a154 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a15c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a168 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a170 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a1ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a200 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a210 10c .cfa: sp 0 + .ra: x30
STACK CFI a21c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a224 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a230 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a244 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a314 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a320 44 .cfa: sp 0 + .ra: x30
STACK CFI a324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a32c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a368 44 .cfa: sp 0 + .ra: x30
STACK CFI a36c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a374 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a3a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a3b0 60 .cfa: sp 0 + .ra: x30
STACK CFI a3b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a408 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a410 e4 .cfa: sp 0 + .ra: x30
STACK CFI a41c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a424 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a434 x21: .cfa -16 + ^
STACK CFI a4ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a4b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a4cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a4d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a4ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a4f8 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT a540 f8 .cfa: sp 0 + .ra: x30
STACK CFI a544 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI a554 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI a574 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI a628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a62c .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT a638 4c .cfa: sp 0 + .ra: x30
STACK CFI a63c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a644 x19: .cfa -16 + ^
STACK CFI a670 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a674 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a680 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a688 f8 .cfa: sp 0 + .ra: x30
STACK CFI a68c .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI a69c x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI a6bc x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI a770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a774 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI INIT a780 49c .cfa: sp 0 + .ra: x30
STACK CFI a784 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a78c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a7a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a984 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ac08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ac0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT ac20 4c .cfa: sp 0 + .ra: x30
STACK CFI ac24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ac58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ac68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ac70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac78 90 .cfa: sp 0 + .ra: x30
STACK CFI ac7c .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI ac84 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI ac98 x21: .cfa -336 + ^
STACK CFI ad00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ad04 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x29: .cfa -368 + ^
STACK CFI INIT ad08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad18 170 .cfa: sp 0 + .ra: x30
STACK CFI ad1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ad24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ad34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ad50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ad58 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ae10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ae14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT ae88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae98 348 .cfa: sp 0 + .ra: x30
STACK CFI ae9c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI aea8 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI aecc x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI aed4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI b084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b088 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x29: .cfa -288 + ^
STACK CFI INIT b1e0 94 .cfa: sp 0 + .ra: x30
STACK CFI b1e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b1ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b1f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b204 x23: .cfa -16 + ^
STACK CFI b234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b238 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b24c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b250 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b278 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b288 9c .cfa: sp 0 + .ra: x30
STACK CFI b28c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b294 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b2a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b2ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b2e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b2e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI b2f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b2fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT b328 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b338 d4 .cfa: sp 0 + .ra: x30
STACK CFI b33c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI b34c x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI b378 x21: .cfa -256 + ^
STACK CFI b3fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b400 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x29: .cfa -288 + ^
STACK CFI INIT b410 108 .cfa: sp 0 + .ra: x30
STACK CFI b414 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI b424 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI b450 x21: .cfa -256 + ^
STACK CFI b4f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b4f8 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x29: .cfa -288 + ^
STACK CFI INIT b518 400 .cfa: sp 0 + .ra: x30
STACK CFI b51c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI b524 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI b534 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI b554 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI b55c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI b790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b794 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT b918 b0 .cfa: sp 0 + .ra: x30
STACK CFI b91c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b924 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b930 x21: .cfa -16 + ^
STACK CFI b99c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b9a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b9c8 d94 .cfa: sp 0 + .ra: x30
STACK CFI b9cc .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI b9d4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI b9e0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI b9f4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI ba04 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI ba54 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI bc10 x25: x25 x26: x26
STACK CFI bc60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI bc64 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI bf74 x25: x25 x26: x26
STACK CFI bf80 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI c490 x25: x25 x26: x26
STACK CFI c498 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI c740 x25: x25 x26: x26
STACK CFI c758 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT c760 a4 .cfa: sp 0 + .ra: x30
STACK CFI c764 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI c774 x19: .cfa -272 + ^
STACK CFI c7fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c800 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT c808 58 .cfa: sp 0 + .ra: x30
STACK CFI c848 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c860 68 .cfa: sp 0 + .ra: x30
STACK CFI c8b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c8c8 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c8f0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c918 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c940 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT c968 34 .cfa: sp 0 + .ra: x30
STACK CFI c988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c9a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9a8 5c .cfa: sp 0 + .ra: x30
STACK CFI c9ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c9bc x19: .cfa -32 + ^
STACK CFI c9fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ca00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT ca08 28 .cfa: sp 0 + .ra: x30
STACK CFI ca0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ca2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ca30 58 .cfa: sp 0 + .ra: x30
STACK CFI ca34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ca40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ca74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ca84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ca88 28 .cfa: sp 0 + .ra: x30
STACK CFI ca8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI caac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cab0 28 .cfa: sp 0 + .ra: x30
STACK CFI cab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cad8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT cae0 70 .cfa: sp 0 + .ra: x30
STACK CFI cae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI caec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI caf4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cb20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cb24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cb4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT cb50 ec .cfa: sp 0 + .ra: x30
STACK CFI cb54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cb5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cb68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cb74 x23: .cfa -16 + ^
STACK CFI cbf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cbf8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT cc40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc58 c4 .cfa: sp 0 + .ra: x30
STACK CFI cc5c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI cc64 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI cd14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cd18 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x29: .cfa -272 + ^
STACK CFI INIT cd20 58 .cfa: sp 0 + .ra: x30
STACK CFI cd24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cd2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cd64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cd68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI cd74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT cd78 2c .cfa: sp 0 + .ra: x30
STACK CFI cd7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cd84 x19: .cfa -16 + ^
STACK CFI cda0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cda8 178 .cfa: sp 0 + .ra: x30
STACK CFI cdac .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI cdb4 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI cdd4 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI ce60 x23: .cfa -304 + ^
STACK CFI ceb0 x23: x23
STACK CFI ced8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cedc .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI cf00 x23: .cfa -304 + ^
STACK CFI cf0c x23: x23
STACK CFI cf14 x23: .cfa -304 + ^
STACK CFI cf18 x23: x23
STACK CFI INIT cf20 34 .cfa: sp 0 + .ra: x30
STACK CFI cf28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cf30 x19: .cfa -16 + ^
STACK CFI cf44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cf58 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT cfa8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT cfc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT cfd8 84 .cfa: sp 0 + .ra: x30
STACK CFI cfdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cfe4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT d060 2f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d358 e8 .cfa: sp 0 + .ra: x30
STACK CFI d35c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d364 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d370 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d380 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d434 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT d440 5c .cfa: sp 0 + .ra: x30
STACK CFI d444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d44c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d494 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT d4a0 284 .cfa: sp 0 + .ra: x30
STACK CFI d4a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d4ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d4b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d4c4 x23: .cfa -16 + ^
STACK CFI d598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d59c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI d614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT d728 bc .cfa: sp 0 + .ra: x30
STACK CFI d72c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d738 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d748 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d754 x23: .cfa -16 + ^
STACK CFI d76c x21: x21 x22: x22
STACK CFI d770 x23: x23
STACK CFI d7a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d7a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI d7d8 x21: x21 x22: x22
STACK CFI d7dc x23: x23
STACK CFI d7e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d7e8 f8 .cfa: sp 0 + .ra: x30
STACK CFI d7ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d7f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d804 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d80c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d8bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d8c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT d8e0 130 .cfa: sp 0 + .ra: x30
STACK CFI d8e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d8ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d8f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d900 x23: .cfa -32 + ^
STACK CFI d9cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d9d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT da10 158 .cfa: sp 0 + .ra: x30
STACK CFI da14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI da1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI da28 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI da30 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI da40 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI db0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI db10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT db68 d4 .cfa: sp 0 + .ra: x30
STACK CFI db6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI db74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI db7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI db8c x23: .cfa -16 + ^
STACK CFI dc14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI dc18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT dc40 b0 .cfa: sp 0 + .ra: x30
STACK CFI dc44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dc4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dc54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI dcd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dcd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT dcf0 c0 .cfa: sp 0 + .ra: x30
STACK CFI dcf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI dcfc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI dd04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI dd14 x23: .cfa -16 + ^
STACK CFI dd94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI dd98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT ddb0 f8 .cfa: sp 0 + .ra: x30
STACK CFI ddb4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ddbc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ddc8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ddd8 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI de20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI de24 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT dea8 10c .cfa: sp 0 + .ra: x30
STACK CFI deac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI deb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI df68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI df6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT dfb8 34 .cfa: sp 0 + .ra: x30
STACK CFI dfbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dfc4 x19: .cfa -16 + ^
STACK CFI dfe8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dff0 48 .cfa: sp 0 + .ra: x30
STACK CFI dff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e000 x19: .cfa -16 + ^
STACK CFI e024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e038 60 .cfa: sp 0 + .ra: x30
STACK CFI e03c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e044 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e05c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI e088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e090 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e098 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e0a0 1cc .cfa: sp 0 + .ra: x30
STACK CFI e0ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e0bc x21: .cfa -16 + ^
STACK CFI e0dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e13c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e140 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e18c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e25c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT e270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT e280 15c .cfa: sp 0 + .ra: x30
STACK CFI e284 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e28c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e294 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e29c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e2ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e37c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e380 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT e3e0 218 .cfa: sp 0 + .ra: x30
STACK CFI e3e4 .cfa: sp 1136 +
STACK CFI e3e8 .ra: .cfa -1128 + ^ x29: .cfa -1136 + ^
STACK CFI e3f0 x27: .cfa -1056 + ^ x28: .cfa -1048 + ^
STACK CFI e3fc x19: .cfa -1120 + ^ x20: .cfa -1112 + ^
STACK CFI e408 x21: .cfa -1104 + ^ x22: .cfa -1096 + ^
STACK CFI e410 x23: .cfa -1088 + ^ x24: .cfa -1080 + ^
STACK CFI e41c x25: .cfa -1072 + ^ x26: .cfa -1064 + ^
STACK CFI e534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e538 .cfa: sp 1136 + .ra: .cfa -1128 + ^ x19: .cfa -1120 + ^ x20: .cfa -1112 + ^ x21: .cfa -1104 + ^ x22: .cfa -1096 + ^ x23: .cfa -1088 + ^ x24: .cfa -1080 + ^ x25: .cfa -1072 + ^ x26: .cfa -1064 + ^ x27: .cfa -1056 + ^ x28: .cfa -1048 + ^ x29: .cfa -1136 + ^
STACK CFI INIT e5f8 234 .cfa: sp 0 + .ra: x30
STACK CFI e5fc .cfa: sp 1248 +
STACK CFI e600 .ra: .cfa -1240 + ^ x29: .cfa -1248 + ^
STACK CFI e60c x21: .cfa -1216 + ^ x22: .cfa -1208 + ^
STACK CFI e61c x19: .cfa -1232 + ^ x20: .cfa -1224 + ^
STACK CFI e634 x23: .cfa -1200 + ^ x24: .cfa -1192 + ^
STACK CFI e6b4 x25: .cfa -1184 + ^
STACK CFI e73c x25: x25
STACK CFI e768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e76c .cfa: sp 1248 + .ra: .cfa -1240 + ^ x19: .cfa -1232 + ^ x20: .cfa -1224 + ^ x21: .cfa -1216 + ^ x22: .cfa -1208 + ^ x23: .cfa -1200 + ^ x24: .cfa -1192 + ^ x29: .cfa -1248 + ^
STACK CFI e784 x25: .cfa -1184 + ^
STACK CFI e7a8 x25: x25
STACK CFI e7c4 x25: .cfa -1184 + ^
STACK CFI e824 x25: x25
STACK CFI e828 x25: .cfa -1184 + ^
STACK CFI INIT e830 3fc .cfa: sp 0 + .ra: x30
STACK CFI e838 .cfa: sp 4208 +
STACK CFI e83c .ra: .cfa -4200 + ^ x29: .cfa -4208 + ^
STACK CFI e844 x23: .cfa -4160 + ^ x24: .cfa -4152 + ^
STACK CFI e84c x21: .cfa -4176 + ^ x22: .cfa -4168 + ^
STACK CFI e858 x19: .cfa -4192 + ^ x20: .cfa -4184 + ^
STACK CFI e95c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e960 .cfa: sp 4208 + .ra: .cfa -4200 + ^ x19: .cfa -4192 + ^ x20: .cfa -4184 + ^ x21: .cfa -4176 + ^ x22: .cfa -4168 + ^ x23: .cfa -4160 + ^ x24: .cfa -4152 + ^ x29: .cfa -4208 + ^
STACK CFI INIT ec30 2a4 .cfa: sp 0 + .ra: x30
STACK CFI ec34 .cfa: sp 2176 +
STACK CFI ec40 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI ec48 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI ec54 x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI ec68 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI ec74 x27: .cfa -2096 + ^
STACK CFI ecb0 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI ecdc x19: x19 x20: x20
STACK CFI ed20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ed24 .cfa: sp 2176 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x27: .cfa -2096 + ^ x29: .cfa -2176 + ^
STACK CFI ee54 x19: x19 x20: x20
STACK CFI ee5c x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI eecc x19: x19 x20: x20
STACK CFI eed0 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI INIT eed8 d8 .cfa: sp 0 + .ra: x30
STACK CFI eedc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI eee8 x23: .cfa -32 + ^
STACK CFI eef4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ef00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ef90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ef94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT efb0 114 .cfa: sp 0 + .ra: x30
STACK CFI efb4 .cfa: sp 2112 +
STACK CFI efc4 .ra: .cfa -2104 + ^ x29: .cfa -2112 + ^
STACK CFI efcc x19: .cfa -2096 + ^
STACK CFI f058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f05c .cfa: sp 2112 + .ra: .cfa -2104 + ^ x19: .cfa -2096 + ^ x29: .cfa -2112 + ^
STACK CFI INIT f0c8 6c .cfa: sp 0 + .ra: x30
STACK CFI f0cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f0d4 x21: .cfa -16 + ^
STACK CFI f0e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f124 x19: x19 x20: x20
STACK CFI f130 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT f138 14c .cfa: sp 0 + .ra: x30
STACK CFI f13c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f144 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f150 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f184 x23: .cfa -16 + ^
STACK CFI f218 x23: x23
STACK CFI f220 x21: x21 x22: x22
STACK CFI f22c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f230 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI f258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f25c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI f270 x21: x21 x22: x22
STACK CFI f278 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT f288 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT f318 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT f398 5e4 .cfa: sp 0 + .ra: x30
STACK CFI f3a0 .cfa: sp 8384 +
STACK CFI f3ac .ra: .cfa -8376 + ^ x29: .cfa -8384 + ^
STACK CFI f3d4 x25: .cfa -8320 + ^ x26: .cfa -8312 + ^
STACK CFI f3dc x19: .cfa -8368 + ^ x20: .cfa -8360 + ^
STACK CFI f3e8 x21: .cfa -8352 + ^ x22: .cfa -8344 + ^
STACK CFI f3ec x23: .cfa -8336 + ^ x24: .cfa -8328 + ^
STACK CFI f3f0 x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI f618 x19: x19 x20: x20
STACK CFI f61c x21: x21 x22: x22
STACK CFI f620 x27: x27 x28: x28
STACK CFI f628 x23: x23 x24: x24
STACK CFI f62c x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x23: .cfa -8336 + ^ x24: .cfa -8328 + ^ x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI f6a8 x19: x19 x20: x20
STACK CFI f6b0 x21: x21 x22: x22
STACK CFI f6b4 x23: x23 x24: x24
STACK CFI f6b8 x27: x27 x28: x28
STACK CFI f6e8 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI f6ec .cfa: sp 8384 + .ra: .cfa -8376 + ^ x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x23: .cfa -8336 + ^ x24: .cfa -8328 + ^ x25: .cfa -8320 + ^ x26: .cfa -8312 + ^ x27: .cfa -8304 + ^ x28: .cfa -8296 + ^ x29: .cfa -8384 + ^
STACK CFI f87c x19: x19 x20: x20
STACK CFI f880 x21: x21 x22: x22
STACK CFI f884 x23: x23 x24: x24
STACK CFI f888 x27: x27 x28: x28
STACK CFI f88c x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x23: .cfa -8336 + ^ x24: .cfa -8328 + ^ x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI f8cc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI f8d4 x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x23: .cfa -8336 + ^ x24: .cfa -8328 + ^ x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI f8f4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI f8f8 x19: .cfa -8368 + ^ x20: .cfa -8360 + ^
STACK CFI f8fc x21: .cfa -8352 + ^ x22: .cfa -8344 + ^
STACK CFI f900 x23: .cfa -8336 + ^ x24: .cfa -8328 + ^
STACK CFI f904 x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI f918 x19: x19 x20: x20
STACK CFI f91c x21: x21 x22: x22
STACK CFI f920 x23: x23 x24: x24
STACK CFI f924 x27: x27 x28: x28
STACK CFI f92c x19: .cfa -8368 + ^ x20: .cfa -8360 + ^ x21: .cfa -8352 + ^ x22: .cfa -8344 + ^ x23: .cfa -8336 + ^ x24: .cfa -8328 + ^ x27: .cfa -8304 + ^ x28: .cfa -8296 + ^
STACK CFI INIT f980 5c .cfa: sp 0 + .ra: x30
STACK CFI f984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f994 x19: .cfa -32 + ^
STACK CFI f9d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f9d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT f9e0 34 .cfa: sp 0 + .ra: x30
STACK CFI f9e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f9ec x19: .cfa -16 + ^
STACK CFI fa10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fa18 30c .cfa: sp 0 + .ra: x30
STACK CFI fa1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fa24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI fa34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI fa54 x27: .cfa -16 + ^
STACK CFI fa74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI fa80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fb38 x23: x23 x24: x24
STACK CFI fb3c x25: x25 x26: x26
STACK CFI fb44 x27: x27
STACK CFI fb54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI fb58 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI fc98 x23: x23 x24: x24
STACK CFI fca0 x25: x25 x26: x26
STACK CFI fcc0 x27: x27
STACK CFI fcc4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI fcdc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI fcec x27: x27
STACK CFI fcf4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI fd04 x23: x23 x24: x24
STACK CFI fd08 x25: x25 x26: x26
STACK CFI fd0c x27: x27
STACK CFI INIT fd28 130 .cfa: sp 0 + .ra: x30
STACK CFI fd2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fd38 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI fde0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI fde4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT fe58 d0 .cfa: sp 0 + .ra: x30
STACK CFI fe5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fe64 x19: .cfa -16 + ^
STACK CFI feec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fef0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT ff28 1d0 .cfa: sp 0 + .ra: x30
STACK CFI ff2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ff38 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI ff70 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ff78 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10078 x19: x19 x20: x20
STACK CFI 1007c x23: x23 x24: x24
STACK CFI 1008c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 10090 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 100a4 x19: x19 x20: x20
STACK CFI 100ac x23: x23 x24: x24
STACK CFI 100b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 100b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 100e8 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 100f8 54 .cfa: sp 0 + .ra: x30
STACK CFI 100fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10104 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10150 40 .cfa: sp 0 + .ra: x30
STACK CFI 10154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1015c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10178 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10190 40 .cfa: sp 0 + .ra: x30
STACK CFI 10194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1019c x19: .cfa -16 + ^
STACK CFI 101b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 101bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 101d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 101d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101dc x19: .cfa -16 + ^
STACK CFI 101f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 101fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10210 28 .cfa: sp 0 + .ra: x30
STACK CFI 10214 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 10228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 10238 78 .cfa: sp 0 + .ra: x30
STACK CFI 1023c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10244 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 102ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 102b0 84 .cfa: sp 0 + .ra: x30
STACK CFI 102b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 102bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 102e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 102e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10338 84 .cfa: sp 0 + .ra: x30
STACK CFI 1033c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1036c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 103a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 103ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 103c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 103f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 103f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 103fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10404 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1043c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1045c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 10460 7c .cfa: sp 0 + .ra: x30
STACK CFI 10464 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1046c x19: .cfa -16 + ^
STACK CFI 104b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 104b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 104cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 104d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 104e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 104e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 104ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10528 8c .cfa: sp 0 + .ra: x30
STACK CFI 1052c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10534 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10540 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1057c x19: x19 x20: x20
STACK CFI 10584 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 10588 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 105ac x19: x19 x20: x20
STACK CFI INIT 105b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 105bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 105c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 105fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10600 10 .cfa: sp 0 + .ra: x30
