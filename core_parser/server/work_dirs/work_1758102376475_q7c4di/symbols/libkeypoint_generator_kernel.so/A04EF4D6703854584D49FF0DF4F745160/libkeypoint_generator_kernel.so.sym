MODULE Linux arm64 A04EF4D6703854584D49FF0DF4F745160 libkeypoint_generator_kernel.so
INFO CODE_ID D6F44EA0387058544D49FF0DF4F74516
PUBLIC 7ef0 0 _init
PUBLIC 83c0 0 _GLOBAL__sub_I_trt_keypoint_generator.cpp
PUBLIC 8460 0 _GLOBAL__sub_I_checkMacrosPlugin.cpp
PUBLIC 8800 0 call_weak_fn
PUBLIC 8814 0 deregister_tm_clones
PUBLIC 8844 0 register_tm_clones
PUBLIC 8880 0 __do_global_dtors_aux
PUBLIC 88d0 0 frame_dummy
PUBLIC 88e0 0 trt_plugin::KPG_Plugin::getNbOutputs() const
PUBLIC 88f0 0 trt_plugin::KPG_Plugin::initialize() [clone .localalias]
PUBLIC 8900 0 trt_plugin::KPG_Plugin::detachFromContext()
PUBLIC 8910 0 trt_plugin::KPG_Plugin::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 8940 0 trt_plugin::KPG_Plugin::getSerializationSize() const
PUBLIC 8950 0 trt_plugin::KPG_Plugin::serialize(void*) const
PUBLIC 8980 0 trt_plugin::KPG_Plugin::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 89b0 0 trt_plugin::KPG_Plugin::getPluginType() const
PUBLIC 89c0 0 trt_plugin::KPG_PluginCreator::getPluginVersion() const
PUBLIC 89d0 0 trt_plugin::KPG_Plugin::getPluginNamespace() const
PUBLIC 89e0 0 trt_plugin::KPG_Plugin::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 8a00 0 trt_plugin::KPG_Plugin::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 8a10 0 trt_plugin::KPG_Plugin::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 8a20 0 trt_plugin::KPG_PluginCreator::getFieldNames()
PUBLIC 8a30 0 trt_plugin::KPG_Plugin::~KPG_Plugin()
PUBLIC 8a90 0 trt_plugin::KPG_Plugin::~KPG_Plugin() [clone .localalias]
PUBLIC 8ac0 0 trt_plugin::KPG_Plugin::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 8cb0 0 trt_plugin::KPG_Plugin::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 8d60 0 trt_plugin::KPG_Plugin::setPluginNamespace(char const*)
PUBLIC 8da0 0 trt_plugin::KPG_Plugin::destroy()
PUBLIC 8df0 0 trt_plugin::KPG_Plugin::KPG_Plugin(int, int, int, int)
PUBLIC 8e30 0 trt_plugin::KPG_Plugin::clone() const
PUBLIC 8ef0 0 trt_plugin::KPG_PluginCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 91d0 0 trt_plugin::KPG_Plugin::KPG_Plugin(void const*, unsigned long)
PUBLIC 9210 0 trt_plugin::KPG_PluginCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 92e0 0 trt_plugin::KPG_PluginCreator::KPG_PluginCreator()
PUBLIC 9530 0 std::ctype<char>::do_widen(char) const
PUBLIC 9540 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 9550 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 9560 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 9570 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 9580 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 9590 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 95a0 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 95c0 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 95d0 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 95e0 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 95f0 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 9600 0 trt_plugin::BaseCreator::getPluginNamespace() const
PUBLIC 9610 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 9640 0 nvinfer1::PluginRegistrar<trt_plugin::KPG_PluginCreator>::~PluginRegistrar()
PUBLIC 9670 0 std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::~vector()
PUBLIC 9680 0 trt_plugin::KPG_PluginCreator::~KPG_PluginCreator()
PUBLIC 96d0 0 trt_plugin::BaseCreator::setPluginNamespace(char const*)
PUBLIC 9710 0 nvinfer1::plugin::caughtError(std::exception const&)
PUBLIC 9880 0 void std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> >::_M_realloc_insert<nvinfer1::PluginField>(__gnu_cxx::__normal_iterator<nvinfer1::PluginField*, std::vector<nvinfer1::PluginField, std::allocator<nvinfer1::PluginField> > >, nvinfer1::PluginField&&)
PUBLIC 9a20 0 nvinfer1::plugin::TRTException::log(std::ostream&) const
PUBLIC 9c40 0 nvinfer1::plugin::throwCudaError(char const*, char const*, int, int, char const*)
PUBLIC 9cf0 0 nvinfer1::plugin::throwCublasError(char const*, char const*, int, int, char const*)
PUBLIC 9e80 0 nvinfer1::plugin::throwCudnnError(char const*, char const*, int, int, char const*)
PUBLIC 9f30 0 nvinfer1::plugin::logError(char const*, char const*, char const*, int)
PUBLIC a2d0 0 nvinfer1::plugin::reportValidationFailure(char const*, char const*, int)
PUBLIC a6c0 0 nvinfer1::plugin::throwPluginError(char const*, char const*, int, int, char const*)
PUBLIC a770 0 nvinfer1::plugin::reportAssertion(char const*, char const*, int)
PUBLIC ab90 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC abb0 0 nvinfer1::plugin::TRTException::~TRTException()
PUBLIC abf0 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC ac10 0 nvinfer1::plugin::CudaError::~CudaError()
PUBLIC ac50 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC ac70 0 nvinfer1::plugin::CublasError::~CublasError()
PUBLIC acb0 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC acd0 0 nvinfer1::plugin::CudnnError::~CudnnError()
PUBLIC ad10 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC ad30 0 nvinfer1::plugin::PluginError::~PluginError()
PUBLIC ad70 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC ae20 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC aec0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC af70 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b000 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b0b0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b150 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b200 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::~LogStream()
PUBLIC b2a0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b350 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b3e0 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b490 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::~LogStream()
PUBLIC b530 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b5e0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::~LogStream()
PUBLIC b670 0 virtual thunk to nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b720 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::~LogStream()
PUBLIC b7b0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC b810 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC b870 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b8d0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC b930 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::~Buf()
PUBLIC b990 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::~Buf()
PUBLIC b9f0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::~Buf()
PUBLIC ba50 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::~Buf()
PUBLIC bab0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)3>::Buf::sync()
PUBLIC bc60 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)4>::Buf::sync()
PUBLIC be10 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)2>::Buf::sync()
PUBLIC bfc0 0 nvinfer1::plugin::LogStream<(nvinfer1::ILogger::Severity)1>::Buf::sync()
PUBLIC c170 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c1d0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC c230 0 _fini
STACK CFI INIT 8814 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8844 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8880 50 .cfa: sp 0 + .ra: x30
STACK CFI 8890 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8898 x19: .cfa -16 + ^
STACK CFI 88c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 88d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9530 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9540 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9550 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9560 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9570 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9590 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 95f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8900 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8910 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8950 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8980 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 89d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 89e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a30 60 .cfa: sp 0 + .ra: x30
STACK CFI 8a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a44 x19: .cfa -16 + ^
STACK CFI 8a80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8a8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9610 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9640 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9670 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a90 28 .cfa: sp 0 + .ra: x30
STACK CFI 8a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a9c x19: .cfa -16 + ^
STACK CFI 8ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9680 48 .cfa: sp 0 + .ra: x30
STACK CFI 9684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9698 x19: .cfa -16 + ^
STACK CFI 96c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ac0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8ac4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8acc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8adc x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8b7c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c38 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 8cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8cb0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8cb4 .cfa: sp 128 +
STACK CFI 8cb8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8cc4 x19: .cfa -16 + ^
STACK CFI 8d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8d4c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8d60 40 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8da0 48 .cfa: sp 0 + .ra: x30
STACK CFI 8dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8dc4 x19: .cfa -16 + ^
STACK CFI 8ddc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 96d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 970c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9710 168 .cfa: sp 0 + .ra: x30
STACK CFI 9714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9720 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9730 x21: .cfa -16 + ^
STACK CFI 97e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 97e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 982c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8df0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e30 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8e34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8e3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8ea4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8eb0 x21: .cfa -16 + ^
STACK CFI 8ec8 x21: x21
STACK CFI 8eec x21: .cfa -16 + ^
STACK CFI INIT 8ef0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 8ef4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 8f00 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 8f18 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 9108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 910c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 9178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 917c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 91d0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9210 c8 .cfa: sp 0 + .ra: x30
STACK CFI 9214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 921c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9228 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 927c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9298 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9880 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 9884 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9894 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 98a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 98b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 99e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 99e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 92e0 244 .cfa: sp 0 + .ra: x30
STACK CFI 92e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 92f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9308 x21: .cfa -48 + ^
STACK CFI 9444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9448 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 83c0 9c .cfa: sp 0 + .ra: x30
STACK CFI 83c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ab90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT abb0 38 .cfa: sp 0 + .ra: x30
STACK CFI abb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abc4 x19: .cfa -16 + ^
STACK CFI abe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT abf0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac10 38 .cfa: sp 0 + .ra: x30
STACK CFI ac14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac24 x19: .cfa -16 + ^
STACK CFI ac44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac70 38 .cfa: sp 0 + .ra: x30
STACK CFI ac74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac84 x19: .cfa -16 + ^
STACK CFI aca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT acb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT acd0 38 .cfa: sp 0 + .ra: x30
STACK CFI acd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ace4 x19: .cfa -16 + ^
STACK CFI ad04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad30 38 .cfa: sp 0 + .ra: x30
STACK CFI ad34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad44 x19: .cfa -16 + ^
STACK CFI ad64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ad70 b0 .cfa: sp 0 + .ra: x30
STACK CFI ad74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ad94 x21: .cfa -16 + ^
STACK CFI ae1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT aec0 a4 .cfa: sp 0 + .ra: x30
STACK CFI aec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aed4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aee4 x21: .cfa -16 + ^
STACK CFI af60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b000 b0 .cfa: sp 0 + .ra: x30
STACK CFI b004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b014 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b024 x21: .cfa -16 + ^
STACK CFI b0ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b150 b0 .cfa: sp 0 + .ra: x30
STACK CFI b154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b164 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b174 x21: .cfa -16 + ^
STACK CFI b1fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b2a0 a4 .cfa: sp 0 + .ra: x30
STACK CFI b2a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b2b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b2c4 x21: .cfa -16 + ^
STACK CFI b340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b3e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b3e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b3f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b404 x21: .cfa -16 + ^
STACK CFI b48c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b530 a4 .cfa: sp 0 + .ra: x30
STACK CFI b534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b554 x21: .cfa -16 + ^
STACK CFI b5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b670 a4 .cfa: sp 0 + .ra: x30
STACK CFI b674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b684 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b694 x21: .cfa -16 + ^
STACK CFI b710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b7b0 54 .cfa: sp 0 + .ra: x30
STACK CFI b7b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b7c8 x19: .cfa -16 + ^
STACK CFI b800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b810 54 .cfa: sp 0 + .ra: x30
STACK CFI b814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b828 x19: .cfa -16 + ^
STACK CFI b860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b870 54 .cfa: sp 0 + .ra: x30
STACK CFI b874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b888 x19: .cfa -16 + ^
STACK CFI b8c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b8d0 54 .cfa: sp 0 + .ra: x30
STACK CFI b8d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b8e8 x19: .cfa -16 + ^
STACK CFI b920 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b0b0 98 .cfa: sp 0 + .ra: x30
STACK CFI b0b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b0c8 x19: .cfa -16 + ^
STACK CFI b144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae20 98 .cfa: sp 0 + .ra: x30
STACK CFI ae24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae38 x19: .cfa -16 + ^
STACK CFI aeb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b490 98 .cfa: sp 0 + .ra: x30
STACK CFI b494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b4a8 x19: .cfa -16 + ^
STACK CFI b524 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b200 98 .cfa: sp 0 + .ra: x30
STACK CFI b204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b218 x19: .cfa -16 + ^
STACK CFI b294 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b930 60 .cfa: sp 0 + .ra: x30
STACK CFI b934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b948 x19: .cfa -16 + ^
STACK CFI b98c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b990 60 .cfa: sp 0 + .ra: x30
STACK CFI b994 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b9a8 x19: .cfa -16 + ^
STACK CFI b9ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b9f0 60 .cfa: sp 0 + .ra: x30
STACK CFI b9f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba08 x19: .cfa -16 + ^
STACK CFI ba4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba50 60 .cfa: sp 0 + .ra: x30
STACK CFI ba54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba68 x19: .cfa -16 + ^
STACK CFI baac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af70 8c .cfa: sp 0 + .ra: x30
STACK CFI af74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af88 x19: .cfa -16 + ^
STACK CFI aff8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b350 8c .cfa: sp 0 + .ra: x30
STACK CFI b354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b368 x19: .cfa -16 + ^
STACK CFI b3d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b720 8c .cfa: sp 0 + .ra: x30
STACK CFI b724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b738 x19: .cfa -16 + ^
STACK CFI b7a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b5e0 8c .cfa: sp 0 + .ra: x30
STACK CFI b5e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b5f8 x19: .cfa -16 + ^
STACK CFI b668 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9a20 214 .cfa: sp 0 + .ra: x30
STACK CFI 9a24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9a3c x23: .cfa -16 + ^
STACK CFI 9b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9b8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT bab0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bab4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI babc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bad4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bbd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bbd4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT bc60 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bc64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bc6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bc84 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bd80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bd84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT be10 1a4 .cfa: sp 0 + .ra: x30
STACK CFI be14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI be1c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI be34 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bf30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bf34 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT bfc0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bfc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bfcc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bfe4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c0e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c0e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9c40 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9c44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9c54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9cf0 184 .cfa: sp 0 + .ra: x30
STACK CFI 9cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9d04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9e80 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9e84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9e94 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 9f30 394 .cfa: sp 0 + .ra: x30
STACK CFI 9f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9f4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9f54 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a1d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a1d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a21c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT c170 54 .cfa: sp 0 + .ra: x30
STACK CFI c174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c188 x19: .cfa -16 + ^
STACK CFI c1c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c1d0 60 .cfa: sp 0 + .ra: x30
STACK CFI c1d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1e8 x19: .cfa -16 + ^
STACK CFI c22c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a2d0 3ec .cfa: sp 0 + .ra: x30
STACK CFI a2d4 .cfa: sp 528 +
STACK CFI a2d8 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI a2e0 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI a2e8 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI a2f4 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI a300 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI a578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a57c .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT a6c0 a8 .cfa: sp 0 + .ra: x30
STACK CFI a6c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a6d4 x19: .cfa -64 + ^
STACK CFI INIT a770 414 .cfa: sp 0 + .ra: x30
STACK CFI a774 .cfa: sp 512 +
STACK CFI a778 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI a780 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI a790 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI a79c x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI a7a4 x25: .cfa -448 + ^
STACK CFI INIT 8460 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 8464 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 846c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8478 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 879c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 87a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
