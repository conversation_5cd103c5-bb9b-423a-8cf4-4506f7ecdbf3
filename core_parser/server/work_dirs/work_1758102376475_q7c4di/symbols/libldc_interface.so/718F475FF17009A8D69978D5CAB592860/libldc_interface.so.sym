MODULE Linux arm64 718F475FF17009A8D69978D5CAB592860 libldc_interface.so
INFO CODE_ID 5F478F7170F1A809D69978D5CAB59286
PUBLIC 1d50 0 _init
PUBLIC 20c0 0 call_weak_fn
PUBLIC 20d4 0 deregister_tm_clones
PUBLIC 2104 0 register_tm_clones
PUBLIC 2140 0 __do_global_dtors_aux
PUBLIC 2190 0 frame_dummy
PUBLIC 21a0 0 lios::ldc::LdcNvMedia::LdcNvMedia(unsigned int, unsigned int)
PUBLIC 2450 0 lios::ldc::LdcNvMedia::~LdcNvMedia()
PUBLIC 2530 0 lios::ldc::LdcNvMedia::SetParameters(NvMediaLdcCameraIntrinsic const&, NvMediaLdcCameraIntrinsic const&, NvMediaLdcLensDistortion const&, NvMediaLdcRegionParameters const&)
PUBLIC 27e0 0 lios::ldc::LdcNvMedia::TransformSurface(NvSciBufObjRefRec*, NvSciBufObjRefRec*)
PUBLIC 2960 0 lios::ldc::LdcNvMedia::RegisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2a00 0 lios::ldc::LdcNvMedia::UnregisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2aa0 0 lios::ldc::LdcNvMedia::GetLdcBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 2b80 0 _fini
STACK CFI INIT 20d4 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2104 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2140 50 .cfa: sp 0 + .ra: x30
STACK CFI 2150 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2158 x19: .cfa -16 + ^
STACK CFI 2188 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2190 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21a0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 21a4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 21ac x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 21b8 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 21cc x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 23ac .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2450 dc .cfa: sp 0 + .ra: x30
STACK CFI 2454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 245c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 246c x21: .cfa -16 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 24f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2530 2ac .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 2540 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 2564 x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 2574 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 2708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 270c .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x29: .cfa -352 + ^
STACK CFI INIT 27e0 180 .cfa: sp 0 + .ra: x30
STACK CFI 27e4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 27ec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2824 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 289c x21: x21 x22: x22
STACK CFI 28a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 28e8 x21: x21 x22: x22
STACK CFI 2928 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2948 x21: x21 x22: x22
STACK CFI 294c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 2960 98 .cfa: sp 0 + .ra: x30
STACK CFI 2964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 296c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2974 x21: .cfa -16 + ^
STACK CFI 29cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 29f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a00 98 .cfa: sp 0 + .ra: x30
STACK CFI 2a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a14 x21: .cfa -16 + ^
STACK CFI 2a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2aa0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2aa4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2aac x19: .cfa -128 + ^
STACK CFI 2b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b58 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
