MODULE Linux arm64 A9121A5808799E2AA61B01ED1042A4850 libopencv_dnn_superres.so.4.3
INFO CODE_ID 581A12A979082A9EA61B01ED1042A4854A01F176
PUBLIC 41c0 0 _init
PUBLIC 4790 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.76]
PUBLIC 4830 0 _GLOBAL__sub_I_dnn_superres.cpp
PUBLIC 4860 0 call_weak_fn
PUBLIC 4878 0 deregister_tm_clones
PUBLIC 48b0 0 register_tm_clones
PUBLIC 48f0 0 __do_global_dtors_aux
PUBLIC 4938 0 frame_dummy
PUBLIC 4970 0 cv::Algorithm::clear()
PUBLIC 4978 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 4980 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 4988 0 cv::Algorithm::empty() const
PUBLIC 4990 0 cv::dnn::dnn4_v20200310::Layer::getFLOPS(std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > > const&, std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > > const&) const
PUBLIC 4998 0 std::_Sp_counted_ptr<cv::dnn_superres::DepthToSpace*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 49a0 0 std::_Sp_counted_ptr<cv::dnn_superres::DnnSuperResImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 49a8 0 std::_Sp_counted_ptr<cv::dnn_superres::DepthToSpace*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 49b0 0 std::_Sp_counted_ptr<cv::dnn_superres::DnnSuperResImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 49b8 0 std::_Sp_counted_ptr<cv::dnn_superres::DnnSuperResImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 49c0 0 std::_Sp_counted_ptr<cv::dnn_superres::DnnSuperResImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 49c8 0 std::_Sp_counted_ptr<cv::dnn_superres::DepthToSpace*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 49d0 0 std::_Sp_counted_ptr<cv::dnn_superres::DepthToSpace*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 49d8 0 cv::dnn_superres::DepthToSpace::~DepthToSpace()
PUBLIC 49e8 0 cv::dnn_superres::DepthToSpace::~DepthToSpace()
PUBLIC 4a10 0 std::_Sp_counted_ptr<cv::dnn_superres::DepthToSpace*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a48 0 cv::dnn_superres::DepthToSpace::create(cv::dnn::dnn4_v20200310::LayerParams&)
PUBLIC 4af8 0 std::_Sp_counted_ptr<cv::dnn_superres::DnnSuperResImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4b38 0 cv::Mat::~Mat()
PUBLIC 4bc8 0 cv::dnn_superres::DnnSuperResImpl::setModel(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 4bd8 0 cv::dnn_superres::DnnSuperResImpl::getScale()
PUBLIC 4be0 0 cv::dnn_superres::DnnSuperResImpl::getAlgorithm[abi:cxx11]()
PUBLIC 4cb8 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 4d78 0 cv::dnn_superres::DepthToSpace::forward(cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 4fc0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 5008 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 5058 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 50d8 0 cv::dnn_superres::DnnSuperResImpl::readModel(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 58c0 0 cv::dnn_superres::DnnSuperResImpl::readModel(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6160 0 cv::dnn_superres::DnnSuperResImpl::preprocess_YCrCb(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 68a0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, cv::dnn::dnn4_v20200310::DictValue>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, cv::dnn::dnn4_v20200310::DictValue> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, cv::dnn::dnn4_v20200310::DictValue> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, cv::dnn::dnn4_v20200310::DictValue> >*)
PUBLIC 6a08 0 cv::dnn::dnn4_v20200310::LayerParams::~LayerParams()
PUBLIC 6af0 0 cv::dnn_superres::DnnSuperResImpl::DnnSuperResImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 6de0 0 cv::dnn_superres::DnnSuperResImpl::DnnSuperResImpl()
PUBLIC 7028 0 cv::dnn_superres::DnnSuperResImpl::create()
PUBLIC 70d8 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 7420 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC 7770 0 cv::dnn_superres::DnnSuperResImpl::reconstruct_YCrCb(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, int)
PUBLIC 84c0 0 cv::dnn_superres::DnnSuperResImpl::upsample(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 9620 0 cv::dnn_superres::DnnSuperResImpl::upsampleMultioutput(cv::_InputArray const&, std::vector<cv::Mat, std::allocator<cv::Mat> >&, std::vector<int, std::allocator<int> > const&, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC a3a0 0 std::vector<int, std::allocator<int> >::operator=(std::vector<int, std::allocator<int> > const&)
PUBLIC a4f0 0 cv::dnn_superres::DepthToSpace::getMemoryShapes(std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > > const&, int, std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >&, std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >&) const
PUBLIC a8a0 0 _fini
STACK CFI INIT 4970 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4978 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4980 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4988 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4998 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 49ec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4a08 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 4a10 38 .cfa: sp 0 + .ra: x30
STACK CFI 4a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4a38 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4a40 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4a44 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 4a48 ac .cfa: sp 0 + .ra: x30
STACK CFI 4a4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a54 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 4aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4ab0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 4af8 40 .cfa: sp 0 + .ra: x30
STACK CFI 4afc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4b30 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4b34 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 4790 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4794 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47a0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 4820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4824 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 4b38 90 .cfa: sp 0 + .ra: x30
STACK CFI 4b3c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4bb0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4bc4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 4bc8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4be0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4be4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4bec .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 4c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4c48 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 4c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4c90 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 4cb8 bc .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4cc0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 4d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4d68 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 4d78 248 .cfa: sp 0 + .ra: x30
STACK CFI 4d7c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d88 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 4fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4fa4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4fc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5004 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5008 50 .cfa: sp 0 + .ra: x30
STACK CFI 5010 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5054 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5058 7c .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 50b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 50d0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 50d8 7e8 .cfa: sp 0 + .ra: x30
STACK CFI 50dc .cfa: sp 640 +
STACK CFI 50e0 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 50fc .ra: .cfa -560 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 54bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 54c0 .cfa: sp 640 + .ra: .cfa -560 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI INIT 58c0 898 .cfa: sp 0 + .ra: x30
STACK CFI 58c4 .cfa: sp 640 +
STACK CFI 58c8 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 58d0 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 58d8 x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 58e8 .ra: .cfa -560 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 5d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5d70 .cfa: sp 640 + .ra: .cfa -560 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI INIT 6160 728 .cfa: sp 0 + .ra: x30
STACK CFI 6164 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6168 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6170 .ra: .cfa -240 + ^
STACK CFI 6244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6248 .cfa: sp 272 + .ra: .cfa -240 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6378 .cfa: sp 272 + .ra: .cfa -240 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 64b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 64c0 .cfa: sp 272 + .ra: .cfa -240 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI INIT 68a0 164 .cfa: sp 0 + .ra: x30
STACK CFI 68a8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 68b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 68b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 68c4 .ra: .cfa -16 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 69fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT 6a08 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6a0c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a14 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6af0 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6afc x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6b0c .ra: .cfa -224 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6b84 .cfa: sp 272 + .ra: .cfa -224 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6d70 .cfa: sp 272 + .ra: .cfa -224 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI INIT 6de0 244 .cfa: sp 0 + .ra: x30
STACK CFI 6de4 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6dec x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6dfc .ra: .cfa -224 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6e24 .cfa: sp 272 + .ra: .cfa -224 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6fcc .cfa: sp 272 + .ra: .cfa -224 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI INIT 7028 ac .cfa: sp 0 + .ra: x30
STACK CFI 702c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7038 .ra: .cfa -16 + ^
STACK CFI 707c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7080 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 70d8 344 .cfa: sp 0 + .ra: x30
STACK CFI 70dc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 70e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 70f8 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7358 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7420 334 .cfa: sp 0 + .ra: x30
STACK CFI 7424 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7430 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7440 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7698 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7770 d2c .cfa: sp 0 + .ra: x30
STACK CFI 7774 .cfa: sp 816 +
STACK CFI 777c x21: .cfa -800 + ^ x22: .cfa -792 + ^
STACK CFI 778c .ra: .cfa -744 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^
STACK CFI 77a4 v8: .cfa -736 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 7894 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7898 .cfa: sp 816 + .ra: .cfa -744 + ^ v8: .cfa -736 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^
STACK CFI 817c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8180 .cfa: sp 816 + .ra: .cfa -744 + ^ v8: .cfa -736 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^
STACK CFI INIT 84c0 1124 .cfa: sp 0 + .ra: x30
STACK CFI 84c4 .cfa: sp 1024 +
STACK CFI 84c8 x19: .cfa -1024 + ^ x20: .cfa -1016 + ^
STACK CFI 84d0 x25: .cfa -976 + ^ x26: .cfa -968 + ^
STACK CFI 84d8 x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 84e8 .ra: .cfa -944 + ^ x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^
STACK CFI 8c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8c04 .cfa: sp 1024 + .ra: .cfa -944 + ^ x19: .cfa -1024 + ^ x20: .cfa -1016 + ^ x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x25: .cfa -976 + ^ x26: .cfa -968 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI 931c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9320 .cfa: sp 1024 + .ra: .cfa -944 + ^ x19: .cfa -1024 + ^ x20: .cfa -1016 + ^ x21: .cfa -1008 + ^ x22: .cfa -1000 + ^ x23: .cfa -992 + ^ x24: .cfa -984 + ^ x25: .cfa -976 + ^ x26: .cfa -968 + ^ x27: .cfa -960 + ^ x28: .cfa -952 + ^
STACK CFI INIT 9620 d6c .cfa: sp 0 + .ra: x30
STACK CFI 9624 .cfa: sp 1184 +
STACK CFI 9628 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 9638 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI 9658 .ra: .cfa -1104 + ^ v8: .cfa -1096 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^
STACK CFI 96f0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 96f4 .cfa: sp 1184 + .ra: .cfa -1104 + ^ v8: .cfa -1096 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^ x26: .cfa -1128 + ^ x27: .cfa -1120 + ^ x28: .cfa -1112 + ^
STACK CFI INIT a3a0 14c .cfa: sp 0 + .ra: x30
STACK CFI a3a4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a3b8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI a428 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT a4f0 3b0 .cfa: sp 0 + .ra: x30
STACK CFI a4f4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a500 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a510 .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI a6d4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI a6d8 .cfa: sp 112 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI INIT 4830 30 .cfa: sp 0 + .ra: x30
STACK CFI 4834 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4850 .cfa: sp 0 + .ra: .ra x19: x19
