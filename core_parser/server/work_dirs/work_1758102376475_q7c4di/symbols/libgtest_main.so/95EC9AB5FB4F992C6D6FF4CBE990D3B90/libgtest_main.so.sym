MODULE Linux arm64 95EC9AB5FB4F992C6D6FF4CBE990D3B90 libgtest_main.so
INFO CODE_ID B59AEC954FFB2C996D6FF4CBE990D3B9
PUBLIC 758 0 _init
PUBLIC 810 0 main
PUBLIC 860 0 _GLOBAL__sub_I_gtest_main.cc
PUBLIC 89c 0 call_weak_fn
PUBLIC 8b0 0 deregister_tm_clones
PUBLIC 8e0 0 register_tm_clones
PUBLIC 91c 0 __do_global_dtors_aux
PUBLIC 96c 0 frame_dummy
PUBLIC 970 0 _fini
STACK CFI INIT 8b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 91c 50 .cfa: sp 0 + .ra: x30
STACK CFI 92c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 934 x19: .cfa -16 + ^
STACK CFI 964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 96c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 810 48 .cfa: sp 0 + .ra: x30
STACK CFI 814 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 820 x19: .cfa -32 + ^
STACK CFI 854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 860 3c .cfa: sp 0 + .ra: x30
STACK CFI 864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86c x19: .cfa -16 + ^
STACK CFI 894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
