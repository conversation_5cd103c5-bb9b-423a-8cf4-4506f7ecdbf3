MODULE Linux arm64 A9217A421549B031881648EFA3D7D79D0 libxcb-present.so.0
INFO CODE_ID 427A21A9491531B0881648EFA3D7D79DD40EAAAB
PUBLIC e38 0 xcb_present_notify_next
PUBLIC e58 0 xcb_present_notify_end
PUBLIC e70 0 xcb_present_query_version
PUBLIC ee0 0 xcb_present_query_version_unchecked
PUBLIC f50 0 xcb_present_query_version_reply
PUBLIC f58 0 xcb_present_pixmap_sizeof
PUBLIC f68 0 xcb_present_pixmap_checked
PUBLIC 1028 0 xcb_present_pixmap
PUBLIC 10e8 0 xcb_present_pixmap_notifies
PUBLIC 10f0 0 xcb_present_pixmap_notifies_length
PUBLIC 1108 0 xcb_present_pixmap_notifies_iterator
PUBLIC 1138 0 xcb_present_notify_msc_checked
PUBLIC 11b8 0 xcb_present_notify_msc
PUBLIC 1238 0 xcb_present_event_next
PUBLIC 1258 0 xcb_present_event_end
PUBLIC 1270 0 xcb_present_select_input_checked
PUBLIC 12e8 0 xcb_present_select_input
PUBLIC 1360 0 xcb_present_query_capabilities
PUBLIC 13d0 0 xcb_present_query_capabilities_unchecked
PUBLIC 1438 0 xcb_present_query_capabilities_reply
PUBLIC 1440 0 xcb_present_redirect_notify_sizeof
PUBLIC 1450 0 xcb_present_redirect_notify_notifies
PUBLIC 1458 0 xcb_present_redirect_notify_notifies_length
PUBLIC 1470 0 xcb_present_redirect_notify_notifies_iterator
STACK CFI INIT d78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT da8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT de8 48 .cfa: sp 0 + .ra: x30
STACK CFI dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI df4 x19: .cfa -16 + ^
STACK CFI e2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e38 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT e58 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT e70 6c .cfa: sp 0 + .ra: x30
STACK CFI e74 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI e84 x19: .cfa -112 + ^
STACK CFI ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ed8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT ee0 6c .cfa: sp 0 + .ra: x30
STACK CFI ee4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ef4 x19: .cfa -112 + ^
STACK CFI f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f48 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT f50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f58 c .cfa: sp 0 + .ra: x30
STACK CFI INIT f68 bc .cfa: sp 0 + .ra: x30
STACK CFI f6c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI f78 x19: .cfa -192 + ^
STACK CFI 101c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1020 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 1028 bc .cfa: sp 0 + .ra: x30
STACK CFI 102c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 1038 x19: .cfa -192 + ^
STACK CFI 10dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10e0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 10e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1108 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1138 80 .cfa: sp 0 + .ra: x30
STACK CFI 113c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 114c x19: .cfa -128 + ^
STACK CFI 11b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11b4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 11b8 7c .cfa: sp 0 + .ra: x30
STACK CFI 11bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 11cc x19: .cfa -128 + ^
STACK CFI 122c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1230 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1238 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1258 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1270 78 .cfa: sp 0 + .ra: x30
STACK CFI 1274 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1284 x19: .cfa -112 + ^
STACK CFI 12e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 12e8 74 .cfa: sp 0 + .ra: x30
STACK CFI 12ec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 12fc x19: .cfa -112 + ^
STACK CFI 1354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1358 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1360 6c .cfa: sp 0 + .ra: x30
STACK CFI 1364 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1374 x19: .cfa -96 + ^
STACK CFI 13c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 13c8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 13d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 13d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 13e4 x19: .cfa -96 + ^
STACK CFI 1430 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1434 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1438 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1440 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1458 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1470 2c .cfa: sp 0 + .ra: x30
