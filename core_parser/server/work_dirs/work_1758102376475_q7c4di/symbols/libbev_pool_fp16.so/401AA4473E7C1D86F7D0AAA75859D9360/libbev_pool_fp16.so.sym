MODULE Linux arm64 401AA4473E7C1D86F7D0AAA75859D9360 libbev_pool_fp16.so
INFO CODE_ID 47A41A407C3E861DF7D0AAA75859D936
PUBLIC 3d88 0 _init
PUBLIC 4070 0 _GLOBAL__sub_I_trt_bev_pool.cpp
PUBLIC 40ec 0 call_weak_fn
PUBLIC 4100 0 deregister_tm_clones
PUBLIC 4130 0 register_tm_clones
PUBLIC 416c 0 __do_global_dtors_aux
PUBLIC 41bc 0 frame_dummy
PUBLIC 41c0 0 mmdeploy::TRTBEVPoolV2::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 4250 0 mmdeploy::TRTBEVPoolV2::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 42a0 0 mmdeploy::TRTBEVPoolV2::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 42b0 0 mmdeploy::TRTBEVPoolV2::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 42c0 0 mmdeploy::TRTBEVPoolV2::getPluginType() const
PUBLIC 42d0 0 mmdeploy::TRTBEVPoolV2::getPluginVersion() const
PUBLIC 42e0 0 mmdeploy::TRTBEVPoolV2::getNbOutputs() const
PUBLIC 42f0 0 mmdeploy::TRTBEVPoolV2::getSerializationSize() const
PUBLIC 4300 0 mmdeploy::TRTBEVPoolV2::serialize(void*) const
PUBLIC 4320 0 mmdeploy::(anonymous namespace)::logError(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4370 0 mmdeploy::TRTBEVPoolV2::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 44a0 0 mmdeploy::TRTBEVPoolV2::TRTBEVPoolV2(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, int)
PUBLIC 45d0 0 mmdeploy::TRTBEVPoolV2::clone() const
PUBLIC 4660 0 mmdeploy::TRTBEVPoolV2Creator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 4a40 0 mmdeploy::TRTBEVPoolV2::TRTBEVPoolV2(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, void const*, unsigned long)
PUBLIC 4b70 0 mmdeploy::TRTBEVPoolV2Creator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 4d10 0 mmdeploy::TRTBEVPoolV2Creator::TRTBEVPoolV2Creator()
PUBLIC 4e30 0 mmdeploy::TRTBEVPoolV2::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 5240 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 5250 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 5260 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 5270 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 5280 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 5290 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 52a0 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 52c0 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 52d0 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 52e0 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 52f0 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 5300 0 mmdeploy::TRTPluginBase::getPluginVersion() const
PUBLIC 5310 0 mmdeploy::TRTPluginBase::initialize()
PUBLIC 5320 0 mmdeploy::TRTPluginBase::terminate()
PUBLIC 5330 0 mmdeploy::TRTPluginBase::getPluginNamespace() const
PUBLIC 5340 0 mmdeploy::TRTPluginBase::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 5350 0 mmdeploy::TRTPluginBase::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 5360 0 mmdeploy::TRTPluginBase::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 5370 0 mmdeploy::TRTPluginBase::detachFromContext()
PUBLIC 5380 0 mmdeploy::TRTPluginCreatorBase::getPluginVersion() const
PUBLIC 5390 0 mmdeploy::TRTPluginCreatorBase::getFieldNames()
PUBLIC 53a0 0 mmdeploy::TRTPluginCreatorBase::getPluginNamespace() const
PUBLIC 53b0 0 mmdeploy::TRTBEVPoolV2::~TRTBEVPoolV2()
PUBLIC 5410 0 mmdeploy::TRTBEVPoolV2::~TRTBEVPoolV2()
PUBLIC 5470 0 mmdeploy::TRTPluginCreatorBase::setPluginNamespace(char const*)
PUBLIC 54b0 0 mmdeploy::TRTPluginBase::setPluginNamespace(char const*)
PUBLIC 54f0 0 mmdeploy::TRTPluginBase::destroy()
PUBLIC 5570 0 nvinfer1::PluginRegistrar<mmdeploy::TRTBEVPoolV2Creator>::~PluginRegistrar()
PUBLIC 55d0 0 mmdeploy::TRTBEVPoolV2Creator::~TRTBEVPoolV2Creator()
PUBLIC 5630 0 mmdeploy::TRTBEVPoolV2Creator::~TRTBEVPoolV2Creator()
PUBLIC 5690 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > __gnu_cxx::__to_xstring<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, char>(int (*)(char*, unsigned long, char const*, std::__va_list), unsigned long, char const*, ...)
PUBLIC 57e0 0 _fini
STACK CFI INIT 4100 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4130 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 416c 50 .cfa: sp 0 + .ra: x30
STACK CFI 417c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4184 x19: .cfa -16 + ^
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41bc 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5250 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5260 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5280 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5300 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5330 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5340 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5380 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c0 8c .cfa: sp 0 + .ra: x30
STACK CFI 41c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41d0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4250 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 42e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4300 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4320 44 .cfa: sp 0 + .ra: x30
STACK CFI 4324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 432c x19: .cfa -16 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 53b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53c8 x19: .cfa -16 + ^
STACK CFI 53fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5400 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5408 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5410 5c .cfa: sp 0 + .ra: x30
STACK CFI 5414 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5428 x19: .cfa -16 + ^
STACK CFI 5468 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5470 40 .cfa: sp 0 + .ra: x30
STACK CFI 5474 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 547c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54b0 40 .cfa: sp 0 + .ra: x30
STACK CFI 54b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 550c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5520 x19: .cfa -16 + ^
STACK CFI 5560 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5570 54 .cfa: sp 0 + .ra: x30
STACK CFI 5574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5588 x19: .cfa -16 + ^
STACK CFI 55b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 55b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 55c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55d0 54 .cfa: sp 0 + .ra: x30
STACK CFI 55d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55e8 x19: .cfa -16 + ^
STACK CFI 5614 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5618 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5620 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5630 54 .cfa: sp 0 + .ra: x30
STACK CFI 5634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5648 x19: .cfa -16 + ^
STACK CFI 5680 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4370 12c .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 438c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4390 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43ec x19: x19 x20: x20
STACK CFI 43f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 44a0 12c .cfa: sp 0 + .ra: x30
STACK CFI 44a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 44b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 44bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 44d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4584 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 45d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 45d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4648 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4660 3dc .cfa: sp 0 + .ra: x30
STACK CFI 4664 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 466c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4688 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 489c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 48a0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 4978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 497c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4a40 124 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4a5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4a6c x23: .cfa -32 + ^
STACK CFI 4b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4b1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4b70 198 .cfa: sp 0 + .ra: x30
STACK CFI 4b74 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4b7c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4b88 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4b90 x25: .cfa -64 + ^
STACK CFI 4c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4c7c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 4d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 4d10 120 .cfa: sp 0 + .ra: x30
STACK CFI 4d14 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4d2c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4df4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 4dfc x21: .cfa -96 + ^
STACK CFI INIT 5690 150 .cfa: sp 0 + .ra: x30
STACK CFI 5694 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 56a0 .cfa: x29 304 +
STACK CFI 56b8 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 56d0 x21: .cfa -272 + ^
STACK CFI 5760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5764 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 5784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5788 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4e30 408 .cfa: sp 0 + .ra: x30
STACK CFI 4e34 .cfa: sp 688 +
STACK CFI 4e38 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 4e40 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 4e6c x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 4e8c x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 4e94 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 5034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5038 .cfa: sp 688 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 5150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5154 .cfa: sp 688 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI INIT 4070 7c .cfa: sp 0 + .ra: x30
STACK CFI 4074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 407c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
