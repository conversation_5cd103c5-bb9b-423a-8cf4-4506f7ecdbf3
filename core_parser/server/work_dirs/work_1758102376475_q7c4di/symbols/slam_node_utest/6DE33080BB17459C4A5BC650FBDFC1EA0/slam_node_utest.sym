MODULE Linux arm64 6DE33080BB17459C4A5BC650FBDFC1EA0 slam_node_utest
INFO CODE_ID 8030E36D17BB9C454A5BC650FBDFC1EA
PUBLIC 2830 0 _init
PUBLIC 2bb0 0 std::default_delete<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >::operator()(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >*) const [clone .isra.0]
PUBLIC 2bf0 0 main
PUBLIC 2c20 0 _GLOBAL__sub_I__ZN14Queue_ops_Test10test_info_E
PUBLIC 2fa0 0 _start
PUBLIC 2ff0 0 call_weak_fn
PUBLIC 3004 0 deregister_tm_clones
PUBLIC 3048 0 register_tm_clones
PUBLIC 3098 0 __do_global_dtors_aux
PUBLIC 30c8 0 frame_dummy
PUBLIC 30d0 0 Queue_ops_Test::TestBody()
PUBLIC 3300 0 testing::Test::Setup()
PUBLIC 3310 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::~TestFactoryImpl()
PUBLIC 3320 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::~TestFactoryImpl()
PUBLIC 3330 0 Queue_ops_Test::~Queue_ops_Test()
PUBLIC 3340 0 Queue_ops_Test::~Queue_ops_Test()
PUBLIC 3380 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::CreateTest()
PUBLIC 33d0 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 3480 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*)
PUBLIC 34d0 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 3600 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > testing::PrintToString<int>(int const&)
PUBLIC 38f0 0 testing::AssertionResult testing::internal::CmpHelperOpFailure<int, int>(char const*, char const*, int const&, int const&, char const*)
PUBLIC 4460 0 __libc_csu_init
PUBLIC 44e0 0 __libc_csu_fini
PUBLIC 44e4 0 _fini
STACK CFI INIT 3004 44 .cfa: sp 0 + .ra: x30
STACK CFI 3020 .cfa: sp 16 +
STACK CFI 3038 .cfa: sp 0 +
STACK CFI 303c .cfa: sp 16 +
STACK CFI 3040 .cfa: sp 0 +
STACK CFI INIT 3048 50 .cfa: sp 0 + .ra: x30
STACK CFI 3070 .cfa: sp 16 +
STACK CFI 3088 .cfa: sp 0 +
STACK CFI 308c .cfa: sp 16 +
STACK CFI 3090 .cfa: sp 0 +
STACK CFI INIT 3098 30 .cfa: sp 0 + .ra: x30
STACK CFI 309c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a4 x19: .cfa -16 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3300 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3320 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3330 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3340 34 .cfa: sp 0 + .ra: x30
STACK CFI 3344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3354 x19: .cfa -16 + ^
STACK CFI 3370 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3380 50 .cfa: sp 0 + .ra: x30
STACK CFI 3384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3390 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 33b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2bb0 40 .cfa: sp 0 + .ra: x30
STACK CFI 2bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bc4 x19: .cfa -16 + ^
STACK CFI 2be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bf0 24 .cfa: sp 0 + .ra: x30
STACK CFI 2bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 33d0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 33d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3404 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 347c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3480 44 .cfa: sp 0 + .ra: x30
STACK CFI 3488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3490 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 34d0 12c .cfa: sp 0 + .ra: x30
STACK CFI 34d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 34dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3510 x21: x21 x22: x22
STACK CFI 351c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3520 x23: .cfa -16 + ^
STACK CFI 35bc x21: x21 x22: x22
STACK CFI 35c0 x23: x23
STACK CFI 35ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 35f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3600 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 3604 .cfa: sp 544 +
STACK CFI 3608 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 3610 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 361c x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 3630 x21: .cfa -512 + ^ x22: .cfa -504 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 3804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3808 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI INIT 38f0 b70 .cfa: sp 0 + .ra: x30
STACK CFI 38f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 38fc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3908 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 3914 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3920 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3f44 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2c20 380 .cfa: sp 0 + .ra: x30
STACK CFI 2c24 .cfa: sp 160 +
STACK CFI 2c28 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2c30 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2c4c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 2de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2de4 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 30d0 228 .cfa: sp 0 + .ra: x30
STACK CFI 30d4 .cfa: sp 624 +
STACK CFI 30dc .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 30e4 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 31a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31a8 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x29: .cfa -624 + ^
STACK CFI INIT 4460 7c .cfa: sp 0 + .ra: x30
STACK CFI 4464 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 446c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4478 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 448c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 44d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 44e0 4 .cfa: sp 0 + .ra: x30
