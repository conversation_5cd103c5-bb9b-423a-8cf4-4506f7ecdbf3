MODULE Linux arm64 6EF974BC651CC37F73F90AD1AC3F11770 libnvstream_core_buf.so
INFO CODE_ID BC74F96E1C657FC373F90AD1AC3F1177
PUBLIC 4e00 0 _init
PUBLIC 5470 0 _GLOBAL__sub_I_buf_alloc.cpp
PUBLIC 566c 0 call_weak_fn
PUBLIC 5680 0 deregister_tm_clones
PUBLIC 56b0 0 register_tm_clones
PUBLIC 56ec 0 __do_global_dtors_aux
PUBLIC 573c 0 frame_dummy
PUBLIC 5740 0 linvs::buf::SetC2cBufAttrList(linvs::buf::BufAttrList&)
PUBLIC 58a0 0 linvs::buf::GetBaseBufAttrList(linvs::buf::BufAttrList&, linvs::buf::BaseBufDesc const&)
PUBLIC 5a20 0 linvs::buf::AllocBuf(std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >&, unsigned int, linvs::buf::AllocParams const&)
PUBLIC 5c60 0 std::vector<int, std::allocator<int> >::~vector()
PUBLIC 5c70 0 std::unordered_map<linvs::buf::BufAccessPerm, NvSciBufAttrValAccessPerm, std::hash<linvs::buf::BufAccessPerm>, std::equal_to<linvs::buf::BufAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> > >::~unordered_map()
PUBLIC 5ce0 0 std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >::_M_default_append(unsigned long)
PUBLIC 5eb0 0 void std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >::_M_realloc_insert<linvs::buf::BufAttrList const*>(__gnu_cxx::__normal_iterator<linvs::buf::BufAttrList const**, std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > >, linvs::buf::BufAttrList const*&&)
PUBLIC 5fe0 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 6110 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_insert_unique_node(unsigned long, unsigned long, std::__detail::_Hash_node<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, false>*, unsigned long)
PUBLIC 6220 0 linvs::buf::BufAttrList::BufAttrList()
PUBLIC 6310 0 linvs::buf::BufAttrList::BufAttrList(linvs::buf::BufAttrList const&)
PUBLIC 6410 0 linvs::buf::BufAttrList::operator=(linvs::buf::BufAttrList const&)
PUBLIC 6540 0 linvs::buf::BufAttrList::operator*() const
PUBLIC 6550 0 linvs::buf::BufAttrList::operator*()
PUBLIC 6560 0 linvs::buf::BufAttrList::operator bool() const
PUBLIC 6580 0 linvs::buf::BufAttrList::~BufAttrList()
PUBLIC 6640 0 linvs::buf::BufAttrList::AllocBufObj()
PUBLIC 66e0 0 linvs::buf::BufAttrList::AllocBufObj(linvs::buf::BufObj&)
PUBLIC 6750 0 linvs::buf::BufAttrList::SetAttrs(NvSciBufAttrKeyValuePair*, unsigned long)
PUBLIC 67a0 0 linvs::buf::BufAttrList::GetSlotCount()
PUBLIC 67b0 0 linvs::buf::BufAttrList::GetAttrs(NvSciBufAttrKeyValuePair*, unsigned long) const
PUBLIC 6800 0 linvs::buf::BufAttrList::GetSlotAttrs(unsigned long, NvSciBufAttrKeyValuePair*, unsigned long)
PUBLIC 6850 0 linvs::buf::BufAttrList::AppendUnreconciled(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >)
PUBLIC 6960 0 linvs::buf::BufAttrList::Reconciled()
PUBLIC 69c0 0 linvs::buf::BufAttrList::ValidateReconciled(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > const&)
PUBLIC 6af0 0 linvs::buf::BufAttrList::Reconcile(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > const&, linvs::buf::BufAttrList&)
PUBLIC 6c40 0 linvs::buf::BufAttrList::SerialBufAttrValuesToStr(linvs::buf::BufAttrValues const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&)
PUBLIC 7720 0 linvs::buf::BufAttrList::BufAttrList(NvSciBufAttrListRec* const&)
PUBLIC 7840 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<linvs::buf::BufAttrList::NvSciBufAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 7850 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<linvs::buf::BufAttrList::NvSciBufAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 78b0 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<linvs::buf::BufAttrList::NvSciBufAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 78d0 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<linvs::buf::BufAttrList::NvSciBufAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 78e0 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<linvs::buf::BufAttrList::NvSciBufAttrListWrapper>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 78f0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 79b0 0 linvs::buf::BufModule::BufModule()
PUBLIC 79f0 0 linvs::buf::BufModule::operator bool()
PUBLIC 7a00 0 linvs::buf::BufModule::operator*()
PUBLIC 7a10 0 linvs::buf::BufModule::operator*() const
PUBLIC 7a20 0 linvs::buf::BufModule::~BufModule()
PUBLIC 7a40 0 linvs::buf::BufModule::CreateAttrList()
PUBLIC 7ae0 0 linvs::buf::BufModule::CreateAttrList(linvs::buf::BufAttrList&)
PUBLIC 7b50 0 linvs::buf::BufObj::GetAttrList(NvSciBufAttrListRec*&) const
PUBLIC 7ba0 0 linvs::buf::BufObj::GetAttrs(NvSciBufAttrKeyValuePair*, unsigned long) const
PUBLIC 7c20 0 linvs::buf::BufObj::BufObj(NvSciBufObjRefRec* const&)
PUBLIC 7c30 0 linvs::buf::BufObj::operator*() const
PUBLIC 7c40 0 linvs::buf::BufObj::operator*()
PUBLIC 7c50 0 linvs::buf::BufObj::operator bool() const
PUBLIC 7c60 0 linvs::buf::BufObj::operator==(linvs::buf::BufObj const&)
PUBLIC 7ca0 0 linvs::buf::BufObj::operator==(NvSciBufObjRefRec* const&)
PUBLIC 7cc0 0 linvs::buf::BufObj::operator!=(linvs::buf::BufObj const&)
PUBLIC 7d00 0 linvs::buf::BufObj::operator!=(NvSciBufObjRefRec* const&)
PUBLIC 7d20 0 linvs::buf::BufObj::Reset()
PUBLIC 7d50 0 linvs::buf::BufObj::~BufObj()
PUBLIC 7d70 0 linvs::buf::BufObj::DeepClone()
PUBLIC 7e10 0 linvs::buf::BufObj::DupFrom(linvs::buf::BufObj const&)
PUBLIC 7e70 0 linvs::buf::BufObj::BufObj(linvs::buf::BufObj const&)
PUBLIC 7ec0 0 linvs::buf::BufObj::operator=(linvs::buf::BufObj const&)
PUBLIC 7f20 0 linvs::buf::BufObj::DupFrom(NvSciBufObjRefRec* const&)
PUBLIC 7f80 0 linvs::buf::BufObj::FlushCpuCache() const
PUBLIC 8040 0 linvs::buf::BufObj::GetPtr() const
PUBLIC 80b0 0 linvs::buf::BufObj::CpuWrite(void const*, int)
PUBLIC 80f0 0 linvs::buf::BufObj::GetConstPtr() const
PUBLIC 8160 0 linvs::buf::BufObj::DumpAttrInfo(linvs::buf::BufAttrValues&) const
PUBLIC 89e0 0 linvs::buf::BufObj::DeepCopy(linvs::buf::BufObj&)
PUBLIC 8ba0 0 linvs::buf::BufObj::GetPixels(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC 9160 0 linvs::buf::BufObj::PutPixels(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > > const&)
PUBLIC 9700 0 linvs::buf::BufObj::AllocPlanesMem(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC 9b80 0 linvs::buf::BufObj::GetPixelsAlloc(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC 9bc0 0 linvs::buf::BufAttrValues::~BufAttrValues()
PUBLIC 9cb0 0 std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> >::_M_default_append(unsigned long)
PUBLIC 9dd0 0 std::vector<unsigned int, std::allocator<unsigned int> >::_M_default_append(unsigned long)
PUBLIC 9ef0 0 std::vector<unsigned long, std::allocator<unsigned long> >::_M_default_append(unsigned long)
PUBLIC a010 0 std::vector<NvSciBufAttrValColorStd, std::allocator<NvSciBufAttrValColorStd> >::_M_default_append(unsigned long)
PUBLIC a130 0 std::vector<unsigned char, std::allocator<unsigned char> >::_M_default_append(unsigned long)
PUBLIC a270 0 void std::vector<NvSciBufType, std::allocator<NvSciBufType> >::_M_realloc_insert<NvSciBufType const&>(__gnu_cxx::__normal_iterator<NvSciBufType*, std::vector<NvSciBufType, std::allocator<NvSciBufType> > >, NvSciBufType const&)
PUBLIC a3a0 0 std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >::_M_default_append(unsigned long)
PUBLIC a688 0 _fini
STACK CFI INIT 5680 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 56ec 50 .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5704 x19: .cfa -16 + ^
STACK CFI 5734 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 573c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c70 64 .cfa: sp 0 + .ra: x30
STACK CFI 5c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5740 154 .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5750 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5760 x23: .cfa -64 + ^
STACK CFI 57b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 57bc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5ce0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 5ce8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5cf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5cf8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5d14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5d58 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 5d5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5d64 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5e10 x27: x27 x28: x28
STACK CFI 5e30 x25: x25 x26: x26
STACK CFI 5e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5e38 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5eb0 128 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5ec4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5ed8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5f68 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5fe0 124 .cfa: sp 0 + .ra: x30
STACK CFI 5fe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ff0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ffc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 609c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6110 10c .cfa: sp 0 + .ra: x30
STACK CFI 6114 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 611c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6128 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 61a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 58a0 180 .cfa: sp 0 + .ra: x30
STACK CFI 58a4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 58b0 x23: .cfa -176 + ^
STACK CFI 58bc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 58c4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 59e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 59e8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 5a20 234 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5a2c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5a34 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5a40 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5a74 x25: .cfa -80 + ^
STACK CFI 5b64 x25: x25
STACK CFI 5b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5b84 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 5b8c x25: x25
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ba8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 5bf8 x25: x25
STACK CFI 5c08 x25: .cfa -80 + ^
STACK CFI INIT 5470 1fc .cfa: sp 0 + .ra: x30
STACK CFI 5474 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5484 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 54a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 55b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 55bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7850 60 .cfa: sp 0 + .ra: x30
STACK CFI 7854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7864 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 78b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 78bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 78c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 78d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6220 ec .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 622c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6298 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 62f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6310 fc .cfa: sp 0 + .ra: x30
STACK CFI 6314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 631c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6334 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6380 x21: x21 x22: x22
STACK CFI 638c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6390 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6410 124 .cfa: sp 0 + .ra: x30
STACK CFI 6414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6420 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6430 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64dc x21: x21 x22: x22
STACK CFI 64ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 64fc x21: x21 x22: x22
STACK CFI 6500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6540 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6550 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6560 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6580 bc .cfa: sp 0 + .ra: x30
STACK CFI 6584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 658c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 662c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6640 94 .cfa: sp 0 + .ra: x30
STACK CFI 6648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6658 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6688 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 66b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 66e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66f8 x19: .cfa -16 + ^
STACK CFI 671c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6720 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6750 50 .cfa: sp 0 + .ra: x30
STACK CFI 6754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6770 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6774 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 67a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 67b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 67b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6800 50 .cfa: sp 0 + .ra: x30
STACK CFI 6804 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6820 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6824 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6850 104 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 685c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6868 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 690c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6910 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6960 5c .cfa: sp 0 + .ra: x30
STACK CFI 6964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 698c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 69c0 12c .cfa: sp 0 + .ra: x30
STACK CFI 69c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 69cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 69d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 6a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6a8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 6ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ad4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6af0 14c .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6afc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b08 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 6be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6be8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6c40 ae0 .cfa: sp 0 + .ra: x30
STACK CFI 6c44 .cfa: sp 592 +
STACK CFI 6c48 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 6c50 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 6c5c x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 6c70 x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 758c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7590 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^ x29: .cfa -592 + ^
STACK CFI INIT 78f0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7934 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 798c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7998 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7720 11c .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 772c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7738 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 77ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 77b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 79b0 40 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 79c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 79c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 79d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 79f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a20 18 .cfa: sp 0 + .ra: x30
STACK CFI 7a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7a40 94 .cfa: sp 0 + .ra: x30
STACK CFI 7a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7abc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ae0 68 .cfa: sp 0 + .ra: x30
STACK CFI 7ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7af4 x19: .cfa -16 + ^
STACK CFI 7b18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b50 4c .cfa: sp 0 + .ra: x30
STACK CFI 7b54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7b6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7ba0 7c .cfa: sp 0 + .ra: x30
STACK CFI 7ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7be8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7c20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c60 34 .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c74 x19: .cfa -16 + ^
STACK CFI 7c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7ca0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cc0 34 .cfa: sp 0 + .ra: x30
STACK CFI 7cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7cd4 x19: .cfa -16 + ^
STACK CFI 7cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bc0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 9bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9bcc x19: .cfa -16 + ^
STACK CFI 9c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d20 2c .cfa: sp 0 + .ra: x30
STACK CFI 7d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d2c x19: .cfa -16 + ^
STACK CFI 7d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d50 14 .cfa: sp 0 + .ra: x30
STACK CFI 7d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d70 98 .cfa: sp 0 + .ra: x30
STACK CFI 7d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7da0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7df0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e10 60 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e1c x19: .cfa -16 + ^
STACK CFI 7e40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7e58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e70 48 .cfa: sp 0 + .ra: x30
STACK CFI 7e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ec0 5c .cfa: sp 0 + .ra: x30
STACK CFI 7ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ed0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f20 54 .cfa: sp 0 + .ra: x30
STACK CFI 7f28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7f48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7f80 b8 .cfa: sp 0 + .ra: x30
STACK CFI 7f8c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7fb0 x19: .cfa -64 + ^
STACK CFI 8000 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8004 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8040 64 .cfa: sp 0 + .ra: x30
STACK CFI 8044 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 804c x19: .cfa -32 + ^
STACK CFI 8074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8078 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 80b0 40 .cfa: sp 0 + .ra: x30
STACK CFI 80b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 80bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 80e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 80f0 64 .cfa: sp 0 + .ra: x30
STACK CFI 80f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 80fc x19: .cfa -32 + ^
STACK CFI 8124 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8128 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9cb0 114 .cfa: sp 0 + .ra: x30
STACK CFI 9cb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9cc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9cc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9cd4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9d28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9dd0 114 .cfa: sp 0 + .ra: x30
STACK CFI 9dd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9de0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9de8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9df4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9e48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9ef0 114 .cfa: sp 0 + .ra: x30
STACK CFI 9ef8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9f08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9f14 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9f68 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9fe0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a010 114 .cfa: sp 0 + .ra: x30
STACK CFI a018 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a020 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a028 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a034 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a088 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a0fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a100 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a130 13c .cfa: sp 0 + .ra: x30
STACK CFI a138 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a140 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a148 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a194 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a198 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a208 x23: x23 x24: x24
STACK CFI a20c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a214 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a270 128 .cfa: sp 0 + .ra: x30
STACK CFI a274 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a284 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a298 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a328 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8160 880 .cfa: sp 0 + .ra: x30
STACK CFI 8164 .cfa: sp 848 +
STACK CFI 8174 .ra: .cfa -840 + ^ x29: .cfa -848 + ^
STACK CFI 817c x19: .cfa -832 + ^ x20: .cfa -824 + ^
STACK CFI 86c4 x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 870c x21: x21 x22: x22
STACK CFI 8740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8744 .cfa: sp 848 + .ra: .cfa -840 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x29: .cfa -848 + ^
STACK CFI 8768 x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 8790 x21: x21 x22: x22
STACK CFI 87b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87b8 .cfa: sp 848 + .ra: .cfa -840 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x29: .cfa -848 + ^
STACK CFI INIT 89e0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 89e8 .cfa: sp 528 +
STACK CFI 89f0 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 89f8 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 8a04 x21: .cfa -496 + ^
STACK CFI 8b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8b70 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x29: .cfa -528 + ^
STACK CFI INIT 8ba0 5b4 .cfa: sp 0 + .ra: x30
STACK CFI 8ba8 .cfa: sp 592 +
STACK CFI 8bb0 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 8bb8 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 8bc4 x21: .cfa -560 + ^
STACK CFI 8ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8ea4 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x29: .cfa -592 + ^
STACK CFI INIT 9160 59c .cfa: sp 0 + .ra: x30
STACK CFI 9168 .cfa: sp 592 +
STACK CFI 9170 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 9178 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 9184 x21: .cfa -560 + ^
STACK CFI 9450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9454 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x29: .cfa -592 + ^
STACK CFI INIT a3a0 2e8 .cfa: sp 0 + .ra: x30
STACK CFI a3a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a3b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a3c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a49c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a4a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a634 x23: x23 x24: x24
STACK CFI a638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a63c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a648 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 9700 474 .cfa: sp 0 + .ra: x30
STACK CFI 9708 .cfa: sp 544 +
STACK CFI 9710 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 9718 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 9720 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 972c x23: .cfa -496 + ^
STACK CFI 99d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 99dc .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x29: .cfa -544 + ^
STACK CFI INIT 9b80 40 .cfa: sp 0 + .ra: x30
STACK CFI 9b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9b8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
