MODULE Linux arm64 14AC8C064BE6A6C54491A61AA213D68E0 libGL.so.1
INFO CODE_ID 068CAC14E64BC5A64491A61AA213D68ED8387F8D
PUBLIC 42c18 0 glXAllocateMemoryNV
PUBLIC 42c98 0 glXBindChannelToWindowSGIX
PUBLIC 42d18 0 glXBindHyperpipeSGIX
PUBLIC 42d78 0 glXBindSwapBarrierNV
PUBLIC 42df0 0 glXBindSwapBarrierSGIX
PUBLIC 42e60 0 glXBindTexImageEXT
PUBLIC 42ed8 0 glXBindVideoCaptureDeviceNV
PUBLIC 42f50 0 glXBindVideoDeviceNV
PUBLIC 42fd0 0 glXBindVideoImageNV
PUBLIC 43050 0 glXBlitContextFramebufferAMD
PUBLIC 43128 0 glXChannelRectSGIX
PUBLIC 431d8 0 glXChannelRectSyncSGIX
PUBLIC 43258 0 glXChooseFBConfig
PUBLIC 43270 0 glXChooseFBConfigSGIX
PUBLIC 432e8 0 glXChooseVisual
PUBLIC 43300 0 glXCopyBufferSubDataNV
PUBLIC 433b0 0 glXCopyContext
PUBLIC 433c8 0 glXCopyImageSubDataNV
PUBLIC 434d0 0 glXCopySubBufferMESA
PUBLIC 43568 0 glXCreateAssociatedContextAMD
PUBLIC 435c8 0 glXCreateAssociatedContextAttribsAMD
PUBLIC 43638 0 glXCreateContext
PUBLIC 43650 0 glXCreateContextAttribsARB
PUBLIC 436e0 0 glXCreateContextWithConfigSGIX
PUBLIC 43770 0 glXCreateGLXPbufferSGIX
PUBLIC 43800 0 glXCreateGLXPixmap
PUBLIC 43818 0 glXCreateGLXPixmapMESA
PUBLIC 43890 0 glXCreateGLXPixmapWithConfigSGIX
PUBLIC 43900 0 glXCreateNewContext
PUBLIC 43918 0 glXCreatePbuffer
PUBLIC 43930 0 glXCreatePixmap
PUBLIC 43948 0 glXCreateWindow
PUBLIC 43960 0 glXCushionSGI
PUBLIC 439d0 0 glXDelayBeforeSwapNV
PUBLIC 43a48 0 glXDeleteAssociatedContextAMD
PUBLIC 43aa0 0 glXDestroyContext
PUBLIC 43ab8 0 glXDestroyGLXPbufferSGIX
PUBLIC 43b18 0 glXDestroyGLXPixmap
PUBLIC 43b30 0 glXDestroyHyperpipeConfigSGIX
PUBLIC 43b90 0 glXDestroyPbuffer
PUBLIC 43ba8 0 glXDestroyPixmap
PUBLIC 43bc0 0 glXDestroyWindow
PUBLIC 43bd8 0 glXEnumerateVideoCaptureDevicesNV
PUBLIC 43c48 0 glXEnumerateVideoDevicesNV
PUBLIC 43cb8 0 glXFreeContextEXT
PUBLIC 43d18 0 glXFreeMemoryNV
PUBLIC 43d70 0 glXGetAGPOffsetMESA
PUBLIC 43dc8 0 glXGetClientString
PUBLIC 43de0 0 glXGetConfig
PUBLIC 43df8 0 glXGetContextGPUIDAMD
PUBLIC 43e50 0 glXGetContextIDEXT
PUBLIC 43ea8 0 glXGetCurrentAssociatedContextAMD
PUBLIC 43ee8 0 glXGetCurrentContext
PUBLIC 43f00 0 glXGetCurrentDisplay
PUBLIC 43f40 0 glXGetCurrentDisplayEXT
PUBLIC 43f80 0 glXGetCurrentDrawable
PUBLIC 43f98 0 glXGetCurrentReadDrawable
PUBLIC 43fb0 0 glXGetCurrentReadDrawableSGI
PUBLIC 43ff0 0 glXGetDriverConfig
PUBLIC 44048 0 glXGetFBConfigAttrib
PUBLIC 44060 0 glXGetFBConfigAttribSGIX
PUBLIC 440e0 0 glXGetFBConfigFromVisualSGIX
PUBLIC 44140 0 glXGetFBConfigs
PUBLIC 44158 0 glXGetGPUIDsAMD
PUBLIC 441b8 0 glXGetGPUInfoAMD
PUBLIC 44248 0 glXGetMscRateOML
PUBLIC 442c8 0 glXGetProcAddress
PUBLIC 442e0 0 glXGetProcAddressARB
PUBLIC 442f8 0 glXGetRefreshRateSGI
PUBLIC 44350 0 glXGetScreenDriver
PUBLIC 443b0 0 glXGetSelectedEvent
PUBLIC 443c8 0 glXGetSelectedEventSGIX
PUBLIC 44438 0 glXGetSwapIntervalMESA
PUBLIC 44478 0 glXGetSyncValuesOML
PUBLIC 44508 0 glXGetTransparentIndexSUN
PUBLIC 44588 0 glXGetVideoDeviceNV
PUBLIC 44608 0 glXGetVideoInfoNV
PUBLIC 44698 0 glXGetVideoSyncSGI
PUBLIC 446f0 0 glXGetVisualFromFBConfig
PUBLIC 44708 0 glXGetVisualFromFBConfigSGIX
PUBLIC 44768 0 glXHyperpipeAttribSGIX
PUBLIC 447f8 0 glXHyperpipeConfigSGIX
PUBLIC 44888 0 glXImportContextEXT
PUBLIC 448e8 0 glXIsDirect
PUBLIC 44900 0 glXJoinSwapGroupNV
PUBLIC 44978 0 glXJoinSwapGroupSGIX
PUBLIC 449e8 0 glXLockVideoCaptureDeviceNV
PUBLIC 44a48 0 glXMakeAssociatedContextCurrentAMD
PUBLIC 44aa0 0 glXMakeContextCurrent
PUBLIC 44ab8 0 glXMakeCurrent
PUBLIC 44ad0 0 glXMakeCurrentReadSGI
PUBLIC 44b50 0 glXNamedCopyBufferSubDataNV
PUBLIC 44c00 0 glXQueryChannelDeltasSGIX
PUBLIC 44cb0 0 glXQueryChannelRectSGIX
PUBLIC 44d60 0 glXQueryContext
PUBLIC 44d78 0 glXQueryContextInfoEXT
PUBLIC 44df8 0 glXQueryCurrentRendererIntegerMESA
PUBLIC 44e58 0 glXQueryCurrentRendererStringMESA
PUBLIC 44eb0 0 glXQueryDrawable
PUBLIC 44ec8 0 glXQueryExtension
PUBLIC 44ee0 0 glXQueryExtensionsString
PUBLIC 44ef8 0 glXQueryFrameCountNV
PUBLIC 44f70 0 glXQueryGLXPbufferSGIX
PUBLIC 44fe8 0 glXQueryHyperpipeAttribSGIX
PUBLIC 45078 0 glXQueryHyperpipeBestAttribSGIX
PUBLIC 45110 0 glXQueryHyperpipeConfigSGIX
PUBLIC 45188 0 glXQueryHyperpipeNetworkSGIX
PUBLIC 451e8 0 glXQueryMaxSwapBarriersSGIX
PUBLIC 45260 0 glXQueryMaxSwapGroupsNV
PUBLIC 452e0 0 glXQueryRendererIntegerMESA
PUBLIC 45370 0 glXQueryRendererStringMESA
PUBLIC 453e8 0 glXQueryServerString
PUBLIC 45400 0 glXQuerySwapGroupNV
PUBLIC 45480 0 glXQueryVersion
PUBLIC 45498 0 glXQueryVideoCaptureDeviceNV
PUBLIC 45518 0 glXReleaseBuffersMESA
PUBLIC 45578 0 glXReleaseTexImageEXT
PUBLIC 455e8 0 glXReleaseVideoCaptureDeviceNV
PUBLIC 45648 0 glXReleaseVideoDeviceNV
PUBLIC 456c0 0 glXReleaseVideoImageNV
PUBLIC 45720 0 glXResetFrameCountNV
PUBLIC 45780 0 glXSelectEvent
PUBLIC 45798 0 glXSelectEventSGIX
PUBLIC 45808 0 glXSendPbufferToVideoNV
PUBLIC 45898 0 glXSet3DfxModeMESA
PUBLIC 458f0 0 glXSwapBuffers
PUBLIC 45908 0 glXSwapBuffersMscOML
PUBLIC 45998 0 glXSwapIntervalEXT
PUBLIC 45a08 0 glXSwapIntervalMESA
PUBLIC 45a60 0 glXSwapIntervalSGI
PUBLIC 45ab8 0 glXUseXFont
PUBLIC 45ad0 0 glXWaitForMscOML
PUBLIC 45b88 0 glXWaitForSbcOML
PUBLIC 45c20 0 glXWaitGL
PUBLIC 45c38 0 glXWaitVideoSyncSGI
PUBLIC 45cb0 0 glXWaitX
PUBLIC 50000 0 glAccum
PUBLIC 50080 0 glAccumxOES
PUBLIC 50100 0 glAcquireKeyedMutexWin32EXT
PUBLIC 50180 0 glActiveProgramEXT
PUBLIC 50200 0 glActiveShaderProgram
PUBLIC 50280 0 glActiveShaderProgramEXT
PUBLIC 50300 0 glActiveStencilFaceEXT
PUBLIC 50380 0 glActiveTexture
PUBLIC 50400 0 glActiveTextureARB
PUBLIC 50480 0 glActiveVaryingNV
PUBLIC 50500 0 glAlphaFragmentOp1ATI
PUBLIC 50580 0 glAlphaFragmentOp2ATI
PUBLIC 50600 0 glAlphaFragmentOp3ATI
PUBLIC 50680 0 glAlphaFunc
PUBLIC 50700 0 glAlphaFuncQCOM
PUBLIC 50780 0 glAlphaFuncx
PUBLIC 50800 0 glAlphaFuncxOES
PUBLIC 50880 0 glAlphaToCoverageDitherControlNV
PUBLIC 50900 0 glApplyFramebufferAttachmentCMAAINTEL
PUBLIC 50980 0 glApplyTextureEXT
PUBLIC 50a00 0 glAreProgramsResidentNV
PUBLIC 50a80 0 glAreTexturesResident
PUBLIC 50b00 0 glAreTexturesResidentEXT
PUBLIC 50b80 0 glArrayElement
PUBLIC 50c00 0 glArrayElementEXT
PUBLIC 50c80 0 glArrayObjectATI
PUBLIC 50d00 0 glAsyncCopyBufferSubDataNVX
PUBLIC 50d80 0 glAsyncCopyImageSubDataNVX
PUBLIC 50e00 0 glAsyncMarkerSGIX
PUBLIC 50e80 0 glAttachObjectARB
PUBLIC 50f00 0 glAttachShader
PUBLIC 50f80 0 glBegin
PUBLIC 51000 0 glBeginConditionalRender
PUBLIC 51080 0 glBeginConditionalRenderNV
PUBLIC 51100 0 glBeginConditionalRenderNVX
PUBLIC 51180 0 glBeginFragmentShaderATI
PUBLIC 51200 0 glBeginOcclusionQueryNV
PUBLIC 51280 0 glBeginPerfMonitorAMD
PUBLIC 51300 0 glBeginPerfQueryINTEL
PUBLIC 51380 0 glBeginQuery
PUBLIC 51400 0 glBeginQueryARB
PUBLIC 51480 0 glBeginQueryEXT
PUBLIC 51500 0 glBeginQueryIndexed
PUBLIC 51580 0 glBeginTransformFeedback
PUBLIC 51600 0 glBeginTransformFeedbackEXT
PUBLIC 51680 0 glBeginTransformFeedbackNV
PUBLIC 51700 0 glBeginVertexShaderEXT
PUBLIC 51780 0 glBeginVideoCaptureNV
PUBLIC 51800 0 glBindANCCaptureStreamBufferNVX
PUBLIC 51880 0 glBindAttribLocation
PUBLIC 51900 0 glBindAttribLocationARB
PUBLIC 51980 0 glBindBuffer
PUBLIC 51a00 0 glBindBufferARB
PUBLIC 51a80 0 glBindBufferBase
PUBLIC 51b00 0 glBindBufferBaseEXT
PUBLIC 51b80 0 glBindBufferBaseNV
PUBLIC 51c00 0 glBindBufferOffsetEXT
PUBLIC 51c80 0 glBindBufferOffsetNV
PUBLIC 51d00 0 glBindBufferRange
PUBLIC 51d80 0 glBindBufferRangeEXT
PUBLIC 51e00 0 glBindBufferRangeNV
PUBLIC 51e80 0 glBindBuffersBase
PUBLIC 51f00 0 glBindBuffersRange
PUBLIC 51f80 0 glBindFragDataLocation
PUBLIC 52000 0 glBindFragDataLocationEXT
PUBLIC 52080 0 glBindFragDataLocationIndexed
PUBLIC 52100 0 glBindFragDataLocationIndexedEXT
PUBLIC 52180 0 glBindFragmentShaderATI
PUBLIC 52200 0 glBindFramebuffer
PUBLIC 52280 0 glBindFramebufferEXT
PUBLIC 52300 0 glBindFramebufferOES
PUBLIC 52380 0 glBindImageTexture
PUBLIC 52400 0 glBindImageTextureEXT
PUBLIC 52480 0 glBindImageTextures
PUBLIC 52500 0 glBindLightParameterEXT
PUBLIC 52580 0 glBindMaterialParameterEXT
PUBLIC 52600 0 glBindMultiTextureEXT
PUBLIC 52680 0 glBindParameterEXT
PUBLIC 52700 0 glBindProgramARB
PUBLIC 52780 0 glBindProgramNV
PUBLIC 52800 0 glBindProgramPipeline
PUBLIC 52880 0 glBindProgramPipelineEXT
PUBLIC 52900 0 glBindRenderbuffer
PUBLIC 52980 0 glBindRenderbufferEXT
PUBLIC 52a00 0 glBindRenderbufferOES
PUBLIC 52a80 0 glBindSampler
PUBLIC 52b00 0 glBindSamplers
PUBLIC 52b80 0 glBindShadingRateImageNV
PUBLIC 52c00 0 glBindTexGenParameterEXT
PUBLIC 52c80 0 glBindTexture
PUBLIC 52d00 0 glBindTextureEXT
PUBLIC 52d80 0 glBindTextureUnit
PUBLIC 52e00 0 glBindTextureUnitParameterEXT
PUBLIC 52e80 0 glBindTextures
PUBLIC 52f00 0 glBindTransformFeedback
PUBLIC 52f80 0 glBindTransformFeedbackEXT
PUBLIC 53000 0 glBindTransformFeedbackNV
PUBLIC 53080 0 glBindVertexArray
PUBLIC 53100 0 glBindVertexArrayAPPLE
PUBLIC 53180 0 glBindVertexArrayOES
PUBLIC 53200 0 glBindVertexBuffer
PUBLIC 53280 0 glBindVertexBuffers
PUBLIC 53300 0 glBindVertexShaderEXT
PUBLIC 53380 0 glBindVideoCaptureStreamBufferNV
PUBLIC 53400 0 glBindVideoCaptureStreamTextureNV
PUBLIC 53480 0 glBinormal3bEXT
PUBLIC 53500 0 glBinormal3bvEXT
PUBLIC 53580 0 glBinormal3dEXT
PUBLIC 53600 0 glBinormal3dvEXT
PUBLIC 53680 0 glBinormal3fEXT
PUBLIC 53700 0 glBinormal3fvEXT
PUBLIC 53780 0 glBinormal3iEXT
PUBLIC 53800 0 glBinormal3ivEXT
PUBLIC 53880 0 glBinormal3sEXT
PUBLIC 53900 0 glBinormal3svEXT
PUBLIC 53980 0 glBinormalPointerEXT
PUBLIC 53a00 0 glBitmap
PUBLIC 53a80 0 glBitmapxOES
PUBLIC 53b00 0 glBlendBarrier
PUBLIC 53b80 0 glBlendBarrierKHR
PUBLIC 53c00 0 glBlendBarrierNV
PUBLIC 53c80 0 glBlendColor
PUBLIC 53d00 0 glBlendColorEXT
PUBLIC 53d80 0 glBlendColorxOES
PUBLIC 53e00 0 glBlendEquation
PUBLIC 53e80 0 glBlendEquationEXT
PUBLIC 53f00 0 glBlendEquationIndexedAMD
PUBLIC 53f80 0 glBlendEquationOES
PUBLIC 54000 0 glBlendEquationSeparate
PUBLIC 54080 0 glBlendEquationSeparateEXT
PUBLIC 54100 0 glBlendEquationSeparateIndexedAMD
PUBLIC 54180 0 glBlendEquationSeparateOES
PUBLIC 54200 0 glBlendEquationSeparatei
PUBLIC 54280 0 glBlendEquationSeparateiARB
PUBLIC 54300 0 glBlendEquationSeparateiEXT
PUBLIC 54380 0 glBlendEquationSeparateiOES
PUBLIC 54400 0 glBlendEquationi
PUBLIC 54480 0 glBlendEquationiARB
PUBLIC 54500 0 glBlendEquationiEXT
PUBLIC 54580 0 glBlendEquationiOES
PUBLIC 54600 0 glBlendFunc
PUBLIC 54680 0 glBlendFuncIndexedAMD
PUBLIC 54700 0 glBlendFuncSeparate
PUBLIC 54780 0 glBlendFuncSeparateEXT
PUBLIC 54800 0 glBlendFuncSeparateINGR
PUBLIC 54880 0 glBlendFuncSeparateIndexedAMD
PUBLIC 54900 0 glBlendFuncSeparateOES
PUBLIC 54980 0 glBlendFuncSeparatei
PUBLIC 54a00 0 glBlendFuncSeparateiARB
PUBLIC 54a80 0 glBlendFuncSeparateiEXT
PUBLIC 54b00 0 glBlendFuncSeparateiOES
PUBLIC 54b80 0 glBlendFunci
PUBLIC 54c00 0 glBlendFunciARB
PUBLIC 54c80 0 glBlendFunciEXT
PUBLIC 54d00 0 glBlendFunciOES
PUBLIC 54d80 0 glBlendParameteriNV
PUBLIC 54e00 0 glBlitFramebuffer
PUBLIC 54e80 0 glBlitFramebufferANGLE
PUBLIC 54f00 0 glBlitFramebufferEXT
PUBLIC 54f80 0 glBlitFramebufferNV
PUBLIC 55000 0 glBlitNamedFramebuffer
PUBLIC 55080 0 glBufferAddressRangeNV
PUBLIC 55100 0 glBufferAttachMemoryNV
PUBLIC 55180 0 glBufferData
PUBLIC 55200 0 glBufferDataARB
PUBLIC 55280 0 glBufferDataSysmem
PUBLIC 55300 0 glBufferPageCommitmentARB
PUBLIC 55380 0 glBufferParameteriAPPLE
PUBLIC 55400 0 glBufferStorage
PUBLIC 55480 0 glBufferStorageEXT
PUBLIC 55500 0 glBufferStorageExternalEXT
PUBLIC 55580 0 glBufferStorageMemEXT
PUBLIC 55600 0 glBufferSubData
PUBLIC 55680 0 glBufferSubDataARB
PUBLIC 55700 0 glCallCommandListNV
PUBLIC 55780 0 glCallList
PUBLIC 55800 0 glCallLists
PUBLIC 55880 0 glCheckFramebufferStatus
PUBLIC 55900 0 glCheckFramebufferStatusEXT
PUBLIC 55980 0 glCheckFramebufferStatusOES
PUBLIC 55a00 0 glCheckNamedFramebufferStatus
PUBLIC 55a80 0 glCheckNamedFramebufferStatusEXT
PUBLIC 55b00 0 glClampColor
PUBLIC 55b80 0 glClampColorARB
PUBLIC 55c00 0 glClear
PUBLIC 55c80 0 glClearAccum
PUBLIC 55d00 0 glClearAccumxOES
PUBLIC 55d80 0 glClearBufferData
PUBLIC 55e00 0 glClearBufferSubData
PUBLIC 55e80 0 glClearBufferfi
PUBLIC 55f00 0 glClearBufferfv
PUBLIC 55f80 0 glClearBufferiv
PUBLIC 56000 0 glClearBufferuiv
PUBLIC 56080 0 glClearColor
PUBLIC 56100 0 glClearColorIiEXT
PUBLIC 56180 0 glClearColorIuiEXT
PUBLIC 56200 0 glClearColorx
PUBLIC 56280 0 glClearColorxOES
PUBLIC 56300 0 glClearDepth
PUBLIC 56380 0 glClearDepthdNV
PUBLIC 56400 0 glClearDepthf
PUBLIC 56480 0 glClearDepthfOES
PUBLIC 56500 0 glClearDepthx
PUBLIC 56580 0 glClearDepthxOES
PUBLIC 56600 0 glClearIndex
PUBLIC 56680 0 glClearNamedBufferData
PUBLIC 56700 0 glClearNamedBufferDataEXT
PUBLIC 56780 0 glClearNamedBufferSubData
PUBLIC 56800 0 glClearNamedBufferSubDataEXT
PUBLIC 56880 0 glClearNamedFramebufferfi
PUBLIC 56900 0 glClearNamedFramebufferfv
PUBLIC 56980 0 glClearNamedFramebufferiv
PUBLIC 56a00 0 glClearNamedFramebufferuiv
PUBLIC 56a80 0 glClearPixelLocalStorageuiEXT
PUBLIC 56b00 0 glClearStencil
PUBLIC 56b80 0 glClearTexImage
PUBLIC 56c00 0 glClearTexImageEXT
PUBLIC 56c80 0 glClearTexSubImage
PUBLIC 56d00 0 glClearTexSubImageEXT
PUBLIC 56d80 0 glClientActiveTexture
PUBLIC 56e00 0 glClientActiveTextureARB
PUBLIC 56e80 0 glClientActiveVertexStreamATI
PUBLIC 56f00 0 glClientAttribDefaultEXT
PUBLIC 56f80 0 glClientWaitSemaphoreui64NVX
PUBLIC 57000 0 glClientWaitSync
PUBLIC 57080 0 glClientWaitSyncAPPLE
PUBLIC 57100 0 glClientWaitSyncValueuiNVX
PUBLIC 57180 0 glClipControl
PUBLIC 57200 0 glClipControlEXT
PUBLIC 57280 0 glClipPlane
PUBLIC 57300 0 glClipPlanef
PUBLIC 57380 0 glClipPlanefIMG
PUBLIC 57400 0 glClipPlanefOES
PUBLIC 57480 0 glClipPlanex
PUBLIC 57500 0 glClipPlanexIMG
PUBLIC 57580 0 glClipPlanexOES
PUBLIC 57600 0 glColor3b
PUBLIC 57680 0 glColor3bv
PUBLIC 57700 0 glColor3d
PUBLIC 57780 0 glColor3dv
PUBLIC 57800 0 glColor3f
PUBLIC 57880 0 glColor3fVertex3fSUN
PUBLIC 57900 0 glColor3fVertex3fvSUN
PUBLIC 57980 0 glColor3fv
PUBLIC 57a00 0 glColor3hNV
PUBLIC 57a80 0 glColor3hvNV
PUBLIC 57b00 0 glColor3i
PUBLIC 57b80 0 glColor3iv
PUBLIC 57c00 0 glColor3s
PUBLIC 57c80 0 glColor3sv
PUBLIC 57d00 0 glColor3ub
PUBLIC 57d80 0 glColor3ubv
PUBLIC 57e00 0 glColor3ui
PUBLIC 57e80 0 glColor3uiv
PUBLIC 57f00 0 glColor3us
PUBLIC 57f80 0 glColor3usv
PUBLIC 58000 0 glColor3x
PUBLIC 58080 0 glColor3xOES
PUBLIC 58100 0 glColor3xvOES
PUBLIC 58180 0 glColor4b
PUBLIC 58200 0 glColor4bv
PUBLIC 58280 0 glColor4d
PUBLIC 58300 0 glColor4dv
PUBLIC 58380 0 glColor4f
PUBLIC 58400 0 glColor4fNormal3fVertex3fSUN
PUBLIC 58480 0 glColor4fNormal3fVertex3fvSUN
PUBLIC 58500 0 glColor4fv
PUBLIC 58580 0 glColor4hNV
PUBLIC 58600 0 glColor4hvNV
PUBLIC 58680 0 glColor4i
PUBLIC 58700 0 glColor4iv
PUBLIC 58780 0 glColor4s
PUBLIC 58800 0 glColor4sv
PUBLIC 58880 0 glColor4ub
PUBLIC 58900 0 glColor4ubVertex2fSUN
PUBLIC 58980 0 glColor4ubVertex2fvSUN
PUBLIC 58a00 0 glColor4ubVertex3fSUN
PUBLIC 58a80 0 glColor4ubVertex3fvSUN
PUBLIC 58b00 0 glColor4ubv
PUBLIC 58b80 0 glColor4ui
PUBLIC 58c00 0 glColor4uiv
PUBLIC 58c80 0 glColor4us
PUBLIC 58d00 0 glColor4usv
PUBLIC 58d80 0 glColor4x
PUBLIC 58e00 0 glColor4xOES
PUBLIC 58e80 0 glColor4xvOES
PUBLIC 58f00 0 glColorFormatNV
PUBLIC 58f80 0 glColorFragmentOp1ATI
PUBLIC 59000 0 glColorFragmentOp2ATI
PUBLIC 59080 0 glColorFragmentOp3ATI
PUBLIC 59100 0 glColorMask
PUBLIC 59180 0 glColorMaskIndexedEXT
PUBLIC 59200 0 glColorMaski
PUBLIC 59280 0 glColorMaskiEXT
PUBLIC 59300 0 glColorMaskiOES
PUBLIC 59380 0 glColorMaterial
PUBLIC 59400 0 glColorP3ui
PUBLIC 59480 0 glColorP3uiv
PUBLIC 59500 0 glColorP4ui
PUBLIC 59580 0 glColorP4uiv
PUBLIC 59600 0 glColorPointer
PUBLIC 59680 0 glColorPointerEXT
PUBLIC 59700 0 glColorPointerListIBM
PUBLIC 59780 0 glColorPointervINTEL
PUBLIC 59800 0 glColorSubTable
PUBLIC 59880 0 glColorSubTableEXT
PUBLIC 59900 0 glColorTable
PUBLIC 59980 0 glColorTableEXT
PUBLIC 59a00 0 glColorTableParameterfv
PUBLIC 59a80 0 glColorTableParameterfvSGI
PUBLIC 59b00 0 glColorTableParameteriv
PUBLIC 59b80 0 glColorTableParameterivSGI
PUBLIC 59c00 0 glColorTableSGI
PUBLIC 59c80 0 glCombinerInputNV
PUBLIC 59d00 0 glCombinerOutputNV
PUBLIC 59d80 0 glCombinerParameterfNV
PUBLIC 59e00 0 glCombinerParameterfvNV
PUBLIC 59e80 0 glCombinerParameteriNV
PUBLIC 59f00 0 glCombinerParameterivNV
PUBLIC 59f80 0 glCombinerStageParameterfvNV
PUBLIC 5a000 0 glCommandListSegmentsNV
PUBLIC 5a080 0 glCompileCommandListNV
PUBLIC 5a100 0 glCompileShader
PUBLIC 5a180 0 glCompileShaderARB
PUBLIC 5a200 0 glCompileShaderIncludeARB
PUBLIC 5a280 0 glCompressedMultiTexImage1DEXT
PUBLIC 5a300 0 glCompressedMultiTexImage2DEXT
PUBLIC 5a380 0 glCompressedMultiTexImage3DEXT
PUBLIC 5a400 0 glCompressedMultiTexSubImage1DEXT
PUBLIC 5a480 0 glCompressedMultiTexSubImage2DEXT
PUBLIC 5a500 0 glCompressedMultiTexSubImage3DEXT
PUBLIC 5a580 0 glCompressedTexImage1D
PUBLIC 5a600 0 glCompressedTexImage1DARB
PUBLIC 5a680 0 glCompressedTexImage2D
PUBLIC 5a700 0 glCompressedTexImage2DARB
PUBLIC 5a780 0 glCompressedTexImage3D
PUBLIC 5a800 0 glCompressedTexImage3DARB
PUBLIC 5a880 0 glCompressedTexImage3DNV
PUBLIC 5a900 0 glCompressedTexImage3DOES
PUBLIC 5a980 0 glCompressedTexSubImage1D
PUBLIC 5aa00 0 glCompressedTexSubImage1DARB
PUBLIC 5aa80 0 glCompressedTexSubImage2D
PUBLIC 5ab00 0 glCompressedTexSubImage2DARB
PUBLIC 5ab80 0 glCompressedTexSubImage3D
PUBLIC 5ac00 0 glCompressedTexSubImage3DARB
PUBLIC 5ac80 0 glCompressedTexSubImage3DNV
PUBLIC 5ad00 0 glCompressedTexSubImage3DOES
PUBLIC 5ad80 0 glCompressedTextureImage1DEXT
PUBLIC 5ae00 0 glCompressedTextureImage2DEXT
PUBLIC 5ae80 0 glCompressedTextureImage3DEXT
PUBLIC 5af00 0 glCompressedTextureSubImage1D
PUBLIC 5af80 0 glCompressedTextureSubImage1DEXT
PUBLIC 5b000 0 glCompressedTextureSubImage2D
PUBLIC 5b080 0 glCompressedTextureSubImage2DEXT
PUBLIC 5b100 0 glCompressedTextureSubImage3D
PUBLIC 5b180 0 glCompressedTextureSubImage3DEXT
PUBLIC 5b200 0 glConservativeRasterParameterfNV
PUBLIC 5b280 0 glConservativeRasterParameteriNV
PUBLIC 5b300 0 glConvolutionFilter1D
PUBLIC 5b380 0 glConvolutionFilter1DEXT
PUBLIC 5b400 0 glConvolutionFilter2D
PUBLIC 5b480 0 glConvolutionFilter2DEXT
PUBLIC 5b500 0 glConvolutionParameterf
PUBLIC 5b580 0 glConvolutionParameterfEXT
PUBLIC 5b600 0 glConvolutionParameterfv
PUBLIC 5b680 0 glConvolutionParameterfvEXT
PUBLIC 5b700 0 glConvolutionParameteri
PUBLIC 5b780 0 glConvolutionParameteriEXT
PUBLIC 5b800 0 glConvolutionParameteriv
PUBLIC 5b880 0 glConvolutionParameterivEXT
PUBLIC 5b900 0 glConvolutionParameterxOES
PUBLIC 5b980 0 glConvolutionParameterxvOES
PUBLIC 5ba00 0 glCopyBufferSubData
PUBLIC 5ba80 0 glCopyBufferSubDataNV
PUBLIC 5bb00 0 glCopyColorSubTable
PUBLIC 5bb80 0 glCopyColorSubTableEXT
PUBLIC 5bc00 0 glCopyColorTable
PUBLIC 5bc80 0 glCopyColorTableSGI
PUBLIC 5bd00 0 glCopyConvolutionFilter1D
PUBLIC 5bd80 0 glCopyConvolutionFilter1DEXT
PUBLIC 5be00 0 glCopyConvolutionFilter2D
PUBLIC 5be80 0 glCopyConvolutionFilter2DEXT
PUBLIC 5bf00 0 glCopyImageSubData
PUBLIC 5bf80 0 glCopyImageSubDataEXT
PUBLIC 5c000 0 glCopyImageSubDataNV
PUBLIC 5c080 0 glCopyImageSubDataOES
PUBLIC 5c100 0 glCopyMultiTexImage1DEXT
PUBLIC 5c180 0 glCopyMultiTexImage2DEXT
PUBLIC 5c200 0 glCopyMultiTexSubImage1DEXT
PUBLIC 5c280 0 glCopyMultiTexSubImage2DEXT
PUBLIC 5c300 0 glCopyMultiTexSubImage3DEXT
PUBLIC 5c380 0 glCopyNamedBufferSubData
PUBLIC 5c400 0 glCopyPathNV
PUBLIC 5c480 0 glCopyPixels
PUBLIC 5c500 0 glCopyTexImage1D
PUBLIC 5c580 0 glCopyTexImage1DEXT
PUBLIC 5c600 0 glCopyTexImage2D
PUBLIC 5c680 0 glCopyTexImage2DEXT
PUBLIC 5c700 0 glCopyTexSubImage1D
PUBLIC 5c780 0 glCopyTexSubImage1DEXT
PUBLIC 5c800 0 glCopyTexSubImage2D
PUBLIC 5c880 0 glCopyTexSubImage2DEXT
PUBLIC 5c900 0 glCopyTexSubImage3D
PUBLIC 5c980 0 glCopyTexSubImage3DEXT
PUBLIC 5ca00 0 glCopyTexSubImage3DNV
PUBLIC 5ca80 0 glCopyTexSubImage3DOES
PUBLIC 5cb00 0 glCopyTextureImage1DEXT
PUBLIC 5cb80 0 glCopyTextureImage2DEXT
PUBLIC 5cc00 0 glCopyTextureLevelsAPPLE
PUBLIC 5cc80 0 glCopyTextureSubImage1D
PUBLIC 5cd00 0 glCopyTextureSubImage1DEXT
PUBLIC 5cd80 0 glCopyTextureSubImage2D
PUBLIC 5ce00 0 glCopyTextureSubImage2DEXT
PUBLIC 5ce80 0 glCopyTextureSubImage3D
PUBLIC 5cf00 0 glCopyTextureSubImage3DEXT
PUBLIC 5cf80 0 glCoverFillPathInstancedNV
PUBLIC 5d000 0 glCoverFillPathNV
PUBLIC 5d080 0 glCoverStrokePathInstancedNV
PUBLIC 5d100 0 glCoverStrokePathNV
PUBLIC 5d180 0 glCoverageMaskNV
PUBLIC 5d200 0 glCoverageModulationNV
PUBLIC 5d280 0 glCoverageModulationTableNV
PUBLIC 5d300 0 glCoverageOperationNV
PUBLIC 5d380 0 glCreateBuffers
PUBLIC 5d400 0 glCreateCommandListsNV
PUBLIC 5d480 0 glCreateFramebuffers
PUBLIC 5d500 0 glCreateMemoryObjectsEXT
PUBLIC 5d580 0 glCreatePerfQueryINTEL
PUBLIC 5d600 0 glCreateProgram
PUBLIC 5d680 0 glCreateProgramObjectARB
PUBLIC 5d700 0 glCreateProgramPipelines
PUBLIC 5d780 0 glCreateProgressFenceNVX
PUBLIC 5d800 0 glCreateQueries
PUBLIC 5d880 0 glCreateRenderbuffers
PUBLIC 5d900 0 glCreateSamplers
PUBLIC 5d980 0 glCreateShader
PUBLIC 5da00 0 glCreateShaderObjectARB
PUBLIC 5da80 0 glCreateShaderProgramEXT
PUBLIC 5db00 0 glCreateShaderProgramv
PUBLIC 5db80 0 glCreateShaderProgramvEXT
PUBLIC 5dc00 0 glCreateStatesNV
PUBLIC 5dc80 0 glCreateSyncFromCLeventARB
PUBLIC 5dd00 0 glCreateTextures
PUBLIC 5dd80 0 glCreateTransformFeedbacks
PUBLIC 5de00 0 glCreateVertexArrays
PUBLIC 5de80 0 glCullFace
PUBLIC 5df00 0 glCullParameterdvEXT
PUBLIC 5df80 0 glCullParameterfvEXT
PUBLIC 5e000 0 glCurrentPaletteMatrixARB
PUBLIC 5e080 0 glCurrentPaletteMatrixOES
PUBLIC 5e100 0 glDebugMessageCallback
PUBLIC 5e180 0 glDebugMessageCallbackAMD
PUBLIC 5e200 0 glDebugMessageCallbackARB
PUBLIC 5e280 0 glDebugMessageCallbackKHR
PUBLIC 5e300 0 glDebugMessageCallbackOES
PUBLIC 5e380 0 glDebugMessageControl
PUBLIC 5e400 0 glDebugMessageControlARB
PUBLIC 5e480 0 glDebugMessageControlKHR
PUBLIC 5e500 0 glDebugMessageControlOES
PUBLIC 5e580 0 glDebugMessageEnableAMD
PUBLIC 5e600 0 glDebugMessageInsert
PUBLIC 5e680 0 glDebugMessageInsertAMD
PUBLIC 5e700 0 glDebugMessageInsertARB
PUBLIC 5e780 0 glDebugMessageInsertKHR
PUBLIC 5e800 0 glDebugMessageInsertOES
PUBLIC 5e880 0 glDeformSGIX
PUBLIC 5e900 0 glDeformationMap3dSGIX
PUBLIC 5e980 0 glDeformationMap3fSGIX
PUBLIC 5ea00 0 glDeleteAsyncMarkersSGIX
PUBLIC 5ea80 0 glDeleteBuffers
PUBLIC 5eb00 0 glDeleteBuffersARB
PUBLIC 5eb80 0 glDeleteCommandListsNV
PUBLIC 5ec00 0 glDeleteFencesAPPLE
PUBLIC 5ec80 0 glDeleteFencesNV
PUBLIC 5ed00 0 glDeleteFragmentShaderATI
PUBLIC 5ed80 0 glDeleteFramebuffers
PUBLIC 5ee00 0 glDeleteFramebuffersEXT
PUBLIC 5ee80 0 glDeleteFramebuffersOES
PUBLIC 5ef00 0 glDeleteLists
PUBLIC 5ef80 0 glDeleteMemoryObjectsEXT
PUBLIC 5f000 0 glDeleteNamedStringARB
PUBLIC 5f080 0 glDeleteNamesAMD
PUBLIC 5f100 0 glDeleteObjectARB
PUBLIC 5f180 0 glDeleteOcclusionQueriesNV
PUBLIC 5f200 0 glDeletePathsNV
PUBLIC 5f280 0 glDeletePerfMonitorsAMD
PUBLIC 5f300 0 glDeletePerfQueryINTEL
PUBLIC 5f380 0 glDeleteProgram
PUBLIC 5f400 0 glDeleteProgramPipelines
PUBLIC 5f480 0 glDeleteProgramPipelinesEXT
PUBLIC 5f500 0 glDeleteProgramsARB
PUBLIC 5f580 0 glDeleteProgramsNV
PUBLIC 5f600 0 glDeleteQueries
PUBLIC 5f680 0 glDeleteQueriesARB
PUBLIC 5f700 0 glDeleteQueriesEXT
PUBLIC 5f780 0 glDeleteQueryResourceTagNV
PUBLIC 5f800 0 glDeleteRenderbuffers
PUBLIC 5f880 0 glDeleteRenderbuffersEXT
PUBLIC 5f900 0 glDeleteRenderbuffersOES
PUBLIC 5f980 0 glDeleteSamplers
PUBLIC 5fa00 0 glDeleteSemaphoresEXT
PUBLIC 5fa80 0 glDeleteShader
PUBLIC 5fb00 0 glDeleteStatesNV
PUBLIC 5fb80 0 glDeleteSync
PUBLIC 5fc00 0 glDeleteSyncAPPLE
PUBLIC 5fc80 0 glDeleteTextures
PUBLIC 5fd00 0 glDeleteTexturesEXT
PUBLIC 5fd80 0 glDeleteTransformFeedbacks
PUBLIC 5fe00 0 glDeleteTransformFeedbacksEXT
PUBLIC 5fe80 0 glDeleteTransformFeedbacksNV
PUBLIC 5ff00 0 glDeleteVertexArrays
PUBLIC 5ff80 0 glDeleteVertexArraysAPPLE
PUBLIC 60000 0 glDeleteVertexArraysOES
PUBLIC 60080 0 glDeleteVertexShaderEXT
PUBLIC 60100 0 glDepthBoundsEXT
PUBLIC 60180 0 glDepthBoundsdNV
PUBLIC 60200 0 glDepthFunc
PUBLIC 60280 0 glDepthMask
PUBLIC 60300 0 glDepthRange
PUBLIC 60380 0 glDepthRangeArraydvNV
PUBLIC 60400 0 glDepthRangeArrayfvNV
PUBLIC 60480 0 glDepthRangeArrayfvOES
PUBLIC 60500 0 glDepthRangeArrayv
PUBLIC 60580 0 glDepthRangeIndexed
PUBLIC 60600 0 glDepthRangeIndexeddNV
PUBLIC 60680 0 glDepthRangeIndexedfNV
PUBLIC 60700 0 glDepthRangeIndexedfOES
PUBLIC 60780 0 glDepthRangedNV
PUBLIC 60800 0 glDepthRangef
PUBLIC 60880 0 glDepthRangefOES
PUBLIC 60900 0 glDepthRangex
PUBLIC 60980 0 glDepthRangexOES
PUBLIC 60a00 0 glDetachObjectARB
PUBLIC 60a80 0 glDetachShader
PUBLIC 60b00 0 glDetailTexFuncSGIS
PUBLIC 60b80 0 glDevtoolsCorrelationIdNVX
PUBLIC 60c00 0 glDevtoolsInvokeStreamedCallbackNVX
PUBLIC 60c80 0 glDisable
PUBLIC 60d00 0 glDisableClientState
PUBLIC 60d80 0 glDisableClientStateIndexedEXT
PUBLIC 60e00 0 glDisableClientStateiEXT
PUBLIC 60e80 0 glDisableDriverControlQCOM
PUBLIC 60f00 0 glDisableIndexedEXT
PUBLIC 60f80 0 glDisableVariantClientStateEXT
PUBLIC 61000 0 glDisableVertexArrayAttrib
PUBLIC 61080 0 glDisableVertexArrayAttribEXT
PUBLIC 61100 0 glDisableVertexArrayEXT
PUBLIC 61180 0 glDisableVertexAttribAPPLE
PUBLIC 61200 0 glDisableVertexAttribArray
PUBLIC 61280 0 glDisableVertexAttribArrayARB
PUBLIC 61300 0 glDisablei
PUBLIC 61380 0 glDisableiEXT
PUBLIC 61400 0 glDisableiNV
PUBLIC 61480 0 glDisableiOES
PUBLIC 61500 0 glDiscardFramebufferEXT
PUBLIC 61580 0 glDispatchCompute
PUBLIC 61600 0 glDispatchComputeGroupSizeARB
PUBLIC 61680 0 glDispatchComputeIndirect
PUBLIC 61700 0 glDrawArrays
PUBLIC 61780 0 glDrawArraysEXT
PUBLIC 61800 0 glDrawArraysIndirect
PUBLIC 61880 0 glDrawArraysInstanced
PUBLIC 61900 0 glDrawArraysInstancedANGLE
PUBLIC 61980 0 glDrawArraysInstancedARB
PUBLIC 61a00 0 glDrawArraysInstancedBaseInstance
PUBLIC 61a80 0 glDrawArraysInstancedBaseInstanceEXT
PUBLIC 61b00 0 glDrawArraysInstancedEXT
PUBLIC 61b80 0 glDrawArraysInstancedNV
PUBLIC 61c00 0 glDrawBuffer
PUBLIC 61c80 0 glDrawBuffers
PUBLIC 61d00 0 glDrawBuffersARB
PUBLIC 61d80 0 glDrawBuffersATI
PUBLIC 61e00 0 glDrawBuffersEXT
PUBLIC 61e80 0 glDrawBuffersIndexedEXT
PUBLIC 61f00 0 glDrawBuffersNV
PUBLIC 61f80 0 glDrawCommandsAddressNV
PUBLIC 62000 0 glDrawCommandsNV
PUBLIC 62080 0 glDrawCommandsStatesAddressNV
PUBLIC 62100 0 glDrawCommandsStatesNV
PUBLIC 62180 0 glDrawElementArrayAPPLE
PUBLIC 62200 0 glDrawElementArrayATI
PUBLIC 62280 0 glDrawElements
PUBLIC 62300 0 glDrawElementsBaseVertex
PUBLIC 62380 0 glDrawElementsBaseVertexEXT
PUBLIC 62400 0 glDrawElementsBaseVertexOES
PUBLIC 62480 0 glDrawElementsIndirect
PUBLIC 62500 0 glDrawElementsInstanced
PUBLIC 62580 0 glDrawElementsInstancedANGLE
PUBLIC 62600 0 glDrawElementsInstancedARB
PUBLIC 62680 0 glDrawElementsInstancedBaseInstance
PUBLIC 62700 0 glDrawElementsInstancedBaseInstanceEXT
PUBLIC 62780 0 glDrawElementsInstancedBaseVertex
PUBLIC 62800 0 glDrawElementsInstancedBaseVertexBaseInstance
PUBLIC 62880 0 glDrawElementsInstancedBaseVertexBaseInstanceEXT
PUBLIC 62900 0 glDrawElementsInstancedBaseVertexEXT
PUBLIC 62980 0 glDrawElementsInstancedBaseVertexOES
PUBLIC 62a00 0 glDrawElementsInstancedEXT
PUBLIC 62a80 0 glDrawElementsInstancedNV
PUBLIC 62b00 0 glDrawMeshArraysSUN
PUBLIC 62b80 0 glDrawMeshNV
PUBLIC 62c00 0 glDrawMeshTasksIndirectNV
PUBLIC 62c80 0 glDrawMeshTasksNV
PUBLIC 62d00 0 glDrawPixels
PUBLIC 62d80 0 glDrawRangeElementArrayAPPLE
PUBLIC 62e00 0 glDrawRangeElementArrayATI
PUBLIC 62e80 0 glDrawRangeElements
PUBLIC 62f00 0 glDrawRangeElementsBaseVertex
PUBLIC 62f80 0 glDrawRangeElementsBaseVertexEXT
PUBLIC 63000 0 glDrawRangeElementsBaseVertexOES
PUBLIC 63080 0 glDrawRangeElementsEXT
PUBLIC 63100 0 glDrawTexfOES
PUBLIC 63180 0 glDrawTexfvOES
PUBLIC 63200 0 glDrawTexiOES
PUBLIC 63280 0 glDrawTexivOES
PUBLIC 63300 0 glDrawTexsOES
PUBLIC 63380 0 glDrawTexsvOES
PUBLIC 63400 0 glDrawTextureNV
PUBLIC 63480 0 glDrawTexxOES
PUBLIC 63500 0 glDrawTexxvOES
PUBLIC 63580 0 glDrawTransformFeedback
PUBLIC 63600 0 glDrawTransformFeedbackEXT
PUBLIC 63680 0 glDrawTransformFeedbackInstanced
PUBLIC 63700 0 glDrawTransformFeedbackInstancedEXT
PUBLIC 63780 0 glDrawTransformFeedbackNV
PUBLIC 63800 0 glDrawTransformFeedbackStream
PUBLIC 63880 0 glDrawTransformFeedbackStreamInstanced
PUBLIC 63900 0 glDrawVkImageNV
PUBLIC 63980 0 glEGLImageTargetRenderbufferStorageOES
PUBLIC 63a00 0 glEGLImageTargetTexStorageEXT
PUBLIC 63a80 0 glEGLImageTargetTexture2DOES
PUBLIC 63b00 0 glEGLImageTargetTextureStorageEXT
PUBLIC 63b80 0 glEdgeFlag
PUBLIC 63c00 0 glEdgeFlagFormatNV
PUBLIC 63c80 0 glEdgeFlagPointer
PUBLIC 63d00 0 glEdgeFlagPointerEXT
PUBLIC 63d80 0 glEdgeFlagPointerListIBM
PUBLIC 63e00 0 glEdgeFlagv
PUBLIC 63e80 0 glElementPointerAPPLE
PUBLIC 63f00 0 glElementPointerATI
PUBLIC 63f80 0 glEnable
PUBLIC 64000 0 glEnableClientState
PUBLIC 64080 0 glEnableClientStateIndexedEXT
PUBLIC 64100 0 glEnableClientStateiEXT
PUBLIC 64180 0 glEnableDriverControlQCOM
PUBLIC 64200 0 glEnableIndexedEXT
PUBLIC 64280 0 glEnableVariantClientStateEXT
PUBLIC 64300 0 glEnableVertexArrayAttrib
PUBLIC 64380 0 glEnableVertexArrayAttribEXT
PUBLIC 64400 0 glEnableVertexArrayEXT
PUBLIC 64480 0 glEnableVertexAttribAPPLE
PUBLIC 64500 0 glEnableVertexAttribArray
PUBLIC 64580 0 glEnableVertexAttribArrayARB
PUBLIC 64600 0 glEnablei
PUBLIC 64680 0 glEnableiEXT
PUBLIC 64700 0 glEnableiNV
PUBLIC 64780 0 glEnableiOES
PUBLIC 64800 0 glEnd
PUBLIC 64880 0 glEndConditionalRender
PUBLIC 64900 0 glEndConditionalRenderNV
PUBLIC 64980 0 glEndConditionalRenderNVX
PUBLIC 64a00 0 glEndFragmentShaderATI
PUBLIC 64a80 0 glEndList
PUBLIC 64b00 0 glEndOcclusionQueryNV
PUBLIC 64b80 0 glEndPerfMonitorAMD
PUBLIC 64c00 0 glEndPerfQueryINTEL
PUBLIC 64c80 0 glEndQuery
PUBLIC 64d00 0 glEndQueryARB
PUBLIC 64d80 0 glEndQueryEXT
PUBLIC 64e00 0 glEndQueryIndexed
PUBLIC 64e80 0 glEndTilingQCOM
PUBLIC 64f00 0 glEndTransformFeedback
PUBLIC 64f80 0 glEndTransformFeedbackEXT
PUBLIC 65000 0 glEndTransformFeedbackNV
PUBLIC 65080 0 glEndVertexShaderEXT
PUBLIC 65100 0 glEndVideoCaptureNV
PUBLIC 65180 0 glEvalCoord1d
PUBLIC 65200 0 glEvalCoord1dv
PUBLIC 65280 0 glEvalCoord1f
PUBLIC 65300 0 glEvalCoord1fv
PUBLIC 65380 0 glEvalCoord1xOES
PUBLIC 65400 0 glEvalCoord1xvOES
PUBLIC 65480 0 glEvalCoord2d
PUBLIC 65500 0 glEvalCoord2dv
PUBLIC 65580 0 glEvalCoord2f
PUBLIC 65600 0 glEvalCoord2fv
PUBLIC 65680 0 glEvalCoord2xOES
PUBLIC 65700 0 glEvalCoord2xvOES
PUBLIC 65780 0 glEvalMapsNV
PUBLIC 65800 0 glEvalMesh1
PUBLIC 65880 0 glEvalMesh2
PUBLIC 65900 0 glEvalPoint1
PUBLIC 65980 0 glEvalPoint2
PUBLIC 65a00 0 glEvaluateDepthValuesARB
PUBLIC 65a80 0 glExecuteProgramNV
PUBLIC 65b00 0 glExtGetBufferPointervQCOM
PUBLIC 65b80 0 glExtGetBuffersQCOM
PUBLIC 65c00 0 glExtGetFramebuffersQCOM
PUBLIC 65c80 0 glExtGetProgramBinarySourceQCOM
PUBLIC 65d00 0 glExtGetProgramsQCOM
PUBLIC 65d80 0 glExtGetRenderbuffersQCOM
PUBLIC 65e00 0 glExtGetShadersQCOM
PUBLIC 65e80 0 glExtGetTexLevelParameterivQCOM
PUBLIC 65f00 0 glExtGetTexSubImageQCOM
PUBLIC 65f80 0 glExtGetTexturesQCOM
PUBLIC 66000 0 glExtIsProgramBinaryQCOM
PUBLIC 66080 0 glExtTexObjectStateOverrideiQCOM
PUBLIC 66100 0 glExtractComponentEXT
PUBLIC 66180 0 glFeedbackBuffer
PUBLIC 66200 0 glFeedbackBufferxOES
PUBLIC 66280 0 glFenceSync
PUBLIC 66300 0 glFenceSyncAPPLE
PUBLIC 66380 0 glFenceValueuiNVX
PUBLIC 66400 0 glFinalCombinerInputNV
PUBLIC 66480 0 glFinish
PUBLIC 66500 0 glFinishAsyncSGIX
PUBLIC 66580 0 glFinishFenceAPPLE
PUBLIC 66600 0 glFinishFenceNV
PUBLIC 66680 0 glFinishObjectAPPLE
PUBLIC 66700 0 glFinishTextureSUNX
PUBLIC 66780 0 glFlush
PUBLIC 66800 0 glFlushMappedBufferRange
PUBLIC 66880 0 glFlushMappedBufferRangeAPPLE
PUBLIC 66900 0 glFlushMappedBufferRangeEXT
PUBLIC 66980 0 glFlushMappedNamedBufferRange
PUBLIC 66a00 0 glFlushMappedNamedBufferRangeEXT
PUBLIC 66a80 0 glFlushPixelDataRangeNV
PUBLIC 66b00 0 glFlushRasterSGIX
PUBLIC 66b80 0 glFlushStaticDataIBM
PUBLIC 66c00 0 glFlushVertexArrayRangeAPPLE
PUBLIC 66c80 0 glFlushVertexArrayRangeNV
PUBLIC 66d00 0 glFogCoordFormatNV
PUBLIC 66d80 0 glFogCoordPointer
PUBLIC 66e00 0 glFogCoordPointerEXT
PUBLIC 66e80 0 glFogCoordPointerListIBM
PUBLIC 66f00 0 glFogCoordd
PUBLIC 66f80 0 glFogCoorddEXT
PUBLIC 67000 0 glFogCoorddv
PUBLIC 67080 0 glFogCoorddvEXT
PUBLIC 67100 0 glFogCoordf
PUBLIC 67180 0 glFogCoordfEXT
PUBLIC 67200 0 glFogCoordfv
PUBLIC 67280 0 glFogCoordfvEXT
PUBLIC 67300 0 glFogCoordhNV
PUBLIC 67380 0 glFogCoordhvNV
PUBLIC 67400 0 glFogFuncSGIS
PUBLIC 67480 0 glFogf
PUBLIC 67500 0 glFogfv
PUBLIC 67580 0 glFogi
PUBLIC 67600 0 glFogiv
PUBLIC 67680 0 glFogx
PUBLIC 67700 0 glFogxOES
PUBLIC 67780 0 glFogxv
PUBLIC 67800 0 glFogxvOES
PUBLIC 67880 0 glFragmentColorMaterialSGIX
PUBLIC 67900 0 glFragmentCoverageColorNV
PUBLIC 67980 0 glFragmentLightModelfSGIX
PUBLIC 67a00 0 glFragmentLightModelfvSGIX
PUBLIC 67a80 0 glFragmentLightModeliSGIX
PUBLIC 67b00 0 glFragmentLightModelivSGIX
PUBLIC 67b80 0 glFragmentLightfSGIX
PUBLIC 67c00 0 glFragmentLightfvSGIX
PUBLIC 67c80 0 glFragmentLightiSGIX
PUBLIC 67d00 0 glFragmentLightivSGIX
PUBLIC 67d80 0 glFragmentMaterialfSGIX
PUBLIC 67e00 0 glFragmentMaterialfvSGIX
PUBLIC 67e80 0 glFragmentMaterialiSGIX
PUBLIC 67f00 0 glFragmentMaterialivSGIX
PUBLIC 67f80 0 glFrameTerminatorGREMEDY
PUBLIC 68000 0 glFrameZoomSGIX
PUBLIC 68080 0 glFramebufferDrawBufferEXT
PUBLIC 68100 0 glFramebufferDrawBuffersEXT
PUBLIC 68180 0 glFramebufferFetchBarrierEXT
PUBLIC 68200 0 glFramebufferFetchBarrierQCOM
PUBLIC 68280 0 glFramebufferFoveationConfigQCOM
PUBLIC 68300 0 glFramebufferFoveationParametersQCOM
PUBLIC 68380 0 glFramebufferParameteri
PUBLIC 68400 0 glFramebufferParameteriMESA
PUBLIC 68480 0 glFramebufferPixelLocalStorageSizeEXT
PUBLIC 68500 0 glFramebufferReadBufferEXT
PUBLIC 68580 0 glFramebufferRenderbuffer
PUBLIC 68600 0 glFramebufferRenderbufferEXT
PUBLIC 68680 0 glFramebufferRenderbufferOES
PUBLIC 68700 0 glFramebufferSampleLocationsfvARB
PUBLIC 68780 0 glFramebufferSampleLocationsfvNV
PUBLIC 68800 0 glFramebufferSamplePositionsfvAMD
PUBLIC 68880 0 glFramebufferTexture
PUBLIC 68900 0 glFramebufferTexture1D
PUBLIC 68980 0 glFramebufferTexture1DEXT
PUBLIC 68a00 0 glFramebufferTexture2D
PUBLIC 68a80 0 glFramebufferTexture2DDownsampleIMG
PUBLIC 68b00 0 glFramebufferTexture2DEXT
PUBLIC 68b80 0 glFramebufferTexture2DMultisampleEXT
PUBLIC 68c00 0 glFramebufferTexture2DMultisampleIMG
PUBLIC 68c80 0 glFramebufferTexture2DOES
PUBLIC 68d00 0 glFramebufferTexture3D
PUBLIC 68d80 0 glFramebufferTexture3DEXT
PUBLIC 68e00 0 glFramebufferTexture3DOES
PUBLIC 68e80 0 glFramebufferTextureARB
PUBLIC 68f00 0 glFramebufferTextureEXT
PUBLIC 68f80 0 glFramebufferTextureFaceARB
PUBLIC 69000 0 glFramebufferTextureFaceEXT
PUBLIC 69080 0 glFramebufferTextureLayer
PUBLIC 69100 0 glFramebufferTextureLayerARB
PUBLIC 69180 0 glFramebufferTextureLayerDownsampleIMG
PUBLIC 69200 0 glFramebufferTextureLayerEXT
PUBLIC 69280 0 glFramebufferTextureLayerNV
PUBLIC 69300 0 glFramebufferTextureMultisampleMultiviewOVR
PUBLIC 69380 0 glFramebufferTextureMultiviewOVR
PUBLIC 69400 0 glFramebufferTextureOES
PUBLIC 69480 0 glFreeObjectBufferATI
PUBLIC 69500 0 glFrontFace
PUBLIC 69580 0 glFrustum
PUBLIC 69600 0 glFrustumf
PUBLIC 69680 0 glFrustumfOES
PUBLIC 69700 0 glFrustumx
PUBLIC 69780 0 glFrustumxOES
PUBLIC 69800 0 glGenAsyncMarkersSGIX
PUBLIC 69880 0 glGenBuffers
PUBLIC 69900 0 glGenBuffersARB
PUBLIC 69980 0 glGenFencesAPPLE
PUBLIC 69a00 0 glGenFencesNV
PUBLIC 69a80 0 glGenFragmentShadersATI
PUBLIC 69b00 0 glGenFramebuffers
PUBLIC 69b80 0 glGenFramebuffersEXT
PUBLIC 69c00 0 glGenFramebuffersOES
PUBLIC 69c80 0 glGenLists
PUBLIC 69d00 0 glGenNamesAMD
PUBLIC 69d80 0 glGenOcclusionQueriesNV
PUBLIC 69e00 0 glGenPathsNV
PUBLIC 69e80 0 glGenPerfMonitorsAMD
PUBLIC 69f00 0 glGenProgramPipelines
PUBLIC 69f80 0 glGenProgramPipelinesEXT
PUBLIC 6a000 0 glGenProgramsARB
PUBLIC 6a080 0 glGenProgramsNV
PUBLIC 6a100 0 glGenQueries
PUBLIC 6a180 0 glGenQueriesARB
PUBLIC 6a200 0 glGenQueriesEXT
PUBLIC 6a280 0 glGenQueryResourceTagNV
PUBLIC 6a300 0 glGenRenderbuffers
PUBLIC 6a380 0 glGenRenderbuffersEXT
PUBLIC 6a400 0 glGenRenderbuffersOES
PUBLIC 6a480 0 glGenSamplers
PUBLIC 6a500 0 glGenSemaphoresEXT
PUBLIC 6a580 0 glGenSymbolsEXT
PUBLIC 6a600 0 glGenTextures
PUBLIC 6a680 0 glGenTexturesEXT
PUBLIC 6a700 0 glGenTransformFeedbacks
PUBLIC 6a780 0 glGenTransformFeedbacksEXT
PUBLIC 6a800 0 glGenTransformFeedbacksNV
PUBLIC 6a880 0 glGenVertexArrays
PUBLIC 6a900 0 glGenVertexArraysAPPLE
PUBLIC 6a980 0 glGenVertexArraysOES
PUBLIC 6aa00 0 glGenVertexShadersEXT
PUBLIC 6aa80 0 glGenerateMipmap
PUBLIC 6ab00 0 glGenerateMipmapEXT
PUBLIC 6ab80 0 glGenerateMipmapOES
PUBLIC 6ac00 0 glGenerateMultiTexMipmapEXT
PUBLIC 6ac80 0 glGenerateTextureMipmap
PUBLIC 6ad00 0 glGenerateTextureMipmapEXT
PUBLIC 6ad80 0 glGetActiveAtomicCounterBufferiv
PUBLIC 6ae00 0 glGetActiveAttrib
PUBLIC 6ae80 0 glGetActiveAttribARB
PUBLIC 6af00 0 glGetActiveSubroutineName
PUBLIC 6af80 0 glGetActiveSubroutineUniformName
PUBLIC 6b000 0 glGetActiveSubroutineUniformiv
PUBLIC 6b080 0 glGetActiveUniform
PUBLIC 6b100 0 glGetActiveUniformARB
PUBLIC 6b180 0 glGetActiveUniformBlockName
PUBLIC 6b200 0 glGetActiveUniformBlockiv
PUBLIC 6b280 0 glGetActiveUniformName
PUBLIC 6b300 0 glGetActiveUniformsiv
PUBLIC 6b380 0 glGetActiveVaryingNV
PUBLIC 6b400 0 glGetArrayObjectfvATI
PUBLIC 6b480 0 glGetArrayObjectivATI
PUBLIC 6b500 0 glGetAttachedObjectsARB
PUBLIC 6b580 0 glGetAttachedShaders
PUBLIC 6b600 0 glGetAttribLocation
PUBLIC 6b680 0 glGetAttribLocationARB
PUBLIC 6b700 0 glGetBooleanIndexedvEXT
PUBLIC 6b780 0 glGetBooleani_v
PUBLIC 6b800 0 glGetBooleanv
PUBLIC 6b880 0 glGetBufferParameteri64v
PUBLIC 6b900 0 glGetBufferParameteriv
PUBLIC 6b980 0 glGetBufferParameterivARB
PUBLIC 6ba00 0 glGetBufferParameterui64vNV
PUBLIC 6ba80 0 glGetBufferPointerv
PUBLIC 6bb00 0 glGetBufferPointervARB
PUBLIC 6bb80 0 glGetBufferPointervOES
PUBLIC 6bc00 0 glGetBufferSubData
PUBLIC 6bc80 0 glGetBufferSubDataARB
PUBLIC 6bd00 0 glGetClipPlane
PUBLIC 6bd80 0 glGetClipPlanef
PUBLIC 6be00 0 glGetClipPlanefOES
PUBLIC 6be80 0 glGetClipPlanex
PUBLIC 6bf00 0 glGetClipPlanexOES
PUBLIC 6bf80 0 glGetColorTable
PUBLIC 6c000 0 glGetColorTableEXT
PUBLIC 6c080 0 glGetColorTableParameterfv
PUBLIC 6c100 0 glGetColorTableParameterfvEXT
PUBLIC 6c180 0 glGetColorTableParameterfvSGI
PUBLIC 6c200 0 glGetColorTableParameteriv
PUBLIC 6c280 0 glGetColorTableParameterivEXT
PUBLIC 6c300 0 glGetColorTableParameterivSGI
PUBLIC 6c380 0 glGetColorTableSGI
PUBLIC 6c400 0 glGetCombinerInputParameterfvNV
PUBLIC 6c480 0 glGetCombinerInputParameterivNV
PUBLIC 6c500 0 glGetCombinerOutputParameterfvNV
PUBLIC 6c580 0 glGetCombinerOutputParameterivNV
PUBLIC 6c600 0 glGetCombinerStageParameterfvNV
PUBLIC 6c680 0 glGetCommandHeaderNV
PUBLIC 6c700 0 glGetCompressedMultiTexImageEXT
PUBLIC 6c780 0 glGetCompressedTexImage
PUBLIC 6c800 0 glGetCompressedTexImageARB
PUBLIC 6c880 0 glGetCompressedTexImageNV
PUBLIC 6c900 0 glGetCompressedTextureImage
PUBLIC 6c980 0 glGetCompressedTextureImageEXT
PUBLIC 6ca00 0 glGetCompressedTextureSubImage
PUBLIC 6ca80 0 glGetConvolutionFilter
PUBLIC 6cb00 0 glGetConvolutionFilterEXT
PUBLIC 6cb80 0 glGetConvolutionParameterfv
PUBLIC 6cc00 0 glGetConvolutionParameterfvEXT
PUBLIC 6cc80 0 glGetConvolutionParameteriv
PUBLIC 6cd00 0 glGetConvolutionParameterivEXT
PUBLIC 6cd80 0 glGetConvolutionParameterxvOES
PUBLIC 6ce00 0 glGetCoverageModulationTableNV
PUBLIC 6ce80 0 glGetDebugMessageLog
PUBLIC 6cf00 0 glGetDebugMessageLogAMD
PUBLIC 6cf80 0 glGetDebugMessageLogARB
PUBLIC 6d000 0 glGetDebugMessageLogKHR
PUBLIC 6d080 0 glGetDebugMessageLogOES
PUBLIC 6d100 0 glGetDetailTexFuncSGIS
PUBLIC 6d180 0 glGetDoubleIndexedvEXT
PUBLIC 6d200 0 glGetDoublei_v
PUBLIC 6d280 0 glGetDoublei_vEXT
PUBLIC 6d300 0 glGetDoublev
PUBLIC 6d380 0 glGetDriverControlStringQCOM
PUBLIC 6d400 0 glGetDriverControlsQCOM
PUBLIC 6d480 0 glGetError
PUBLIC 6d500 0 glGetFenceivNV
PUBLIC 6d580 0 glGetFinalCombinerInputParameterfvNV
PUBLIC 6d600 0 glGetFinalCombinerInputParameterivNV
PUBLIC 6d680 0 glGetFirstPerfQueryIdINTEL
PUBLIC 6d700 0 glGetFixedv
PUBLIC 6d780 0 glGetFixedvOES
PUBLIC 6d800 0 glGetFloatIndexedvEXT
PUBLIC 6d880 0 glGetFloati_v
PUBLIC 6d900 0 glGetFloati_vEXT
PUBLIC 6d980 0 glGetFloati_vNV
PUBLIC 6da00 0 glGetFloati_vOES
PUBLIC 6da80 0 glGetFloatv
PUBLIC 6db00 0 glGetFogFuncSGIS
PUBLIC 6db80 0 glGetFragDataIndex
PUBLIC 6dc00 0 glGetFragDataIndexEXT
PUBLIC 6dc80 0 glGetFragDataLocation
PUBLIC 6dd00 0 glGetFragDataLocationEXT
PUBLIC 6dd80 0 glGetFragmentLightfvSGIX
PUBLIC 6de00 0 glGetFragmentLightivSGIX
PUBLIC 6de80 0 glGetFragmentMaterialfvSGIX
PUBLIC 6df00 0 glGetFragmentMaterialivSGIX
PUBLIC 6df80 0 glGetFramebufferAttachmentParameteriv
PUBLIC 6e000 0 glGetFramebufferAttachmentParameterivEXT
PUBLIC 6e080 0 glGetFramebufferAttachmentParameterivOES
PUBLIC 6e100 0 glGetFramebufferParameterfvAMD
PUBLIC 6e180 0 glGetFramebufferParameteriv
PUBLIC 6e200 0 glGetFramebufferParameterivEXT
PUBLIC 6e280 0 glGetFramebufferParameterivMESA
PUBLIC 6e300 0 glGetFramebufferPixelLocalStorageSizeEXT
PUBLIC 6e380 0 glGetGraphicsResetStatus
PUBLIC 6e400 0 glGetGraphicsResetStatusARB
PUBLIC 6e480 0 glGetGraphicsResetStatusEXT
PUBLIC 6e500 0 glGetGraphicsResetStatusKHR
PUBLIC 6e580 0 glGetHandleARB
PUBLIC 6e600 0 glGetHistogram
PUBLIC 6e680 0 glGetHistogramEXT
PUBLIC 6e700 0 glGetHistogramParameterfv
PUBLIC 6e780 0 glGetHistogramParameterfvEXT
PUBLIC 6e800 0 glGetHistogramParameteriv
PUBLIC 6e880 0 glGetHistogramParameterivEXT
PUBLIC 6e900 0 glGetHistogramParameterxvOES
PUBLIC 6e980 0 glGetImageHandleARB
PUBLIC 6ea00 0 glGetImageHandleNV
PUBLIC 6ea80 0 glGetImageTransformParameterfvHP
PUBLIC 6eb00 0 glGetImageTransformParameterivHP
PUBLIC 6eb80 0 glGetInfoLogARB
PUBLIC 6ec00 0 glGetInstrumentsSGIX
PUBLIC 6ec80 0 glGetInteger64i_v
PUBLIC 6ed00 0 glGetInteger64v
PUBLIC 6ed80 0 glGetInteger64vAPPLE
PUBLIC 6ee00 0 glGetIntegerIndexedvEXT
PUBLIC 6ee80 0 glGetIntegeri_v
PUBLIC 6ef00 0 glGetIntegeri_vEXT
PUBLIC 6ef80 0 glGetIntegerui64i_vNV
PUBLIC 6f000 0 glGetIntegerui64vNV
PUBLIC 6f080 0 glGetIntegerv
PUBLIC 6f100 0 glGetInternalformatSampleivNV
PUBLIC 6f180 0 glGetInternalformati64v
PUBLIC 6f200 0 glGetInternalformativ
PUBLIC 6f280 0 glGetInvariantBooleanvEXT
PUBLIC 6f300 0 glGetInvariantFloatvEXT
PUBLIC 6f380 0 glGetInvariantIntegervEXT
PUBLIC 6f400 0 glGetLightfv
PUBLIC 6f480 0 glGetLightiv
PUBLIC 6f500 0 glGetLightxOES
PUBLIC 6f580 0 glGetLightxv
PUBLIC 6f600 0 glGetLightxvOES
PUBLIC 6f680 0 glGetListParameterfvSGIX
PUBLIC 6f700 0 glGetListParameterivSGIX
PUBLIC 6f780 0 glGetLocalConstantBooleanvEXT
PUBLIC 6f800 0 glGetLocalConstantFloatvEXT
PUBLIC 6f880 0 glGetLocalConstantIntegervEXT
PUBLIC 6f900 0 glGetMapAttribParameterfvNV
PUBLIC 6f980 0 glGetMapAttribParameterivNV
PUBLIC 6fa00 0 glGetMapControlPointsNV
PUBLIC 6fa80 0 glGetMapParameterfvNV
PUBLIC 6fb00 0 glGetMapParameterivNV
PUBLIC 6fb80 0 glGetMapdv
PUBLIC 6fc00 0 glGetMapfv
PUBLIC 6fc80 0 glGetMapiv
PUBLIC 6fd00 0 glGetMapxvOES
PUBLIC 6fd80 0 glGetMaterialfv
PUBLIC 6fe00 0 glGetMaterialiv
PUBLIC 6fe80 0 glGetMaterialxOES
PUBLIC 6ff00 0 glGetMaterialxv
PUBLIC 6ff80 0 glGetMaterialxvOES
PUBLIC 70000 0 glGetMemoryObjectDetachedResourcesuivNV
PUBLIC 70080 0 glGetMemoryObjectParameterivEXT
PUBLIC 70100 0 glGetMinmax
PUBLIC 70180 0 glGetMinmaxEXT
PUBLIC 70200 0 glGetMinmaxParameterfv
PUBLIC 70280 0 glGetMinmaxParameterfvEXT
PUBLIC 70300 0 glGetMinmaxParameteriv
PUBLIC 70380 0 glGetMinmaxParameterivEXT
PUBLIC 70400 0 glGetMultiTexEnvfvEXT
PUBLIC 70480 0 glGetMultiTexEnvivEXT
PUBLIC 70500 0 glGetMultiTexGendvEXT
PUBLIC 70580 0 glGetMultiTexGenfvEXT
PUBLIC 70600 0 glGetMultiTexGenivEXT
PUBLIC 70680 0 glGetMultiTexImageEXT
PUBLIC 70700 0 glGetMultiTexLevelParameterfvEXT
PUBLIC 70780 0 glGetMultiTexLevelParameterivEXT
PUBLIC 70800 0 glGetMultiTexParameterIivEXT
PUBLIC 70880 0 glGetMultiTexParameterIuivEXT
PUBLIC 70900 0 glGetMultiTexParameterfvEXT
PUBLIC 70980 0 glGetMultiTexParameterivEXT
PUBLIC 70a00 0 glGetMultisamplefv
PUBLIC 70a80 0 glGetMultisamplefvNV
PUBLIC 70b00 0 glGetNamedBufferParameteri64v
PUBLIC 70b80 0 glGetNamedBufferParameteri64vEXT
PUBLIC 70c00 0 glGetNamedBufferParameteriv
PUBLIC 70c80 0 glGetNamedBufferParameterivEXT
PUBLIC 70d00 0 glGetNamedBufferParameterui64vNV
PUBLIC 70d80 0 glGetNamedBufferPointerv
PUBLIC 70e00 0 glGetNamedBufferPointervEXT
PUBLIC 70e80 0 glGetNamedBufferSubData
PUBLIC 70f00 0 glGetNamedBufferSubDataEXT
PUBLIC 70f80 0 glGetNamedFramebufferAttachmentParameteriv
PUBLIC 71000 0 glGetNamedFramebufferAttachmentParameterivEXT
PUBLIC 71080 0 glGetNamedFramebufferParameterfvAMD
PUBLIC 71100 0 glGetNamedFramebufferParameteriv
PUBLIC 71180 0 glGetNamedFramebufferParameterivEXT
PUBLIC 71200 0 glGetNamedProgramLocalParameterIivEXT
PUBLIC 71280 0 glGetNamedProgramLocalParameterIuivEXT
PUBLIC 71300 0 glGetNamedProgramLocalParameterdvEXT
PUBLIC 71380 0 glGetNamedProgramLocalParameterfvEXT
PUBLIC 71400 0 glGetNamedProgramStringEXT
PUBLIC 71480 0 glGetNamedProgramivEXT
PUBLIC 71500 0 glGetNamedRenderbufferParameteriv
PUBLIC 71580 0 glGetNamedRenderbufferParameterivEXT
PUBLIC 71600 0 glGetNamedStringARB
PUBLIC 71680 0 glGetNamedStringivARB
PUBLIC 71700 0 glGetNextPerfQueryIdINTEL
PUBLIC 71780 0 glGetObjectBufferfvATI
PUBLIC 71800 0 glGetObjectBufferivATI
PUBLIC 71880 0 glGetObjectLabel
PUBLIC 71900 0 glGetObjectLabelEXT
PUBLIC 71980 0 glGetObjectLabelKHR
PUBLIC 71a00 0 glGetObjectLabelOES
PUBLIC 71a80 0 glGetObjectParameterfvARB
PUBLIC 71b00 0 glGetObjectParameterivAPPLE
PUBLIC 71b80 0 glGetObjectParameterivARB
PUBLIC 71c00 0 glGetObjectPtrLabel
PUBLIC 71c80 0 glGetObjectPtrLabelKHR
PUBLIC 71d00 0 glGetObjectPtrLabelOES
PUBLIC 71d80 0 glGetOcclusionQueryivNV
PUBLIC 71e00 0 glGetOcclusionQueryuivNV
PUBLIC 71e80 0 glGetPathColorGenfvNV
PUBLIC 71f00 0 glGetPathColorGenivNV
PUBLIC 71f80 0 glGetPathCommandsNV
PUBLIC 72000 0 glGetPathCoordsNV
PUBLIC 72080 0 glGetPathDashArrayNV
PUBLIC 72100 0 glGetPathLengthNV
PUBLIC 72180 0 glGetPathMetricRangeNV
PUBLIC 72200 0 glGetPathMetricsNV
PUBLIC 72280 0 glGetPathParameterfvNV
PUBLIC 72300 0 glGetPathParameterivNV
PUBLIC 72380 0 glGetPathSpacingNV
PUBLIC 72400 0 glGetPathTexGenfvNV
PUBLIC 72480 0 glGetPathTexGenivNV
PUBLIC 72500 0 glGetPerfCounterInfoINTEL
PUBLIC 72580 0 glGetPerfMonitorCounterDataAMD
PUBLIC 72600 0 glGetPerfMonitorCounterInfoAMD
PUBLIC 72680 0 glGetPerfMonitorCounterStringAMD
PUBLIC 72700 0 glGetPerfMonitorCountersAMD
PUBLIC 72780 0 glGetPerfMonitorGroupStringAMD
PUBLIC 72800 0 glGetPerfMonitorGroupsAMD
PUBLIC 72880 0 glGetPerfQueryDataINTEL
PUBLIC 72900 0 glGetPerfQueryIdByNameINTEL
PUBLIC 72980 0 glGetPerfQueryInfoINTEL
PUBLIC 72a00 0 glGetPixelMapfv
PUBLIC 72a80 0 glGetPixelMapuiv
PUBLIC 72b00 0 glGetPixelMapusv
PUBLIC 72b80 0 glGetPixelMapxv
PUBLIC 72c00 0 glGetPixelTexGenParameterfvSGIS
PUBLIC 72c80 0 glGetPixelTexGenParameterivSGIS
PUBLIC 72d00 0 glGetPixelTransformParameterfvEXT
PUBLIC 72d80 0 glGetPixelTransformParameterivEXT
PUBLIC 72e00 0 glGetPointerIndexedvEXT
PUBLIC 72e80 0 glGetPointeri_vEXT
PUBLIC 72f00 0 glGetPointerv
PUBLIC 72f80 0 glGetPointervEXT
PUBLIC 73000 0 glGetPointervKHR
PUBLIC 73080 0 glGetPointervOES
PUBLIC 73100 0 glGetPolygonStipple
PUBLIC 73180 0 glGetProgramBinary
PUBLIC 73200 0 glGetProgramBinaryOES
PUBLIC 73280 0 glGetProgramEnvParameterIivNV
PUBLIC 73300 0 glGetProgramEnvParameterIuivNV
PUBLIC 73380 0 glGetProgramEnvParameterdvARB
PUBLIC 73400 0 glGetProgramEnvParameterfvARB
PUBLIC 73480 0 glGetProgramInfoLog
PUBLIC 73500 0 glGetProgramInterfaceiv
PUBLIC 73580 0 glGetProgramLocalParameterIivNV
PUBLIC 73600 0 glGetProgramLocalParameterIuivNV
PUBLIC 73680 0 glGetProgramLocalParameterdvARB
PUBLIC 73700 0 glGetProgramLocalParameterfvARB
PUBLIC 73780 0 glGetProgramNamedParameterdvNV
PUBLIC 73800 0 glGetProgramNamedParameterfvNV
PUBLIC 73880 0 glGetProgramParameterdvNV
PUBLIC 73900 0 glGetProgramParameterfvNV
PUBLIC 73980 0 glGetProgramPipelineInfoLog
PUBLIC 73a00 0 glGetProgramPipelineInfoLogEXT
PUBLIC 73a80 0 glGetProgramPipelineiv
PUBLIC 73b00 0 glGetProgramPipelineivEXT
PUBLIC 73b80 0 glGetProgramResourceIndex
PUBLIC 73c00 0 glGetProgramResourceLocation
PUBLIC 73c80 0 glGetProgramResourceLocationIndex
PUBLIC 73d00 0 glGetProgramResourceLocationIndexEXT
PUBLIC 73d80 0 glGetProgramResourceName
PUBLIC 73e00 0 glGetProgramResourcefvNV
PUBLIC 73e80 0 glGetProgramResourceiv
PUBLIC 73f00 0 glGetProgramStageiv
PUBLIC 73f80 0 glGetProgramStringARB
PUBLIC 74000 0 glGetProgramStringNV
PUBLIC 74080 0 glGetProgramSubroutineParameteruivNV
PUBLIC 74100 0 glGetProgramiv
PUBLIC 74180 0 glGetProgramivARB
PUBLIC 74200 0 glGetProgramivNV
PUBLIC 74280 0 glGetQueryBufferObjecti64v
PUBLIC 74300 0 glGetQueryBufferObjectiv
PUBLIC 74380 0 glGetQueryBufferObjectui64v
PUBLIC 74400 0 glGetQueryBufferObjectuiv
PUBLIC 74480 0 glGetQueryIndexediv
PUBLIC 74500 0 glGetQueryObjecti64v
PUBLIC 74580 0 glGetQueryObjecti64vEXT
PUBLIC 74600 0 glGetQueryObjectiv
PUBLIC 74680 0 glGetQueryObjectivARB
PUBLIC 74700 0 glGetQueryObjectivEXT
PUBLIC 74780 0 glGetQueryObjectui64v
PUBLIC 74800 0 glGetQueryObjectui64vEXT
PUBLIC 74880 0 glGetQueryObjectui64vNV
PUBLIC 74900 0 glGetQueryObjectuiv
PUBLIC 74980 0 glGetQueryObjectuivARB
PUBLIC 74a00 0 glGetQueryObjectuivEXT
PUBLIC 74a80 0 glGetQueryiv
PUBLIC 74b00 0 glGetQueryivARB
PUBLIC 74b80 0 glGetQueryivEXT
PUBLIC 74c00 0 glGetRenderbufferParameteriv
PUBLIC 74c80 0 glGetRenderbufferParameterivEXT
PUBLIC 74d00 0 glGetRenderbufferParameterivOES
PUBLIC 74d80 0 glGetSamplerParameterIiv
PUBLIC 74e00 0 glGetSamplerParameterIivEXT
PUBLIC 74e80 0 glGetSamplerParameterIivOES
PUBLIC 74f00 0 glGetSamplerParameterIuiv
PUBLIC 74f80 0 glGetSamplerParameterIuivEXT
PUBLIC 75000 0 glGetSamplerParameterIuivOES
PUBLIC 75080 0 glGetSamplerParameterfv
PUBLIC 75100 0 glGetSamplerParameteriv
PUBLIC 75180 0 glGetSemaphoreParameterui64vEXT
PUBLIC 75200 0 glGetSeparableFilter
PUBLIC 75280 0 glGetSeparableFilterEXT
PUBLIC 75300 0 glGetShaderInfoLog
PUBLIC 75380 0 glGetShaderPrecisionFormat
PUBLIC 75400 0 glGetShaderSource
PUBLIC 75480 0 glGetShaderSourceARB
PUBLIC 75500 0 glGetShaderiv
PUBLIC 75580 0 glGetShadingRateImagePaletteNV
PUBLIC 75600 0 glGetShadingRateSampleLocationivNV
PUBLIC 75680 0 glGetSharpenTexFuncSGIS
PUBLIC 75700 0 glGetStageIndexNV
PUBLIC 75780 0 glGetString
PUBLIC 75800 0 glGetStringi
PUBLIC 75880 0 glGetSubroutineIndex
PUBLIC 75900 0 glGetSubroutineUniformLocation
PUBLIC 75980 0 glGetSynciv
PUBLIC 75a00 0 glGetSyncivAPPLE
PUBLIC 75a80 0 glGetSyncv64NVX
PUBLIC 75b00 0 glGetTexBumpParameterfvATI
PUBLIC 75b80 0 glGetTexBumpParameterivATI
PUBLIC 75c00 0 glGetTexEnvfv
PUBLIC 75c80 0 glGetTexEnviv
PUBLIC 75d00 0 glGetTexEnvxv
PUBLIC 75d80 0 glGetTexEnvxvOES
PUBLIC 75e00 0 glGetTexFilterFuncSGIS
PUBLIC 75e80 0 glGetTexGendv
PUBLIC 75f00 0 glGetTexGenfv
PUBLIC 75f80 0 glGetTexGenfvOES
PUBLIC 76000 0 glGetTexGeniv
PUBLIC 76080 0 glGetTexGenivOES
PUBLIC 76100 0 glGetTexGenxvOES
PUBLIC 76180 0 glGetTexImage
PUBLIC 76200 0 glGetTexImageNV
PUBLIC 76280 0 glGetTexLevelParameterfv
PUBLIC 76300 0 glGetTexLevelParameterfvNV
PUBLIC 76380 0 glGetTexLevelParameteriv
PUBLIC 76400 0 glGetTexLevelParameterivNV
PUBLIC 76480 0 glGetTexLevelParameterxvOES
PUBLIC 76500 0 glGetTexParameterIiv
PUBLIC 76580 0 glGetTexParameterIivEXT
PUBLIC 76600 0 glGetTexParameterIivOES
PUBLIC 76680 0 glGetTexParameterIuiv
PUBLIC 76700 0 glGetTexParameterIuivEXT
PUBLIC 76780 0 glGetTexParameterIuivOES
PUBLIC 76800 0 glGetTexParameterPointervAPPLE
PUBLIC 76880 0 glGetTexParameterfv
PUBLIC 76900 0 glGetTexParameteriv
PUBLIC 76980 0 glGetTexParameterxv
PUBLIC 76a00 0 glGetTexParameterxvOES
PUBLIC 76a80 0 glGetTextureHandleARB
PUBLIC 76b00 0 glGetTextureHandleIMG
PUBLIC 76b80 0 glGetTextureHandleNV
PUBLIC 76c00 0 glGetTextureImage
PUBLIC 76c80 0 glGetTextureImageEXT
PUBLIC 76d00 0 glGetTextureLevelParameterfv
PUBLIC 76d80 0 glGetTextureLevelParameterfvEXT
PUBLIC 76e00 0 glGetTextureLevelParameteriv
PUBLIC 76e80 0 glGetTextureLevelParameterivEXT
PUBLIC 76f00 0 glGetTextureParameterIiv
PUBLIC 76f80 0 glGetTextureParameterIivEXT
PUBLIC 77000 0 glGetTextureParameterIuiv
PUBLIC 77080 0 glGetTextureParameterIuivEXT
PUBLIC 77100 0 glGetTextureParameterfv
PUBLIC 77180 0 glGetTextureParameterfvEXT
PUBLIC 77200 0 glGetTextureParameteriv
PUBLIC 77280 0 glGetTextureParameterivEXT
PUBLIC 77300 0 glGetTextureSamplerHandleARB
PUBLIC 77380 0 glGetTextureSamplerHandleIMG
PUBLIC 77400 0 glGetTextureSamplerHandleNV
PUBLIC 77480 0 glGetTextureSubImage
PUBLIC 77500 0 glGetTrackMatrixivNV
PUBLIC 77580 0 glGetTransformFeedbackVarying
PUBLIC 77600 0 glGetTransformFeedbackVaryingEXT
PUBLIC 77680 0 glGetTransformFeedbackVaryingNV
PUBLIC 77700 0 glGetTransformFeedbacki64_v
PUBLIC 77780 0 glGetTransformFeedbacki_v
PUBLIC 77800 0 glGetTransformFeedbackiv
PUBLIC 77880 0 glGetTranslatedShaderSourceANGLE
PUBLIC 77900 0 glGetUniformBlockIndex
PUBLIC 77980 0 glGetUniformBufferSizeEXT
PUBLIC 77a00 0 glGetUniformIndices
PUBLIC 77a80 0 glGetUniformLocation
PUBLIC 77b00 0 glGetUniformLocationARB
PUBLIC 77b80 0 glGetUniformOffsetEXT
PUBLIC 77c00 0 glGetUniformSubroutineuiv
PUBLIC 77c80 0 glGetUniformdv
PUBLIC 77d00 0 glGetUniformfv
PUBLIC 77d80 0 glGetUniformfvARB
PUBLIC 77e00 0 glGetUniformi64vARB
PUBLIC 77e80 0 glGetUniformi64vNV
PUBLIC 77f00 0 glGetUniformiv
PUBLIC 77f80 0 glGetUniformivARB
PUBLIC 78000 0 glGetUniformui64vARB
PUBLIC 78080 0 glGetUniformui64vNV
PUBLIC 78100 0 glGetUniformuiv
PUBLIC 78180 0 glGetUniformuivEXT
PUBLIC 78200 0 glGetUnsignedBytei_vEXT
PUBLIC 78280 0 glGetUnsignedBytevEXT
PUBLIC 78300 0 glGetVariantArrayObjectfvATI
PUBLIC 78380 0 glGetVariantArrayObjectivATI
PUBLIC 78400 0 glGetVariantBooleanvEXT
PUBLIC 78480 0 glGetVariantFloatvEXT
PUBLIC 78500 0 glGetVariantIntegervEXT
PUBLIC 78580 0 glGetVariantPointervEXT
PUBLIC 78600 0 glGetVaryingLocationNV
PUBLIC 78680 0 glGetVertexArrayIndexed64iv
PUBLIC 78700 0 glGetVertexArrayIndexediv
PUBLIC 78780 0 glGetVertexArrayIntegeri_vEXT
PUBLIC 78800 0 glGetVertexArrayIntegervEXT
PUBLIC 78880 0 glGetVertexArrayPointeri_vEXT
PUBLIC 78900 0 glGetVertexArrayPointervEXT
PUBLIC 78980 0 glGetVertexArrayiv
PUBLIC 78a00 0 glGetVertexAttribArrayObjectfvATI
PUBLIC 78a80 0 glGetVertexAttribArrayObjectivATI
PUBLIC 78b00 0 glGetVertexAttribIiv
PUBLIC 78b80 0 glGetVertexAttribIivEXT
PUBLIC 78c00 0 glGetVertexAttribIuiv
PUBLIC 78c80 0 glGetVertexAttribIuivEXT
PUBLIC 78d00 0 glGetVertexAttribLdv
PUBLIC 78d80 0 glGetVertexAttribLdvEXT
PUBLIC 78e00 0 glGetVertexAttribLi64vNV
PUBLIC 78e80 0 glGetVertexAttribLui64vARB
PUBLIC 78f00 0 glGetVertexAttribLui64vNV
PUBLIC 78f80 0 glGetVertexAttribPointerv
PUBLIC 79000 0 glGetVertexAttribPointervARB
PUBLIC 79080 0 glGetVertexAttribPointervNV
PUBLIC 79100 0 glGetVertexAttribdv
PUBLIC 79180 0 glGetVertexAttribdvARB
PUBLIC 79200 0 glGetVertexAttribdvNV
PUBLIC 79280 0 glGetVertexAttribfv
PUBLIC 79300 0 glGetVertexAttribfvARB
PUBLIC 79380 0 glGetVertexAttribfvNV
PUBLIC 79400 0 glGetVertexAttribiv
PUBLIC 79480 0 glGetVertexAttribivARB
PUBLIC 79500 0 glGetVertexAttribivNV
PUBLIC 79580 0 glGetVideoCaptureANCStreamivNVX
PUBLIC 79600 0 glGetVideoCaptureStreamdvNV
PUBLIC 79680 0 glGetVideoCaptureStreamfvNV
PUBLIC 79700 0 glGetVideoCaptureStreamivNV
PUBLIC 79780 0 glGetVideoCaptureivNV
PUBLIC 79800 0 glGetVideoi64vNV
PUBLIC 79880 0 glGetVideoivNV
PUBLIC 79900 0 glGetVideoui64vNV
PUBLIC 79980 0 glGetVideouivNV
PUBLIC 79a00 0 glGetVkProcAddrNV
PUBLIC 79a80 0 glGetnColorTable
PUBLIC 79b00 0 glGetnColorTableARB
PUBLIC 79b80 0 glGetnCompressedTexImage
PUBLIC 79c00 0 glGetnCompressedTexImageARB
PUBLIC 79c80 0 glGetnConvolutionFilter
PUBLIC 79d00 0 glGetnConvolutionFilterARB
PUBLIC 79d80 0 glGetnHistogram
PUBLIC 79e00 0 glGetnHistogramARB
PUBLIC 79e80 0 glGetnMapdv
PUBLIC 79f00 0 glGetnMapdvARB
PUBLIC 79f80 0 glGetnMapfv
PUBLIC 7a000 0 glGetnMapfvARB
PUBLIC 7a080 0 glGetnMapiv
PUBLIC 7a100 0 glGetnMapivARB
PUBLIC 7a180 0 glGetnMinmax
PUBLIC 7a200 0 glGetnMinmaxARB
PUBLIC 7a280 0 glGetnPixelMapfv
PUBLIC 7a300 0 glGetnPixelMapfvARB
PUBLIC 7a380 0 glGetnPixelMapuiv
PUBLIC 7a400 0 glGetnPixelMapuivARB
PUBLIC 7a480 0 glGetnPixelMapusv
PUBLIC 7a500 0 glGetnPixelMapusvARB
PUBLIC 7a580 0 glGetnPolygonStipple
PUBLIC 7a600 0 glGetnPolygonStippleARB
PUBLIC 7a680 0 glGetnSeparableFilter
PUBLIC 7a700 0 glGetnSeparableFilterARB
PUBLIC 7a780 0 glGetnTexImage
PUBLIC 7a800 0 glGetnTexImageARB
PUBLIC 7a880 0 glGetnUniformdv
PUBLIC 7a900 0 glGetnUniformdvARB
PUBLIC 7a980 0 glGetnUniformfv
PUBLIC 7aa00 0 glGetnUniformfvARB
PUBLIC 7aa80 0 glGetnUniformfvEXT
PUBLIC 7ab00 0 glGetnUniformfvKHR
PUBLIC 7ab80 0 glGetnUniformi64vARB
PUBLIC 7ac00 0 glGetnUniformiv
PUBLIC 7ac80 0 glGetnUniformivARB
PUBLIC 7ad00 0 glGetnUniformivEXT
PUBLIC 7ad80 0 glGetnUniformivKHR
PUBLIC 7ae00 0 glGetnUniformui64vARB
PUBLIC 7ae80 0 glGetnUniformuiv
PUBLIC 7af00 0 glGetnUniformuivARB
PUBLIC 7af80 0 glGetnUniformuivKHR
PUBLIC 7b000 0 glGlobalAlphaFactorbSUN
PUBLIC 7b080 0 glGlobalAlphaFactordSUN
PUBLIC 7b100 0 glGlobalAlphaFactorfSUN
PUBLIC 7b180 0 glGlobalAlphaFactoriSUN
PUBLIC 7b200 0 glGlobalAlphaFactorsSUN
PUBLIC 7b280 0 glGlobalAlphaFactorubSUN
PUBLIC 7b300 0 glGlobalAlphaFactoruiSUN
PUBLIC 7b380 0 glGlobalAlphaFactorusSUN
PUBLIC 7b400 0 glGpuSyncAcquireNVX
PUBLIC 7b480 0 glGpuSyncCreateNVX
PUBLIC 7b500 0 glGpuSyncDestroyNVX
PUBLIC 7b580 0 glGpuSyncReleaseNVX
PUBLIC 7b600 0 glHint
PUBLIC 7b680 0 glHintPGI
PUBLIC 7b700 0 glHistogram
PUBLIC 7b780 0 glHistogramEXT
PUBLIC 7b800 0 glIglooInterfaceSGIX
PUBLIC 7b880 0 glImageTransformParameterfHP
PUBLIC 7b900 0 glImageTransformParameterfvHP
PUBLIC 7b980 0 glImageTransformParameteriHP
PUBLIC 7ba00 0 glImageTransformParameterivHP
PUBLIC 7ba80 0 glImportMemoryFdEXT
PUBLIC 7bb00 0 glImportMemoryWin32HandleEXT
PUBLIC 7bb80 0 glImportMemoryWin32NameEXT
PUBLIC 7bc00 0 glImportSemaphoreFdEXT
PUBLIC 7bc80 0 glImportSemaphoreWin32HandleEXT
PUBLIC 7bd00 0 glImportSemaphoreWin32NameEXT
PUBLIC 7bd80 0 glImportSyncEXT
PUBLIC 7be00 0 glIndexFormatNV
PUBLIC 7be80 0 glIndexFuncEXT
PUBLIC 7bf00 0 glIndexMask
PUBLIC 7bf80 0 glIndexMaterialEXT
PUBLIC 7c000 0 glIndexPointer
PUBLIC 7c080 0 glIndexPointerEXT
PUBLIC 7c100 0 glIndexPointerListIBM
PUBLIC 7c180 0 glIndexd
PUBLIC 7c200 0 glIndexdv
PUBLIC 7c280 0 glIndexf
PUBLIC 7c300 0 glIndexfv
PUBLIC 7c380 0 glIndexi
PUBLIC 7c400 0 glIndexiv
PUBLIC 7c480 0 glIndexs
PUBLIC 7c500 0 glIndexsv
PUBLIC 7c580 0 glIndexub
PUBLIC 7c600 0 glIndexubv
PUBLIC 7c680 0 glIndexxOES
PUBLIC 7c700 0 glIndexxvOES
PUBLIC 7c780 0 glInitNames
PUBLIC 7c800 0 glInsertComponentEXT
PUBLIC 7c880 0 glInsertEventMarkerEXT
PUBLIC 7c900 0 glInstrumentsBufferSGIX
PUBLIC 7c980 0 glInterleavedArrays
PUBLIC 7ca00 0 glInterpolatePathsNV
PUBLIC 7ca80 0 glInvalidateBufferData
PUBLIC 7cb00 0 glInvalidateBufferSubData
PUBLIC 7cb80 0 glInvalidateFramebuffer
PUBLIC 7cc00 0 glInvalidateNamedFramebufferData
PUBLIC 7cc80 0 glInvalidateNamedFramebufferSubData
PUBLIC 7cd00 0 glInvalidateSubFramebuffer
PUBLIC 7cd80 0 glInvalidateTexImage
PUBLIC 7ce00 0 glInvalidateTexSubImage
PUBLIC 7ce80 0 glIsAsyncMarkerSGIX
PUBLIC 7cf00 0 glIsBuffer
PUBLIC 7cf80 0 glIsBufferARB
PUBLIC 7d000 0 glIsBufferResidentNV
PUBLIC 7d080 0 glIsCommandListNV
PUBLIC 7d100 0 glIsEnabled
PUBLIC 7d180 0 glIsEnabledIndexedEXT
PUBLIC 7d200 0 glIsEnabledi
PUBLIC 7d280 0 glIsEnablediEXT
PUBLIC 7d300 0 glIsEnablediNV
PUBLIC 7d380 0 glIsEnablediOES
PUBLIC 7d400 0 glIsFenceAPPLE
PUBLIC 7d480 0 glIsFenceNV
PUBLIC 7d500 0 glIsFramebuffer
PUBLIC 7d580 0 glIsFramebufferEXT
PUBLIC 7d600 0 glIsFramebufferOES
PUBLIC 7d680 0 glIsImageHandleResidentARB
PUBLIC 7d700 0 glIsImageHandleResidentNV
PUBLIC 7d780 0 glIsList
PUBLIC 7d800 0 glIsMemoryObjectEXT
PUBLIC 7d880 0 glIsNameAMD
PUBLIC 7d900 0 glIsNamedBufferResidentNV
PUBLIC 7d980 0 glIsNamedStringARB
PUBLIC 7da00 0 glIsObjectBufferATI
PUBLIC 7da80 0 glIsOcclusionQueryNV
PUBLIC 7db00 0 glIsPathNV
PUBLIC 7db80 0 glIsPointInFillPathNV
PUBLIC 7dc00 0 glIsPointInStrokePathNV
PUBLIC 7dc80 0 glIsProgram
PUBLIC 7dd00 0 glIsProgramARB
PUBLIC 7dd80 0 glIsProgramNV
PUBLIC 7de00 0 glIsProgramPipeline
PUBLIC 7de80 0 glIsProgramPipelineEXT
PUBLIC 7df00 0 glIsQuery
PUBLIC 7df80 0 glIsQueryARB
PUBLIC 7e000 0 glIsQueryEXT
PUBLIC 7e080 0 glIsRenderbuffer
PUBLIC 7e100 0 glIsRenderbufferEXT
PUBLIC 7e180 0 glIsRenderbufferOES
PUBLIC 7e200 0 glIsSampler
PUBLIC 7e280 0 glIsSemaphoreEXT
PUBLIC 7e300 0 glIsShader
PUBLIC 7e380 0 glIsStateNV
PUBLIC 7e400 0 glIsSync
PUBLIC 7e480 0 glIsSyncAPPLE
PUBLIC 7e500 0 glIsTexture
PUBLIC 7e580 0 glIsTextureEXT
PUBLIC 7e600 0 glIsTextureHandleResidentARB
PUBLIC 7e680 0 glIsTextureHandleResidentNV
PUBLIC 7e700 0 glIsTransformFeedback
PUBLIC 7e780 0 glIsTransformFeedbackEXT
PUBLIC 7e800 0 glIsTransformFeedbackNV
PUBLIC 7e880 0 glIsVariantEnabledEXT
PUBLIC 7e900 0 glIsVertexArray
PUBLIC 7e980 0 glIsVertexArrayAPPLE
PUBLIC 7ea00 0 glIsVertexArrayOES
PUBLIC 7ea80 0 glIsVertexAttribEnabledAPPLE
PUBLIC 7eb00 0 glLGPUCopyImageSubDataNVX
PUBLIC 7eb80 0 glLGPUInterlockNVX
PUBLIC 7ec00 0 glLGPUNamedBufferSubDataNVX
PUBLIC 7ec80 0 glLabelObjectEXT
PUBLIC 7ed00 0 glLightEnviSGIX
PUBLIC 7ed80 0 glLightModelf
PUBLIC 7ee00 0 glLightModelfv
PUBLIC 7ee80 0 glLightModeli
PUBLIC 7ef00 0 glLightModeliv
PUBLIC 7ef80 0 glLightModelx
PUBLIC 7f000 0 glLightModelxOES
PUBLIC 7f080 0 glLightModelxv
PUBLIC 7f100 0 glLightModelxvOES
PUBLIC 7f180 0 glLightf
PUBLIC 7f200 0 glLightfv
PUBLIC 7f280 0 glLighti
PUBLIC 7f300 0 glLightiv
PUBLIC 7f380 0 glLightx
PUBLIC 7f400 0 glLightxOES
PUBLIC 7f480 0 glLightxv
PUBLIC 7f500 0 glLightxvOES
PUBLIC 7f580 0 glLineStipple
PUBLIC 7f600 0 glLineWidth
PUBLIC 7f680 0 glLineWidthx
PUBLIC 7f700 0 glLineWidthxOES
PUBLIC 7f780 0 glLinkProgram
PUBLIC 7f800 0 glLinkProgramARB
PUBLIC 7f880 0 glListBase
PUBLIC 7f900 0 glListDrawCommandsStatesClientNV
PUBLIC 7f980 0 glListParameterfSGIX
PUBLIC 7fa00 0 glListParameterfvSGIX
PUBLIC 7fa80 0 glListParameteriSGIX
PUBLIC 7fb00 0 glListParameterivSGIX
PUBLIC 7fb80 0 glLoadIdentity
PUBLIC 7fc00 0 glLoadIdentityDeformationMapSGIX
PUBLIC 7fc80 0 glLoadMatrixd
PUBLIC 7fd00 0 glLoadMatrixf
PUBLIC 7fd80 0 glLoadMatrixx
PUBLIC 7fe00 0 glLoadMatrixxOES
PUBLIC 7fe80 0 glLoadName
PUBLIC 7ff00 0 glLoadPaletteFromModelViewMatrixOES
PUBLIC 7ff80 0 glLoadProgramNV
PUBLIC 80000 0 glLoadTransformEXT
PUBLIC 80080 0 glLoadTransposeMatrixd
PUBLIC 80100 0 glLoadTransposeMatrixdARB
PUBLIC 80180 0 glLoadTransposeMatrixf
PUBLIC 80200 0 glLoadTransposeMatrixfARB
PUBLIC 80280 0 glLoadTransposeMatrixxOES
PUBLIC 80300 0 glLockArraysEXT
PUBLIC 80380 0 glLogicOp
PUBLIC 80400 0 glMakeBufferNonResidentNV
PUBLIC 80480 0 glMakeBufferResidentNV
PUBLIC 80500 0 glMakeImageHandleNonResidentARB
PUBLIC 80580 0 glMakeImageHandleNonResidentNV
PUBLIC 80600 0 glMakeImageHandleResidentARB
PUBLIC 80680 0 glMakeImageHandleResidentNV
PUBLIC 80700 0 glMakeNamedBufferNonResidentNV
PUBLIC 80780 0 glMakeNamedBufferResidentNV
PUBLIC 80800 0 glMakeTextureHandleNonResidentARB
PUBLIC 80880 0 glMakeTextureHandleNonResidentNV
PUBLIC 80900 0 glMakeTextureHandleResidentARB
PUBLIC 80980 0 glMakeTextureHandleResidentNV
PUBLIC 80a00 0 glMap1d
PUBLIC 80a80 0 glMap1f
PUBLIC 80b00 0 glMap1xOES
PUBLIC 80b80 0 glMap2d
PUBLIC 80c00 0 glMap2f
PUBLIC 80c80 0 glMap2xOES
PUBLIC 80d00 0 glMapBuffer
PUBLIC 80d80 0 glMapBufferARB
PUBLIC 80e00 0 glMapBufferOES
PUBLIC 80e80 0 glMapBufferRange
PUBLIC 80f00 0 glMapBufferRangeEXT
PUBLIC 80f80 0 glMapControlPointsNV
PUBLIC 81000 0 glMapGrid1d
PUBLIC 81080 0 glMapGrid1f
PUBLIC 81100 0 glMapGrid1xOES
PUBLIC 81180 0 glMapGrid2d
PUBLIC 81200 0 glMapGrid2f
PUBLIC 81280 0 glMapGrid2xOES
PUBLIC 81300 0 glMapNamedBuffer
PUBLIC 81380 0 glMapNamedBufferEXT
PUBLIC 81400 0 glMapNamedBufferRange
PUBLIC 81480 0 glMapNamedBufferRangeEXT
PUBLIC 81500 0 glMapObjectBufferATI
PUBLIC 81580 0 glMapParameterfvNV
PUBLIC 81600 0 glMapParameterivNV
PUBLIC 81680 0 glMapTexture2DINTEL
PUBLIC 81700 0 glMapVertexAttrib1dAPPLE
PUBLIC 81780 0 glMapVertexAttrib1fAPPLE
PUBLIC 81800 0 glMapVertexAttrib2dAPPLE
PUBLIC 81880 0 glMapVertexAttrib2fAPPLE
PUBLIC 81900 0 glMaterialf
PUBLIC 81980 0 glMaterialfv
PUBLIC 81a00 0 glMateriali
PUBLIC 81a80 0 glMaterialiv
PUBLIC 81b00 0 glMaterialx
PUBLIC 81b80 0 glMaterialxOES
PUBLIC 81c00 0 glMaterialxv
PUBLIC 81c80 0 glMaterialxvOES
PUBLIC 81d00 0 glMatrixFrustumEXT
PUBLIC 81d80 0 glMatrixIndexPointerARB
PUBLIC 81e00 0 glMatrixIndexPointerOES
PUBLIC 81e80 0 glMatrixIndexubvARB
PUBLIC 81f00 0 glMatrixIndexuivARB
PUBLIC 81f80 0 glMatrixIndexusvARB
PUBLIC 82000 0 glMatrixLoad3x2fNV
PUBLIC 82080 0 glMatrixLoad3x3fNV
PUBLIC 82100 0 glMatrixLoadIdentityEXT
PUBLIC 82180 0 glMatrixLoadTranspose3x3fNV
PUBLIC 82200 0 glMatrixLoadTransposedEXT
PUBLIC 82280 0 glMatrixLoadTransposefEXT
PUBLIC 82300 0 glMatrixLoaddEXT
PUBLIC 82380 0 glMatrixLoadfEXT
PUBLIC 82400 0 glMatrixMode
PUBLIC 82480 0 glMatrixMult3x2fNV
PUBLIC 82500 0 glMatrixMult3x3fNV
PUBLIC 82580 0 glMatrixMultTranspose3x3fNV
PUBLIC 82600 0 glMatrixMultTransposedEXT
PUBLIC 82680 0 glMatrixMultTransposefEXT
PUBLIC 82700 0 glMatrixMultdEXT
PUBLIC 82780 0 glMatrixMultfEXT
PUBLIC 82800 0 glMatrixOrthoEXT
PUBLIC 82880 0 glMatrixPopEXT
PUBLIC 82900 0 glMatrixPushEXT
PUBLIC 82980 0 glMatrixRotatedEXT
PUBLIC 82a00 0 glMatrixRotatefEXT
PUBLIC 82a80 0 glMatrixScaledEXT
PUBLIC 82b00 0 glMatrixScalefEXT
PUBLIC 82b80 0 glMatrixTranslatedEXT
PUBLIC 82c00 0 glMatrixTranslatefEXT
PUBLIC 82c80 0 glMaxShaderCompilerThreadsARB
PUBLIC 82d00 0 glMaxShaderCompilerThreadsKHR
PUBLIC 82d80 0 glMemoryBarrier
PUBLIC 82e00 0 glMemoryBarrierByRegion
PUBLIC 82e80 0 glMemoryBarrierEXT
PUBLIC 82f00 0 glMemoryObjectParameterivEXT
PUBLIC 82f80 0 glMinSampleShading
PUBLIC 83000 0 glMinSampleShadingARB
PUBLIC 83080 0 glMinSampleShadingOES
PUBLIC 83100 0 glMinmax
PUBLIC 83180 0 glMinmaxEXT
PUBLIC 83200 0 glMultMatrixd
PUBLIC 83280 0 glMultMatrixf
PUBLIC 83300 0 glMultMatrixx
PUBLIC 83380 0 glMultMatrixxOES
PUBLIC 83400 0 glMultTransformEXT
PUBLIC 83480 0 glMultTransposeMatrixd
PUBLIC 83500 0 glMultTransposeMatrixdARB
PUBLIC 83580 0 glMultTransposeMatrixf
PUBLIC 83600 0 glMultTransposeMatrixfARB
PUBLIC 83680 0 glMultTransposeMatrixxOES
PUBLIC 83700 0 glMultiDrawArrays
PUBLIC 83780 0 glMultiDrawArraysEXT
PUBLIC 83800 0 glMultiDrawArraysIndirect
PUBLIC 83880 0 glMultiDrawArraysIndirectAMD
PUBLIC 83900 0 glMultiDrawArraysIndirectBindlessCountNV
PUBLIC 83980 0 glMultiDrawArraysIndirectBindlessNV
PUBLIC 83a00 0 glMultiDrawArraysIndirectCount
PUBLIC 83a80 0 glMultiDrawArraysIndirectCountARB
PUBLIC 83b00 0 glMultiDrawArraysIndirectEXT
PUBLIC 83b80 0 glMultiDrawElementArrayAPPLE
PUBLIC 83c00 0 glMultiDrawElements
PUBLIC 83c80 0 glMultiDrawElementsBaseVertex
PUBLIC 83d00 0 glMultiDrawElementsBaseVertexEXT
PUBLIC 83d80 0 glMultiDrawElementsEXT
PUBLIC 83e00 0 glMultiDrawElementsIndirect
PUBLIC 83e80 0 glMultiDrawElementsIndirectAMD
PUBLIC 83f00 0 glMultiDrawElementsIndirectBindlessCountNV
PUBLIC 83f80 0 glMultiDrawElementsIndirectBindlessNV
PUBLIC 84000 0 glMultiDrawElementsIndirectCount
PUBLIC 84080 0 glMultiDrawElementsIndirectCountARB
PUBLIC 84100 0 glMultiDrawElementsIndirectEXT
PUBLIC 84180 0 glMultiDrawMeshTasksIndirectCountNV
PUBLIC 84200 0 glMultiDrawMeshTasksIndirectNV
PUBLIC 84280 0 glMultiDrawRangeElementArrayAPPLE
PUBLIC 84300 0 glMultiModeDrawArraysIBM
PUBLIC 84380 0 glMultiModeDrawElementsIBM
PUBLIC 84400 0 glMultiTexBufferEXT
PUBLIC 84480 0 glMultiTexCoord1bOES
PUBLIC 84500 0 glMultiTexCoord1bvOES
PUBLIC 84580 0 glMultiTexCoord1d
PUBLIC 84600 0 glMultiTexCoord1dARB
PUBLIC 84680 0 glMultiTexCoord1dv
PUBLIC 84700 0 glMultiTexCoord1dvARB
PUBLIC 84780 0 glMultiTexCoord1f
PUBLIC 84800 0 glMultiTexCoord1fARB
PUBLIC 84880 0 glMultiTexCoord1fv
PUBLIC 84900 0 glMultiTexCoord1fvARB
PUBLIC 84980 0 glMultiTexCoord1hNV
PUBLIC 84a00 0 glMultiTexCoord1hvNV
PUBLIC 84a80 0 glMultiTexCoord1i
PUBLIC 84b00 0 glMultiTexCoord1iARB
PUBLIC 84b80 0 glMultiTexCoord1iv
PUBLIC 84c00 0 glMultiTexCoord1ivARB
PUBLIC 84c80 0 glMultiTexCoord1s
PUBLIC 84d00 0 glMultiTexCoord1sARB
PUBLIC 84d80 0 glMultiTexCoord1sv
PUBLIC 84e00 0 glMultiTexCoord1svARB
PUBLIC 84e80 0 glMultiTexCoord1x
PUBLIC 84f00 0 glMultiTexCoord1xOES
PUBLIC 84f80 0 glMultiTexCoord1xv
PUBLIC 85000 0 glMultiTexCoord1xvOES
PUBLIC 85080 0 glMultiTexCoord2bOES
PUBLIC 85100 0 glMultiTexCoord2bvOES
PUBLIC 85180 0 glMultiTexCoord2d
PUBLIC 85200 0 glMultiTexCoord2dARB
PUBLIC 85280 0 glMultiTexCoord2dv
PUBLIC 85300 0 glMultiTexCoord2dvARB
PUBLIC 85380 0 glMultiTexCoord2f
PUBLIC 85400 0 glMultiTexCoord2fARB
PUBLIC 85480 0 glMultiTexCoord2fv
PUBLIC 85500 0 glMultiTexCoord2fvARB
PUBLIC 85580 0 glMultiTexCoord2hNV
PUBLIC 85600 0 glMultiTexCoord2hvNV
PUBLIC 85680 0 glMultiTexCoord2i
PUBLIC 85700 0 glMultiTexCoord2iARB
PUBLIC 85780 0 glMultiTexCoord2iv
PUBLIC 85800 0 glMultiTexCoord2ivARB
PUBLIC 85880 0 glMultiTexCoord2s
PUBLIC 85900 0 glMultiTexCoord2sARB
PUBLIC 85980 0 glMultiTexCoord2sv
PUBLIC 85a00 0 glMultiTexCoord2svARB
PUBLIC 85a80 0 glMultiTexCoord2x
PUBLIC 85b00 0 glMultiTexCoord2xOES
PUBLIC 85b80 0 glMultiTexCoord2xv
PUBLIC 85c00 0 glMultiTexCoord2xvOES
PUBLIC 85c80 0 glMultiTexCoord3bOES
PUBLIC 85d00 0 glMultiTexCoord3bvOES
PUBLIC 85d80 0 glMultiTexCoord3d
PUBLIC 85e00 0 glMultiTexCoord3dARB
PUBLIC 85e80 0 glMultiTexCoord3dv
PUBLIC 85f00 0 glMultiTexCoord3dvARB
PUBLIC 85f80 0 glMultiTexCoord3f
PUBLIC 86000 0 glMultiTexCoord3fARB
PUBLIC 86080 0 glMultiTexCoord3fv
PUBLIC 86100 0 glMultiTexCoord3fvARB
PUBLIC 86180 0 glMultiTexCoord3hNV
PUBLIC 86200 0 glMultiTexCoord3hvNV
PUBLIC 86280 0 glMultiTexCoord3i
PUBLIC 86300 0 glMultiTexCoord3iARB
PUBLIC 86380 0 glMultiTexCoord3iv
PUBLIC 86400 0 glMultiTexCoord3ivARB
PUBLIC 86480 0 glMultiTexCoord3s
PUBLIC 86500 0 glMultiTexCoord3sARB
PUBLIC 86580 0 glMultiTexCoord3sv
PUBLIC 86600 0 glMultiTexCoord3svARB
PUBLIC 86680 0 glMultiTexCoord3x
PUBLIC 86700 0 glMultiTexCoord3xOES
PUBLIC 86780 0 glMultiTexCoord3xv
PUBLIC 86800 0 glMultiTexCoord3xvOES
PUBLIC 86880 0 glMultiTexCoord4bOES
PUBLIC 86900 0 glMultiTexCoord4bvOES
PUBLIC 86980 0 glMultiTexCoord4d
PUBLIC 86a00 0 glMultiTexCoord4dARB
PUBLIC 86a80 0 glMultiTexCoord4dv
PUBLIC 86b00 0 glMultiTexCoord4dvARB
PUBLIC 86b80 0 glMultiTexCoord4f
PUBLIC 86c00 0 glMultiTexCoord4fARB
PUBLIC 86c80 0 glMultiTexCoord4fv
PUBLIC 86d00 0 glMultiTexCoord4fvARB
PUBLIC 86d80 0 glMultiTexCoord4hNV
PUBLIC 86e00 0 glMultiTexCoord4hvNV
PUBLIC 86e80 0 glMultiTexCoord4i
PUBLIC 86f00 0 glMultiTexCoord4iARB
PUBLIC 86f80 0 glMultiTexCoord4iv
PUBLIC 87000 0 glMultiTexCoord4ivARB
PUBLIC 87080 0 glMultiTexCoord4s
PUBLIC 87100 0 glMultiTexCoord4sARB
PUBLIC 87180 0 glMultiTexCoord4sv
PUBLIC 87200 0 glMultiTexCoord4svARB
PUBLIC 87280 0 glMultiTexCoord4x
PUBLIC 87300 0 glMultiTexCoord4xOES
PUBLIC 87380 0 glMultiTexCoord4xv
PUBLIC 87400 0 glMultiTexCoord4xvOES
PUBLIC 87480 0 glMultiTexCoordP1ui
PUBLIC 87500 0 glMultiTexCoordP1uiv
PUBLIC 87580 0 glMultiTexCoordP2ui
PUBLIC 87600 0 glMultiTexCoordP2uiv
PUBLIC 87680 0 glMultiTexCoordP3ui
PUBLIC 87700 0 glMultiTexCoordP3uiv
PUBLIC 87780 0 glMultiTexCoordP4ui
PUBLIC 87800 0 glMultiTexCoordP4uiv
PUBLIC 87880 0 glMultiTexCoordPointerEXT
PUBLIC 87900 0 glMultiTexEnvfEXT
PUBLIC 87980 0 glMultiTexEnvfvEXT
PUBLIC 87a00 0 glMultiTexEnviEXT
PUBLIC 87a80 0 glMultiTexEnvivEXT
PUBLIC 87b00 0 glMultiTexGendEXT
PUBLIC 87b80 0 glMultiTexGendvEXT
PUBLIC 87c00 0 glMultiTexGenfEXT
PUBLIC 87c80 0 glMultiTexGenfvEXT
PUBLIC 87d00 0 glMultiTexGeniEXT
PUBLIC 87d80 0 glMultiTexGenivEXT
PUBLIC 87e00 0 glMultiTexImage1DEXT
PUBLIC 87e80 0 glMultiTexImage2DEXT
PUBLIC 87f00 0 glMultiTexImage3DEXT
PUBLIC 87f80 0 glMultiTexParameterIivEXT
PUBLIC 88000 0 glMultiTexParameterIuivEXT
PUBLIC 88080 0 glMultiTexParameterfEXT
PUBLIC 88100 0 glMultiTexParameterfvEXT
PUBLIC 88180 0 glMultiTexParameteriEXT
PUBLIC 88200 0 glMultiTexParameterivEXT
PUBLIC 88280 0 glMultiTexRenderbufferEXT
PUBLIC 88300 0 glMultiTexSubImage1DEXT
PUBLIC 88380 0 glMultiTexSubImage2DEXT
PUBLIC 88400 0 glMultiTexSubImage3DEXT
PUBLIC 88480 0 glMulticastBarrierNV
PUBLIC 88500 0 glMulticastBlitFramebufferNV
PUBLIC 88580 0 glMulticastBufferSubDataNV
PUBLIC 88600 0 glMulticastCopyBufferSubDataNV
PUBLIC 88680 0 glMulticastCopyImageSubDataNV
PUBLIC 88700 0 glMulticastFramebufferSampleLocationsfvNV
PUBLIC 88780 0 glMulticastGetQueryObjecti64vNV
PUBLIC 88800 0 glMulticastGetQueryObjectivNV
PUBLIC 88880 0 glMulticastGetQueryObjectui64vNV
PUBLIC 88900 0 glMulticastGetQueryObjectuivNV
PUBLIC 88980 0 glMulticastScissorArrayvNVX
PUBLIC 88a00 0 glMulticastViewportArrayvNVX
PUBLIC 88a80 0 glMulticastViewportPositionWScaleNVX
PUBLIC 88b00 0 glMulticastWaitSyncNV
PUBLIC 88b80 0 glNVENCInterOpFunctionNVX
PUBLIC 88c00 0 glNamedBufferAttachMemoryNV
PUBLIC 88c80 0 glNamedBufferData
PUBLIC 88d00 0 glNamedBufferDataEXT
PUBLIC 88d80 0 glNamedBufferPageCommitmentARB
PUBLIC 88e00 0 glNamedBufferPageCommitmentEXT
PUBLIC 88e80 0 glNamedBufferStorage
PUBLIC 88f00 0 glNamedBufferStorageEXT
PUBLIC 88f80 0 glNamedBufferStorageExternalEXT
PUBLIC 89000 0 glNamedBufferStorageMemEXT
PUBLIC 89080 0 glNamedBufferSubData
PUBLIC 89100 0 glNamedBufferSubDataEXT
PUBLIC 89180 0 glNamedCopyBufferSubDataEXT
PUBLIC 89200 0 glNamedFramebufferDrawBuffer
PUBLIC 89280 0 glNamedFramebufferDrawBuffers
PUBLIC 89300 0 glNamedFramebufferParameteri
PUBLIC 89380 0 glNamedFramebufferParameteriEXT
PUBLIC 89400 0 glNamedFramebufferReadBuffer
PUBLIC 89480 0 glNamedFramebufferRenderbuffer
PUBLIC 89500 0 glNamedFramebufferRenderbufferEXT
PUBLIC 89580 0 glNamedFramebufferSampleLocationsfvARB
PUBLIC 89600 0 glNamedFramebufferSampleLocationsfvNV
PUBLIC 89680 0 glNamedFramebufferSamplePositionsfvAMD
PUBLIC 89700 0 glNamedFramebufferTexture
PUBLIC 89780 0 glNamedFramebufferTexture1DEXT
PUBLIC 89800 0 glNamedFramebufferTexture2DEXT
PUBLIC 89880 0 glNamedFramebufferTexture3DEXT
PUBLIC 89900 0 glNamedFramebufferTextureEXT
PUBLIC 89980 0 glNamedFramebufferTextureFaceEXT
PUBLIC 89a00 0 glNamedFramebufferTextureLayer
PUBLIC 89a80 0 glNamedFramebufferTextureLayerEXT
PUBLIC 89b00 0 glNamedProgramLocalParameter4dEXT
PUBLIC 89b80 0 glNamedProgramLocalParameter4dvEXT
PUBLIC 89c00 0 glNamedProgramLocalParameter4fEXT
PUBLIC 89c80 0 glNamedProgramLocalParameter4fvEXT
PUBLIC 89d00 0 glNamedProgramLocalParameterI4iEXT
PUBLIC 89d80 0 glNamedProgramLocalParameterI4ivEXT
PUBLIC 89e00 0 glNamedProgramLocalParameterI4uiEXT
PUBLIC 89e80 0 glNamedProgramLocalParameterI4uivEXT
PUBLIC 89f00 0 glNamedProgramLocalParameters4fvEXT
PUBLIC 89f80 0 glNamedProgramLocalParametersI4ivEXT
PUBLIC 8a000 0 glNamedProgramLocalParametersI4uivEXT
PUBLIC 8a080 0 glNamedProgramStringEXT
PUBLIC 8a100 0 glNamedRenderbufferStorage
PUBLIC 8a180 0 glNamedRenderbufferStorageEXT
PUBLIC 8a200 0 glNamedRenderbufferStorageMultisample
PUBLIC 8a280 0 glNamedRenderbufferStorageMultisampleAdvancedAMD
PUBLIC 8a300 0 glNamedRenderbufferStorageMultisampleCoverageEXT
PUBLIC 8a380 0 glNamedRenderbufferStorageMultisampleEXT
PUBLIC 8a400 0 glNamedStringARB
PUBLIC 8a480 0 glNewList
PUBLIC 8a500 0 glNewObjectBufferATI
PUBLIC 8a580 0 glNormal3b
PUBLIC 8a600 0 glNormal3bv
PUBLIC 8a680 0 glNormal3d
PUBLIC 8a700 0 glNormal3dv
PUBLIC 8a780 0 glNormal3f
PUBLIC 8a800 0 glNormal3fVertex3fSUN
PUBLIC 8a880 0 glNormal3fVertex3fvSUN
PUBLIC 8a900 0 glNormal3fv
PUBLIC 8a980 0 glNormal3hNV
PUBLIC 8aa00 0 glNormal3hvNV
PUBLIC 8aa80 0 glNormal3i
PUBLIC 8ab00 0 glNormal3iv
PUBLIC 8ab80 0 glNormal3s
PUBLIC 8ac00 0 glNormal3sv
PUBLIC 8ac80 0 glNormal3x
PUBLIC 8ad00 0 glNormal3xOES
PUBLIC 8ad80 0 glNormal3xvOES
PUBLIC 8ae00 0 glNormalFormatNV
PUBLIC 8ae80 0 glNormalP3ui
PUBLIC 8af00 0 glNormalP3uiv
PUBLIC 8af80 0 glNormalPointer
PUBLIC 8b000 0 glNormalPointerEXT
PUBLIC 8b080 0 glNormalPointerListIBM
PUBLIC 8b100 0 glNormalPointervINTEL
PUBLIC 8b180 0 glNormalStream3bATI
PUBLIC 8b200 0 glNormalStream3bvATI
PUBLIC 8b280 0 glNormalStream3dATI
PUBLIC 8b300 0 glNormalStream3dvATI
PUBLIC 8b380 0 glNormalStream3fATI
PUBLIC 8b400 0 glNormalStream3fvATI
PUBLIC 8b480 0 glNormalStream3iATI
PUBLIC 8b500 0 glNormalStream3ivATI
PUBLIC 8b580 0 glNormalStream3sATI
PUBLIC 8b600 0 glNormalStream3svATI
PUBLIC 8b680 0 glObjectLabel
PUBLIC 8b700 0 glObjectLabelKHR
PUBLIC 8b780 0 glObjectLabelOES
PUBLIC 8b800 0 glObjectPtrLabel
PUBLIC 8b880 0 glObjectPtrLabelKHR
PUBLIC 8b900 0 glObjectPtrLabelOES
PUBLIC 8b980 0 glObjectPurgeableAPPLE
PUBLIC 8ba00 0 glObjectUnpurgeableAPPLE
PUBLIC 8ba80 0 glOrtho
PUBLIC 8bb00 0 glOrthof
PUBLIC 8bb80 0 glOrthofOES
PUBLIC 8bc00 0 glOrthox
PUBLIC 8bc80 0 glOrthoxOES
PUBLIC 8bd00 0 glPNTrianglesfATI
PUBLIC 8bd80 0 glPNTrianglesiATI
PUBLIC 8be00 0 glPassTexCoordATI
PUBLIC 8be80 0 glPassThrough
PUBLIC 8bf00 0 glPassThroughxOES
PUBLIC 8bf80 0 glPatchParameterfv
PUBLIC 8c000 0 glPatchParameterfvEXT
PUBLIC 8c080 0 glPatchParameterfvNV
PUBLIC 8c100 0 glPatchParameteri
PUBLIC 8c180 0 glPatchParameteriEXT
PUBLIC 8c200 0 glPatchParameteriNV
PUBLIC 8c280 0 glPatchParameteriOES
PUBLIC 8c300 0 glPathColorGenNV
PUBLIC 8c380 0 glPathCommandsNV
PUBLIC 8c400 0 glPathCoordsNV
PUBLIC 8c480 0 glPathCoverDepthFuncNV
PUBLIC 8c500 0 glPathDashArrayNV
PUBLIC 8c580 0 glPathFogGenNV
PUBLIC 8c600 0 glPathGlyphIndexArrayNV
PUBLIC 8c680 0 glPathGlyphIndexRangeNV
PUBLIC 8c700 0 glPathGlyphRangeNV
PUBLIC 8c780 0 glPathGlyphsNV
PUBLIC 8c800 0 glPathMemoryGlyphIndexArrayNV
PUBLIC 8c880 0 glPathParameterfNV
PUBLIC 8c900 0 glPathParameterfvNV
PUBLIC 8c980 0 glPathParameteriNV
PUBLIC 8ca00 0 glPathParameterivNV
PUBLIC 8ca80 0 glPathStencilDepthOffsetNV
PUBLIC 8cb00 0 glPathStencilFuncNV
PUBLIC 8cb80 0 glPathStringNV
PUBLIC 8cc00 0 glPathSubCommandsNV
PUBLIC 8cc80 0 glPathSubCoordsNV
PUBLIC 8cd00 0 glPathTexGenNV
PUBLIC 8cd80 0 glPauseTransformFeedback
PUBLIC 8ce00 0 glPauseTransformFeedbackEXT
PUBLIC 8ce80 0 glPauseTransformFeedbackNV
PUBLIC 8cf00 0 glPixelDataRangeNV
PUBLIC 8cf80 0 glPixelMapfv
PUBLIC 8d000 0 glPixelMapuiv
PUBLIC 8d080 0 glPixelMapusv
PUBLIC 8d100 0 glPixelMapx
PUBLIC 8d180 0 glPixelStoref
PUBLIC 8d200 0 glPixelStorei
PUBLIC 8d280 0 glPixelStorex
PUBLIC 8d300 0 glPixelTexGenParameterfSGIS
PUBLIC 8d380 0 glPixelTexGenParameterfvSGIS
PUBLIC 8d400 0 glPixelTexGenParameteriSGIS
PUBLIC 8d480 0 glPixelTexGenParameterivSGIS
PUBLIC 8d500 0 glPixelTexGenSGIX
PUBLIC 8d580 0 glPixelTransferf
PUBLIC 8d600 0 glPixelTransferi
PUBLIC 8d680 0 glPixelTransferxOES
PUBLIC 8d700 0 glPixelTransformParameterfEXT
PUBLIC 8d780 0 glPixelTransformParameterfvEXT
PUBLIC 8d800 0 glPixelTransformParameteriEXT
PUBLIC 8d880 0 glPixelTransformParameterivEXT
PUBLIC 8d900 0 glPixelZoom
PUBLIC 8d980 0 glPixelZoomxOES
PUBLIC 8da00 0 glPointAlongPathNV
PUBLIC 8da80 0 glPointParameterf
PUBLIC 8db00 0 glPointParameterfARB
PUBLIC 8db80 0 glPointParameterfEXT
PUBLIC 8dc00 0 glPointParameterfSGIS
PUBLIC 8dc80 0 glPointParameterfv
PUBLIC 8dd00 0 glPointParameterfvARB
PUBLIC 8dd80 0 glPointParameterfvEXT
PUBLIC 8de00 0 glPointParameterfvSGIS
PUBLIC 8de80 0 glPointParameteri
PUBLIC 8df00 0 glPointParameteriNV
PUBLIC 8df80 0 glPointParameteriv
PUBLIC 8e000 0 glPointParameterivNV
PUBLIC 8e080 0 glPointParameterx
PUBLIC 8e100 0 glPointParameterxOES
PUBLIC 8e180 0 glPointParameterxv
PUBLIC 8e200 0 glPointParameterxvOES
PUBLIC 8e280 0 glPointSize
PUBLIC 8e300 0 glPointSizePointerOES
PUBLIC 8e380 0 glPointSizex
PUBLIC 8e400 0 glPointSizexOES
PUBLIC 8e480 0 glPollAsyncSGIX
PUBLIC 8e500 0 glPollInstrumentsSGIX
PUBLIC 8e580 0 glPolygonMode
PUBLIC 8e600 0 glPolygonModeNV
PUBLIC 8e680 0 glPolygonOffset
PUBLIC 8e700 0 glPolygonOffsetClamp
PUBLIC 8e780 0 glPolygonOffsetClampEXT
PUBLIC 8e800 0 glPolygonOffsetEXT
PUBLIC 8e880 0 glPolygonOffsetx
PUBLIC 8e900 0 glPolygonOffsetxOES
PUBLIC 8e980 0 glPolygonStipple
PUBLIC 8ea00 0 glPopAttrib
PUBLIC 8ea80 0 glPopClientAttrib
PUBLIC 8eb00 0 glPopDebugGroup
PUBLIC 8eb80 0 glPopDebugGroupKHR
PUBLIC 8ec00 0 glPopDebugGroupOES
PUBLIC 8ec80 0 glPopGroupMarkerEXT
PUBLIC 8ed00 0 glPopMatrix
PUBLIC 8ed80 0 glPopName
PUBLIC 8ee00 0 glPresentFrameDualFillNV
PUBLIC 8ee80 0 glPresentFrameKeyedNV
PUBLIC 8ef00 0 glPrimitiveBoundingBox
PUBLIC 8ef80 0 glPrimitiveBoundingBoxARB
PUBLIC 8f000 0 glPrimitiveBoundingBoxEXT
PUBLIC 8f080 0 glPrimitiveBoundingBoxOES
PUBLIC 8f100 0 glPrimitiveRestart
PUBLIC 8f180 0 glPrimitiveRestartIndex
PUBLIC 8f200 0 glPrimitiveRestartIndexNV
PUBLIC 8f280 0 glPrimitiveRestartNV
PUBLIC 8f300 0 glPrioritizeTextures
PUBLIC 8f380 0 glPrioritizeTexturesEXT
PUBLIC 8f400 0 glPrioritizeTexturesxOES
PUBLIC 8f480 0 glProgramBinary
PUBLIC 8f500 0 glProgramBinaryOES
PUBLIC 8f580 0 glProgramBufferParametersIivNV
PUBLIC 8f600 0 glProgramBufferParametersIuivNV
PUBLIC 8f680 0 glProgramBufferParametersfvNV
PUBLIC 8f700 0 glProgramEnvParameter4dARB
PUBLIC 8f780 0 glProgramEnvParameter4dvARB
PUBLIC 8f800 0 glProgramEnvParameter4fARB
PUBLIC 8f880 0 glProgramEnvParameter4fvARB
PUBLIC 8f900 0 glProgramEnvParameterI4iNV
PUBLIC 8f980 0 glProgramEnvParameterI4ivNV
PUBLIC 8fa00 0 glProgramEnvParameterI4uiNV
PUBLIC 8fa80 0 glProgramEnvParameterI4uivNV
PUBLIC 8fb00 0 glProgramEnvParameters4fvEXT
PUBLIC 8fb80 0 glProgramEnvParametersI4ivNV
PUBLIC 8fc00 0 glProgramEnvParametersI4uivNV
PUBLIC 8fc80 0 glProgramLocalParameter4dARB
PUBLIC 8fd00 0 glProgramLocalParameter4dvARB
PUBLIC 8fd80 0 glProgramLocalParameter4fARB
PUBLIC 8fe00 0 glProgramLocalParameter4fvARB
PUBLIC 8fe80 0 glProgramLocalParameterI4iNV
PUBLIC 8ff00 0 glProgramLocalParameterI4ivNV
PUBLIC 8ff80 0 glProgramLocalParameterI4uiNV
PUBLIC 90000 0 glProgramLocalParameterI4uivNV
PUBLIC 90080 0 glProgramLocalParameters4fvEXT
PUBLIC 90100 0 glProgramLocalParametersI4ivNV
PUBLIC 90180 0 glProgramLocalParametersI4uivNV
PUBLIC 90200 0 glProgramNamedParameter4dNV
PUBLIC 90280 0 glProgramNamedParameter4dvNV
PUBLIC 90300 0 glProgramNamedParameter4fNV
PUBLIC 90380 0 glProgramNamedParameter4fvNV
PUBLIC 90400 0 glProgramParameter4dNV
PUBLIC 90480 0 glProgramParameter4dvNV
PUBLIC 90500 0 glProgramParameter4fNV
PUBLIC 90580 0 glProgramParameter4fvNV
PUBLIC 90600 0 glProgramParameteri
PUBLIC 90680 0 glProgramParameteriARB
PUBLIC 90700 0 glProgramParameteriEXT
PUBLIC 90780 0 glProgramParameters4dvNV
PUBLIC 90800 0 glProgramParameters4fvNV
PUBLIC 90880 0 glProgramPathFragmentInputGenNV
PUBLIC 90900 0 glProgramStringARB
PUBLIC 90980 0 glProgramSubroutineParametersuivNV
PUBLIC 90a00 0 glProgramUniform1d
PUBLIC 90a80 0 glProgramUniform1dEXT
PUBLIC 90b00 0 glProgramUniform1dv
PUBLIC 90b80 0 glProgramUniform1dvEXT
PUBLIC 90c00 0 glProgramUniform1f
PUBLIC 90c80 0 glProgramUniform1fEXT
PUBLIC 90d00 0 glProgramUniform1fv
PUBLIC 90d80 0 glProgramUniform1fvEXT
PUBLIC 90e00 0 glProgramUniform1i
PUBLIC 90e80 0 glProgramUniform1i64ARB
PUBLIC 90f00 0 glProgramUniform1i64NV
PUBLIC 90f80 0 glProgramUniform1i64vARB
PUBLIC 91000 0 glProgramUniform1i64vNV
PUBLIC 91080 0 glProgramUniform1iEXT
PUBLIC 91100 0 glProgramUniform1iv
PUBLIC 91180 0 glProgramUniform1ivEXT
PUBLIC 91200 0 glProgramUniform1ui
PUBLIC 91280 0 glProgramUniform1ui64ARB
PUBLIC 91300 0 glProgramUniform1ui64NV
PUBLIC 91380 0 glProgramUniform1ui64vARB
PUBLIC 91400 0 glProgramUniform1ui64vNV
PUBLIC 91480 0 glProgramUniform1uiEXT
PUBLIC 91500 0 glProgramUniform1uiv
PUBLIC 91580 0 glProgramUniform1uivEXT
PUBLIC 91600 0 glProgramUniform2d
PUBLIC 91680 0 glProgramUniform2dEXT
PUBLIC 91700 0 glProgramUniform2dv
PUBLIC 91780 0 glProgramUniform2dvEXT
PUBLIC 91800 0 glProgramUniform2f
PUBLIC 91880 0 glProgramUniform2fEXT
PUBLIC 91900 0 glProgramUniform2fv
PUBLIC 91980 0 glProgramUniform2fvEXT
PUBLIC 91a00 0 glProgramUniform2i
PUBLIC 91a80 0 glProgramUniform2i64ARB
PUBLIC 91b00 0 glProgramUniform2i64NV
PUBLIC 91b80 0 glProgramUniform2i64vARB
PUBLIC 91c00 0 glProgramUniform2i64vNV
PUBLIC 91c80 0 glProgramUniform2iEXT
PUBLIC 91d00 0 glProgramUniform2iv
PUBLIC 91d80 0 glProgramUniform2ivEXT
PUBLIC 91e00 0 glProgramUniform2ui
PUBLIC 91e80 0 glProgramUniform2ui64ARB
PUBLIC 91f00 0 glProgramUniform2ui64NV
PUBLIC 91f80 0 glProgramUniform2ui64vARB
PUBLIC 92000 0 glProgramUniform2ui64vNV
PUBLIC 92080 0 glProgramUniform2uiEXT
PUBLIC 92100 0 glProgramUniform2uiv
PUBLIC 92180 0 glProgramUniform2uivEXT
PUBLIC 92200 0 glProgramUniform3d
PUBLIC 92280 0 glProgramUniform3dEXT
PUBLIC 92300 0 glProgramUniform3dv
PUBLIC 92380 0 glProgramUniform3dvEXT
PUBLIC 92400 0 glProgramUniform3f
PUBLIC 92480 0 glProgramUniform3fEXT
PUBLIC 92500 0 glProgramUniform3fv
PUBLIC 92580 0 glProgramUniform3fvEXT
PUBLIC 92600 0 glProgramUniform3i
PUBLIC 92680 0 glProgramUniform3i64ARB
PUBLIC 92700 0 glProgramUniform3i64NV
PUBLIC 92780 0 glProgramUniform3i64vARB
PUBLIC 92800 0 glProgramUniform3i64vNV
PUBLIC 92880 0 glProgramUniform3iEXT
PUBLIC 92900 0 glProgramUniform3iv
PUBLIC 92980 0 glProgramUniform3ivEXT
PUBLIC 92a00 0 glProgramUniform3ui
PUBLIC 92a80 0 glProgramUniform3ui64ARB
PUBLIC 92b00 0 glProgramUniform3ui64NV
PUBLIC 92b80 0 glProgramUniform3ui64vARB
PUBLIC 92c00 0 glProgramUniform3ui64vNV
PUBLIC 92c80 0 glProgramUniform3uiEXT
PUBLIC 92d00 0 glProgramUniform3uiv
PUBLIC 92d80 0 glProgramUniform3uivEXT
PUBLIC 92e00 0 glProgramUniform4d
PUBLIC 92e80 0 glProgramUniform4dEXT
PUBLIC 92f00 0 glProgramUniform4dv
PUBLIC 92f80 0 glProgramUniform4dvEXT
PUBLIC 93000 0 glProgramUniform4f
PUBLIC 93080 0 glProgramUniform4fEXT
PUBLIC 93100 0 glProgramUniform4fv
PUBLIC 93180 0 glProgramUniform4fvEXT
PUBLIC 93200 0 glProgramUniform4i
PUBLIC 93280 0 glProgramUniform4i64ARB
PUBLIC 93300 0 glProgramUniform4i64NV
PUBLIC 93380 0 glProgramUniform4i64vARB
PUBLIC 93400 0 glProgramUniform4i64vNV
PUBLIC 93480 0 glProgramUniform4iEXT
PUBLIC 93500 0 glProgramUniform4iv
PUBLIC 93580 0 glProgramUniform4ivEXT
PUBLIC 93600 0 glProgramUniform4ui
PUBLIC 93680 0 glProgramUniform4ui64ARB
PUBLIC 93700 0 glProgramUniform4ui64NV
PUBLIC 93780 0 glProgramUniform4ui64vARB
PUBLIC 93800 0 glProgramUniform4ui64vNV
PUBLIC 93880 0 glProgramUniform4uiEXT
PUBLIC 93900 0 glProgramUniform4uiv
PUBLIC 93980 0 glProgramUniform4uivEXT
PUBLIC 93a00 0 glProgramUniformHandleui64ARB
PUBLIC 93a80 0 glProgramUniformHandleui64IMG
PUBLIC 93b00 0 glProgramUniformHandleui64NV
PUBLIC 93b80 0 glProgramUniformHandleui64vARB
PUBLIC 93c00 0 glProgramUniformHandleui64vIMG
PUBLIC 93c80 0 glProgramUniformHandleui64vNV
PUBLIC 93d00 0 glProgramUniformMatrix2dv
PUBLIC 93d80 0 glProgramUniformMatrix2dvEXT
PUBLIC 93e00 0 glProgramUniformMatrix2fv
PUBLIC 93e80 0 glProgramUniformMatrix2fvEXT
PUBLIC 93f00 0 glProgramUniformMatrix2x3dv
PUBLIC 93f80 0 glProgramUniformMatrix2x3dvEXT
PUBLIC 94000 0 glProgramUniformMatrix2x3fv
PUBLIC 94080 0 glProgramUniformMatrix2x3fvEXT
PUBLIC 94100 0 glProgramUniformMatrix2x4dv
PUBLIC 94180 0 glProgramUniformMatrix2x4dvEXT
PUBLIC 94200 0 glProgramUniformMatrix2x4fv
PUBLIC 94280 0 glProgramUniformMatrix2x4fvEXT
PUBLIC 94300 0 glProgramUniformMatrix3dv
PUBLIC 94380 0 glProgramUniformMatrix3dvEXT
PUBLIC 94400 0 glProgramUniformMatrix3fv
PUBLIC 94480 0 glProgramUniformMatrix3fvEXT
PUBLIC 94500 0 glProgramUniformMatrix3x2dv
PUBLIC 94580 0 glProgramUniformMatrix3x2dvEXT
PUBLIC 94600 0 glProgramUniformMatrix3x2fv
PUBLIC 94680 0 glProgramUniformMatrix3x2fvEXT
PUBLIC 94700 0 glProgramUniformMatrix3x4dv
PUBLIC 94780 0 glProgramUniformMatrix3x4dvEXT
PUBLIC 94800 0 glProgramUniformMatrix3x4fv
PUBLIC 94880 0 glProgramUniformMatrix3x4fvEXT
PUBLIC 94900 0 glProgramUniformMatrix4dv
PUBLIC 94980 0 glProgramUniformMatrix4dvEXT
PUBLIC 94a00 0 glProgramUniformMatrix4fv
PUBLIC 94a80 0 glProgramUniformMatrix4fvEXT
PUBLIC 94b00 0 glProgramUniformMatrix4x2dv
PUBLIC 94b80 0 glProgramUniformMatrix4x2dvEXT
PUBLIC 94c00 0 glProgramUniformMatrix4x2fv
PUBLIC 94c80 0 glProgramUniformMatrix4x2fvEXT
PUBLIC 94d00 0 glProgramUniformMatrix4x3dv
PUBLIC 94d80 0 glProgramUniformMatrix4x3dvEXT
PUBLIC 94e00 0 glProgramUniformMatrix4x3fv
PUBLIC 94e80 0 glProgramUniformMatrix4x3fvEXT
PUBLIC 94f00 0 glProgramUniformui64NV
PUBLIC 94f80 0 glProgramUniformui64vNV
PUBLIC 95000 0 glProgramVertexLimitNV
PUBLIC 95080 0 glProvokingVertex
PUBLIC 95100 0 glProvokingVertexEXT
PUBLIC 95180 0 glPushAttrib
PUBLIC 95200 0 glPushClientAttrib
PUBLIC 95280 0 glPushClientAttribDefaultEXT
PUBLIC 95300 0 glPushDebugGroup
PUBLIC 95380 0 glPushDebugGroupKHR
PUBLIC 95400 0 glPushDebugGroupOES
PUBLIC 95480 0 glPushGroupMarkerEXT
PUBLIC 95500 0 glPushMatrix
PUBLIC 95580 0 glPushName
PUBLIC 95600 0 glQueryCounter
PUBLIC 95680 0 glQueryCounterEXT
PUBLIC 95700 0 glQueryCounterNV
PUBLIC 95780 0 glQueryMatrixxOES
PUBLIC 95800 0 glQueryObjectParameteruiAMD
PUBLIC 95880 0 glQueryResourceNV
PUBLIC 95900 0 glQueryResourceTagNV
PUBLIC 95980 0 glRasterPos2d
PUBLIC 95a00 0 glRasterPos2dv
PUBLIC 95a80 0 glRasterPos2f
PUBLIC 95b00 0 glRasterPos2fv
PUBLIC 95b80 0 glRasterPos2i
PUBLIC 95c00 0 glRasterPos2iv
PUBLIC 95c80 0 glRasterPos2s
PUBLIC 95d00 0 glRasterPos2sv
PUBLIC 95d80 0 glRasterPos2xOES
PUBLIC 95e00 0 glRasterPos2xvOES
PUBLIC 95e80 0 glRasterPos3d
PUBLIC 95f00 0 glRasterPos3dv
PUBLIC 95f80 0 glRasterPos3f
PUBLIC 96000 0 glRasterPos3fv
PUBLIC 96080 0 glRasterPos3i
PUBLIC 96100 0 glRasterPos3iv
PUBLIC 96180 0 glRasterPos3s
PUBLIC 96200 0 glRasterPos3sv
PUBLIC 96280 0 glRasterPos3xOES
PUBLIC 96300 0 glRasterPos3xvOES
PUBLIC 96380 0 glRasterPos4d
PUBLIC 96400 0 glRasterPos4dv
PUBLIC 96480 0 glRasterPos4f
PUBLIC 96500 0 glRasterPos4fv
PUBLIC 96580 0 glRasterPos4i
PUBLIC 96600 0 glRasterPos4iv
PUBLIC 96680 0 glRasterPos4s
PUBLIC 96700 0 glRasterPos4sv
PUBLIC 96780 0 glRasterPos4xOES
PUBLIC 96800 0 glRasterPos4xvOES
PUBLIC 96880 0 glRasterSamplesEXT
PUBLIC 96900 0 glReadBuffer
PUBLIC 96980 0 glReadBufferIndexedEXT
PUBLIC 96a00 0 glReadBufferNV
PUBLIC 96a80 0 glReadInstrumentsSGIX
PUBLIC 96b00 0 glReadPixels
PUBLIC 96b80 0 glReadnPixels
PUBLIC 96c00 0 glReadnPixelsARB
PUBLIC 96c80 0 glReadnPixelsEXT
PUBLIC 96d00 0 glReadnPixelsKHR
PUBLIC 96d80 0 glRectd
PUBLIC 96e00 0 glRectdv
PUBLIC 96e80 0 glRectf
PUBLIC 96f00 0 glRectfv
PUBLIC 96f80 0 glRecti
PUBLIC 97000 0 glRectiv
PUBLIC 97080 0 glRects
PUBLIC 97100 0 glRectsv
PUBLIC 97180 0 glRectxOES
PUBLIC 97200 0 glRectxvOES
PUBLIC 97280 0 glReferencePlaneSGIX
PUBLIC 97300 0 glReleaseKeyedMutexWin32EXT
PUBLIC 97380 0 glReleaseShaderCompiler
PUBLIC 97400 0 glRenderGpuMaskNV
PUBLIC 97480 0 glRenderMode
PUBLIC 97500 0 glRenderbufferStorage
PUBLIC 97580 0 glRenderbufferStorageEXT
PUBLIC 97600 0 glRenderbufferStorageMultisample
PUBLIC 97680 0 glRenderbufferStorageMultisampleANGLE
PUBLIC 97700 0 glRenderbufferStorageMultisampleAPPLE
PUBLIC 97780 0 glRenderbufferStorageMultisampleAdvancedAMD
PUBLIC 97800 0 glRenderbufferStorageMultisampleCoverageNV
PUBLIC 97880 0 glRenderbufferStorageMultisampleEXT
PUBLIC 97900 0 glRenderbufferStorageMultisampleIMG
PUBLIC 97980 0 glRenderbufferStorageMultisampleNV
PUBLIC 97a00 0 glRenderbufferStorageOES
PUBLIC 97a80 0 glReplacementCodePointerSUN
PUBLIC 97b00 0 glReplacementCodeubSUN
PUBLIC 97b80 0 glReplacementCodeubvSUN
PUBLIC 97c00 0 glReplacementCodeuiColor3fVertex3fSUN
PUBLIC 97c80 0 glReplacementCodeuiColor3fVertex3fvSUN
PUBLIC 97d00 0 glReplacementCodeuiColor4fNormal3fVertex3fSUN
PUBLIC 97d80 0 glReplacementCodeuiColor4fNormal3fVertex3fvSUN
PUBLIC 97e00 0 glReplacementCodeuiColor4ubVertex3fSUN
PUBLIC 97e80 0 glReplacementCodeuiColor4ubVertex3fvSUN
PUBLIC 97f00 0 glReplacementCodeuiNormal3fVertex3fSUN
PUBLIC 97f80 0 glReplacementCodeuiNormal3fVertex3fvSUN
PUBLIC 98000 0 glReplacementCodeuiSUN
PUBLIC 98080 0 glReplacementCodeuiTexCoord2fColor4fNormal3fVertex3fSUN
PUBLIC 98100 0 glReplacementCodeuiTexCoord2fColor4fNormal3fVertex3fvSUN
PUBLIC 98180 0 glReplacementCodeuiTexCoord2fNormal3fVertex3fSUN
PUBLIC 98200 0 glReplacementCodeuiTexCoord2fNormal3fVertex3fvSUN
PUBLIC 98280 0 glReplacementCodeuiTexCoord2fVertex3fSUN
PUBLIC 98300 0 glReplacementCodeuiTexCoord2fVertex3fvSUN
PUBLIC 98380 0 glReplacementCodeuiVertex3fSUN
PUBLIC 98400 0 glReplacementCodeuiVertex3fvSUN
PUBLIC 98480 0 glReplacementCodeuivSUN
PUBLIC 98500 0 glReplacementCodeusSUN
PUBLIC 98580 0 glReplacementCodeusvSUN
PUBLIC 98600 0 glRequestResidentProgramsNV
PUBLIC 98680 0 glResetHistogram
PUBLIC 98700 0 glResetHistogramEXT
PUBLIC 98780 0 glResetMemoryObjectParameterNV
PUBLIC 98800 0 glResetMinmax
PUBLIC 98880 0 glResetMinmaxEXT
PUBLIC 98900 0 glResizeBuffersMESA
PUBLIC 98980 0 glResolveDepthValuesNV
PUBLIC 98a00 0 glResolveMultisampleFramebufferAPPLE
PUBLIC 98a80 0 glResumeTransformFeedback
PUBLIC 98b00 0 glResumeTransformFeedbackEXT
PUBLIC 98b80 0 glResumeTransformFeedbackNV
PUBLIC 98c00 0 glRotated
PUBLIC 98c80 0 glRotatef
PUBLIC 98d00 0 glRotatex
PUBLIC 98d80 0 glRotatexOES
PUBLIC 98e00 0 glSampleCoverage
PUBLIC 98e80 0 glSampleCoverageARB
PUBLIC 98f00 0 glSampleCoveragex
PUBLIC 98f80 0 glSampleCoveragexOES
PUBLIC 99000 0 glSampleMapATI
PUBLIC 99080 0 glSampleMaskEXT
PUBLIC 99100 0 glSampleMaskIndexedNV
PUBLIC 99180 0 glSampleMaskSGIS
PUBLIC 99200 0 glSampleMaski
PUBLIC 99280 0 glSamplePatternEXT
PUBLIC 99300 0 glSamplePatternSGIS
PUBLIC 99380 0 glSamplerParameterIiv
PUBLIC 99400 0 glSamplerParameterIivEXT
PUBLIC 99480 0 glSamplerParameterIivOES
PUBLIC 99500 0 glSamplerParameterIuiv
PUBLIC 99580 0 glSamplerParameterIuivEXT
PUBLIC 99600 0 glSamplerParameterIuivOES
PUBLIC 99680 0 glSamplerParameterf
PUBLIC 99700 0 glSamplerParameterfv
PUBLIC 99780 0 glSamplerParameteri
PUBLIC 99800 0 glSamplerParameteriv
PUBLIC 99880 0 glScaled
PUBLIC 99900 0 glScalef
PUBLIC 99980 0 glScalex
PUBLIC 99a00 0 glScalexOES
PUBLIC 99a80 0 glScissor
PUBLIC 99b00 0 glScissorArrayv
PUBLIC 99b80 0 glScissorArrayvNV
PUBLIC 99c00 0 glScissorArrayvOES
PUBLIC 99c80 0 glScissorExclusiveArrayvNV
PUBLIC 99d00 0 glScissorExclusiveNV
PUBLIC 99d80 0 glScissorIndexed
PUBLIC 99e00 0 glScissorIndexedNV
PUBLIC 99e80 0 glScissorIndexedOES
PUBLIC 99f00 0 glScissorIndexedv
PUBLIC 99f80 0 glScissorIndexedvNV
PUBLIC 9a000 0 glScissorIndexedvOES
PUBLIC 9a080 0 glSecondaryColor3b
PUBLIC 9a100 0 glSecondaryColor3bEXT
PUBLIC 9a180 0 glSecondaryColor3bv
PUBLIC 9a200 0 glSecondaryColor3bvEXT
PUBLIC 9a280 0 glSecondaryColor3d
PUBLIC 9a300 0 glSecondaryColor3dEXT
PUBLIC 9a380 0 glSecondaryColor3dv
PUBLIC 9a400 0 glSecondaryColor3dvEXT
PUBLIC 9a480 0 glSecondaryColor3f
PUBLIC 9a500 0 glSecondaryColor3fEXT
PUBLIC 9a580 0 glSecondaryColor3fv
PUBLIC 9a600 0 glSecondaryColor3fvEXT
PUBLIC 9a680 0 glSecondaryColor3hNV
PUBLIC 9a700 0 glSecondaryColor3hvNV
PUBLIC 9a780 0 glSecondaryColor3i
PUBLIC 9a800 0 glSecondaryColor3iEXT
PUBLIC 9a880 0 glSecondaryColor3iv
PUBLIC 9a900 0 glSecondaryColor3ivEXT
PUBLIC 9a980 0 glSecondaryColor3s
PUBLIC 9aa00 0 glSecondaryColor3sEXT
PUBLIC 9aa80 0 glSecondaryColor3sv
PUBLIC 9ab00 0 glSecondaryColor3svEXT
PUBLIC 9ab80 0 glSecondaryColor3ub
PUBLIC 9ac00 0 glSecondaryColor3ubEXT
PUBLIC 9ac80 0 glSecondaryColor3ubv
PUBLIC 9ad00 0 glSecondaryColor3ubvEXT
PUBLIC 9ad80 0 glSecondaryColor3ui
PUBLIC 9ae00 0 glSecondaryColor3uiEXT
PUBLIC 9ae80 0 glSecondaryColor3uiv
PUBLIC 9af00 0 glSecondaryColor3uivEXT
PUBLIC 9af80 0 glSecondaryColor3us
PUBLIC 9b000 0 glSecondaryColor3usEXT
PUBLIC 9b080 0 glSecondaryColor3usv
PUBLIC 9b100 0 glSecondaryColor3usvEXT
PUBLIC 9b180 0 glSecondaryColorFormatNV
PUBLIC 9b200 0 glSecondaryColorP3ui
PUBLIC 9b280 0 glSecondaryColorP3uiv
PUBLIC 9b300 0 glSecondaryColorPointer
PUBLIC 9b380 0 glSecondaryColorPointerEXT
PUBLIC 9b400 0 glSecondaryColorPointerListIBM
PUBLIC 9b480 0 glSelectBuffer
PUBLIC 9b500 0 glSelectPerfMonitorCountersAMD
PUBLIC 9b580 0 glSemaphoreParameterui64vEXT
PUBLIC 9b600 0 glSeparableFilter2D
PUBLIC 9b680 0 glSeparableFilter2DEXT
PUBLIC 9b700 0 glSetFenceAPPLE
PUBLIC 9b780 0 glSetFenceNV
PUBLIC 9b800 0 glSetFragmentShaderConstantATI
PUBLIC 9b880 0 glSetInvariantEXT
PUBLIC 9b900 0 glSetLocalConstantEXT
PUBLIC 9b980 0 glSetMultisamplefvAMD
PUBLIC 9ba00 0 glShadeModel
PUBLIC 9ba80 0 glShaderBinary
PUBLIC 9bb00 0 glShaderOp1EXT
PUBLIC 9bb80 0 glShaderOp2EXT
PUBLIC 9bc00 0 glShaderOp3EXT
PUBLIC 9bc80 0 glShaderSource
PUBLIC 9bd00 0 glShaderSourceARB
PUBLIC 9bd80 0 glShaderStorageBlockBinding
PUBLIC 9be00 0 glShadingRateImageBarrierNV
PUBLIC 9be80 0 glShadingRateImagePaletteNV
PUBLIC 9bf00 0 glShadingRateSampleOrderCustomNV
PUBLIC 9bf80 0 glShadingRateSampleOrderNV
PUBLIC 9c000 0 glSharpenTexFuncSGIS
PUBLIC 9c080 0 glSignalSemaphoreEXT
PUBLIC 9c100 0 glSignalSemaphoreui64NVX
PUBLIC 9c180 0 glSignalVkFenceNV
PUBLIC 9c200 0 glSignalVkSemaphoreNV
PUBLIC 9c280 0 glSpecializeShader
PUBLIC 9c300 0 glSpecializeShaderARB
PUBLIC 9c380 0 glSpriteParameterfSGIX
PUBLIC 9c400 0 glSpriteParameterfvSGIX
PUBLIC 9c480 0 glSpriteParameteriSGIX
PUBLIC 9c500 0 glSpriteParameterivSGIX
PUBLIC 9c580 0 glStartInstrumentsSGIX
PUBLIC 9c600 0 glStartTilingQCOM
PUBLIC 9c680 0 glStateCaptureNV
PUBLIC 9c700 0 glStencilClearTagEXT
PUBLIC 9c780 0 glStencilFillPathInstancedNV
PUBLIC 9c800 0 glStencilFillPathNV
PUBLIC 9c880 0 glStencilFunc
PUBLIC 9c900 0 glStencilFuncSeparate
PUBLIC 9c980 0 glStencilFuncSeparateATI
PUBLIC 9ca00 0 glStencilMask
PUBLIC 9ca80 0 glStencilMaskSeparate
PUBLIC 9cb00 0 glStencilOp
PUBLIC 9cb80 0 glStencilOpSeparate
PUBLIC 9cc00 0 glStencilOpSeparateATI
PUBLIC 9cc80 0 glStencilOpValueAMD
PUBLIC 9cd00 0 glStencilStrokePathInstancedNV
PUBLIC 9cd80 0 glStencilStrokePathNV
PUBLIC 9ce00 0 glStencilThenCoverFillPathInstancedNV
PUBLIC 9ce80 0 glStencilThenCoverFillPathNV
PUBLIC 9cf00 0 glStencilThenCoverStrokePathInstancedNV
PUBLIC 9cf80 0 glStencilThenCoverStrokePathNV
PUBLIC 9d000 0 glStopInstrumentsSGIX
PUBLIC 9d080 0 glStringMarkerGREMEDY
PUBLIC 9d100 0 glSubpixelPrecisionBiasNV
PUBLIC 9d180 0 glSwizzleEXT
PUBLIC 9d200 0 glSyncTextureINTEL
PUBLIC 9d280 0 glTagSampleBufferSGIX
PUBLIC 9d300 0 glTangent3bEXT
PUBLIC 9d380 0 glTangent3bvEXT
PUBLIC 9d400 0 glTangent3dEXT
PUBLIC 9d480 0 glTangent3dvEXT
PUBLIC 9d500 0 glTangent3fEXT
PUBLIC 9d580 0 glTangent3fvEXT
PUBLIC 9d600 0 glTangent3iEXT
PUBLIC 9d680 0 glTangent3ivEXT
PUBLIC 9d700 0 glTangent3sEXT
PUBLIC 9d780 0 glTangent3svEXT
PUBLIC 9d800 0 glTangentPointerEXT
PUBLIC 9d880 0 glTbufferMask3DFX
PUBLIC 9d900 0 glTessellationFactorAMD
PUBLIC 9d980 0 glTessellationModeAMD
PUBLIC 9da00 0 glTestFenceAPPLE
PUBLIC 9da80 0 glTestFenceNV
PUBLIC 9db00 0 glTestObjectAPPLE
PUBLIC 9db80 0 glTexAttachMemoryNV
PUBLIC 9dc00 0 glTexBuffer
PUBLIC 9dc80 0 glTexBufferARB
PUBLIC 9dd00 0 glTexBufferEXT
PUBLIC 9dd80 0 glTexBufferOES
PUBLIC 9de00 0 glTexBufferRange
PUBLIC 9de80 0 glTexBufferRangeEXT
PUBLIC 9df00 0 glTexBufferRangeOES
PUBLIC 9df80 0 glTexBumpParameterfvATI
PUBLIC 9e000 0 glTexBumpParameterivATI
PUBLIC 9e080 0 glTexCoord1bOES
PUBLIC 9e100 0 glTexCoord1bvOES
PUBLIC 9e180 0 glTexCoord1d
PUBLIC 9e200 0 glTexCoord1dv
PUBLIC 9e280 0 glTexCoord1f
PUBLIC 9e300 0 glTexCoord1fv
PUBLIC 9e380 0 glTexCoord1hNV
PUBLIC 9e400 0 glTexCoord1hvNV
PUBLIC 9e480 0 glTexCoord1i
PUBLIC 9e500 0 glTexCoord1iv
PUBLIC 9e580 0 glTexCoord1s
PUBLIC 9e600 0 glTexCoord1sv
PUBLIC 9e680 0 glTexCoord1xOES
PUBLIC 9e700 0 glTexCoord1xvOES
PUBLIC 9e780 0 glTexCoord2bOES
PUBLIC 9e800 0 glTexCoord2bvOES
PUBLIC 9e880 0 glTexCoord2d
PUBLIC 9e900 0 glTexCoord2dv
PUBLIC 9e980 0 glTexCoord2f
PUBLIC 9ea00 0 glTexCoord2fColor3fVertex3fSUN
PUBLIC 9ea80 0 glTexCoord2fColor3fVertex3fvSUN
PUBLIC 9eb00 0 glTexCoord2fColor4fNormal3fVertex3fSUN
PUBLIC 9eb80 0 glTexCoord2fColor4fNormal3fVertex3fvSUN
PUBLIC 9ec00 0 glTexCoord2fColor4ubVertex3fSUN
PUBLIC 9ec80 0 glTexCoord2fColor4ubVertex3fvSUN
PUBLIC 9ed00 0 glTexCoord2fNormal3fVertex3fSUN
PUBLIC 9ed80 0 glTexCoord2fNormal3fVertex3fvSUN
PUBLIC 9ee00 0 glTexCoord2fVertex3fSUN
PUBLIC 9ee80 0 glTexCoord2fVertex3fvSUN
PUBLIC 9ef00 0 glTexCoord2fv
PUBLIC 9ef80 0 glTexCoord2hNV
PUBLIC 9f000 0 glTexCoord2hvNV
PUBLIC 9f080 0 glTexCoord2i
PUBLIC 9f100 0 glTexCoord2iv
PUBLIC 9f180 0 glTexCoord2s
PUBLIC 9f200 0 glTexCoord2sv
PUBLIC 9f280 0 glTexCoord2xOES
PUBLIC 9f300 0 glTexCoord2xvOES
PUBLIC 9f380 0 glTexCoord3bOES
PUBLIC 9f400 0 glTexCoord3bvOES
PUBLIC 9f480 0 glTexCoord3d
PUBLIC 9f500 0 glTexCoord3dv
PUBLIC 9f580 0 glTexCoord3f
PUBLIC 9f600 0 glTexCoord3fv
PUBLIC 9f680 0 glTexCoord3hNV
PUBLIC 9f700 0 glTexCoord3hvNV
PUBLIC 9f780 0 glTexCoord3i
PUBLIC 9f800 0 glTexCoord3iv
PUBLIC 9f880 0 glTexCoord3s
PUBLIC 9f900 0 glTexCoord3sv
PUBLIC 9f980 0 glTexCoord3xOES
PUBLIC 9fa00 0 glTexCoord3xvOES
PUBLIC 9fa80 0 glTexCoord4bOES
PUBLIC 9fb00 0 glTexCoord4bvOES
PUBLIC 9fb80 0 glTexCoord4d
PUBLIC 9fc00 0 glTexCoord4dv
PUBLIC 9fc80 0 glTexCoord4f
PUBLIC 9fd00 0 glTexCoord4fColor4fNormal3fVertex4fSUN
PUBLIC 9fd80 0 glTexCoord4fColor4fNormal3fVertex4fvSUN
PUBLIC 9fe00 0 glTexCoord4fVertex4fSUN
PUBLIC 9fe80 0 glTexCoord4fVertex4fvSUN
PUBLIC 9ff00 0 glTexCoord4fv
PUBLIC 9ff80 0 glTexCoord4hNV
PUBLIC a0000 0 glTexCoord4hvNV
PUBLIC a0080 0 glTexCoord4i
PUBLIC a0100 0 glTexCoord4iv
PUBLIC a0180 0 glTexCoord4s
PUBLIC a0200 0 glTexCoord4sv
PUBLIC a0280 0 glTexCoord4xOES
PUBLIC a0300 0 glTexCoord4xvOES
PUBLIC a0380 0 glTexCoordFormatNV
PUBLIC a0400 0 glTexCoordP1ui
PUBLIC a0480 0 glTexCoordP1uiv
PUBLIC a0500 0 glTexCoordP2ui
PUBLIC a0580 0 glTexCoordP2uiv
PUBLIC a0600 0 glTexCoordP3ui
PUBLIC a0680 0 glTexCoordP3uiv
PUBLIC a0700 0 glTexCoordP4ui
PUBLIC a0780 0 glTexCoordP4uiv
PUBLIC a0800 0 glTexCoordPointer
PUBLIC a0880 0 glTexCoordPointerEXT
PUBLIC a0900 0 glTexCoordPointerListIBM
PUBLIC a0980 0 glTexCoordPointervINTEL
PUBLIC a0a00 0 glTexEnvf
PUBLIC a0a80 0 glTexEnvfv
PUBLIC a0b00 0 glTexEnvi
PUBLIC a0b80 0 glTexEnviv
PUBLIC a0c00 0 glTexEnvx
PUBLIC a0c80 0 glTexEnvxOES
PUBLIC a0d00 0 glTexEnvxv
PUBLIC a0d80 0 glTexEnvxvOES
PUBLIC a0e00 0 glTexFilterFuncSGIS
PUBLIC a0e80 0 glTexGend
PUBLIC a0f00 0 glTexGendv
PUBLIC a0f80 0 glTexGenf
PUBLIC a1000 0 glTexGenfOES
PUBLIC a1080 0 glTexGenfv
PUBLIC a1100 0 glTexGenfvOES
PUBLIC a1180 0 glTexGeni
PUBLIC a1200 0 glTexGeniOES
PUBLIC a1280 0 glTexGeniv
PUBLIC a1300 0 glTexGenivOES
PUBLIC a1380 0 glTexGenxOES
PUBLIC a1400 0 glTexGenxvOES
PUBLIC a1480 0 glTexImage1D
PUBLIC a1500 0 glTexImage2D
PUBLIC a1580 0 glTexImage2DMultisample
PUBLIC a1600 0 glTexImage2DMultisampleCoverageNV
PUBLIC a1680 0 glTexImage3D
PUBLIC a1700 0 glTexImage3DEXT
PUBLIC a1780 0 glTexImage3DMultisample
PUBLIC a1800 0 glTexImage3DMultisampleCoverageNV
PUBLIC a1880 0 glTexImage3DNV
PUBLIC a1900 0 glTexImage3DOES
PUBLIC a1980 0 glTexImage4DSGIS
PUBLIC a1a00 0 glTexPageCommitmentARB
PUBLIC a1a80 0 glTexPageCommitmentEXT
PUBLIC a1b00 0 glTexParameterIiv
PUBLIC a1b80 0 glTexParameterIivEXT
PUBLIC a1c00 0 glTexParameterIivOES
PUBLIC a1c80 0 glTexParameterIuiv
PUBLIC a1d00 0 glTexParameterIuivEXT
PUBLIC a1d80 0 glTexParameterIuivOES
PUBLIC a1e00 0 glTexParameterf
PUBLIC a1e80 0 glTexParameterfv
PUBLIC a1f00 0 glTexParameteri
PUBLIC a1f80 0 glTexParameteriv
PUBLIC a2000 0 glTexParameterx
PUBLIC a2080 0 glTexParameterxOES
PUBLIC a2100 0 glTexParameterxv
PUBLIC a2180 0 glTexParameterxvOES
PUBLIC a2200 0 glTexRenderbufferNV
PUBLIC a2280 0 glTexStorage1D
PUBLIC a2300 0 glTexStorage1DEXT
PUBLIC a2380 0 glTexStorage2D
PUBLIC a2400 0 glTexStorage2DEXT
PUBLIC a2480 0 glTexStorage2DMultisample
PUBLIC a2500 0 glTexStorage3D
PUBLIC a2580 0 glTexStorage3DEXT
PUBLIC a2600 0 glTexStorage3DMultisample
PUBLIC a2680 0 glTexStorage3DMultisampleOES
PUBLIC a2700 0 glTexStorageMem1DEXT
PUBLIC a2780 0 glTexStorageMem2DEXT
PUBLIC a2800 0 glTexStorageMem2DMultisampleEXT
PUBLIC a2880 0 glTexStorageMem3DEXT
PUBLIC a2900 0 glTexStorageMem3DMultisampleEXT
PUBLIC a2980 0 glTexStorageSparseAMD
PUBLIC a2a00 0 glTexSubImage1D
PUBLIC a2a80 0 glTexSubImage1DEXT
PUBLIC a2b00 0 glTexSubImage2D
PUBLIC a2b80 0 glTexSubImage2DEXT
PUBLIC a2c00 0 glTexSubImage3D
PUBLIC a2c80 0 glTexSubImage3DEXT
PUBLIC a2d00 0 glTexSubImage3DNV
PUBLIC a2d80 0 glTexSubImage3DOES
PUBLIC a2e00 0 glTexSubImage4DSGIS
PUBLIC a2e80 0 glTextureAttachMemoryNV
PUBLIC a2f00 0 glTextureBarrier
PUBLIC a2f80 0 glTextureBarrierNV
PUBLIC a3000 0 glTextureBuffer
PUBLIC a3080 0 glTextureBufferEXT
PUBLIC a3100 0 glTextureBufferRange
PUBLIC a3180 0 glTextureBufferRangeEXT
PUBLIC a3200 0 glTextureColorMaskSGIS
PUBLIC a3280 0 glTextureFoveationParametersQCOM
PUBLIC a3300 0 glTextureImage1DEXT
PUBLIC a3380 0 glTextureImage2DEXT
PUBLIC a3400 0 glTextureImage2DMultisampleCoverageNV
PUBLIC a3480 0 glTextureImage2DMultisampleNV
PUBLIC a3500 0 glTextureImage3DEXT
PUBLIC a3580 0 glTextureImage3DMultisampleCoverageNV
PUBLIC a3600 0 glTextureImage3DMultisampleNV
PUBLIC a3680 0 glTextureLightEXT
PUBLIC a3700 0 glTextureMaterialEXT
PUBLIC a3780 0 glTextureNormalEXT
PUBLIC a3800 0 glTexturePageCommitmentEXT
PUBLIC a3880 0 glTextureParameterIiv
PUBLIC a3900 0 glTextureParameterIivEXT
PUBLIC a3980 0 glTextureParameterIuiv
PUBLIC a3a00 0 glTextureParameterIuivEXT
PUBLIC a3a80 0 glTextureParameterf
PUBLIC a3b00 0 glTextureParameterfEXT
PUBLIC a3b80 0 glTextureParameterfv
PUBLIC a3c00 0 glTextureParameterfvEXT
PUBLIC a3c80 0 glTextureParameteri
PUBLIC a3d00 0 glTextureParameteriEXT
PUBLIC a3d80 0 glTextureParameteriv
PUBLIC a3e00 0 glTextureParameterivEXT
PUBLIC a3e80 0 glTextureRangeAPPLE
PUBLIC a3f00 0 glTextureRenderbufferEXT
PUBLIC a3f80 0 glTextureStorage1D
PUBLIC a4000 0 glTextureStorage1DEXT
PUBLIC a4080 0 glTextureStorage2D
PUBLIC a4100 0 glTextureStorage2DEXT
PUBLIC a4180 0 glTextureStorage2DMultisample
PUBLIC a4200 0 glTextureStorage2DMultisampleEXT
PUBLIC a4280 0 glTextureStorage3D
PUBLIC a4300 0 glTextureStorage3DEXT
PUBLIC a4380 0 glTextureStorage3DMultisample
PUBLIC a4400 0 glTextureStorage3DMultisampleEXT
PUBLIC a4480 0 glTextureStorageMem1DEXT
PUBLIC a4500 0 glTextureStorageMem2DEXT
PUBLIC a4580 0 glTextureStorageMem2DMultisampleEXT
PUBLIC a4600 0 glTextureStorageMem3DEXT
PUBLIC a4680 0 glTextureStorageMem3DMultisampleEXT
PUBLIC a4700 0 glTextureStorageSparseAMD
PUBLIC a4780 0 glTextureSubImage1D
PUBLIC a4800 0 glTextureSubImage1DEXT
PUBLIC a4880 0 glTextureSubImage2D
PUBLIC a4900 0 glTextureSubImage2DEXT
PUBLIC a4980 0 glTextureSubImage3D
PUBLIC a4a00 0 glTextureSubImage3DEXT
PUBLIC a4a80 0 glTextureView
PUBLIC a4b00 0 glTextureViewEXT
PUBLIC a4b80 0 glTextureViewOES
PUBLIC a4c00 0 glTrackMatrixNV
PUBLIC a4c80 0 glTransformFeedbackAttribsNV
PUBLIC a4d00 0 glTransformFeedbackBufferBase
PUBLIC a4d80 0 glTransformFeedbackBufferRange
PUBLIC a4e00 0 glTransformFeedbackStreamAttribsNV
PUBLIC a4e80 0 glTransformFeedbackVaryings
PUBLIC a4f00 0 glTransformFeedbackVaryingsEXT
PUBLIC a4f80 0 glTransformFeedbackVaryingsNV
PUBLIC a5000 0 glTransformPathNV
PUBLIC a5080 0 glTranslated
PUBLIC a5100 0 glTranslatef
PUBLIC a5180 0 glTranslatex
PUBLIC a5200 0 glTranslatexOES
PUBLIC a5280 0 glUniform1d
PUBLIC a5300 0 glUniform1dv
PUBLIC a5380 0 glUniform1f
PUBLIC a5400 0 glUniform1fARB
PUBLIC a5480 0 glUniform1fv
PUBLIC a5500 0 glUniform1fvARB
PUBLIC a5580 0 glUniform1i
PUBLIC a5600 0 glUniform1i64ARB
PUBLIC a5680 0 glUniform1i64NV
PUBLIC a5700 0 glUniform1i64vARB
PUBLIC a5780 0 glUniform1i64vNV
PUBLIC a5800 0 glUniform1iARB
PUBLIC a5880 0 glUniform1iv
PUBLIC a5900 0 glUniform1ivARB
PUBLIC a5980 0 glUniform1ui
PUBLIC a5a00 0 glUniform1ui64ARB
PUBLIC a5a80 0 glUniform1ui64NV
PUBLIC a5b00 0 glUniform1ui64vARB
PUBLIC a5b80 0 glUniform1ui64vNV
PUBLIC a5c00 0 glUniform1uiEXT
PUBLIC a5c80 0 glUniform1uiv
PUBLIC a5d00 0 glUniform1uivEXT
PUBLIC a5d80 0 glUniform2d
PUBLIC a5e00 0 glUniform2dv
PUBLIC a5e80 0 glUniform2f
PUBLIC a5f00 0 glUniform2fARB
PUBLIC a5f80 0 glUniform2fv
PUBLIC a6000 0 glUniform2fvARB
PUBLIC a6080 0 glUniform2i
PUBLIC a6100 0 glUniform2i64ARB
PUBLIC a6180 0 glUniform2i64NV
PUBLIC a6200 0 glUniform2i64vARB
PUBLIC a6280 0 glUniform2i64vNV
PUBLIC a6300 0 glUniform2iARB
PUBLIC a6380 0 glUniform2iv
PUBLIC a6400 0 glUniform2ivARB
PUBLIC a6480 0 glUniform2ui
PUBLIC a6500 0 glUniform2ui64ARB
PUBLIC a6580 0 glUniform2ui64NV
PUBLIC a6600 0 glUniform2ui64vARB
PUBLIC a6680 0 glUniform2ui64vNV
PUBLIC a6700 0 glUniform2uiEXT
PUBLIC a6780 0 glUniform2uiv
PUBLIC a6800 0 glUniform2uivEXT
PUBLIC a6880 0 glUniform3d
PUBLIC a6900 0 glUniform3dv
PUBLIC a6980 0 glUniform3f
PUBLIC a6a00 0 glUniform3fARB
PUBLIC a6a80 0 glUniform3fv
PUBLIC a6b00 0 glUniform3fvARB
PUBLIC a6b80 0 glUniform3i
PUBLIC a6c00 0 glUniform3i64ARB
PUBLIC a6c80 0 glUniform3i64NV
PUBLIC a6d00 0 glUniform3i64vARB
PUBLIC a6d80 0 glUniform3i64vNV
PUBLIC a6e00 0 glUniform3iARB
PUBLIC a6e80 0 glUniform3iv
PUBLIC a6f00 0 glUniform3ivARB
PUBLIC a6f80 0 glUniform3ui
PUBLIC a7000 0 glUniform3ui64ARB
PUBLIC a7080 0 glUniform3ui64NV
PUBLIC a7100 0 glUniform3ui64vARB
PUBLIC a7180 0 glUniform3ui64vNV
PUBLIC a7200 0 glUniform3uiEXT
PUBLIC a7280 0 glUniform3uiv
PUBLIC a7300 0 glUniform3uivEXT
PUBLIC a7380 0 glUniform4d
PUBLIC a7400 0 glUniform4dv
PUBLIC a7480 0 glUniform4f
PUBLIC a7500 0 glUniform4fARB
PUBLIC a7580 0 glUniform4fv
PUBLIC a7600 0 glUniform4fvARB
PUBLIC a7680 0 glUniform4i
PUBLIC a7700 0 glUniform4i64ARB
PUBLIC a7780 0 glUniform4i64NV
PUBLIC a7800 0 glUniform4i64vARB
PUBLIC a7880 0 glUniform4i64vNV
PUBLIC a7900 0 glUniform4iARB
PUBLIC a7980 0 glUniform4iv
PUBLIC a7a00 0 glUniform4ivARB
PUBLIC a7a80 0 glUniform4ui
PUBLIC a7b00 0 glUniform4ui64ARB
PUBLIC a7b80 0 glUniform4ui64NV
PUBLIC a7c00 0 glUniform4ui64vARB
PUBLIC a7c80 0 glUniform4ui64vNV
PUBLIC a7d00 0 glUniform4uiEXT
PUBLIC a7d80 0 glUniform4uiv
PUBLIC a7e00 0 glUniform4uivEXT
PUBLIC a7e80 0 glUniformBlockBinding
PUBLIC a7f00 0 glUniformBufferEXT
PUBLIC a7f80 0 glUniformHandleui64ARB
PUBLIC a8000 0 glUniformHandleui64IMG
PUBLIC a8080 0 glUniformHandleui64NV
PUBLIC a8100 0 glUniformHandleui64vARB
PUBLIC a8180 0 glUniformHandleui64vIMG
PUBLIC a8200 0 glUniformHandleui64vNV
PUBLIC a8280 0 glUniformMatrix2dv
PUBLIC a8300 0 glUniformMatrix2fv
PUBLIC a8380 0 glUniformMatrix2fvARB
PUBLIC a8400 0 glUniformMatrix2x3dv
PUBLIC a8480 0 glUniformMatrix2x3fv
PUBLIC a8500 0 glUniformMatrix2x3fvNV
PUBLIC a8580 0 glUniformMatrix2x4dv
PUBLIC a8600 0 glUniformMatrix2x4fv
PUBLIC a8680 0 glUniformMatrix2x4fvNV
PUBLIC a8700 0 glUniformMatrix3dv
PUBLIC a8780 0 glUniformMatrix3fv
PUBLIC a8800 0 glUniformMatrix3fvARB
PUBLIC a8880 0 glUniformMatrix3x2dv
PUBLIC a8900 0 glUniformMatrix3x2fv
PUBLIC a8980 0 glUniformMatrix3x2fvNV
PUBLIC a8a00 0 glUniformMatrix3x4dv
PUBLIC a8a80 0 glUniformMatrix3x4fv
PUBLIC a8b00 0 glUniformMatrix3x4fvNV
PUBLIC a8b80 0 glUniformMatrix4dv
PUBLIC a8c00 0 glUniformMatrix4fv
PUBLIC a8c80 0 glUniformMatrix4fvARB
PUBLIC a8d00 0 glUniformMatrix4x2dv
PUBLIC a8d80 0 glUniformMatrix4x2fv
PUBLIC a8e00 0 glUniformMatrix4x2fvNV
PUBLIC a8e80 0 glUniformMatrix4x3dv
PUBLIC a8f00 0 glUniformMatrix4x3fv
PUBLIC a8f80 0 glUniformMatrix4x3fvNV
PUBLIC a9000 0 glUniformSubroutinesuiv
PUBLIC a9080 0 glUniformui64NV
PUBLIC a9100 0 glUniformui64vNV
PUBLIC a9180 0 glUnlockArraysEXT
PUBLIC a9200 0 glUnmapBuffer
PUBLIC a9280 0 glUnmapBufferARB
PUBLIC a9300 0 glUnmapBufferOES
PUBLIC a9380 0 glUnmapNamedBuffer
PUBLIC a9400 0 glUnmapNamedBufferEXT
PUBLIC a9480 0 glUnmapObjectBufferATI
PUBLIC a9500 0 glUnmapTexture2DINTEL
PUBLIC a9580 0 glUpdateObjectBufferATI
PUBLIC a9600 0 glUploadGpuMaskNVX
PUBLIC a9680 0 glUseProgram
PUBLIC a9700 0 glUseProgramObjectARB
PUBLIC a9780 0 glUseProgramStages
PUBLIC a9800 0 glUseProgramStagesEXT
PUBLIC a9880 0 glUseShaderProgramEXT
PUBLIC a9900 0 glVDPAUFiniNV
PUBLIC a9980 0 glVDPAUGetSurfaceivNV
PUBLIC a9a00 0 glVDPAUInitNV
PUBLIC a9a80 0 glVDPAUIsSurfaceNV
PUBLIC a9b00 0 glVDPAUMapSurfacesNV
PUBLIC a9b80 0 glVDPAURegisterOutputSurfaceNV
PUBLIC a9c00 0 glVDPAURegisterVideoSurfaceNV
PUBLIC a9c80 0 glVDPAURegisterVideoSurfaceWithPictureStructureNV
PUBLIC a9d00 0 glVDPAUSurfaceAccessNV
PUBLIC a9d80 0 glVDPAUUnmapSurfacesNV
PUBLIC a9e00 0 glVDPAUUnregisterSurfaceNV
PUBLIC a9e80 0 glValidBackBufferHintAutodesk
PUBLIC a9f00 0 glValidateProgram
PUBLIC a9f80 0 glValidateProgramARB
PUBLIC aa000 0 glValidateProgramPipeline
PUBLIC aa080 0 glValidateProgramPipelineEXT
PUBLIC aa100 0 glVariantArrayObjectATI
PUBLIC aa180 0 glVariantPointerEXT
PUBLIC aa200 0 glVariantbvEXT
PUBLIC aa280 0 glVariantdvEXT
PUBLIC aa300 0 glVariantfvEXT
PUBLIC aa380 0 glVariantivEXT
PUBLIC aa400 0 glVariantsvEXT
PUBLIC aa480 0 glVariantubvEXT
PUBLIC aa500 0 glVariantuivEXT
PUBLIC aa580 0 glVariantusvEXT
PUBLIC aa600 0 glVertex2bOES
PUBLIC aa680 0 glVertex2bvOES
PUBLIC aa700 0 glVertex2d
PUBLIC aa780 0 glVertex2dv
PUBLIC aa800 0 glVertex2f
PUBLIC aa880 0 glVertex2fv
PUBLIC aa900 0 glVertex2hNV
PUBLIC aa980 0 glVertex2hvNV
PUBLIC aaa00 0 glVertex2i
PUBLIC aaa80 0 glVertex2iv
PUBLIC aab00 0 glVertex2s
PUBLIC aab80 0 glVertex2sv
PUBLIC aac00 0 glVertex2xOES
PUBLIC aac80 0 glVertex2xvOES
PUBLIC aad00 0 glVertex3bOES
PUBLIC aad80 0 glVertex3bvOES
PUBLIC aae00 0 glVertex3d
PUBLIC aae80 0 glVertex3dv
PUBLIC aaf00 0 glVertex3f
PUBLIC aaf80 0 glVertex3fv
PUBLIC ab000 0 glVertex3hNV
PUBLIC ab080 0 glVertex3hvNV
PUBLIC ab100 0 glVertex3i
PUBLIC ab180 0 glVertex3iv
PUBLIC ab200 0 glVertex3s
PUBLIC ab280 0 glVertex3sv
PUBLIC ab300 0 glVertex3xOES
PUBLIC ab380 0 glVertex3xvOES
PUBLIC ab400 0 glVertex4bOES
PUBLIC ab480 0 glVertex4bvOES
PUBLIC ab500 0 glVertex4d
PUBLIC ab580 0 glVertex4dv
PUBLIC ab600 0 glVertex4f
PUBLIC ab680 0 glVertex4fv
PUBLIC ab700 0 glVertex4hNV
PUBLIC ab780 0 glVertex4hvNV
PUBLIC ab800 0 glVertex4i
PUBLIC ab880 0 glVertex4iv
PUBLIC ab900 0 glVertex4s
PUBLIC ab980 0 glVertex4sv
PUBLIC aba00 0 glVertex4xOES
PUBLIC aba80 0 glVertex4xvOES
PUBLIC abb00 0 glVertexArrayAttribBinding
PUBLIC abb80 0 glVertexArrayAttribFormat
PUBLIC abc00 0 glVertexArrayAttribIFormat
PUBLIC abc80 0 glVertexArrayAttribLFormat
PUBLIC abd00 0 glVertexArrayBindVertexBufferEXT
PUBLIC abd80 0 glVertexArrayBindingDivisor
PUBLIC abe00 0 glVertexArrayColorOffsetEXT
PUBLIC abe80 0 glVertexArrayEdgeFlagOffsetEXT
PUBLIC abf00 0 glVertexArrayElementBuffer
PUBLIC abf80 0 glVertexArrayFogCoordOffsetEXT
PUBLIC ac000 0 glVertexArrayIndexOffsetEXT
PUBLIC ac080 0 glVertexArrayMultiTexCoordOffsetEXT
PUBLIC ac100 0 glVertexArrayNormalOffsetEXT
PUBLIC ac180 0 glVertexArrayParameteriAPPLE
PUBLIC ac200 0 glVertexArrayRangeAPPLE
PUBLIC ac280 0 glVertexArrayRangeNV
PUBLIC ac300 0 glVertexArraySecondaryColorOffsetEXT
PUBLIC ac380 0 glVertexArrayTexCoordOffsetEXT
PUBLIC ac400 0 glVertexArrayVertexAttribBindingEXT
PUBLIC ac480 0 glVertexArrayVertexAttribDivisorEXT
PUBLIC ac500 0 glVertexArrayVertexAttribFormatEXT
PUBLIC ac580 0 glVertexArrayVertexAttribIFormatEXT
PUBLIC ac600 0 glVertexArrayVertexAttribIOffsetEXT
PUBLIC ac680 0 glVertexArrayVertexAttribLFormatEXT
PUBLIC ac700 0 glVertexArrayVertexAttribLOffsetEXT
PUBLIC ac780 0 glVertexArrayVertexAttribOffsetEXT
PUBLIC ac800 0 glVertexArrayVertexBindingDivisorEXT
PUBLIC ac880 0 glVertexArrayVertexBuffer
PUBLIC ac900 0 glVertexArrayVertexBuffers
PUBLIC ac980 0 glVertexArrayVertexOffsetEXT
PUBLIC aca00 0 glVertexAttrib1d
PUBLIC aca80 0 glVertexAttrib1dARB
PUBLIC acb00 0 glVertexAttrib1dNV
PUBLIC acb80 0 glVertexAttrib1dv
PUBLIC acc00 0 glVertexAttrib1dvARB
PUBLIC acc80 0 glVertexAttrib1dvNV
PUBLIC acd00 0 glVertexAttrib1f
PUBLIC acd80 0 glVertexAttrib1fARB
PUBLIC ace00 0 glVertexAttrib1fNV
PUBLIC ace80 0 glVertexAttrib1fv
PUBLIC acf00 0 glVertexAttrib1fvARB
PUBLIC acf80 0 glVertexAttrib1fvNV
PUBLIC ad000 0 glVertexAttrib1hNV
PUBLIC ad080 0 glVertexAttrib1hvNV
PUBLIC ad100 0 glVertexAttrib1s
PUBLIC ad180 0 glVertexAttrib1sARB
PUBLIC ad200 0 glVertexAttrib1sNV
PUBLIC ad280 0 glVertexAttrib1sv
PUBLIC ad300 0 glVertexAttrib1svARB
PUBLIC ad380 0 glVertexAttrib1svNV
PUBLIC ad400 0 glVertexAttrib2d
PUBLIC ad480 0 glVertexAttrib2dARB
PUBLIC ad500 0 glVertexAttrib2dNV
PUBLIC ad580 0 glVertexAttrib2dv
PUBLIC ad600 0 glVertexAttrib2dvARB
PUBLIC ad680 0 glVertexAttrib2dvNV
PUBLIC ad700 0 glVertexAttrib2f
PUBLIC ad780 0 glVertexAttrib2fARB
PUBLIC ad800 0 glVertexAttrib2fNV
PUBLIC ad880 0 glVertexAttrib2fv
PUBLIC ad900 0 glVertexAttrib2fvARB
PUBLIC ad980 0 glVertexAttrib2fvNV
PUBLIC ada00 0 glVertexAttrib2hNV
PUBLIC ada80 0 glVertexAttrib2hvNV
PUBLIC adb00 0 glVertexAttrib2s
PUBLIC adb80 0 glVertexAttrib2sARB
PUBLIC adc00 0 glVertexAttrib2sNV
PUBLIC adc80 0 glVertexAttrib2sv
PUBLIC add00 0 glVertexAttrib2svARB
PUBLIC add80 0 glVertexAttrib2svNV
PUBLIC ade00 0 glVertexAttrib3d
PUBLIC ade80 0 glVertexAttrib3dARB
PUBLIC adf00 0 glVertexAttrib3dNV
PUBLIC adf80 0 glVertexAttrib3dv
PUBLIC ae000 0 glVertexAttrib3dvARB
PUBLIC ae080 0 glVertexAttrib3dvNV
PUBLIC ae100 0 glVertexAttrib3f
PUBLIC ae180 0 glVertexAttrib3fARB
PUBLIC ae200 0 glVertexAttrib3fNV
PUBLIC ae280 0 glVertexAttrib3fv
PUBLIC ae300 0 glVertexAttrib3fvARB
PUBLIC ae380 0 glVertexAttrib3fvNV
PUBLIC ae400 0 glVertexAttrib3hNV
PUBLIC ae480 0 glVertexAttrib3hvNV
PUBLIC ae500 0 glVertexAttrib3s
PUBLIC ae580 0 glVertexAttrib3sARB
PUBLIC ae600 0 glVertexAttrib3sNV
PUBLIC ae680 0 glVertexAttrib3sv
PUBLIC ae700 0 glVertexAttrib3svARB
PUBLIC ae780 0 glVertexAttrib3svNV
PUBLIC ae800 0 glVertexAttrib4Nbv
PUBLIC ae880 0 glVertexAttrib4NbvARB
PUBLIC ae900 0 glVertexAttrib4Niv
PUBLIC ae980 0 glVertexAttrib4NivARB
PUBLIC aea00 0 glVertexAttrib4Nsv
PUBLIC aea80 0 glVertexAttrib4NsvARB
PUBLIC aeb00 0 glVertexAttrib4Nub
PUBLIC aeb80 0 glVertexAttrib4NubARB
PUBLIC aec00 0 glVertexAttrib4Nubv
PUBLIC aec80 0 glVertexAttrib4NubvARB
PUBLIC aed00 0 glVertexAttrib4Nuiv
PUBLIC aed80 0 glVertexAttrib4NuivARB
PUBLIC aee00 0 glVertexAttrib4Nusv
PUBLIC aee80 0 glVertexAttrib4NusvARB
PUBLIC aef00 0 glVertexAttrib4bv
PUBLIC aef80 0 glVertexAttrib4bvARB
PUBLIC af000 0 glVertexAttrib4d
PUBLIC af080 0 glVertexAttrib4dARB
PUBLIC af100 0 glVertexAttrib4dNV
PUBLIC af180 0 glVertexAttrib4dv
PUBLIC af200 0 glVertexAttrib4dvARB
PUBLIC af280 0 glVertexAttrib4dvNV
PUBLIC af300 0 glVertexAttrib4f
PUBLIC af380 0 glVertexAttrib4fARB
PUBLIC af400 0 glVertexAttrib4fNV
PUBLIC af480 0 glVertexAttrib4fv
PUBLIC af500 0 glVertexAttrib4fvARB
PUBLIC af580 0 glVertexAttrib4fvNV
PUBLIC af600 0 glVertexAttrib4hNV
PUBLIC af680 0 glVertexAttrib4hvNV
PUBLIC af700 0 glVertexAttrib4iv
PUBLIC af780 0 glVertexAttrib4ivARB
PUBLIC af800 0 glVertexAttrib4s
PUBLIC af880 0 glVertexAttrib4sARB
PUBLIC af900 0 glVertexAttrib4sNV
PUBLIC af980 0 glVertexAttrib4sv
PUBLIC afa00 0 glVertexAttrib4svARB
PUBLIC afa80 0 glVertexAttrib4svNV
PUBLIC afb00 0 glVertexAttrib4ubNV
PUBLIC afb80 0 glVertexAttrib4ubv
PUBLIC afc00 0 glVertexAttrib4ubvARB
PUBLIC afc80 0 glVertexAttrib4ubvNV
PUBLIC afd00 0 glVertexAttrib4uiv
PUBLIC afd80 0 glVertexAttrib4uivARB
PUBLIC afe00 0 glVertexAttrib4usv
PUBLIC afe80 0 glVertexAttrib4usvARB
PUBLIC aff00 0 glVertexAttribArrayObjectATI
PUBLIC aff80 0 glVertexAttribBinding
PUBLIC b0000 0 glVertexAttribDivisor
PUBLIC b0080 0 glVertexAttribDivisorANGLE
PUBLIC b0100 0 glVertexAttribDivisorARB
PUBLIC b0180 0 glVertexAttribDivisorEXT
PUBLIC b0200 0 glVertexAttribDivisorNV
PUBLIC b0280 0 glVertexAttribFormat
PUBLIC b0300 0 glVertexAttribFormatNV
PUBLIC b0380 0 glVertexAttribI1i
PUBLIC b0400 0 glVertexAttribI1iEXT
PUBLIC b0480 0 glVertexAttribI1iv
PUBLIC b0500 0 glVertexAttribI1ivEXT
PUBLIC b0580 0 glVertexAttribI1ui
PUBLIC b0600 0 glVertexAttribI1uiEXT
PUBLIC b0680 0 glVertexAttribI1uiv
PUBLIC b0700 0 glVertexAttribI1uivEXT
PUBLIC b0780 0 glVertexAttribI2i
PUBLIC b0800 0 glVertexAttribI2iEXT
PUBLIC b0880 0 glVertexAttribI2iv
PUBLIC b0900 0 glVertexAttribI2ivEXT
PUBLIC b0980 0 glVertexAttribI2ui
PUBLIC b0a00 0 glVertexAttribI2uiEXT
PUBLIC b0a80 0 glVertexAttribI2uiv
PUBLIC b0b00 0 glVertexAttribI2uivEXT
PUBLIC b0b80 0 glVertexAttribI3i
PUBLIC b0c00 0 glVertexAttribI3iEXT
PUBLIC b0c80 0 glVertexAttribI3iv
PUBLIC b0d00 0 glVertexAttribI3ivEXT
PUBLIC b0d80 0 glVertexAttribI3ui
PUBLIC b0e00 0 glVertexAttribI3uiEXT
PUBLIC b0e80 0 glVertexAttribI3uiv
PUBLIC b0f00 0 glVertexAttribI3uivEXT
PUBLIC b0f80 0 glVertexAttribI4bv
PUBLIC b1000 0 glVertexAttribI4bvEXT
PUBLIC b1080 0 glVertexAttribI4i
PUBLIC b1100 0 glVertexAttribI4iEXT
PUBLIC b1180 0 glVertexAttribI4iv
PUBLIC b1200 0 glVertexAttribI4ivEXT
PUBLIC b1280 0 glVertexAttribI4sv
PUBLIC b1300 0 glVertexAttribI4svEXT
PUBLIC b1380 0 glVertexAttribI4ubv
PUBLIC b1400 0 glVertexAttribI4ubvEXT
PUBLIC b1480 0 glVertexAttribI4ui
PUBLIC b1500 0 glVertexAttribI4uiEXT
PUBLIC b1580 0 glVertexAttribI4uiv
PUBLIC b1600 0 glVertexAttribI4uivEXT
PUBLIC b1680 0 glVertexAttribI4usv
PUBLIC b1700 0 glVertexAttribI4usvEXT
PUBLIC b1780 0 glVertexAttribIFormat
PUBLIC b1800 0 glVertexAttribIFormatNV
PUBLIC b1880 0 glVertexAttribIPointer
PUBLIC b1900 0 glVertexAttribIPointerEXT
PUBLIC b1980 0 glVertexAttribL1d
PUBLIC b1a00 0 glVertexAttribL1dEXT
PUBLIC b1a80 0 glVertexAttribL1dv
PUBLIC b1b00 0 glVertexAttribL1dvEXT
PUBLIC b1b80 0 glVertexAttribL1i64NV
PUBLIC b1c00 0 glVertexAttribL1i64vNV
PUBLIC b1c80 0 glVertexAttribL1ui64ARB
PUBLIC b1d00 0 glVertexAttribL1ui64NV
PUBLIC b1d80 0 glVertexAttribL1ui64vARB
PUBLIC b1e00 0 glVertexAttribL1ui64vNV
PUBLIC b1e80 0 glVertexAttribL2d
PUBLIC b1f00 0 glVertexAttribL2dEXT
PUBLIC b1f80 0 glVertexAttribL2dv
PUBLIC b2000 0 glVertexAttribL2dvEXT
PUBLIC b2080 0 glVertexAttribL2i64NV
PUBLIC b2100 0 glVertexAttribL2i64vNV
PUBLIC b2180 0 glVertexAttribL2ui64NV
PUBLIC b2200 0 glVertexAttribL2ui64vNV
PUBLIC b2280 0 glVertexAttribL3d
PUBLIC b2300 0 glVertexAttribL3dEXT
PUBLIC b2380 0 glVertexAttribL3dv
PUBLIC b2400 0 glVertexAttribL3dvEXT
PUBLIC b2480 0 glVertexAttribL3i64NV
PUBLIC b2500 0 glVertexAttribL3i64vNV
PUBLIC b2580 0 glVertexAttribL3ui64NV
PUBLIC b2600 0 glVertexAttribL3ui64vNV
PUBLIC b2680 0 glVertexAttribL4d
PUBLIC b2700 0 glVertexAttribL4dEXT
PUBLIC b2780 0 glVertexAttribL4dv
PUBLIC b2800 0 glVertexAttribL4dvEXT
PUBLIC b2880 0 glVertexAttribL4i64NV
PUBLIC b2900 0 glVertexAttribL4i64vNV
PUBLIC b2980 0 glVertexAttribL4ui64NV
PUBLIC b2a00 0 glVertexAttribL4ui64vNV
PUBLIC b2a80 0 glVertexAttribLFormat
PUBLIC b2b00 0 glVertexAttribLFormatNV
PUBLIC b2b80 0 glVertexAttribLPointer
PUBLIC b2c00 0 glVertexAttribLPointerEXT
PUBLIC b2c80 0 glVertexAttribP1ui
PUBLIC b2d00 0 glVertexAttribP1uiv
PUBLIC b2d80 0 glVertexAttribP2ui
PUBLIC b2e00 0 glVertexAttribP2uiv
PUBLIC b2e80 0 glVertexAttribP3ui
PUBLIC b2f00 0 glVertexAttribP3uiv
PUBLIC b2f80 0 glVertexAttribP4ui
PUBLIC b3000 0 glVertexAttribP4uiv
PUBLIC b3080 0 glVertexAttribParameteriAMD
PUBLIC b3100 0 glVertexAttribPointer
PUBLIC b3180 0 glVertexAttribPointerARB
PUBLIC b3200 0 glVertexAttribPointerNV
PUBLIC b3280 0 glVertexAttribs1dvNV
PUBLIC b3300 0 glVertexAttribs1fvNV
PUBLIC b3380 0 glVertexAttribs1hvNV
PUBLIC b3400 0 glVertexAttribs1svNV
PUBLIC b3480 0 glVertexAttribs2dvNV
PUBLIC b3500 0 glVertexAttribs2fvNV
PUBLIC b3580 0 glVertexAttribs2hvNV
PUBLIC b3600 0 glVertexAttribs2svNV
PUBLIC b3680 0 glVertexAttribs3dvNV
PUBLIC b3700 0 glVertexAttribs3fvNV
PUBLIC b3780 0 glVertexAttribs3hvNV
PUBLIC b3800 0 glVertexAttribs3svNV
PUBLIC b3880 0 glVertexAttribs4dvNV
PUBLIC b3900 0 glVertexAttribs4fvNV
PUBLIC b3980 0 glVertexAttribs4hvNV
PUBLIC b3a00 0 glVertexAttribs4svNV
PUBLIC b3a80 0 glVertexAttribs4ubvNV
PUBLIC b3b00 0 glVertexBindingDivisor
PUBLIC b3b80 0 glVertexBlendARB
PUBLIC b3c00 0 glVertexBlendEnvfATI
PUBLIC b3c80 0 glVertexBlendEnviATI
PUBLIC b3d00 0 glVertexFormatNV
PUBLIC b3d80 0 glVertexP2ui
PUBLIC b3e00 0 glVertexP2uiv
PUBLIC b3e80 0 glVertexP3ui
PUBLIC b3f00 0 glVertexP3uiv
PUBLIC b3f80 0 glVertexP4ui
PUBLIC b4000 0 glVertexP4uiv
PUBLIC b4080 0 glVertexPointer
PUBLIC b4100 0 glVertexPointerEXT
PUBLIC b4180 0 glVertexPointerListIBM
PUBLIC b4200 0 glVertexPointervINTEL
PUBLIC b4280 0 glVertexStream1dATI
PUBLIC b4300 0 glVertexStream1dvATI
PUBLIC b4380 0 glVertexStream1fATI
PUBLIC b4400 0 glVertexStream1fvATI
PUBLIC b4480 0 glVertexStream1iATI
PUBLIC b4500 0 glVertexStream1ivATI
PUBLIC b4580 0 glVertexStream1sATI
PUBLIC b4600 0 glVertexStream1svATI
PUBLIC b4680 0 glVertexStream2dATI
PUBLIC b4700 0 glVertexStream2dvATI
PUBLIC b4780 0 glVertexStream2fATI
PUBLIC b4800 0 glVertexStream2fvATI
PUBLIC b4880 0 glVertexStream2iATI
PUBLIC b4900 0 glVertexStream2ivATI
PUBLIC b4980 0 glVertexStream2sATI
PUBLIC b4a00 0 glVertexStream2svATI
PUBLIC b4a80 0 glVertexStream3dATI
PUBLIC b4b00 0 glVertexStream3dvATI
PUBLIC b4b80 0 glVertexStream3fATI
PUBLIC b4c00 0 glVertexStream3fvATI
PUBLIC b4c80 0 glVertexStream3iATI
PUBLIC b4d00 0 glVertexStream3ivATI
PUBLIC b4d80 0 glVertexStream3sATI
PUBLIC b4e00 0 glVertexStream3svATI
PUBLIC b4e80 0 glVertexStream4dATI
PUBLIC b4f00 0 glVertexStream4dvATI
PUBLIC b4f80 0 glVertexStream4fATI
PUBLIC b5000 0 glVertexStream4fvATI
PUBLIC b5080 0 glVertexStream4iATI
PUBLIC b5100 0 glVertexStream4ivATI
PUBLIC b5180 0 glVertexStream4sATI
PUBLIC b5200 0 glVertexStream4svATI
PUBLIC b5280 0 glVertexWeightPointerEXT
PUBLIC b5300 0 glVertexWeightfEXT
PUBLIC b5380 0 glVertexWeightfvEXT
PUBLIC b5400 0 glVertexWeighthNV
PUBLIC b5480 0 glVertexWeighthvNV
PUBLIC b5500 0 glVideoCaptureNV
PUBLIC b5580 0 glVideoCaptureStreamParameterdvNV
PUBLIC b5600 0 glVideoCaptureStreamParameterfvNV
PUBLIC b5680 0 glVideoCaptureStreamParameterivNV
PUBLIC b5700 0 glViewport
PUBLIC b5780 0 glViewportArrayv
PUBLIC b5800 0 glViewportArrayvNV
PUBLIC b5880 0 glViewportArrayvOES
PUBLIC b5900 0 glViewportIndexedf
PUBLIC b5980 0 glViewportIndexedfNV
PUBLIC b5a00 0 glViewportIndexedfOES
PUBLIC b5a80 0 glViewportIndexedfv
PUBLIC b5b00 0 glViewportIndexedfvNV
PUBLIC b5b80 0 glViewportIndexedfvOES
PUBLIC b5c00 0 glViewportPositionWScaleNV
PUBLIC b5c80 0 glViewportSwizzleNV
PUBLIC b5d00 0 glWaitSemaphoreEXT
PUBLIC b5d80 0 glWaitSemaphoreui64NVX
PUBLIC b5e00 0 glWaitSync
PUBLIC b5e80 0 glWaitSyncAPPLE
PUBLIC b5f00 0 glWaitSyncValueuiNVX
PUBLIC b5f80 0 glWaitVkSemaphoreNV
PUBLIC b6000 0 glWeightPathsNV
PUBLIC b6080 0 glWeightPointerARB
PUBLIC b6100 0 glWeightPointerOES
PUBLIC b6180 0 glWeightbvARB
PUBLIC b6200 0 glWeightdvARB
PUBLIC b6280 0 glWeightfvARB
PUBLIC b6300 0 glWeightivARB
PUBLIC b6380 0 glWeightsvARB
PUBLIC b6400 0 glWeightubvARB
PUBLIC b6480 0 glWeightuivARB
PUBLIC b6500 0 glWeightusvARB
PUBLIC b6580 0 glWindowBackBufferHintAutodesk
PUBLIC b6600 0 glWindowPos2d
PUBLIC b6680 0 glWindowPos2dARB
PUBLIC b6700 0 glWindowPos2dMESA
PUBLIC b6780 0 glWindowPos2dv
PUBLIC b6800 0 glWindowPos2dvARB
PUBLIC b6880 0 glWindowPos2dvMESA
PUBLIC b6900 0 glWindowPos2f
PUBLIC b6980 0 glWindowPos2fARB
PUBLIC b6a00 0 glWindowPos2fMESA
PUBLIC b6a80 0 glWindowPos2fv
PUBLIC b6b00 0 glWindowPos2fvARB
PUBLIC b6b80 0 glWindowPos2fvMESA
PUBLIC b6c00 0 glWindowPos2i
PUBLIC b6c80 0 glWindowPos2iARB
PUBLIC b6d00 0 glWindowPos2iMESA
PUBLIC b6d80 0 glWindowPos2iv
PUBLIC b6e00 0 glWindowPos2ivARB
PUBLIC b6e80 0 glWindowPos2ivMESA
PUBLIC b6f00 0 glWindowPos2s
PUBLIC b6f80 0 glWindowPos2sARB
PUBLIC b7000 0 glWindowPos2sMESA
PUBLIC b7080 0 glWindowPos2sv
PUBLIC b7100 0 glWindowPos2svARB
PUBLIC b7180 0 glWindowPos2svMESA
PUBLIC b7200 0 glWindowPos3d
PUBLIC b7280 0 glWindowPos3dARB
PUBLIC b7300 0 glWindowPos3dMESA
PUBLIC b7380 0 glWindowPos3dv
PUBLIC b7400 0 glWindowPos3dvARB
PUBLIC b7480 0 glWindowPos3dvMESA
PUBLIC b7500 0 glWindowPos3f
PUBLIC b7580 0 glWindowPos3fARB
PUBLIC b7600 0 glWindowPos3fMESA
PUBLIC b7680 0 glWindowPos3fv
PUBLIC b7700 0 glWindowPos3fvARB
PUBLIC b7780 0 glWindowPos3fvMESA
PUBLIC b7800 0 glWindowPos3i
PUBLIC b7880 0 glWindowPos3iARB
PUBLIC b7900 0 glWindowPos3iMESA
PUBLIC b7980 0 glWindowPos3iv
PUBLIC b7a00 0 glWindowPos3ivARB
PUBLIC b7a80 0 glWindowPos3ivMESA
PUBLIC b7b00 0 glWindowPos3s
PUBLIC b7b80 0 glWindowPos3sARB
PUBLIC b7c00 0 glWindowPos3sMESA
PUBLIC b7c80 0 glWindowPos3sv
PUBLIC b7d00 0 glWindowPos3svARB
PUBLIC b7d80 0 glWindowPos3svMESA
PUBLIC b7e00 0 glWindowPos4dMESA
PUBLIC b7e80 0 glWindowPos4dvMESA
PUBLIC b7f00 0 glWindowPos4fMESA
PUBLIC b7f80 0 glWindowPos4fvMESA
PUBLIC b8000 0 glWindowPos4iMESA
PUBLIC b8080 0 glWindowPos4ivMESA
PUBLIC b8100 0 glWindowPos4sMESA
PUBLIC b8180 0 glWindowPos4svMESA
PUBLIC b8200 0 glWindowRectanglesEXT
PUBLIC b8280 0 glWriteMaskEXT
STACK CFI INIT 42b58 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42bc8 48 .cfa: sp 0 + .ra: x30
STACK CFI 42bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42bd4 x19: .cfa -16 + ^
STACK CFI 42c0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42c10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42c18 80 .cfa: sp 0 + .ra: x30
STACK CFI 42c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42c2c v10: .cfa -24 + ^
STACK CFI 42c34 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 42c44 x19: .cfa -32 + ^
STACK CFI 42c80 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 42c84 .cfa: sp 48 + .ra: .cfa -40 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 42c94 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI INIT 42c98 7c .cfa: sp 0 + .ra: x30
STACK CFI 42c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42cc4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42d00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 42d18 60 .cfa: sp 0 + .ra: x30
STACK CFI 42d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42d30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 42d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42d78 74 .cfa: sp 0 + .ra: x30
STACK CFI 42d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42da4 x21: .cfa -16 + ^
STACK CFI 42dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 42dd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 42de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 42df0 70 .cfa: sp 0 + .ra: x30
STACK CFI 42df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42e04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42e1c x21: .cfa -16 + ^
STACK CFI 42e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 42e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 42e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 42e60 78 .cfa: sp 0 + .ra: x30
STACK CFI 42e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42e74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42e8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42ec8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 42ed8 74 .cfa: sp 0 + .ra: x30
STACK CFI 42edc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42eec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42f04 x21: .cfa -16 + ^
STACK CFI 42f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 42f38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 42f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 42f50 7c .cfa: sp 0 + .ra: x30
STACK CFI 42f54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42f64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42f7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42fb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 42fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 42fd0 7c .cfa: sp 0 + .ra: x30
STACK CFI 42fd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42fe4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42ffc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43038 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 43048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 43050 d4 .cfa: sp 0 + .ra: x30
STACK CFI 43054 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 43064 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4307c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4308c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 43098 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 430a4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 43104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 43108 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 43120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 43128 ac .cfa: sp 0 + .ra: x30
STACK CFI 4312c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4313c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43154 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43164 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43170 x25: .cfa -16 + ^
STACK CFI 431b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 431b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 431d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 431d8 7c .cfa: sp 0 + .ra: x30
STACK CFI 431dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 431ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43204 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4323c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43240 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 43250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 43258 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43270 78 .cfa: sp 0 + .ra: x30
STACK CFI 43274 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43284 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4329c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 432d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 432d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 432e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 432e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43300 b0 .cfa: sp 0 + .ra: x30
STACK CFI 43304 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43314 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4332c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4333c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43348 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 43394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 43398 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 433ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 433b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 433c8 108 .cfa: sp 0 + .ra: x30
STACK CFI 433cc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 433f0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 43408 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 43418 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 43424 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 43430 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 434b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 434b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 434cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 434d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 434d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 434e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 434fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4350c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4354c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43550 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 43560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 43568 5c .cfa: sp 0 + .ra: x30
STACK CFI 4356c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43580 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 435b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 435b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 435c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 435c8 70 .cfa: sp 0 + .ra: x30
STACK CFI 435cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 435dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 435f4 x21: .cfa -16 + ^
STACK CFI 43624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 43634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43638 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43650 8c .cfa: sp 0 + .ra: x30
STACK CFI 43654 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43664 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4367c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4368c x23: .cfa -16 + ^
STACK CFI 436c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 436c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 436d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 436e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 436e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 436f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4370c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4371c x23: .cfa -16 + ^
STACK CFI 43754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 43758 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 43768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 43770 8c .cfa: sp 0 + .ra: x30
STACK CFI 43774 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43784 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4379c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 437ac x23: .cfa -16 + ^
STACK CFI 437e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 437e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 437f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 43800 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43818 78 .cfa: sp 0 + .ra: x30
STACK CFI 4381c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4382c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43844 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4387c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43880 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4388c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 43890 70 .cfa: sp 0 + .ra: x30
STACK CFI 43894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 438a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 438bc x21: .cfa -16 + ^
STACK CFI 438ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 438f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 438fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43900 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43918 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43930 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43948 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43960 70 .cfa: sp 0 + .ra: x30
STACK CFI 43964 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43974 v8: .cfa -16 + ^
STACK CFI 43980 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 439bc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 439c0 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 439cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 439d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 439d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 439e4 v8: .cfa -16 + ^
STACK CFI 439f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43a2c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 43a30 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 43a40 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 43a48 58 .cfa: sp 0 + .ra: x30
STACK CFI 43a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43a64 x19: .cfa -16 + ^
STACK CFI 43a8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43a90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43a9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43aa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43ab8 5c .cfa: sp 0 + .ra: x30
STACK CFI 43abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43ad0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 43b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 43b18 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43b30 60 .cfa: sp 0 + .ra: x30
STACK CFI 43b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43b48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 43b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 43b90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43ba8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43bc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43bd8 70 .cfa: sp 0 + .ra: x30
STACK CFI 43bdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43bec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43c04 x21: .cfa -16 + ^
STACK CFI 43c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43c38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 43c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43c48 70 .cfa: sp 0 + .ra: x30
STACK CFI 43c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43c5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43c74 x21: .cfa -16 + ^
STACK CFI 43ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 43cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43cb8 5c .cfa: sp 0 + .ra: x30
STACK CFI 43cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43cd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 43d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 43d18 54 .cfa: sp 0 + .ra: x30
STACK CFI 43d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43d34 x19: .cfa -16 + ^
STACK CFI 43d5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43d68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43d70 58 .cfa: sp 0 + .ra: x30
STACK CFI 43d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43d8c x19: .cfa -16 + ^
STACK CFI 43db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43dc8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43de0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43df8 58 .cfa: sp 0 + .ra: x30
STACK CFI 43dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43e14 x19: .cfa -16 + ^
STACK CFI 43e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43e40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43e4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43e50 54 .cfa: sp 0 + .ra: x30
STACK CFI 43e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43e6c x19: .cfa -16 + ^
STACK CFI 43e94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43e98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43ea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43ea8 3c .cfa: sp 0 + .ra: x30
STACK CFI 43eac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43ed4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 43edc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43ee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43ee8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f00 3c .cfa: sp 0 + .ra: x30
STACK CFI 43f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 43f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43f40 3c .cfa: sp 0 + .ra: x30
STACK CFI 43f44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 43f74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43f80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43f98 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43fb0 3c .cfa: sp 0 + .ra: x30
STACK CFI 43fb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43fdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 43fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43fe8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43ff0 54 .cfa: sp 0 + .ra: x30
STACK CFI 43ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4400c x19: .cfa -16 + ^
STACK CFI 44034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44038 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 44040 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44048 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44060 7c .cfa: sp 0 + .ra: x30
STACK CFI 44064 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44074 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4408c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 440c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 440c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 440d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 440e0 5c .cfa: sp 0 + .ra: x30
STACK CFI 440e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 440f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4412c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44130 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44140 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44158 60 .cfa: sp 0 + .ra: x30
STACK CFI 4415c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44170 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 441a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 441a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 441b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 441b8 90 .cfa: sp 0 + .ra: x30
STACK CFI 441bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 441cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 441e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 441f4 x23: .cfa -16 + ^
STACK CFI 4422c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 44230 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 44244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 44248 7c .cfa: sp 0 + .ra: x30
STACK CFI 4424c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4425c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44274 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 442ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 442b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 442c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 442c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 442e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 442f8 58 .cfa: sp 0 + .ra: x30
STACK CFI 442fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44314 x19: .cfa -16 + ^
STACK CFI 4433c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44340 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4434c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44350 5c .cfa: sp 0 + .ra: x30
STACK CFI 44354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44368 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4439c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 443a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 443a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 443b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 443c8 70 .cfa: sp 0 + .ra: x30
STACK CFI 443cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 443dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 443f4 x21: .cfa -16 + ^
STACK CFI 44424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 44428 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 44434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 44438 40 .cfa: sp 0 + .ra: x30
STACK CFI 4443c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44464 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4446c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44474 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44478 90 .cfa: sp 0 + .ra: x30
STACK CFI 4447c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4448c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 444a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 444b4 x23: .cfa -16 + ^
STACK CFI 444ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 444f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 44504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 44508 7c .cfa: sp 0 + .ra: x30
STACK CFI 4450c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4451c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44534 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4456c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44570 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44588 7c .cfa: sp 0 + .ra: x30
STACK CFI 4458c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4459c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 445b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 445ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 445f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44608 90 .cfa: sp 0 + .ra: x30
STACK CFI 4460c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4461c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 44634 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 44644 x23: .cfa -16 + ^
STACK CFI 4467c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 44680 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 44694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 44698 58 .cfa: sp 0 + .ra: x30
STACK CFI 4469c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 446b4 x19: .cfa -16 + ^
STACK CFI 446dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 446e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 446ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 446f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44708 5c .cfa: sp 0 + .ra: x30
STACK CFI 4470c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44758 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44768 90 .cfa: sp 0 + .ra: x30
STACK CFI 4476c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4477c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 44794 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 447a4 x23: .cfa -16 + ^
STACK CFI 447dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 447e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 447f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 447f8 90 .cfa: sp 0 + .ra: x30
STACK CFI 447fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4480c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 44824 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 44834 x23: .cfa -16 + ^
STACK CFI 4486c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 44870 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 44884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 44888 5c .cfa: sp 0 + .ra: x30
STACK CFI 4488c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 448a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 448d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 448d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 448e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 448e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44900 74 .cfa: sp 0 + .ra: x30
STACK CFI 44904 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44914 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4492c x21: .cfa -16 + ^
STACK CFI 4495c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 44960 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 44970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 44978 70 .cfa: sp 0 + .ra: x30
STACK CFI 4497c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4498c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 449a4 x21: .cfa -16 + ^
STACK CFI 449d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 449d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 449e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 449e8 5c .cfa: sp 0 + .ra: x30
STACK CFI 449ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44a00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44a48 58 .cfa: sp 0 + .ra: x30
STACK CFI 44a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44a64 x19: .cfa -16 + ^
STACK CFI 44a8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44a90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 44a9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44aa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44ab8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44ad0 7c .cfa: sp 0 + .ra: x30
STACK CFI 44ad4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44ae4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44afc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44b50 b0 .cfa: sp 0 + .ra: x30
STACK CFI 44b54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44b64 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44b7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44b8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44b98 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 44be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 44be8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 44bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 44c00 ac .cfa: sp 0 + .ra: x30
STACK CFI 44c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44c14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44c2c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44c3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44c48 x25: .cfa -16 + ^
STACK CFI 44c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 44c90 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 44ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 44cb0 ac .cfa: sp 0 + .ra: x30
STACK CFI 44cb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44cc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44cdc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44cec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44cf8 x25: .cfa -16 + ^
STACK CFI 44d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 44d40 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 44d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 44d60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44d78 7c .cfa: sp 0 + .ra: x30
STACK CFI 44d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44da4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44de0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44df8 60 .cfa: sp 0 + .ra: x30
STACK CFI 44dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44e10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44e48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44e58 54 .cfa: sp 0 + .ra: x30
STACK CFI 44e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44e74 x19: .cfa -16 + ^
STACK CFI 44e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 44ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 44ea8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44eb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44ec8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44ee0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44ef8 74 .cfa: sp 0 + .ra: x30
STACK CFI 44efc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44f0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44f24 x21: .cfa -16 + ^
STACK CFI 44f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 44f58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 44f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 44f70 78 .cfa: sp 0 + .ra: x30
STACK CFI 44f74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44f84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44f9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44fd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 44fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 44fe8 90 .cfa: sp 0 + .ra: x30
STACK CFI 44fec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 44ffc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45014 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45024 x23: .cfa -16 + ^
STACK CFI 4505c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 45060 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 45074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 45078 98 .cfa: sp 0 + .ra: x30
STACK CFI 4507c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4508c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 450a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 450b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 450f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 450f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4510c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 45110 78 .cfa: sp 0 + .ra: x30
STACK CFI 45114 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4512c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45144 x21: .cfa -16 + ^
STACK CFI 45174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45178 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 45188 5c .cfa: sp 0 + .ra: x30
STACK CFI 4518c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 451a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 451d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 451d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 451e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 451e8 74 .cfa: sp 0 + .ra: x30
STACK CFI 451ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 451fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45214 x21: .cfa -16 + ^
STACK CFI 45244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45248 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 45260 7c .cfa: sp 0 + .ra: x30
STACK CFI 45264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45274 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4528c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 452c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 452c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 452d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 452e0 90 .cfa: sp 0 + .ra: x30
STACK CFI 452e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 452f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4530c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4531c x23: .cfa -16 + ^
STACK CFI 45354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 45358 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4536c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 45370 78 .cfa: sp 0 + .ra: x30
STACK CFI 45374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45384 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4539c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 453d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 453d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 453e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 453e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45400 7c .cfa: sp 0 + .ra: x30
STACK CFI 45404 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45414 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4542c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45468 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 45478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 45480 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45498 7c .cfa: sp 0 + .ra: x30
STACK CFI 4549c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 454ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 454c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 454fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45500 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 45510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 45518 60 .cfa: sp 0 + .ra: x30
STACK CFI 4551c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45530 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45568 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45578 70 .cfa: sp 0 + .ra: x30
STACK CFI 4557c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4558c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 455a4 x21: .cfa -16 + ^
STACK CFI 455d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 455d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 455e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 455e8 5c .cfa: sp 0 + .ra: x30
STACK CFI 455ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45600 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45638 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45648 74 .cfa: sp 0 + .ra: x30
STACK CFI 4564c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4565c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45674 x21: .cfa -16 + ^
STACK CFI 456a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 456a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 456b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 456c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 456c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 456d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4570c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45710 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4571c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45720 60 .cfa: sp 0 + .ra: x30
STACK CFI 45724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45738 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4576c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45770 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4577c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45780 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45798 70 .cfa: sp 0 + .ra: x30
STACK CFI 4579c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 457ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 457c4 x21: .cfa -16 + ^
STACK CFI 457f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 457f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 45808 90 .cfa: sp 0 + .ra: x30
STACK CFI 4580c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4581c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45834 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45844 x23: .cfa -16 + ^
STACK CFI 4587c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 45880 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 45894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 45898 58 .cfa: sp 0 + .ra: x30
STACK CFI 4589c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 458b4 x19: .cfa -16 + ^
STACK CFI 458dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 458e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 458ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 458f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45908 8c .cfa: sp 0 + .ra: x30
STACK CFI 4590c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4591c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45934 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45944 x23: .cfa -16 + ^
STACK CFI 4597c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 45980 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 45990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 45998 70 .cfa: sp 0 + .ra: x30
STACK CFI 4599c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 459ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 459c4 x21: .cfa -16 + ^
STACK CFI 459f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 459f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 45a08 58 .cfa: sp 0 + .ra: x30
STACK CFI 45a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45a24 x19: .cfa -16 + ^
STACK CFI 45a4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45a50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 45a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45a60 58 .cfa: sp 0 + .ra: x30
STACK CFI 45a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45a7c x19: .cfa -16 + ^
STACK CFI 45aa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 45ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45ab8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45ad0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 45ad4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 45ae4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 45afc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 45b0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 45b18 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 45b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 45b68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 45b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 45b88 98 .cfa: sp 0 + .ra: x30
STACK CFI 45b8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45b9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45bb4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45bc4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 45c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45c08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 45c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 45c20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c38 74 .cfa: sp 0 + .ra: x30
STACK CFI 45c3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45c4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45c64 x21: .cfa -16 + ^
STACK CFI 45c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45c98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 45cb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b20 24 .cfa: sp 0 + .ra: x30
STACK CFI 42b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42b00 20 .cfa: sp 0 + .ra: x30
STACK CFI 42b04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45cc8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45cd8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45ce8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45cf0 84 .cfa: sp 0 + .ra: x30
STACK CFI 45cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45cfc x19: .cfa -16 + ^
STACK CFI 45d30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45d78 38 .cfa: sp 0 + .ra: x30
STACK CFI 45d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45d94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45dac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45db0 14 .cfa: sp 0 + .ra: x30
STACK CFI 45db4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45dc8 88 .cfa: sp 0 + .ra: x30
STACK CFI 45dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45dd4 x19: .cfa -16 + ^
STACK CFI 45dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45e50 c4 .cfa: sp 0 + .ra: x30
STACK CFI 45e54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45e64 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 45ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 45ef4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 45f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 45f18 58 .cfa: sp 0 + .ra: x30
STACK CFI 45f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45f24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45f70 28 .cfa: sp 0 + .ra: x30
STACK CFI 45f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45f7c x19: .cfa -16 + ^
STACK CFI 45f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45f98 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45fb8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 45fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45fc4 x19: .cfa -16 + ^
STACK CFI 46058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46060 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46068 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46070 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46090 54 .cfa: sp 0 + .ra: x30
STACK CFI 46094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4609c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 460a4 x21: .cfa -16 + ^
STACK CFI 460e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 460e8 3c .cfa: sp 0 + .ra: x30
STACK CFI 460ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 460fc x19: .cfa -16 + ^
STACK CFI 46120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46128 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46130 84 .cfa: sp 0 + .ra: x30
