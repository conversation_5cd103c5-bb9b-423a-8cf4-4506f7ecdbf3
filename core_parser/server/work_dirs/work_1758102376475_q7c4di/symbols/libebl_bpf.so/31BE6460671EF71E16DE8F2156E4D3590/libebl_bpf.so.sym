MODULE Linux arm64 31BE6460671EF71E16DE8F2156E4D3590 libebl_bpf.so
INFO CODE_ID 6064BE311E671EF716DE8F2156E4D359CA548BD4
PUBLIC 9a0 0 bpf_init
STACK CFI INIT 7d8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 808 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 848 48 .cfa: sp 0 + .ra: x30
STACK CFI 84c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 854 x19: .cfa -16 + ^
STACK CFI 88c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 890 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 898 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 900 a0 .cfa: sp 0 + .ra: x30
STACK CFI 904 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 90c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 978 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9a0 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT a18 98 .cfa: sp 0 + .ra: x30
STACK CFI a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3c x19: .cfa -16 + ^
STACK CFI a94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad0 79c .cfa: sp 0 + .ra: x30
STACK CFI ad4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI adc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI af0 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI b04 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI b30 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI c08 x21: x21 x22: x22
STACK CFI c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c3c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI c40 x21: x21 x22: x22
STACK CFI c44 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 125c x21: x21 x22: x22
STACK CFI 1268 x21: .cfa -224 + ^ x22: .cfa -216 + ^
