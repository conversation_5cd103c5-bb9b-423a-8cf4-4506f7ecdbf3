MODULE Linux arm64 EB62DA9E9D120F18DF4387081495897A0 libigwo_api.so
INFO CODE_ID 9EDA62EB129D180FDF4387081495897A
FILE 0 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/data_buffer/data_buffer.h
FILE 1 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/igwo/sensor_data.h
FILE 2 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/igwo_api_impl.cpp
FILE 3 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/aarch64-buildroot-linux-gnu/bits/gthr-default.h
FILE 4 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/std_mutex.h
FILE 5 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/unique_lock.h
FILE 6 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/iostream
FILE 7 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/CommaInitializer.h
FUNC 1730 3c 0 _GLOBAL__sub_I_igwo_api_impl.cpp
1730 c 190 2
173c 18 74 6
1754 4 190 2
1758 8 74 6
1760 4 190 2
1764 8 74 6
FUNC 1840 44 0 std::unique_lock<std::mutex>::unlock()
1840 8 191 5
1848 4 193 5
184c 4 191 5
1850 4 191 5
1854 4 193 5
1858 4 195 5
185c c 778 3
1868 4 779 3
186c 4 198 5
1870 c 200 5
187c 8 194 5
FUNC 1890 98 0 odometry_create_handle
1890 14 9 2
18a4 4 9 2
18a8 4 10 2
18ac 4 10 2
18b0 8 10 2
18b8 4 11 2
18bc 4 12 2
18c0 4 12 2
18c4 8 12 2
18cc 8 12 2
18d4 4 14 2
18d8 4 12 2
18dc 4 12 2
18e0 4 14 2
18e4 8 12 2
18ec 10 12 2
18fc 4 20 0
1900 4 20 0
1904 c 14 2
1910 8 10 2
1918 10 10 2
FUNC 1930 8 0 odometry_get_state
1930 4 20 2
1934 4 20 2
FUNC 1940 8 0 odometry_get_trigger_state
1940 4 26 2
1944 4 26 2
FUNC 1950 18 0 odometry_process
1950 8 28 2
1958 4 30 2
195c c 33 2
FUNC 1970 dc 0 odometry_add_imu_data
1970 4 35 2
1974 4 35 2
1978 4 69 5
197c 8 35 2
1984 4 40 2
1988 4 748 3
198c 8 37 2
1994 8 40 2
199c 4 38 2
19a0 4 748 3
19a4 4 38 2
19a8 8 40 2
19b0 4 39 2
19b4 8 69 5
19bc 4 41 2
19c0 4 69 5
19c4 4 41 2
19c8 8 41 2
19d0 4 39 2
19d4 4 748 3
19d8 4 749 3
19dc 4 103 4
19e0 8 142 5
19e8 4 44 2
19ec 8 44 2
19f4 8 105 5
19fc 8 45 2
1a04 4 45 2
1a08 4 106 5
1a0c 4 195 5
1a10 8 778 3
1a18 4 779 3
1a1c 8 45 2
1a24 4 45 2
1a28 4 104 4
1a2c 8 105 5
1a34 4 105 5
1a38 c 106 5
1a44 8 106 5
FUNC 1a50 134 0 odometry_add_ins_data
1a50 4 47 2
1a54 4 47 2
1a58 4 69 5
1a5c 8 47 2
1a64 4 51 2
1a68 4 50 2
1a6c 8 49 2
1a74 4 51 2
1a78 4 50 2
1a7c 4 52 2
1a80 4 51 2
1a84 4 57 2
1a88 4 52 2
1a8c 4 748 3
1a90 4 58 2
1a94 8 51 2
1a9c 8 53 2
1aa4 8 54 2
1aac 8 55 2
1ab4 4 57 2
1ab8 8 52 2
1ac0 8 53 2
1ac8 8 56 2
1ad0 4 54 2
1ad4 4 55 2
1ad8 8 56 2
1ae0 4 59 2
1ae4 4 69 5
1ae8 4 748 3
1aec 4 54 2
1af0 8 69 5
1af8 4 63 2
1afc 4 55 2
1b00 4 63 2
1b04 4 58 2
1b08 4 59 2
1b0c 4 748 3
1b10 4 749 3
1b14 4 103 4
1b18 8 142 5
1b20 4 66 2
1b24 8 66 2
1b2c 8 105 5
1b34 8 67 2
1b3c 4 67 2
1b40 4 106 5
1b44 4 195 5
1b48 8 778 3
1b50 4 779 3
1b54 8 67 2
1b5c 4 67 2
1b60 4 104 4
1b64 8 105 5
1b6c 4 105 5
1b70 c 106 5
1b7c 8 106 5
FUNC 1b90 170 0 odometry_add_odo_data
1b90 4 69 2
1b94 4 71 2
1b98 4 69 2
1b9c 4 69 5
1ba0 8 69 2
1ba8 4 71 2
1bac 4 69 2
1bb0 4 74 2
1bb4 4 77 2
1bb8 4 748 3
1bbc 4 73 2
1bc0 4 84 2
1bc4 4 79 2
1bc8 4 76 2
1bcc 4 81 2
1bd0 4 71 2
1bd4 8 75 2
1bdc 4 83 2
1be0 4 77 2
1be4 4 74 2
1be8 4 78 2
1bec 4 72 2
1bf0 4 74 2
1bf4 4 72 2
1bf8 4 85 2
1bfc 4 74 2
1c00 4 85 2
1c04 4 79 2
1c08 4 80 2
1c0c 4 81 2
1c10 4 75 2
1c14 4 82 2
1c18 4 87 2
1c1c 4 75 2
1c20 8 85 2
1c28 4 86 2
1c2c 4 84 2
1c30 4 87 2
1c34 4 87 2
1c38 4 91 2
1c3c 4 748 3
1c40 4 86 2
1c44 4 93 2
1c48 4 69 5
1c4c 4 74 2
1c50 4 93 2
1c54 4 92 2
1c58 4 86 2
1c5c 4 69 5
1c60 4 94 2
1c64 4 69 5
1c68 4 86 2
1c6c 4 92 2
1c70 4 94 2
1c74 4 87 2
1c78 4 83 2
1c7c 4 91 2
1c80 4 748 3
1c84 4 749 3
1c88 4 103 4
1c8c 8 142 5
1c94 4 97 2
1c98 8 97 2
1ca0 8 105 5
1ca8 c 98 2
1cb4 4 98 2
1cb8 4 106 5
1cbc 4 195 5
1cc0 8 778 3
1cc8 4 779 3
1ccc c 98 2
1cd8 4 98 2
1cdc 4 104 4
1ce0 8 105 5
1ce8 4 105 5
1cec c 106 5
1cf8 8 106 5
FUNC 1d00 d4 0 odometry_add_gps_data
1d00 8 100 2
1d08 4 69 5
1d0c 4 100 2
1d10 4 102 2
1d14 4 69 5
1d18 8 102 2
1d20 4 102 2
1d24 4 100 2
1d28 4 748 3
1d2c 4 102 2
1d30 4 102 2
1d34 4 102 2
1d38 4 748 3
1d3c 4 38 7
1d40 8 69 5
1d48 4 78 7
1d4c 4 123 1
1d50 4 126 1
1d54 8 78 7
1d5c 4 748 3
1d60 4 749 3
1d64 4 103 4
1d68 8 142 5
1d70 4 104 2
1d74 8 104 2
1d7c 8 105 5
1d84 8 105 2
1d8c 4 105 2
1d90 4 106 5
1d94 4 195 5
1d98 8 778 3
1da0 4 779 3
1da4 8 105 2
1dac 4 105 2
1db0 4 104 4
1db4 8 105 5
1dbc 4 105 5
1dc0 c 106 5
1dcc 8 106 5
FUNC 1de0 180 0 odometry_add_vehicle_data
1de0 8 107 2
1de8 4 69 5
1dec 4 107 2
1df0 4 118 2
1df4 4 113 2
1df8 4 111 2
1dfc 4 117 2
1e00 4 122 2
1e04 4 127 2
1e08 4 120 2
1e0c 4 112 2
1e10 4 110 2
1e14 4 116 2
1e18 4 114 2
1e1c 4 121 2
1e20 4 125 2
1e24 4 124 2
1e28 4 109 2
1e2c 4 107 2
1e30 4 748 3
1e34 4 109 2
1e38 4 126 2
1e3c 4 113 2
1e40 4 112 2
1e44 4 111 2
1e48 4 110 2
1e4c 4 117 2
1e50 4 116 2
1e54 4 118 2
1e58 4 114 2
1e5c 4 121 2
1e60 4 120 2
1e64 4 122 2
1e68 4 124 2
1e6c 4 126 2
1e70 4 748 3
1e74 4 127 2
1e78 4 132 2
1e7c 4 137 2
1e80 4 142 2
1e84 4 129 2
1e88 4 131 2
1e8c 4 134 2
1e90 4 136 2
1e94 4 139 2
1e98 4 130 2
1e9c 4 135 2
1ea0 4 69 5
1ea4 4 125 2
1ea8 4 140 2
1eac 4 141 2
1eb0 8 69 5
1eb8 4 129 2
1ebc 4 130 2
1ec0 4 131 2
1ec4 4 134 2
1ec8 4 135 2
1ecc 4 136 2
1ed0 4 139 2
1ed4 4 140 2
1ed8 4 141 2
1edc 4 132 2
1ee0 4 137 2
1ee4 4 142 2
1ee8 4 748 3
1eec 4 749 3
1ef0 4 103 4
1ef4 8 142 5
1efc 4 145 2
1f00 8 145 2
1f08 8 105 5
1f10 8 146 2
1f18 4 146 2
1f1c 4 106 5
1f20 4 195 5
1f24 8 778 3
1f2c 4 779 3
1f30 8 146 2
1f38 4 146 2
1f3c 4 104 4
1f40 8 105 5
1f48 4 105 5
1f4c c 106 5
1f58 8 106 5
FUNC 1f60 30 0 odometry_destroy_handle
1f60 4 150 2
1f64 10 148 2
1f74 4 150 2
1f78 8 150 2
1f80 4 151 2
1f84 4 151 2
1f88 4 150 2
1f8c 4 150 2
FUNC 1f90 c4 0 odometry_output3d
1f90 10 153 2
1fa0 4 153 2
1fa4 8 156 2
1fac 4 156 2
1fb0 4 158 2
1fb4 4 162 2
1fb8 8 157 2
1fc0 4 158 2
1fc4 8 163 2
1fcc 4 160 2
1fd0 8 165 2
1fd8 4 161 2
1fdc 8 164 2
1fe4 4 159 2
1fe8 8 166 2
1ff0 8 167 2
1ff8 8 163 2
2000 8 164 2
2008 8 167 2
2010 8 165 2
2018 8 166 2
2020 4 160 2
2024 4 168 2
2028 4 159 2
202c 8 168 2
2034 4 161 2
2038 4 162 2
203c 4 168 2
2040 c 170 2
204c 8 170 2
FUNC 2060 c8 0 odometry_output2d
2060 10 172 2
2070 4 175 2
2074 4 172 2
2078 4 175 2
207c 4 175 2
2080 4 177 2
2084 4 178 2
2088 4 187 2
208c 4 178 2
2090 4 187 2
2094 4 180 2
2098 8 183 2
20a0 4 179 2
20a4 8 185 2
20ac 4 181 2
20b0 8 184 2
20b8 4 182 2
20bc 8 186 2
20c4 4 176 2
20c8 4 180 2
20cc 8 183 2
20d4 8 185 2
20dc 8 187 2
20e4 8 184 2
20ec 8 186 2
20f4 4 188 2
20f8 4 179 2
20fc 8 188 2
2104 4 177 2
2108 4 181 2
210c 4 182 2
2110 4 188 2
2114 c 190 2
2120 8 190 2
PUBLIC 1588 0 _init
PUBLIC 176c 0 call_weak_fn
PUBLIC 1780 0 deregister_tm_clones
PUBLIC 17b0 0 register_tm_clones
PUBLIC 17ec 0 __do_global_dtors_aux
PUBLIC 183c 0 frame_dummy
PUBLIC 2128 0 _fini
STACK CFI INIT 1780 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17ec 50 .cfa: sp 0 + .ra: x30
STACK CFI 17fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1804 x19: .cfa -16 + ^
STACK CFI 1834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 183c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1840 44 .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1850 x19: .cfa -16 + ^
STACK CFI 1878 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 187c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1890 98 .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 189c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18a4 x21: .cfa -16 + ^
STACK CFI 190c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1910 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1950 18 .cfa: sp 0 + .ra: x30
STACK CFI 1954 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1970 dc .cfa: sp 0 + .ra: x30
STACK CFI 1974 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1984 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a08 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 1a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a28 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a50 134 .cfa: sp 0 + .ra: x30
STACK CFI 1a54 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1a64 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 1b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b40 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x29: .cfa -240 + ^
STACK CFI 1b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b60 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x29: .cfa -240 + ^
STACK CFI INIT 1b90 170 .cfa: sp 0 + .ra: x30
STACK CFI 1b94 .cfa: sp 880 +
STACK CFI 1ba4 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 1bb0 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 1cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cb8 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x29: .cfa -880 + ^
STACK CFI 1cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cdc .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x29: .cfa -880 + ^
STACK CFI INIT 1d00 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1d28 x19: .cfa -96 + ^
STACK CFI 1d8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d90 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI 1dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1db0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1de0 180 .cfa: sp 0 + .ra: x30
STACK CFI 1de8 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1e30 x19: .cfa -224 + ^
STACK CFI 1f18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f1c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x29: .cfa -240 + ^
STACK CFI 1f38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f3c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x29: .cfa -240 + ^
STACK CFI INIT 1f60 30 .cfa: sp 0 + .ra: x30
STACK CFI 1f68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f70 x19: .cfa -16 + ^
STACK CFI 1f88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f90 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1f94 .cfa: sp 864 +
STACK CFI 1f98 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 1fa0 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2060 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2064 .cfa: sp 864 +
STACK CFI 2068 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2070 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 2124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1730 3c .cfa: sp 0 + .ra: x30
STACK CFI 1734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 173c x19: .cfa -16 + ^
STACK CFI 1764 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
