MODULE Linux arm64 98D257E3F5449A40E43DCCC130E20C3F0 libxkbcommon-x11.so.0
INFO CODE_ID E357D29844F5409AE43DCCC130E20C3FED7412B2
PUBLIC 2d18 0 xkb_x11_keymap_new_from_device
PUBLIC 4570 0 xkb_x11_state_new_from_device
PUBLIC 4668 0 xkb_x11_setup_xkb_extension
PUBLIC 47c0 0 xkb_x11_get_core_keyboard_device_id
STACK CFI INIT 2958 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2988 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 29cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29d4 x19: .cfa -16 + ^
STACK CFI 2a0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a18 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a30 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 2a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ba8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2d18 1854 .cfa: sp 0 + .ra: x30
STACK CFI 2d1c .cfa: sp 384 +
STACK CFI 2d20 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 2d28 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 2d38 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 2e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e34 .cfa: sp 384 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x29: .cfa -288 + ^
STACK CFI 2e38 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 2eb4 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2fe4 x25: x25 x26: x26
STACK CFI 2fe8 x27: x27 x28: x28
STACK CFI 3014 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 3274 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 329c x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 32b4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 32d8 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 32f4 x27: x27 x28: x28
STACK CFI 330c x25: x25 x26: x26
STACK CFI 3310 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 36d4 x27: x27 x28: x28
STACK CFI 3704 x25: x25 x26: x26
STACK CFI 3708 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 37a8 x25: x25 x26: x26
STACK CFI 37ac x27: x27 x28: x28
STACK CFI 37b0 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 3c38 x25: x25 x26: x26
STACK CFI 3c3c x27: x27 x28: x28
STACK CFI 3c40 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 3cbc x25: x25 x26: x26
STACK CFI 3cc0 x27: x27 x28: x28
STACK CFI 3cc4 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 3ed4 x25: x25 x26: x26
STACK CFI 3ed8 x27: x27 x28: x28
STACK CFI 3edc x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4284 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4288 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 428c x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4454 x25: x25 x26: x26
STACK CFI 4458 x27: x27 x28: x28
STACK CFI 445c x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 4494 x25: x25 x26: x26
STACK CFI 4498 x27: x27 x28: x28
STACK CFI 449c x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 4570 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4574 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 458c x21: .cfa -16 + ^
STACK CFI 45f4 x21: x21
STACK CFI 45f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4614 x21: x21
STACK CFI 4618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 461c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4660 x21: x21
STACK CFI 4664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4668 158 .cfa: sp 0 + .ra: x30
STACK CFI 466c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4674 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4680 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 46b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 46bc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 46c0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 46dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 46fc x21: x21 x22: x22
STACK CFI 4700 x25: x25 x26: x26
STACK CFI 4704 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4740 x27: .cfa -32 + ^
STACK CFI 4774 x21: x21 x22: x22
STACK CFI 4778 x25: x25 x26: x26
STACK CFI 477c x27: x27
STACK CFI 4780 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 478c x21: x21 x22: x22
STACK CFI 4790 x25: x25 x26: x26
STACK CFI 4794 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 4798 x21: x21 x22: x22
STACK CFI 479c x25: x25 x26: x26
STACK CFI 47a0 x27: x27
STACK CFI 47a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 47b0 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 47b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 47b8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 47bc x27: .cfa -32 + ^
STACK CFI INIT 47c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47e8 x19: .cfa -16 + ^
STACK CFI 4818 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 481c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4828 98 .cfa: sp 0 + .ra: x30
STACK CFI 482c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4834 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 483c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 485c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 48bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 48c0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 48c4 .cfa: sp 656 +
STACK CFI 48c8 .ra: .cfa -648 + ^ x29: .cfa -656 + ^
STACK CFI 48d0 x23: .cfa -608 + ^ x24: .cfa -600 + ^
STACK CFI 48dc x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 4908 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 4914 x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 491c x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI 4a3c x19: x19 x20: x20
STACK CFI 4a44 x21: x21 x22: x22
STACK CFI 4a48 x27: x27 x28: x28
STACK CFI 4a70 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4a74 .cfa: sp 656 + .ra: .cfa -648 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^ x27: .cfa -576 + ^ x28: .cfa -568 + ^ x29: .cfa -656 + ^
STACK CFI 4a78 x19: x19 x20: x20
STACK CFI 4a7c x21: x21 x22: x22
STACK CFI 4a80 x27: x27 x28: x28
STACK CFI 4a8c x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 4a90 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI 4a94 x27: .cfa -576 + ^ x28: .cfa -568 + ^
STACK CFI INIT 4a98 20 .cfa: sp 0 + .ra: x30
STACK CFI 4a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ab4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ab8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ac0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4aec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b30 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4b34 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4b3c x19: .cfa -256 + ^
STACK CFI 4bac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bb0 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x29: .cfa -272 + ^
STACK CFI INIT 4bf8 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c48 13c .cfa: sp 0 + .ra: x30
STACK CFI 4c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ca0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4ca4 x21: .cfa -16 + ^
STACK CFI 4cec x21: x21
STACK CFI 4d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4d5c x21: .cfa -16 + ^
STACK CFI 4d6c x21: x21
STACK CFI INIT 4d88 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4d8c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4d94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4da0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4dac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4dd8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4de4 x27: .cfa -16 + ^
STACK CFI 4e38 x21: x21 x22: x22
STACK CFI 4e40 x27: x27
STACK CFI 4e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 4e60 a0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f00 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f48 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f88 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fc8 5c .cfa: sp 0 + .ra: x30
STACK CFI 4fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5004 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5028 50 .cfa: sp 0 + .ra: x30
STACK CFI 502c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 503c x19: .cfa -16 + ^
STACK CFI 5074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5078 74 .cfa: sp 0 + .ra: x30
STACK CFI 5080 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5088 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5090 x21: .cfa -16 + ^
STACK CFI 50dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 50f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5114 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5138 238 .cfa: sp 0 + .ra: x30
STACK CFI 513c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5150 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5158 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5164 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 51c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5208 x27: x27 x28: x28
STACK CFI 5220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5224 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5238 x27: x27 x28: x28
STACK CFI 52a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 52ac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5344 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5348 x27: x27 x28: x28
STACK CFI 536c x27: .cfa -16 + ^ x28: .cfa -8 + ^
