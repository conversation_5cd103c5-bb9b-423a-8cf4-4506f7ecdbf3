MODULE Linux arm64 DE3899C9105FB1C37310285BB33056450 libnvstream_core_nvipc.so
INFO CODE_ID C99938DE5F10C3B17310285BB3305645
PUBLIC 1288 0 _init
PUBLIC 13f0 0 call_weak_fn
PUBLIC 1404 0 deregister_tm_clones
PUBLIC 1434 0 register_tm_clones
PUBLIC 1470 0 __do_global_dtors_aux
PUBLIC 14c0 0 frame_dummy
PUBLIC 14d0 0 std::call_once<linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)::{lambda()#1}>(std::once_flag&, linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)::{lambda()#1}&&)::{lambda()#2}::_FUN()
PUBLIC 1510 0 linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 15f0 0 linvs::nvipc::Endpoint::~Endpoint()
PUBLIC 1620 0 linvs::nvipc::Endpoint::operator bool() const
PUBLIC 1630 0 linvs::nvipc::Endpoint::operator*() const
PUBLIC 1640 0 linvs::nvipc::Endpoint::GetEndpointInfo(linvs::nvipc::EndpointInfo&) const
PUBLIC 16b0 0 linvs::nvipc::Endpoint::GetEvent(unsigned int&) const
PUBLIC 1700 0 linvs::nvipc::Endpoint::GetLinuxFd() const
PUBLIC 1760 0 linvs::nvipc::Endpoint::Read(void*, unsigned long) const
PUBLIC 17c0 0 linvs::nvipc::Endpoint::Write(void const*, unsigned long) const
PUBLIC 1820 0 linvs::nvipc::EndpointInfo::operator*()
PUBLIC 1824 0 _fini
STACK CFI INIT 1404 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1434 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1470 50 .cfa: sp 0 + .ra: x30
STACK CFI 1480 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1488 x19: .cfa -16 + ^
STACK CFI 14b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 14d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1510 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 15f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1600 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1620 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1640 68 .cfa: sp 0 + .ra: x30
STACK CFI 1644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1654 x19: .cfa -16 + ^
STACK CFI 1678 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 167c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 16b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1700 60 .cfa: sp 0 + .ra: x30
STACK CFI 1704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1714 x19: .cfa -32 + ^
STACK CFI 1730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1734 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1760 54 .cfa: sp 0 + .ra: x30
STACK CFI 1764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 17c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 17c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1820 4 .cfa: sp 0 + .ra: x30
