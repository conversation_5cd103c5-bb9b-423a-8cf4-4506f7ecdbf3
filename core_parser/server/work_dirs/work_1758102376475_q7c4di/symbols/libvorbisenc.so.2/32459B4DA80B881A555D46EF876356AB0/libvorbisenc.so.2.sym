MODULE Linux arm64 32459B4DA80B881A555D46EF876356AB0 libvorbisenc.so.2
INFO CODE_ID 4D9B45320BA81A88555D46EF876356AB1A0396CB
PUBLIC 14130 0 vorbis_encode_setup_init
PUBLIC 15348 0 vorbis_encode_setup_vbr
PUBLIC 15408 0 vorbis_encode_init_vbr
PUBLIC 15478 0 vorbis_encode_setup_managed
PUBLIC 15598 0 vorbis_encode_init
PUBLIC 15608 0 vorbis_encode_ctl
STACK CFI INIT 13a98 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13ac8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13b08 48 .cfa: sp 0 + .ra: x30
STACK CFI 13b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13b14 x19: .cfa -16 + ^
STACK CFI 13b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 13b50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13b58 128 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13c80 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13d60 130 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e90 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13f40 11c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14060 d0 .cfa: sp 0 + .ra: x30
STACK CFI 14064 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1406c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14078 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14094 v8: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14114 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14118 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14130 1218 .cfa: sp 0 + .ra: x30
STACK CFI 14134 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1413c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 14168 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 14178 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 141c8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 141d4 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 141e0 v8: .cfa -144 + ^ v9: .cfa -136 + ^
STACK CFI 141e8 v10: .cfa -128 + ^ v11: .cfa -120 + ^
STACK CFI 141ec v12: .cfa -112 + ^ v13: .cfa -104 + ^
STACK CFI 14cac x21: x21 x22: x22
STACK CFI 14cb0 x23: x23 x24: x24
STACK CFI 14cb4 x25: x25 x26: x26
STACK CFI 14cb8 x27: x27 x28: x28
STACK CFI 14cbc v8: v8 v9: v9
STACK CFI 14cc0 v10: v10 v11: v11
STACK CFI 14cc4 v12: v12 v13: v13
STACK CFI 14ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14cd0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 14ce4 v10: .cfa -128 + ^ v11: .cfa -120 + ^ v12: .cfa -112 + ^ v13: .cfa -104 + ^ v8: .cfa -144 + ^ v9: .cfa -136 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 1502c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 15048 v10: .cfa -128 + ^ v11: .cfa -120 + ^ v12: .cfa -112 + ^ v13: .cfa -104 + ^ v8: .cfa -144 + ^ v9: .cfa -136 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 15338 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 1533c x21: x21 x22: x22
STACK CFI 15340 x25: x25 x26: x26
STACK CFI INIT 15348 bc .cfa: sp 0 + .ra: x30
STACK CFI 15354 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 153f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 153f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 153fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15408 6c .cfa: sp 0 + .ra: x30
STACK CFI 1540c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15414 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1543c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15440 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15458 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15478 11c .cfa: sp 0 + .ra: x30
STACK CFI 154e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15558 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1558c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 15598 6c .cfa: sp 0 + .ra: x30
STACK CFI 1559c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 155a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 155cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 155d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 155e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 155e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15608 48c .cfa: sp 0 + .ra: x30
STACK CFI 1560c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15614 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 156a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 156a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
