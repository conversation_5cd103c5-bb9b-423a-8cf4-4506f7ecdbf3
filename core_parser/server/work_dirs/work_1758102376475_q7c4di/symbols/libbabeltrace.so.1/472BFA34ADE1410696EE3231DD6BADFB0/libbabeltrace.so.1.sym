MODULE Linux arm64 472BFA34ADE1410696EE3231DD6BADFB0 libbabeltrace.so.1
INFO CODE_ID 34FA2B47E1AD064196EE3231DD6BADFBFF7599BB
PUBLIC 3158 0 bt_iter_free_pos
PUBLIC 31c0 0 bt_iter_set_pos
PUBLIC 3908 0 bt_iter_get_pos
PUBLIC 3b10 0 bt_iter_create_time_pos
PUBLIC 3b40 0 bt_iter_add_trace
PUBLIC 3c68 0 bt_iter_init
PUBLIC 3dd0 0 bt_iter_create
PUBLIC 3e50 0 bt_iter_fini
PUBLIC 3eb8 0 bt_iter_destroy
PUBLIC 3f08 0 bt_iter_next
PUBLIC 40e0 0 bt_context_create
PUBLIC 4150 0 bt_context_add_trace
PUBLIC 4480 0 bt_context_remove_trace
PUBLIC 44b8 0 bt_context_get
PUBLIC 44f8 0 bt_context_put
PUBLIC 45a0 0 bt_trace_handle_create
PUBLIC 45e0 0 bt_trace_handle_destroy
PUBLIC 45e8 0 bt_trace_handle_get_path
PUBLIC 4628 0 bt_trace_handle_get_timestamp_begin
PUBLIC 4690 0 bt_trace_handle_get_timestamp_end
PUBLIC 4aa8 0 bt_trace_collection_add
PUBLIC 4bb8 0 bt_trace_collection_remove
PUBLIC 4bf0 0 bt_init_trace_collection
PUBLIC 4c68 0 bt_finalize_trace_collection
PUBLIC 4d30 0 bt_lookup_format
PUBLIC 4d58 0 bt_fprintf_format_list
PUBLIC 4e48 0 bt_unregister_format
PUBLIC 4ed0 0 bt_register_format
PUBLIC 4f48 0 bt_packet_seek_get_error
PUBLIC 4f78 0 bt_packet_seek_set_error
PUBLIC 5108 0 bt_heap_init
PUBLIC 5120 0 bt_heap_free
PUBLIC 5128 0 bt_heap_replace_max
PUBLIC 5198 0 bt_heap_insert
PUBLIC 5230 0 bt_heap_remove
PUBLIC 52b0 0 bt_heap_cherrypick
PUBLIC 53a0 0 bt_heap_copy
PUBLIC 5708 0 bt_array_rw
PUBLIC 57b8 0 bt_array_declaration_new
PUBLIC 5850 0 bt_array_len
PUBLIC 5870 0 bt_array_index
PUBLIC 5898 0 bt_get_array_len
PUBLIC 58a8 0 bt_get_char_array
PUBLIC 5b10 0 bt_enum_uint_to_quark_set
PUBLIC 5c60 0 bt_enum_int_to_quark_set
PUBLIC 5db0 0 bt_enum_quark_to_range_set
PUBLIC 5dc0 0 bt_enum_signed_insert
PUBLIC 5f28 0 bt_enum_unsigned_insert
PUBLIC 6090 0 bt_enum_get_nr_enumerators
PUBLIC 60b0 0 bt_enum_declaration_new
PUBLIC 6448 0 bt_float_declaration_new
PUBLIC 6638 0 bt_integer_declaration_new
PUBLIC 66c8 0 bt_get_int_encoding
PUBLIC 66d8 0 bt_get_int_base
PUBLIC 66e8 0 bt_get_int_len
PUBLIC 66f8 0 bt_get_int_byte_order
PUBLIC 6708 0 bt_get_int_signedness
PUBLIC 6718 0 bt_get_unsigned_int
PUBLIC 6770 0 bt_get_signed_int
PUBLIC 67c8 0 bt_int_is_char
PUBLIC 6a80 0 bt_sequence_rw
PUBLIC 6be8 0 bt_sequence_declaration_new
PUBLIC 6c98 0 bt_sequence_len
PUBLIC 6ca8 0 bt_sequence_index
PUBLIC 6e18 0 bt_string_declaration_new
PUBLIC 6e78 0 bt_get_string_encoding
PUBLIC 6e88 0 bt_get_string
PUBLIC 7190 0 bt_struct_rw
PUBLIC 7240 0 bt_struct_declaration_new
PUBLIC 7300 0 bt_struct_declaration_add_field
PUBLIC 73a0 0 bt_struct_declaration_lookup_field_index
PUBLIC 7408 0 bt_struct_declaration_get_field_from_index
PUBLIC 7428 0 bt_struct_definition_get_field_from_index
PUBLIC 7448 0 bt_struct_declaration_len
PUBLIC 7770 0 bt_untagged_bt_variant_declaration_new
PUBLIC 7808 0 bt_variant_declaration_new
PUBLIC 78a0 0 bt_untagged_variant_declaration_add_field
PUBLIC 7928 0 bt_untagged_variant_declaration_get_field_from_tag
PUBLIC 79a0 0 bt_variant_get_current_field
PUBLIC 7ac8 0 bt_variant_rw
PUBLIC 7c20 0 bt_declaration_unref
PUBLIC 7e88 0 bt_lookup_declaration
PUBLIC 7ee0 0 bt_lookup_path_definition
PUBLIC 80e8 0 bt_register_field_definition
PUBLIC 8158 0 bt_declaration_ref
PUBLIC 8168 0 bt_register_declaration
PUBLIC 81d8 0 bt_definition_ref
PUBLIC 81e8 0 bt_definition_unref
PUBLIC 8210 0 bt_new_declaration_scope
PUBLIC 82d8 0 bt_free_declaration_scope
PUBLIC 8318 0 bt_lookup_struct_declaration
PUBLIC 8370 0 bt_register_struct_declaration
PUBLIC 8420 0 bt_lookup_variant_declaration
PUBLIC 8478 0 bt_register_variant_declaration
PUBLIC 8528 0 bt_lookup_enum_declaration
PUBLIC 8580 0 bt_register_enum_declaration
PUBLIC 8630 0 bt_new_definition_path
PUBLIC 87a8 0 bt_append_scope_path
PUBLIC 88c8 0 bt_new_definition_scope
PUBLIC 8a58 0 bt_free_definition_scope
PUBLIC 8a90 0 bt_lookup_definition
PUBLIC 8ad0 0 bt_lookup_integer
PUBLIC 8b28 0 bt_lookup_enum
PUBLIC 8b80 0 bt_lookup_variant
STACK CFI INIT 2ec0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f30 48 .cfa: sp 0 + .ra: x30
STACK CFI 2f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f3c x19: .cfa -16 + ^
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2dd8 50 .cfa: sp 0 + .ra: x30
STACK CFI 2ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f80 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb8 78 .cfa: sp 0 + .ra: x30
STACK CFI 2fc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fd4 x19: .cfa -16 + ^
STACK CFI 2ffc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3000 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 302c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3030 94 .cfa: sp 0 + .ra: x30
STACK CFI 3034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3040 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30c8 90 .cfa: sp 0 + .ra: x30
STACK CFI 30d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30e0 x19: .cfa -16 + ^
STACK CFI 311c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3120 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 312c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3138 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3158 64 .cfa: sp 0 + .ra: x30
STACK CFI 3160 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 316c x19: .cfa -16 + ^
STACK CFI 3184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3188 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 31b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 31c0 744 .cfa: sp 0 + .ra: x30
STACK CFI 31d0 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 31d8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 31e4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 323c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3258 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3268 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3328 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3354 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3358 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 335c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3450 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3468 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 352c x23: x23 x24: x24
STACK CFI 3530 x25: x25 x26: x26
STACK CFI 3534 x27: x27 x28: x28
STACK CFI 3548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 354c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 3598 x23: x23 x24: x24
STACK CFI 35a0 x25: x25 x26: x26
STACK CFI 35a8 x27: x27 x28: x28
STACK CFI 35fc x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3604 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3640 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3648 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 364c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 36d8 x23: x23 x24: x24
STACK CFI 36dc x25: x25 x26: x26
STACK CFI 36e0 x27: x27 x28: x28
STACK CFI 36f0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3724 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3728 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3878 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 387c x27: x27 x28: x28
STACK CFI 3894 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 38a0 x23: x23 x24: x24
STACK CFI 38a4 x25: x25 x26: x26
STACK CFI 38a8 x27: x27 x28: x28
STACK CFI 38ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38b8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 38cc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 38d4 x23: x23 x24: x24
STACK CFI 38d8 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 3908 208 .cfa: sp 0 + .ra: x30
STACK CFI 390c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3914 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 391c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3940 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 39bc x25: .cfa -96 + ^
STACK CFI 3a5c x25: x25
STACK CFI 3a68 x21: x21 x22: x22
STACK CFI 3a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 3a90 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 3ab0 x21: x21 x22: x22
STACK CFI 3abc x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 3b04 x21: x21 x22: x22 x25: x25
STACK CFI 3b08 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3b0c x25: .cfa -96 + ^
STACK CFI INIT 3b10 2c .cfa: sp 0 + .ra: x30
STACK CFI 3b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b20 x19: .cfa -16 + ^
STACK CFI 3b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b40 124 .cfa: sp 0 + .ra: x30
STACK CFI 3b44 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3b4c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3b58 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3b64 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3b88 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3b90 x27: .cfa -48 + ^
STACK CFI 3c1c x19: x19 x20: x20
STACK CFI 3c20 x27: x27
STACK CFI 3c28 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x27: .cfa -48 + ^
STACK CFI 3c2c x19: x19 x20: x20
STACK CFI 3c30 x27: x27
STACK CFI 3c54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3c58 .cfa: sp 128 + .ra: .cfa -120 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 3c5c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3c60 x27: .cfa -48 + ^
STACK CFI INIT 3c68 168 .cfa: sp 0 + .ra: x30
STACK CFI 3c6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3c84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3cb0 x23: .cfa -16 + ^
STACK CFI 3d54 x21: x21 x22: x22
STACK CFI 3d58 x23: x23
STACK CFI 3d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3d98 x23: x23
STACK CFI 3da0 x21: x21 x22: x22
STACK CFI 3dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3db0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3db8 x21: x21 x22: x22
STACK CFI 3dc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3dcc x21: x21 x22: x22
STACK CFI INIT 3dd0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ddc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3df0 x21: .cfa -16 + ^
STACK CFI 3e14 x21: x21
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e48 x21: x21
STACK CFI INIT 3e50 68 .cfa: sp 0 + .ra: x30
STACK CFI 3e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e5c x19: .cfa -16 + ^
STACK CFI 3e90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3eb8 4c .cfa: sp 0 + .ra: x30
STACK CFI 3ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ec4 x19: .cfa -16 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ee0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f08 158 .cfa: sp 0 + .ra: x30
STACK CFI 3f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f30 x21: .cfa -16 + ^
STACK CFI 3fa4 x21: x21
STACK CFI 3fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4008 x21: x21
STACK CFI 400c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4010 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4034 x21: x21
STACK CFI 403c x21: .cfa -16 + ^
STACK CFI INIT 4060 80 .cfa: sp 0 + .ra: x30
STACK CFI 4064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4070 x19: .cfa -16 + ^
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 40e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40f0 x19: .cfa -16 + ^
STACK CFI 4148 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4150 330 .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4164 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 416c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 418c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4194 x25: .cfa -16 + ^
STACK CFI 42d4 x19: x19 x20: x20
STACK CFI 42d8 x21: x21 x22: x22
STACK CFI 42dc x25: x25
STACK CFI 42e8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 42ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4320 x19: x19 x20: x20
STACK CFI 4324 x21: x21 x22: x22
STACK CFI 432c x25: x25
STACK CFI 4330 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4334 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 43c4 x19: x19 x20: x20
STACK CFI 43c8 x21: x21 x22: x22
STACK CFI 43d0 x25: x25
STACK CFI 43d4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 43d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 43e4 x19: x19 x20: x20 x25: x25
STACK CFI 43ec x21: x21 x22: x22
STACK CFI 43f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 4420 x19: x19 x20: x20
STACK CFI 4424 x21: x21 x22: x22
STACK CFI 4428 x25: x25
STACK CFI 442c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 4458 x19: x19 x20: x20
STACK CFI 445c x21: x21 x22: x22
STACK CFI 4460 x25: x25
STACK CFI 4464 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI INIT 4480 34 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44b8 3c .cfa: sp 0 + .ra: x30
STACK CFI 44d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 44f8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 44fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4504 x19: .cfa -16 + ^
STACK CFI 4524 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4528 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4554 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 45a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45b0 x19: .cfa -16 + ^
STACK CFI 45d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45e8 3c .cfa: sp 0 + .ra: x30
STACK CFI 45f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 460c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4610 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4618 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4628 68 .cfa: sp 0 + .ra: x30
STACK CFI 4630 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4640 x19: .cfa -16 + ^
STACK CFI 4660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4668 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4678 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4684 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4690 68 .cfa: sp 0 + .ra: x30
STACK CFI 4698 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46a8 x19: .cfa -16 + ^
STACK CFI 46c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46f8 320 .cfa: sp 0 + .ra: x30
STACK CFI 46fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4704 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 472c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4730 x23: .cfa -16 + ^
STACK CFI 4740 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 485c x21: x21 x22: x22
STACK CFI 4874 x23: x23
STACK CFI 48a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 48ac x21: x21 x22: x22
STACK CFI 48b0 x23: x23
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4978 x21: x21 x22: x22
STACK CFI 497c x23: x23
STACK CFI 4980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4984 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4a18 28 .cfa: sp 0 + .ra: x30
STACK CFI 4a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a24 x19: .cfa -16 + ^
STACK CFI 4a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a40 64 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a50 x19: .cfa -16 + ^
STACK CFI 4a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4a88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4aa8 110 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ab4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4abc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4bb8 34 .cfa: sp 0 + .ra: x30
STACK CFI 4bc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4be0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4bf0 74 .cfa: sp 0 + .ra: x30
STACK CFI 4bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bfc x19: .cfa -16 + ^
STACK CFI 4c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4c68 54 .cfa: sp 0 + .ra: x30
STACK CFI 4c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c74 x19: .cfa -16 + ^
STACK CFI 4c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4cc0 70 .cfa: sp 0 + .ra: x30
STACK CFI 4cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4cd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4cf0 x21: .cfa -16 + ^
STACK CFI 4d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4d30 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d58 f0 .cfa: sp 0 + .ra: x30
STACK CFI 4d5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4dd0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4e48 84 .cfa: sp 0 + .ra: x30
STACK CFI 4e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2e28 80 .cfa: sp 0 + .ra: x30
STACK CFI 2e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ed0 74 .cfa: sp 0 + .ra: x30
STACK CFI 4ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2db0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f48 30 .cfa: sp 0 + .ra: x30
STACK CFI 4f4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4f78 2c .cfa: sp 0 + .ra: x30
STACK CFI 4f7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4fa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4fa8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4fac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fbc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4fc8 x23: .cfa -16 + ^
STACK CFI 5058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 505c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5078 8c .cfa: sp 0 + .ra: x30
STACK CFI 507c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5084 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 50cc x21: .cfa -16 + ^
STACK CFI 50f4 x21: x21
STACK CFI INIT 5108 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5120 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5128 70 .cfa: sp 0 + .ra: x30
STACK CFI 512c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5134 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5168 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5198 94 .cfa: sp 0 + .ra: x30
STACK CFI 519c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5230 80 .cfa: sp 0 + .ra: x30
STACK CFI 523c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5248 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 526c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 527c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 52a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 52a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 52b0 ec .cfa: sp 0 + .ra: x30
STACK CFI 52b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 52bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 52d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 52dc x23: .cfa -16 + ^
STACK CFI 5338 x21: x21 x22: x22
STACK CFI 5340 x23: x23
STACK CFI 5348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 534c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5358 x21: x21 x22: x22
STACK CFI 535c x23: x23
STACK CFI 5360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5364 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5380 x21: x21 x22: x22
STACK CFI 5388 x23: x23
STACK CFI 538c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5394 x21: x21 x22: x22 x23: x23
STACK CFI INIT 53a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 53a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5414 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5420 88 .cfa: sp 0 + .ra: x30
STACK CFI 5424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 542c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54a8 230 .cfa: sp 0 + .ra: x30
STACK CFI 54ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 54b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 54c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 54cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5548 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5598 x27: .cfa -16 + ^
STACK CFI 5678 x27: x27
STACK CFI 568c x25: x25 x26: x26
STACK CFI 5690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5694 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 56d4 x27: .cfa -16 + ^
STACK CFI INIT 56d8 30 .cfa: sp 0 + .ra: x30
STACK CFI 56dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56e4 x19: .cfa -16 + ^
STACK CFI 5704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5708 b0 .cfa: sp 0 + .ra: x30
STACK CFI 570c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5714 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 571c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 577c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5780 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5794 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57b8 94 .cfa: sp 0 + .ra: x30
STACK CFI 57bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5850 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5870 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5898 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 58a8 70 .cfa: sp 0 + .ra: x30
STACK CFI 58ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58b4 x19: .cfa -16 + ^
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 58e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5918 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5928 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5930 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5938 44 .cfa: sp 0 + .ra: x30
STACK CFI 593c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5944 x19: .cfa -16 + ^
STACK CFI 5978 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5980 114 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 598c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5994 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 59a4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5a74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5a98 74 .cfa: sp 0 + .ra: x30
STACK CFI 5a9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5aa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5aac x21: .cfa -16 + ^
STACK CFI 5b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5b10 14c .cfa: sp 0 + .ra: x30
STACK CFI 5b14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5b2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b34 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5c60 14c .cfa: sp 0 + .ra: x30
STACK CFI 5c64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c84 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5da4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5db0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5dc0 168 .cfa: sp 0 + .ra: x30
STACK CFI 5dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5dd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ddc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5de4 x23: .cfa -16 + ^
STACK CFI 5e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5e74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5f28 168 .cfa: sp 0 + .ra: x30
STACK CFI 5f2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f38 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f4c x23: .cfa -16 + ^
STACK CFI 5fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6090 1c .cfa: sp 0 + .ra: x30
STACK CFI 6094 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 60a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 60b0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 60b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 60bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60c8 x21: .cfa -16 + ^
STACK CFI 617c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6180 48 .cfa: sp 0 + .ra: x30
STACK CFI 6184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 618c x19: .cfa -16 + ^
STACK CFI 61c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61c8 38 .cfa: sp 0 + .ra: x30
STACK CFI 61cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61d4 x19: .cfa -16 + ^
STACK CFI 61fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6200 244 .cfa: sp 0 + .ra: x30
STACK CFI 6204 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 620c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6218 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6224 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6234 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 637c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6448 f4 .cfa: sp 0 + .ra: x30
STACK CFI 644c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6454 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 645c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 646c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6540 28 .cfa: sp 0 + .ra: x30
STACK CFI 6544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 654c x19: .cfa -16 + ^
STACK CFI 6564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6568 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6570 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6574 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 657c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6584 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6594 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6638 90 .cfa: sp 0 + .ra: x30
STACK CFI 663c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6644 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6650 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 665c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 666c x25: .cfa -16 + ^
STACK CFI 66c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 66c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66d8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6708 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6718 58 .cfa: sp 0 + .ra: x30
STACK CFI 671c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6724 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 676c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6770 58 .cfa: sp 0 + .ra: x30
STACK CFI 6774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 677c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67c8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6808 9c .cfa: sp 0 + .ra: x30
STACK CFI 680c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6814 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6820 x21: .cfa -16 + ^
STACK CFI 68a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 68a8 194 .cfa: sp 0 + .ra: x30
STACK CFI 68ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 69a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 69ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 69e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 69ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a40 3c .cfa: sp 0 + .ra: x30
STACK CFI 6a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a4c x19: .cfa -16 + ^
STACK CFI 6a78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a80 168 .cfa: sp 0 + .ra: x30
STACK CFI 6a84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6a8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6a98 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6aa8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b0c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6b2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6b40 x27: .cfa -16 + ^
STACK CFI 6bb4 x25: x25 x26: x26
STACK CFI 6bb8 x27: x27
STACK CFI 6be0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6be4 x27: .cfa -16 + ^
STACK CFI INIT 6be8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6bec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6bf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6bfc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6c98 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ca8 68 .cfa: sp 0 + .ra: x30
STACK CFI 6ce8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6d10 30 .cfa: sp 0 + .ra: x30
STACK CFI 6d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d1c x19: .cfa -16 + ^
STACK CFI 6d3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d48 cc .cfa: sp 0 + .ra: x30
STACK CFI 6d4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d6c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6df4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6e18 60 .cfa: sp 0 + .ra: x30
STACK CFI 6e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e24 x19: .cfa -16 + ^
STACK CFI 6e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e78 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e88 38 .cfa: sp 0 + .ra: x30
STACK CFI 6e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ec0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 6ec4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6ecc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ed4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ee4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7058 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7078 6c .cfa: sp 0 + .ra: x30
STACK CFI 707c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7084 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 70e8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 70ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 716c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7190 b0 .cfa: sp 0 + .ra: x30
STACK CFI 7194 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 71a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 71b4 x21: .cfa -16 + ^
STACK CFI 7200 x21: x21
STACK CFI 7204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7208 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 720c x21: x21
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 721c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7240 bc .cfa: sp 0 + .ra: x30
STACK CFI 7244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 724c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7254 x21: .cfa -16 + ^
STACK CFI 72f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7300 9c .cfa: sp 0 + .ra: x30
STACK CFI 7304 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 730c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 731c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7324 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 73a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 73a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 73b4 x19: .cfa -32 + ^
STACK CFI 73f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7408 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7428 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7448 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7458 6c .cfa: sp 0 + .ra: x30
STACK CFI 745c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7464 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 74c8 34 .cfa: sp 0 + .ra: x30
STACK CFI 74cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74d4 x19: .cfa -16 + ^
STACK CFI 74f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7500 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 7504 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 750c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7514 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7524 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7684 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 769c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 76a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 76c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 76c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 774c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7770 98 .cfa: sp 0 + .ra: x30
STACK CFI 7774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 777c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7808 98 .cfa: sp 0 + .ra: x30
STACK CFI 780c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7814 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7820 x21: .cfa -16 + ^
STACK CFI 789c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 78a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 78a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 78ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 78bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78c4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7928 78 .cfa: sp 0 + .ra: x30
STACK CFI 792c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7938 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7994 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 79a0 124 .cfa: sp 0 + .ra: x30
STACK CFI 79a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 79ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 79b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7ac8 7c .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ad4 x19: .cfa -16 + ^
STACK CFI 7b0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b48 5c .cfa: sp 0 + .ra: x30
STACK CFI 7b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ba8 74 .cfa: sp 0 + .ra: x30
STACK CFI 7bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bbc x21: .cfa -16 + ^
STACK CFI 7c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7c20 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c48 240 .cfa: sp 0 + .ra: x30
STACK CFI 7c4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7c54 x25: .cfa -16 + ^
STACK CFI 7c5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7c68 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 7cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 7d1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7d38 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e2c x23: x23 x24: x24
STACK CFI 7e60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e78 x23: x23 x24: x24
STACK CFI 7e7c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e84 x23: x23 x24: x24
STACK CFI INIT 7e88 58 .cfa: sp 0 + .ra: x30
STACK CFI 7e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ee0 204 .cfa: sp 0 + .ra: x30
STACK CFI 7ee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7eec x23: .cfa -16 + ^
STACK CFI 7ef8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7f00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8064 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 80e8 6c .cfa: sp 0 + .ra: x30
STACK CFI 80f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8100 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8110 x21: .cfa -16 + ^
STACK CFI 8140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8144 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 814c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8158 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8168 6c .cfa: sp 0 + .ra: x30
STACK CFI 8170 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8178 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8188 x21: .cfa -16 + ^
STACK CFI 81c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 81c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 81cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 81d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 81e8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8210 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8214 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 821c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8224 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8230 x23: .cfa -16 + ^
STACK CFI 82d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 82d8 40 .cfa: sp 0 + .ra: x30
STACK CFI 82dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82e4 x19: .cfa -16 + ^
STACK CFI 8314 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8318 58 .cfa: sp 0 + .ra: x30
STACK CFI 8320 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8328 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8370 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8378 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 838c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 83e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 83ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 83f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 83fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8420 58 .cfa: sp 0 + .ra: x30
STACK CFI 8428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8430 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 845c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8460 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 846c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8478 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8480 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8488 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8494 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 84f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 84f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 84fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8528 58 .cfa: sp 0 + .ra: x30
STACK CFI 8530 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8538 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8568 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8580 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8588 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 859c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 85f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 860c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8630 178 .cfa: sp 0 + .ra: x30
STACK CFI 8634 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 863c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8644 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8654 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 86c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 86c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 86fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8700 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 87a8 120 .cfa: sp 0 + .ra: x30
STACK CFI 87ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 87b4 x25: .cfa -32 + ^
STACK CFI 87bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 87cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 887c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 88c8 18c .cfa: sp 0 + .ra: x30
STACK CFI 88cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 88d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8914 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8918 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8920 x23: .cfa -16 + ^
STACK CFI 8968 x23: x23
STACK CFI 8978 x21: x21 x22: x22
STACK CFI 89b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 89c8 x23: .cfa -16 + ^
STACK CFI 8a14 x21: x21 x22: x22
STACK CFI 8a18 x23: x23
STACK CFI 8a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8a58 34 .cfa: sp 0 + .ra: x30
STACK CFI 8a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a68 x19: .cfa -16 + ^
STACK CFI 8a88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8a90 40 .cfa: sp 0 + .ra: x30
STACK CFI 8a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a9c x19: .cfa -16 + ^
STACK CFI 8abc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ac0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ad0 54 .cfa: sp 0 + .ra: x30
STACK CFI 8ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8adc x19: .cfa -16 + ^
STACK CFI 8b10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b28 58 .cfa: sp 0 + .ra: x30
STACK CFI 8b2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b34 x19: .cfa -16 + ^
STACK CFI 8b6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b80 70 .cfa: sp 0 + .ra: x30
STACK CFI 8b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8bc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
