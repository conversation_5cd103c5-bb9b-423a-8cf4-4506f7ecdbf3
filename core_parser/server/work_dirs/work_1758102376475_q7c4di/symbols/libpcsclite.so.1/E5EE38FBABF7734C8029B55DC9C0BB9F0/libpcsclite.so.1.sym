MODULE Linux arm64 E5EE38FBABF7734C8029B55DC9C0BB9F0 libpcsclite.so.1
INFO CODE_ID FB38EEE5F7AB4C738029B55DC9C0BB9F747777AD
PUBLIC 1c20 0 pcsc_stringify_error
PUBLIC 2540 0 SCardReleaseContext
PUBLIC 2750 0 SCardConnect
PUBLIC 2958 0 SCardReconnect
PUBLIC 2ae8 0 SCardDisconnect
PUBLIC 2c40 0 SCardBeginTransaction
PUBLIC 2d58 0 SCardEndTransaction
PUBLIC 2e40 0 SCardStatus
PUBLIC 3258 0 SCardGetStatusChange
PUBLIC 3c58 0 SCardControl
PUBLIC 3df8 0 SCardGetAttrib
PUBLIC 3eb0 0 SCardSetAttrib
PUBLIC 3ef8 0 SCardTransmit
PUBLIC 4168 0 SCardListReaders
PUBLIC 4330 0 SCardFreeMemory
PUBLIC 4378 0 SCardListReaderGroups
PUBLIC 4488 0 SCardCancel
PUBLIC 45b8 0 SCardIsValidContext
PUBLIC 4690 0 SCardEstablishContext
STACK CFI INIT 1898 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18c8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1908 48 .cfa: sp 0 + .ra: x30
STACK CFI 190c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1914 x19: .cfa -16 + ^
STACK CFI 194c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1950 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1958 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 195c .cfa: sp 2464 +
STACK CFI 1968 .ra: .cfa -2456 + ^ x29: .cfa -2464 + ^
STACK CFI 1970 x19: .cfa -2448 + ^ x20: .cfa -2440 + ^
STACK CFI 197c x21: .cfa -2432 + ^ x22: .cfa -2424 + ^
STACK CFI 1988 x23: .cfa -2416 + ^ x24: .cfa -2408 + ^
STACK CFI 1a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a14 .cfa: sp 2464 + .ra: .cfa -2456 + ^ x19: .cfa -2448 + ^ x20: .cfa -2440 + ^ x21: .cfa -2432 + ^ x22: .cfa -2424 + ^ x23: .cfa -2416 + ^ x24: .cfa -2408 + ^ x29: .cfa -2464 + ^
STACK CFI 1b50 x25: .cfa -2400 + ^ x26: .cfa -2392 + ^
STACK CFI 1ba4 x25: x25 x26: x26
STACK CFI 1c00 x25: .cfa -2400 + ^ x26: .cfa -2392 + ^
STACK CFI 1c10 x25: x25 x26: x26
STACK CFI 1c18 x25: .cfa -2400 + ^ x26: .cfa -2392 + ^
STACK CFI INIT 1c20 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1e6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1eb4 x21: x21 x22: x22
STACK CFI INIT 1ec8 8c .cfa: sp 0 + .ra: x30
STACK CFI 1ecc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ed4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ee4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1f58 84 .cfa: sp 0 + .ra: x30
STACK CFI 1f5c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f84 x21: .cfa -32 + ^
STACK CFI 1fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1fd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1fe0 118 .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1fec x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1ffc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2028 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2030 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2040 x27: .cfa -32 + ^
STACK CFI 20ac x19: x19 x20: x20
STACK CFI 20b0 x23: x23 x24: x24
STACK CFI 20b4 x27: x27
STACK CFI 20c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 20cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 20d0 x23: x23 x24: x24
STACK CFI 20d8 x27: x27
STACK CFI 20e0 x19: x19 x20: x20
STACK CFI 20f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI INIT 20f8 78 .cfa: sp 0 + .ra: x30
STACK CFI 20fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2104 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2114 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2148 x21: x21 x22: x22
STACK CFI 2154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2158 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2168 x21: x21 x22: x22
STACK CFI INIT 2170 64 .cfa: sp 0 + .ra: x30
STACK CFI 2194 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21d8 64 .cfa: sp 0 + .ra: x30
STACK CFI 21fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2240 54 .cfa: sp 0 + .ra: x30
STACK CFI 2244 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2258 x19: .cfa -16 + ^
STACK CFI 2270 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2274 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2298 54 .cfa: sp 0 + .ra: x30
STACK CFI 229c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22b0 x19: .cfa -16 + ^
STACK CFI 22c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 22cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 22e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22f0 80 .cfa: sp 0 + .ra: x30
STACK CFI 22f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2304 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 236c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2370 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 2374 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 237c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 238c x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 23ac x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 23c4 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 23e8 x25: x25 x26: x26
STACK CFI 2410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2414 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x29: .cfa -400 + ^
STACK CFI 2430 x27: .cfa -320 + ^
STACK CFI 24a4 x27: x27
STACK CFI 24a8 x27: .cfa -320 + ^
STACK CFI 24ac x27: x27
STACK CFI 24b0 x25: x25 x26: x26
STACK CFI 24bc x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^
STACK CFI 2528 x27: x27
STACK CFI 252c x27: .cfa -320 + ^
STACK CFI 2534 x25: x25 x26: x26 x27: x27
STACK CFI 2538 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 253c x27: .cfa -320 + ^
STACK CFI INIT 2540 210 .cfa: sp 0 + .ra: x30
STACK CFI 2544 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 254c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2568 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2578 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 25d0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2610 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2684 x27: x27 x28: x28
STACK CFI 26ac x25: x25 x26: x26
STACK CFI 26b8 x23: x23 x24: x24
STACK CFI 26dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 2704 x23: x23 x24: x24
STACK CFI 2710 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2740 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2744 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2748 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 274c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 2750 204 .cfa: sp 0 + .ra: x30
STACK CFI 2754 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 275c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 276c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 2788 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 27a4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 27d8 x27: .cfa -176 + ^
STACK CFI 281c x25: x25 x26: x26
STACK CFI 2820 x27: x27
STACK CFI 2824 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2828 x25: x25 x26: x26
STACK CFI 2858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 285c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 2868 x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^
STACK CFI 289c x27: x27
STACK CFI 28a8 x25: x25 x26: x26
STACK CFI 28b8 x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^
STACK CFI 293c x25: x25 x26: x26 x27: x27
STACK CFI 2940 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2944 x27: .cfa -176 + ^
STACK CFI INIT 2958 190 .cfa: sp 0 + .ra: x30
STACK CFI 295c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 296c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2984 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2998 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 29a4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 29b0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2a68 x21: x21 x22: x22
STACK CFI 2a6c x23: x23 x24: x24
STACK CFI 2a70 x25: x25 x26: x26
STACK CFI 2a74 x27: x27 x28: x28
STACK CFI 2a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aa0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2ab8 x21: x21 x22: x22
STACK CFI 2abc x23: x23 x24: x24
STACK CFI 2ac0 x25: x25 x26: x26
STACK CFI 2ac4 x27: x27 x28: x28
STACK CFI 2ad8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2adc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2ae0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2ae4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2ae8 154 .cfa: sp 0 + .ra: x30
STACK CFI 2aec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2af4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2b0c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b88 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2c40 114 .cfa: sp 0 + .ra: x30
STACK CFI 2c44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2c4c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2c58 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2c68 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2c84 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d50 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2d58 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2d5c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2d64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2d7c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2df8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2e40 414 .cfa: sp 0 + .ra: x30
STACK CFI 2e44 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2e54 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2e74 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2fa8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3258 a00 .cfa: sp 0 + .ra: x30
STACK CFI 325c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 3264 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 329c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 32a4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 32cc x19: x19 x20: x20
STACK CFI 32d4 x27: x27 x28: x28
STACK CFI 3300 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3304 .cfa: sp 256 + .ra: .cfa -248 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x29: .cfa -256 + ^
STACK CFI 3314 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 3318 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 334c x19: x19 x20: x20
STACK CFI 3350 x25: x25 x26: x26
STACK CFI 3354 x27: x27 x28: x28
STACK CFI 3358 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 338c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 35cc x23: x23 x24: x24
STACK CFI 35e8 x19: x19 x20: x20
STACK CFI 35ec x25: x25 x26: x26
STACK CFI 35f0 x27: x27 x28: x28
STACK CFI 35f4 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 393c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3944 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 399c x23: x23 x24: x24
STACK CFI 39fc x19: x19 x20: x20
STACK CFI 3a00 x25: x25 x26: x26
STACK CFI 3a04 x27: x27 x28: x28
STACK CFI 3a08 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 3a98 x23: x23 x24: x24
STACK CFI 3a9c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 3b80 x23: x23 x24: x24
STACK CFI 3b84 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 3c44 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c48 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 3c4c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 3c50 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 3c54 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 3c58 19c .cfa: sp 0 + .ra: x30
STACK CFI 3c5c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3c64 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3c74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3c90 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3d18 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 3d24 x27: .cfa -64 + ^
STACK CFI 3d6c x27: x27
STACK CFI 3d70 x27: .cfa -64 + ^
STACK CFI 3da8 x27: x27
STACK CFI 3db8 x27: .cfa -64 + ^
STACK CFI 3de4 x27: x27
STACK CFI 3df0 x27: .cfa -64 + ^
STACK CFI INIT 3df8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3e00 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ea0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3eb0 44 .cfa: sp 0 + .ra: x30
STACK CFI 3eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ef0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ef8 26c .cfa: sp 0 + .ra: x30
STACK CFI 3efc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3f04 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 3f30 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 3f48 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3f64 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3f70 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 40bc x19: x19 x20: x20
STACK CFI 40c0 x21: x21 x22: x22
STACK CFI 40c4 x23: x23 x24: x24
STACK CFI 40c8 x27: x27 x28: x28
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 40f4 .cfa: sp 176 + .ra: .cfa -168 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 4100 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 410c x21: x21 x22: x22
STACK CFI 4110 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 413c x19: x19 x20: x20
STACK CFI 4140 x21: x21 x22: x22
STACK CFI 4144 x23: x23 x24: x24
STACK CFI 4148 x27: x27 x28: x28
STACK CFI 4154 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4158 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 415c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 4160 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 4168 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 416c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4174 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 417c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4198 x27: .cfa -16 + ^
STACK CFI 41ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 41c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 425c x19: x19 x20: x20
STACK CFI 4260 x21: x21 x22: x22
STACK CFI 4274 x23: x23 x24: x24
STACK CFI 427c x27: x27
STACK CFI 4280 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 4284 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 4288 x19: x19 x20: x20
STACK CFI 428c x21: x21 x22: x22
STACK CFI 4290 x23: x23 x24: x24 x27: x27
STACK CFI 42a4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 42a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 42b8 x23: x23 x24: x24
STACK CFI 42c0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 42c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 42ec x19: x19 x20: x20
STACK CFI 42f0 x21: x21 x22: x22
STACK CFI 42f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4300 x19: x19 x20: x20
STACK CFI 4304 x21: x21 x22: x22
STACK CFI 4308 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4314 x19: x19 x20: x20
STACK CFI 4318 x21: x21 x22: x22
STACK CFI 431c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4328 x19: x19 x20: x20
STACK CFI 432c x21: x21 x22: x22
STACK CFI INIT 4330 44 .cfa: sp 0 + .ra: x30
STACK CFI 4334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 433c x19: .cfa -16 + ^
STACK CFI 435c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4360 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4370 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4378 10c .cfa: sp 0 + .ra: x30
STACK CFI 437c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 438c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 439c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 43bc x23: .cfa -48 + ^
STACK CFI 443c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4440 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4488 130 .cfa: sp 0 + .ra: x30
STACK CFI 448c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4494 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 44a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 44e0 x23: .cfa -48 + ^
STACK CFI 44f8 x23: x23
STACK CFI 451c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4520 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 4558 x23: x23
STACK CFI 455c x23: .cfa -48 + ^
STACK CFI 4588 x23: x23
STACK CFI 458c x23: .cfa -48 + ^
STACK CFI 4598 x23: x23
STACK CFI 45b4 x23: .cfa -48 + ^
STACK CFI INIT 45b8 24 .cfa: sp 0 + .ra: x30
STACK CFI 45bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 45e4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 45ec x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4644 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 4690 458 .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 469c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 46ac x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 46c0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 46f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 46f8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 47f4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4814 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4898 x25: x25 x26: x26
STACK CFI 489c x27: x27 x28: x28
STACK CFI 4964 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 496c x25: x25 x26: x26
STACK CFI 4970 x27: x27 x28: x28
STACK CFI 4974 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 497c x25: x25 x26: x26
STACK CFI 4980 x27: x27 x28: x28
STACK CFI 4984 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a24 x25: x25 x26: x26
STACK CFI 4a28 x27: x27 x28: x28
STACK CFI 4a2c x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a68 x25: x25 x26: x26
STACK CFI 4a6c x27: x27 x28: x28
STACK CFI 4a70 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4ac8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4acc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4ad0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4ae0 x25: x25 x26: x26
STACK CFI 4ae4 x27: x27 x28: x28
STACK CFI INIT 4ae8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4af8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b5c x19: x19 x20: x20
STACK CFI 4b68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b78 x19: x19 x20: x20
STACK CFI 4b80 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b90 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ba0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4c60 318 .cfa: sp 0 + .ra: x30
STACK CFI 4c64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4c78 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4c84 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4c90 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4c98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4ca4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4e68 x19: x19 x20: x20
STACK CFI 4e6c x21: x21 x22: x22
STACK CFI 4e70 x23: x23 x24: x24
STACK CFI 4e74 x25: x25 x26: x26
STACK CFI 4e78 x27: x27 x28: x28
STACK CFI 4e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e80 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4f78 158 .cfa: sp 0 + .ra: x30
STACK CFI 4f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5008 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 508c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5090 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 50d0 270 .cfa: sp 0 + .ra: x30
STACK CFI 50d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 50dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 50e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5198 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 51a0 x23: .cfa -32 + ^
STACK CFI 51c8 x23: x23
STACK CFI 5250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5254 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 529c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5328 x23: .cfa -32 + ^
STACK CFI 533c x23: x23
STACK CFI INIT 5340 bc .cfa: sp 0 + .ra: x30
STACK CFI 5348 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5350 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 53f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5400 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5420 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5440 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5470 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5490 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 54b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 54d0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54e8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5500 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5520 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5648 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5650 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5670 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5690 14c .cfa: sp 0 + .ra: x30
STACK CFI 5694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 569c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5724 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 57e0 164 .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5894 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5948 5c .cfa: sp 0 + .ra: x30
STACK CFI 594c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5954 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 59a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59a8 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 59ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 59b4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 59d0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 59e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 59f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5b0c x21: x21 x22: x22
STACK CFI 5b24 x19: x19 x20: x20
STACK CFI 5b28 x23: x23 x24: x24
STACK CFI 5b30 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 5b34 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5be4 x23: x23 x24: x24
STACK CFI 5be8 x19: x19 x20: x20
STACK CFI 5bec x21: x21 x22: x22
STACK CFI 5bf4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 5bf8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5c2c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5c34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c3c x19: x19 x20: x20
STACK CFI 5c40 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 5c50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c58 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c68 bc .cfa: sp 0 + .ra: x30
STACK CFI 5c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d28 3c .cfa: sp 0 + .ra: x30
STACK CFI 5d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d34 x19: .cfa -16 + ^
STACK CFI 5d58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5d68 94 .cfa: sp 0 + .ra: x30
STACK CFI 5d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d8c x21: .cfa -16 + ^
STACK CFI 5dd8 x21: x21
STACK CFI 5ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5de0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5de4 x21: x21
STACK CFI 5df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5e00 1c .cfa: sp 0 + .ra: x30
STACK CFI 5e04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5e20 200 .cfa: sp 0 + .ra: x30
STACK CFI 5e24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5e34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5e40 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5e48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5e54 x27: .cfa -16 + ^
STACK CFI 5ea8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5f6c x25: x25 x26: x26
STACK CFI 5f70 x27: x27
STACK CFI 5f78 x19: x19 x20: x20
STACK CFI 5f7c x21: x21 x22: x22
STACK CFI 5f88 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 5f8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5f9c x19: x19 x20: x20
STACK CFI 5fa0 x21: x21 x22: x22
STACK CFI 5fa8 x25: x25 x26: x26
STACK CFI 5fac x27: x27
STACK CFI 5fb0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 5fb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5fbc x19: x19 x20: x20
STACK CFI 5fc0 x21: x21 x22: x22
STACK CFI 5fc8 x27: x27
STACK CFI 5fcc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 5fd0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5ff4 x25: x25 x26: x26
STACK CFI 5ff8 x19: x19 x20: x20
STACK CFI 5ffc x21: x21 x22: x22
STACK CFI 6000 x27: x27
STACK CFI 6008 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6010 x19: x19 x20: x20
STACK CFI 6014 x21: x21 x22: x22
STACK CFI 6018 x25: x25 x26: x26
STACK CFI 601c x27: x27
STACK CFI INIT 6020 64 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 602c x19: .cfa -16 + ^
STACK CFI 6078 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 607c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6088 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 60c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 60f8 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6118 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6130 94 .cfa: sp 0 + .ra: x30
STACK CFI 6134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 613c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6158 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 61b0 x19: x19 x20: x20
STACK CFI 61b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 61bc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 61c8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 61e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6208 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6228 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6248 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6268 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6288 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 62a8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 62c8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 62e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6308 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6318 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6320 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6328 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6330 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6338 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6348 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6358 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6360 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6368 18 .cfa: sp 0 + .ra: x30
STACK CFI 636c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 637c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6388 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6398 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63e0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6440 54 .cfa: sp 0 + .ra: x30
STACK CFI 6444 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6454 x19: .cfa -48 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6490 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6498 8c .cfa: sp 0 + .ra: x30
STACK CFI 649c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 64b8 x19: .cfa -48 + ^
STACK CFI 651c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6520 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6528 60 .cfa: sp 0 + .ra: x30
STACK CFI 6534 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 653c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6588 6c .cfa: sp 0 + .ra: x30
STACK CFI 658c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6594 x19: .cfa -48 + ^
STACK CFI 65e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 65e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 65f8 110 .cfa: sp 0 + .ra: x30
STACK CFI 65fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 660c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6628 x21: .cfa -48 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 668c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6708 ec .cfa: sp 0 + .ra: x30
STACK CFI 670c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6714 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6730 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6734 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6738 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6754 x23: .cfa -16 + ^
STACK CFI 6798 x19: x19 x20: x20
STACK CFI 67a0 x23: x23
STACK CFI 67a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 67a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 67e4 x19: x19 x20: x20
STACK CFI 67ec x23: x23
STACK CFI 67f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 67f8 94 .cfa: sp 0 + .ra: x30
STACK CFI 67fc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 680c x19: .cfa -160 + ^
STACK CFI 6854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6858 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 6890 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68c0 fc .cfa: sp 0 + .ra: x30
STACK CFI 68c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 68cc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 68d8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 68ec x25: .cfa -96 + ^
STACK CFI 68fc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 693c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6940 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 69c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 69c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69d4 x19: .cfa -16 + ^
STACK CFI 6a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6a2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a30 3c .cfa: sp 0 + .ra: x30
STACK CFI 6a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a3c x19: .cfa -16 + ^
STACK CFI 6a54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a70 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6a74 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 6a84 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 6ab0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6b30 x21: x21 x22: x22
STACK CFI 6b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b50 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI 6ba0 x21: x21 x22: x22
STACK CFI 6ba4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6bd8 x21: x21 x22: x22
STACK CFI 6c14 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6c48 x21: x21 x22: x22
STACK CFI 6c4c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI INIT 6c50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c58 1dc .cfa: sp 0 + .ra: x30
STACK CFI 6c5c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6c6c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6c74 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 6c8c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6c94 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6c9c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6d88 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6e38 15c .cfa: sp 0 + .ra: x30
STACK CFI 6e3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6e44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6e50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ec8 x19: x19 x20: x20
STACK CFI 6eec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ef0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6f40 x19: x19 x20: x20
STACK CFI 6f44 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6f68 x19: x19 x20: x20
STACK CFI 6f6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6f78 x19: x19 x20: x20
STACK CFI 6f7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6f88 x19: x19 x20: x20
STACK CFI 6f90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 6f98 88 .cfa: sp 0 + .ra: x30
STACK CFI 6f9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6fa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fb0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7008 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7020 120 .cfa: sp 0 + .ra: x30
STACK CFI 7024 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 702c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 703c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7050 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 711c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7120 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
