MODULE Linux arm64 36E718F2F23A7D1828FC12BE7E67A2000 libXdamage.so.1
INFO CODE_ID F218E7363AF2187D28FC12BE7E67A2000368D583
PUBLIC d28 0 XDamageFindDisplay
PUBLIC 1230 0 XDamageQueryExtension
PUBLIC 1290 0 XDamageQueryVersion
PUBLIC 12e8 0 XDamageCreate
PUBLIC 13c8 0 XDamageDestroy
PUBLIC 1478 0 XDamageSubtract
PUBLIC 1540 0 XDamageAdd
STACK CFI INIT b48 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT b78 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb8 48 .cfa: sp 0 + .ra: x30
STACK CFI bbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc4 x19: .cfa -16 + ^
STACK CFI bfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c08 11c .cfa: sp 0 + .ra: x30
STACK CFI c0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d28 308 .cfa: sp 0 + .ra: x30
STACK CFI d2c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI d34 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d40 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI d48 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e08 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI e0c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e5c x27: .cfa -64 + ^
STACK CFI f44 x27: x27
STACK CFI f9c x25: x25 x26: x26
STACK CFI fa0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI fd4 x25: x25 x26: x26
STACK CFI fd8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI fdc x25: x25 x26: x26
STACK CFI fe0 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI fe4 x27: x27
STACK CFI fe8 x27: .cfa -64 + ^
STACK CFI 101c x25: x25 x26: x26
STACK CFI 1020 x27: x27
STACK CFI 1028 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 102c x27: .cfa -64 + ^
STACK CFI INIT 1030 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 103c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1078 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 110c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1110 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 111c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1120 110 .cfa: sp 0 + .ra: x30
STACK CFI 1124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 112c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1138 x21: .cfa -16 + ^
STACK CFI 1170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1174 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1188 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 122c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1230 60 .cfa: sp 0 + .ra: x30
STACK CFI 1234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 123c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 127c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1280 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 128c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1290 58 .cfa: sp 0 + .ra: x30
STACK CFI 1294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 129c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12e8 e0 .cfa: sp 0 + .ra: x30
STACK CFI 12ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1304 x23: .cfa -16 + ^
STACK CFI 13a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 13ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 13c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 13c8 ac .cfa: sp 0 + .ra: x30
STACK CFI 13cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13dc x21: .cfa -16 + ^
STACK CFI 1460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1464 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1478 c4 .cfa: sp 0 + .ra: x30
STACK CFI 147c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1484 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 148c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1498 x23: .cfa -16 + ^
STACK CFI 1524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1528 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1540 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1544 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 154c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1554 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 15ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
