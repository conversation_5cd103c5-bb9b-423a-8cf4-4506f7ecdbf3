MODULE Linux arm64 9DD2E873D9F1E21F7702EDEC3B2F7DF10 libbabeltrace-ctf-metadata.so.1
INFO CODE_ID 73E8D29DF1D91FE27702EDEC3B2F7DF1FCA5883B
PUBLIC c68 0 bt_ctf_metadata_hook
STACK CFI INIT a08 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a38 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT a78 48 .cfa: sp 0 + .ra: x30
STACK CFI a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a84 x19: .cfa -16 + ^
STACK CFI abc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac8 5c .cfa: sp 0 + .ra: x30
STACK CFI acc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae0 x19: .cfa -16 + ^
STACK CFI b0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b28 6c .cfa: sp 0 + .ra: x30
STACK CFI b2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b98 cc .cfa: sp 0 + .ra: x30
STACK CFI b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ba4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bac x21: .cfa -16 + ^
STACK CFI c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a0 54 .cfa: sp 0 + .ra: x30
STACK CFI 9a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 990 c .cfa: sp 0 + .ra: x30
