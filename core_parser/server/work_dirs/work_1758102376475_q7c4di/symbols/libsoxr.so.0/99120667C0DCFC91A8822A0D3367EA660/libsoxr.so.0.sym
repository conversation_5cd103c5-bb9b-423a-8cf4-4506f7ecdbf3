MODULE Linux arm64 99120667C0DCFC91A8822A0D3367EA660 libsoxr.so.0
INFO CODE_ID 67061299DCC091FCA8822A0D3367EA66190DAA72
PUBLIC 2e78 0 soxr_version
PUBLIC 2e88 0 soxr_quality_spec
PUBLIC 3148 0 soxr_engine
PUBLIC 3158 0 soxr_num_clips
PUBLIC 3160 0 soxr_error
PUBLIC 3168 0 soxr_runtime_spec
PUBLIC 31c8 0 soxr_io_spec
PUBLIC 3248 0 soxr_set_input_fn
PUBLIC 3268 0 soxr_delay
PUBLIC 3298 0 soxr_set_io_ratio
PUBLIC 34f0 0 soxr_set_num_channels
PUBLIC 3560 0 soxr_delete
PUBLIC 3590 0 soxr_create
PUBLIC 39d8 0 soxr_clear
PUBLIC 3b28 0 soxr_output
PUBLIC 3d88 0 soxr_process
PUBLIC 3fa0 0 soxr_oneshot
PUBLIC 40d0 0 soxr_set_error
STACK CFI INIT 2748 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2778 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 27bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27c4 x19: .cfa -16 + ^
STACK CFI 27fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2800 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2808 90 .cfa: sp 0 + .ra: x30
STACK CFI 280c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 281c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2834 x21: .cfa -48 + ^
STACK CFI 2890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2894 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2898 12c .cfa: sp 0 + .ra: x30
STACK CFI 289c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 28a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 28c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 28e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 28f4 x23: .cfa -32 + ^
STACK CFI 2930 x23: x23
STACK CFI 2954 x21: x21 x22: x22
STACK CFI 2958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 295c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2974 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 298c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 29c8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 29cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 29d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2aa8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2ab8 50 .cfa: sp 0 + .ra: x30
STACK CFI 2abc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ac4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ad0 x21: .cfa -16 + ^
STACK CFI 2b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2b08 74 .cfa: sp 0 + .ra: x30
STACK CFI 2b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b20 x21: .cfa -16 + ^
STACK CFI 2b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2b80 8c .cfa: sp 0 + .ra: x30
STACK CFI 2b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ba8 x21: .cfa -16 + ^
STACK CFI 2be0 x21: x21
STACK CFI 2c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c10 bc .cfa: sp 0 + .ra: x30
STACK CFI 2c14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c28 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c6c x25: .cfa -16 + ^
STACK CFI 2cac x25: x25
STACK CFI 2cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2cc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2cd0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 2cd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2cdc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ce8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^
STACK CFI 2d24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d84 x23: x23 x24: x24
STACK CFI 2d88 x25: x25 x26: x26
STACK CFI 2d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x29: x29
STACK CFI 2d9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2da8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2dac .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 2dbc x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 2e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e74 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT 2e78 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e88 2bc .cfa: sp 0 + .ra: x30
STACK CFI 2e8c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2e94 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2ea4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2eac x23: .cfa -96 + ^
STACK CFI 2ec0 v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI 2f84 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2f88 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3148 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3158 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3160 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3168 5c .cfa: sp 0 + .ra: x30
STACK CFI 316c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 31bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI INIT 31c8 7c .cfa: sp 0 + .ra: x30
STACK CFI 31cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 322c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3230 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3248 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3268 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3298 258 .cfa: sp 0 + .ra: x30
STACK CFI 329c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 32a4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 32b0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 32f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 32f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 3304 v8: .cfa -48 + ^
STACK CFI 3338 v8: v8
STACK CFI 3344 v8: .cfa -48 + ^
STACK CFI 3350 v8: v8
STACK CFI 336c v8: .cfa -48 + ^
STACK CFI 3374 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 33a8 x23: x23 x24: x24
STACK CFI 33ac v8: v8
STACK CFI 33b0 v8: .cfa -48 + ^
STACK CFI 33b8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 33c0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3494 x23: x23 x24: x24
STACK CFI 3498 x25: x25 x26: x26
STACK CFI 349c v8: v8
STACK CFI 34a4 v8: .cfa -48 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 34ac v8: v8
STACK CFI 34b0 x23: x23 x24: x24
STACK CFI 34b4 x25: x25 x26: x26
STACK CFI 34bc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 34c0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 34c4 v8: .cfa -48 + ^
STACK CFI 34e0 v8: v8
STACK CFI 34e4 x23: x23 x24: x24
STACK CFI 34e8 x25: x25 x26: x26
STACK CFI INIT 34f0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3560 2c .cfa: sp 0 + .ra: x30
STACK CFI 3568 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3570 x19: .cfa -16 + ^
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3590 444 .cfa: sp 0 + .ra: x30
STACK CFI 3594 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 35a0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 35b0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 35c4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 35d0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 35d8 v8: .cfa -112 + ^
STACK CFI 3688 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 368c .cfa: sp 192 + .ra: .cfa -184 + ^ v8: .cfa -112 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI INIT 39d8 14c .cfa: sp 0 + .ra: x30
STACK CFI 39dc .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 39e4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 39f0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a30 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x29: .cfa -368 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b20 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x29: .cfa -368 + ^
STACK CFI INIT 3b28 25c .cfa: sp 0 + .ra: x30
STACK CFI 3b2c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3b34 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 3b40 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3b70 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 3ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3ba4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 3bb8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3bcc x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3d24 x21: x21 x22: x22
STACK CFI 3d28 x23: x23 x24: x24
STACK CFI 3d2c x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 3d68 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3d7c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3d80 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI INIT 3d88 214 .cfa: sp 0 + .ra: x30
STACK CFI 3d8c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3d94 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3db0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3dbc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3dc8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3dd4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3ea0 x19: x19 x20: x20
STACK CFI 3ea4 x23: x23 x24: x24
STACK CFI 3ea8 x25: x25 x26: x26
STACK CFI 3eac x27: x27 x28: x28
STACK CFI 3ec8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3ecc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 3f60 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3f6c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 3f74 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3f78 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3f7c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3f80 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3f84 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 3fa0 12c .cfa: sp 0 + .ra: x30
STACK CFI 3fa4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3fac x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 3fbc x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 3fd4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3fe0 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 3fec x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4038 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 40d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4100 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 4104 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4114 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 419c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 420c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4278 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 42e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4354 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43a0 278 .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4438 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 44fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 456c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4618 28a8 .cfa: sp 0 + .ra: x30
STACK CFI 461c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 462c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 46c8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 46d4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4708 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4710 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4c54 x21: x21 x22: x22
STACK CFI 4c5c x23: x23 x24: x24
STACK CFI 4c64 x25: x25 x26: x26
STACK CFI 4c68 x27: x27 x28: x28
STACK CFI 4c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c84 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 4cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cf4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 4d14 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4d24 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4d74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4d7c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 517c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 51bc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 51c8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 51fc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5204 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5748 x21: x21 x22: x22
STACK CFI 5750 x23: x23 x24: x24
STACK CFI 5758 x25: x25 x26: x26
STACK CFI 575c x27: x27 x28: x28
STACK CFI 5a30 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5b70 x21: x21 x22: x22
STACK CFI 5b78 x23: x23 x24: x24
STACK CFI 5b7c x25: x25 x26: x26
STACK CFI 5b84 x27: x27 x28: x28
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ba8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 5ef8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6540 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6548 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 6ec0 2abc .cfa: sp 0 + .ra: x30
STACK CFI 6ec4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6ed4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f64 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 6f7c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6f88 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6fbc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6fc4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7588 x21: x21 x22: x22
STACK CFI 7590 x23: x23 x24: x24
STACK CFI 7598 x25: x25 x26: x26
STACK CFI 759c x27: x27 x28: x28
STACK CFI 75b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75b8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 7638 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 7648 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7698 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 76a0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7adc x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7b1c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7b28 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7b5c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 7b64 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 812c x21: x21 x22: x22
STACK CFI 8134 x23: x23 x24: x24
STACK CFI 813c x25: x25 x26: x26
STACK CFI 8140 x27: x27 x28: x28
STACK CFI 8454 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8594 x21: x21 x22: x22
STACK CFI 859c x23: x23 x24: x24
STACK CFI 85a0 x25: x25 x26: x26
STACK CFI 85a8 x27: x27 x28: x28
STACK CFI 85c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 85cc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8900 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8f6c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8f74 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 9980 280 .cfa: sp 0 + .ra: x30
STACK CFI 998c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9998 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9a44 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 9a60 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9b24 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 9b40 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9be4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 9bec .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9c00 68 .cfa: sp 0 + .ra: x30
STACK CFI 9c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c0c x19: .cfa -16 + ^
STACK CFI 9c64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9c68 68 .cfa: sp 0 + .ra: x30
STACK CFI 9c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c74 x19: .cfa -16 + ^
STACK CFI 9ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9cd0 2c .cfa: sp 0 + .ra: x30
STACK CFI 9cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d00 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9db8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 9dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9dc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9ddc x21: .cfa -16 + ^
STACK CFI 9e08 x21: x21
STACK CFI 9e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9e78 x21: x21
STACK CFI 9e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9e80 50 .cfa: sp 0 + .ra: x30
STACK CFI 9e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9e8c x19: .cfa -16 + ^
STACK CFI 9ecc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9ed0 278 .cfa: sp 0 + .ra: x30
STACK CFI 9ed4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9edc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ee4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9ef0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9f74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9f7c x25: .cfa -16 + ^
STACK CFI a06c x25: x25
STACK CFI a070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a074 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a080 x25: .cfa -16 + ^
STACK CFI a108 x25: x25
STACK CFI a10c x25: .cfa -16 + ^
STACK CFI a144 x25: x25
STACK CFI INIT a148 50 .cfa: sp 0 + .ra: x30
STACK CFI a14c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a154 x19: .cfa -16 + ^
STACK CFI a194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a198 28c .cfa: sp 0 + .ra: x30
STACK CFI a19c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a1a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a1b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a1bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a244 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a24c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a344 x25: x25 x26: x26
STACK CFI a348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a34c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a358 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a3e4 x25: x25 x26: x26
STACK CFI a3e8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a420 x25: x25 x26: x26
STACK CFI INIT a428 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a440 54 .cfa: sp 0 + .ra: x30
STACK CFI a444 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a44c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a454 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a498 54 .cfa: sp 0 + .ra: x30
STACK CFI a49c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a4f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a508 54 .cfa: sp 0 + .ra: x30
STACK CFI a50c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a514 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a51c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a560 54 .cfa: sp 0 + .ra: x30
STACK CFI a564 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a56c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a574 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a5b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a5b8 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT a630 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT a6f0 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT a768 c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT a828 164 .cfa: sp 0 + .ra: x30
STACK CFI a834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a840 v8: .cfa -16 + ^
STACK CFI a874 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI a878 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a898 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI a8a0 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a950 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI a954 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a988 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT a990 188 .cfa: sp 0 + .ra: x30
STACK CFI a994 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a99c v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI a9a8 v12: .cfa -48 + ^ v13: .cfa -40 + ^
STACK CFI a9b0 v14: .cfa -32 + ^ v15: .cfa -24 + ^
STACK CFI a9b8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a9c0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a9c8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a9d4 v10: .cfa -64 + ^ v11: .cfa -56 + ^
STACK CFI aae4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI aae8 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^ v14: .cfa -32 + ^ v15: .cfa -24 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT ab18 cc .cfa: sp 0 + .ra: x30
STACK CFI ab1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ab38 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI ab98 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI ab9c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT abe8 194 .cfa: sp 0 + .ra: x30
STACK CFI abec .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI abf8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ac08 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ac18 v8: .cfa -64 + ^ v9: .cfa -56 + ^ x23: .cfa -80 + ^
STACK CFI ac24 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI ac30 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI ad50 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ad54 .cfa: sp 128 + .ra: .cfa -120 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT ad80 744 .cfa: sp 0 + .ra: x30
STACK CFI ad8c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI ada0 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI adb8 v8: .cfa -112 + ^ v9: .cfa -104 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI adc0 v10: .cfa -96 + ^ v11: .cfa -88 + ^
STACK CFI ae80 v12: .cfa -80 + ^ v13: .cfa -72 + ^
STACK CFI ae84 v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI b128 v12: v12 v13: v13
STACK CFI b12c v14: v14 v15: v15
STACK CFI b31c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b320 .cfa: sp 208 + .ra: .cfa -200 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI b3b8 v12: .cfa -80 + ^ v13: .cfa -72 + ^
STACK CFI b3c4 v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI b420 v12: v12 v13: v13 v14: v14 v15: v15
STACK CFI b49c v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI b4bc v12: v12 v13: v13
STACK CFI b4c0 v14: v14 v15: v15
STACK CFI INIT b4c8 1b4 .cfa: sp 0 + .ra: x30
STACK CFI b4d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b4ec v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI b57c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI b580 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI b5e4 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI b5f4 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI b66c v12: v12 v13: v13
STACK CFI b674 v10: v10 v11: v11
STACK CFI b678 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI INIT b680 c4 .cfa: sp 0 + .ra: x30
STACK CFI b684 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b6b4 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI b704 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI b708 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b710 v10: .cfa -16 + ^
STACK CFI b73c v10: v10
STACK CFI b740 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI INIT b748 274 .cfa: sp 0 + .ra: x30
STACK CFI INIT b9c0 420 .cfa: sp 0 + .ra: x30
STACK CFI bb78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bb88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI bb94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bdd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT bde0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI bde4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bdf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI be5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI be64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI beac x21: x21 x22: x22
STACK CFI bf68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI bf78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI bf80 x21: x21 x22: x22
STACK CFI INIT bf88 1a4 .cfa: sp 0 + .ra: x30
STACK CFI bf8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bf98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c004 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c008 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c050 x21: x21 x22: x22
STACK CFI c10c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c110 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c11c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c124 x21: x21 x22: x22
STACK CFI INIT c130 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c1e8 d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c2c0 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT c358 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c400 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c408 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c410 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c418 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c420 340 .cfa: sp 0 + .ra: x30
STACK CFI c428 .cfa: sp 4192 +
STACK CFI c434 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI c584 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c588 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI c590 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI c594 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI c598 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI c59c x25: .cfa -4128 + ^
STACK CFI c718 x19: x19 x20: x20
STACK CFI c71c x21: x21 x22: x22
STACK CFI c720 x23: x23 x24: x24
STACK CFI c724 x25: x25
STACK CFI c73c x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI c740 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI c744 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI c748 x25: .cfa -4128 + ^
STACK CFI c74c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI c750 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI c754 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI c758 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI c75c x25: .cfa -4128 + ^
STACK CFI INIT c760 3e4 .cfa: sp 0 + .ra: x30
STACK CFI c764 .cfa: sp 2144 +
STACK CFI c770 .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI c928 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c92c .cfa: sp 2144 + .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI c944 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI c94c x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI c964 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI c968 x25: .cfa -2080 + ^
STACK CFI cb0c x19: x19 x20: x20
STACK CFI cb10 x21: x21 x22: x22
STACK CFI cb14 x23: x23 x24: x24
STACK CFI cb18 x25: x25
STACK CFI cb34 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI cb38 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI cb3c x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI cb40 x25: .cfa -2080 + ^
STACK CFI INIT cb48 f0 .cfa: sp 0 + .ra: x30
STACK CFI cb4c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI cb5c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI cb6c v8: .cfa -32 + ^
STACK CFI cb74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI cb8c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI cbbc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI cc14 x25: x25 x26: x26
STACK CFI cc1c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cc20 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -32 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI cc34 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT cc38 bc .cfa: sp 0 + .ra: x30
STACK CFI cc3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI cc4c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI cc54 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI cc60 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI cc94 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI cce0 x23: x23 x24: x24
STACK CFI ccf0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ccf8 b0 .cfa: sp 0 + .ra: x30
STACK CFI ccfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cd04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cd10 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cd68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cd6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cd84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cd88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cda4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT cda8 1d0 .cfa: sp 0 + .ra: x30
STACK CFI cdac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cdb4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cdc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cdd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cdd8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ce6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ce70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI cec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI cec8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI cf60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI cf64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT cf78 2d8 .cfa: sp 0 + .ra: x30
STACK CFI cf7c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cf84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cf90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cfa0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cfa8 x25: .cfa -16 + ^
STACK CFI d080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d084 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d148 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d1dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d1e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT d250 2e8 .cfa: sp 0 + .ra: x30
STACK CFI d254 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d25c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d268 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d278 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d280 x25: .cfa -16 + ^
STACK CFI d360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d364 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d42c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d4c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI d4c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT d538 4a8 .cfa: sp 0 + .ra: x30
STACK CFI d53c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d548 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI d550 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d560 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI d568 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI d570 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI d8c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d8c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI d974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d978 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT d9e0 464 .cfa: sp 0 + .ra: x30
STACK CFI d9e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d9ec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI d9f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI da04 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI da0c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI da14 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI da8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI da90 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI ddd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI dddc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT de48 554 .cfa: sp 0 + .ra: x30
STACK CFI de4c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI de58 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI de60 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI de74 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI de9c x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI dec8 v8: .cfa -128 + ^ v9: .cfa -120 + ^
STACK CFI ded4 v10: .cfa -112 + ^ v11: .cfa -104 + ^
STACK CFI dee0 v12: .cfa -96 + ^ v13: .cfa -88 + ^
STACK CFI e118 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e11c .cfa: sp 224 + .ra: .cfa -216 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v13: .cfa -88 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT e3a0 bc .cfa: sp 0 + .ra: x30
STACK CFI e3a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e3b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT e460 62c .cfa: sp 0 + .ra: x30
STACK CFI e464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI e474 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e47c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI e48c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e494 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI e4d4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI e6c4 x21: x21 x22: x22
STACK CFI e6f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e6f8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT ea90 10c .cfa: sp 0 + .ra: x30
STACK CFI ea94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ea9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI eaa4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI eab0 x23: .cfa -16 + ^
STACK CFI eb44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI eb48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI eb90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT eba0 1648 .cfa: sp 0 + .ra: x30
STACK CFI eba4 .cfa: sp 448 +
STACK CFI ebb0 .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI ebb8 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI ebcc v10: .cfa -320 + ^ v11: .cfa -312 + ^
STACK CFI ebf0 v12: .cfa -304 + ^ v13: .cfa -296 + ^ v14: .cfa -288 + ^ v15: .cfa -280 + ^
STACK CFI ebfc x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI ec10 x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI ed20 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI ed2c v8: .cfa -336 + ^ v9: .cfa -328 + ^
STACK CFI ef3c v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI f004 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f008 .cfa: sp 448 + .ra: .cfa -424 + ^ v10: .cfa -320 + ^ v11: .cfa -312 + ^ v12: .cfa -304 + ^ v13: .cfa -296 + ^ v14: .cfa -288 + ^ v15: .cfa -280 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI f02c v8: .cfa -336 + ^ v9: .cfa -328 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI f554 x21: x21 x22: x22
STACK CFI f558 v8: v8 v9: v9
STACK CFI f560 v8: .cfa -336 + ^ v9: .cfa -328 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI f688 v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI f694 v8: .cfa -336 + ^ v9: .cfa -328 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI f73c x21: x21 x22: x22
STACK CFI f740 v8: v8 v9: v9
STACK CFI f744 v8: .cfa -336 + ^ v9: .cfa -328 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 101c0 v8: v8 v9: v9 x21: x21 x22: x22
STACK CFI 101c4 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 101c8 v8: .cfa -336 + ^ v9: .cfa -328 + ^
STACK CFI INIT 101e8 a4 .cfa: sp 0 + .ra: x30
STACK CFI 101ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 101f4 x23: .cfa -16 + ^
STACK CFI 10204 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1026c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10290 74 .cfa: sp 0 + .ra: x30
STACK CFI 10294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1029c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 102a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 10308 d8 .cfa: sp 0 + .ra: x30
STACK CFI 10310 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10338 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 103c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 103c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 103dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 103e0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10420 104 .cfa: sp 0 + .ra: x30
STACK CFI 10424 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1042c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10438 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10444 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1050c x21: x21 x22: x22
STACK CFI 10510 x23: x23 x24: x24
STACK CFI 10514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10518 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 10520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10528 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10548 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10560 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10570 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10580 bc .cfa: sp 0 + .ra: x30
STACK CFI 10584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10590 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10640 424 .cfa: sp 0 + .ra: x30
STACK CFI 10644 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1064c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1065c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10664 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 108a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 108ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10a68 488 .cfa: sp 0 + .ra: x30
STACK CFI 10a6c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10a74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10a84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10a8c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10d08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10ef0 184 .cfa: sp 0 + .ra: x30
STACK CFI 10ef4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10efc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10f10 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10f3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10f40 x25: .cfa -16 + ^
STACK CFI 11048 x23: x23 x24: x24
STACK CFI 1104c x25: x25
STACK CFI 1105c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11060 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1106c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11070 x25: .cfa -16 + ^
STACK CFI INIT 11078 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 1107c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11084 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 11098 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 110c4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 110c8 x25: .cfa -64 + ^
STACK CFI 11118 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1111c v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 11120 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 11128 v14: .cfa -56 + ^
STACK CFI 112c4 v8: v8 v9: v9
STACK CFI 112c8 v10: v10 v11: v11
STACK CFI 112cc v12: v12 v13: v13
STACK CFI 112d0 v14: v14
STACK CFI 11300 x23: x23 x24: x24
STACK CFI 11304 x25: x25
STACK CFI 11314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11318 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 11324 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11328 x25: .cfa -64 + ^
STACK CFI INIT 11330 2ec .cfa: sp 0 + .ra: x30
STACK CFI 11334 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1133c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11348 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11358 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11508 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11620 300 .cfa: sp 0 + .ra: x30
STACK CFI 11624 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1162c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1163c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11648 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 117f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 117f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11920 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 11924 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1192c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11938 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11948 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11ae4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11c00 14c .cfa: sp 0 + .ra: x30
STACK CFI 11c04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11c0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11c20 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11c4c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11c50 x25: .cfa -16 + ^
STACK CFI 11d20 x23: x23 x24: x24
STACK CFI 11d24 x25: x25
STACK CFI 11d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11d38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 11d44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11d48 x25: .cfa -16 + ^
STACK CFI INIT 11d50 19c .cfa: sp 0 + .ra: x30
STACK CFI 11d54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11d5c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11d64 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11d84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11d8c x25: .cfa -16 + ^
STACK CFI 11ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 11ef0 17c .cfa: sp 0 + .ra: x30
STACK CFI 11ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11efc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11f0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 12070 164 .cfa: sp 0 + .ra: x30
STACK CFI 12074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1207c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1208c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 121d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 121d8 150 .cfa: sp 0 + .ra: x30
STACK CFI 121dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 121e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 121f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 12328 274 .cfa: sp 0 + .ra: x30
STACK CFI INIT 125a0 420 .cfa: sp 0 + .ra: x30
STACK CFI 12758 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12768 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12774 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 129b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 129c0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 129c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 129d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12a40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12a44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12a8c x21: x21 x22: x22
STACK CFI 12b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12b58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12b60 x21: x21 x22: x22
STACK CFI INIT 12b68 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 12b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12b78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12be4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12be8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12c30 x21: x21 x22: x22
STACK CFI 12cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12cf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 12cfc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12d04 x21: x21 x22: x22
STACK CFI INIT 12d10 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12dc8 d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ea0 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12f38 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fd8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fe0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fe8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ff0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ff8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13000 340 .cfa: sp 0 + .ra: x30
STACK CFI 13008 .cfa: sp 4192 +
STACK CFI 13014 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 13164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 13168 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 13170 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 13174 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 13178 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 1317c x25: .cfa -4128 + ^
STACK CFI 132f8 x19: x19 x20: x20
STACK CFI 132fc x21: x21 x22: x22
STACK CFI 13300 x23: x23 x24: x24
STACK CFI 13304 x25: x25
STACK CFI 1331c x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 13320 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 13324 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 13328 x25: .cfa -4128 + ^
STACK CFI 1332c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 13330 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 13334 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 13338 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 1333c x25: .cfa -4128 + ^
STACK CFI INIT 13340 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 13344 .cfa: sp 2144 +
STACK CFI 13350 .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 13508 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1350c .cfa: sp 2144 + .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 13524 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 1352c x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 13544 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI 13548 x25: .cfa -2080 + ^
STACK CFI 136ec x19: x19 x20: x20
STACK CFI 136f0 x21: x21 x22: x22
STACK CFI 136f4 x23: x23 x24: x24
STACK CFI 136f8 x25: x25
STACK CFI 13714 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 13718 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 1371c x23: .cfa -2096 + ^ x24: .cfa -2088 + ^
STACK CFI 13720 x25: .cfa -2080 + ^
STACK CFI INIT 13728 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1372c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1373c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 13748 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 13758 v8: .cfa -32 + ^
STACK CFI 13760 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1379c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 137f4 x25: x25 x26: x26
STACK CFI 137fc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13800 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -32 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 13814 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 13818 bc .cfa: sp 0 + .ra: x30
STACK CFI 1381c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1382c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1383c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 13844 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 13878 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 138c0 x23: x23 x24: x24
STACK CFI 138d0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 138d8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 138dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 138e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 138f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1394c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13964 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13968 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 13988 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1398c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13994 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 139a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 139b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 139b8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 13a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13a50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 13aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13aa8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 13b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13b44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13b58 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 13b5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13b64 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13b70 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13b80 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13b88 x25: .cfa -16 + ^
STACK CFI 13c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13c64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 13d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13d28 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 13dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13dc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13e30 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 13e34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13e3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13e48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13e58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13e60 x25: .cfa -16 + ^
STACK CFI 13f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13f44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 14008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1400c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 140a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 140a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14118 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 1411c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14128 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14130 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14140 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 14148 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14150 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 144a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 144a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 14554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14558 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 145c0 464 .cfa: sp 0 + .ra: x30
STACK CFI 145c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 145cc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 145d8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 145e4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 145ec x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 145f4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1466c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14670 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 149b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 149bc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 14a28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14a38 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14a48 bc .cfa: sp 0 + .ra: x30
STACK CFI 14a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14a58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 14b08 428 .cfa: sp 0 + .ra: x30
STACK CFI 14b0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14b14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14b20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14b2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14d70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 14f30 478 .cfa: sp 0 + .ra: x30
STACK CFI 14f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14f3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14f48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14f54 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 151bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 151c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 153a8 184 .cfa: sp 0 + .ra: x30
STACK CFI 153ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 153b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 153c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 153f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 153f8 x25: .cfa -16 + ^
STACK CFI 15500 x23: x23 x24: x24
STACK CFI 15504 x25: x25
STACK CFI 15514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15518 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 15524 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 15528 x25: .cfa -16 + ^
STACK CFI INIT 15530 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 15534 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1553c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15550 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1557c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 15580 x25: .cfa -64 + ^
STACK CFI 155d0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 155d4 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 155d8 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 155e0 v14: .cfa -56 + ^
STACK CFI 1577c v8: v8 v9: v9
STACK CFI 15780 v10: v10 v11: v11
STACK CFI 15784 v12: v12 v13: v13
STACK CFI 15788 v14: v14
STACK CFI 157b8 x23: x23 x24: x24
STACK CFI 157bc x25: x25
STACK CFI 157cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 157d0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 157dc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 157e0 x25: .cfa -64 + ^
STACK CFI INIT 157e8 2ac .cfa: sp 0 + .ra: x30
STACK CFI 157ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 157f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15800 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15810 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1598c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15990 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15a98 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 15a9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15aa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15ab4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15ac0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 15c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15c34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15d50 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 15d54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15d5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15d6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15d78 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 15ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15edc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15ff0 14c .cfa: sp 0 + .ra: x30
STACK CFI 15ff4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 15ffc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16010 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1603c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16040 x25: .cfa -16 + ^
STACK CFI 16110 x23: x23 x24: x24
STACK CFI 16114 x25: x25
STACK CFI 16124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16128 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 16134 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16138 x25: .cfa -16 + ^
STACK CFI INIT 16140 184 .cfa: sp 0 + .ra: x30
STACK CFI 16144 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1614c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16158 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1617c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16184 x25: .cfa -16 + ^
STACK CFI 162c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 162c8 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 162cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 162d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 162e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16490 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 16494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1649c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 164ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1663c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16640 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 16644 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1664c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1665c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 167dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 167e0 190 .cfa: sp 0 + .ra: x30
STACK CFI 167e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 167ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 167fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1696c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16970 17c .cfa: sp 0 + .ra: x30
STACK CFI 16974 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1697c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1698c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16af0 164 .cfa: sp 0 + .ra: x30
STACK CFI 16af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16afc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16b0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16c58 150 .cfa: sp 0 + .ra: x30
STACK CFI 16c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16c64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16c74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 16da8 cdc .cfa: sp 0 + .ra: x30
STACK CFI 16dac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16dc4 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 16ddc v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 16e68 v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 16e88 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 17a60 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI INIT 17a88 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b40 190 .cfa: sp 0 + .ra: x30
STACK CFI 17b48 .cfa: sp 32 + v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 17b50 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI 17cb8 .cfa: sp 0 + v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI INIT 17cd0 4c .cfa: sp 0 + .ra: x30
STACK CFI 17cf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17d14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17d20 250 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17f70 174 .cfa: sp 0 + .ra: x30
STACK CFI INIT 180e8 10c .cfa: sp 0 + .ra: x30
STACK CFI 180ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 180f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18104 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18130 x23: .cfa -16 + ^
STACK CFI 181bc x21: x21 x22: x22
STACK CFI 181c0 x23: x23
STACK CFI 181cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 181d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 181d4 x21: x21 x22: x22
STACK CFI 181dc x23: x23
STACK CFI 181e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 181ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 181f8 bc .cfa: sp 0 + .ra: x30
STACK CFI 18240 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 182a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 182b8 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18320 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18330 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18340 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18350 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 18354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1835c x19: .cfa -16 + ^
STACK CFI 18364 v8: .cfa -8 + ^
STACK CFI 183cc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x29: x29
STACK CFI 183d0 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1841c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x29: x29
STACK CFI 18420 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 18470 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x29: x29
STACK CFI 18474 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18508 ac .cfa: sp 0 + .ra: x30
STACK CFI 1850c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18518 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18530 x21: .cfa -16 + ^
STACK CFI 18598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1859c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 185b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 185b8 bc .cfa: sp 0 + .ra: x30
STACK CFI 185bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 185c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 18678 58 .cfa: sp 0 + .ra: x30
STACK CFI 1867c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18684 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1868c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 186cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 186d0 110c .cfa: sp 0 + .ra: x30
STACK CFI 186d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 186f4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 18700 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 18708 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 18758 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 187f0 v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI 1880c v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 18828 v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 18844 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 18910 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 194c0 x27: x27 x28: x28
STACK CFI 194c4 v8: v8 v9: v9
STACK CFI 194c8 v10: v10 v11: v11
STACK CFI 194cc v12: v12 v13: v13
STACK CFI 194d0 v14: v14 v15: v15
STACK CFI 19500 x25: x25 x26: x26
STACK CFI 19514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 19518 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 19530 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 19568 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 1959c v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 195f0 v8: v8 v9: v9
STACK CFI 19624 x27: x27 x28: x28
STACK CFI 19648 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 19658 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 19698 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 196bc x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 19710 x27: x27 x28: x28
STACK CFI 19714 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 19738 x27: x27 x28: x28
STACK CFI 1973c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 19740 x27: x27 x28: x28
STACK CFI 19744 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 19774 x27: x27 x28: x28
STACK CFI 19778 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 197e0 994 .cfa: sp 0 + .ra: x30
STACK CFI 197e4 .cfa: sp 688 +
STACK CFI 197ec .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI 19800 x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 19820 x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI 19954 v8: .cfa -592 + ^
STACK CFI 19a2c v8: v8
STACK CFI 19a68 v8: .cfa -592 + ^
STACK CFI 19c50 v8: v8
STACK CFI 19d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19d54 .cfa: sp 688 + .ra: .cfa -680 + ^ v8: .cfa -592 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^ x29: .cfa -688 + ^
STACK CFI 1a088 v8: v8
STACK CFI 1a094 v8: .cfa -592 + ^
STACK CFI 1a0bc v8: v8
STACK CFI 1a0dc v8: .cfa -592 + ^
STACK CFI 1a13c v8: v8
STACK CFI 1a148 v8: .cfa -592 + ^
STACK CFI 1a16c v8: v8
STACK CFI 1a170 v8: .cfa -592 + ^
STACK CFI INIT 1a178 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 1a17c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1a184 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1a190 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1a1a0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1a1ac x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1a1c4 v8: .cfa -80 + ^
STACK CFI 1a440 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1a444 .cfa: sp 160 + .ra: .cfa -152 + ^ v8: .cfa -80 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1a448 274 .cfa: sp 0 + .ra: x30
STACK CFI 1a450 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1a45c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1a464 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1a474 v8: .cfa -48 + ^ v9: .cfa -40 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1a47c v12: .cfa -16 + ^
STACK CFI 1a604 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 1a68c v10: v10 v11: v11
STACK CFI 1a6ac .cfa: sp 0 + .ra: .ra v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1a6b0 .cfa: sp 128 + .ra: .cfa -120 + ^ v12: .cfa -16 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a6c0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a6cc x21: .cfa -16 + ^
STACK CFI 1a6e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a714 x19: x19 x20: x20
STACK CFI 1a724 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 1a728 5c .cfa: sp 0 + .ra: x30
STACK CFI 1a72c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a734 x19: .cfa -16 + ^
STACK CFI 1a748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a74c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a780 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a788 10 .cfa: sp 0 + .ra: x30
