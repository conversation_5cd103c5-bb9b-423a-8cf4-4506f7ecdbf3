MODULE Linux arm64 32B9ED77C8EFC1A0264AC95C34E2C6D70 libbrotlidec.so.1
INFO CODE_ID 77EDB932EFC8A0C1264AC95C34E2C6D7A9E54019
PUBLIC 4fc8 0 BrotliDecoderSetParameter
PUBLIC 5028 0 BrotliDecoderCreateInstance
PUBLIC 5100 0 BrotliDecoderDestroyInstance
PUBLIC 5148 0 BrotliDecoderDecompressStream
PUBLIC 6048 0 BrotliDecoderDecompress
PUBLIC 6120 0 BrotliDecoderHasMoreOutput
PUBLIC 6160 0 BrotliDecoderTakeOutput
PUBLIC 6228 0 BrotliDecoderIsUsed
PUBLIC 6248 0 BrotliDecoderIsFinished
PUBLIC 6278 0 BrotliDecoderGetErrorCode
PUBLIC 6280 0 BrotliDecoderErrorString
PUBLIC 6410 0 BrotliDecoderVersion
STACK CFI INIT bc8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT bf8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT c38 48 .cfa: sp 0 + .ra: x30
STACK CFI c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c44 x19: .cfa -16 + ^
STACK CFI c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c98 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT cf0 554 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1248 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1300 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1370 16c .cfa: sp 0 + .ra: x30
STACK CFI 1374 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 137c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1388 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13a8 x25: .cfa -16 + ^
STACK CFI 13b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1464 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1494 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14e0 4c .cfa: sp 0 + .ra: x30
STACK CFI 14e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14f0 x19: .cfa -16 + ^
STACK CFI 1504 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1508 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1528 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1530 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1534 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 153c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15f8 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 15fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1604 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1614 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 161c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1634 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1640 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1778 x23: x23 x24: x24
STACK CFI 177c x25: x25 x26: x26
STACK CFI 178c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 1790 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 17b4 x23: x23 x24: x24
STACK CFI 17b8 x25: x25 x26: x26
STACK CFI 17c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 17c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 17d0 a8c .cfa: sp 0 + .ra: x30
STACK CFI 17d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 17dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 17e8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 17fc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1920 x23: x23 x24: x24
STACK CFI 1924 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1ac8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1adc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1ccc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ce4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1cec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1d04 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d08 x23: x23 x24: x24
STACK CFI 1d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d1c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 1d28 x23: x23 x24: x24
STACK CFI 1d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d40 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1d68 x23: x23 x24: x24
STACK CFI 1d6c x25: x25 x26: x26
STACK CFI 1d70 x27: x27 x28: x28
STACK CFI 1d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d8c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1dc8 x25: x25 x26: x26
STACK CFI 1dcc x27: x27 x28: x28
STACK CFI 1dd8 x23: x23 x24: x24
STACK CFI 1e5c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1ef8 x23: x23 x24: x24
STACK CFI 1f0c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1f38 x23: x23 x24: x24
STACK CFI 1f40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1f48 x23: x23 x24: x24
STACK CFI 1f54 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 20f4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 20fc x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2130 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2140 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2148 x23: x23 x24: x24
STACK CFI 214c x25: x25 x26: x26
STACK CFI 2150 x27: x27 x28: x28
STACK CFI 2154 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2170 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2178 x23: x23 x24: x24
STACK CFI 217c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 218c x23: x23 x24: x24
STACK CFI 2190 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2194 x23: x23 x24: x24
STACK CFI 2198 x25: x25 x26: x26
STACK CFI 219c x27: x27 x28: x28
STACK CFI 21a0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 21fc x25: x25 x26: x26
STACK CFI 2200 x27: x27 x28: x28
STACK CFI 220c x23: x23 x24: x24
STACK CFI 2210 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2224 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 222c x23: x23 x24: x24
STACK CFI 2238 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2244 x23: x23 x24: x24
STACK CFI 2248 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2250 x23: x23 x24: x24
STACK CFI 2254 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 2260 208 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2468 1c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2628 1d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27f8 a4c .cfa: sp 0 + .ra: x30
STACK CFI 27fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2804 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2810 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 28bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3248 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3260 c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3328 39c .cfa: sp 0 + .ra: x30
STACK CFI 332c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3378 x19: .cfa -32 + ^
STACK CFI 34c4 x19: x19
STACK CFI 352c x19: .cfa -32 + ^
STACK CFI 3534 x19: x19
STACK CFI 3560 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3564 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3640 x19: x19
STACK CFI 3644 x19: .cfa -32 + ^
STACK CFI 36bc x19: x19
STACK CFI 36c0 x19: .cfa -32 + ^
STACK CFI INIT 36c8 350 .cfa: sp 0 + .ra: x30
STACK CFI 36cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3714 x19: .cfa -32 + ^
STACK CFI 3868 x19: x19
STACK CFI 3880 x19: .cfa -32 + ^
STACK CFI 3888 x19: x19
STACK CFI 38b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 38b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3998 x19: x19
STACK CFI 399c x19: .cfa -32 + ^
STACK CFI 3a10 x19: x19
STACK CFI 3a14 x19: .cfa -32 + ^
STACK CFI INIT 3a18 364 .cfa: sp 0 + .ra: x30
STACK CFI 3a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a64 x19: .cfa -32 + ^
STACK CFI 3be0 x19: x19
STACK CFI 3be4 x19: .cfa -32 + ^
STACK CFI 3bec x19: x19
STACK CFI 3c18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3cfc x19: x19
STACK CFI 3d00 x19: .cfa -32 + ^
STACK CFI 3d74 x19: x19
STACK CFI 3d78 x19: .cfa -32 + ^
STACK CFI INIT 3d80 ba0 .cfa: sp 0 + .ra: x30
STACK CFI 3d84 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d8c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3d98 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 3e60 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4040 x23: x23 x24: x24
STACK CFI 4174 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 418c x23: x23 x24: x24
STACK CFI 4190 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4194 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4198 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 428c x23: x23 x24: x24
STACK CFI 4290 x25: x25 x26: x26
STACK CFI 4294 x27: x27 x28: x28
STACK CFI 4298 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 42ec x23: x23 x24: x24
STACK CFI 42f0 x25: x25 x26: x26
STACK CFI 42f4 x27: x27 x28: x28
STACK CFI 42fc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4300 x23: x23 x24: x24
STACK CFI 4314 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4470 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 44a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 44dc x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 46f8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4720 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4728 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4730 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4770 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4774 x23: x23 x24: x24
STACK CFI 478c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4798 x23: x23 x24: x24
STACK CFI 47d8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 47f0 x23: x23 x24: x24
STACK CFI 4884 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 488c x23: x23 x24: x24
STACK CFI 4890 x25: x25 x26: x26
STACK CFI 4894 x27: x27 x28: x28
STACK CFI 48ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 48b4 x23: x23 x24: x24
STACK CFI 48cc x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 48d8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 48e0 x23: x23 x24: x24
STACK CFI 4914 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4918 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 491c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 4920 1d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4af8 4cc .cfa: sp 0 + .ra: x30
STACK CFI 4afc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4b14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4be0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4fc8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5028 d8 .cfa: sp 0 + .ra: x30
STACK CFI 502c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5034 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 503c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5048 x23: .cfa -16 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 50a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 50c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 50cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5100 44 .cfa: sp 0 + .ra: x30
STACK CFI 5108 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5110 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 511c x21: .cfa -16 + ^
STACK CFI 513c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5148 f00 .cfa: sp 0 + .ra: x30
STACK CFI 514c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5154 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5164 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5180 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 518c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 525c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6048 d8 .cfa: sp 0 + .ra: x30
STACK CFI 6050 .cfa: sp 5248 +
STACK CFI 6064 .ra: .cfa -5240 + ^ x29: .cfa -5248 + ^
STACK CFI 6070 x19: .cfa -5232 + ^ x20: .cfa -5224 + ^
STACK CFI 6098 x21: .cfa -5216 + ^
STACK CFI 6118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 611c .cfa: sp 5248 + .ra: .cfa -5240 + ^ x19: .cfa -5232 + ^ x20: .cfa -5224 + ^ x21: .cfa -5216 + ^ x29: .cfa -5248 + ^
STACK CFI INIT 6120 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6160 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6164 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6170 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 617c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6224 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6228 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6248 30 .cfa: sp 0 + .ra: x30
STACK CFI 6260 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6274 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6278 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6280 18c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6410 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6420 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 6424 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 65dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 65e0 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6608 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 660c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6614 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 661c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 662c x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6778 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6814 x27: x27 x28: x28
STACK CFI 682c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6830 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 68c4 x27: x27 x28: x28
STACK CFI INIT 68d8 228 .cfa: sp 0 + .ra: x30
STACK CFI 68dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 68ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 68f4 x21: .cfa -16 + ^
STACK CFI 69d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 69d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b00 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b0c x19: .cfa -16 + ^
STACK CFI 6bd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6bf8 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6c48 7c .cfa: sp 0 + .ra: x30
STACK CFI 6c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c54 x19: .cfa -16 + ^
STACK CFI 6cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cc8 40 .cfa: sp 0 + .ra: x30
STACK CFI 6ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cd4 x19: .cfa -16 + ^
STACK CFI 6d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d08 70 .cfa: sp 0 + .ra: x30
STACK CFI 6d0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d3c x23: .cfa -16 + ^
STACK CFI 6d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
