MODULE Linux arm64 5FFE6184A53A741FD5E15280816A7FAC0 libspeexdsp.so.1
INFO CODE_ID 8461FE5F3AA51F74D5E15280816A7FAC4EA2C3D1
PUBLIC 1fd0 0 speex_preprocess_state_init
PUBLIC 2618 0 speex_preprocess_state_destroy
PUBLIC 26f8 0 speex_preprocess_run
PUBLIC 3590 0 speex_preprocess
PUBLIC 3598 0 speex_preprocess_estimate_update
PUBLIC 3700 0 speex_preprocess_ctl
PUBLIC 4138 0 jitter_buffer_reset
PUBLIC 41f0 0 jitter_buffer_destroy
PUBLIC 4218 0 jitter_buffer_put
PUBLIC 4498 0 jitter_buffer_get
PUBLIC 49f8 0 jitter_buffer_get_another
PUBLIC 4af8 0 jitter_buffer_update_delay
PUBLIC 4b00 0 jitter_buffer_get_pointer_timestamp
PUBLIC 4b08 0 jitter_buffer_tick
PUBLIC 4b90 0 jitter_buffer_remaining_span
PUBLIC 4c18 0 jitter_buffer_ctl
PUBLIC 4de8 0 jitter_buffer_init
PUBLIC 4e80 0 speex_echo_state_init_mc
PUBLIC 5358 0 speex_echo_state_init
PUBLIC 5368 0 speex_echo_state_reset
PUBLIC 5558 0 speex_echo_state_destroy
PUBLIC 5650 0 speex_echo_playback
PUBLIC 57a8 0 speex_echo_cancellation
PUBLIC 7258 0 speex_echo_capture
PUBLIC 7398 0 speex_echo_cancel
PUBLIC 74d0 0 speex_echo_ctl
PUBLIC 8e88 0 speex_resampler_destroy
PUBLIC 8ed0 0 speex_resampler_process_float
PUBLIC 9088 0 speex_resampler_process_int
PUBLIC 93a0 0 speex_resampler_process_interleaved_float
PUBLIC 9478 0 speex_resampler_process_interleaved_int
PUBLIC 9550 0 speex_resampler_get_rate
PUBLIC 9568 0 speex_resampler_set_rate_frac
PUBLIC 9680 0 speex_resampler_set_rate
PUBLIC 9690 0 speex_resampler_get_ratio
PUBLIC 96a8 0 speex_resampler_set_quality
PUBLIC 96f0 0 speex_resampler_init_frac
PUBLIC 9878 0 speex_resampler_init
PUBLIC 9890 0 speex_resampler_get_quality
PUBLIC 98a0 0 speex_resampler_set_input_stride
PUBLIC 98a8 0 speex_resampler_get_input_stride
PUBLIC 98b8 0 speex_resampler_set_output_stride
PUBLIC 98c0 0 speex_resampler_get_output_stride
PUBLIC 98d0 0 speex_resampler_get_input_latency
PUBLIC 98e0 0 speex_resampler_get_output_latency
PUBLIC 9900 0 speex_resampler_skip_zeros
PUBLIC 9938 0 speex_resampler_reset_mem
PUBLIC 9968 0 speex_resampler_strerror
PUBLIC 99d8 0 speex_buffer_init
PUBLIC 9a28 0 speex_buffer_destroy
PUBLIC 9a50 0 speex_buffer_write
PUBLIC 9b28 0 speex_buffer_writezeros
PUBLIC 9be0 0 speex_buffer_read
PUBLIC 9cc8 0 speex_buffer_get_available
PUBLIC 9cd0 0 speex_buffer_resize
PUBLIC 9d08 0 speex_decorrelate_new
PUBLIC 9ec0 0 speex_decorrelate
PUBLIC a378 0 speex_decorrelate_destroy
STACK CFI INIT 1b78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ba8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be8 48 .cfa: sp 0 + .ra: x30
STACK CFI 1bec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bf4 x19: .cfa -16 + ^
STACK CFI 1c2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c38 23c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e78 158 .cfa: sp 0 + .ra: x30
STACK CFI 1e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e88 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ea0 x21: .cfa -16 + ^
STACK CFI 1fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1fc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1fd0 644 .cfa: sp 0 + .ra: x30
STACK CFI 1fd4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1fdc x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1fec x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2004 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 2024 v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^ v14: .cfa -32 + ^ v15: .cfa -24 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI 25c8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 25cc .cfa: sp 176 + .ra: .cfa -168 + ^ v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^ v14: .cfa -32 + ^ v15: .cfa -24 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2618 e0 .cfa: sp 0 + .ra: x30
STACK CFI 261c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2624 x19: .cfa -16 + ^
STACK CFI 26f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 26f8 e98 .cfa: sp 0 + .ra: x30
STACK CFI 26fc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 270c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2714 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2724 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2750 v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v13: .cfa -88 + ^ v14: .cfa -80 + ^ v15: .cfa -72 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 328c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3290 .cfa: sp 224 + .ra: .cfa -216 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v13: .cfa -88 + ^ v14: .cfa -80 + ^ v15: .cfa -72 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 3590 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3598 168 .cfa: sp 0 + .ra: x30
STACK CFI 359c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 35a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 35b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 36fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3700 550 .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3740 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3748 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37a0 x19: x19 x20: x20
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 37a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37ac x19: x19 x20: x20
STACK CFI 37b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37c0 x19: x19 x20: x20
STACK CFI 37c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37fc x19: x19 x20: x20
STACK CFI 3800 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3858 x19: x19 x20: x20
STACK CFI 385c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3890 x19: x19 x20: x20
STACK CFI 3894 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38c8 x19: x19 x20: x20
STACK CFI 38cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38dc x19: x19 x20: x20
STACK CFI 38e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38f0 x19: x19 x20: x20
STACK CFI 38f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 390c x19: x19 x20: x20
STACK CFI 3910 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3950 x19: x19 x20: x20
STACK CFI 3954 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 398c x19: x19 x20: x20
STACK CFI 3990 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39a0 x19: x19 x20: x20
STACK CFI 39a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39b4 x19: x19 x20: x20
STACK CFI 39b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39cc x19: x19 x20: x20
STACK CFI 39d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a18 x19: x19 x20: x20
STACK CFI 3a1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a40 x19: x19 x20: x20
STACK CFI 3a44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a54 x19: x19 x20: x20
STACK CFI 3a58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3aa0 x19: x19 x20: x20
STACK CFI 3aa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3aec x19: x19 x20: x20
STACK CFI 3af0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b40 x19: x19 x20: x20
STACK CFI 3b44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b54 x19: x19 x20: x20
STACK CFI 3b58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b64 x19: x19 x20: x20
STACK CFI 3b68 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b78 x19: x19 x20: x20
STACK CFI 3b7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b94 x19: x19 x20: x20
STACK CFI 3b98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ba8 x19: x19 x20: x20
STACK CFI 3bac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bc4 x19: x19 x20: x20
STACK CFI 3bc8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bd8 x19: x19 x20: x20
STACK CFI 3bdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bec x19: x19 x20: x20
STACK CFI 3bf0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c1c x19: x19 x20: x20
STACK CFI 3c20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c44 x19: x19 x20: x20
STACK CFI 3c48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3c50 1ec .cfa: sp 0 + .ra: x30
STACK CFI 3c74 .cfa: sp 16 +
STACK CFI 3e1c .cfa: sp 0 +
STACK CFI 3e20 .cfa: sp 16 +
STACK CFI 3e34 .cfa: sp 0 +
STACK CFI INIT 3e40 110 .cfa: sp 0 + .ra: x30
STACK CFI 3e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e4c x19: .cfa -16 + ^
STACK CFI 3ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3f4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f50 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f60 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f94 x23: .cfa -16 + ^
STACK CFI 4040 x23: x23
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 410c x23: x23
STACK CFI 411c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4120 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4128 x23: x23
STACK CFI 412c x23: .cfa -16 + ^
STACK CFI 4130 x23: x23
STACK CFI INIT 4138 b8 .cfa: sp 0 + .ra: x30
STACK CFI 413c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4148 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4154 x21: .cfa -16 + ^
STACK CFI 41e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 41e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 41f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 41f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41fc x19: .cfa -16 + ^
STACK CFI 4210 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4218 280 .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4224 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4230 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 436c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4370 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 43a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 441c x23: .cfa -16 + ^
STACK CFI 4468 x23: x23
STACK CFI INIT 4498 55c .cfa: sp 0 + .ra: x30
STACK CFI 449c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 457c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 45dc x25: .cfa -16 + ^
STACK CFI 4664 x25: x25
STACK CFI 4680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4684 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 46dc x25: .cfa -16 + ^
STACK CFI 47a4 x25: x25
STACK CFI 47c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 47c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 47dc x25: x25
STACK CFI 47e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 47e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 492c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4930 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 49c8 x25: .cfa -16 + ^
STACK CFI INIT 49f8 100 .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a14 x21: .cfa -16 + ^
STACK CFI 4a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4af8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b08 84 .cfa: sp 0 + .ra: x30
STACK CFI 4b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b18 x19: .cfa -16 + ^
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b90 84 .cfa: sp 0 + .ra: x30
STACK CFI 4b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4c18 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 4d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4da8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4de8 94 .cfa: sp 0 + .ra: x30
STACK CFI 4dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4df8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4e80 4d8 .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4e8c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4e98 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4eb0 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4ebc x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5128 v10: .cfa -32 + ^
STACK CFI 5130 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 516c v8: v8 v9: v9
STACK CFI 5170 v10: v10
STACK CFI 5348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 534c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 5358 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5368 1f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5558 f8 .cfa: sp 0 + .ra: x30
STACK CFI 555c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5564 x19: .cfa -16 + ^
STACK CFI 564c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5650 158 .cfa: sp 0 + .ra: x30
STACK CFI 5654 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 565c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5728 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 579c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 57a8 1ab0 .cfa: sp 0 + .ra: x30
STACK CFI 57ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 57b8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 57c8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 57ec v10: .cfa -96 + ^ v11: .cfa -88 + ^ v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 6dc8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6dcc .cfa: sp 208 + .ra: .cfa -200 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 7044 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7048 .cfa: sp 208 + .ra: .cfa -200 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 7258 13c .cfa: sp 0 + .ra: x30
STACK CFI 725c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7264 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7270 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 72e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 72ec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7360 x23: x23 x24: x24
STACK CFI 7364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7368 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7398 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73a0 130 .cfa: sp 0 + .ra: x30
STACK CFI 73a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 73ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 73bc x21: .cfa -16 + ^
STACK CFI 74c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 74c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 74d0 218 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 74e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 74e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7508 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7514 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7524 v8: .cfa -16 + ^
STACK CFI 7594 x23: x23 x24: x24
STACK CFI 759c x25: x25 x26: x26
STACK CFI 75a0 v8: v8
STACK CFI 75ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 75b0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 765c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7698 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 76b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 76cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 76e8 38 .cfa: sp 0 + .ra: x30
STACK CFI 76ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76f8 x19: .cfa -16 + ^
STACK CFI 771c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7720 24 .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 772c x19: .cfa -16 + ^
STACK CFI 7740 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7748 e4 .cfa: sp 0 + .ra: x30
STACK CFI 774c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7758 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7764 v8: .cfa -8 + ^
STACK CFI 776c x21: .cfa -16 + ^
STACK CFI 77c0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 77c4 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7828 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7830 90 .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7840 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7888 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 78bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 78c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78d0 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 78d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 78e4 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 78f4 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 78fc v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 790c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7920 v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI 7938 x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 7944 v10: .cfa -80 + ^ v11: .cfa -72 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 7ba4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7ba8 .cfa: sp 192 + .ra: .cfa -184 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7bb8 48 .cfa: sp 0 + .ra: x30
STACK CFI 7bbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7bc4 x19: .cfa -16 + ^
STACK CFI 7bfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c00 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c78 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cc8 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d80 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7dd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7dd4 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 7e6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7e70 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ef0 118 .cfa: sp 0 + .ra: x30
STACK CFI 7ef4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ff4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8008 13c .cfa: sp 0 + .ra: x30
STACK CFI 800c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8148 18c .cfa: sp 0 + .ra: x30
STACK CFI 814c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8168 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8170 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8178 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 82c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 82c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 82d8 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 82dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 82f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8300 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8308 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8488 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8498 c8 .cfa: sp 0 + .ra: x30
STACK CFI 849c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 84a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 84b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 84c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 855c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8560 c0 .cfa: sp 0 + .ra: x30
STACK CFI 8564 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8570 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8578 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8580 x25: .cfa -48 + ^
STACK CFI 8590 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 861c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 8620 158 .cfa: sp 0 + .ra: x30
STACK CFI 8628 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 863c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 864c v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI 8658 v12: .cfa -40 + ^
STACK CFI 8678 x19: .cfa -48 + ^
STACK CFI 86c0 x19: x19
STACK CFI 8744 v10: v10 v11: v11
STACK CFI 874c v12: v12
STACK CFI 8760 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 8764 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8768 v12: v12
STACK CFI 8770 v10: v10 v11: v11
STACK CFI 8774 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI INIT 8778 710 .cfa: sp 0 + .ra: x30
STACK CFI 877c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8788 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8798 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 87c8 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 889c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 88c4 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 893c x25: x25 x26: x26
STACK CFI 8940 v8: v8 v9: v9
STACK CFI 8b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8b0c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8bb0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 8c08 v8: v8 v9: v9
STACK CFI 8c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8ca0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8d88 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8e88 48 .cfa: sp 0 + .ra: x30
STACK CFI 8e8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e94 x19: .cfa -16 + ^
STACK CFI 8ecc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ed0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 8ed4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8edc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8ef0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8efc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8f0c x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 900c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9010 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9088 314 .cfa: sp 0 + .ra: x30
STACK CFI 908c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 9098 .cfa: x29 208 +
STACK CFI 909c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 90a4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 90ac x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 90dc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 9108 v8: .cfa -112 + ^ v9: .cfa -104 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9358 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 935c .cfa: x29 208 + .ra: .cfa -200 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 93a0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 93a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 93ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 93b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 93c4 x27: .cfa -16 + ^
STACK CFI 93d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 93e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9454 x21: x21 x22: x22
STACK CFI 9458 x23: x23 x24: x24
STACK CFI 9470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 9478 d4 .cfa: sp 0 + .ra: x30
STACK CFI 947c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9484 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9490 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 949c x27: .cfa -16 + ^
STACK CFI 94b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 94bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 952c x21: x21 x22: x22
STACK CFI 9530 x23: x23 x24: x24
STACK CFI 9548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 9550 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9568 118 .cfa: sp 0 + .ra: x30
STACK CFI 966c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 967c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9680 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9690 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 96d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 96e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 96f0 188 .cfa: sp 0 + .ra: x30
STACK CFI 96f4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9704 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9720 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 972c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9734 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9814 x23: x23 x24: x24
STACK CFI 9818 x25: x25 x26: x26
STACK CFI 981c x27: x27 x28: x28
STACK CFI 9830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9834 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 9844 x23: x23 x24: x24
STACK CFI 9848 x25: x25 x26: x26
STACK CFI 984c x27: x27 x28: x28
STACK CFI 9850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9854 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 9874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9878 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9890 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 98a8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 98c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 98e0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9900 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9938 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9968 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 99d8 4c .cfa: sp 0 + .ra: x30
STACK CFI 99dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 99e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9a28 28 .cfa: sp 0 + .ra: x30
STACK CFI 9a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9a34 x19: .cfa -16 + ^
STACK CFI 9a4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9a50 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9a6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a74 x23: .cfa -16 + ^
STACK CFI 9afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9b00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9b28 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9b2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9b38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9b58 x21: .cfa -16 + ^
STACK CFI 9bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9be0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 9be4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9bec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9c08 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9c7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9cc8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9cd0 38 .cfa: sp 0 + .ra: x30
STACK CFI 9cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ce0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9d08 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 9d0c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9d14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9d1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9d28 x23: .cfa -32 + ^
STACK CFI 9dfc v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 9e0c v10: .cfa -24 + ^
STACK CFI 9e4c v8: v8 v9: v9
STACK CFI 9e50 v10: v10
STACK CFI 9ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9ec0 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 9ef4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 9f00 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 9f10 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 9f1c v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 9f28 v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 9f34 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 9f3c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 9f5c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 9f64 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 9f78 v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI a314 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a318 .cfa: sp 192 + .ra: .cfa -184 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI a34c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a350 .cfa: sp 192 + .ra: .cfa -184 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT a378 58 .cfa: sp 0 + .ra: x30
STACK CFI a37c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a384 x19: .cfa -16 + ^
STACK CFI a3cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a3d0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI a440 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a450 x21: .cfa -16 + ^
STACK CFI a47c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a53c x19: x19 x20: x20
STACK CFI a544 x21: x21
STACK CFI a58c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a594 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a598 x19: x19 x20: x20
STACK CFI a59c x21: x21
STACK CFI a5a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a5a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a5bc x21: .cfa -16 + ^
STACK CFI a5c0 x21: x21
STACK CFI INIT a5c8 3a0 .cfa: sp 0 + .ra: x30
STACK CFI a5cc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a5d8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a5e4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a720 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a730 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a85c x19: x19 x20: x20
STACK CFI a864 x25: x25 x26: x26
STACK CFI a91c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI a920 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a924 x19: x19 x20: x20
STACK CFI a930 x25: x25 x26: x26
STACK CFI a938 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI a93c .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT a968 1c0 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab28 230 .cfa: sp 0 + .ra: x30
STACK CFI ab2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI abe8 x21: .cfa -16 + ^
STACK CFI ad40 x19: x19 x20: x20
STACK CFI ad44 x21: x21
STACK CFI ad48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ad4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ad50 x19: x19 x20: x20
STACK CFI ad54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad58 3a0 .cfa: sp 0 + .ra: x30
STACK CFI ad5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ad68 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI ad88 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ae3c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ae68 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ae78 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI afec x19: x19 x20: x20
STACK CFI aff0 x21: x21 x22: x22
STACK CFI aff4 x25: x25 x26: x26
STACK CFI b0b4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI b0b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI b0bc x19: x19 x20: x20
STACK CFI b0c0 x21: x21 x22: x22
STACK CFI b0c8 x25: x25 x26: x26
STACK CFI b0d0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI b0d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT b0f8 ac4 .cfa: sp 0 + .ra: x30
STACK CFI b10c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b118 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b124 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b130 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b13c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b14c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b82c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI bb88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bb8c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT bbc0 a14 .cfa: sp 0 + .ra: x30
STACK CFI bbd4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI bbe0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI bbec x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI bbf8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI bc04 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI bc10 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c1d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c1d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI c4e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c4e8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI c5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT c5d8 1e4 .cfa: sp 0 + .ra: x30
STACK CFI c5dc .cfa: sp 112 +
STACK CFI c5e0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c5e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c5f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c600 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c614 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c644 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c730 x19: x19 x20: x20
STACK CFI c734 x23: x23 x24: x24
STACK CFI c738 x25: x25 x26: x26
STACK CFI c73c x27: x27 x28: x28
STACK CFI c748 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c74c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI c7b0 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI c7b4 x23: x23 x24: x24
STACK CFI c7b8 x27: x27 x28: x28
STACK CFI INIT c7c0 228 .cfa: sp 0 + .ra: x30
STACK CFI c7c4 .cfa: sp 128 +
STACK CFI c7c8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c7d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI c7e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c7e4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI c7ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c81c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c928 x19: x19 x20: x20
STACK CFI c92c x21: x21 x22: x22
STACK CFI c930 x25: x25 x26: x26
STACK CFI c934 x27: x27 x28: x28
STACK CFI c940 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI c944 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT c9e8 270 .cfa: sp 0 + .ra: x30
STACK CFI c9ec .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI c9f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI ca14 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI ca48 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI ca60 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI ca64 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ca68 v8: .cfa -128 + ^ v9: .cfa -120 + ^
STACK CFI ca6c v10: .cfa -112 + ^ v11: .cfa -104 + ^
STACK CFI ca70 v12: .cfa -96 + ^
STACK CFI cc1c x21: x21 x22: x22
STACK CFI cc20 x25: x25 x26: x26
STACK CFI cc24 x27: x27 x28: x28
STACK CFI cc28 v8: v8 v9: v9
STACK CFI cc2c v10: v10 v11: v11
STACK CFI cc30 v12: v12
STACK CFI cc3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI cc40 .cfa: sp 224 + .ra: .cfa -216 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT cc58 44 .cfa: sp 0 + .ra: x30
STACK CFI cc60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cc68 x19: .cfa -16 + ^
STACK CFI cc88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI cc8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI cc94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
