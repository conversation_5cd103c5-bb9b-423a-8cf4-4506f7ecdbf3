MODULE Linux arm64 4514751D3E4AA70746EE55038084585E0 libopencv_fuzzy.so.4.3
INFO CODE_ID 1D7514454A3E07A746EE55038084585E61F570FA
PUBLIC 2480 0 _init
PUBLIC 2920 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.53]
PUBLIC 29c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.31]
PUBLIC 2a60 0 call_weak_fn
PUBLIC 2a78 0 deregister_tm_clones
PUBLIC 2ab0 0 register_tm_clones
PUBLIC 2af0 0 __do_global_dtors_aux
PUBLIC 2b38 0 frame_dummy
PUBLIC 2b70 0 cv::_InputArray::getMat(int) const
PUBLIC 2c68 0 cv::Mat::~Mat()
PUBLIC 2d00 0 cv::Mat::operator=(cv::Mat&&)
PUBLIC 2e30 0 cv::MatExpr::~MatExpr()
PUBLIC 2fe0 0 cv::ft::FT02D_components(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC 3ee0 0 cv::ft::FT02D_inverseFT(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, int, int)
PUBLIC 4650 0 cv::ft::FT02D_process(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC 5660 0 cv::ft::FT02D_iteration(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_OutputArray const&, bool)
PUBLIC 7088 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 7148 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 7490 0 cv::ft::FT02D_FL_process_float(cv::_InputArray const&, int, cv::_OutputArray const&)
PUBLIC 82b0 0 cv::ft::FT02D_FL_process(cv::_InputArray const&, int, cv::_OutputArray const&)
PUBLIC 9140 0 cv::ft::FT12D_inverseFT(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, int, int)
PUBLIC 9960 0 cv::ft::FT12D_createPolynomMatrixHorizontal(int, cv::_OutputArray const&, int)
PUBLIC 9e60 0 cv::ft::FT12D_createPolynomMatrixVertical(int, cv::_OutputArray const&, int)
PUBLIC a360 0 cv::ft::FT12D_polynomial(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC cb50 0 cv::ft::FT12D_components(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC cdf0 0 cv::ft::FT12D_process(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC edd0 0 cv::ft::filter(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC f0f0 0 cv::ft::createKernel(int, int, cv::_OutputArray const&, int)
PUBLIC f540 0 cv::ft::inpaint(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, int, int, int)
PUBLIC 10500 0 cv::ft::createKernel(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, int)
PUBLIC 10bf0 0 _fini
STACK CFI INIT 2920 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2924 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2930 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 29b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 29b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2b70 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2b74 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b80 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2bb8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2c48 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2c68 90 .cfa: sp 0 + .ra: x30
STACK CFI 2c6c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2ce0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2ce8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2cf4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2d00 120 .cfa: sp 0 + .ra: x30
STACK CFI 2d04 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d10 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2e00 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 2e30 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2e34 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e40 .ra: .cfa -16 + ^
STACK CFI 2f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2fa0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2fe0 ed8 .cfa: sp 0 + .ra: x30
STACK CFI 2fe4 .cfa: sp 1632 +
STACK CFI 2fe8 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^
STACK CFI 2ff0 x21: .cfa -1616 + ^ x22: .cfa -1608 + ^
STACK CFI 2ff8 x23: .cfa -1600 + ^ x24: .cfa -1592 + ^
STACK CFI 3000 x27: .cfa -1568 + ^ x28: .cfa -1560 + ^
STACK CFI 3014 .ra: .cfa -1552 + ^ v8: .cfa -1544 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^
STACK CFI 3bb4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3bb8 .cfa: sp 1632 + .ra: .cfa -1552 + ^ v8: .cfa -1544 + ^ x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x21: .cfa -1616 + ^ x22: .cfa -1608 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^ x27: .cfa -1568 + ^ x28: .cfa -1560 + ^
STACK CFI INIT 3ee0 75c .cfa: sp 0 + .ra: x30
STACK CFI 3ee4 .cfa: sp 672 +
STACK CFI 3ee8 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI 3ef0 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 3f08 .ra: .cfa -592 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI 4518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 451c .cfa: sp 672 + .ra: .cfa -592 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI INIT 4650 fe8 .cfa: sp 0 + .ra: x30
STACK CFI 4654 .cfa: sp 1632 +
STACK CFI 4658 x19: .cfa -1632 + ^ x20: .cfa -1624 + ^
STACK CFI 4660 x23: .cfa -1600 + ^ x24: .cfa -1592 + ^
STACK CFI 4680 .ra: .cfa -1552 + ^ v8: .cfa -1544 + ^ x21: .cfa -1616 + ^ x22: .cfa -1608 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^ x27: .cfa -1568 + ^ x28: .cfa -1560 + ^
STACK CFI 53b0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 53b4 .cfa: sp 1632 + .ra: .cfa -1552 + ^ v8: .cfa -1544 + ^ x19: .cfa -1632 + ^ x20: .cfa -1624 + ^ x21: .cfa -1616 + ^ x22: .cfa -1608 + ^ x23: .cfa -1600 + ^ x24: .cfa -1592 + ^ x25: .cfa -1584 + ^ x26: .cfa -1576 + ^ x27: .cfa -1568 + ^ x28: .cfa -1560 + ^
STACK CFI INIT 5660 1a08 .cfa: sp 0 + .ra: x30
STACK CFI 5664 .cfa: sp 1664 +
STACK CFI 5668 x23: .cfa -1632 + ^ x24: .cfa -1624 + ^
STACK CFI 5678 v8: .cfa -1568 + ^ v9: .cfa -1560 + ^ x19: .cfa -1664 + ^ x20: .cfa -1656 + ^
STACK CFI 5698 .ra: .cfa -1584 + ^ x21: .cfa -1648 + ^ x22: .cfa -1640 + ^ x25: .cfa -1616 + ^ x26: .cfa -1608 + ^ x27: .cfa -1600 + ^ x28: .cfa -1592 + ^
STACK CFI 6e84 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6e88 .cfa: sp 1664 + .ra: .cfa -1584 + ^ v8: .cfa -1568 + ^ v9: .cfa -1560 + ^ x19: .cfa -1664 + ^ x20: .cfa -1656 + ^ x21: .cfa -1648 + ^ x22: .cfa -1640 + ^ x23: .cfa -1632 + ^ x24: .cfa -1624 + ^ x25: .cfa -1616 + ^ x26: .cfa -1608 + ^ x27: .cfa -1600 + ^ x28: .cfa -1592 + ^
STACK CFI INIT 7088 bc .cfa: sp 0 + .ra: x30
STACK CFI 708c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7090 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 7134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7138 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 7148 344 .cfa: sp 0 + .ra: x30
STACK CFI 714c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7158 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7168 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 73c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 73c8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7490 df4 .cfa: sp 0 + .ra: x30
STACK CFI 7494 .cfa: sp 928 +
STACK CFI 7498 x23: .cfa -896 + ^ x24: .cfa -888 + ^
STACK CFI 74b4 .ra: .cfa -848 + ^ x19: .cfa -928 + ^ x20: .cfa -920 + ^ x21: .cfa -912 + ^ x22: .cfa -904 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^
STACK CFI 80bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 80c0 .cfa: sp 928 + .ra: .cfa -848 + ^ x19: .cfa -928 + ^ x20: .cfa -920 + ^ x21: .cfa -912 + ^ x22: .cfa -904 + ^ x23: .cfa -896 + ^ x24: .cfa -888 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^
STACK CFI INIT 82b0 e70 .cfa: sp 0 + .ra: x30
STACK CFI 82b4 .cfa: sp 976 +
STACK CFI 82b8 x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI 82d8 .ra: .cfa -896 + ^ v8: .cfa -880 + ^ v9: .cfa -872 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI 8f24 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8f28 .cfa: sp 976 + .ra: .cfa -896 + ^ v8: .cfa -880 + ^ v9: .cfa -872 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI INIT 29c0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 29c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29d0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2a54 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 9140 80c .cfa: sp 0 + .ra: x30
STACK CFI 9144 .cfa: sp 784 +
STACK CFI 9148 x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI 9168 .ra: .cfa -704 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 9894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9898 .cfa: sp 784 + .ra: .cfa -704 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI INIT 9960 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 9968 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 9978 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 9984 x21: .cfa -432 + ^ x22: .cfa -424 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 99e0 .ra: .cfa -368 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 9df8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9dfc .cfa: sp 448 + .ra: .cfa -368 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI INIT 9e60 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 9e68 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 9e78 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 9e84 x21: .cfa -432 + ^ x22: .cfa -424 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 9ee0 .ra: .cfa -368 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI a2f8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a2fc .cfa: sp 448 + .ra: .cfa -368 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI INIT a360 27c4 .cfa: sp 0 + .ra: x30
STACK CFI a364 .cfa: sp 3088 +
STACK CFI a368 x21: .cfa -3072 + ^ x22: .cfa -3064 + ^
STACK CFI a378 x19: .cfa -3088 + ^ x20: .cfa -3080 + ^ x23: .cfa -3056 + ^ x24: .cfa -3048 + ^
STACK CFI a388 x25: .cfa -3040 + ^ x26: .cfa -3032 + ^
STACK CFI a3a0 .ra: .cfa -3008 + ^ v8: .cfa -3000 + ^ x27: .cfa -3024 + ^ x28: .cfa -3016 + ^
STACK CFI c554 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c558 .cfa: sp 3088 + .ra: .cfa -3008 + ^ v8: .cfa -3000 + ^ x19: .cfa -3088 + ^ x20: .cfa -3080 + ^ x21: .cfa -3072 + ^ x22: .cfa -3064 + ^ x23: .cfa -3056 + ^ x24: .cfa -3048 + ^ x25: .cfa -3040 + ^ x26: .cfa -3032 + ^ x27: .cfa -3024 + ^ x28: .cfa -3016 + ^
STACK CFI INIT cb50 290 .cfa: sp 0 + .ra: x30
STACK CFI cb54 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI cb78 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI cb9c x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI cbe0 .ra: .cfa -384 + ^
STACK CFI cd8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI cd90 .cfa: sp 432 + .ra: .cfa -384 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI INIT cdf0 1fc0 .cfa: sp 0 + .ra: x30
STACK CFI cdf4 .cfa: sp 2688 +
STACK CFI cdf8 x19: .cfa -2688 + ^ x20: .cfa -2680 + ^
STACK CFI ce00 x21: .cfa -2672 + ^ x22: .cfa -2664 + ^
STACK CFI ce24 .ra: .cfa -2608 + ^ v8: .cfa -2600 + ^ x23: .cfa -2656 + ^ x24: .cfa -2648 + ^ x25: .cfa -2640 + ^ x26: .cfa -2632 + ^ x27: .cfa -2624 + ^ x28: .cfa -2616 + ^
STACK CFI ea54 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ea58 .cfa: sp 2688 + .ra: .cfa -2608 + ^ v8: .cfa -2600 + ^ x19: .cfa -2688 + ^ x20: .cfa -2680 + ^ x21: .cfa -2672 + ^ x22: .cfa -2664 + ^ x23: .cfa -2656 + ^ x24: .cfa -2648 + ^ x25: .cfa -2640 + ^ x26: .cfa -2632 + ^ x27: .cfa -2624 + ^ x28: .cfa -2616 + ^
STACK CFI INIT edd0 304 .cfa: sp 0 + .ra: x30
STACK CFI edd4 .cfa: sp 528 +
STACK CFI edd8 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI edec x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI edf8 .ra: .cfa -480 + ^
STACK CFI f060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI f068 .cfa: sp 528 + .ra: .cfa -480 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI INIT f0f0 440 .cfa: sp 0 + .ra: x30
STACK CFI f0f8 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI f10c x21: .cfa -336 + ^ x22: .cfa -328 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI f118 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI f120 .ra: .cfa -280 + ^ x27: .cfa -288 + ^
STACK CFI f470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI f474 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^
STACK CFI INIT f540 fa0 .cfa: sp 0 + .ra: x30
STACK CFI f544 .cfa: sp 1216 +
STACK CFI f54c x23: .cfa -1184 + ^ x24: .cfa -1176 + ^
STACK CFI f55c x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^
STACK CFI f578 .ra: .cfa -1136 + ^ v8: .cfa -1128 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI f5ac .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f5b0 .cfa: sp 1216 + .ra: .cfa -1136 + ^ v8: .cfa -1128 + ^ x19: .cfa -1216 + ^ x20: .cfa -1208 + ^ x21: .cfa -1200 + ^ x22: .cfa -1192 + ^ x23: .cfa -1184 + ^ x24: .cfa -1176 + ^ x25: .cfa -1168 + ^ x26: .cfa -1160 + ^ x27: .cfa -1152 + ^ x28: .cfa -1144 + ^
STACK CFI INIT 10500 6dc .cfa: sp 0 + .ra: x30
STACK CFI 10504 .cfa: sp 752 +
STACK CFI 10508 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 10518 x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^
STACK CFI 10528 .ra: .cfa -688 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^
STACK CFI 10a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10a68 .cfa: sp 752 + .ra: .cfa -688 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^
