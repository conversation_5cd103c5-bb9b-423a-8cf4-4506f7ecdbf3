MODULE Linux arm64 5AD0ACED9289EA2347AB30E6FA517E770 libicutest.so.66
INFO CODE_ID EDACD05A899223EA47AB30E6FA517E77077071B1
PUBLIC 6f38 0 cleanUpTestTree
PUBLIC 6f70 0 addTest
PUBLIC 70e0 0 str_timeDelta
PUBLIC 7218 0 getTestName
PUBLIC 7230 0 vlog_info
PUBLIC 7318 0 log_err
PUBLIC 7410 0 getTest
PUBLIC 74e8 0 log_err_status
PUBLIC 7688 0 log_info
PUBLIC 7738 0 log_verbose
PUBLIC 7900 0 log_knownIssue
PUBLIC 79a8 0 log_data_err
PUBLIC 7ad8 0 getTestOption
PUBLIC 7b78 0 setTestOption
PUBLIC 7c48 0 ctest_xml_setFileName
PUBLIC 7c60 0 initArgs
PUBLIC 8280 0 ctest_xml_init
PUBLIC 83d8 0 ctest_xml_fini
PUBLIC 8438 0 ctest_xml_testcase
PUBLIC 8a70 0 showTests
PUBLIC 8af8 0 runTests
PUBLIC 8d70 0 runTestRequest
PUBLIC 8fd8 0 RBTestDataModule::getInfo(DataMap const*&, UErrorCode&) const
PUBLIC 8ff0 0 TestLog::~TestLog()
PUBLIC 8ff8 0 TestLog::~TestLog()
PUBLIC 9020 0 IcuTestErrorCode::setScope(icu_66::UnicodeString const&)
PUBLIC 9028 0 TestDataModule::TestDataModule(char const*, TestLog&, UErrorCode&)
PUBLIC 9040 0 TestDataModule::~TestDataModule()
PUBLIC 9070 0 RBTestDataModule::~RBTestDataModule()
PUBLIC 90c0 0 RBTestDataModule::~RBTestDataModule()
PUBLIC 90e8 0 TestDataModule::~TestDataModule()
PUBLIC 9110 0 TestDataModule::getName() const
PUBLIC 9118 0 IcuTestErrorCode::setScope(char const*)
PUBLIC 91d8 0 IcuTestErrorCode::errlog(signed char, icu_66::UnicodeString const&, char const*) const
PUBLIC 94b0 0 IcuTestErrorCode::~IcuTestErrorCode()
PUBLIC 9568 0 IcuTestErrorCode::~IcuTestErrorCode()
PUBLIC 9590 0 IcuTestErrorCode::errIfFailureAndReset()
PUBLIC 9650 0 IcuTestErrorCode::errIfFailureAndReset(char const*, ...)
PUBLIC 97b8 0 IcuTestErrorCode::errDataIfFailureAndReset()
PUBLIC 9878 0 IcuTestErrorCode::errDataIfFailureAndReset(char const*, ...)
PUBLIC 99e0 0 IcuTestErrorCode::expectErrorAndReset(UErrorCode)
PUBLIC 9b00 0 IcuTestErrorCode::expectErrorAndReset(UErrorCode, char const*, ...)
PUBLIC 9cd0 0 IcuTestErrorCode::handleFailure() const
PUBLIC 9d68 0 RBTestDataModule::getTestBundle(char const*, UErrorCode&)
PUBLIC 9ee0 0 RBTestDataModule::RBTestDataModule(char const*, TestLog&, UErrorCode&)
PUBLIC a080 0 TestDataModule::getTestDataModule(char const*, TestLog&, UErrorCode&)
PUBLIC a130 0 RBTestDataModule::createTestData(int, UErrorCode&) const
PUBLIC a260 0 RBTestDataModule::createTestData(char const*, UErrorCode&) const
PUBLIC a390 0 icu_66::MaybeStackArray<char, 40>::MaybeStackArray()
PUBLIC a3a8 0 icu_66::MaybeStackArray<char, 40>::MaybeStackArray(int)
PUBLIC a450 0 icu_66::MaybeStackArray<char, 40>::~MaybeStackArray()
PUBLIC a478 0 icu_66::MaybeStackArray<char, 40>::MaybeStackArray(icu_66::MaybeStackArray<char, 40>&&)
PUBLIC a4c8 0 icu_66::MaybeStackArray<char, 40>::operator=(icu_66::MaybeStackArray<char, 40>&&)
PUBLIC a570 0 icu_66::MaybeStackArray<char, 40>::getCapacity() const
PUBLIC a578 0 icu_66::MaybeStackArray<char, 40>::getAlias() const
PUBLIC a580 0 icu_66::MaybeStackArray<char, 40>::getArrayLimit() const
PUBLIC a590 0 icu_66::MaybeStackArray<char, 40>::operator[](long) const
PUBLIC a5a0 0 icu_66::MaybeStackArray<char, 40>::operator[](long)
PUBLIC a5b0 0 icu_66::MaybeStackArray<char, 40>::aliasInstead(char*, int)
PUBLIC a628 0 icu_66::MaybeStackArray<char, 40>::resize(int, int)
PUBLIC a6e0 0 icu_66::MaybeStackArray<char, 40>::orphanOrClone(int, int&)
PUBLIC a790 0 icu_66::MaybeStackArray<char, 40>::releaseArray()
PUBLIC a7a8 0 icu_66::MaybeStackArray<char, 40>::resetToStackArray()
PUBLIC a7c0 0 icu_66::MaybeStackArray<char, 40>::operator==(icu_66::MaybeStackArray<char, 40> const&)
PUBLIC a7c8 0 icu_66::MaybeStackArray<char, 40>::operator!=(icu_66::MaybeStackArray<char, 40> const&)
PUBLIC a7d0 0 icu_66::MaybeStackArray<char, 40>::MaybeStackArray(icu_66::MaybeStackArray<char, 40> const&)
PUBLIC a7d8 0 icu_66::MaybeStackArray<char, 40>::operator=(icu_66::MaybeStackArray<char, 40> const&)
PUBLIC a7e0 0 RBTestData::getInfo(DataMap const*&, UErrorCode&) const
PUBLIC a7f8 0 RBTestData::nextSettings(DataMap const*&, UErrorCode&)
PUBLIC a8f8 0 RBTestData::nextCase(DataMap const*&, UErrorCode&)
PUBLIC aa00 0 TestData::TestData(char const*)
PUBLIC aa20 0 TestData::~TestData()
PUBLIC aa98 0 RBTestData::~RBTestData()
PUBLIC aae8 0 RBTestData::~RBTestData()
PUBLIC ab10 0 TestData::~TestData()
PUBLIC ab38 0 TestData::getName() const
PUBLIC ab40 0 RBTestData::RBTestData(char const*)
PUBLIC ab78 0 RBTestData::RBTestData(UResourceBundle*, UResourceBundle*, UErrorCode&)
PUBLIC ad10 0 deleteResBund
PUBLIC ad28 0 RBDataMap::getItem(char const*, UErrorCode&) const
PUBLIC ade8 0 RBDataMap::getString(char const*, UErrorCode&) const
PUBLIC ae88 0 RBDataMap::getInt28(char const*, UErrorCode&) const
PUBLIC aed0 0 RBDataMap::getUInt28(char const*, UErrorCode&) const
PUBLIC af18 0 RBDataMap::getIntVector(int&, char const*, UErrorCode&) const
PUBLIC af70 0 RBDataMap::getBinary(int&, char const*, UErrorCode&) const
PUBLIC afc8 0 RBDataMap::getStringArray(int&, char const*, UErrorCode&) const
PUBLIC b128 0 DataMap::~DataMap()
PUBLIC b130 0 RBDataMap::~RBDataMap()
PUBLIC b180 0 RBDataMap::~RBDataMap()
PUBLIC b1a8 0 DataMap::~DataMap()
PUBLIC b1d0 0 DataMap::DataMap()
PUBLIC b1e8 0 DataMap::utoi(icu_66::UnicodeString const&) const
PUBLIC b2a0 0 RBDataMap::getInt(char const*, UErrorCode&) const
PUBLIC b358 0 RBDataMap::getIntArray(int&, char const*, UErrorCode&) const
PUBLIC b4b8 0 RBDataMap::RBDataMap()
PUBLIC b5d0 0 RBDataMap::init(UResourceBundle*, UErrorCode&)
PUBLIC b738 0 RBDataMap::RBDataMap(UResourceBundle*, UErrorCode&)
PUBLIC b848 0 RBDataMap::init(UResourceBundle*, UResourceBundle*, UErrorCode&)
PUBLIC ba00 0 RBDataMap::RBDataMap(UResourceBundle*, UResourceBundle*, UErrorCode&)
PUBLIC bb08 0 UPerfTest::runIndexedTest(int, signed char, char const*&, char*)
PUBLIC bb38 0 UPerfTest::~UPerfTest()
PUBLIC bb90 0 UPerfTest::~UPerfTest()
PUBLIC bbb8 0 UPerfTest::usage()
PUBLIC bd20 0 UPerfTest::runTestLoop(char*, char*)
PUBLIC c570 0 UPerfFunction::~UPerfFunction()
PUBLIC c578 0 UPerfFunction::~UPerfFunction()
PUBLIC c5a0 0 UPerfTest::init(UOption*, int, UErrorCode&)
PUBLIC c8a0 0 UPerfTest::UPerfTest(int, char const**, UErrorCode&)
PUBLIC c910 0 UPerfTest::UPerfTest(int, char const**, UOption*, int, char const*, UErrorCode&)
PUBLIC c980 0 UPerfTest::getLines(UErrorCode&)
PUBLIC cb20 0 UPerfTest::getBuffer(int&, UErrorCode&)
PUBLIC cbc0 0 UPerfTest::runTest(char*, char*)
PUBLIC ccf8 0 UPerfTest::run()
PUBLIC ce08 0 UPerfTest::setPath(char*)
PUBLIC ce10 0 UPerfTest::setCaller(UPerfTest*)
PUBLIC ce28 0 UPerfTest::callTest(UPerfTest&, char*)
PUBLIC ce80 0 UPerfFunction::getEventsPerIteration()
PUBLIC ce88 0 UPerfFunction::time(int, UErrorCode*)
PUBLIC cf70 0 uprv_dummyFunction_CT
STACK CFI INIT 64a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 64d8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6518 48 .cfa: sp 0 + .ra: x30
STACK CFI 651c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6524 x19: .cfa -16 + ^
STACK CFI 655c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6560 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6568 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6570 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65a8 58 .cfa: sp 0 + .ra: x30
STACK CFI 65ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6600 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6604 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 660c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 661c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 6688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 668c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT 66a0 64 .cfa: sp 0 + .ra: x30
STACK CFI 66a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66b8 x21: .cfa -16 + ^
STACK CFI 66ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 66f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6708 120 .cfa: sp 0 + .ra: x30
STACK CFI 670c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6828 11c .cfa: sp 0 + .ra: x30
STACK CFI 682c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 6834 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 684c x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 693c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6940 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x29: .cfa -352 + ^
STACK CFI INIT 6948 f8 .cfa: sp 0 + .ra: x30
STACK CFI 694c .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 695c x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 6980 x21: .cfa -320 + ^
STACK CFI 6a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6a3c .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x29: .cfa -352 + ^
STACK CFI INIT 6a40 a8 .cfa: sp 0 + .ra: x30
STACK CFI 6a44 .cfa: sp 592 +
STACK CFI 6a4c .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 6a58 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 6a80 x21: .cfa -560 + ^
STACK CFI 6ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6ae4 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x29: .cfa -592 + ^
STACK CFI INIT 6ae8 110 .cfa: sp 0 + .ra: x30
STACK CFI 6aec .cfa: sp 608 +
STACK CFI 6af0 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI 6af8 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI 6b08 x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 6b20 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI 6bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6bf4 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x29: .cfa -608 + ^
STACK CFI INIT 6bf8 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6bfc .cfa: sp 560 +
STACK CFI 6c04 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 6c0c x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 6c1c x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 6ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ca4 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x29: .cfa -560 + ^
STACK CFI INIT 6ca8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ce8 6c .cfa: sp 0 + .ra: x30
STACK CFI 6cec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cf4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d58 fc .cfa: sp 0 + .ra: x30
STACK CFI 6d5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6d6c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d84 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 6d88 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6d9c x23: .cfa -80 + ^
STACK CFI 6e38 x21: x21 x22: x22
STACK CFI 6e3c x23: x23
STACK CFI 6e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e44 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 6e4c x21: x21 x22: x22
STACK CFI 6e50 x23: x23
STACK CFI INIT 6e58 e0 .cfa: sp 0 + .ra: x30
STACK CFI 6e5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6e6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e84 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 6e88 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6f28 x21: x21 x22: x22
STACK CFI 6f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6f38 38 .cfa: sp 0 + .ra: x30
STACK CFI 6f3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f44 x19: .cfa -16 + ^
STACK CFI 6f6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6f70 170 .cfa: sp 0 + .ra: x30
STACK CFI 6f74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6f7c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6f88 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6f94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6fac x25: .cfa -48 + ^
STACK CFI 7048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 704c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 70e0 134 .cfa: sp 0 + .ra: x30
STACK CFI 70e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7100 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^
STACK CFI 7150 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 7154 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 7180 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 7184 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 71d0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 71d4 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 7200 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 7204 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7218 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7230 e4 .cfa: sp 0 + .ra: x30
STACK CFI 7234 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 723c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7248 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7254 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 7310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7318 f4 .cfa: sp 0 + .ra: x30
STACK CFI 731c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7324 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 73f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73f8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI INIT 7410 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7414 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 741c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7424 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7444 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 74a4 x23: x23 x24: x24
STACK CFI 74cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 74d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 74e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 74e8 19c .cfa: sp 0 + .ra: x30
STACK CFI 74ec .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 74fc x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 7570 x21: .cfa -272 + ^
STACK CFI 75b4 x21: x21
STACK CFI 75d4 x21: .cfa -272 + ^
STACK CFI 75f8 x21: x21
STACK CFI 7618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 761c .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
STACK CFI 7650 x21: .cfa -272 + ^
STACK CFI 7660 x21: x21
STACK CFI 7680 x21: .cfa -272 + ^
STACK CFI INIT 7688 b0 .cfa: sp 0 + .ra: x30
STACK CFI 768c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 76b8 x19: .cfa -288 + ^
STACK CFI 7730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7734 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 7738 a8 .cfa: sp 0 + .ra: x30
STACK CFI 773c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 774c x19: .cfa -288 + ^
STACK CFI 77d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77dc .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x29: .cfa -304 + ^
STACK CFI INIT 77e0 11c .cfa: sp 0 + .ra: x30
STACK CFI 77e4 .cfa: sp 2192 +
STACK CFI 77e8 .ra: .cfa -2184 + ^ x29: .cfa -2192 + ^
STACK CFI 77f0 x23: .cfa -2144 + ^
STACK CFI 77f8 x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI 7838 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI 78b4 x21: x21 x22: x22
STACK CFI 78e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 78e4 .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x29: .cfa -2192 + ^
STACK CFI 78f0 x21: x21 x22: x22
STACK CFI 78f8 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI INIT 7900 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7904 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 7914 x19: .cfa -272 + ^
STACK CFI 799c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 79a0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x29: .cfa -288 + ^
STACK CFI INIT 79a8 12c .cfa: sp 0 + .ra: x30
STACK CFI 79ac .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 79bc x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 79ec x21: .cfa -288 + ^
STACK CFI 7aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7aa8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT 7ad8 9c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b78 cc .cfa: sp 0 + .ra: x30
STACK CFI 7b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b88 x19: .cfa -16 + ^
STACK CFI 7bc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7c10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7c28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c48 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c60 620 .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7c70 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7c7c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7cc0 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7ce4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7d78 x27: x27 x28: x28
STACK CFI 7df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7df8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 8048 x27: x27 x28: x28
STACK CFI 804c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 80a8 x27: x27 x28: x28
STACK CFI 80ac x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 80f8 x27: x27 x28: x28
STACK CFI 8100 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8120 x27: x27 x28: x28
STACK CFI 8124 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8260 x27: x27 x28: x28
STACK CFI 8264 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 8280 154 .cfa: sp 0 + .ra: x30
STACK CFI 8284 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 828c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8294 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 82b0 x23: .cfa -16 + ^
STACK CFI 8374 x23: x23
STACK CFI 8384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8388 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 839c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 83d0 x23: x23
STACK CFI INIT 83d8 60 .cfa: sp 0 + .ra: x30
STACK CFI 83dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83e4 x19: .cfa -16 + ^
STACK CFI 8434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8438 98 .cfa: sp 0 + .ra: x30
STACK CFI 843c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8448 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 84cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 84d0 5a0 .cfa: sp 0 + .ra: x30
STACK CFI 84d4 .cfa: sp 800 +
STACK CFI 84dc .ra: .cfa -792 + ^ x29: .cfa -800 + ^
STACK CFI 84e4 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 84f8 x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI 8504 x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI 8520 v8: .cfa -704 + ^ v9: .cfa -696 + ^
STACK CFI 8550 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 855c x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 8650 x25: x25 x26: x26
STACK CFI 8654 x27: x27 x28: x28
STACK CFI 8684 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8688 .cfa: sp 800 + .ra: .cfa -792 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^ x29: .cfa -800 + ^
STACK CFI 86c0 v10: .cfa -688 + ^
STACK CFI 87fc v10: v10 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8808 v10: .cfa -688 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 8858 v10: v10
STACK CFI 8898 x25: x25 x26: x26
STACK CFI 889c x27: x27 x28: x28
STACK CFI 88a0 x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 89ac v10: .cfa -688 + ^
STACK CFI 89c0 v10: v10
STACK CFI 8a00 v10: .cfa -688 + ^
STACK CFI 8a18 v10: v10
STACK CFI 8a30 v10: .cfa -688 + ^
STACK CFI 8a60 v10: v10 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8a64 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 8a68 x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 8a6c v10: .cfa -688 + ^
STACK CFI INIT 8a70 84 .cfa: sp 0 + .ra: x30
STACK CFI 8a78 .cfa: sp 4144 +
STACK CFI 8a7c .ra: .cfa -4136 + ^ x29: .cfa -4144 + ^
STACK CFI 8a84 x19: .cfa -4128 + ^ x20: .cfa -4120 + ^
STACK CFI 8adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8ae0 .cfa: sp 4144 + .ra: .cfa -4136 + ^ x19: .cfa -4128 + ^ x20: .cfa -4120 + ^ x29: .cfa -4144 + ^
STACK CFI INIT 8af8 274 .cfa: sp 0 + .ra: x30
STACK CFI 8b00 .cfa: sp 4192 +
STACK CFI 8b04 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 8b0c x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 8b14 x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 8b2c x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 8ba4 x25: .cfa -4128 + ^
STACK CFI 8ca8 x25: x25
STACK CFI 8ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ce4 .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x21: .cfa -4160 + ^ x22: .cfa -4152 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x29: .cfa -4192 + ^
STACK CFI 8d2c x25: .cfa -4128 + ^
STACK CFI 8d30 x25: x25
STACK CFI 8d68 x25: .cfa -4128 + ^
STACK CFI INIT 8d70 268 .cfa: sp 0 + .ra: x30
STACK CFI 8d74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8d80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8d90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8d98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8db4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8dd4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8e40 x25: x25 x26: x26
STACK CFI 8e44 x27: x27 x28: x28
STACK CFI 8e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8e78 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8f40 x25: x25 x26: x26
STACK CFI 8f44 x27: x27 x28: x28
STACK CFI 8f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8f4c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8f6c x25: x25 x26: x26
STACK CFI 8f70 x27: x27 x28: x28
STACK CFI 8f8c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8f98 x25: x25 x26: x26
STACK CFI 8f9c x27: x27 x28: x28
STACK CFI 8fa0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8fb8 x25: x25 x26: x26
STACK CFI 8fbc x27: x27 x28: x28
STACK CFI INIT 8fd8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a390 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a3a8 a8 .cfa: sp 0 + .ra: x30
STACK CFI a3ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a3b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a3dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a3e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a3ec x21: .cfa -16 + ^
STACK CFI a418 x21: x21
STACK CFI a41c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a420 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a430 x21: x21
STACK CFI a434 x21: .cfa -16 + ^
STACK CFI INIT a450 24 .cfa: sp 0 + .ra: x30
STACK CFI a460 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a478 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT a4c8 a8 .cfa: sp 0 + .ra: x30
STACK CFI a4cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a4d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a528 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a56c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a578 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a580 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT a590 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a5a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT a5b0 74 .cfa: sp 0 + .ra: x30
STACK CFI a5c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a5c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a5d4 x21: .cfa -16 + ^
STACK CFI a5f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a600 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a628 b4 .cfa: sp 0 + .ra: x30
STACK CFI a62c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a638 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a644 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a684 x19: x19 x20: x20
STACK CFI a68c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a6c8 x19: x19 x20: x20
STACK CFI a6d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT a6e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI a6e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a6ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a6f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a734 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a744 x23: .cfa -16 + ^
STACK CFI a770 x23: x23
STACK CFI a774 x23: .cfa -16 + ^
STACK CFI a778 x23: x23
STACK CFI a78c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a790 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7a8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ff8 24 .cfa: sp 0 + .ra: x30
STACK CFI 8ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9004 x19: .cfa -16 + ^
STACK CFI 9018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9028 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9040 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9070 50 .cfa: sp 0 + .ra: x30
STACK CFI 9074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9084 x19: .cfa -16 + ^
STACK CFI 90bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 90c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 90c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90cc x19: .cfa -16 + ^
STACK CFI 90e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 90e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 90ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90f4 x19: .cfa -16 + ^
STACK CFI 9108 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9110 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9118 c0 .cfa: sp 0 + .ra: x30
STACK CFI 911c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 912c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9158 x21: .cfa -96 + ^
STACK CFI 91bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 91c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 91d8 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 91dc .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 91e4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 91f0 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 9200 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 920c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 93dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 93e0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x29: .cfa -288 + ^
STACK CFI INIT 94b0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 94b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 94c0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 94f4 x21: .cfa -96 + ^
STACK CFI 9528 x21: x21
STACK CFI 9558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 955c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 9560 x21: .cfa -96 + ^
STACK CFI INIT 9568 24 .cfa: sp 0 + .ra: x30
STACK CFI 956c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9574 x19: .cfa -16 + ^
STACK CFI 9588 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9590 c0 .cfa: sp 0 + .ra: x30
STACK CFI 9594 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 959c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 95c0 x21: .cfa -96 + ^
STACK CFI 9600 x21: x21
STACK CFI 9624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9628 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 9638 x21: .cfa -96 + ^
STACK CFI INIT 9650 164 .cfa: sp 0 + .ra: x30
STACK CFI 9658 .cfa: sp 4400 +
STACK CFI 9660 .ra: .cfa -4392 + ^ x29: .cfa -4400 + ^
STACK CFI 9668 x21: .cfa -4368 + ^ x22: .cfa -4360 + ^
STACK CFI 96c4 x19: .cfa -4384 + ^ x20: .cfa -4376 + ^
STACK CFI 975c x19: x19 x20: x20
STACK CFI 9788 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 978c .cfa: sp 4400 + .ra: .cfa -4392 + ^ x21: .cfa -4368 + ^ x22: .cfa -4360 + ^ x29: .cfa -4400 + ^
STACK CFI 979c x19: .cfa -4384 + ^ x20: .cfa -4376 + ^
STACK CFI INIT 97b8 c0 .cfa: sp 0 + .ra: x30
STACK CFI 97bc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 97c4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 97e8 x21: .cfa -96 + ^
STACK CFI 9828 x21: x21
STACK CFI 984c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9850 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 9860 x21: .cfa -96 + ^
STACK CFI INIT 9878 164 .cfa: sp 0 + .ra: x30
STACK CFI 9880 .cfa: sp 4400 +
STACK CFI 9888 .ra: .cfa -4392 + ^ x29: .cfa -4400 + ^
STACK CFI 9890 x21: .cfa -4368 + ^ x22: .cfa -4360 + ^
STACK CFI 98ec x19: .cfa -4384 + ^ x20: .cfa -4376 + ^
STACK CFI 9984 x19: x19 x20: x20
STACK CFI 99b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 99b4 .cfa: sp 4400 + .ra: .cfa -4392 + ^ x21: .cfa -4368 + ^ x22: .cfa -4360 + ^ x29: .cfa -4400 + ^
STACK CFI 99c4 x19: .cfa -4384 + ^ x20: .cfa -4376 + ^
STACK CFI INIT 99e0 120 .cfa: sp 0 + .ra: x30
STACK CFI 99e4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 99ec x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 99f4 x23: .cfa -224 + ^
STACK CFI 99fc x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 9ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9ac8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x29: .cfa -272 + ^
STACK CFI INIT 9b00 1cc .cfa: sp 0 + .ra: x30
STACK CFI 9b08 .cfa: sp 4544 +
STACK CFI 9b1c .ra: .cfa -4536 + ^ x29: .cfa -4544 + ^
STACK CFI 9b24 x19: .cfa -4528 + ^ x20: .cfa -4520 + ^
STACK CFI 9b48 x21: .cfa -4512 + ^ x22: .cfa -4504 + ^
STACK CFI 9bb8 x23: .cfa -4496 + ^ x24: .cfa -4488 + ^
STACK CFI 9c4c x23: x23 x24: x24
STACK CFI 9c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9c90 .cfa: sp 4544 + .ra: .cfa -4536 + ^ x19: .cfa -4528 + ^ x20: .cfa -4520 + ^ x21: .cfa -4512 + ^ x22: .cfa -4504 + ^ x29: .cfa -4544 + ^
STACK CFI 9c94 x23: .cfa -4496 + ^ x24: .cfa -4488 + ^
STACK CFI INIT 9cd0 98 .cfa: sp 0 + .ra: x30
STACK CFI 9cd4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9ce4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9d00 x21: .cfa -96 + ^
STACK CFI 9d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9d50 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9d68 178 .cfa: sp 0 + .ra: x30
STACK CFI 9d6c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9d74 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 9d84 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 9df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9dfc .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x29: .cfa -288 + ^
STACK CFI 9e00 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 9e10 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 9e8c x23: x23 x24: x24
STACK CFI 9e90 x25: x25 x26: x26
STACK CFI 9ea0 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 9ea4 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI INIT 9ee0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 9ee4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9eec x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9efc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9f14 x23: .cfa -96 + ^
STACK CFI 9f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9f8c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT a080 ac .cfa: sp 0 + .ra: x30
STACK CFI a084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a08c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a0a8 x21: .cfa -16 + ^
STACK CFI a0dc x21: x21
STACK CFI a0e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a0e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a0f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a0f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a110 x21: x21
STACK CFI a114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a118 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a130 12c .cfa: sp 0 + .ra: x30
STACK CFI a134 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a13c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a14c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a1a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a1a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI a1b0 x23: .cfa -32 + ^
STACK CFI a20c x23: x23
STACK CFI a210 x23: .cfa -32 + ^
STACK CFI a220 x23: x23
STACK CFI a224 x23: .cfa -32 + ^
STACK CFI a23c x23: x23
STACK CFI a244 x23: .cfa -32 + ^
STACK CFI INIT a260 12c .cfa: sp 0 + .ra: x30
STACK CFI a264 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a26c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a27c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI a2e0 x23: .cfa -32 + ^
STACK CFI a33c x23: x23
STACK CFI a340 x23: .cfa -32 + ^
STACK CFI a350 x23: x23
STACK CFI a354 x23: .cfa -32 + ^
STACK CFI a36c x23: x23
STACK CFI a374 x23: .cfa -32 + ^
STACK CFI INIT a7e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a7f8 100 .cfa: sp 0 + .ra: x30
STACK CFI a7fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a804 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a818 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a824 x23: .cfa -32 + ^
STACK CFI a8b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a8b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT a8f8 104 .cfa: sp 0 + .ra: x30
STACK CFI a8fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a904 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a91c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a928 x23: .cfa -32 + ^
STACK CFI a9b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a9b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT aa00 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT aa20 74 .cfa: sp 0 + .ra: x30
STACK CFI aa24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa34 x19: .cfa -16 + ^
STACK CFI aa7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aa90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aa98 50 .cfa: sp 0 + .ra: x30
STACK CFI aa9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aaac x19: .cfa -16 + ^
STACK CFI aae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aae8 24 .cfa: sp 0 + .ra: x30
STACK CFI aaec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aaf4 x19: .cfa -16 + ^
STACK CFI ab08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab10 24 .cfa: sp 0 + .ra: x30
STACK CFI ab14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab1c x19: .cfa -16 + ^
STACK CFI ab30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab40 38 .cfa: sp 0 + .ra: x30
STACK CFI ab44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab4c x19: .cfa -16 + ^
STACK CFI ab74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab78 194 .cfa: sp 0 + .ra: x30
STACK CFI ab7c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ab84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ab90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aba0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI acb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI acb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT ad10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad28 c0 .cfa: sp 0 + .ra: x30
STACK CFI ad2c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ad34 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ad44 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI adb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI adbc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT ade8 9c .cfa: sp 0 + .ra: x30
STACK CFI adec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI adf8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ae18 x21: .cfa -32 + ^
STACK CFI ae6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ae70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT ae88 48 .cfa: sp 0 + .ra: x30
STACK CFI ae8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae9c x19: .cfa -16 + ^
STACK CFI aebc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aec0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aecc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aed0 48 .cfa: sp 0 + .ra: x30
STACK CFI aed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aee4 x19: .cfa -16 + ^
STACK CFI af04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI af08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI af14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af18 58 .cfa: sp 0 + .ra: x30
STACK CFI af1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI af5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI af60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI af6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT af70 58 .cfa: sp 0 + .ra: x30
STACK CFI af74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI afb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI afb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI afc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT afc8 160 .cfa: sp 0 + .ra: x30
STACK CFI afcc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI afd4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI afdc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI afec x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI b034 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI b0e0 x19: x19 x20: x20
STACK CFI b114 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b118 .cfa: sp 160 + .ra: .cfa -152 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI b11c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI INIT b128 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b130 4c .cfa: sp 0 + .ra: x30
STACK CFI b134 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b144 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b180 24 .cfa: sp 0 + .ra: x30
STACK CFI b184 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b18c x19: .cfa -16 + ^
STACK CFI b1a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b1a8 24 .cfa: sp 0 + .ra: x30
STACK CFI b1ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b1b4 x19: .cfa -16 + ^
STACK CFI b1c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b1d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b1e8 b4 .cfa: sp 0 + .ra: x30
STACK CFI b1ec .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI b1fc x21: .cfa -288 + ^
STACK CFI b204 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI b278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b27c .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x29: .cfa -320 + ^
STACK CFI INIT b2a0 b8 .cfa: sp 0 + .ra: x30
STACK CFI b2a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI b2b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b2b8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI b2dc x23: .cfa -96 + ^
STACK CFI b328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b32c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT b358 15c .cfa: sp 0 + .ra: x30
STACK CFI b35c .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI b364 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI b370 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI b38c x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^
STACK CFI b400 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI b454 x19: x19 x20: x20
STACK CFI b494 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b498 .cfa: sp 240 + .ra: .cfa -232 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x29: .cfa -240 + ^
STACK CFI b49c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI INIT b4b8 114 .cfa: sp 0 + .ra: x30
STACK CFI b4bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b4c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b4d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b554 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT b5d0 168 .cfa: sp 0 + .ra: x30
STACK CFI b5d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI b5dc x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI b5e8 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI b5f4 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI b600 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI b618 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI b6fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b700 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT b738 110 .cfa: sp 0 + .ra: x30
STACK CFI b73c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b744 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b74c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b7bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b7c8 x23: .cfa -16 + ^
STACK CFI b7f8 x23: x23
STACK CFI b7fc x23: .cfa -16 + ^
STACK CFI b814 x23: x23
STACK CFI b824 x23: .cfa -16 + ^
STACK CFI INIT b848 1b8 .cfa: sp 0 + .ra: x30
STACK CFI b84c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI b854 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI b860 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI b86c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI b884 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI b8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b8f8 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI b908 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI b9c0 x21: x21 x22: x22
STACK CFI b9c8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI INIT ba00 108 .cfa: sp 0 + .ra: x30
STACK CFI ba04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ba20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI ba90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ba94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT ce80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb08 30 .cfa: sp 0 + .ra: x30
STACK CFI bb0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bb34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ce88 e4 .cfa: sp 0 + .ra: x30
STACK CFI ce8c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ce94 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI cea4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ceb8 x23: .cfa -96 + ^
STACK CFI cf64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cf68 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT bb38 58 .cfa: sp 0 + .ra: x30
STACK CFI bb3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb4c x19: .cfa -16 + ^
STACK CFI bb8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bb90 24 .cfa: sp 0 + .ra: x30
STACK CFI bb94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb9c x19: .cfa -16 + ^
STACK CFI bbb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bbb8 164 .cfa: sp 0 + .ra: x30
STACK CFI bbbc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI bbc8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bbd0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bbf4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI bd14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI bd18 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT bd20 850 .cfa: sp 0 + .ra: x30
STACK CFI bd24 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI bd40 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI bd6c v10: .cfa -192 + ^ v11: .cfa -184 + ^
STACK CFI bd78 v8: .cfa -208 + ^ v9: .cfa -200 + ^
STACK CFI bd8c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI bd98 v12: .cfa -176 + ^ v13: .cfa -168 + ^
STACK CFI bea0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bea4 .cfa: sp 304 + .ra: .cfa -296 + ^ v10: .cfa -192 + ^ v11: .cfa -184 + ^ v12: .cfa -176 + ^ v13: .cfa -168 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI bee8 x23: x23 x24: x24
STACK CFI beec x25: x25 x26: x26
STACK CFI bef0 x27: x27 x28: x28
STACK CFI bf54 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI bf5c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI bf68 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI c4ac x23: x23 x24: x24
STACK CFI c4b0 x25: x25 x26: x26
STACK CFI c4b4 x27: x27 x28: x28
STACK CFI c4b8 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI c540 x23: x23 x24: x24
STACK CFI c544 x25: x25 x26: x26
STACK CFI c548 x27: x27 x28: x28
STACK CFI c54c x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI c554 x23: x23 x24: x24
STACK CFI c558 x25: x25 x26: x26
STACK CFI c55c x27: x27 x28: x28
STACK CFI c564 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI c568 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI c56c x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT c570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c578 24 .cfa: sp 0 + .ra: x30
STACK CFI c57c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c584 x19: .cfa -16 + ^
STACK CFI c598 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c5a0 2fc .cfa: sp 0 + .ra: x30
STACK CFI c5a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c5ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c5b8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c5c8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI c7d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c7dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI c7e0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c7f0 x27: .cfa -32 + ^
STACK CFI c838 x25: x25 x26: x26
STACK CFI c83c x27: x27
STACK CFI c888 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c88c x27: .cfa -32 + ^
STACK CFI c890 x25: x25 x26: x26 x27: x27
STACK CFI INIT c8a0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT c910 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT c980 19c .cfa: sp 0 + .ra: x30
STACK CFI c984 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c98c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c994 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI c9a0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI c9ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI c9f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI ca10 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ca1c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI cac0 x23: x23 x24: x24
STACK CFI cac4 x27: x27 x28: x28
STACK CFI cacc x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI cb0c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI cb10 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI cb14 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT cb20 a0 .cfa: sp 0 + .ra: x30
STACK CFI cb24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cb2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cb48 x21: .cfa -16 + ^
STACK CFI cb9c x21: x21
STACK CFI cbac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cbb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cbbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT cbc0 134 .cfa: sp 0 + .ra: x30
STACK CFI cbc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cbcc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cbd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI cc5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cc60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT ccf8 110 .cfa: sp 0 + .ra: x30
STACK CFI cd0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cd18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cd24 x23: .cfa -16 + ^
STACK CFI cd30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cdb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cdb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI cdec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT ce08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ce10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ce28 54 .cfa: sp 0 + .ra: x30
STACK CFI ce2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ce38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ce44 x21: .cfa -16 + ^
STACK CFI ce78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT cf70 8 .cfa: sp 0 + .ra: x30
