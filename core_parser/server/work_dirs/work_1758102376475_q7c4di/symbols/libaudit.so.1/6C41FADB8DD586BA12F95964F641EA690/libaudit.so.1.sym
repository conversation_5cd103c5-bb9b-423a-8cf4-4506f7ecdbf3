MODULE Linux arm64 6C41FADB8DD586BA12F95964F641EA690 libaudit.so.1
INFO CODE_ID DBFA416CD58DBA8612F95964F641EA69A46F748B
PUBLIC 3248 0 audit_request_status
PUBLIC 33f0 0 get_auditfail_action
PUBLIC 3428 0 audit_set_enabled
PUBLIC 34e0 0 audit_set_failure
PUBLIC 35a0 0 audit_set_pid
PUBLIC 36d0 0 audit_set_rate_limit
PUBLIC 3790 0 audit_set_backlog_limit
PUBLIC 3850 0 audit_set_backlog_wait_time
PUBLIC 3910 0 audit_set_feature
PUBLIC 39d8 0 audit_request_features
PUBLIC 3a80 0 audit_set_loginuid_immutable
PUBLIC 3a90 0 audit_get_features
PUBLIC 3ae8 0 audit_reset_lost
PUBLIC 3bc0 0 audit_request_rules_list_data
PUBLIC 3c38 0 audit_request_signal_info
PUBLIC 3c98 0 audit_update_watch_perms
PUBLIC 3d20 0 audit_add_rule_data
PUBLIC 3dc0 0 audit_delete_rule_data
PUBLIC 3e68 0 audit_trim_subtrees
PUBLIC 3ed8 0 audit_make_equivalent
PUBLIC 3fc8 0 audit_getloginuid
PUBLIC 40b0 0 audit_setloginuid
PUBLIC 4200 0 audit_get_session
PUBLIC 42e8 0 audit_rule_syscall_data
PUBLIC 4330 0 audit_rule_interfield_comp_data
PUBLIC 47f8 0 audit_rule_free_data
PUBLIC 4800 0 audit_detect_machine
PUBLIC 4868 0 audit_rule_syscallbyname_data
PUBLIC 4958 0 audit_add_watch_dir
PUBLIC 4aa8 0 audit_add_watch
PUBLIC 4ab8 0 audit_add_dir
PUBLIC 4ac8 0 audit_determine_machine
PUBLIC 4c98 0 audit_rule_fieldpair_data
PUBLIC 56b8 0 audit_number_to_errmsg
PUBLIC 5788 0 audit_can_control
PUBLIC 57f0 0 audit_is_enabled
PUBLIC 5938 0 audit_can_write
PUBLIC 59a0 0 audit_can_read
PUBLIC 5a08 0 set_aumessage_mode
PUBLIC 5a20 0 audit_msg
PUBLIC 5b48 0 audit_open
PUBLIC 5c40 0 audit_close
PUBLIC 5c50 0 audit_get_reply
PUBLIC 5f10 0 __audit_send
PUBLIC 61c8 0 audit_send
PUBLIC 6220 0 audit_name_to_field
PUBLIC 6398 0 audit_field_to_name
PUBLIC 6400 0 audit_name_to_syscall
PUBLIC 6cd0 0 audit_syscall_to_name
PUBLIC 6ed8 0 audit_name_to_flag
PUBLIC 7050 0 audit_flag_to_name
PUBLIC 7088 0 audit_name_to_action
PUBLIC 7200 0 audit_action_to_name
PUBLIC 7238 0 audit_name_to_msg_type
PUBLIC 7460 0 audit_msg_type_to_name
PUBLIC 74d0 0 audit_name_to_machine
PUBLIC 7648 0 audit_machine_to_name
PUBLIC 7680 0 audit_machine_to_elf
PUBLIC 76d8 0 audit_elf_to_machine
PUBLIC 7738 0 audit_operator_to_symbol
PUBLIC 77a0 0 audit_name_to_errno
PUBLIC 7930 0 audit_errno_to_name
PUBLIC 7968 0 audit_name_to_ftype
PUBLIC 7ae0 0 audit_ftype_to_name
PUBLIC 7b48 0 audit_name_to_fstype
PUBLIC 7cb8 0 audit_fstype_to_name
PUBLIC 8018 0 audit_value_needs_encoding
PUBLIC 8068 0 audit_encode_value
PUBLIC 81c8 0 audit_encode_nv_string
PUBLIC 8310 0 audit_log_user_message
PUBLIC 8518 0 audit_log_user_comm_message
PUBLIC 87f8 0 audit_log_acct_message
PUBLIC 8af8 0 audit_log_user_avc_message
PUBLIC 8d28 0 audit_log_semanage_message
PUBLIC 9220 0 audit_log_user_command
PUBLIC 9618 0 audit_strsplit_r
PUBLIC 96a8 0 audit_strsplit
STACK CFI INIT 2d78 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2da8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2de8 48 .cfa: sp 0 + .ra: x30
STACK CFI 2dec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2df4 x19: .cfa -16 + ^
STACK CFI 2e2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e38 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2e3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e68 x23: .cfa -16 + ^
STACK CFI 2ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2ef0 358 .cfa: sp 0 + .ra: x30
STACK CFI 2ef4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 2f00 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 2f1c x19: .cfa -352 + ^ x20: .cfa -344 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 2f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f88 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI 3000 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 3010 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 30f0 x25: x25 x26: x26
STACK CFI 30f4 x27: x27 x28: x28
STACK CFI 3138 x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 3170 x25: x25 x26: x26
STACK CFI 3174 x27: x27 x28: x28
STACK CFI 3178 x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 31b8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3240 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 3244 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 3248 6c .cfa: sp 0 + .ra: x30
STACK CFI 324c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3260 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 32b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 32b8 134 .cfa: sp 0 + .ra: x30
STACK CFI 32c0 .cfa: sp 9104 +
STACK CFI 32c4 .ra: .cfa -9096 + ^ x29: .cfa -9104 + ^
STACK CFI 32cc x21: .cfa -9072 + ^ x22: .cfa -9064 + ^
STACK CFI 32ec x19: .cfa -9088 + ^ x20: .cfa -9080 + ^
STACK CFI 330c x23: .cfa -9056 + ^
STACK CFI 3380 x19: x19 x20: x20
STACK CFI 3384 x23: x23
STACK CFI 33b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 33bc .cfa: sp 9104 + .ra: .cfa -9096 + ^ x19: .cfa -9088 + ^ x20: .cfa -9080 + ^ x21: .cfa -9072 + ^ x22: .cfa -9064 + ^ x29: .cfa -9104 + ^
STACK CFI 33c0 x19: x19 x20: x20
STACK CFI 33c4 x19: .cfa -9088 + ^ x20: .cfa -9080 + ^ x23: .cfa -9056 + ^
STACK CFI 33d0 x19: x19 x20: x20
STACK CFI 33dc x23: x23
STACK CFI 33e4 x19: .cfa -9088 + ^ x20: .cfa -9080 + ^
STACK CFI 33e8 x23: .cfa -9056 + ^
STACK CFI INIT 33f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 33f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3428 b8 .cfa: sp 0 + .ra: x30
STACK CFI 342c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 343c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3478 x21: .cfa -64 + ^
STACK CFI 34b4 x21: x21
STACK CFI 34d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 34dc x21: .cfa -64 + ^
STACK CFI INIT 34e0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 34e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 34f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3538 x21: .cfa -64 + ^
STACK CFI 3574 x21: x21
STACK CFI 3594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3598 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 359c x21: .cfa -64 + ^
STACK CFI INIT 35a0 130 .cfa: sp 0 + .ra: x30
STACK CFI 35a8 .cfa: sp 9136 +
STACK CFI 35b4 .ra: .cfa -9128 + ^ x29: .cfa -9136 + ^
STACK CFI 35bc x21: .cfa -9104 + ^ x22: .cfa -9096 + ^
STACK CFI 35c8 x19: .cfa -9120 + ^ x20: .cfa -9112 + ^
STACK CFI 35ec x23: .cfa -9088 + ^
STACK CFI 368c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3690 .cfa: sp 9136 + .ra: .cfa -9128 + ^ x19: .cfa -9120 + ^ x20: .cfa -9112 + ^ x21: .cfa -9104 + ^ x22: .cfa -9096 + ^ x23: .cfa -9088 + ^ x29: .cfa -9136 + ^
STACK CFI INIT 36d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 36d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 36e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3728 x21: .cfa -64 + ^
STACK CFI 3764 x21: x21
STACK CFI 3784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3788 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 378c x21: .cfa -64 + ^
STACK CFI INIT 3790 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3794 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 37a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 37e8 x21: .cfa -64 + ^
STACK CFI 3824 x21: x21
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3848 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 384c x21: .cfa -64 + ^
STACK CFI INIT 3850 bc .cfa: sp 0 + .ra: x30
STACK CFI 3854 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3864 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 38a4 x21: .cfa -64 + ^
STACK CFI 38e0 x21: x21
STACK CFI 3900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3904 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 3908 x21: .cfa -64 + ^
STACK CFI INIT 3910 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3914 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3924 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 396c x21: .cfa -48 + ^
STACK CFI 39a8 x21: x21
STACK CFI 39c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 39d0 x21: .cfa -48 + ^
STACK CFI INIT 39d8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 39dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 39ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3a18 x21: .cfa -48 + ^
STACK CFI 3a54 x21: x21
STACK CFI 3a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a78 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3a7c x21: .cfa -48 + ^
STACK CFI INIT 3a80 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a90 54 .cfa: sp 0 + .ra: x30
STACK CFI 3a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a9c x19: .cfa -16 + ^
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ae8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3aec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3af4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3b4c x21: .cfa -80 + ^
STACK CFI 3b88 x21: x21
STACK CFI 3ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3bb8 x21: .cfa -80 + ^
STACK CFI INIT 3bc0 74 .cfa: sp 0 + .ra: x30
STACK CFI 3bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bd8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c38 60 .cfa: sp 0 + .ra: x30
STACK CFI 3c3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c50 x19: .cfa -16 + ^
STACK CFI 3c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c98 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d20 9c .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3dc0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3dc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3dd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3e68 6c .cfa: sp 0 + .ra: x30
STACK CFI 3e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ed8 ec .cfa: sp 0 + .ra: x30
STACK CFI 3edc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ee4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3eec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ef4 x23: .cfa -16 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3fc8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3fcc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3fd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3fec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4014 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4078 x19: x19 x20: x20
STACK CFI 407c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4080 x19: x19 x20: x20
STACK CFI 40a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 40a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 40ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT 40b0 150 .cfa: sp 0 + .ra: x30
STACK CFI 40b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 40bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 40c8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 40e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 412c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 41a0 x25: x25 x26: x26
STACK CFI 41c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 41cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 41e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 41f4 x25: x25 x26: x26
STACK CFI 41fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 4200 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 420c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4224 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 424c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 42b0 x19: x19 x20: x20
STACK CFI 42b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 42b8 x19: x19 x20: x20
STACK CFI 42dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 42e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 42e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT 42e8 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4330 4c8 .cfa: sp 0 + .ra: x30
STACK CFI 4334 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 433c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4354 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 436c x23: .cfa -16 + ^
STACK CFI 4424 x19: x19 x20: x20
STACK CFI 4428 x23: x23
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4560 x19: x19 x20: x20
STACK CFI 4568 x23: x23
STACK CFI 456c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4570 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4790 x19: x19 x20: x20
STACK CFI 4794 x23: x23
STACK CFI 4798 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 47a0 x19: x19 x20: x20
STACK CFI 47a4 x23: x23
STACK CFI 47a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 47b0 x19: x19 x20: x20
STACK CFI 47b4 x23: x23
STACK CFI 47c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 47c8 x19: x19 x20: x20
STACK CFI 47cc x23: x23
STACK CFI 47d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 47d8 x19: x19 x20: x20
STACK CFI 47dc x23: x23
STACK CFI 47e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 47f0 x19: x19 x20: x20
STACK CFI 47f4 x23: x23
STACK CFI INIT 47f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4800 64 .cfa: sp 0 + .ra: x30
STACK CFI 4804 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 480c x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 4854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4858 .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x29: .cfa -432 + ^
STACK CFI INIT 4868 ec .cfa: sp 0 + .ra: x30
STACK CFI 486c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4874 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 48fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4900 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4924 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 494c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4958 150 .cfa: sp 0 + .ra: x30
STACK CFI 495c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4964 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4974 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 49a4 x25: .cfa -16 + ^
STACK CFI 4a30 x25: x25
STACK CFI 4a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4a54 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4a84 x25: .cfa -16 + ^
STACK CFI 4aa4 x25: x25
STACK CFI INIT 4aa8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ab8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ac8 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 4acc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4bc8 x21: .cfa -16 + ^
STACK CFI 4bf4 x21: x21
STACK CFI 4c40 x21: .cfa -16 + ^
STACK CFI 4c48 x21: x21
STACK CFI 4c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c98 a20 .cfa: sp 0 + .ra: x30
STACK CFI 4c9c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ca4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4cb0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4cc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d10 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4d58 x19: x19 x20: x20
STACK CFI 4d5c x23: x23 x24: x24
STACK CFI 4d60 x25: x25 x26: x26
STACK CFI 4d64 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4d7c x25: x25 x26: x26
STACK CFI 4da0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4e24 x19: x19 x20: x20
STACK CFI 4e28 x23: x23 x24: x24
STACK CFI 4e2c x25: x25 x26: x26
STACK CFI 4e30 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4ecc x19: x19 x20: x20
STACK CFI 4ed0 x23: x23 x24: x24
STACK CFI 4ed4 x25: x25 x26: x26
STACK CFI 4ee0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4ee4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5028 x19: x19 x20: x20
STACK CFI 502c x23: x23 x24: x24
STACK CFI 5030 x25: x25 x26: x26
STACK CFI 5034 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5088 x19: x19 x20: x20
STACK CFI 508c x23: x23 x24: x24
STACK CFI 5090 x25: x25 x26: x26
STACK CFI 5094 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 50bc x19: x19 x20: x20
STACK CFI 50c0 x23: x23 x24: x24
STACK CFI 50c4 x25: x25 x26: x26
STACK CFI 50c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 532c x19: x19 x20: x20
STACK CFI 5334 x23: x23 x24: x24
STACK CFI 5338 x25: x25 x26: x26
STACK CFI 533c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5340 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5350 x19: x19 x20: x20
STACK CFI 5354 x23: x23 x24: x24
STACK CFI 5358 x25: x25 x26: x26
STACK CFI 535c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5380 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5388 x19: x19 x20: x20
STACK CFI 538c x23: x23 x24: x24
STACK CFI 5390 x25: x25 x26: x26
STACK CFI 5394 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 53fc x19: x19 x20: x20
STACK CFI 5400 x23: x23 x24: x24
STACK CFI 5404 x25: x25 x26: x26
STACK CFI 5408 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5418 x25: x25 x26: x26
STACK CFI 5444 x19: x19 x20: x20
STACK CFI 5448 x23: x23 x24: x24
STACK CFI 544c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5498 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 54a0 x23: x23 x24: x24
STACK CFI 54a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 54ac x19: x19 x20: x20
STACK CFI 54b0 x23: x23 x24: x24
STACK CFI 54b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 54d4 x25: x25 x26: x26
STACK CFI 54fc x19: x19 x20: x20
STACK CFI 5500 x23: x23 x24: x24
STACK CFI 5504 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5520 x19: x19 x20: x20
STACK CFI 5524 x23: x23 x24: x24
STACK CFI 5528 x25: x25 x26: x26
STACK CFI 5534 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 555c x19: x19 x20: x20
STACK CFI 5560 x23: x23 x24: x24
STACK CFI 5564 x25: x25 x26: x26
STACK CFI 5568 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5588 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5590 x19: x19 x20: x20
STACK CFI 5594 x23: x23 x24: x24
STACK CFI 5598 x25: x25 x26: x26
STACK CFI 559c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 55a4 x19: x19 x20: x20
STACK CFI 55a8 x23: x23 x24: x24
STACK CFI 55ac x25: x25 x26: x26
STACK CFI 55b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 55b8 x19: x19 x20: x20
STACK CFI 55bc x23: x23 x24: x24
STACK CFI 55c0 x25: x25 x26: x26
STACK CFI 55c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 55cc x19: x19 x20: x20
STACK CFI 55d0 x23: x23 x24: x24
STACK CFI 55d4 x25: x25 x26: x26
STACK CFI 55d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 55f8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5600 x19: x19 x20: x20
STACK CFI 5604 x23: x23 x24: x24
STACK CFI 5608 x25: x25 x26: x26
STACK CFI 560c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5614 x19: x19 x20: x20
STACK CFI 5618 x23: x23 x24: x24
STACK CFI 561c x25: x25 x26: x26
STACK CFI 5620 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5628 x19: x19 x20: x20
STACK CFI 562c x23: x23 x24: x24
STACK CFI 5630 x25: x25 x26: x26
STACK CFI 5634 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 563c x19: x19 x20: x20
STACK CFI 5640 x23: x23 x24: x24
STACK CFI 5644 x25: x25 x26: x26
STACK CFI 5648 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5650 x19: x19 x20: x20
STACK CFI 5654 x23: x23 x24: x24
STACK CFI 5658 x25: x25 x26: x26
STACK CFI 565c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 567c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 569c x19: x19 x20: x20
STACK CFI 56a0 x23: x23 x24: x24
STACK CFI 56a4 x25: x25 x26: x26
STACK CFI 56a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56b0 x19: x19 x20: x20
STACK CFI 56b4 x23: x23 x24: x24
STACK CFI INIT 56b8 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5788 68 .cfa: sp 0 + .ra: x30
STACK CFI 578c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57f0 144 .cfa: sp 0 + .ra: x30
STACK CFI 57f8 .cfa: sp 9104 +
STACK CFI 57fc .ra: .cfa -9096 + ^ x29: .cfa -9104 + ^
STACK CFI 5804 x21: .cfa -9072 + ^ x22: .cfa -9064 + ^
STACK CFI 5820 x19: .cfa -9088 + ^ x20: .cfa -9080 + ^
STACK CFI 5840 x23: .cfa -9056 + ^
STACK CFI 58b4 x23: x23
STACK CFI 58d0 x19: x19 x20: x20
STACK CFI 58f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 58fc .cfa: sp 9104 + .ra: .cfa -9096 + ^ x19: .cfa -9088 + ^ x20: .cfa -9080 + ^ x21: .cfa -9072 + ^ x22: .cfa -9064 + ^ x29: .cfa -9104 + ^
STACK CFI 5900 x19: x19 x20: x20
STACK CFI 5908 x19: .cfa -9088 + ^ x20: .cfa -9080 + ^ x23: .cfa -9056 + ^
STACK CFI 5910 x19: x19 x20: x20
STACK CFI 5918 x23: x23
STACK CFI 591c x19: .cfa -9088 + ^ x20: .cfa -9080 + ^
STACK CFI 5924 x19: x19 x20: x20
STACK CFI 592c x19: .cfa -9088 + ^ x20: .cfa -9080 + ^
STACK CFI 5930 x23: .cfa -9056 + ^
STACK CFI INIT 5938 68 .cfa: sp 0 + .ra: x30
STACK CFI 593c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5944 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 599c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 59a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 59a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5a08 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a20 124 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 5a30 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 5b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b14 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x29: .cfa -320 + ^
STACK CFI INIT 5b48 f8 .cfa: sp 0 + .ra: x30
STACK CFI 5b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5b98 x21: .cfa -16 + ^
STACK CFI 5bd4 x21: x21
STACK CFI 5bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5bfc x21: x21
STACK CFI 5c00 x21: .cfa -16 + ^
STACK CFI 5c3c x21: x21
STACK CFI INIT 5c40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c50 2bc .cfa: sp 0 + .ra: x30
STACK CFI 5c54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5c60 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5c7c x27: .cfa -48 + ^
STACK CFI 5c88 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5ca0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5ca8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5d54 x21: x21 x22: x22
STACK CFI 5d58 x23: x23 x24: x24
STACK CFI 5d60 x19: x19 x20: x20
STACK CFI 5d84 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5d88 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 5db8 x19: x19 x20: x20
STACK CFI 5dbc x21: x21 x22: x22
STACK CFI 5dc0 x23: x23 x24: x24
STACK CFI 5dc4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5e08 x19: x19 x20: x20
STACK CFI 5e0c x21: x21 x22: x22
STACK CFI 5e10 x23: x23 x24: x24
STACK CFI 5e14 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5e90 x21: x21 x22: x22
STACK CFI 5e94 x23: x23 x24: x24
STACK CFI 5e9c x19: x19 x20: x20
STACK CFI 5ea0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5eac x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5eb4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5ecc x19: x19 x20: x20
STACK CFI 5ed0 x21: x21 x22: x22
STACK CFI 5ed4 x23: x23 x24: x24
STACK CFI 5ed8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5ef0 x19: x19 x20: x20
STACK CFI 5ef4 x21: x21 x22: x22
STACK CFI 5ef8 x23: x23 x24: x24
STACK CFI 5f00 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5f04 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5f08 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 5f10 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 5f18 .cfa: sp 18128 +
STACK CFI 5f1c .ra: .cfa -18120 + ^ x29: .cfa -18128 + ^
STACK CFI 5f24 x21: .cfa -18096 + ^ x22: .cfa -18088 + ^
STACK CFI 5f40 x19: .cfa -18112 + ^ x20: .cfa -18104 + ^
STACK CFI 5f60 x25: .cfa -18064 + ^ x26: .cfa -18056 + ^
STACK CFI 5f6c x23: .cfa -18080 + ^ x24: .cfa -18072 + ^
STACK CFI 6024 x23: x23 x24: x24
STACK CFI 6028 x25: x25 x26: x26
STACK CFI 6054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6058 .cfa: sp 18128 + .ra: .cfa -18120 + ^ x19: .cfa -18112 + ^ x20: .cfa -18104 + ^ x21: .cfa -18096 + ^ x22: .cfa -18088 + ^ x23: .cfa -18080 + ^ x24: .cfa -18072 + ^ x25: .cfa -18064 + ^ x26: .cfa -18056 + ^ x29: .cfa -18128 + ^
STACK CFI 6104 x23: x23 x24: x24
STACK CFI 6108 x25: x25 x26: x26
STACK CFI 610c x23: .cfa -18080 + ^ x24: .cfa -18072 + ^ x25: .cfa -18064 + ^ x26: .cfa -18056 + ^
STACK CFI 6154 x23: x23 x24: x24
STACK CFI 6158 x25: x25 x26: x26
STACK CFI 6174 x23: .cfa -18080 + ^ x24: .cfa -18072 + ^ x25: .cfa -18064 + ^ x26: .cfa -18056 + ^
STACK CFI 6184 x23: x23 x24: x24
STACK CFI 6188 x25: x25 x26: x26
STACK CFI 61a4 x23: .cfa -18080 + ^ x24: .cfa -18072 + ^ x25: .cfa -18064 + ^ x26: .cfa -18056 + ^
STACK CFI 61ac x23: x23 x24: x24
STACK CFI 61b0 x25: x25 x26: x26
STACK CFI 61b8 x23: .cfa -18080 + ^ x24: .cfa -18072 + ^
STACK CFI 61bc x25: .cfa -18064 + ^ x26: .cfa -18056 + ^
STACK CFI INIT 61c8 54 .cfa: sp 0 + .ra: x30
STACK CFI 61cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61d4 x19: .cfa -32 + ^
STACK CFI 6214 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6218 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6220 174 .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 622c .cfa: x29 112 +
STACK CFI 6230 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 624c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 637c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6380 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6398 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6400 8d0 .cfa: sp 0 + .ra: x30
STACK CFI 6404 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 640c .cfa: x29 112 +
STACK CFI 6410 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6430 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 658c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6590 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6cd0 204 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ed8 178 .cfa: sp 0 + .ra: x30
STACK CFI 6edc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6ee4 .cfa: x29 112 +
STACK CFI 6ee8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6f04 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 703c .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7050 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7088 178 .cfa: sp 0 + .ra: x30
STACK CFI 708c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7094 .cfa: x29 112 +
STACK CFI 7098 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 70b4 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 71e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 71ec .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7200 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7238 228 .cfa: sp 0 + .ra: x30
STACK CFI 723c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7240 .cfa: x29 128 +
STACK CFI 7244 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7250 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 7264 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 73d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 73d8 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7460 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 74d0 178 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 74dc .cfa: x29 112 +
STACK CFI 74e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 74fc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7634 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7648 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7680 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76d8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7738 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77a0 190 .cfa: sp 0 + .ra: x30
STACK CFI 77a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 77a8 .cfa: x29 128 +
STACK CFI 77ac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 77bc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 77d0 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7914 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7930 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7968 178 .cfa: sp 0 + .ra: x30
STACK CFI 796c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7974 .cfa: x29 112 +
STACK CFI 7978 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7994 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7acc .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7ae0 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b48 170 .cfa: sp 0 + .ra: x30
STACK CFI 7b4c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7b54 .cfa: x29 96 +
STACK CFI 7b58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7b70 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7ca4 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7cb8 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d38 ec .cfa: sp 0 + .ra: x30
STACK CFI 7d3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7d44 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7d60 x21: .cfa -80 + ^
STACK CFI 7d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7d9c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7e28 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e34 x19: .cfa -16 + ^
STACK CFI 7e9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ee8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7ef8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7f00 118 .cfa: sp 0 + .ra: x30
STACK CFI 7f04 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7f0c x21: .cfa -160 + ^
STACK CFI 7f14 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7fc4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 8018 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8068 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80d0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 80d8 .cfa: sp 4160 +
STACK CFI 80e0 .ra: .cfa -4152 + ^ x29: .cfa -4160 + ^
STACK CFI 80e8 x21: .cfa -4128 + ^ x22: .cfa -4120 + ^
STACK CFI 80fc x19: .cfa -4144 + ^ x20: .cfa -4136 + ^
STACK CFI 8184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8188 .cfa: sp 4160 + .ra: .cfa -4152 + ^ x19: .cfa -4144 + ^ x20: .cfa -4136 + ^ x21: .cfa -4128 + ^ x22: .cfa -4120 + ^ x29: .cfa -4160 + ^
STACK CFI INIT 81c8 148 .cfa: sp 0 + .ra: x30
STACK CFI 81cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 81d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 81e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 826c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 8270 x23: x23
STACK CFI 82b0 x23: .cfa -32 + ^
STACK CFI 8304 x23: x23
STACK CFI 830c x23: .cfa -32 + ^
STACK CFI INIT 8310 204 .cfa: sp 0 + .ra: x30
STACK CFI 8318 .cfa: sp 9200 +
STACK CFI 831c .ra: .cfa -9160 + ^ x29: .cfa -9168 + ^
STACK CFI 8324 x19: .cfa -9152 + ^ x20: .cfa -9144 + ^
STACK CFI 8330 x21: .cfa -9136 + ^ x22: .cfa -9128 + ^
STACK CFI 834c x23: .cfa -9120 + ^ x24: .cfa -9112 + ^
STACK CFI 8358 x25: .cfa -9104 + ^ x26: .cfa -9096 + ^
STACK CFI 8364 x27: .cfa -9088 + ^
STACK CFI 844c x23: x23 x24: x24
STACK CFI 8450 x25: x25 x26: x26
STACK CFI 8454 x27: x27
STACK CFI 847c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8480 .cfa: sp 9200 + .ra: .cfa -9160 + ^ x19: .cfa -9152 + ^ x20: .cfa -9144 + ^ x21: .cfa -9136 + ^ x22: .cfa -9128 + ^ x23: .cfa -9120 + ^ x24: .cfa -9112 + ^ x25: .cfa -9104 + ^ x26: .cfa -9096 + ^ x27: .cfa -9088 + ^ x29: .cfa -9168 + ^
STACK CFI 84f4 x25: x25 x26: x26
STACK CFI 84f8 x27: x27
STACK CFI 8500 x23: x23 x24: x24
STACK CFI 8508 x23: .cfa -9120 + ^ x24: .cfa -9112 + ^
STACK CFI 850c x25: .cfa -9104 + ^ x26: .cfa -9096 + ^
STACK CFI 8510 x27: .cfa -9088 + ^
STACK CFI INIT 8518 2dc .cfa: sp 0 + .ra: x30
STACK CFI 8520 .cfa: sp 17424 +
STACK CFI 8524 .ra: .cfa -17384 + ^ x29: .cfa -17392 + ^
STACK CFI 852c x19: .cfa -17376 + ^ x20: .cfa -17368 + ^
STACK CFI 8538 x21: .cfa -17360 + ^ x22: .cfa -17352 + ^
STACK CFI 855c x23: .cfa -17344 + ^ x24: .cfa -17336 + ^
STACK CFI 8568 x25: .cfa -17328 + ^ x26: .cfa -17320 + ^
STACK CFI 8574 x27: .cfa -17312 + ^ x28: .cfa -17304 + ^
STACK CFI 869c x23: x23 x24: x24
STACK CFI 86a0 x25: x25 x26: x26
STACK CFI 86a4 x27: x27 x28: x28
STACK CFI 86cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 86d0 .cfa: sp 17424 + .ra: .cfa -17384 + ^ x19: .cfa -17376 + ^ x20: .cfa -17368 + ^ x21: .cfa -17360 + ^ x22: .cfa -17352 + ^ x23: .cfa -17344 + ^ x24: .cfa -17336 + ^ x25: .cfa -17328 + ^ x26: .cfa -17320 + ^ x27: .cfa -17312 + ^ x28: .cfa -17304 + ^ x29: .cfa -17392 + ^
STACK CFI 87c0 x23: x23 x24: x24
STACK CFI 87c4 x25: x25 x26: x26
STACK CFI 87c8 x27: x27 x28: x28
STACK CFI 87d0 x23: .cfa -17344 + ^ x24: .cfa -17336 + ^ x25: .cfa -17328 + ^ x26: .cfa -17320 + ^ x27: .cfa -17312 + ^ x28: .cfa -17304 + ^
STACK CFI 87e4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 87e8 x23: .cfa -17344 + ^ x24: .cfa -17336 + ^
STACK CFI 87ec x25: .cfa -17328 + ^ x26: .cfa -17320 + ^
STACK CFI 87f0 x27: .cfa -17312 + ^ x28: .cfa -17304 + ^
STACK CFI INIT 87f8 2fc .cfa: sp 0 + .ra: x30
STACK CFI 8800 .cfa: sp 9280 +
STACK CFI 8804 .ra: .cfa -9240 + ^ x29: .cfa -9248 + ^
STACK CFI 880c x19: .cfa -9232 + ^ x20: .cfa -9224 + ^
STACK CFI 8818 x21: .cfa -9216 + ^ x22: .cfa -9208 + ^
STACK CFI 8834 x25: .cfa -9184 + ^ x26: .cfa -9176 + ^
STACK CFI 8848 x23: .cfa -9200 + ^ x24: .cfa -9192 + ^
STACK CFI 8854 x27: .cfa -9168 + ^ x28: .cfa -9160 + ^
STACK CFI 896c x23: x23 x24: x24
STACK CFI 8970 x27: x27 x28: x28
STACK CFI 899c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 89a0 .cfa: sp 9280 + .ra: .cfa -9240 + ^ x19: .cfa -9232 + ^ x20: .cfa -9224 + ^ x21: .cfa -9216 + ^ x22: .cfa -9208 + ^ x23: .cfa -9200 + ^ x24: .cfa -9192 + ^ x25: .cfa -9184 + ^ x26: .cfa -9176 + ^ x27: .cfa -9168 + ^ x28: .cfa -9160 + ^ x29: .cfa -9248 + ^
STACK CFI 8aa4 x23: x23 x24: x24
STACK CFI 8aa8 x27: x27 x28: x28
STACK CFI 8ab0 x23: .cfa -9200 + ^ x24: .cfa -9192 + ^ x27: .cfa -9168 + ^ x28: .cfa -9160 + ^
STACK CFI 8ae8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 8aec x23: .cfa -9200 + ^ x24: .cfa -9192 + ^
STACK CFI 8af0 x27: .cfa -9168 + ^ x28: .cfa -9160 + ^
STACK CFI INIT 8af8 230 .cfa: sp 0 + .ra: x30
STACK CFI 8b00 .cfa: sp 9200 +
STACK CFI 8b04 .ra: .cfa -9160 + ^ x29: .cfa -9168 + ^
STACK CFI 8b0c x21: .cfa -9136 + ^ x22: .cfa -9128 + ^
STACK CFI 8b2c x19: .cfa -9152 + ^ x20: .cfa -9144 + ^
STACK CFI 8b38 x23: .cfa -9120 + ^ x24: .cfa -9112 + ^
STACK CFI 8b44 x25: .cfa -9104 + ^ x26: .cfa -9096 + ^
STACK CFI 8b4c x27: .cfa -9088 + ^
STACK CFI 8c18 x19: x19 x20: x20
STACK CFI 8c1c x23: x23 x24: x24
STACK CFI 8c20 x25: x25 x26: x26
STACK CFI 8c24 x27: x27
STACK CFI 8c4c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8c50 .cfa: sp 9200 + .ra: .cfa -9160 + ^ x19: .cfa -9152 + ^ x20: .cfa -9144 + ^ x21: .cfa -9136 + ^ x22: .cfa -9128 + ^ x23: .cfa -9120 + ^ x24: .cfa -9112 + ^ x25: .cfa -9104 + ^ x26: .cfa -9096 + ^ x27: .cfa -9088 + ^ x29: .cfa -9168 + ^
STACK CFI 8cc4 x23: x23 x24: x24
STACK CFI 8cc8 x25: x25 x26: x26
STACK CFI 8ccc x27: x27
STACK CFI 8cd4 x19: x19 x20: x20
STACK CFI 8cd8 x19: .cfa -9152 + ^ x20: .cfa -9144 + ^ x23: .cfa -9120 + ^ x24: .cfa -9112 + ^ x25: .cfa -9104 + ^ x26: .cfa -9096 + ^ x27: .cfa -9088 + ^
STACK CFI 8d04 x19: x19 x20: x20
STACK CFI 8d08 x23: x23 x24: x24
STACK CFI 8d0c x25: x25 x26: x26
STACK CFI 8d10 x27: x27
STACK CFI 8d18 x19: .cfa -9152 + ^ x20: .cfa -9144 + ^
STACK CFI 8d1c x23: .cfa -9120 + ^ x24: .cfa -9112 + ^
STACK CFI 8d20 x25: .cfa -9104 + ^ x26: .cfa -9096 + ^
STACK CFI 8d24 x27: .cfa -9088 + ^
STACK CFI INIT 8d28 4f4 .cfa: sp 0 + .ra: x30
STACK CFI 8d30 .cfa: sp 9376 +
STACK CFI 8d38 .ra: .cfa -9288 + ^ x29: .cfa -9296 + ^
STACK CFI 8d54 x19: .cfa -9280 + ^ x20: .cfa -9272 + ^ x21: .cfa -9264 + ^ x22: .cfa -9256 + ^
STACK CFI 8d68 x23: .cfa -9248 + ^ x24: .cfa -9240 + ^ x25: .cfa -9232 + ^ x26: .cfa -9224 + ^ x27: .cfa -9216 + ^ x28: .cfa -9208 + ^
STACK CFI 8f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8f84 .cfa: sp 9376 + .ra: .cfa -9288 + ^ x19: .cfa -9280 + ^ x20: .cfa -9272 + ^ x21: .cfa -9264 + ^ x22: .cfa -9256 + ^ x23: .cfa -9248 + ^ x24: .cfa -9240 + ^ x25: .cfa -9232 + ^ x26: .cfa -9224 + ^ x27: .cfa -9216 + ^ x28: .cfa -9208 + ^ x29: .cfa -9296 + ^
STACK CFI INIT 9220 348 .cfa: sp 0 + .ra: x30
STACK CFI 9228 .cfa: sp 25600 +
STACK CFI 922c .ra: .cfa -25576 + ^ x29: .cfa -25584 + ^
STACK CFI 9234 x21: .cfa -25552 + ^ x22: .cfa -25544 + ^
STACK CFI 9240 x23: .cfa -25536 + ^ x24: .cfa -25528 + ^
STACK CFI 9258 x19: .cfa -25568 + ^ x20: .cfa -25560 + ^
STACK CFI 9264 x25: .cfa -25520 + ^ x26: .cfa -25512 + ^
STACK CFI 92c4 x27: .cfa -25504 + ^ x28: .cfa -25496 + ^
STACK CFI 943c x19: x19 x20: x20
STACK CFI 9440 x25: x25 x26: x26
STACK CFI 9444 x27: x27 x28: x28
STACK CFI 946c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9470 .cfa: sp 25600 + .ra: .cfa -25576 + ^ x19: .cfa -25568 + ^ x20: .cfa -25560 + ^ x21: .cfa -25552 + ^ x22: .cfa -25544 + ^ x23: .cfa -25536 + ^ x24: .cfa -25528 + ^ x25: .cfa -25520 + ^ x26: .cfa -25512 + ^ x27: .cfa -25504 + ^ x28: .cfa -25496 + ^ x29: .cfa -25584 + ^
STACK CFI 9514 x25: x25 x26: x26
STACK CFI 9518 x27: x27 x28: x28
STACK CFI 9520 x19: x19 x20: x20
STACK CFI 9524 x19: .cfa -25568 + ^ x20: .cfa -25560 + ^ x25: .cfa -25520 + ^ x26: .cfa -25512 + ^
STACK CFI 953c x27: .cfa -25504 + ^ x28: .cfa -25496 + ^
STACK CFI 9548 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 954c x19: .cfa -25568 + ^ x20: .cfa -25560 + ^
STACK CFI 9550 x25: .cfa -25520 + ^ x26: .cfa -25512 + ^
STACK CFI 9554 x27: .cfa -25504 + ^ x28: .cfa -25496 + ^
STACK CFI 9558 x27: x27 x28: x28
STACK CFI 9560 x19: x19 x20: x20
STACK CFI 9564 x25: x25 x26: x26
STACK CFI INIT 9568 b0 .cfa: sp 0 + .ra: x30
STACK CFI 956c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 957c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9588 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 95fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9600 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9618 90 .cfa: sp 0 + .ra: x30
STACK CFI 961c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9624 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9674 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 968c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 96a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 96a8 98 .cfa: sp 0 + .ra: x30
STACK CFI 96ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9708 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 971c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9720 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 973c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
