MODULE Linux arm64 BA78B5FE8602372C0738EAF3B14BD73E0 libebl_riscv.so
INFO CODE_ID FEB578BA02862C370738EAF3B14BD73E9AFF5735
PUBLIC 1940 0 riscv_init
STACK CFI INIT 1758 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1788 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 17cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17d4 x19: .cfa -16 + ^
STACK CFI 180c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1818 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1848 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1870 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1880 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1890 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 18ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1918 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1940 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a48 f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b50 160 .cfa: sp 0 + .ra: x30
STACK CFI 1b54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1b5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1b68 x23: .cfa -32 + ^
STACK CFI 1b88 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1b9c x21: x21 x22: x22
STACK CFI 1bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 1bc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 1c34 x21: x21 x22: x22
STACK CFI 1c40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1c74 x21: x21 x22: x22
STACK CFI 1c84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1ca4 x21: x21 x22: x22
STACK CFI 1cac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 1cb0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cd8 234 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f18 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1f1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f40 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1ff0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2038 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 20e8 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 20ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 20f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2108 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2110 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 21bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 21c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2208 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 22b8 370 .cfa: sp 0 + .ra: x30
STACK CFI 22bc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 22c4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 22d4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 22fc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2358 x25: .cfa -96 + ^
STACK CFI 23b0 x21: x21 x22: x22
STACK CFI 23b4 x25: x25
STACK CFI 23dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 23e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 240c x21: x21 x22: x22
STACK CFI 2414 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2430 x21: x21 x22: x22
STACK CFI 2438 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 243c x21: x21 x22: x22
STACK CFI 2440 x25: x25
STACK CFI 2448 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 24bc x21: x21 x22: x22
STACK CFI 24c0 x25: x25
STACK CFI 24c8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 24e8 x21: x21 x22: x22
STACK CFI 24ec x25: x25
STACK CFI 24f0 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 2514 x21: x21 x22: x22
STACK CFI 2518 x25: x25
STACK CFI 2520 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2528 x21: x21 x22: x22
STACK CFI 252c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 255c x21: x21 x22: x22
STACK CFI 2560 x25: x25
STACK CFI 2568 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2570 x21: x21 x22: x22
STACK CFI 2574 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 25a8 x21: x21 x22: x22
STACK CFI 25ac x25: x25
STACK CFI 25b4 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 25c4 x21: x21 x22: x22
STACK CFI 25c8 x25: x25
STACK CFI 25d0 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 25e0 x21: x21 x22: x22
STACK CFI 25e4 x25: x25
STACK CFI 25ec x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 2600 x21: x21 x22: x22
STACK CFI 2604 x25: x25
STACK CFI 2610 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2614 x25: .cfa -96 + ^
STACK CFI 2620 x21: x21 x22: x22
STACK CFI 2624 x25: x25
