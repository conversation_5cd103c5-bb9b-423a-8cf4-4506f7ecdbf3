MODULE Linux arm64 E2EA65D8BD549233901DFFFC656053290 libebl_m68k.so
INFO CODE_ID D865EAE254BD3392901DFFFC65605329CF07542E
PUBLIC 11d0 0 m68k_init
STACK CFI INIT fe8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1018 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1058 48 .cfa: sp 0 + .ra: x30
STACK CFI 105c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1064 x19: .cfa -16 + ^
STACK CFI 109c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10d8 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1100 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1110 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1130 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1134 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 113c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11a8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1288 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1298 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b8 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13c0 22c .cfa: sp 0 + .ra: x30
STACK CFI 13c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 13cc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 13e0 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 13f4 x23: .cfa -96 + ^
STACK CFI 149c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 14a0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 15f0 218 .cfa: sp 0 + .ra: x30
STACK CFI 15f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1600 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1610 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1618 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 169c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 16e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 16e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1808 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1830 8 .cfa: sp 0 + .ra: x30
