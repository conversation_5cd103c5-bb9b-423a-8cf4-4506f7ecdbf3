MODULE Linux arm64 B891A5C80ED00694FDEDF396A1495AC60 libopencv_xobjdetect.so.4.3
INFO CODE_ID C8A591B8D00E9406FDEDF396A1495AC6F1C929E4
PUBLIC 38e8 0 _init
PUBLIC 3f00 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.35]
PUBLIC 3fa0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.39]
PUBLIC 4040 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.91]
PUBLIC 40e0 0 _GLOBAL__sub_I_feature_evaluator.cpp
PUBLIC 4110 0 _GLOBAL__sub_I_lbpfeatures.cpp
PUBLIC 4140 0 _GLOBAL__sub_I_waldboost.cpp
PUBLIC 4170 0 _GLOBAL__sub_I_wbdetector.cpp
PUBLIC 41a0 0 call_weak_fn
PUBLIC 41b8 0 deregister_tm_clones
PUBLIC 41f0 0 register_tm_clones
PUBLIC 4230 0 __do_global_dtors_aux
PUBLIC 4278 0 frame_dummy
PUBLIC 42b0 0 std::ctype<char>::do_widen(char) const
PUBLIC 42b8 0 cv::xobjdetect::CvParams::printAttrs() const
PUBLIC 42c0 0 cv::xobjdetect::CvParams::scanAttr(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 42c8 0 cv::xobjdetect::CvFeatureParams::init(cv::xobjdetect::CvFeatureParams const&)
PUBLIC 42d8 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPEvaluator*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 42e0 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPFeatureParams*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 42e8 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPEvaluator*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 42f0 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPFeatureParams*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4310 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPFeatureParams*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4318 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPFeatureParams*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 4320 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPFeatureParams*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4328 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPEvaluator*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 4330 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPEvaluator*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4338 0 cv::xobjdetect::CvFeatureParams::~CvFeatureParams()
PUBLIC 4368 0 cv::xobjdetect::CvFeatureParams::~CvFeatureParams()
PUBLIC 43a0 0 cv::xobjdetect::CvFeatureParams::read(cv::FileNode const&)
PUBLIC 4438 0 cv::xobjdetect::CvFeatureEvaluator::init(cv::xobjdetect::CvFeatureParams const*, int, cv::Size_<int>)
PUBLIC 4530 0 cv::xobjdetect::CvFeatureEvaluator::setImage(cv::Mat const&, unsigned char, int, std::vector<int, std::allocator<int> > const&)
PUBLIC 45c0 0 cv::xobjdetect::CvParams::printDefaults() const
PUBLIC 4678 0 cv::xobjdetect::CvLBPEvaluator::~CvLBPEvaluator()
PUBLIC 4848 0 std::_Sp_counted_ptr<cv::xobjdetect::CvLBPEvaluator*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a58 0 cv::xobjdetect::CvFeatureParams::write(cv::FileStorage&) const
PUBLIC 4c30 0 cv::xobjdetect::CvLBPEvaluator::~CvLBPEvaluator()
PUBLIC 4e08 0 cv::xobjdetect::CvFeatureParams::CvFeatureParams()
PUBLIC 4eb0 0 cv::xobjdetect::CvFeatureParams::create()
PUBLIC 4f60 0 cv::xobjdetect::CvFeatureEvaluator::create()
PUBLIC 5070 0 cv::xobjdetect::CvLBPEvaluator::operator()(int)
PUBLIC 51f0 0 cv::xobjdetect::CvLBPFeatureParams::~CvLBPFeatureParams()
PUBLIC 5220 0 cv::xobjdetect::CvLBPFeatureParams::~CvLBPFeatureParams()
PUBLIC 5258 0 cv::xobjdetect::CvLBPEvaluator::init(cv::xobjdetect::CvFeatureParams const*, int, cv::Size_<int>)
PUBLIC 5370 0 cv::xobjdetect::CvLBPEvaluator::setImage(cv::Mat const&, unsigned char, int, std::vector<int, std::allocator<int> > const&)
PUBLIC 55b0 0 cv::xobjdetect::CvLBPEvaluator::writeFeatures(cv::FileStorage&, cv::Mat const&) const
PUBLIC 5b20 0 cv::Mat::~Mat()
PUBLIC 5bb0 0 cv::xobjdetect::CvLBPEvaluator::setWindow(cv::Point_<int> const&)
PUBLIC 5ea0 0 cv::xobjdetect::CvLBPFeatureParams::CvLBPFeatureParams()
PUBLIC 5f20 0 void std::vector<cv::xobjdetect::CvLBPEvaluator::Feature, std::allocator<cv::xobjdetect::CvLBPEvaluator::Feature> >::_M_emplace_back_aux<cv::xobjdetect::CvLBPEvaluator::Feature>(cv::xobjdetect::CvLBPEvaluator::Feature&&)
PUBLIC 6108 0 cv::xobjdetect::CvLBPEvaluator::generateFeatures()
PUBLIC 63e0 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 63e8 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 63f8 0 std::basic_ios<char, std::char_traits<char> >::widen(char) const [clone .isra.44] [clone .constprop.158]
PUBLIC 6450 0 cv::xobjdetect::quantize_data(cv::Mat&, cv::Mat_<float>&, cv::Mat_<float>&)
PUBLIC 66e0 0 cv::Mat::operator=(cv::Mat&&)
PUBLIC 6810 0 cv::MatExpr::~MatExpr()
PUBLIC 69c0 0 cv::xobjdetect::WaldBoost::WaldBoost()
PUBLIC 69f0 0 cv::xobjdetect::WaldBoost::get_feature_indices()
PUBLIC 6a80 0 cv::xobjdetect::WaldBoost::predict(cv::Ptr<cv::xobjdetect::CvFeatureEvaluator>, float*) const
PUBLIC 6d30 0 cv::xobjdetect::WaldBoost::write(cv::FileStorage&) const
PUBLIC 7738 0 cv::xobjdetect::WaldBoost::reset(int)
PUBLIC 7768 0 cv::xobjdetect::WaldBoost::~WaldBoost()
PUBLIC 77b8 0 std::vector<bool, std::allocator<bool> >::_M_insert_aux(std::_Bit_iterator, bool)
PUBLIC 7b18 0 void std::vector<float, std::allocator<float> >::_M_emplace_back_aux<float const&>(float const&)
PUBLIC 7c00 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int const&>(int const&)
PUBLIC 7ce8 0 std::vector<float, std::allocator<float> >::_M_default_append(unsigned long)
PUBLIC 7e38 0 std::vector<int, std::allocator<int> >::_M_default_append(unsigned long)
PUBLIC 7f88 0 cv::xobjdetect::WaldBoost::read(cv::FileNode const&)
PUBLIC 83e8 0 void std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >::_M_emplace_back_aux<cv::Rect_<int> >(cv::Rect_<int>&&)
PUBLIC 8500 0 cv::xobjdetect::WaldBoost::detect(cv::Ptr<cv::xobjdetect::CvFeatureEvaluator>, cv::Mat const&, std::vector<float, std::allocator<float> > const&, std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >&, cv::Mat_<float>&)
PUBLIC 9170 0 void std::vector<double, std::allocator<double> >::_M_emplace_back_aux<double>(double&&)
PUBLIC 9260 0 cv::xobjdetect::WaldBoost::detect(cv::Ptr<cv::xobjdetect::CvFeatureEvaluator>, cv::Mat const&, std::vector<float, std::allocator<float> > const&, std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >&, std::vector<double, std::allocator<double> >&)
PUBLIC 9ba0 0 std::_Bvector_base<std::allocator<bool> >::_M_deallocate()
PUBLIC 9bb8 0 void std::vector<float, std::allocator<float> >::_M_emplace_back_aux<float>(float&&)
PUBLIC 9ca0 0 cv::xobjdetect::WaldBoost::fit(cv::Mat&, cv::Mat&)
PUBLIC c9e8 0 std::_Sp_counted_ptr<cv::xobjdetect::WBDetectorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC c9f0 0 std::_Sp_counted_ptr<cv::xobjdetect::WBDetectorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c9f8 0 cv::xobjdetect::WBDetectorImpl::read(cv::FileNode const&)
PUBLIC ca00 0 cv::xobjdetect::WBDetectorImpl::write(cv::FileStorage&) const
PUBLIC ca08 0 std::_Sp_counted_ptr<cv::xobjdetect::WBDetectorImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC ca10 0 std::_Sp_counted_ptr<cv::xobjdetect::WBDetectorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC ca18 0 cv::xobjdetect::WBDetectorImpl::~WBDetectorImpl()
PUBLIC ca28 0 cv::xobjdetect::WBDetectorImpl::~WBDetectorImpl()
PUBLIC ca50 0 std::_Sp_counted_ptr<cv::xobjdetect::WBDetectorImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ca88 0 cv::xobjdetect::WBDetector::create()
PUBLIC cb60 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC cbc0 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC cc80 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC cd40 0 cv::xobjdetect::WBDetectorImpl::detect(cv::Mat const&, std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >&, std::vector<double, std::allocator<double> >&)
PUBLIC d200 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC d550 0 cv::xobjdetect::WBDetectorImpl::train(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC f970 0 _fini
STACK CFI INIT 42b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42c8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4318 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4328 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4330 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4338 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4368 34 .cfa: sp 0 + .ra: x30
STACK CFI 436c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4398 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 43a0 94 .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43ac .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 43cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 43d0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI 4430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 3f00 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3f04 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f10 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3f94 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 4438 f4 .cfa: sp 0 + .ra: x30
STACK CFI 443c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4444 .ra: .cfa -48 + ^
STACK CFI 449c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 44a0 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 4530 90 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4574 .ra: .cfa -48 + ^
STACK CFI INIT 45c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45d4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 4638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4640 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 4678 1cc .cfa: sp 0 + .ra: x30
STACK CFI 467c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 468c .ra: .cfa -16 + ^
STACK CFI 4814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4818 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 4848 210 .cfa: sp 0 + .ra: x30
STACK CFI 484c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4850 .ra: .cfa -16 + ^
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4a08 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4a18 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 4a58 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 4a5c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4a6c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4a94 .ra: .cfa -48 + ^
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4b9c .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 4c30 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c44 .ra: .cfa -16 + ^
STACK CFI 4dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4dc8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 4e08 9c .cfa: sp 0 + .ra: x30
STACK CFI 4e14 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e24 .ra: .cfa -16 + ^
STACK CFI 4e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4e78 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 4eb0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4eb4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ec0 .ra: .cfa -16 + ^
STACK CFI 4f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4f04 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 4f60 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4f64 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f70 .ra: .cfa -16 + ^
STACK CFI 5034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5038 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 40e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 40e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4100 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5070 180 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51f0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5220 38 .cfa: sp 0 + .ra: x30
STACK CFI 5228 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5254 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3fa0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 3fa4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3fb0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 4030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4034 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 5258 118 .cfa: sp 0 + .ra: x30
STACK CFI 525c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5268 .ra: .cfa -40 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 52d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 52e0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT 5370 240 .cfa: sp 0 + .ra: x30
STACK CFI 5374 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5380 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 538c .ra: .cfa -64 + ^
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5570 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 55b0 570 .cfa: sp 0 + .ra: x30
STACK CFI 55b4 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 55c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 55e4 .ra: .cfa -64 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 59e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 59e8 .cfa: sp 144 + .ra: .cfa -64 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 5b20 90 .cfa: sp 0 + .ra: x30
STACK CFI 5b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5b98 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5ba0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5bac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5bb0 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 5bb4 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 5bb8 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 5bc8 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5bd0 .ra: .cfa -224 + ^
STACK CFI 5dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5dc0 .cfa: sp 288 + .ra: .cfa -224 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI INIT 5ea0 80 .cfa: sp 0 + .ra: x30
STACK CFI 5ea4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5eac .ra: .cfa -16 + ^
STACK CFI 5ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5eec .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5f20 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 5f24 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f38 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 60bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 60c0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 6108 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 610c .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 6124 .ra: .cfa -208 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 6384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6388 .cfa: sp 288 + .ra: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 4110 30 .cfa: sp 0 + .ra: x30
STACK CFI 4114 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4130 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 63e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63e8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4040 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4044 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4050 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 40d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 40d4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 63f8 54 .cfa: sp 0 + .ra: x30
STACK CFI 63fc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6414 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 6418 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6444 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 6448 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 6450 288 .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 6458 x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 6470 .ra: .cfa -224 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 66b4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 66b8 .cfa: sp 304 + .ra: .cfa -224 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 66e0 120 .cfa: sp 0 + .ra: x30
STACK CFI 66e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66f0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 67dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 67e0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 6810 1ac .cfa: sp 0 + .ra: x30
STACK CFI 6814 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6820 .ra: .cfa -16 + ^
STACK CFI 697c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6980 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 69c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 69f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 69f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a00 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6a7c .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 6a80 2ac .cfa: sp 0 + .ra: x30
STACK CFI 6a84 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6a8c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6aa4 .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6d00 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6d08 .cfa: sp 96 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6d28 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 6d30 a04 .cfa: sp 0 + .ra: x30
STACK CFI 6d34 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6d48 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6d6c .ra: .cfa -56 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 7578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 757c .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI INIT 7738 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7768 50 .cfa: sp 0 + .ra: x30
STACK CFI 776c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 77ac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 77b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 77b4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 77b8 360 .cfa: sp 0 + .ra: x30
STACK CFI 77bc .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 77d4 .ra: .cfa -24 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 78c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 78d0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 7900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7908 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI INIT 7b18 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7b1c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7b24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7b30 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7bb8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7c00 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7c04 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7c0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c18 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7ca0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7ce8 14c .cfa: sp 0 + .ra: x30
STACK CFI 7cf0 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7d08 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7d58 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7df8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 7e38 14c .cfa: sp 0 + .ra: x30
STACK CFI 7e40 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7e58 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7ea8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7f48 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 7f88 460 .cfa: sp 0 + .ra: x30
STACK CFI 7f8c .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7f98 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7fac .ra: .cfa -136 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^
STACK CFI 8330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 8338 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^
STACK CFI INIT 83e8 110 .cfa: sp 0 + .ra: x30
STACK CFI 83ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 83f4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 83fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 84c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 84c8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 8500 c30 .cfa: sp 0 + .ra: x30
STACK CFI 8504 .cfa: sp 608 +
STACK CFI 8508 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 8510 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 8528 .ra: .cfa -528 + ^ v8: .cfa -512 + ^ v9: .cfa -504 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 8a20 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8a28 .cfa: sp 608 + .ra: .cfa -528 + ^ v8: .cfa -512 + ^ v9: .cfa -504 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI INIT 9170 e8 .cfa: sp 0 + .ra: x30
STACK CFI 9174 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 917c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9188 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 9210 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 9260 918 .cfa: sp 0 + .ra: x30
STACK CFI 9264 .cfa: sp 560 +
STACK CFI 9294 .ra: .cfa -480 + ^ v8: .cfa -464 + ^ v9: .cfa -456 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 9b48 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9b4c .cfa: sp 560 + .ra: .cfa -480 + ^ v8: .cfa -464 + ^ v9: .cfa -456 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI INIT 9ba0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9bb8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 9bbc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9bc4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9bd0 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 9c58 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 9ca0 2cf4 .cfa: sp 0 + .ra: x30
STACK CFI 9ca4 .cfa: sp 3280 +
STACK CFI 9ca8 x27: .cfa -3216 + ^ x28: .cfa -3208 + ^
STACK CFI 9cd0 .ra: .cfa -3200 + ^ v10: .cfa -3168 + ^ v11: .cfa -3160 + ^ v12: .cfa -3152 + ^ v13: .cfa -3144 + ^ v14: .cfa -3192 + ^ v8: .cfa -3184 + ^ v9: .cfa -3176 + ^ x19: .cfa -3280 + ^ x20: .cfa -3272 + ^ x21: .cfa -3264 + ^ x22: .cfa -3256 + ^ x23: .cfa -3248 + ^ x24: .cfa -3240 + ^ x25: .cfa -3232 + ^ x26: .cfa -3224 + ^
STACK CFI be54 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI be58 .cfa: sp 3280 + .ra: .cfa -3200 + ^ v10: .cfa -3168 + ^ v11: .cfa -3160 + ^ v12: .cfa -3152 + ^ v13: .cfa -3144 + ^ v14: .cfa -3192 + ^ v8: .cfa -3184 + ^ v9: .cfa -3176 + ^ x19: .cfa -3280 + ^ x20: .cfa -3272 + ^ x21: .cfa -3264 + ^ x22: .cfa -3256 + ^ x23: .cfa -3248 + ^ x24: .cfa -3240 + ^ x25: .cfa -3232 + ^ x26: .cfa -3224 + ^ x27: .cfa -3216 + ^ x28: .cfa -3208 + ^
STACK CFI INIT 4140 30 .cfa: sp 0 + .ra: x30
STACK CFI 4144 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4160 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c9e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca28 24 .cfa: sp 0 + .ra: x30
STACK CFI ca2c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ca48 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ca50 38 .cfa: sp 0 + .ra: x30
STACK CFI ca54 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ca78 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI ca80 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ca84 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ca88 d8 .cfa: sp 0 + .ra: x30
STACK CFI ca8c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ca98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI caa0 .ra: .cfa -16 + ^
STACK CFI cb18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI cb1c .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT cb60 5c .cfa: sp 0 + .ra: x30
STACK CFI cb64 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cb68 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI cbac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI cbb0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI cbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT cbc0 bc .cfa: sp 0 + .ra: x30
STACK CFI cbc4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cbc8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI cc6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI cc70 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI cc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT cc80 b4 .cfa: sp 0 + .ra: x30
STACK CFI cc88 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cc94 .ra: .cfa -16 + ^
STACK CFI ccbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI ccc0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cd04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI cd10 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT cd40 494 .cfa: sp 0 + .ra: x30
STACK CFI cd48 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI cd68 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI cd88 .ra: .cfa -200 + ^ v8: .cfa -192 + ^ v9: .cfa -184 + ^ x25: .cfa -208 + ^
STACK CFI cfe0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI cfe4 .cfa: sp 256 + .ra: .cfa -200 + ^ v8: .cfa -192 + ^ v9: .cfa -184 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^
STACK CFI INIT d200 334 .cfa: sp 0 + .ra: x30
STACK CFI d204 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d210 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d220 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI d478 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT d550 2408 .cfa: sp 0 + .ra: x30
STACK CFI d554 .cfa: sp 1296 +
STACK CFI d558 x27: .cfa -1232 + ^ x28: .cfa -1224 + ^
STACK CFI d578 .ra: .cfa -1216 + ^ v8: .cfa -1208 + ^ x19: .cfa -1296 + ^ x20: .cfa -1288 + ^ x21: .cfa -1280 + ^ x22: .cfa -1272 + ^ x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^
STACK CFI f8e0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f8e4 .cfa: sp 1296 + .ra: .cfa -1216 + ^ v8: .cfa -1208 + ^ x19: .cfa -1296 + ^ x20: .cfa -1288 + ^ x21: .cfa -1280 + ^ x22: .cfa -1272 + ^ x23: .cfa -1264 + ^ x24: .cfa -1256 + ^ x25: .cfa -1248 + ^ x26: .cfa -1240 + ^ x27: .cfa -1232 + ^ x28: .cfa -1224 + ^
STACK CFI INIT 4170 30 .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4190 .cfa: sp 0 + .ra: .ra x19: x19
