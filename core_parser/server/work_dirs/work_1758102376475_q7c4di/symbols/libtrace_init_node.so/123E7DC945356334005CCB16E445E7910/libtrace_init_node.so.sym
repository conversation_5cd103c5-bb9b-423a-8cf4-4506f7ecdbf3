MODULE Linux arm64 123E7DC945356334005CCB16E445E7910 libtrace_init_node.so
INFO CODE_ID C97D3E1235453463005CCB16E445E791
PUBLIC 2088 0 _init
PUBLIC 2270 0 _GLOBAL__sub_I_trace_init_node.cpp
PUBLIC 22dc 0 call_weak_fn
PUBLIC 22f0 0 deregister_tm_clones
PUBLIC 2320 0 register_tm_clones
PUBLIC 235c 0 __do_global_dtors_aux
PUBLIC 23ac 0 frame_dummy
PUBLIC 23b0 0 lios::tracing::TraceInitNode::Exit()
PUBLIC 23c0 0 lios::tracing::TraceInitNode::Init(int, char**)
PUBLIC 2460 0 lios_class_loader_destroy_TraceInitNode
PUBLIC 27b0 0 lios_class_loader_create_TraceInitNode
PUBLIC 2a20 0 lios::tracing::TraceInitNode::~TraceInitNode()
PUBLIC 2d40 0 lios::tracing::TraceInitNode::~TraceInitNode()
PUBLIC 3060 0 lios::config::settings::ParamConfig::~ParamConfig()
PUBLIC 3120 0 std::vector<lios::config::settings::NodeConfig::MessageTaskConfig, std::allocator<lios::config::settings::NodeConfig::MessageTaskConfig> >::~vector()
PUBLIC 3210 0 std::vector<lios::config::settings::NodeConfig::TimerTaskConfig, std::allocator<lios::config::settings::NodeConfig::TimerTaskConfig> >::~vector()
PUBLIC 3300 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 33b0 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*)
PUBLIC 3400 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 352c 0 _fini
STACK CFI INIT 22f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2320 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 235c 50 .cfa: sp 0 + .ra: x30
STACK CFI 236c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2374 x19: .cfa -16 + ^
STACK CFI 23a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23ac 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23c0 9c .cfa: sp 0 + .ra: x30
STACK CFI 23c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23d4 x21: .cfa -48 + ^
STACK CFI 2458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a20 320 .cfa: sp 0 + .ra: x30
STACK CFI 2a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a44 x21: .cfa -16 + ^
STACK CFI 2cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2d40 31c .cfa: sp 0 + .ra: x30
STACK CFI 2d44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d64 x21: .cfa -16 + ^
STACK CFI 3028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 302c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3060 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3064 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3070 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3090 x21: .cfa -16 + ^
STACK CFI 30e4 x21: x21
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3114 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 311c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2460 348 .cfa: sp 0 + .ra: x30
STACK CFI 2468 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2478 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 249c x21: .cfa -16 + ^
STACK CFI 275c x21: x21
STACK CFI 2760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2764 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2798 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 27a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3120 ec .cfa: sp 0 + .ra: x30
STACK CFI 3124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 312c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3134 x21: .cfa -16 + ^
STACK CFI 31e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3210 ec .cfa: sp 0 + .ra: x30
STACK CFI 3214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 321c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3224 x21: .cfa -16 + ^
STACK CFI 32d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 32dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 32f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 27b0 268 .cfa: sp 0 + .ra: x30
STACK CFI 27b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 27d0 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2958 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3300 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 330c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 332c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 33a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 33b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 33b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 33ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3400 12c .cfa: sp 0 + .ra: x30
STACK CFI 3404 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 340c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 341c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3440 x21: x21 x22: x22
STACK CFI 344c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3450 x23: .cfa -16 + ^
STACK CFI 34ec x21: x21 x22: x22
STACK CFI 34f0 x23: x23
STACK CFI 351c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3520 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2270 6c .cfa: sp 0 + .ra: x30
STACK CFI 2274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 227c x19: .cfa -16 + ^
STACK CFI 22bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 22c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
