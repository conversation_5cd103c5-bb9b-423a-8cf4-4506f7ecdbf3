MODULE Linux arm64 E8216679520BD347B12D9DAFCC958D5C0 libebl_mips.so
INFO CODE_ID 796621E80B5247D3B12D9DAFCC958D5C729C0001
PUBLIC e50 0 mips_init
STACK CFI INIT c88 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT cb8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT cf8 48 .cfa: sp 0 + .ra: x30
STACK CFI cfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d04 x19: .cfa -16 + ^
STACK CFI d3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d48 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d78 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT da0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT db0 a0 .cfa: sp 0 + .ra: x30
STACK CFI db4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI dbc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e28 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT e50 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT ed0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT ef0 138 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1028 200 .cfa: sp 0 + .ra: x30
STACK CFI 102c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1034 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1044 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 10ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10b0 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 10e4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 10f0 x25: .cfa -160 + ^
STACK CFI 11d8 x23: x23 x24: x24
STACK CFI 11dc x25: x25
STACK CFI 11e0 x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI 11e4 x23: x23 x24: x24
STACK CFI 11e8 x25: x25
STACK CFI 120c x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI 1214 x23: x23 x24: x24
STACK CFI 1218 x25: x25
STACK CFI 1220 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1224 x25: .cfa -160 + ^
STACK CFI INIT 1228 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1268 408 .cfa: sp 0 + .ra: x30
STACK CFI 126c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1274 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1280 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1288 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 12d4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 137c x21: x21 x22: x22
STACK CFI 13a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13a8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI 13cc x21: x21 x22: x22
STACK CFI 13d4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 143c x21: x21 x22: x22
STACK CFI 1440 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 151c x21: x21 x22: x22
STACK CFI 1520 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 15c0 x21: x21 x22: x22
STACK CFI 15c4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1634 x21: x21 x22: x22
STACK CFI 1644 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1668 x21: x21 x22: x22
STACK CFI 166c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI INIT 1670 24 .cfa: sp 0 + .ra: x30
