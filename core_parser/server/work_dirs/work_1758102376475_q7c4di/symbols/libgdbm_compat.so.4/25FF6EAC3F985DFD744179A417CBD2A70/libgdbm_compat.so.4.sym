MODULE Linux arm64 25FF6EAC3F985DFD744179A417CBD2A70 libgdbm_compat.so.4
INFO CODE_ID AC6EFF25983FFD5D744179A417CBD2A7AB273D23
PUBLIC 10b8 0 dbminit
PUBLIC 1138 0 delete
PUBLIC 1150 0 fetch
PUBLIC 1168 0 store
PUBLIC 1198 0 firstkey
PUBLIC 11a8 0 nextkey
PUBLIC 11b8 0 dbmclose
PUBLIC 14a0 0 dbm_open
PUBLIC 1698 0 dbm_delete
PUBLIC 16f8 0 dbm_error
PUBLIC 1700 0 dbm_clearerr
PUBLIC 1708 0 dbm_fetch
PUBLIC 1798 0 dbm_store
PUBLIC 1800 0 dbm_firstkey
PUBLIC 1890 0 dbm_nextkey
PUBLIC 1930 0 dbm_close
PUBLIC 1978 0 dbm_dirfno
PUBLIC 1980 0 dbm_pagfno
PUBLIC 1990 0 dbm_rdonly
STACK CFI INIT ff8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1028 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1068 48 .cfa: sp 0 + .ra: x30
STACK CFI 106c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1074 x19: .cfa -16 + ^
STACK CFI 10ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10b8 7c .cfa: sp 0 + .ra: x30
STACK CFI 10bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1108 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1138 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1150 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1168 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1198 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b8 34 .cfa: sp 0 + .ra: x30
STACK CFI 11bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11c4 x19: .cfa -16 + ^
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11f0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 11f4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 11fc x23: .cfa -320 + ^
STACK CFI 1204 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 1210 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 12e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12e8 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x29: .cfa -368 + ^
STACK CFI INIT 14a0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 14a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1578 x25: .cfa -16 + ^
STACK CFI 15e4 x25: x25
STACK CFI 1600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1604 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1630 x25: .cfa -16 + ^
STACK CFI 1660 x25: x25
STACK CFI INIT 1698 5c .cfa: sp 0 + .ra: x30
STACK CFI 169c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1708 90 .cfa: sp 0 + .ra: x30
STACK CFI 170c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1714 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1720 x21: .cfa -16 + ^
STACK CFI 1760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1764 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1798 64 .cfa: sp 0 + .ra: x30
STACK CFI 179c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 17cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 17f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1800 8c .cfa: sp 0 + .ra: x30
STACK CFI 1804 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 180c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1818 x21: .cfa -16 + ^
STACK CFI 1854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1858 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1890 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 189c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18b4 x21: .cfa -16 + ^
STACK CFI 18f8 x21: x21
STACK CFI 18fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1900 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1920 x21: x21
STACK CFI 192c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1930 48 .cfa: sp 0 + .ra: x30
STACK CFI 1934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 193c x19: .cfa -16 + ^
STACK CFI 1974 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1978 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1980 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1990 14 .cfa: sp 0 + .ra: x30
