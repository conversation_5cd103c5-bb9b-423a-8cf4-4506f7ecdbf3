MODULE Linux arm64 5615A45663C226CB727A10471FDC986A0 libe2e_fusion_base_defines.so
INFO CODE_ID 56A41556C263CB26727A10471FDC986A
FILE 0 /home/<USER>/agent/workspace/MAX/app/e2e_fusion/code/common/base_defines/global_flags.cpp
FILE 1 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/basic_string.h
FILE 2 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
FILE 3 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/char_traits.h
FILE 4 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
FILE 5 /opt/aarch64--glibc--stable-2022.03-1/aarch64-buildroot-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
FILE 6 /root/.conan/data/gflags/2.2.2/_/_/package/322498af116b919454cd4e39505e3895582bffdc/include/gflags/gflags.h
FUNC 1fe0 16d4 0 _GLOBAL__sub_I_global_flags.cpp
1fe0 c 58 0
1fec 8 9 0
1ff4 4 58 0
1ff8 8 565 6
2000 4 58 0
2004 10 565 6
2014 4 58 0
2018 4 9 0
201c 4 247 1
2020 4 58 0
2024 4 565 6
2028 4 160 1
202c 4 451 1
2030 4 160 1
2034 4 451 1
2038 4 247 1
203c 4 9 0
2040 8 9 0
2048 8 247 1
2050 2c 9 0
207c 8 585 6
2084 8 9 0
208c 4 585 6
2090 4 25 0
2094 4 585 6
2098 8 9 0
20a0 8 565 6
20a8 4 9 0
20ac 8 12 0
20b4 4 9 0
20b8 4 12 0
20bc 4 9 0
20c0 20 12 0
20e0 4 9 0
20e4 4 12 0
20e8 4 13 0
20ec c 565 6
20f8 4 13 0
20fc 4 451 1
2100 4 160 1
2104 4 451 1
2108 4 160 1
210c 8 247 1
2114 8 247 1
211c 28 13 0
2144 8 585 6
214c 4 13 0
2150 4 585 6
2154 4 13 0
2158 4 585 6
215c 8 565 6
2164 8 13 0
216c 4 565 6
2170 4 13 0
2174 4 565 6
2178 4 13 0
217c 4 14 0
2180 4 13 0
2184 4 565 6
2188 4 451 1
218c 4 160 1
2190 4 451 1
2194 4 160 1
2198 4 247 1
219c 4 14 0
21a0 4 247 1
21a4 8 247 1
21ac 28 14 0
21d4 8 585 6
21dc 4 14 0
21e0 4 585 6
21e4 4 14 0
21e8 4 585 6
21ec 8 565 6
21f4 8 14 0
21fc 4 565 6
2200 4 14 0
2204 4 565 6
2208 4 14 0
220c 4 15 0
2210 4 14 0
2214 4 565 6
2218 4 451 1
221c 4 160 1
2220 4 451 1
2224 4 160 1
2228 4 247 1
222c 4 15 0
2230 4 247 1
2234 8 247 1
223c 28 15 0
2264 8 585 6
226c 4 15 0
2270 4 585 6
2274 4 15 0
2278 4 585 6
227c 8 565 6
2284 8 15 0
228c 4 565 6
2290 4 15 0
2294 4 565 6
2298 4 15 0
229c 4 16 0
22a0 4 15 0
22a4 4 565 6
22a8 4 451 1
22ac 4 160 1
22b0 4 451 1
22b4 4 160 1
22b8 4 247 1
22bc 4 16 0
22c0 4 247 1
22c4 8 247 1
22cc 28 16 0
22f4 8 585 6
22fc 4 16 0
2300 4 585 6
2304 4 16 0
2308 4 585 6
230c 8 565 6
2314 8 16 0
231c 4 565 6
2320 4 16 0
2324 4 565 6
2328 4 16 0
232c 4 17 0
2330 4 16 0
2334 4 565 6
2338 4 451 1
233c 4 160 1
2340 4 451 1
2344 4 160 1
2348 4 247 1
234c 4 17 0
2350 4 247 1
2354 8 247 1
235c 28 17 0
2384 8 585 6
238c 4 17 0
2390 4 585 6
2394 4 17 0
2398 4 585 6
239c 8 565 6
23a4 8 17 0
23ac 4 565 6
23b0 4 17 0
23b4 4 565 6
23b8 4 17 0
23bc 4 18 0
23c0 4 17 0
23c4 4 565 6
23c8 4 451 1
23cc 4 160 1
23d0 4 451 1
23d4 4 160 1
23d8 4 247 1
23dc 4 18 0
23e0 4 247 1
23e4 8 247 1
23ec 28 18 0
2414 8 585 6
241c 4 18 0
2420 4 585 6
2424 4 18 0
2428 4 585 6
242c 8 565 6
2434 8 18 0
243c 4 19 0
2440 4 18 0
2444 4 19 0
2448 4 18 0
244c 24 19 0
2470 4 18 0
2474 4 19 0
2478 c 565 6
2484 4 22 0
2488 4 451 1
248c 4 160 1
2490 4 451 1
2494 4 160 1
2498 4 247 1
249c 4 22 0
24a0 4 247 1
24a4 8 247 1
24ac 28 22 0
24d4 8 585 6
24dc 4 22 0
24e0 4 585 6
24e4 4 22 0
24e8 4 585 6
24ec 8 565 6
24f4 8 22 0
24fc 4 565 6
2500 4 22 0
2504 4 565 6
2508 4 22 0
250c 4 565 6
2510 4 247 1
2514 4 22 0
2518 4 565 6
251c 4 451 1
2520 4 160 1
2524 4 451 1
2528 4 160 1
252c 4 247 1
2530 4 25 0
2534 8 247 1
253c 28 25 0
2564 8 585 6
256c 4 25 0
2570 8 565 6
2578 8 585 6
2580 4 25 0
2584 4 26 0
2588 4 247 1
258c 8 25 0
2594 4 565 6
2598 4 25 0
259c 4 565 6
25a0 4 25 0
25a4 c 565 6
25b0 4 25 0
25b4 4 565 6
25b8 4 451 1
25bc 4 160 1
25c0 4 451 1
25c4 4 160 1
25c8 4 247 1
25cc 4 26 0
25d0 8 247 1
25d8 28 26 0
2600 8 585 6
2608 4 26 0
260c 4 585 6
2610 4 26 0
2614 4 585 6
2618 4 247 1
261c 8 26 0
2624 c 565 6
2630 4 26 0
2634 4 565 6
2638 4 26 0
263c 4 565 6
2640 4 247 1
2644 4 26 0
2648 4 565 6
264c 4 451 1
2650 4 160 1
2654 4 451 1
2658 4 27 0
265c 4 160 1
2660 8 247 1
2668 4 27 0
266c 4 247 1
2670 28 27 0
2698 8 585 6
26a0 8 565 6
26a8 4 585 6
26ac 4 27 0
26b0 4 585 6
26b4 4 27 0
26b8 4 247 1
26bc 4 32 0
26c0 10 27 0
26d0 c 565 6
26dc 4 27 0
26e0 8 565 6
26e8 4 27 0
26ec 4 565 6
26f0 8 451 1
26f8 4 160 1
26fc 4 451 1
2700 4 28 0
2704 4 160 1
2708 8 247 1
2710 4 28 0
2714 4 247 1
2718 28 28 0
2740 8 585 6
2748 4 28 0
274c 4 585 6
2750 4 28 0
2754 4 585 6
2758 4 247 1
275c 8 28 0
2764 8 565 6
276c 8 28 0
2774 4 247 1
2778 4 28 0
277c c 565 6
2788 4 247 1
278c 4 28 0
2790 4 565 6
2794 8 451 1
279c 4 160 1
27a0 4 451 1
27a4 4 31 0
27a8 4 160 1
27ac 8 247 1
27b4 4 31 0
27b8 4 247 1
27bc 28 31 0
27e4 8 585 6
27ec 4 31 0
27f0 4 585 6
27f4 8 565 6
27fc 4 585 6
2800 4 31 0
2804 4 247 1
2808 10 31 0
2818 c 565 6
2824 8 31 0
282c 4 565 6
2830 8 451 1
2838 4 160 1
283c 4 451 1
2840 4 160 1
2844 4 247 1
2848 4 32 0
284c 8 247 1
2854 28 32 0
287c 8 585 6
2884 4 32 0
2888 4 585 6
288c 8 565 6
2894 4 585 6
2898 4 32 0
289c 4 35 0
28a0 8 32 0
28a8 4 565 6
28ac 4 32 0
28b0 c 565 6
28bc 4 32 0
28c0 4 247 1
28c4 4 32 0
28c8 4 565 6
28cc 8 451 1
28d4 4 160 1
28d8 4 451 1
28dc 4 33 0
28e0 4 160 1
28e4 8 247 1
28ec 4 33 0
28f0 4 247 1
28f4 28 33 0
291c 14 585 6
2930 18 33 0
2948 c 565 6
2954 4 33 0
2958 8 565 6
2960 4 33 0
2964 4 565 6
2968 8 451 1
2970 4 160 1
2974 4 451 1
2978 4 34 0
297c 4 160 1
2980 8 247 1
2988 4 34 0
298c 4 247 1
2990 28 34 0
29b8 8 585 6
29c0 4 34 0
29c4 4 585 6
29c8 4 34 0
29cc 4 585 6
29d0 4 247 1
29d4 10 34 0
29e4 c 565 6
29f0 4 34 0
29f4 8 565 6
29fc 4 34 0
2a00 4 565 6
2a04 8 451 1
2a0c 4 160 1
2a10 4 451 1
2a14 4 160 1
2a18 4 247 1
2a1c 4 35 0
2a20 8 247 1
2a28 28 35 0
2a50 8 585 6
2a58 4 35 0
2a5c 4 585 6
2a60 4 35 0
2a64 4 585 6
2a68 8 247 1
2a70 8 35 0
2a78 4 565 6
2a7c 4 35 0
2a80 c 565 6
2a8c 4 35 0
2a90 4 565 6
2a94 4 35 0
2a98 4 565 6
2a9c 8 451 1
2aa4 4 160 1
2aa8 4 451 1
2aac 4 36 0
2ab0 4 160 1
2ab4 8 247 1
2abc 4 36 0
2ac0 4 247 1
2ac4 28 36 0
2aec 8 585 6
2af4 4 36 0
2af8 4 585 6
2afc 4 36 0
2b00 4 585 6
2b04 4 247 1
2b08 10 36 0
2b18 c 565 6
2b24 4 36 0
2b28 8 565 6
2b30 4 36 0
2b34 4 565 6
2b38 8 451 1
2b40 4 160 1
2b44 4 451 1
2b48 4 37 0
2b4c 4 160 1
2b50 8 247 1
2b58 4 37 0
2b5c 4 247 1
2b60 28 37 0
2b88 8 585 6
2b90 4 37 0
2b94 4 585 6
2b98 4 37 0
2b9c 4 585 6
2ba0 4 247 1
2ba4 10 37 0
2bb4 c 565 6
2bc0 4 37 0
2bc4 8 565 6
2bcc 4 37 0
2bd0 4 565 6
2bd4 8 451 1
2bdc 4 160 1
2be0 4 451 1
2be4 4 38 0
2be8 4 160 1
2bec 8 247 1
2bf4 4 38 0
2bf8 4 247 1
2bfc 28 38 0
2c24 8 585 6
2c2c 4 38 0
2c30 4 585 6
2c34 4 38 0
2c38 4 585 6
2c3c 4 247 1
2c40 4 42 0
2c44 10 38 0
2c54 4 565 6
2c58 4 38 0
2c5c 8 565 6
2c64 4 38 0
2c68 4 565 6
2c6c 8 451 1
2c74 4 160 1
2c78 4 451 1
2c7c 4 39 0
2c80 4 160 1
2c84 8 247 1
2c8c 4 39 0
2c90 4 247 1
2c94 28 39 0
2cbc 8 585 6
2cc4 4 39 0
2cc8 4 585 6
2ccc 4 39 0
2cd0 4 585 6
2cd4 10 39 0
2ce4 c 565 6
2cf0 4 39 0
2cf4 8 565 6
2cfc 4 247 1
2d00 4 39 0
2d04 4 565 6
2d08 8 451 1
2d10 4 160 1
2d14 4 451 1
2d18 4 40 0
2d1c 4 160 1
2d20 4 247 1
2d24 4 40 0
2d28 8 247 1
2d30 28 40 0
2d58 8 585 6
2d60 4 40 0
2d64 4 585 6
2d68 4 40 0
2d6c 4 585 6
2d70 4 43 0
2d74 10 40 0
2d84 14 565 6
2d98 4 40 0
2d9c 8 565 6
2da4 4 40 0
2da8 4 565 6
2dac 8 451 1
2db4 4 160 1
2db8 4 451 1
2dbc 4 41 0
2dc0 4 160 1
2dc4 8 247 1
2dcc 4 41 0
2dd0 4 247 1
2dd4 28 41 0
2dfc 8 585 6
2e04 4 41 0
2e08 4 585 6
2e0c 4 41 0
2e10 4 585 6
2e14 4 247 1
2e18 10 41 0
2e28 c 565 6
2e34 4 41 0
2e38 8 565 6
2e40 4 41 0
2e44 4 565 6
2e48 8 451 1
2e50 4 160 1
2e54 4 451 1
2e58 4 160 1
2e5c 4 247 1
2e60 4 42 0
2e64 8 247 1
2e6c 28 42 0
2e94 8 585 6
2e9c 4 42 0
2ea0 4 585 6
2ea4 8 565 6
2eac 4 585 6
2eb0 4 42 0
2eb4 4 247 1
2eb8 8 42 0
2ec0 4 565 6
2ec4 4 42 0
2ec8 8 565 6
2ed0 4 46 0
2ed4 8 42 0
2edc 4 565 6
2ee0 8 451 1
2ee8 4 160 1
2eec 4 451 1
2ef0 4 160 1
2ef4 4 247 1
2ef8 4 43 0
2efc 8 247 1
2f04 28 43 0
2f2c 8 585 6
2f34 4 43 0
2f38 4 585 6
2f3c 8 565 6
2f44 4 585 6
2f48 4 43 0
2f4c 4 247 1
2f50 8 43 0
2f58 4 565 6
2f5c 4 43 0
2f60 8 565 6
2f68 4 247 1
2f6c 8 43 0
2f74 4 565 6
2f78 8 451 1
2f80 4 160 1
2f84 4 451 1
2f88 4 44 0
2f8c 4 160 1
2f90 8 247 1
2f98 4 44 0
2f9c 4 247 1
2fa0 28 44 0
2fc8 8 585 6
2fd0 4 44 0
2fd4 4 585 6
2fd8 8 565 6
2fe0 4 585 6
2fe4 14 44 0
2ff8 c 565 6
3004 8 44 0
300c 4 565 6
3010 8 451 1
3018 4 160 1
301c 4 451 1
3020 4 160 1
3024 4 247 1
3028 4 46 0
302c 8 247 1
3034 30 46 0
3064 8 585 6
306c 4 46 0
3070 4 585 6
3074 4 46 0
3078 4 585 6
307c 8 46 0
3084 c 565 6
3090 4 46 0
3094 4 565 6
3098 4 46 0
309c 4 565 6
30a0 8 247 1
30a8 4 46 0
30ac 4 565 6
30b0 8 451 1
30b8 4 160 1
30bc 4 451 1
30c0 4 47 0
30c4 4 160 1
30c8 8 247 1
30d0 4 47 0
30d4 4 247 1
30d8 28 47 0
3100 8 585 6
3108 4 47 0
310c 4 585 6
3110 4 47 0
3114 4 585 6
3118 10 47 0
3128 c 565 6
3134 4 47 0
3138 8 565 6
3140 4 247 1
3144 4 47 0
3148 4 565 6
314c 8 451 1
3154 4 160 1
3158 4 451 1
315c 4 48 0
3160 4 160 1
3164 8 247 1
316c 4 48 0
3170 4 247 1
3174 28 48 0
319c 8 585 6
31a4 4 48 0
31a8 4 585 6
31ac 4 48 0
31b0 4 585 6
31b4 10 48 0
31c4 14 565 6
31d8 4 48 0
31dc 4 247 1
31e0 4 565 6
31e4 4 48 0
31e8 8 565 6
31f0 8 451 1
31f8 4 160 1
31fc 4 451 1
3200 4 50 0
3204 4 160 1
3208 8 247 1
3210 4 50 0
3214 4 247 1
3218 28 50 0
3240 8 585 6
3248 4 50 0
324c 4 585 6
3250 4 50 0
3254 4 585 6
3258 4 247 1
325c 10 50 0
326c c 565 6
3278 4 50 0
327c 8 565 6
3284 4 50 0
3288 4 565 6
328c 8 451 1
3294 4 160 1
3298 4 451 1
329c 4 51 0
32a0 4 160 1
32a4 8 247 1
32ac 4 51 0
32b0 4 247 1
32b4 28 51 0
32dc 8 585 6
32e4 4 51 0
32e8 4 585 6
32ec 4 51 0
32f0 4 585 6
32f4 4 247 1
32f8 10 51 0
3308 c 565 6
3314 4 51 0
3318 8 565 6
3320 4 51 0
3324 4 565 6
3328 8 451 1
3330 4 160 1
3334 4 451 1
3338 4 52 0
333c 4 160 1
3340 8 247 1
3348 4 52 0
334c 4 247 1
3350 28 52 0
3378 8 585 6
3380 4 52 0
3384 4 585 6
3388 4 52 0
338c 4 585 6
3390 4 247 1
3394 10 52 0
33a4 c 565 6
33b0 4 52 0
33b4 8 565 6
33bc 4 52 0
33c0 4 565 6
33c4 8 451 1
33cc 4 160 1
33d0 4 451 1
33d4 4 53 0
33d8 4 160 1
33dc 8 247 1
33e4 4 53 0
33e8 4 247 1
33ec 28 53 0
3414 8 585 6
341c 4 53 0
3420 4 585 6
3424 4 53 0
3428 4 585 6
342c 4 247 1
3430 10 53 0
3440 c 565 6
344c 4 53 0
3450 8 565 6
3458 4 53 0
345c 4 565 6
3460 8 451 1
3468 4 160 1
346c 4 451 1
3470 4 54 0
3474 4 160 1
3478 4 247 1
347c 4 54 0
3480 8 247 1
3488 28 54 0
34b0 8 585 6
34b8 4 54 0
34bc 4 585 6
34c0 4 54 0
34c4 4 585 6
34c8 4 58 0
34cc 4 247 1
34d0 10 54 0
34e0 c 565 6
34ec 4 54 0
34f0 8 565 6
34f8 4 54 0
34fc 4 565 6
3500 8 451 1
3508 4 160 1
350c 4 451 1
3510 4 55 0
3514 4 160 1
3518 8 247 1
3520 4 55 0
3524 4 247 1
3528 28 55 0
3550 8 585 6
3558 4 55 0
355c 4 585 6
3560 4 55 0
3564 4 585 6
3568 10 55 0
3578 4 565 6
357c 4 55 0
3580 4 565 6
3584 4 55 0
3588 8 565 6
3590 8 451 1
3598 4 160 1
359c 4 451 1
35a0 4 57 0
35a4 4 160 1
35a8 8 247 1
35b0 4 57 0
35b4 4 247 1
35b8 28 57 0
35e0 8 585 6
35e8 4 57 0
35ec 4 585 6
35f0 4 57 0
35f4 4 585 6
35f8 10 57 0
3608 4 565 6
360c 4 57 0
3610 8 565 6
3618 4 57 0
361c 4 565 6
3620 8 451 1
3628 4 160 1
362c 4 451 1
3630 4 160 1
3634 4 247 1
3638 4 58 0
363c 8 247 1
3644 28 58 0
366c 8 585 6
3674 8 58 0
367c 8 585 6
3684 10 58 0
3694 14 58 0
36a8 4 58 0
36ac 8 58 0
FUNC 3790 4c 0 fLS::StringFlagDestructor::~StringFlagDestructor()
3790 8 587 6
3798 4 222 1
379c 4 587 6
37a0 4 587 6
37a4 4 222 1
37a8 8 231 1
37b0 4 128 5
37b4 8 222 1
37bc 8 231 1
37c4 4 590 6
37c8 4 590 6
37cc 4 128 5
37d0 4 590 6
37d4 8 590 6
FUNC 37e0 b4 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&)
37e0 10 525 1
37f0 4 193 1
37f4 4 157 1
37f8 c 527 1
3804 4 335 3
3808 4 335 3
380c 4 215 2
3810 4 335 3
3814 8 217 2
381c 8 348 1
3824 4 349 1
3828 4 300 3
382c 4 300 3
3830 4 232 2
3834 4 183 1
3838 4 300 3
383c 4 527 1
3840 4 527 1
3844 8 527 1
384c 8 363 3
3854 8 219 2
385c c 219 2
3868 4 179 1
386c 8 211 1
3874 10 365 3
3884 4 365 3
3888 4 212 2
388c 8 212 2
FUNC 38a0 ac 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag)
38a0 4 206 2
38a4 8 211 2
38ac c 206 2
38b8 4 211 2
38bc 4 104 4
38c0 c 215 2
38cc 8 217 2
38d4 4 225 2
38d8 4 348 1
38dc 4 225 2
38e0 4 348 1
38e4 4 349 1
38e8 8 300 3
38f0 4 232 2
38f4 4 183 1
38f8 4 300 3
38fc 4 233 2
3900 4 233 2
3904 8 233 2
390c 8 363 3
3914 4 219 2
3918 4 219 2
391c 4 219 2
3920 4 179 1
3924 4 211 1
3928 4 211 1
392c c 365 3
3938 4 365 3
393c 4 365 3
3940 4 212 2
3944 8 212 2
PUBLIC 1ef8 0 _init
PUBLIC 36b4 0 call_weak_fn
PUBLIC 36c8 0 deregister_tm_clones
PUBLIC 36f8 0 register_tm_clones
PUBLIC 3734 0 __do_global_dtors_aux
PUBLIC 3784 0 frame_dummy
PUBLIC 394c 0 _fini
STACK CFI INIT 36c8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36f8 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3734 50 .cfa: sp 0 + .ra: x30
STACK CFI 3744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 374c x19: .cfa -16 + ^
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3784 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3790 4c .cfa: sp 0 + .ra: x30
STACK CFI 3794 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37a0 x19: .cfa -16 + ^
STACK CFI 37cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 37d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 37e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 37f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 384c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 38a0 ac .cfa: sp 0 + .ra: x30
STACK CFI 38a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 38b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 3908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 390c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1fe0 16d4 .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1fec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1ff8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2008 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2018 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2024 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 36b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
