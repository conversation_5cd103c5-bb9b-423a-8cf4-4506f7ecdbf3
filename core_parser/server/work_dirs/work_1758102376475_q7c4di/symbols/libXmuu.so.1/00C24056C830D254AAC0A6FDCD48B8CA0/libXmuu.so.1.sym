MODULE Linux arm64 00C24056C830D254AAC0A6FDCD48B8CA0 libXmuu.so.1
INFO CODE_ID 5640C20030C854D2AAC0A6FDCD48B8CA3A4562E8
PUBLIC 16a8 0 XmuClientWindow
PUBLIC 1790 0 XmuCursorNameToIndex
PUBLIC 1840 0 XmuPrintDefaultErrorMessage
PUBLIC 1de0 0 XmuSimpleErrorHandler
PUBLIC 1e40 0 XmuGetHostname
PUBLIC 1e80 0 XmuCopyISOLatin1Lowered
PUBLIC 1f08 0 XmuCopyISOLatin1Uppered
PUBLIC 1f90 0 XmuCompareISOLatin1
PUBLIC 2138 0 XmuNCopyISOLatin1Lowered
PUBLIC 21f0 0 XmuNCopyISOLatin1Uppered
PUBLIC 22a8 0 XmuSnprintf
STACK CFI INIT 1468 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1498 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 14dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14e4 x19: .cfa -16 + ^
STACK CFI 151c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1520 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1528 180 .cfa: sp 0 + .ra: x30
STACK CFI 152c .cfa: sp 208 +
STACK CFI 1538 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1540 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 154c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 1558 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1588 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1594 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 1610 x23: x23 x24: x24
STACK CFI 1614 x25: x25 x26: x26
STACK CFI 1640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 1644 .cfa: sp 208 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 1648 x23: x23 x24: x24
STACK CFI 164c x25: x25 x26: x26
STACK CFI 1650 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 1690 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1694 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 1698 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 16a8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 16ac .cfa: sp 128 +
STACK CFI 16b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 171c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1720 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1790 ac .cfa: sp 0 + .ra: x30
STACK CFI 1794 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 179c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 17a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 182c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1830 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1840 5a0 .cfa: sp 0 + .ra: x30
STACK CFI 1848 .cfa: sp 16528 +
STACK CFI 184c .ra: .cfa -16520 + ^ x29: .cfa -16528 + ^
STACK CFI 1854 x19: .cfa -16512 + ^ x20: .cfa -16504 + ^
STACK CFI 185c x21: .cfa -16496 + ^ x22: .cfa -16488 + ^
STACK CFI 1868 x23: .cfa -16480 + ^ x24: .cfa -16472 + ^
STACK CFI 1874 x25: .cfa -16464 + ^ x26: .cfa -16456 + ^
STACK CFI 187c x27: .cfa -16448 + ^ x28: .cfa -16440 + ^
STACK CFI 1af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1afc .cfa: sp 16528 + .ra: .cfa -16520 + ^ x19: .cfa -16512 + ^ x20: .cfa -16504 + ^ x21: .cfa -16496 + ^ x22: .cfa -16488 + ^ x23: .cfa -16480 + ^ x24: .cfa -16472 + ^ x25: .cfa -16464 + ^ x26: .cfa -16456 + ^ x27: .cfa -16448 + ^ x28: .cfa -16440 + ^ x29: .cfa -16528 + ^
STACK CFI INIT 1de0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e40 3c .cfa: sp 0 + .ra: x30
STACK CFI 1e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1e80 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f08 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f90 1a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2138 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21f0 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a8 cc .cfa: sp 0 + .ra: x30
STACK CFI 22ac .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 22b8 x19: .cfa -304 + ^
STACK CFI 236c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2370 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x29: .cfa -320 + ^
