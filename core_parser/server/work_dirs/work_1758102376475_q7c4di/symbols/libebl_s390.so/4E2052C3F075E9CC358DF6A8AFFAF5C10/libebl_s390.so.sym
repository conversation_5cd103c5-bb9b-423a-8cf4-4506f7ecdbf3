MODULE Linux arm64 4E2052C3F075E9CC358DF6A8AFFAF5C10 libebl_s390.so
INFO CODE_ID C352204E75F0CCE9358DF6A8AFFAF5C1418B3C87
PUBLIC 1e20 0 s390_init
STACK CFI INIT 1c38 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c68 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ca8 48 .cfa: sp 0 + .ra: x30
STACK CFI 1cac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cb4 x19: .cfa -16 + ^
STACK CFI 1cec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1cf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cf8 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d28 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d80 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1d84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1d8c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1df8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1e20 13c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f60 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f80 178 .cfa: sp 0 + .ra: x30
STACK CFI 1f84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1f8c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1f9c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1fc4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1fd4 x21: x21 x22: x22
STACK CFI 1ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1ffc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 202c x25: .cfa -96 + ^
STACK CFI 20a8 x21: x21 x22: x22
STACK CFI 20b8 x25: x25
STACK CFI 20bc x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 20d4 x25: x25
STACK CFI 20dc x21: x21 x22: x22
STACK CFI 20e0 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^
STACK CFI 20e4 x21: x21 x22: x22
STACK CFI 20e8 x25: x25
STACK CFI 20f0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 20f4 x25: .cfa -96 + ^
STACK CFI INIT 20f8 23c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2338 214 .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2344 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2358 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 236c x23: .cfa -144 + ^
STACK CFI 2414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2418 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2550 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 2554 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2560 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2570 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2578 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 260c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2658 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2848 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 284c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2858 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2868 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2870 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2904 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 294c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2950 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2af8 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b38 44 .cfa: sp 0 + .ra: x30
STACK CFI 2b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b80 324 .cfa: sp 0 + .ra: x30
STACK CFI 2b84 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 2b8c x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 2bb0 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 2bc4 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 2bd4 x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 2bdc x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 2dec x19: x19 x20: x20
STACK CFI 2df0 x21: x21 x22: x22
STACK CFI 2df4 x23: x23 x24: x24
STACK CFI 2df8 x27: x27 x28: x28
STACK CFI 2e18 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 2e1c .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI 2e6c x19: x19 x20: x20
STACK CFI 2e70 x21: x21 x22: x22
STACK CFI 2e74 x23: x23 x24: x24
STACK CFI 2e78 x27: x27 x28: x28
STACK CFI 2e7c x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 2e80 x19: x19 x20: x20
STACK CFI 2e84 x21: x21 x22: x22
STACK CFI 2e88 x23: x23 x24: x24
STACK CFI 2e8c x27: x27 x28: x28
STACK CFI 2e94 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 2e98 x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 2e9c x23: .cfa -384 + ^ x24: .cfa -376 + ^
STACK CFI 2ea0 x27: .cfa -352 + ^ x28: .cfa -344 + ^
