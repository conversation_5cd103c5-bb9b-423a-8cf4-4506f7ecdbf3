MODULE Linux arm64 0F8EE5CE574CD6254C9A1027352B826D0 libmenu.so.6
INFO CODE_ID CEE58E0F4C5725D64C9A1027352B826D224A0107
PUBLIC 2628 0 set_menu_fore
PUBLIC 26c0 0 menu_fore
PUBLIC 26d8 0 set_menu_back
PUBLIC 2770 0 menu_back
PUBLIC 2788 0 set_menu_grey
PUBLIC 2820 0 menu_grey
PUBLIC 28a8 0 pos_menu_cursor
PUBLIC 2c68 0 menu_driver
PUBLIC 3408 0 set_menu_format
PUBLIC 3578 0 menu_format
PUBLIC 3ef0 0 set_menu_init
PUBLIC 3f28 0 menu_init
PUBLIC 3f40 0 set_menu_term
PUBLIC 3f78 0 menu_term
PUBLIC 3f90 0 set_item_init
PUBLIC 3fc8 0 item_init
PUBLIC 3fe0 0 set_item_term
PUBLIC 4018 0 item_term
PUBLIC 4030 0 set_current_item
PUBLIC 4158 0 current_item
PUBLIC 4178 0 item_index
PUBLIC 4198 0 item_name
PUBLIC 41b0 0 item_description
PUBLIC 4220 0 new_item
PUBLIC 4338 0 free_item
PUBLIC 4398 0 set_menu_mark
PUBLIC 44f8 0 menu_mark
PUBLIC 4510 0 set_item_opts
PUBLIC 45f8 0 item_opts_off
PUBLIC 4640 0 item_opts_on
PUBLIC 4660 0 item_opts
PUBLIC 4680 0 set_top_row
PUBLIC 4778 0 top_row
PUBLIC 47a0 0 set_item_userptr
PUBLIC 47d8 0 item_userptr
PUBLIC 47f0 0 set_item_value
PUBLIC 48e0 0 item_value
PUBLIC 48f8 0 item_visible
PUBLIC 4948 0 set_menu_items
PUBLIC 4a10 0 menu_items
PUBLIC 4a28 0 item_count
PUBLIC 4a40 0 new_menu_sp
PUBLIC 4b00 0 new_menu
PUBLIC 4b18 0 free_menu
PUBLIC 4ba0 0 set_menu_opts
PUBLIC 4c78 0 menu_opts_off
PUBLIC 4c98 0 menu_opts_on
PUBLIC 4cb8 0 menu_opts
PUBLIC 4cd8 0 set_menu_pad
PUBLIC 4d70 0 menu_pad
PUBLIC 4d88 0 menu_pattern
PUBLIC 4db0 0 set_menu_pattern
PUBLIC 55d8 0 post_menu
PUBLIC 57b0 0 unpost_menu
PUBLIC 58c8 0 menu_request_name
PUBLIC 5910 0 menu_request_by_name
PUBLIC 5a10 0 scale_menu
PUBLIC 5a98 0 set_menu_spacing
PUBLIC 5b78 0 menu_spacing
PUBLIC 5bd0 0 set_menu_sub
PUBLIC 5c58 0 menu_sub
PUBLIC 5c90 0 set_menu_userptr
PUBLIC 5cc8 0 menu_userptr
PUBLIC 5ce0 0 set_menu_win
PUBLIC 5d68 0 menu_win
STACK CFI INIT 2568 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2598 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d8 48 .cfa: sp 0 + .ra: x30
STACK CFI 25dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25e4 x19: .cfa -16 + ^
STACK CFI 261c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2628 94 .cfa: sp 0 + .ra: x30
STACK CFI 262c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2640 x21: .cfa -16 + ^
STACK CFI 2688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 268c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26d8 94 .cfa: sp 0 + .ra: x30
STACK CFI 26dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26f0 x21: .cfa -16 + ^
STACK CFI 2738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 273c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2770 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2788 94 .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27a0 x21: .cfa -16 + ^
STACK CFI 27e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 27ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2820 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2838 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28a8 184 .cfa: sp 0 + .ra: x30
STACK CFI 28ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 28b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 28cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 28f0 x23: .cfa -32 + ^
STACK CFI 296c x23: x23
STACK CFI 299c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 29b4 x23: x23
STACK CFI 29b8 x23: .cfa -32 + ^
STACK CFI 29e8 x23: x23
STACK CFI 29ec x23: .cfa -32 + ^
STACK CFI 2a20 x23: x23
STACK CFI 2a28 x23: .cfa -32 + ^
STACK CFI INIT 2a30 238 .cfa: sp 0 + .ra: x30
STACK CFI 2a34 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2a3c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2a4c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2a84 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2a90 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2a94 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2ab8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2abc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2ac0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2ac4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2b8c x23: x23 x24: x24
STACK CFI 2b90 x25: x25 x26: x26
STACK CFI 2b9c x27: x27 x28: x28
STACK CFI 2bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2bb0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2c28 x23: x23 x24: x24
STACK CFI 2c2c x25: x25 x26: x26
STACK CFI 2c30 x27: x27 x28: x28
STACK CFI 2c38 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2c54 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 2c68 7a0 .cfa: sp 0 + .ra: x30
STACK CFI 2c6c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2c74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2c7c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2c98 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d70 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 2d94 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2d98 x25: x25 x26: x26
STACK CFI 2dd0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 2ddc x25: x25 x26: x26
STACK CFI 3064 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 30d4 x25: x25 x26: x26
STACK CFI 3210 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 322c x25: x25 x26: x26
STACK CFI 3290 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 337c x25: x25 x26: x26
STACK CFI 3384 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 33d4 x25: x25 x26: x26
STACK CFI 33d8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3404 x25: x25 x26: x26
STACK CFI INIT 3408 170 .cfa: sp 0 + .ra: x30
STACK CFI 340c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3414 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3424 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3430 x23: .cfa -16 + ^
STACK CFI 34ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 34f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3578 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 35c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35d4 x19: .cfa -16 + ^
STACK CFI 361c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3628 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3630 180 .cfa: sp 0 + .ra: x30
STACK CFI 3634 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3640 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3658 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36b4 x19: x19 x20: x20
STACK CFI 36c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 36c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3700 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3784 x23: x23 x24: x24
STACK CFI 3794 x19: x19 x20: x20
STACK CFI 379c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 37a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 37a8 x23: x23 x24: x24
STACK CFI INIT 37b0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3818 3c8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3be0 dc .cfa: sp 0 + .ra: x30
STACK CFI 3be4 .cfa: sp 48 +
STACK CFI 3be8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bf0 x19: .cfa -16 + ^
STACK CFI 3c10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c14 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c94 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3cc0 22c .cfa: sp 0 + .ra: x30
STACK CFI 3cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ccc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cdc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3cec x23: .cfa -16 + ^
STACK CFI 3d98 x23: x23
STACK CFI 3d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3e84 x23: x23
STACK CFI 3e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ec8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ef0 34 .cfa: sp 0 + .ra: x30
STACK CFI 3ef4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f28 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f40 34 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f78 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f90 34 .cfa: sp 0 + .ra: x30
STACK CFI 3f94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fc8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fe0 34 .cfa: sp 0 + .ra: x30
STACK CFI 3fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4018 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4030 128 .cfa: sp 0 + .ra: x30
STACK CFI 4034 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4040 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4050 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4158 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4178 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4198 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c8 58 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 420c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4210 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 421c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4220 114 .cfa: sp 0 + .ra: x30
STACK CFI 4224 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 422c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4234 x21: .cfa -16 + ^
STACK CFI 42dc x21: x21
STACK CFI 42e0 x21: .cfa -16 + ^
STACK CFI 42e4 x21: x21
STACK CFI 4300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4304 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4310 x21: x21
STACK CFI 431c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4320 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 432c x21: x21
STACK CFI INIT 4338 60 .cfa: sp 0 + .ra: x30
STACK CFI 433c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4378 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4398 160 .cfa: sp 0 + .ra: x30
STACK CFI 439c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43c0 x25: .cfa -16 + ^
STACK CFI 4464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4468 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 44f8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4510 e4 .cfa: sp 0 + .ra: x30
STACK CFI 4514 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4524 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4560 x19: x19 x20: x20
STACK CFI 456c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4570 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4594 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45dc x19: x19 x20: x20
STACK CFI 45f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45f8 44 .cfa: sp 0 + .ra: x30
STACK CFI 4620 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4640 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4660 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4680 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4684 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 468c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4698 x21: .cfa -16 + ^
STACK CFI 4718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 471c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4778 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a0 34 .cfa: sp 0 + .ra: x30
STACK CFI 47a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 47d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 47d8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47f0 ec .cfa: sp 0 + .ra: x30
STACK CFI 47f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4804 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 483c x19: x19 x20: x20
STACK CFI 4850 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4854 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4868 x19: x19 x20: x20
STACK CFI 487c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48bc x19: x19 x20: x20
STACK CFI 48c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48d4 x19: x19 x20: x20
STACK CFI INIT 48e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48f8 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4948 c8 .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4958 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4970 x21: .cfa -16 + ^
STACK CFI 49b4 x19: x19 x20: x20
STACK CFI 49bc x21: x21
STACK CFI 49c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 49c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 49d0 x19: x19 x20: x20
STACK CFI 49d8 x21: x21
STACK CFI 49dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49e0 x19: x19 x20: x20
STACK CFI 49f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4a04 x19: x19 x20: x20
STACK CFI 4a0c x21: x21
STACK CFI INIT 4a10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a28 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a40 bc .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4ac8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b18 84 .cfa: sp 0 + .ra: x30
STACK CFI 4b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ba0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4bac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bb8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c78 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c98 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4cb8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4cd8 98 .cfa: sp 0 + .ra: x30
STACK CFI 4cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ce4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4cf0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4d70 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d88 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4db0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 4db4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4dbc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4dc4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4dd8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4e38 x25: .cfa -48 + ^
STACK CFI 4e98 x25: x25
STACK CFI 4ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ec8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 4ee8 x25: .cfa -48 + ^
STACK CFI 4ef0 x25: x25
STACK CFI 4f84 x25: .cfa -48 + ^
STACK CFI INIT 4f88 4ac .cfa: sp 0 + .ra: x30
STACK CFI 4f8c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4f94 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4fb0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 520c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5328 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5438 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 543c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5444 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5464 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 55d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 55d8 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 55dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5708 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57b0 118 .cfa: sp 0 + .ra: x30
STACK CFI 57b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57c4 x21: .cfa -16 + ^
STACK CFI 5878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 587c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 58c8 44 .cfa: sp 0 + .ra: x30
STACK CFI 58ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5908 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5910 100 .cfa: sp 0 + .ra: x30
STACK CFI 5914 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 591c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5938 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 59c4 x19: x19 x20: x20
STACK CFI 59f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 59fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5a04 x19: x19 x20: x20
STACK CFI 5a0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 5a10 88 .cfa: sp 0 + .ra: x30
STACK CFI 5a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a28 x21: .cfa -16 + ^
STACK CFI 5a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5a98 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5a9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ab0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5abc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ac8 x23: .cfa -16 + ^
STACK CFI 5b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5b58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5b78 54 .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bd0 84 .cfa: sp 0 + .ra: x30
STACK CFI 5bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5bdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5be8 x21: .cfa -16 + ^
STACK CFI 5c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c58 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c90 34 .cfa: sp 0 + .ra: x30
STACK CFI 5c94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5cc8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ce0 84 .cfa: sp 0 + .ra: x30
STACK CFI 5ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5cf8 x21: .cfa -16 + ^
STACK CFI 5d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d68 30 .cfa: sp 0 + .ra: x30
