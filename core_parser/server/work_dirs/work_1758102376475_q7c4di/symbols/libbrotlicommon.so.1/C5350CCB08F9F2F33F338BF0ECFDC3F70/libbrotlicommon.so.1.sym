MODULE Linux arm64 C5350CCB08F9F2F33F338BF0ECFDC3F70 libbrotlicommon.so.1
INFO CODE_ID CB0C35C5F908F3F23F338BF0ECFDC3F7C9B646CE
PUBLIC 818 0 BrotliGetDictionary
PUBLIC 828 0 BrotliSetDictionaryData
PUBLIC 848 0 BrotliDefaultAllocFunc
PUBLIC 850 0 BrotliDefaultFreeFunc
PUBLIC 858 0 BrotliGetTransforms
PUBLIC 868 0 BrotliTransformDictionaryWord
STACK CFI INIT 758 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 788 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c8 48 .cfa: sp 0 + .ra: x30
STACK CFI 7cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d4 x19: .cfa -16 + ^
STACK CFI 80c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 818 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 828 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 848 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 858 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 868 1fc .cfa: sp 0 + .ra: x30
