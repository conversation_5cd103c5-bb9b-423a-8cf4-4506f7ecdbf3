MODULE Linux arm64 0814E2792842444AF20AB6E33DE2827C0 libnuma.so.1
INFO CODE_ID 79E2140842284A44F20AB6E33DE2827C8717BEE7
PUBLIC 3848 0 numa_bitmask_isbitset
PUBLIC 3910 0 numa_bitmask_setall
PUBLIC 3950 0 numa_bitmask_clearall
PUBLIC 39a0 0 numa_bitmask_setbit
PUBLIC 39d0 0 numa_bitmask_clearbit
PUBLIC 3a08 0 numa_bitmask_nbytes
PUBLIC 3a20 0 numa_bitmask_free
PUBLIC 3a50 0 numa_bitmask_equal
PUBLIC 3ad8 0 numa_bitmask_weight
PUBLIC 3b30 0 numa_error
PUBLIC 3b80 0 numa_bitmask_alloc
PUBLIC 3ce8 0 numa_warn
PUBLIC 3e20 0 numa_pagesize
PUBLIC 3e58 0 numa_num_configured_nodes
PUBLIC 3ed8 0 numa_num_configured_cpus
PUBLIC 3ee8 0 numa_num_possible_nodes
PUBLIC 3ef8 0 numa_num_possible_cpus
PUBLIC 3f08 0 numa_num_task_nodes
PUBLIC 3f18 0 numa_num_thread_nodes
PUBLIC 3f20 0 numa_num_task_cpus
PUBLIC 3f30 0 numa_num_thread_cpus
PUBLIC 3f38 0 numa_max_node
PUBLIC 3f50 0 numa_max_possible_node
PUBLIC 3f68 0 numa_allocate_cpumask
PUBLIC 3f80 0 numa_allocate_nodemask
PUBLIC 3f98 0 numa_node_size64
PUBLIC 4218 0 numa_node_size
PUBLIC 4278 0 numa_available
PUBLIC 42c0 0 numa_interleave_memory
PUBLIC 4318 0 numa_interleave_memory
PUBLIC 4328 0 numa_tonode_memory
PUBLIC 4390 0 numa_tonodemask_memory
PUBLIC 4400 0 numa_tonodemask_memory
PUBLIC 4438 0 numa_setlocal_memory
PUBLIC 4448 0 numa_police_memory
PUBLIC 44b0 0 numa_alloc
PUBLIC 4518 0 numa_realloc
PUBLIC 4538 0 numa_alloc_interleaved_subset
PUBLIC 45d8 0 numa_alloc_interleaved_subset
PUBLIC 4658 0 numa_alloc_interleaved
PUBLIC 4668 0 numa_set_interleave_mask
PUBLIC 46b8 0 numa_get_interleave_node
PUBLIC 4720 0 numa_alloc_onnode
PUBLIC 47b8 0 numa_alloc_local
PUBLIC 4818 0 numa_set_bind_policy
PUBLIC 4850 0 numa_set_membind
PUBLIC 4860 0 numa_set_membind
PUBLIC 4870 0 copy_bitmask_to_nodemask
PUBLIC 48f0 0 numa_get_interleave_mask
PUBLIC 49a8 0 copy_bitmask_to_bitmask
PUBLIC 4a28 0 numa_get_interleave_mask
PUBLIC 50b0 0 copy_nodemask_to_bitmask
PUBLIC 5128 0 numa_set_interleave_mask
PUBLIC 51a8 0 numa_get_membind
PUBLIC 5278 0 numa_get_membind
PUBLIC 52f8 0 numa_get_mems_allowed
PUBLIC 5358 0 numa_free
PUBLIC 5360 0 numa_parse_bitmap
PUBLIC 5520 0 numa_parse_bitmap
PUBLIC 56e8 0 numa_node_to_cpus
PUBLIC 5a20 0 numa_node_to_cpus
PUBLIC 5d30 0 numa_node_of_cpu
PUBLIC 5dd8 0 numa_run_on_node_mask
PUBLIC 6010 0 numa_run_on_node_mask
PUBLIC 6188 0 numa_run_on_node_mask_all
PUBLIC 6300 0 numa_get_run_node_mask
PUBLIC 6478 0 numa_get_run_node_mask
PUBLIC 65b0 0 numa_migrate_pages
PUBLIC 65f8 0 numa_move_pages
PUBLIC 6610 0 numa_run_on_node
PUBLIC 66a8 0 numa_preferred
PUBLIC 6760 0 numa_set_preferred
PUBLIC 67c0 0 numa_set_localalloc
PUBLIC 67d8 0 numa_bind
PUBLIC 6838 0 numa_bind
PUBLIC 6860 0 numa_set_strict
PUBLIC 68a0 0 numa_parse_nodestring
PUBLIC 68b0 0 numa_parse_nodestring_all
PUBLIC 68c0 0 numa_parse_cpustring
PUBLIC 68d0 0 numa_parse_cpustring_all
PUBLIC 68e0 0 get_mempolicy
PUBLIC 6900 0 mbind
PUBLIC 6920 0 set_mempolicy
PUBLIC 6938 0 migrate_pages
PUBLIC 6950 0 move_pages
PUBLIC 6970 0 numa_sched_setaffinity
PUBLIC 6998 0 numa_sched_setaffinity
PUBLIC 69d8 0 numa_sched_getaffinity
PUBLIC 6a00 0 numa_sched_getaffinity
PUBLIC 6a40 0 numa_distance
STACK CFI INIT 3758 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3788 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 37c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37cc x19: .cfa -16 + ^
STACK CFI 3804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3808 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3810 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3828 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3848 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3868 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3870 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3878 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3880 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3910 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3950 50 .cfa: sp 0 + .ra: x30
STACK CFI 3964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3994 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 39a0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 39d0 34 .cfa: sp 0 + .ra: x30
STACK CFI 39e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3a08 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a20 30 .cfa: sp 0 + .ra: x30
STACK CFI 3a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a30 x19: .cfa -16 + ^
STACK CFI 3a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e90 bc .cfa: sp 0 + .ra: x30
STACK CFI 2e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e9c x19: .cfa -16 + ^
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a50 88 .cfa: sp 0 + .ra: x30
STACK CFI 3a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ab4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ad8 58 .cfa: sp 0 + .ra: x30
STACK CFI 3ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f50 168 .cfa: sp 0 + .ra: x30
STACK CFI 2f54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f5c .cfa: x29 80 +
STACK CFI 2f60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f74 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30a4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3b30 4c .cfa: sp 0 + .ra: x30
STACK CFI 3b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b80 8c .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c10 60 .cfa: sp 0 + .ra: x30
STACK CFI 3c14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c70 34 .cfa: sp 0 + .ra: x30
STACK CFI 3c78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ca8 3c .cfa: sp 0 + .ra: x30
STACK CFI 3cb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3cd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ce8 138 .cfa: sp 0 + .ra: x30
STACK CFI 3cec .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 3cf4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 3d20 x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 3d60 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 3df0 x25: x25 x26: x26
STACK CFI 3e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e18 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI 3e1c x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI INIT 3e20 34 .cfa: sp 0 + .ra: x30
STACK CFI 3e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e2c x19: .cfa -16 + ^
STACK CFI 3e50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e58 80 .cfa: sp 0 + .ra: x30
STACK CFI 3e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ec0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3ed8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ee8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ef8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f08 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f38 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f50 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f68 14 .cfa: sp 0 + .ra: x30
STACK CFI 3f6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f80 14 .cfa: sp 0 + .ra: x30
STACK CFI 3f84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f98 27c .cfa: sp 0 + .ra: x30
STACK CFI 3f9c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3fac x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 3fcc v8: .cfa -128 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 402c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4038 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 4188 x23: x23 x24: x24
STACK CFI 418c x25: x25 x26: x26
STACK CFI 41bc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 41c0 .cfa: sp 224 + .ra: .cfa -216 + ^ v8: .cfa -128 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 41ec x23: x23 x24: x24
STACK CFI 41f0 x25: x25 x26: x26
STACK CFI 420c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 4210 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI INIT 30b8 68c .cfa: sp 0 + .ra: x30
STACK CFI 30bc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 30c4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 30cc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 30dc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3114 .cfa: sp 160 + .ra: .cfa -152 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 3120 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 312c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 33f8 v8: .cfa -64 + ^
STACK CFI 34a4 v8: v8
STACK CFI 34c8 v8: .cfa -64 + ^
STACK CFI 350c v8: v8
STACK CFI 3568 x19: x19 x20: x20
STACK CFI 357c x25: x25 x26: x26
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3588 .cfa: sp 160 + .ra: .cfa -152 + ^ v8: .cfa -64 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 3680 v8: v8
STACK CFI 36ac v8: .cfa -64 + ^
STACK CFI 36e8 v8: v8
STACK CFI 370c v8: .cfa -64 + ^
STACK CFI 3714 v8: v8
STACK CFI 3734 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 3738 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 373c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3740 v8: .cfa -64 + ^
STACK CFI INIT 4218 5c .cfa: sp 0 + .ra: x30
STACK CFI 421c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4224 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 426c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4270 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4278 44 .cfa: sp 0 + .ra: x30
STACK CFI 427c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 42c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42d0 x19: .cfa -48 + ^
STACK CFI 430c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4310 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4318 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4328 68 .cfa: sp 0 + .ra: x30
STACK CFI 432c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4334 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 433c x21: .cfa -32 + ^
STACK CFI 438c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4390 70 .cfa: sp 0 + .ra: x30
STACK CFI 4394 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43a4 x19: .cfa -48 + ^
STACK CFI 43f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4400 34 .cfa: sp 0 + .ra: x30
STACK CFI 4404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4438 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4448 68 .cfa: sp 0 + .ra: x30
STACK CFI 444c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4454 x21: .cfa -16 + ^
STACK CFI 4460 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 44b0 64 .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 44fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4500 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4518 20 .cfa: sp 0 + .ra: x30
STACK CFI 451c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4538 a0 .cfa: sp 0 + .ra: x30
STACK CFI 453c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 454c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4558 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 45c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 45d8 7c .cfa: sp 0 + .ra: x30
STACK CFI 45dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45fc x21: .cfa -16 + ^
STACK CFI 4638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 463c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4658 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4668 50 .cfa: sp 0 + .ra: x30
STACK CFI 466c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4680 x19: .cfa -16 + ^
STACK CFI 46a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46b8 64 .cfa: sp 0 + .ra: x30
STACK CFI 46bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46cc x19: .cfa -32 + ^
STACK CFI 4714 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4718 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4720 98 .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 472c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4734 x21: .cfa -32 + ^
STACK CFI 47b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 47b8 5c .cfa: sp 0 + .ra: x30
STACK CFI 47bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4818 34 .cfa: sp 0 + .ra: x30
STACK CFI 481c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4848 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4850 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4860 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4870 80 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4880 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 488c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 48ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 48f0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 48f4 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 4900 x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 490c x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 498c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4990 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x29: .cfa -320 + ^
STACK CFI INIT 49a8 7c .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a28 7c .cfa: sp 0 + .ra: x30
STACK CFI 4a2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4aa8 334 .cfa: sp 0 + .ra: x30
STACK CFI 4aac .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4ab4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4abc x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4ac8 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 4adc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4af8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4b00 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4b08 v10: .cfa -48 + ^
STACK CFI 4cc8 x21: x21 x22: x22
STACK CFI 4ccc x27: x27 x28: x28
STACK CFI 4cd0 v10: v10
STACK CFI 4cfc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4d00 .cfa: sp 160 + .ra: .cfa -152 + ^ v10: .cfa -48 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 4d24 x21: x21 x22: x22
STACK CFI 4d28 x27: x27 x28: x28
STACK CFI 4d2c v10: v10
STACK CFI 4d48 v10: .cfa -48 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4d6c x21: x21 x22: x22
STACK CFI 4d70 x27: x27 x28: x28
STACK CFI 4d74 v10: v10
STACK CFI 4d78 v10: .cfa -48 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4dcc v10: v10 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 4dd0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4dd4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4dd8 v10: .cfa -48 + ^
STACK CFI INIT 4de0 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 4de4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4dec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4df4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4e00 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4e14 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 4e30 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4e38 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4fb0 x21: x21 x22: x22
STACK CFI 4fb4 x25: x25 x26: x26
STACK CFI 4fe0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4fe4 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 5030 x21: x21 x22: x22
STACK CFI 5034 x25: x25 x26: x26
STACK CFI 5038 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5070 x21: x21 x22: x22
STACK CFI 5074 x25: x25 x26: x26
STACK CFI 5078 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 50a4 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 50a8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 50ac x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT 50b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 50b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 50cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 511c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5120 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5128 7c .cfa: sp 0 + .ra: x30
STACK CFI 512c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5134 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5188 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 51a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 51a8 d0 .cfa: sp 0 + .ra: x30
STACK CFI 51ac .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 51b8 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 51d4 x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^
STACK CFI 525c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5260 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x29: .cfa -336 + ^
STACK CFI INIT 5278 7c .cfa: sp 0 + .ra: x30
STACK CFI 527c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5284 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 52ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 52f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52f8 5c .cfa: sp 0 + .ra: x30
STACK CFI 52fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5304 x19: .cfa -16 + ^
STACK CFI 5334 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5338 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5350 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5358 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5360 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5364 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 536c x27: .cfa -32 + ^
STACK CFI 5374 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5380 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5394 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 53c0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5448 x19: x19 x20: x20
STACK CFI 5450 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 54cc x19: x19 x20: x20
STACK CFI 54f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 54fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 550c x19: x19 x20: x20
STACK CFI 5518 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 5520 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 5524 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 552c x27: .cfa -32 + ^
STACK CFI 5534 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5540 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5560 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5584 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 560c x19: x19 x20: x20
STACK CFI 5614 x23: x23 x24: x24
STACK CFI 5618 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5694 x19: x19 x20: x20
STACK CFI 569c x23: x23 x24: x24
STACK CFI 56c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 56c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 56d4 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 56e0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 56e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 56e8 338 .cfa: sp 0 + .ra: x30
STACK CFI 56ec .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 56f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 56fc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 570c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 5724 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5754 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5788 x27: x27 x28: x28
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 57b8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 5890 x27: x27 x28: x28
STACK CFI 5894 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5954 x27: x27 x28: x28
STACK CFI 5958 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5960 x27: x27 x28: x28
STACK CFI 5964 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5a04 x27: x27 x28: x28
STACK CFI 5a1c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 5a20 30c .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5a2c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5a38 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5a48 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ad8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 5af4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5ba8 x25: x25 x26: x26
STACK CFI 5bac x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5bb0 x25: x25 x26: x26
STACK CFI 5bb4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5c88 x25: x25 x26: x26
STACK CFI 5c8c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5cdc x25: x25 x26: x26
STACK CFI 5cf4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5d04 x25: x25 x26: x26
STACK CFI 5d28 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT 5d30 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5d44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5dd8 234 .cfa: sp 0 + .ra: x30
STACK CFI 5ddc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5de0 .cfa: x29 112 +
STACK CFI 5de4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5dec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5df8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5e0c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5e14 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f18 .cfa: x29 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6010 178 .cfa: sp 0 + .ra: x30
STACK CFI 6014 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 601c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 602c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 60dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 60e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 6188 178 .cfa: sp 0 + .ra: x30
STACK CFI 618c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6194 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 61a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6258 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 62fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 6300 174 .cfa: sp 0 + .ra: x30
STACK CFI 6304 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 630c x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 6330 x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 6450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6454 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT 6478 134 .cfa: sp 0 + .ra: x30
STACK CFI 647c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6490 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 64dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6568 x19: x19 x20: x20
STACK CFI 6590 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6594 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 65b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 65b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65c8 x21: .cfa -16 + ^
STACK CFI 65f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 65f8 14 .cfa: sp 0 + .ra: x30
STACK CFI 65fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6608 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6610 98 .cfa: sp 0 + .ra: x30
STACK CFI 6614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 661c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6674 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66a8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 66ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 66b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 66c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6760 5c .cfa: sp 0 + .ra: x30
STACK CFI 6764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 676c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 679c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 67d8 5c .cfa: sp 0 + .ra: x30
STACK CFI 67dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 67e8 x19: .cfa -48 + ^
STACK CFI 682c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6830 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6838 28 .cfa: sp 0 + .ra: x30
STACK CFI 683c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6844 x19: .cfa -16 + ^
STACK CFI 685c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6860 40 .cfa: sp 0 + .ra: x30
STACK CFI 6868 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 689c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 68a0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68d0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 68e0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6900 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6920 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6938 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6950 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6970 24 .cfa: sp 0 + .ra: x30
STACK CFI 6974 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6990 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6998 40 .cfa: sp 0 + .ra: x30
STACK CFI 699c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69a4 x19: .cfa -32 + ^
STACK CFI 69d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 69d8 24 .cfa: sp 0 + .ra: x30
STACK CFI 69dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 69f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a00 40 .cfa: sp 0 + .ra: x30
STACK CFI 6a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a0c x19: .cfa -32 + ^
STACK CFI 6a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a40 30c .cfa: sp 0 + .ra: x30
STACK CFI 6a44 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 6a4c x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 6a54 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 6a5c x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI 6a6c x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 6ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6ad4 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x29: .cfa -320 + ^
STACK CFI 6ad8 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 6cbc x27: x27 x28: x28
STACK CFI 6cc4 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 6d0c x27: x27 x28: x28
STACK CFI 6d10 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 6d40 x27: x27 x28: x28
STACK CFI 6d48 x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 6d50 8c .cfa: sp 0 + .ra: x30
STACK CFI 6d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6da8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6dd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6de0 164 .cfa: sp 0 + .ra: x30
STACK CFI 6de4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6dec x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6dfc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6e08 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6e34 x25: .cfa -48 + ^
STACK CFI 6eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6eb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6f48 240 .cfa: sp 0 + .ra: x30
STACK CFI 6f4c .cfa: sp 1200 +
STACK CFI 6f50 .ra: .cfa -1192 + ^ x29: .cfa -1200 + ^
STACK CFI 6f58 x19: .cfa -1184 + ^ x20: .cfa -1176 + ^
STACK CFI 6f64 x21: .cfa -1168 + ^ x22: .cfa -1160 + ^
STACK CFI 6fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ff0 .cfa: sp 1200 + .ra: .cfa -1192 + ^ x19: .cfa -1184 + ^ x20: .cfa -1176 + ^ x21: .cfa -1168 + ^ x22: .cfa -1160 + ^ x29: .cfa -1200 + ^
STACK CFI 7020 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 703c x23: x23 x24: x24
STACK CFI 7078 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 7090 x25: .cfa -1136 + ^
STACK CFI 70c0 x23: x23 x24: x24
STACK CFI 70c4 x25: x25
STACK CFI 70c8 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^
STACK CFI 7118 x23: x23 x24: x24
STACK CFI 711c x25: x25
STACK CFI 7120 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^
STACK CFI 7134 x23: x23 x24: x24
STACK CFI 7138 x25: x25
STACK CFI 713c x23: .cfa -1152 + ^ x24: .cfa -1144 + ^ x25: .cfa -1136 + ^
STACK CFI 717c x23: x23 x24: x24 x25: x25
STACK CFI 7180 x23: .cfa -1152 + ^ x24: .cfa -1144 + ^
STACK CFI 7184 x25: .cfa -1136 + ^
STACK CFI INIT 7188 288 .cfa: sp 0 + .ra: x30
STACK CFI 718c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7194 .cfa: x29 304 +
STACK CFI 7198 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 71b4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 737c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7380 .cfa: x29 304 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 7410 208 .cfa: sp 0 + .ra: x30
STACK CFI 7414 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 7420 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 7434 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 74c8 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 7504 x23: x23 x24: x24
STACK CFI 7530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7534 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x29: .cfa -384 + ^
STACK CFI 7590 x23: x23 x24: x24
STACK CFI 7594 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 75ac x23: x23 x24: x24
STACK CFI 7614 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI INIT 7618 e4 .cfa: sp 0 + .ra: x30
STACK CFI 761c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7628 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7638 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7640 x23: .cfa -32 + ^
STACK CFI 7684 x21: x21 x22: x22
STACK CFI 768c x23: x23
STACK CFI 7698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 769c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 76c8 x21: x21 x22: x22
STACK CFI 76cc x23: x23
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 76ec x21: x21 x22: x22
STACK CFI 76f0 x23: x23
STACK CFI INIT 7700 7c .cfa: sp 0 + .ra: x30
STACK CFI 7704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7710 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7738 x21: .cfa -16 + ^
STACK CFI 7758 x21: x21
STACK CFI 7764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7768 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7778 x21: x21
STACK CFI INIT 7780 18c .cfa: sp 0 + .ra: x30
STACK CFI 7784 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 7794 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 77b4 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 7840 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 78c8 x19: x19 x20: x20
STACK CFI 78cc x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 78d0 x19: x19 x20: x20
STACK CFI 78f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 78f8 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x29: .cfa -352 + ^
STACK CFI 7900 x19: x19 x20: x20
STACK CFI 7908 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI INIT 7910 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7948 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 79a0 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a30 13c .cfa: sp 0 + .ra: x30
STACK CFI 7a34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7a3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7a50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7a68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7a94 x25: .cfa -32 + ^
STACK CFI 7af8 x25: x25
STACK CFI 7b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7b20 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 7b24 x25: x25
STACK CFI 7b30 x25: .cfa -32 + ^
STACK CFI 7b44 x25: x25
STACK CFI 7b4c x25: .cfa -32 + ^
STACK CFI 7b58 x25: x25
STACK CFI 7b68 x25: .cfa -32 + ^
