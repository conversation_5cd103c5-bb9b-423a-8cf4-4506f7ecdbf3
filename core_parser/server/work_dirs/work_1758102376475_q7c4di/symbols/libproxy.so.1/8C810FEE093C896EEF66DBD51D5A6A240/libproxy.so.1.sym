MODULE Linux arm64 8C810FEE093C896EEF66DBD51D5A6A240 libproxy.so.1
INFO CODE_ID EE0F818C3C096E89EF66DBD51D5A6A2431302E51
PUBLIC 7940 0 libproxy::config_extension::set_creds(libproxy::url const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 7948 0 libproxy::config_extension::operator<(libmodman::base_extension const&) const
PUBLIC 7950 0 libproxy::config_extension::get_ignore[abi:cxx11](libproxy::url const&)
PUBLIC 7968 0 libproxy::config_extension::get_valid()
PUBLIC 7970 0 libproxy::config_extension::set_valid(bool)
PUBLIC 7978 0 libmodman::extension<libproxy::config_extension, false>::get_base_type() const
PUBLIC 7988 0 libproxy::pacrunner_extension::get(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, libproxy::url const&)
PUBLIC 7b50 0 libproxy::pacrunner::pacrunner(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, libproxy::url const&)
PUBLIC 7b68 0 libproxy::pacrunner_extension::pacrunner_extension()
PUBLIC 7b90 0 libproxy::pacrunner_extension::~pacrunner_extension()
PUBLIC 7bf0 0 libproxy::pacrunner_extension::~pacrunner_extension()
PUBLIC 7c18 0 libmodman::extension<libproxy::pacrunner_extension, true>::get_base_type() const
PUBLIC 7c28 0 libmodman::extension<libproxy::pacrunner_extension, true>::operator<(libmodman::base_extension const&) const
PUBLIC 7c30 0 libproxy::wpad_extension::operator<(libproxy::wpad_extension const&) const
PUBLIC 7d28 0 libmodman::extension<libproxy::wpad_extension, false>::get_base_type() const
PUBLIC 7d38 0 libmodman::extension<libproxy::wpad_extension, false>::operator<(libmodman::base_extension const&) const
PUBLIC 7ec8 0 px_proxy_factory_free
PUBLIC 9c28 0 px_proxy_factory_new
PUBLIC c770 0 px_proxy_factory_get_proxies
PUBLIC ca00 0 std::ctype<char>::do_widen(char) const
PUBLIC ca08 0 libmodman::module_manager::get_extensions<libproxy::wpad_extension>() const::pcmp::cmp(libproxy::wpad_extension*, libproxy::wpad_extension*)
PUBLIC ca18 0 libmodman::module_manager::get_extensions<libproxy::config_extension>() const::pcmp::cmp(libproxy::config_extension*, libproxy::config_extension*)
PUBLIC ca28 0 libmodman::extension<libproxy::network_extension, false>::operator<(libmodman::base_extension const&) const
PUBLIC ca30 0 libmodman::extension<libproxy::ignore_extension, false>::operator<(libmodman::base_extension const&) const
PUBLIC ca38 0 libmodman::module_manager::get_extensions<libproxy::network_extension>() const::pcmp::cmp(libproxy::network_extension*, libproxy::network_extension*)
PUBLIC ca60 0 libmodman::module_manager::get_extensions<libproxy::ignore_extension>() const::pcmp::cmp(libproxy::ignore_extension*, libproxy::ignore_extension*)
PUBLIC ca88 0 libmodman::module_manager::get_extensions<libproxy::pacrunner_extension>() const::pcmp::cmp(libproxy::pacrunner_extension*, libproxy::pacrunner_extension*)
PUBLIC cab0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(char const*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC cb68 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC cbd0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::_Identity<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_erase(std::_Rb_tree_node<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >*)
PUBLIC cc38 0 void std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_realloc_insert<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(__gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC ce60 0 void std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::emplace_back<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC ced0 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_insert_rval(__gnu_cxx::__normal_iterator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC d168 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::find(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) const
PUBLIC d2a0 0 void std::vector<libproxy::network_extension*, std::allocator<libproxy::network_extension*> >::_M_realloc_insert<libproxy::network_extension* const&>(__gnu_cxx::__normal_iterator<libproxy::network_extension**, std::vector<libproxy::network_extension*, std::allocator<libproxy::network_extension*> > >, libproxy::network_extension* const&)
PUBLIC d3c8 0 void std::vector<libproxy::wpad_extension*, std::allocator<libproxy::wpad_extension*> >::_M_realloc_insert<libproxy::wpad_extension* const&>(__gnu_cxx::__normal_iterator<libproxy::wpad_extension**, std::vector<libproxy::wpad_extension*, std::allocator<libproxy::wpad_extension*> > >, libproxy::wpad_extension* const&)
PUBLIC d4f0 0 void std::vector<libproxy::config_extension*, std::allocator<libproxy::config_extension*> >::_M_realloc_insert<libproxy::config_extension* const&>(__gnu_cxx::__normal_iterator<libproxy::config_extension**, std::vector<libproxy::config_extension*, std::allocator<libproxy::config_extension*> > >, libproxy::config_extension* const&)
PUBLIC d618 0 void std::vector<libproxy::ignore_extension*, std::allocator<libproxy::ignore_extension*> >::_M_realloc_insert<libproxy::ignore_extension* const&>(__gnu_cxx::__normal_iterator<libproxy::ignore_extension**, std::vector<libproxy::ignore_extension*, std::allocator<libproxy::ignore_extension*> > >, libproxy::ignore_extension* const&)
PUBLIC d740 0 void std::vector<libproxy::pacrunner_extension*, std::allocator<libproxy::pacrunner_extension*> >::_M_realloc_insert<libproxy::pacrunner_extension* const&>(__gnu_cxx::__normal_iterator<libproxy::pacrunner_extension**, std::vector<libproxy::pacrunner_extension*, std::allocator<libproxy::pacrunner_extension*> > >, libproxy::pacrunner_extension* const&)
PUBLIC d868 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char const*>(char const*, char const*, std::forward_iterator_tag)
PUBLIC d948 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag)
PUBLIC da28 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::_Identity<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::equal_range(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC dc18 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::_Identity<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::erase(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC dd08 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC de80 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC e120 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&>&&, std::tuple<>&&)
PUBLIC f9e0 0 libproxy::url::get_ips(bool)
PUBLIC fbe8 0 libproxy::url::get_port() const
PUBLIC fbf0 0 libproxy::url::empty_cache()
PUBLIC fc60 0 libproxy::url::operator=(libproxy::url const&)
PUBLIC fdd8 0 libproxy::url::url(libproxy::url const&)
PUBLIC fef0 0 libproxy::url::~url()
PUBLIC ffa0 0 libproxy::url::encode(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 10400 0 libproxy::url::url(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 11440 0 libproxy::url::is_valid(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 114c0 0 libproxy::url::operator=(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 11568 0 libproxy::url::get_host[abi:cxx11]() const
PUBLIC 115b0 0 libproxy::url::get_password[abi:cxx11]() const
PUBLIC 115f8 0 libproxy::url::get_path[abi:cxx11]() const
PUBLIC 11640 0 libproxy::url::get_query[abi:cxx11]() const
PUBLIC 11688 0 libproxy::url::get_scheme[abi:cxx11]() const
PUBLIC 116d0 0 libproxy::url::get_username[abi:cxx11]() const
PUBLIC 11718 0 libproxy::url::to_string[abi:cxx11]() const
PUBLIC 11760 0 libproxy::url::operator==(libproxy::url const&) const
PUBLIC 11820 0 libproxy::url::get_pac()
PUBLIC 12510 0 libproxy::parse_error::~parse_error()
PUBLIC 12528 0 libproxy::parse_error::~parse_error()
PUBLIC 12560 0 std::vector<char, std::allocator<char> >::_M_default_append(unsigned long)
PUBLIC 126f8 0 libmodman::extension<libproxy::config_extension, false>::base_type()
PUBLIC 12840 0 void std::vector<libproxy::url, std::allocator<libproxy::url> >::_M_realloc_insert<libproxy::url>(__gnu_cxx::__normal_iterator<libproxy::url*, std::vector<libproxy::url, std::allocator<libproxy::url> > >, libproxy::url&&)
PUBLIC 12e00 0 libmodman::extension<libproxy::ignore_extension, false>::base_type()
PUBLIC 12e18 0 libmodman::extension<libproxy::ignore_extension, false>::get_base_type() const
PUBLIC 13e20 0 libmodman::extension<libproxy::wpad_extension, false>::base_type()
PUBLIC 14058 0 libmodman::module_manager::~module_manager()
PUBLIC 151b0 0 libmodman::module_manager::load_builtin(mm_module*)
PUBLIC 15280 0 libmodman::module_manager::load_file(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool)
PUBLIC 15bd8 0 libmodman::module_manager::load_dir(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool)
PUBLIC 160c8 0 std::_Rb_tree<void*, void*, std::_Identity<void*>, std::less<void*>, std::allocator<void*> >::_M_erase(std::_Rb_tree_node<void*>*)
PUBLIC 16110 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >*)
PUBLIC 16180 0 void std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> >::_M_realloc_insert<libmodman::base_extension* const&>(__gnu_cxx::__normal_iterator<libmodman::base_extension**, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, libmodman::base_extension* const&)
PUBLIC 162a8 0 std::pair<std::_Rb_tree_iterator<void*>, bool> std::_Rb_tree<void*, void*, std::_Identity<void*>, std::less<void*>, std::allocator<void*> >::_M_insert_unique<void*>(void*&&)
PUBLIC 163f0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_insert_node(std::_Rb_tree_node_base*, std::_Rb_tree_node_base*, std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >*)
PUBLIC 164c8 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::vector<libmodman::base_extension*, std::allocator<libmodman::base_extension*> > > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>&&, std::tuple<>&&)
STACK CFI INIT 7880 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78b0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 78f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 78f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78fc x19: .cfa -16 + ^
STACK CFI 7934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7938 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7948 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7978 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7950 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7968 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7970 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7988 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 798c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7994 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 79a0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 79ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 79e4 x25: .cfa -64 + ^
STACK CFI 7a6c x25: x25
STACK CFI 7a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7a9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI 7ad0 x25: x25
STACK CFI 7aec x25: .cfa -64 + ^
STACK CFI 7af8 x25: x25
STACK CFI 7afc x25: .cfa -64 + ^
STACK CFI 7b00 x25: x25
STACK CFI 7b04 x25: .cfa -64 + ^
STACK CFI INIT 7b50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b68 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b90 5c .cfa: sp 0 + .ra: x30
STACK CFI 7b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ba4 x19: .cfa -16 + ^
STACK CFI 7bdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7be0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7bf0 28 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7bfc x19: .cfa -16 + ^
STACK CFI 7c14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d28 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7c30 f4 .cfa: sp 0 + .ra: x30
STACK CFI 7c34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7c3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7c64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7c70 x25: .cfa -16 + ^
STACK CFI 7cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT ca00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca08 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca18 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca38 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca60 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT ca88 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d40 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7d44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7d54 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7d5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7dcc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 7dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7e00 5c .cfa: sp 0 + .ra: x30
STACK CFI 7e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7e60 54 .cfa: sp 0 + .ra: x30
STACK CFI 7e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7eb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ec0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ec8 30 .cfa: sp 0 + .ra: x30
STACK CFI 7ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ed8 x19: .cfa -16 + ^
STACK CFI 7ef0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cab0 b8 .cfa: sp 0 + .ra: x30
STACK CFI cab4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cabc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI cac4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI cad0 x23: .cfa -16 + ^
STACK CFI cb38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI cb3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT cb68 68 .cfa: sp 0 + .ra: x30
STACK CFI cb6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cb74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cb84 x21: .cfa -16 + ^
STACK CFI cbb0 x21: x21
STACK CFI cbc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cbc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cbcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT cbd0 64 .cfa: sp 0 + .ra: x30
STACK CFI cbd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cbe0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cbe8 x21: .cfa -16 + ^
STACK CFI cc2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT cc38 224 .cfa: sp 0 + .ra: x30
STACK CFI cc3c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cc48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cc50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cc60 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI cdfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ce00 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT ce60 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT ced0 298 .cfa: sp 0 + .ra: x30
STACK CFI ced4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cedc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cee4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cefc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cf08 x25: .cfa -16 + ^
STACK CFI d050 x21: x21 x22: x22
STACK CFI d058 x25: x25
STACK CFI d05c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI d060 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d090 x25: x25
STACK CFI d09c x21: x21 x22: x22
STACK CFI d0b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI d0b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d0f4 x21: x21 x22: x22 x25: x25
STACK CFI d10c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI d110 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT d168 138 .cfa: sp 0 + .ra: x30
STACK CFI d16c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d174 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d180 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d194 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d230 x23: x23 x24: x24
STACK CFI d24c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI d250 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d26c x23: x23 x24: x24
STACK CFI d274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI d278 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI d294 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d29c x23: x23 x24: x24
STACK CFI INIT d2a0 128 .cfa: sp 0 + .ra: x30
STACK CFI d2a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d2b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d2c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d358 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d3c8 128 .cfa: sp 0 + .ra: x30
STACK CFI d3cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d3dc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d3f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d47c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d480 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d4f0 128 .cfa: sp 0 + .ra: x30
STACK CFI d4f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d504 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d518 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d5a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d618 128 .cfa: sp 0 + .ra: x30
STACK CFI d61c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d62c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d640 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d6cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d6d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d740 128 .cfa: sp 0 + .ra: x30
STACK CFI d744 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d754 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d768 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d7f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI d7f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d868 dc .cfa: sp 0 + .ra: x30
STACK CFI d86c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d878 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d888 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d8f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d8fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7ef8 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 7efc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7f04 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7f14 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7f2c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 7fc8 x25: .cfa -96 + ^
STACK CFI 8048 x25: x25
STACK CFI 80f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 80fc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 8104 x25: x25
STACK CFI 8110 x25: .cfa -96 + ^
STACK CFI 8130 x25: x25
STACK CFI 8178 x25: .cfa -96 + ^
STACK CFI 817c x25: x25
STACK CFI 8180 x25: .cfa -96 + ^
STACK CFI 81a8 x25: x25
STACK CFI 81b8 x25: .cfa -96 + ^
STACK CFI 81c4 x25: x25
STACK CFI 81c8 x25: .cfa -96 + ^
STACK CFI INIT d948 dc .cfa: sp 0 + .ra: x30
STACK CFI d94c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d958 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d968 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d9d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d9dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 81d0 fe4 .cfa: sp 0 + .ra: x30
STACK CFI 81d4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 81dc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 81e8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 8200 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 821c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 852c x21: x21 x22: x22
STACK CFI 8554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8558 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI 889c x21: x21 x22: x22
STACK CFI 88a0 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 88f4 x21: x21 x22: x22
STACK CFI 8940 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 8f04 x21: x21 x22: x22
STACK CFI 8f08 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 8f44 x21: x21 x22: x22
STACK CFI 8f5c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 90f8 x21: x21 x22: x22
STACK CFI 90fc x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI INIT da28 1ec .cfa: sp 0 + .ra: x30
STACK CFI da2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI da34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI da3c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI da4c x27: .cfa -16 + ^
STACK CFI da58 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI da5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI dae0 x21: x21 x22: x22
STACK CFI dae4 x25: x25 x26: x26
STACK CFI dae8 x27: x27
STACK CFI dafc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI db00 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI dbe8 x21: x21 x22: x22
STACK CFI dbf0 x25: x25 x26: x26
STACK CFI dbf4 x27: x27
STACK CFI dbf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI dbfc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT dc18 ec .cfa: sp 0 + .ra: x30
STACK CFI dc1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI dc24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI dc30 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI dcc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI dcc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI dd00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT dd08 178 .cfa: sp 0 + .ra: x30
STACK CFI dd0c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI dd14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI dd20 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI dd28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI dd30 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI de00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI de04 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI de58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI de5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI de7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT de80 29c .cfa: sp 0 + .ra: x30
STACK CFI de84 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI de94 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dea4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI deac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI deb0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI df3c x25: x25 x26: x26
STACK CFI df48 x19: x19 x20: x20
STACK CFI df4c x21: x21 x22: x22
STACK CFI df54 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI df58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI dfe0 x19: x19 x20: x20
STACK CFI dfe4 x21: x21 x22: x22
STACK CFI dfe8 x25: x25 x26: x26
STACK CFI dfec .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI dff0 .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI dffc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e004 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e058 x19: x19 x20: x20
STACK CFI e05c x21: x21 x22: x22
STACK CFI e06c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI e070 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e0d0 x25: x25 x26: x26
STACK CFI e0e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e0ec x19: x19 x20: x20
STACK CFI e0f0 x21: x21 x22: x22
STACK CFI e0f8 x25: x25 x26: x26
STACK CFI e0fc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI e100 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e108 x25: x25 x26: x26
STACK CFI e10c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e118 x25: x25 x26: x26
STACK CFI INIT e120 178 .cfa: sp 0 + .ra: x30
STACK CFI e124 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e12c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e138 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e144 x23: .cfa -32 + ^
STACK CFI e1e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e1e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI e280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e284 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 91b8 a70 .cfa: sp 0 + .ra: x30
STACK CFI 91bc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 91d0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 91d8 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 91e4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 91f0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 91f8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 998c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9990 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 9c28 44 .cfa: sp 0 + .ra: x30
STACK CFI 9c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c38 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e298 d0 .cfa: sp 0 + .ra: x30
STACK CFI e2a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e2ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e2b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e2c0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e2d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e31c x23: x23 x24: x24
STACK CFI e32c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e330 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT e368 d0 .cfa: sp 0 + .ra: x30
STACK CFI e374 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e37c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e388 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e390 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e3a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e3ec x23: x23 x24: x24
STACK CFI e3fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e400 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT e438 d0 .cfa: sp 0 + .ra: x30
STACK CFI e444 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e44c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e458 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e460 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e474 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e4bc x23: x23 x24: x24
STACK CFI e4cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e4d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT e508 d0 .cfa: sp 0 + .ra: x30
STACK CFI e514 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e51c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e528 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e530 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e544 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e58c x23: x23 x24: x24
STACK CFI e59c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e5a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e5d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT e5d8 d0 .cfa: sp 0 + .ra: x30
STACK CFI e5e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e5ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e5f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e600 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e614 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e65c x23: x23 x24: x24
STACK CFI e66c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e670 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e6a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT e6a8 178 .cfa: sp 0 + .ra: x30
STACK CFI e6ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e6b4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e6c0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e6d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e6e0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e7bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e7c0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI e81c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT e820 21c .cfa: sp 0 + .ra: x30
STACK CFI e824 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e82c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e840 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e848 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e850 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e860 x27: .cfa -16 + ^
STACK CFI e998 x27: x27
STACK CFI ea04 x21: x21 x22: x22
STACK CFI ea08 x23: x23 x24: x24
STACK CFI ea0c x25: x25 x26: x26
STACK CFI ea14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ea18 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI ea20 x21: x21 x22: x22
STACK CFI ea24 x23: x23 x24: x24
STACK CFI ea28 x25: x25 x26: x26
STACK CFI ea2c x27: x27
STACK CFI ea30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ea34 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT ea40 178 .cfa: sp 0 + .ra: x30
STACK CFI ea44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ea4c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI ea58 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ea6c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ea78 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI eb54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI eb58 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI ebb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT ebb8 21c .cfa: sp 0 + .ra: x30
STACK CFI ebbc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ebc4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ebd8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ebe0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ebe8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ebf8 x27: .cfa -16 + ^
STACK CFI ed30 x27: x27
STACK CFI ed9c x21: x21 x22: x22
STACK CFI eda0 x23: x23 x24: x24
STACK CFI eda4 x25: x25 x26: x26
STACK CFI edac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI edb0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI edb8 x21: x21 x22: x22
STACK CFI edbc x23: x23 x24: x24
STACK CFI edc0 x25: x25 x26: x26
STACK CFI edc4 x27: x27
STACK CFI edc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI edcc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9c70 8ec .cfa: sp 0 + .ra: x30
STACK CFI 9c74 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 9c7c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9c88 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9cac x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 9e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9e7c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT a560 6d8 .cfa: sp 0 + .ra: x30
STACK CFI a564 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI a574 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a57c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI a5bc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI a5fc x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI a600 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI a728 x21: x21 x22: x22
STACK CFI a72c x27: x27 x28: x28
STACK CFI a748 x19: x19 x20: x20
STACK CFI a754 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a758 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI aa10 x21: x21 x22: x22
STACK CFI aa14 x27: x27 x28: x28
STACK CFI aa18 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI ab80 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI ab84 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI ab88 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI ac00 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI ac04 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI ac0c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI ac10 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT edd8 178 .cfa: sp 0 + .ra: x30
STACK CFI eddc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI ede4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI edf0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ee04 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ee10 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI eeec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI eef0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI ef4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT ef50 21c .cfa: sp 0 + .ra: x30
STACK CFI ef54 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ef5c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ef70 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ef78 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ef80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ef90 x27: .cfa -16 + ^
STACK CFI f0c8 x27: x27
STACK CFI f134 x21: x21 x22: x22
STACK CFI f138 x23: x23 x24: x24
STACK CFI f13c x25: x25 x26: x26
STACK CFI f144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f148 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI f150 x21: x21 x22: x22
STACK CFI f154 x23: x23 x24: x24
STACK CFI f158 x25: x25 x26: x26
STACK CFI f15c x27: x27
STACK CFI f160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f164 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT f170 178 .cfa: sp 0 + .ra: x30
STACK CFI f174 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f17c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f188 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f19c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI f1a8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f288 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI f2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT f2e8 21c .cfa: sp 0 + .ra: x30
STACK CFI f2ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f2f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f308 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f310 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f318 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f328 x27: .cfa -16 + ^
STACK CFI f460 x27: x27
STACK CFI f4cc x21: x21 x22: x22
STACK CFI f4d0 x23: x23 x24: x24
STACK CFI f4d4 x25: x25 x26: x26
STACK CFI f4dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f4e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI f4e8 x21: x21 x22: x22
STACK CFI f4ec x23: x23 x24: x24
STACK CFI f4f0 x25: x25 x26: x26
STACK CFI f4f4 x27: x27
STACK CFI f4f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f4fc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT ac38 740 .cfa: sp 0 + .ra: x30
STACK CFI ac3c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI ac4c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI ac58 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI ac74 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI ac8c x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI afa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI afac .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT f508 178 .cfa: sp 0 + .ra: x30
STACK CFI f50c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f514 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f520 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f534 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI f540 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f61c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f620 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI f67c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT f680 21c .cfa: sp 0 + .ra: x30
STACK CFI f684 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f68c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f6a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f6a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f6b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f6c0 x27: .cfa -16 + ^
STACK CFI f7f8 x27: x27
STACK CFI f864 x21: x21 x22: x22
STACK CFI f868 x23: x23 x24: x24
STACK CFI f86c x25: x25 x26: x26
STACK CFI f874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f878 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI f880 x21: x21 x22: x22
STACK CFI f884 x23: x23 x24: x24
STACK CFI f888 x25: x25 x26: x26
STACK CFI f88c x27: x27
STACK CFI f890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f894 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT b378 810 .cfa: sp 0 + .ra: x30
STACK CFI b37c .cfa: sp 512 +
STACK CFI b380 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI b388 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI b3a8 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI b3b0 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI b3c4 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI b3cc x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI b79c x21: x21 x22: x22
STACK CFI b7a0 x23: x23 x24: x24
STACK CFI b7a4 x25: x25 x26: x26
STACK CFI b7d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI b7d4 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI b86c x21: x21 x22: x22
STACK CFI b870 x23: x23 x24: x24
STACK CFI b874 x25: x25 x26: x26
STACK CFI b878 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI ba58 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI ba5c x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI ba60 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI ba64 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI INIT 77f0 3c .cfa: sp 0 + .ra: x30
STACK CFI 77f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 77fc x19: .cfa -16 + ^
STACK CFI 7820 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bb88 690 .cfa: sp 0 + .ra: x30
STACK CFI bb8c .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI bb94 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI bba4 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI bbac x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI bbd8 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI bc30 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI bfa8 x27: x27 x28: x28
STACK CFI bfd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bfd8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI c054 x27: x27 x28: x28
STACK CFI c058 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI c134 x27: x27 x28: x28
STACK CFI c138 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI c148 x27: x27 x28: x28
STACK CFI c150 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT c218 558 .cfa: sp 0 + .ra: x30
STACK CFI c21c .cfa: sp 688 +
STACK CFI c220 .ra: .cfa -680 + ^ x29: .cfa -688 + ^
STACK CFI c228 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI c234 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI c274 x23: .cfa -640 + ^ x24: .cfa -632 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI c2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI c2e8 .cfa: sp 688 + .ra: .cfa -680 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^ x29: .cfa -688 + ^
STACK CFI c2f8 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c52c x25: x25 x26: x26
STACK CFI c57c x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c580 x25: x25 x26: x26
STACK CFI c584 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c608 x25: x25 x26: x26
STACK CFI c60c x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c614 x25: x25 x26: x26
STACK CFI c638 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c648 x25: x25 x26: x26
STACK CFI c650 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c738 x25: x25 x26: x26
STACK CFI c758 x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI c768 x25: x25 x26: x26
STACK CFI c76c x25: .cfa -624 + ^ x26: .cfa -616 + ^
STACK CFI INIT c770 28c .cfa: sp 0 + .ra: x30
STACK CFI c774 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI c77c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI c790 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI c7a4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI c94c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c950 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 12510 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12528 38 .cfa: sp 0 + .ra: x30
STACK CFI 1252c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1253c x19: .cfa -16 + ^
STACK CFI 1255c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f8a0 140 .cfa: sp 0 + .ra: x30
STACK CFI f8a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f8ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f8bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f8c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f8d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI f958 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT f9e0 204 .cfa: sp 0 + .ra: x30
STACK CFI f9e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI f9ec x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI f9f8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI fa34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI fa38 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI fa74 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI faa4 x23: x23 x24: x24
STACK CFI fadc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI fae4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI fbcc x23: x23 x24: x24
STACK CFI fbd0 x25: x25 x26: x26
STACK CFI fbdc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI fbe0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI INIT fbe8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbf0 70 .cfa: sp 0 + .ra: x30
STACK CFI fbfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fc04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fc40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fc44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI fc58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT fc60 178 .cfa: sp 0 + .ra: x30
STACK CFI fc64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fc70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fcf0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fd2c x23: .cfa -16 + ^
STACK CFI fd9c x19: x19 x20: x20
STACK CFI fda0 x23: x23
STACK CFI fdac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI fdb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI fdc8 x23: x23
STACK CFI fdd4 x19: x19 x20: x20
STACK CFI INIT fdd8 114 .cfa: sp 0 + .ra: x30
STACK CFI fddc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fde4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI fdf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI fdfc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI fe08 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fe64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI fe68 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI fe70 x27: .cfa -16 + ^
STACK CFI INIT fef0 b0 .cfa: sp 0 + .ra: x30
STACK CFI fef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fefc x19: .cfa -16 + ^
STACK CFI ff90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ff94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ff9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12560 13c .cfa: sp 0 + .ra: x30
STACK CFI 12568 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12570 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12578 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 125c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 125c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 125c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12638 x23: x23 x24: x24
STACK CFI 1263c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12644 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT ffa0 460 .cfa: sp 0 + .ra: x30
STACK CFI ffa4 .cfa: sp 560 +
STACK CFI ffac .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI ffb8 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI ffcc x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI ffdc x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI 100a0 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 100b4 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 10140 x27: x27 x28: x28
STACK CFI 10214 x19: x19 x20: x20
STACK CFI 10224 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10228 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI 10278 x27: x27 x28: x28
STACK CFI 10290 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 10320 x27: x27 x28: x28
STACK CFI 10330 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 10338 x27: x27 x28: x28
STACK CFI 1033c x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 10340 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI 10348 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 1039c x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 103cc x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI 103d0 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 103f4 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 103f8 x27: x27 x28: x28
STACK CFI 103fc x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT 10400 103c .cfa: sp 0 + .ra: x30
STACK CFI 10404 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 10410 x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 1041c x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 10438 x19: .cfa -464 + ^ x20: .cfa -456 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 10440 x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 10828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1082c .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^ x29: .cfa -480 + ^
STACK CFI INIT 11440 80 .cfa: sp 0 + .ra: x30
STACK CFI 11444 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 11450 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 1149c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 114a0 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x29: .cfa -288 + ^
STACK CFI INIT 114c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 114c4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 114cc x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 114e8 x21: .cfa -272 + ^
STACK CFI 11530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11534 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI INIT 11568 44 .cfa: sp 0 + .ra: x30
STACK CFI 1156c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1157c x19: .cfa -16 + ^
STACK CFI 115a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 115b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 115b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 115c4 x19: .cfa -16 + ^
STACK CFI 115f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 115f8 44 .cfa: sp 0 + .ra: x30
STACK CFI 115fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1160c x19: .cfa -16 + ^
STACK CFI 11638 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11640 44 .cfa: sp 0 + .ra: x30
STACK CFI 11644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11654 x19: .cfa -16 + ^
STACK CFI 11680 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11688 44 .cfa: sp 0 + .ra: x30
STACK CFI 1168c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1169c x19: .cfa -16 + ^
STACK CFI 116c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 116d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 116d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 116e4 x19: .cfa -16 + ^
STACK CFI 11710 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11718 44 .cfa: sp 0 + .ra: x30
STACK CFI 1171c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1172c x19: .cfa -16 + ^
STACK CFI 11758 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11760 c0 .cfa: sp 0 + .ra: x30
STACK CFI 11764 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1176c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1177c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11798 x23: .cfa -64 + ^
STACK CFI 117f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 117f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11820 cf0 .cfa: sp 0 + .ra: x30
STACK CFI 11824 .cfa: sp 432 + .ra: .cfa -424 + ^ x29: .cfa -432 + ^
STACK CFI 1182c x21: .cfa -400 + ^ x22: .cfa -392 + ^
STACK CFI 11838 x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 11860 x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^
STACK CFI 1186c x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 11934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11938 .cfa: sp 432 + .ra: .cfa -424 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^ x29: .cfa -432 + ^
STACK CFI INIT 126f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 126a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 126f8 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12708 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 126a8 48 .cfa: sp 0 + .ra: x30
STACK CFI 126ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 126b8 x19: .cfa -16 + ^
STACK CFI 126ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12718 124 .cfa: sp 0 + .ra: x30
STACK CFI 1271c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1272c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1273c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1275c x23: .cfa -32 + ^
STACK CFI 1278c x23: x23
STACK CFI 127c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 127c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 127fc x23: x23
STACK CFI 12820 x23: .cfa -32 + ^
STACK CFI 12828 x23: x23
STACK CFI 12830 x23: .cfa -32 + ^
STACK CFI 12834 x23: x23
STACK CFI 12838 x23: .cfa -32 + ^
STACK CFI INIT 12840 258 .cfa: sp 0 + .ra: x30
STACK CFI 12844 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 12854 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 12868 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12874 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1297c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12980 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 12a98 314 .cfa: sp 0 + .ra: x30
STACK CFI 12a9c .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 12aa4 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 12ab0 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 12ab8 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 12acc x25: .cfa -304 + ^
STACK CFI 12c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 12c14 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x29: .cfa -368 + ^
STACK CFI INIT 12db0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12db8 44 .cfa: sp 0 + .ra: x30
STACK CFI 12dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12dc8 x19: .cfa -16 + ^
STACK CFI 12df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12e28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e30 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 12e34 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 12e40 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 12e50 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 12e68 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 12e70 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 12fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12fe8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x29: .cfa -272 + ^
STACK CFI 1309c x27: .cfa -192 + ^
STACK CFI 1314c x27: x27
STACK CFI 13208 x27: .cfa -192 + ^
STACK CFI 1320c x27: x27
STACK CFI 13238 x27: .cfa -192 + ^
STACK CFI 13288 x27: x27
STACK CFI 132bc x27: .cfa -192 + ^
STACK CFI 132c0 x27: x27
STACK CFI 132e8 x27: .cfa -192 + ^
STACK CFI 13344 x27: x27
STACK CFI 13364 x27: .cfa -192 + ^
STACK CFI 133ac x27: x27
STACK CFI 133b4 x27: .cfa -192 + ^
STACK CFI 133c8 x27: x27
STACK CFI 133d0 x27: .cfa -192 + ^
STACK CFI INIT 133d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13428 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 133e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 133e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 133f0 x19: .cfa -16 + ^
STACK CFI 13420 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 13430 120 .cfa: sp 0 + .ra: x30
STACK CFI 13434 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 13440 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 13458 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 13498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1349c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 13550 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13558 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 135a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13560 44 .cfa: sp 0 + .ra: x30
STACK CFI 13564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13570 x19: .cfa -16 + ^
STACK CFI 135a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 135b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 135b8 808 .cfa: sp 0 + .ra: x30
STACK CFI 135bc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 135c8 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 135d8 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 135f8 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1372c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13730 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 13e10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13dc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13dc8 48 .cfa: sp 0 + .ra: x30
STACK CFI 13dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13dd8 x19: .cfa -16 + ^
STACK CFI 13e0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 13e38 4c .cfa: sp 0 + .ra: x30
STACK CFI 13e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13e44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13e88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e90 16c .cfa: sp 0 + .ra: x30
STACK CFI 13e94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 13e9c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13eb4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 13f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 13f98 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 14000 58 .cfa: sp 0 + .ra: x30
STACK CFI 14004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14010 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 160c8 44 .cfa: sp 0 + .ra: x30
STACK CFI 160d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 160d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 16110 70 .cfa: sp 0 + .ra: x30
STACK CFI 16118 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16120 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16128 x21: .cfa -16 + ^
STACK CFI 16178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 14058 114 .cfa: sp 0 + .ra: x30
STACK CFI 1405c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14064 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1406c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14074 x23: .cfa -16 + ^
STACK CFI 14168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 16180 128 .cfa: sp 0 + .ra: x30
STACK CFI 16184 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16194 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 161a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 16238 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 162a8 148 .cfa: sp 0 + .ra: x30
STACK CFI 162ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 162b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 162c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 162c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1637c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 16380 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 163bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 163c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 163f0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 163f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 163fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1640c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16444 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 16458 x23: .cfa -32 + ^
STACK CFI 164ac x23: x23
STACK CFI 164b0 x23: .cfa -32 + ^
STACK CFI 164b4 x23: x23
STACK CFI 164b8 x23: .cfa -32 + ^
STACK CFI 164c0 x23: x23
STACK CFI INIT 164c8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 164cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 164d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 164e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 164ec x23: .cfa -16 + ^
STACK CFI 16558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1655c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 16598 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 14170 103c .cfa: sp 0 + .ra: x30
STACK CFI 14174 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1417c x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 141b8 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 141e8 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 141f4 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 144f4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 145b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 145bc .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x29: .cfa -272 + ^
STACK CFI 145f0 x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 14758 x25: x25 x26: x26
STACK CFI 1475c x27: x27 x28: x28
STACK CFI 14760 x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 1489c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 148d0 x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 148d4 x25: x25 x26: x26
STACK CFI 148d8 x27: x27 x28: x28
STACK CFI 148dc x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 1511c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15120 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 15124 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 15128 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1512c x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 15130 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 151b0 cc .cfa: sp 0 + .ra: x30
STACK CFI 151b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 151bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 151d8 x23: .cfa -16 + ^
STACK CFI 151e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15230 x21: x21 x22: x22
STACK CFI 15234 x23: x23
STACK CFI 1525c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15260 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15280 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 15284 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1528c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1529c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 152b0 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 15314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15318 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 1531c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1543c x25: x25 x26: x26
STACK CFI 15440 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 15458 x25: x25 x26: x26
STACK CFI 1545c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 154c0 x25: x25 x26: x26
STACK CFI 154c4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 15690 x25: x25 x26: x26
STACK CFI 15694 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 15710 x25: x25 x26: x26
STACK CFI 15714 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI INIT 165b8 24c .cfa: sp 0 + .ra: x30
STACK CFI 165bc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 165cc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 165e0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 165fc x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 16780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16784 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 16808 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 1680c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 16814 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 1681c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1682c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 16844 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 16864 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 168c4 x19: x19 x20: x20
STACK CFI 168c8 x27: x27 x28: x28
STACK CFI 168f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 168f4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 16aac x27: x27 x28: x28
STACK CFI 16ab0 x19: x19 x20: x20
STACK CFI 16ab8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 16abc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 16ac0 558 .cfa: sp 0 + .ra: x30
STACK CFI 16ac4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 16acc x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 16ae0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 16af0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 16b0c x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 16b14 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 16edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16ee0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT 17018 17c .cfa: sp 0 + .ra: x30
STACK CFI 1701c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 17024 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 1702c x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 17038 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 17058 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 17060 x27: .cfa -96 + ^
STACK CFI 17148 x19: x19 x20: x20
STACK CFI 1714c x27: x27
STACK CFI 17174 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 17178 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI 17180 x19: x19 x20: x20
STACK CFI 17184 x27: x27
STACK CFI 1718c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 17190 x27: .cfa -96 + ^
STACK CFI INIT 15748 490 .cfa: sp 0 + .ra: x30
STACK CFI 1574c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 1575c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 15778 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 15780 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 15788 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1578c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 15a00 x21: x21 x22: x22
STACK CFI 15a04 x23: x23 x24: x24
STACK CFI 15a08 x25: x25 x26: x26
STACK CFI 15a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 15a34 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 15bc8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 15bcc x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 15bd0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 15bd4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 15bd8 4f0 .cfa: sp 0 + .ra: x30
STACK CFI 15bdc .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 15be4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 15bf0 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 15bfc x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 15c18 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 15f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15f14 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 7830 3c .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 783c x19: .cfa -16 + ^
STACK CFI 7860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
