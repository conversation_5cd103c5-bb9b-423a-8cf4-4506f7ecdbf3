MODULE Linux arm64 BFB9C4669BB4258D58715ECCC763F4B80 libxcb-shm.so.0
INFO CODE_ID 66C4B9BFB49B8D2558715ECCC763F4B888C7AF07
PUBLIC e48 0 xcb_shm_seg_next
PUBLIC e68 0 xcb_shm_seg_end
PUBLIC e80 0 xcb_shm_query_version
PUBLIC ee8 0 xcb_shm_query_version_unchecked
PUBLIC f50 0 xcb_shm_query_version_reply
PUBLIC f58 0 xcb_shm_attach_checked
PUBLIC fd8 0 xcb_shm_attach
PUBLIC 1050 0 xcb_shm_detach_checked
PUBLIC 10c0 0 xcb_shm_detach
PUBLIC 1128 0 xcb_shm_put_image_checked
PUBLIC 11f8 0 xcb_shm_put_image
PUBLIC 12c0 0 xcb_shm_get_image
PUBLIC 1358 0 xcb_shm_get_image_unchecked
PUBLIC 13f0 0 xcb_shm_get_image_reply
PUBLIC 13f8 0 xcb_shm_create_pixmap_checked
PUBLIC 1480 0 xcb_shm_create_pixmap
PUBLIC 1508 0 xcb_shm_attach_fd_checked
PUBLIC 1590 0 xcb_shm_attach_fd
PUBLIC 1618 0 xcb_shm_create_segment
PUBLIC 1698 0 xcb_shm_create_segment_unchecked
PUBLIC 1718 0 xcb_shm_create_segment_reply
PUBLIC 1720 0 xcb_shm_create_segment_reply_fds
STACK CFI INIT d88 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT db8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT df8 48 .cfa: sp 0 + .ra: x30
STACK CFI dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e04 x19: .cfa -16 + ^
STACK CFI e3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e48 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT e68 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT e80 64 .cfa: sp 0 + .ra: x30
STACK CFI e84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e94 x19: .cfa -96 + ^
STACK CFI edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ee0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT ee8 64 .cfa: sp 0 + .ra: x30
STACK CFI eec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI efc x19: .cfa -96 + ^
STACK CFI f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f48 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT f50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f58 7c .cfa: sp 0 + .ra: x30
STACK CFI f5c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI f6c x19: .cfa -112 + ^
STACK CFI fcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fd0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT fd8 78 .cfa: sp 0 + .ra: x30
STACK CFI fdc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI fec x19: .cfa -112 + ^
STACK CFI 1048 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 104c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1050 6c .cfa: sp 0 + .ra: x30
STACK CFI 1054 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1064 x19: .cfa -96 + ^
STACK CFI 10b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10b8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10c0 68 .cfa: sp 0 + .ra: x30
STACK CFI 10c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10d4 x19: .cfa -96 + ^
STACK CFI 1120 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1124 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x29: .cfa -112 + ^
STACK CFI INIT 1128 cc .cfa: sp 0 + .ra: x30
STACK CFI 112c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 113c x19: .cfa -128 + ^
STACK CFI 11ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11f0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 11f8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 11fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 120c x19: .cfa -128 + ^
STACK CFI 12b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12bc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 12c0 98 .cfa: sp 0 + .ra: x30
STACK CFI 12c4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 12d4 x19: .cfa -128 + ^
STACK CFI 1350 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1354 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1358 94 .cfa: sp 0 + .ra: x30
STACK CFI 135c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 136c x19: .cfa -128 + ^
STACK CFI 13e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 13e8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 13f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13f8 88 .cfa: sp 0 + .ra: x30
STACK CFI 13fc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 140c x19: .cfa -128 + ^
STACK CFI 1478 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 147c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1480 84 .cfa: sp 0 + .ra: x30
STACK CFI 1484 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1494 x19: .cfa -128 + ^
STACK CFI 14fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1500 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1508 88 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 151c x19: .cfa -112 + ^
STACK CFI 1588 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 158c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1590 84 .cfa: sp 0 + .ra: x30
STACK CFI 1594 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 15a4 x19: .cfa -112 + ^
STACK CFI 160c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1610 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1618 7c .cfa: sp 0 + .ra: x30
STACK CFI 161c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 162c x19: .cfa -112 + ^
STACK CFI 168c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1690 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1698 7c .cfa: sp 0 + .ra: x30
STACK CFI 169c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 16ac x19: .cfa -112 + ^
STACK CFI 170c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1710 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1718 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1720 10 .cfa: sp 0 + .ra: x30
