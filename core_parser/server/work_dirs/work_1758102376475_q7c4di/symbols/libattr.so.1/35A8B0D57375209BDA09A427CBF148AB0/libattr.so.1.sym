MODULE Linux arm64 35A8B0D57375209BDA09A427CBF148AB0 libattr.so.1
INFO CODE_ID D5B0A83575739B20DA09A427CBF148AB6363EF37
PUBLIC 1508 0 attr_copy_action
PUBLIC 1880 0 attr_copy_check_permissions
PUBLIC 18a0 0 attr_copy_fd
PUBLIC 1e98 0 attr_copy_file
PUBLIC 2730 0 attr_get
PUBLIC 2880 0 attr_getf
PUBLIC 29a8 0 attr_set
PUBLIC 2ac0 0 attr_setf
PUBLIC 2bc0 0 attr_remove
PUBLIC 2ca8 0 attr_removef
PUBLIC 2d70 0 attr_list
PUBLIC 2fb8 0 attr_listf
PUBLIC 31c8 0 attr_multi
PUBLIC 32d0 0 attr_multif
PUBLIC 33d8 0 setxattr
PUBLIC 3410 0 lsetxattr
PUBLIC 3448 0 fsetxattr
PUBLIC 3480 0 getxattr
PUBLIC 34a0 0 lgetxattr
PUBLIC 34c0 0 fgetxattr
PUBLIC 34e0 0 listxattr
PUBLIC 34f8 0 llistxattr
PUBLIC 3510 0 flistxattr
PUBLIC 3528 0 removexattr
PUBLIC 3548 0 lremovexattr
PUBLIC 3568 0 fremovexattr
STACK CFI INIT 1448 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1478 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 14bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14c4 x19: .cfa -16 + ^
STACK CFI 14fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1500 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1508 378 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1514 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 151c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 155c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1560 .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 1568 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 156c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1574 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1608 x19: x19 x20: x20
STACK CFI 160c x25: x25 x26: x26
STACK CFI 1610 x27: x27 x28: x28
STACK CFI 1620 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1624 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 16cc x19: x19 x20: x20
STACK CFI 16d0 x25: x25 x26: x26
STACK CFI 16d4 x27: x27 x28: x28
STACK CFI 16d8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1874 x19: x19 x20: x20
STACK CFI 1878 x25: x25 x26: x26
STACK CFI 187c x27: x27 x28: x28
STACK CFI INIT 1880 1c .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1898 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18a0 5f4 .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 18b0 .cfa: x29 160 +
STACK CFI 18b4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 18c4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 18d0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 18d8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 18e8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1c80 .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1e98 5f4 .cfa: sp 0 + .ra: x30
STACK CFI 1e9c .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1ea8 .cfa: x29 144 +
STACK CFI 1eac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1ebc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1ecc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1ed4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 1ef0 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2278 .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2490 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 249c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 24fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2500 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2570 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2574 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 257c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 258c x21: .cfa -16 + ^
STACK CFI 2618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 261c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2638 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2660 cc .cfa: sp 0 + .ra: x30
STACK CFI 2664 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 266c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2678 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2684 x23: .cfa -16 + ^
STACK CFI 26c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 26cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2730 14c .cfa: sp 0 + .ra: x30
STACK CFI 2734 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 2740 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 2750 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 276c x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 2784 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 2790 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 2824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2828 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 2880 128 .cfa: sp 0 + .ra: x30
STACK CFI 2884 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 288c x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 2898 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 28b0 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 28bc x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 28c8 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 2958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 295c .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 29a8 114 .cfa: sp 0 + .ra: x30
STACK CFI 29ac .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 29b4 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 29c0 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 29dc x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 29e8 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 2aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2aa4 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 2ac0 100 .cfa: sp 0 + .ra: x30
STACK CFI 2ac4 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 2acc x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 2adc x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 2af8 x19: .cfa -384 + ^ x20: .cfa -376 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 2b04 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 2bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2bb0 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI INIT 2bc0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 2bcc x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 2bd4 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 2be4 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 2bec x25: .cfa -320 + ^ x26: .cfa -312 + ^
STACK CFI 2c08 x27: .cfa -304 + ^
STACK CFI 2c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2c8c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x25: .cfa -320 + ^ x26: .cfa -312 + ^ x27: .cfa -304 + ^ x29: .cfa -384 + ^
STACK CFI INIT 2ca8 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 2cb4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 2cc0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 2ccc x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 2ce4 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 2d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d60 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x29: .cfa -368 + ^
STACK CFI INIT 2d70 248 .cfa: sp 0 + .ra: x30
STACK CFI 2d74 .cfa: sp 65536 +
STACK CFI 2d7c .cfa: sp 65968 +
STACK CFI 2d88 .ra: .cfa -65960 + ^ x29: .cfa -65968 + ^
STACK CFI 2d90 x19: .cfa -65952 + ^ x20: .cfa -65944 + ^
STACK CFI 2db0 x27: .cfa -65888 + ^ x28: .cfa -65880 + ^
STACK CFI 2db8 x25: .cfa -65904 + ^ x26: .cfa -65896 + ^
STACK CFI 2dc4 x23: .cfa -65920 + ^ x24: .cfa -65912 + ^
STACK CFI 2e08 x21: .cfa -65936 + ^ x22: .cfa -65928 + ^
STACK CFI 2ebc x21: x21 x22: x22
STACK CFI 2ec4 x23: x23 x24: x24
STACK CFI 2ec8 x25: x25 x26: x26
STACK CFI 2ef8 .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 2efc .cfa: sp 65536 +
STACK CFI 2f00 .cfa: sp 0 +
STACK CFI 2f04 .cfa: sp 65968 + .ra: .cfa -65960 + ^ x19: .cfa -65952 + ^ x20: .cfa -65944 + ^ x23: .cfa -65920 + ^ x24: .cfa -65912 + ^ x25: .cfa -65904 + ^ x26: .cfa -65896 + ^ x27: .cfa -65888 + ^ x28: .cfa -65880 + ^ x29: .cfa -65968 + ^
STACK CFI 2f1c x23: x23 x24: x24
STACK CFI 2f20 x25: x25 x26: x26
STACK CFI 2f24 x21: .cfa -65936 + ^ x22: .cfa -65928 + ^ x23: .cfa -65920 + ^ x24: .cfa -65912 + ^ x25: .cfa -65904 + ^ x26: .cfa -65896 + ^
STACK CFI 2f64 x21: x21 x22: x22
STACK CFI 2f68 x25: x25 x26: x26
STACK CFI 2f70 x23: x23 x24: x24
STACK CFI 2f88 x21: .cfa -65936 + ^ x22: .cfa -65928 + ^ x23: .cfa -65920 + ^ x24: .cfa -65912 + ^ x25: .cfa -65904 + ^ x26: .cfa -65896 + ^
STACK CFI 2f98 x21: x21 x22: x22
STACK CFI 2f9c x23: x23 x24: x24
STACK CFI 2fa0 x25: x25 x26: x26
STACK CFI 2fac x21: .cfa -65936 + ^ x22: .cfa -65928 + ^
STACK CFI 2fb0 x23: .cfa -65920 + ^ x24: .cfa -65912 + ^
STACK CFI 2fb4 x25: .cfa -65904 + ^ x26: .cfa -65896 + ^
STACK CFI INIT 2fb8 20c .cfa: sp 0 + .ra: x30
STACK CFI 2fbc .cfa: sp 65536 +
STACK CFI 2fc4 .cfa: sp 65952 +
STACK CFI 2fd0 .ra: .cfa -65944 + ^ x29: .cfa -65952 + ^
STACK CFI 2fd8 x21: .cfa -65920 + ^ x22: .cfa -65912 + ^
STACK CFI 2ffc x25: .cfa -65888 + ^ x26: .cfa -65880 + ^ x27: .cfa -65872 + ^ x28: .cfa -65864 + ^
STACK CFI 3008 x19: .cfa -65936 + ^ x20: .cfa -65928 + ^
STACK CFI 3010 x23: .cfa -65904 + ^ x24: .cfa -65896 + ^
STACK CFI 30f4 x19: x19 x20: x20
STACK CFI 30fc x23: x23 x24: x24
STACK CFI 3130 .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3134 .cfa: sp 65536 +
STACK CFI 3138 .cfa: sp 0 +
STACK CFI 313c .cfa: sp 65952 + .ra: .cfa -65944 + ^ x19: .cfa -65936 + ^ x20: .cfa -65928 + ^ x21: .cfa -65920 + ^ x22: .cfa -65912 + ^ x23: .cfa -65904 + ^ x24: .cfa -65896 + ^ x25: .cfa -65888 + ^ x26: .cfa -65880 + ^ x27: .cfa -65872 + ^ x28: .cfa -65864 + ^ x29: .cfa -65952 + ^
STACK CFI 3164 x19: x19 x20: x20
STACK CFI 3168 x23: x23 x24: x24
STACK CFI 316c x19: .cfa -65936 + ^ x20: .cfa -65928 + ^ x23: .cfa -65904 + ^ x24: .cfa -65896 + ^
STACK CFI 3180 x19: x19 x20: x20
STACK CFI 3184 x23: x23 x24: x24
STACK CFI 319c x19: .cfa -65936 + ^ x20: .cfa -65928 + ^ x23: .cfa -65904 + ^ x24: .cfa -65896 + ^
STACK CFI 31ac x19: x19 x20: x20
STACK CFI 31b0 x23: x23 x24: x24
STACK CFI 31bc x19: .cfa -65936 + ^ x20: .cfa -65928 + ^
STACK CFI 31c0 x23: .cfa -65904 + ^ x24: .cfa -65896 + ^
STACK CFI INIT 31c8 108 .cfa: sp 0 + .ra: x30
STACK CFI 31cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 31d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 31e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 31ec x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 32b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 32b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 32d0 108 .cfa: sp 0 + .ra: x30
STACK CFI 32d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 32dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 33c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 33d8 38 .cfa: sp 0 + .ra: x30
STACK CFI 33e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 340c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3410 38 .cfa: sp 0 + .ra: x30
STACK CFI 341c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3444 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3448 38 .cfa: sp 0 + .ra: x30
STACK CFI 3454 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 347c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3480 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34f8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3510 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3528 20 .cfa: sp 0 + .ra: x30
STACK CFI 352c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3544 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3548 20 .cfa: sp 0 + .ra: x30
STACK CFI 354c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3568 20 .cfa: sp 0 + .ra: x30
STACK CFI 356c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x29: x29
