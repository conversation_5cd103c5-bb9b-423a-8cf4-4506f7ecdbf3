MODULE Linux arm64 DFB0FA326B3C0620DEB116A868307A720 libextend_interface.so
INFO CODE_ID 32FAB0DF3C6B2006DEB116A868307A72
PUBLIC 1648 0 _init
PUBLIC 1840 0 _GLOBAL__sub_I_extend_calib.cpp
PUBLIC 188c 0 call_weak_fn
PUBLIC 18a0 0 deregister_tm_clones
PUBLIC 18d0 0 register_tm_clones
PUBLIC 190c 0 __do_global_dtors_aux
PUBLIC 195c 0 frame_dummy
PUBLIC 1960 0 lios::extend::ConvertImage::GetBuffParams(std::shared_ptr<lios::camera::camera_stream::StreamImageData> const&)
PUBLIC 1c70 0 lios::extend::ConvertImage::Convert(std::shared_ptr<lios::camera::camera_stream::StreamImageData> const&, cv::Mat&)
PUBLIC 2370 0 lios::extend::ConvertImage::convert(std::shared_ptr<lios::camera::camera_stream::StreamImageData>*, cv::Mat&)
PUBLIC 2380 0 cv::Mat::~Mat()
PUBLIC 2420 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&)
PUBLIC 24fc 0 _fini
STACK CFI INIT 18a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 190c 50 .cfa: sp 0 + .ra: x30
STACK CFI 191c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1924 x19: .cfa -16 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 195c 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2380 94 .cfa: sp 0 + .ra: x30
STACK CFI 2384 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2390 x19: .cfa -16 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 240c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1960 310 .cfa: sp 0 + .ra: x30
STACK CFI 1964 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 196c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1988 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1bd4 x23: .cfa -16 + ^
STACK CFI 1c20 x23: x23
STACK CFI 1c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2420 dc .cfa: sp 0 + .ra: x30
STACK CFI 2424 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2430 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2484 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 249c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1c70 6f8 .cfa: sp 0 + .ra: x30
STACK CFI 1c74 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1c7c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 1c88 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 1c9c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1e28 x23: x23 x24: x24
STACK CFI 1e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e30 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI 1e48 x23: x23 x24: x24
STACK CFI 1e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e5c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI 1e60 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 1f1c x25: x25 x26: x26
STACK CFI 1f24 x23: x23 x24: x24
STACK CFI 1f48 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1f74 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 20e0 x23: x23 x24: x24
STACK CFI 20e4 x25: x25 x26: x26
STACK CFI 20e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20ec .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x29: .cfa -240 + ^
STACK CFI 2108 x23: x23 x24: x24
STACK CFI 210c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2148 x25: x25 x26: x26
STACK CFI 21a8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 21dc x25: x25 x26: x26
STACK CFI 2204 x23: x23 x24: x24
STACK CFI 2208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 220c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI 2224 x25: x25 x26: x26
STACK CFI 223c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2298 x23: x23 x24: x24
STACK CFI 229c x25: x25 x26: x26
STACK CFI 22a0 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 22e4 x25: x25 x26: x26
STACK CFI 22fc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 235c x25: x25 x26: x26
STACK CFI 2360 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT 2370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1840 4c .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1854 x19: .cfa -32 + ^
STACK CFI 1888 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
