MODULE Linux arm64 8119564B567CF2D32A0186D0C77B19BB0 libnss_nis.so.2
INFO CODE_ID 4B5619817C56D3F22A0186D0C77B19BB924F490B
PUBLIC 2398 0 _nss_nis_setprotoent
PUBLIC 23f8 0 _nss_nis_endprotoent
PUBLIC 2468 0 _nss_nis_getprotoent_r
PUBLIC 2578 0 _nss_nis_getprotobyname_r
PUBLIC 2728 0 _nss_nis_getprotobynumber_r
PUBLIC 2b40 0 _nss_nis_endservent
PUBLIC 2bb8 0 _nss_nis_setservent
PUBLIC 2c18 0 _nss_nis_getservent_r
PUBLIC 2d90 0 _nss_nis_getservbyname_r
PUBLIC 3098 0 _nss_nis_getservbyport_r
PUBLIC 3938 0 _nss_nis_endhostent
PUBLIC 39a8 0 _nss_nis_gethostent_r
PUBLIC 3d08 0 _nss_nis_gethostbyname2_r
PUBLIC 3d28 0 _nss_nis_gethostbyname_r
PUBLIC 3d50 0 _nss_nis_gethostbyaddr_r
PUBLIC 3f50 0 _nss_nis_gethostbyname4_r
PUBLIC 4260 0 _nss_nis_setnetent
PUBLIC 42d0 0 _nss_nis_getnetent_r
PUBLIC 45c0 0 _nss_nis_getnetbyname_r
PUBLIC 4858 0 _nss_nis_getnetbyaddr_r
PUBLIC 4af0 0 _nss_nis_endgrent
PUBLIC 4c10 0 _nss_nis_setgrent
PUBLIC 4c80 0 _nss_nis_getgrent_r
PUBLIC 5030 0 _nss_nis_getgrnam_r
PUBLIC 51e0 0 _nss_nis_getgrgid_r
PUBLIC 5550 0 _nss_nis_endpwent
PUBLIC 5670 0 _nss_nis_setpwent
PUBLIC 56e0 0 _nss_nis_getpwent_r
PUBLIC 5bd8 0 _nss_nis_getpwnam_r
PUBLIC 5ef8 0 _nss_nis_getpwuid_r
PUBLIC 6420 0 _nss_nis_setrpcent
PUBLIC 6478 0 _nss_nis_endrpcent
PUBLIC 64e0 0 _nss_nis_getrpcent_r
PUBLIC 6590 0 _nss_nis_getrpcbyname_r
PUBLIC 6710 0 _nss_nis_getrpcbynumber_r
PUBLIC 6a48 0 _nss_nis_endetherent
PUBLIC 6ab8 0 _nss_nis_setetherent
PUBLIC 6b18 0 _nss_nis_getetherent_r
PUBLIC 6c28 0 _nss_nis_gethostton_r
PUBLIC 6dd8 0 _nss_nis_getntohost_r
PUBLIC 6fc8 0 _nss_nis_setspent
PUBLIC 7038 0 _nss_nis_getspent_r
PUBLIC 7380 0 _nss_nis_getspnam_r
PUBLIC 7608 0 _nss_nis_setnetgrent
PUBLIC 7728 0 _nss_nis_endnetgrent
PUBLIC 7758 0 _nss_nis_getnetgrent_r
PUBLIC 7908 0 _nss_nis_setaliasent
PUBLIC 7978 0 _nss_nis_getaliasent_r
PUBLIC 7c38 0 _nss_nis_getaliasbyname_r
PUBLIC 7ef8 0 _nss_nis_getpublickey
PUBLIC 8038 0 _nss_nis_getsecretkey
PUBLIC 81d0 0 _nss_nis_netname2user
PUBLIC 83f0 0 _nss_nis_initgroups_dyn
STACK CFI INIT 2148 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2178 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b8 48 .cfa: sp 0 + .ra: x30
STACK CFI 21bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21c4 x19: .cfa -16 + ^
STACK CFI 21fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2200 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2208 d4 .cfa: sp 0 + .ra: x30
STACK CFI 220c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2214 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2228 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 22cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 22e0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2310 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2318 x19: .cfa -32 + ^
STACK CFI 237c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2380 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 238c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2390 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2398 5c .cfa: sp 0 + .ra: x30
STACK CFI 239c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23ac x19: .cfa -16 + ^
STACK CFI 23f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23f8 70 .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 240c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2468 10c .cfa: sp 0 + .ra: x30
STACK CFI 246c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 247c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2484 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2490 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 249c x25: .cfa -16 + ^
STACK CFI 2558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 255c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2578 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 257c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2584 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2590 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 25a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 25f8 x25: .cfa -48 + ^
STACK CFI 2670 x25: x25
STACK CFI 2698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 269c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 26e0 x25: .cfa -48 + ^
STACK CFI 26f4 x25: x25
STACK CFI 26fc x25: .cfa -48 + ^
STACK CFI 270c x25: x25
STACK CFI 2724 x25: .cfa -48 + ^
STACK CFI INIT 2728 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 272c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2734 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2744 x25: .cfa -96 + ^
STACK CFI 2758 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2764 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 285c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2860 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 28d0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 28d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 28dc x21: .cfa -48 + ^
STACK CFI 2900 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2988 x19: x19 x20: x20
STACK CFI 29ac .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 29b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 29b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29c0 x19: x19 x20: x20
STACK CFI 29c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 29c8 178 .cfa: sp 0 + .ra: x30
STACK CFI 29e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29f0 x21: .cfa -16 + ^
STACK CFI 2a04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a9c x19: x19 x20: x20
STACK CFI 2aa8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b04 x19: x19 x20: x20
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2b10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b20 x19: x19 x20: x20
STACK CFI 2b24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b34 x19: x19 x20: x20
STACK CFI INIT 2b40 78 .cfa: sp 0 + .ra: x30
STACK CFI 2b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2bb8 5c .cfa: sp 0 + .ra: x30
STACK CFI 2bbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bcc x19: .cfa -16 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c18 174 .cfa: sp 0 + .ra: x30
STACK CFI 2c1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c34 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c4c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2d90 308 .cfa: sp 0 + .ra: x30
STACK CFI 2d94 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2d98 .cfa: x29 208 +
STACK CFI 2d9c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2db8 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2dc8 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f54 .cfa: x29 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3098 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 309c .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 30a0 .cfa: x29 256 +
STACK CFI 30a4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 30b4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 30bc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 30cc x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 30d8 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 327c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3280 .cfa: x29 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 3360 2ec .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 336c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3374 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 337c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 338c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3394 x27: .cfa -16 + ^
STACK CFI 355c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3560 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 359c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 35a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 360c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3650 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 3654 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3658 .cfa: x29 144 +
STACK CFI 365c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3664 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3688 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3698 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 385c .cfa: x29 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 3938 6c .cfa: sp 0 + .ra: x30
STACK CFI 393c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 394c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 39a8 35c .cfa: sp 0 + .ra: x30
STACK CFI 39ac .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 39c0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 39d8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 39e0 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3a24 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3a3c x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3ba4 x25: x25 x26: x26
STACK CFI 3ba8 x27: x27 x28: x28
STACK CFI 3bb0 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3bc4 x25: x25 x26: x26
STACK CFI 3bcc x27: x27 x28: x28
STACK CFI 3c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3c24 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 3c40 x25: x25 x26: x26
STACK CFI 3c44 x27: x27 x28: x28
STACK CFI 3c54 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3c60 x25: x25 x26: x26
STACK CFI 3c64 x27: x27 x28: x28
STACK CFI 3c6c x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3c7c x25: x25 x26: x26
STACK CFI 3c84 x27: x27 x28: x28
STACK CFI 3c98 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3cb4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3cd8 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3ce4 x25: x25 x26: x26
STACK CFI 3ce8 x27: x27 x28: x28
STACK CFI 3cf0 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3cf8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3cfc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3d00 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 3d08 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d28 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d50 1fc .cfa: sp 0 + .ra: x30
STACK CFI 3d54 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3d5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3d6c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3d88 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3d90 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3d9c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3ec0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3f50 310 .cfa: sp 0 + .ra: x30
STACK CFI 3f54 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3f58 .cfa: x29 160 +
STACK CFI 3f5c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3f6c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 3f88 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3f90 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3fa0 x27: .cfa -80 + ^
STACK CFI 4148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 414c .cfa: x29 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x29: .cfa -160 + ^
STACK CFI INIT 4260 6c .cfa: sp 0 + .ra: x30
STACK CFI 4264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4274 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42d0 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 42d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 42e8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4300 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4330 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 433c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4340 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4480 x23: x23 x24: x24
STACK CFI 4484 x25: x25 x26: x26
STACK CFI 4488 x27: x27 x28: x28
STACK CFI 44cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44d0 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI 44e4 x23: x23 x24: x24
STACK CFI 44ec x25: x25 x26: x26
STACK CFI 44f0 x27: x27 x28: x28
STACK CFI 4500 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 451c x23: x23 x24: x24
STACK CFI 4520 x25: x25 x26: x26
STACK CFI 4524 x27: x27 x28: x28
STACK CFI 4534 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4568 x23: x23 x24: x24
STACK CFI 456c x25: x25 x26: x26
STACK CFI 4570 x27: x27 x28: x28
STACK CFI 4588 x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4590 x23: x23 x24: x24
STACK CFI 4594 x25: x25 x26: x26
STACK CFI 4598 x27: x27 x28: x28
STACK CFI 459c x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 45a4 x23: x23 x24: x24
STACK CFI 45a8 x25: x25 x26: x26
STACK CFI 45ac x27: x27 x28: x28
STACK CFI 45b4 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 45b8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 45bc x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 45c0 294 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 45c8 .cfa: x29 128 +
STACK CFI 45cc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 45e8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 45f0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 45fc x27: .cfa -48 + ^
STACK CFI 4778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 477c .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4858 22c .cfa: sp 0 + .ra: x30
STACK CFI 485c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4864 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4874 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4890 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 48a0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 48b8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4984 x27: x27 x28: x28
STACK CFI 49b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 49b8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 49d0 x27: x27 x28: x28
STACK CFI 49e0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a34 x27: x27 x28: x28
STACK CFI 4a48 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a64 x27: x27 x28: x28
STACK CFI 4a70 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4a78 x27: x27 x28: x28
STACK CFI 4a80 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 4a88 64 .cfa: sp 0 + .ra: x30
STACK CFI 4a8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4af0 50 .cfa: sp 0 + .ra: x30
STACK CFI 4af4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b40 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4b44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c10 70 .cfa: sp 0 + .ra: x30
STACK CFI 4c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c24 x19: .cfa -16 + ^
STACK CFI 4c70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4c80 3ac .cfa: sp 0 + .ra: x30
STACK CFI 4c84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 4c98 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 4cb4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 4cc0 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 4cc8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 4cf4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4e18 x27: x27 x28: x28
STACK CFI 4e1c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4e7c x27: x27 x28: x28
STACK CFI 4ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4ed0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 4f90 x27: x27 x28: x28
STACK CFI 4f94 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4fc4 x27: x27 x28: x28
STACK CFI 4fd8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 4ff8 x27: x27 x28: x28
STACK CFI 500c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5014 x27: x27 x28: x28
STACK CFI 5018 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 5020 x27: x27 x28: x28
STACK CFI 5028 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 5030 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 5034 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 503c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5048 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 505c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 50b0 x25: .cfa -48 + ^
STACK CFI 5128 x25: x25
STACK CFI 5150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5154 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 5198 x25: .cfa -48 + ^
STACK CFI 51ac x25: x25
STACK CFI 51b4 x25: .cfa -48 + ^
STACK CFI 51c4 x25: x25
STACK CFI 51dc x25: .cfa -48 + ^
STACK CFI INIT 51e0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 51e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 51ec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 51fc x25: .cfa -96 + ^
STACK CFI 5210 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 521c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5314 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5388 68 .cfa: sp 0 + .ra: x30
STACK CFI 538c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 539c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53a8 x21: .cfa -16 + ^
STACK CFI 53ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 53f0 160 .cfa: sp 0 + .ra: x30
STACK CFI 5420 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5428 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5430 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5438 x23: .cfa -32 + ^
STACK CFI 5498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 549c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 54f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 54fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5550 50 .cfa: sp 0 + .ra: x30
STACK CFI 5554 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 559c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 55a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 55ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 565c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5670 70 .cfa: sp 0 + .ra: x30
STACK CFI 5674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5684 x19: .cfa -16 + ^
STACK CFI 56d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 56e0 4f8 .cfa: sp 0 + .ra: x30
STACK CFI 56e4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 56f0 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 5700 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 571c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5728 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 5754 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5898 x27: x27 x28: x28
STACK CFI 589c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5a4c x27: x27 x28: x28
STACK CFI 5a50 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5acc x27: x27 x28: x28
STACK CFI 5b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5b1c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 5b30 x27: x27 x28: x28
STACK CFI 5b38 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5b5c x27: x27 x28: x28
STACK CFI 5b70 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5b84 x27: x27 x28: x28
STACK CFI 5b90 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5ba0 x27: x27 x28: x28
STACK CFI 5ba8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5bb0 x27: x27 x28: x28
STACK CFI 5bb4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 5bcc x27: x27 x28: x28
STACK CFI 5bd4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 5bd8 320 .cfa: sp 0 + .ra: x30
STACK CFI 5bdc .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5be4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 5bf0 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5c04 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 5c2c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5c68 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5cfc x25: x25 x26: x26
STACK CFI 5d00 x27: x27 x28: x28
STACK CFI 5d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5d2c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 5e30 x27: x27 x28: x28
STACK CFI 5e34 x25: x25 x26: x26
STACK CFI 5e40 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5e68 x25: x25 x26: x26
STACK CFI 5e6c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5e80 x25: x25 x26: x26
STACK CFI 5e84 x27: x27 x28: x28
STACK CFI 5e8c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5e9c x25: x25 x26: x26
STACK CFI 5ea0 x27: x27 x28: x28
STACK CFI 5ea4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5eb4 x25: x25 x26: x26
STACK CFI 5ed0 x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 5eec x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5ef0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 5ef4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 5ef8 300 .cfa: sp 0 + .ra: x30
STACK CFI 5efc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5f04 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5f14 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 5f2c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5f34 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 6048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 604c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x29: .cfa -192 + ^
STACK CFI 6094 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 6144 x27: x27 x28: x28
STACK CFI 61b8 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 61c4 x27: x27 x28: x28
STACK CFI 61d0 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 61f0 x27: x27 x28: x28
STACK CFI INIT 61f8 e4 .cfa: sp 0 + .ra: x30
STACK CFI 61fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6204 x21: .cfa -48 + ^
STACK CFI 620c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 62c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 62c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 62e0 140 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 62ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 62f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6300 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6310 x25: .cfa -16 + ^
STACK CFI 63c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 63c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 63e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 63e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6418 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6420 58 .cfa: sp 0 + .ra: x30
STACK CFI 6424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6434 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6478 68 .cfa: sp 0 + .ra: x30
STACK CFI 647c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 648c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 64dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 64e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 64f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6500 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6508 x23: .cfa -16 + ^
STACK CFI 657c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6580 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6590 17c .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 659c x27: .cfa -48 + ^
STACK CFI 65a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 65c0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 65d8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 668c x25: x25 x26: x26
STACK CFI 66c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 66cc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI 66d4 x25: x25 x26: x26
STACK CFI 66d8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 66e8 x25: x25 x26: x26
STACK CFI 66ec x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 66f4 x25: x25 x26: x26
STACK CFI 6708 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 6710 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 6714 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 671c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 672c x25: .cfa -80 + ^
STACK CFI 6740 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 674c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6844 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 68b8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 68bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 68c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 68d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 697c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6980 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6990 b8 .cfa: sp 0 + .ra: x30
STACK CFI 69c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69c8 x19: .cfa -32 + ^
STACK CFI 6a2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 6a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6a48 70 .cfa: sp 0 + .ra: x30
STACK CFI 6a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6ab8 5c .cfa: sp 0 + .ra: x30
STACK CFI 6abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6acc x19: .cfa -16 + ^
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b18 10c .cfa: sp 0 + .ra: x30
STACK CFI 6b1c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6b2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b34 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6b40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6b4c x25: .cfa -16 + ^
STACK CFI 6c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6c0c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6c28 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 6c2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c34 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6c40 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6c54 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6ca8 x25: .cfa -48 + ^
STACK CFI 6d20 x25: x25
STACK CFI 6d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6d4c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 6d90 x25: .cfa -48 + ^
STACK CFI 6da4 x25: x25
STACK CFI 6dac x25: .cfa -48 + ^
STACK CFI 6dbc x25: x25
STACK CFI 6dd4 x25: .cfa -48 + ^
STACK CFI INIT 6dd8 1ec .cfa: sp 0 + .ra: x30
STACK CFI 6ddc .cfa: sp 176 +
STACK CFI 6de0 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6de8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6df4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6e08 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6e2c x25: .cfa -96 + ^
STACK CFI 6f00 x25: x25
STACK CFI 6f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6f30 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6f40 x25: x25
STACK CFI 6f44 x25: .cfa -96 + ^
STACK CFI 6f48 x25: x25
STACK CFI 6f50 x25: .cfa -96 + ^
STACK CFI 6f84 x25: x25
STACK CFI 6f90 x25: .cfa -96 + ^
STACK CFI 6fa4 x25: x25
STACK CFI 6fc0 x25: .cfa -96 + ^
STACK CFI INIT 6fc8 6c .cfa: sp 0 + .ra: x30
STACK CFI 6fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fdc x19: .cfa -16 + ^
STACK CFI 7030 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7038 348 .cfa: sp 0 + .ra: x30
STACK CFI 703c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7050 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 7068 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7094 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 709c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 70a0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7218 x23: x23 x24: x24
STACK CFI 721c x25: x25 x26: x26
STACK CFI 7220 x27: x27 x28: x28
STACK CFI 7264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7268 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 72c0 x23: x23 x24: x24
STACK CFI 72c4 x25: x25 x26: x26
STACK CFI 72c8 x27: x27 x28: x28
STACK CFI 72d0 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7340 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7348 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7350 x23: x23 x24: x24
STACK CFI 7354 x25: x25 x26: x26
STACK CFI 7358 x27: x27 x28: x28
STACK CFI 735c x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7364 x23: x23 x24: x24
STACK CFI 7368 x25: x25 x26: x26
STACK CFI 736c x27: x27 x28: x28
STACK CFI 7374 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7378 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 737c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 7380 284 .cfa: sp 0 + .ra: x30
STACK CFI 7384 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 738c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7398 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 73b8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 73c0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 73e0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7490 x19: x19 x20: x20
STACK CFI 7494 x25: x25 x26: x26
STACK CFI 7498 x27: x27 x28: x28
STACK CFI 74bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 74c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 74c4 x27: x27 x28: x28
STACK CFI 74d0 x19: x19 x20: x20
STACK CFI 74d4 x25: x25 x26: x26
STACK CFI 74d8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 7514 x19: x19 x20: x20
STACK CFI 7518 x25: x25 x26: x26
STACK CFI 7520 x27: x27 x28: x28
STACK CFI 7528 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 753c x19: x19 x20: x20
STACK CFI 7540 x25: x25 x26: x26
STACK CFI 7544 x27: x27 x28: x28
STACK CFI 754c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 755c x19: x19 x20: x20
STACK CFI 7560 x25: x25 x26: x26
STACK CFI 7564 x27: x27 x28: x28
STACK CFI 7568 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 75e4 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 75f8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 75fc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7600 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 7608 120 .cfa: sp 0 + .ra: x30
STACK CFI 760c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7614 x21: .cfa -48 + ^
STACK CFI 761c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 76d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 76dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7728 30 .cfa: sp 0 + .ra: x30
STACK CFI 772c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7734 x19: .cfa -16 + ^
STACK CFI 7754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7758 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7778 18c .cfa: sp 0 + .ra: x30
STACK CFI 777c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7784 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7790 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 779c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 77a4 x25: .cfa -16 + ^
STACK CFI 78cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 78d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 78f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 78f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7908 6c .cfa: sp 0 + .ra: x30
STACK CFI 790c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 791c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7978 2bc .cfa: sp 0 + .ra: x30
STACK CFI 797c .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7990 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 79ac x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 79d4 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 79e0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 79e4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7b34 x23: x23 x24: x24
STACK CFI 7b38 x25: x25 x26: x26
STACK CFI 7b3c x27: x27 x28: x28
STACK CFI 7b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b84 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI 7b98 x23: x23 x24: x24
STACK CFI 7b9c x25: x25 x26: x26
STACK CFI 7ba0 x27: x27 x28: x28
STACK CFI 7ba8 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 7be0 x23: x23 x24: x24
STACK CFI 7be4 x25: x25 x26: x26
STACK CFI 7bec x27: x27 x28: x28
STACK CFI 7bfc x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 7c04 x23: x23 x24: x24
STACK CFI 7c08 x25: x25 x26: x26
STACK CFI 7c0c x27: x27 x28: x28
STACK CFI 7c10 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 7c18 x23: x23 x24: x24
STACK CFI 7c1c x25: x25 x26: x26
STACK CFI 7c20 x27: x27 x28: x28
STACK CFI 7c28 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 7c2c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 7c30 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 7c38 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 7c3c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7c40 .cfa: x29 128 +
STACK CFI 7c44 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 7c50 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 7c68 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 7c74 x27: .cfa -48 + ^
STACK CFI 7e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7e18 .cfa: x29 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7ef8 140 .cfa: sp 0 + .ra: x30
STACK CFI 7efc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f18 x23: .cfa -48 + ^
STACK CFI 7fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7fe0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8038 194 .cfa: sp 0 + .ra: x30
STACK CFI 803c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 8048 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 8050 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 8060 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 8158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 815c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI INIT 81d0 220 .cfa: sp 0 + .ra: x30
STACK CFI 81d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 81dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 81e8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8200 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8210 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 829c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 83f0 67c .cfa: sp 0 + .ra: x30
STACK CFI 83f4 .cfa: sp 1312 +
STACK CFI 83fc .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI 8400 .cfa: x29 1296 +
STACK CFI 840c x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI 8414 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI 8420 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI 842c x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI 86c4 .cfa: sp 1312 +
STACK CFI 86e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 86e4 .cfa: x29 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x28: .cfa -1208 + ^ x29: .cfa -1296 + ^
