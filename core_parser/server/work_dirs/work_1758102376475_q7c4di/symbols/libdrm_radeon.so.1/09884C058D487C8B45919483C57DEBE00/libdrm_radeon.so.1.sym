MODULE Linux arm64 09884C058D487C8B45919483C57DEBE00 libdrm_radeon.so.1
INFO CODE_ID 054C8809488D8B7C45919483C57DEBE08CEA4200
PUBLIC 1cc0 0 radeon_bo_manager_gem_ctor
PUBLIC 1d00 0 radeon_bo_manager_gem_dtor
PUBLIC 1d10 0 radeon_gem_name_bo
PUBLIC 1d18 0 radeon_gem_get_reloc_in_cs
PUBLIC 1d20 0 radeon_gem_get_kernel_name
PUBLIC 1dc0 0 radeon_gem_set_domain
PUBLIC 1e30 0 radeon_gem_prime_share_bo
PUBLIC 1e48 0 radeon_gem_bo_open_prime
PUBLIC 27e0 0 radeon_cs_manager_gem_ctor
PUBLIC 2880 0 radeon_cs_manager_gem_dtor
PUBLIC 2d70 0 radeon_cs_space_add_persistent_bo
PUBLIC 2e48 0 radeon_cs_space_check_with_bo
PUBLIC 2ea8 0 radeon_cs_space_check
PUBLIC 2eb0 0 radeon_cs_space_reset_bos
PUBLIC 2f18 0 radeon_bo_debug
PUBLIC 2f48 0 radeon_bo_open
PUBLIC 2f58 0 radeon_bo_ref
PUBLIC 2f78 0 radeon_bo_unref
PUBLIC 2fa0 0 radeon_bo_map
PUBLIC 2fb8 0 radeon_bo_unmap
PUBLIC 2fd0 0 radeon_bo_wait
PUBLIC 2ff0 0 radeon_bo_is_busy
PUBLIC 3008 0 radeon_bo_set_tiling
PUBLIC 3020 0 radeon_bo_get_tiling
PUBLIC 3038 0 radeon_bo_is_static
PUBLIC 3058 0 radeon_bo_is_referenced_by_cs
PUBLIC 3068 0 radeon_bo_get_handle
PUBLIC 3070 0 radeon_bo_get_src_domain
PUBLIC 3088 0 radeon_cs_create
PUBLIC 3098 0 radeon_cs_write_reloc
PUBLIC 30b0 0 radeon_cs_begin
PUBLIC 30c8 0 radeon_cs_end
PUBLIC 30e0 0 radeon_cs_emit
PUBLIC 30f8 0 radeon_cs_destroy
PUBLIC 3110 0 radeon_cs_erase
PUBLIC 3128 0 radeon_cs_need_flush
PUBLIC 3140 0 radeon_cs_print
PUBLIC 3158 0 radeon_cs_set_limit
PUBLIC 3178 0 radeon_cs_space_set_flush
PUBLIC 3188 0 radeon_cs_get_id
PUBLIC 5ea0 0 radeon_surface_manager_new
PUBLIC 7510 0 radeon_surface_manager_free
PUBLIC 7518 0 radeon_surface_init
PUBLIC 7550 0 radeon_surface_best
STACK CFI INIT 1688 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16b8 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f8 48 .cfa: sp 0 + .ra: x30
STACK CFI 16fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1704 x19: .cfa -16 + ^
STACK CFI 173c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1740 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1748 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1750 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1770 70 .cfa: sp 0 + .ra: x30
STACK CFI 1774 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17e0 90 .cfa: sp 0 + .ra: x30
STACK CFI 17e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1820 x21: .cfa -48 + ^
STACK CFI 1868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 186c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1870 6c .cfa: sp 0 + .ra: x30
STACK CFI 1874 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1898 x19: .cfa -48 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 18e0 84 .cfa: sp 0 + .ra: x30
STACK CFI 18e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18f8 x21: .cfa -32 + ^
STACK CFI 195c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1960 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1968 198 .cfa: sp 0 + .ra: x30
STACK CFI 196c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1974 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1980 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 19ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1a48 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1b00 124 .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1b0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b2c x21: .cfa -64 + ^
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b80 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1c28 98 .cfa: sp 0 + .ra: x30
STACK CFI 1c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1cc0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cd0 x19: .cfa -16 + ^
STACK CFI 1cf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d20 9c .cfa: sp 0 + .ra: x30
STACK CFI 1d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d48 x21: .cfa -32 + ^
STACK CFI 1d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1dc0 6c .cfa: sp 0 + .ra: x30
STACK CFI 1dc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1de8 x19: .cfa -48 + ^
STACK CFI 1e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1e30 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e48 cc .cfa: sp 0 + .ra: x30
STACK CFI 1e4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1e54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1e7c x23: .cfa -32 + ^
STACK CFI 1efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1f00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1f18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f20 88 .cfa: sp 0 + .ra: x30
STACK CFI 1f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1fa8 9c .cfa: sp 0 + .ra: x30
STACK CFI 1fac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1fb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fd0 x21: .cfa -16 + ^
STACK CFI 2020 x21: x21
STACK CFI 2040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2048 70 .cfa: sp 0 + .ra: x30
STACK CFI 204c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2054 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2064 x21: .cfa -16 + ^
STACK CFI 20b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 20b8 144 .cfa: sp 0 + .ra: x30
STACK CFI 20bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 20c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 20d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 20e8 x23: .cfa -48 + ^
STACK CFI 21f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 21f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2200 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 2204 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 220c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 221c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2250 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 22c8 x21: x21 x22: x22
STACK CFI 22d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 23a8 x21: x21 x22: x22
STACK CFI 23b8 x23: x23 x24: x24
STACK CFI 23bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 247c x21: x21 x22: x22
STACK CFI 2480 x23: x23 x24: x24
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2488 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2498 x21: x21 x22: x22
STACK CFI 249c x23: x23 x24: x24
STACK CFI 24bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 24c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 24c8 148 .cfa: sp 0 + .ra: x30
STACK CFI 24cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2518 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 25b0 x21: x21 x22: x22
STACK CFI 25c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 25d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 25fc x21: x21 x22: x22
STACK CFI INIT 2610 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2614 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2620 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2628 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 265c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 266c x23: .cfa -16 + ^
STACK CFI 26ac x23: x23
STACK CFI INIT 26e8 f4 .cfa: sp 0 + .ra: x30
STACK CFI 26ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2704 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 270c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 274c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2750 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2788 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 27e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 27e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2808 x21: .cfa -48 + ^
STACK CFI 2878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 287c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2888 4e4 .cfa: sp 0 + .ra: x30
STACK CFI 288c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2894 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 28a4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 28c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 28f4 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2adc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2d70 d8 .cfa: sp 0 + .ra: x30
STACK CFI 2d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e48 60 .cfa: sp 0 + .ra: x30
STACK CFI 2e4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e54 x19: .cfa -48 + ^
STACK CFI 2ea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ea4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ea8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb0 64 .cfa: sp 0 + .ra: x30
STACK CFI 2eb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ebc x21: .cfa -16 + ^
STACK CFI 2ed0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f04 x19: x19 x20: x20
STACK CFI 2f10 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 2f18 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f48 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f58 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f78 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ff0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3008 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3020 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3038 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3058 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3068 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3070 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3088 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3098 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3110 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3128 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3140 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3158 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3178 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3188 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3190 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3198 41c .cfa: sp 0 + .ra: x30
STACK CFI 3584 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 35bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35c8 x19: .cfa -32 + ^
STACK CFI 3618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 361c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3630 178 .cfa: sp 0 + .ra: x30
STACK CFI 3778 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37a8 25c .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37cc x21: .cfa -16 + ^
STACK CFI 384c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3850 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 396c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3970 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3a08 130 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b38 244 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d80 244 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fc8 dc .cfa: sp 0 + .ra: x30
STACK CFI 3fcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fdc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3fe8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4004 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 40a8 14c .cfa: sp 0 + .ra: x30
STACK CFI 40ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 40bc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 40d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 40e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4144 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 41dc x21: x21 x22: x22
STACK CFI 41f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 41f8 8c .cfa: sp 0 + .ra: x30
STACK CFI 41fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 420c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4218 x21: .cfa -16 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 424c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4288 238 .cfa: sp 0 + .ra: x30
STACK CFI 428c .cfa: sp 128 +
STACK CFI 4290 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4298 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 42ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 42d0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4434 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4488 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 44b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 44b8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 44c0 22c .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 128 +
STACK CFI 44cc .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 44d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 44e0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 44f4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 44fc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 465c .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 46ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46b0 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 46e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46e4 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 46f0 514 .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4c08 78 .cfa: sp 0 + .ra: x30
STACK CFI 4c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c18 x19: .cfa -32 + ^
STACK CFI 4c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c80 160 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4de0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4dfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4e88 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4e8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ea4 x19: .cfa -16 + ^
STACK CFI 4f40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f50 224 .cfa: sp 0 + .ra: x30
STACK CFI 4f54 .cfa: sp 96 +
STACK CFI 4f58 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4f60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4f70 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4f84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 500c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5010 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5178 fc .cfa: sp 0 + .ra: x30
STACK CFI 517c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5194 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 51d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5250 x19: x19 x20: x20
STACK CFI 525c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5260 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5270 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5278 260 .cfa: sp 0 + .ra: x30
STACK CFI 527c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5288 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5328 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 5380 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5468 x21: x21 x22: x22
STACK CFI 5470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5474 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 548c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 549c x21: x21 x22: x22
STACK CFI 54b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 54c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 54d4 x21: x21 x22: x22
STACK CFI INIT 54d8 120 .cfa: sp 0 + .ra: x30
STACK CFI 54dc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 54ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5504 x23: .cfa -16 + ^
STACK CFI 5574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55d0 x19: x19 x20: x20
STACK CFI 55e0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 55e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 55f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 55f8 cc .cfa: sp 0 + .ra: x30
STACK CFI 55fc .cfa: sp 1616 +
STACK CFI 5610 .ra: .cfa -1608 + ^ x29: .cfa -1616 + ^
STACK CFI 561c x21: .cfa -1584 + ^ x22: .cfa -1576 + ^
STACK CFI 562c x23: .cfa -1568 + ^
STACK CFI 5638 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 5694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5698 .cfa: sp 1616 + .ra: .cfa -1608 + ^ x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x29: .cfa -1616 + ^
STACK CFI INIT 56c8 308 .cfa: sp 0 + .ra: x30
STACK CFI 56cc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5704 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 598c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 59cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 59d0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 59d4 .cfa: sp 1616 +
STACK CFI 59e0 .ra: .cfa -1608 + ^ x29: .cfa -1616 + ^
STACK CFI 59ec x21: .cfa -1584 + ^ x22: .cfa -1576 + ^
STACK CFI 5a10 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 5a24 x23: .cfa -1568 + ^
STACK CFI 5a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5a6c .cfa: sp 1616 + .ra: .cfa -1608 + ^ x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x29: .cfa -1616 + ^
STACK CFI INIT 5a98 128 .cfa: sp 0 + .ra: x30
STACK CFI 5a9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5aa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ab4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ba8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5bc0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 5bc4 .cfa: sp 96 +
STACK CFI 5bc8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5bd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5be0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5bf4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c80 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5ea0 166c .cfa: sp 0 + .ra: x30
STACK CFI 5ea4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5eb0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ebc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f88 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7510 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7518 34 .cfa: sp 0 + .ra: x30
STACK CFI 751c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7534 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7544 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7548 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7550 34 .cfa: sp 0 + .ra: x30
STACK CFI 7554 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 756c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 757c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7580 .cfa: sp 0 + .ra: .ra x29: x29
