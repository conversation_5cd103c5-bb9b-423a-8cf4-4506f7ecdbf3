Operating system: Linux
                  0.0.0 Linux 6.1.119-rt45-prod-rt-tegra #1 SMP PREEMPT_RT Sat Sep  6 00:56:30 CST 2025 aarch64
CPU: arm64
     12 CPUs

GPU: UNKNOWN

Crash reason:  SIGSEGV /0x00000000
Crash address: 0xb02
Process uptime: not available

Thread 0 (crashed)
 0  libe2e_common.so!uni_perception::vla::SamplePoint(std::vector<uni_perception::vla::Vec2d, std::allocator<uni_perception::vla::Vec2d> > const&, int) [types.hpp : 1169 + 0x0]
     x0 = 0x0000000000000007    x1 = 0x0000000000000000
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000ffffa066c800    x5 = 0x00000001093d65c0
     x6 = 0x0000000000000000    x7 = 0x0000ffff86180a28
     x8 = 0x0000fffd1897e100    x9 = 0x0000000000020613
    x10 = 0x0000ffffaf503158   x11 = 0x0002ffffa0816881
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000002
    x14 = 0x0000000000000020   x15 = 0x0000000000000a20
    x16 = 0x0000fffd6d1ad750   x17 = 0x0000fffd6c251fb0
    x18 = 0x0000000000000018   x19 = 0x0000000000000000
    x20 = 0x0000000000000000   x21 = 0x0000000000000000
    x22 = 0x0000000000000000   x23 = 0x0000000000000000
    x24 = 0x0000fffd1897e100   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000000000000000    fp = 0x0000fffd1897df20
     lr = 0x0000fffd6c97ae50    sp = 0x0000fffd1897df20
     pc = 0x0000fffd6c2523b0
    Found by: given as instruction pointer in context
 1  libe2e_nodes.so!operator() [vla_diffusion_impl.cpp : 1311 + 0x14]
    x19 = 0x0000000000000000   x20 = 0x0000000000000000
    x21 = 0x0000000000000000   x22 = 0x0000ffffb1f57b50
    x23 = 0x0000000000000000   x24 = 0x0000ffffb1f57b50
    x25 = 0x0000ffffb1f57b50   x26 = 0x0000fffd1897e100
    x27 = 0x0000fffd1897e1c0   x28 = 0x0000000000000000
     fp = 0x0000fffd1897e030    sp = 0x0000fffd1897e030
     pc = 0x0000fffd6c97ae50
    Found by: call frame info
 2  libe2e_nodes.so!uni_perception::vla::VlaDiffusionImpl::UpdateSdV2NodeSequence(uni_perception::vla::VlaModelFrame*) [vla_diffusion_impl.cpp : 1337 + 0x4]
    x19 = 0x0000000000000000   x20 = 0x0000fffd1897e1f0
    x21 = 0x0000000000000000   x22 = 0x0000ffffb1f57b50
    x23 = 0x0000000000000000   x24 = 0x0000fffd01be2010
    x25 = 0x00000000000000c0   x26 = 0x0000ffffa066c740
    x27 = 0x0000fffd1897e250   x28 = 0x0000fff5ce8e2c00
     fp = 0x0000fffd1897e120    sp = 0x0000fffd1897e120
     pc = 0x0000fffd6c98e070
    Found by: call frame info
 3  libe2e_nodes.so!uni_perception::vla::VlaDiffusionImpl::Detect(void*) [vla_diffusion_impl.cpp : 834 + 0x8]
    x19 = 0x0000fffa2f43ba10   x20 = 0x0000fff5ce8e2c00
    x21 = 0x0000000000000000   x22 = 0x0000fffcf627e810
    x23 = 0x0000fffd1897e460   x24 = 0x0000fffd01be2010
    x25 = 0x0000000000000007   x26 = 0x000000000000000d
    x27 = 0x0000ffffa0718160   x28 = 0x0000fffd6e5d8b00
     fp = 0x0000fffd1897e280    sp = 0x0000fffd1897e280
     pc = 0x0000fffd6c9962d4
    Found by: call frame info
 4  libe2e_nodes.so!uni_perception::vla::E2eModelExecutorNode::OnDiffusionInfer(std::shared_ptr<lios::node::DagMessage> const&) [e2e_model_executor_node.cpp : 489 + 0x4]
    x19 = 0x0000fff64d31e680   x20 = 0x0000fffa2f43ba10
    x21 = 0x0000000000000000   x22 = 0x0000fffcf627e810
    x23 = 0x0000fffd1897e460   x24 = 0x0000fffd01be2010
    x25 = 0x0000000000000007   x26 = 0x000000000000000d
    x27 = 0x0000ffffa0718160   x28 = 0x0000fffd6e5d8b00
     fp = 0x0000fffd1897e300    sp = 0x0000fffd1897e300
     pc = 0x0000fffd6c8ac19c
    Found by: call frame info
 5  libscheduling.so.3!std::_Function_handler<void (), lios::scheduling::DagGraph::TriggerTask(std::shared_ptr<lios::scheduling::CallbackHandle> const&, std::shared_ptr<void> const&, std::shared_ptr<lios::node::ItcHeader> const&)::{lambda()#1}>::_M_invoke(std::_Any_data const&) + 0x20
    x19 = 0x0000fff7f7135d40   x20 = 0x0000fffd0181c9f0
    x21 = 0x000000000000000f   x22 = 0x20c49ba5e353f7cf
    x23 = 0x0000ffffafe87528   x24 = 0x0000000000000001
    x25 = 0x0000ffffab9e70c0   x26 = 0x0000000000000000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e4b0    sp = 0x0000fffd1897e4b0
     pc = 0x0000ffffb1b46744
    Found by: call frame info
 6  libconcurrent.so.3!std::_Function_handler<void (), lios::concurrent::TaskHandler::SetTask(std::function<void ()>&&)::{lambda()#1}>::_M_invoke(std::_Any_data const&) + 0x8c
    x19 = 0x0000fffd019d5d50   x20 = 0x0000fffd0181c9f0
    x21 = 0x000000000000000f   x22 = 0x20c49ba5e353f7cf
    x23 = 0x0000ffffafe87528   x24 = 0x0000000000000001
    x25 = 0x0000ffffab9e70c0   x26 = 0x0000000000000000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e4d0    sp = 0x0000fffd1897e4d0
     pc = 0x0000ffffb1921600
    Found by: call frame info
 7  libconcurrent.so.3!lios::concurrent::TaskHandler::Run(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int) + 0x60
    x19 = 0x0000fffd019d5d50   x20 = 0x0000fffd019d5d70
    x21 = 0x0000fffd019d5d68   x22 = 0x0000ffffa2e06018
    x23 = 0x0000ffffafe87528   x24 = 0x0000000000000001
    x25 = 0x0000ffffab9e70c0   x26 = 0x0000000000000000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e510    sp = 0x0000fffd1897e510
     pc = 0x0000ffffb1921c84
    Found by: call frame info
 8  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0x1f8
    x19 = 0x0000ffffab9e7090   x20 = 0x0000fffd019d5d50
    x21 = 0x0000fffd1897e5d8   x22 = 0x0000ffffab9e7118
    x23 = 0x0000ffffafe87528   x24 = 0x0000000000000001
    x25 = 0x0000ffffab9e70c0   x26 = 0x0000000000000000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e580    sp = 0x0000fffd1897e580
     pc = 0x0000ffffb1912b3c
    Found by: call frame info
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
    x19 = 0x0000ffffaf8317a0   x20 = 0x0000fffd1897f2dc
    x21 = 0x0000fffd1897e6f8   x22 = 0x0000ffffb0207780
    x23 = 0x0000ffffafd555e0   x24 = 0x0000000000000002
    x25 = 0x0000fffd18172000   x26 = 0x0000ffffafe80000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e5f0    sp = 0x0000fffd1897e5f0
     pc = 0x0000ffffb0061ae0
    Found by: call frame info
10  libc.so.6 + 0x85958
    x19 = 0x0000000000000000   x20 = 0x0000fffd1897f2dc
    x21 = 0x0000fffd1897e6f8   x22 = 0x0000ffffb0207780
    x23 = 0x0000ffffafd555e0   x24 = 0x0000000000000002
    x25 = 0x0000fffd18172000   x26 = 0x0000ffffafe80000
    x27 = 0x0000ffffb0207040   x28 = 0x0000fffd18172000
     fp = 0x0000fffd1897e750    sp = 0x0000fffd1897e610
     pc = 0x0000ffffafd5595c
    Found by: call frame info
11  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fffd1897e760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 1
 0  libc.so.6 + 0xbbed0
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000fffd2c4af538    x3 = 0x0000fffd2c4af538
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000000000010    x7 = 0x0000000000002046
     x8 = 0x0000000000000073    x9 = 0x0010f62310985fca
    x10 = 0x00ffffffffffffff   x11 = 0x0000000475d8580d
    x12 = 0x0000000000003710   x13 = 0x0000000000010000
    x14 = 0x0000000000003730   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd961e0
    x18 = 0x0000ffffab9d9fe0   x19 = 0x0000000000000000
    x20 = 0x0000fffd2c4af538   x21 = 0x0000000000000000
    x22 = 0x0000fffd2c4af538   x23 = 0x0000ffffafe87000
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000ffffac219e80   x27 = 0x0000ffffab9c1540
    x28 = 0x0000ffffb1944af8    fp = 0x0000fffd2c4af4a0
     lr = 0x0000ffffafd8beb8    sp = 0x0000fffd2c4af470
     pc = 0x0000ffffafd8bed0
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xbbeb4
     fp = 0x0000fffd2c4af4f0    lr = 0x0000ffffafd961fc
     sp = 0x0000fffd2c4af4b0    pc = 0x0000ffffafd8beb8
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0xc61f8
     fp = 0x0000fffd2c4af500    lr = 0x0000ffffb193abd4
     sp = 0x0000fffd2c4af500    pc = 0x0000ffffafd961fc
    Found by: previous frame's frame pointer
 3  libtimer.so.3!lios::timer::TimeRate::Sleep(long) + 0xc0
     fp = 0x0000fffd2c4af550    lr = 0x0000ffffb193b8bc
     sp = 0x0000fffd2c4af510    pc = 0x0000ffffb193abd4
    Found by: previous frame's frame pointer
 4  0xffffb1944af4
    x19 = 0x0000ffffb1944508   x20 = 0x0023ffffb190f76c
    x21 = 0x0000fffd2c4af550    fp = 0x0000000000000028
     sp = 0x0000fffd2c4af560    pc = 0x0000ffffb1944af8
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2c4af588    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0x855dc
     sp = 0x0000fffd2c4af648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fffd2c4af760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 2
 0  libc.so.6 + 0xa2fc0
     x0 = 0x0000fffd1b5ff8d4    x1 = 0x0000000000000000
     x2 = 0x00000000000ad050    x3 = 0x0000fffd1b64a000
     x4 = 0x0000fffd1b6f70d0    x5 = 0x0000fff453895570
     x6 = 0x0000fff453dfd010    x7 = 0x000000000003de00
     x8 = 0x0000000000000000    x9 = 0x0000fffd1b9df958
    x10 = 0x00000000000bfab0   x11 = 0x0000ffff7f22ec18
    x12 = 0x7000000000000000   x13 = 0x0000000000000018
    x14 = 0x0000000000000318   x15 = 0x000000000000ebc8
    x16 = 0x0000fffd6d1a1840   x17 = 0x0000ffffafd72ec0
    x18 = 0x000000000000000e   x19 = 0x00000000000f77fc
    x20 = 0x0000fff453e03840   x21 = 0x00000000000f7800
    x22 = 0x0000fff453e03380   x23 = 0x0000fff453e02e80
    x24 = 0x0000000163a477c0   x25 = 0x0000fff9b3082130
    x26 = 0x0000000000000068   x27 = 0x0000fff5ca338e00
    x28 = 0x0000000165928d40    fp = 0x0000fffd1b9dd8d0
     lr = 0x0000fffd6c8f9608    sp = 0x0000fffd1b5ff8b0
     pc = 0x0000ffffafd72fc0
    Found by: given as instruction pointer in context

Thread 3
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1568    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd263f0610   x11 = 0x0000000000000030
    x12 = 0x0000000000002170   x13 = 0x0000000000010000
    x14 = 0x00000000000021a0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff861be890   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1568
    x22 = 0x0000ffffaf9c1568   x23 = 0x0000000000000020
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000010   x27 = 0x0000000000000000
    x28 = 0x0000fffd263ef4d8    fp = 0x0000fffd263ef480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd263ef480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd263ef520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd263ef490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd263ef580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd263ef530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd263ef5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd263ef590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd263f02d8
    x19 = 0x0000fffd263ef6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd25be3000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab839f18
     fp = 0x0000ffffab861010    sp = 0x0000fffd263ef600
     pc = 0x0000fffd263f02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd263ef648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd263ef760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 4
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1c2c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1999f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000290   x13 = 0x0000000000010000
    x14 = 0x0000000000000350   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f277c08   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1c2c
    x22 = 0x0000ffffaf9c1c2c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1999e4d8    fp = 0x0000fffd1999e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1999e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1999e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1999e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1999e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1999e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1999e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1999e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1999f2d8
    x19 = 0x0000fffd1999e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd19192000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e6b18
     fp = 0x0000ffffaf831760    sp = 0x0000fffd1999e600
     pc = 0x0000fffd1999f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1999e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1999e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 5
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1cec    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1918f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000970   x13 = 0x0000000000010000
    x14 = 0x0000000000000a30   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f234c08   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1cec
    x22 = 0x0000ffffaf9c1cec   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1918e4d8    fp = 0x0000fffd1918e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1918e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1918e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1918e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1918e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1918e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1918e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1918e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1918f2d8
    x19 = 0x0000fffd1918e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd18982000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e6e18
     fp = 0x0000ffffab861050    sp = 0x0000fffd1918e600
     pc = 0x0000fffd1918f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1918e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1918e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 6
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1e6c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1816f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000201570   x13 = 0x000000000020bf50
    x14 = 0x0000000000201630   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1beb98   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1e6c
    x22 = 0x0000ffffaf9c1e6c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1816e4d8    fp = 0x0000fffd1816e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1816e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1816e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1816e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1816e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1816e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1816e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1816e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1816f2d8
    x19 = 0x0000fffd1816e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd17962000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e7418
     fp = 0x0000ffffaf8317f0    sp = 0x0000fffd1816e600
     pc = 0x0000fffd1816f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1816e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1816e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 7
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c189c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1591f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000a10   x13 = 0x0000000000010000
    x14 = 0x0000000000000ad0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1d6a10   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c189c
    x22 = 0x0000ffffaf9c189c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1591e4d8    fp = 0x0000fffd1591e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1591e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1591e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1591e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1591e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1591e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1591e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1591e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1591f2d8
    x19 = 0x0000fffd1591e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd15112000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e8618
     fp = 0x0000ffffaf831920    sp = 0x0000fffd1591e600
     pc = 0x0000fffd1591f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1591e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1591e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 8
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c2708    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000f18
     x8 = 0x0000000000000062    x9 = 0x001b3d2ebc2f680a
    x10 = 0x00ffffffffffffff   x11 = 0x000000026b78510d
    x12 = 0x0000fffd128be5e0   x13 = 0x0000ffffaf8052d0
    x14 = 0x0000000000000000   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c2708
    x22 = 0x0000ffffaf9c2708   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd128be4d8    fp = 0x0000fffd128be480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd128be480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd128be520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd128be490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd128be580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd128be530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd128be5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd128be590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd128bf2d8
    x19 = 0x0000fffd128be6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd120b2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff8613f818
     fp = 0x0000ffffaf8319e0    sp = 0x0000fffd128be600
     pc = 0x0000fffd128bf2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd128be648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd128be760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 9
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c213c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x000000000000000a
     x8 = 0x0000000000000062    x9 = 0x0000fffd1795f958
    x10 = 0x000000003674da70   x11 = 0x0000ffff7f1a6a80
    x12 = 0x00000000000000c7   x13 = 0x0000000000000018
    x14 = 0x00000000000000f0   x15 = 0x0000000000006808
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1a4580   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c213c
    x22 = 0x0000ffffaf9c213c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1795e4d8    fp = 0x0000fffd1795e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1795e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1795e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1795e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1795e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1795e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1795e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1795e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1795f2d8
    x19 = 0x0000fffd1795e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd17152000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e7718
     fp = 0x0000ffffaf831820    sp = 0x0000fffd1795e600
     pc = 0x0000fffd1795f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1795e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1795e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 10
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffff82ef403c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd22b80610   x11 = 0x00000000000000c0
    x12 = 0x0000000005322e48   x13 = 0x0000000005331bb8
    x14 = 0x0000000005322f08   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff82feca98   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffff82ef403c
    x22 = 0x0000ffff82ef403c   x23 = 0x000000000000001f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000000000000004
    x28 = 0x0000fffd22b7f4d8    fp = 0x0000fffd22b7f480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd22b7f480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd22b7f520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd22b7f490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd22b7f580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd22b7f530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd22b7f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd22b7f590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd22b802d8
    x19 = 0x0000fffd22b7f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd22373000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9b7118
     fp = 0x0000ffffaf8315c0    sp = 0x0000fffd22b7f600
     pc = 0x0000fffd22b802dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd22b7f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd22b7f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 11
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c210c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1510f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000470   x13 = 0x0000000000010000
    x14 = 0x0000000000000530   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1dec08   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c210c
    x22 = 0x0000ffffaf9c210c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1510e4d8    fp = 0x0000fffd1510e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1510e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1510e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1510e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1510e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1510e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1510e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1510e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1510f2d8
    x19 = 0x0000fffd1510e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd14902000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e8918
     fp = 0x0000ffffaf831910    sp = 0x0000fffd1510e600
     pc = 0x0000fffd1510f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1510e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1510e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 12
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c19e8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000f18
     x8 = 0x0000000000000062    x9 = 0x001b3d2ebc2f680a
    x10 = 0x00ffffffffffffff   x11 = 0x000000026b78510d
    x12 = 0x0000fffd1c1ee5e0   x13 = 0x0000ffffab8d6000
    x14 = 0x0000000000000000   x15 = 0x0000ffffab8dc900
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c19e8
    x22 = 0x0000ffffaf9c19e8   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd1c1ee4d8    fp = 0x0000fffd1c1ee480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1c1ee480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1c1ee520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1c1ee490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1c1ee580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1c1ee530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1c1ee5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1c1ee590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1c1ef2d8
    x19 = 0x0000fffd1c1ee6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1b9e2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e6218
     fp = 0x0000ffffab861060    sp = 0x0000fffd1c1ee600
     pc = 0x0000fffd1c1ef2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1c1ee648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1c1ee760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 13
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c21cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1612f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000910   x13 = 0x0000000000010000
    x14 = 0x00000000000009d0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffab7fca10   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c21cc
    x22 = 0x0000ffffaf9c21cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1612e4d8    fp = 0x0000fffd1612e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1612e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1612e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1612e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1612e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1612e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1612e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1612e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1612f2d8
    x19 = 0x0000fffd1612e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd15922000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e8318
     fp = 0x0000ffffaf8318e0    sp = 0x0000fffd1612e600
     pc = 0x0000fffd1612f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1612e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1612e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 14
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c23ac    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x000000000000000a
     x8 = 0x0000000000000062    x9 = 0x0000fffd148ff958
    x10 = 0x0000000000000da0   x11 = 0x0000ffff7f1cea08
    x12 = 0x00000000000000c7   x13 = 0x0000000000000018
    x14 = 0x00000000000000f0   x15 = 0x000000000000e808
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1cc5f0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c23ac
    x22 = 0x0000ffffaf9c23ac   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd148fe4d8    fp = 0x0000fffd148fe480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd148fe480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd148fe520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd148fe490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd148fe580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd148fe530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd148fe5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd148fe590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd148ff2d8
    x19 = 0x0000fffd148fe6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd140f2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e8c18
     fp = 0x0000ffffaf831970    sp = 0x0000fffd148fe600
     pc = 0x0000fffd148ff2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd148fe648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd148fe760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 15
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c25bc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd140ef610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000e90   x13 = 0x0000000000010000
    x14 = 0x0000000000000f50   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffab7f4a10   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c25bc
    x22 = 0x0000ffffaf9c25bc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd140ee4d8    fp = 0x0000fffd140ee480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd140ee480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd140ee520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd140ee490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd140ee580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd140ee530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd140ee5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd140ee590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd140ef2d8
    x19 = 0x0000fffd140ee6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd138e2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e8f18
     fp = 0x0000ffffaf831790    sp = 0x0000fffd140ee600
     pc = 0x0000fffd140ef2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd140ee648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd140ee760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 16
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c24fc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd138df610   x11 = 0x00000000000000c0
    x12 = 0x00000000003f3a28   x13 = 0x00000000003f7b08
    x14 = 0x00000000003f3ae8   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1b6bc8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c24fc
    x22 = 0x0000ffffaf9c24fc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd138de4d8    fp = 0x0000fffd138de480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd138de480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd138de520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd138de490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd138de580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd138de530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd138de5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd138de590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd138df2d8
    x19 = 0x0000fffd138de6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd130d2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff8613f218
     fp = 0x0000ffffaf831780    sp = 0x0000fffd138de600
     pc = 0x0000fffd138df2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd138de648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd138de760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 17
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c26ac    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd10e9f610   x11 = 0x00000000000000c0
    x12 = 0x0000000000004270   x13 = 0x0000000000010000
    x14 = 0x0000000000004330   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffab804a18   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c26ac
    x22 = 0x0000ffffaf9c26ac   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd10e9e4d8    fp = 0x0000fffd10e9e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd10e9e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd10e9e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd10e9e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd10e9e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd10e9e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd10e9e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd10e9e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd10e9f2d8
    x19 = 0x0000fffd10e9e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd10692000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff86140a18
     fp = 0x0000ffffaf8316c0    sp = 0x0000fffd10e9e600
     pc = 0x0000fffd10e9f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd10e9e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd10e9e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 18
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff45403574c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffcf55ff610   x11 = 0x00000000000000c0
    x12 = 0x0000000011da6ed0   x13 = 0x0000000011db68f0
    x14 = 0x0000000011da6f90   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff8300bbf8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff45403574c
    x22 = 0x0000fff45403574c   x23 = 0x000000000000001f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000000000000004
    x28 = 0x0000fffcf55fe478    fp = 0x0000fffcf55fe420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcf55fe420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcf55fe4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffcf55fe430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffcf55fe520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffcf55fe4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffcf55fe5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffcf55fe530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffcf55ff2d8
    x19 = 0x0000fffcf55fe6f8   x20 = 0x0000fffd22b80600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffcf4df2000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd22b7fec0   x26 = 0x0000fffcf4df2000
    x27 = 0x0000000000000000   x28 = 0x0000fff45381cc40
     fp = 0x0000ffffa4d7df80    sp = 0x0000fffcf55fe600
     pc = 0x0000fffcf55ff2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffcf55fe648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffcf55fe760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 19
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff45403577c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffcd160f610   x11 = 0x0000000000000030
    x12 = 0x0000000000027040   x13 = 0x00000000000300a0
    x14 = 0x0000000000027070   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff83021828   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff45403577c
    x22 = 0x0000fff45403577c   x23 = 0x000000000000001f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000000000000004
    x28 = 0x0000fffcd160e478    fp = 0x0000fffcd160e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcd160e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcd160e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffcd160e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffcd160e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffcd160e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffcd160e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffcd160e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffcd160f2d8
    x19 = 0x0000fffcd160e6f8   x20 = 0x0000fffd22b80600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffcd0e02000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd22b7fec0   x26 = 0x0000fffcd0e02000
    x27 = 0x0000000000000000   x28 = 0x0000fff5d28f1b40
     fp = 0x0000ffffa4d7e200    sp = 0x0000fffcd160e600
     pc = 0x0000fffcd160f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffcd160e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  dmabuf: + 0x803ffc
     sp = 0x0000fffcd160e658    pc = 0x0000fffcd0e02000
    Found by: stack scanning
 7  dmabuf: + 0x803ffc
     sp = 0x0000fffcd160e670    pc = 0x0000fffcd0e02000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fffcd160e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 20
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff4540357a8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fff7fe62d610   x11 = 0x0000000000000030
    x12 = 0x000000000006b970   x13 = 0x0000000000070710
    x14 = 0x000000000006b9a0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff83011628   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff4540357a8
    x22 = 0x0000fff4540357a8   x23 = 0x000000000000003c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000001e   x27 = 0x0000000000000000
    x28 = 0x0000fff7fe62c478    fp = 0x0000fff7fe62c420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fe62c420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fe62c4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fe62c430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fe62c520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fff7fe62c4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fff7fe62c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fe62c530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfff7fe62d2d8
    x19 = 0x0000fff7fe62c6f8   x20 = 0x0000fffd22b80600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7fde20000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd22b7fec0   x26 = 0x0000fff7fde20000
    x27 = 0x0000000000000000   x28 = 0x0000fff802f7cec0
     fp = 0x0000ffffa4d7e300    sp = 0x0000fff7fe62c600
     pc = 0x0000fff7fe62d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fe62c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff7fe62c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 21
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff453c1cf60    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7e3fcc5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7e3fcc4c8
     x8 = 0x0000000000000062    x9 = 0x00061d055a0e4bed
    x10 = 0x00ffffffffffffff   x11 = 0x000000046026250d
    x12 = 0x0000fff7e3fcc5e0   x13 = 0x0000ffffb1f57b50
    x14 = 0x0000000000000008   x15 = 0x0000fff453fd21d0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x000000000000001e   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fff453c1cf60
    x22 = 0x0000fff453c1cf60   x23 = 0x0000000000000002
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000fff453c1cf38
    x28 = 0x0000000000000000    fp = 0x0000fff7e3fcc460
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7e3fcc460
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7e3fcc510    lr = 0x0000ffffafd55140
     sp = 0x0000fff7e3fcc470    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7e3fcc570    lr = 0x0000ffffb191b580
     sp = 0x0000fff7e3fcc520    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xcc
     fp = 0x0000fff7e3fcc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7e3fcc580    pc = 0x0000ffffb191b580
    Found by: previous frame's frame pointer
 4  0xfff7e3fcd2d8
    x19 = 0x0000fff7e3fcc6f8   x20 = 0x0000fffd22b80600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7e37c0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd22b7fec0   x26 = 0x0000fff7e37c0000
    x27 = 0x0000000000000001   x28 = 0x0000000000000011
     fp = 0x0000fff8e67e6bc0    sp = 0x0000fff7e3fcc600
     pc = 0x0000fff7e3fcd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7e3fcc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff7e3fcc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 22
 0  libc.so.6 + 0xe96a8
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000000080000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000ffffecd28380    x7 = 0x0000ffffecd28380
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x0000000000000000   x11 = 0x0000000000000000
    x12 = 0x0000ffffb028e000   x13 = 0x0000000000000020
    x14 = 0x0000000002eb084a   x15 = 0x00000000000052e8
    x16 = 0x0000ffffafdb9680   x17 = 0x0000ffffb02011a0
    x18 = 0x00000000000000cc   x19 = 0x0000000000000000
    x20 = 0x0000000000000001   x21 = 0x0000ffffaf8241d0
    x22 = 0x0000ffffaf8241c0   x23 = 0x0000000080000000
    x24 = 0x0000ffffecd28538   x25 = 0x0000ffffecd28508
    x26 = 0x000000000000000c   x27 = 0x0000aaaabe561910
    x28 = 0x0000000000000000    fp = 0x0000ffffecd283e0
     lr = 0x0000ffffb005c018    sp = 0x0000ffffecd283b0
     pc = 0x0000ffffafdb96a8
    Found by: given as instruction pointer in context
 1  libstdc++.so.6!std::__atomic_futex_unsigned_base::_M_futex_wait_until(unsigned int*, unsigned int, bool, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<1l, 1000000000l> >) + 0x1b4
     fp = 0x0000ffffecd28430    lr = 0x0000ffffb1c1c518
     sp = 0x0000ffffecd283f0    pc = 0x0000ffffb005c018
    Found by: previous frame's frame pointer
 2  0xffffab7d88ac
    x19 = 0x0000ffffecd28470   x20 = 0x0000ffffb1c1cc3c
    x23 = 0x0000ffffecd28540   x24 = 0x0000ffffaf883600
     fp = 0x0000ffffecd28668    sp = 0x0000ffffecd28470
     pc = 0x0000ffffab7d88b0
    Found by: call frame info

Thread 23
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffb03a2680    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x000000000000000f
     x8 = 0x0000000000000062    x9 = 0x0000000000000008
    x10 = 0x0000ffffab76d610   x11 = 0x0000000000000080
    x12 = 0x00000000000cb070   x13 = 0x00000000000d0e40
    x14 = 0x00000000000cb0f0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffab780ff8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffb03a2680
    x22 = 0x0000ffffb03a2680   x23 = 0x0000000000000220
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000110   x27 = 0x0000000000000000
    x28 = 0x0000ffffab76c3a8    fp = 0x0000ffffab76c350
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffab76c350
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffab76c3f0    lr = 0x0000ffffafd54b20
     sp = 0x0000ffffab76c360    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffffab76c450    lr = 0x0000ffffb0350440
     sp = 0x0000ffffab76c400    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libelog.so.1!vbsutil::elog::Log::run() + 0x9c
     fp = 0x0000ffffab76c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffab76c460    pc = 0x0000ffffb0350440
    Found by: previous frame's frame pointer
 4  0xffffab76d2d8
    x19 = 0x0000ffffab76c6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffaaf60000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffaaf60000
    x27 = 0x0000ffffb03a25d8   x28 = 0x0000ffffaf831208
     fp = 0x0000ffffaf831230    sp = 0x0000ffffab76c600
     pc = 0x0000ffffab76d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffab76c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffffab76c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 24
 0  libc.so.6 + 0xebd74
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffffa8ddbfc0
     x2 = 0x0000000000000014    x3 = 0x00000000000003e8
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x0000ffffa8ddc210
     x8 = 0x0000000000000016    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffa8ddc5e0   x13 = 0x0000ffffaf89d000
    x14 = 0x0000000000000008   x15 = 0x0000000000000001
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbbed0
    x18 = 0x0000ffffb0207798   x19 = 0x000000000000000d
    x20 = 0x00000000000003e8   x21 = 0x0000000000000014
    x22 = 0x0000000000000000   x23 = 0x0000ffffa8ddbfc0
    x24 = 0x0000000000000002   x25 = 0x0000ffffa85d0000
    x26 = 0x0000ffffafe80000   x27 = 0x0000ffffb0207040
    x28 = 0x0000ffffb01fd4f0    fp = 0x0000ffffa8ddbe30
     lr = 0x0000ffffafdbbd50    sp = 0x0000ffffa8ddbe30
     pc = 0x0000ffffafdbbd74
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xebd4c
     fp = 0x0000ffffa8ddbe70    lr = 0x0000ffffb069d3d8
     sp = 0x0000ffffa8ddbe40    pc = 0x0000ffffafdbbd50
    Found by: previous frame's frame pointer
 2  libshmmq.so!shmmq::ShmmqManager::WaitSignal(unsigned int const&) + 0xa4
     fp = 0x0000ffffa8ddc2a0    lr = 0x0000ffffb0772634
     sp = 0x0000ffffa8ddbe80    pc = 0x0000ffffb069d3d8
    Found by: previous frame's frame pointer

Thread 25
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffab7770d4    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffffa85cc5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffffa85cc498
     x8 = 0x0000000000000062    x9 = 0x001001feb18880dd
    x10 = 0x00ffffffffffffff   x11 = 0x0000000473f0100d
    x12 = 0x00000000000088f0   x13 = 0x0000000000010000
    x14 = 0x0000000000008af0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffffab797da0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffab7770d4
    x22 = 0x0000ffffab7770d4   x23 = 0x0000000000000095
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000004a   x27 = 0x0000ffffab7770a8
    x28 = 0x0000000000000000    fp = 0x0000ffffa85cc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffa85cc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffa85cc4e0    lr = 0x0000ffffafd55140
     sp = 0x0000ffffa85cc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffffa85cc540    lr = 0x0000ffffb079973c
     sp = 0x0000ffffa85cc4f0    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libdsfdds.so!li::dsfdds::ResourceEvent::event_service() + 0x248
     fp = 0x0000ffffa85cc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffa85cc550    pc = 0x0000ffffb079973c
    Found by: previous frame's frame pointer
 4  0xffffa85cd2d8
    x19 = 0x0000ffffa85cc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffa7dc0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffa7dc0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb1f57b50
     fp = 0x0000ffffaf8e4220    sp = 0x0000ffffa85cc600
     pc = 0x0000ffffa85cd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffa85cc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  dmabuf: + 0x2000ffc
     sp = 0x0000ffffa85cc658    pc = 0x0000ffffa7dc0000
    Found by: stack scanning
 7  dmabuf: + 0x2000ffc
     sp = 0x0000ffffa85cc670    pc = 0x0000ffffa7dc0000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000ffffa85cc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 26
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a4    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd763fc5e0   x13 = 0x0000ffffab7f5fc8
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a4
    x22 = 0x0000ffffa0ff14a4   x23 = 0x000000000000001f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000000000000004
    x28 = 0x0000fffd763fc3b8    fp = 0x0000fffd763fc360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd763fc360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd763fc400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd763fc370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd763fc460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd763fc410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd763fc5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd763fc470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd763fd2d8
    x19 = 0x0000fffd763fc6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd75bf0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd75bf0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b160    sp = 0x0000fffd763fc600
     pc = 0x0000fffd763fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd763fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd763fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 27
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd753dc5e0   x13 = 0x0000ffffab7f5fd8
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000008
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000004   x27 = 0x0000000000000000
    x28 = 0x0000fffd753dc3b8    fp = 0x0000fffd753dc360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd753dc360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd753dc400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd753dc370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd753dc460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd753dc410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd753dc5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd753dc470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd753dd2d8
    x19 = 0x0000fffd753dc6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd74bd0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd74bd0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b180    sp = 0x0000fffd753dc600
     pc = 0x0000fffd753dd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd753dc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd753dc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 28
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd743bc5e0   x13 = 0x0000ffffa102d000
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x000000000000000c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000006   x27 = 0x0000000000000000
    x28 = 0x0000fffd743bc3b8    fp = 0x0000fffd743bc360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd743bc360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd743bc400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd743bc370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd743bc460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd743bc410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd743bc5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd743bc470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd743bd2d8
    x19 = 0x0000fffd743bc6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd73bb0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd73bb0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1a0    sp = 0x0000fffd743bc600
     pc = 0x0000fffd743bd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd743bc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd743bc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 29
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000ffffa0a29000    x1 = 0x000000000000000a
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x000000000000000a    x7 = 0x0000000000000005
     x8 = 0x0000000000000049    x9 = 0x0000000000000050
    x10 = 0x00000000000001d8   x11 = 0x0000000000010000
    x12 = 0x0000000000000228   x13 = 0x0000000000000018
    x14 = 0x0000fffd6fb2d688   x15 = 0x0000000000009348
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000ffffa0a29000
    x20 = 0x000000000000000a   x21 = 0x0000000000000000
    x22 = 0x0000ffffa0a29000   x23 = 0x000000000000000a
    x24 = 0x000000000000000a   x25 = 0x0000ffff8bbc0008
    x26 = 0x000000000000000a   x27 = 0x0000ffff8bbc0008
    x28 = 0x0000fffd6f320000    fp = 0x0000fffd6fb2c470
     lr = 0x0000ffffafdb0b00    sp = 0x0000fffd6fb2c450
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fffd6fb2c4a0    lr = 0x0000ffff876e1bc0
     sp = 0x0000fffd6fb2c480    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libcuda.so.1 + 0x2c1bbc
     fp = 0x0000fffd6fb2c550    lr = 0x0000ffff8778c490
     sp = 0x0000fffd6fb2c4b0    pc = 0x0000ffff876e1bc0
    Found by: previous frame's frame pointer
 3  libcuda.so.1 + 0x36c48c
     fp = 0x0000fffd6fb2c5e0    lr = 0x0000ffff876d53ac
     sp = 0x0000fffd6fb2c560    pc = 0x0000ffff8778c490
    Found by: previous frame's frame pointer
 4  libcuda.so.1 + 0x2b53a8
     fp = 0x0000fffd6fb2c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fffd6fb2c5f0    pc = 0x0000ffff876d53ac
    Found by: previous frame's frame pointer
 5  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fffd6fb2c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 30
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffff7f292230    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffff8741c260
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffff8741c5e0   x13 = 0x0000ffffab80e2b8
    x14 = 0x0000000000000008   x15 = 0x0000000000a21f00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff9f6a4618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffff7f292230
    x22 = 0x0000ffff7f292230   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000ffff8741c248    fp = 0x0000ffff8741c1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff8741c1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff8741c290    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff8741c200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff8741c2f0    lr = 0x0000ffffb0bd40c8
     sp = 0x0000ffff8741c2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerSyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000ffff8741c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff8741c300    pc = 0x0000ffffb0bd40c8
    Found by: previous frame's frame pointer
 4  0xffff8741d2d8
    x19 = 0x0000ffff8741c6f8   x20 = 0x0000ffff9f6a4600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffff86c10000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff86c10000
    x27 = 0x0000000000000000   x28 = 0x0000ffff7f292208
     fp = 0x0000ffff7f2d9460    sp = 0x0000ffff8741c600
     pc = 0x0000ffff8741d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff8741c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff8741c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 31
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1598    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000191
     x8 = 0x0000000000000062    x9 = 0x0000fffd2ccc0958
    x10 = 0x0000000000001340   x11 = 0x0000000000003ffe
    x12 = 0x0000000000007ffe   x13 = 0x0000000000000040
    x14 = 0x00000000d0a0b003   x15 = 0x00000000e0a0b003
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1598
    x22 = 0x0000ffffaf9c1598   x23 = 0x000000000000001c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000000000000000
    x28 = 0x0000fffd2ccbf4d8    fp = 0x0000fffd2ccbf480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2ccbf480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2ccbf520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2ccbf490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2ccbf580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd2ccbf530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd2ccbf5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2ccbf590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd2ccc02d8
    x19 = 0x0000fffd2ccbf6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd2c4b3000   x24 = 0x0000ffffafe80000
    x25 = 0x5f72656b726f7720   x26 = 0x0000ffffab838418
     fp = 0x0000ffffaf831570    sp = 0x0000fffd2ccbf600
     pc = 0x0000fffd2ccc02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2ccbf648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2ccbf760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 32
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffb1947668    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff7fcbc5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffff7fcbc4b8
     x8 = 0x0000000000000062    x9 = 0x0017fea632e74ddf
    x10 = 0x00ffffffffffffff   x11 = 0x00000004484ea10d
    x12 = 0x0000000000010000   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e7   x15 = 0x0000fff9b36a0fc0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x00000000000001f9   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffb1947668
    x22 = 0x0000ffffb1947668   x23 = 0x0000000000000008
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000004   x27 = 0x0000ffffb1947640
    x28 = 0x0000000000000000    fp = 0x0000ffff7fcbc450
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff7fcbc450
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff7fcbc500    lr = 0x0000ffffafd55140
     sp = 0x0000ffff7fcbc460    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffff7fcbc560    lr = 0x0000ffffb193e2b4
     sp = 0x0000ffff7fcbc510    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libtimer.so.3!lios::timer::TimerMonitorManager::PeriodicMonitor(long) + 0xf0
     fp = 0x0000ffff7fcbc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff7fcbc570    pc = 0x0000ffffb193e2b4
    Found by: previous frame's frame pointer
 4  0xffff7fcbd2d8
    x19 = 0x0000ffff7fcbc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffff7f4b0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffff7f4b0000
    x27 = 0x0000ffffafd555e0   x28 = 0x0000000077359400
     fp = 0x0000ffffab822dc0    sp = 0x0000ffff7fcbc600
     pc = 0x0000ffff7fcbd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff7fcbc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff7fcbc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 33
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06a28    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000029b6
     x8 = 0x0000000000000062    x9 = 0x0000fffd2ac80958
    x10 = 0x0000000000000d00   x11 = 0x000000007ffffd00
    x12 = 0x004000007ffffd00   x13 = 0x0000000000000100
    x14 = 0x00000001b030b004   x15 = 0x00000001c030b004
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000000000000022   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06a28
    x22 = 0x0000ffffa2e06a28   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000fffd2ac7f478    fp = 0x0000fffd2ac7f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2ac7f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2ac7f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2ac7f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2ac7f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2ac7f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2ac7f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2ac7f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd2ac802d8
    x19 = 0x0000fffd2ac7f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd2a473000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd2a473000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa3215ac0
     fp = 0x0000ffffaf9fc360    sp = 0x0000fffd2ac7f600
     pc = 0x0000fffd2ac802dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2ac7f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2ac7f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 34
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06a58    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd2a470610   x11 = 0x0000000000000030
    x12 = 0x00000000000418e8   x13 = 0x0000000000051678
    x14 = 0x0000000000041918   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1ac550   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06a58
    x22 = 0x0000ffffa2e06a58   x23 = 0x0000000000000020
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000010   x27 = 0x0000000000000000
    x28 = 0x0000fffd2a46f478    fp = 0x0000fffd2a46f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2a46f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2a46f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2a46f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2a46f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2a46f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2a46f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2a46f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd2a4702d8
    x19 = 0x0000fffd2a46f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd29c63000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd29c63000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa3336c00
     fp = 0x0000ffff7f2e9660    sp = 0x0000fffd2a46f600
     pc = 0x0000fffd2a4702dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2a46f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2a46f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 35
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06ae8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd28430610   x11 = 0x0000000000000030
    x12 = 0x0000000000305478   x13 = 0x0000000000312d80
    x14 = 0x00000000003054a8   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f212748   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06ae8
    x22 = 0x0000ffffa2e06ae8   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000fffd2842f478    fp = 0x0000fffd2842f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2842f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2842f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2842f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2842f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2842f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2842f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2842f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd284302d8
    x19 = 0x0000fffd2842f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd27c23000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd27c23000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa3336d80
     fp = 0x0000ffffab821480    sp = 0x0000fffd2842f600
     pc = 0x0000fffd284302dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2842f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2842f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 36
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06b18    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd27c1f430
     x8 = 0x0000000000000062    x9 = 0x0000fffd27c1f4f0
    x10 = 0x0000000000000035   x11 = 0x00000000ffffffc8
    x12 = 0x0000fffd27c1f4e0   x13 = 0x0000fffd27c1f5a0
    x14 = 0x0000000000000000   x15 = 0x0000ffffab8d8300
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06b18
    x22 = 0x0000ffffa2e06b18   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd27c1f478    fp = 0x0000fffd27c1f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd27c1f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd27c1f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd27c1f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd27c1f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd27c1f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd27c1f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd27c1f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd27c202d8
    x19 = 0x0000fffd27c1f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd27413000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd27413000
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffffab8578a0    sp = 0x0000fffd27c1f600
     pc = 0x0000fffd27c202dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd27c1f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd27c1f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 37
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2ea90cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000002
    x10 = 0x0000fffd25be0610   x11 = 0x0000000000000020
    x12 = 0x000000000000d438   x13 = 0x0000000000010000
    x14 = 0x000000000000d458   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff86100fb0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2ea90cc
    x22 = 0x0000ffffa2ea90cc   x23 = 0x0000000000000013
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000009   x27 = 0x0000000000000004
    x28 = 0x0000fffd25bdf488    fp = 0x0000fffd25bdf430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd25bdf430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd25bdf4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd25bdf440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd25bdf530    lr = 0x0000ffffb1923208
     sp = 0x0000fffd25bdf4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffd25bdf5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd25bdf540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffd25be02d8
    x19 = 0x0000fffd25bdf6f8   x20 = 0x0000ffffa3fed600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd253d3000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa3fecec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa2ea9070    fp = 0x0000ffffa2e18200
     sp = 0x0000fffd25bdf600    pc = 0x0000fffd25be02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd25bdf648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd25bdf760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 38
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06bd8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd24bc0610   x11 = 0x0000000000000030
    x12 = 0x000000000003f990   x13 = 0x00000000000402c0
    x14 = 0x000000000003f9c0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f16c4c0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06bd8
    x22 = 0x0000ffffa2e06bd8   x23 = 0x0000000000000114
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000008a   x27 = 0x0000000000000000
    x28 = 0x0000fffd24bbf478    fp = 0x0000fffd24bbf420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd24bbf420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd24bbf4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd24bbf430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd24bbf520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd24bbf4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd24bbf5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd24bbf530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd24bc02d8
    x19 = 0x0000fffd24bbf6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd243b3000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd243b3000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa066b480
     fp = 0x0000ffffab884300    sp = 0x0000fffd24bbf600
     pc = 0x0000fffd24bc02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd24bbf648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd24bbf760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 39
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06c08    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd243b0610   x11 = 0x0000000000000030
    x12 = 0x0000000000025290   x13 = 0x00000000000343f0
    x14 = 0x00000000000252c0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f18c850   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06c08
    x22 = 0x0000ffffa2e06c08   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000fffd243af478    fp = 0x0000fffd243af420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd243af420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd243af4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd243af430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd243af520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd243af4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd243af5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd243af530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd243b02d8
    x19 = 0x0000fffd243af6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd23ba3000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd23ba3000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa101e440
     fp = 0x0000ffffab8843a0    sp = 0x0000fffd243af600
     pc = 0x0000fffd243b02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd243af648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd243af760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 40
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06c6c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd23390610   x11 = 0x0000000000000030
    x12 = 0x000000000030ab70   x13 = 0x000000000031a920
    x14 = 0x000000000030aba0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1536e0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06c6c
    x22 = 0x0000ffffa2e06c6c   x23 = 0x000000000000008b
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000045   x27 = 0x0000000000000004
    x28 = 0x0000fffd2338f478    fp = 0x0000fffd2338f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2338f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2338f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2338f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2338f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2338f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2338f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2338f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd233902d8
    x19 = 0x0000fffd2338f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd22b83000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd22b83000
    x27 = 0x0000000000000000   x28 = 0x0000fff5d28f13c0
     fp = 0x0000ffffaf9fc800    sp = 0x0000fffd2338f600
     pc = 0x0000fffd233902dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2338f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2338f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 41
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06c9c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd22370610   x11 = 0x0000000000000030
    x12 = 0x0000000000050e20   x13 = 0x0000000000059b00
    x14 = 0x0000000000050e50   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f14b820   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06c9c
    x22 = 0x0000ffffa2e06c9c   x23 = 0x000000000000001f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000f   x27 = 0x0000000000000004
    x28 = 0x0000fffd2236f478    fp = 0x0000fffd2236f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2236f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2236f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2236f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2236f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2236f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2236f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2236f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd223702d8
    x19 = 0x0000fffd2236f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd21b63000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd21b63000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa3213180
     fp = 0x0000ffffaf9fcc40    sp = 0x0000fffd2236f600
     pc = 0x0000fffd223702dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2236f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2236f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 42
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06cc8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd21b60610   x11 = 0x0000000000000030
    x12 = 0x000000000006e7b0   x13 = 0x00000000000711e0
    x14 = 0x000000000006e7e0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff861c64c0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06cc8
    x22 = 0x0000ffffa2e06cc8   x23 = 0x0000000000000114
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000008a   x27 = 0x0000000000000000
    x28 = 0x0000fffd21b5f478    fp = 0x0000fffd21b5f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd21b5f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd21b5f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd21b5f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd21b5f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd21b5f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd21b5f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd21b5f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd21b602d8
    x19 = 0x0000fffd21b5f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd21353000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd21353000
    x27 = 0x0000000000000000   x28 = 0x0000fffa8391a840
     fp = 0x0000ffffaf9fce80    sp = 0x0000fffd21b5f600
     pc = 0x0000fffd21b602dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd21b5f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  val_lidar_stream_0_1 + 0xc0ffc
     sp = 0x0000fffd21b5f658    pc = 0x0000fffd21353000
    Found by: stack scanning
 7  val_lidar_stream_0_1 + 0xc0ffc
     sp = 0x0000fffd21b5f670    pc = 0x0000fffd21353000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fffd21b5f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 43
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06cfc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd2128f610   x11 = 0x0000000000000030
    x12 = 0x00000000003045f8   x13 = 0x000000000030f370
    x14 = 0x0000000000304628   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff830944c8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06cfc
    x22 = 0x0000ffffa2e06cfc   x23 = 0x000000000000003b
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000001d   x27 = 0x0000000000000004
    x28 = 0x0000fffd2128e478    fp = 0x0000fffd2128e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2128e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2128e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2128e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2128e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2128e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2128e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2128e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd2128f2d8
    x19 = 0x0000fffd2128e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd20a82000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd20a82000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa3336a80
     fp = 0x0000ffffab8210c0    sp = 0x0000fffd2128e600
     pc = 0x0000fffd2128f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2128e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2128e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 44
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2ea95cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000018e6
     x8 = 0x0000000000000062    x9 = 0x001b3c80f4f00ade
    x10 = 0x00ffffffffffffff   x11 = 0x00000003957e430d
    x12 = 0x0000fffd20a7e5e0   x13 = 0x0000ffffab7ee1f0
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa3fed618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2ea95cc
    x22 = 0x0000ffffa2ea95cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd20a7e488    fp = 0x0000fffd20a7e430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd20a7e430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd20a7e4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd20a7e440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd20a7e530    lr = 0x0000ffffb1923208
     sp = 0x0000fffd20a7e4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffd20a7e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd20a7e540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffd20a7f2d8
    x19 = 0x0000fffd20a7e6f8   x20 = 0x0000ffffa3fed600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd20272000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa3fecec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa2ea9570    fp = 0x0000ffffa2e18600
     sp = 0x0000fffd20a7e600    pc = 0x0000fffd20a7f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd20a7e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fffd20a7e698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fffd20a7e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 45
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06d28    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd2026e430
     x8 = 0x0000000000000062    x9 = 0x0000fffd2026e4f0
    x10 = 0x0000000000000036   x11 = 0x00000000ffffffc8
    x12 = 0x0000fffd2026e4e0   x13 = 0x0000fffd2026e5a0
    x14 = 0x0000000000000001   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06d28
    x22 = 0x0000ffffa2e06d28   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd2026e478    fp = 0x0000fffd2026e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2026e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2026e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2026e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2026e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2026e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2026e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2026e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd2026f2d8
    x19 = 0x0000fffd2026e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1fa62000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1fa62000
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffffab8215a0    sp = 0x0000fffd2026e600
     pc = 0x0000fffd2026f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2026e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2026e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 46
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06d88    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd1ea3e430
     x8 = 0x0000000000000062    x9 = 0x0000fffd1ea3e4f0
    x10 = 0x0000000000000038   x11 = 0x00000000ffffffc8
    x12 = 0x0000fffd1ea3e4e0   x13 = 0x0000fffd1ea3e5a0
    x14 = 0x0000000000000001   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06d88
    x22 = 0x0000ffffa2e06d88   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd1ea3e478    fp = 0x0000fffd1ea3e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1ea3e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1ea3e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1ea3e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1ea3e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1ea3e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1ea3e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1ea3e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1ea3f2d8
    x19 = 0x0000fffd1ea3e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1e232000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1e232000
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffffab821820    sp = 0x0000fffd1ea3e600
     pc = 0x0000fffd1ea3f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1ea3e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1ea3e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 47
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf82f06c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x7f7f7f7f7f7f7f7f
     x8 = 0x0000000000000062    x9 = 0x000000000000000e
    x10 = 0x0000ffffacffd610   x11 = 0x0000000000000180
    x12 = 0x000000000004d6c0   x13 = 0x0000000000050540
    x14 = 0x0000000000000000   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffaf88d758   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf82f06c
    x22 = 0x0000ffffaf82f06c   x23 = 0x0000000000001d9f
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000ecf   x27 = 0x0000000000000004
    x28 = 0x0000ffffacffc358    fp = 0x0000ffffacffc300
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffacffc300
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffacffc3a0    lr = 0x0000ffffafd54b20
     sp = 0x0000ffffacffc310    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffffacffc400    lr = 0x0000ffffb024fe24
     sp = 0x0000ffffacffc3b0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liblog.so.3!lios::log::details::AsyncThreadPool::FetchLogMsg() + 0xa0
     fp = 0x0000ffffacffc5d0    lr = 0x0000ffffb02500e8
     sp = 0x0000ffffacffc410    pc = 0x0000ffffb024fe24
    Found by: previous frame's frame pointer
 4  0xffffacffd2d8
    x19 = 0x0000ffffacffc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffaf82f010   x24 = 0x0000000000000001
     fp = 0x0000ffffaf82f010    sp = 0x0000ffffacffc5e0
     pc = 0x0000ffffacffd2dc
    Found by: call frame info
 5  libc.so.6 + 0x856c4
     sp = 0x0000ffffacffc5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 6  libc.so.6 + 0x855dc
     sp = 0x0000ffffacffc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 7  dmabuf: + 0xeffc
     sp = 0x0000ffffacffc658    pc = 0x0000ffffac7f0000
    Found by: stack scanning
 8  dmabuf: + 0xeffc
     sp = 0x0000ffffacffc670    pc = 0x0000ffffac7f0000
    Found by: stack scanning
 9  libc.so.6 + 0xeba48
     sp = 0x0000ffffacffc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 48
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c1b6c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd1a9bf610   x11 = 0x00000000000000c0
    x12 = 0x0000000000000af0   x13 = 0x0000000000010000
    x14 = 0x0000000000000bb0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f224c08   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c1b6c
    x22 = 0x0000ffffaf9c1b6c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1a9be4d8    fp = 0x0000fffd1a9be480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1a9be480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1a9be520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1a9be490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1a9be580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1a9be530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1a9be5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1a9be590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1a9bf2d8
    x19 = 0x0000fffd1a9be6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1a1b2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9e6818
     fp = 0x0000ffffaf831730    sp = 0x0000fffd1a9be600
     pc = 0x0000fffd1a9bf2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1a9be648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1a9be760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 49
 0  libc.so.6 + 0xbbed0
     x0 = 0x0000000000000000    x1 = 0x0000000000000000
     x2 = 0x0000ffffac1fc5d8    x3 = 0x0000ffffac1fc5d8
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000033303033    x7 = 0x0000000000000004
     x8 = 0x0000000000000073    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffac1fc5e0   x13 = 0x0000ffffaf804de0
    x14 = 0x0000000000000008   x15 = 0x0000000000004248
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd961e0
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000000
    x20 = 0x0000ffffac1fc5d8   x21 = 0x0000000000000000
    x22 = 0x0000ffffac1fc5d8   x23 = 0x0000ffffafe87000
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000ffffafe80000   x27 = 0x0000ffffb0207040
    x28 = 0x0000ffffab9f0000    fp = 0x0000ffffac1fc530
     lr = 0x0000ffffafd8beb8    sp = 0x0000ffffac1fc500
     pc = 0x0000ffffafd8bed0
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xbbeb4
     fp = 0x0000ffffac1fc580    lr = 0x0000ffffafd961fc
     sp = 0x0000ffffac1fc540    pc = 0x0000ffffafd8beb8
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0xc61f8
     fp = 0x0000ffffac1fc590    lr = 0x0000ffffb1c1c0e0
     sp = 0x0000ffffac1fc590    pc = 0x0000ffffafd961fc
    Found by: previous frame's frame pointer
 3  libapp.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::app::AppContainer::Run()::{lambda()#1}> > >::_M_run() + 0xac
     fp = 0x0000ffffac1fc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffac1fc5a0    pc = 0x0000ffffb1c1c0e0
    Found by: previous frame's frame pointer
 4  0xffffac1fd2d8
    x19 = 0x0000ffffac1fc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffffaf8d1640    fp = 0x0000ffffaf898440
     sp = 0x0000ffffac1fc600    pc = 0x0000ffffac1fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffac1fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffffac1fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 50
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffafc16030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000ffffa9dfbb40
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002036
     x8 = 0x0000000000000062    x9 = 0x00133fbc758880dd
    x10 = 0x00ffffffffffffff   x11 = 0x0000000473f0100d
    x12 = 0x00000000000c5ab0   x13 = 0x00000000000d1aa0
    x14 = 0x00000000000c5ac0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff861adaf0   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffafc16030
    x22 = 0x0000ffffa9dfba38   x23 = 0x0000ffffb01fd4f0
    x24 = 0x0000000000000002   x25 = 0x0000ffffa95f0000
    x26 = 0x0000ffffafe80000   x27 = 0x0000ffffb0207040
    x28 = 0x0000ffffa95f0000    fp = 0x0000ffffa9dfb9f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffa9dfb9f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffa9dfba60    lr = 0x0000ffffafd5e028
     sp = 0x0000ffffa9dfba00    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000ffffa9dfba90    lr = 0x0000ffffb068befc
     sp = 0x0000ffffa9dfba70    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000ffffa9dfbd40    lr = 0x0000ffffb068c7c4
     sp = 0x0000ffffa9dfbaa0    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaf856c5c
    x19 = 0x00000000000003e8   x20 = 0x0000000000000330
    x21 = 0x0000ffffa95f0000   x22 = 0x0000ffffa95f0000
     fp = 0x0000ffffa9dfc2a0    sp = 0x0000ffffa9dfbd50
     pc = 0x0000ffffaf856c60
    Found by: call frame info

Thread 51
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b0b80    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffffa95ec280
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffffa95ec0d8
     x8 = 0x0000000000000062    x9 = 0x001001feb18880dd
    x10 = 0x00ffffffffffffff   x11 = 0x0000000473f0100d
    x12 = 0x0000000000000287   x13 = 0x0000000000000018
    x14 = 0x0000000000000198   x15 = 0x000000000000fde8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000000000000018   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b0b80
    x22 = 0x0000ffffaf8b0b80   x23 = 0x0000000000000092
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000049   x27 = 0x0000ffffaf8b0b58
    x28 = 0x0000000000000000    fp = 0x0000ffffa95ec070
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffa95ec070
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffa95ec120    lr = 0x0000ffffafd55140
     sp = 0x0000ffffa95ec080    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffffa95ec180    lr = 0x0000ffffb070d57c
     sp = 0x0000ffffa95ec130    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libdsfdds.so!li::dsfdds::MqDiscoveryEndpoint::SendFunc() + 0x228
     fp = 0x0000ffffa95ec5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffa95ec190    pc = 0x0000ffffb070d57c
    Found by: previous frame's frame pointer
 4  0xffffa95ed2d8
    x19 = 0x0000ffffa95ec6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffa8de0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffa8de0000
    x27 = 0x0000ffffaf8b0b28   x28 = 0x0000ffffa95ec2e0
     fp = 0x0000ffffaf831270    sp = 0x0000ffffa95ec600
     pc = 0x0000ffffa95ed2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffa95ec648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffffa95ec760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 52
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06e78    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000007e6c
     x8 = 0x0000000000000062    x9 = 0x0000fffd1693f958
    x10 = 0x0000000000000d00   x11 = 0x0000000000000000
    x12 = 0x0000ffffa4b04228   x13 = 0x0000000000000000
    x14 = 0x0000000000303006   x15 = 0x0000000010303006
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06e78
    x22 = 0x0000ffffa2e06e78   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000fffd1693e478    fp = 0x0000fffd1693e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1693e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1693e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1693e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1693e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1693e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1693e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1693e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1693f2d8
    x19 = 0x0000fffd1693e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd16132000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd16132000
    x27 = 0x0000000000000000   x28 = 0x0000fffcf62df2c0
     fp = 0x0000ffffab85a2e0    sp = 0x0000fffd1693e600
     pc = 0x0000fffd1693f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1693e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1693e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 53
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf82d068    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffffa5dbb588
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffffa5dbb478
     x8 = 0x0000000000000062    x9 = 0x0011ad370ae2b229
    x10 = 0x00ffffffffffffff   x11 = 0x000000043babc40d
    x12 = 0x0000000000007740   x13 = 0x0000000000010000
    x14 = 0x0000000000007760   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffffab7a4058   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf82d068
    x22 = 0x0000ffffaf82d068   x23 = 0x0000000000000014
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000a   x27 = 0x0000ffffaf82d040
    x28 = 0x0000000000000000    fp = 0x0000ffffa5dbb410
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffa5dbb410
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffa5dbb4c0    lr = 0x0000ffffafd55140
     sp = 0x0000ffffa5dbb420    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffffa5dbb520    lr = 0x0000ffffb19108ec
     sp = 0x0000ffffa5dbb4d0    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::concurrent::RepeatedAndTriggerableTaskRunner::RepeatedAndTriggerableTaskRunner(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::variant<std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000l> >, std::chrono::duration<long, std::ratio<1l, 1000l> >, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<60l, 1l> >, std::chrono::duration<long, std::ratio<3600l, 1l> > > const&, std::function<void ()>&&)::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::concurrent::RepeatedAndTriggerableTaskRunner::RepeatedAndTriggerableTaskRunner(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::variant<std::chrono::duration<long, std::ratio<1l, 1000000000l> >, std::chrono::duration<long, std::ratio<1l, 1000000l> >, std::chrono::duration<long, std::ratio<1l, 1000l> >, std::chrono::duration<long, std::ratio<1l, 1l> >, std::chrono::duration<long, std::ratio<60l, 1l> >, std::chrono::duration<long, std::ratio<3600l, 1l> > > const&, std::function<void ()>&&)::{lambda()#1}&&)::{lambda()#1}> > >::_M_run() + 0x128
     fp = 0x0000ffffa5dbb5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffa5dbb530    pc = 0x0000ffffb19108ec
    Found by: previous frame's frame pointer
 4  0xffffa5dbc2d8
    x19 = 0x0000ffffa5dbb6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffa55af000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffafe81350   x26 = 0x0000ffffaf82d010
     fp = 0x0000ffffaf82d100    sp = 0x0000ffffa5dbb600
     pc = 0x0000ffffa5dbc2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffa5dbb648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffffa5dbb760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 54
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c27c8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd1068e2a0
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00ffffffffffffff   x11 = 0x000000026bb55a0d
    x12 = 0x0000fffd1068e5e0   x13 = 0x0000ffff8612b000
    x14 = 0x0000000000000008   x15 = 0x0000ffff8612ef00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c27c8
    x22 = 0x0000ffffaf9c27c8   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd1068e4d8    fp = 0x0000fffd1068e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1068e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1068e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1068e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1068e580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1068e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1068e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1068e590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1068f2d8
    x19 = 0x0000fffd1068e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd0fe82000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff86141018
     fp = 0x0000ffffaf831a60    sp = 0x0000fffd1068e600
     pc = 0x0000fffd1068f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1068e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  dmabuf: + 0x13ffffc
     sp = 0x0000fffd1068e658    pc = 0x0000fffd0fe82000
    Found by: stack scanning
 7  dmabuf: + 0x13ffffc
     sp = 0x0000fffd1068e670    pc = 0x0000fffd0fe82000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fffd1068e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 55
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffb1aa8360    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000003ab
     x8 = 0x0000000000000062    x9 = 0x0000fffce6dcf958
    x10 = 0x0000000000001ce0   x11 = 0xffc0000000208040
    x12 = 0xffc0000000308040   x13 = 0x0000000000000080
    x14 = 0x00000004d020b003   x15 = 0x00000004e020b003
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffb1aa8360
    x22 = 0x0000ffffb1aa8360   x23 = 0x000000000000006c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000036   x27 = 0x0000000000000000
    x28 = 0x0000fffce6dce488    fp = 0x0000fffce6dce430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffce6dce430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffce6dce4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffce6dce440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffce6dce530    lr = 0x0000ffffb1923208
     sp = 0x0000fffce6dce4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffce6dce5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffce6dce540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffce6dcf2d8
    x19 = 0x0000fffce6dce6f8   x20 = 0x0000ffffa9dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x000000000000006e
    x23 = 0x0000fffce65c2000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa9dfcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffb1aa8308    fp = 0x0000ffffa9e22180
     sp = 0x0000fffce6dce600    pc = 0x0000fffce6dcf2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffce6dce648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffce6dce760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 56
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa5493030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffd0ea7e120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001efa
     x8 = 0x0000000000000062    x9 = 0x0000887449287bcb
    x10 = 0x00ffffffffffffff   x11 = 0x000000044e818b0d
    x12 = 0x00000000000031c0   x13 = 0x0000000000010000
    x14 = 0x00000000000032a0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff86210f40   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa5493030
    x22 = 0x0000fffd0ea7e018   x23 = 0x0000fffd0ea7e5b8
    x24 = 0x0000fffd0ea7e5a8   x25 = 0x0000fffd0ea7e598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffceedeeec0
    x28 = 0x0000fffd0e272000    fp = 0x0000fffd0ea7dfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd0ea7dfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd0ea7e040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffd0ea7dfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffd0ea7e070    lr = 0x0000ffffb068befc
     sp = 0x0000fffd0ea7e050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffd0ea7e320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffd0ea7e080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffa5061924
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffa509dfc0
    x21 = 0x0000ffffa5061780   x22 = 0x0000ffffa5061928
     fp = 0x0000ffffa5061780    sp = 0x0000fffd0ea7e330
     pc = 0x0000ffffa5061928
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fffd0ea7e360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fffd0ea7e420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 57
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1041580    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd76c0c1e8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd76c0c0c8
     x8 = 0x0000000000000062    x9 = 0x001245cd9b07f180
    x10 = 0x00ffffffffffffff   x11 = 0x000000043cdcf10d
    x12 = 0x0000fffd76c0c5e0   x13 = 0x0000ffffab7f5fc0
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1041580
    x22 = 0x0000ffffa1041580   x23 = 0x0000000000000002
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000001   x27 = 0x0000ffffa1041558
    x28 = 0x0000000000000000    fp = 0x0000fffd76c0c060
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd76c0c060
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd76c0c110    lr = 0x0000ffffafd55140
     sp = 0x0000fffd76c0c070    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fffd76c0c170    lr = 0x0000ffffb0d2e76c
     sp = 0x0000fffd76c0c120    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  liberpc.so.1!vbs::rpc::transport::TCPProxyTransport::heartbeat() + 0x488
     fp = 0x0000fffd76c0c3e0    lr = 0x0000ffffb0d2a9f0
     sp = 0x0000fffd76c0c180    pc = 0x0000ffffb0d2e76c
    Found by: previous frame's frame pointer
 4  0xffffa0ff13fc
    x19 = 0x0000ffffb0d80640   x20 = 0x0000fffd76c0c438
    x21 = 0x0000000000000001   x22 = 0x0000fffd76c0c5d0
    x23 = 0x0000ffffa0ff14d8   x24 = 0x0000000000000000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000ffffa1021780
    x27 = 0x0000000000000002   x28 = 0x0000000000000008
     fp = 0x0000ffffb1f57b50    sp = 0x0000fffd76c0c3f0
     pc = 0x0000ffffa0ff1400
    Found by: call frame info
 5  liberpc.so.1!boost::wrapexcept<std::logic_error>::rethrow() const + 0xf24
     sp = 0x0000fffd76c0c450    pc = 0x0000ffffb0d00760
    Found by: stack scanning
 6  liberpc.so.1!boost::wrapexcept<std::logic_error>::rethrow() const + 0xec4
     sp = 0x0000fffd76c0c458    pc = 0x0000ffffb0d00700
    Found by: stack scanning
 7  liberpc.so.1!boost_asio_detail_posix_thread_function + 0x18
     sp = 0x0000fffd76c0c470    pc = 0x0000ffffb0d244dc
    Found by: stack scanning
 8  0xfffd76c0d2d8
    x19 = 0x0000fffd76c0c6f8   x20 = 0x0000ffffa2dfd600
     fp = 0x0000ffffa103b150    sp = 0x0000fffd76c0c490
     pc = 0x0000fffd76c0d2dc
    Found by: call frame info
 9  libc.so.6 + 0x855dc
     sp = 0x0000fffd76c0c498    pc = 0x0000ffffafd555e0
    Found by: stack scanning
10  libc.so.6 + 0x556e0
     sp = 0x0000fffd76c0c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
11  libc.so.6 + 0x856b4
     sp = 0x0000fffd76c0c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
12  libc.so.6 + 0x85958
     sp = 0x0000fffd76c0c600    pc = 0x0000ffffafd5595c
    Found by: stack scanning
13  libc.so.6 + 0x855dc
     sp = 0x0000fffd76c0c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
14  libc.so.6 + 0xeba48
     sp = 0x0000fffd76c0c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 58
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000008c    x1 = 0x0000fffcd27ed6c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffc102f1160    x5 = 0x0000fffc102f1110
     x6 = 0x0000000000000000    x7 = 0x0000fffc102f1260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffc102f15e0   x13 = 0x0000ffffab7ee200
    x14 = 0x0000000000000008   x15 = 0x00000000001552f0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x000000000000008c
    x20 = 0x0000000000000000   x21 = 0x0000fffc102f1110
    x22 = 0x0000fffc102f1160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd27ed6c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000008c
    x28 = 0x0000fffc0fae5000    fp = 0x0000fffc102f1040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffc102f1040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffc102f1080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffc102f1050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffc102f12f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffc102f1090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffc102f13dc
    x19 = 0x0000ffffa2e2a490   x20 = 0x0000ffffa2e2a47c
    x21 = 0x0000fffc102f15c8   x22 = 0x0000000000000000
    x23 = 0x0000fffc0fae5000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffc0fae5000
    x27 = 0x0000fffc102f1110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a460    sp = 0x0000fffc102f1300
     pc = 0x0000fffc102f13e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffc102f1328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffc102f13c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffc102f1410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffc102f14f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffc102f1560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffc102f15c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4cc1fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffc102f15e0
     pc = 0x00004cc200000001
    Found by: call frame info
11  model.tar + 0x18dd3ffc
     sp = 0x0000fffc102f15e8    pc = 0x0000fffc00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffc102f1648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  model.tar + 0x288b8ffc
     sp = 0x0000fffc102f1658    pc = 0x0000fffc0fae5000
    Found by: stack scanning
14  model.tar + 0x288b8ffc
     sp = 0x0000fffc102f1670    pc = 0x0000fffc0fae5000
    Found by: stack scanning
15  libc.so.6 + 0xeba48
     sp = 0x0000fffc102f1760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 59
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd73bac5e0   x13 = 0x0000ffffab7f5f70
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x000000000000000e
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000007   x27 = 0x0000000000000000
    x28 = 0x0000fffd73bac3b8    fp = 0x0000fffd73bac360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd73bac360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd73bac400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd73bac370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd73bac460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd73bac410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd73bac5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd73bac470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd73bad2d8
    x19 = 0x0000fffd73bac6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd733a0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd733a0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1b0    sp = 0x0000fffd73bac600
     pc = 0x0000fffd73bad2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd73bac648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd73bac760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 60
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd72b8c5e0   x13 = 0x0000ffffab7f5f80
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000012
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000009   x27 = 0x0000000000000000
    x28 = 0x0000fffd72b8c3b8    fp = 0x0000fffd72b8c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd72b8c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd72b8c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd72b8c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd72b8c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd72b8c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd72b8c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd72b8c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd72b8d2d8
    x19 = 0x0000fffd72b8c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd72380000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd72380000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1d0    sp = 0x0000fffd72b8c600
     pc = 0x0000fffd72b8d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd72b8c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd72b8c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 61
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000091    x1 = 0x0000fffcd27feec0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffba37b8160    x5 = 0x0000fffba37b8110
     x6 = 0x0000000000000000    x7 = 0x0000fffba37b8260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffba37b85e0   x13 = 0x0000ffffab7ee208
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x0000000000000091
    x20 = 0x0000000000000000   x21 = 0x0000fffba37b8110
    x22 = 0x0000fffba37b8160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd27feec0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000091
    x28 = 0x0000fffba2fac000    fp = 0x0000fffba37b8040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffba37b8040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffba37b8080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffba37b8050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffba37b82f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffba37b8090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffba37b83dc
    x19 = 0x0000ffffa2e2a570   x20 = 0x0000ffffa2e2a55c
    x21 = 0x0000fffba37b85c8   x22 = 0x0000000000000000
    x23 = 0x0000fffba2fac000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffba2fac000
    x27 = 0x0000fffba37b8110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a540    sp = 0x0000fffba37b8300
     pc = 0x0000fffba37b83e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffba37b8328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffba37b83c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffba37b8410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffba37b84f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffba37b8560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffba37b85c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4cc1fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffba37b85e0
     pc = 0x00004cc200000001
    Found by: call frame info
11  model.tar + 0xc1ecffc
     sp = 0x0000fffba37b85e8    pc = 0x0000fffb00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffba37b8648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  model.tar + 0x79a2affc
     sp = 0x0000fffba37b8658    pc = 0x0000fffba2fac000
    Found by: stack scanning
14  model.tar + 0x79a2affc
     sp = 0x0000fffba37b8670    pc = 0x0000fffba2fac000
    Found by: stack scanning
15  libc.so.6 + 0xeba48
     sp = 0x0000fffba37b8760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 62
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd71b6c5e0   x13 = 0x0000ffffab7f5f90
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000016
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000b   x27 = 0x0000000000000000
    x28 = 0x0000fffd71b6c3b8    fp = 0x0000fffd71b6c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd71b6c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd71b6c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd71b6c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd71b6c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd71b6c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd71b6c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd71b6c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd71b6d2d8
    x19 = 0x0000fffd71b6c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd71360000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd71360000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1f0    sp = 0x0000fffd71b6c600
     pc = 0x0000fffd71b6d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd71b6c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd71b6c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 63
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd70b4c5e0   x13 = 0x0000ffffab7f5fa0
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x000000000000001a
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000d   x27 = 0x0000000000000000
    x28 = 0x0000fffd70b4c3b8    fp = 0x0000fffd70b4c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd70b4c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd70b4c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd70b4c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd70b4c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd70b4c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd70b4c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd70b4c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd70b4d2d8
    x19 = 0x0000fffd70b4c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd70340000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd70340000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b210    sp = 0x0000fffd70b4c600
     pc = 0x0000fffd70b4d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd70b4c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd70b4c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 64
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000008f    x1 = 0x0000fffcd2815ec0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffaf3e0f160    x5 = 0x0000fffaf3e0f110
     x6 = 0x0000000000000000    x7 = 0x000000000000000b
     x8 = 0x00000000000000cf    x9 = 0x0000000000000006
    x10 = 0x0000fffaf3e10610   x11 = 0x0000000000000060
    x12 = 0x0000000000022fa0   x13 = 0x0000000000030020
    x14 = 0x0000000000023000   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff861f7670   x19 = 0x000000000000008f
    x20 = 0x0000000000000000   x21 = 0x0000fffaf3e0f110
    x22 = 0x0000fffaf3e0f160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd2815ec0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000008f
    x28 = 0x0000fffaf3603000    fp = 0x0000fffaf3e0f040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffaf3e0f040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffaf3e0f080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffaf3e0f050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffaf3e0f2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffaf3e0f090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffaf3e0f3dc
    x19 = 0x0000ffffa2e2a650   x20 = 0x0000ffffa2e2a63c
    x21 = 0x0000fffaf3e0f5c8   x22 = 0x0000000000000000
    x23 = 0x0000fffaf3603000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffaf3603000
    x27 = 0x0000fffaf3e0f110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a620    sp = 0x0000fffaf3e0f300
     pc = 0x0000fffaf3e0f3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffaf3e0f328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffaf3e0f3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffaf3e0f410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffaf3e0f4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffaf3e0f560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffaf3e0f5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4cc1fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffaf3e0f5e0
     pc = 0x00004cc200000001
    Found by: call frame info
11  model.tar + 0x4c582ffc
     sp = 0x0000fffaf3e0f5e8    pc = 0x0000fffa00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffaf3e0f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  model.tar + 0x1abb6ffc
     sp = 0x0000fffaf3e0f658    pc = 0x0000fffaf3603000
    Found by: stack scanning
14  model.tar + 0x1abb6ffc
     sp = 0x0000fffaf3e0f670    pc = 0x0000fffaf3603000
    Found by: stack scanning
15  libc.so.6 + 0xeba48
     sp = 0x0000fffaf3e0f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 65
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd7033c5e0   x13 = 0x0000ffffab7f5fa8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x000000000000001c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000e   x27 = 0x0000000000000000
    x28 = 0x0000fffd7033c3b8    fp = 0x0000fffd7033c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd7033c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd7033c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd7033c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd7033c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd7033c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd7033c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd7033c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd7033d2d8
    x19 = 0x0000fffd7033c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd6fb30000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd6fb30000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b220    sp = 0x0000fffd7033c600
     pc = 0x0000fffd7033d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd7033c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd7033c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 66
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8bda64    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff7c73c5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffff7c73c488
     x8 = 0x0000000000000062    x9 = 0x00035fddb0f5ab9a
    x10 = 0x00ffffffffffffff   x11 = 0x000000027dd4060d
    x12 = 0x0000000000000000   x13 = 0x0000000000010000
    x14 = 0x0000000000000360   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffffab9bb670   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8bda64
    x22 = 0x0000ffffaf8bda64   x23 = 0x0000000000000019
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000000c   x27 = 0x0000ffffaf8bda38
    x28 = 0x0000000000000000    fp = 0x0000ffff7c73c420
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff7c73c420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff7c73c4d0    lr = 0x0000ffffafd55140
     sp = 0x0000ffff7c73c430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffff7c73c530    lr = 0x0000ffffb08f9654
     sp = 0x0000ffff7c73c4e0    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::ertps::rtps::ResourceEvent::event_service() + 0x240
     fp = 0x0000ffff7c73c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff7c73c540    pc = 0x0000ffffb08f9654
    Found by: previous frame's frame pointer
 4  0xffff7c73d2d8
    x19 = 0x0000ffff7c73c6f8   x20 = 0x0000ffff9f6a4600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffff7bf30000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff7bf30000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb1f57b50
     fp = 0x0000ffff7f29af40    sp = 0x0000ffff7c73c600
     pc = 0x0000ffff7c73d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff7c73c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff7c73c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 67
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000067    x1 = 0x0000ffffab8eacc0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffff7cf4c160    x5 = 0x0000ffff7cf4c110
     x6 = 0x0000000000000000    x7 = 0x0000ffff7cf4c260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffff7cf4c5e0   x13 = 0x0000000000000018
    x14 = 0x0000000000000008   x15 = 0x0000000000a02740
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000000000ad50f8   x19 = 0x0000000000000067
    x20 = 0x0000000000000000   x21 = 0x0000ffff7cf4c110
    x22 = 0x0000ffff7cf4c160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab8eacc0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000067
    x28 = 0x0000ffff7c740000    fp = 0x0000ffff7cf4c040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffff7cf4c040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffff7cf4c080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffff7cf4c050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffff7cf4c2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffff7cf4c090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffff7cf4c3dc
    x19 = 0x0000ffffab8a2530   x20 = 0x0000ffffab8a251c
    x21 = 0x0000ffff7cf4c5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffff7c740000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff7c740000
    x27 = 0x0000ffff7cf4c110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a2500    sp = 0x0000ffff7cf4c300
     pc = 0x0000ffff7cf4c3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffff7cf4c328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libjemalloc.so.2!extent_try_coalesce_impl [extent.c : 806 + 0x0]
     sp = 0x0000ffff7cf4c350    pc = 0x0000ffffb1caf858
    Found by: stack scanning
 6  0xffff7cf4d60c
    x19 = 0x0000ffffa3411228   x20 = 0x0000ffffaf5192d8
    x21 = 0x0000ffffaf5192e8   x22 = 0x0000ffffaf5192f0
    x23 = 0x0000ffffaf51a0b8   x24 = 0x1bbd716dcced7c00
    x25 = 0x0000ffff7cf4c400   x26 = 0x0000ffffb1cb162c
    x27 = 0x393232357cf4c400   x28 = 0x1bbd716dcced7c00
     fp = 0x0000ffffa3417180    sp = 0x0000ffff7cf4c3c0
     pc = 0x0000ffff7cf4d610
    Found by: call frame info
 7  libc.so.6 + 0x79cec
     sp = 0x0000ffff7cf4c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 8  libjemalloc.so.2!je_arena_decay [arena.c : 427 + 0x1c]
     sp = 0x0000ffff7cf4c480    pc = 0x0000ffffb1c77a70
    Found by: stack scanning
 9  0xffff7cf4d60c
    x19 = 0x0000ffffa3411040   x20 = 0x0000000000000000
    x21 = 0x0000ffffa3411f80   x22 = 0x0000ffffa34010b8
    x23 = 0x0000000000000001   x24 = 0x0000ffff7cf4d1d8
    x25 = 0x0000ffffa3403a38   x26 = 0x0000ffffa3411f40
    x27 = 0x0000ffffa34010b8   x28 = 0x1bbd716dcced7c00
     fp = 0x0000ffffa3401040    sp = 0x0000ffff7cf4c4f0
     pc = 0x0000ffff7cf4d610
    Found by: call frame info
10  libc.so.6 + 0x856b4
     sp = 0x0000ffff7cf4c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
11  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffff7cf4c5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
12  0x43f7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffff7cf4c5e0
     pc = 0x000043f800000001
    Found by: call frame info
13  libc.so.6 + 0x855dc
     sp = 0x0000ffff7cf4c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
14  libc.so.6 + 0xeba48
     sp = 0x0000ffff7cf4c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 68
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000098    x1 = 0x0000fffcd2826c80
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffaa5e91160    x5 = 0x0000fffaa5e91110
     x6 = 0x0000000000000000    x7 = 0x0000fffaa5e91260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffaa5e915e0   x13 = 0x0000ffffab7ee218
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x0000000000000098
    x20 = 0x0000000000000000   x21 = 0x0000fffaa5e91110
    x22 = 0x0000fffaa5e91160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd2826c80   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000098
    x28 = 0x0000fffaa5685000    fp = 0x0000fffaa5e91040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffaa5e91040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffaa5e91080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffaa5e91050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffaa5e912f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffaa5e91090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffaa5e913dc
    x19 = 0x0000ffffa2e2a730   x20 = 0x0000ffffa2e2a71c
    x21 = 0x0000fffaa5e915c8   x22 = 0x0000000000000000
    x23 = 0x0000fffaa5685000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffaa5685000
    x27 = 0x0000fffaa5e91110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a700    sp = 0x0000fffaa5e91300
     pc = 0x0000fffaa5e913e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffaa5e91328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffaa5e913c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffaa5e91410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffaa5e914f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffaa5e91560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffaa5e915c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4cedfffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffaa5e915e0
     pc = 0x00004cee00000001
    Found by: call frame info
11  model.tar + 0x4c582ffc
     sp = 0x0000fffaa5e915e8    pc = 0x0000fffa00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffaa5e91648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  libc.so.6 + 0xeba48
     sp = 0x0000fffaa5e91760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 69
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b71b8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000003
     x8 = 0x0000000000000062    x9 = 0x0000000000000002
    x10 = 0x0000ffff82d1d610   x11 = 0x0000000000000020
    x12 = 0x0000000000000fa0   x13 = 0x0000000000010000
    x14 = 0x0000000000000fc0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff861e5f60   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b71b8
    x22 = 0x0000ffffaf8b71b8   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000ffff82d1c488    fp = 0x0000ffff82d1c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff82d1c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff82d1c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff82d1c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff82d1c530    lr = 0x0000ffffb1923208
     sp = 0x0000ffff82d1c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000ffff82d1c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff82d1c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xffff82d1d2d8
    x19 = 0x0000ffff82d1c6f8   x20 = 0x0000ffff9f6a4600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffff82510000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffffb19233e8
    x27 = 0x0000ffffaf8b7160    fp = 0x0000ffffab822440
     sp = 0x0000ffff82d1c600    pc = 0x0000ffff82d1d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff82d1c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff82d1c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 70
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c14ac    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x000000000000087a
     x8 = 0x0000000000000062    x9 = 0x0000ffff80cdd958
    x10 = 0x0000000000003220   x11 = 0x000001ffffffff80
    x12 = 0x000003ffffffff80   x13 = 0x0000000000000040
    x14 = 0x0000000220a03004   x15 = 0x0000000230a03004
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000000000000012   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c14ac
    x22 = 0x0000ffffaf9c14ac   x23 = 0x0000000000000047
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000023   x27 = 0x0000000000000004
    x28 = 0x0000ffff80cdc4d8    fp = 0x0000ffff80cdc480
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff80cdc480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff80cdc520    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff80cdc490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff80cdc580    lr = 0x0000ffffb1912a30
     sp = 0x0000ffff80cdc530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000ffff80cdc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff80cdc590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xffff80cdd2d8
    x19 = 0x0000ffff80cdc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffff804d0000   x24 = 0x0000ffffafe80000
    x25 = 0x5f72656b726f7720   x26 = 0x0000ffffab838118
     fp = 0x0000ffffaf831520    sp = 0x0000ffff80cdc600
     pc = 0x0000ffff80cdd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff80cdc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff80cdc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 71
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000099    x1 = 0x0000fffcd28374c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffaa4bfc160    x5 = 0x0000fffaa4bfc110
     x6 = 0x0000000000000000    x7 = 0x0000fffaa4bfc260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffaa4bfc5e0   x13 = 0x0000ffffab7ee220
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x0000000000000099
    x20 = 0x0000000000000000   x21 = 0x0000fffaa4bfc110
    x22 = 0x0000fffaa4bfc160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd28374c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000099
    x28 = 0x0000fffaa43f0000    fp = 0x0000fffaa4bfc040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffaa4bfc040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffaa4bfc080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffaa4bfc050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffaa4bfc2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffaa4bfc090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffaa4bfc3dc
    x19 = 0x0000ffffa2e2a810   x20 = 0x0000ffffa2e2a7fc
    x21 = 0x0000fffaa4bfc5c8   x22 = 0x0000000000000000
    x23 = 0x0000fffaa43f0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffaa43f0000
    x27 = 0x0000fffaa4bfc110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a7e0    sp = 0x0000fffaa4bfc300
     pc = 0x0000fffaa4bfc3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffaa4bfc328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffaa4bfc3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffaa4bfc410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffaa4bfc4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffaa4bfc560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffaa4bfc5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4cedfffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffaa4bfc5e0
     pc = 0x00004cee00000001
    Found by: call frame info
11  model.tar + 0x4c582ffc
     sp = 0x0000fffaa4bfc5e8    pc = 0x0000fffa00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffaa4bfc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  libc.so.6 + 0xeba48
     sp = 0x0000fffaa4bfc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 72
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffcd26b8630    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffa8664c260
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffa8664c5e0   x13 = 0x0000ffffab7ee240
    x14 = 0x0000000000000008   x15 = 0x00000000000fdeb8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd1b1cf618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fffcd26b8630
    x22 = 0x0000fffcd26b8630   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffa8664c248    fp = 0x0000fffa8664c1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffa8664c1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffa8664c290    lr = 0x0000ffffafd54b20
     sp = 0x0000fffa8664c200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffa8664c2f0    lr = 0x0000ffffb0bd40c8
     sp = 0x0000fffa8664c2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerSyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000fffa8664c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffa8664c300    pc = 0x0000ffffb0bd40c8
    Found by: previous frame's frame pointer
 4  0xfffa8664d2d8
    x19 = 0x0000fffa8664c6f8   x20 = 0x0000fffd1b1cf600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffa85e40000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffa85e40000
    x27 = 0x0000000000000000   x28 = 0x0000fffcd26b8608
     fp = 0x0000fffcd26c4c60    sp = 0x0000fffa8664c600
     pc = 0x0000fffa8664d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffa8664c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffa8664c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 73
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa5015130    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffcce5ae5e0   x13 = 0x0000000000000002
    x14 = 0x0000000000000008   x15 = 0x000000000034ae58
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000000000000018   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa5015130
    x22 = 0x0000ffffa5015130   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffcce5ae408    fp = 0x0000fffcce5ae3b0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcce5ae3b0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcce5ae450    lr = 0x0000ffffafd54b20
     sp = 0x0000fffcce5ae3c0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffcce5ae4b0    lr = 0x0000fffd6cb6ae80
     sp = 0x0000fffcce5ae460    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libe2e_nodes.so!uni_perception::vla::StaticPostprocessNode::PubStaticRaw() [static_postprocess_node.cpp : 215 + 0x0]
     fp = 0x0000fffcce5ae5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffcce5ae4c0    pc = 0x0000fffd6cb6ae80
    Found by: previous frame's frame pointer
 4  0xfffcce5af2d8
    x19 = 0x0000fffcce5ae6f8   x20 = 0x0000fffcd0dff600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffccdda2000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcd0dfeec0   x26 = 0x0000fffccdda2000
    x27 = 0x0000000000000000   x28 = 0xffffff80ffffffd8
     fp = 0x0000ffffa32ff100    sp = 0x0000fffcce5ae600
     pc = 0x0000fffcce5af2dc
    Found by: call frame info
 5  dmabuf: + 0x8015fc
     sp = 0x0000fffcce5ae640    pc = 0x0000fffcd0dff600
    Found by: stack scanning
 6  libc.so.6 + 0x855dc
     sp = 0x0000fffcce5ae648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 7  dmabuf: + 0x200ffc
     sp = 0x0000fffcce5ae658    pc = 0x0000fffccdda2000
    Found by: stack scanning
 8  dmabuf: + 0x800ebc
     sp = 0x0000fffcce5ae668    pc = 0x0000fffcd0dfeec0
    Found by: stack scanning
 9  dmabuf: + 0x200ffc
     sp = 0x0000fffcce5ae670    pc = 0x0000fffccdda2000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fffcce5ae760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning
11  dmabuf: + 0x8015fc
     sp = 0x0000fffcce5ae780    pc = 0x0000fffcd0dff600
    Found by: stack scanning

Thread 74
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa362a030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffa053fb120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002044
     x8 = 0x0000000000000062    x9 = 0x0014155c4880c05e
    x10 = 0x00ffffffffffffff   x11 = 0x00000004759b4f0d
    x12 = 0x00000000001c2110   x13 = 0x00000000001d1630
    x14 = 0x00000000001c2290   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff86199910   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa362a030
    x22 = 0x0000fffa053fb018   x23 = 0x0000fffa053fb5b8
    x24 = 0x0000fffa053fb5a8   x25 = 0x0000fffa053fb598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fffa04bef000    fp = 0x0000fffa053fafd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffa053fafd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffa053fb040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffa053fafe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffa053fb070    lr = 0x0000ffffb068befc
     sp = 0x0000fffa053fb050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffa053fb320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffa053fb080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaa26e424
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffaa29ab80
    x21 = 0x0000ffffaa26e280   x22 = 0x0000ffffaa26e428
     fp = 0x0000ffffaa26e280    sp = 0x0000fffa053fb330
     pc = 0x0000ffffaa26e428
    Found by: call frame info
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffa053fb3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x855dc
     sp = 0x0000fffa053fb3e8    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 7  model.tar + 0x51171ffc
     sp = 0x0000fffa053fb3f8    pc = 0x0000fffa04bef000
    Found by: stack scanning
 8  libc.so.6 + 0x79cec
     sp = 0x0000fffa053fb410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 9  libc.so.6 + 0x8ccd0
     sp = 0x0000fffa053fb4b0    pc = 0x0000ffffafd5ccd4
    Found by: stack scanning
10  libc.so.6 + 0x556e0
     sp = 0x0000fffa053fb4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
11  libdsfdds.so!li::dsfdds::TopicQos::~TopicQos() + 0x260
     sp = 0x0000fffa053fb510    pc = 0x0000ffffb076a254
    Found by: stack scanning
12  0xfffa053fb58c
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fffa053fb5c8
    x21 = 0x0000fffa053fb5b8   x22 = 0x1bbd716dcced7c00
    x23 = 0x0000fffa053fb5f0   x24 = 0x0000ffffb0061ae0
    x25 = 0x0000ffffaa2150a0   x26 = 0x0000fffa053fc2dc
     fp = 0x0000ffffaa2150a8    sp = 0x0000fffa053fb5c0
     pc = 0x0000fffa053fb590
    Found by: call frame info
13  libc.so.6 + 0x855dc
     sp = 0x0000fffa053fb648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
14  model.tar + 0x51171ffc
     sp = 0x0000fffa053fb658    pc = 0x0000fffa04bef000
    Found by: stack scanning
15  model.tar + 0x51171ffc
     sp = 0x0000fffa053fb670    pc = 0x0000fffa04bef000
    Found by: stack scanning
16  libc.so.6 + 0xeba48
     sp = 0x0000fffa053fb760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 75
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3604030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffcd05ee120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002030
     x8 = 0x0000000000000062    x9 = 0x0012e42ed20236c6
    x10 = 0x00ffffffffffffff   x11 = 0x000000047338f50d
    x12 = 0x0000000000000e07   x13 = 0x0000000000000018
    x14 = 0x0000000000000288   x15 = 0x0000000000002808
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff861cd970   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3604030
    x22 = 0x0000fffcd05ee018   x23 = 0x0000fffcd05ee5b8
    x24 = 0x0000fffcd05ee5a8   x25 = 0x0000fffcd05ee598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fffccfde2000    fp = 0x0000fffcd05edfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcd05edfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcd05ee040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffcd05edfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffcd05ee070    lr = 0x0000ffffb068befc
     sp = 0x0000fffcd05ee050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffcd05ee320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffcd05ee080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaa270224
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff9b275b940
    x21 = 0x0000ffffaa270080   x22 = 0x0000ffffaa270228
     fp = 0x0000ffffaa270080    sp = 0x0000fffcd05ee330
     pc = 0x0000ffffaa270228
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fffcd05ee360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fffcd05ee420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 76
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf85c6c8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000ffff804cd610   x11 = 0x0000000000000030
    x12 = 0x0000000000002060   x13 = 0x0000000000011600
    x14 = 0x0000000000002090   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f19c708   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf85c6c8
    x22 = 0x0000ffffaf85c6c8   x23 = 0x00000000000001b4
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000da   x27 = 0x0000000000000000
    x28 = 0x0000ffff804cc488    fp = 0x0000ffff804cc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff804cc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff804cc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff804cc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff804cc530    lr = 0x0000ffffb1923208
     sp = 0x0000ffff804cc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000ffff804cc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff804cc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xffff804cd2d8
    x19 = 0x0000ffff804cc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffff7fcc0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffb19233e8
    x27 = 0x0000ffffaf85c670    fp = 0x0000ffffab822d80
     sp = 0x0000ffff804cc600    pc = 0x0000ffff804cd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff804cc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff804cc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 77
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b75c8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000018e4
     x8 = 0x0000000000000062    x9 = 0x001b1dfc8f78575b
    x10 = 0x00ffffffffffffff   x11 = 0x0000000395413a0d
    x12 = 0x0000ffff7e77c5e0   x13 = 0x0000ffffab8d6000
    x14 = 0x0000000000000008   x15 = 0x0000ffffab8d6700
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff81cfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b75c8
    x22 = 0x0000ffffaf8b75c8   x23 = 0x0000000000000004
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000000000000000
    x28 = 0x0000ffff7e77c488    fp = 0x0000ffff7e77c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff7e77c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff7e77c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff7e77c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff7e77c530    lr = 0x0000ffffb1923208
     sp = 0x0000ffff7e77c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000ffff7e77c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff7e77c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xffff7e77d2d8
    x19 = 0x0000ffff7e77c6f8   x20 = 0x0000ffff81cfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffff7df70000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff81cfcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaf8b7570    fp = 0x0000ffffaf9be580
     sp = 0x0000ffff7e77c600    pc = 0x0000ffff7e77d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff7e77c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff7e77c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 78
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3601030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffd1b1ce120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002044
     x8 = 0x0000000000000062    x9 = 0x0014155c4880c05e
    x10 = 0x00ffffffffffffff   x11 = 0x00000004759b4f0d
    x12 = 0x0000000000280a90   x13 = 0x000000000028a3e0
    x14 = 0x0000000000280d10   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffffab7edfb0   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3601030
    x22 = 0x0000fffd1b1ce018   x23 = 0x0000fffd1b1ce5b8
    x24 = 0x0000fffd1b1ce5a8   x25 = 0x0000fffd1b1ce598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fffd1a9c2000    fp = 0x0000fffd1b1cdfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1b1cdfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1b1ce040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffd1b1cdfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffd1b1ce070    lr = 0x0000ffffb068befc
     sp = 0x0000fffd1b1ce050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffd1b1ce320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffd1b1ce080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaa271b24
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffaa29a7c0
    x21 = 0x0000ffffaa271980   x22 = 0x0000ffffaa271b28
     fp = 0x0000ffffaa271980    sp = 0x0000fffd1b1ce330
     pc = 0x0000ffffaa271b28
    Found by: call frame info
 5  libjemalloc.so.2!je_tcache_alloc_small_hard [tcache.c : 238 + 0x4]
     sp = 0x0000fffd1b1ce360    pc = 0x0000ffffb1cd3468
    Found by: stack scanning
 6  0xfffd1b1cf70e
    x19 = 0x0000fffd1b1ce4d8   x20 = 0x0000ffffa3001040
    x21 = 0x0000000000000020    fp = 0x0000fffd1b1cf9a0
     sp = 0x0000fffd1b1ce390    pc = 0x0000fffd1b1cf712
    Found by: call frame info

Thread 79
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b72cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd2bca0610   x11 = 0x0000000000000030
    x12 = 0x0000000000000c70   x13 = 0x0000000000010800
    x14 = 0x0000000000000ca0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f194660   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b72cc
    x22 = 0x0000ffffaf8b72cc   x23 = 0x00000000000000af
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000057   x27 = 0x0000000000000004
    x28 = 0x0000fffd2bc9f488    fp = 0x0000fffd2bc9f430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2bc9f430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2bc9f4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2bc9f440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2bc9f530    lr = 0x0000ffffb1923208
     sp = 0x0000fffd2bc9f4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffd2bc9f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2bc9f540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffd2bca02d8
    x19 = 0x0000fffd2bc9f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd2b493000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffb19233e8
    x27 = 0x0000ffffaf8b7270    fp = 0x0000ffffab81c900
     sp = 0x0000fffd2bc9f600    pc = 0x0000fffd2bca02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2bc9f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2bc9f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 80
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000100    x1 = 0x0000fff8e57e5f00
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8bb9f9160    x5 = 0x0000fff8bb9f9110
     x6 = 0x0000000000000000    x7 = 0x0000fff8bb9f9260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8bb9f95e0   x13 = 0x0000ffff861aa2e0
    x14 = 0x0000000000000008   x15 = 0x0000ffffaa2187c0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000100
    x20 = 0x0000000000000000   x21 = 0x0000fff8bb9f9110
    x22 = 0x0000fff8bb9f9160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e57e5f00   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000100
    x28 = 0x0000fff8bb1ed000    fp = 0x0000fff8bb9f9040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8bb9f9040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8bb9f9080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8bb9f9050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8bb9f92f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8bb9f9090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8bb9f93dc
    x19 = 0x0000ffffaa2ffab0   x20 = 0x0000ffffaa2ffa9c
    x21 = 0x0000fff8bb9f95c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8bb1ed000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8bb1ed000
    x27 = 0x0000fff8bb9f9110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa2ffa80    sp = 0x0000fff8bb9f9300
     pc = 0x0000fff8bb9f93e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8bb9f9328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8bb9f93c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8bb9f9410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8bb9f94f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8bb9f9560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8bb9f95c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1ce7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8bb9f95e0
     pc = 0x00001ce800000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8bb9f9648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  dmabuf: + 0x200ffc
     sp = 0x0000fff8bb9f9658    pc = 0x0000fff8bb1ed000
    Found by: stack scanning
13  dmabuf: + 0x200ffc
     sp = 0x0000fff8bb9f9670    pc = 0x0000fff8bb1ed000
    Found by: stack scanning
14  libc.so.6 + 0xeba48
     sp = 0x0000fff8bb9f9760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 81
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b76cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000018e4
     x8 = 0x0000000000000062    x9 = 0x001b1dfc8f78575b
    x10 = 0x00ffffffffffffff   x11 = 0x0000000395413a0d
    x12 = 0x0000fffd2b48f5e0   x13 = 0x0000ffffab80e1f8
    x14 = 0x0000000000000008   x15 = 0x000000000000d380
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff81cfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b76cc
    x22 = 0x0000ffffaf8b76cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd2b48f488    fp = 0x0000fffd2b48f430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2b48f430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2b48f4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2b48f440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2b48f530    lr = 0x0000ffffb1923208
     sp = 0x0000fffd2b48f4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffd2b48f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2b48f540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffd2b4902d8
    x19 = 0x0000fffd2b48f6f8   x20 = 0x0000ffff81cfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd2ac83000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff81cfcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaf8b7670    fp = 0x0000ffffab81b0c0
     sp = 0x0000fffd2b48f600    pc = 0x0000fffd2b4902dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2b48f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2b48f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 82
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000103    x1 = 0x0000fff8e57f62c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8bafe8160    x5 = 0x0000fff8bafe8110
     x6 = 0x0000000000000000    x7 = 0x0000fff8bafe8260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8bafe85e0   x13 = 0x0000ffffaa2da000
    x14 = 0x0000000000000008   x15 = 0x0000ffffaa2de600
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000103
    x20 = 0x0000000000000000   x21 = 0x0000fff8bafe8110
    x22 = 0x0000fff8bafe8160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e57f62c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000103
    x28 = 0x0000fff8ba7dc000    fp = 0x0000fff8bafe8040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8bafe8040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8bafe8080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8bafe8050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8bafe82f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8bafe8090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8bafe83dc
    x19 = 0x0000ffffaa2ffe30   x20 = 0x0000ffffaa2ffe1c
    x21 = 0x0000fff8bafe85c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8ba7dc000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8ba7dc000
    x27 = 0x0000fff8bafe8110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa2ffe00    sp = 0x0000fff8bafe8300
     pc = 0x0000fff8bafe83e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8bafe8328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8bafe83c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8bafe8410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8bafe84f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8bafe8560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8bafe85c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1ce7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8bafe85e0
     pc = 0x00001ce800000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8bafe8648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000fff8bafe8760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 83
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06ba8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd253cf430
     x8 = 0x0000000000000062    x9 = 0x0000fffd253cf4f0
    x10 = 0x0000000000000038   x11 = 0x00000000ffffffc8
    x12 = 0x0000fffd253cf4e0   x13 = 0x0000fffd253cf5a0
    x14 = 0x0000000000000000   x15 = 0x0000ffffab8d9100
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06ba8
    x22 = 0x0000ffffa2e06ba8   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd253cf478    fp = 0x0000fffd253cf420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd253cf420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd253cf4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd253cf430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd253cf520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd253cf4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd253cf5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd253cf530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd253d02d8
    x19 = 0x0000fffd253cf6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd24bc3000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd24bc3000
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffffab867f80    sp = 0x0000fffd253cf600
     pc = 0x0000fffd253d02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd253cf648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd253cf760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 84
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06abc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd28c40610   x11 = 0x0000000000000030
    x12 = 0x0000000000001ba0   x13 = 0x0000000000010000
    x14 = 0x0000000000001bd0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f21a5c8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06abc
    x22 = 0x0000ffffa2e06abc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd28c3f478    fp = 0x0000fffd28c3f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd28c3f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd28c3f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd28c3f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd28c3f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd28c3f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd28c3f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd28c3f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd28c402d8
    x19 = 0x0000fffd28c3f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd28433000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd28433000
    x27 = 0x0000000000000000   x28 = 0x0000fff7dae08f80
     fp = 0x0000ffffab8213a0    sp = 0x0000fffd28c3f600
     pc = 0x0000fffd28c402dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd28c3f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd28c3f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 85
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06db8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd1e22e430
     x8 = 0x0000000000000062    x9 = 0x0000fffd1e22e4f0
    x10 = 0x0000000000000039   x11 = 0x00000000ffffffc8
    x12 = 0x0000fffd1e22e4e0   x13 = 0x0000fffd1e22e5a0
    x14 = 0x0000000000000001   x15 = 0x0000ffffab8dbb00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06db8
    x22 = 0x0000ffffa2e06db8   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd1e22e478    fp = 0x0000fffd1e22e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1e22e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1e22e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1e22e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1e22e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1e22e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1e22e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1e22e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1e22f2d8
    x19 = 0x0000fffd1e22e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1da22000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1da22000
    x27 = 0x0000000000000000   x28 = 0x0000000000000000
     fp = 0x0000ffffab8219c0    sp = 0x0000fffd1e22e600
     pc = 0x0000fffd1e22f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1e22e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1e22e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 86
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000104    x1 = 0x0000fff8e5807ac0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8ba7d8160    x5 = 0x0000fff8ba7d8110
     x6 = 0x0000000000000000    x7 = 0x0000000000002030
     x8 = 0x00000000000000cf    x9 = 0x000fa6710e0236c6
    x10 = 0x00ffffffffffffff   x11 = 0x000000047338f50d
    x12 = 0x000000000058cba0   x13 = 0x00000000005921c0
    x14 = 0x000000000058cc00   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff8623d950   x19 = 0x0000000000000104
    x20 = 0x0000000000000000   x21 = 0x0000fff8ba7d8110
    x22 = 0x0000fff8ba7d8160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e5807ac0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000104
    x28 = 0x0000fff8b9fcc000    fp = 0x0000fff8ba7d8040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8ba7d8040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8ba7d8080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8ba7d8050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8ba7d82f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8ba7d8090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8ba7d83dc
    x19 = 0x0000ffffaa2fff10   x20 = 0x0000ffffaa2ffefc
    x21 = 0x0000fff8ba7d85c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8b9fcc000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b9fcc000
    x27 = 0x0000fff8ba7d8110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa2ffee0    sp = 0x0000fff8ba7d8300
     pc = 0x0000fff8ba7d83e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8ba7d8328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8ba7d83c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8ba7d8410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8ba7d84f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8ba7d8560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8ba7d85c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1ce7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8ba7d85e0
     pc = 0x00001ce800000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8ba7d8648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000fff8ba7d8760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 87
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06de8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd1da1f610   x11 = 0x0000000000000030
    x12 = 0x00000000000ac480   x13 = 0x00000000000ba8e0
    x14 = 0x00000000000ac4b0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff86332468   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06de8
    x22 = 0x0000ffffa2e06de8   x23 = 0x0000000000000038
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000001c   x27 = 0x0000000000000000
    x28 = 0x0000fffd1da1e478    fp = 0x0000fffd1da1e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1da1e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1da1e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1da1e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1da1e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1da1e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1da1e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1da1e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1da1f2d8
    x19 = 0x0000fffd1da1e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1d212000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1d212000
    x27 = 0x0000000000000000   x28 = 0x0000fff5d282b700
     fp = 0x0000ffffab821ae0    sp = 0x0000fffd1da1e600
     pc = 0x0000fffd1da1f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1da1e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1da1e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 88
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e7b900    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd1a1ae498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd1a1ae3d8
     x8 = 0x0000000000000062    x9 = 0x000eef55c7218e67
    x10 = 0x00ffffffffffffff   x11 = 0x0000000471cabf0d
    x12 = 0x0000000007f1f670   x13 = 0x0000000007f28320
    x14 = 0x0000000007f1f6a0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff861388c0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e7b900
    x22 = 0x0000ffffa2e7b900   x23 = 0x00000000000000b8
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000005c   x27 = 0x0000ffffa2e7b8d8
    x28 = 0x0000000000000000    fp = 0x0000fffd1a1ae370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1a1ae370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1a1ae420    lr = 0x0000ffffafd55140
     sp = 0x0000fffd1a1ae380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fffd1a1ae570    lr = 0x0000ffffa3769818
     sp = 0x0000fffd1a1ae430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fffd1a1ae580    lr = 0x0000ffffa54acee8
     sp = 0x0000fffd1a1ae580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fffd1a1ae5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fffd1a1ae590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa2e0e914
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa2e190f0    sp = 0x0000fffd1a1ae5d0
     pc = 0x0000ffffa2e0e918
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fffd1a1ae5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fffd1a1ae648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  0xfffffffd
     sp = 0x0000fffd1a1ae698    pc = 0x0000000100000001
    Found by: stack scanning
 9  libc.so.6 + 0xeba48
     sp = 0x0000fffd1a1ae760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 89
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06e1c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000004e8
     x8 = 0x0000000000000062    x9 = 0x0000fffd1d20f958
    x10 = 0x00000000000009e0   x11 = 0x0000000000000000
    x12 = 0x0000000000000040   x13 = 0x0000000000000100
    x14 = 0x0000000180303007   x15 = 0x0000000190303007
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x000000000000001f   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06e1c
    x22 = 0x0000ffffa2e06e1c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd1d20e478    fp = 0x0000fffd1d20e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1d20e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1d20e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1d20e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1d20e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1d20e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1d20e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1d20e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1d20f2d8
    x19 = 0x0000fffd1d20e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1ca02000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1ca02000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa333b940
     fp = 0x0000ffffab821bc0    sp = 0x0000fffd1d20e600
     pc = 0x0000fffd1d20f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1d20e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1d20e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 90
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c252c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000016
     x8 = 0x0000000000000062    x9 = 0x000000000000000a
    x10 = 0x0000fffd130cf610   x11 = 0x00000000000000c0
    x12 = 0x0000000000024a08   x13 = 0x00000000000307d0
    x14 = 0x0000000000024ac8   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f2faa20   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c252c
    x22 = 0x0000ffffaf9c252c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd130ce4d8    fp = 0x0000fffd130ce480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd130ce480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd130ce520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd130ce490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd130ce580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd130ce530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd130ce5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd130ce590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd130cf2d8
    x19 = 0x0000fffd130ce6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd128c2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff8613f518
     fp = 0x0000ffffaf8319a0    sp = 0x0000fffd130ce600
     pc = 0x0000fffd130cf2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd130ce648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd130ce760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 91
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa322d270    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff640000000
     x8 = 0x0000000000000062    x9 = 0x000000000000dce0
    x10 = 0x0000fff650c6e7c0   x11 = 0x0002ffffa3051601
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000002
    x14 = 0x0000000000000020   x15 = 0x0000000000000100
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000000000000018   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa322d270
    x22 = 0x0000ffffa322d270   x23 = 0x0000000000000004
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000000000000000
    x28 = 0x0000fffcfd42e4d8    fp = 0x0000fffcfd42e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcfd42e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcfd42e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffcfd42e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffcfd42e750    lr = 0x0000fffd69f9c118
     sp = 0x0000fffcfd42e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libopencv_core.so.4.3!cv::WorkerThread::thread_body() + 0x114
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fffcfd42e760    pc = 0x0000fffd69f9c118
    Found by: previous frame's frame pointer

Thread 92
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000107    x1 = 0x0000fff8e5829480
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8b95b7160    x5 = 0x0000fff8b95b7110
     x6 = 0x0000000000000000    x7 = 0x0000fff8b95b7260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8b95b75e0   x13 = 0x0000ffff861aa2c0
    x14 = 0x0000000000000008   x15 = 0x000000000000a2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000107
    x20 = 0x0000000000000000   x21 = 0x0000fff8b95b7110
    x22 = 0x0000fff8b95b7160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e5829480   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000107
    x28 = 0x0000fff8b8dab000    fp = 0x0000fff8b95b7040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8b95b7040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8b95b7080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8b95b7050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8b95b72f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8b95b7090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8b95b73dc
    x19 = 0x0000ffffaa3000d0   x20 = 0x0000ffffaa3000bc
    x21 = 0x0000fff8b95b75c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8b8dab000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b8dab000
    x27 = 0x0000fff8b95b7110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa3000a0    sp = 0x0000fff8b95b7300
     pc = 0x0000fff8b95b73e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8b95b7328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8b95b73c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8b95b7410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8b95b74f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8b95b7560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8b95b75c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1d17fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8b95b75e0
     pc = 0x00001d1800000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8b95b7648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000fff8b95b7760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 93
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2eab860    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffc53f715d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffc53f71488
     x8 = 0x0000000000000062    x9 = 0x0011adc31f2fdb57
    x10 = 0x00ffffffffffffff   x11 = 0x00000002d60b080d
    x12 = 0x00000000000040e0   x13 = 0x0000000000010000
    x14 = 0x00000000000042e0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff86378da0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2eab860
    x22 = 0x0000ffffa2eab860   x23 = 0x0000000000000042
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000021   x27 = 0x0000ffffa2eab838
    x28 = 0x0000000000000000    fp = 0x0000fffc53f71420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffc53f71420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffc53f714d0    lr = 0x0000ffffafd55140
     sp = 0x0000fffc53f71430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fffc53f71530    lr = 0x0000ffffb08f9654
     sp = 0x0000fffc53f714e0    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::ertps::rtps::ResourceEvent::event_service() + 0x240
     fp = 0x0000fffc53f715f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffc53f71540    pc = 0x0000ffffb08f9654
    Found by: previous frame's frame pointer
 4  0xfffc53f722d8
    x19 = 0x0000fffc53f716f8   x20 = 0x0000fffd1b1cf600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffc53765000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffc53765000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb1f57b50
     fp = 0x0000fffcd26c5180    sp = 0x0000fffc53f71600
     pc = 0x0000fffc53f722dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffc53f71648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  model.tar + 0x288b8ffc
     sp = 0x0000fffc53f71658    pc = 0x0000fffc53765000
    Found by: stack scanning
 7  model.tar + 0x288b8ffc
     sp = 0x0000fffc53f71670    pc = 0x0000fffc53765000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fffc53f71760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 94
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000009a    x1 = 0x0000fffcd28483c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffa8816c160    x5 = 0x0000fffa8816c110
     x6 = 0x0000000000000000    x7 = 0x0000fffa8816c260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffa8816c5e0   x13 = 0x0000ffffab7ee228
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x000000000000009a
    x20 = 0x0000000000000000   x21 = 0x0000fffa8816c110
    x22 = 0x0000fffa8816c160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd28483c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000009a
    x28 = 0x0000fffa87960000    fp = 0x0000fffa8816c040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffa8816c040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffa8816c080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffa8816c050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffa8816c2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffa8816c090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffa8816c3dc
    x19 = 0x0000ffffa2e2a8f0   x20 = 0x0000ffffa2e2a8dc
    x21 = 0x0000fffa8816c5c8   x22 = 0x0000000000000000
    x23 = 0x0000fffa87960000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffa87960000
    x27 = 0x0000fffa8816c110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a8c0    sp = 0x0000fffa8816c300
     pc = 0x0000fffa8816c3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffa8816c328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffa8816c3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffa8816c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffa8816c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffa8816c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffa8816c5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4ceefffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffa8816c5e0
     pc = 0x00004cef00000001
    Found by: call frame info
11  model.tar + 0x4c582ffc
     sp = 0x0000fffa8816c5e8    pc = 0x0000fffa00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffa8816c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  libc.so.6 + 0xeba48
     sp = 0x0000fffa8816c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 95
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b7cc8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000002
    x10 = 0x0000fffceedef610   x11 = 0x0000000000000020
    x12 = 0x0000000000008d70   x13 = 0x0000000000010000
    x14 = 0x0000000000008d90   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff86108fb0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b7cc8
    x22 = 0x0000ffffaf8b7cc8   x23 = 0x000000000000000c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000006   x27 = 0x0000000000000000
    x28 = 0x0000fffceedee488    fp = 0x0000fffceedee430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffceedee430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffceedee4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffceedee440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffceedee530    lr = 0x0000ffffb1923208
     sp = 0x0000fffceedee4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fffceedee5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffceedee540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfffceedef2d8
    x19 = 0x0000fffceedee6f8   x20 = 0x0000ffff81cfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffcee5e2000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff81cfcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaf8b7c70    fp = 0x0000ffffab81bf40
     sp = 0x0000fffceedee600    pc = 0x0000fffceedef2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffceedee648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffceedee760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 96
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa360d030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffcd262e120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002044
     x8 = 0x0000000000000062    x9 = 0x0014155c4880c05e
    x10 = 0x00ffffffffffffff   x11 = 0x00000004759b4f0d
    x12 = 0x7000000000000000   x13 = 0x0000000000009fff
    x14 = 0x000000000000000f   x15 = 0x0000000000000025
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x000000000000000f   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa360d030
    x22 = 0x0000fffcd262e018   x23 = 0x0000fffcd262e5b8
    x24 = 0x0000fffcd262e5a8   x25 = 0x0000fffcd262e598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffccedbeec0
    x28 = 0x0000fffcd1e22000    fp = 0x0000fffcd262dfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcd262dfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcd262e040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffcd262dfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffcd262e070    lr = 0x0000ffffb068befc
     sp = 0x0000fffcd262e050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffcd262e320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffcd262e080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfffd331625a4
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fffba3d4b500
    x21 = 0x0000fffd33162400   x22 = 0x0000fffd331625a8
     fp = 0x0000fffd33162400    sp = 0x0000fffcd262e330
     pc = 0x0000fffd331625a8
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fffcd262e360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fffcd262e420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 97
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa06965cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001cd6
     x8 = 0x0000000000000062    x9 = 0x001b796caecd1dc2
    x10 = 0x00ffffffffffffff   x11 = 0x000000040d2de90d
    x12 = 0x0000fff8336dc5e0   x13 = 0x0000ffff8633e1e8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa062da00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff8346fd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa06965cc
    x22 = 0x0000ffffa06965cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff8336dc488    fp = 0x0000fff8336dc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8336dc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8336dc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff8336dc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff8336dc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff8336dc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff8336dc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff8336dc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff8336dd2d8
    x19 = 0x0000fff8336dc6f8   x20 = 0x0000fff8346fd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff832ed0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff8346fcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0696570    fp = 0x0000ffffa0618680
     sp = 0x0000fff8336dc600    pc = 0x0000fff8336dd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff8336dc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff8336dc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff8336dc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 98
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3600030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffcee5de120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001ffe
     x8 = 0x0000000000000062    x9 = 0x000fe93d2a1041b9
    x10 = 0x00ffffffffffffff   x11 = 0x000000046d43140d
    x12 = 0x0000000000010000   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e7   x15 = 0x0000fffd6dad2bc0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x000000000000005f   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3600030
    x22 = 0x0000fffcee5de018   x23 = 0x0000fffcee5de5b8
    x24 = 0x0000fffcee5de5a8   x25 = 0x0000fffcee5de598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fffceddd2000    fp = 0x0000fffcee5ddfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcee5ddfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcee5de040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffcee5ddfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffcee5de070    lr = 0x0000ffffb068befc
     sp = 0x0000fffcee5de050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffcee5de320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffcee5de080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaa272024
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffaa29a1c0
    x21 = 0x0000ffffaa271e80   x22 = 0x0000ffffaa272028
     fp = 0x0000ffffaa271e80    sp = 0x0000fffcee5de330
     pc = 0x0000ffffaa272028
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fffcee5de360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fffcee5de420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 99
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0a8c7cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001cd4
     x8 = 0x0000000000000062    x9 = 0x001b5ae835b722c2
    x10 = 0x00ffffffffffffff   x11 = 0x000000040cf0e00d
    x12 = 0x0000fff832ecb540   x13 = 0x0000ffff863361f0
    x14 = 0x0000000000000008   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd01c3f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0a8c7cc
    x22 = 0x0000ffffa0a8c7cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff832ecc488    fp = 0x0000fff832ecc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff832ecc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff832ecc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff832ecc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff832ecc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff832ecc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff832ecc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff832ecc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff832ecd2d8
    x19 = 0x0000fff832ecc6f8   x20 = 0x0000fffd01c3f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff8326c0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd01c3eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0a8c770    fp = 0x0000ffffa0adb500
     sp = 0x0000fff832ecc600    pc = 0x0000fff832ecd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff832ecc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff832ecc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff832ecc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 100
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff9b2815664    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffcd1e1e5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffcd1e1e488
     x8 = 0x0000000000000062    x9 = 0x0009cf0d3a70bf8f
    x10 = 0x00ffffffffffffff   x11 = 0x000000042bef720d
    x12 = 0x0000000000000026   x13 = 0x0000000000000000
    x14 = 0x0000000000000001   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff861f27c0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fff9b2815664
    x22 = 0x0000fff9b2815664   x23 = 0x00000000000002e3
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000171   x27 = 0x0000fff9b2815638
    x28 = 0x0000000000000000    fp = 0x0000fffcd1e1e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcd1e1e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcd1e1e4d0    lr = 0x0000ffffafd55140
     sp = 0x0000fffcd1e1e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fffcd1e1e530    lr = 0x0000ffffb08f9654
     sp = 0x0000fffcd1e1e4e0    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::ertps::rtps::ResourceEvent::event_service() + 0x240
     fp = 0x0000fffcd1e1e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffcd1e1e540    pc = 0x0000ffffb08f9654
    Found by: previous frame's frame pointer
 4  0xfffcd1e1f2d8
    x19 = 0x0000fffcd1e1e6f8   x20 = 0x0000fffcf7c1f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffcd1612000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fffcd1612000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb1f57b50
     fp = 0x0000ffffaa29f4c0    sp = 0x0000fffcd1e1e600
     pc = 0x0000fffcd1e1f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffcd1e1e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffcd1e1e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 101
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000108    x1 = 0x0000fff8e583aa80
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8b8da7160    x5 = 0x0000fff8b8da7110
     x6 = 0x0000000000000000    x7 = 0x0000fff8b8da7260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8b8da75e0   x13 = 0x0000ffff861aa2c8
    x14 = 0x0000000000000008   x15 = 0x000000000000a2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000108
    x20 = 0x0000000000000000   x21 = 0x0000fff8b8da7110
    x22 = 0x0000fff8b8da7160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e583aa80   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000108
    x28 = 0x0000fff8b859b000    fp = 0x0000fff8b8da7040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8b8da7040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8b8da7080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8b8da7050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8b8da72f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8b8da7090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8b8da73dc
    x19 = 0x0000ffffaa3001b0   x20 = 0x0000ffffaa30019c
    x21 = 0x0000fff8b8da75c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8b859b000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b859b000
    x27 = 0x0000fff8b8da7110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa300180    sp = 0x0000fff8b8da7300
     pc = 0x0000fff8b8da73e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8b8da7328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8b8da73c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8b8da7410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8b8da74f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8b8da7560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8b8da75c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1d18fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8b8da75e0
     pc = 0x00001d1900000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8b8da7648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000fff8b8da7760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 102
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000109    x1 = 0x0000fff8e584b740
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8b8597160    x5 = 0x0000fff8b8597110
     x6 = 0x0000000000000000    x7 = 0x0000fff8b8597260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8b85975e0   x13 = 0x0000ffff861aa2d0
    x14 = 0x0000000000000008   x15 = 0x000000000000a2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000fff8b8597110
    x22 = 0x0000fff8b8597160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e584b740   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000109
    x28 = 0x0000fff8b7d8b000    fp = 0x0000fff8b8597040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8b8597040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8b8597080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8b8597050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8b85972f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8b8597090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8b85973dc
    x19 = 0x0000ffffaa300290   x20 = 0x0000ffffaa30027c
    x21 = 0x0000fff8b85975c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8b7d8b000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b7d8b000
    x27 = 0x0000fff8b8597110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa300260    sp = 0x0000fff8b8597300
     pc = 0x0000fff8b85973e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8b8597328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8b85973c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8b8597410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8b85974f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8b8597560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8b85975c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1d18fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8b85975e0
     pc = 0x00001d1900000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8b8597648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  dmabuf: + 0x200ffc
     sp = 0x0000fff8b8597658    pc = 0x0000fff8b7d8b000
    Found by: stack scanning
13  dmabuf: + 0x200ffc
     sp = 0x0000fff8b8597670    pc = 0x0000fff8b7d8b000
    Found by: stack scanning
14  libc.so.6 + 0xeba48
     sp = 0x0000fff8b8597760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 103
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000105    x1 = 0x0000fff8e58182c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fff8b9fc8160    x5 = 0x0000fff8b9fc8110
     x6 = 0x0000000000000000    x7 = 0x000000000000202e
     x8 = 0x00000000000000cf    x9 = 0x000f87ec821674c6
    x10 = 0x00ffffffffffffff   x11 = 0x0000000472fbec0d
    x12 = 0x0000000000216810   x13 = 0x0000000000221f90
    x14 = 0x0000000000216870   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff8621f950   x19 = 0x0000000000000105
    x20 = 0x0000000000000000   x21 = 0x0000fff8b9fc8110
    x22 = 0x0000fff8b9fc8160   x23 = 0x000000000000ffdc
    x24 = 0x0000fff8e58182c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000105
    x28 = 0x0000fff8b97bc000    fp = 0x0000fff8b9fc8040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fff8b9fc8040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fff8b9fc8080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fff8b9fc8050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fff8b9fc82f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fff8b9fc8090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfff8b9fc83dc
    x19 = 0x0000ffffaa2ffff0   x20 = 0x0000ffffaa2fffdc
    x21 = 0x0000fff8b9fc85c8   x22 = 0x0000000000000000
    x23 = 0x0000fff8b97bc000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b97bc000
    x27 = 0x0000fff8b9fc8110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffaa2fffc0    sp = 0x0000fff8b9fc8300
     pc = 0x0000fff8b9fc83e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fff8b9fc8328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8b9fc83c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fff8b9fc8410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fff8b9fc84f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fff8b9fc8560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fff8b9fc85c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x1d17fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fff8b9fc85e0
     pc = 0x00001d1800000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000fff8b9fc8648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  dmabuf: + 0x200ffc
     sp = 0x0000fff8b9fc8658    pc = 0x0000fff8b97bc000
    Found by: stack scanning
13  dmabuf: + 0x200ffc
     sp = 0x0000fff8b9fc8670    pc = 0x0000fff8b97bc000
    Found by: stack scanning
14  libc.so.6 + 0xeba48
     sp = 0x0000fff8b9fc8760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 104
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff9b2647634    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000001
     x8 = 0x0000000000000062    x9 = 0x0000fff8b69fb800
    x10 = 0x0000000000000030   x11 = 0x0000fff8b4d235c0
    x12 = 0x0000fff8b4d23980   x13 = 0x0000ffffb0cb1798
    x14 = 0x0000000000000001   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff8619dfb0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff9b2647634
    x22 = 0x0000fff9b2647634   x23 = 0x000000000000003b
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x000000000000001d   x27 = 0x0000000000000004
    x28 = 0x0000fff8b69fc248    fp = 0x0000fff8b69fc1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b69fc1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b69fc290    lr = 0x0000ffffafd54b20
     sp = 0x0000fff8b69fc200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff8b69fc2f0    lr = 0x0000ffffb0bd40c8
     sp = 0x0000fff8b69fc2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerSyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000fff8b69fc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff8b69fc300    pc = 0x0000ffffb0bd40c8
    Found by: previous frame's frame pointer
 4  0xfff8b69fd2d8
    x19 = 0x0000fff8b69fc6f8   x20 = 0x0000fffcf7c1f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff8b61f0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b61f0000
    x27 = 0x0000000000000000   x28 = 0x0000fff9b2647608
     fp = 0x0000ffffaa29e9c0    sp = 0x0000fff8b69fc600
     pc = 0x0000fff8b69fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff8b69fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff8b69fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 105
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000ffffa0b551d0    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000ffffa0b551dc    x7 = 0x0000ffffa0b551d8
     x8 = 0x0000000000000049    x9 = 0x0000ffffa0b72dc0
    x10 = 0x0000000000002fff   x11 = 0x0020ffffafa26601
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x00000000000688f8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000ffffa0b551d0
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff831eac4f0   x23 = 0x00000000000186a0
    x24 = 0x0000ffffa0b6d1f0   x25 = 0x0000fff831eac5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff831eac4c8
    x28 = 0x0000fff8316a0000    fp = 0x0000fff831eac3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff831eac3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff831eac5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff831eac3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff831eac750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff831eac600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff831eac760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 106
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1c14030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fff8b0e3c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001e66
     x8 = 0x0000000000000062    x9 = 0x0015838b5f07f180
    x10 = 0x00ffffffffffffff   x11 = 0x000000043cdcf10d
    x12 = 0x0000000000010000   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e7   x15 = 0x0000ffffa331cf40
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x000000000000007b   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1c14030
    x22 = 0x0000fff8b0e3c018   x23 = 0x0000fff8b0e3c5b8
    x24 = 0x0000fff8b0e3c5a8   x25 = 0x0000fff8b0e3c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fff8b0630000    fp = 0x0000fff8b0e3bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b0e3bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b0e3c040    lr = 0x0000ffffafd5e028
     sp = 0x0000fff8b0e3bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fff8b0e3c070    lr = 0x0000ffffb068befc
     sp = 0x0000fff8b0e3c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fff8b0e3c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fff8b0e3c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff8b46bc924
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff8b4d1f040
    x21 = 0x0000fff8b46bc780   x22 = 0x0000fff8b46bc928
     fp = 0x0000fff8b46bc780    sp = 0x0000fff8b0e3c330
     pc = 0x0000fff8b46bc928
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fff8b0e3c360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fff8b0e3c420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 107
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff9b2648230    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff8b61ec260
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fff8b61ec5e0   x13 = 0x0000ffff861aa280
    x14 = 0x0000000000000008   x15 = 0x0000000000c68fa8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff9b2648230
    x22 = 0x0000fff9b2648230   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fff8b61ec248    fp = 0x0000fff8b61ec1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b61ec1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b61ec290    lr = 0x0000ffffafd54b20
     sp = 0x0000fff8b61ec200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff8b61ec2f0    lr = 0x0000ffffb0bd4b88
     sp = 0x0000fff8b61ec2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerAsyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000fff8b61ec5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff8b61ec300    pc = 0x0000ffffb0bd4b88
    Found by: previous frame's frame pointer
 4  0xfff8b61ed2d8
    x19 = 0x0000fff8b61ec6f8   x20 = 0x0000fffcf7c1f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff8b59e0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x0000fff8b59e0000
    x27 = 0x0000000000000000   x28 = 0x0000fff9b2648208
     fp = 0x0000ffffaa29eca0    sp = 0x0000fff8b61ec600
     pc = 0x0000fff8b61ed2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff8b61ec648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff8b61ec760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 108
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1c12030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fff8afe1c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002022
     x8 = 0x0000000000000062    x9 = 0x00120e8eff09f9b2
    x10 = 0x00ffffffffffffff   x11 = 0x00000004718db60d
    x12 = 0x0000000000000c07   x13 = 0x0000000000000018
    x14 = 0x0000000000000270   x15 = 0x000000000000e768
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff86329970   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1c12030
    x22 = 0x0000fff8afe1c018   x23 = 0x0000fff8afe1c5b8
    x24 = 0x0000fff8afe1c5a8   x25 = 0x0000fff8afe1c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fff8af610000    fp = 0x0000fff8afe1bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8afe1bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8afe1c040    lr = 0x0000ffffafd5e028
     sp = 0x0000fff8afe1bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fff8afe1c070    lr = 0x0000ffffb068befc
     sp = 0x0000fff8afe1c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fff8afe1c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fff8afe1c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff8b3fb2ea4
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff8b4712ec0
    x21 = 0x0000fff8b3fb2d00   x22 = 0x0000fff8b3fb2ea8
     fp = 0x0000fff8b3fb2d00    sp = 0x0000fff8afe1c330
     pc = 0x0000fff8b3fb2ea8
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fff8afe1c360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fff8afe1c420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 109
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fffcf6349d50    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fffcf6349d5c    x7 = 0x0000fffcf6349d58
     x8 = 0x0000000000000049    x9 = 0x0000fffcf6366dc0
    x10 = 0x0000000000002fff   x11 = 0x0020ffffac447881
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x000000000006d438
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fffcf6349d50
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff83169c4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fffcf6361d70   x25 = 0x0000fff83169c5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff83169c4c8
    x28 = 0x0000fff830e90000    fp = 0x0000fff83169c3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff83169c3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff83169c5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff83169c3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff83169c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff83169c600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff83169c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 110
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffcf62b2100    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff82f65c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff82f65c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x00000000000581b8   x13 = 0x00000000000653d0
    x14 = 0x00000000000581e8   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff82f99708   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fffcf62b2100
    x22 = 0x0000fffcf62b2100   x23 = 0x0000000000000194
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000ca   x27 = 0x0000fffcf62b20d8
    x28 = 0x0000000000000000    fp = 0x0000fff82f65c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff82f65c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff82f65c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff82f65c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff82f65c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff82f65c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff82f65c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff82f65c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff82f65c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff82f65c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa06cfb14
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa07141a0    sp = 0x0000fff82f65c5d0
     pc = 0x0000ffffa06cfb18
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff82f65c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff82f65c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  dmabuf: + 0x61ffffc
     sp = 0x0000fff82f65c658    pc = 0x0000fff82ee50000
    Found by: stack scanning
 9  dmabuf: + 0x61ffffc
     sp = 0x0000fff82f65c670    pc = 0x0000fff82ee50000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fff82f65c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 111
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0a8cacc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3a
     x8 = 0x0000000000000062    x9 = 0x0003a1e7577ab35c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041919ab0d
    x12 = 0x0000fff801e9c5e0   x13 = 0x0000ffff8633e1e8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa0b2cc00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0a8cacc
    x22 = 0x0000ffffa0a8cacc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff801e9c488    fp = 0x0000fff801e9c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff801e9c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff801e9c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff801e9c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff801e9c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff801e9c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff801e9c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff801e9c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff801e9d2d8
    x19 = 0x0000fff801e9c6f8   x20 = 0x0000fffcf7c1f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff801690000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0a8ca70    fp = 0x0000ffffa0adbb00
     sp = 0x0000fff801e9c600    pc = 0x0000fff801e9d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff801e9c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff801e9c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff801e9c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 112
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff8b7b61bcc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3c
     x8 = 0x0000000000000062    x9 = 0x0003c06bd090ae5c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041956b40d
    x12 = 0x0000fff802ebc5e0   x13 = 0x0000ffff8615b1e8
    x14 = 0x0000000000000008   x15 = 0x0000fff8b7b67200
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd0c46f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff8b7b61bcc
    x22 = 0x0000fff8b7b61bcc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff802ebc488    fp = 0x0000fff802ebc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff802ebc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff802ebc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff802ebc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff802ebc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff802ebc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff802ebc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff802ebc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff802ebd2d8
    x19 = 0x0000fff802ebc6f8   x20 = 0x0000fffd0c46f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff8026b0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd0c46eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000fff8b7b61b70    fp = 0x0000fff8b7b62f80
     sp = 0x0000fff802ebc600    pc = 0x0000fff802ebd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff802ebc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff802ebc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff802ebc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 113
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0b33100    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff83067c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff83067c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x000000000006be28   x13 = 0x0000000000073a58
    x14 = 0x000000000006be58   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff82ee4528   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0b33100
    x22 = 0x0000ffffa0b33100   x23 = 0x0000000000000194
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000ca   x27 = 0x0000ffffa0b330d8
    x28 = 0x0000000000000000    fp = 0x0000fff83067c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff83067c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff83067c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff83067c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff83067c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff83067c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff83067c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff83067c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff83067c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff83067c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa0a60914
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa0a9e580    sp = 0x0000fff83067c5d0
     pc = 0x0000ffffa0a60918
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff83067c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff83067c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fff83067c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 114
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa06967cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001cd6
     x8 = 0x0000000000000062    x9 = 0x001b796caecd1dc2
    x10 = 0x00ffffffffffffff   x11 = 0x000000040d2de90d
    x12 = 0x0000fff8326bb540   x13 = 0x0000ffff8633e1f0
    x14 = 0x0000000000000008   x15 = 0x0000fffcf6344a00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff8346fd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa06967cc
    x22 = 0x0000ffffa06967cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff8326bc488    fp = 0x0000fff8326bc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8326bc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8326bc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff8326bc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff8326bc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff8326bc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff8326bc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff8326bc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff8326bd2d8
    x19 = 0x0000fff8326bc6f8   x20 = 0x0000fff8346fd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff831eb0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff8346fcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0696770    fp = 0x0000ffffa0618880
     sp = 0x0000fff8326bc600    pc = 0x0000fff8326bd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff8326bc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff8326bc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff8326bc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 115
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff8b7b61dcc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3a
     x8 = 0x0000000000000062    x9 = 0x0003a1e7577ab35c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041919ab0d
    x12 = 0x0000fff7ffe5b540   x13 = 0x0000ffff8615b1f0
    x14 = 0x0000000000000008   x15 = 0x0000fff82884a580
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd0c46f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff8b7b61dcc
    x22 = 0x0000fff8b7b61dcc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7ffe5c488    fp = 0x0000fff7ffe5c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ffe5c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ffe5c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7ffe5c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7ffe5c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7ffe5c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7ffe5c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7ffe5c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7ffe5d2d8
    x19 = 0x0000fff7ffe5c6f8   x20 = 0x0000fffd0c46f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7ff650000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd0c46eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000fff8b7b61d70    fp = 0x0000ffffa486e180
     sp = 0x0000fff7ffe5c600    pc = 0x0000fff7ffe5d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7ffe5c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7ffe5c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7ffe5c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 116
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaa2e4ecc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3a
     x8 = 0x0000000000000062    x9 = 0x0003a1e7577ab35c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041919ab0d
    x12 = 0x0000fff7fde1b540   x13 = 0x0000ffff82fef1f0
    x14 = 0x0000000000000008   x15 = 0x0000fff8b3809a00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff830e8d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaa2e4ecc
    x22 = 0x0000ffffaa2e4ecc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7fde1c488    fp = 0x0000fff7fde1c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fde1c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fde1c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fde1c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fde1c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7fde1c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7fde1c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fde1c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7fde1d2d8
    x19 = 0x0000fff7fde1c6f8   x20 = 0x0000fff830e8d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7fd610000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff830e8cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaa2e4e70    fp = 0x0000fff9b27988c0
     sp = 0x0000fff7fde1c600    pc = 0x0000fff7fde1d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fde1c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7fde1c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7fde1c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 117
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3208a28    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fff8346fd610   x11 = 0x0000000000000030
    x12 = 0x0000000018116230   x13 = 0x00000000181260d0
    x14 = 0x0000000018116260   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffab80a7f0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3208a28
    x22 = 0x0000ffffa3208a28   x23 = 0x0000000000000080
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000040   x27 = 0x0000000000000000
    x28 = 0x0000fff8346fc478    fp = 0x0000fff8346fc420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8346fc420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8346fc4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff8346fc430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff8346fc520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fff8346fc4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fff8346fc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff8346fc530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfff8346fd2d8
    x19 = 0x0000fff8346fc6f8   x20 = 0x0000ffff81cfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fff833ef0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff81cfcec0   x26 = 0x0000fff833ef0000
    x27 = 0x0000000000000000   x28 = 0x0000fffa859d5940
     fp = 0x0000ffffab84e460    sp = 0x0000fff8346fc600
     pc = 0x0000fff8346fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff8346fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff8346fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 118
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0696acc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3e
     x8 = 0x0000000000000062    x9 = 0x0003def049a6a95c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041993bd0d
    x12 = 0x0000fff80168c5e0   x13 = 0x0000ffff863361e8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa0655200
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd01c3f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0696acc
    x22 = 0x0000ffffa0696acc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff80168c488    fp = 0x0000fff80168c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff80168c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff80168c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff80168c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff80168c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff80168c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff80168c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff80168c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff80168d2d8
    x19 = 0x0000fff80168c6f8   x20 = 0x0000fffd01c3f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff800e80000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd01c3eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0696a70    fp = 0x0000ffffa0618e80
     sp = 0x0000fff80168c600    pc = 0x0000fff80168d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff80168c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff80168c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff80168c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 119
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000ffffa0bdd910    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000ffffa0bdd91c    x7 = 0x0000ffffa0bdd918
     x8 = 0x0000000000000049    x9 = 0x0000fff8b784bac0
    x10 = 0x0000000000002fff   x11 = 0x0020ffffa4a35481
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x0000000000069138
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000ffffa0bdd910
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7fee3c4f0   x23 = 0x00000000000186a0
    x24 = 0x0000ffffa0bf5930   x25 = 0x0000fff7fee3c5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7fee3c4c8
    x28 = 0x0000fff7fe630000    fp = 0x0000fff7fee3c3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7fee3c3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7fee3c5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7fee3c3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7fee3c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7fee3c600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7fee3c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 120
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa4c03fcc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d40
     x8 = 0x0000000000000062    x9 = 0x0003fd74c290c96c
    x10 = 0x00ffffffffffffff   x11 = 0x0000000419d0c60d
    x12 = 0x0000fff7fbddc5e0   x13 = 0x0000ffff830151e8
    x14 = 0x0000000000000008   x15 = 0x0000fff9b23f8c00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff7ff64d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa4c03fcc
    x22 = 0x0000ffffa4c03fcc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7fbddc488    fp = 0x0000fff7fbddc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fbddc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fbddc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fbddc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fbddc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7fbddc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7fbddc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fbddc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7fbddd2d8
    x19 = 0x0000fff7fbddc6f8   x20 = 0x0000fff7ff64d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7fb5d0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff7ff64cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa4c03f70    fp = 0x0000fff9b23f4ac0
     sp = 0x0000fff7fbddc600    pc = 0x0000fff7fbddd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fbddc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7fbddc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7fbddc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 121
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3220ccc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d44
     x8 = 0x0000000000000062    x9 = 0x00043a7db525b5ff
    x10 = 0x00ffffffffffffff   x11 = 0x000000041a4ad80d
    x12 = 0x0000fff7fa5ac5e0   x13 = 0x0000ffff830251e8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa3244400
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff7fe62d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3220ccc
    x22 = 0x0000ffffa3220ccc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7fa5ac488    fp = 0x0000fff7fa5ac430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fa5ac430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fa5ac4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fa5ac440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fa5ac530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7fa5ac4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7fa5ac5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fa5ac540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7fa5ad2d8
    x19 = 0x0000fff7fa5ac6f8   x20 = 0x0000fff7fe62d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7f9da0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff7fe62cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa3220c70    fp = 0x0000ffffa323d080
     sp = 0x0000fff7fa5ac600    pc = 0x0000fff7fa5ad2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fa5ac648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7fa5ac698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7fa5ac760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 122
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff827f9e900    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7ee93c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7ee93c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000000652048   x13 = 0x000000000065a1a0
    x14 = 0x0000000000652078   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff83080638   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fff827f9e900
    x22 = 0x0000fff827f9e900   x23 = 0x00000000000001c4
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000e2   x27 = 0x0000fff827f9e8d8
    x28 = 0x0000000000000000    fp = 0x0000fff7ee93c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ee93c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ee93c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7ee93c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7ee93c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7ee93c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7ee93c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7ee93c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7ee93c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7ee93c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xfff83509dd14
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa4838280    sp = 0x0000fff7ee93c5d0
     pc = 0x0000fff83509dd18
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7ee93c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7ee93c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fff7ee93c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 123
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fff802ef0d90    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fff802ef0d9c    x7 = 0x0000fff802ef0d98
     x8 = 0x0000000000000049    x9 = 0x0000fff802f0d200
    x10 = 0x0000000000002fff   x11 = 0x0020ffffa3028101
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x0000000000069fd8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fff802ef0d90
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7fd60c4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fff802f08db0   x25 = 0x0000fff7fd60c5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7fd60c4c8
    x28 = 0x0000fff7fce00000    fp = 0x0000fff7fd60c3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7fd60c3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7fd60c5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7fd60c3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7fd60c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7fd60c600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7fd60c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 124
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff9b23f51cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3c
     x8 = 0x0000000000000062    x9 = 0x0003c06bd090ae5c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041956b40d
    x12 = 0x0000fff7f7c9a540   x13 = 0x0000ffff830151f0
    x14 = 0x0000000000000008   x15 = 0x0000fff834704880
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff7ff64d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fff9b23f51cc
    x22 = 0x0000fff9b23f51cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7f7c9b488    fp = 0x0000fff7f7c9b430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7f7c9b430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7f7c9b4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7f7c9b440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7f7c9b530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7f7c9b4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7f7c9b5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7f7c9b540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7f7c9c2d8
    x19 = 0x0000fff7f7c9b6f8   x20 = 0x0000fff7ff64d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7f748f000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff7ff64cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000fff9b23f5170    fp = 0x0000ffffa4d17080
     sp = 0x0000fff7f7c9b600    pc = 0x0000fff7f7c9c2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7f7c9b648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  val_camera_stream_21_1 + 0xc0ffc
     sp = 0x0000fff7f7c9b658    pc = 0x0000fff7f748f000
    Found by: stack scanning
 7  val_camera_stream_21_1 + 0xc0ffc
     sp = 0x0000fff7f7c9b670    pc = 0x0000fff7f748f000
    Found by: stack scanning
 8  0xfffffffd
     sp = 0x0000fff7f7c9b698    pc = 0x0000000100000001
    Found by: stack scanning
 9  libc.so.6 + 0xeba48
     sp = 0x0000fff7f7c9b760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 125
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fffce5ec2550    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fffce5ec255c    x7 = 0x0000fffce5ec2558
     x8 = 0x0000000000000049    x9 = 0x0000fffce5edfc40
    x10 = 0x0000000000002fff   x11 = 0x0020ffffaa01ac01
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x00000000000645f8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fffce5ec2550
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7ec0ec4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fffce5eda570   x25 = 0x0000fff7ec0ec5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7ec0ec4c8
    x28 = 0x0000fff7eb8e0000    fp = 0x0000fff7ec0ec3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7ec0ec3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7ec0ec5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7ec0ec3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7ec0ec750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7ec0ec600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7ec0ec760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 126
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf81d080    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffffaf3fc5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffffaf3fc4b8
     x8 = 0x0000000000000062    x9 = 0x000e19b5f3fd7caa
    x10 = 0x00ffffffffffffff   x11 = 0x00000004701f800d
    x12 = 0x0000ffffb028e000   x13 = 0x000000000000003f
    x14 = 0x0000000001c77555   x15 = 0x0000000000000000
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x000000000000002a   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf81d080
    x22 = 0x0000ffffaf81d080   x23 = 0x0000000000000016
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000b   x27 = 0x0000ffffaf81d058
    x28 = 0x0000000000000000    fp = 0x0000ffffaf3fc450
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffffaf3fc450
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffffaf3fc500    lr = 0x0000ffffafd55140
     sp = 0x0000ffffaf3fc460    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffffaf3fc560    lr = 0x0000ffffb025161c
     sp = 0x0000ffffaf3fc510    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  liblog.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::log::details::PeriodicFlusher::PeriodicFlusher(std::function<void ()> const&)::{lambda()#1}> > >::_M_run() + 0xd8
     fp = 0x0000ffffaf3fc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffffaf3fc570    pc = 0x0000ffffb025161c
    Found by: previous frame's frame pointer
 4  0xffffaf3fd2d8
    x19 = 0x0000ffffaf3fc6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffffaebf0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffffaebf0000
    x27 = 0x0000ffffaf3fc5d8   x28 = 0x0000ffffaf822000
     fp = 0x0000ffffaf822000    sp = 0x0000ffffaf3fc600
     pc = 0x0000ffffaf3fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffffaf3fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffffaf3fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 127
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fffa2f3a3e90    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fffa2f3a3e9c    x7 = 0x0000fffa2f3a3e98
     x8 = 0x0000000000000049    x9 = 0x0000fffa2f3c0580
    x10 = 0x0000000000002fff   x11 = 0x0020ffffa341a601
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x000000000006eb18
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fffa2f3a3e90
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7eb8dc4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fffa2f3bbeb0   x25 = 0x0000fff7eb8dc5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7eb8dc4c8
    x28 = 0x0000fff7eb0d0000    fp = 0x0000fff7eb8dc3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7eb8dc3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7eb8dc5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7eb8dc3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7eb8dc750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7eb8dc600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7eb8dc760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 128
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fff8b380e990    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fff8b380e99c    x7 = 0x0000fff8b380e998
     x8 = 0x0000000000000049    x9 = 0x0000fff8b296f800
    x10 = 0x0000000000002fff   x11 = 0x0020ffffa1a33e01
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x00000000000645f8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fff8b380e990
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7fcdfc4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fff8b38269b0   x25 = 0x0000fff7fcdfc5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7fcdfc4c8
    x28 = 0x0000fff7fc5f0000    fp = 0x0000fff7fcdfc3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7fcdfc3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7fcdfc5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7fcdfc3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7fcdfc750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7fcdfc600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7fcdfc760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 129
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0696ccc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3c
     x8 = 0x0000000000000062    x9 = 0x0003c06bd090ae5c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041956b40d
    x12 = 0x0000fff7fb5cb540   x13 = 0x0000ffff863361f8
    x14 = 0x0000000000000008   x15 = 0x0000fffcf62da300
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd01c3f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0696ccc
    x22 = 0x0000ffffa0696ccc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7fb5cc488    fp = 0x0000fff7fb5cc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fb5cc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fb5cc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fb5cc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fb5cc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7fb5cc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7fb5cc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fb5cc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7fb5cd2d8
    x19 = 0x0000fff7fb5cc6f8   x20 = 0x0000fffd01c3f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7fadc0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd01c3eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0696c70    fp = 0x0000ffffa0623080
     sp = 0x0000fff7fb5cc600    pc = 0x0000fff7fb5cd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fb5cc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7fb5cc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7fb5cc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 130
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff8b6fc8104    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7e6bcc498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7e6bcc3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000004662ed8   x13 = 0x0000000004669db0
    x14 = 0x0000000004662f08   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff7f11e6c8   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fff8b6fc8104
    x22 = 0x0000fff8b6fc8104   x23 = 0x00000000000001bf
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x00000000000000df   x27 = 0x0000fff8b6fc80d8
    x28 = 0x0000000000000000    fp = 0x0000fff7e6bcc370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7e6bcc370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7e6bcc420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7e6bcc380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7e6bcc570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7e6bcc430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7e6bcc580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7e6bcc580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7e6bcc5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7e6bcc590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa4d1d414
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000fff8e69d3c00    sp = 0x0000fff7e6bcc5d0
     pc = 0x0000ffffa4d1d418
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7e6bcc5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7e6bcc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  dmabuf: + 0xc3fffc
     sp = 0x0000fff7e6bcc658    pc = 0x0000fff7e63c0000
    Found by: stack scanning
 9  dmabuf: + 0xc3fffc
     sp = 0x0000fff7e6bcc670    pc = 0x0000fff7e63c0000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fff7e6bcc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 131
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffce63c2904    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7e577c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7e577c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000004e63ac0   x13 = 0x0000000004e6db50
    x14 = 0x0000000004e63af0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff7f126590   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fffce63c2904
    x22 = 0x0000fffce63c2904   x23 = 0x00000000000001af
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x00000000000000d7   x27 = 0x0000fffce63c28d8
    x28 = 0x0000000000000000    fp = 0x0000fff7e577c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7e577c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7e577c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7e577c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7e577c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7e577c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7e577c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7e577c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7e577c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7e577c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xfffa85b48014
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa50029b0    sp = 0x0000fff7e577c5d0
     pc = 0x0000fffa85b48018
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7e577c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7e577c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  dmabuf: + 0xf9fffc
     sp = 0x0000fff7e577c658    pc = 0x0000fff7e4f70000
    Found by: stack scanning
 9  dmabuf: + 0xf9fffc
     sp = 0x0000fff7e577c670    pc = 0x0000fff7e4f70000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fff7e577c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 132
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffd685e5030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffd0bc5e120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002036
     x8 = 0x0000000000000062    x9 = 0x00133fbc758880dd
    x10 = 0x00ffffffffffffff   x11 = 0x0000000473f0100d
    x12 = 0x7000000000000000   x13 = 0x0000000000000018
    x14 = 0x00000000000002b8   x15 = 0x0000000000002948
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x000000000000000d   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000fffd685e5030
    x22 = 0x0000fffd0bc5e018   x23 = 0x0000fffd0bc5e5b8
    x24 = 0x0000fffd0bc5e5a8   x25 = 0x0000fffd0bc5e598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf55feec0
    x28 = 0x0000fffd0b452000    fp = 0x0000fffd0bc5dfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd0bc5dfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd0bc5e040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffd0bc5dfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffd0bc5e070    lr = 0x0000ffffb068befc
     sp = 0x0000fffd0bc5e050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffd0bc5e320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffd0bc5e080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff453fd40a4
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff4540b4b00
    x21 = 0x0000fff453fd3f00   x22 = 0x0000fff453fd40a8
     fp = 0x0000fff453fd3f00    sp = 0x0000fffd0bc5e330
     pc = 0x0000fff453fd40a8
    Found by: call frame info
 5  ld-linux-aarch64.so.1 + 0xe6fd
     sp = 0x0000fffd0bc5e360    pc = 0x0000ffffb1f26701
    Found by: stack scanning
 6  ld-linux-aarch64.so.1 + 0x10790
     sp = 0x0000fffd0bc5e370    pc = 0x0000ffffb1f28794
    Found by: stack scanning
 7  libjemalloc.so.2!je_free_default [jemalloc.c : 3020 + 0x0]
     sp = 0x0000fffd0bc5e390    pc = 0x0000ffffb1c6aecc
    Found by: stack scanning
 8  0xffffaf830b7c
    x19 = 0x303439360bc5f6f0   x20 = 0x1bbd716dcced7c00
    x21 = 0x0000fffd0bc5e4e0   x22 = 0x0000ffffafd49cd8
    x23 = 0x0000fffd0bc5e430   x24 = 0x0000000000800000
    x25 = 0x0000fffd0bc5e6f8   x26 = 0x0000fffcf55ff600
    x27 = 0x0000ffffafd555e0   x28 = 0x0000000000000000
     fp = 0x0000fffd0bc5f610    sp = 0x0000fffd0bc5e450
     pc = 0x0000ffffaf830b80
    Found by: call frame info

Thread 133
 0  libc.so.6 + 0xebd74
     x0 = 0xfffffffffffffffc    x1 = 0x0000ffffa9e03000
     x2 = 0x0000000000000010    x3 = 0x0000000000002710
     x4 = 0x0000000000000000    x5 = 0x0000000000000008
     x6 = 0x0000000000000000    x7 = 0x0000000000001e56
     x8 = 0x0000000000000016    x9 = 0x00148f6745c22ff6
    x10 = 0x00ffffffffffffff   x11 = 0x000000043af4a90d
    x12 = 0x0000000000010000   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e0   x15 = 0x0000ffffa9e11f80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbbed0
    x18 = 0x000000000000003f   x19 = 0x0000000000000005
    x20 = 0x0000000000002710   x21 = 0x0000000000000010
    x22 = 0x0000000000000000   x23 = 0x0000ffffa9e03000
    x24 = 0x0000ffffaaf5c580   x25 = 0x0000ffffaa750000
    x26 = 0x0000ffffafe80000   x27 = 0x0000ffffb0207040
    x28 = 0x0000ffffaa750000    fp = 0x0000ffffaaf5c0e0
     lr = 0x0000ffffafdbbd50    sp = 0x0000ffffaaf5c0e0
     pc = 0x0000ffffafdbbd74
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xebd4c
     fp = 0x0000ffffaaf5c120    lr = 0x0000ffffb0f2c078
     sp = 0x0000ffffaaf5c0f0    pc = 0x0000ffffafdbbd50
    Found by: previous frame's frame pointer
 2  libVBSFramework.so!vbs::reactor::EPollPoller::poll(int, std::vector<vbs::reactor::Channel*, std::allocator<vbs::reactor::Channel*> >*) + 0x54
     fp = 0x0000ffffaaf5c3a0    lr = 0x0000ffffb0f2e0f0
     sp = 0x0000ffffaaf5c130    pc = 0x0000ffffb0f2c078
    Found by: previous frame's frame pointer
 3  0xffffaaf5c4f4
    x19 = 0x0000000000000000   x20 = 0x0000ffffaaf5c580
    x21 = 0x0000ffffaaf5c530   x22 = 0x0000000000000001
    x23 = 0x0000ffffaa750000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffaaf5d610   x26 = 0x0000ffffaaf5c468
     fp = 0x0000000000000000    sp = 0x0000ffffaaf5c3b0
     pc = 0x0000ffffaaf5c4f8
    Found by: call frame info

Thread 134
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffa85ad52cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d40
     x8 = 0x0000000000000062    x9 = 0x0003fd74c290c96c
    x10 = 0x00ffffffffffffff   x11 = 0x0000000419d0c60d
    x12 = 0x0000fff7fadbc5e0   x13 = 0x0000ffff8300d1e8
    x14 = 0x0000000000000008   x15 = 0x0000fffa85ad8400
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff8026ad618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fffa85ad52cc
    x22 = 0x0000fffa85ad52cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7fadbc488    fp = 0x0000fff7fadbc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7fadbc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7fadbc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7fadbc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7fadbc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7fadbc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7fadbc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7fadbc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7fadbd2d8
    x19 = 0x0000fff7fadbc6f8   x20 = 0x0000fff8026ad600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7fa5b0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff8026acec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000fffa85ad5270    fp = 0x0000fffa85b50e80
     sp = 0x0000fff7fadbc600    pc = 0x0000fff7fadbd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7fadbc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7fadbc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7fadbc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 135
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000ffffa103b000    x1 = 0x0000000000000002
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000000000002    x7 = 0x0000000000000001
     x8 = 0x0000000000000049    x9 = 0x0000000000000010
    x10 = 0x0000000000000008   x11 = 0x0000000000010000
    x12 = 0x0000000000000018   x13 = 0x0000000000000018
    x14 = 0x0000ffff7bf2d628   x15 = 0x000000000000fc88
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000ffffa103b000
    x20 = 0x0000000000000002   x21 = 0x0000000000000000
    x22 = 0x0000ffffa103b000   x23 = 0x0000000000000002
    x24 = 0x0000000000000002   x25 = 0x0000ffff8bbc0008
    x26 = 0x0000000000000002   x27 = 0x0000ffff8bbc0008
    x28 = 0x0000ffff7b720000    fp = 0x0000ffff7bf2c470
     lr = 0x0000ffffafdb0b00    sp = 0x0000ffff7bf2c450
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000ffff7bf2c4a0    lr = 0x0000ffff876e1bc0
     sp = 0x0000ffff7bf2c480    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libcuda.so.1 + 0x2c1bbc
     fp = 0x0000ffff7bf2c550    lr = 0x0000ffff8778c490
     sp = 0x0000ffff7bf2c4b0    pc = 0x0000ffff876e1bc0
    Found by: previous frame's frame pointer
 3  libcuda.so.1 + 0x36c48c
     fp = 0x0000ffff7bf2c5e0    lr = 0x0000ffff876d53ac
     sp = 0x0000ffff7bf2c560    pc = 0x0000ffff8778c490
    Found by: previous frame's frame pointer
 4  libcuda.so.1 + 0x2b53a8
     fp = 0x0000ffff7bf2c750    lr = 0x0000ffffafd5595c
     sp = 0x0000ffff7bf2c5f0    pc = 0x0000ffff876d53ac
    Found by: previous frame's frame pointer
 5  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000ffff7bf2c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 136
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fffcf63b41d0    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fffcf63b41dc    x7 = 0x0000fffcf63b41d8
     x8 = 0x0000000000000049    x9 = 0x0000fffcf63d1dc0
    x10 = 0x0000000000002fff   x11 = 0x0020ffffa0c1c581
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x0000000000067138
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fffcf63b41d0
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7f9d9c4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fffcf63cc1f0   x25 = 0x0000fff7f9d9c5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7f9d9c4c8
    x28 = 0x0000fff7f9590000    fp = 0x0000fff7f9d9c3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7f9d9c3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7f9d9c5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7f9d9c3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7f9d9c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7f9d9c600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7f9d9c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 137
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0b36100    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7f958c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7f958c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000001155668   x13 = 0x000000000115d320
    x14 = 0x0000000001155698   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff830455f0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0b36100
    x22 = 0x0000ffffa0b36100   x23 = 0x00000000000001bc
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000de   x27 = 0x0000ffffa0b360d8
    x28 = 0x0000000000000000    fp = 0x0000fff7f958c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7f958c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7f958c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7f958c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7f958c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7f958c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7f958c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7f958c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7f958c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7f958c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa0a61214
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa0a9e850    sp = 0x0000fff7f958c5d0
     pc = 0x0000ffffa0a61218
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7f958c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7f958c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fff7f958c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 138
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd75bec5e0   x13 = 0x0000ffffab7f5fd0
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000006
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000003   x27 = 0x0000000000000000
    x28 = 0x0000fffd75bec3b8    fp = 0x0000fffd75bec360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd75bec360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd75bec400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd75bec370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd75bec460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd75bec410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd75bec5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd75bec470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd75bed2d8
    x19 = 0x0000fffd75bec6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd753e0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd753e0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b170    sp = 0x0000fffd75bec600
     pc = 0x0000fffd75bed2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd75bec648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd75bec760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 139
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fff8b37ed100    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7ed91c498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7ed91c3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000002b5a108   x13 = 0x0000000002b63cf8
    x14 = 0x0000000002b5a138   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff8304d580   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fff8b37ed100
    x22 = 0x0000fff8b37ed100   x23 = 0x00000000000001a4
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x00000000000000d2   x27 = 0x0000fff8b37ed0d8
    x28 = 0x0000000000000000    fp = 0x0000fff7ed91c370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ed91c370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ed91c420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7ed91c380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7ed91c570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7ed91c430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7ed91c580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7ed91c580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7ed91c5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7ed91c590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xfff8b37b0c14
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffaa2278f0    sp = 0x0000fff7ed91c5d0
     pc = 0x0000fff8b37b0c18
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7ed91c5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7ed91c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fff7ed91c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 140
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000fff834705450    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000fff83470545c    x7 = 0x0000fff834705458
     x8 = 0x0000000000000049    x9 = 0x0000fff834722740
    x10 = 0x0000000000002fff   x11 = 0x0020ffffac45e481
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000020
    x14 = 0x0000000000002000   x15 = 0x0000000000067578
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000fff834705450
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fff7ed10c4f0   x23 = 0x00000000000186a0
    x24 = 0x0000fff83471d470   x25 = 0x0000fff7ed10c5c0
    x26 = 0x0000000000000000   x27 = 0x0000fff7ed10c4c8
    x28 = 0x0000fff7ec900000    fp = 0x0000fff7ed10c3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fff7ed10c3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fff7ed10c5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fff7ed10c3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fff7ed10c750    lr = 0x0000ffffafd5595c
     sp = 0x0000fff7ed10c600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fff7ed10c760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 141
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffcf62b5104    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7ea8bc498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7ea8bc3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x00000000000510c0   x13 = 0x0000000000055320
    x14 = 0x00000000000510f0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff7f1168f0   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000fffcf62b5104
    x22 = 0x0000fffcf62b5104   x23 = 0x00000000000001bb
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x00000000000000dd   x27 = 0x0000fffcf62b50d8
    x28 = 0x0000000000000000    fp = 0x0000fff7ea8bc370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ea8bc370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ea8bc420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7ea8bc380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7ea8bc570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7ea8bc430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7ea8bc580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7ea8bc580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7ea8bc5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7ea8bc590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa06d0414
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa07143d0    sp = 0x0000fff7ea8bc5d0
     pc = 0x0000ffffa06d0418
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7ea8bc5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7ea8bc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  dmabuf: + 0xc3fffc
     sp = 0x0000fff7ea8bc658    pc = 0x0000fff7ea0b0000
    Found by: stack scanning
 9  dmabuf: + 0xc3fffc
     sp = 0x0000fff7ea8bc670    pc = 0x0000fff7ea0b0000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fff7ea8bc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 142
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa33ed904    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fff7e37bc498
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fff7e37bc3d8
     x8 = 0x0000000000000062    x9 = 0x00107c10e1265613
    x10 = 0x00ffffffffffffff   x11 = 0x0000000474e4340d
    x12 = 0x0000000000b53bc0   x13 = 0x0000000000b620b8
    x14 = 0x0000000000b53bf0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffff7f136720   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa33ed904
    x22 = 0x0000ffffa33ed904   x23 = 0x00000000000001cf
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x00000000000000e7   x27 = 0x0000ffffa33ed8d8
    x28 = 0x0000000000000000    fp = 0x0000fff7e37bc370
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7e37bc370
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7e37bc420    lr = 0x0000ffffafd55140
     sp = 0x0000fff7e37bc380    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fff7e37bc570    lr = 0x0000ffffa3769818
     sp = 0x0000fff7e37bc430    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libnvscistream.so.1 + 0x36814
     fp = 0x0000fff7e37bc580    lr = 0x0000ffffa54acee8
     sp = 0x0000fff7e37bc580    pc = 0x0000ffffa3769818
    Found by: previous frame's frame pointer
 4  libnvstream_core_stream.so!linvs::stream::StreamEngine::HandleEvent(std::shared_ptr<linvs::block::IBlock> const&) + 0x34
     fp = 0x0000fff7e37bc5c0    lr = 0x0000ffffa54ad2a0
     sp = 0x0000fff7e37bc590    pc = 0x0000ffffa54acee8
    Found by: previous frame's frame pointer
 5  0xffffa3211e14
    x19 = 0x0000ffffa54b1000   x20 = 0x72656d75736e6f43
     fp = 0x0000ffffa323a3f0    sp = 0x0000fff7e37bc5d0
     pc = 0x0000ffffa3211e18
    Found by: call frame info
 6  libc.so.6 + 0x856c4
     sp = 0x0000fff7e37bc5f0    pc = 0x0000ffffafd556c8
    Found by: stack scanning
 7  libc.so.6 + 0x855dc
     sp = 0x0000fff7e37bc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 8  dmabuf: + 0x7a7fffc
     sp = 0x0000fff7e37bc658    pc = 0x0000fff7e2fb0000
    Found by: stack scanning
 9  dmabuf: + 0x7a7fffc
     sp = 0x0000fff7e37bc670    pc = 0x0000fff7e2fb0000
    Found by: stack scanning
10  libc.so.6 + 0xeba48
     sp = 0x0000fff7e37bc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 143
 0  libc.so.6 + 0xee324
     x0 = 0x000000000000001c    x1 = 0x0000fffd7741c398
     x2 = 0x0000000000000020    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000000000000000    x7 = 0x0000000000000000
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd7741c5e0   x13 = 0x0000ffffab7f5fb8
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe2a0
    x18 = 0x0000ffffa2dfd618   x19 = 0x000000000000001c
    x20 = 0x0000000000000000   x21 = 0x0000000000000020
    x22 = 0x0000fffd7741c398   x23 = 0x0000fffd7741c398
    x24 = 0x0000000000000000   x25 = 0x8fafd21e25c5e09b
    x26 = 0xb2ab117a257edf0d   x27 = 0x0000fffd7741c398
    x28 = 0x0000fffd7741be38    fp = 0x0000fffd7741bd10
     lr = 0x0000ffffafdbe304    sp = 0x0000fffd7741bd10
     pc = 0x0000ffffafdbe324
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee300
     fp = 0x0000fffd7741bd40    lr = 0x0000ffffb0d3ffdc
     sp = 0x0000fffd7741bd20    pc = 0x0000ffffafdbe304
    Found by: previous frame's frame pointer
 2  liberpc.so.1!boost::asio::detail::socket_ops::sync_recv1(int, unsigned char, void*, unsigned long, int, boost::system::error_code&) + 0xa8
     fp = 0x0000fffd7741bde0    lr = 0x0000ffffb0d2c1a8
     sp = 0x0000fffd7741bd50    pc = 0x0000ffffb0d3ffdc
    Found by: previous frame's frame pointer

Thread 144
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd74bcc5e0   x13 = 0x0000ffffab7f5fe0
    x14 = 0x0000000000000008   x15 = 0x0000000000005fe8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x000000000000000a
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000005   x27 = 0x0000000000000000
    x28 = 0x0000fffd74bcc3b8    fp = 0x0000fffd74bcc360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd74bcc360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd74bcc400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd74bcc370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd74bcc460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd74bcc410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd74bcc5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd74bcc470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd74bcd2d8
    x19 = 0x0000fffd74bcc6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd743c0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd743c0000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b190    sp = 0x0000fffd74bcc600
     pc = 0x0000fffd74bcd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd74bcc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd74bcc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 145
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd7339c5e0   x13 = 0x0000ffffab7f5f78
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000000000000000
    x28 = 0x0000fffd7339c3b8    fp = 0x0000fffd7339c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd7339c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd7339c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd7339c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd7339c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd7339c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd7339c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd7339c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd7339d2d8
    x19 = 0x0000fffd7339c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd72b90000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd72b90000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1c0    sp = 0x0000fffd7339c600
     pc = 0x0000fffd7339d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd7339c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd7339c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 146
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd7237c5e0   x13 = 0x0000ffffab7f5f88
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000014
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000a   x27 = 0x0000000000000000
    x28 = 0x0000fffd7237c3b8    fp = 0x0000fffd7237c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd7237c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd7237c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd7237c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd7237c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd7237c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd7237c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd7237c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd7237d2d8
    x19 = 0x0000fffd7237c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd71b70000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd71b70000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b1e0    sp = 0x0000fffd7237c600
     pc = 0x0000fffd7237d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd7237c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd7237c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 147
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0ff14a0    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffd7135c5e0   x13 = 0x0000ffffab7f5f98
    x14 = 0x0000000000000008   x15 = 0x0000ffffa1030e80
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffa2dfd618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0ff14a0
    x22 = 0x0000ffffa0ff14a0   x23 = 0x0000000000000018
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000000c   x27 = 0x0000000000000000
    x28 = 0x0000fffd7135c3b8    fp = 0x0000fffd7135c360
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd7135c360
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd7135c400    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd7135c370    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd7135c460    lr = 0x0000ffffb0d2958c
     sp = 0x0000fffd7135c410    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  liberpc.so.1!boost::asio::detail::posix_thread::func<boost::asio::thread_pool::thread_function>::run() + 0x1d8
     fp = 0x0000fffd7135c5f0    lr = 0x0000ffffb0d244dc
     sp = 0x0000fffd7135c470    pc = 0x0000ffffb0d2958c
    Found by: previous frame's frame pointer
 4  0xfffd7135d2d8
    x19 = 0x0000fffd7135c6f8   x20 = 0x0000ffffa2dfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffd70b50000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffa2dfcec0   x26 = 0x0000fffd70b50000
    x27 = 0x0000000000000000   x28 = 0x0000ffffb0d80640
     fp = 0x0000ffffa103b200    sp = 0x0000fffd7135c600
     pc = 0x0000fffd7135d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd7135c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd7135c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 148
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000069    x1 = 0x0000ffffab91ce00
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffff7df6c160    x5 = 0x0000ffff7df6c110
     x6 = 0x0000000000000000    x7 = 0x000000000000000b
     x8 = 0x00000000000000cf    x9 = 0x0000000000000006
    x10 = 0x0000ffff7df6d610   x11 = 0x0000000000000060
    x12 = 0x00000000000252a0   x13 = 0x0000000000030020
    x14 = 0x0000000000025300   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff86177670   x19 = 0x0000000000000069
    x20 = 0x0000000000000000   x21 = 0x0000ffff7df6c110
    x22 = 0x0000ffff7df6c160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab91ce00   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000069
    x28 = 0x0000ffff7d760000    fp = 0x0000ffff7df6c040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffff7df6c040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffff7df6c080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffff7df6c050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffff7df6c2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffff7df6c090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffff7df6c3dc
    x19 = 0x0000ffffab8a26f0   x20 = 0x0000ffffab8a26dc
    x21 = 0x0000ffff7df6c5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffff7d760000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff7d760000
    x27 = 0x0000ffff7df6c110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a26c0    sp = 0x0000ffff7df6c300
     pc = 0x0000ffff7df6c3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffff7df6c328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libjemalloc.so.2!je_tsd_cleanup [tsd.c : 382 + 0x4]
     sp = 0x0000ffff7df6c3a0    pc = 0x0000ffffb1cd9638
    Found by: stack scanning
 6  0x1bbd716dcced7bfc
    x19 = 0x0000ffff7df6c4e0   x20 = 0x0000ffffafd49cd8
    x21 = 0x0000ffff7df6c430    fp = 0x313332357df6d610
     sp = 0x0000ffff7df6c3d0    pc = 0x1bbd716dcced7c00
    Found by: call frame info
 7  libc.so.6 + 0x79cec
     sp = 0x0000ffff7df6c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 8  libjemalloc.so.2!je_pac_maybe_decay_purge [pac.c : 507 + 0xc]
     sp = 0x0000ffff7df6c460    pc = 0x0000ffffb1cbe51c
    Found by: stack scanning
 9  0xffffa4e03a34
    x19 = 0x0000000000000011   x20 = 0x0000ffff7df6d610
    x21 = 0x0000ffff7df6c4f0   x22 = 0x0000000000000007
    x23 = 0x000000002f47c79c   x24 = 0x1bbd716dcced7c00
    x25 = 0x0000ffff7df6c500   x26 = 0x0000ffffb1c77948
     fp = 0x0000ffffa4e11f40    sp = 0x0000ffff7df6c4c0
     pc = 0x0000ffffa4e03a38
    Found by: call frame info
10  libc.so.6 + 0x556e0
     sp = 0x0000ffff7df6c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
11  libc.so.6 + 0x856b4
     sp = 0x0000ffff7df6c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
12  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffff7df6c5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
13  0x43f7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffff7df6c5e0
     pc = 0x000043f800000001
    Found by: call frame info
14  libc.so.6 + 0x855dc
     sp = 0x0000ffff7df6c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
15  libc.so.6 + 0xeba48
     sp = 0x0000ffff7df6c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 149
 0  libc.so.6 + 0xee418
     x0 = 0x0000000000000068    x1 = 0x0000ffffab8fb3c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffff7d75c160    x5 = 0x0000ffff7d75c110
     x6 = 0x0000000000000000    x7 = 0x0000ffff7d75c260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffff7d75c5e0   x13 = 0x0000000000000018
    x14 = 0x0000000000000008   x15 = 0x0000000000a02740
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000000000ae56c0   x19 = 0x0000000000000068
    x20 = 0x0000000000000000   x21 = 0x0000ffff7d75c110
    x22 = 0x0000ffff7d75c160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab8fb3c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x0000000000000068
    x28 = 0x0000ffff7cf50000    fp = 0x0000ffff7d75c040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffff7d75c040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffff7d75c080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffff7d75c050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffff7d75c2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffff7d75c090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffff7d75c3dc
    x19 = 0x0000ffffab8a2610   x20 = 0x0000ffffab8a25fc
    x21 = 0x0000ffff7d75c5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffff7cf50000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff7cf50000
    x27 = 0x0000ffff7d75c110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a25e0    sp = 0x0000ffff7d75c300
     pc = 0x0000ffff7d75c3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffff7d75c328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libjemalloc.so.2!je_tsd_cleanup [tsd.c : 382 + 0x4]
     sp = 0x0000ffff7d75c3a0    pc = 0x0000ffffb1cd9638
    Found by: stack scanning
 6  0x1bbd716dcced7bfc
    x19 = 0x0000ffff7d75c4e0   x20 = 0x0000ffffafd49cd8
    x21 = 0x0000ffff7d75c430    fp = 0x303332357d75d610
     sp = 0x0000ffff7d75c3d0    pc = 0x1bbd716dcced7c00
    Found by: call frame info
 7  libc.so.6 + 0x79cec
     sp = 0x0000ffff7d75c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 8  libjemalloc.so.2!je_pac_maybe_decay_purge [pac.c : 507 + 0xc]
     sp = 0x0000ffff7d75c460    pc = 0x0000ffffb1cbe51c
    Found by: stack scanning
 9  0xffffa4a03a34
    x19 = 0x000000000000000b   x20 = 0x0000ffff7d75d610
    x21 = 0x0000ffff7d75c4f0   x22 = 0x0000000000000007
    x23 = 0x000000002f47c79c   x24 = 0x1bbd716dcced7c00
    x25 = 0x0000ffff7d75c500   x26 = 0x0000ffffb1c77948
     fp = 0x0000ffffa4a11f40    sp = 0x0000ffff7d75c4c0
     pc = 0x0000ffffa4a03a38
    Found by: call frame info
10  libc.so.6 + 0x556e0
     sp = 0x0000ffff7d75c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
11  libc.so.6 + 0x856b4
     sp = 0x0000ffff7d75c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
12  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffff7d75c5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
13  0x43f7fffffffd
    x19 = 0x0000000000000000   x20 = 0x0100ffef00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffff7d75c5e0
     pc = 0x000043f800000001
    Found by: call frame info
14  libc.so.6 + 0x855dc
     sp = 0x0000ffff7d75c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
15  libc.so.6 + 0xeba48
     sp = 0x0000ffff7d75c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 150
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000006a    x1 = 0x0000ffffab92de00
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffffa47fc160    x5 = 0x0000ffffa47fc110
     x6 = 0x0000000000000000    x7 = 0x0000ffffa47fc260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffa47fc5e0   x13 = 0x0000ffffab80e290
    x14 = 0x0000000000000008   x15 = 0x0000000000b09be0
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff9f6a4618   x19 = 0x000000000000006a
    x20 = 0x0000000000000000   x21 = 0x0000ffffa47fc110
    x22 = 0x0000ffffa47fc160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab92de00   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000006a
    x28 = 0x0000ffffa3ff0000    fp = 0x0000ffffa47fc040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffffa47fc040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffffa47fc080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffffa47fc050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffffa47fc2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffffa47fc090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffffa47fc3dc
    x19 = 0x0000ffffab8a27d0   x20 = 0x0000ffffab8a27bc
    x21 = 0x0000ffffa47fc5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffffa3ff0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffffa3ff0000
    x27 = 0x0000ffffa47fc110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a27a0    sp = 0x0000ffffa47fc300
     pc = 0x0000ffffa47fc3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffffa47fc328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000ffffa47fc3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000ffffa47fc410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000ffffa47fc4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000ffffa47fc560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffffa47fc5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4425fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffffa47fc5e0
     pc = 0x0000442600000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000ffffa47fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000ffffa47fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 151
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000006c    x1 = 0x0000ffffab94f6c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffffa25ec160    x5 = 0x0000ffffa25ec110
     x6 = 0x0000000000000000    x7 = 0x0000ffffa25ec260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffa25ec5e0   x13 = 0x0000ffffab80e2a0
    x14 = 0x0000000000000008   x15 = 0x000000000000b988
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff9f6a4618   x19 = 0x000000000000006c
    x20 = 0x0000000000000000   x21 = 0x0000ffffa25ec110
    x22 = 0x0000ffffa25ec160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab94f6c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000006c
    x28 = 0x0000ffffa1de0000    fp = 0x0000ffffa25ec040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffffa25ec040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffffa25ec080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffffa25ec050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffffa25ec2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffffa25ec090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffffa25ec3dc
    x19 = 0x0000ffffab8a2990   x20 = 0x0000ffffab8a297c
    x21 = 0x0000ffffa25ec5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffffa1de0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffffa1de0000
    x27 = 0x0000ffffa25ec110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a2960    sp = 0x0000ffffa25ec300
     pc = 0x0000ffffa25ec3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffffa25ec328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000ffffa25ec3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000ffffa25ec410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000ffffa25ec4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000ffffa25ec560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffffa25ec5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4426fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffffa25ec5e0
     pc = 0x0000442700000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000ffffa25ec648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000ffffa25ec760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 152
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000006b    x1 = 0x0000ffffab93e100
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffffa2dfc160    x5 = 0x0000ffffa2dfc110
     x6 = 0x0000000000000000    x7 = 0x0000ffffa2dfc260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffa2dfc5e0   x13 = 0x0000ffffab80e298
    x14 = 0x0000000000000008   x15 = 0x0000000000b1a1a8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff9f6a4618   x19 = 0x000000000000006b
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2dfc110
    x22 = 0x0000ffffa2dfc160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab93e100   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000006b
    x28 = 0x0000ffffa25f0000    fp = 0x0000ffffa2dfc040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffffa2dfc040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffffa2dfc080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffffa2dfc050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffffa2dfc2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffffa2dfc090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffffa2dfc3dc
    x19 = 0x0000ffffab8a28b0   x20 = 0x0000ffffab8a289c
    x21 = 0x0000ffffa2dfc5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffffa25f0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffffa25f0000
    x27 = 0x0000ffffa2dfc110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a2880    sp = 0x0000ffffa2dfc300
     pc = 0x0000ffffa2dfc3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffffa2dfc328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000ffffa2dfc3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000ffffa2dfc410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000ffffa2dfc4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000ffffa2dfc560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffffa2dfc5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4425fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffffa2dfc5e0
     pc = 0x0000442600000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000ffffa2dfc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000ffffa2dfc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 153
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffff83001e30    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffff86c0c260
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffff86c0c5e0   x13 = 0x0000ffffab80e2d8
    x14 = 0x0000000000000008   x15 = 0x0000000000a230e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff9f6a4618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffff83001e30
    x22 = 0x0000ffff83001e30   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000ffff86c0c248    fp = 0x0000ffff86c0c1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff86c0c1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff86c0c290    lr = 0x0000ffffafd54b20
     sp = 0x0000ffff86c0c200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000ffff86c0c2f0    lr = 0x0000ffffb0bd4b88
     sp = 0x0000ffff86c0c2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerAsyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000ffff86c0c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff86c0c300    pc = 0x0000ffffb0bd4b88
    Found by: previous frame's frame pointer
 4  0xffff86c0d2d8
    x19 = 0x0000ffff86c0c6f8   x20 = 0x0000ffff9f6a4600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000ffff86400000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffff86400000
    x27 = 0x0000000000000000   x28 = 0x0000ffff83001e08
     fp = 0x0000ffff7f2d98a0    sp = 0x0000ffff86c0c600
     pc = 0x0000ffff86c0d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff86c0c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff86c0c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 154
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000006d    x1 = 0x0000ffffab9602c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000ffffa19fc160    x5 = 0x0000ffffa19fc110
     x6 = 0x0000000000000000    x7 = 0x0000ffffa19fc260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000ffffa19fc5e0   x13 = 0x0000ffffab80e2a8
    x14 = 0x0000000000000008   x15 = 0x0000000000b41d20
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000ffff9f6a4618   x19 = 0x000000000000006d
    x20 = 0x0000000000000000   x21 = 0x0000ffffa19fc110
    x22 = 0x0000ffffa19fc160   x23 = 0x000000000000ffdc
    x24 = 0x0000ffffab9602c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000006d
    x28 = 0x0000ffffa11f0000    fp = 0x0000ffffa19fc040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000ffffa19fc040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000ffffa19fc080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000ffffa19fc050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000ffffa19fc2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000ffffa19fc090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xffffa19fc3dc
    x19 = 0x0000ffffab8a2a70   x20 = 0x0000ffffab8a2a5c
    x21 = 0x0000ffffa19fc5c8   x22 = 0x0000000000000000
    x23 = 0x0000ffffa11f0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff9f6a3ec0   x26 = 0x0000ffffa11f0000
    x27 = 0x0000ffffa19fc110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffab8a2a40    sp = 0x0000ffffa19fc300
     pc = 0x0000ffffa19fc3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000ffffa19fc328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000ffffa19fc3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000ffffa19fc410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000ffffa19fc4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000ffffa19fc560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000ffffa19fc5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4426fffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000ffffa19fc5e0
     pc = 0x0000442700000001
    Found by: call frame info
11  libc.so.6 + 0x855dc
     sp = 0x0000ffffa19fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
12  libc.so.6 + 0xeba48
     sp = 0x0000ffffa19fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 155
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3208470    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000ffff9f6a35d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffff9f6a34c8
     x8 = 0x0000000000000062    x9 = 0x0018973cc2a3a2ca
    x10 = 0x00ffffffffffffff   x11 = 0x00000004497fce0d
    x12 = 0x0000800000000000   x13 = 0x0000000000000100
    x14 = 0x000000001010b000   x15 = 0x000000002010b000
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000000000000013   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3208470
    x22 = 0x0000ffffa3208470   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000ffffa3208448
    x28 = 0x0000000000000000    fp = 0x0000ffff9f6a3460
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff9f6a3460
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff9f6a3510    lr = 0x0000ffffafd55140
     sp = 0x0000ffff9f6a3470    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000ffff9f6a3570    lr = 0x0000ffffb191b580
     sp = 0x0000ffff9f6a3520    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xcc
     fp = 0x0000ffff9f6a35f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000ffff9f6a3580    pc = 0x0000ffffb191b580
    Found by: previous frame's frame pointer
 4  0xffff9f6a42d8
    x19 = 0x0000ffff9f6a36f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000ffff9ee97000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000ffff9ee97000
    x27 = 0x6563696e202c333d   x28 = 0x0000000000000010
     fp = 0x0000ffffaf8314e0    sp = 0x0000ffff9f6a3600
     pc = 0x0000ffff9f6a42dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000ffff9f6a3648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000ffff9f6a3760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 156
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaa601030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000ffff8250c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001f74
     x8 = 0x0000000000000062    x9 = 0x0007af83781dd629
    x10 = 0x00ffffffffffffff   x11 = 0x000000045ccfa70d
    x12 = 0x00000000000021c0   x13 = 0x0000000000010000
    x14 = 0x0000000000002280   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffffab9cba10   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffaa601030
    x22 = 0x0000ffff8250c018   x23 = 0x0000ffff8250c5b8
    x24 = 0x0000ffff8250c5a8   x25 = 0x0000ffff8250c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000ffff9f6a3ec0
    x28 = 0x0000ffff81d00000    fp = 0x0000ffff8250bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000ffff8250bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000ffff8250c040    lr = 0x0000ffffafd5e028
     sp = 0x0000ffff8250bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000ffff8250c070    lr = 0x0000ffffb068befc
     sp = 0x0000ffff8250c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000ffff8250c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000ffff8250c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffff7f1fd9a4
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffaf850800
    x21 = 0x0000ffff7f1fd800   x22 = 0x0000ffff7f1fd9a8
     fp = 0x0000ffff7f1fd800    sp = 0x0000ffff8250c330
     pc = 0x0000ffff7f1fd9a8
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000ffff8250c360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000ffff8250c420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 157
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06a8c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd29c60610   x11 = 0x0000000000000030
    x12 = 0x0000000000001a10   x13 = 0x0000000000010000
    x14 = 0x0000000000001a40   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f17b5e8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06a8c
    x22 = 0x0000ffffa2e06a8c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd29c5f478    fp = 0x0000fffd29c5f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd29c5f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd29c5f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd29c5f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd29c5f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd29c5f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd29c5f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd29c5f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd29c602d8
    x19 = 0x0000fffd29c5f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd29453000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd29453000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa333bac0
     fp = 0x0000ffff7f2e98a0    sp = 0x0000fffd29c5f600
     pc = 0x0000fffd29c602dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd29c5f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd29c5f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 158
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06b4c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000520
     x8 = 0x0000000000000062    x9 = 0x0000fffd27410958
    x10 = 0x00000000000002c0   x11 = 0x0000000000000000
    x12 = 0x0000ffffa081e828   x13 = 0x0000000000000000
    x14 = 0x000000000030300b   x15 = 0x000000001030300b
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06b4c
    x22 = 0x0000ffffa2e06b4c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd2740f478    fp = 0x0000fffd2740f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2740f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2740f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd2740f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd2740f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd2740f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd2740f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2740f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd274102d8
    x19 = 0x0000fffd2740f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd26c03000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd26c03000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa066c2c0
     fp = 0x0000ffffab857800    sp = 0x0000fffd2740f600
     pc = 0x0000fffd274102dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2740f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2740f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 159
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06470    x1 = 0x0000000000000089
     x2 = 0x0000000000000000    x3 = 0x0000fffd2944f5d8
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd2944f4c8
     x8 = 0x0000000000000062    x9 = 0x001878b83f8a2dca
    x10 = 0x00ffffffffffffff   x11 = 0x000000044942c50d
    x12 = 0x0000fffd2944f5e0   x13 = 0x0000ffffaf8052d0
    x14 = 0x0000000000000008   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54f60
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000089
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06470
    x22 = 0x0000ffffa2e06470   x23 = 0x0000000000000010
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000008   x27 = 0x0000ffffa2e06448
    x28 = 0x0000000000000000    fp = 0x0000fffd2944f460
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd2944f460
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd2944f510    lr = 0x0000ffffafd55140
     sp = 0x0000fffd2944f470    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8513c
     fp = 0x0000fffd2944f570    lr = 0x0000ffffb191b580
     sp = 0x0000fffd2944f520    pc = 0x0000ffffafd55140
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::TaskExecutor::GetCreator(lios::concurrent::TaskDefine const&)::{lambda()#2}> > >::_M_run() + 0xcc
     fp = 0x0000fffd2944f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd2944f580    pc = 0x0000ffffb191b580
    Found by: previous frame's frame pointer
 4  0xfffd294502d8
    x19 = 0x0000fffd2944f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd28c43000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd28c43000
    x27 = 0x69645f656d69745f   x28 = 0x0000000000000010
     fp = 0x0000ffffaf831c00    sp = 0x0000fffd2944f600
     pc = 0x0000fffd294502dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd2944f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd2944f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 160
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06b7c    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd26c00610   x11 = 0x0000000000000030
    x12 = 0x00000000000006f0   x13 = 0x0000000000010000
    x14 = 0x0000000000000720   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f1c45f8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06b7c
    x22 = 0x0000ffffa2e06b7c   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd26bff478    fp = 0x0000fffd26bff420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd26bff420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd26bff4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd26bff430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd26bff520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd26bff4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd26bff5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd26bff530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd26c002d8
    x19 = 0x0000fffd26bff6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd263f3000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd263f3000
    x27 = 0x0000000000000000   x28 = 0x0000fff5d28f1c00
     fp = 0x0000ffffab857a60    sp = 0x0000fffd26bff600
     pc = 0x0000fffd26c002dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd26bff648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd26bff760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 161
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06c38    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd23ba0610   x11 = 0x0000000000000030
    x12 = 0x0000000000065b00   x13 = 0x0000000000071b50
    x14 = 0x0000000000065b30   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff8633a4c0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06c38
    x22 = 0x0000ffffa2e06c38   x23 = 0x0000000000000114
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x000000000000008a   x27 = 0x0000000000000000
    x28 = 0x0000fffd23b9f478    fp = 0x0000fffd23b9f420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd23b9f420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd23b9f4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd23b9f430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd23b9f520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd23b9f4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd23b9f5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd23b9f530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd23ba02d8
    x19 = 0x0000fffd23b9f6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd23393000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd23393000
    x27 = 0x0000000000000000   x28 = 0x0000fffa84432780
     fp = 0x0000ffffab884440    sp = 0x0000fffd23b9f600
     pc = 0x0000fffd23ba02dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd23b9f648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd23b9f760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 162
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06d58    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000005
     x8 = 0x0000000000000062    x9 = 0x0000000000000003
    x10 = 0x0000fffd1fa5f610   x11 = 0x0000000000000030
    x12 = 0x00000000000175b0   x13 = 0x0000000000027460
    x14 = 0x00000000000175e0   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff7f15b900   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06d58
    x22 = 0x0000ffffa2e06d58   x23 = 0x0000000000000004
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000000000000000
    x28 = 0x0000fffd1fa5e478    fp = 0x0000fffd1fa5e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1fa5e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1fa5e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1fa5e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1fa5e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1fa5e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1fa5e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1fa5e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1fa5f2d8
    x19 = 0x0000fffd1fa5e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1f252000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd1f252000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa0728440
     fp = 0x0000ffffab821760    sp = 0x0000fffd1fa5e600
     pc = 0x0000fffd1fa5f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1fa5e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1fa5e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 163
 0  libc.so.6 + 0xe0b1c
     x0 = 0x0000ffffa2f76dd0    x1 = 0x0000000000000004
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x0000000000000000
     x6 = 0x0000ffffa2f76ddc    x7 = 0x0000ffffa2f76dd8
     x8 = 0x0000000000000049    x9 = 0x0000ffffa2f93dc0
    x10 = 0x0000000000002fff   x11 = 0x0004ffffaa016b81
    x12 = 0x0000ffffb1f16b00   x13 = 0x0000000000000004
    x14 = 0x0000000000000040   x15 = 0x00000000000072e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdb0a20
    x18 = 0x0000000000000018   x19 = 0x0000ffffa2f76dd0
    x20 = 0x0000000000000004   x21 = 0x0000000000000000
    x22 = 0x0000fffd1f24e4f0   x23 = 0x00000000000186a0
    x24 = 0x0000ffffa2f8edf0   x25 = 0x0000fffd1f24e5c0
    x26 = 0x0000000000000000   x27 = 0x0000fffd1f24e4c8
    x28 = 0x0000fffd1ea42000    fp = 0x0000fffd1f24e3d0
     lr = 0x0000ffffafdb0b00    sp = 0x0000fffd1f24e3b0
     pc = 0x0000ffffafdb0b1c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xe0afc
     fp = 0x0000fffd1f24e5f0    lr = 0x0000ffffa37118f4
     sp = 0x0000fffd1f24e3e0    pc = 0x0000ffffafdb0b00
    Found by: previous frame's frame pointer
 2  libnvscievent.so + 0x18f0
     fp = 0x0000fffd1f24e750    lr = 0x0000ffffafd5595c
     sp = 0x0000fffd1f24e600    pc = 0x0000ffffa37118f4
    Found by: previous frame's frame pointer
 3  libc.so.6 + 0x85958
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fffd1f24e760    pc = 0x0000ffffafd5595c
    Found by: previous frame's frame pointer

Thread 164
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c17d8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffd1c9fe2a0
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00ffffffffffffff   x11 = 0x000000026b78510d
    x12 = 0x0000fffd1c9fe5e0   x13 = 0x0000ffffaf8052e0
    x14 = 0x0000000000000008   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb0207798   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c17d8
    x22 = 0x0000ffffaf9c17d8   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffd1c9fe4d8    fp = 0x0000fffd1c9fe480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1c9fe480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1c9fe520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1c9fe490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1c9fe580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd1c9fe530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd1c9fe5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1c9fe590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd1c9ff2d8
    x19 = 0x0000fffd1c9fe6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd1c1f2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffffab9b8f18
     fp = 0x0000ffffaf831700    sp = 0x0000fffd1c9fe600
     pc = 0x0000fffd1c9ff2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1c9fe648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1c9fe760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 165
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa2e06e48    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000003a7
     x8 = 0x0000000000000062    x9 = 0x0000fffd1714f958
    x10 = 0x0000000000000aa0   x11 = 0x0000010000000000
    x12 = 0x0000090000000000   x13 = 0x0000000000000100
    x14 = 0x0000000010303003   x15 = 0x0000000020303003
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa2e06e48
    x22 = 0x0000ffffa2e06e48   x23 = 0x0000000000000004
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000002   x27 = 0x0000000000000000
    x28 = 0x0000fffd1714e478    fp = 0x0000fffd1714e420
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd1714e420
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd1714e4c0    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd1714e430    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd1714e520    lr = 0x0000ffffb191d9bc
     sp = 0x0000fffd1714e4d0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskExecutor::ExecutorWorker(int) + 0x138
     fp = 0x0000fffd1714e5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd1714e530    pc = 0x0000ffffb191d9bc
    Found by: previous frame's frame pointer
 4  0xfffd1714f2d8
    x19 = 0x0000fffd1714e6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd16942000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffffb0207040   x26 = 0x0000fffd16942000
    x27 = 0x0000000000000000   x28 = 0x0000ffffa9efa000
     fp = 0x0000ffffab85a100    sp = 0x0000fffd1714e600
     pc = 0x0000fffd1714f2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd1714e648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffd1714e760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 166
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf9c26dc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x00000000000008a5
     x8 = 0x0000000000000062    x9 = 0x0000fffd120af958
    x10 = 0x0000000000000240   x11 = 0x0000000000000000
    x12 = 0x0000ffffa52471a8   x13 = 0x0000000000000000
    x14 = 0x0000000000a03004   x15 = 0x0000000010a03004
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffffb1cf8424   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf9c26dc
    x22 = 0x0000ffffaf9c26dc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffd120ae4d8    fp = 0x0000fffd120ae480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffd120ae480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffd120ae520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffd120ae490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffd120ae580    lr = 0x0000ffffb1912a30
     sp = 0x0000fffd120ae530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::TaskCreator::ExclusiveWorker() + 0xec
     fp = 0x0000fffd120ae5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffd120ae590    pc = 0x0000ffffb1912a30
    Found by: previous frame's frame pointer
 4  0xfffd120af2d8
    x19 = 0x0000fffd120ae6f8   x20 = 0x0000ffffb0207780
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fffd118a2000   x24 = 0x0000ffffafe80000
    x25 = 0x69645f656d69745f   x26 = 0x0000ffff8613fe18
     fp = 0x0000ffffaf831800    sp = 0x0000fffd120ae600
     pc = 0x0000fffd120af2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffd120ae648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  dmabuf: + 0x9ffffc
     sp = 0x0000fffd120ae658    pc = 0x0000fffd118a2000
    Found by: stack scanning
 7  dmabuf: + 0x9ffffc
     sp = 0x0000fffd120ae670    pc = 0x0000fffd118a2000
    Found by: stack scanning
 8  libc.so.6 + 0xeba48
     sp = 0x0000fffd120ae760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 167
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa322d3b4    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000ffffa32370e8
     x8 = 0x0000000000000062    x9 = 0x0000ffffa32370c8
    x10 = 0x0000ffffa32370b8   x11 = 0x0000ffffa3237108
    x12 = 0x0000ffffa32370f8   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e3   x15 = 0x0000ffff82ef4550
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x00000000000000c8   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa322d3b4
    x22 = 0x0000ffffa322d3b4   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fffcf740e4d8    fp = 0x0000fffcf740e480
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffcf740e480
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffcf740e520    lr = 0x0000ffffafd54b20
     sp = 0x0000fffcf740e490    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffcf740e750    lr = 0x0000fffd69f9c118
     sp = 0x0000fffcf740e530    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libopencv_core.so.4.3!cv::WorkerThread::thread_body() + 0x114
     fp = 0x0000000000000000    lr = 0x0000ffffafdbba4c
     sp = 0x0000fffcf740e760    pc = 0x0000fffd69f9c118
    Found by: previous frame's frame pointer

Thread 168
 0  libc.so.6 + 0xee418
     x0 = 0x000000000000009b    x1 = 0x0000fffcd28595c0
     x2 = 0x000000000000ffdc    x3 = 0x0000000000000000
     x4 = 0x0000fffa8795c160    x5 = 0x0000fffa8795c110
     x6 = 0x0000000000000000    x7 = 0x0000fffa8795c260
     x8 = 0x00000000000000cf    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffa8795c5e0   x13 = 0x0000ffffab7ee230
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafdbe390
    x18 = 0x0000fffd1b1cf618   x19 = 0x000000000000009b
    x20 = 0x0000000000000000   x21 = 0x0000fffa8795c110
    x22 = 0x0000fffa8795c160   x23 = 0x000000000000ffdc
    x24 = 0x0000fffcd28595c0   x25 = 0x0000ffffb0cc0070
    x26 = 0x000000000000001c   x27 = 0x000000000000009b
    x28 = 0x0000fffa87150000    fp = 0x0000fffa8795c040
     lr = 0x0000ffffafdbe3f8    sp = 0x0000fffa8795c040
     pc = 0x0000ffffafdbe418
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0xee3f4
     fp = 0x0000fffa8795c080    lr = 0x0000ffffb0a1ccf8
     sp = 0x0000fffa8795c050    pc = 0x0000ffffafdbe3f8
    Found by: previous frame's frame pointer
 2  libertps.so.1!evbs::edds::rtps::UDPChannelResource::Receive(unsigned char*, unsigned int, unsigned int&, vbsutil::xmlparser::Locator_t&) + 0xc4
     fp = 0x0000fffa8795c2f0    lr = 0x0000ffffb0a1d3d4
     sp = 0x0000fffa8795c090    pc = 0x0000ffffb0a1ccf8
    Found by: previous frame's frame pointer
 3  0xfffa8795c3dc
    x19 = 0x0000ffffa2e2a9d0   x20 = 0x0000ffffa2e2a9bc
    x21 = 0x0000fffa8795c5c8   x22 = 0x0000000000000000
    x23 = 0x0000fffa87150000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffa87150000
    x27 = 0x0000fffa8795c110   x28 = 0x000000000000ffdc
     fp = 0x0000ffffa2e2a9a0    sp = 0x0000fffa8795c300
     pc = 0x0000fffa8795c3e0
    Found by: call frame info
 4  libc.so.6 + 0x855dc
     sp = 0x0000fffa8795c328    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fffa8795c3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x79cec
     sp = 0x0000fffa8795c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 7  libc.so.6 + 0x556e0
     sp = 0x0000fffa8795c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
 8  libc.so.6 + 0x856b4
     sp = 0x0000fffa8795c560    pc = 0x0000ffffafd556b8
    Found by: stack scanning
 9  libstdc++.so.6!std::error_code::default_error_condition() const + 0x3c
     sp = 0x0000fffa8795c5c0    pc = 0x0000ffffb0061ae0
    Found by: stack scanning
10  0x4ceefffffffd
    x19 = 0x0000000000000000   x20 = 0x50031fac00000000
     fp = 0x0000000200fa1ce8    sp = 0x0000fffa8795c5e0
     pc = 0x00004cef00000001
    Found by: call frame info
11  model.tar + 0x4c582ffc
     sp = 0x0000fffa8795c5e8    pc = 0x0000fffa00000000
    Found by: stack scanning
12  libc.so.6 + 0x855dc
     sp = 0x0000fffa8795c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  libc.so.6 + 0xeba48
     sp = 0x0000fffa8795c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 169
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffcd26b9230    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000fffa8543c260
     x8 = 0x0000000000000062    x9 = 0x0000000000000000
    x10 = 0x00000000ffffff80   x11 = 0x00000000ffffffd8
    x12 = 0x0000fffa8543c5e0   x13 = 0x0000ffffab7ee260
    x14 = 0x0000000000000008   x15 = 0x000000000000e2e8
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd1b1cf618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fffcd26b9230
    x22 = 0x0000fffcd26b9230   x23 = 0x0000000000000000
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000000   x27 = 0x0000000000000000
    x28 = 0x0000fffa8543c248    fp = 0x0000fffa8543c1f0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffa8543c1f0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffa8543c290    lr = 0x0000ffffafd54b20
     sp = 0x0000fffa8543c200    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fffa8543c2f0    lr = 0x0000ffffb0bd4b88
     sp = 0x0000fffa8543c2a0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libertps.so.1!evbs::edds::rtps::FlowControllerImpl<evbs::edds::rtps::FlowControllerAsyncPublishMode, evbs::edds::rtps::FlowControllerFifoSchedule>::run() + 0x164
     fp = 0x0000fffa8543c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fffa8543c300    pc = 0x0000ffffb0bd4b88
    Found by: previous frame's frame pointer
 4  0xfffa8543d2d8
    x19 = 0x0000fffa8543c6f8   x20 = 0x0000fffd1b1cf600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fffa84c30000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd1b1ceec0   x26 = 0x0000fffa84c30000
    x27 = 0x0000000000000000   x28 = 0x0000fffcd26b9208
     fp = 0x0000fffcd26c4820    sp = 0x0000fffa8543c600
     pc = 0x0000fffa8543d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fffa8543c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fffa8543c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 170
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3603030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fffccf5ce120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002044
     x8 = 0x0000000000000062    x9 = 0x0014155c4880c05e
    x10 = 0x00ffffffffffffff   x11 = 0x00000004759b4f0d
    x12 = 0x00000000002824d0   x13 = 0x0000000000287c90
    x14 = 0x0000000000282690   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff861d9cd0   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3603030
    x22 = 0x0000fffccf5ce018   x23 = 0x0000fffccf5ce5b8
    x24 = 0x0000fffccf5ce5a8   x25 = 0x0000fffccf5ce598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fffccedc2000    fp = 0x0000fffccf5cdfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fffccf5cdfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fffccf5ce040    lr = 0x0000ffffafd5e028
     sp = 0x0000fffccf5cdfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fffccf5ce070    lr = 0x0000ffffb068befc
     sp = 0x0000fffccf5ce050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fffccf5ce320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fffccf5ce080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xffffaa270c24
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000ffffaa241040
    x21 = 0x0000ffffaa270a80   x22 = 0x0000ffffaa270c28
     fp = 0x0000ffffaa270a80    sp = 0x0000fffccf5ce330
     pc = 0x0000ffffaa270c28
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fffccf5ce360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fffccf5ce420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 171
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1c15030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fff8b164c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000002044
     x8 = 0x0000000000000062    x9 = 0x0014155c4880c05e
    x10 = 0x00ffffffffffffff   x11 = 0x00000004759b4f0d
    x12 = 0x0000000000000c07   x13 = 0x0000000000000018
    x14 = 0x0000000000000270   x15 = 0x000000000000f768
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000ffff8625ac40   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1c15030
    x22 = 0x0000fff8b164c018   x23 = 0x0000fff8b164c5b8
    x24 = 0x0000fff8b164c5a8   x25 = 0x0000fff8b164c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fff8b0e40000    fp = 0x0000fff8b164bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b164bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b164c040    lr = 0x0000ffffafd5e028
     sp = 0x0000fff8b164bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fff8b164c070    lr = 0x0000ffffb068befc
     sp = 0x0000fff8b164c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fff8b164c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fff8b164c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff8b46bc424
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff8b4d1fb80
    x21 = 0x0000fff8b46bc280   x22 = 0x0000fff8b46bc428
     fp = 0x0000fff8b46bc280    sp = 0x0000fff8b164c330
     pc = 0x0000fff8b46bc428
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fff8b164c360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fff8b164c420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 172
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1c19030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fff8b225c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001f86
     x8 = 0x0000000000000062    x9 = 0x0008c22c631964ce
    x10 = 0x00ffffffffffffff   x11 = 0x000000045ef4f80d
    x12 = 0x0000000000000000   x13 = 0x0000fff8b225d71a
    x14 = 0x0000000000071aa0   x15 = 0x0000000000084f30
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000000000000018   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1c19030
    x22 = 0x0000fff8b225c018   x23 = 0x0000fff8b225c5b8
    x24 = 0x0000fff8b225c5a8   x25 = 0x0000fff8b225c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fff8b1a50000    fp = 0x0000fff8b225bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b225bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b225c040    lr = 0x0000ffffafd5e028
     sp = 0x0000fff8b225bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fff8b225c070    lr = 0x0000ffffb068befc
     sp = 0x0000fff8b225c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fff8b225c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fff8b225c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff9b2826124
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff9b2721cc0
    x21 = 0x0000fff9b2825f80   x22 = 0x0000fff9b2826128
     fp = 0x0000fff9b2825f80    sp = 0x0000fff8b225c330
     pc = 0x0000fff9b2826128
    Found by: call frame info
 5  libc.so.6 + 0x79cd4
     sp = 0x0000fff8b225c3c0    pc = 0x0000ffffafd49cd8
    Found by: stack scanning
 6  libc.so.6 + 0x855dc
     sp = 0x0000fff8b225c3e8    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 7  libc.so.6 + 0x79cec
     sp = 0x0000fff8b225c410    pc = 0x0000ffffafd49cf0
    Found by: stack scanning
 8  libc.so.6 + 0x8ccd0
     sp = 0x0000fff8b225c4b0    pc = 0x0000ffffafd5ccd4
    Found by: stack scanning
 9  libc.so.6 + 0x556e0
     sp = 0x0000fff8b225c4f0    pc = 0x0000ffffafd256e4
    Found by: stack scanning
10  libdsfdds.so!li::dsfdds::TopicQos::~TopicQos() + 0x260
     sp = 0x0000fff8b225c510    pc = 0x0000ffffb076a254
    Found by: stack scanning
11  0xfff8b225c58c
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff8b225c5c8
    x21 = 0x0000fff8b225c5b8   x22 = 0x1bbd716dcced7c00
    x23 = 0x0000fff8b225c5f0   x24 = 0x0000ffffb0061ae0
    x25 = 0x0000ffffaa3aa520   x26 = 0x0000fff8b225d2dc
     fp = 0x0000ffffaa3aa528    sp = 0x0000fff8b225c5c0
     pc = 0x0000fff8b225c590
    Found by: call frame info
12  libc.so.6 + 0x855dc
     sp = 0x0000fff8b225c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
13  libc.so.6 + 0xeba48
     sp = 0x0000fff8b225c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 173
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa1c13030    x1 = 0x0000000000000109
     x2 = 0x0000000000000000    x3 = 0x0000fff8b062c120
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001e66
     x8 = 0x0000000000000062    x9 = 0x0015838b5f07f180
    x10 = 0x00ffffffffffffff   x11 = 0x000000043cdcf10d
    x12 = 0x0000000000010000   x13 = 0x0000ffffb1f57b50
    x14 = 0x00000000000003e7   x15 = 0x0000fffcd27c0e60
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd5e080
    x18 = 0x0000000000000074   x19 = 0x0000000000000109
    x20 = 0x0000000000000000   x21 = 0x0000ffffa1c13030
    x22 = 0x0000fff8b062c018   x23 = 0x0000fff8b062c5b8
    x24 = 0x0000fff8b062c5a8   x25 = 0x0000fff8b062c598
    x26 = 0x0000ffffb1f57b50   x27 = 0x0000fffcf7c1eec0
    x28 = 0x0000fff8afe20000    fp = 0x0000fff8b062bfd0
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff8b062bfd0
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff8b062c040    lr = 0x0000ffffafd5e028
     sp = 0x0000fff8b062bfe0    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x8e024
     fp = 0x0000fff8b062c070    lr = 0x0000ffffb068befc
     sp = 0x0000fff8b062c050    pc = 0x0000ffffafd5e028
    Found by: previous frame's frame pointer
 3  libshmmq.so!shmmq::QueueSegment::WaitMessage(unsigned int) + 0x128
     fp = 0x0000fff8b062c320    lr = 0x0000ffffb0756fa4
     sp = 0x0000fff8b062c080    pc = 0x0000ffffb068befc
    Found by: previous frame's frame pointer
 4  0xfff8b46bd5a4
    x19 = 0x0000ffffb1f57b50   x20 = 0x0000fff8b467dcc0
    x21 = 0x0000fff8b46bd400   x22 = 0x0000fff8b46bd5a8
     fp = 0x0000fff8b46bd400    sp = 0x0000fff8b062c330
     pc = 0x0000fff8b46bd5a8
    Found by: call frame info
 5  libjemalloc.so.2 + 0x8e3fd
     sp = 0x0000fff8b062c360    pc = 0x0000ffffb1cd3401
    Found by: stack scanning
 6  libjemalloc.so.2!je_malloc_default [tcache_inlines.h : 68 + 0x18]
     sp = 0x0000fff8b062c420    pc = 0x0000ffffb1c66da8
    Found by: stack scanning

Thread 174
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaf8b7bc8    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000000004
     x8 = 0x0000000000000062    x9 = 0x0000000000000002
    x10 = 0x0000fff82fe6d610   x11 = 0x0000000000000020
    x12 = 0x0000000000008d70   x13 = 0x0000000000010000
    x14 = 0x0000000000008d90   x15 = 0x0000000000000018
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000ffff86151fb0   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaf8b7bc8
    x22 = 0x0000ffffaf8b7bc8   x23 = 0x000000000000000c
    x24 = 0x0000000000000000   x25 = 0x0000000000000000
    x26 = 0x0000000000000006   x27 = 0x0000000000000000
    x28 = 0x0000fff82fe6c488    fp = 0x0000fff82fe6c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff82fe6c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff82fe6c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff82fe6c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff82fe6c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff82fe6c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff82fe6c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff82fe6c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff82fe6d2d8
    x19 = 0x0000fff82fe6c6f8   x20 = 0x0000ffff81cfd600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000002
    x23 = 0x0000fff82f660000   x24 = 0x0000ffffafe80000
    x25 = 0x0000ffff81cfcec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaf8b7b70    fp = 0x0000ffffab81bc80
     sp = 0x0000fff82fe6c600    pc = 0x0000fff82fe6d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff82fe6c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  libc.so.6 + 0xeba48
     sp = 0x0000fff82fe6c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 175
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0a8c5cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001cd6
     x8 = 0x0000000000000062    x9 = 0x001b796caecd1dc2
    x10 = 0x00ffffffffffffff   x11 = 0x000000040d2de90d
    x12 = 0x0000fff833eec5e0   x13 = 0x0000ffff863361e8
    x14 = 0x0000000000000008   x15 = 0x0000ffffa0b27400
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffd01c3f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0a8c5cc
    x22 = 0x0000ffffa0a8c5cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff833eec488    fp = 0x0000fff833eec430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff833eec430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff833eec4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff833eec440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff833eec530    lr = 0x0000ffffb1923208
     sp = 0x0000fff833eec4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff833eec5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff833eec540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff833eed2d8
    x19 = 0x0000fff833eec6f8   x20 = 0x0000fffd01c3f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff8336e0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffd01c3eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0a8c570    fp = 0x0000ffffa0adb300
     sp = 0x0000fff833eec600    pc = 0x0000fff833eed2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff833eec648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff833eec698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff833eec760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 176
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffaa2e4ccc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3c
     x8 = 0x0000000000000062    x9 = 0x0003c06bd090ae5c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041956b40d
    x12 = 0x0000fff800e7c5e0   x13 = 0x0000ffff82fef1e8
    x14 = 0x0000000000000008   x15 = 0x0000fff9b278e200
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff830e8d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffaa2e4ccc
    x22 = 0x0000ffffaa2e4ccc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff800e7c488    fp = 0x0000fff800e7c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff800e7c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff800e7c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff800e7c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff800e7c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff800e7c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff800e7c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff800e7c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff800e7d2d8
    x19 = 0x0000fff800e7c6f8   x20 = 0x0000fff830e8d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff800670000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff830e8cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffaa2e4c70    fp = 0x0000fff9b27986c0
     sp = 0x0000fff800e7c600    pc = 0x0000fff800e7d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff800e7c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff800e7c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff800e7c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 177
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa0a8cccc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3a
     x8 = 0x0000000000000062    x9 = 0x0003a1e7577ab35c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041919ab0d
    x12 = 0x0000fff80066b540   x13 = 0x0000ffff8633e1f0
    x14 = 0x0000000000000008   x15 = 0x0000ffffa0bcfc00
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fffcf7c1f618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa0a8cccc
    x22 = 0x0000ffffa0a8cccc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff80066c488    fp = 0x0000fff80066c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff80066c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff80066c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff80066c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff80066c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff80066c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff80066c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff80066c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff80066d2d8
    x19 = 0x0000fff80066c6f8   x20 = 0x0000fffcf7c1f600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7ffe60000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fffcf7c1eec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa0a8cc70    fp = 0x0000ffffa0adbd00
     sp = 0x0000fff80066c600    pc = 0x0000fff80066d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff80066c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff80066c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff80066c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 178
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000fffa85ad54cc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3c
     x8 = 0x0000000000000062    x9 = 0x0003c06bd090ae5c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041956b40d
    x12 = 0x0000fff7ee12b540   x13 = 0x0000ffff8300d1f0
    x14 = 0x0000000000000008   x15 = 0x0000fff9b2ff0100
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff8026ad618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000fffa85ad54cc
    x22 = 0x0000fffa85ad54cc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7ee12c488    fp = 0x0000fff7ee12c430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ee12c430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ee12c4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7ee12c440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7ee12c530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7ee12c4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7ee12c5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7ee12c540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7ee12d2d8
    x19 = 0x0000fff7ee12c6f8   x20 = 0x0000fff8026ad600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7ed920000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff8026acec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000fffa85ad5470    fp = 0x0000fffa85acf080
     sp = 0x0000fff7ee12c600    pc = 0x0000fff7ee12d2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7ee12c648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7ee12c698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7ee12c760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Thread 179
 0  libc.so.6 + 0x81e9c
     x0 = 0x0000ffffa3220ecc    x1 = 0x0000000000000189
     x2 = 0x0000000000000000    x3 = 0x0000000000000000
     x4 = 0x0000000000000000    x5 = 0x00000000ffffffff
     x6 = 0x0000000000000000    x7 = 0x0000000000001d3e
     x8 = 0x0000000000000062    x9 = 0x0003def049a6a95c
    x10 = 0x00ffffffffffffff   x11 = 0x000000041993bd0d
    x12 = 0x0000fff7ec8fb540   x13 = 0x0000ffff830251f0
    x14 = 0x0000000000000008   x15 = 0x0000000000000001
    x16 = 0x0000000000000001   x17 = 0x0000ffffafd54920
    x18 = 0x0000fff7fe62d618   x19 = 0x0000000000000189
    x20 = 0x0000000000000000   x21 = 0x0000ffffa3220ecc
    x22 = 0x0000ffffa3220ecc   x23 = 0x0000000000000003
    x24 = 0x0000000000000000   x25 = 0x0000000000000001
    x26 = 0x0000000000000001   x27 = 0x0000000000000004
    x28 = 0x0000fff7ec8fc488    fp = 0x0000fff7ec8fc430
     lr = 0x0000ffffafd51e7c    sp = 0x0000fff7ec8fc430
     pc = 0x0000ffffafd51e9c
    Found by: given as instruction pointer in context
 1  libc.so.6 + 0x81e78
     fp = 0x0000fff7ec8fc4d0    lr = 0x0000ffffafd54b20
     sp = 0x0000fff7ec8fc440    pc = 0x0000ffffafd51e7c
    Found by: previous frame's frame pointer
 2  libc.so.6 + 0x84b1c
     fp = 0x0000fff7ec8fc530    lr = 0x0000ffffb1923208
     sp = 0x0000fff7ec8fc4e0    pc = 0x0000ffffafd54b20
    Found by: previous frame's frame pointer
 3  libconcurrent.so.3!lios::concurrent::ThreadPool::ThreadFunc() + 0x104
     fp = 0x0000fff7ec8fc5f0    lr = 0x0000ffffb0061ae0
     sp = 0x0000fff7ec8fc540    pc = 0x0000ffffb1923208
    Found by: previous frame's frame pointer
 4  0xfff7ec8fd2d8
    x19 = 0x0000fff7ec8fc6f8   x20 = 0x0000fff7fe62d600
    x21 = 0x0000ffffafd555e0   x22 = 0x0000000000000000
    x23 = 0x0000fff7ec0f0000   x24 = 0x0000ffffafe80000
    x25 = 0x0000fff7fe62cec0   x26 = 0x1bbd716dcced7c00
    x27 = 0x0000ffffa3220e70    fp = 0x0000ffffa323d280
     sp = 0x0000fff7ec8fc600    pc = 0x0000fff7ec8fd2dc
    Found by: call frame info
 5  libc.so.6 + 0x855dc
     sp = 0x0000fff7ec8fc648    pc = 0x0000ffffafd555e0
    Found by: stack scanning
 6  0xfffffffd
     sp = 0x0000fff7ec8fc698    pc = 0x0000000100000001
    Found by: stack scanning
 7  libc.so.6 + 0xeba48
     sp = 0x0000fff7ec8fc760    pc = 0x0000ffffafdbba4c
    Found by: stack scanning

Loaded modules:
0x100000000 - 0x10050ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x100600000 - 0x10070ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x100800000 - 0x10790ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x107a00000 - 0x107b0ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x107c00000 - 0x107c0ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x107e00000 - 0x107e0ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x108000000 - 0x10800ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x108400000 - 0x1090a2fff  dmabuf:  ???  (WARNING: No symbols, , )
0x109200000 - 0x1097fffff  dmabuf:  ???  (WARNING: No symbols, , )
0x109c00000 - 0x15fa70fff  dmabuf:  ???  (WARNING: No symbols, , )
0x15fc00000 - 0x17675ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x176800000 - 0x18415ffff  dmabuf:  ???  (WARNING: No symbols, , )
0x184200000 - 0x1849fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xaaaabe557000 - 0xaaaabe560fff  vla_arch_container  ???  (main)
0xfff3afb8f000 - 0xfff4501d8fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff4595d9000 - 0xfff4d3003fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff4d3a04000 - 0xfff54d42efff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff54ec2f000 - 0xfff5c8659fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff5d345a000 - 0xfff64ce84fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff64ea85000 - 0xfff650bf6fff  libhpo.so  ???
0xfff65252a000 - 0xfff74597ffff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff746780000 - 0xfff7607fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff760ac7000 - 0xfff7663e6fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7663e7000 - 0xfff78ec9ffff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff78eca0000 - 0xfff7951fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7954a0000 - 0xfff7a85fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7a8880000 - 0xfff7b77fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7b7890000 - 0xfff7dab2ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7db530000 - 0xfff7e2faffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7e3fd0000 - 0xfff7e4f6ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7e5780000 - 0xfff7e63bffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7e73e0000 - 0xfff7e8c5ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7e9470000 - 0xfff7ea0affff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7ee940000 - 0xfff7f6ffffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff7f730d000 - 0xfff7f73cdfff  val_camera_stream_23_1  ???  (WARNING: No symbols, , )
0xfff7f73ce000 - 0xfff7f748efff  val_camera_stream_21_1  ???  (WARNING: No symbols, , )
0xfff7f84af000 - 0xfff7f856ffff  val_camera_stream_22_1  ???  (WARNING: No symbols, , )
0xfff8034c0000 - 0xfff80d3fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff80dc40000 - 0xfff82707ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff82828f000 - 0xfff82834ffff  val_camera_stream_18_1  ???  (WARNING: No symbols, , )
0xfff828c50000 - 0xfff82ee4ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff834cb5000 - 0xfff834d75fff  val_camera_stream_20_1  ???  (WARNING: No symbols, , )
0xfff834d76000 - 0xfff834e36fff  val_camera_stream_17_1  ???  (WARNING: No symbols, , )
0xfff8355e7000 - 0xfff835946fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff835947000 - 0xfff85e1fffff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff85e29d000 - 0xfff85e49dfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff85e49e000 - 0xfff8af60ffff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff8b6a7e000 - 0xfff8b6c7efff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8b6fff000 - 0xfff8b71fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8b7288000 - 0xfff8b7689fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8b7b8a000 - 0xfff8b7d8afff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8b95bb000 - 0xfff8b97bbfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8bafec000 - 0xfff8bb1ecfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8bb9fd000 - 0xfff8bbffffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8bcc1a000 - 0xfff8bce1afff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8bce1b000 - 0xfff8e56d3fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff8e570f000 - 0xfff8e57cffff  val_camera_stream_19_1  ???  (WARNING: No symbols, , )
0xfff8e5ad0000 - 0xfff8e66d5fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff8e69d6000 - 0xfff937b47fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff937b48000 - 0xfff93834bfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff93834c000 - 0xfff9b1d76fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfff9b1d77000 - 0xfff9b2379fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff9b287a000 - 0xfff9b2e7cfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfff9b3a7d000 - 0xfffa04beefff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffa053ff000 - 0xfffa055fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffa0567e000 - 0xfffa0573efff  val_camera_stream_4_1  ???  (WARNING: No symbols, , )
0xfffa0573f000 - 0xfffa06344fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffa06345000 - 0xfffa2ebfdfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffa2ebfe000 - 0xfffa2effffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffa2f07c000 - 0xfffa2f27cfff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffa2f57d000 - 0xfffa2f97efff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffa2f97f000 - 0xfffa7fca3fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffa7fca4000 - 0xfffa814affff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffaa5e95000 - 0xfffac0a4bfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffad8a4c000 - 0xfffaf3602fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffaf3e13000 - 0xfffb29580fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffb29581000 - 0xfffba2fabfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffba3dbc000 - 0xfffbcc674fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffbcc675000 - 0xfffbe722bfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffbe722c000 - 0xfffc0fae4fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffc102f5000 - 0xfffc2aeabfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffc2aeac000 - 0xfffc53764fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffc53f75000 - 0xfffc7c82dfff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffc7c82e000 - 0xfffc7ca2efff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffc7ca2f000 - 0xfffccdba0fff  model.tar  ???  (WARNING: No symbols, model.tar, 000000000000000000000000000000000)
0xfffccdba1000 - 0xfffccdda1fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffccf660000 - 0xfffccfa61fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffcd05fe000 - 0xfffcd0e01fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd01541000 - 0xfffd01741fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd08491000 - 0xfffd08551fff  val_camera_stream_3_1  ???  (WARNING: No symbols, , )
0xfffd0be0c000 - 0xfffd0becffff  liblayer_norm_kernel.so  ???
0xfffd0bed9000 - 0xfffd0bf9cfff  libkeypoint_generator_kernel.so  ???
0xfffd0bfa6000 - 0xfffd0c068fff  libmulti_scale_deformable_attn.so  ???
0xfffd0c472000 - 0xfffd0e271fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd0ea82000 - 0xfffd0fe81fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd10ea2000 - 0xfffd118a1fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd21292000 - 0xfffd21352fff  val_lidar_stream_0_1  ???  (WARNING: No symbols, , )
0xfffd2ccc3000 - 0xfffd32562fff  libnvrtc.so.12  ???
0xfffd35e00000 - 0xfffd35e00fff  libNvEpl.so  ???
0xfffd35e30000 - 0xfffd35e34fff  libtegradisp.so  ???
0xfffd35e60000 - 0xfffd35e66fff  libnvrm_stream.so  ???
0xfffd35e90000 - 0xfffd35ea5fff  libtegrawfd.so  ???
0xfffd35eda000 - 0xfffd360defff  libdb_cxx-18.1.so  ???
0xfffd360ec000 - 0xfffd362c3fff  libdb-18.1.so  ???
0xfffd362d0000 - 0xfffd36321fff  libnvvideo_t26x.so  ???
0xfffd36350000 - 0xfffd3636afff  libnvparser_prod.so  ???
0xfffd36390000 - 0xfffd369c8fff  libnvdla_runtime.so  ???
0xfffd36a00000 - 0xfffd36c56fff  libnvvic.so  ???
0xfffd36c80000 - 0xfffd36cabfff  libnvrm_surface.so  ???
0xfffd36cc7000 - 0xfffd36e5dfff  libnppc.so.12  ???
0xfffd36e70000 - 0xfffd36e70fff  libpthread.so.0  ???
0xfffd36e98000 - 0xfffd36eb9fff  libencoder_interface.so  ???
0xfffd36ebd000 - 0xfffd36ef0fff  libpersist.so.3  ???
0xfffd36ef4000 - 0xfffd3726afff  libtokenizers_c.so  ???
0xfffd372da000 - 0xfffd372fefff  libtokenizers_cpp.so  ???
0xfffd37301000 - 0xfffd3932dfff  libhpo_llm.so  ???
0xfffd3940d000 - 0xfffd3ca8afff  libtce.so  ???
0xfffd3ca9e000 - 0xfffd3cb26fff  libcognition_vlm_gpu_platform_mlc_model_runtime.so  ???
0xfffd3cb2b000 - 0xfffd3ccd2fff  libagent_vla_idls.so  ???
0xfffd3ccdd000 - 0xfffd3cdedfff  libvla_idls.so  ???
0xfffd3cdf6000 - 0xfffd3cfccfff  liblmm_idls.so  ???
0xfffd3cfd7000 - 0xfffd3d6fbfff  libmap_and_navi_idls.so  ???
0xfffd3d71e000 - 0xfffd3d89efff  libe2e_pnp_idls.so  ???
0xfffd3d8a8000 - 0xfffd3d971fff  libdds_location_idls.so  ???
0xfffd3d977000 - 0xfffd3dc2ffff  libehorizon_idls.so  ???
0xfffd3dc3f000 - 0xfffd3dc98fff  libdiagnose_idls.so  ???
0xfffd3dc9c000 - 0xfffd3e1f9fff  libvs_idls.so  ???
0xfffd3e215000 - 0xfffd3e26bfff  libradar.so  ???
0xfffd3e26f000 - 0xfffd3e2e1fff  libgnss.so  ???
0xfffd3e2e6000 - 0xfffd3e440fff  libdata_recorder_idls.so  ???
0xfffd3e44d000 - 0xfffd3e6f5fff  libvehicle_status.so  ???
0xfffd3e708000 - 0xfffd3e737fff  libodometry_idls.so  ???
0xfffd3e73b000 - 0xfffd3edb6fff  libenv_model_idls.so  ???
0xfffd3ede3000 - 0xfffd3ee75fff  libpas.so  ???
0xfffd3ee7b000 - 0xfffd3f095fff  libbev_perception_idls.so  ???
0xfffd3f0a1000 - 0xfffd3f6f5fff  libperception_idls.so  ???
0xfffd3f718000 - 0xfffd3f73cfff  libglog.so.1  ???
0xfffd3f750000 - 0xfffd4024afff  libnvinfer_plugin.so.10  ???
0xfffd40d10000 - 0xfffd4111efff  libnvonnxparser.so.10  ???
0xfffd41140000 - 0xfffd63ea6fff  libnvinfer.so.10  ???
0xfffd66c40000 - 0xfffd66c4efff  libnvmedia_tensor.so  ???
0xfffd66c70000 - 0xfffd66c7dfff  libnvmedia_iofa_sci.so  ???
0xfffd66ca0000 - 0xfffd66caafff  libnvmedia_ijpe_sci.so  ???
0xfffd66cd0000 - 0xfffd66cd9fff  libnvmedia_ijpd_sci.so  ???
0xfffd66d00000 - 0xfffd66d0efff  libnvmedia_iep_sci_prod.so  ???
0xfffd66d30000 - 0xfffd66d3bfff  libnvmedia_ide_sci_prod.so  ???
0xfffd66d60000 - 0xfffd66d65fff  libnvmedia_ide_parser_prod.so  ???
0xfffd66d90000 - 0xfffd66d95fff  libnvmedia_eglstream.so  ???
0xfffd66dc0000 - 0xfffd66dd5fff  libnvmedia_dla.so  ???
0xfffd66e00000 - 0xfffd66e22fff  libnvmedia2d.so  ???
0xfffd66e50000 - 0xfffd66e55fff  libnvscicommon_debug.so  ???
0xfffd66e80000 - 0xfffd66e8bfff  libnvscic2cpcie.so  ???
0xfffd66eb0000 - 0xfffd66eb3fff  libnvscic2ccommon.so  ???
0xfffd66ee0000 - 0xfffd66ee7fff  libnvscic2c.so  ???
0xfffd66f10000 - 0xfffd67c16fff  libnppig.so.12  ???
0xfffd67c33000 - 0xfffd67feafff  libnppicc.so.12  ???
0xfffd68000000 - 0xfffd68051fff  libopencv_xphoto.so.4.3  ???
0xfffd68070000 - 0xfffd68081fff  libopencv_xobjdetect.so.4.3  ???
0xfffd680a0000 - 0xfffd681adfff  libopencv_ximgproc.so.4.3  ???
0xfffd682d0000 - 0xfffd6856dfff  libopencv_xfeatures2d.so.4.3  ???
0xfffd68590000 - 0xfffd685d2fff  libopencv_videostab.so.4.3  ???
0xfffd685e5000 - 0xfffd685e5fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd685f0000 - 0xfffd68640fff  libopencv_videoio.so.4.3  ???
0xfffd68655000 - 0xfffd68655fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68660000 - 0xfffd686bafff  libopencv_video.so.4.3  ???
0xfffd686d0000 - 0xfffd688e0fff  libopencv_tracking.so.4.3  ???
0xfffd688f7000 - 0xfffd688f7fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68900000 - 0xfffd6894afff  libopencv_text.so.4.3  ???
0xfffd68960000 - 0xfffd689a1fff  libopencv_surface_matching.so.4.3  ???
0xfffd689b3000 - 0xfffd689b3fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd689c0000 - 0xfffd689dffff  libopencv_superres.so.4.3  ???
0xfffd689f0000 - 0xfffd68a01fff  libopencv_structured_light.so.4.3  ???
0xfffd68a13000 - 0xfffd68a13fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68a20000 - 0xfffd68aacfff  libopencv_stitching.so.4.3  ???
0xfffd68ac1000 - 0xfffd68ac1fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68ad0000 - 0xfffd68af4fff  libopencv_stereo.so.4.3  ???
0xfffd68b07000 - 0xfffd68b07fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68b10000 - 0xfffd68b34fff  libopencv_shape.so.4.3  ???
0xfffd68b46000 - 0xfffd68b46fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68b47000 - 0xfffd68b4dfff  libbev_pool_fp16.so  ???
0xfffd68b50000 - 0xfffd68b75fff  libopencv_saliency.so.4.3  ???
0xfffd68b88000 - 0xfffd68b8dfff  libgrid_sample.so  ???
0xfffd68b90000 - 0xfffd68c65fff  libopencv_rgbd.so.4.3  ???
0xfffd68c80000 - 0xfffd68c9dfff  libopencv_reg.so.4.3  ???
0xfffd68caf000 - 0xfffd68caffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68cb0000 - 0xfffd68cbbfff  libopencv_rapid.so.4.3  ???
0xfffd68cd0000 - 0xfffd68ce0fff  libopencv_quality.so.4.3  ???
0xfffd68d00000 - 0xfffd68d08fff  libopencv_plot.so.4.3  ???
0xfffd68d19000 - 0xfffd68d19fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68d20000 - 0xfffd68dc0fff  libopencv_photo.so.4.3  ???
0xfffd68dd3000 - 0xfffd68dd3fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68de0000 - 0xfffd68de7fff  libopencv_phase_unwrapping.so.4.3  ???
0xfffd68e00000 - 0xfffd68e5ffff  libopencv_optflow.so.4.3  ???
0xfffd68e70000 - 0xfffd68ecefff  libopencv_objdetect.so.4.3  ???
0xfffd68ee1000 - 0xfffd68ee1fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68ee4000 - 0xfffd68eeffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68ef0000 - 0xfffd68f82fff  libopencv_ml.so.4.3  ???
0xfffd68f96000 - 0xfffd68f9ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68fa0000 - 0xfffd68fc2fff  libopencv_line_descriptor.so.4.3  ???
0xfffd68fd4000 - 0xfffd68fdffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd68fe0000 - 0xfffd68fe1fff  libopencv_intensity_transform.so.4.3  ???
0xfffd68ff3000 - 0xfffd68ffffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69000000 - 0xfffd69317fff  libopencv_imgproc.so.4.3  ???
0xfffd693da000 - 0xfffd693dffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd693e0000 - 0xfffd695a6fff  libopencv_imgcodecs.so.4.3  ???
0xfffd695c9000 - 0xfffd695cffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd695d0000 - 0xfffd695e4fff  libopencv_img_hash.so.4.3  ???
0xfffd695f6000 - 0xfffd695fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69600000 - 0xfffd69609fff  libopencv_highgui.so.4.3  ???
0xfffd6961a000 - 0xfffd6961ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69620000 - 0xfffd6962efff  libopencv_hfs.so.4.3  ???
0xfffd69640000 - 0xfffd6964ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69650000 - 0xfffd6984ffff  libopencv_gapi.so.4.3  ???
0xfffd69866000 - 0xfffd6986ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69870000 - 0xfffd69881fff  libopencv_fuzzy.so.4.3  ???
0xfffd69893000 - 0xfffd6989ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd698a0000 - 0xfffd69934fff  libopencv_features2d.so.4.3  ???
0xfffd6994c000 - 0xfffd6994ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69950000 - 0xfffd699befff  libopencv_face.so.4.3  ???
0xfffd699d4000 - 0xfffd699dffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd699e0000 - 0xfffd69d7cfff  libopencv_dnn.so.4.3  ???
0xfffd69daa000 - 0xfffd69daffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd69db0000 - 0xfffd6a0acfff  libopencv_core.so.4.3  ???
0xfffd6a0cf000 - 0xfffd6a0cffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd6a0d0000 - 0xfffd6a20dfff  libopencv_calib3d.so.4.3  ???
0xfffd6a224000 - 0xfffd6a228fff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd6a229000 - 0xfffd6a4d1fff  libcity_noa_hmi_idls.so  ???
0xfffd6a4e3000 - 0xfffd6a737fff  libdriver_interaction_idls.so  ???
0xfffd6a746000 - 0xfffd6b14bfff  libxcu_soa_msg.so  ???
0xfffd6b17f000 - 0xfffd6b63efff  libpnc_idls.so  ???
0xfffd6b658000 - 0xfffd6bbdffff  libbehavior_idls.so  ???
0xfffd6bbfd000 - 0xfffd6be41fff  libfunction_idls.so  ???
0xfffd6be53000 - 0xfffd6c159fff  libceres.so.2  ???
0xfffd6c160000 - 0xfffd6c366fff  libe2e_common.so  ???
0xfffd6c377000 - 0xfffd6d188fff  libe2e_nodes.so  ???
0xfffd6d1d0000 - 0xfffd6d68cfff  libgnat-24.so  ???
0xfffd6d6ba000 - 0xfffd6d6bffff  dmabuf:  ???  (WARNING: No symbols, , )
0xfffd6d6c0000 - 0xfffd6d6f2fff  libnvscisync.so.1  ???
0xfffd6d998000 - 0xfffd6d99ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff7f000000 - 0xffff7f017fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff7f01a000 - 0xffff7f01ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff7f020000 - 0xffff7f069fff  libopencv_flann.so.4.3  ???
0xffff7f07c000 - 0xffff7f07ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff7f080000 - 0xffff7f095fff  libopencv_dpm.so.4.3  ???
0xffff7f0a6000 - 0xffff7f0a9fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff7f0ac000 - 0xffff7f0adfff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff830a0000 - 0xffff830b8fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff830c1000 - 0xffff830c4fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff830c9000 - 0xffff830c9fff  sem.val_camera_stream_23_1  ???  (WARNING: No symbols, , )
0xffff830ca000 - 0xffff830cafff  sem.val_camera_stream_21_1  ???  (WARNING: No symbols, , )
0xffff830cb000 - 0xffff830cbfff  sem.val_camera_stream_22_1  ???  (WARNING: No symbols, , )
0xffff830cc000 - 0xffff830cffff  ctrl  ???  (WARNING: No symbols, , )
0xffff830d0000 - 0xffff830d0fff  sem.val_camera_stream_18_1  ???  (WARNING: No symbols, , )
0xffff830d1000 - 0xffff830d2fff  ctrl  ???  (WARNING: No symbols, , )
0xffff830d3000 - 0xffff830d3fff  sem.val_camera_stream_20_1  ???  (WARNING: No symbols, , )
0xffff830d4000 - 0xffff830d4fff  sem.val_camera_stream_17_1  ???  (WARNING: No symbols, , )
0xffff830d5000 - 0xffff830d5fff  sem.val_camera_stream_19_1  ???  (WARNING: No symbols, , )
0xffff830d6000 - 0xffff830ddfff  ctrl  ???  (WARNING: No symbols, , )
0xffff830de000 - 0xffff830e1fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff830fc000 - 0xffff830fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffff87420000 - 0xffff8bacbfff  libcuda.so.1  ???  (WARNING: No symbols, libcuda.so.1, 1E82876A7F5062175ABD8004577178D70)
0xffff8bc3e000 - 0xffff9c481fff  libcublasLt.so.12  ???
0xffff9f6a7000 - 0xffffa05dcfff  libcublas.so.12  ???
0xffffa0e02000 - 0xffffa0e1ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa0e20000 - 0xffffa0e2bfff  libopencv_dnn_superres.so.4.3  ???
0xffffa0e3d000 - 0xffffa0e3ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa0e40000 - 0xffffa0ebbfff  libopencv_datasets.so.4.3  ???
0xffffa0ecf000 - 0xffffa0ecffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa0ed0000 - 0xffffa0f12fff  libopencv_ccalib.so.4.3  ???
0xffffa0f24000 - 0xffffa0f2ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa0f30000 - 0xffffa0f62fff  libopencv_bioinspired.so.4.3  ???
0xffffa0f75000 - 0xffffa0f7ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa0f80000 - 0xffffa0fb4fff  libopencv_aruco.so.4.3  ???
0xffffa0fea000 - 0xffffa0feffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa1c00000 - 0xffffa1c09fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa1c0a000 - 0xffffa1c0afff  sem.val_camera_stream_4_1  ???  (WARNING: No symbols, , )
0xffffa1c0b000 - 0xffffa1c0bfff  sem.val_camera_stream_3_1  ???  (WARNING: No symbols, , )
0xffffa1c0c000 - 0xffffa1c0ffff  ctrl  ???  (WARNING: No symbols, , )
0xffffa1c12000 - 0xffffa1c19fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa1c1a000 - 0xffffa1dd3fff  libperception_base.so  ???
0xffffa3600000 - 0xffffa360ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa3610000 - 0xffffa3616fff  libopencv_dnn_objdetect.so.4.3  ???
0xffffa3627000 - 0xffffa362afff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa362b000 - 0xffffa362dfff  liberror_interface.so  ???
0xffffa3630000 - 0xffffa364cfff  libopencv_bgsegm.so.4.3  ???
0xffffa365e000 - 0xffffa365ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa3660000 - 0xffffa3680fff  libgflags.so.2.2  ???
0xffffa3683000 - 0xffffa36b2fff  libgrid_map_core.so  ???
0xffffa36b5000 - 0xffffa36dcfff  libatl_lidar_node.so  ???
0xffffa36e0000 - 0xffffa36e0fff  libdl.so.2  ???
0xffffa3701000 - 0xffffa3704fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa3705000 - 0xffffa3707fff  libvic_interface.so  ???
0xffffa370a000 - 0xffffa370dfff  libwfd_interface.so  ???
0xffffa3710000 - 0xffffa3714fff  libnvscievent.so  ???  (WARNING: No symbols, libnvscievent.so, 8EA4D4B940DCD0876C1224CF3B1AA0160)
0xffffa3731000 - 0xffffa3732fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa3733000 - 0xffffa37d6fff  libnvscistream.so.1  ???  (WARNING: No symbols, libnvscistream.so.1, 8B48ED91111A69B486826696FE06FCA00)
0xffffa5400000 - 0xffffa5409fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa540a000 - 0xffffa540efff  libnvscicommon.so.1  ???
0xffffa5411000 - 0xffffa541bfff  libnvstream_core_block.so  ???
0xffffa541e000 - 0xffffa5425fff  libnvstream_core_network.so  ???
0xffffa5428000 - 0xffffa5474fff  libnvscibuf.so.1  ???
0xffffa5492000 - 0xffffa5493fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffa5494000 - 0xffffa5494fff  sem.val_lidar_stream_0_1  ???  (WARNING: No symbols, , )
0xffffa5495000 - 0xffffa5495fff  ctrl  ???  (WARNING: No symbols, , )
0xffffa5496000 - 0xffffa5499fff  libimage_interface.so  ???
0xffffa549c000 - 0xffffa54b5fff  libnvstream_core_stream.so  ???
0xffffa54b8000 - 0xffffa54e0fff  libnvstream_core_helper.so  ???
0xffffa54e4000 - 0xffffa5520fff  libnvstream_core_channel.so  ???
0xffffa5524000 - 0xffffa55a9fff  libcamera_interface.so  ???
0xffffa5dbf000 - 0xffffa7dbffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaa600000 - 0xffffaa600fff  ctrl  ???  (WARNING: No symbols, , )
0xffffaa601000 - 0xffffaa601fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaa602000 - 0xffffaa605fff  libnvstream_core_error.so  ???
0xffffaa608000 - 0xffffaa60dfff  libnvstream_core_sync.so  ???
0xffffaa610000 - 0xffffaa61dfff  libnvstream_core_buf.so  ???
0xffffaa620000 - 0xffffaa62dfff  libgpu_interface.so  ???
0xffffaa630000 - 0xffffaa6f5fff  liblidar.so  ???
0xffffaa6fc000 - 0xffffaa74cfff  libfreq_reducer_node.so  ???
0xffffac600000 - 0xffffac602fff  libjpe_interface.so  ???
0xffffac605000 - 0xffffac60cfff  libnvstream_core_utils.so  ???
0xffffac60f000 - 0xffffac61dfff  libnvcuextend.so  ???
0xffffac620000 - 0xffffac62efff  libnvrm_host1x.so  ???
0xffffac641000 - 0xffffac641fff  ctrl  ???  (WARNING: No symbols, , )
0xffffac642000 - 0xffffac643fff  libnvstream_core_nvipc.so  ???
0xffffac646000 - 0xffffac64ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac650000 - 0xffffac652fff  libnvrm_chip.so  ???
0xffffac671000 - 0xffffac67ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac680000 - 0xffffac69cfff  libnvsciipc.so  ???
0xffffac6cd000 - 0xffffac6cffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac6d0000 - 0xffffac6d6fff  libnvrm_sync.so  ???
0xffffac6f1000 - 0xffffac6fffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac700000 - 0xffffac700fff  libnvtegrahv.so  ???
0xffffac721000 - 0xffffac72ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac730000 - 0xffffac733fff  libnvsocsys.so  ???
0xffffac751000 - 0xffffac75ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffac760000 - 0xffffac7c7fff  libnvrm_gpu_std.so  ???
0xffffac7e1000 - 0xffffac7effff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaea00000 - 0xffffaea0ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaea10000 - 0xffffaea21fff  libnvos.so  ???
0xffffaea41000 - 0xffffaea4ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaea50000 - 0xffffaea58fff  libnvrm_mem.so  ???
0xffffaea71000 - 0xffffaea7ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaea80000 - 0xffffaead0fff  libgomp.so.1  ???
0xffffaeaf1000 - 0xffffaeaf9fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaeafa000 - 0xffffaebb7fff  libcudart.so.12  ???
0xffffaebbf000 - 0xffffaebd3fff  libmps_idls.so  ???
0xffffaebd6000 - 0xffffaebedfff  libmps_util.so  ???
0xffffafc00000 - 0xffffafc13fff  libnvcucompat.so  ???
0xffffafc16000 - 0xffffafcb5fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffafcb6000 - 0xffffafccdfff  libz.so.1  ???
0xffffafcd0000 - 0xffffafe69fff  libc.so.6  ???  (WARNING: No symbols, libc.so.6, 951F2EA6DEF9571AD5B80979EEDF2C7A0)
0xffffafe8e000 - 0xffffafe8ffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffafe90000 - 0xffffafeaefff  libgcc_s.so.1  ???
0xffffafec1000 - 0xffffafecffff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffafed0000 - 0xffffaff54fff  libm.so.6  ???
0xffffaff71000 - 0xffffaff71fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffaff80000 - 0xffffb01ecfff  libstdc++.so.6  ???
0xffffb020c000 - 0xffffb023ffff  libfmt.so.7  ???
0xffffb0242000 - 0xffffb025dfff  liblog.so.3  ???
0xffffb0260000 - 0xffffb0268fff  libatomic.so.1  ???
0xffffb0282000 - 0xffffb0282fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffb0290000 - 0xffffb02fefff  libyaml-cpp.so.0.7  ???
0xffffb0303000 - 0xffffb0305fff  liblog_upload_trigger.so  ???
0xffffb0308000 - 0xffffb0308fff  libtrigger_log.so.3  ???
0xffffb0309000 - 0xffffb030afff  libtrigger_log.so.3  ???
0xffffb030b000 - 0xffffb0320fff  libecdr.so.1  ???
0xffffb0323000 - 0xffffb039ffff  libelog.so.1  ???
0xffffb03a3000 - 0xffffb0480fff  libxmlparser.so.1  ???
0xffffb0489000 - 0xffffb063efff  libvbsutils.so  ???
0xffffb0648000 - 0xffffb0671fff  libmembuf.so  ???
0xffffb0674000 - 0xffffb06acfff  libshmmq.so  ???
0xffffb06af000 - 0xffffb07cbfff  libdsfdds.so  ???
0xffffb07d3000 - 0xffffb0caffff  libertps.so.1  ???
0xffffb0ccb000 - 0xffffb0d7bfff  liberpc.so.1  ???
0xffffb0d81000 - 0xffffb0fcefff  libVBSFramework.so  ???
0xffffb0fe0000 - 0xffffb107afff  libzstd.so.1  ???
0xffffb1091000 - 0xffffb1091fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffb1094000 - 0xffffb1095fff  libshmmem.so  ???
0xffffb1098000 - 0xffffb10a0fff  dmabuf:  ???  (WARNING: No symbols, , )
0xffffb10a1000 - 0xffffb10a1fff  ctrl  ???  (WARNING: No symbols, , )
0xffffb10a2000 - 0xffffb10c7fff  libcompression.so  ???
0xffffb10cb000 - 0xffffb139cfff  libprotobuf.so.********  ???
0xffffb13ac000 - 0xffffb168efff  libprotoc.so.********  ???
0xffffb1699000 - 0xffffb16d4fff  libutils.so.3  ???
0xffffb16d8000 - 0xffffb17d6fff  libconfig.so.3  ???
0xffffb17df000 - 0xffffb181ffff  libsodium.so.23  ???
0xffffb1822000 - 0xffffb18f6fff  libzmq.so.5  ???
0xffffb1901000 - 0xffffb192bfff  libconcurrent.so.3  ???
0xffffb192e000 - 0xffffb1942fff  libtimer.so.3  ???
0xffffb1948000 - 0xffffb1a90fff  libipc.so.3  ???
0xffffb1a98000 - 0xffffb1aa6fff  libcom.so.3  ???
0xffffb1aa9000 - 0xffffb1b08fff  libnode.so.3  ???
0xffffb1b0e000 - 0xffffb1b8ffff  libscheduling.so.3  ???
0xffffb1b94000 - 0xffffb1c03fff  librecording.so.3  ???
0xffffb1c08000 - 0xffffb1c41fff  libapp.so.3  ???
0xffffb1c45000 - 0xffffb1cf1fff  libjemalloc.so.2  ???
0xffffb1f18000 - 0xffffb1f3efff  ld-linux-aarch64.so.1  ???  (WARNING: No symbols, ld-linux-aarch64.so.1, 69E86C7EF24401CB158D11B598EA2E960)
0xffffb1f45000 - 0xffffb1f4cfff  liblidds.so.3  ???
0xffffb1f55000 - 0xffffb1f55fff  linux-gate.so  ???
