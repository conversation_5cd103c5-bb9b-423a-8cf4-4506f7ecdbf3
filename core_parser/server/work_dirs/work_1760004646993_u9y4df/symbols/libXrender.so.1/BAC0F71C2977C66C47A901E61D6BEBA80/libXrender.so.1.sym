MODULE Linux arm64 BAC0F71C2977C66C47A901E61D6BEBA80 libXrender.so.1
INFO CODE_ID 1CF7C0BA77296CC647A901E61D6BEBA8F4CE10B9
PUBLIC 2010 0 XRenderParseColor
PUBLIC 2200 0 XRenderFindDisplay
PUBLIC 25e4 0 XRenderAddTraps
PUBLIC 2870 0 XRenderComposite
PUBLIC 29d0 0 XRenderCreateCursor
PUBLIC 2ae0 0 XRenderCreateAnimCursor
PUBLIC 2c84 0 XRenderFillRectangle
PUBLIC 2e64 0 XRenderFillRectangles
PUBLIC 3140 0 XRenderSetPictureFilter
PUBLIC 32e0 0 XRenderCreateGlyphSet
PUBLIC 33c0 0 XRenderReferenceGlyphSet
PUBLIC 34b0 0 XRenderFreeGlyphSet
PUBLIC 3570 0 XRenderAddGlyphs
PUBLIC 3800 0 XRenderFreeGlyphs
PUBLIC 39b0 0 XRenderCompositeString8
PUBLIC 3c80 0 XRenderCompositeString16
PUBLIC 3f20 0 XRenderCompositeString32
PUBLIC 41b0 0 XRenderCompositeText8
PUBLIC 44f0 0 XRenderCompositeText16
PUBLIC 4840 0 XRenderCompositeText32
PUBLIC 4b90 0 XRenderCreatePicture
PUBLIC 4cc0 0 XRenderChangePicture
PUBLIC 4db0 0 XRenderSetPictureClipRectangles
PUBLIC 4e90 0 XRenderSetPictureClipRegion
PUBLIC 5010 0 XRenderSetPictureTransform
PUBLIC 5114 0 XRenderFreePicture
PUBLIC 51d0 0 XRenderCreateSolidFill
PUBLIC 52d0 0 XRenderCreateLinearGradient
PUBLIC 5550 0 XRenderCreateRadialGradient
PUBLIC 57e0 0 XRenderCreateConicalGradient
PUBLIC 5a70 0 XRenderCompositeTrapezoids
PUBLIC 5d40 0 XRenderCompositeDoublePoly
PUBLIC 61e0 0 XRenderCompositeTriangles
PUBLIC 64e4 0 XRenderCompositeTriStrip
PUBLIC 67b0 0 XRenderCompositeTriFan
PUBLIC 6bc0 0 XRenderQueryExtension
PUBLIC 6c30 0 XRenderQueryFormats
PUBLIC 72f0 0 XRenderQueryFilters
PUBLIC 7660 0 XRenderQueryVersion
PUBLIC 76e4 0 XRenderQuerySubpixelOrder
PUBLIC 7760 0 XRenderSetSubpixelOrder
PUBLIC 77e4 0 XRenderFindVisualFormat
PUBLIC 7884 0 XRenderFindFormat
PUBLIC 7ac0 0 XRenderFindStandardFormat
PUBLIC 7b14 0 XRenderQueryPictIndexValues
STACK CFI INIT 1a70 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1aa0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ae0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1aec x19: .cfa -16 + ^
STACK CFI 1b24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b40 24 .cfa: sp 0 + .ra: x30
STACK CFI 1b48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b64 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ba0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c44 124 .cfa: sp 0 + .ra: x30
STACK CFI 1c4c .cfa: sp 288 +
STACK CFI 1c58 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d58 .cfa: sp 288 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1d70 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 1d78 .cfa: sp 112 +
STACK CFI 1d84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1da0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1dac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1db8 x27: .cfa -16 + ^
STACK CFI 1e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1e80 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1ef4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1f50 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1f58 .cfa: sp 80 +
STACK CFI 1f64 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f6c x19: .cfa -16 + ^
STACK CFI 1fb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1fc0 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2010 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2018 .cfa: sp 80 +
STACK CFI 2024 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 202c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2038 x21: .cfa -16 + ^
STACK CFI 2190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2198 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2200 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 2208 .cfa: sp 192 +
STACK CFI 2214 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 221c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2228 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 22e4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 22ec .cfa: sp 192 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2324 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2354 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2364 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23c0 x21: x21 x22: x22
STACK CFI 23c4 x25: x25 x26: x26
STACK CFI 2444 x19: x19 x20: x20
STACK CFI 2448 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25a8 x21: x21 x22: x22
STACK CFI 25ac x25: x25 x26: x26
STACK CFI 25c4 x19: x19 x20: x20
STACK CFI 25cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25d0 x19: x19 x20: x20
STACK CFI 25d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 25e4 28c .cfa: sp 0 + .ra: x30
STACK CFI 25ec .cfa: sp 144 +
STACK CFI 25f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2600 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2610 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 261c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2624 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 278c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 27d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 27e0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2870 15c .cfa: sp 0 + .ra: x30
STACK CFI 2878 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2880 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 288c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2898 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 28a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 28b0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 28bc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 28c8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 299c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 29a4 .cfa: sp 128 + .ra: .cfa -120 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 29c4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 29d0 10c .cfa: sp 0 + .ra: x30
STACK CFI 29d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 29e0 v8: .cfa -8 + ^
STACK CFI 29e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29f8 x23: .cfa -16 + ^
STACK CFI 2ab0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2ab8 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ad4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2ae0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 2ae8 .cfa: sp 96 +
STACK CFI 2af4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2afc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b04 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2b0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2b34 x25: .cfa -16 + ^
STACK CFI 2be0 x25: x25
STACK CFI 2c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2c1c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2c70 x25: x25
STACK CFI 2c80 x25: .cfa -16 + ^
STACK CFI INIT 2c84 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 2c8c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ca0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2cac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2cb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ccc x27: .cfa -16 + ^
STACK CFI 2da8 x27: x27
STACK CFI 2dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2db4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2db8 x27: x27
STACK CFI 2dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2dd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2e64 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 2e6c .cfa: sp 128 +
STACK CFI 2e78 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2e80 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2e88 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2e9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ea4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 30cc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 310c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3140 19c .cfa: sp 0 + .ra: x30
STACK CFI 3148 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3150 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3160 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 316c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3280 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 329c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 32e0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 32e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 339c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 33a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 33b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 33c0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 33c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33d0 v8: .cfa -8 + ^
STACK CFI 33d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33e0 x21: .cfa -16 + ^
STACK CFI 3488 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3490 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 34a8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 34b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 34b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34c8 x21: .cfa -16 + ^
STACK CFI 354c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3554 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3570 288 .cfa: sp 0 + .ra: x30
STACK CFI 3578 .cfa: sp 112 +
STACK CFI 3584 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 358c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3598 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 35a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 35b4 x27: .cfa -16 + ^
STACK CFI 371c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3724 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 375c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3764 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3800 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3808 .cfa: sp 80 +
STACK CFI 3814 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 381c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3824 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 382c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3920 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3958 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 39b0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 39b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 39c0 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 39cc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 39d8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 39e4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 39f0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 39f8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3bb0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3bb8 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3c54 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3c5c .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3c80 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 3c88 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3c90 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 3c9c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3ca8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3cb4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3cc0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3cc8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3e48 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3e50 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3ef8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3f00 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3f20 290 .cfa: sp 0 + .ra: x30
STACK CFI 3f28 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3f30 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 3f3c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3f48 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3f54 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3f60 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3f68 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 40e8 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4188 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4190 .cfa: sp 128 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 41b0 338 .cfa: sp 0 + .ra: x30
STACK CFI 41b8 .cfa: sp 144 +
STACK CFI 41c4 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 41cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 41d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 41dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 41e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 41f8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 44bc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 44c4 .cfa: sp 144 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 44f0 350 .cfa: sp 0 + .ra: x30
STACK CFI 44f8 .cfa: sp 144 +
STACK CFI 4504 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 450c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4514 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 451c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4528 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4538 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4814 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 481c .cfa: sp 144 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4840 348 .cfa: sp 0 + .ra: x30
STACK CFI 4848 .cfa: sp 144 +
STACK CFI 4854 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 485c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4864 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4870 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 487c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4888 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4b5c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4b64 .cfa: sp 144 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4b90 128 .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ba0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ba8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4bb0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4bbc x25: .cfa -16 + ^
STACK CFI 4c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 4cc0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4cc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4cd0 v8: .cfa -16 + ^
STACK CFI 4cd8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ce0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d88 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d90 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4da0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4db0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4db8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4dc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4dc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4dd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4de0 x25: .cfa -16 + ^
STACK CFI 4e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4e6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 4e90 178 .cfa: sp 0 + .ra: x30
STACK CFI 4e98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4ea0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4eac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4ebc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4fb8 x23: x23 x24: x24
STACK CFI 4fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4fc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4fc8 x23: x23 x24: x24
STACK CFI 4fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5010 104 .cfa: sp 0 + .ra: x30
STACK CFI 5018 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5020 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 502c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 50f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5100 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 510c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5114 b8 .cfa: sp 0 + .ra: x30
STACK CFI 511c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5124 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 512c x21: .cfa -16 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 51c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 51d0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 51d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 51e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 52a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 52c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 52d0 278 .cfa: sp 0 + .ra: x30
STACK CFI 52d8 .cfa: sp 96 +
STACK CFI 52e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 52ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 52f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5304 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 548c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5494 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5550 288 .cfa: sp 0 + .ra: x30
STACK CFI 5558 .cfa: sp 96 +
STACK CFI 5564 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 556c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5574 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5584 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 571c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5724 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 57e0 288 .cfa: sp 0 + .ra: x30
STACK CFI 57e8 .cfa: sp 96 +
STACK CFI 57f4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 57fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5808 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5810 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5830 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5960 x21: x21 x22: x22
STACK CFI 5994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 599c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5a00 x21: x21 x22: x22
STACK CFI 5a0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5a10 x21: x21 x22: x22
STACK CFI 5a18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5a60 x21: x21 x22: x22
STACK CFI 5a64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5a70 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 5a78 .cfa: sp 160 +
STACK CFI 5a84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5a9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5aa8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5ab0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5abc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5c54 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ca8 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5d40 4a0 .cfa: sp 0 + .ra: x30
STACK CFI 5d48 .cfa: sp 128 +
STACK CFI 5d4c .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5d54 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5d7c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5d8c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5d98 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 61ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 61b4 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 61d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 61e0 304 .cfa: sp 0 + .ra: x30
STACK CFI 61e8 .cfa: sp 144 +
STACK CFI 61f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 61fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6208 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6210 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 621c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6228 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 646c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6474 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 64ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 64b4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 64e4 2cc .cfa: sp 0 + .ra: x30
STACK CFI 64ec .cfa: sp 144 +
STACK CFI 64f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6500 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 650c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6514 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6520 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 652c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6760 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 67a0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 67b0 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 67b8 .cfa: sp 144 +
STACK CFI 67c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 67d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 67dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 67e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 67ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 67f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6a4c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6a8c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6aa4 11c .cfa: sp 0 + .ra: x30
STACK CFI 6aac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ab4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6bc0 6c .cfa: sp 0 + .ra: x30
STACK CFI 6bc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6bd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6c30 6bc .cfa: sp 0 + .ra: x30
STACK CFI 6c38 .cfa: sp 240 +
STACK CFI 6c44 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6c4c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6ca8 x23: x23 x24: x24
STACK CFI 6cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6cdc .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6da8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6e18 x21: x21 x22: x22
STACK CFI 6e1c x23: x23 x24: x24
STACK CFI 6e24 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6e28 x21: x21 x22: x22
STACK CFI 6e30 x23: x23 x24: x24
STACK CFI 6e34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6e74 x23: x23 x24: x24
STACK CFI 6e7c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6e94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6ec4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6ecc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6ed8 x25: x25 x26: x26
STACK CFI 6edc x27: x27 x28: x28
STACK CFI 6ee0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7208 x21: x21 x22: x22
STACK CFI 720c x25: x25 x26: x26
STACK CFI 7210 x27: x27 x28: x28
STACK CFI 7214 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 722c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7230 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7234 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7238 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 723c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7298 x21: x21 x22: x22
STACK CFI 72a0 x23: x23 x24: x24
STACK CFI 72a4 x25: x25 x26: x26
STACK CFI 72a8 x27: x27 x28: x28
STACK CFI 72ac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72cc x25: x25 x26: x26
STACK CFI 72d0 x27: x27 x28: x28
STACK CFI 72d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72d8 x21: x21 x22: x22
STACK CFI 72e0 x23: x23 x24: x24
STACK CFI 72e4 x25: x25 x26: x26
STACK CFI 72e8 x27: x27 x28: x28
STACK CFI INIT 72f0 36c .cfa: sp 0 + .ra: x30
STACK CFI 72f8 .cfa: sp 144 +
STACK CFI 7304 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 730c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7314 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 73b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 73c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 73dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7514 x23: x23 x24: x24
STACK CFI 7518 x25: x25 x26: x26
STACK CFI 751c x27: x27 x28: x28
STACK CFI 754c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7554 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7580 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7584 x23: x23 x24: x24
STACK CFI 7588 x25: x25 x26: x26
STACK CFI 758c x27: x27 x28: x28
STACK CFI 7594 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7598 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 759c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 75e0 x23: x23 x24: x24
STACK CFI 75e4 x25: x25 x26: x26
STACK CFI 75e8 x27: x27 x28: x28
STACK CFI 75ec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7608 x23: x23 x24: x24
STACK CFI 760c x25: x25 x26: x26
STACK CFI 7644 x27: x27 x28: x28
STACK CFI 7648 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 764c x23: x23 x24: x24
STACK CFI 7650 x25: x25 x26: x26
STACK CFI 7658 x27: x27 x28: x28
STACK CFI INIT 7660 84 .cfa: sp 0 + .ra: x30
STACK CFI 7668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7678 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 76c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 76dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 76e4 7c .cfa: sp 0 + .ra: x30
STACK CFI 76ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 76f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 76fc x21: .cfa -16 + ^
STACK CFI 773c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7748 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7760 84 .cfa: sp 0 + .ra: x30
STACK CFI 7768 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7770 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7778 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 77c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 77cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 77dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 77e4 a0 .cfa: sp 0 + .ra: x30
STACK CFI 77ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 77fc x21: .cfa -16 + ^
STACK CFI 7864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 786c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 787c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7884 234 .cfa: sp 0 + .ra: x30
STACK CFI 788c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7894 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 78a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78a8 x23: .cfa -16 + ^
STACK CFI 7a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7a7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7ac0 54 .cfa: sp 0 + .ra: x30
STACK CFI 7ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ad8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ae4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7af8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b14 214 .cfa: sp 0 + .ra: x30
STACK CFI 7b1c .cfa: sp 144 +
STACK CFI 7b28 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7b30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7b38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7b50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7bd4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7be8 x25: x25 x26: x26
STACK CFI 7c18 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7cb4 x25: x25 x26: x26
STACK CFI 7ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7cf0 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7cf4 x25: x25 x26: x26
STACK CFI 7d14 x25: .cfa -16 + ^ x26: .cfa -8 + ^
