MODULE Linux arm64 4CAF6D4820F233B1C49C67DA25C341000 libcdio_paranoia.so.2
INFO CODE_ID 486DAF4CF220B133C49C67DA25C34100020FABC4
PUBLIC 25a0 0 cdio_paranoia_init
PUBLIC 26e4 0 cdio_paranoia_set_range
PUBLIC 2704 0 cdio_paranoia_cachemodel_size
PUBLIC 2730 0 cdio_paranoia_free
PUBLIC 2804 0 cdio_paranoia_modeset
PUBLIC 2820 0 cdio_paranoia_seek
PUBLIC 28e0 0 cdio_paranoia_read_limited
PUBLIC 5050 0 cdio_paranoia_read
PUBLIC 5070 0 cdio_paranoia_overlapset
PUBLIC 50a0 0 cdio_paranoia_version
STACK CFI INIT 1190 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1200 48 .cfa: sp 0 + .ra: x30
STACK CFI 1204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 120c x19: .cfa -16 + ^
STACK CFI 1244 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1250 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1260 298 .cfa: sp 0 + .ra: x30
STACK CFI 1268 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1270 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 14d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1500 20c .cfa: sp 0 + .ra: x30
STACK CFI 1508 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1524 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1530 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1544 x19: .cfa -16 + ^
STACK CFI 15fc x19: x19
STACK CFI 1624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16fc x19: .cfa -16 + ^
STACK CFI INIT 1710 274 .cfa: sp 0 + .ra: x30
STACK CFI 1718 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1720 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1730 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 173c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1744 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1830 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1900 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1984 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 198c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1994 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1a84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1b44 94 .cfa: sp 0 + .ra: x30
STACK CFI 1b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bc0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1be0 2c .cfa: sp 0 + .ra: x30
STACK CFI 1be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c10 6c .cfa: sp 0 + .ra: x30
STACK CFI 1c18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c80 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1c88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ca0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ca8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1d14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1d70 8c .cfa: sp 0 + .ra: x30
STACK CFI 1d78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e00 94 .cfa: sp 0 + .ra: x30
STACK CFI 1e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1e94 78 .cfa: sp 0 + .ra: x30
STACK CFI 1e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ea4 x19: .cfa -16 + ^
STACK CFI 1f04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f10 4c .cfa: sp 0 + .ra: x30
STACK CFI 1f20 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f28 x19: .cfa -16 + ^
STACK CFI 1f50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f60 18 .cfa: sp 0 + .ra: x30
STACK CFI 1f68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f80 88 .cfa: sp 0 + .ra: x30
STACK CFI 1f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ff8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2010 20 .cfa: sp 0 + .ra: x30
STACK CFI 2018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2024 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2030 cc .cfa: sp 0 + .ra: x30
STACK CFI 2038 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2040 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 204c x25: .cfa -16 + ^
STACK CFI 2054 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2060 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 20f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2100 94 .cfa: sp 0 + .ra: x30
STACK CFI 2108 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2110 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2118 x21: .cfa -16 + ^
STACK CFI 2184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 218c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2194 20 .cfa: sp 0 + .ra: x30
STACK CFI 219c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21b4 90 .cfa: sp 0 + .ra: x30
STACK CFI 21c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2234 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2244 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2250 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2258 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2260 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2278 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2280 x25: .cfa -16 + ^
STACK CFI 22c0 x25: x25
STACK CFI 22c8 x21: x21 x22: x22
STACK CFI 22d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 22e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2314 90 .cfa: sp 0 + .ra: x30
STACK CFI 231c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2324 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2334 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2340 x23: .cfa -16 + ^
STACK CFI 2388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2390 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23a4 94 .cfa: sp 0 + .ra: x30
STACK CFI 23ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23b4 x21: .cfa -16 + ^
STACK CFI 23bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2418 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2440 15c .cfa: sp 0 + .ra: x30
STACK CFI 2448 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2450 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2460 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2550 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2588 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 25a0 144 .cfa: sp 0 + .ra: x30
STACK CFI 25a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 26dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 26e4 20 .cfa: sp 0 + .ra: x30
STACK CFI 26ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2704 28 .cfa: sp 0 + .ra: x30
STACK CFI 270c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2724 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2730 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2738 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2740 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2804 1c .cfa: sp 0 + .ra: x30
STACK CFI 280c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2820 bc .cfa: sp 0 + .ra: x30
STACK CFI 2828 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2830 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2838 x21: .cfa -32 + ^
STACK CFI 28bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 28c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 28e0 2768 .cfa: sp 0 + .ra: x30
STACK CFI 28e8 .cfa: sp 288 +
STACK CFI 28fc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2908 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 293c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2940 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2944 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2948 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2d20 x19: x19 x20: x20
STACK CFI 2d28 x23: x23 x24: x24
STACK CFI 2d2c x25: x25 x26: x26
STACK CFI 2d30 x27: x27 x28: x28
STACK CFI 2d58 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2d60 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 382c x19: x19 x20: x20
STACK CFI 3834 x23: x23 x24: x24
STACK CFI 3838 x25: x25 x26: x26
STACK CFI 383c x27: x27 x28: x28
STACK CFI 3840 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4eac x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4ec0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 500c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5010 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5014 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5018 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 501c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5050 1c .cfa: sp 0 + .ra: x30
STACK CFI 5058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5064 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5070 2c .cfa: sp 0 + .ra: x30
STACK CFI 507c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 508c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50a0 20 .cfa: sp 0 + .ra: x30
STACK CFI 50a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50b8 .cfa: sp 0 + .ra: .ra x29: x29
