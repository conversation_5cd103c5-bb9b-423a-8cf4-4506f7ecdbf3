MODULE Linux arm64 99D58256DB6A12518F1636954E3BC9280 libcom.so.3
INFO CODE_ID 5682D5996ADB51128F1636954E3BC928
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 5730 24 0 init_have_lse_atomics
5730 4 45 0
5734 4 46 0
5738 4 45 0
573c 4 46 0
5740 4 47 0
5744 4 47 0
5748 4 48 0
574c 4 47 0
5750 4 48 0
PUBLIC 4ed8 0 _init
PUBLIC 53f0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 5500 0 _GLOBAL__sub_I_factory_creator.cpp
PUBLIC 5710 0 _GLOBAL__sub_I_stat_manager_impl.cpp
PUBLIC 5754 0 call_weak_fn
PUBLIC 5770 0 deregister_tm_clones
PUBLIC 57a0 0 register_tm_clones
PUBLIC 57e0 0 __do_global_dtors_aux
PUBLIC 5830 0 frame_dummy
PUBLIC 5840 0 lios::com::FactoryCreator::GetFactory(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5bb0 0 lios::com::GenericFactory::~GenericFactory()
PUBLIC 5be0 0 std::any::_Manager_internal<lios::com::LiddsFactory>::_S_manage(std::any::_Op, std::any const*, std::any::_Arg*)
PUBLIC 5c40 0 std::any::_Manager_internal<lios::com::IpcFactory>::_S_manage(std::any::_Op, std::any const*, std::any::_Arg*)
PUBLIC 5ca0 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*) [clone .isra.0]
PUBLIC 5e20 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 5f80 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 6020 0 std::_Function_handler<void (), lios::com::StatusTaskRunner::Execute(std::function<void ()>&&)::{lambda()#1}>::_M_invoke(std::_Any_data const&)
PUBLIC 6050 0 std::_Function_handler<void (), lios::com::StatusTaskRunner::Execute(std::function<void ()>&&)::{lambda()#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (), lios::com::StatusTaskRunner::Execute(std::function<void ()>&&)::{lambda()#1}> const&, std::_Manager_operation)
PUBLIC 6160 0 lios::com::StatusTaskRunner::Execute(std::function<void ()>&&)
PUBLIC 6760 0 lios::com::stat::ComSubscriberStateImpl::~ComSubscriberStateImpl()
PUBLIC 6820 0 lios::com::stat::ComSubscriberStateImpl::~ComSubscriberStateImpl() [clone .localalias]
PUBLIC 6850 0 lios::com::stat::ComSubscriberStateImpl::ComSubscriberStateImpl(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6a70 0 lios::com::stat::ComSubscriberStateImpl::SetSubscribeState(bool)
PUBLIC 6b20 0 lios::com::stat::ComSubscriberStateImpl::SetListening()
PUBLIC 6be0 0 lios::com::stat::ComSubscriberState::ComSubscriberState(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6c50 0 lios::com::stat::ComSubscriberState::~ComSubscriberState()
PUBLIC 6cc0 0 lios::com::stat::ComSubscriberState::SetSubscribeState(bool)
PUBLIC 6cd0 0 lios::com::stat::ComSubscriberState::SetListening()
PUBLIC 6ce0 0 lios::com::stat::ComSubscriberStateImpl::UpdateData(lios::com::MessageInfo const&, long)
PUBLIC 7300 0 lios::com::stat::ComSubscriberState::UpdateData(lios::com::MessageInfo const&, long)
PUBLIC 7310 0 void std::vector<long, std::allocator<long> >::_M_realloc_insert<long const&>(__gnu_cxx::__normal_iterator<long*, std::vector<long, std::allocator<long> > >, long const&)
PUBLIC 7490 0 lios::com::stat::ComEntity::operator()(lios::com::stat::ComEntity const&) const [clone .isra.0]
PUBLIC 78d0 0 std::_Hashtable<lios::com::stat::ComEntity, std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*>, std::allocator<std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*> >, std::__detail::_Select1st, std::equal_to<lios::com::stat::ComEntity>, lios::com::stat::ComEntity, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_find_before_node(unsigned long, lios::com::stat::ComEntity const&, unsigned long) const [clone .isra.0]
PUBLIC 79c0 0 lios::com::stat::StatManagerImpl::~StatManagerImpl()
PUBLIC 7bb0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > lios::com::stat::(anonymous namespace)::ConcatItems<std::vector<long, std::allocator<long> > >(std::vector<long, std::allocator<long> > const&)
PUBLIC 8070 0 lios::com::stat::StatManagerImpl::UpdateSubscriberNotify(lios::com::stat::ComEntity const&)
PUBLIC 8210 0 lios::com::stat::StatManagerImpl::PrintSubscriberNotify(lios::com::stat::NotifyStat const&, char const*)
PUBLIC 8660 0 lios::com::stat::StatManagerImpl::PrintSubscriberDelay(lios::com::stat::DelayStat&, char const*)
PUBLIC 93d0 0 lios::com::stat::StatManagerImpl::PrintSubscriberSequence(lios::com::stat::SequenceStat const&, char const*)
PUBLIC a270 0 lios::com::stat::StatManagerImpl::GetInstance()
PUBLIC a4f0 0 lios::com::stat::StatManagerImpl::AddSubscriber(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::com::stat::SubscriberStat*)
PUBLIC b230 0 lios::com::stat::StatManagerImpl::RemoveSubscriber(lios::com::stat::SubscriberStat*)
PUBLIC b4b0 0 void lios::com::stat::(anonymous namespace)::SwapPush<lios::com::stat::DelayStat>(std::vector<lios::com::stat::DelayStat, std::allocator<lios::com::stat::DelayStat> >&, lios::com::stat::DelayStat&)
PUBLIC b590 0 lios::com::stat::StatManagerImpl::DumpState()
PUBLIC be40 0 lios::com::stat::StatManagerImpl::PeriodicState()
PUBLIC c010 0 std::thread::_M_thread_deps_never_run()
PUBLIC c020 0 lios::concurrent::Thread::~Thread()
PUBLIC c070 0 lios::concurrent::Thread::~Thread()
PUBLIC c0c0 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::com::stat::StatManagerImpl::worker_::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::com::stat::StatManagerImpl::worker_::{lambda()#1}&&)::{lambda()#1}> > >::~_State_impl()
PUBLIC c110 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::com::stat::StatManagerImpl::worker_::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::com::stat::StatManagerImpl::worker_::{lambda()#1}&&)::{lambda()#1}> > >::~_State_impl()
PUBLIC c170 0 std::_Hashtable<lios::com::stat::ComEntity, std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*>, std::allocator<std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*> >, std::__detail::_Select1st, std::equal_to<lios::com::stat::ComEntity>, lios::com::stat::ComEntity, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::~_Hashtable()
PUBLIC c280 0 std::vector<lios::com::stat::DelayStat, std::allocator<lios::com::stat::DelayStat> >::~vector()
PUBLIC c310 0 std::vector<lios::com::stat::SequenceStat, std::allocator<lios::com::stat::SequenceStat> >::~vector()
PUBLIC c3c0 0 void std::vector<lios::com::stat::ComEntity, std::allocator<lios::com::stat::ComEntity> >::_M_realloc_insert<lios::com::stat::ComEntity const&>(__gnu_cxx::__normal_iterator<lios::com::stat::ComEntity*, std::vector<lios::com::stat::ComEntity, std::allocator<lios::com::stat::ComEntity> > >, lios::com::stat::ComEntity const&)
PUBLIC c8b0 0 std::_Hashtable<lios::com::stat::ComEntity, std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*>, std::allocator<std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*> >, std::__detail::_Select1st, std::equal_to<lios::com::stat::ComEntity>, lios::com::stat::ComEntity, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC c9f0 0 std::_Hashtable<lios::com::stat::ComEntity, std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*>, std::allocator<std::pair<lios::com::stat::ComEntity const, lios::com::stat::SubscriberStat*> >, std::__detail::_Select1st, std::equal_to<lios::com::stat::ComEntity>, lios::com::stat::ComEntity, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_find_before_node(lios::com::stat::ComEntity const&)
PUBLIC caa0 0 void std::vector<lios::com::stat::DelayStat, std::allocator<lios::com::stat::DelayStat> >::_M_realloc_insert<lios::com::stat::DelayStat>(__gnu_cxx::__normal_iterator<lios::com::stat::DelayStat*, std::vector<lios::com::stat::DelayStat, std::allocator<lios::com::stat::DelayStat> > >, lios::com::stat::DelayStat&&)
PUBLIC cca0 0 void std::vector<lios::com::stat::SequenceStat, std::allocator<lios::com::stat::SequenceStat> >::_M_realloc_insert<lios::com::stat::SequenceStat>(__gnu_cxx::__normal_iterator<lios::com::stat::SequenceStat*, std::vector<lios::com::stat::SequenceStat, std::allocator<lios::com::stat::SequenceStat> > >, lios::com::stat::SequenceStat&&)
PUBLIC ced0 0 void std::vector<lios::com::stat::NotifyStat, std::allocator<lios::com::stat::NotifyStat> >::_M_realloc_insert<lios::com::stat::NotifyStat>(__gnu_cxx::__normal_iterator<lios::com::stat::NotifyStat*, std::vector<lios::com::stat::NotifyStat, std::allocator<lios::com::stat::NotifyStat> > >, lios::com::stat::NotifyStat&&)
PUBLIC d020 0 std::thread::_State_impl<std::thread::_Invoker<std::tuple<lios::concurrent::Thread::Thread<lios::com::stat::StatManagerImpl::worker_::{lambda()#1}>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, lios::com::stat::StatManagerImpl::worker_::{lambda()#1}&&)::{lambda()#1}> > >::_M_run()
PUBLIC d050 0 lios::com::stat::StatManager::UpdateSubscriberListener(lios::com::stat::ComEntity const&)
PUBLIC d090 0 __aarch64_cas1_acq_rel
PUBLIC d0c4 0 _fini
STACK CFI INIT 5770 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 57e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57ec x19: .cfa -16 + ^
STACK CFI 5824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bb0 2c .cfa: sp 0 + .ra: x30
STACK CFI 5bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5be0 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c40 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53f0 104 .cfa: sp 0 + .ra: x30
STACK CFI 53f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5404 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 540c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 548c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5ca0 180 .cfa: sp 0 + .ra: x30
STACK CFI 5ca8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5cb0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5cb8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5cc4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5ce8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5cec x27: .cfa -16 + ^
STACK CFI 5d40 x21: x21 x22: x22
STACK CFI 5d44 x27: x27
STACK CFI 5d60 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 5d7c x21: x21 x22: x22 x27: x27
STACK CFI 5d98 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 5db4 x21: x21 x22: x22 x27: x27
STACK CFI 5df0 x25: x25 x26: x26
STACK CFI 5e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5e20 158 .cfa: sp 0 + .ra: x30
STACK CFI 5e24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5e38 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5f64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5840 36c .cfa: sp 0 + .ra: x30
STACK CFI 5844 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5854 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 585c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 58e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5f80 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f8c x19: .cfa -16 + ^
STACK CFI 5fac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5fb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 601c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5500 210 .cfa: sp 0 + .ra: x30
STACK CFI 5504 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5514 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5524 x21: .cfa -16 + ^
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 56f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6020 24 .cfa: sp 0 + .ra: x30
STACK CFI 603c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6050 104 .cfa: sp 0 + .ra: x30
STACK CFI 6054 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 605c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 60dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 60e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6120 x21: x21 x22: x22
STACK CFI 6124 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 6160 5f4 .cfa: sp 0 + .ra: x30
STACK CFI 6164 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6174 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6180 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 62a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 6344 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 63b0 x23: x23 x24: x24
STACK CFI 64bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 652c x23: x23 x24: x24
STACK CFI 6590 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6594 x23: x23 x24: x24
STACK CFI 65dc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 65e8 x23: x23 x24: x24
STACK CFI 6604 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6640 x23: x23 x24: x24
STACK CFI 6650 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6668 x23: x23 x24: x24
STACK CFI 6694 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 669c x23: x23 x24: x24
STACK CFI 66e4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 66e8 x23: x23 x24: x24
STACK CFI 6704 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6714 x23: x23 x24: x24
STACK CFI 6730 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6734 x23: x23 x24: x24
STACK CFI INIT 6760 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 676c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 681c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6820 28 .cfa: sp 0 + .ra: x30
STACK CFI 6824 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 682c x19: .cfa -16 + ^
STACK CFI 6844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6850 21c .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6864 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6874 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 687c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6888 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 69b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 69b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 69f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 69fc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6a70 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6a74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6a88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6a90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6b04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6b20 bc .cfa: sp 0 + .ra: x30
STACK CFI 6b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b34 x19: .cfa -32 + ^
STACK CFI 6ba4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ba8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6be0 70 .cfa: sp 0 + .ra: x30
STACK CFI 6be4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6bec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6bf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6c04 x23: .cfa -16 + ^
STACK CFI 6c34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6c38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c50 64 .cfa: sp 0 + .ra: x30
STACK CFI 6c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c5c x19: .cfa -16 + ^
STACK CFI 6c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7310 180 .cfa: sp 0 + .ra: x30
STACK CFI 7314 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 731c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 732c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7338 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 73c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 73c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6ce0 620 .cfa: sp 0 + .ra: x30
STACK CFI 6ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6cf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6d00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 6d44 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6d48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6ec8 x25: .cfa -32 + ^
STACK CFI 6f08 x25: x25
STACK CFI 6f90 x21: x21 x22: x22
STACK CFI 6f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 6f9c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6fbc x25: .cfa -32 + ^
STACK CFI 6fdc x25: x25
STACK CFI 729c x21: x21 x22: x22
STACK CFI 72a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 72a4 x25: .cfa -32 + ^
STACK CFI 72a8 x25: x25
STACK CFI 72c4 x25: .cfa -32 + ^
STACK CFI 72cc x25: x25
STACK CFI 72f4 x25: .cfa -32 + ^
STACK CFI INIT 7300 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c020 44 .cfa: sp 0 + .ra: x30
STACK CFI c024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c038 x19: .cfa -16 + ^
STACK CFI c050 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c054 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c070 4c .cfa: sp 0 + .ra: x30
STACK CFI c074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c088 x19: .cfa -16 + ^
STACK CFI c0a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c0ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c0c0 50 .cfa: sp 0 + .ra: x30
STACK CFI c0c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c0dc x19: .cfa -16 + ^
STACK CFI c10c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c110 5c .cfa: sp 0 + .ra: x30
STACK CFI c114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c12c x19: .cfa -16 + ^
STACK CFI c168 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7490 440 .cfa: sp 0 + .ra: x30
STACK CFI 7494 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 749c x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI 74b4 x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI 7760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7764 .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^ x29: .cfa -384 + ^
STACK CFI INIT 78d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 78d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 78dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 78fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 798c x19: x19 x20: x20
STACK CFI 799c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 79a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 79a4 x19: x19 x20: x20
STACK CFI 79b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 79c0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 79c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7bac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7bb0 4bc .cfa: sp 0 + .ra: x30
STACK CFI 7bb4 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 7bc4 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 7bf0 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 7bfc x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 7c1c x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 7c20 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 7e9c x21: x21 x22: x22
STACK CFI 7ea0 x23: x23 x24: x24
STACK CFI 7ea4 x27: x27 x28: x28
STACK CFI 7eac x19: x19 x20: x20
STACK CFI 7ed4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 7ed8 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI 7f90 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 7fbc x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 7fcc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 7fd0 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 7fd4 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 7fd8 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 7fdc x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 8070 198 .cfa: sp 0 + .ra: x30
STACK CFI 8074 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 807c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8084 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8090 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 80b0 x25: .cfa -16 + ^
STACK CFI 8120 x25: x25
STACK CFI 8164 x19: x19 x20: x20
STACK CFI 816c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8170 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 81b4 x25: .cfa -16 + ^
STACK CFI 81e0 x25: x25
STACK CFI 81f4 x19: x19 x20: x20
STACK CFI 81fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8200 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8204 x25: .cfa -16 + ^
STACK CFI INIT 8210 44c .cfa: sp 0 + .ra: x30
STACK CFI 8214 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 821c x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 8234 x19: .cfa -352 + ^ x20: .cfa -344 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 8264 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 8440 x25: x25 x26: x26
STACK CFI 84c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84c8 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x29: .cfa -368 + ^
STACK CFI 85b8 x25: x25 x26: x26
STACK CFI 85bc x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 8654 x25: x25 x26: x26
STACK CFI 8658 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI INIT 8660 d6c .cfa: sp 0 + .ra: x30
STACK CFI 8664 .cfa: sp 608 +
STACK CFI 8674 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI 86a0 x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI 8ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8ed4 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^ x29: .cfa -608 + ^
STACK CFI INIT 93d0 e9c .cfa: sp 0 + .ra: x30
STACK CFI 93d4 .cfa: sp 512 +
STACK CFI 93d8 .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 93e0 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 93fc x19: .cfa -480 + ^ x20: .cfa -472 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 99a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 99a4 .cfa: sp 512 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT c170 10c .cfa: sp 0 + .ra: x30
STACK CFI c174 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c17c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c184 x21: .cfa -16 + ^
STACK CFI c268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c26c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a270 274 .cfa: sp 0 + .ra: x30
STACK CFI a274 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a284 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI a2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2d4 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI a2e0 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a2f4 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a300 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a454 x21: x21 x22: x22
STACK CFI a458 x23: x23 x24: x24
STACK CFI a45c x25: x25 x26: x26
STACK CFI a460 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI a4a4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a4a8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI a4ac x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI a4b0 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI INIT c280 8c .cfa: sp 0 + .ra: x30
STACK CFI c284 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c28c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c294 x21: .cfa -16 + ^
STACK CFI c2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c2e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c310 a4 .cfa: sp 0 + .ra: x30
STACK CFI c314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c31c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c324 x21: .cfa -16 + ^
STACK CFI c38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c390 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c3b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c3c0 4f0 .cfa: sp 0 + .ra: x30
STACK CFI c3c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c3d4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c3e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c3f4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c408 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c68c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT c8b0 140 .cfa: sp 0 + .ra: x30
STACK CFI c8b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c8c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c8d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI c980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c984 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a4f0 d3c .cfa: sp 0 + .ra: x30
STACK CFI a4f4 .cfa: sp 576 +
STACK CFI a500 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI a524 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI a52c x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI a540 x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI a548 x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI a54c x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI a8d8 x21: x21 x22: x22
STACK CFI a8dc x23: x23 x24: x24
STACK CFI a8e0 x25: x25 x26: x26
STACK CFI a8e4 x27: x27 x28: x28
STACK CFI a910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a914 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^ x29: .cfa -576 + ^
STACK CFI ab58 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ab74 x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI ab9c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI abb8 x27: x27 x28: x28
STACK CFI abbc x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI b124 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b128 x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI b12c x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI b130 x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI b134 x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI INIT c9f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI c9f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c9fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ca0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ca84 x21: x21 x22: x22
STACK CFI ca8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT b230 274 .cfa: sp 0 + .ra: x30
STACK CFI b234 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b23c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b260 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b28c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b2b4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b360 x25: x25 x26: x26
STACK CFI b384 x23: x23 x24: x24
STACK CFI b394 x21: x21 x22: x22
STACK CFI b3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI b3c0 x21: x21 x22: x22
STACK CFI b3e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI b404 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b43c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b488 x25: x25 x26: x26
STACK CFI b490 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b498 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b49c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b4a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT caa0 1fc .cfa: sp 0 + .ra: x30
STACK CFI caa4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cab4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cac8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cad4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI cc40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI cc44 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT b4b0 dc .cfa: sp 0 + .ra: x30
STACK CFI b4b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b548 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b54c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b584 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b588 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI INIT cca0 22c .cfa: sp 0 + .ra: x30
STACK CFI cca4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ccb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ccc8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ccd8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^
STACK CFI ce58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ce5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT ced0 150 .cfa: sp 0 + .ra: x30
STACK CFI ced4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cedc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cee8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cef4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI cfb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI cfb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT b590 8a8 .cfa: sp 0 + .ra: x30
STACK CFI b594 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI b5c8 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI b5d8 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI b628 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI b634 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI b654 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI b9c8 x23: x23 x24: x24
STACK CFI bb38 x21: x21 x22: x22
STACK CFI bb40 x19: x19 x20: x20
STACK CFI bb44 x25: x25 x26: x26
STACK CFI bb48 x27: x27 x28: x28
STACK CFI bb4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bb50 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI bbcc x23: x23 x24: x24
STACK CFI bc50 x19: x19 x20: x20
STACK CFI bc54 x21: x21 x22: x22
STACK CFI bc58 x25: x25 x26: x26
STACK CFI bc5c x27: x27 x28: x28
STACK CFI bc60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bc64 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI bc84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bc98 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI bcdc x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI bcfc x23: x23 x24: x24
STACK CFI bd00 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI bd04 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bd08 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI bd0c x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI bd10 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI bd14 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI bd18 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI bd1c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI bd40 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI bd44 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI bd48 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI bdbc x23: .cfa -448 + ^ x24: .cfa -440 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI bdcc x23: x23 x24: x24
STACK CFI bdd0 x27: x27 x28: x28
STACK CFI bdd4 x23: .cfa -448 + ^ x24: .cfa -440 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI bdec x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI be14 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI be18 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI be24 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI INIT be40 1cc .cfa: sp 0 + .ra: x30
STACK CFI be44 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI be58 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI be74 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI be80 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bf7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf80 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI bfb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bfb8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT d020 28 .cfa: sp 0 + .ra: x30
STACK CFI d024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d02c x19: .cfa -16 + ^
STACK CFI d044 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5710 20 .cfa: sp 0 + .ra: x30
STACK CFI 5714 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d050 3c .cfa: sp 0 + .ra: x30
STACK CFI d054 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d05c x19: .cfa -16 + ^
STACK CFI d074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d078 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI d088 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d090 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5730 24 .cfa: sp 0 + .ra: x30
STACK CFI 5734 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 574c .cfa: sp 0 + .ra: .ra x29: x29
