MODULE Linux arm64 7B0D25AF1E1C71ABBBF6848B2313C9A30 libnvstream_core_buf.so
INFO CODE_ID AF250D7B1C1EAB71BBF6848B2313C9A3
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 5f10 24 0 init_have_lse_atomics
5f10 4 45 0
5f14 4 46 0
5f18 4 45 0
5f1c 4 46 0
5f20 4 47 0
5f24 4 47 0
5f28 4 48 0
5f2c 4 47 0
5f30 4 48 0
PUBLIC 5508 0 _init
PUBLIC 5bf0 0 _GLOBAL__sub_I_buf_alloc.cpp
PUBLIC 5f34 0 call_weak_fn
PUBLIC 5f50 0 deregister_tm_clones
PUBLIC 5f80 0 register_tm_clones
PUBLIC 5fc0 0 __do_global_dtors_aux
PUBLIC 6010 0 frame_dummy
PUBLIC 6020 0 linvs::buf::SetC2cBufAttrList(linvs::buf::BufAttrList&)
PUBLIC 61b0 0 linvs::buf::GetBaseBufAttrList(linvs::buf::BufAttrList&, linvs::buf::BaseBufDesc const&)
PUBLIC 62e0 0 linvs::buf::AllocBuf(std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >&, unsigned int, linvs::buf::AllocParams const&)
PUBLIC 6570 0 std::vector<int, std::allocator<int> >::~vector()
PUBLIC 6590 0 std::unordered_map<linvs::buf::BufAccessPerm, NvSciBufAttrValAccessPerm, std::hash<linvs::buf::BufAccessPerm>, std::equal_to<linvs::buf::BufAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> > >::~unordered_map()
PUBLIC 6600 0 std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >::_M_default_append(unsigned long)
PUBLIC 67e0 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::clear()
PUBLIC 6830 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_deallocate_buckets()
PUBLIC 6850 0 void std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >::_M_realloc_insert<linvs::buf::BufAttrList const*>(__gnu_cxx::__normal_iterator<linvs::buf::BufAttrList const**, std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > >, linvs::buf::BufAttrList const*&&)
PUBLIC 69d0 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 6b00 0 std::_Hashtable<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_insert_unique_node(unsigned long, unsigned long, std::__detail::_Hash_node<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, false>*, unsigned long)
PUBLIC 6c10 0 std::__detail::_Map_base<linvs::buf::BufAccessPerm, std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm>, std::allocator<std::pair<linvs::buf::BufAccessPerm const, NvSciBufAttrValAccessPerm> >, std::__detail::_Select1st, std::equal_to<linvs::buf::BufAccessPerm>, std::hash<linvs::buf::BufAccessPerm>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](linvs::buf::BufAccessPerm const&)
PUBLIC 6d00 0 linvs::buf::BufAttrList::operator*() const
PUBLIC 6d10 0 linvs::buf::BufAttrList::operator*()
PUBLIC 6d20 0 linvs::buf::BufAttrList::operator bool() const
PUBLIC 6d40 0 linvs::buf::BufAttrList::AllocBufObj()
PUBLIC 6de0 0 linvs::buf::BufAttrList::AllocBufObj(linvs::buf::BufObj&)
PUBLIC 6e50 0 linvs::buf::BufAttrList::SetAttrs(NvSciBufAttrKeyValuePair*, unsigned long)
PUBLIC 6ea0 0 linvs::buf::BufAttrList::GetSlotCount()
PUBLIC 6eb0 0 linvs::buf::BufAttrList::GetAttrs(NvSciBufAttrKeyValuePair*, unsigned long) const
PUBLIC 6f00 0 linvs::buf::BufAttrList::GetSlotAttrs(unsigned long, NvSciBufAttrKeyValuePair*, unsigned long)
PUBLIC 6f50 0 linvs::buf::BufAttrList::AppendUnreconciled(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >)
PUBLIC 7070 0 linvs::buf::BufAttrList::Reconciled()
PUBLIC 7100 0 linvs::buf::BufAttrList::ValidateReconciled(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > const&)
PUBLIC 7290 0 linvs::buf::BufAttrList::~BufAttrList()
PUBLIC 7340 0 linvs::buf::BufAttrList::BufAttrList()
PUBLIC 7420 0 linvs::buf::BufAttrList::Reconcile(std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > const&, linvs::buf::BufAttrList&)
PUBLIC 75f0 0 linvs::buf::BufAttrList::BufAttrList(NvSciBufAttrListRec* const&)
PUBLIC 7710 0 linvs::buf::BufAttrList::BufAttrList(linvs::buf::BufAttrList const&)
PUBLIC 77f0 0 linvs::buf::BufAttrList::operator=(linvs::buf::BufAttrList const&)
PUBLIC 7900 0 linvs::buf::BufAttrList::SerialBufAttrValuesToStr(linvs::buf::BufAttrValues const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&)
PUBLIC 8440 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8450 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8470 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8480 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8490 0 std::_Sp_counted_ptr_inplace<linvs::buf::BufAttrList::NvSciBufAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8500 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 8580 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 8620 0 linvs::buf::BufModule::BufModule()
PUBLIC 8660 0 linvs::buf::BufModule::operator bool()
PUBLIC 8670 0 linvs::buf::BufModule::operator*()
PUBLIC 8680 0 linvs::buf::BufModule::operator*() const
PUBLIC 8690 0 linvs::buf::BufModule::~BufModule()
PUBLIC 86b0 0 linvs::buf::BufModule::CreateAttrList()
PUBLIC 8750 0 linvs::buf::BufModule::CreateAttrList(linvs::buf::BufAttrList&)
PUBLIC 87c0 0 linvs::buf::BufObj::GetAttrList(NvSciBufAttrListRec*&) const
PUBLIC 8810 0 linvs::buf::BufObj::GetAttrs(NvSciBufAttrKeyValuePair*, unsigned long) const
PUBLIC 88c0 0 linvs::buf::BufObj::BufObj(NvSciBufObjRefRec* const&)
PUBLIC 88d0 0 linvs::buf::BufObj::operator*() const
PUBLIC 88e0 0 linvs::buf::BufObj::operator*()
PUBLIC 88f0 0 linvs::buf::BufObj::operator bool() const
PUBLIC 8900 0 linvs::buf::BufObj::operator==(linvs::buf::BufObj const&)
PUBLIC 8940 0 linvs::buf::BufObj::operator==(NvSciBufObjRefRec* const&)
PUBLIC 8960 0 linvs::buf::BufObj::operator!=(linvs::buf::BufObj const&)
PUBLIC 89a0 0 linvs::buf::BufObj::operator!=(NvSciBufObjRefRec* const&)
PUBLIC 89c0 0 linvs::buf::BufObj::Reset()
PUBLIC 89f0 0 linvs::buf::BufObj::~BufObj()
PUBLIC 8a10 0 linvs::buf::BufObj::DeepClone()
PUBLIC 8af0 0 linvs::buf::BufObj::DupFrom(linvs::buf::BufObj const&)
PUBLIC 8b50 0 linvs::buf::BufObj::BufObj(linvs::buf::BufObj const&)
PUBLIC 8ba0 0 linvs::buf::BufObj::operator=(linvs::buf::BufObj const&)
PUBLIC 8c00 0 linvs::buf::BufObj::DupFrom(NvSciBufObjRefRec* const&)
PUBLIC 8c60 0 linvs::buf::BufObj::FlushCpuCache() const
PUBLIC 8d40 0 linvs::buf::BufObj::GetPtr() const
PUBLIC 8de0 0 linvs::buf::BufObj::CpuWrite(void const*, int)
PUBLIC 8e20 0 linvs::buf::BufObj::GetConstPtr() const
PUBLIC 8ec0 0 linvs::buf::BufObj::DumpAttrInfo(linvs::buf::BufAttrValues&) const
PUBLIC 9800 0 linvs::buf::BufObj::DeepCopy(linvs::buf::BufObj&)
PUBLIC 9a90 0 linvs::buf::BufObj::GetPixels(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC a130 0 linvs::buf::BufObj::PutPixels(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > > const&)
PUBLIC a7c0 0 linvs::buf::BufObj::AllocPlanesMem(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC ad40 0 linvs::buf::BufObj::GetPixelsAlloc(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >&) const
PUBLIC ad80 0 linvs::buf::BufAttrValues::~BufAttrValues()
PUBLIC aef0 0 std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> >::_M_default_append(unsigned long)
PUBLIC b190 0 std::vector<unsigned int, std::allocator<unsigned int> >::_M_default_append(unsigned long)
PUBLIC b310 0 std::vector<unsigned long, std::allocator<unsigned long> >::_M_default_append(unsigned long)
PUBLIC b490 0 std::vector<NvSciBufAttrValColorStd, std::allocator<NvSciBufAttrValColorStd> >::_M_default_append(unsigned long)
PUBLIC b730 0 std::vector<unsigned char, std::allocator<unsigned char> >::_M_default_append(unsigned long)
PUBLIC b8a0 0 void std::vector<NvSciBufType, std::allocator<NvSciBufType> >::_M_realloc_insert<NvSciBufType const&>(__gnu_cxx::__normal_iterator<NvSciBufType*, std::vector<NvSciBufType, std::allocator<NvSciBufType> > >, NvSciBufType const&)
PUBLIC ba20 0 std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >::_M_default_append(unsigned long)
PUBLIC bcb0 0 __aarch64_ldadd4_acq_rel
PUBLIC bce0 0 _fini
STACK CFI INIT 5f50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5fc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fcc x19: .cfa -16 + ^
STACK CFI 6004 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6570 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6590 6c .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 659c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 65f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6020 188 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6038 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 60b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 60bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6600 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6608 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6614 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 661c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6668 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 666c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 667c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 672c x27: x27 x28: x28
STACK CFI 6744 x23: x23 x24: x24
STACK CFI 674c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6750 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 67e0 4c .cfa: sp 0 + .ra: x30
STACK CFI 67e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6830 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6850 180 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 685c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 686c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6878 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6904 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 69d0 12c .cfa: sp 0 + .ra: x30
STACK CFI 69d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b00 110 .cfa: sp 0 + .ra: x30
STACK CFI 6b04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6bb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c10 ec .cfa: sp 0 + .ra: x30
STACK CFI 6c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ca0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 61b0 128 .cfa: sp 0 + .ra: x30
STACK CFI 61b4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 61cc x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 62d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 62d4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 62e0 290 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 62f4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 62fc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6308 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6348 x25: .cfa -80 + ^
STACK CFI 6450 x25: x25
STACK CFI 6488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 648c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 6490 x25: x25
STACK CFI 6494 x25: .cfa -80 + ^
STACK CFI 64cc x25: x25
STACK CFI 64d8 x25: .cfa -80 + ^
STACK CFI 64dc x25: x25
STACK CFI 6504 x25: .cfa -80 + ^
STACK CFI 652c x25: x25
STACK CFI 6530 x25: .cfa -80 + ^
STACK CFI 6554 x25: x25
STACK CFI 6558 x25: .cfa -80 + ^
STACK CFI 656c x25: x25
STACK CFI INIT 5bf0 314 .cfa: sp 0 + .ra: x30
STACK CFI 5bf4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5c04 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5c3c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5e24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI INIT 8440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8450 20 .cfa: sp 0 + .ra: x30
STACK CFI 845c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8468 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8490 70 .cfa: sp 0 + .ra: x30
STACK CFI 8494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84a4 x19: .cfa -16 + ^
STACK CFI 84e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 84ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 84fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d20 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d40 94 .cfa: sp 0 + .ra: x30
STACK CFI 6d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6de0 6c .cfa: sp 0 + .ra: x30
STACK CFI 6de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6df8 x19: .cfa -16 + ^
STACK CFI 6e1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6e50 50 .cfa: sp 0 + .ra: x30
STACK CFI 6e54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ea0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6eb0 50 .cfa: sp 0 + .ra: x30
STACK CFI 6eb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ed0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6f00 50 .cfa: sp 0 + .ra: x30
STACK CFI 6f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6f20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6f24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6f50 114 .cfa: sp 0 + .ra: x30
STACK CFI 6f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f70 x23: .cfa -16 + ^
STACK CFI 7014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7018 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7070 90 .cfa: sp 0 + .ra: x30
STACK CFI 7074 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 70d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7100 188 .cfa: sp 0 + .ra: x30
STACK CFI 7104 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 710c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7118 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7120 x23: .cfa -32 + ^
STACK CFI 71fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7200 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8500 78 .cfa: sp 0 + .ra: x30
STACK CFI 8504 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8514 x19: .cfa -16 + ^
STACK CFI 8548 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 854c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 855c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8568 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8580 9c .cfa: sp 0 + .ra: x30
STACK CFI 8584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8590 x19: .cfa -16 + ^
STACK CFI 85d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 860c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7290 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 729c x19: .cfa -16 + ^
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 72e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 730c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7318 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7330 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7340 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 734c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 73c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 73f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7420 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 7424 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 742c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7444 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 7548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 754c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 75f0 11c .cfa: sp 0 + .ra: x30
STACK CFI 75f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 75fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7608 x21: .cfa -16 + ^
STACK CFI 7690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7694 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 76d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7710 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 771c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7734 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7758 x21: x21 x22: x22
STACK CFI 7764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7768 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 77f0 10c .cfa: sp 0 + .ra: x30
STACK CFI 77f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7800 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7810 x21: .cfa -16 + ^
STACK CFI 7880 x21: x21
STACK CFI 7890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7894 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7898 x21: x21
STACK CFI 78a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7900 b40 .cfa: sp 0 + .ra: x30
STACK CFI 7904 .cfa: sp 672 +
STACK CFI 7910 .ra: .cfa -664 + ^ x29: .cfa -672 + ^
STACK CFI 7918 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 7920 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 7934 x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 82a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 82a4 .cfa: sp 672 + .ra: .cfa -664 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^ x29: .cfa -672 + ^
STACK CFI INIT 8620 40 .cfa: sp 0 + .ra: x30
STACK CFI 8624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8634 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8660 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8690 18 .cfa: sp 0 + .ra: x30
STACK CFI 8694 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 86b0 94 .cfa: sp 0 + .ra: x30
STACK CFI 86b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 86f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 86f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 872c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8750 68 .cfa: sp 0 + .ra: x30
STACK CFI 8754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8764 x19: .cfa -16 + ^
STACK CFI 8788 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 878c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 87c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 87c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 87e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8810 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8814 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8888 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 88c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 88d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8900 34 .cfa: sp 0 + .ra: x30
STACK CFI 8904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8914 x19: .cfa -16 + ^
STACK CFI 8930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8940 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8960 34 .cfa: sp 0 + .ra: x30
STACK CFI 8964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8974 x19: .cfa -16 + ^
STACK CFI 8990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad80 168 .cfa: sp 0 + .ra: x30
STACK CFI ad84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad8c x19: .cfa -16 + ^
STACK CFI aed8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aedc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89c0 2c .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 89cc x19: .cfa -16 + ^
STACK CFI 89e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89f0 14 .cfa: sp 0 + .ra: x30
STACK CFI 89f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a10 dc .cfa: sp 0 + .ra: x30
STACK CFI 8a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8af0 60 .cfa: sp 0 + .ra: x30
STACK CFI 8af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8afc x19: .cfa -16 + ^
STACK CFI 8b20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8b50 44 .cfa: sp 0 + .ra: x30
STACK CFI 8b54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8ba0 58 .cfa: sp 0 + .ra: x30
STACK CFI 8ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8bb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8c00 54 .cfa: sp 0 + .ra: x30
STACK CFI 8c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8c60 e0 .cfa: sp 0 + .ra: x30
STACK CFI 8c64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8c7c x19: .cfa -80 + ^
STACK CFI 8ce0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8d40 9c .cfa: sp 0 + .ra: x30
STACK CFI 8d44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d54 x19: .cfa -32 + ^
STACK CFI 8da8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8dac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8de0 40 .cfa: sp 0 + .ra: x30
STACK CFI 8de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8dec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8e20 9c .cfa: sp 0 + .ra: x30
STACK CFI 8e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8e34 x19: .cfa -32 + ^
STACK CFI 8e88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8e8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT aef0 29c .cfa: sp 0 + .ra: x30
STACK CFI aef8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI af00 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI af10 x25: .cfa -16 + ^
STACK CFI af24 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI af34 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI afd4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b084 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b0a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b0fc x23: x23 x24: x24
STACK CFI b10c x21: x21 x22: x22
STACK CFI b114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b118 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT b190 178 .cfa: sp 0 + .ra: x30
STACK CFI b198 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b1a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b1b0 x25: .cfa -16 + ^
STACK CFI b1c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b1d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b24c x21: x21 x22: x22
STACK CFI b250 x23: x23 x24: x24
STACK CFI b258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b25c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b29c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b2a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT b310 178 .cfa: sp 0 + .ra: x30
STACK CFI b318 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b320 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b330 x25: .cfa -16 + ^
STACK CFI b344 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b354 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b3cc x21: x21 x22: x22
STACK CFI b3d0 x23: x23 x24: x24
STACK CFI b3d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b3dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b41c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b424 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT b490 29c .cfa: sp 0 + .ra: x30
STACK CFI b498 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b4a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b4b0 x25: .cfa -16 + ^
STACK CFI b4c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b4d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b574 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b61c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b624 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b648 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b69c x23: x23 x24: x24
STACK CFI b6ac x21: x21 x22: x22
STACK CFI b6b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI b6b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT b730 16c .cfa: sp 0 + .ra: x30
STACK CFI b738 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b744 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b74c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b76c x25: .cfa -16 + ^
STACK CFI b7e8 x25: x25
STACK CFI b808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b80c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI b830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b838 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI b848 x25: .cfa -16 + ^
STACK CFI INIT b8a0 180 .cfa: sp 0 + .ra: x30
STACK CFI b8a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b8ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b8bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b8c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI b950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI b954 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8ec0 934 .cfa: sp 0 + .ra: x30
STACK CFI 8ec4 .cfa: sp 960 +
STACK CFI 8edc .ra: .cfa -952 + ^ x29: .cfa -960 + ^
STACK CFI 8ee4 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 8f24 x21: .cfa -928 + ^ x22: .cfa -920 + ^
STACK CFI 8f28 x23: .cfa -912 + ^ x24: .cfa -904 + ^
STACK CFI 8f2c x25: .cfa -896 + ^ x26: .cfa -888 + ^
STACK CFI 8f30 x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI 952c x21: x21 x22: x22
STACK CFI 9534 x23: x23 x24: x24
STACK CFI 9538 x25: x25 x26: x26
STACK CFI 953c x27: x27 x28: x28
STACK CFI 9564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9568 .cfa: sp 960 + .ra: .cfa -952 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^ x27: .cfa -880 + ^ x28: .cfa -872 + ^ x29: .cfa -960 + ^
STACK CFI 97d8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 97e4 x21: .cfa -928 + ^ x22: .cfa -920 + ^
STACK CFI 97e8 x23: .cfa -912 + ^ x24: .cfa -904 + ^
STACK CFI 97ec x25: .cfa -896 + ^ x26: .cfa -888 + ^
STACK CFI 97f0 x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI INIT 9800 284 .cfa: sp 0 + .ra: x30
STACK CFI 9804 .cfa: sp 528 +
STACK CFI 9814 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 981c x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 9828 x21: .cfa -496 + ^
STACK CFI 9a3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9a40 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x29: .cfa -528 + ^
STACK CFI INIT 9a90 6a0 .cfa: sp 0 + .ra: x30
STACK CFI 9a94 .cfa: sp 592 +
STACK CFI 9aa4 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 9aac x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 9ab8 x21: .cfa -560 + ^
STACK CFI 9e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9e40 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x29: .cfa -592 + ^
STACK CFI INIT a130 690 .cfa: sp 0 + .ra: x30
STACK CFI a134 .cfa: sp 592 +
STACK CFI a144 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI a14c x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI a158 x21: .cfa -560 + ^
STACK CFI a4cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a4d0 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x29: .cfa -592 + ^
STACK CFI INIT ba20 28c .cfa: sp 0 + .ra: x30
STACK CFI ba28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ba34 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ba3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ba50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI badc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI bc1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bc20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a7c0 574 .cfa: sp 0 + .ra: x30
STACK CFI a7c4 .cfa: sp 544 +
STACK CFI a7d4 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI a7dc x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI a7e4 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI ab14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab18 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x29: .cfa -544 + ^
STACK CFI ab78 x23: .cfa -496 + ^
STACK CFI abb4 x23: x23
STACK CFI ac24 x23: .cfa -496 + ^
STACK CFI ac28 x23: x23
STACK CFI ac2c x23: .cfa -496 + ^
STACK CFI ac3c x23: x23
STACK CFI acfc x23: .cfa -496 + ^
STACK CFI ad00 x23: x23
STACK CFI ad28 x23: .cfa -496 + ^
STACK CFI INIT ad40 40 .cfa: sp 0 + .ra: x30
STACK CFI ad44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ad6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ad70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ad7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bcb0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5f10 24 .cfa: sp 0 + .ra: x30
STACK CFI 5f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f2c .cfa: sp 0 + .ra: .ra x29: x29
