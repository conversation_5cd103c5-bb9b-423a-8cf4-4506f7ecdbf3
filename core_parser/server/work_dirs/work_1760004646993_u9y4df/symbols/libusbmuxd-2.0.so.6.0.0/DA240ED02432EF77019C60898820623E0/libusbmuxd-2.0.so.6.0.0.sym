MODULE Linux arm64 DA240ED02432EF77019C60898820623E0 libusbmuxd-2.0.so.6
INFO CODE_ID D00E24DA322477EF019C60898820623E0490C7F4
PUBLIC 4b90 0 usbmuxd_events_subscribe
PUBLIC 4df0 0 usbmuxd_events_unsubscribe
PUBLIC 4fe0 0 usbmuxd_subscribe
PUBLIC 5050 0 usbmuxd_unsubscribe
PUBLIC 5084 0 usbmuxd_get_device_list
PUBLIC 5690 0 usbmuxd_device_list_free
PUBLIC 56c4 0 usbmuxd_get_device_by_udid
PUBLIC 5850 0 usbmuxd_get_device
PUBLIC 5a80 0 usbmuxd_connect
PUBLIC 5de0 0 usbmuxd_disconnect
PUBLIC 5e00 0 usbmuxd_send
PUBLIC 5f70 0 usbmuxd_recv_timeout
PUBLIC 5fb0 0 usbmuxd_recv
PUBLIC 5fd0 0 usbmuxd_read_buid
PUBLIC 6220 0 usbmuxd_read_pair_record
PUBLIC 64d0 0 usbmuxd_save_pair_record_with_device_id
PUBLIC 67b0 0 usbmuxd_save_pair_record
PUBLIC 67d4 0 usbmuxd_delete_pair_record
PUBLIC 6a30 0 libusbmuxd_set_use_inotify
PUBLIC 6a50 0 libusbmuxd_set_debug_level
STACK CFI INIT 2220 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2290 48 .cfa: sp 0 + .ra: x30
STACK CFI 2294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 229c x19: .cfa -16 + ^
STACK CFI 22d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 2344 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2368 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2374 a0 .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2384 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2390 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 240c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2414 98 .cfa: sp 0 + .ra: x30
STACK CFI 2420 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2428 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 245c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2464 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 248c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 24b0 374 .cfa: sp 0 + .ra: x30
STACK CFI 24b8 .cfa: sp 96 +
STACK CFI 24c8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2504 x21: .cfa -16 + ^
STACK CFI 25a4 x21: x21
STACK CFI 25d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25d8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2600 x21: x21
STACK CFI 2608 x21: .cfa -16 + ^
STACK CFI 280c x21: x21
STACK CFI 2810 x21: .cfa -16 + ^
STACK CFI INIT 2824 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 282c .cfa: sp 224 +
STACK CFI 2838 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 285c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2864 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2954 x19: x19 x20: x20
STACK CFI 295c x23: x23 x24: x24
STACK CFI 2988 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2990 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 29d8 x19: x19 x20: x20
STACK CFI 29dc x23: x23 x24: x24
STACK CFI 29e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 29e8 x19: x19 x20: x20
STACK CFI 29ec x23: x23 x24: x24
STACK CFI 29f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2a24 x19: x19 x20: x20
STACK CFI 2a28 x23: x23 x24: x24
STACK CFI 2a2c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2a9c x19: x19 x20: x20
STACK CFI 2aa0 x23: x23 x24: x24
STACK CFI 2ae0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ae4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 2af0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2af8 .cfa: sp 80 +
STACK CFI 2b08 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c20 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2cc4 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2ccc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ce0 x21: .cfa -16 + ^
STACK CFI 2d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2da4 11c .cfa: sp 0 + .ra: x30
STACK CFI 2dac .cfa: sp 80 +
STACK CFI 2dbc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2dc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e24 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2e34 x21: .cfa -16 + ^
STACK CFI 2ea8 x21: x21
STACK CFI 2eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2eb4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2eb8 x21: .cfa -16 + ^
STACK CFI INIT 2ec0 10c .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 320 +
STACK CFI 2edc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ee8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2fc8 .cfa: sp 320 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2fd0 34c .cfa: sp 0 + .ra: x30
STACK CFI 2fd8 .cfa: sp 464 +
STACK CFI 2fe8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ff4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 310c .cfa: sp 464 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3320 4dc .cfa: sp 0 + .ra: x30
STACK CFI 3328 .cfa: sp 320 +
STACK CFI 3338 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3364 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3388 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 33a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3400 x19: x19 x20: x20
STACK CFI 3404 x21: x21 x22: x22
STACK CFI 3408 x23: x23 x24: x24
STACK CFI 3434 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 343c .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3448 x19: x19 x20: x20
STACK CFI 344c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3470 x19: x19 x20: x20
STACK CFI 3474 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3478 x19: x19 x20: x20
STACK CFI 347c x21: x21 x22: x22
STACK CFI 3480 x23: x23 x24: x24
STACK CFI 3484 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3524 x25: .cfa -16 + ^
STACK CFI 36ac x25: x25
STACK CFI 36e4 x19: x19 x20: x20
STACK CFI 36e8 x21: x21 x22: x22
STACK CFI 36ec x23: x23 x24: x24
STACK CFI 36f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36f8 .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3750 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3754 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3758 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 375c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3760 x25: .cfa -16 + ^
STACK CFI 3764 x25: x25
STACK CFI 37a0 x25: .cfa -16 + ^
STACK CFI 37ac x25: x25
STACK CFI 37b0 x25: .cfa -16 + ^
STACK CFI 37e0 x25: x25
STACK CFI 37e4 x25: .cfa -16 + ^
STACK CFI INIT 3800 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 3808 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3828 .cfa: sp 33040 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 388c .cfa: sp 96 +
STACK CFI 38a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 38b0 .cfa: sp 33040 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3ab0 80 .cfa: sp 0 + .ra: x30
STACK CFI 3ab8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ac0 x19: .cfa -16 + ^
STACK CFI 3b04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b30 38 .cfa: sp 0 + .ra: x30
STACK CFI 3b38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b40 x19: .cfa -16 + ^
STACK CFI 3b60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b70 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3b78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b84 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c50 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3c58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c74 x21: .cfa -16 + ^
STACK CFI 3cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3cb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3d24 5f4 .cfa: sp 0 + .ra: x30
STACK CFI 3d2c .cfa: sp 128 +
STACK CFI 3d30 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d50 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3df0 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4320 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 4328 .cfa: sp 80 +
STACK CFI 4338 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4340 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4348 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43e8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4510 680 .cfa: sp 0 + .ra: x30
STACK CFI 4518 .cfa: sp 384 +
STACK CFI 4528 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4530 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4548 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 478c .cfa: sp 384 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4b90 260 .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 336 +
STACK CFI 4ba4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4bb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4be4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4cd8 x23: x23 x24: x24
STACK CFI 4d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d10 .cfa: sp 336 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4d40 x23: x23 x24: x24
STACK CFI 4d48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4d90 x23: x23 x24: x24
STACK CFI 4da0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4dec x23: x23 x24: x24
STACK CFI INIT 4df0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 4df8 .cfa: sp 336 +
STACK CFI 4e04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e58 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4edc x23: x23 x24: x24
STACK CFI 4f28 x19: x19 x20: x20
STACK CFI 4f54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4f5c .cfa: sp 336 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4fc4 x19: x19 x20: x20
STACK CFI 4fd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fd8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 4fe0 68 .cfa: sp 0 + .ra: x30
STACK CFI 4ff0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ff8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5004 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5050 34 .cfa: sp 0 + .ra: x30
STACK CFI 5058 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5060 x19: .cfa -16 + ^
STACK CFI 507c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5084 604 .cfa: sp 0 + .ra: x30
STACK CFI 508c .cfa: sp 192 +
STACK CFI 509c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 50ac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 50c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 50d0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 53a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 53b0 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5690 34 .cfa: sp 0 + .ra: x30
STACK CFI 56a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 56b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 56c4 18c .cfa: sp 0 + .ra: x30
STACK CFI 56cc .cfa: sp 80 +
STACK CFI 56d8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 56e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5700 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 57b8 x23: x23 x24: x24
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57f0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5834 x23: x23 x24: x24
STACK CFI 583c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5840 x23: x23 x24: x24
STACK CFI 584c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5850 228 .cfa: sp 0 + .ra: x30
STACK CFI 5858 .cfa: sp 112 +
STACK CFI 5864 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 587c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 588c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 58a0 x27: .cfa -16 + ^
STACK CFI 58c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 58cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5998 x21: x21 x22: x22
STACK CFI 599c x23: x23 x24: x24
STACK CFI 59a8 x25: x25 x26: x26
STACK CFI 59ac x27: x27
STACK CFI 59d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59e0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5a3c x21: x21 x22: x22
STACK CFI 5a44 x23: x23 x24: x24
STACK CFI 5a50 x25: x25 x26: x26 x27: x27
STACK CFI 5a58 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5a5c x25: x25 x26: x26
STACK CFI 5a68 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5a6c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5a70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5a74 x27: .cfa -16 + ^
STACK CFI INIT 5a80 35c .cfa: sp 0 + .ra: x30
STACK CFI 5a88 .cfa: sp 144 +
STACK CFI 5a94 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5ab4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5ac0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5ac8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5d2c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5de0 18 .cfa: sp 0 + .ra: x30
STACK CFI 5de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5e00 168 .cfa: sp 0 + .ra: x30
STACK CFI 5e10 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5e20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5e34 x23: .cfa -16 + ^
STACK CFI 5ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5f1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5f70 40 .cfa: sp 0 + .ra: x30
STACK CFI 5f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f84 x19: .cfa -16 + ^
STACK CFI 5fa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5fb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5fd0 250 .cfa: sp 0 + .ra: x30
STACK CFI 5fd8 .cfa: sp 112 +
STACK CFI 5fe4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5fec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6000 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6028 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6034 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 60cc x23: x23 x24: x24
STACK CFI 60d0 x25: x25 x26: x26
STACK CFI 6100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6108 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 613c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6198 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 620c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6218 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 621c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 6220 2ac .cfa: sp 0 + .ra: x30
STACK CFI 6228 .cfa: sp 128 +
STACK CFI 6234 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 623c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 625c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6288 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6294 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 62a0 x27: .cfa -16 + ^
STACK CFI 6358 x23: x23 x24: x24
STACK CFI 635c x25: x25 x26: x26
STACK CFI 6360 x27: x27
STACK CFI 6390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6398 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 63cc x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 6428 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 64b4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 64c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 64c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 64c8 x27: .cfa -16 + ^
STACK CFI INIT 64d0 2dc .cfa: sp 0 + .ra: x30
STACK CFI 64d8 .cfa: sp 112 +
STACK CFI 64e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 64f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 651c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6540 x25: .cfa -16 + ^
STACK CFI 6640 x23: x23 x24: x24
STACK CFI 6644 x25: x25
STACK CFI 6674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 667c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 66f4 x25: x25
STACK CFI 6750 x23: x23 x24: x24
STACK CFI 6754 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 6798 x23: x23 x24: x24 x25: x25
STACK CFI 67a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 67a8 x25: .cfa -16 + ^
STACK CFI INIT 67b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 67b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67d4 254 .cfa: sp 0 + .ra: x30
STACK CFI 67dc .cfa: sp 96 +
STACK CFI 67e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 67f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6808 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6828 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 68e4 x21: x21 x22: x22
STACK CFI 68e8 x23: x23 x24: x24
STACK CFI 6914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 691c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6990 x23: x23 x24: x24
STACK CFI 69ec x21: x21 x22: x22
STACK CFI 69f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6a14 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6a20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a24 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 6a30 20 .cfa: sp 0 + .ra: x30
STACK CFI 6a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a50 24 .cfa: sp 0 + .ra: x30
STACK CFI 6a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a6c .cfa: sp 0 + .ra: .ra x29: x29
