MODULE Linux arm64 95873C23C3D797018D7F232B17C7E69E0 libextend_interface.so
INFO CODE_ID 233C8795D7C301978D7F232B17C7E69E
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 1ac0 24 0 init_have_lse_atomics
1ac0 4 45 0
1ac4 4 46 0
1ac8 4 45 0
1acc 4 46 0
1ad0 4 47 0
1ad4 4 47 0
1ad8 4 48 0
1adc 4 47 0
1ae0 4 48 0
PUBLIC 1820 0 _init
PUBLIC 1a40 0 _GLOBAL__sub_I_extend_calib.cpp
PUBLIC 1ae4 0 call_weak_fn
PUBLIC 1b00 0 deregister_tm_clones
PUBLIC 1b30 0 register_tm_clones
PUBLIC 1b70 0 __do_global_dtors_aux
PUBLIC 1bc0 0 frame_dummy
PUBLIC 1bd0 0 lios::extend::ConvertImage::GetBuffParams(std::shared_ptr<lios::camera::camera_stream::StreamImageData> const&)
PUBLIC 1ef0 0 lios::extend::ConvertImage::Convert(std::shared_ptr<lios::camera::camera_stream::StreamImageData> const&, cv::Mat&)
PUBLIC 2620 0 lios::extend::ConvertImage::convert(std::shared_ptr<lios::camera::camera_stream::StreamImageData>*, cv::Mat&)
PUBLIC 2630 0 cv::Mat::~Mat()
PUBLIC 26c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&)
PUBLIC 27d0 0 __aarch64_ldadd4_acq_rel
PUBLIC 2800 0 _fini
STACK CFI INIT 1b00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b70 48 .cfa: sp 0 + .ra: x30
STACK CFI 1b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b7c x19: .cfa -16 + ^
STACK CFI 1bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1bc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2630 90 .cfa: sp 0 + .ra: x30
STACK CFI 2634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2640 x19: .cfa -16 + ^
STACK CFI 26b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1bd0 318 .cfa: sp 0 + .ra: x30
STACK CFI 1bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1bdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1c04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1c30 x23: .cfa -16 + ^
STACK CFI 1c6c x23: x23
STACK CFI 1c78 x21: x21 x22: x22
STACK CFI 1c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1c8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e94 x21: x21 x22: x22
STACK CFI 1e9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ea0 x21: x21 x22: x22
STACK CFI 1ebc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 26c0 104 .cfa: sp 0 + .ra: x30
STACK CFI 26c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 26d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2754 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1ef0 728 .cfa: sp 0 + .ra: x30
STACK CFI 1ef4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 1f04 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 1f10 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 1f2c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 1f38 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 20e8 x23: x23 x24: x24
STACK CFI 20f0 x25: x25 x26: x26
STACK CFI 20f4 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 210c x23: x23 x24: x24
STACK CFI 2114 x25: x25 x26: x26
STACK CFI 213c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2140 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x29: .cfa -304 + ^
STACK CFI 21f0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2214 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 2260 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 22c4 x27: x27 x28: x28
STACK CFI 23e4 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2400 x27: x27 x28: x28
STACK CFI 24fc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2500 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2504 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 2508 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 250c x27: x27 x28: x28
STACK CFI 25dc x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 25e8 x27: x27 x28: x28
STACK CFI 2608 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2610 x27: x27 x28: x28
STACK CFI INIT 2620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a40 7c .cfa: sp 0 + .ra: x30
STACK CFI 1a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a5c x19: .cfa -32 + ^
STACK CFI 1aac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ab8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 27d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1ac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1adc .cfa: sp 0 + .ra: .ra x29: x29
