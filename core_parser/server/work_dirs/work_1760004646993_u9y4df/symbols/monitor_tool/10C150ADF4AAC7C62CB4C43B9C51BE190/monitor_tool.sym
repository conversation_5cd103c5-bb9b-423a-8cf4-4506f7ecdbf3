MODULE Linux arm64 10C150ADF4AAC7C62CB4C43B9C51BE190 monitor_tool
INFO CODE_ID AD50C110AAF4C6C72CB4C43B9C51BE19
PUBLIC 4140 0 _init
PUBLIC 4500 0 main
PUBLIC 4870 0 _GLOBAL__sub_I_monitor_tool.cpp
PUBLIC 48dc 0 _start
PUBLIC 492c 0 call_weak_fn
PUBLIC 4940 0 deregister_tm_clones
PUBLIC 4984 0 register_tm_clones
PUBLIC 49d4 0 __do_global_dtors_aux
PUBLIC 4a04 0 frame_dummy
PUBLIC 4a10 0 PrintHelp()
PUBLIC 4ab0 0 ParamAnalysis(int, char**, lios::monitor::MonitorRequest&, int&)
PUBLIC 4c60 0 std::ctype<char>::do_widen(char) const
PUBLIC 4c70 0 cereal::detail::OutputArchiveBase::rtti()
PUBLIC 4c80 0 cereal::detail::InputArchiveBase::rtti()
PUBLIC 4c90 0 lios::monitor::MonitorRequest::~MonitorRequest()
PUBLIC 4d00 0 cereal::Exception::~Exception()
PUBLIC 4d20 0 cereal::Exception::~Exception()
PUBLIC 4d60 0 cereal::OutputArchive<cereal::PortableBinaryOutputArchive, 1u>::~OutputArchive()
PUBLIC 5000 0 lios::monitor::MonitorResponse::~MonitorResponse()
PUBLIC 50e0 0 lios::monitor::MonitorResponse::~MonitorResponse()
PUBLIC 51c0 0 cereal::InputArchive<cereal::PortableBinaryInputArchive, 1u>::~InputArchive()
PUBLIC 5490 0 lios::monitor::MonitorRequest::~MonitorRequest()
PUBLIC 5510 0 cereal::PortableBinaryInputArchive::~PortableBinaryInputArchive()
PUBLIC 57e0 0 cereal::InputArchive<cereal::PortableBinaryInputArchive, 1u>::~InputArchive()
PUBLIC 5ab0 0 cereal::PortableBinaryInputArchive::~PortableBinaryInputArchive()
PUBLIC 5d80 0 cereal::PortableBinaryOutputArchive::~PortableBinaryOutputArchive()
PUBLIC 6020 0 cereal::OutputArchive<cereal::PortableBinaryOutputArchive, 1u>::~OutputArchive()
PUBLIC 62c0 0 cereal::PortableBinaryOutputArchive::~PortableBinaryOutputArchive()
PUBLIC 6560 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > __gnu_cxx::__to_xstring<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, char>(int (*)(char*, unsigned long, char const*, std::__va_list), unsigned long, char const*, ...)
PUBLIC 66b0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC 67c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(char const*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC 6850 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 68b0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 6910 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 69c0 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*)
PUBLIC 6a10 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 6b40 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, char const*)
PUBLIC 6bf0 0 lios::monitor::MonitorResponse::ToBuffer(std::vector<char, std::allocator<char> >&) const
PUBLIC 8470 0 lios::monitor::MonitorRequest::ToBuffer(std::vector<char, std::allocator<char> >&) const
PUBLIC 9ed0 0 lios::monitor::MonitorRequest::FromBuffer(std::vector<char, std::allocator<char> > const&)
PUBLIC b590 0 void cereal::load<cereal::PortableBinaryInputArchive, std::unordered_map, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >(cereal::PortableBinaryInputArchive&, std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&)
PUBLIC c420 0 lios::monitor::MonitorResponse::FromBuffer(std::vector<char, std::allocator<char> > const&)
PUBLIC d0a0 0 __libc_csu_init
PUBLIC d120 0 __libc_csu_fini
PUBLIC d124 0 _fini
STACK CFI INIT 4940 44 .cfa: sp 0 + .ra: x30
STACK CFI 495c .cfa: sp 16 +
STACK CFI 4974 .cfa: sp 0 +
STACK CFI 4978 .cfa: sp 16 +
STACK CFI 497c .cfa: sp 0 +
STACK CFI INIT 4984 50 .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 16 +
STACK CFI 49c4 .cfa: sp 0 +
STACK CFI 49c8 .cfa: sp 16 +
STACK CFI 49cc .cfa: sp 0 +
STACK CFI INIT 49d4 30 .cfa: sp 0 + .ra: x30
STACK CFI 49d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49e0 x19: .cfa -16 + ^
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a04 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c90 70 .cfa: sp 0 + .ra: x30
STACK CFI 4c94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ca8 x19: .cfa -16 + ^
STACK CFI 4cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4cfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d20 38 .cfa: sp 0 + .ra: x30
STACK CFI 4d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d34 x19: .cfa -16 + ^
STACK CFI 4d54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d60 29c .cfa: sp 0 + .ra: x30
STACK CFI 4d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d80 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5000 dc .cfa: sp 0 + .ra: x30
STACK CFI 5004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5014 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 502c x21: .cfa -16 + ^
STACK CFI 5084 x21: x21
STACK CFI 50d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 50e0 dc .cfa: sp 0 + .ra: x30
STACK CFI 50e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 510c x21: .cfa -16 + ^
STACK CFI 5164 x21: x21
STACK CFI 51ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 51b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 51b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 51c0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 51c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 51e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5408 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5490 74 .cfa: sp 0 + .ra: x30
STACK CFI 5494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54a8 x19: .cfa -16 + ^
STACK CFI 5500 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5510 2cc .cfa: sp 0 + .ra: x30
STACK CFI 5514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5530 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5758 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 57e0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5800 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ab0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 5ab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ac4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ad0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d80 29c .cfa: sp 0 + .ra: x30
STACK CFI 5d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5da0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5fa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6020 29c .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6034 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6040 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 624c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 62c0 29c .cfa: sp 0 + .ra: x30
STACK CFI 62c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 64ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4a10 94 .cfa: sp 0 + .ra: x30
STACK CFI 4a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ab0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 4ab4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4abc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4ac8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4ad0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4ae0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4af0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4bbc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4500 368 .cfa: sp 0 + .ra: x30
STACK CFI 4504 .cfa: sp 512 +
STACK CFI 450c .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 4514 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 451c x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 4528 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 453c x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 45e8 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 4714 x27: x27 x28: x28
STACK CFI 4738 x21: x21 x22: x22
STACK CFI 473c x23: x23 x24: x24
STACK CFI 4740 x25: x25 x26: x26
STACK CFI 4750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4754 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x29: .cfa -512 + ^
STACK CFI 4760 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 47a4 x27: x27 x28: x28
STACK CFI 47a8 x21: x21 x22: x22
STACK CFI 47ac x23: x23 x24: x24
STACK CFI 47b0 x25: x25 x26: x26
STACK CFI 47b4 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 47f0 x25: x25 x26: x26
STACK CFI 47f4 x21: x21 x22: x22
STACK CFI 47f8 x23: x23 x24: x24
STACK CFI 4804 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 4810 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 485c x27: x27 x28: x28
STACK CFI 4864 x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 6560 150 .cfa: sp 0 + .ra: x30
STACK CFI 6564 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 6570 .cfa: x29 304 +
STACK CFI 6588 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 65a0 x21: .cfa -272 + ^
STACK CFI 6630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6634 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 6654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6658 .cfa: x29 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x29: .cfa -304 + ^
STACK CFI 66ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 66b0 108 .cfa: sp 0 + .ra: x30
STACK CFI 66bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 66d0 x19: .cfa -16 + ^
STACK CFI 6750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6754 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 67a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 67a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 67c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 67c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 67cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 67d8 x21: .cfa -16 + ^
STACK CFI 6840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6844 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6850 54 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6868 x19: .cfa -16 + ^
STACK CFI 68a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 68b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 68b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 68c8 x19: .cfa -16 + ^
STACK CFI 690c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6910 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 691c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 693c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6940 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 69b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 69c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 69fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6a10 12c .cfa: sp 0 + .ra: x30
STACK CFI 6a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6a1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6a2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a50 x21: x21 x22: x22
STACK CFI 6a5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a60 x23: .cfa -16 + ^
STACK CFI 6afc x21: x21 x22: x22
STACK CFI 6b00 x23: x23
STACK CFI 6b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 6b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6b40 ac .cfa: sp 0 + .ra: x30
STACK CFI 6b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6b4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6b5c x21: .cfa -16 + ^
STACK CFI 6bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6bf0 1878 .cfa: sp 0 + .ra: x30
STACK CFI 6bf4 .cfa: sp 1072 +
STACK CFI 6bf8 .ra: .cfa -1064 + ^ x29: .cfa -1072 + ^
STACK CFI 6c00 x19: .cfa -1056 + ^ x20: .cfa -1048 + ^
STACK CFI 6c0c x21: .cfa -1040 + ^ x22: .cfa -1032 + ^
STACK CFI 6c20 x23: .cfa -1024 + ^ x24: .cfa -1016 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^
STACK CFI 740c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7410 .cfa: sp 1072 + .ra: .cfa -1064 + ^ x19: .cfa -1056 + ^ x20: .cfa -1048 + ^ x21: .cfa -1040 + ^ x22: .cfa -1032 + ^ x23: .cfa -1024 + ^ x24: .cfa -1016 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^ x29: .cfa -1072 + ^
STACK CFI INIT 8470 1a54 .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 1072 +
STACK CFI 8478 .ra: .cfa -1064 + ^ x29: .cfa -1072 + ^
STACK CFI 8480 x19: .cfa -1056 + ^ x20: .cfa -1048 + ^
STACK CFI 848c x21: .cfa -1040 + ^ x22: .cfa -1032 + ^
STACK CFI 84a0 x23: .cfa -1024 + ^ x24: .cfa -1016 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^
STACK CFI 8ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8ca8 .cfa: sp 1072 + .ra: .cfa -1064 + ^ x19: .cfa -1056 + ^ x20: .cfa -1048 + ^ x21: .cfa -1040 + ^ x22: .cfa -1032 + ^ x23: .cfa -1024 + ^ x24: .cfa -1016 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^ x29: .cfa -1072 + ^
STACK CFI INIT 9ed0 16bc .cfa: sp 0 + .ra: x30
STACK CFI 9ed4 .cfa: sp 992 +
STACK CFI 9ed8 .ra: .cfa -984 + ^ x29: .cfa -992 + ^
STACK CFI 9ee0 x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI 9eec x23: .cfa -944 + ^ x24: .cfa -936 + ^
STACK CFI 9ef8 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI 9f04 x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI a6a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a6a8 .cfa: sp 992 + .ra: .cfa -984 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^ x29: .cfa -992 + ^
STACK CFI INIT b590 e90 .cfa: sp 0 + .ra: x30
STACK CFI b594 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI b5ac x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI b678 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI b684 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI b690 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI ba30 x23: x23 x24: x24
STACK CFI ba34 x25: x25 x26: x26
STACK CFI ba38 x27: x27 x28: x28
STACK CFI ba44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ba48 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^ x29: .cfa -400 + ^
STACK CFI bbb8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bbbc x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI bbcc x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI bc2c x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI bd84 x27: x27 x28: x28
STACK CFI bdb4 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI bdcc x27: x27 x28: x28
STACK CFI bdd0 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI bdd8 x27: x27 x28: x28
STACK CFI bde0 x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI c410 x27: x27 x28: x28
STACK CFI INIT c420 c80 .cfa: sp 0 + .ra: x30
STACK CFI c424 .cfa: sp 976 +
STACK CFI c428 .ra: .cfa -968 + ^ x29: .cfa -976 + ^
STACK CFI c430 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI c43c x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI c444 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI c454 x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI ca00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ca04 .cfa: sp 976 + .ra: .cfa -968 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^ x29: .cfa -976 + ^
STACK CFI INIT 4870 6c .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 487c x19: .cfa -16 + ^
STACK CFI 48bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT d0a0 7c .cfa: sp 0 + .ra: x30
STACK CFI d0a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d0ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d0b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d0cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d120 4 .cfa: sp 0 + .ra: x30
