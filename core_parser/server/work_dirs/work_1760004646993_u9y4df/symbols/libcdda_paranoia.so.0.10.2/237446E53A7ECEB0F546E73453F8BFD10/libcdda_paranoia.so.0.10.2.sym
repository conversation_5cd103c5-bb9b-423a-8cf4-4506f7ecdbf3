MODULE Linux arm64 237446E53A7ECEB0F546E73453F8BFD10 libcdda_paranoia.so.0
INFO CODE_ID E54674237E3AB0CEF546E73453F8BFD14B16351E
PUBLIC 23c0 0 paranoia_free
PUBLIC 2404 0 paranoia_modeset
PUBLIC 2410 0 paranoia_seek
PUBLIC 24a0 0 i_read_c_block
PUBLIC 29f4 0 paranoia_read_limited
PUBLIC 4710 0 paranoia_read
PUBLIC 4720 0 paranoia_overlapset
PUBLIC 4740 0 paranoia_version
PUBLIC 4770 0 i_cblock_destructor
PUBLIC 47b4 0 new_list
PUBLIC 47e4 0 add_elem
PUBLIC 4870 0 new_elem
PUBLIC 48a0 0 free_elem
PUBLIC 4924 0 free_list
PUBLIC 4960 0 get_elem
PUBLIC 4970 0 copy_list
PUBLIC 49c0 0 new_c_block
PUBLIC 49f0 0 new_v_fragment
PUBLIC 4a60 0 free_v_fragment
PUBLIC 4a70 0 c_first
PUBLIC 4a84 0 c_last
PUBLIC 4aa0 0 c_next
PUBLIC 4ab4 0 c_prev
PUBLIC 4ad0 0 v_first
PUBLIC 4ae4 0 v_last
PUBLIC 4b00 0 v_next
PUBLIC 4b14 0 free_c_block
PUBLIC 4b80 0 v_prev
PUBLIC 4b94 0 recover_cache
PUBLIC 4be0 0 v_buffer
PUBLIC 4c00 0 c_alloc
PUBLIC 4c40 0 c_set
PUBLIC 4c50 0 c_insert
PUBLIC 4d14 0 c_remove
PUBLIC 4da0 0 c_overwrite
PUBLIC 4dd4 0 c_append
PUBLIC 4e60 0 c_removef
PUBLIC 4ea0 0 i_paranoia_firstlast
PUBLIC 4fc0 0 paranoia_init
PUBLIC 5090 0 paranoia_cachemodel_size
PUBLIC 50b0 0 paranoia_resetcache
PUBLIC 5104 0 paranoia_resetall
PUBLIC 5140 0 i_paranoia_trim
PUBLIC 5210 0 offset_adjust_settings
PUBLIC 5440 0 offset_add_value
PUBLIC 54b0 0 i_paranoia_overlap_r
PUBLIC 54f0 0 i_paranoia_overlap_f
PUBLIC 5540 0 i_stutter_or_gap
PUBLIC 5570 0 i_analyze_rift_f
PUBLIC 57a0 0 i_analyze_rift_r
PUBLIC 59a0 0 analyze_rift_silence_f
PUBLIC 5a30 0 sort_alloc
PUBLIC 5aa4 0 sort_unsortall
PUBLIC 5b20 0 sort_free
PUBLIC 5b60 0 sort_setup
PUBLIC 5bf0 0 sort_getmatch
PUBLIC 5d04 0 sort_nextmatch
STACK CFI INIT 1de0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e10 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e50 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e5c x19: .cfa -16 + ^
STACK CFI 1e94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1eb0 200 .cfa: sp 0 + .ra: x30
STACK CFI 1eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ed4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1eec x19: .cfa -16 + ^
STACK CFI 1fa4 x19: x19
STACK CFI 1fcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20a0 x19: .cfa -16 + ^
STACK CFI INIT 20b0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2120 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2140 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2190 19c .cfa: sp 0 + .ra: x30
STACK CFI 2194 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 219c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 21ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 21b8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 21c4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 21cc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 22a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 22a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 2330 88 .cfa: sp 0 + .ra: x30
STACK CFI 2334 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 233c x21: .cfa -16 + ^
STACK CFI 2344 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 239c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 23b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 23c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 23c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23cc x19: .cfa -16 + ^
STACK CFI 2400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2404 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2410 90 .cfa: sp 0 + .ra: x30
STACK CFI 2414 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 241c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2428 x21: .cfa -16 + ^
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2488 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 24a0 554 .cfa: sp 0 + .ra: x30
STACK CFI 24a4 .cfa: sp 192 +
STACK CFI 24b8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 24e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2500 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2834 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 29f4 1d1c .cfa: sp 0 + .ra: x30
STACK CFI 29f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2a04 .cfa: x29 96 +
STACK CFI 2a20 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f6c .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4710 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4720 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4740 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4750 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4760 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4770 40 .cfa: sp 0 + .ra: x30
STACK CFI 4778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4780 x19: .cfa -16 + ^
STACK CFI 47a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47b4 30 .cfa: sp 0 + .ra: x30
STACK CFI 47b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 47e4 88 .cfa: sp 0 + .ra: x30
STACK CFI 47e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4848 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4870 2c .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 487c x19: .cfa -16 + ^
STACK CFI 4898 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 48a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4914 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4924 3c .cfa: sp 0 + .ra: x30
STACK CFI 4928 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4930 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 495c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4970 4c .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 497c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 49c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 49c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49cc x19: .cfa -16 + ^
STACK CFI 49ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 49f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 49f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49fc x23: .cfa -16 + ^
STACK CFI 4a08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4a60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a84 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4aa0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ab4 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ad0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ae4 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b14 6c .cfa: sp 0 + .ra: x30
STACK CFI 4b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b20 x21: .cfa -16 + ^
STACK CFI 4b34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b5c x19: x19 x20: x20
STACK CFI 4b6c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b80 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b94 4c .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ba0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4be0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c00 40 .cfa: sp 0 + .ra: x30
STACK CFI 4c04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c1c x21: .cfa -16 + ^
STACK CFI 4c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c50 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4c58 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4c68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c80 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4c88 x25: .cfa -16 + ^
STACK CFI 4cc8 x25: x25
STACK CFI 4cd0 x21: x21 x22: x22
STACK CFI 4ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 4ce8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4d14 88 .cfa: sp 0 + .ra: x30
STACK CFI 4d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4da0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4dd4 84 .cfa: sp 0 + .ra: x30
STACK CFI 4dd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4de0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4df0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4dfc x23: .cfa -16 + ^
STACK CFI 4e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4e48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4e60 38 .cfa: sp 0 + .ra: x30
STACK CFI 4e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ea0 11c .cfa: sp 0 + .ra: x30
STACK CFI 4ea4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4eb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ebc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4fc0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5090 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50b0 54 .cfa: sp 0 + .ra: x30
STACK CFI 50b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50bc x19: .cfa -16 + ^
STACK CFI 5100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5104 38 .cfa: sp 0 + .ra: x30
STACK CFI 5108 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5110 x19: .cfa -16 + ^
STACK CFI 5138 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5140 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5144 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 514c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5174 x21: .cfa -16 + ^
STACK CFI 519c x21: x21
STACK CFI 51b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 51bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5208 x21: x21
STACK CFI 520c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5210 230 .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 521c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5234 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5328 x19: x19 x20: x20
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 53fc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5438 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 543c x19: x19 x20: x20
STACK CFI INIT 5440 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54b0 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54f0 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5570 22c .cfa: sp 0 + .ra: x30
STACK CFI 5574 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 557c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5594 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 55a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 55ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 55b8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5634 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 572c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 5788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 578c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 57a0 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 57a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 57ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 57c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 57d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 57dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 57e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 584c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 592c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5988 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 59a0 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a30 74 .cfa: sp 0 + .ra: x30
STACK CFI 5a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5aa4 7c .cfa: sp 0 + .ra: x30
STACK CFI 5aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ab4 x19: .cfa -16 + ^
STACK CFI 5af4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b20 38 .cfa: sp 0 + .ra: x30
STACK CFI 5b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b2c x19: .cfa -16 + ^
STACK CFI 5b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b60 90 .cfa: sp 0 + .ra: x30
STACK CFI 5b64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b6c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5bf0 114 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d04 24 .cfa: sp 0 + .ra: x30
