MODULE Linux arm64 CF7F81D58EC348E8261FF107DE6061B80 libcanberra-gtk3.so.0
INFO CODE_ID D5817FCFC38EE848261FF107DE6061B8C417D4C2
PUBLIC 1d90 0 ca_gtk_context_get_for_screen
PUBLIC 2044 0 ca_gtk_context_get
PUBLIC 2060 0 ca_gtk_proplist_set_for_widget
PUBLIC 27a0 0 ca_gtk_proplist_set_for_event
PUBLIC 2ae0 0 ca_gtk_play_for_widget
PUBLIC 2d04 0 ca_gtk_play_for_event
PUBLIC 2f34 0 ca_gtk_widget_disable_sounds
STACK CFI INIT 1b30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ba0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bac x19: .cfa -16 + ^
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1bf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1c08 .cfa: sp 48 +
STACK CFI 1c14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ca8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1cd0 20 .cfa: sp 0 + .ra: x30
STACK CFI 1cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1cf0 9c .cfa: sp 0 + .ra: x30
STACK CFI 1cf8 .cfa: sp 48 +
STACK CFI 1d08 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d14 x19: .cfa -16 + ^
STACK CFI 1d80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d88 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d90 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 1d98 .cfa: sp 80 +
STACK CFI 1da4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1dac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1db4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e1c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2044 1c .cfa: sp 0 + .ra: x30
STACK CFI 204c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2060 740 .cfa: sp 0 + .ra: x30
STACK CFI 2068 .cfa: sp 160 +
STACK CFI 2074 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2080 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 21d0 .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 21e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2564 x23: x23 x24: x24
STACK CFI 2574 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 257c x23: x23 x24: x24
STACK CFI 2604 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 261c x23: x23 x24: x24
STACK CFI 269c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 26c4 x23: x23 x24: x24
STACK CFI 26c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 26ec x23: x23 x24: x24
STACK CFI 26f0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2714 x23: x23 x24: x24
STACK CFI 2718 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2760 x23: x23 x24: x24
STACK CFI 2764 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2798 x23: x23 x24: x24
STACK CFI 279c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 27a0 340 .cfa: sp 0 + .ra: x30
STACK CFI 27a8 .cfa: sp 112 +
STACK CFI 27b4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 286c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 28b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 28c4 x25: .cfa -16 + ^
STACK CFI 28cc v8: .cfa -8 + ^
STACK CFI 298c x23: x23 x24: x24
STACK CFI 2990 x25: x25
STACK CFI 2994 v8: v8
STACK CFI 2abc v8: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2ac0 x23: x23 x24: x24
STACK CFI 2ac8 x25: x25
STACK CFI 2acc v8: v8
STACK CFI 2ad4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2ad8 x25: .cfa -16 + ^
STACK CFI 2adc v8: .cfa -8 + ^
STACK CFI INIT 2ae0 224 .cfa: sp 0 + .ra: x30
STACK CFI 2ae8 .cfa: sp 304 +
STACK CFI 2af4 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2afc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2b40 x21: .cfa -192 + ^
STACK CFI 2be0 x21: x21
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c14 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI 2c24 x21: .cfa -192 + ^
STACK CFI 2c30 x21: x21
STACK CFI 2c38 x21: .cfa -192 + ^
STACK CFI 2c78 x21: x21
STACK CFI 2cbc x21: .cfa -192 + ^
STACK CFI 2cfc x21: x21
STACK CFI 2d00 x21: .cfa -192 + ^
STACK CFI INIT 2d04 230 .cfa: sp 0 + .ra: x30
STACK CFI 2d0c .cfa: sp 304 +
STACK CFI 2d18 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2d20 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2d64 x21: .cfa -192 + ^
STACK CFI 2e08 x21: x21
STACK CFI 2e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e3c .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI 2e44 x21: x21
STACK CFI 2e54 x21: .cfa -192 + ^
STACK CFI 2e60 x21: x21
STACK CFI 2e68 x21: .cfa -192 + ^
STACK CFI 2ea8 x21: x21
STACK CFI 2eec x21: .cfa -192 + ^
STACK CFI 2f2c x21: x21
STACK CFI 2f30 x21: .cfa -192 + ^
STACK CFI INIT 2f34 60 .cfa: sp 0 + .ra: x30
STACK CFI 2f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f44 x21: .cfa -16 + ^
STACK CFI 2f4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2f80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
