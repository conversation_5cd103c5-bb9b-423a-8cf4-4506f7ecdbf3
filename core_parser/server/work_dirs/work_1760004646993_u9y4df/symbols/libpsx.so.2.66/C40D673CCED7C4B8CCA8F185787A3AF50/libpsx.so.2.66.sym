MODULE Linux arm64 C40D673CCED7C4B8CCA8F185787A3AF50 libpsx.so.2
INFO CODE_ID 3C670DC4D7CEB8C4CCA8F185787A3AF5E0A06964
PUBLIC 2020 0 psx_load_syscalls
PUBLIC 2050 0 __wrap_pthread_create
PUBLIC 2250 0 __psx_syscall
PUBLIC 2890 0 psx_syscall3
PUBLIC 28d4 0 psx_syscall6
PUBLIC 2920 0 psx_set_sensitivity
PUBLIC 29a0 0 __so_start
STACK CFI INIT 1570 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 15e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15ec x19: .cfa -16 + ^
STACK CFI 1624 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1640 94 .cfa: sp 0 + .ra: x30
STACK CFI 1648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1658 x19: .cfa -16 + ^
STACK CFI 16b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 16c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16d4 f8 .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1760 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 176c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1788 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1798 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 17d0 118 .cfa: sp 0 + .ra: x30
STACK CFI 17d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1840 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 184c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1854 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 18cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 18f0 12c .cfa: sp 0 + .ra: x30
STACK CFI 18f8 .cfa: sp 464 +
STACK CFI 1904 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 190c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1918 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a18 .cfa: sp 464 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a20 90 .cfa: sp 0 + .ra: x30
STACK CFI 1a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a34 x19: .cfa -16 + ^
STACK CFI 1aa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ab0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ad0 x19: .cfa -16 + ^
STACK CFI 1af8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b00 80 .cfa: sp 0 + .ra: x30
STACK CFI 1b08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b1c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1b80 88 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b9c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1c10 198 .cfa: sp 0 + .ra: x30
STACK CFI 1c18 .cfa: sp 352 +
STACK CFI 1c24 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1c2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1c34 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1c40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1c4c x25: .cfa -16 + ^
STACK CFI 1d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1da4 .cfa: sp 352 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1db0 150 .cfa: sp 0 + .ra: x30
STACK CFI 1db8 .cfa: sp 304 +
STACK CFI 1dc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1dcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1dd8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1eec .cfa: sp 304 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f00 118 .cfa: sp 0 + .ra: x30
STACK CFI 1f08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f1c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ff0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2020 30 .cfa: sp 0 + .ra: x30
STACK CFI 2028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2044 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2050 1fc .cfa: sp 0 + .ra: x30
STACK CFI 2058 .cfa: sp 368 +
STACK CFI 2064 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 206c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 207c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2084 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2090 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 21cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 21d4 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2250 638 .cfa: sp 0 + .ra: x30
STACK CFI 2258 .cfa: sp 320 +
STACK CFI 2260 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2284 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 22c4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 22d0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 23c4 x19: x19 x20: x20
STACK CFI 23c8 x25: x25 x26: x26
STACK CFI 23f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2400 .cfa: sp 320 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 2404 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2654 x19: x19 x20: x20
STACK CFI 2658 x25: x25 x26: x26
STACK CFI 265c x27: x27 x28: x28
STACK CFI 2660 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2708 x27: x27 x28: x28
STACK CFI 271c x19: x19 x20: x20
STACK CFI 2720 x25: x25 x26: x26
STACK CFI 2724 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2864 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 287c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2880 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2884 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2890 44 .cfa: sp 0 + .ra: x30
STACK CFI 2898 .cfa: sp 48 +
STACK CFI 28ac .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 28d4 48 .cfa: sp 0 + .ra: x30
STACK CFI 28dc .cfa: sp 64 +
STACK CFI 28f0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2920 80 .cfa: sp 0 + .ra: x30
STACK CFI 2928 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2940 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2954 x21: .cfa -16 + ^
STACK CFI 2974 x19: x19 x20: x20
STACK CFI 297c x21: x21
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2988 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29a0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 29a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 29c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 2b60 1c .cfa: sp 0 + .ra: x30
STACK CFI 2b64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b80 18 .cfa: sp 0 + .ra: x30
STACK CFI 2b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b90 .cfa: sp 0 + .ra: .ra x29: x29
