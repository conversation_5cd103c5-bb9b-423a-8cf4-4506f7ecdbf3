MODULE Linux arm64 54FC00D5690EF695C4294A6517309A6A0 libpipeline.so.1
INFO CODE_ID D500FC540E6995F6C4294A6517309A6A9D3314E2
PUBLIC 3630 0 pipecmd_new_function
PUBLIC 36e0 0 pipecmd_new_passthrough
PUBLIC 3710 0 pipecmd_dup
PUBLIC 39e0 0 pipecmd_arg
PUBLIC 3ae0 0 pipecmd_new
PUBLIC 3ca4 0 pipecmd_new_argstr
PUBLIC 3d60 0 pipecmd_argf
PUBLIC 3e40 0 pipecmd_argv
PUBLIC 3f40 0 pipecmd_new_argv
PUBLIC 3f80 0 pipecmd_new_args
PUBLIC 4040 0 pipecmd_args
PUBLIC 4120 0 pipecmd_argstr
PUBLIC 41b0 0 pipecmd_get_nargs
PUBLIC 41f4 0 pipecmd_nice
PUBLIC 4210 0 pipecmd_discard_err
PUBLIC 4230 0 pipecmd_chdir
PUBLIC 4270 0 pipecmd_fchdir
PUBLIC 4290 0 pipecmd_setenv
PUBLIC 4340 0 pipecmd_unsetenv
PUBLIC 43e4 0 pipecmd_clearenv
PUBLIC 4470 0 pipecmd_pre_exec
PUBLIC 4490 0 pipecmd_sequence_command
PUBLIC 4564 0 pipecmd_new_sequencev
PUBLIC 46d0 0 pipecmd_new_sequence
PUBLIC 4790 0 pipecmd_dump
PUBLIC 49b4 0 pipecmd_tostring
PUBLIC 4c70 0 pipecmd_exec
PUBLIC 50e0 0 pipecmd_free
PUBLIC 51f0 0 pipeline_new
PUBLIC 5280 0 pipeline_join
PUBLIC 54c0 0 pipeline_command
PUBLIC 5564 0 pipeline_new_command_argv
PUBLIC 55c0 0 pipeline_new_command_args
PUBLIC 5680 0 pipeline_command_argv
PUBLIC 56c4 0 pipeline_command_args
PUBLIC 5780 0 pipeline_command_argstr
PUBLIC 57b4 0 pipeline_commandv
PUBLIC 5870 0 pipeline_new_commandv
PUBLIC 58d0 0 pipeline_new_commands
PUBLIC 5990 0 pipeline_commands
PUBLIC 5a50 0 pipeline_get_ncommands
PUBLIC 5a70 0 pipeline_get_command
PUBLIC 5ab0 0 pipeline_set_command
PUBLIC 5b00 0 pipeline_get_pid
PUBLIC 5b60 0 pipeline_want_in
PUBLIC 5b90 0 pipeline_want_out
PUBLIC 5bc0 0 pipeline_connect
PUBLIC 5d60 0 pipeline_want_infile
PUBLIC 5dc0 0 pipeline_want_outfile
PUBLIC 5e20 0 pipeline_ignore_signals
PUBLIC 5e40 0 pipeline_get_infile
PUBLIC 5f14 0 pipeline_get_outfile
PUBLIC 5ff0 0 pipeline_dump
PUBLIC 60c0 0 pipeline_tostring
PUBLIC 61a0 0 pipeline_install_post_fork
PUBLIC 61c0 0 pipeline_start
PUBLIC 69b0 0 pipeline_wait_all
PUBLIC 6fd0 0 pipeline_wait
PUBLIC 6ff0 0 pipeline_free
PUBLIC 7094 0 pipeline_run
PUBLIC 70e0 0 pipeline_read
PUBLIC 7100 0 pipeline_peek
PUBLIC 7120 0 pipeline_peek_size
PUBLIC 7154 0 pipeline_peek_skip
PUBLIC 71e0 0 pipeline_pump
PUBLIC 7e30 0 pipeline_readline
PUBLIC 7eb0 0 pipeline_peekline
STACK CFI INIT 22b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2320 48 .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 232c x19: .cfa -16 + ^
STACK CFI 2364 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2380 64 .cfa: sp 0 + .ra: x30
STACK CFI 2398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23e4 14c .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 384 +
STACK CFI 23f8 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 2400 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2488 .cfa: sp 384 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x29: .cfa -240 + ^
STACK CFI 2494 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 24c8 x21: x21 x22: x22
STACK CFI 2520 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2524 x21: x21 x22: x22
STACK CFI 252c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI INIT 2530 30 .cfa: sp 0 + .ra: x30
STACK CFI 253c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2560 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2568 .cfa: sp 176 +
STACK CFI 2574 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2584 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 270c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2714 .cfa: sp 176 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2734 48 .cfa: sp 0 + .ra: x30
STACK CFI 273c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2744 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2778 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2780 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 96 +
STACK CFI 278c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2794 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 279c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 27c4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 27f4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 28a4 x25: x25 x26: x26
STACK CFI 28d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28e0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 29fc x25: x25 x26: x26
STACK CFI 2a00 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2a10 x25: x25 x26: x26
STACK CFI 2a14 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2a20 x25: x25 x26: x26
STACK CFI 2a28 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2a2c x25: x25 x26: x26
STACK CFI 2a30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 2a34 17c .cfa: sp 0 + .ra: x30
STACK CFI 2a3c .cfa: sp 80 +
STACK CFI 2a48 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a50 x23: .cfa -16 + ^
STACK CFI 2a5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2b00 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2bb0 64 .cfa: sp 0 + .ra: x30
STACK CFI 2be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c14 8c .cfa: sp 0 + .ra: x30
STACK CFI 2c1c .cfa: sp 80 +
STACK CFI 2c2c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c90 .cfa: sp 80 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2ca0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2ca8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2cb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2cb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2cc4 x23: .cfa -16 + ^
STACK CFI 2d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2d20 160 .cfa: sp 0 + .ra: x30
STACK CFI 2d28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d44 .cfa: sp 4192 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2e34 .cfa: sp 80 +
STACK CFI 2e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2e50 .cfa: sp 4192 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2e80 1ec .cfa: sp 0 + .ra: x30
STACK CFI 2e88 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2e90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2ea8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2ef4 x25: .cfa -16 + ^
STACK CFI 2f14 x25: x25
STACK CFI 2f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2fcc x25: .cfa -16 + ^
STACK CFI 2ff0 x25: x25
STACK CFI 3038 x25: .cfa -16 + ^
STACK CFI 303c x25: x25
STACK CFI 3060 x25: .cfa -16 + ^
STACK CFI 3064 x25: x25
STACK CFI 3068 x25: .cfa -16 + ^
STACK CFI INIT 3070 124 .cfa: sp 0 + .ra: x30
STACK CFI 3078 .cfa: sp 96 +
STACK CFI 307c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3084 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 30a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 318c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3194 210 .cfa: sp 0 + .ra: x30
STACK CFI 319c .cfa: sp 144 +
STACK CFI 31a0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 31ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 31bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 31e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 326c x27: .cfa -16 + ^
STACK CFI 32ec x19: x19 x20: x20
STACK CFI 32f0 x27: x27
STACK CFI 3328 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3330 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3360 x27: x27
STACK CFI 336c x19: x19 x20: x20
STACK CFI 3390 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3394 x27: .cfa -16 + ^
STACK CFI 3398 x19: x19 x20: x20 x27: x27
STACK CFI 339c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 33a0 x27: .cfa -16 + ^
STACK CFI INIT 33a4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 96 +
STACK CFI 33bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 344c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3454 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3490 dc .cfa: sp 0 + .ra: x30
STACK CFI 3498 .cfa: sp 352 +
STACK CFI 34a8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 34b8 x19: .cfa -192 + ^
STACK CFI INIT 3570 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3578 .cfa: sp 288 +
STACK CFI 3588 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 362c .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3630 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3638 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3640 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 364c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3658 x23: .cfa -16 + ^
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 36dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 36e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 36e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3710 2cc .cfa: sp 0 + .ra: x30
STACK CFI 3718 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3724 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3734 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 37c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3800 x25: x25 x26: x26
STACK CFI 3884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 388c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 3920 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 394c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 3954 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3960 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3964 x25: x25 x26: x26
STACK CFI 3988 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 398c x25: x25 x26: x26
STACK CFI 39b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 39b4 x25: x25 x26: x26
STACK CFI 39d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 39e0 100 .cfa: sp 0 + .ra: x30
STACK CFI 39e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a00 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3a58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ae0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3ae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3af4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b04 v8: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3c5c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3c64 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ca4 bc .cfa: sp 0 + .ra: x30
STACK CFI 3cac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cb4 x21: .cfa -32 + ^
STACK CFI 3cbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d60 dc .cfa: sp 0 + .ra: x30
STACK CFI 3d68 .cfa: sp 288 +
STACK CFI 3d78 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3d88 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e38 .cfa: sp 288 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3e40 fc .cfa: sp 0 + .ra: x30
STACK CFI 3e48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3eb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f40 40 .cfa: sp 0 + .ra: x30
STACK CFI 3f48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f50 x19: .cfa -48 + ^
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f80 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3f88 .cfa: sp 288 +
STACK CFI 3f98 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 402c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4034 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 4040 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4048 .cfa: sp 288 +
STACK CFI 4054 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40f8 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 4120 88 .cfa: sp 0 + .ra: x30
STACK CFI 4128 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4130 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 413c x21: .cfa -32 + ^
STACK CFI 417c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4184 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 41b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 41f4 1c .cfa: sp 0 + .ra: x30
STACK CFI 41fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4208 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4210 1c .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4230 3c .cfa: sp 0 + .ra: x30
STACK CFI 4238 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4240 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4270 1c .cfa: sp 0 + .ra: x30
STACK CFI 4278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4290 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4298 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 42fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4304 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4340 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4348 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4350 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4358 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4368 x23: .cfa -16 + ^
STACK CFI 43a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 43a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43e4 8c .cfa: sp 0 + .ra: x30
STACK CFI 43ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4400 x21: .cfa -16 + ^
STACK CFI 442c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4434 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4470 20 .cfa: sp 0 + .ra: x30
STACK CFI 4478 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4480 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4490 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4498 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4530 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4564 16c .cfa: sp 0 + .ra: x30
STACK CFI 456c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4580 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 458c x23: .cfa -16 + ^
STACK CFI 4594 v8: .cfa -8 + ^
STACK CFI 4688 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4690 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 46d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 46d8 .cfa: sp 288 +
STACK CFI 46e8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 477c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4784 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 4790 224 .cfa: sp 0 + .ra: x30
STACK CFI 4798 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 47a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47ec x23: .cfa -16 + ^
STACK CFI 4864 x23: x23
STACK CFI 48a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 48a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 49ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 49b4 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 49bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4b48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4c50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c70 470 .cfa: sp 0 + .ra: x30
STACK CFI 4c78 .cfa: sp 256 +
STACK CFI 4c84 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d40 x25: .cfa -16 + ^
STACK CFI 4d68 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4d70 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4d90 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4e5c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4e60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4e64 x25: .cfa -16 + ^
STACK CFI 4ee4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4eec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4ef0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ef4 x25: .cfa -16 + ^
STACK CFI 4f40 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4f60 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 50e0 10c .cfa: sp 0 + .ra: x30
STACK CFI 50f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5120 x21: .cfa -16 + ^
STACK CFI 5154 x21: x21
STACK CFI 51ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 51b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 51f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 51f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5204 x19: .cfa -16 + ^
STACK CFI 5274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 527c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5280 240 .cfa: sp 0 + .ra: x30
STACK CFI 5288 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5294 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 52a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 541c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 54c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 550c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 554c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5554 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5564 54 .cfa: sp 0 + .ra: x30
STACK CFI 556c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 55c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 55c8 .cfa: sp 288 +
STACK CFI 55d8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 566c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5674 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 5680 44 .cfa: sp 0 + .ra: x30
STACK CFI 5688 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5698 x19: .cfa -48 + ^
STACK CFI 56bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 56c4 b4 .cfa: sp 0 + .ra: x30
STACK CFI 56cc .cfa: sp 272 +
STACK CFI 56dc .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 576c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5774 .cfa: sp 272 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5780 34 .cfa: sp 0 + .ra: x30
STACK CFI 5788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5790 x19: .cfa -16 + ^
STACK CFI 57ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 57b4 bc .cfa: sp 0 + .ra: x30
STACK CFI 57bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5840 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5870 58 .cfa: sp 0 + .ra: x30
STACK CFI 5878 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5880 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5888 x21: .cfa -48 + ^
STACK CFI 58c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 58d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 58d8 .cfa: sp 288 +
STACK CFI 58e8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 597c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5984 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 5990 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5998 .cfa: sp 288 +
STACK CFI 59a8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 5a3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a44 .cfa: sp 288 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI INIT 5a50 1c .cfa: sp 0 + .ra: x30
STACK CFI 5a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a70 40 .cfa: sp 0 + .ra: x30
STACK CFI 5a78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5aa0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ab0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5aec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b00 5c .cfa: sp 0 + .ra: x30
STACK CFI 5b34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5b60 28 .cfa: sp 0 + .ra: x30
STACK CFI 5b68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b90 28 .cfa: sp 0 + .ra: x30
STACK CFI 5b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bc0 198 .cfa: sp 0 + .ra: x30
STACK CFI 5bc8 .cfa: sp 128 +
STACK CFI 5bcc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5be8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5cc8 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d60 5c .cfa: sp 0 + .ra: x30
STACK CFI 5d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d70 x19: .cfa -16 + ^
STACK CFI 5d98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5da0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5dc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 5dc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5dd0 x19: .cfa -16 + ^
STACK CFI 5df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e20 1c .cfa: sp 0 + .ra: x30
STACK CFI 5e28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5e40 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5e48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f14 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ff0 cc .cfa: sp 0 + .ra: x30
STACK CFI 5ff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6000 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 600c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 609c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 60c0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 60c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60f0 x23: .cfa -16 + ^
STACK CFI 6178 x23: x23
STACK CFI 617c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6184 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 61a0 20 .cfa: sp 0 + .ra: x30
STACK CFI 61a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 61c0 7e8 .cfa: sp 0 + .ra: x30
STACK CFI 61c8 .cfa: sp 416 +
STACK CFI 61d0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 61d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 61fc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6560 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 69b0 620 .cfa: sp 0 + .ra: x30
STACK CFI 69b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 69c0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 69d4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 6cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6cfc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6fd0 20 .cfa: sp 0 + .ra: x30
STACK CFI 6fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6fe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6ff0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7000 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 700c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7094 44 .cfa: sp 0 + .ra: x30
STACK CFI 709c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70a4 x19: .cfa -16 + ^
STACK CFI 70d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 70e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 70e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 70f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7100 1c .cfa: sp 0 + .ra: x30
STACK CFI 7108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7120 34 .cfa: sp 0 + .ra: x30
STACK CFI 7128 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 713c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7144 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7148 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7154 88 .cfa: sp 0 + .ra: x30
STACK CFI 7164 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7188 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7194 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 71e0 c50 .cfa: sp 0 + .ra: x30
STACK CFI 71e8 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 71fc x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7208 .cfa: sp 896 + x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7c34 .cfa: sp 160 +
STACK CFI 7c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7c54 .cfa: sp 896 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 7e30 7c .cfa: sp 0 + .ra: x30
STACK CFI 7e38 .cfa: sp 48 +
STACK CFI 7e48 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e50 x19: .cfa -16 + ^
STACK CFI 7ea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ea8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7eb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 7eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ec4 .cfa: sp 0 + .ra: .ra x29: x29
