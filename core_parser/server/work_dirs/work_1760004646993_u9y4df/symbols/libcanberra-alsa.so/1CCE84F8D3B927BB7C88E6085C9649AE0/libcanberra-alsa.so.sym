MODULE Linux arm64 1CCE84F8D3B927BB7C88E6085C9649AE0 libcanberra-alsa.so
INFO CODE_ID F884CE1CB9D3BB277C88E6085C9649AEF367E96B
PUBLIC 2160 0 alsa_driver_destroy
PUBLIC 2330 0 alsa_driver_open
PUBLIC 24d0 0 alsa_driver_change_device
PUBLIC 25a0 0 alsa_driver_change_props
PUBLIC 26b4 0 alsa_driver_cache
PUBLIC 2780 0 alsa_driver_play
PUBLIC 2ae0 0 alsa_driver_cancel
PUBLIC 2c44 0 driver_playing
STACK CFI INIT 1830 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1860 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18ac x19: .cfa -16 + ^
STACK CFI 18e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1900 98 .cfa: sp 0 + .ra: x30
STACK CFI 1908 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1910 x19: .cfa -16 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 195c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19a0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 19b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19b8 x19: .cfa -16 + ^
STACK CFI 19f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a80 340 .cfa: sp 0 + .ra: x30
STACK CFI 1a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a94 .cfa: x29 48 +
STACK CFI 1a98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1aa8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c6c .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1dc0 398 .cfa: sp 0 + .ra: x30
STACK CFI 1dc8 .cfa: sp 144 +
STACK CFI 1dcc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1dd4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e00 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1e4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1f1c x25: x25 x26: x26
STACK CFI 1fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 1fd0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2010 x25: x25 x26: x26
STACK CFI 2018 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 20a0 x25: x25 x26: x26
STACK CFI 20dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 20e0 x25: x25 x26: x26
STACK CFI 2124 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 213c x25: x25 x26: x26
STACK CFI 2140 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2144 x25: x25 x26: x26
STACK CFI 2154 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2160 1cc .cfa: sp 0 + .ra: x30
STACK CFI 2168 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2174 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 217c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21a0 x23: .cfa -16 + ^
STACK CFI 21ec x23: x23
STACK CFI 2260 x19: x19 x20: x20
STACK CFI 2268 x21: x21 x22: x22
STACK CFI 226c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2274 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2280 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2290 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 229c x19: x19 x20: x20
STACK CFI 22a4 x21: x21 x22: x22
STACK CFI 22ec x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2330 19c .cfa: sp 0 + .ra: x30
STACK CFI 2338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2344 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 239c x19: x19 x20: x20
STACK CFI 23a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23d8 x19: x19 x20: x20
STACK CFI 23e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23ec x19: x19 x20: x20
STACK CFI 2438 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2484 x19: x19 x20: x20
STACK CFI 248c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 24d0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 24d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 25a0 114 .cfa: sp 0 + .ra: x30
STACK CFI 25a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 26b4 c4 .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 26d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2780 360 .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 96 +
STACK CFI 2794 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 27d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 27e4 x25: .cfa -16 + ^
STACK CFI 2894 x19: x19 x20: x20
STACK CFI 2898 x23: x23 x24: x24
STACK CFI 289c x25: x25
STACK CFI 28a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 28b0 x19: x19 x20: x20
STACK CFI 28b4 x23: x23 x24: x24
STACK CFI 28b8 x25: x25
STACK CFI 28e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 28ec .cfa: sp 96 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 28fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2908 x23: x23 x24: x24
STACK CFI 2910 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 295c x23: x23 x24: x24
STACK CFI 2960 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 296c x19: x19 x20: x20
STACK CFI 2974 x23: x23 x24: x24
STACK CFI 29bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2a00 x23: x23 x24: x24
STACK CFI 2a04 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2a44 x25: .cfa -16 + ^
STACK CFI 2ad0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 2ad4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2ad8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2adc x25: .cfa -16 + ^
STACK CFI INIT 2ae0 164 .cfa: sp 0 + .ra: x30
STACK CFI 2ae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2af4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2b7c x23: x23 x24: x24
STACK CFI 2b88 x19: x19 x20: x20
STACK CFI 2b90 x21: x21 x22: x22
STACK CFI 2b94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2bb8 x21: x21 x22: x22
STACK CFI 2c04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2c44 194 .cfa: sp 0 + .ra: x30
STACK CFI 2c4c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c58 x21: .cfa -16 + ^
STACK CFI 2c64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cac x19: x19 x20: x20
STACK CFI 2cb4 x21: x21
STACK CFI 2cb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2cc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ccc x19: x19 x20: x20 x21: x21
STACK CFI 2cdc x21: .cfa -16 + ^
STACK CFI 2ce8 x21: x21
STACK CFI 2cf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2cfc x19: x19 x20: x20
STACK CFI 2d04 x21: x21
STACK CFI 2d4c x21: .cfa -16 + ^
STACK CFI 2d8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2dcc x19: x19 x20: x20
STACK CFI 2dd4 x21: x21
