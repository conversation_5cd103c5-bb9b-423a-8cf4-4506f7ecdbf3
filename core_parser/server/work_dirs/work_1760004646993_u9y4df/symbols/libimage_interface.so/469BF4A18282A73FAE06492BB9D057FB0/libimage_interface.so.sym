MODULE Linux arm64 469BF4A18282A73FAE06492BB9D057FB0 libimage_interface.so
INFO CODE_ID A1F49B4682823FA7AE06492BB9D057FB
PUBLIC 2208 0 _init
PUBLIC 24e0 0 _GLOBAL__sub_I_image_nvmedia.cpp
PUBLIC 2560 0 call_weak_fn
PUBLIC 2580 0 deregister_tm_clones
PUBLIC 25b0 0 register_tm_clones
PUBLIC 25f0 0 __do_global_dtors_aux
PUBLIC 2640 0 frame_dummy
PUBLIC 2650 0 std::_Hashtable<lios::image::ImageSupportFeature, std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> >, std::__detail::_Select1st, std::equal_to<lios::image::ImageSupportFeature>, std::hash<lios::image::ImageSupportFeature>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::find(lios::image::ImageSupportFeature const&) [clone .isra.0]
PUBLIC 26e0 0 lios::image::GetImageBufAttrList(linvs::buf::BufAttrList*, lios::image::ImageType, int, int)
PUBLIC 2b10 0 lios::image::AllocImages(std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >*, int, lios::image::ImageType, int, int, signed char)
PUBLIC 3190 0 std::unordered_map<lios::image::ImageSupportFeature, linvs::buf::BufAttrList, std::hash<lios::image::ImageSupportFeature>, std::equal_to<lios::image::ImageSupportFeature>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> > >::~unordered_map()
PUBLIC 3220 0 std::vector<linvs::buf::BufObj, std::allocator<linvs::buf::BufObj> >::_M_default_append(unsigned long)
PUBLIC 3400 0 void std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> >::_M_realloc_insert<linvs::buf::BufAttrList const*>(__gnu_cxx::__normal_iterator<linvs::buf::BufAttrList const**, std::vector<linvs::buf::BufAttrList const*, std::allocator<linvs::buf::BufAttrList const*> > >, linvs::buf::BufAttrList const*&&)
PUBLIC 3580 0 std::_Hashtable<lios::image::ImageSupportFeature, std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> >, std::__detail::_Select1st, std::equal_to<lios::image::ImageSupportFeature>, std::hash<lios::image::ImageSupportFeature>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 36b0 0 std::__detail::_Map_base<lios::image::ImageSupportFeature, std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList>, std::allocator<std::pair<lios::image::ImageSupportFeature const, linvs::buf::BufAttrList> >, std::__detail::_Select1st, std::equal_to<lios::image::ImageSupportFeature>, std::hash<lios::image::ImageSupportFeature>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](lios::image::ImageSupportFeature&&)
PUBLIC 388c 0 _fini
STACK CFI INIT 2580 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 25f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25fc x19: .cfa -16 + ^
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2650 90 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3190 88 .cfa: sp 0 + .ra: x30
STACK CFI 3194 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 319c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31a4 x21: .cfa -16 + ^
STACK CFI 3204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3208 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 26e0 430 .cfa: sp 0 + .ra: x30
STACK CFI 26e4 .cfa: sp 560 +
STACK CFI 26f8 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 2704 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 270c x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 271c x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 2798 x25: .cfa -496 + ^
STACK CFI 291c x25: x25
STACK CFI 2950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2954 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI 295c x25: .cfa -496 + ^
STACK CFI 2af4 x25: x25
STACK CFI 2b04 x25: .cfa -496 + ^
STACK CFI 2b08 x25: x25
STACK CFI 2b0c x25: .cfa -496 + ^
STACK CFI INIT 3220 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 3228 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3234 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 323c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3288 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 328c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 329c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 334c x27: x27 x28: x28
STACK CFI 3364 x23: x23 x24: x24
STACK CFI 336c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3370 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3400 180 .cfa: sp 0 + .ra: x30
STACK CFI 3404 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 340c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 341c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3428 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 34b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 34b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3580 12c .cfa: sp 0 + .ra: x30
STACK CFI 3584 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3598 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 363c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3640 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 36b0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 36b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 36bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 36d8 x23: .cfa -32 + ^
STACK CFI 3780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3784 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2b10 680 .cfa: sp 0 + .ra: x30
STACK CFI 2b14 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2b24 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2b30 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2b3c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2b48 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2b50 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e7c .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT 24e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24f8 x21: .cfa -16 + ^
STACK CFI 2554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
