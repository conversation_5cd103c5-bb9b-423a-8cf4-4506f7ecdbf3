MODULE Linux arm64 77E1143521386E55C1F1484BD109B48A0 libcolord_sensor_camera.so
INFO CODE_ID 3514E1773821556EC1F1484BD109B48A4C7407A7
PUBLIC 1910 0 cd_plugin_get_description
PUBLIC 1930 0 cd_plugin_coldplug
PUBLIC 1a20 0 cd_plugin_initialize
PUBLIC 1af0 0 cd_plugin_destroy
STACK CFI INIT 13b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1420 48 .cfa: sp 0 + .ra: x30
STACK CFI 1424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 142c x19: .cfa -16 + ^
STACK CFI 1464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1480 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 1488 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1490 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 14d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 14e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 14f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 14fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1500 x27: .cfa -16 + ^
STACK CFI 17d8 x19: x19 x20: x20
STACK CFI 17e4 x23: x23 x24: x24
STACK CFI 17e8 x25: x25 x26: x26
STACK CFI 17ec x27: x27
STACK CFI 17f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 17f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1824 e4 .cfa: sp 0 + .ra: x30
STACK CFI 182c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1838 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 184c x21: .cfa -16 + ^
STACK CFI 18b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 18e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1910 20 .cfa: sp 0 + .ra: x30
STACK CFI 1918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1928 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1930 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1938 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1944 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1958 x21: .cfa -16 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1a20 d0 .cfa: sp 0 + .ra: x30
STACK CFI 1a28 .cfa: sp 80 +
STACK CFI 1a3c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a54 x21: .cfa -16 + ^
STACK CFI 1ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1aec .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1af0 38 .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b00 x19: .cfa -16 + ^
STACK CFI 1b1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
