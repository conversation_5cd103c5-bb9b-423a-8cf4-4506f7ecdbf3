MODULE Linux arm64 5FC22FD8ED1BBAC4CA6BEBCD3C495F220 libopencv_rapid.so.4.3
INFO CODE_ID D82FC25F1BEDC4BACA6BEBCD3C495F22C6004516
PUBLIC 2108 0 _init
PUBLIC 24e0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.129]
PUBLIC 2580 0 call_weak_fn
PUBLIC 2598 0 deregister_tm_clones
PUBLIC 25d0 0 register_tm_clones
PUBLIC 2610 0 __do_global_dtors_aux
PUBLIC 2658 0 frame_dummy
PUBLIC 2690 0 cv::Mat::~Mat()
PUBLIC 2720 0 cv::rapid::drawWireframe(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Scalar_<double> const&, int, bool)
PUBLIC 3740 0 cv::rapid::drawSearchLines(cv::_InputOutputArray const&, cv::_InputArray const&, cv::Scalar_<double> const&)
PUBLIC 3970 0 cv::rapid::Contour3DSampler::~Contour3DSampler()
PUBLIC 3ac0 0 cv::rapid::extractLineBundle(int, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 4bd0 0 cv::rapid::findCorrespondencies(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 5ab0 0 cv::rapid::drawCorrespondencies(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 6e00 0 cv::rapid::filterCorrespondencies(cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputArray const&)
PUBLIC 7c68 0 cv::MatExpr::~MatExpr()
PUBLIC 7e18 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int const&>(int const&)
PUBLIC 7f00 0 std::vector<float, std::allocator<float> >::_M_default_append(unsigned long)
PUBLIC 8050 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int>(int&&)
PUBLIC 8138 0 void std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_emplace_back_aux<cv::Vec<float, 3> >(cv::Vec<float, 3>&&)
PUBLIC 8278 0 void std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > >::_M_emplace_back_aux<cv::Vec<float, 2> >(cv::Vec<float, 2>&&)
PUBLIC 8380 0 cv::rapid::extractControlPoints(int, int, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Size_<int> const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 9d70 0 cv::rapid::rapid(cv::_InputArray const&, int, int, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&)
PUBLIC a620 0 _fini
STACK CFI INIT 24e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 24f0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2574 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2690 90 .cfa: sp 0 + .ra: x30
STACK CFI 2694 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2708 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2710 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2720 1010 .cfa: sp 0 + .ra: x30
STACK CFI 2724 .cfa: sp 544 +
STACK CFI 2728 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 2738 x21: .cfa -528 + ^ x22: .cfa -520 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI 2758 .ra: .cfa -464 + ^ v8: .cfa -456 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 2ed0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2ed8 .cfa: sp 544 + .ra: .cfa -464 + ^ v8: .cfa -456 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT 3740 230 .cfa: sp 0 + .ra: x30
STACK CFI 3744 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 3754 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 3760 .ra: .cfa -128 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 389c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 38a0 .cfa: sp 176 + .ra: .cfa -128 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI INIT 3970 144 .cfa: sp 0 + .ra: x30
STACK CFI 3974 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3980 .ra: .cfa -16 + ^
STACK CFI 3a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3a88 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 3ac0 10fc .cfa: sp 0 + .ra: x30
STACK CFI 3ac4 .cfa: sp 720 +
STACK CFI 3af4 .ra: .cfa -640 + ^ v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -632 + ^ v8: .cfa -624 + ^ v9: .cfa -616 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^ x28: .cfa -648 + ^
STACK CFI 43c4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 43c8 .cfa: sp 720 + .ra: .cfa -640 + ^ v10: .cfa -608 + ^ v11: .cfa -600 + ^ v12: .cfa -632 + ^ v8: .cfa -624 + ^ v9: .cfa -616 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^ x28: .cfa -648 + ^
STACK CFI INIT 4bd0 ec8 .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 688 +
STACK CFI 4bd8 x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI 4be8 x19: .cfa -688 + ^ x20: .cfa -680 + ^ x21: .cfa -672 + ^ x22: .cfa -664 + ^
STACK CFI 4bf4 x23: .cfa -656 + ^ x24: .cfa -648 + ^
STACK CFI 4c04 .ra: .cfa -624 + ^
STACK CFI 52b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 52b8 .cfa: sp 688 + .ra: .cfa -624 + ^ x19: .cfa -688 + ^ x20: .cfa -680 + ^ x21: .cfa -672 + ^ x22: .cfa -664 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^ x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI INIT 5ab0 1334 .cfa: sp 0 + .ra: x30
STACK CFI 5ab4 .cfa: sp 912 +
STACK CFI 5ab8 x21: .cfa -896 + ^ x22: .cfa -888 + ^
STACK CFI 5ac8 x19: .cfa -912 + ^ x20: .cfa -904 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^
STACK CFI 5ae8 .ra: .cfa -832 + ^ v8: .cfa -824 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x28: .cfa -840 + ^
STACK CFI 62b0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 62b8 .cfa: sp 912 + .ra: .cfa -832 + ^ v8: .cfa -824 + ^ x19: .cfa -912 + ^ x20: .cfa -904 + ^ x21: .cfa -896 + ^ x22: .cfa -888 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x28: .cfa -840 + ^
STACK CFI INIT 6e00 e4c .cfa: sp 0 + .ra: x30
STACK CFI 6e04 .cfa: sp 816 +
STACK CFI 6e08 x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 6e30 .ra: .cfa -736 + ^ v8: .cfa -728 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 7744 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7748 .cfa: sp 816 + .ra: .cfa -736 + ^ v8: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI INIT 7c68 1ac .cfa: sp 0 + .ra: x30
STACK CFI 7c6c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c78 .ra: .cfa -16 + ^
STACK CFI 7dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7dd8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 7e18 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7e1c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7e24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e30 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7eb8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 7f00 14c .cfa: sp 0 + .ra: x30
STACK CFI 7f08 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f20 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7f70 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 800c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8010 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 8050 e8 .cfa: sp 0 + .ra: x30
STACK CFI 8054 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 805c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8068 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 80e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 80f0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 8138 140 .cfa: sp 0 + .ra: x30
STACK CFI 813c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8148 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8150 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 823c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 8240 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 8278 100 .cfa: sp 0 + .ra: x30
STACK CFI 827c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8284 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 828c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 8348 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 8380 19c4 .cfa: sp 0 + .ra: x30
STACK CFI 8384 .cfa: sp 1168 +
STACK CFI 83a8 .ra: .cfa -1088 + ^ v10: .cfa -1080 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 9730 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9734 .cfa: sp 1168 + .ra: .cfa -1088 + ^ v10: .cfa -1080 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI INIT 9d70 890 .cfa: sp 0 + .ra: x30
STACK CFI 9d74 .cfa: sp 1152 +
STACK CFI 9d98 .ra: .cfa -1056 + ^ v8: .cfa -1048 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI a060 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a068 .cfa: sp 1152 + .ra: .cfa -1056 + ^ v8: .cfa -1048 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
