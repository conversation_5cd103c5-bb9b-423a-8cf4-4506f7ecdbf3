MODULE Linux arm64 16886AFCBD04000FBADD24F50C14BA690 libboost_math_c99f.so.1.77.0
INFO CODE_ID FC6A881604BD0F00BADD24F50C14BA69
PUBLIC 1370 0 _init
PUBLIC 1590 0 _GLOBAL__sub_I_asinhf.cpp
PUBLIC 15b0 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::erf<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 1610 0 _GLOBAL__sub_I_erfcf.cpp
PUBLIC 1670 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::erf<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 16d0 0 _GLOBAL__sub_I_erff.cpp
PUBLIC 1730 0 _GLOBAL__sub_I_expm1f.cpp
PUBLIC 1750 0 boost::math::tools::promote_args<double, float, float, float, float, float>::type boost::math::lgamma<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, int*, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 17b0 0 _GLOBAL__sub_I_lgammaf.cpp
PUBLIC 1800 0 _GLOBAL__sub_I_nextafterf.cpp
PUBLIC 1870 0 _GLOBAL__sub_I_nexttowardf.cpp
PUBLIC 18e4 0 call_weak_fn
PUBLIC 1900 0 deregister_tm_clones
PUBLIC 1930 0 register_tm_clones
PUBLIC 1970 0 __do_global_dtors_aux
PUBLIC 19c0 0 frame_dummy
PUBLIC 19d0 0 boost_acoshf
PUBLIC 1b50 0 double boost::math::detail::asinh_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 20f0 0 boost_asinhf
PUBLIC 2730 0 boost_atanhf
PUBLIC 29a0 0 boost_cbrtf
PUBLIC 2c00 0 boost_copysignf
PUBLIC 2c30 0 double boost::math::detail::erf_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, bool, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, std::integral_constant<int, 53> const&) [clone .isra.0]
PUBLIC 3190 0 boost_erfcf
PUBLIC 3250 0 double boost::math::detail::erf_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(double, bool, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, std::integral_constant<int, 53> const&) [clone .isra.0]
PUBLIC 37b0 0 boost_erff
PUBLIC 3870 0 boost_expm1f
PUBLIC 3a50 0 boost_fmaxf
PUBLIC 3a80 0 boost_fminf
PUBLIC 3ab0 0 bool boost::math::tr1::signbit<float>(float)
PUBLIC 3ac0 0 int boost::math::tr1::fpclassify<float>(float)
PUBLIC 3b10 0 bool boost::math::tr1::isfinite<float>(float)
PUBLIC 3b30 0 bool boost::math::tr1::isinf<float>(float)
PUBLIC 3b50 0 bool boost::math::tr1::isnan<float>(float)
PUBLIC 3b60 0 bool boost::math::tr1::isnormal<float>(float)
PUBLIC 3b90 0 boost_hypotf
PUBLIC 3c10 0 double boost::math::detail::gamma_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&) [clone .isra.0]
PUBLIC 4280 0 double boost::math::detail::lgamma_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&, int*) [clone .isra.0]
PUBLIC 4930 0 boost_lgammaf
PUBLIC 49f0 0 double boost::math::unchecked_factorial<double>(unsigned int)
PUBLIC 4a70 0 boost_llroundf
PUBLIC 4b90 0 boost_log1pf
PUBLIC 4bf0 0 boost_lroundf
PUBLIC 4d10 0 float boost::math::detail::float_prior_imp<float, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(float const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 4ef0 0 float boost::math::detail::float_next_imp<float, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(float const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 50d0 0 boost_nextafterf
PUBLIC 5400 0 long double boost::math::detail::float_prior_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 56c0 0 long double boost::math::detail::float_next_imp<long double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> >(long double const&, std::integral_constant<bool, true> const&, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&) [clone .isra.0]
PUBLIC 5980 0 boost_nexttowardf
PUBLIC 5e40 0 boost_roundf
PUBLIC 5f00 0 double boost::math::detail::gamma_imp<double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy>, boost::math::lanczos::lanczos13m53>(double, boost::math::policies::policy<boost::math::policies::domain_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::pole_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::overflow_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::evaluation_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::rounding_error<(boost::math::policies::error_policy_type)1>, boost::math::policies::promote_float<false>, boost::math::policies::promote_double<false>, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy, boost::math::policies::default_policy> const&, boost::math::lanczos::lanczos13m53 const&) [clone .isra.0]
PUBLIC 6570 0 boost_tgammaf
PUBLIC 6630 0 boost_truncf
PUBLIC 66a0 0 _fini
STACK CFI INIT 1900 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1930 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1970 48 .cfa: sp 0 + .ra: x30
STACK CFI 1974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 197c x19: .cfa -16 + ^
STACK CFI 19b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 19c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d0 17c .cfa: sp 0 + .ra: x30
STACK CFI 19d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ad0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b50 59c .cfa: sp 0 + .ra: x30
STACK CFI 1b58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b6c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1ba4 v8: v8 v9: v9
STACK CFI 1ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bac .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1bc0 v10: .cfa -32 + ^
STACK CFI 1bd8 v10: v10
STACK CFI 1be8 v8: v8 v9: v9
STACK CFI 1bec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bf0 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1c2c v10: .cfa -32 + ^
STACK CFI 1d28 v10: v10
STACK CFI 1d40 v8: v8 v9: v9
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d48 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1d6c v8: v8 v9: v9
STACK CFI 1d70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d98 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1e2c v10: .cfa -32 + ^
STACK CFI 1f08 v10: v10
STACK CFI 1f18 v10: .cfa -32 + ^
STACK CFI 1f38 v8: v8 v9: v9
STACK CFI 1f3c v10: v10
STACK CFI 1f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f44 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1f50 v8: v8 v9: v9
STACK CFI 1f60 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1f6c v8: v8 v9: v9
STACK CFI 1f78 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1f80 v8: v8 v9: v9
STACK CFI 1f94 v10: .cfa -32 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2034 v10: v10
STACK CFI 2040 v10: .cfa -32 + ^
STACK CFI 204c v10: v10
STACK CFI 208c v10: .cfa -32 + ^
STACK CFI 2090 v10: v10
STACK CFI 20c8 v10: .cfa -32 + ^
STACK CFI 20d4 v10: v10
STACK CFI INIT 20f0 638 .cfa: sp 0 + .ra: x30
STACK CFI 20f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20fc v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2154 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 2158 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2360 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 2364 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 23d0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 23d4 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2438 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 243c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1590 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2730 264 .cfa: sp 0 + .ra: x30
STACK CFI 2738 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2748 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 2754 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 27c4 v8: v8 v9: v9
STACK CFI 27cc v10: v10 v11: v11
STACK CFI 27dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 27e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 27f0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2804 v8: v8 v9: v9
STACK CFI 280c v10: v10 v11: v11
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2818 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 288c v8: v8 v9: v9
STACK CFI 2890 v10: v10 v11: v11
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2898 .cfa: sp 64 + .ra: .cfa -56 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 28ac v8: v8 v9: v9
STACK CFI 28b4 v10: v10 v11: v11
STACK CFI 28c0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 297c v8: v8 v9: v9
STACK CFI 2984 v10: v10 v11: v11
STACK CFI 2990 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29a0 254 .cfa: sp 0 + .ra: x30
STACK CFI 29a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 29b4 v8: .cfa -24 + ^
STACK CFI 2a10 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 2a14 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2a24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a28 x21: .cfa -32 + ^
STACK CFI 2b54 x19: x19 x20: x20
STACK CFI 2b58 x21: x21
STACK CFI 2b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 2be8 x19: x19 x20: x20 x21: x21
STACK CFI 2bec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2bf0 x21: .cfa -32 + ^
STACK CFI INIT 2c00 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c30 554 .cfa: sp 0 + .ra: x30
STACK CFI 2c34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c40 x19: .cfa -64 + ^
STACK CFI 2c50 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 2cd4 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 2d18 v10: v10 v11: v11
STACK CFI 2d68 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 2d6c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 2dec v12: .cfa -56 + ^
STACK CFI 2df0 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 2f24 v12: v12
STACK CFI 2f30 v10: v10 v11: v11
STACK CFI 2f58 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -56 + ^
STACK CFI 30c4 v12: v12
STACK CFI 30c8 v10: v10 v11: v11
STACK CFI 30d0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -56 + ^
STACK CFI 3178 v10: v10 v11: v11 v12: v12
STACK CFI 317c v12: .cfa -56 + ^
STACK CFI 3180 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI INIT 15b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 15b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 160c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3190 bc .cfa: sp 0 + .ra: x30
STACK CFI 3198 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31a4 v8: .cfa -32 + ^
STACK CFI 31f0 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 31f4 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 320c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 3210 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3248 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 1610 58 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1660 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3250 554 .cfa: sp 0 + .ra: x30
STACK CFI 3254 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3260 x19: .cfa -64 + ^
STACK CFI 3270 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 32f4 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3338 v10: v10 v11: v11
STACK CFI 3388 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x29: x29
STACK CFI 338c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI 340c v12: .cfa -56 + ^
STACK CFI 3410 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 3544 v12: v12
STACK CFI 3550 v10: v10 v11: v11
STACK CFI 3578 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -56 + ^
STACK CFI 36e4 v12: v12
STACK CFI 36e8 v10: v10 v11: v11
STACK CFI 36f0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -56 + ^
STACK CFI 3798 v10: v10 v11: v11 v12: v12
STACK CFI 379c v12: .cfa -56 + ^
STACK CFI 37a0 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI INIT 1670 60 .cfa: sp 0 + .ra: x30
STACK CFI 1674 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37b0 bc .cfa: sp 0 + .ra: x30
STACK CFI 37b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37c4 v8: .cfa -32 + ^
STACK CFI 3810 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 3814 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 382c .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 3830 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3868 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 16d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3870 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 387c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3968 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 39fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1730 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a50 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a80 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ab0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ac0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b10 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b30 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b50 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b60 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b90 80 .cfa: sp 0 + .ra: x30
STACK CFI 3bb0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 49f4 .cfa: sp 1408 +
STACK CFI 4a08 .ra: .cfa -1400 + ^ x29: .cfa -1408 + ^
STACK CFI 4a10 x19: .cfa -1392 + ^
STACK CFI 4a60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a64 .cfa: sp 1408 + .ra: .cfa -1400 + ^ x19: .cfa -1392 + ^ x29: .cfa -1408 + ^
STACK CFI INIT 3c10 664 .cfa: sp 0 + .ra: x30
STACK CFI 3c14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3c20 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 3c50 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 3c54 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 3dfc v12: v12 v13: v13
STACK CFI 3e04 v10: v10 v11: v11
STACK CFI 3e24 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 3e2c v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 3e34 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 3e64 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3e68 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3e90 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3e94 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3efc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 3f00 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3f48 v10: v10 v11: v11
STACK CFI 3f50 v12: v12 v13: v13
STACK CFI 3f54 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 40c4 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 40cc v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 4154 v10: v10 v11: v11
STACK CFI 4158 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 4174 v10: v10 v11: v11
STACK CFI 417c v12: v12 v13: v13
STACK CFI 418c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 4190 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4198 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 41c4 v10: v10 v11: v11
STACK CFI 41d4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 4218 v10: v10 v11: v11
STACK CFI 4228 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 4238 v12: v12 v13: v13
STACK CFI INIT 4280 6a8 .cfa: sp 0 + .ra: x30
STACK CFI 428c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4298 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 42a0 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 4318 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 431c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4390 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 4394 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4428 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 4434 v12: .cfa -16 + ^
STACK CFI 4458 v10: v10 v11: v11
STACK CFI 445c v12: v12
STACK CFI 456c v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 4580 v12: .cfa -16 + ^
STACK CFI 4638 v10: v10 v11: v11
STACK CFI 463c v12: v12
STACK CFI 4654 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^
STACK CFI 4708 v12: v12
STACK CFI 4730 v10: v10 v11: v11
STACK CFI 4734 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^
STACK CFI 47f0 v12: v12
STACK CFI 481c v10: v10 v11: v11
STACK CFI 4820 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^
STACK CFI 48f8 v12: v12
STACK CFI 490c v10: v10 v11: v11
STACK CFI 4910 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^
STACK CFI INIT 1750 5c .cfa: sp 0 + .ra: x30
STACK CFI 1754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4930 bc .cfa: sp 0 + .ra: x30
STACK CFI 4938 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4944 v8: .cfa -32 + ^
STACK CFI 4990 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 4994 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 49ac .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 49b0 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 49e8 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 17b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 17c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a70 118 .cfa: sp 0 + .ra: x30
STACK CFI 4a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a84 v8: .cfa -16 + ^
STACK CFI 4ad8 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 4adc .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b90 58 .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4bc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4bc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4be0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4be4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4bf0 118 .cfa: sp 0 + .ra: x30
STACK CFI 4bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c04 v8: .cfa -16 + ^
STACK CFI 4c58 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 4c5c .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d10 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 4d14 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d24 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 4dc0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 4dc4 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4de4 x21: .cfa -48 + ^
STACK CFI 4e04 x21: x21
STACK CFI 4e64 x21: .cfa -48 + ^
STACK CFI 4eb0 x21: x21
STACK CFI 4eb8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 4ebc .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 4ee0 x21: x21
STACK CFI 4ee4 x21: .cfa -48 + ^
STACK CFI INIT 4ef0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 4ef4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4efc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4f04 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 4f7c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 4f80 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4ffc x21: .cfa -48 + ^
STACK CFI 501c x21: x21
STACK CFI 5040 x21: .cfa -48 + ^
STACK CFI 5090 x21: x21
STACK CFI 5098 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 509c .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 50c0 x21: x21
STACK CFI 50c4 x21: .cfa -48 + ^
STACK CFI INIT 50d0 324 .cfa: sp 0 + .ra: x30
STACK CFI 50d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 50e8 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 5128 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 512c .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 521c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 524c x19: x19 x20: x20
STACK CFI 52bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 52e8 x19: x19 x20: x20
STACK CFI 5324 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 53a4 x19: x19 x20: x20
STACK CFI 53b0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 53b4 .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 53ec x19: x19 x20: x20
STACK CFI 53f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 1800 68 .cfa: sp 0 + .ra: x30
STACK CFI 1814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1824 x19: .cfa -16 + ^
STACK CFI 183c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1840 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1860 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5400 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 540c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 54cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 55c0 x21: .cfa -80 + ^
STACK CFI 5604 x21: x21
STACK CFI 5640 x21: .cfa -80 + ^
STACK CFI 5680 x21: x21
STACK CFI 5684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5688 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI 56b8 x21: x21
STACK CFI 56bc x21: .cfa -80 + ^
STACK CFI INIT 56c0 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 56c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 56cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 578c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 5878 x21: .cfa -80 + ^
STACK CFI 58bc x21: x21
STACK CFI 58f8 x21: .cfa -80 + ^
STACK CFI 5938 x21: x21
STACK CFI 593c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5940 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI 5970 x21: x21
STACK CFI 5974 x21: .cfa -80 + ^
STACK CFI INIT 5980 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5994 v8: .cfa -80 + ^
STACK CFI 59f4 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 59f8 .cfa: sp 112 + .ra: .cfa -104 + ^ v8: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI 5a08 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5a60 x19: x19 x20: x20
STACK CFI 5b04 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5b50 x19: x19 x20: x20
STACK CFI 5b84 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5bd8 x19: x19 x20: x20
STACK CFI 5c60 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5cd0 x19: x19 x20: x20
STACK CFI 5cd4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d6c x19: x19 x20: x20
STACK CFI 5d70 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d78 x19: x19 x20: x20
STACK CFI 5d90 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d9c x19: x19 x20: x20
STACK CFI 5da4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5e2c x19: x19 x20: x20
STACK CFI 5e34 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 1870 74 .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1894 x19: .cfa -16 + ^
STACK CFI 18ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 18e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e40 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e50 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 5eac .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5ed4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 5ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5ee8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 5eec .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f00 664 .cfa: sp 0 + .ra: x30
STACK CFI 5f04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5f10 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 5f40 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 5f44 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 60ec v12: v12 v13: v13
STACK CFI 60f4 v10: v10 v11: v11
STACK CFI 6114 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 611c v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 6124 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 6154 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 6158 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6180 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 6184 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 61ec .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 61f0 .cfa: sp 80 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6238 v10: v10 v11: v11
STACK CFI 6240 v12: v12 v13: v13
STACK CFI 6244 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 63b4 v10: v10 v11: v11 v12: v12 v13: v13
STACK CFI 63bc v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 6444 v10: v10 v11: v11
STACK CFI 6448 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 6464 v10: v10 v11: v11
STACK CFI 646c v12: v12 v13: v13
STACK CFI 647c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 6480 .cfa: sp 80 + .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6488 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 64b4 v10: v10 v11: v11
STACK CFI 64c4 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 6508 v10: v10 v11: v11
STACK CFI 6518 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 6528 v12: v12 v13: v13
STACK CFI INIT 6570 bc .cfa: sp 0 + .ra: x30
STACK CFI 6578 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6580 v8: .cfa -32 + ^
STACK CFI 65cc .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 65d0 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 65e8 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI 65ec .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 6628 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 6630 70 .cfa: sp 0 + .ra: x30
STACK CFI 6634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6640 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 6668 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 666c .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 668c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
STACK CFI 6690 .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 669c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x29: x29
