MODULE Linux arm64 77D220A2E10A4E6CE8E8A0AC082EC8410 libnvstream_core_network.so
INFO CODE_ID A220D2770AE16C4EE8E8A0AC082EC841
PUBLIC 4760 0 _init
PUBLIC 4b60 0 call_weak_fn
PUBLIC 4b80 0 deregister_tm_clones
PUBLIC 4bb0 0 register_tm_clones
PUBLIC 4bf0 0 __do_global_dtors_aux
PUBLIC 4c40 0 frame_dummy
PUBLIC 4c50 0 linvs::network::DgramSocket::DgramSocket(int)
PUBLIC 4c80 0 linvs::network::DgramSocket::Open(int, int)
PUBLIC 4c90 0 linvs::network::DgramSocket::SendTo(void const*, long, sockaddr const*, unsigned int, int)
PUBLIC 4cf0 0 linvs::network::DgramSocket::RecvFrom(void*, unsigned long, sockaddr*, unsigned int*, int)
PUBLIC 4d70 0 linvs::network::DgramSocket::SendToTimeout(void const*, long, long, sockaddr const*, unsigned int, int)
PUBLIC 4e30 0 linvs::network::DgramSocket::RecvFromTimeout(void*, unsigned long, long, sockaddr*, unsigned int*, int)
PUBLIC 4ef0 0 linvs::network::Socket::~Socket()
PUBLIC 4f20 0 linvs::network::Socket::~Socket()
PUBLIC 4f60 0 linvs::network::DgramSocket::~DgramSocket()
PUBLIC 4f90 0 linvs::network::DgramSocket::~DgramSocket()
PUBLIC 4fd0 0 linvs::network::Socket::Socket(int)
PUBLIC 4ff0 0 linvs::network::Socket::Open(int, int, int)
PUBLIC 5060 0 linvs::network::Socket::Close()
PUBLIC 50a0 0 linvs::network::Socket::WaitEvent(pollfd*, unsigned long, long)
PUBLIC 5160 0 linvs::network::StreamSocket::StreamSocket(int)
PUBLIC 5190 0 linvs::network::StreamSocket::Open(int, int)
PUBLIC 51a0 0 linvs::network::StreamSocket::Send(void const*, long, int)
PUBLIC 51b0 0 linvs::network::StreamSocket::Recv(void*, unsigned long, int)
PUBLIC 51c0 0 linvs::network::StreamSocket::SendTimeout(void const*, long, long, int)
PUBLIC 5270 0 linvs::network::StreamSocket::RecvTimeout(void*, unsigned long, long, int)
PUBLIC 5320 0 linvs::network::StreamSocket::~StreamSocket()
PUBLIC 5350 0 linvs::network::StreamSocket::~StreamSocket()
PUBLIC 5390 0 linvs::network::StreamSocketServer::Listen(int)
PUBLIC 53a0 0 linvs::network::StreamSocketServer::Accept()
PUBLIC 54c0 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 54d0 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 54f0 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5500 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5570 0 std::_Sp_counted_ptr_inplace<linvs::network::StreamSocket, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5580 0 linvs::network::UdpDgramSocketClient::Open()
PUBLIC 5590 0 linvs::network::UdpDgramSocketClient::Bind(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short)
PUBLIC 5650 0 linvs::network::UdpDgramSocketClient::SendTo(void const*, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short, int)
PUBLIC 5720 0 linvs::network::UdpDgramSocketClient::RecvFrom(void*, unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, unsigned short&, int)
PUBLIC 57f0 0 linvs::network::UdpDgramSocketClient::SendToTimeout(void const*, long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short, int)
PUBLIC 58d0 0 linvs::network::UdpDgramSocketClient::RecvFromTimeout(void*, unsigned long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, unsigned short&, int)
PUBLIC 59a0 0 linvs::network::UdpDgramSocketClient::CreateInstance(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned short)
PUBLIC 5ab0 0 linvs::network::UdpDgramSocketClient::~UdpDgramSocketClient()
PUBLIC 5ae0 0 linvs::network::UdpDgramSocketClient::~UdpDgramSocketClient()
PUBLIC 5b20 0 linvs::network::UnixDgramSocketClient::~UnixDgramSocketClient()
PUBLIC 5cf0 0 linvs::network::UnixDgramSocketClient::~UnixDgramSocketClient() [clone .localalias]
PUBLIC 5d20 0 linvs::network::UnixDgramSocketClient::UnixDgramSocketClient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5e50 0 linvs::network::UnixDgramSocketClient::Open()
PUBLIC 5f60 0 linvs::network::UnixDgramSocketClient::SendTo(void const*, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 6040 0 linvs::network::UnixDgramSocketClient::RecvFrom(void*, unsigned long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, int)
PUBLIC 60f0 0 linvs::network::UnixDgramSocketClient::SendToTimeout(void const*, long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 61e0 0 linvs::network::UnixDgramSocketClient::RecvFromTimeout(void*, unsigned long, long, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, int)
PUBLIC 6290 0 linvs::network::UnixDgramSocketClient::CreateInstance(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6380 0 linvs::network::UnixStreamSocketClient::Connect(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6470 0 linvs::network::UnixStreamSocketClient::Open()
PUBLIC 6480 0 linvs::network::UnixStreamSocketClient::CreateInstance()
PUBLIC 6510 0 linvs::network::UnixStreamSocketClient::~UnixStreamSocketClient()
PUBLIC 6540 0 linvs::network::UnixStreamSocketClient::~UnixStreamSocketClient()
PUBLIC 6580 0 linvs::network::UnixStreamSocketServer::Bind(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6680 0 linvs::network::UnixStreamSocketServer::~UnixStreamSocketServer()
PUBLIC 6840 0 linvs::network::UnixStreamSocketServer::~UnixStreamSocketServer() [clone .localalias]
PUBLIC 6870 0 linvs::network::UnixStreamSocketServer::Open()
PUBLIC 6880 0 linvs::network::UnixStreamSocketServer::CreateInstance()
PUBLIC 695c 0 _fini
STACK CFI INIT 4b80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bb0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4bf0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bfc x19: .cfa -16 + ^
STACK CFI 4c34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ef0 24 .cfa: sp 0 + .ra: x30
STACK CFI 4efc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4f20 38 .cfa: sp 0 + .ra: x30
STACK CFI 4f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f34 x19: .cfa -16 + ^
STACK CFI 4f54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4f60 24 .cfa: sp 0 + .ra: x30
STACK CFI 4f6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4f90 38 .cfa: sp 0 + .ra: x30
STACK CFI 4f94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fa4 x19: .cfa -16 + ^
STACK CFI 4fc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c50 30 .cfa: sp 0 + .ra: x30
STACK CFI 4c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c5c x19: .cfa -16 + ^
STACK CFI 4c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4c80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c90 60 .cfa: sp 0 + .ra: x30
STACK CFI 4c94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4cc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4cf0 74 .cfa: sp 0 + .ra: x30
STACK CFI 4cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d14 x19: .cfa -16 + ^
STACK CFI 4d2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4d60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d70 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4d74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d88 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d94 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4da0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e0c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4e30 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4e34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4e48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4e54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4e60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ecc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4fd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ff0 64 .cfa: sp 0 + .ra: x30
STACK CFI 4ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ffc x19: .cfa -16 + ^
STACK CFI 5024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5060 34 .cfa: sp 0 + .ra: x30
STACK CFI 5064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 506c x19: .cfa -16 + ^
STACK CFI 5090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 50a0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 50a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 511c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5320 24 .cfa: sp 0 + .ra: x30
STACK CFI 532c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5350 38 .cfa: sp 0 + .ra: x30
STACK CFI 5354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5364 x19: .cfa -16 + ^
STACK CFI 5384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5160 30 .cfa: sp 0 + .ra: x30
STACK CFI 5164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 516c x19: .cfa -16 + ^
STACK CFI 518c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5190 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 51a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51c0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 51c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 524c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5270 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5274 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5288 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5294 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 52f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54d0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 54f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5500 70 .cfa: sp 0 + .ra: x30
STACK CFI 5504 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5514 x19: .cfa -16 + ^
STACK CFI 5558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 555c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 556c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53a0 11c .cfa: sp 0 + .ra: x30
STACK CFI 53a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 53bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5428 x21: x21 x22: x22
STACK CFI 5450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5454 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5484 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5ab0 24 .cfa: sp 0 + .ra: x30
STACK CFI 5abc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ad0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ae0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5af4 x19: .cfa -16 + ^
STACK CFI 5b14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5580 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5590 bc .cfa: sp 0 + .ra: x30
STACK CFI 5594 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 55b0 x19: .cfa -48 + ^
STACK CFI 561c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5620 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5650 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5654 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 566c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5720 cc .cfa: sp 0 + .ra: x30
STACK CFI 5724 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5734 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 573c x21: .cfa -80 + ^
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 57e0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 57f0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 57f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5810 x19: .cfa -80 + ^
STACK CFI 5894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5898 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x29: .cfa -96 + ^
STACK CFI INIT 58d0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 58d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 58e4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 58f0 x21: .cfa -80 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5994 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x29: .cfa -112 + ^
STACK CFI INIT 59a0 104 .cfa: sp 0 + .ra: x30
STACK CFI 59a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 59b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b20 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 5b24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5b34 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5b60 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bc8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 5bd0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5c58 x23: x23 x24: x24
STACK CFI 5c5c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5c9c x23: x23 x24: x24
STACK CFI 5ca4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 5cf0 28 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cfc x19: .cfa -16 + ^
STACK CFI 5d14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d20 128 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5d34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5e50 10c .cfa: sp 0 + .ra: x30
STACK CFI 5e54 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5e68 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ec0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 5f60 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5f64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 5f80 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 5f8c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6024 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI INIT 6040 ac .cfa: sp 0 + .ra: x30
STACK CFI 6044 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 6058 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 6084 x21: .cfa -144 + ^
STACK CFI 60b0 x21: x21
STACK CFI 60d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60dc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI 60e8 x21: .cfa -144 + ^
STACK CFI INIT 60f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 60f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 6114 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 6120 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 6128 x23: .cfa -144 + ^
STACK CFI 61c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 61c4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI INIT 61e0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 61f8 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 622c x21: .cfa -144 + ^
STACK CFI 6254 x21: x21
STACK CFI 627c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6280 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI 6284 x21: .cfa -144 + ^
STACK CFI INIT 6290 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 629c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62a4 x21: .cfa -16 + ^
STACK CFI 62dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 62e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6324 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 633c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6340 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6510 24 .cfa: sp 0 + .ra: x30
STACK CFI 651c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6530 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6540 38 .cfa: sp 0 + .ra: x30
STACK CFI 6544 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6554 x19: .cfa -16 + ^
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6380 ec .cfa: sp 0 + .ra: x30
STACK CFI 6384 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 63a4 x19: .cfa -144 + ^
STACK CFI 642c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6430 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6470 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6480 90 .cfa: sp 0 + .ra: x30
STACK CFI 6484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6490 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 64cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 64f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6580 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6584 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6590 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6638 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 663c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6680 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 6684 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6694 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 66b4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6728 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 672c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 67ac x23: x23 x24: x24
STACK CFI 67b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 67ec x23: x23 x24: x24
STACK CFI 67f4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT 6840 28 .cfa: sp 0 + .ra: x30
STACK CFI 6844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 684c x19: .cfa -16 + ^
STACK CFI 6864 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6870 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6880 dc .cfa: sp 0 + .ra: x30
STACK CFI 6884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 68e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 68e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6928 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 693c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6940 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
