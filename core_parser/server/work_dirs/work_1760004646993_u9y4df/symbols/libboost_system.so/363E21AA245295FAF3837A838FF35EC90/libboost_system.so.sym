MODULE Linux arm64 363E21AA245295FAF3837A838FF35EC90 libboost_system.so.1.77.0
INFO CODE_ID AA213E365224FA95F3837A838FF35EC9
PUBLIC 490 0 _init
PUBLIC 4f0 0 call_weak_fn
PUBLIC 510 0 deregister_tm_clones
PUBLIC 540 0 register_tm_clones
PUBLIC 580 0 __do_global_dtors_aux
PUBLIC 5d0 0 frame_dummy
PUBLIC 5e0 0 boost::system::dummy_exported_function()
PUBLIC 5e4 0 _fini
STACK CFI INIT 510 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 540 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 580 48 .cfa: sp 0 + .ra: x30
STACK CFI 584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58c x19: .cfa -16 + ^
STACK CFI 5c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e0 4 .cfa: sp 0 + .ra: x30
