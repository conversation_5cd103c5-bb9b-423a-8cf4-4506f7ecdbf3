MODULE Linux arm64 5458D8760DAE8530D2E76E75BA2D344D0 lmnet.so
INFO CODE_ID 76D85854AE0D3085D2E76E75BA2D344D419A6ADE
PUBLIC 17f4 0 netQueryInterface
PUBLIC 3cf0 0 netClassExit
PUBLIC 3d90 0 netClassInit
PUBLIC 3e90 0 modInit
STACK CFI INIT 16d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1740 48 .cfa: sp 0 + .ra: x30
STACK CFI 1744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 174c x19: .cfa -16 + ^
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a0 28 .cfa: sp 0 + .ra: x30
STACK CFI 17ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 17dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17f4 e0 .cfa: sp 0 + .ra: x30
STACK CFI 17fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 18cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 18d4 2c .cfa: sp 0 + .ra: x30
STACK CFI 18e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1900 88 .cfa: sp 0 + .ra: x30
STACK CFI 1908 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1914 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1924 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 196c x19: x19 x20: x20
STACK CFI 1980 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1990 78 .cfa: sp 0 + .ra: x30
STACK CFI 1998 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19ac x21: .cfa -16 + ^
STACK CFI 19f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 19f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a10 43c .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a20 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a30 x27: .cfa -16 + ^
STACK CFI 1a48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1ad4 x19: x19 x20: x20
STACK CFI 1af0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1af8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1b38 x19: x19 x20: x20
STACK CFI 1b54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1b5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1ba0 x19: x19 x20: x20
STACK CFI 1ba4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1bd8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1cdc x19: x19 x20: x20
STACK CFI 1ce0 x23: x23 x24: x24
STACK CFI 1ce8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1de0 x19: x19 x20: x20
STACK CFI 1dfc x23: x23 x24: x24
STACK CFI 1e18 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1e20 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1e24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e40 x23: x23 x24: x24
STACK CFI 1e44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 1e50 84 .cfa: sp 0 + .ra: x30
STACK CFI 1e58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ed4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ef4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1fc0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2010 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2080 164 .cfa: sp 0 + .ra: x30
STACK CFI 2088 .cfa: sp 64 +
STACK CFI 2094 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 209c x21: .cfa -16 + ^
STACK CFI 20c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2114 x19: x19 x20: x20
STACK CFI 213c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2144 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 217c x19: x19 x20: x20
STACK CFI 2180 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2190 x19: x19 x20: x20
STACK CFI 2194 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21bc x19: x19 x20: x20
STACK CFI 21cc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 21dc .cfa: sp 64 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 21e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 21e4 d0 .cfa: sp 0 + .ra: x30
STACK CFI 21ec .cfa: sp 64 +
STACK CFI 21f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2200 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2208 x21: .cfa -16 + ^
STACK CFI 2270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2278 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 22b4 1ec .cfa: sp 0 + .ra: x30
STACK CFI 22bc .cfa: sp 160 +
STACK CFI 22cc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 22d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 22e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2328 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 233c x25: .cfa -16 + ^
STACK CFI 2408 x23: x23 x24: x24
STACK CFI 240c x25: x25
STACK CFI 2410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2418 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2468 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2480 x23: x23 x24: x24 x25: x25
STACK CFI 2494 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2498 x25: .cfa -16 + ^
STACK CFI INIT 24a0 618 .cfa: sp 0 + .ra: x30
STACK CFI 24a8 .cfa: sp 224 +
STACK CFI 24b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 24c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2544 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2558 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2790 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 280c .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2850 x23: x23 x24: x24
STACK CFI 2854 x27: x27 x28: x28
STACK CFI 2858 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 290c x23: x23 x24: x24
STACK CFI 2910 x27: x27 x28: x28
STACK CFI 2914 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 293c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2950 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2974 x23: x23 x24: x24
STACK CFI 2978 x27: x27 x28: x28
STACK CFI 297c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2a64 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2a84 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2aac x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2ab0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2ab4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2ac0 74 .cfa: sp 0 + .ra: x30
STACK CFI 2ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2af0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b34 288 .cfa: sp 0 + .ra: x30
STACK CFI 2b3c .cfa: sp 144 +
STACK CFI 2b48 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b58 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2b60 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2b68 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2c1c x27: .cfa -16 + ^
STACK CFI 2c78 x27: x27
STACK CFI 2cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2cc0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2d50 x27: .cfa -16 + ^
STACK CFI 2d94 x27: x27
STACK CFI 2d9c x27: .cfa -16 + ^
STACK CFI 2da4 x27: x27
STACK CFI 2db8 x27: .cfa -16 + ^
STACK CFI INIT 2dc0 1c .cfa: sp 0 + .ra: x30
STACK CFI 2dc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2de0 16c .cfa: sp 0 + .ra: x30
STACK CFI 2de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2df4 .cfa: sp 1488 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e5c .cfa: sp 48 +
STACK CFI 2e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e6c .cfa: sp 1488 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2e7c x21: .cfa -16 + ^
STACK CFI 2eb4 x21: x21
STACK CFI 2ef4 x21: .cfa -16 + ^
STACK CFI 2f14 x21: x21
STACK CFI 2f18 x21: .cfa -16 + ^
STACK CFI 2f40 x21: x21
STACK CFI 2f48 x21: .cfa -16 + ^
STACK CFI INIT 2f50 104 .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 176 +
STACK CFI 2f68 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3028 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3054 2c .cfa: sp 0 + .ra: x30
STACK CFI 305c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3080 260 .cfa: sp 0 + .ra: x30
STACK CFI 3088 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 30a0 .cfa: sp 8320 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3164 .cfa: sp 64 +
STACK CFI 3178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3180 .cfa: sp 8320 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 32e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 32f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32f8 x21: .cfa -16 + ^
STACK CFI 3300 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3350 5f8 .cfa: sp 0 + .ra: x30
STACK CFI 3358 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3370 .cfa: sp 1152 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 34a0 .cfa: sp 80 +
STACK CFI 34b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 34bc .cfa: sp 1152 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3950 258 .cfa: sp 0 + .ra: x30
STACK CFI 3958 .cfa: sp 160 +
STACK CFI 3968 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 397c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3984 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 398c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 399c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 39a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3ae8 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3bb0 140 .cfa: sp 0 + .ra: x30
STACK CFI 3bb8 .cfa: sp 96 +
STACK CFI 3bc4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3bd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3bdc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c04 x25: .cfa -16 + ^
STACK CFI 3c5c x25: x25
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3c9c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3cbc x25: x25
STACK CFI 3cc0 x25: .cfa -16 + ^
STACK CFI 3cdc x25: x25
STACK CFI 3cec x25: .cfa -16 + ^
STACK CFI INIT 3cf0 80 .cfa: sp 0 + .ra: x30
STACK CFI 3cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d10 x21: .cfa -16 + ^
STACK CFI 3d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3d70 20 .cfa: sp 0 + .ra: x30
STACK CFI 3d78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d90 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3d98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3da0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3dac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3dc4 x23: .cfa -16 + ^
STACK CFI 3e58 x23: x23
STACK CFI 3e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3e74 x23: x23
STACK CFI 3e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3e90 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3e98 .cfa: sp 64 +
STACK CFI 3ea4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3eac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3eb8 x21: .cfa -16 + ^
STACK CFI 3f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f54 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4f70 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fa0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fe0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1690 24 .cfa: sp 0 + .ra: x30
STACK CFI 1694 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16ac .cfa: sp 0 + .ra: .ra x29: x29
