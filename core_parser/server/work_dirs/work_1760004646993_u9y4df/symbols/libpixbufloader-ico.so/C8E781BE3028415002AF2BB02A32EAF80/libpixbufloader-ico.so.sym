MODULE Linux arm64 C8E781BE3028415002AF2BB02A32EAF80 libpixbufloader-ico.so
INFO CODE_ID BE81E7C82830504102AF2BB02A32EAF8C46E35A2
PUBLIC 3094 0 fill_vtable
PUBLIC 30e4 0 fill_info
STACK CFI INIT 12d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1300 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1340 48 .cfa: sp 0 + .ra: x30
STACK CFI 1344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 134c x19: .cfa -16 + ^
STACK CFI 1384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1390 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13a0 2c .cfa: sp 0 + .ra: x30
STACK CFI 13a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 13d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13e0 x19: .cfa -16 + ^
STACK CFI 1408 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1410 15c .cfa: sp 0 + .ra: x30
STACK CFI 1418 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1428 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1570 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1578 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15f0 x21: .cfa -16 + ^
STACK CFI 1628 x21: x21
STACK CFI INIT 1654 fd8 .cfa: sp 0 + .ra: x30
STACK CFI 165c .cfa: sp 192 +
STACK CFI 1668 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1684 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 168c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 16b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1728 x19: x19 x20: x20
STACK CFI 172c x21: x21 x22: x22
STACK CFI 1730 x23: x23 x24: x24
STACK CFI 1734 x25: x25 x26: x26
STACK CFI 1738 x27: x27 x28: x28
STACK CFI 1760 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1768 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1c14 x19: x19 x20: x20
STACK CFI 1c18 x21: x21 x22: x22
STACK CFI 1c1c x23: x23 x24: x24
STACK CFI 1c20 x25: x25 x26: x26
STACK CFI 1c24 x27: x27 x28: x28
STACK CFI 1c28 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 25ec x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 25f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 25fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2600 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2630 14c .cfa: sp 0 + .ra: x30
STACK CFI 2638 .cfa: sp 80 +
STACK CFI 2644 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 264c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2654 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2660 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2724 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2780 89c .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 192 +
STACK CFI 2794 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 279c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 27a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 27ac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 27b4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 27d0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2a28 .cfa: sp 192 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2a54 v8: .cfa -16 + ^
STACK CFI 2f2c v8: v8
STACK CFI 2f34 v8: .cfa -16 + ^
STACK CFI 2fdc v8: v8
STACK CFI 2ff4 v8: .cfa -16 + ^
STACK CFI 3008 v8: v8
STACK CFI 3010 v8: .cfa -16 + ^
STACK CFI 3014 v8: v8
STACK CFI 3018 v8: .cfa -16 + ^
STACK CFI INIT 3020 74 .cfa: sp 0 + .ra: x30
STACK CFI 3028 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3038 x19: .cfa -16 + ^
STACK CFI 3050 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3058 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 308c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3094 50 .cfa: sp 0 + .ra: x30
STACK CFI 309c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e4 5c .cfa: sp 0 + .ra: x30
STACK CFI 30ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3100 .cfa: sp 0 + .ra: .ra x29: x29
