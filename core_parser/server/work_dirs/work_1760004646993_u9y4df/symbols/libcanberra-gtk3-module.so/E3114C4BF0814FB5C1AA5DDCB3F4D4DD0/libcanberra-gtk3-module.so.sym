MODULE Linux arm64 E3114C4BF0814FB5C1AA5DDCB3F4D4DD0 libcanberra-gtk3-module.so
INFO CODE_ID 4B4C11E381F0B54FC1AA5DDCB3F4D4DD4B268ABA
PUBLIC 3f90 0 gtk_module_init
PUBLIC 4430 0 g_module_check_init
STACK CFI INIT 2180 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 21f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 21f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21fc x19: .cfa -16 + ^
STACK CFI 2234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 74 .cfa: sp 0 + .ra: x30
STACK CFI 2258 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2260 x19: .cfa -16 + ^
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 22b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 22c4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 22cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2348 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2370 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 2378 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2380 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 238c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2394 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 239c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 262c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2634 1450 .cfa: sp 0 + .ra: x30
STACK CFI 263c .cfa: sp 160 +
STACK CFI 2648 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2650 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 265c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 26a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 26b0 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 26b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27f0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2828 x25: x25 x26: x26
STACK CFI 2960 x21: x21 x22: x22
STACK CFI 2964 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29e4 x21: x21 x22: x22
STACK CFI 29e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2a0c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2a9c x25: x25 x26: x26
STACK CFI 2ab8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2ad4 x25: x25 x26: x26
STACK CFI 2b30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2b34 x25: x25 x26: x26
STACK CFI 2c5c x21: x21 x22: x22
STACK CFI 2c60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e1c x21: x21 x22: x22
STACK CFI 2e20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e78 x21: x21 x22: x22
STACK CFI 2e7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2efc x21: x21 x22: x22
STACK CFI 2f00 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2f04 x25: x25 x26: x26
STACK CFI 2f4c x21: x21 x22: x22
STACK CFI 2f50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 31bc x21: x21 x22: x22
STACK CFI 31c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3204 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3270 x25: x25 x26: x26
STACK CFI 32b4 x21: x21 x22: x22
STACK CFI 32b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3340 x21: x21 x22: x22
STACK CFI 3344 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 34e8 x25: x25 x26: x26
STACK CFI 3538 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 35b0 x25: x25 x26: x26
STACK CFI 35f4 x21: x21 x22: x22
STACK CFI 35f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3644 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3668 x25: x25 x26: x26
STACK CFI 3768 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 37bc x25: x25 x26: x26
STACK CFI 3924 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3944 x25: x25 x26: x26
STACK CFI 3968 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3978 x25: x25 x26: x26
STACK CFI 39ec x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a44 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 3a48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3a4c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a50 x25: x25 x26: x26
STACK CFI 3a78 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a7c x21: x21 x22: x22
STACK CFI 3a80 x25: x25 x26: x26
STACK CFI INIT 3a84 70 .cfa: sp 0 + .ra: x30
STACK CFI 3a8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ae8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3af4 5c .cfa: sp 0 + .ra: x30
STACK CFI 3afc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b50 bc .cfa: sp 0 + .ra: x30
STACK CFI 3b58 .cfa: sp 48 +
STACK CFI 3b64 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3be0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c10 130 .cfa: sp 0 + .ra: x30
STACK CFI 3c18 .cfa: sp 64 +
STACK CFI 3c24 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c4c x21: .cfa -16 + ^
STACK CFI 3cc4 x21: x21
STACK CFI 3ccc x19: x19 x20: x20
STACK CFI 3cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3cf8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d34 x19: x19 x20: x20 x21: x21
STACK CFI 3d38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d3c x21: .cfa -16 + ^
STACK CFI INIT 3d40 250 .cfa: sp 0 + .ra: x30
STACK CFI 3d48 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3d50 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3d5c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3d70 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3da0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3da8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3e94 x25: x25 x26: x26
STACK CFI 3e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3ea0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3f48 x25: x25 x26: x26
STACK CFI 3f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3f68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3f90 498 .cfa: sp 0 + .ra: x30
STACK CFI 3f98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fa8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3fb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4430 20 .cfa: sp 0 + .ra: x30
STACK CFI 4438 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4444 .cfa: sp 0 + .ra: .ra x29: x29
