MODULE Linux arm64 C97B2AF8F3F48CB355EA848B6DE09E9B0 lmtcpsrv.so
INFO CODE_ID F82A7BC9F4F3B38C55EA848B6DE09E9BC9ED2715
PUBLIC 1cb0 0 tcps_sessDebugPrint
PUBLIC 1e94 0 tcps_sessQueryInterface
PUBLIC 20a0 0 tcpsrvDebugPrint
PUBLIC 2544 0 tcpsrvQueryInterface
PUBLIC 27d0 0 tcpsrvConstruct
PUBLIC 2864 0 tcps_sessConstruct
PUBLIC 28f4 0 tcps_sessDestruct
PUBLIC 37f0 0 tcpsrvDestruct
PUBLIC 5744 0 tcps_sessClassExit
PUBLIC 57e4 0 tcps_sessClassInit
PUBLIC 5980 0 tcpsrvClassExit
PUBLIC 5be4 0 tcpsrvClassInit
PUBLIC 5e40 0 modInit
STACK CFI INIT 1ba0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bd0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c10 48 .cfa: sp 0 + .ra: x30
STACK CFI 1c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c1c x19: .cfa -16 + ^
STACK CFI 1c54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c70 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1cb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1cd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1cd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ce0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d20 24 .cfa: sp 0 + .ra: x30
STACK CFI 1d2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d44 24 .cfa: sp 0 + .ra: x30
STACK CFI 1d50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d70 24 .cfa: sp 0 + .ra: x30
STACK CFI 1d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d94 40 .cfa: sp 0 + .ra: x30
STACK CFI 1da4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1dcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1dd4 24 .cfa: sp 0 + .ra: x30
STACK CFI 1de0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e00 24 .cfa: sp 0 + .ra: x30
STACK CFI 1e0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e24 70 .cfa: sp 0 + .ra: x30
STACK CFI 1e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e40 x21: .cfa -16 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1e94 cc .cfa: sp 0 + .ra: x30
STACK CFI 1e9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1f60 28 .cfa: sp 0 + .ra: x30
STACK CFI 1f6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f90 24 .cfa: sp 0 + .ra: x30
STACK CFI 1f9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1fb4 5c .cfa: sp 0 + .ra: x30
STACK CFI 1fbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2000 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2010 8c .cfa: sp 0 + .ra: x30
STACK CFI 2018 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2020 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2028 x21: .cfa -16 + ^
STACK CFI 208c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2094 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 20a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 20a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 20cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20e4 24 .cfa: sp 0 + .ra: x30
STACK CFI 20f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2100 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2110 24 .cfa: sp 0 + .ra: x30
STACK CFI 211c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 212c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2134 24 .cfa: sp 0 + .ra: x30
STACK CFI 2140 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2150 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2160 24 .cfa: sp 0 + .ra: x30
STACK CFI 216c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 217c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2184 24 .cfa: sp 0 + .ra: x30
STACK CFI 2190 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 21bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21d4 24 .cfa: sp 0 + .ra: x30
STACK CFI 21e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2200 24 .cfa: sp 0 + .ra: x30
STACK CFI 220c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 221c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2224 24 .cfa: sp 0 + .ra: x30
STACK CFI 2230 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2250 24 .cfa: sp 0 + .ra: x30
STACK CFI 225c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 226c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2274 24 .cfa: sp 0 + .ra: x30
STACK CFI 2280 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2290 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 22ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 22bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22c4 24 .cfa: sp 0 + .ra: x30
STACK CFI 22d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 22e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 230c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2314 24 .cfa: sp 0 + .ra: x30
STACK CFI 2320 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2340 24 .cfa: sp 0 + .ra: x30
STACK CFI 234c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 235c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2364 24 .cfa: sp 0 + .ra: x30
STACK CFI 2370 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2380 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2390 24 .cfa: sp 0 + .ra: x30
STACK CFI 239c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23b4 24 .cfa: sp 0 + .ra: x30
STACK CFI 23c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 23ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2404 24 .cfa: sp 0 + .ra: x30
STACK CFI 2410 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2420 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2430 24 .cfa: sp 0 + .ra: x30
STACK CFI 243c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 244c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2454 24 .cfa: sp 0 + .ra: x30
STACK CFI 2460 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2480 24 .cfa: sp 0 + .ra: x30
STACK CFI 248c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 249c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24a4 24 .cfa: sp 0 + .ra: x30
STACK CFI 24b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 24dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24f4 24 .cfa: sp 0 + .ra: x30
STACK CFI 2500 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2510 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2520 24 .cfa: sp 0 + .ra: x30
STACK CFI 252c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2544 258 .cfa: sp 0 + .ra: x30
STACK CFI 2560 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 278c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27a0 2c .cfa: sp 0 + .ra: x30
STACK CFI 27ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e4 x19: .cfa -16 + ^
STACK CFI 2854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 285c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2864 90 .cfa: sp 0 + .ra: x30
STACK CFI 286c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2878 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 28e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 28f4 d0 .cfa: sp 0 + .ra: x30
STACK CFI 28fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2908 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 29ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29c4 8c .cfa: sp 0 + .ra: x30
STACK CFI 29cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2a50 184 .cfa: sp 0 + .ra: x30
STACK CFI 2a58 .cfa: sp 64 +
STACK CFI 2a64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a78 x21: .cfa -16 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2ae4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2bd4 70 .cfa: sp 0 + .ra: x30
STACK CFI 2bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c44 70 .cfa: sp 0 + .ra: x30
STACK CFI 2c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2cb4 70 .cfa: sp 0 + .ra: x30
STACK CFI 2cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ccc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2d24 70 .cfa: sp 0 + .ra: x30
STACK CFI 2d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2d94 64 .cfa: sp 0 + .ra: x30
STACK CFI 2d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2dac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e00 ec .cfa: sp 0 + .ra: x30
STACK CFI 2e08 .cfa: sp 80 +
STACK CFI 2e0c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2eb4 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2ef0 130 .cfa: sp 0 + .ra: x30
STACK CFI 2ef8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f24 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f88 x21: x21 x22: x22
STACK CFI 2f8c x23: x23 x24: x24
STACK CFI 2fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3020 25c .cfa: sp 0 + .ra: x30
STACK CFI 3028 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3030 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3038 x23: .cfa -16 + ^
STACK CFI 3044 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 326c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3274 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3280 568 .cfa: sp 0 + .ra: x30
STACK CFI 3288 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 32a4 .cfa: sp 8416 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 333c x27: .cfa -16 + ^
STACK CFI 3340 x28: .cfa -8 + ^
STACK CFI 347c x27: x27
STACK CFI 3480 x28: x28
STACK CFI 34e4 .cfa: sp 96 +
STACK CFI 34fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3504 .cfa: sp 8416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 37c4 x27: x27 x28: x28
STACK CFI 37e0 x27: .cfa -16 + ^
STACK CFI 37e4 x28: .cfa -8 + ^
STACK CFI INIT 37f0 240 .cfa: sp 0 + .ra: x30
STACK CFI 37f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3804 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 380c x23: .cfa -16 + ^
STACK CFI 3a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3a28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3a30 270 .cfa: sp 0 + .ra: x30
STACK CFI 3a38 .cfa: sp 144 +
STACK CFI 3a3c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b04 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3b5c x23: .cfa -16 + ^
STACK CFI 3b94 x23: x23
STACK CFI 3be0 x23: .cfa -16 + ^
STACK CFI 3c8c x23: x23
STACK CFI 3c9c x23: .cfa -16 + ^
STACK CFI INIT 3ca0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3cb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3d50 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 3d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d6c .cfa: sp 65600 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3d74 .cfa: sp 131136 +
STACK CFI 3d7c .cfa: sp 131168 +
STACK CFI 3e68 .cfa: sp 131136 +
STACK CFI 3e6c .cfa: sp 64 +
STACK CFI 3e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e88 .cfa: sp 131168 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3f30 670 .cfa: sp 0 + .ra: x30
STACK CFI 3f38 .cfa: sp 176 +
STACK CFI 3f44 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3f4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3f58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3f64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3f84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3fbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 431c x25: x25 x26: x26
STACK CFI 4324 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 43bc x25: x25 x26: x26
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 43fc .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4424 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4450 x25: x25 x26: x26
STACK CFI 4468 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 44e0 x25: x25 x26: x26
STACK CFI 44e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4588 x25: x25 x26: x26
STACK CFI 458c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 45a0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 45a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 45c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 466c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4680 fc .cfa: sp 0 + .ra: x30
STACK CFI 4688 .cfa: sp 288 +
STACK CFI 4698 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46ac x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4758 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4780 38 .cfa: sp 0 + .ra: x30
STACK CFI 4788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4794 x19: .cfa -16 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 47c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4810 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4818 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4820 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4828 x21: .cfa -16 + ^
STACK CFI 48b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 48c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 48e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4930 3c .cfa: sp 0 + .ra: x30
STACK CFI 4938 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4940 x19: .cfa -16 + ^
STACK CFI 4964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4970 50 .cfa: sp 0 + .ra: x30
STACK CFI 4980 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4988 x19: .cfa -16 + ^
STACK CFI 49a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 49d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49d8 x19: .cfa -16 + ^
STACK CFI 49f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a10 50 .cfa: sp 0 + .ra: x30
STACK CFI 4a20 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a28 x19: .cfa -16 + ^
STACK CFI 4a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a60 50 .cfa: sp 0 + .ra: x30
STACK CFI 4a70 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a78 x19: .cfa -16 + ^
STACK CFI 4a98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ab0 50 .cfa: sp 0 + .ra: x30
STACK CFI 4ac0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ac8 x19: .cfa -16 + ^
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b00 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 4b08 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b24 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4b2c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c34 x19: x19 x20: x20
STACK CFI 4c38 x21: x21 x22: x22
STACK CFI 4c48 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4c50 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4c70 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4c88 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c8c x19: x19 x20: x20
STACK CFI 4c90 x21: x21 x22: x22
STACK CFI 4ca0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 4cb0 53c .cfa: sp 0 + .ra: x30
STACK CFI 4cb8 .cfa: sp 352 +
STACK CFI 4cc4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4ce4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 51c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 51d0 .cfa: sp 352 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 51f0 470 .cfa: sp 0 + .ra: x30
STACK CFI 51f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5210 .cfa: sp 2720 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 526c .cfa: sp 80 +
STACK CFI 5284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 528c .cfa: sp 2720 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5660 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5678 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5680 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 573c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5744 a0 .cfa: sp 0 + .ra: x30
STACK CFI 574c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5758 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5764 x21: .cfa -16 + ^
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 57e4 19c .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 57f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5800 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5808 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5968 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5980 168 .cfa: sp 0 + .ra: x30
STACK CFI 5988 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5994 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 59a0 x21: .cfa -16 + ^
STACK CFI 5ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5af0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5af8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5b04 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5b38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b40 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5b44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5b60 x25: .cfa -16 + ^
STACK CFI 5bc4 x23: x23 x24: x24
STACK CFI 5bc8 x25: x25
STACK CFI 5bcc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 5be4 254 .cfa: sp 0 + .ra: x30
STACK CFI 5bec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5c00 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5c10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5d74 x25: .cfa -16 + ^
STACK CFI 5e04 x25: x25
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5e1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5e20 x25: x25
STACK CFI 5e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5e40 108 .cfa: sp 0 + .ra: x30
STACK CFI 5e48 .cfa: sp 64 +
STACK CFI 5e54 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e68 x21: .cfa -16 + ^
STACK CFI 5f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f20 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5f50 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b60 24 .cfa: sp 0 + .ra: x30
STACK CFI 1b64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x29: x29
