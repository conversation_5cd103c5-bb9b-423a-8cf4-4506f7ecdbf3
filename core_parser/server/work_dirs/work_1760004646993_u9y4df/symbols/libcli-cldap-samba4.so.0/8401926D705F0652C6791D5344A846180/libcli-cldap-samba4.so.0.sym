MODULE Linux arm64 8401926D705F0652C6791D5344A846180 libcli-cldap-samba4.so.0
INFO CODE_ID 6D9201845F705206C6791D5344A846181C01A910
PUBLIC 35a0 0 push_netlogon_samlogon_response
PUBLIC 36e0 0 pull_netlogon_samlogon_response
PUBLIC 3a30 0 map_netlogon_samlogon_response
PUBLIC 3bb4 0 push_nbt_netlogon_response
PUBLIC 3d10 0 pull_nbt_netlogon_response
PUBLIC 3f00 0 cldap_socket_init
PUBLIC 4130 0 cldap_set_incoming_handler
PUBLIC 41a0 0 cldap_reply_send
PUBLIC 4430 0 cldap_search_send
PUBLIC 4890 0 cldap_search_recv
PUBLIC 4b30 0 cldap_search
PUBLIC 4c90 0 cldap_netlogon_create_filter
PUBLIC 4e10 0 cldap_netlogon_send
PUBLIC 4fa0 0 cldap_netlogon_recv
PUBLIC 50f0 0 cldap_netlogon
PUBLIC 5250 0 cldap_empty_reply
PUBLIC 52e4 0 cldap_error_reply
PUBLIC 5390 0 cldap_netlogon_reply
STACK CFI INIT 2b50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bcc x19: .cfa -16 + ^
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c20 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c30 x19: .cfa -16 + ^
STACK CFI 2ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2ce0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cf0 x19: .cfa -16 + ^
STACK CFI 2d08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2d74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d94 5c .cfa: sp 0 + .ra: x30
STACK CFI 2d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2da4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2df0 374 .cfa: sp 0 + .ra: x30
STACK CFI 2df8 .cfa: sp 80 +
STACK CFI 2e04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e10 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fe4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 30b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 30bc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3164 110 .cfa: sp 0 + .ra: x30
STACK CFI 316c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3174 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3204 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3274 220 .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 64 +
STACK CFI 3288 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3294 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 335c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3364 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3494 10c .cfa: sp 0 + .ra: x30
STACK CFI 349c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 354c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3578 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 358c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 35a0 138 .cfa: sp 0 + .ra: x30
STACK CFI 35a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3634 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3670 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 36e0 348 .cfa: sp 0 + .ra: x30
STACK CFI 36f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3704 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3710 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3748 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 3760 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 37e0 x23: x23 x24: x24
STACK CFI 37e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 380c x23: x23 x24: x24
STACK CFI 3814 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3828 x25: .cfa -16 + ^
STACK CFI 386c x25: x25
STACK CFI 38fc x23: x23 x24: x24
STACK CFI 3904 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3908 x25: x25
STACK CFI 3914 x23: x23 x24: x24
STACK CFI 391c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 394c x25: x25
STACK CFI 39b8 x25: .cfa -16 + ^
STACK CFI 39ec x25: x25
STACK CFI INIT 3a30 184 .cfa: sp 0 + .ra: x30
STACK CFI 3a38 .cfa: sp 192 +
STACK CFI 3a3c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a44 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3aa0 .cfa: sp 192 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3bb4 154 .cfa: sp 0 + .ra: x30
STACK CFI 3bbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3be8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3bf8 x19: x19 x20: x20
STACK CFI 3c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3c7c x19: x19 x20: x20
STACK CFI 3c80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ce0 x21: .cfa -16 + ^
STACK CFI 3d04 x21: x21
STACK CFI INIT 3d10 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 3d18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d28 x21: .cfa -16 + ^
STACK CFI 3d34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d7c x19: x19 x20: x20
STACK CFI 3d84 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3d8c .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3da0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3ddc x19: x19 x20: x20
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3df0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e28 x19: x19 x20: x20
STACK CFI 3e30 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3e38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e44 x19: x19 x20: x20
STACK CFI 3e48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e70 x19: x19 x20: x20
STACK CFI 3e74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3eac x19: x19 x20: x20
STACK CFI 3eb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3f00 22c .cfa: sp 0 + .ra: x30
STACK CFI 3f08 .cfa: sp 96 +
STACK CFI 3f14 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f24 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3f6c x25: .cfa -16 + ^
STACK CFI 3f90 x25: x25
STACK CFI 3fcc x23: x23 x24: x24
STACK CFI 3fd0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3fd8 x23: x23 x24: x24
STACK CFI 4010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4018 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 401c x25: x25
STACK CFI 40c0 x23: x23 x24: x24
STACK CFI 40c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 410c x25: .cfa -16 + ^
STACK CFI 4114 x23: x23 x24: x24
STACK CFI 411c x25: x25
STACK CFI 4124 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4128 x25: .cfa -16 + ^
STACK CFI INIT 4130 70 .cfa: sp 0 + .ra: x30
STACK CFI 4150 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4158 x19: .cfa -16 + ^
STACK CFI 417c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4184 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41a0 290 .cfa: sp 0 + .ra: x30
STACK CFI 41a8 .cfa: sp 112 +
STACK CFI 41ac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 41b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4248 x23: .cfa -16 + ^
STACK CFI 4360 x21: x21 x22: x22
STACK CFI 4364 x23: x23
STACK CFI 439c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43a4 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 43b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 43b4 x23: x23
STACK CFI 43d4 x21: x21 x22: x22
STACK CFI 43e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 43ec x23: x23
STACK CFI 43f4 x23: .cfa -16 + ^
STACK CFI 4414 x23: x23
STACK CFI 441c x21: x21 x22: x22
STACK CFI 4428 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 442c x23: .cfa -16 + ^
STACK CFI INIT 4430 45c .cfa: sp 0 + .ra: x30
STACK CFI 4438 .cfa: sp 96 +
STACK CFI 4448 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4458 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4464 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45a0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 45f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4730 x23: x23 x24: x24
STACK CFI 47b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4830 x23: x23 x24: x24
STACK CFI 4838 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4854 x23: x23 x24: x24
STACK CFI 4858 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4860 x23: x23 x24: x24
STACK CFI 486c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4870 x23: x23 x24: x24
STACK CFI 4888 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 4890 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4898 .cfa: sp 96 +
STACK CFI 48a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 48ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 48b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 48c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 493c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4944 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4a70 bc .cfa: sp 0 + .ra: x30
STACK CFI 4a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a88 x21: .cfa -16 + ^
STACK CFI 4b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4b30 160 .cfa: sp 0 + .ra: x30
STACK CFI 4b38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b40 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4b94 x23: .cfa -16 + ^
STACK CFI 4c00 x23: x23
STACK CFI 4c04 x23: .cfa -16 + ^
STACK CFI 4c2c x23: x23
STACK CFI 4c30 x23: .cfa -16 + ^
STACK CFI 4c4c x23: x23
STACK CFI 4c54 x23: .cfa -16 + ^
STACK CFI 4c58 x23: x23
STACK CFI 4c5c x23: .cfa -16 + ^
STACK CFI 4c8c x23: x23
STACK CFI INIT 4c90 178 .cfa: sp 0 + .ra: x30
STACK CFI 4c98 .cfa: sp 80 +
STACK CFI 4ca4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4cac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4cb8 x21: .cfa -16 + ^
STACK CFI 4dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4e04 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4e10 18c .cfa: sp 0 + .ra: x30
STACK CFI 4e18 .cfa: sp 80 +
STACK CFI 4e28 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4f30 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4fa0 150 .cfa: sp 0 + .ra: x30
STACK CFI 4fa8 .cfa: sp 80 +
STACK CFI 4fb4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4fd0 x23: .cfa -16 + ^
STACK CFI 506c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5074 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 50f0 160 .cfa: sp 0 + .ra: x30
STACK CFI 50f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5100 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 510c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5130 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5154 x23: .cfa -16 + ^
STACK CFI 51c0 x23: x23
STACK CFI 51c4 x23: .cfa -16 + ^
STACK CFI 51ec x23: x23
STACK CFI 51f0 x23: .cfa -16 + ^
STACK CFI 520c x23: x23
STACK CFI 5214 x23: .cfa -16 + ^
STACK CFI 5218 x23: x23
STACK CFI 521c x23: .cfa -16 + ^
STACK CFI 524c x23: x23
STACK CFI INIT 5250 94 .cfa: sp 0 + .ra: x30
STACK CFI 5258 .cfa: sp 112 +
STACK CFI 5268 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5278 x19: .cfa -16 + ^
STACK CFI 52d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 52e0 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 52e4 ac .cfa: sp 0 + .ra: x30
STACK CFI 52ec .cfa: sp 128 +
STACK CFI 52fc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 530c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5318 x21: .cfa -16 + ^
STACK CFI 5384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 538c .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5390 154 .cfa: sp 0 + .ra: x30
STACK CFI 5398 .cfa: sp 176 +
STACK CFI 53a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 53ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 53b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 53c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 54bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 54c4 .cfa: sp 176 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
