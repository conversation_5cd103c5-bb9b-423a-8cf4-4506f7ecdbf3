MODULE Linux arm64 19DD61120F9D30E2ED3A6EDE130657EE0 librspreload.so
INFO CODE_ID 1261DD199D0FE230ED3A6EDE130657EEBED067F1
PUBLIC 2c50 0 recv
PUBLIC 2d20 0 recvfrom
PUBLIC 2e00 0 read
PUBLIC 2ef4 0 poll
PUBLIC 30f4 0 socket
PUBLIC 33f0 0 bind
PUBLIC 3470 0 listen
PUBLIC 35a4 0 accept
PUBLIC 3770 0 connect
PUBLIC 39b4 0 recvmsg
PUBLIC 3a80 0 readv
PUBLIC 3b74 0 send
PUBLIC 3c40 0 sendto
PUBLIC 3d20 0 sendmsg
PUBLIC 3df0 0 write
PUBLIC 3ee4 0 writev
PUBLIC 3fe0 0 select
PUBLIC 4810 0 shutdown
PUBLIC 4890 0 close
PUBLIC 49f4 0 getpeername
PUBLIC 4a74 0 getsockname
PUBLIC 4b30 0 setsockopt
PUBLIC 4bc0 0 getsockopt
PUBLIC 4c50 0 fcntl
PUBLIC 4e20 0 dup2
PUBLIC 50f4 0 sendfile
STACK CFI INIT 1a20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a50 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a90 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a9c x19: .cfa -16 + ^
STACK CFI 1ad4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af0 15c .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 80 +
STACK CFI 1afc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1b18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1b70 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1be8 x23: x23 x24: x24
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c1c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1c40 x23: x23 x24: x24
STACK CFI 1c48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 1c50 64 .cfa: sp 0 + .ra: x30
STACK CFI 1c58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c60 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1cb4 138 .cfa: sp 0 + .ra: x30
STACK CFI 1cbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ccc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1cf4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1cfc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1d58 x21: x21 x22: x22
STACK CFI 1d5c x23: x23 x24: x24
STACK CFI 1d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1db8 x21: x21 x22: x22
STACK CFI 1dbc x23: x23 x24: x24
STACK CFI 1dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1df0 264 .cfa: sp 0 + .ra: x30
STACK CFI 1df8 .cfa: sp 224 +
STACK CFI 1e04 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1e0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1e24 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1f44 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2054 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 205c .cfa: sp 160 +
STACK CFI 2068 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2070 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2094 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 214c .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2188 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2220 x27: x27 x28: x28
STACK CFI 2224 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2240 x27: x27 x28: x28
STACK CFI 2258 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2340 x27: x27 x28: x28
STACK CFI 2344 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2350 900 .cfa: sp 0 + .ra: x30
STACK CFI 2358 .cfa: sp 368 +
STACK CFI 2364 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2370 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 23c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 23d0 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 23e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2924 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2938 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2b70 x23: x23 x24: x24
STACK CFI 2b74 x27: x27 x28: x28
STACK CFI 2b7c x25: x25 x26: x26
STACK CFI 2b88 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c2c x23: x23 x24: x24
STACK CFI 2c30 x27: x27 x28: x28
STACK CFI 2c34 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c40 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2c44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2c4c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2c50 cc .cfa: sp 0 + .ra: x30
STACK CFI 2c58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d20 dc .cfa: sp 0 + .ra: x30
STACK CFI 2d28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d9c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2dbc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2e00 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2e08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e5c x23: .cfa -16 + ^
STACK CFI 2e8c x23: x23
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2eac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2eb0 x23: x23
STACK CFI 2ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2edc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ef4 200 .cfa: sp 0 + .ra: x30
STACK CFI 2efc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f04 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f10 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f20 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2fbc x25: .cfa -16 + ^
STACK CFI 308c x25: x25
STACK CFI 3090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3098 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 30f4 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 30fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3104 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3110 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3120 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3128 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3158 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3204 x19: x19 x20: x20
STACK CFI 3230 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3238 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 32e0 x19: x19 x20: x20
STACK CFI 32e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3334 x19: x19 x20: x20
STACK CFI 3348 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3350 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 33f0 80 .cfa: sp 0 + .ra: x30
STACK CFI 33f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3450 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3464 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3468 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3470 134 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 348c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 34ec x19: x19 x20: x20
STACK CFI 34f0 x21: x21 x22: x22
STACK CFI 34f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3544 x19: x19 x20: x20
STACK CFI 354c x21: x21 x22: x22
STACK CFI 355c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3564 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3568 x19: x19 x20: x20
STACK CFI 3570 x21: x21 x22: x22
STACK CFI 358c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3590 x19: x19 x20: x20
STACK CFI 3598 x21: x21 x22: x22
STACK CFI 359c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35a4 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 35ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 35b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 35c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 35d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3614 x23: x23 x24: x24
STACK CFI 3630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3638 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3690 x23: x23 x24: x24
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 369c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3770 244 .cfa: sp 0 + .ra: x30
STACK CFI 3778 .cfa: sp 112 +
STACK CFI 3784 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 378c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3794 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 379c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 37d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 37ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3814 x25: x25 x26: x26
STACK CFI 381c x27: x27 x28: x28
STACK CFI 3864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3870 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 3874 x25: x25 x26: x26
STACK CFI 3878 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3940 x25: x25 x26: x26
STACK CFI 3944 x27: x27 x28: x28
STACK CFI 3948 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3994 x25: x25 x26: x26
STACK CFI 3998 x27: x27 x28: x28
STACK CFI 399c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 39a4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 39a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 39ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 39b4 cc .cfa: sp 0 + .ra: x30
STACK CFI 39bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 39c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3a80 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3a88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3aa0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3adc x23: .cfa -16 + ^
STACK CFI 3b0c x23: x23
STACK CFI 3b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3b30 x23: x23
STACK CFI 3b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3b74 cc .cfa: sp 0 + .ra: x30
STACK CFI 3b7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bf0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c40 dc .cfa: sp 0 + .ra: x30
STACK CFI 3c48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d20 cc .cfa: sp 0 + .ra: x30
STACK CFI 3d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3df0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e4c x23: .cfa -16 + ^
STACK CFI 3e7c x23: x23
STACK CFI 3e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3ea0 x23: x23
STACK CFI 3ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ecc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ee4 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3eec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ef4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f04 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f40 x23: .cfa -16 + ^
STACK CFI 3f70 x23: x23
STACK CFI 3f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f90 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3f94 x23: x23
STACK CFI 3fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3fe0 830 .cfa: sp 0 + .ra: x30
STACK CFI 3fe8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ff0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3ff8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 401c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4074 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4190 x27: x27 x28: x28
STACK CFI 4198 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4238 x27: x27 x28: x28
STACK CFI 4250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4258 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4740 x27: x27 x28: x28
STACK CFI 4758 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4800 x27: x27 x28: x28
STACK CFI 4808 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4810 78 .cfa: sp 0 + .ra: x30
STACK CFI 4818 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 486c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 487c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4880 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4890 164 .cfa: sp 0 + .ra: x30
STACK CFI 4898 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 48a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 48c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 48e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 492c x21: x21 x22: x22
STACK CFI 4930 x23: x23 x24: x24
STACK CFI 4934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 493c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4940 x21: x21 x22: x22
STACK CFI 4944 x23: x23 x24: x24
STACK CFI 495c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4964 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 49b4 x21: x21 x22: x22
STACK CFI 49b8 x23: x23 x24: x24
STACK CFI 49bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 49d0 x21: x21 x22: x22
STACK CFI 49e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 49f4 80 .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a74 bc .cfa: sp 0 + .ra: x30
STACK CFI 4a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4b30 90 .cfa: sp 0 + .ra: x30
STACK CFI 4b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4bc0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4bc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4c50 1cc .cfa: sp 0 + .ra: x30
STACK CFI 4c58 .cfa: sp 112 +
STACK CFI 4c64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c6c x21: .cfa -32 + ^
STACK CFI 4c7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ddc .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4e20 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 4e28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4e30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4e3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4ed0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4f24 x25: .cfa -16 + ^
STACK CFI 4f40 x25: x25
STACK CFI 4fac x23: x23 x24: x24
STACK CFI 4fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4fc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4ffc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5028 x23: x23 x24: x24
STACK CFI 502c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5034 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 505c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5064 x23: x23 x24: x24
STACK CFI 5070 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 508c x25: x25
STACK CFI 5090 x23: x23 x24: x24
STACK CFI 50b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 50d0 x23: x23 x24: x24
STACK CFI 50e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 50e8 x25: x25
STACK CFI INIT 50f4 158 .cfa: sp 0 + .ra: x30
STACK CFI 50fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5104 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5114 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 514c x23: .cfa -16 + ^
STACK CFI 515c x23: x23
STACK CFI 5178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5188 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 51f8 x23: x23
STACK CFI 51fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5204 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5250 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 19e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5280 1c .cfa: sp 0 + .ra: x30
STACK CFI 5284 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5294 .cfa: sp 0 + .ra: .ra x29: x29
