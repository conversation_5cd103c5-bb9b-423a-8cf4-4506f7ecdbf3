MODULE Linux arm64 CA0D21C12FBCC17F09BD05E7840C020F0 libpipewire-module-pipe-tunnel.so
INFO CODE_ID C1210DCABC2F7FC109BD05E7840C020FFB31BF5D
PUBLIC a960 0 pipewire__module_init
STACK CFI INIT 5910 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5940 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5980 48 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 598c x19: .cfa -16 + ^
STACK CFI 59c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 59d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 59e0 340 .cfa: sp 0 + .ra: x30
STACK CFI 59e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5ae0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5c24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5c7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5cfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5d20 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 5d28 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d30 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d3c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5d50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5d54 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5f4c x19: x19 x20: x20
STACK CFI 5f54 x23: x23 x24: x24
STACK CFI 5f68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f70 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5f74 x19: x19 x20: x20
STACK CFI 5f80 x23: x23 x24: x24
STACK CFI 5f8c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5f9c x19: x19 x20: x20
STACK CFI 5fa8 x23: x23 x24: x24
STACK CFI 5fb4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5fbc .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5fc8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5fcc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 5fd0 50 .cfa: sp 0 + .ra: x30
STACK CFI 5fd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fe0 x19: .cfa -16 + ^
STACK CFI 6018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6020 48 .cfa: sp 0 + .ra: x30
STACK CFI 6028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 604c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 605c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6070 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 6078 .cfa: sp 480 +
STACK CFI 608c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 60a4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 60ac x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 60c4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 6870 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6878 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 8d00 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8d08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d10 x19: .cfa -32 + ^
STACK CFI 8d6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8da4 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8dac .cfa: sp 64 +
STACK CFI 8dc4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8e50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8e58 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8e60 160 .cfa: sp 0 + .ra: x30
STACK CFI 8e68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8e70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8e78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8f6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8fc0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 8fc8 .cfa: sp 64 +
STACK CFI 8fd4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fdc x19: .cfa -16 + ^
STACK CFI 9030 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9038 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 907c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 90e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 90ec .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9194 54 .cfa: sp 0 + .ra: x30
STACK CFI 919c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91a4 x19: .cfa -16 + ^
STACK CFI 91e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 91f0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 91f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9208 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9258 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 92a0 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 92a8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 92b0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 92c8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 92ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 92f4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 92f8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9400 x19: x19 x20: x20
STACK CFI 9404 x21: x21 x22: x22
STACK CFI 9408 x25: x25 x26: x26
STACK CFI 940c x27: x27 x28: x28
STACK CFI 941c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9424 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 94d4 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 94fc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9504 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 9528 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 9554 4a4 .cfa: sp 0 + .ra: x30
STACK CFI 955c .cfa: sp 112 +
STACK CFI 9560 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9568 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9574 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9580 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9588 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9590 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9668 x21: x21 x22: x22
STACK CFI 9670 x25: x25 x26: x26
STACK CFI 9674 x27: x27 x28: x28
STACK CFI 9680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 9688 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 98e0 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 9918 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9930 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9980 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 999c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 99c0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9a00 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 9a08 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a14 .cfa: sp 1200 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9a54 x21: .cfa -48 + ^
STACK CFI 9a5c x22: .cfa -40 + ^
STACK CFI 9a64 x23: .cfa -32 + ^
STACK CFI 9a68 x24: .cfa -24 + ^
STACK CFI 9bbc x21: x21
STACK CFI 9bc4 x22: x22
STACK CFI 9bc8 x23: x23
STACK CFI 9bcc x24: x24
STACK CFI 9bec .cfa: sp 80 +
STACK CFI 9bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9bfc .cfa: sp 1200 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9c14 x25: .cfa -16 + ^
STACK CFI 9c80 x25: x25
STACK CFI 9d78 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 9d8c x21: .cfa -48 + ^
STACK CFI 9d90 x22: .cfa -40 + ^
STACK CFI 9d94 x23: .cfa -32 + ^
STACK CFI 9d98 x24: .cfa -24 + ^
STACK CFI 9d9c x25: .cfa -16 + ^
STACK CFI INIT 9da0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 9da8 .cfa: sp 112 +
STACK CFI 9dac .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9db4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9de4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9dec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9df0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9df4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9dfc x27: .cfa -16 + ^
STACK CFI 9e94 x21: x21 x22: x22
STACK CFI 9e98 x23: x23 x24: x24
STACK CFI 9e9c x25: x25 x26: x26
STACK CFI 9ea0 x27: x27
STACK CFI 9ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9eac .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9ec0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9f0c x23: x23 x24: x24
STACK CFI 9f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f20 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9f38 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a000 v8: .cfa -8 + ^
STACK CFI a0a0 v8: v8
STACK CFI a0e8 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI a0ec x23: x23 x24: x24
STACK CFI a0f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a168 v8: .cfa -8 + ^
STACK CFI a180 v8: v8 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI a1b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a20c x21: x21 x22: x22
STACK CFI a210 x23: x23 x24: x24
STACK CFI a214 x25: x25 x26: x26
STACK CFI a218 x27: x27
STACK CFI a21c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a234 v8: .cfa -8 + ^
STACK CFI INIT a264 104 .cfa: sp 0 + .ra: x30
STACK CFI a26c .cfa: sp 96 +
STACK CFI a270 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a278 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a284 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2c8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a370 e0 .cfa: sp 0 + .ra: x30
STACK CFI a378 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a380 x19: .cfa -16 + ^
STACK CFI a434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a43c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a450 50 .cfa: sp 0 + .ra: x30
STACK CFI a458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a460 x19: .cfa -16 + ^
STACK CFI a498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a4a0 4b8 .cfa: sp 0 + .ra: x30
STACK CFI a4a8 .cfa: sp 448 +
STACK CFI a4b8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a4cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a4d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a4e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a658 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a960 13dc .cfa: sp 0 + .ra: x30
STACK CFI a968 .cfa: sp 320 +
STACK CFI a974 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a97c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a994 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a9e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI aaac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI aab0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI aee8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b088 x23: x23 x24: x24
STACK CFI b08c x27: x27 x28: x28
STACK CFI b090 v8: v8 v9: v9
STACK CFI b114 x25: x25 x26: x26
STACK CFI b144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b14c .cfa: sp 320 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI b1a8 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b1cc x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b1ec x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b248 x23: x23 x24: x24
STACK CFI b24c x27: x27 x28: x28
STACK CFI b254 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b270 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b280 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b284 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b2b0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b2e8 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b350 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b3a4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b3a8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b3ac v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b428 x23: x23 x24: x24
STACK CFI b430 x27: x27 x28: x28
STACK CFI b434 v8: v8 v9: v9
STACK CFI b438 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b450 v8: v8 v9: v9
STACK CFI b484 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b5dc x23: x23 x24: x24
STACK CFI b5e0 x25: x25 x26: x26
STACK CFI b5e4 x27: x27 x28: x28
STACK CFI b5e8 v8: v8 v9: v9
STACK CFI b5ec v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b62c v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b644 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b648 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b674 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b700 v8: v8 v9: v9
STACK CFI b714 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b740 x23: x23 x24: x24
STACK CFI b748 x27: x27 x28: x28
STACK CFI b74c v8: v8 v9: v9
STACK CFI b750 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b770 v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b7c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b8b4 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b980 v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b994 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b998 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b9d0 x23: x23 x24: x24
STACK CFI b9d4 x27: x27 x28: x28
STACK CFI b9d8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI b9f0 v8: v8 v9: v9 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI ba08 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bc9c v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bca0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bca4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI bca8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bcac v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI bcb0 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bcc0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bce8 x23: x23 x24: x24
STACK CFI bcf0 x27: x27 x28: x28
STACK CFI bcf4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
