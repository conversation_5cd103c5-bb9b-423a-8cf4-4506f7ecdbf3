MODULE Linux arm64 1A94FF83A1E84F2F3FC191621E41C0ED0 libpipewire-module-link-factory.so
INFO CODE_ID 83FF941AE8A12F4F3FC191621E41C0EDFA814574
PUBLIC 2de0 0 pipewire__module_init
STACK CFI INIT 1cd0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d00 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d40 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d4c x19: .cfa -16 + ^
STACK CFI 1d84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1da0 cc .cfa: sp 0 + .ra: x30
STACK CFI 1da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1db0 x19: .cfa -16 + ^
STACK CFI 1e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e70 68 .cfa: sp 0 + .ra: x30
STACK CFI 1e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e80 x19: .cfa -16 + ^
STACK CFI 1ec0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1ed0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ee0 2c .cfa: sp 0 + .ra: x30
STACK CFI 1ee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ef8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f10 64 .cfa: sp 0 + .ra: x30
STACK CFI 1f18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f24 x19: .cfa -16 + ^
STACK CFI 1f6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f74 104 .cfa: sp 0 + .ra: x30
STACK CFI 1f7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1fec x21: x21 x22: x22
STACK CFI 1ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 201c x21: x21 x22: x22
STACK CFI 2028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2030 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 205c x21: x21 x22: x22
STACK CFI 2064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 206c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2080 50 .cfa: sp 0 + .ra: x30
STACK CFI 2088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 20a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20d0 118 .cfa: sp 0 + .ra: x30
STACK CFI 20d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 216c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 21f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 21f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2200 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2230 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2290 120 .cfa: sp 0 + .ra: x30
STACK CFI 2298 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 22ec x21: .cfa -16 + ^
STACK CFI 2384 x21: x21
STACK CFI 2388 x21: .cfa -16 + ^
STACK CFI 238c x21: x21
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 23b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23cc x21: .cfa -16 + ^
STACK CFI 2470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2480 194 .cfa: sp 0 + .ra: x30
STACK CFI 2488 .cfa: sp 112 +
STACK CFI 2494 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 249c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2544 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2614 13c .cfa: sp 0 + .ra: x30
STACK CFI 261c .cfa: sp 96 +
STACK CFI 2628 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26b8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 26bc x21: .cfa -16 + ^
STACK CFI 2728 x21: x21
STACK CFI 272c x21: .cfa -16 + ^
STACK CFI 2744 x21: x21
STACK CFI 274c x21: .cfa -16 + ^
STACK CFI INIT 2750 428 .cfa: sp 0 + .ra: x30
STACK CFI 2758 .cfa: sp 112 +
STACK CFI 2764 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 276c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2774 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2780 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2808 x27: .cfa -16 + ^
STACK CFI 2978 x25: x25 x26: x26
STACK CFI 297c x27: x27
STACK CFI 29b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 29b8 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 29f4 x25: x25 x26: x26
STACK CFI 2a10 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2a4c x27: x27
STACK CFI 2a68 x27: .cfa -16 + ^
STACK CFI 2a98 x25: x25 x26: x26 x27: x27
STACK CFI 2ab8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2b2c x25: x25 x26: x26
STACK CFI 2b30 x27: x27
STACK CFI 2b34 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2b64 x25: x25 x26: x26
STACK CFI 2b68 x27: x27
STACK CFI 2b70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b74 x27: .cfa -16 + ^
STACK CFI INIT 2b80 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2b88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b94 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2c30 68 .cfa: sp 0 + .ra: x30
STACK CFI 2c38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c40 x19: .cfa -16 + ^
STACK CFI 2c80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ca0 13c .cfa: sp 0 + .ra: x30
STACK CFI 2ca8 .cfa: sp 96 +
STACK CFI 2cb8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d6c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2de0 24c .cfa: sp 0 + .ra: x30
STACK CFI 2de8 .cfa: sp 96 +
STACK CFI 2df4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e00 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e0c x23: .cfa -16 + ^
STACK CFI 2fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2fb0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
