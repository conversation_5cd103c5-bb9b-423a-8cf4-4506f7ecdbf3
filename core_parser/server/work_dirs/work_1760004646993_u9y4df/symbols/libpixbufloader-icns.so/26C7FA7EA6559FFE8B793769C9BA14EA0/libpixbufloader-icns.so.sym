MODULE Linux arm64 26C7FA7EA6559FFE8B793769C9BA14EA0 libpixbufloader-icns.so
INFO CODE_ID 7EFAC72655A6FE9F8B793769C9BA14EA35C5F8A1
PUBLIC 1a30 0 fill_vtable
PUBLIC 1a74 0 fill_info
STACK CFI INIT f90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1000 48 .cfa: sp 0 + .ra: x30
STACK CFI 1004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 100c x19: .cfa -16 + ^
STACK CFI 1044 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1050 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1060 cc .cfa: sp 0 + .ra: x30
STACK CFI 1068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 10e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1120 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1130 428 .cfa: sp 0 + .ra: x30
STACK CFI 1138 .cfa: sp 112 +
STACK CFI 1148 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1160 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11d0 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1200 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1208 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1360 x21: x21 x22: x22
STACK CFI 1368 x23: x23 x24: x24
STACK CFI 136c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13a8 x21: x21 x22: x22
STACK CFI 13ac x23: x23 x24: x24
STACK CFI 13b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14ac x21: x21 x22: x22
STACK CFI 14b0 x23: x23 x24: x24
STACK CFI 14b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14fc x21: x21 x22: x22
STACK CFI 1500 x23: x23 x24: x24
STACK CFI 1504 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1510 x21: x21 x22: x22
STACK CFI 1514 x23: x23 x24: x24
STACK CFI 1518 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1524 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 1528 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 152c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 1560 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1568 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1580 .cfa: sp 4176 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1678 .cfa: sp 64 +
STACK CFI 168c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1694 .cfa: sp 4176 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1730 f4 .cfa: sp 0 + .ra: x30
STACK CFI 1738 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1748 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1824 80 .cfa: sp 0 + .ra: x30
STACK CFI 182c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1838 x19: .cfa -16 + ^
STACK CFI 1864 x19: x19
STACK CFI 186c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1898 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18a4 188 .cfa: sp 0 + .ra: x30
STACK CFI 18ac .cfa: sp 64 +
STACK CFI 18b0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19f0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a30 44 .cfa: sp 0 + .ra: x30
STACK CFI 1a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a74 58 .cfa: sp 0 + .ra: x30
STACK CFI 1a7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a90 .cfa: sp 0 + .ra: .ra x29: x29
