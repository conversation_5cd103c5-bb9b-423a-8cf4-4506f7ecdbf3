MODULE Linux arm64 9A5FDC9C622FD2FACC610E9FCFD2E9D90 libcryptsetup-token-systemd-pkcs11.so
INFO CODE_ID 9CDC5F9A2F62FAD2CC610E9FCFD2E9D98F97A0CF
PUBLIC 1690 0 cryptsetup_token_version
PUBLIC 16b0 0 cryptsetup_token_open_pin
PUBLIC 1b44 0 cryptsetup_token_open
PUBLIC 1b70 0 cryptsetup_token_buffer_free
PUBLIC 1be0 0 cryptsetup_token_dump
PUBLIC 1e34 0 cryptsetup_token_validate
STACK CFI INIT 1160 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1190 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 11d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11dc x19: .cfa -16 + ^
STACK CFI 1214 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1230 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 1238 .cfa: sp 320 +
STACK CFI 1248 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1250 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1264 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 13b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 13bc .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 14e0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 14e8 .cfa: sp 96 +
STACK CFI 14f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1504 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 161c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1624 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1690 20 .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16b0 494 .cfa: sp 0 + .ra: x30
STACK CFI 16b8 .cfa: sp 208 +
STACK CFI 16c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 16e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16ec x27: .cfa -16 + ^
STACK CFI 1904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 190c .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1b44 2c .cfa: sp 0 + .ra: x30
STACK CFI 1b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b70 70 .cfa: sp 0 + .ra: x30
STACK CFI 1b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b88 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1be0 254 .cfa: sp 0 + .ra: x30
STACK CFI 1be8 .cfa: sp 144 +
STACK CFI 1bf8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1c08 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1c38 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1c6c x27: .cfa -16 + ^
STACK CFI 1cf4 x23: x23 x24: x24
STACK CFI 1cf8 x27: x27
STACK CFI 1d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1d3c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1d44 x27: x27
STACK CFI 1db8 x23: x23 x24: x24
STACK CFI 1dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1dc8 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1e00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e24 x27: .cfa -16 + ^
STACK CFI 1e28 x23: x23 x24: x24 x27: x27
STACK CFI 1e2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e30 x27: .cfa -16 + ^
STACK CFI INIT 1e34 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 1e3c .cfa: sp 64 +
STACK CFI 1e4c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f28 x21: .cfa -16 + ^
STACK CFI 1f58 x21: x21
STACK CFI 1f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f98 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1fd4 x21: .cfa -16 + ^
STACK CFI 1ff4 x21: x21
STACK CFI 1ff8 x21: .cfa -16 + ^
