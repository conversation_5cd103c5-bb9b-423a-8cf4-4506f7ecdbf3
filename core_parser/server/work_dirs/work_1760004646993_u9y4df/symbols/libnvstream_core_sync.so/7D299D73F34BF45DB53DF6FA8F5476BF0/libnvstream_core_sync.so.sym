MODULE Linux arm64 7D299D73F34BF45DB53DF6FA8F5476BF0 libnvstream_core_sync.so
INFO CODE_ID 739D297D4BF35DF4B53DF6FA8F5476BF
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 3a10 24 0 init_have_lse_atomics
3a10 4 45 0
3a14 4 46 0
3a18 4 45 0
3a1c 4 46 0
3a20 4 47 0
3a24 4 47 0
3a28 4 48 0
3a2c 4 47 0
3a30 4 48 0
PUBLIC 3660 0 _init
PUBLIC 3a34 0 call_weak_fn
PUBLIC 3a50 0 deregister_tm_clones
PUBLIC 3a80 0 register_tm_clones
PUBLIC 3ac0 0 __do_global_dtors_aux
PUBLIC 3b10 0 frame_dummy
PUBLIC 3b20 0 linvs::sync::GetBaseSyncAttrList(linvs::sync::SyncAttrList&, linvs::sync::BaseSyncDesc const&)
PUBLIC 3c00 0 linvs::sync::SyncAttrList::operator bool() const
PUBLIC 3c20 0 linvs::sync::SyncAttrList::operator*()
PUBLIC 3c30 0 linvs::sync::SyncAttrList::operator*() const
PUBLIC 3c40 0 linvs::sync::SyncAttrList::AllocObj(linvs::sync::SyncObj&)
PUBLIC 3cc0 0 linvs::sync::SyncAttrList::SetAttrs(NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3d10 0 linvs::sync::SyncAttrList::GetSlotCount()
PUBLIC 3d20 0 linvs::sync::SyncAttrList::GetAttrs(NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3d70 0 linvs::sync::SyncAttrList::GetSlotAttrs(unsigned long, NvSciSyncAttrKeyValuePair*, unsigned long)
PUBLIC 3dc0 0 linvs::sync::SyncAttrList::DebugDump(void**, unsigned long*)
PUBLIC 3e10 0 linvs::sync::SyncAttrList::AppendUnreconciled(std::vector<linvs::sync::SyncAttrList*, std::allocator<linvs::sync::SyncAttrList*> > const&)
PUBLIC 3f30 0 linvs::sync::SyncAttrList::Reconciled()
PUBLIC 3fc0 0 linvs::sync::SyncAttrList::ValidateReconciled(std::vector<linvs::sync::SyncAttrList*, std::allocator<linvs::sync::SyncAttrList*> > const&)
PUBLIC 4150 0 linvs::sync::SyncAttrList::~SyncAttrList()
PUBLIC 4200 0 linvs::sync::SyncAttrList::SyncAttrList()
PUBLIC 42e0 0 linvs::sync::SyncAttrList::Reconcile(std::vector<linvs::sync::SyncAttrList const*, std::allocator<linvs::sync::SyncAttrList const*> > const&, linvs::sync::SyncAttrList&)
PUBLIC 44b0 0 linvs::sync::SyncAttrList::SyncAttrList(NvSciSyncAttrListRec* const&)
PUBLIC 45d0 0 linvs::sync::SyncAttrList::SyncAttrList(linvs::sync::SyncAttrList const&)
PUBLIC 46b0 0 linvs::sync::SyncAttrList::operator=(linvs::sync::SyncAttrList const&)
PUBLIC 47c0 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 47d0 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 47f0 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4800 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4810 0 std::_Sp_counted_ptr_inplace<linvs::sync::SyncAttrList::NvSciSyncAttrListWrapper, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4880 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 4900 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 49a0 0 linvs::sync::CpuWaiter::~CpuWaiter()
PUBLIC 49c0 0 linvs::sync::CpuWaiter::operator*()
PUBLIC 49d0 0 linvs::sync::CpuWaiter::Wait(linvs::sync::SyncFence&, int)
PUBLIC 4a40 0 linvs::sync::SyncFence::operator*() const
PUBLIC 4a50 0 linvs::sync::SyncFence::operator*()
PUBLIC 4a60 0 linvs::sync::SyncFence::Reset()
PUBLIC 4a70 0 linvs::sync::SyncFence::~SyncFence()
PUBLIC 4a90 0 linvs::sync::SyncFence::Clear()
PUBLIC 4aa0 0 linvs::sync::SyncFence::ExtractFence(unsigned long&, unsigned long&)
PUBLIC 4af0 0 linvs::sync::SyncFence::Dup(linvs::sync::SyncFence const&)
PUBLIC 4b50 0 linvs::sync::SyncFence::SyncFence(linvs::sync::SyncFence const&)
PUBLIC 4b60 0 linvs::sync::SyncFence::operator=(linvs::sync::SyncFence const&)
PUBLIC 4ba0 0 linvs::sync::SyncModule::SyncModule()
PUBLIC 4bf0 0 linvs::sync::SyncModule::~SyncModule()
PUBLIC 4c10 0 linvs::sync::SyncModule::operator*()
PUBLIC 4c20 0 linvs::sync::SyncModule::operator*() const
PUBLIC 4c30 0 linvs::sync::SyncModule::operator bool()
PUBLIC 4c40 0 linvs::sync::SyncModule::CreateAttrList(linvs::sync::SyncAttrList&)
PUBLIC 4cb0 0 linvs::sync::SyncModule::AllocCpuWaiter(linvs::sync::CpuWaiter&)
PUBLIC 4d20 0 linvs::sync::SyncObj::SyncObj(NvSciSyncObjRec* const&)
PUBLIC 4d30 0 linvs::sync::SyncObj::operator bool() const
PUBLIC 4d40 0 linvs::sync::SyncObj::operator*() const
PUBLIC 4d50 0 linvs::sync::SyncObj::operator*()
PUBLIC 4d60 0 linvs::sync::SyncObj::AllocFence(linvs::sync::SyncFence&)
PUBLIC 4dd0 0 linvs::sync::SyncObj::Reset()
PUBLIC 4e00 0 linvs::sync::SyncObj::~SyncObj()
PUBLIC 4e20 0 linvs::sync::SyncObj::Dup(linvs::sync::SyncObj const&)
PUBLIC 4e80 0 linvs::sync::SyncObj::SyncObj(linvs::sync::SyncObj const&)
PUBLIC 4ed0 0 linvs::sync::SyncObj::operator=(linvs::sync::SyncObj const&)
PUBLIC 4f30 0 __aarch64_ldadd4_acq_rel
PUBLIC 4f60 0 _fini
STACK CFI INIT 3a50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ac0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3acc x19: .cfa -16 + ^
STACK CFI 3b04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b20 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3be0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3be4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT 47c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47d0 20 .cfa: sp 0 + .ra: x30
STACK CFI 47dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 47e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 47f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4810 70 .cfa: sp 0 + .ra: x30
STACK CFI 4814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4824 x19: .cfa -16 + ^
STACK CFI 4868 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 486c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c40 74 .cfa: sp 0 + .ra: x30
STACK CFI 3c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c58 x19: .cfa -16 + ^
STACK CFI 3c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3cc0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ce0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ce4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3d10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d20 50 .cfa: sp 0 + .ra: x30
STACK CFI 3d24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3d70 50 .cfa: sp 0 + .ra: x30
STACK CFI 3d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3dc0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3de4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3e10 114 .cfa: sp 0 + .ra: x30
STACK CFI 3e14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e30 x23: .cfa -16 + ^
STACK CFI 3ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3ed8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3f30 88 .cfa: sp 0 + .ra: x30
STACK CFI 3f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3fc0 184 .cfa: sp 0 + .ra: x30
STACK CFI 3fc4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3fcc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3fd8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3fe0 x23: .cfa -32 + ^
STACK CFI 40bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 40c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4880 78 .cfa: sp 0 + .ra: x30
STACK CFI 4884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4894 x19: .cfa -16 + ^
STACK CFI 48c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 48dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4900 9c .cfa: sp 0 + .ra: x30
STACK CFI 4904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4910 x19: .cfa -16 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4954 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4980 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 498c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4998 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4150 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 415c x19: .cfa -16 + ^
STACK CFI 41a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4200 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 420c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 428c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 42b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 42d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42e0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 42e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 42ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4304 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 4408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 440c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 44b0 11c .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44c8 x21: .cfa -16 + ^
STACK CFI 4550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4554 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4594 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 45d0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 45d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4618 x21: x21 x22: x22
STACK CFI 4624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 46b0 10c .cfa: sp 0 + .ra: x30
STACK CFI 46b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 46d0 x21: .cfa -16 + ^
STACK CFI 4740 x21: x21
STACK CFI 4750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4754 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4758 x21: x21
STACK CFI 4764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4768 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 49a0 20 .cfa: sp 0 + .ra: x30
STACK CFI 49ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 49d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a70 14 .cfa: sp 0 + .ra: x30
STACK CFI 4a74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4aa0 50 .cfa: sp 0 + .ra: x30
STACK CFI 4aa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4acc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4af0 60 .cfa: sp 0 + .ra: x30
STACK CFI 4af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4afc x19: .cfa -16 + ^
STACK CFI 4b20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4b60 3c .cfa: sp 0 + .ra: x30
STACK CFI 4b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ba0 44 .cfa: sp 0 + .ra: x30
STACK CFI 4ba4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4bc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4bf0 20 .cfa: sp 0 + .ra: x30
STACK CFI 4bfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4c10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4c40 68 .cfa: sp 0 + .ra: x30
STACK CFI 4c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c54 x19: .cfa -16 + ^
STACK CFI 4c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4cb0 68 .cfa: sp 0 + .ra: x30
STACK CFI 4cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cc4 x19: .cfa -16 + ^
STACK CFI 4ce8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4cec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d20 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4d60 68 .cfa: sp 0 + .ra: x30
STACK CFI 4d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d74 x19: .cfa -16 + ^
STACK CFI 4d98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4dd0 2c .cfa: sp 0 + .ra: x30
STACK CFI 4dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ddc x19: .cfa -16 + ^
STACK CFI 4df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e00 14 .cfa: sp 0 + .ra: x30
STACK CFI 4e04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e20 60 .cfa: sp 0 + .ra: x30
STACK CFI 4e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e2c x19: .cfa -16 + ^
STACK CFI 4e50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4e68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e80 44 .cfa: sp 0 + .ra: x30
STACK CFI 4e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ed0 58 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4f30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a10 24 .cfa: sp 0 + .ra: x30
STACK CFI 3a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x29: x29
