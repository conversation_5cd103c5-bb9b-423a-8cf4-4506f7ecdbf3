MODULE Linux arm64 F547858BBA28BA516E3F96E3543B8E550 libpangoxft-1.0.so.0
INFO CODE_ID 8B8547F528BA51BA6E3F96E3543B8E55584258FC
PUBLIC 3fe0 0 pango_xft_font_get_type
PUBLIC 4110 0 pango_xft_font_get_font
PUBLIC 4530 0 pango_xft_font_get_display
PUBLIC 45c0 0 pango_xft_font_get_unknown_glyph
PUBLIC 4650 0 pango_xft_font_lock_face
PUBLIC 46e0 0 pango_xft_font_unlock_face
PUBLIC 4760 0 pango_xft_font_get_glyph
PUBLIC 47f0 0 pango_xft_font_has_char
PUBLIC 4880 0 pango_xft_font_map_get_type
PUBLIC 48f0 0 pango_xft_get_font_map
PUBLIC 5470 0 pango_xft_shutdown_display
PUBLIC 55e0 0 pango_xft_set_default_substitute
PUBLIC 5624 0 pango_xft_substitute_changed
PUBLIC 5640 0 pango_xft_get_context
PUBLIC 5690 0 pango_xft_renderer_get_type
PUBLIC 5700 0 pango_xft_renderer_new
PUBLIC 5750 0 pango_xft_picture_render
PUBLIC 5914 0 pango_xft_renderer_set_draw
PUBLIC 59a0 0 pango_xft_renderer_set_default_color
PUBLIC 5b00 0 pango_xft_render_layout
PUBLIC 5c34 0 pango_xft_render_layout_line
PUBLIC 5d40 0 pango_xft_render_transformed
PUBLIC 5eb0 0 pango_xft_render
STACK CFI INIT 2e40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ebc x19: .cfa -16 + ^
STACK CFI 2ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f10 18 .cfa: sp 0 + .ra: x30
STACK CFI 2f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f30 1c .cfa: sp 0 + .ra: x30
STACK CFI 2f38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f50 28 .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f80 20 .cfa: sp 0 + .ra: x30
STACK CFI 2f88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fa0 30 .cfa: sp 0 + .ra: x30
STACK CFI 2fac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fd0 98 .cfa: sp 0 + .ra: x30
STACK CFI 2fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3020 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 305c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3070 bc .cfa: sp 0 + .ra: x30
STACK CFI 3084 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3130 98 .cfa: sp 0 + .ra: x30
STACK CFI 3138 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3140 x19: .cfa -16 + ^
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 31b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 31d0 80 .cfa: sp 0 + .ra: x30
STACK CFI 31d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31e0 x19: .cfa -16 + ^
STACK CFI 3238 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3240 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3250 44 .cfa: sp 0 + .ra: x30
STACK CFI 3264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 326c x19: .cfa -16 + ^
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3294 64 .cfa: sp 0 + .ra: x30
STACK CFI 329c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32a4 x19: .cfa -16 + ^
STACK CFI 32e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3300 58 .cfa: sp 0 + .ra: x30
STACK CFI 3308 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3310 x19: .cfa -16 + ^
STACK CFI 3338 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3360 20 .cfa: sp 0 + .ra: x30
STACK CFI 3368 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3374 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3380 134 .cfa: sp 0 + .ra: x30
STACK CFI 3388 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3390 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 33a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 33e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 33e4 x23: .cfa -16 + ^
STACK CFI 34ac x21: x21 x22: x22
STACK CFI 34b0 x23: x23
STACK CFI INIT 34b4 1c .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 34d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34f0 34 .cfa: sp 0 + .ra: x30
STACK CFI 34f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3500 x19: .cfa -16 + ^
STACK CFI 351c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3524 34 .cfa: sp 0 + .ra: x30
STACK CFI 352c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3534 x19: .cfa -16 + ^
STACK CFI 3550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3560 78 .cfa: sp 0 + .ra: x30
STACK CFI 3568 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3570 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3578 x21: .cfa -16 + ^
STACK CFI 35c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 35e0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 35e8 .cfa: sp 48 +
STACK CFI 35f4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36a0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 36c0 58 .cfa: sp 0 + .ra: x30
STACK CFI 36c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36d0 x19: .cfa -16 + ^
STACK CFI 36f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3720 10c .cfa: sp 0 + .ra: x30
STACK CFI 3728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3730 x19: .cfa -16 + ^
STACK CFI 3818 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3820 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3830 60 .cfa: sp 0 + .ra: x30
STACK CFI 3838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3840 x19: .cfa -16 + ^
STACK CFI 3880 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3890 104 .cfa: sp 0 + .ra: x30
STACK CFI 3898 .cfa: sp 96 +
STACK CFI 389c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38dc .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 38e0 x21: .cfa -16 + ^
STACK CFI 395c x21: x21
STACK CFI 3960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3968 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 398c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3994 78 .cfa: sp 0 + .ra: x30
STACK CFI 399c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39a4 x19: .cfa -16 + ^
STACK CFI 3a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a10 5c .cfa: sp 0 + .ra: x30
STACK CFI 3a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a34 x19: .cfa -16 + ^
STACK CFI 3a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a70 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3a78 .cfa: sp 128 +
STACK CFI 3a88 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a94 v10: .cfa -24 + ^
STACK CFI 3aa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ab8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -32 + ^
STACK CFI 3b58 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3b60 .cfa: sp 128 + .ra: .cfa -56 + ^ v10: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3b64 258 .cfa: sp 0 + .ra: x30
STACK CFI 3b6c .cfa: sp 240 +
STACK CFI 3b78 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3b80 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3b8c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3b98 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3ba8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3bb0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3c70 .cfa: sp 240 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3c84 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 3c90 v10: .cfa -16 + ^
STACK CFI 3da8 v8: v8 v9: v9
STACK CFI 3dac v10: v10
STACK CFI 3db4 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 3db8 v10: .cfa -16 + ^
STACK CFI INIT 3dc0 120 .cfa: sp 0 + .ra: x30
STACK CFI 3dc8 .cfa: sp 112 +
STACK CFI 3dd4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ddc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3de4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3df0 x23: .cfa -16 + ^
STACK CFI 3e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3e78 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ee0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3ee8 .cfa: sp 96 +
STACK CFI 3ef4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3efc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f7c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3fe0 70 .cfa: sp 0 + .ra: x30
STACK CFI 3fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ff0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 401c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4050 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4058 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4064 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40a8 x19: x19 x20: x20
STACK CFI 40ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 40d8 x19: x19 x20: x20
STACK CFI INIT 4110 28 .cfa: sp 0 + .ra: x30
STACK CFI 4118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4124 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 412c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4140 98 .cfa: sp 0 + .ra: x30
STACK CFI 4148 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4150 x19: .cfa -16 + ^
STACK CFI 4170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4178 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41e0 138 .cfa: sp 0 + .ra: x30
STACK CFI 41e8 .cfa: sp 144 +
STACK CFI 41f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 41fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4204 x21: .cfa -64 + ^
STACK CFI 420c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 4218 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 4224 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 42d8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 42e0 .cfa: sp 144 + .ra: .cfa -88 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4320 64 .cfa: sp 0 + .ra: x30
STACK CFI 4328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4330 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 435c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 437c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4384 2c .cfa: sp 0 + .ra: x30
STACK CFI 438c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4394 x19: .cfa -16 + ^
STACK CFI 43a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43b0 180 .cfa: sp 0 + .ra: x30
STACK CFI 43b8 .cfa: sp 80 +
STACK CFI 43c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43dc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4490 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4530 8c .cfa: sp 0 + .ra: x30
STACK CFI 4538 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4540 x19: .cfa -16 + ^
STACK CFI 4580 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 45b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45c0 8c .cfa: sp 0 + .ra: x30
STACK CFI 45c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4618 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4650 88 .cfa: sp 0 + .ra: x30
STACK CFI 4658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4660 x19: .cfa -16 + ^
STACK CFI 469c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 46e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46f0 x19: .cfa -16 + ^
STACK CFI 472c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4734 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4740 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4760 90 .cfa: sp 0 + .ra: x30
STACK CFI 4768 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4770 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 47e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 47f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 47f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4800 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 484c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4880 70 .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 48e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 48f0 178 .cfa: sp 0 + .ra: x30
STACK CFI 48f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4900 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4908 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4960 x21: x21 x22: x22
STACK CFI 496c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4974 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4978 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 49f0 x21: x21 x22: x22
STACK CFI 49fc x23: x23 x24: x24
STACK CFI 4a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a08 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4a40 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 4a70 278 .cfa: sp 0 + .ra: x30
STACK CFI 4a78 .cfa: sp 112 +
STACK CFI 4a84 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4ad8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4ae0 .cfa: sp 112 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4ae4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ae8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4b94 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4ca8 x19: x19 x20: x20
STACK CFI 4cac x23: x23 x24: x24
STACK CFI 4cb0 x25: x25 x26: x26
STACK CFI 4cb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4cc8 x19: x19 x20: x20
STACK CFI 4ccc x23: x23 x24: x24
STACK CFI 4cdc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ce0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ce4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 4cf0 374 .cfa: sp 0 + .ra: x30
STACK CFI 4cf8 .cfa: sp 112 +
STACK CFI 4d04 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d14 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d58 x25: .cfa -16 + ^
STACK CFI 4d94 x19: x19 x20: x20
STACK CFI 4d98 x25: x25
STACK CFI 4dc4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4dcc .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e58 x19: x19 x20: x20
STACK CFI 4e5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4f38 x19: x19 x20: x20
STACK CFI 4f3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4f90 x19: x19 x20: x20
STACK CFI 4f94 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 4fa8 x25: x25
STACK CFI 4fb4 x19: x19 x20: x20
STACK CFI 4fcc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4fec x19: x19 x20: x20
STACK CFI 4ff0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 4ff8 x25: x25
STACK CFI 4ffc x25: .cfa -16 + ^
STACK CFI 5054 x25: x25
STACK CFI 5058 x19: x19 x20: x20
STACK CFI 505c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5060 x25: .cfa -16 + ^
STACK CFI INIT 5064 278 .cfa: sp 0 + .ra: x30
STACK CFI 506c .cfa: sp 160 +
STACK CFI 5078 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5080 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5090 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 509c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 50a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 51a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 51b0 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 52e0 190 .cfa: sp 0 + .ra: x30
STACK CFI 52e8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 52f0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 52f8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5308 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5338 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5340 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 53d4 x19: x19 x20: x20
STACK CFI 53d8 x27: x27 x28: x28
STACK CFI 53e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 53f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5404 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 540c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5468 x19: x19 x20: x20
STACK CFI 546c x27: x27 x28: x28
STACK CFI INIT 5470 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5478 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5480 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5488 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 551c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5524 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5540 9c .cfa: sp 0 + .ra: x30
STACK CFI 5548 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5550 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5558 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 55d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 55e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 55e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55fc x21: .cfa -16 + ^
STACK CFI 561c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5624 1c .cfa: sp 0 + .ra: x30
STACK CFI 562c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5640 50 .cfa: sp 0 + .ra: x30
STACK CFI 5648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5658 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5660 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5684 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5690 70 .cfa: sp 0 + .ra: x30
STACK CFI 5698 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5700 48 .cfa: sp 0 + .ra: x30
STACK CFI 5708 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5710 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5750 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 5760 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5768 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5774 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5788 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 57c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 581c x21: x21 x22: x22
STACK CFI 5820 x23: x23 x24: x24
STACK CFI 5824 x25: x25 x26: x26
STACK CFI 5828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5830 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5854 x21: x21 x22: x22
STACK CFI 5858 x23: x23 x24: x24
STACK CFI 585c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5864 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5890 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 589c x21: x21 x22: x22
STACK CFI 58a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 58fc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 5914 84 .cfa: sp 0 + .ra: x30
STACK CFI 591c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5924 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5964 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 596c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59a0 94 .cfa: sp 0 + .ra: x30
STACK CFI 59a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5a34 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5a3c .cfa: sp 64 +
STACK CFI 5a40 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ae0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b00 134 .cfa: sp 0 + .ra: x30
STACK CFI 5b10 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b18 x23: .cfa -16 + ^
STACK CFI 5b24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ba0 x19: x19 x20: x20
STACK CFI 5ba4 x21: x21 x22: x22
STACK CFI 5bac .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 5bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5bb8 x19: x19 x20: x20
STACK CFI 5bc0 x21: x21 x22: x22
STACK CFI 5bd0 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 5c08 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5c14 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI INIT 5c34 108 .cfa: sp 0 + .ra: x30
STACK CFI 5c44 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5c4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5c64 x23: .cfa -16 + ^
STACK CFI 5cac x19: x19 x20: x20
STACK CFI 5cb4 x23: x23
STACK CFI 5cb8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5cc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5cc4 x19: x19 x20: x20
STACK CFI 5cd4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5d10 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5d1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 5d40 170 .cfa: sp 0 + .ra: x30
STACK CFI 5d50 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5d58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5d68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5d74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5d80 x25: .cfa -16 + ^
STACK CFI 5df8 x19: x19 x20: x20
STACK CFI 5e00 x23: x23 x24: x24
STACK CFI 5e04 x25: x25
STACK CFI 5e08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5e10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5e2c x19: x19 x20: x20
STACK CFI 5e38 x23: x23 x24: x24
STACK CFI 5e3c x25: x25
STACK CFI 5e40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5e48 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5e84 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5e90 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 5eb0 13c .cfa: sp 0 + .ra: x30
STACK CFI 5ec0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ec8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ed8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ee4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5f30 x23: x23 x24: x24
STACK CFI 5f40 x19: x19 x20: x20
STACK CFI 5f48 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5f50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5f6c x19: x19 x20: x20
STACK CFI 5f78 x23: x23 x24: x24
STACK CFI 5f7c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5f84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5fc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5fcc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
