MODULE Linux arm64 69197DE9042E565F3A71ACF515565C3A0 libcli-ldap-common-samba4.so.0
INFO CODE_ID E97D19692E045F563A71ACF515565C3A6C4204BA
PUBLIC 2470 0 new_ldap_message
PUBLIC 2494 0 ldap_encode
PUBLIC 34b0 0 asn1_read_OctetString_talloc
PUBLIC 3ff0 0 ldap_decode_attribs_bare
PUBLIC 4140 0 ldap_decode
PUBLIC 52b0 0 ldap_full_packet
PUBLIC 5310 0 ldap_encode_ndr_uint32
PUBLIC 5380 0 ldap_encode_ndr_dom_sid
PUBLIC 5430 0 ldap_encode_ndr_GUID
PUBLIC 54c0 0 ldap_decode_ndr_GUID
STACK CFI INIT 1db0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e20 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e2c x19: .cfa -16 + ^
STACK CFI 1e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e80 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 1e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1edc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1f14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1f50 x21: x21 x22: x22
STACK CFI 1fa0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1fd4 x21: x21 x22: x22
STACK CFI 20c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2194 x21: x21 x22: x22
STACK CFI 21d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 21e8 x21: x21 x22: x22
STACK CFI 21ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 2240 fc .cfa: sp 0 + .ra: x30
STACK CFI 2248 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2250 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2268 x21: .cfa -16 + ^
STACK CFI 22fc x21: x21
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2310 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2318 x21: x21
STACK CFI 231c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2324 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2328 x21: x21
STACK CFI 2334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2340 60 .cfa: sp 0 + .ra: x30
STACK CFI 2348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2358 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2364 x21: .cfa -16 + ^
STACK CFI 2398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 23a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 23a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 23b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 23c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 23c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2450 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 2470 24 .cfa: sp 0 + .ra: x30
STACK CFI 2478 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2494 1018 .cfa: sp 0 + .ra: x30
STACK CFI 249c .cfa: sp 144 +
STACK CFI 24a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 24b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 24c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2540 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 25b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25e4 x25: x25 x26: x26
STACK CFI 264c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2654 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 26e0 x25: x25 x26: x26
STACK CFI 26e4 x27: x27 x28: x28
STACK CFI 284c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 28c4 x25: x25 x26: x26
STACK CFI 2918 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 29bc x25: x25 x26: x26
STACK CFI 2a0c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2a58 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b0c x25: x25 x26: x26
STACK CFI 2b10 x27: x27 x28: x28
STACK CFI 2b60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ba8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c34 x25: x25 x26: x26
STACK CFI 2c38 x27: x27 x28: x28
STACK CFI 2c54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d40 x25: x25 x26: x26
STACK CFI 2e48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2e8c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2f30 x25: x25 x26: x26
STACK CFI 2f34 x27: x27 x28: x28
STACK CFI 2f98 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fd4 x25: x25 x26: x26
STACK CFI 2fd8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3040 x25: x25 x26: x26
STACK CFI 3044 x27: x27 x28: x28
STACK CFI 3078 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3084 x27: x27 x28: x28
STACK CFI 3088 x25: x25 x26: x26
STACK CFI 308c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3090 x25: x25 x26: x26
STACK CFI 3094 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3098 x25: x25 x26: x26
STACK CFI 309c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 30a0 x25: x25 x26: x26
STACK CFI 30a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 30a8 x25: x25 x26: x26
STACK CFI 30ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 30b0 x25: x25 x26: x26
STACK CFI 30b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 30fc x27: x27 x28: x28
STACK CFI 3118 x25: x25 x26: x26
STACK CFI 311c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3148 x27: x27 x28: x28
STACK CFI 3164 x25: x25 x26: x26
STACK CFI 3168 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 316c x25: x25 x26: x26
STACK CFI 3170 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 31ac x27: x27 x28: x28
STACK CFI 31c8 x25: x25 x26: x26
STACK CFI 31cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31d0 x25: x25 x26: x26
STACK CFI 31d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31d8 x25: x25 x26: x26
STACK CFI 31dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31e0 x25: x25 x26: x26
STACK CFI 31e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31e8 x25: x25 x26: x26
STACK CFI 31ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31f0 x25: x25 x26: x26
STACK CFI 31f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31f8 x25: x25 x26: x26
STACK CFI 31fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3244 x25: x25 x26: x26
STACK CFI 324c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32a4 x25: x25 x26: x26
STACK CFI 32a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32ac x25: x25 x26: x26
STACK CFI 32b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32b4 x25: x25 x26: x26
STACK CFI 32b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 32dc x25: x25 x26: x26
STACK CFI 32e0 x27: x27 x28: x28
STACK CFI 32e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32e8 x25: x25 x26: x26
STACK CFI 32ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32f0 x25: x25 x26: x26
STACK CFI 32f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32f8 x25: x25 x26: x26
STACK CFI 32fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3300 x25: x25 x26: x26
STACK CFI 3308 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 330c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3310 x27: x27 x28: x28
STACK CFI 3314 x25: x25 x26: x26
STACK CFI 3318 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 331c x25: x25 x26: x26
STACK CFI 3320 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3324 x25: x25 x26: x26
STACK CFI 3328 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 332c x25: x25 x26: x26
STACK CFI 3330 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 334c x25: x25 x26: x26
STACK CFI 3350 x27: x27 x28: x28
STACK CFI 3354 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3358 x25: x25 x26: x26
STACK CFI 335c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3360 x25: x25 x26: x26
STACK CFI 3364 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3368 x25: x25 x26: x26
STACK CFI 336c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3370 x25: x25 x26: x26
STACK CFI 3374 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3378 x25: x25 x26: x26
STACK CFI 337c x27: x27 x28: x28
STACK CFI 3380 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3384 x25: x25 x26: x26
STACK CFI 3388 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 338c x25: x25 x26: x26
STACK CFI 3390 x27: x27 x28: x28
STACK CFI 3394 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3398 x25: x25 x26: x26
STACK CFI 339c x27: x27 x28: x28
STACK CFI 33a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 33a4 x25: x25 x26: x26
STACK CFI 33a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 33ac x25: x25 x26: x26
STACK CFI 33b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33b4 x25: x25 x26: x26
STACK CFI 33b8 x27: x27 x28: x28
STACK CFI 33bc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33c0 x25: x25 x26: x26
STACK CFI 33c4 x27: x27 x28: x28
STACK CFI 33c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33cc x25: x25 x26: x26
STACK CFI 33d0 x27: x27 x28: x28
STACK CFI 33d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33d8 x25: x25 x26: x26
STACK CFI 33dc x27: x27 x28: x28
STACK CFI 33e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33e4 x25: x25 x26: x26
STACK CFI 33e8 x27: x27 x28: x28
STACK CFI 33ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33f0 x25: x25 x26: x26
STACK CFI 33f4 x27: x27 x28: x28
STACK CFI 33f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 33fc x25: x25 x26: x26
STACK CFI 3400 x27: x27 x28: x28
STACK CFI 3404 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3408 x25: x25 x26: x26
STACK CFI 340c x27: x27 x28: x28
STACK CFI 3410 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3414 x25: x25 x26: x26
STACK CFI 3418 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 341c x25: x25 x26: x26
STACK CFI 3420 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3424 x25: x25 x26: x26
STACK CFI 3428 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 342c x25: x25 x26: x26
STACK CFI 3430 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3434 x25: x25 x26: x26
STACK CFI 3438 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 343c x25: x25 x26: x26
STACK CFI 3440 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3444 x25: x25 x26: x26
STACK CFI 3448 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 344c x25: x25 x26: x26
STACK CFI 3450 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3454 x25: x25 x26: x26
STACK CFI 3458 x27: x27 x28: x28
STACK CFI 345c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3460 x25: x25 x26: x26
STACK CFI 3464 x27: x27 x28: x28
STACK CFI 3468 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 346c x25: x25 x26: x26
STACK CFI 3470 x27: x27 x28: x28
STACK CFI 3474 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3478 x25: x25 x26: x26
STACK CFI 347c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3480 x25: x25 x26: x26
STACK CFI 3484 x27: x27 x28: x28
STACK CFI 3488 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 348c x25: x25 x26: x26
STACK CFI 3490 x27: x27 x28: x28
STACK CFI 3494 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3498 x25: x25 x26: x26
STACK CFI 349c x27: x27 x28: x28
STACK CFI 34a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 34a4 x25: x25 x26: x26
STACK CFI 34a8 x27: x27 x28: x28
STACK CFI INIT 34b0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 34b8 .cfa: sp 80 +
STACK CFI 34c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 34cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34d8 x21: .cfa -16 + ^
STACK CFI 3554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 355c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3560 cc .cfa: sp 0 + .ra: x30
STACK CFI 3568 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3570 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3580 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 35ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3630 820 .cfa: sp 0 + .ra: x30
STACK CFI 3638 .cfa: sp 112 +
STACK CFI 3644 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 364c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3654 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 375c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 37ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3838 x23: x23 x24: x24
STACK CFI 39e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3ad4 x23: x23 x24: x24
STACK CFI 3d58 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3da8 x23: x23 x24: x24
STACK CFI 3e4c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 3e50 198 .cfa: sp 0 + .ra: x30
STACK CFI 3e58 .cfa: sp 96 +
STACK CFI 3e64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ed0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3f00 x23: .cfa -16 + ^
STACK CFI 3fa0 x23: x23
STACK CFI 3fa4 x23: .cfa -16 + ^
STACK CFI 3fd4 x23: x23
STACK CFI 3fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fe0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3fe4 x23: .cfa -16 + ^
STACK CFI INIT 3ff0 148 .cfa: sp 0 + .ra: x30
STACK CFI 3ff8 .cfa: sp 128 +
STACK CFI 4004 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 400c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4014 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4020 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 402c x25: .cfa -16 + ^
STACK CFI 412c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4134 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4140 116c .cfa: sp 0 + .ra: x30
STACK CFI 4148 .cfa: sp 240 +
STACK CFI 4154 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 415c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4168 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41c8 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4558 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 45b0 x23: x23 x24: x24
STACK CFI 4724 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4820 x23: x23 x24: x24
STACK CFI 49bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 49c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4a30 x23: x23 x24: x24
STACK CFI 4a34 x25: x25 x26: x26
STACK CFI 4b0c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b68 x23: x23 x24: x24
STACK CFI 4c00 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4c34 x23: x23 x24: x24
STACK CFI 4c94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4c9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4ca4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e24 x23: x23 x24: x24
STACK CFI 4e28 x25: x25 x26: x26
STACK CFI 4e2c x27: x27 x28: x28
STACK CFI 4e30 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e5c x23: x23 x24: x24
STACK CFI 4e60 x25: x25 x26: x26
STACK CFI 4e64 x27: x27 x28: x28
STACK CFI 4e68 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e88 x23: x23 x24: x24
STACK CFI 4e8c x25: x25 x26: x26
STACK CFI 4e90 x27: x27 x28: x28
STACK CFI 4e94 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e9c x23: x23 x24: x24
STACK CFI 4ea4 x25: x25 x26: x26
STACK CFI 4ea8 x27: x27 x28: x28
STACK CFI 4eac x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4efc x23: x23 x24: x24
STACK CFI 4f00 x25: x25 x26: x26
STACK CFI 4f04 x27: x27 x28: x28
STACK CFI 4f08 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f0c x23: x23 x24: x24
STACK CFI 4f10 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f14 x23: x23 x24: x24
STACK CFI 4f18 x25: x25 x26: x26
STACK CFI 4f1c x27: x27 x28: x28
STACK CFI 4f20 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f38 x23: x23 x24: x24
STACK CFI 4f3c x25: x25 x26: x26
STACK CFI 4f40 x27: x27 x28: x28
STACK CFI 4f48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f4c x23: x23 x24: x24
STACK CFI 4f50 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f54 x23: x23 x24: x24
STACK CFI 4f58 x25: x25 x26: x26
STACK CFI 4f5c x27: x27 x28: x28
STACK CFI 4f60 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f64 x23: x23 x24: x24
STACK CFI 4f68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f6c x23: x23 x24: x24
STACK CFI 4f7c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4fbc x23: x23 x24: x24
STACK CFI 4fc0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4fc4 x23: x23 x24: x24
STACK CFI 4fdc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5048 x23: x23 x24: x24
STACK CFI 5050 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 50ac x23: x23 x24: x24
STACK CFI 50dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 510c x23: x23 x24: x24
STACK CFI 5114 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5118 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 511c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5120 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5180 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5184 x23: x23 x24: x24
STACK CFI 5188 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 518c x23: x23 x24: x24
STACK CFI 5190 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5194 x23: x23 x24: x24
STACK CFI 5198 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51b0 x23: x23 x24: x24
STACK CFI 51b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51bc x23: x23 x24: x24
STACK CFI 51c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51c4 x23: x23 x24: x24
STACK CFI 51c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51cc x23: x23 x24: x24
STACK CFI 51d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51d4 x23: x23 x24: x24
STACK CFI 51d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51dc x23: x23 x24: x24
STACK CFI 51e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51e4 x23: x23 x24: x24
STACK CFI 51e8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51ec x23: x23 x24: x24
STACK CFI 51f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51f4 x23: x23 x24: x24
STACK CFI 51f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 51fc x23: x23 x24: x24
STACK CFI 5200 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5204 x23: x23 x24: x24
STACK CFI 5208 x25: x25 x26: x26
STACK CFI 520c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5210 x23: x23 x24: x24
STACK CFI 5214 x25: x25 x26: x26
STACK CFI 522c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5294 x23: x23 x24: x24
STACK CFI 5298 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 529c x23: x23 x24: x24
STACK CFI 52a0 x25: x25 x26: x26
STACK CFI 52a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 52a8 x23: x23 x24: x24
STACK CFI INIT 52b0 58 .cfa: sp 0 + .ra: x30
STACK CFI 52d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5300 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5310 68 .cfa: sp 0 + .ra: x30
STACK CFI 5318 .cfa: sp 32 +
STACK CFI 5328 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 536c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5374 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5380 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5388 .cfa: sp 80 +
STACK CFI 5398 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53b4 x21: .cfa -16 + ^
STACK CFI 541c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5424 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5430 90 .cfa: sp 0 + .ra: x30
STACK CFI 5438 .cfa: sp 64 +
STACK CFI 5444 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 544c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54b4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 54c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 54c8 .cfa: sp 64 +
STACK CFI 54dc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54ec x19: .cfa -16 + ^
STACK CFI 5558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5560 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
