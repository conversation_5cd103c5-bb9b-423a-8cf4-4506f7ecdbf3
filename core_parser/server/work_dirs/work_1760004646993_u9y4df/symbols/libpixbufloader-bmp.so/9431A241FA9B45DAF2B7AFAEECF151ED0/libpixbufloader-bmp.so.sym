MODULE Linux arm64 9431A241FA9B45DAF2B7AFAEECF151ED0 libpixbufloader-bmp.so
INFO CODE_ID 41A231949BFADA45F2B7AFAEECF151ED320FD0C1
PUBLIC 2a74 0 fill_vtable
PUBLIC 2ac0 0 fill_info
STACK CFI INIT d60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 48 .cfa: sp 0 + .ra: x30
STACK CFI dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ddc x19: .cfa -16 + ^
STACK CFI e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e30 d4 .cfa: sp 0 + .ra: x30
STACK CFI e38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI e80 x21: .cfa -16 + ^
STACK CFI ebc x21: x21
STACK CFI ec8 x21: .cfa -16 + ^
STACK CFI INIT f04 234 .cfa: sp 0 + .ra: x30
STACK CFI f10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1080 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 108c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1140 120 .cfa: sp 0 + .ra: x30
STACK CFI 1148 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1158 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 11e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1260 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1268 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1270 x21: .cfa -16 + ^
STACK CFI 1278 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12b4 x19: x19 x20: x20
STACK CFI 12c0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 12c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1300 x19: x19 x20: x20
STACK CFI 1330 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 1340 138c .cfa: sp 0 + .ra: x30
STACK CFI 1348 .cfa: sp 112 +
STACK CFI 134c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1354 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1364 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1390 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 14fc x23: x23 x24: x24
STACK CFI 1504 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1518 x23: x23 x24: x24
STACK CFI 1548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1550 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 1598 x23: x23 x24: x24
STACK CFI 159c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1724 x23: x23 x24: x24
STACK CFI 1728 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1740 x23: x23 x24: x24
STACK CFI 1748 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 174c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 176c x27: .cfa -16 + ^
STACK CFI 185c x27: x27
STACK CFI 1864 x25: x25 x26: x26
STACK CFI 19d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1c88 x27: x27
STACK CFI 1c8c x25: x25 x26: x26
STACK CFI 1ca4 x23: x23 x24: x24
STACK CFI 1ca8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1cfc x25: x25 x26: x26 x27: x27
STACK CFI 1d5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1d60 x27: .cfa -16 + ^
STACK CFI 1d88 x25: x25 x26: x26 x27: x27
STACK CFI 21f4 x23: x23 x24: x24
STACK CFI 21f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2520 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2538 x25: x25 x26: x26 x27: x27
STACK CFI 25e0 x23: x23 x24: x24
STACK CFI 25e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 25e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25ec x27: .cfa -16 + ^
STACK CFI 25f0 x25: x25 x26: x26 x27: x27
STACK CFI 262c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2630 x27: .cfa -16 + ^
STACK CFI 2634 x25: x25 x26: x26 x27: x27
STACK CFI 26c8 x23: x23 x24: x24
STACK CFI INIT 26d0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 26d8 .cfa: sp 176 +
STACK CFI 26e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 26ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 26f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2708 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 276c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 27e4 x27: x27 x28: x28
STACK CFI 2824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 282c .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2830 x27: x27 x28: x28
STACK CFI 2864 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2930 x27: x27 x28: x28
STACK CFI 2938 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2970 x27: x27 x28: x28
STACK CFI INIT 2974 40 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29b4 c0 .cfa: sp 0 + .ra: x30
STACK CFI 29c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2a74 4c .cfa: sp 0 + .ra: x30
STACK CFI 2a7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ac0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x29: x29
