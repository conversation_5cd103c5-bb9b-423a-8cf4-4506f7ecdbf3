MODULE Linux arm64 727A47177B069A1E0B89AFD6524C85FE0 libproxy.so.1
INFO CODE_ID 17477A72067B1E9A0B89AFD6524C85FE3A015961
PUBLIC bc4 0 px_proxy_factory_copy
PUBLIC c00 0 px_proxy_factory_new
PUBLIC c40 0 px_proxy_factory_get_type
PUBLIC ca4 0 px_proxy_factory_get_proxies
PUBLIC cc0 0 px_proxy_factory_free_proxies
PUBLIC cf0 0 px_proxy_factory_free
STACK CFI INIT ac0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT af0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b30 48 .cfa: sp 0 + .ra: x30
STACK CFI b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b3c x19: .cfa -16 + ^
STACK CFI b74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b90 34 .cfa: sp 0 + .ra: x30
STACK CFI b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bc4 34 .cfa: sp 0 + .ra: x30
STACK CFI bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bd4 x19: .cfa -16 + ^
STACK CFI bf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c00 3c .cfa: sp 0 + .ra: x30
STACK CFI c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c14 x19: .cfa -16 + ^
STACK CFI c34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c40 64 .cfa: sp 0 + .ra: x30
STACK CFI c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ca4 1c .cfa: sp 0 + .ra: x30
STACK CFI cac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc0 28 .cfa: sp 0 + .ra: x30
STACK CFI cc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ce0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cf0 38 .cfa: sp 0 + .ra: x30
STACK CFI cf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d00 x19: .cfa -16 + ^
STACK CFI d20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
