MODULE Linux arm64 AB86297B95EAC5C8F4F82931F05B96950 libplc4.so
INFO CODE_ID 7B2986ABEA95C8C5F4F82931F05B9695D555D162
PUBLIC 13f0 0 libVersionPoint
PUBLIC 1400 0 PL_strlen
PUBLIC 1474 0 PL_strnlen
PUBLIC 14b0 0 PL_strcpy
PUBLIC 14d0 0 PL_strncpy
PUBLIC 1514 0 PL_strncpyz
PUBLIC 1580 0 PL_strdup
PUBLIC 15f0 0 PL_strfree
PUBLIC 1600 0 PL_strndup
PUBLIC 1680 0 PL_strcasecmp
PUBLIC 16f4 0 PL_strncasecmp
PUBLIC 1760 0 PL_strcasestr
PUBLIC 1800 0 PL_strcaserstr
PUBLIC 18c0 0 PL_strncasestr
PUBLIC 1990 0 PL_strncaserstr
PUBLIC 1a70 0 PL_strcat
PUBLIC 1a90 0 PL_strncat
PUBLIC 1b04 0 PL_strcatn
PUBLIC 1b90 0 PL_strcmp
PUBLIC 1bb4 0 PL_strncmp
PUBLIC 1be0 0 PL_strchr
PUBLIC 1bf4 0 PL_strrchr
PUBLIC 1c10 0 PL_strnchr
PUBLIC 1c54 0 PL_strnrchr
PUBLIC 1cc0 0 PL_strpbrk
PUBLIC 1ce0 0 PL_strprbrk
PUBLIC 1d70 0 PL_strnpbrk
PUBLIC 1dd0 0 PL_strnprbrk
PUBLIC 1e70 0 PL_strstr
PUBLIC 1ea0 0 PL_strrstr
PUBLIC 1fa4 0 PL_strnstr
PUBLIC 20a0 0 PL_strnrstr
PUBLIC 21d0 0 PL_strtok_r
PUBLIC 2660 0 PL_Base64Encode
PUBLIC 2720 0 PL_Base64Decode
PUBLIC 2840 0 PL_FPrintError
PUBLIC 28f4 0 PL_PrintError
PUBLIC 2950 0 PL_CreateLongOptState
PUBLIC 2a40 0 PL_CreateOptState
PUBLIC 2a50 0 PL_DestroyOptState
PUBLIC 2a84 0 PL_GetNextOpt
STACK CFI INIT 1320 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1350 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1390 48 .cfa: sp 0 + .ra: x30
STACK CFI 1394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 139c x19: .cfa -16 + ^
STACK CFI 13d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 13e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1400 74 .cfa: sp 0 + .ra: x30
STACK CFI 1410 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1418 x19: .cfa -16 + ^
STACK CFI 1438 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1440 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1474 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 14d0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1514 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1580 6c .cfa: sp 0 + .ra: x30
STACK CFI 1588 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1594 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 15d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 15dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 15f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1600 78 .cfa: sp 0 + .ra: x30
STACK CFI 1608 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 161c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 162c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1680 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f4 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1760 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1778 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1780 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17a0 x21: .cfa -16 + ^
STACK CFI 17cc x21: x21
STACK CFI 17d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 17e4 x21: x21
STACK CFI 17f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1800 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1808 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1818 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1820 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1888 x21: x21 x22: x22
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 189c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 18a0 x21: x21 x22: x22
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 18c0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 18d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1954 x21: x21 x22: x22
STACK CFI 1960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1968 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 196c x21: x21 x22: x22
STACK CFI 1978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1990 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1998 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1a0c x21: x21 x22: x22
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1a48 x21: x21 x22: x22
STACK CFI 1a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a70 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a90 74 .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1aa4 x19: .cfa -16 + ^
STACK CFI 1ac0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1af0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b04 88 .cfa: sp 0 + .ra: x30
STACK CFI 1b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b18 x19: .cfa -16 + ^
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b90 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bb4 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bf4 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c10 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c54 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ce0 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d70 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dd0 9c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e70 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ea0 104 .cfa: sp 0 + .ra: x30
STACK CFI 1ea8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1eb8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ecc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ee0 x23: .cfa -16 + ^
STACK CFI 1f38 x21: x21 x22: x22
STACK CFI 1f44 x23: x23
STACK CFI 1f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1f60 x21: x21 x22: x22
STACK CFI 1f64 x23: x23
STACK CFI 1f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1f98 x21: x21 x22: x22
STACK CFI 1f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1fa4 fc .cfa: sp 0 + .ra: x30
STACK CFI 1fbc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1fc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1fd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fec x23: .cfa -16 + ^
STACK CFI 2044 x21: x21 x22: x22
STACK CFI 204c x23: x23
STACK CFI 2050 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2054 x21: x21 x22: x22
STACK CFI 205c x23: x23
STACK CFI 2064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2074 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2088 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2094 x21: x21 x22: x22
STACK CFI 2098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20a0 124 .cfa: sp 0 + .ra: x30
STACK CFI 20a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 20b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 20cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 20e4 x23: .cfa -16 + ^
STACK CFI 212c x21: x21 x22: x22
STACK CFI 2130 x23: x23
STACK CFI 2134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 213c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 216c x21: x21 x22: x22 x23: x23
STACK CFI 217c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2184 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2188 x21: x21 x22: x22
STACK CFI 2194 x23: x23
STACK CFI 2198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 21b0 x21: x21 x22: x22
STACK CFI 21b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 21d0 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2280 40 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22c0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2310 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2344 ac .cfa: sp 0 + .ra: x30
STACK CFI 2360 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 239c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23f0 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2454 68 .cfa: sp 0 + .ra: x30
STACK CFI 2468 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 24c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2520 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2530 54 .cfa: sp 0 + .ra: x30
STACK CFI 2538 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2574 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 257c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2584 dc .cfa: sp 0 + .ra: x30
STACK CFI 25a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2604 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2614 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2618 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2660 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2678 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 26b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2720 114 .cfa: sp 0 + .ra: x30
STACK CFI 2728 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2730 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2780 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 27bc x21: .cfa -32 + ^
STACK CFI 27fc x21: x21
STACK CFI 2808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2810 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 281c x21: x21
STACK CFI 2828 x21: .cfa -32 + ^
STACK CFI 282c x21: x21
STACK CFI INIT 2840 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2848 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2854 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2860 x23: .cfa -16 + ^
STACK CFI 28bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 28c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 28ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 28f4 54 .cfa: sp 0 + .ra: x30
STACK CFI 28fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2908 x19: .cfa -32 + ^
STACK CFI 291c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2924 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2950 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2958 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2960 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2968 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2974 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 29e4 x21: x21 x22: x22
STACK CFI 29e8 x23: x23 x24: x24
STACK CFI 29f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2a1c x21: x21 x22: x22
STACK CFI 2a20 x23: x23 x24: x24
STACK CFI INIT 2a40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a50 34 .cfa: sp 0 + .ra: x30
STACK CFI 2a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a60 x19: .cfa -16 + ^
STACK CFI 2a7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a84 32c .cfa: sp 0 + .ra: x30
STACK CFI 2a8c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2a98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b7c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2b9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2bc0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2bc4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2c40 x21: x21 x22: x22
STACK CFI 2c48 x23: x23 x24: x24
STACK CFI 2c50 x25: x25 x26: x26
STACK CFI 2c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2c74 x21: x21 x22: x22
STACK CFI 2c78 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2c88 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2c8c x21: x21 x22: x22
STACK CFI 2d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d44 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 2d58 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2d98 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2d9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2da8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2dac x25: .cfa -16 + ^ x26: .cfa -8 + ^
