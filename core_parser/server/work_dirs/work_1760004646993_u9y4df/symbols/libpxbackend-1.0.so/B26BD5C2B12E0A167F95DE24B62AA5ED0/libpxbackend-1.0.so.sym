MODULE Linux arm64 B26BD5C2B12E0A167F95DE24B62AA5ED0 libpxbackend-1.0.so
INFO CODE_ID C2D56BB22EB1160A7F95DE24B62AA5ED1F7924C7
PUBLIC 4f80 0 px_manager_get_type
PUBLIC 4ff0 0 px_manager_new_with_options
PUBLIC 50c0 0 px_manager_new
PUBLIC 50e0 0 px_manager_pac_download
PUBLIC 53e0 0 px_strv_builder_add_proxy
PUBLIC 55f0 0 px_manager_is_ignore
PUBLIC 6134 0 px_config_get_type
PUBLIC 62f4 0 px_manager_get_configuration
PUBLIC 66a0 0 px_pacrunner_get_type
PUBLIC 67e0 0 px_manager_get_proxies_sync
PUBLIC 71b0 0 px_config_env_get_type
PUBLIC 7220 0 px_config_gnome_get_type
PUBLIC 7290 0 px_config_kde_get_type
PUBLIC 7300 0 px_config_sysconfig_get_type
PUBLIC 7370 0 px_pacrunner_duktape_get_type
STACK CFI INIT 30f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3120 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3160 48 .cfa: sp 0 + .ra: x30
STACK CFI 3164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 316c x19: .cfa -16 + ^
STACK CFI 31a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 31b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 31c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 31e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 31e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3200 18 .cfa: sp 0 + .ra: x30
STACK CFI 3208 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3210 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3220 34 .cfa: sp 0 + .ra: x30
STACK CFI 3228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 323c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3254 38 .cfa: sp 0 + .ra: x30
STACK CFI 325c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3270 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3290 38 .cfa: sp 0 + .ra: x30
STACK CFI 3298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 32d0 38 .cfa: sp 0 + .ra: x30
STACK CFI 32d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3310 18 .cfa: sp 0 + .ra: x30
STACK CFI 3318 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3320 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3330 2c .cfa: sp 0 + .ra: x30
STACK CFI 3338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 334c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3360 70 .cfa: sp 0 + .ra: x30
STACK CFI 3368 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3370 x19: .cfa -16 + ^
STACK CFI 33a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 33a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 33c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33d0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 33d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 34d0 90 .cfa: sp 0 + .ra: x30
STACK CFI 34d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3560 9c .cfa: sp 0 + .ra: x30
STACK CFI 3578 .cfa: sp 64 +
STACK CFI 357c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3584 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3590 x21: .cfa -16 + ^
STACK CFI 35f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3600 9c .cfa: sp 0 + .ra: x30
STACK CFI 3618 .cfa: sp 64 +
STACK CFI 361c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3624 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3630 x21: .cfa -16 + ^
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 36a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 36b8 .cfa: sp 64 +
STACK CFI 36bc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 36c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36d0 x21: .cfa -16 + ^
STACK CFI 3734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3740 9c .cfa: sp 0 + .ra: x30
STACK CFI 3758 .cfa: sp 64 +
STACK CFI 375c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3770 x21: .cfa -16 + ^
STACK CFI 37d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 37e0 9c .cfa: sp 0 + .ra: x30
STACK CFI 37f8 .cfa: sp 64 +
STACK CFI 37fc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3804 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3810 x21: .cfa -16 + ^
STACK CFI 3874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3880 104 .cfa: sp 0 + .ra: x30
STACK CFI 3888 .cfa: sp 64 +
STACK CFI 388c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3894 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38d8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 38ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38f4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 390c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3914 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3918 x21: .cfa -16 + ^
STACK CFI 3978 x21: x21
STACK CFI 397c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3984 48 .cfa: sp 0 + .ra: x30
STACK CFI 398c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 39d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 39d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a50 3c .cfa: sp 0 + .ra: x30
STACK CFI 3a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a60 x19: .cfa -16 + ^
STACK CFI 3a84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a90 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3aa0 x19: .cfa -16 + ^
STACK CFI 3b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b50 58 .cfa: sp 0 + .ra: x30
STACK CFI 3b58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b60 x19: .cfa -16 + ^
STACK CFI 3b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3bb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bd0 x19: .cfa -16 + ^
STACK CFI 3bf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c00 6c .cfa: sp 0 + .ra: x30
STACK CFI 3c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c10 x19: .cfa -16 + ^
STACK CFI 3c50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c70 6c .cfa: sp 0 + .ra: x30
STACK CFI 3c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c80 x19: .cfa -16 + ^
STACK CFI 3cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ce0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cf0 x19: .cfa -16 + ^
STACK CFI 3d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d60 7c .cfa: sp 0 + .ra: x30
STACK CFI 3d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d70 x19: .cfa -16 + ^
STACK CFI 3dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3de0 108 .cfa: sp 0 + .ra: x30
STACK CFI 3de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3df4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e24 x21: .cfa -16 + ^
STACK CFI 3ea4 x21: x21
STACK CFI 3ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3eb0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3eb4 x21: x21
STACK CFI 3ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ef0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3ef8 .cfa: sp 64 +
STACK CFI 3efc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f14 x21: .cfa -16 + ^
STACK CFI 3f78 x21: x21
STACK CFI 3f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f84 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3fa0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3fa8 .cfa: sp 64 +
STACK CFI 3fac .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fc4 x21: .cfa -16 + ^
STACK CFI 4028 x21: x21
STACK CFI 402c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4034 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4050 438 .cfa: sp 0 + .ra: x30
STACK CFI 4058 .cfa: sp 128 +
STACK CFI 405c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4064 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 40ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40b0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 40f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 420c x21: x21 x22: x22
STACK CFI 4210 x23: x23 x24: x24
STACK CFI 4214 x25: x25 x26: x26
STACK CFI 4218 x27: x27 x28: x28
STACK CFI 421c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 42a4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 42dc x25: x25 x26: x26
STACK CFI 42e0 x27: x27 x28: x28
STACK CFI 431c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4324 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 43a0 x21: x21 x22: x22
STACK CFI 43a4 x23: x23 x24: x24
STACK CFI 43a8 x25: x25 x26: x26
STACK CFI 43ac x27: x27 x28: x28
STACK CFI 43b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 43c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4474 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4478 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 447c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4480 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4484 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4490 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4498 .cfa: sp 64 +
STACK CFI 449c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44b4 x21: .cfa -16 + ^
STACK CFI 4518 x21: x21
STACK CFI 451c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4524 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4550 1c .cfa: sp 0 + .ra: x30
STACK CFI 4558 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4570 5c .cfa: sp 0 + .ra: x30
STACK CFI 4578 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4594 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 45d0 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 45d8 .cfa: sp 128 +
STACK CFI 45dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 45e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4614 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4658 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 465c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 476c x21: x21 x22: x22
STACK CFI 4770 x23: x23 x24: x24
STACK CFI 47a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 47b0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 48d0 x21: x21 x22: x22
STACK CFI 48d4 x23: x23 x24: x24
STACK CFI 491c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4964 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4968 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 496c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 4970 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4978 .cfa: sp 64 +
STACK CFI 497c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4984 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4994 x21: .cfa -16 + ^
STACK CFI 49f8 x21: x21
STACK CFI 49fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4a04 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a30 5c .cfa: sp 0 + .ra: x30
STACK CFI 4a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a90 48 .cfa: sp 0 + .ra: x30
STACK CFI 4a98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4aa0 x19: .cfa -16 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ae0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4ae8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4af4 x19: .cfa -16 + ^
STACK CFI 4bac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4bc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4bd0 104 .cfa: sp 0 + .ra: x30
STACK CFI 4bd8 .cfa: sp 112 +
STACK CFI 4be4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4bec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c40 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4c70 x21: .cfa -16 + ^
STACK CFI 4cb0 x21: x21
STACK CFI 4cb4 x21: .cfa -16 + ^
STACK CFI 4cc8 x21: x21
STACK CFI 4cd0 x21: .cfa -16 + ^
STACK CFI INIT 4cd4 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4cdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ce8 .cfa: sp 1072 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d60 .cfa: sp 32 +
STACK CFI 4d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d70 .cfa: sp 1072 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d90 68 .cfa: sp 0 + .ra: x30
STACK CFI 4d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4da4 x19: .cfa -16 + ^
STACK CFI 4df0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4e00 98 .cfa: sp 0 + .ra: x30
STACK CFI 4e08 .cfa: sp 48 +
STACK CFI 4e14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e1c x19: .cfa -16 + ^
STACK CFI 4e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4e94 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ea0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4ea8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4eb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ebc x21: .cfa -16 + ^
STACK CFI 4f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4f80 70 .cfa: sp 0 + .ra: x30
STACK CFI 4f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ff0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4ff8 .cfa: sp 304 +
STACK CFI 5008 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 5014 x19: .cfa -208 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 50b4 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 50c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 50c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50e0 2fc .cfa: sp 0 + .ra: x30
STACK CFI 50e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50fc x21: .cfa -16 + ^
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 52a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 52a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 52fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5304 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 533c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5344 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 53a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 53ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 53e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 53e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53f8 x21: .cfa -16 + ^
STACK CFI 543c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5444 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5460 190 .cfa: sp 0 + .ra: x30
STACK CFI 546c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5474 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5484 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 54a0 x19: x19 x20: x20
STACK CFI 54a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 54b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5560 x19: x19 x20: x20
STACK CFI 5570 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5578 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 55f0 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 55f8 .cfa: sp 128 +
STACK CFI 5604 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 560c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5624 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5634 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5638 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5644 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5870 x21: x21 x22: x22
STACK CFI 5874 x23: x23 x24: x24
STACK CFI 5878 x25: x25 x26: x26
STACK CFI 587c x27: x27 x28: x28
STACK CFI 5884 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 58bc x21: x21 x22: x22
STACK CFI 58c4 x23: x23 x24: x24
STACK CFI 58c8 x25: x25 x26: x26
STACK CFI 58cc x27: x27 x28: x28
STACK CFI 58f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58fc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5a84 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5a88 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5a8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5a90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5a94 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5aa0 16c .cfa: sp 0 + .ra: x30
STACK CFI 5aa8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ab4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5abc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c10 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 5c2c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5c34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ca8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cc0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5cc4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5cd0 x25: .cfa -16 + ^
STACK CFI 5d90 x23: x23 x24: x24
STACK CFI 5d94 x25: x25
STACK CFI 5dfc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 5eb4 190 .cfa: sp 0 + .ra: x30
STACK CFI 5ebc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ec4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ef0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5ef4 x21: .cfa -16 + ^
STACK CFI 5f14 x21: x21
STACK CFI 5f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5f74 x21: x21
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6044 f0 .cfa: sp 0 + .ra: x30
STACK CFI 604c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6054 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6064 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 608c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 60e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 60e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6134 b0 .cfa: sp 0 + .ra: x30
STACK CFI 613c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6144 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6170 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6188 x21: .cfa -16 + ^
STACK CFI 61d0 x21: x21
STACK CFI 61dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 61e4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 61ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6204 x21: .cfa -16 + ^
STACK CFI 6274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 627c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6290 64 .cfa: sp 0 + .ra: x30
STACK CFI 6298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 62e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 62f4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 62fc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6304 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6310 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 63a0 bc .cfa: sp 0 + .ra: x30
STACK CFI 63a8 .cfa: sp 64 +
STACK CFI 63b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 63c4 x19: .cfa -16 + ^
STACK CFI 6450 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6458 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6460 bc .cfa: sp 0 + .ra: x30
STACK CFI 6468 .cfa: sp 64 +
STACK CFI 6478 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6484 x19: .cfa -16 + ^
STACK CFI 6510 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6518 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6520 bc .cfa: sp 0 + .ra: x30
STACK CFI 6528 .cfa: sp 64 +
STACK CFI 6538 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6544 x19: .cfa -16 + ^
STACK CFI 65d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 65d8 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 65e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 65e8 .cfa: sp 64 +
STACK CFI 65f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6604 x19: .cfa -16 + ^
STACK CFI 6690 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6698 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 66a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 66f4 x21: .cfa -16 + ^
STACK CFI 673c x21: x21
STACK CFI 6748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6750 8c .cfa: sp 0 + .ra: x30
STACK CFI 6758 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6760 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 676c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 67b4 x21: x21 x22: x22
STACK CFI 67c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 67d0 x21: x21 x22: x22
STACK CFI 67d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67e0 90c .cfa: sp 0 + .ra: x30
STACK CFI 67e8 .cfa: sp 176 +
STACK CFI 67f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6800 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6824 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 690c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6914 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 6920 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 692c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6930 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6b8c x23: x23 x24: x24
STACK CFI 6b90 x25: x25 x26: x26
STACK CFI 6b94 x27: x27 x28: x28
STACK CFI 6b98 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7060 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7098 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 70dc x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 70e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 70e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 70e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 70f0 bc .cfa: sp 0 + .ra: x30
STACK CFI 70f8 .cfa: sp 64 +
STACK CFI 7108 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7114 x19: .cfa -16 + ^
STACK CFI 71a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 71a8 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 71b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7220 70 .cfa: sp 0 + .ra: x30
STACK CFI 7228 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7230 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 725c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7290 70 .cfa: sp 0 + .ra: x30
STACK CFI 7298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 72c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 72cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 72f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7300 70 .cfa: sp 0 + .ra: x30
STACK CFI 7308 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7310 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 733c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7370 70 .cfa: sp 0 + .ra: x30
STACK CFI 7378 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7380 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 73a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 73d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 73e0 20c .cfa: sp 0 + .ra: x30
STACK CFI 73e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 73f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7404 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 758c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
