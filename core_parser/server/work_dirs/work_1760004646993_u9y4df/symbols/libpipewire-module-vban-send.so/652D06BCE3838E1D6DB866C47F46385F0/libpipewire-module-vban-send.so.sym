MODULE Linux arm64 652D06BCE3838E1D6DB866C47F46385F0 libpipewire-module-vban-send.so
INFO CODE_ID BC062D6583E31D8E6DB866C47F46385F1F1830F4
PUBLIC a410 0 pipewire__module_init
STACK CFI INIT 2970 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29ec x19: .cfa -16 + ^
STACK CFI 2a24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a40 1c .cfa: sp 0 + .ra: x30
STACK CFI 2a48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2a60 340 .cfa: sp 0 + .ra: x30
STACK CFI 2a68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ca4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2cfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2da0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2da8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2dc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2de8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2df0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2df8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e00 x19: .cfa -16 + ^
STACK CFI 2e38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e40 12c .cfa: sp 0 + .ra: x30
STACK CFI 2e48 .cfa: sp 96 +
STACK CFI 2e50 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f68 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f70 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2f78 .cfa: sp 80 +
STACK CFI 2f8c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ff4 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3060 90 .cfa: sp 0 + .ra: x30
STACK CFI 3070 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 307c x19: .cfa -16 + ^
STACK CFI 30a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 30ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30f0 54 .cfa: sp 0 + .ra: x30
STACK CFI 30f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3100 x19: .cfa -16 + ^
STACK CFI 313c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3144 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 314c .cfa: sp 112 +
STACK CFI 3150 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3158 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3168 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 32f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 32fc .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3310 50 .cfa: sp 0 + .ra: x30
STACK CFI 3318 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3320 x19: .cfa -16 + ^
STACK CFI 3358 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3360 104 .cfa: sp 0 + .ra: x30
STACK CFI 3368 .cfa: sp 96 +
STACK CFI 336c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3374 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3380 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 33c4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 345c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3464 30c .cfa: sp 0 + .ra: x30
STACK CFI 346c .cfa: sp 96 +
STACK CFI 3478 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3480 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3524 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 356c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3574 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3770 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3778 .cfa: sp 64 +
STACK CFI 377c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3798 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3844 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3934 2b28 .cfa: sp 0 + .ra: x30
STACK CFI 393c .cfa: sp 480 +
STACK CFI 3950 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3968 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3970 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3988 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 40e8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6460 58c .cfa: sp 0 + .ra: x30
STACK CFI 6468 .cfa: sp 240 +
STACK CFI 6474 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 647c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 649c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 64a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 654c x21: x21 x22: x22
STACK CFI 6550 x23: x23 x24: x24
STACK CFI 655c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6564 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6568 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 65ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6730 x27: x27 x28: x28
STACK CFI 673c x25: x25 x26: x26
STACK CFI 6740 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6798 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 67b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 67b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 67fc x27: x27 x28: x28
STACK CFI 6814 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6868 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 688c x27: x27 x28: x28
STACK CFI 68bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 69a8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 69ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 69b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 69b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 69b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 69f0 4d8 .cfa: sp 0 + .ra: x30
STACK CFI 69f8 .cfa: sp 160 +
STACK CFI 69fc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 6a04 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 6a18 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6a20 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 6a24 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6a28 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6a7c v8: .cfa -48 + ^
STACK CFI 6bcc v8: v8
STACK CFI 6bf4 x21: x21 x22: x22
STACK CFI 6bf8 x23: x23 x24: x24
STACK CFI 6bfc x25: x25 x26: x26
STACK CFI 6c00 x27: x27 x28: x28
STACK CFI 6c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c14 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 6c24 v8: .cfa -48 + ^
STACK CFI 6c68 v8: v8
STACK CFI 6cf0 v8: .cfa -48 + ^
STACK CFI 6d3c v8: v8
STACK CFI 6d54 v8: .cfa -48 + ^
STACK CFI 6d6c v8: v8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6da8 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 6dc0 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 6dec v8: v8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e24 .cfa: sp 160 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6ed0 5c0 .cfa: sp 0 + .ra: x30
STACK CFI 6ed8 .cfa: sp 304 +
STACK CFI 6ee4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6eec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6f0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f18 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6f20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6f24 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 71cc x19: x19 x20: x20
STACK CFI 71d0 x21: x21 x22: x22
STACK CFI 71d4 x25: x25 x26: x26
STACK CFI 71d8 x27: x27 x28: x28
STACK CFI 7200 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7208 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 72a8 x19: x19 x20: x20
STACK CFI 72ac x21: x21 x22: x22
STACK CFI 72b0 x25: x25 x26: x26
STACK CFI 72b4 x27: x27 x28: x28
STACK CFI 72b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7398 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 73f0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7414 .cfa: sp 304 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 742c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 747c x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7480 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7484 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7488 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 748c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7490 6d0 .cfa: sp 0 + .ra: x30
STACK CFI 7498 .cfa: sp 288 +
STACK CFI 74a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 74ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 74d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 74d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 74dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 74e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 75b0 x21: x21 x22: x22
STACK CFI 75b4 x23: x23 x24: x24
STACK CFI 75b8 x25: x25 x26: x26
STACK CFI 75bc x27: x27 x28: x28
STACK CFI 75e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75ec .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 760c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7688 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 76a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7b4c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7b50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7b54 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7b58 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b5c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7b60 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 7b68 .cfa: sp 448 +
STACK CFI 7b78 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7b8c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7b94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7ba4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7d18 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8024 1614 .cfa: sp 0 + .ra: x30
STACK CFI 802c .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 803c .cfa: sp 1312 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8078 x23: .cfa -80 + ^
STACK CFI 807c x24: .cfa -72 + ^
STACK CFI 8084 x25: .cfa -64 + ^
STACK CFI 808c x26: .cfa -56 + ^
STACK CFI 8094 x27: .cfa -48 + ^
STACK CFI 809c x28: .cfa -40 + ^
STACK CFI 81a8 v8: .cfa -32 + ^
STACK CFI 81b0 v9: .cfa -24 + ^
STACK CFI 81b8 v10: .cfa -16 + ^
STACK CFI 82a8 v10: v10 v8: v8 v9: v9
STACK CFI 849c v8: .cfa -32 + ^
STACK CFI 84a4 v9: .cfa -24 + ^
STACK CFI 84ac v10: .cfa -16 + ^
STACK CFI 8850 x23: x23
STACK CFI 8854 x24: x24
STACK CFI 8858 x25: x25
STACK CFI 885c x26: x26
STACK CFI 8860 x27: x27
STACK CFI 8864 x28: x28
STACK CFI 8868 v8: v8
STACK CFI 886c v9: v9
STACK CFI 8870 v10: v10
STACK CFI 88a4 .cfa: sp 128 +
STACK CFI 88b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 88bc .cfa: sp 1312 + .ra: .cfa -120 + ^ v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8ae4 x23: x23
STACK CFI 8ae8 x24: x24
STACK CFI 8aec x25: x25
STACK CFI 8af0 x26: x26
STACK CFI 8af4 x27: x27
STACK CFI 8af8 x28: x28
STACK CFI 8afc v8: v8
STACK CFI 8b00 v9: v9
STACK CFI 8b04 v10: v10
STACK CFI 8b08 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8b10 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 8d30 v10: v10 v8: v8 v9: v9
STACK CFI 8d88 x23: x23
STACK CFI 8d90 x24: x24
STACK CFI 8d94 x25: x25
STACK CFI 8d98 x26: x26
STACK CFI 8d9c x27: x27
STACK CFI 8da0 x28: x28
STACK CFI 8da4 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8e18 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 8ee4 v10: v10 v8: v8 v9: v9
STACK CFI 8efc v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 8fa4 v10: v10 v8: v8 v9: v9
STACK CFI 9000 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9158 v10: v10 v8: v8 v9: v9
STACK CFI 9170 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9414 v10: v10 v8: v8 v9: v9
STACK CFI 9444 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9604 v10: v10 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9614 x23: .cfa -80 + ^
STACK CFI 9618 x24: .cfa -72 + ^
STACK CFI 961c x25: .cfa -64 + ^
STACK CFI 9620 x26: .cfa -56 + ^
STACK CFI 9624 x27: .cfa -48 + ^
STACK CFI 9628 x28: .cfa -40 + ^
STACK CFI 962c v8: .cfa -32 + ^
STACK CFI 9630 v9: .cfa -24 + ^
STACK CFI 9634 v10: .cfa -16 + ^
STACK CFI INIT 9640 178 .cfa: sp 0 + .ra: x30
STACK CFI 9648 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9658 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9664 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 966c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 967c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9688 .cfa: sp 624 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9744 .cfa: sp 96 +
STACK CFI 975c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9764 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 97c0 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 97c8 .cfa: sp 160 +
STACK CFI 97cc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 97d4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 97e4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 97f0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9844 x19: x19 x20: x20
STACK CFI 984c x23: x23 x24: x24
STACK CFI 9854 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9858 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 985c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9860 v8: .cfa -48 + ^
STACK CFI 99e8 x19: x19 x20: x20
STACK CFI 99ec x21: x21 x22: x22
STACK CFI 99f0 x23: x23 x24: x24
STACK CFI 99f4 x27: x27 x28: x28
STACK CFI 99f8 v8: v8
STACK CFI 9a08 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 9a10 .cfa: sp 160 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9bcc v8: v8 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 9be4 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9cb0 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9d28 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9d54 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9d58 x21: x21 x22: x22
STACK CFI 9d5c x27: x27 x28: x28
STACK CFI 9d60 v8: v8
STACK CFI INIT 9d64 6a8 .cfa: sp 0 + .ra: x30
STACK CFI 9d6c .cfa: sp 240 +
STACK CFI 9d7c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9d9c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9da4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9df8 x19: x19 x20: x20
STACK CFI 9e00 x27: x27 x28: x28
STACK CFI 9e08 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e0c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9e10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9e14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a0fc x19: x19 x20: x20
STACK CFI a100 x21: x21 x22: x22
STACK CFI a104 x23: x23 x24: x24
STACK CFI a108 x25: x25 x26: x26
STACK CFI a10c x27: x27 x28: x28
STACK CFI a134 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a13c .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a2fc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a348 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a360 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI a364 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a368 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a36c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a370 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a374 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a3a4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a3d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a3d4 x21: x21 x22: x22
STACK CFI a3d8 x23: x23 x24: x24
STACK CFI a3dc x25: x25 x26: x26
STACK CFI INIT a410 127c .cfa: sp 0 + .ra: x30
STACK CFI a418 .cfa: sp 208 +
STACK CFI a424 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a42c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a434 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a448 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a48c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a4c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ac40 x23: x23 x24: x24
STACK CFI ac48 x27: x27 x28: x28
STACK CFI ac68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI acfc x23: x23 x24: x24
STACK CFI ad30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI ad38 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI ad68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI adb0 x27: x27 x28: x28
STACK CFI adb8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI af3c x27: x27 x28: x28
STACK CFI af40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI af7c x27: x27 x28: x28
STACK CFI af80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b13c x27: x27 x28: x28
STACK CFI b140 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b318 x27: x27 x28: x28
STACK CFI b348 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b42c x27: x27 x28: x28
STACK CFI b430 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b670 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b684 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b688 x27: .cfa -16 + ^ x28: .cfa -8 + ^
