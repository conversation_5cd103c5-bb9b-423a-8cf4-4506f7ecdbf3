MODULE Linux arm64 92A8EA9E8F71481F61015F357D87F1E40 libpipewire-module-protocol-simple.so
INFO CODE_ID 9EEAA892718F1F4861015F357D87F1E4E9EAAF12
PUBLIC 7f70 0 pipewire__module_init
STACK CFI INIT 28a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2910 48 .cfa: sp 0 + .ra: x30
STACK CFI 2914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 291c x19: .cfa -16 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2960 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2970 340 .cfa: sp 0 + .ra: x30
STACK CFI 2978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2cb0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cc0 x19: .cfa -16 + ^
STACK CFI 2cf8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d00 50 .cfa: sp 0 + .ra: x30
STACK CFI 2d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d10 x19: .cfa -16 + ^
STACK CFI 2d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d50 340 .cfa: sp 0 + .ra: x30
STACK CFI 2d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d6c x19: .cfa -16 + ^
STACK CFI 2db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2e48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2e50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3090 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 3098 .cfa: sp 480 +
STACK CFI 30ac .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 30c4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 30cc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 30e4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 3890 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3898 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 5d20 184 .cfa: sp 0 + .ra: x30
STACK CFI 5d28 .cfa: sp 48 +
STACK CFI 5d34 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e0c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ea4 18 .cfa: sp 0 + .ra: x30
STACK CFI 5eac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5eb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ec0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5ec8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ed8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ee0 x21: .cfa -16 + ^
STACK CFI 5f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5fb4 184 .cfa: sp 0 + .ra: x30
STACK CFI 5fbc .cfa: sp 80 +
STACK CFI 5fc0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5fc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5fec .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 603c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6044 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6140 88 .cfa: sp 0 + .ra: x30
STACK CFI 6148 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6150 x19: .cfa -16 + ^
STACK CFI 6190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6198 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 61bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61d0 224 .cfa: sp 0 + .ra: x30
STACK CFI 61d8 .cfa: sp 112 +
STACK CFI 61dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 61e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 61f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6200 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6208 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6238 x27: .cfa -16 + ^
STACK CFI 6274 x27: x27
STACK CFI 6280 x19: x19 x20: x20
STACK CFI 6288 x25: x25 x26: x26
STACK CFI 6294 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 629c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6304 x27: x27
STACK CFI 6308 x27: .cfa -16 + ^
STACK CFI 6320 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27
STACK CFI 6350 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6358 .cfa: sp 112 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 6370 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 63b4 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27
STACK CFI INIT 63f4 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 63fc .cfa: sp 112 +
STACK CFI 6400 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6408 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6414 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6424 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 642c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6430 x27: .cfa -16 + ^
STACK CFI 64f8 x19: x19 x20: x20
STACK CFI 64fc x23: x23 x24: x24
STACK CFI 650c x27: x27
STACK CFI 6510 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6518 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 654c x19: x19 x20: x20 x23: x23 x24: x24 x27: x27
STACK CFI 657c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6584 .cfa: sp 112 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 659c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 6684 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27
STACK CFI INIT 66c4 120 .cfa: sp 0 + .ra: x30
STACK CFI 66cc .cfa: sp 64 +
STACK CFI 66d0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6700 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 670c x21: .cfa -16 + ^
STACK CFI 6738 x21: x21
STACK CFI 673c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6744 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 67d4 x21: x21
STACK CFI 67d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 67e4 e0 .cfa: sp 0 + .ra: x30
STACK CFI 67ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 687c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 68c4 41c .cfa: sp 0 + .ra: x30
STACK CFI 68cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68d8 .cfa: x29 64 +
STACK CFI 68dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68f4 x23: .cfa -16 + ^
STACK CFI 6abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ac4 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6ce0 8a0 .cfa: sp 0 + .ra: x30
STACK CFI 6ce8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6cfc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6d08 .cfa: sp 736 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6d4c x19: .cfa -80 + ^
STACK CFI 6d54 x20: .cfa -72 + ^
STACK CFI 6d5c x23: .cfa -48 + ^
STACK CFI 6d60 x24: .cfa -40 + ^
STACK CFI 6e40 x27: .cfa -16 + ^
STACK CFI 6e48 x28: .cfa -8 + ^
STACK CFI 71a8 x19: x19
STACK CFI 71ac x20: x20
STACK CFI 71b0 x23: x23
STACK CFI 71b4 x24: x24
STACK CFI 71b8 x27: x27
STACK CFI 71bc x28: x28
STACK CFI 71dc .cfa: sp 96 +
STACK CFI 71ec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 71f4 .cfa: sp 736 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7260 x27: x27 x28: x28
STACK CFI 7270 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72dc x27: x27 x28: x28
STACK CFI 7304 x19: x19
STACK CFI 7308 x20: x20
STACK CFI 730c x23: x23
STACK CFI 7310 x24: x24
STACK CFI 7318 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7330 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 734c x19: x19
STACK CFI 7354 x20: x20
STACK CFI 7358 x23: x23
STACK CFI 735c x24: x24
STACK CFI 7360 x27: x27
STACK CFI 7364 x28: x28
STACK CFI 7368 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 73c0 x19: x19
STACK CFI 73c4 x20: x20
STACK CFI 73c8 x23: x23
STACK CFI 73cc x24: x24
STACK CFI 73d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 744c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 74cc x27: x27 x28: x28
STACK CFI 74f8 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 7568 x19: .cfa -80 + ^
STACK CFI 756c x20: .cfa -72 + ^
STACK CFI 7570 x23: .cfa -48 + ^
STACK CFI 7574 x24: .cfa -40 + ^
STACK CFI 7578 x27: .cfa -16 + ^
STACK CFI 757c x28: .cfa -8 + ^
STACK CFI INIT 7580 578 .cfa: sp 0 + .ra: x30
STACK CFI 7588 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 759c .cfa: sp 1248 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7618 x25: .cfa -32 + ^
STACK CFI 761c x26: .cfa -24 + ^
STACK CFI 7718 x25: x25
STACK CFI 771c x26: x26
STACK CFI 773c .cfa: sp 96 +
STACK CFI 774c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7754 .cfa: sp 1248 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 7814 x25: .cfa -32 + ^
STACK CFI 7818 x26: .cfa -24 + ^
STACK CFI 7820 x25: x25 x26: x26
STACK CFI 7824 x25: .cfa -32 + ^
STACK CFI 782c x26: .cfa -24 + ^
STACK CFI 7834 x27: .cfa -16 + ^
STACK CFI 7914 x25: x25
STACK CFI 7918 x26: x26
STACK CFI 791c x27: x27
STACK CFI 7920 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7a60 x25: x25
STACK CFI 7a64 x26: x26
STACK CFI 7a68 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ad8 x27: .cfa -16 + ^
STACK CFI 7adc x25: x25
STACK CFI 7ae0 x26: x26
STACK CFI 7ae4 x27: x27
STACK CFI 7aec x25: .cfa -32 + ^
STACK CFI 7af0 x26: .cfa -24 + ^
STACK CFI 7af4 x27: .cfa -16 + ^
STACK CFI INIT 7b00 470 .cfa: sp 0 + .ra: x30
STACK CFI 7b08 .cfa: sp 112 +
STACK CFI 7b18 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7b28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7b38 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7d6c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7f70 250 .cfa: sp 0 + .ra: x30
STACK CFI 7f78 .cfa: sp 112 +
STACK CFI 7f84 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7f8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7f94 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7ffc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8000 x25: .cfa -16 + ^
STACK CFI 80a8 x23: x23 x24: x24
STACK CFI 80b0 x25: x25
STACK CFI 80e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80e8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 819c x23: x23 x24: x24
STACK CFI 81a0 x25: x25
STACK CFI 81a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 81ac x25: .cfa -16 + ^
STACK CFI 81b0 x23: x23 x24: x24 x25: x25
