MODULE Linux arm64 CF0E9BD3F58FC47BAFB82877346A88050 libpsl.so.5
INFO CODE_ID D39B0ECF8FF57BC4AFB82877346A88058D9EA841
PUBLIC 1e30 0 psl_is_public_suffix
PUBLIC 1e70 0 psl_is_public_suffix2
PUBLIC 1ea4 0 psl_unregistrable_domain
PUBLIC 1f90 0 psl_registrable_domain
PUBLIC 2080 0 psl_free
PUBLIC 2124 0 psl_load_fp
PUBLIC 27a0 0 psl_load_file
PUBLIC 2804 0 psl_builtin
PUBLIC 2830 0 psl_suffix_count
PUBLIC 28a0 0 psl_suffix_exception_count
PUBLIC 2910 0 psl_suffix_wildcard_count
PUBLIC 2980 0 psl_builtin_file_time
PUBLIC 29a0 0 psl_builtin_sha1sum
PUBLIC 29c4 0 psl_builtin_filename
PUBLIC 29f0 0 psl_builtin_outdated
PUBLIC 2a80 0 psl_dist_filename
PUBLIC 2aa4 0 psl_get_version
PUBLIC 2ac4 0 psl_check_version_number
PUBLIC 2b34 0 psl_is_cookie_domain_acceptable
PUBLIC 2c80 0 psl_free_string
PUBLIC 2cb0 0 psl_str_to_utf8lower
PUBLIC 2f90 0 psl_latest
STACK CFI INIT 12d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1300 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1340 48 .cfa: sp 0 + .ra: x30
STACK CFI 1344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 134c x19: .cfa -16 + ^
STACK CFI 1384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1390 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13a0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 13a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 141c x19: x19 x20: x20
STACK CFI 1424 x21: x21 x22: x22
STACK CFI 142c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 1434 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1438 x19: x19 x20: x20
STACK CFI 143c x21: x21 x22: x22
STACK CFI 144c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 1454 ac .cfa: sp 0 + .ra: x30
STACK CFI 145c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1500 370 .cfa: sp 0 + .ra: x30
STACK CFI 1508 .cfa: sp 64 +
STACK CFI 1518 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16a8 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1870 c8 .cfa: sp 0 + .ra: x30
STACK CFI 187c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1884 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 188c x21: .cfa -16 + ^
STACK CFI 18f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 192c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1940 64 .cfa: sp 0 + .ra: x30
STACK CFI 1948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1978 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1998 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 199c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19a4 384 .cfa: sp 0 + .ra: x30
STACK CFI 19ac .cfa: sp 208 +
STACK CFI 19b0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b28 .cfa: sp 208 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d30 100 .cfa: sp 0 + .ra: x30
STACK CFI 1d38 .cfa: sp 192 +
STACK CFI 1d44 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d78 x21: x21 x22: x22
STACK CFI 1da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dac .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1e20 x21: x21 x22: x22
STACK CFI 1e2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 1e30 38 .cfa: sp 0 + .ra: x30
STACK CFI 1e38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1e58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e70 34 .cfa: sp 0 + .ra: x30
STACK CFI 1e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ea4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1eac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1eb8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1f90 ec .cfa: sp 0 + .ra: x30
STACK CFI 1f98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1fa8 x21: .cfa -16 + ^
STACK CFI 1fb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2044 x19: x19 x20: x20
STACK CFI 2050 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2058 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2068 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 2070 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2074 x19: x19 x20: x20
STACK CFI INIT 2080 a4 .cfa: sp 0 + .ra: x30
STACK CFI 20a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 211c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2124 674 .cfa: sp 0 + .ra: x30
STACK CFI 212c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2144 .cfa: sp 656 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2170 x23: .cfa -48 + ^
STACK CFI 2184 x24: .cfa -40 + ^
STACK CFI 21c4 x25: .cfa -32 + ^
STACK CFI 21c8 x26: .cfa -24 + ^
STACK CFI 21cc x27: .cfa -16 + ^
STACK CFI 21d0 x28: .cfa -8 + ^
STACK CFI 2388 x23: x23
STACK CFI 238c x24: x24
STACK CFI 2390 x25: x25
STACK CFI 2394 x26: x26
STACK CFI 2398 x27: x27
STACK CFI 239c x28: x28
STACK CFI 23bc .cfa: sp 96 +
STACK CFI 23cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 23d4 .cfa: sp 656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2598 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2608 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 26d8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 26e4 x23: x23
STACK CFI 26e8 x24: x24
STACK CFI 26f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2734 x23: x23
STACK CFI 2738 x24: x24
STACK CFI 2744 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2754 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2758 x23: .cfa -48 + ^
STACK CFI 275c x24: .cfa -40 + ^
STACK CFI 2760 x25: .cfa -32 + ^
STACK CFI 2764 x26: .cfa -24 + ^
STACK CFI 2768 x27: .cfa -16 + ^
STACK CFI 276c x28: .cfa -8 + ^
STACK CFI 2780 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 278c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 27a0 64 .cfa: sp 0 + .ra: x30
STACK CFI 27a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 27fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2804 24 .cfa: sp 0 + .ra: x30
STACK CFI 280c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2820 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2830 68 .cfa: sp 0 + .ra: x30
STACK CFI 2838 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2868 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2870 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2874 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2880 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2884 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2890 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 28a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2900 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2910 68 .cfa: sp 0 + .ra: x30
STACK CFI 2918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2948 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2950 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2960 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2970 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2980 20 .cfa: sp 0 + .ra: x30
STACK CFI 2988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2998 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 29a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 29bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29c4 24 .cfa: sp 0 + .ra: x30
STACK CFI 29cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 29e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 29f8 .cfa: sp 160 +
STACK CFI 2a08 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a74 .cfa: sp 160 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2a80 24 .cfa: sp 0 + .ra: x30
STACK CFI 2a88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2aa4 20 .cfa: sp 0 + .ra: x30
STACK CFI 2aac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2abc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ac4 70 .cfa: sp 0 + .ra: x30
STACK CFI 2acc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b34 148 .cfa: sp 0 + .ra: x30
STACK CFI 2b3c .cfa: sp 80 +
STACK CFI 2b48 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ba8 x21: x21 x22: x22
STACK CFI 2bb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2bc8 x21: x21 x22: x22
STACK CFI 2bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bfc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2c6c x21: x21 x22: x22
STACK CFI 2c78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 2c80 28 .cfa: sp 0 + .ra: x30
STACK CFI 2c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cb0 2dc .cfa: sp 0 + .ra: x30
STACK CFI 2cb8 .cfa: sp 112 +
STACK CFI 2cc4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2cd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ce0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d58 x19: x19 x20: x20
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2d90 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2d94 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e40 x19: x19 x20: x20
STACK CFI 2e44 x23: x23 x24: x24
STACK CFI 2e48 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e80 x19: x19 x20: x20
STACK CFI 2e84 x23: x23 x24: x24
STACK CFI 2e8c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2ef8 x19: x19 x20: x20
STACK CFI 2f00 x23: x23 x24: x24
STACK CFI 2f04 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f34 x19: x19 x20: x20
STACK CFI 2f38 x23: x23 x24: x24
STACK CFI 2f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f54 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 2f5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f60 x19: x19 x20: x20
STACK CFI 2f6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f70 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2f80 x19: x19 x20: x20
STACK CFI 2f88 x23: x23 x24: x24
STACK CFI INIT 2f90 178 .cfa: sp 0 + .ra: x30
STACK CFI 2f98 .cfa: sp 256 +
STACK CFI 2fa4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2fc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3014 x23: .cfa -16 + ^
STACK CFI 3088 x23: x23
STACK CFI 308c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3094 .cfa: sp 256 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3098 x23: x23
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30cc .cfa: sp 256 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3100 x23: .cfa -16 + ^
