MODULE Linux arm64 3DC6A665B02F6880ED4FA6831F1656450 libauth4-samba4.so.0
INFO CODE_ID 65A6C63D2FB08068ED4FA6831F1656457A7D1007
PUBLIC 4684 0 auth_context_set_challenge
PUBLIC 4700 0 auth_get_challenge
PUBLIC 5240 0 auth_check_password_send
PUBLIC 5670 0 auth_check_password_recv
PUBLIC 58a0 0 auth_check_password
PUBLIC 5c00 0 auth_methods_from_lp
PUBLIC 5c80 0 auth_backend_byname
PUBLIC 5d10 0 auth_register
PUBLIC 5e80 0 auth4_anonymous_init
PUBLIC 5f00 0 auth4_winbind_init
PUBLIC 5f90 0 auth4_sam_init
PUBLIC 6070 0 auth_interface_version
PUBLIC 6094 0 auth4_init
PUBLIC 6120 0 auth_context_create_methods
PUBLIC 63c0 0 auth_context_create
PUBLIC 6490 0 auth_context_create_for_netlogon
PUBLIC 6550 0 encrypt_user_info
PUBLIC 8340 0 authenticate_ldap_simple_bind_send
PUBLIC 85d4 0 authenticate_ldap_simple_bind_recv
STACK CFI INIT 3c00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c70 48 .cfa: sp 0 + .ra: x30
STACK CFI 3c74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c7c x19: .cfa -16 + ^
STACK CFI 3cb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3cc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3da4 48 .cfa: sp 0 + .ra: x30
STACK CFI 3dac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3dc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3dd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3df0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e40 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3e48 .cfa: sp 64 +
STACK CFI 3e58 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e74 x21: .cfa -16 + ^
STACK CFI 3f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f34 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f40 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3f48 .cfa: sp 80 +
STACK CFI 3f54 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f74 x23: .cfa -16 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 400c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4020 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4028 .cfa: sp 96 +
STACK CFI 4034 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 403c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4044 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4050 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 405c x25: .cfa -16 + ^
STACK CFI 40fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4104 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4120 198 .cfa: sp 0 + .ra: x30
STACK CFI 4128 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4130 x23: .cfa -16 + ^
STACK CFI 413c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4228 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4288 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 42a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 42c0 22c .cfa: sp 0 + .ra: x30
STACK CFI 42c8 .cfa: sp 80 +
STACK CFI 42d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 43f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 43f8 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 44f0 194 .cfa: sp 0 + .ra: x30
STACK CFI 44f8 .cfa: sp 64 +
STACK CFI 4504 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 450c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4514 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4604 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4684 74 .cfa: sp 0 + .ra: x30
STACK CFI 468c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4694 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 46e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 46f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4700 128 .cfa: sp 0 + .ra: x30
STACK CFI 4708 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4710 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4750 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4830 120 .cfa: sp 0 + .ra: x30
STACK CFI 4838 .cfa: sp 96 +
STACK CFI 4844 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 484c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4854 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4860 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 486c x25: .cfa -16 + ^
STACK CFI 4934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 493c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4950 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 4958 .cfa: sp 96 +
STACK CFI 4964 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 496c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4974 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4980 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 498c x25: .cfa -16 + ^
STACK CFI 4aa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4ab0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4b34 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4b3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b70 x23: .cfa -16 + ^
STACK CFI 4bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4bcc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c04 168 .cfa: sp 0 + .ra: x30
STACK CFI 4c0c .cfa: sp 96 +
STACK CFI 4c18 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4c40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4c54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4c64 x25: .cfa -16 + ^
STACK CFI 4cdc x21: x21 x22: x22
STACK CFI 4ce0 x23: x23 x24: x24
STACK CFI 4ce4 x25: x25
STACK CFI 4d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d18 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 4d24 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4d2c x21: x21 x22: x22
STACK CFI 4d34 x23: x23 x24: x24
STACK CFI 4d38 x25: x25
STACK CFI 4d3c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4d50 x21: x21 x22: x22
STACK CFI 4d54 x23: x23 x24: x24
STACK CFI 4d58 x25: x25
STACK CFI 4d60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d64 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d68 x25: .cfa -16 + ^
STACK CFI INIT 4d70 4d0 .cfa: sp 0 + .ra: x30
STACK CFI 4d78 .cfa: sp 80 +
STACK CFI 4d7c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4d98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4dc4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4e18 x23: x23 x24: x24
STACK CFI 4e20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4e38 x23: x23 x24: x24
STACK CFI 4e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e74 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4f28 x23: x23 x24: x24
STACK CFI 4f2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4f8c x23: x23 x24: x24
STACK CFI 4f90 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4fe0 x23: x23 x24: x24
STACK CFI 4fe4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5020 x23: x23 x24: x24
STACK CFI 5024 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 50cc x23: x23 x24: x24
STACK CFI 50d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 50f8 x23: x23 x24: x24
STACK CFI 50fc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 510c x23: x23 x24: x24
STACK CFI 5114 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 513c x23: x23 x24: x24
STACK CFI 5144 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5240 324 .cfa: sp 0 + .ra: x30
STACK CFI 5248 .cfa: sp 96 +
STACK CFI 5254 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 525c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5268 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5270 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 53a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 53b0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5564 104 .cfa: sp 0 + .ra: x30
STACK CFI 556c .cfa: sp 64 +
STACK CFI 557c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 558c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5598 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5650 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5670 230 .cfa: sp 0 + .ra: x30
STACK CFI 5678 .cfa: sp 96 +
STACK CFI 5684 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 568c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5694 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 56a0 x23: .cfa -16 + ^
STACK CFI 5784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 578c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 58a0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 58a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 58b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 58bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 58c8 x23: .cfa -16 + ^
STACK CFI 5914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 591c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5960 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5980 c4 .cfa: sp 0 + .ra: x30
STACK CFI 5988 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5990 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5998 x21: .cfa -16 + ^
STACK CFI 5a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5a44 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 5a4c .cfa: sp 144 +
STACK CFI 5a58 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a60 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5a74 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5bb8 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5c00 80 .cfa: sp 0 + .ra: x30
STACK CFI 5c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c10 x19: .cfa -16 + ^
STACK CFI 5c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c80 8c .cfa: sp 0 + .ra: x30
STACK CFI 5c88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c94 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5d10 170 .cfa: sp 0 + .ra: x30
STACK CFI 5d18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5d5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5de8 x21: x21 x22: x22
STACK CFI 5dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5df4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5e2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e3c x21: x21 x22: x22
STACK CFI 5e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e80 78 .cfa: sp 0 + .ra: x30
STACK CFI 5e88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e98 x19: .cfa -16 + ^
STACK CFI 5eb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5eb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f00 8c .cfa: sp 0 + .ra: x30
STACK CFI 5f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f34 x19: .cfa -16 + ^
STACK CFI 5f4c x19: x19
STACK CFI 5f50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f90 dc .cfa: sp 0 + .ra: x30
STACK CFI 5f98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5fa0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fb0 x21: .cfa -16 + ^
STACK CFI 5fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6070 24 .cfa: sp 0 + .ra: x30
STACK CFI 6078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 608c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6094 8c .cfa: sp 0 + .ra: x30
STACK CFI 609c .cfa: sp 64 +
STACK CFI 60b8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 611c .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6120 298 .cfa: sp 0 + .ra: x30
STACK CFI 6128 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6130 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 613c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6148 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6154 x25: .cfa -16 + ^
STACK CFI 628c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6294 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 62d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 62d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6308 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 63c0 cc .cfa: sp 0 + .ra: x30
STACK CFI 63c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 63d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 63e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 646c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6490 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6498 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 64a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 64a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 64c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6528 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6550 474 .cfa: sp 0 + .ra: x30
STACK CFI 6558 .cfa: sp 256 +
STACK CFI 6564 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 656c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6578 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 65cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 65d4 .cfa: sp 256 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 65e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 66e4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6758 x25: x25 x26: x26
STACK CFI 6764 x23: x23 x24: x24
STACK CFI 684c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6860 x23: x23 x24: x24
STACK CFI 6868 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 686c x23: x23 x24: x24
STACK CFI 6880 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6920 x23: x23 x24: x24
STACK CFI 6940 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 694c x25: x25 x26: x26
STACK CFI 6958 x23: x23 x24: x24
STACK CFI 6960 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6984 x23: x23 x24: x24
STACK CFI 6988 x25: x25 x26: x26
STACK CFI 698c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 69b0 x23: x23 x24: x24
STACK CFI 69b4 x25: x25 x26: x26
STACK CFI 69bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 69c0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 69c4 36c .cfa: sp 0 + .ra: x30
STACK CFI 69cc .cfa: sp 96 +
STACK CFI 69dc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 69e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 69f4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a00 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6bbc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6d30 318 .cfa: sp 0 + .ra: x30
STACK CFI 6d38 .cfa: sp 224 +
STACK CFI 6d3c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6d44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6d60 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6d6c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6db0 x27: .cfa -16 + ^
STACK CFI 6e48 x27: x27
STACK CFI 6ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6edc .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6ee0 x27: x27
STACK CFI 6f68 x27: .cfa -16 + ^
STACK CFI 6f7c x27: x27
STACK CFI 6fe0 x27: .cfa -16 + ^
STACK CFI 7040 x27: x27
STACK CFI 7044 x27: .cfa -16 + ^
STACK CFI INIT 7050 12e8 .cfa: sp 0 + .ra: x30
STACK CFI 7058 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7074 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7080 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7090 .cfa: sp 640 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 70dc x25: .cfa -32 + ^
STACK CFI 70e0 x26: .cfa -24 + ^
STACK CFI 713c x25: x25
STACK CFI 7140 x26: x26
STACK CFI 7160 .cfa: sp 96 +
STACK CFI 7178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7180 .cfa: sp 640 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7f1c x25: x25 x26: x26
STACK CFI 7f20 x25: .cfa -32 + ^
STACK CFI 7f24 x26: .cfa -24 + ^
STACK CFI INIT 8340 294 .cfa: sp 0 + .ra: x30
STACK CFI 8348 .cfa: sp 144 +
STACK CFI 8358 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8368 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8378 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8384 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 838c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 859c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 85d4 cc .cfa: sp 0 + .ra: x30
STACK CFI 85dc .cfa: sp 64 +
STACK CFI 85e8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 85f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 85fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 868c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
