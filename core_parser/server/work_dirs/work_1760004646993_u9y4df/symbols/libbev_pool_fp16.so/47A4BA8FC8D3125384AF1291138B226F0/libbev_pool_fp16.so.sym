MODULE Linux arm64 47A4BA8FC8D3125384AF1291138B226F0 libbev_pool_fp16.so
INFO CODE_ID 8FBAA447D3C8531284AF1291138B226F
PUBLIC 3d50 0 _init
PUBLIC 4030 0 _GLOBAL__sub_I_trt_bev_pool.cpp
PUBLIC 4088 0 call_weak_fn
PUBLIC 40a0 0 deregister_tm_clones
PUBLIC 40d0 0 register_tm_clones
PUBLIC 4110 0 __do_global_dtors_aux
PUBLIC 4160 0 frame_dummy
PUBLIC 4170 0 mmdeploy::TRTBEVPoolV2::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 4200 0 mmdeploy::TRTBEVPoolV2::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 4250 0 mmdeploy::TRTBEVPoolV2::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 4260 0 mmdeploy::TRTBEVPoolV2::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 4270 0 mmdeploy::TRTBEVPoolV2::getPluginType() const
PUBLIC 4280 0 mmdeploy::TRTBEVPoolV2::getPluginVersion() const
PUBLIC 4290 0 mmdeploy::TRTBEVPoolV2::getNbOutputs() const
PUBLIC 42a0 0 mmdeploy::TRTBEVPoolV2::getSerializationSize() const
PUBLIC 42b0 0 mmdeploy::TRTBEVPoolV2::serialize(void*) const
PUBLIC 42d0 0 mmdeploy::(anonymous namespace)::logError(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4320 0 std::basic_ostream<char, std::char_traits<char> >& std::endl<char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&) [clone .isra.0]
PUBLIC 43a0 0 mmdeploy::TRTBEVPoolV2::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 4500 0 mmdeploy::TRTBEVPoolV2::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 4930 0 mmdeploy::TRTBEVPoolV2::TRTBEVPoolV2(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, int)
PUBLIC 4a80 0 mmdeploy::TRTBEVPoolV2::clone() const
PUBLIC 4b10 0 mmdeploy::TRTBEVPoolV2Creator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 50c0 0 mmdeploy::TRTBEVPoolV2::TRTBEVPoolV2(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, void const*, unsigned long)
PUBLIC 5200 0 mmdeploy::TRTBEVPoolV2Creator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 53e0 0 mmdeploy::TRTBEVPoolV2Creator::TRTBEVPoolV2Creator()
PUBLIC 5570 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 5580 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 5590 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 55a0 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 55b0 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 55c0 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 55d0 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 55f0 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 5600 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 5610 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 5620 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 5630 0 std::ctype<char>::do_widen(char) const
PUBLIC 5640 0 mmdeploy::TRTPluginBase::getPluginVersion() const
PUBLIC 5650 0 mmdeploy::TRTPluginBase::initialize()
PUBLIC 5660 0 mmdeploy::TRTPluginBase::terminate()
PUBLIC 5670 0 mmdeploy::TRTPluginBase::getPluginNamespace() const
PUBLIC 5680 0 mmdeploy::TRTPluginBase::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 5690 0 mmdeploy::TRTPluginBase::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 56a0 0 mmdeploy::TRTPluginBase::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 56b0 0 mmdeploy::TRTPluginBase::detachFromContext()
PUBLIC 56c0 0 mmdeploy::TRTPluginCreatorBase::getPluginVersion() const
PUBLIC 56d0 0 mmdeploy::TRTPluginCreatorBase::getFieldNames()
PUBLIC 56e0 0 mmdeploy::TRTPluginCreatorBase::getPluginNamespace() const
PUBLIC 56f0 0 mmdeploy::TRTPluginBase::setPluginNamespace(char const*)
PUBLIC 5740 0 mmdeploy::TRTPluginCreatorBase::setPluginNamespace(char const*)
PUBLIC 5790 0 nvinfer1::PluginRegistrar<mmdeploy::TRTBEVPoolV2Creator>::~PluginRegistrar()
PUBLIC 5800 0 mmdeploy::TRTBEVPoolV2::~TRTBEVPoolV2()
PUBLIC 5880 0 mmdeploy::TRTBEVPoolV2Creator::~TRTBEVPoolV2Creator()
PUBLIC 58f0 0 mmdeploy::TRTBEVPoolV2Creator::~TRTBEVPoolV2Creator()
PUBLIC 5960 0 mmdeploy::TRTBEVPoolV2::~TRTBEVPoolV2()
PUBLIC 59d0 0 mmdeploy::TRTPluginBase::destroy()
PUBLIC 5a60 0 _fini
STACK CFI INIT 40a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4110 48 .cfa: sp 0 + .ra: x30
STACK CFI 4114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 411c x19: .cfa -16 + ^
STACK CFI 4154 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4160 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5590 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 55b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 55f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5620 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5640 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5650 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5660 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5690 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 56d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4170 8c .cfa: sp 0 + .ra: x30
STACK CFI 4174 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4180 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4190 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4200 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4280 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 42d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42dc x19: .cfa -16 + ^
STACK CFI 42fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4308 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4310 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4320 7c .cfa: sp 0 + .ra: x30
STACK CFI 4324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 432c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4364 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 56f0 44 .cfa: sp 0 + .ra: x30
STACK CFI 56f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5740 44 .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 574c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5790 6c .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57ac x19: .cfa -16 + ^
STACK CFI 57ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5800 74 .cfa: sp 0 + .ra: x30
STACK CFI 5804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 581c x19: .cfa -16 + ^
STACK CFI 5864 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5868 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5870 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5880 6c .cfa: sp 0 + .ra: x30
STACK CFI 5884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 589c x19: .cfa -16 + ^
STACK CFI 58dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 58e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 58e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 58f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 58f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 590c x19: .cfa -16 + ^
STACK CFI 5954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5960 70 .cfa: sp 0 + .ra: x30
STACK CFI 5964 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 597c x19: .cfa -16 + ^
STACK CFI 59cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43a0 154 .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 43c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4424 x19: x19 x20: x20
STACK CFI 443c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 59d0 90 .cfa: sp 0 + .ra: x30
STACK CFI 59ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a00 x19: .cfa -16 + ^
STACK CFI 5a54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4500 42c .cfa: sp 0 + .ra: x30
STACK CFI 4504 .cfa: sp 704 +
STACK CFI 4510 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 4518 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 452c x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 4544 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 4568 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI 4574 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 4740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4744 .cfa: sp 704 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^ x29: .cfa -640 + ^
STACK CFI INIT 4930 148 .cfa: sp 0 + .ra: x30
STACK CFI 4934 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4944 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4954 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4968 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4a38 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4a80 8c .cfa: sp 0 + .ra: x30
STACK CFI 4a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4afc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b10 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 4b14 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4b24 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4b3c x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 4b60 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 4ca4 x25: x25 x26: x26
STACK CFI 4db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4db8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 4f4c x25: x25 x26: x26
STACK CFI 4fbc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 4fc0 x25: x25 x26: x26
STACK CFI 4fdc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 4fec x25: x25 x26: x26
STACK CFI 4ff0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 50c0 140 .cfa: sp 0 + .ra: x30
STACK CFI 50c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 50d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 50e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 50f0 x23: .cfa -32 + ^
STACK CFI 51bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 51c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5200 1dc .cfa: sp 0 + .ra: x30
STACK CFI 5204 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5218 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5224 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5230 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5348 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 53e0 18c .cfa: sp 0 + .ra: x30
STACK CFI 53e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5408 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 54fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5500 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 5504 x21: .cfa -96 + ^
STACK CFI 5508 x21: x21
STACK CFI 5514 x21: .cfa -96 + ^
STACK CFI INIT 4030 58 .cfa: sp 0 + .ra: x30
STACK CFI 4034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 403c x19: .cfa -16 + ^
STACK CFI 407c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
