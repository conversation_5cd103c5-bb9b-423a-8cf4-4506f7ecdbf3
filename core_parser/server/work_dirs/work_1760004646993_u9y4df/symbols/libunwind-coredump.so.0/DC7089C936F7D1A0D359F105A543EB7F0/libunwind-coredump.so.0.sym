MODULE Linux arm64 DC7089C936F7D1A0D359F105A543EB7F0 libunwind-coredump.so.0
INFO CODE_ID C98970DCF736A0D1D359F105A543EB7FAC6A7AAB
PUBLIC fc0 0 _UCD_get_dyn_info_list_addr
PUBLIC fd4 0 _UCD_access_reg
PUBLIC 1010 0 _UCD_access_mem
PUBLIC 1190 0 _UCD_put_unwind_info
PUBLIC 12f4 0 _UCD_find_proc_info
PUBLIC 1510 0 _UCD_access_fpreg
PUBLIC 1550 0 _UCD_resume
PUBLIC 1780 0 _UCD_get_proc_name
PUBLIC 1b70 0 _UCD_get_num_threads
PUBLIC 1b84 0 _UCD_select_thread
PUBLIC 1bb4 0 _UCD_get_pid
PUBLIC 1bd0 0 _UCD_get_cursig
PUBLIC 1bf0 0 _UCD_add_backing_file_at_segment
PUBLIC 1cb0 0 _UCD_add_backing_file_at_vaddr
PUBLIC 1d00 0 _UCD_destroy
PUBLIC 1df4 0 _UCD_get_threadinfo
PUBLIC 1fd0 0 _UCD_get_mapinfo
PUBLIC 21f0 0 _UCD_create
STACK CFI INIT fc0 14 .cfa: sp 0 + .ra: x30
STACK CFI fc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fd4 38 .cfa: sp 0 + .ra: x30
STACK CFI fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1000 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1004 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1010 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1030 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1078 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1080 x21: .cfa -16 + ^
STACK CFI 10d8 x21: x21
STACK CFI 10dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10fc x21: x21
STACK CFI INIT 1100 88 .cfa: sp 0 + .ra: x30
STACK CFI 1104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 110c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 111c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 117c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1180 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1190 30 .cfa: sp 0 + .ra: x30
STACK CFI 119c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11a4 x19: .cfa -16 + ^
STACK CFI 11b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11c0 134 .cfa: sp 0 + .ra: x30
STACK CFI 11c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1228 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 12f4 21c .cfa: sp 0 + .ra: x30
STACK CFI 12f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1300 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1310 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1318 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1360 x25: .cfa -16 + ^
STACK CFI 1410 x25: x25
STACK CFI 1444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1448 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1458 x25: .cfa -16 + ^
STACK CFI 1480 x25: x25
STACK CFI 1484 x25: .cfa -16 + ^
STACK CFI 149c x25: x25
STACK CFI 14d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 14e4 x25: x25
STACK CFI 14fc x25: .cfa -16 + ^
STACK CFI 150c x25: x25
STACK CFI INIT 1510 3c .cfa: sp 0 + .ra: x30
STACK CFI 1514 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1548 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1550 40 .cfa: sp 0 + .ra: x30
STACK CFI 1554 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 158c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1590 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 1594 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 15ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 15b8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 15d8 x25: x25 x26: x26
STACK CFI 15dc x27: x27 x28: x28
STACK CFI 15e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 15f4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 15f8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15fc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 160c x19: x19 x20: x20
STACK CFI 1610 x21: x21 x22: x22
STACK CFI 1614 x23: x23 x24: x24
STACK CFI 1618 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1620 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1624 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1780 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 1784 .cfa: sp 256 +
STACK CFI 1788 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1794 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 179c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 17a4 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 17fc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 1808 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 18e0 x23: x23 x24: x24
STACK CFI 18e4 x27: x27 x28: x28
STACK CFI 1900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1904 .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 1a58 x23: x23 x24: x24
STACK CFI 1a60 x27: x27 x28: x28
STACK CFI 1a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1a6c .cfa: sp 256 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI 1b4c x23: x23 x24: x24
STACK CFI 1b54 x27: x27 x28: x28
STACK CFI INIT 1b70 14 .cfa: sp 0 + .ra: x30
STACK CFI 1b74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b84 30 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bb4 18 .cfa: sp 0 + .ra: x30
STACK CFI 1bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bd0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1be0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bf0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1bf4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1bfc x21: .cfa -144 + ^
STACK CFI 1c18 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1c64 x19: x19 x20: x20
STACK CFI 1c6c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1c70 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x29: .cfa -176 + ^
STACK CFI 1c74 x19: x19 x20: x20
STACK CFI 1c7c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 1ca0 x19: x19 x20: x20
STACK CFI INIT 1cb0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1cb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d00 f4 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1dac x21: x21 x22: x22
STACK CFI 1dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1df4 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 1df8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1e00 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1e14 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1e1c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1e24 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1e2c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1f74 x19: x19 x20: x20
STACK CFI 1f78 x21: x21 x22: x22
STACK CFI 1f7c x23: x23 x24: x24
STACK CFI 1f80 x25: x25 x26: x26
STACK CFI 1f8c .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1f90 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1fb4 x19: x19 x20: x20
STACK CFI 1fb8 x21: x21 x22: x22
STACK CFI 1fbc x23: x23 x24: x24
STACK CFI 1fc0 x25: x25 x26: x26
STACK CFI INIT 1fd0 21c .cfa: sp 0 + .ra: x30
STACK CFI 1fd4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1fe0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 1fec x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1ff8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2044 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2058 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 219c x25: x25 x26: x26
STACK CFI 21ac x23: x23 x24: x24
STACK CFI 21b8 x19: x19 x20: x20
STACK CFI 21bc x21: x21 x22: x22
STACK CFI 21c0 x27: x27 x28: x28
STACK CFI 21c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21cc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 21dc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21f0 28c .cfa: sp 0 + .ra: x30
STACK CFI 21f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2200 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 220c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 22bc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 22c8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2360 x23: x23 x24: x24
STACK CFI 2364 x25: x25 x26: x26
STACK CFI 2384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2388 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 23c0 x23: x23 x24: x24
STACK CFI 23c4 x25: x25 x26: x26
STACK CFI 23ec x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 23f8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
