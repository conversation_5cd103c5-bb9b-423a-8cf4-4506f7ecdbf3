MODULE Linux arm64 36F7363B1684845A02C39440191BA8930 libpangocairo-1.0.so.0
INFO CODE_ID 3B36F73684165A8402C39440191BA893D08235B0
PUBLIC 64a0 0 pango_cairo_update_context
PUBLIC 6550 0 pango_cairo_context_set_resolution
PUBLIC 6580 0 pango_cairo_context_get_resolution
PUBLIC 6630 0 pango_cairo_context_set_font_options
PUBLIC 6750 0 pango_cairo_context_get_font_options
PUBLIC 6820 0 pango_cairo_context_set_shape_renderer
PUBLIC 68e0 0 pango_cairo_context_get_shape_renderer
PUBLIC 6ae0 0 pango_cairo_update_layout
PUBLIC 6ba0 0 pango_cairo_font_get_type
PUBLIC 74e4 0 pango_cairo_font_get_scaled_font
PUBLIC 8e00 0 pango_cairo_font_map_get_type
PUBLIC 8f84 0 pango_cairo_font_map_set_default
PUBLIC 9014 0 pango_cairo_font_map_set_resolution
PUBLIC 90d0 0 pango_cairo_font_map_get_resolution
PUBLIC 9184 0 pango_cairo_font_map_create_context
PUBLIC 9210 0 pango_cairo_font_map_get_font_type
PUBLIC 92c0 0 pango_cairo_renderer_get_type
PUBLIC 93b0 0 pango_cairo_show_glyph_string
PUBLIC 9550 0 pango_cairo_show_glyph_item
PUBLIC 9710 0 pango_cairo_show_layout_line
PUBLIC 9850 0 pango_cairo_show_layout
PUBLIC 99b4 0 pango_cairo_show_error_underline
PUBLIC 9a70 0 pango_cairo_glyph_string_path
PUBLIC 9bb4 0 pango_cairo_layout_line_path
PUBLIC 9cf0 0 pango_cairo_layout_path
PUBLIC 9e60 0 pango_cairo_error_underline_path
PUBLIC 9ee0 0 pango_cairo_fc_font_get_type
PUBLIC 9f50 0 pango_cairo_fc_font_map_get_type
PUBLIC 9fc0 0 pango_cairo_font_map_new
PUBLIC a0e4 0 pango_cairo_font_map_get_default
PUBLIC a150 0 pango_cairo_create_context
PUBLIC a1c0 0 pango_cairo_create_layout
PUBLIC a230 0 pango_cairo_font_map_new_for_font_type
STACK CFI INIT 4f60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4f90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4fd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 4fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fdc x19: .cfa -16 + ^
STACK CFI 5014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5030 18 .cfa: sp 0 + .ra: x30
STACK CFI 5038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5050 18 .cfa: sp 0 + .ra: x30
STACK CFI 5058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5070 18 .cfa: sp 0 + .ra: x30
STACK CFI 5078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5080 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5090 34 .cfa: sp 0 + .ra: x30
STACK CFI 5098 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50c4 18 .cfa: sp 0 + .ra: x30
STACK CFI 50cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 50e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5100 28 .cfa: sp 0 + .ra: x30
STACK CFI 5108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5130 1c .cfa: sp 0 + .ra: x30
STACK CFI 5138 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5150 1c .cfa: sp 0 + .ra: x30
STACK CFI 5158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5170 38 .cfa: sp 0 + .ra: x30
STACK CFI 5178 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 518c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 51b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 51e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51f0 x19: .cfa -16 + ^
STACK CFI 5238 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5240 1c .cfa: sp 0 + .ra: x30
STACK CFI 5248 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5254 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5260 ec .cfa: sp 0 + .ra: x30
STACK CFI 5268 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5270 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5278 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 52b0 v8: .cfa -16 + ^
STACK CFI 5320 v8: v8
STACK CFI 5330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5338 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5350 20 .cfa: sp 0 + .ra: x30
STACK CFI 5358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5364 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5370 240 .cfa: sp 0 + .ra: x30
STACK CFI 5378 .cfa: sp 240 +
STACK CFI 538c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5398 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 53b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 54f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 54f8 .cfa: sp 240 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 55b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 55b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55e4 x19: .cfa -16 + ^
STACK CFI 561c x19: x19
STACK CFI 5620 x19: .cfa -16 + ^
STACK CFI 5624 x19: x19
STACK CFI INIT 5630 1c .cfa: sp 0 + .ra: x30
STACK CFI 5638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5650 34 .cfa: sp 0 + .ra: x30
STACK CFI 5658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5660 x19: .cfa -16 + ^
STACK CFI 567c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5684 88 .cfa: sp 0 + .ra: x30
STACK CFI 568c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5694 x19: .cfa -16 + ^
STACK CFI 56f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5710 80 .cfa: sp 0 + .ra: x30
STACK CFI 5718 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5720 x19: .cfa -16 + ^
STACK CFI 5778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5780 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5790 ac .cfa: sp 0 + .ra: x30
STACK CFI 5798 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57a0 x19: .cfa -16 + ^
STACK CFI 5824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 582c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5840 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 5848 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5854 v14: .cfa -32 + ^ v15: .cfa -24 + ^
STACK CFI 5860 v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI 5868 v10: .cfa -64 + ^ v11: .cfa -56 + ^
STACK CFI 5874 v12: .cfa -48 + ^ v13: .cfa -40 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 587c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5998 .cfa: sp 128 + .ra: .cfa -120 + ^ v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -48 + ^ v13: .cfa -40 + ^ v14: .cfa -32 + ^ v15: .cfa -24 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 59f0 58 .cfa: sp 0 + .ra: x30
STACK CFI 59f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a00 x19: .cfa -16 + ^
STACK CFI 5a28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a50 168 .cfa: sp 0 + .ra: x30
STACK CFI 5a58 .cfa: sp 96 +
STACK CFI 5a64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a74 x21: .cfa -16 + ^
STACK CFI 5b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b44 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5bc0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5bc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5bd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5bdc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5be8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5c90 150 .cfa: sp 0 + .ra: x30
STACK CFI 5c98 .cfa: sp 128 +
STACK CFI 5ca4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5cac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5cb4 x21: .cfa -64 + ^
STACK CFI 5cbc v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 5cc8 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 5cd4 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 5db8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5dc0 .cfa: sp 128 + .ra: .cfa -88 + ^ v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5de0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5de8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5df0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5dfc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5e08 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5e6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5eb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ec0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ed0 70 .cfa: sp 0 + .ra: x30
STACK CFI 5ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ee0 x19: .cfa -16 + ^
STACK CFI 5f38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f40 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5f48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f5c x21: .cfa -16 + ^
STACK CFI 5ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6000 44 .cfa: sp 0 + .ra: x30
STACK CFI 6008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 601c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 603c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6044 70 .cfa: sp 0 + .ra: x30
STACK CFI 604c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6054 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6060 x21: .cfa -16 + ^
STACK CFI 60ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 60b4 20 .cfa: sp 0 + .ra: x30
STACK CFI 60bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 60cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 60d4 3cc .cfa: sp 0 + .ra: x30
STACK CFI 60dc .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 60e4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 60f0 v8: .cfa -112 + ^ v9: .cfa -104 + ^
STACK CFI 60fc v10: .cfa -96 + ^ v11: .cfa -88 + ^
STACK CFI 6108 v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI 6114 v12: .cfa -80 + ^ v13: .cfa -72 + ^
STACK CFI 6368 v12: v12 v13: v13
STACK CFI 6370 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 6378 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 63bc .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 63c4 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 63f0 v12: v12 v13: v13
STACK CFI 63f8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 6400 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 6460 v12: .cfa -80 + ^ v13: .cfa -72 + ^
STACK CFI INIT 64a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 64b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 64fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6504 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6550 30 .cfa: sp 0 + .ra: x30
STACK CFI 6558 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6564 v8: .cfa -16 + ^
STACK CFI 6578 .cfa: sp 0 + .ra: .ra v8: v8 x29: x29
STACK CFI INIT 6580 5c .cfa: sp 0 + .ra: x30
STACK CFI 6588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6590 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 65e0 4c .cfa: sp 0 + .ra: x30
STACK CFI 65e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65f0 x19: .cfa -16 + ^
STACK CFI 6610 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6618 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6624 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6630 120 .cfa: sp 0 + .ra: x30
STACK CFI 6638 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6640 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6680 x21: .cfa -16 + ^
STACK CFI 66b0 x21: x21
STACK CFI 66bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6724 x21: x21
STACK CFI 6728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6730 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6750 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6758 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6760 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6820 bc .cfa: sp 0 + .ra: x30
STACK CFI 6828 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6830 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 683c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 68a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 68ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 68c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 68e0 ec .cfa: sp 0 + .ra: x30
STACK CFI 68e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 68f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6928 x21: .cfa -16 + ^
STACK CFI 6950 x21: x21
STACK CFI 695c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6994 x21: x21
STACK CFI 69c0 x21: .cfa -16 + ^
STACK CFI 69c4 x21: x21
STACK CFI INIT 69d0 110 .cfa: sp 0 + .ra: x30
STACK CFI 69d8 .cfa: sp 96 +
STACK CFI 69e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 69ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 69fc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6a3c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 6a90 v8: v8 v9: v9
STACK CFI 6ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ac8 .cfa: sp 96 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6ad8 v8: v8 v9: v9
STACK CFI 6adc v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 6ae0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6af8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6ba0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6ba8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6bb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6c60 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 6c68 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c70 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6c80 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6cdc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6d00 x23: x23 x24: x24
STACK CFI 6d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d38 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 6d40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6d78 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6d7c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6e78 x23: x23 x24: x24
STACK CFI 6e7c x25: x25 x26: x26
STACK CFI 6e80 x27: x27 x28: x28
STACK CFI 6e84 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 6f24 448 .cfa: sp 0 + .ra: x30
STACK CFI 6f2c .cfa: sp 144 +
STACK CFI 6f38 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6f40 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6f54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f58 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6f5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6fe0 x19: x19 x20: x20
STACK CFI 6fe4 x21: x21 x22: x22
STACK CFI 6fe8 x23: x23 x24: x24
STACK CFI 6ff0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 6ff8 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7168 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72c8 x27: x27 x28: x28
STACK CFI 72f0 x19: x19 x20: x20
STACK CFI 72f4 x21: x21 x22: x22
STACK CFI 72f8 x23: x23 x24: x24
STACK CFI 7300 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 7308 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7358 x27: x27 x28: x28
STACK CFI 735c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7360 x27: x27 x28: x28
STACK CFI 7364 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7370 58 .cfa: sp 0 + .ra: x30
STACK CFI 7394 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 73b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 73d0 50 .cfa: sp 0 + .ra: x30
STACK CFI 73f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7410 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7414 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7420 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7428 .cfa: sp 64 +
STACK CFI 7434 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 743c x19: .cfa -16 + ^
STACK CFI 74d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 74e0 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 74e4 88 .cfa: sp 0 + .ra: x30
STACK CFI 74f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 752c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7534 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7550 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 755c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7570 5c8 .cfa: sp 0 + .ra: x30
STACK CFI 7578 .cfa: sp 464 +
STACK CFI 758c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7594 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 75f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75fc .cfa: sp 464 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 7600 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7618 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7620 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7628 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 762c v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 7630 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 7634 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 77a4 v14: .cfa -16 + ^
STACK CFI 78bc x21: x21 x22: x22
STACK CFI 78c0 x23: x23 x24: x24
STACK CFI 78c4 x25: x25 x26: x26
STACK CFI 78c8 x27: x27 x28: x28
STACK CFI 78cc v8: v8 v9: v9
STACK CFI 78d0 v10: v10 v11: v11
STACK CFI 78d4 v12: v12 v13: v13
STACK CFI 78d8 v14: v14
STACK CFI 78dc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 78f4 x25: x25 x26: x26
STACK CFI 78fc v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7900 x21: x21 x22: x22
STACK CFI 7908 x23: x23 x24: x24
STACK CFI 790c x25: x25 x26: x26
STACK CFI 7910 x27: x27 x28: x28
STACK CFI 7914 v8: v8 v9: v9
STACK CFI 7918 v10: v10 v11: v11
STACK CFI 791c v12: v12 v13: v13
STACK CFI 7920 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7924 v14: .cfa -16 + ^
STACK CFI 79f4 v14: v14
STACK CFI 79fc v14: .cfa -16 + ^
STACK CFI 7aa0 v14: v14
STACK CFI 7aac v14: .cfa -16 + ^
STACK CFI 7ad4 v14: v14
STACK CFI 7ae0 v14: .cfa -16 + ^
STACK CFI 7aec v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7af0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 7af4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 7af8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 7afc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 7b00 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 7b04 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 7b08 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 7b0c v14: .cfa -16 + ^
STACK CFI 7b14 x21: x21 x22: x22
STACK CFI 7b1c x23: x23 x24: x24
STACK CFI 7b20 x25: x25 x26: x26
STACK CFI 7b24 x27: x27 x28: x28
STACK CFI 7b28 v8: v8 v9: v9
STACK CFI 7b2c v10: v10 v11: v11
STACK CFI 7b30 v12: v12 v13: v13
STACK CFI 7b34 v14: v14
STACK CFI INIT 7b40 520 .cfa: sp 0 + .ra: x30
STACK CFI 7b48 .cfa: sp 160 +
STACK CFI 7b4c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7b54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7b60 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7b74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7ba8 x25: .cfa -16 + ^
STACK CFI 7c74 x25: x25
STACK CFI 7ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7cb0 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7d98 x25: .cfa -16 + ^
STACK CFI 7dd4 x25: x25
STACK CFI 7f04 x25: .cfa -16 + ^
STACK CFI 7f10 x25: x25
STACK CFI 7f14 x25: .cfa -16 + ^
STACK CFI 7f18 x25: x25
STACK CFI 8028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8030 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8034 x25: .cfa -16 + ^
STACK CFI INIT 8060 710 .cfa: sp 0 + .ra: x30
STACK CFI 8068 .cfa: sp 224 +
STACK CFI 8074 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 807c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 8088 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 8098 v8: .cfa -64 + ^ v9: .cfa -56 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 81b8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 81c0 .cfa: sp 224 + .ra: .cfa -152 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 81cc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 81d4 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 81fc v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 821c v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 832c x27: x27 x28: x28
STACK CFI 8330 v10: v10 v11: v11
STACK CFI 8334 v12: v12 v13: v13
STACK CFI 8338 v14: v14 v15: v15
STACK CFI 833c v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 83a4 v10: v10 v11: v11
STACK CFI 83a8 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 8538 v10: v10 v11: v11
STACK CFI 8598 x27: x27 x28: x28
STACK CFI 859c v12: v12 v13: v13
STACK CFI 85a0 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 85dc v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 85e4 v14: v14 v15: v15
STACK CFI 85e8 v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 8714 v14: v14 v15: v15
STACK CFI 8720 v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 8728 v14: v14 v15: v15
STACK CFI 872c x27: x27 x28: x28
STACK CFI 8730 v10: v10 v11: v11
STACK CFI 8734 v12: v12 v13: v13
STACK CFI 8738 v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 875c v10: v10 v11: v11 v12: v12 v13: v13 x27: x27 x28: x28
STACK CFI 8760 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8764 v10: .cfa -48 + ^ v11: .cfa -40 + ^
STACK CFI 8768 v12: .cfa -32 + ^ v13: .cfa -24 + ^
STACK CFI 876c v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI INIT 8770 300 .cfa: sp 0 + .ra: x30
STACK CFI 8778 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 879c .cfa: sp 2224 + v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 88c8 .cfa: sp 128 +
STACK CFI 88e8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 88f0 .cfa: sp 2224 + .ra: .cfa -120 + ^ v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 8a70 48 .cfa: sp 0 + .ra: x30
STACK CFI 8a78 .cfa: sp 32 +
STACK CFI 8a8c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ac0 338 .cfa: sp 0 + .ra: x30
STACK CFI 8ac8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8ae4 .cfa: sp 2272 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8cf8 .cfa: sp 96 +
STACK CFI 8d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d18 .cfa: sp 2272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8e00 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8ec0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8ec8 .cfa: sp 64 +
STACK CFI 8ed4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8edc x19: .cfa -16 + ^
STACK CFI 8f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f80 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8f84 90 .cfa: sp 0 + .ra: x30
STACK CFI 8f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f94 x19: .cfa -16 + ^
STACK CFI 8fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9014 bc .cfa: sp 0 + .ra: x30
STACK CFI 901c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9024 v8: .cfa -8 + ^
STACK CFI 902c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 903c x21: .cfa -16 + ^
STACK CFI 9078 x21: x21
STACK CFI 908c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 909c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 90a0 x21: x21
STACK CFI 90b8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 90d0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 90d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 90f0 x21: .cfa -16 + ^
STACK CFI 912c x21: x21
STACK CFI 9138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9148 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 914c x21: x21
STACK CFI 9178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9184 88 .cfa: sp 0 + .ra: x30
STACK CFI 918c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9194 x19: .cfa -16 + ^
STACK CFI 91d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 91d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9204 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9210 b0 .cfa: sp 0 + .ra: x30
STACK CFI 9218 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9220 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9230 x21: .cfa -16 + ^
STACK CFI 926c x21: x21
STACK CFI 9278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9288 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 928c x21: x21
STACK CFI 92b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 92c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 92c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 92f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9330 7c .cfa: sp 0 + .ra: x30
STACK CFI 9338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9340 x19: .cfa -16 + ^
STACK CFI 9364 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 936c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 937c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9384 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 93a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 93b0 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 93c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 93c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 93d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 94c8 x19: x19 x20: x20
STACK CFI 94d0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 94d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 94f8 x19: x19 x20: x20
STACK CFI 94fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9504 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9510 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 9550 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 9560 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9568 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 957c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 966c x19: x19 x20: x20
STACK CFI 9674 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 967c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 969c x19: x19 x20: x20
STACK CFI 96a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 96a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 96cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 96f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9710 138 .cfa: sp 0 + .ra: x30
STACK CFI 9720 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9728 x21: .cfa -16 + ^
STACK CFI 9734 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 97c0 x19: x19 x20: x20
STACK CFI 97c8 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 97d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 97f0 x19: x19 x20: x20
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 97fc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9808 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 9850 164 .cfa: sp 0 + .ra: x30
STACK CFI 9860 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9868 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9870 x21: .cfa -16 + ^
STACK CFI 9930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9938 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 995c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 99b4 bc .cfa: sp 0 + .ra: x30
STACK CFI 9a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a1c x19: .cfa -48 + ^
STACK CFI 9a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9a70 144 .cfa: sp 0 + .ra: x30
STACK CFI 9a80 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9a88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9a98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9b2c x19: x19 x20: x20
STACK CFI 9b34 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9b3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9b5c x19: x19 x20: x20
STACK CFI 9b60 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9b68 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9b74 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 9bb4 13c .cfa: sp 0 + .ra: x30
STACK CFI 9bc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9bcc x21: .cfa -16 + ^
STACK CFI 9bd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9c68 x19: x19 x20: x20
STACK CFI 9c70 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 9c78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9c98 x19: x19 x20: x20
STACK CFI 9c9c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 9ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9cb0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 9cf0 168 .cfa: sp 0 + .ra: x30
STACK CFI 9d00 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d08 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d10 x21: .cfa -16 + ^
STACK CFI 9dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9e08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9e60 80 .cfa: sp 0 + .ra: x30
STACK CFI 9e68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9ea0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9eb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ebc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9ee0 70 .cfa: sp 0 + .ra: x30
STACK CFI 9ee8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ef0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9f50 70 .cfa: sp 0 + .ra: x30
STACK CFI 9f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9fc0 124 .cfa: sp 0 + .ra: x30
STACK CFI 9fc8 .cfa: sp 48 +
STACK CFI 9fd8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a048 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a0e0 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a0e4 64 .cfa: sp 0 + .ra: x30
STACK CFI a0ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a0f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a120 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a150 6c .cfa: sp 0 + .ra: x30
STACK CFI a158 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a160 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a18c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a194 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a1c0 68 .cfa: sp 0 + .ra: x30
STACK CFI a1c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a1d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a1f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a200 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a230 34 .cfa: sp 0 + .ra: x30
STACK CFI a24c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a264 3a4 .cfa: sp 0 + .ra: x30
STACK CFI a26c .cfa: sp 256 +
STACK CFI a278 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a280 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a290 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a2dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a2e4 x27: .cfa -16 + ^
STACK CFI a2e8 v8: .cfa -8 + ^
STACK CFI a4a0 v8: v8
STACK CFI a4a4 x21: x21 x22: x22
STACK CFI a4a8 x27: x27
STACK CFI a4dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a4e4 .cfa: sp 256 + .ra: .cfa -88 + ^ v8: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI a558 v8: v8 x21: x21 x22: x22 x27: x27
STACK CFI a580 v8: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI a5ac v8: v8 x21: x21 x22: x22 x27: x27
STACK CFI a5d4 v8: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI a5f8 v8: v8 x21: x21 x22: x22 x27: x27
STACK CFI a5fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a600 x27: .cfa -16 + ^
STACK CFI a604 v8: .cfa -8 + ^
