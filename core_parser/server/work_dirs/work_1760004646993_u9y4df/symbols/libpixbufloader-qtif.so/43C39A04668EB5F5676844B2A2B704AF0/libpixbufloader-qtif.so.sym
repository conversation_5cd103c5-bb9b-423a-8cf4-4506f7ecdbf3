MODULE Linux arm64 43C39A04668EB5F5676844B2A2B704AF0 libpixbufloader-qtif.so
INFO CODE_ID 049AC3438E66F5B5676844B2A2B704AF8A5AC787
PUBLIC 1b00 0 fill_vtable
PUBLIC 1b44 0 fill_info
STACK CFI INIT e40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT eb0 48 .cfa: sp 0 + .ra: x30
STACK CFI eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ebc x19: .cfa -16 + ^
STACK CFI ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f10 48 .cfa: sp 0 + .ra: x30
STACK CFI f18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f20 x19: .cfa -32 + ^
STACK CFI f50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f60 48 .cfa: sp 0 + .ra: x30
STACK CFI f68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f70 x19: .cfa -16 + ^
STACK CFI fa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fb0 68 .cfa: sp 0 + .ra: x30
STACK CFI fb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fcc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fd8 x23: .cfa -16 + ^
STACK CFI 1010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1020 360 .cfa: sp 0 + .ra: x30
STACK CFI 1028 .cfa: sp 96 +
STACK CFI 1034 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 103c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1044 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1060 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10d4 x21: x21 x22: x22
STACK CFI 1108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1110 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1144 x21: x21 x22: x22
STACK CFI 114c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 127c x21: x21 x22: x22
STACK CFI 1280 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12a0 x21: x21 x22: x22
STACK CFI 12d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 132c x21: x21 x22: x22
STACK CFI 1330 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 137c x21: x21 x22: x22
STACK CFI INIT 1380 128 .cfa: sp 0 + .ra: x30
STACK CFI 1388 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1398 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 14b0 160 .cfa: sp 0 + .ra: x30
STACK CFI 14b8 .cfa: sp 64 +
STACK CFI 14c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1544 x21: x21 x22: x22
STACK CFI 1570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1578 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1588 x21: x21 x22: x22
STACK CFI 158c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1600 x21: x21 x22: x22
STACK CFI 160c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 1610 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1618 .cfa: sp 64 +
STACK CFI 1624 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 162c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1638 x21: .cfa -16 + ^
STACK CFI 169c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16a4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16c0 43c .cfa: sp 0 + .ra: x30
STACK CFI 16c8 .cfa: sp 128 +
STACK CFI 16d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16ec x27: .cfa -16 + ^
STACK CFI 16f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1700 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 170c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1718 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1754 x19: x19 x20: x20
STACK CFI 1758 x21: x21 x22: x22
STACK CFI 175c x23: x23 x24: x24
STACK CFI 1760 x25: x25 x26: x26
STACK CFI 178c .cfa: sp 0 + .ra: .ra x27: x27 x29: x29
STACK CFI 1794 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1850 x19: x19 x20: x20
STACK CFI 1854 x21: x21 x22: x22
STACK CFI 1858 x23: x23 x24: x24
STACK CFI 185c x25: x25 x26: x26
STACK CFI 1860 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 18dc x19: x19 x20: x20
STACK CFI 18e4 x21: x21 x22: x22
STACK CFI 18e8 x23: x23 x24: x24
STACK CFI 18ec x25: x25 x26: x26
STACK CFI 18f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1ad4 x19: x19 x20: x20
STACK CFI 1ad8 x21: x21 x22: x22
STACK CFI 1adc x23: x23 x24: x24
STACK CFI 1ae0 x25: x25 x26: x26
STACK CFI 1aec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1af0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1af4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1af8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 1b00 44 .cfa: sp 0 + .ra: x30
STACK CFI 1b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b44 5c .cfa: sp 0 + .ra: x30
STACK CFI 1b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b60 .cfa: sp 0 + .ra: .ra x29: x29
