MODULE Linux arm64 F435DA60FB717F916374BCBF1030ED6F0 libavahi-client.so.3
INFO CODE_ID 60DA35F471FB917F6374BCBF1030ED6F357D9CD4
PUBLIC 3c50 0 avahi_client_set_errno
PUBLIC 3ca0 0 avahi_client_set_dbus_error
PUBLIC 3ef0 0 avahi_client_free
PUBLIC 4030 0 avahi_client_new
PUBLIC 4364 0 avahi_client_get_state
PUBLIC 43a4 0 avahi_client_errno
PUBLIC 43e4 0 avahi_client_simple_method_call
PUBLIC 45f0 0 avahi_client_is_connected
PUBLIC 5050 0 avahi_client_get_version_string
PUBLIC 50e4 0 avahi_client_get_domain_name
PUBLIC 5180 0 avahi_client_get_host_name
PUBLIC 5214 0 avahi_client_get_host_name_fqdn
PUBLIC 52b0 0 avahi_client_get_local_service_cookie
PUBLIC 5450 0 avahi_client_set_host_name
PUBLIC 5a74 0 avahi_entry_group_set_state
PUBLIC 5ae0 0 avahi_entry_group_free
PUBLIC 5bb4 0 avahi_entry_group_new
PUBLIC 5e30 0 avahi_entry_group_commit
PUBLIC 5ec0 0 avahi_entry_group_reset
PUBLIC 5f50 0 avahi_entry_group_get_state
PUBLIC 5fa0 0 avahi_entry_group_get_client
PUBLIC 5fe0 0 avahi_entry_group_is_empty
PUBLIC 61a4 0 avahi_entry_group_add_service_strlst
PUBLIC 6500 0 avahi_entry_group_add_service
PUBLIC 6620 0 avahi_entry_group_add_service_subtype
PUBLIC 6980 0 avahi_entry_group_update_service_txt_strlst
PUBLIC 6cb0 0 avahi_entry_group_update_service_txt
PUBLIC 6d94 0 avahi_entry_group_add_address
PUBLIC 7070 0 avahi_entry_group_add_record
PUBLIC 75e4 0 avahi_domain_browser_get_client
PUBLIC 7624 0 avahi_domain_browser_free
PUBLIC 77b0 0 avahi_domain_browser_new
PUBLIC 7c30 0 avahi_domain_browser_event
PUBLIC 7e50 0 avahi_service_type_browser_get_client
PUBLIC 7e90 0 avahi_service_type_browser_free
PUBLIC 7fb0 0 avahi_service_type_browser_new
PUBLIC 82f0 0 avahi_service_type_browser_event
PUBLIC 84f0 0 avahi_service_browser_get_client
PUBLIC 8530 0 avahi_service_browser_free
PUBLIC 8650 0 avahi_service_browser_new
PUBLIC 89e0 0 avahi_service_browser_event
PUBLIC 8ba0 0 avahi_record_browser_get_client
PUBLIC 8be0 0 avahi_record_browser_free
PUBLIC 8d00 0 avahi_record_browser_new
PUBLIC 9074 0 avahi_record_browser_event
PUBLIC 92e0 0 avahi_service_resolver_event
PUBLIC 9790 0 avahi_service_resolver_get_client
PUBLIC 97d0 0 avahi_service_resolver_free
PUBLIC 98f0 0 avahi_service_resolver_new
PUBLIC 9cc0 0 avahi_host_name_resolver_event
PUBLIC 9f54 0 avahi_host_name_resolver_free
PUBLIC a060 0 avahi_host_name_resolver_new
PUBLIC a394 0 avahi_host_name_resolver_get_client
PUBLIC a3d4 0 avahi_address_resolver_event
PUBLIC a670 0 avahi_address_resolver_get_client
PUBLIC a6b0 0 avahi_address_resolver_free
PUBLIC a7b4 0 avahi_address_resolver_new
PUBLIC ab30 0 avahi_xdg_config_open
PUBLIC ada0 0 avahi_nss_support
PUBLIC adf0 0 avahi_error_dbus_to_number
PUBLIC aee0 0 avahi_error_number_to_dbus
PUBLIC baa4 0 avahi_dbus_connection_glue
STACK CFI INIT 36f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3720 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3760 48 .cfa: sp 0 + .ra: x30
STACK CFI 3764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 376c x19: .cfa -16 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 37b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37c0 250 .cfa: sp 0 + .ra: x30
STACK CFI 37c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 37dc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 38b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 38b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3a10 dc .cfa: sp 0 + .ra: x30
STACK CFI 3a18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3af0 15c .cfa: sp 0 + .ra: x30
STACK CFI 3af8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3b08 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 3c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3c10 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3c50 48 .cfa: sp 0 + .ra: x30
STACK CFI 3c70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ca0 84 .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cb0 x19: .cfa -16 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3cdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d24 1cc .cfa: sp 0 + .ra: x30
STACK CFI 3d2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3d3c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3e28 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 3e5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3e64 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ef0 140 .cfa: sp 0 + .ra: x30
STACK CFI 3ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f00 x19: .cfa -16 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 400c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4030 334 .cfa: sp 0 + .ra: x30
STACK CFI 4038 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4040 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4048 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4054 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4060 x25: .cfa -48 + ^
STACK CFI 421c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4224 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT 4364 40 .cfa: sp 0 + .ra: x30
STACK CFI 437c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 43a4 40 .cfa: sp 0 + .ra: x30
STACK CFI 43bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 43e4 20c .cfa: sp 0 + .ra: x30
STACK CFI 43ec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 43f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4400 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4410 x23: .cfa -48 + ^
STACK CFI 44b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 44bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 4504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 450c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 45f0 80 .cfa: sp 0 + .ra: x30
STACK CFI 45f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4600 x19: .cfa -16 + ^
STACK CFI 4624 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 462c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 464c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4670 9e0 .cfa: sp 0 + .ra: x30
STACK CFI 4678 .cfa: sp 208 +
STACK CFI 467c .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4684 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 468c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4694 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 46f4 x19: x19 x20: x20
STACK CFI 46f8 x21: x21 x22: x22
STACK CFI 46fc x23: x23 x24: x24
STACK CFI 4700 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4708 .cfa: sp 208 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 47e8 x19: x19 x20: x20
STACK CFI 47ec x21: x21 x22: x22
STACK CFI 47f0 x23: x23 x24: x24
STACK CFI 47f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47fc .cfa: sp 208 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x29: .cfa -192 + ^
STACK CFI 49bc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 49dc x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4c18 x25: x25 x26: x26
STACK CFI 4c20 x27: x27 x28: x28
STACK CFI 4ca8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4cbc x25: x25 x26: x26
STACK CFI 4cc4 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4cd8 x25: x25 x26: x26
STACK CFI 4ce0 x27: x27 x28: x28
STACK CFI 4ce4 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4cf8 x25: x25 x26: x26
STACK CFI 4d00 x27: x27 x28: x28
STACK CFI 4d10 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4d24 x25: x25 x26: x26
STACK CFI 4d2c x27: x27 x28: x28
STACK CFI 4d54 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4d58 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4d5c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4d80 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4d84 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4d88 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4d8c x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4da4 x25: x25 x26: x26
STACK CFI 4dac x27: x27 x28: x28
STACK CFI 4db0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4dc4 x25: x25 x26: x26
STACK CFI 4dcc x27: x27 x28: x28
STACK CFI 4dd0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4de4 x25: x25 x26: x26
STACK CFI 4dec x27: x27 x28: x28
STACK CFI 4df0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4e04 x25: x25 x26: x26
STACK CFI 4e0c x27: x27 x28: x28
STACK CFI 4e10 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4e68 x25: x25 x26: x26
STACK CFI 4e6c x27: x27 x28: x28
STACK CFI 4e70 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4e84 x25: x25 x26: x26
STACK CFI 4e8c x27: x27 x28: x28
STACK CFI 4e90 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4ea4 x25: x25 x26: x26
STACK CFI 4eac x27: x27 x28: x28
STACK CFI 4eb0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4ec4 x25: x25 x26: x26
STACK CFI 4ecc x27: x27 x28: x28
STACK CFI 4ed0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4ee4 x25: x25 x26: x26
STACK CFI 4eec x27: x27 x28: x28
STACK CFI 4ef0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f04 x25: x25 x26: x26
STACK CFI 4f0c x27: x27 x28: x28
STACK CFI 4f10 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f24 x25: x25 x26: x26
STACK CFI 4f2c x27: x27 x28: x28
STACK CFI 4f30 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f44 x25: x25 x26: x26
STACK CFI 4f4c x27: x27 x28: x28
STACK CFI 4f50 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f64 x25: x25 x26: x26
STACK CFI 4f6c x27: x27 x28: x28
STACK CFI 4f70 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4f84 x25: x25 x26: x26
STACK CFI 4f8c x27: x27 x28: x28
STACK CFI 4f90 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4fa4 x25: x25 x26: x26
STACK CFI 4fac x27: x27 x28: x28
STACK CFI 4fb0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4fc4 x25: x25 x26: x26
STACK CFI 4fcc x27: x27 x28: x28
STACK CFI 4fd0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 4fe4 x25: x25 x26: x26
STACK CFI 4fec x27: x27 x28: x28
STACK CFI 4ff0 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5004 x25: x25 x26: x26
STACK CFI 500c x27: x27 x28: x28
STACK CFI 5010 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5024 x25: x25 x26: x26
STACK CFI 502c x27: x27 x28: x28
STACK CFI 5030 x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 5044 x25: x25 x26: x26
STACK CFI 504c x27: x27 x28: x28
STACK CFI INIT 5050 94 .cfa: sp 0 + .ra: x30
STACK CFI 5058 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5060 x19: .cfa -16 + ^
STACK CFI 5080 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5088 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 50a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 50ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 50e4 94 .cfa: sp 0 + .ra: x30
STACK CFI 50ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50f4 x19: .cfa -16 + ^
STACK CFI 5114 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 511c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5138 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5140 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5180 94 .cfa: sp 0 + .ra: x30
STACK CFI 5188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5190 x19: .cfa -16 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5214 94 .cfa: sp 0 + .ra: x30
STACK CFI 521c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5224 x19: .cfa -16 + ^
STACK CFI 5244 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 524c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5268 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5270 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 52b0 198 .cfa: sp 0 + .ra: x30
STACK CFI 52b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 52c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 52e0 x19: x19 x20: x20
STACK CFI 52e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 52f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5370 x21: x21 x22: x22
STACK CFI 5378 x19: x19 x20: x20
STACK CFI 537c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5384 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5398 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 53e0 x21: x21 x22: x22
STACK CFI 53e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 53fc x21: x21 x22: x22
STACK CFI 5404 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5420 x21: x21 x22: x22
STACK CFI 5444 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5450 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5458 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5460 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5478 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5544 x21: x21 x22: x22
STACK CFI 554c x19: x19 x20: x20
STACK CFI 5550 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5558 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 5568 x19: x19 x20: x20
STACK CFI 556c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5574 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 55c4 x21: x21 x22: x22
STACK CFI 55c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 55e4 x21: x21 x22: x22
STACK CFI 5608 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 5610 198 .cfa: sp 0 + .ra: x30
STACK CFI 5618 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5620 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5634 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 56d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 56dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 572c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5734 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 57b0 194 .cfa: sp 0 + .ra: x30
STACK CFI 57b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 57c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 57d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 57d8 x23: .cfa -48 + ^
STACK CFI 5874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 587c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 58c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 58cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5944 130 .cfa: sp 0 + .ra: x30
STACK CFI 594c .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5960 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5a38 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI INIT 5a74 6c .cfa: sp 0 + .ra: x30
STACK CFI 5ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5ae0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5ae8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5af0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5af8 x21: .cfa -16 + ^
STACK CFI 5b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5b60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5bb4 27c .cfa: sp 0 + .ra: x30
STACK CFI 5bbc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5bcc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 5cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5d04 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 5d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5d58 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5e30 90 .cfa: sp 0 + .ra: x30
STACK CFI 5e38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e40 x19: .cfa -16 + ^
STACK CFI 5e6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ec0 90 .cfa: sp 0 + .ra: x30
STACK CFI 5ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ed0 x19: .cfa -16 + ^
STACK CFI 5efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f50 4c .cfa: sp 0 + .ra: x30
STACK CFI 5f74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5fa0 40 .cfa: sp 0 + .ra: x30
STACK CFI 5fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5fe0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 5fe8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5ff0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 601c x19: x19 x20: x20
STACK CFI 6024 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 602c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 6030 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 603c x23: .cfa -64 + ^
STACK CFI 60c4 x19: x19 x20: x20
STACK CFI 60cc x21: x21 x22: x22
STACK CFI 60d0 x23: x23
STACK CFI 60d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 60dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI 6178 x21: x21 x22: x22 x23: x23
STACK CFI 619c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 61a0 x23: .cfa -64 + ^
STACK CFI INIT 61a4 358 .cfa: sp 0 + .ra: x30
STACK CFI 61ac .cfa: sp 240 +
STACK CFI 61b0 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 61b8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 61bc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 61e8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 61f4 x25: .cfa -96 + ^
STACK CFI 6320 x23: x23 x24: x24
STACK CFI 6324 x25: x25
STACK CFI 6328 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 6360 x23: x23 x24: x24
STACK CFI 6364 x25: x25
STACK CFI 6370 x19: x19 x20: x20
STACK CFI 6374 x21: x21 x22: x22
STACK CFI 6378 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6380 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6384 x23: x23 x24: x24
STACK CFI 638c x25: x25
STACK CFI 63a8 x19: x19 x20: x20
STACK CFI 63ac x21: x21 x22: x22
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63b8 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 63e0 x19: x19 x20: x20
STACK CFI 63e4 x21: x21 x22: x22
STACK CFI 63e8 x23: x23 x24: x24
STACK CFI 63ec x25: x25
STACK CFI 63f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63f8 .cfa: sp 240 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6470 x23: x23 x24: x24
STACK CFI 6474 x25: x25
STACK CFI 649c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 64a0 x25: .cfa -96 + ^
STACK CFI 64a4 x23: x23 x24: x24 x25: x25
STACK CFI 64c8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 64cc x25: .cfa -96 + ^
STACK CFI 64d0 x23: x23 x24: x24 x25: x25
STACK CFI 64f4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 64f8 x25: .cfa -96 + ^
STACK CFI INIT 6500 11c .cfa: sp 0 + .ra: x30
STACK CFI 6508 .cfa: sp 304 +
STACK CFI 650c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6524 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 65f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 65f8 .cfa: sp 304 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6620 360 .cfa: sp 0 + .ra: x30
STACK CFI 6628 .cfa: sp 224 +
STACK CFI 662c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6634 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6664 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6670 x25: .cfa -96 + ^
STACK CFI 6684 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6774 x21: x21 x22: x22
STACK CFI 6778 x23: x23 x24: x24
STACK CFI 677c x25: x25
STACK CFI 6788 x19: x19 x20: x20
STACK CFI 678c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6794 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6798 x23: x23 x24: x24
STACK CFI 67a0 x25: x25
STACK CFI 67bc x19: x19 x20: x20
STACK CFI 67c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67c8 .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 6810 x21: x21 x22: x22
STACK CFI 6814 x23: x23 x24: x24
STACK CFI 6818 x25: x25
STACK CFI 681c x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 6844 x19: x19 x20: x20
STACK CFI 6848 x21: x21 x22: x22
STACK CFI 684c x23: x23 x24: x24
STACK CFI 6850 x25: x25
STACK CFI 6854 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 685c .cfa: sp 224 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 68b4 x21: x21 x22: x22
STACK CFI 68b8 x23: x23 x24: x24
STACK CFI 68bc x25: x25
STACK CFI 68e4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 68e8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 68ec x25: .cfa -96 + ^
STACK CFI 68f0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 6914 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6918 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 691c x25: .cfa -96 + ^
STACK CFI 6920 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 6944 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6948 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 694c x25: .cfa -96 + ^
STACK CFI 6950 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 6974 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6978 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 697c x25: .cfa -96 + ^
STACK CFI INIT 6980 32c .cfa: sp 0 + .ra: x30
STACK CFI 6988 .cfa: sp 208 +
STACK CFI 698c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6994 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 69ac x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 69cc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 69e4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6adc x21: x21 x22: x22
STACK CFI 6ae0 x25: x25 x26: x26
STACK CFI 6ae4 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6b1c x21: x21 x22: x22
STACK CFI 6b20 x25: x25 x26: x26
STACK CFI 6b2c x19: x19 x20: x20
STACK CFI 6b30 x23: x23 x24: x24
STACK CFI 6b34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b3c .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6b40 x25: x25 x26: x26
STACK CFI 6b60 x19: x19 x20: x20
STACK CFI 6b64 x23: x23 x24: x24
STACK CFI 6b68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b70 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6b98 x19: x19 x20: x20
STACK CFI 6b9c x21: x21 x22: x22
STACK CFI 6ba0 x23: x23 x24: x24
STACK CFI 6ba4 x25: x25 x26: x26
STACK CFI 6ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bb0 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 6c14 x21: x21 x22: x22
STACK CFI 6c18 x25: x25 x26: x26
STACK CFI 6c1c x23: x23 x24: x24
STACK CFI 6c40 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6c44 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6c48 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6c4c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6c70 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6c74 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6c78 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 6c7c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6ca0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6ca4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 6ca8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI INIT 6cb0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6cb8 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 6ce0 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 6cf0 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 6cfc x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 6d04 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 6d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 6d94 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 6d9c .cfa: sp 256 +
STACK CFI 6da8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6db4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6dcc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6dd0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6df0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6dfc x27: .cfa -16 + ^
STACK CFI 6e0c x25: x25 x26: x26
STACK CFI 6e14 x27: x27
STACK CFI 6e4c x19: x19 x20: x20
STACK CFI 6e50 x21: x21 x22: x22
STACK CFI 6e58 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 6e60 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6f54 x25: x25 x26: x26
STACK CFI 6f58 x27: x27
STACK CFI 6f5c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6f94 x25: x25 x26: x26
STACK CFI 6f98 x27: x27
STACK CFI 6f9c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6ff4 x25: x25 x26: x26
STACK CFI 6ff8 x27: x27
STACK CFI 6ffc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 702c x25: x25 x26: x26
STACK CFI 7030 x27: x27
STACK CFI 7038 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 703c x27: .cfa -16 + ^
STACK CFI 7040 x25: x25 x26: x26 x27: x27
STACK CFI 7064 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7068 x27: .cfa -16 + ^
STACK CFI INIT 7070 310 .cfa: sp 0 + .ra: x30
STACK CFI 7078 .cfa: sp 368 +
STACK CFI 707c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 7084 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 7088 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 70b8 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 70c0 x25: .cfa -240 + ^
STACK CFI 70d0 x23: x23 x24: x24
STACK CFI 70d8 x25: x25
STACK CFI 70f4 x19: x19 x20: x20
STACK CFI 70f8 x21: x21 x22: x22
STACK CFI 70fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7104 .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 7258 x23: x23 x24: x24
STACK CFI 725c x25: x25
STACK CFI 7260 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 72a0 x19: x19 x20: x20
STACK CFI 72a4 x21: x21 x22: x22
STACK CFI 72a8 x23: x23 x24: x24
STACK CFI 72ac x25: x25
STACK CFI 72b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72b8 .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 7318 x19: x19 x20: x20
STACK CFI 731c x21: x21 x22: x22
STACK CFI 7320 x23: x23 x24: x24
STACK CFI 7324 x25: x25
STACK CFI 7328 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7330 .cfa: sp 368 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x29: .cfa -304 + ^
STACK CFI 734c x23: x23 x24: x24
STACK CFI 7350 x25: x25
STACK CFI 7378 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 737c x25: .cfa -240 + ^
STACK CFI INIT 7380 12c .cfa: sp 0 + .ra: x30
STACK CFI 7388 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7398 .cfa: sp 2112 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 73d8 x23: .cfa -16 + ^
STACK CFI 744c x23: x23
STACK CFI 746c .cfa: sp 64 +
STACK CFI 7478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7480 .cfa: sp 2112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 74a0 x23: .cfa -16 + ^
STACK CFI 74a4 x23: x23
STACK CFI 74a8 x23: .cfa -16 + ^
STACK CFI INIT 74b0 134 .cfa: sp 0 + .ra: x30
STACK CFI 74b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74c4 .cfa: sp 4128 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7510 x21: .cfa -16 + ^
STACK CFI 751c x22: .cfa -8 + ^
STACK CFI 7578 x21: x21
STACK CFI 757c x22: x22
STACK CFI 759c .cfa: sp 48 +
STACK CFI 75a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75ac .cfa: sp 4128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 75d0 x21: .cfa -16 + ^
STACK CFI 75d4 x22: .cfa -8 + ^
STACK CFI 75d8 x21: x21 x22: x22
STACK CFI 75dc x21: .cfa -16 + ^
STACK CFI 75e0 x22: .cfa -8 + ^
STACK CFI INIT 75e4 40 .cfa: sp 0 + .ra: x30
STACK CFI 75fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7624 184 .cfa: sp 0 + .ra: x30
STACK CFI 762c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7658 x21: .cfa -16 + ^
STACK CFI 76c4 x21: x21
STACK CFI 76cc x19: x19 x20: x20
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 76e4 x19: x19 x20: x20
STACK CFI 76e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7734 x21: x21
STACK CFI 7758 x21: .cfa -16 + ^
STACK CFI 775c x21: x21
STACK CFI 7780 x21: .cfa -16 + ^
STACK CFI INIT 77b0 39c .cfa: sp 0 + .ra: x30
STACK CFI 77b8 .cfa: sp 224 +
STACK CFI 77bc .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 77d4 x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^
STACK CFI 798c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7994 .cfa: sp 224 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7b50 e0 .cfa: sp 0 + .ra: x30
STACK CFI 7b58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7be8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c30 218 .cfa: sp 0 + .ra: x30
STACK CFI 7c38 .cfa: sp 160 +
STACK CFI 7c3c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 7c4c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 7d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7d50 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI 7dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7dd0 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x29: .cfa -128 + ^
STACK CFI INIT 7e50 40 .cfa: sp 0 + .ra: x30
STACK CFI 7e68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7e90 118 .cfa: sp 0 + .ra: x30
STACK CFI 7e98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ea4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7fb0 33c .cfa: sp 0 + .ra: x30
STACK CFI 7fb8 .cfa: sp 176 +
STACK CFI 7fbc .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7fd0 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 8150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8158 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 82f0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 82f8 .cfa: sp 192 +
STACK CFI 82fc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 830c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 8450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8458 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 849c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 84a4 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI INIT 84f0 40 .cfa: sp 0 + .ra: x30
STACK CFI 8508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8530 120 .cfa: sp 0 + .ra: x30
STACK CFI 8538 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8544 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 85c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 85c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8650 38c .cfa: sp 0 + .ra: x30
STACK CFI 8658 .cfa: sp 192 +
STACK CFI 865c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8670 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 8818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8820 .cfa: sp 192 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI INIT 89e0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 89e8 .cfa: sp 208 +
STACK CFI 89ec .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 89f4 x23: .cfa -96 + ^
STACK CFI 89fc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8a08 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8b4c .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
STACK CFI 8b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8ba0 40 .cfa: sp 0 + .ra: x30
STACK CFI 8bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8be0 118 .cfa: sp 0 + .ra: x30
STACK CFI 8be8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8bf4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 8c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8c70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8d00 374 .cfa: sp 0 + .ra: x30
STACK CFI 8d08 .cfa: sp 208 +
STACK CFI 8d0c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 8d20 x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^
STACK CFI 8ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8f00 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI INIT 9074 264 .cfa: sp 0 + .ra: x30
STACK CFI 907c .cfa: sp 336 +
STACK CFI 9080 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9088 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 9090 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 909c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 91a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 91b0 .cfa: sp 336 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x29: .cfa -288 + ^
STACK CFI 9234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 923c .cfa: sp 336 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x29: .cfa -288 + ^
STACK CFI INIT 92e0 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 92e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9304 .cfa: sp 544 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9394 .cfa: sp 80 +
STACK CFI 93a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 93ac .cfa: sp 544 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9508 x25: .cfa -16 + ^
STACK CFI 9510 x26: .cfa -8 + ^
STACK CFI 95c8 x25: x25
STACK CFI 95cc x26: x26
STACK CFI 9628 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9648 x25: x25
STACK CFI 964c x26: x26
STACK CFI 9650 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 96bc x25: x25
STACK CFI 96c0 x26: x26
STACK CFI 96c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 96f4 x25: x25 x26: x26
STACK CFI 9714 x25: .cfa -16 + ^
STACK CFI 9718 x26: .cfa -8 + ^
STACK CFI 971c x25: x25 x26: x26
STACK CFI 973c x25: .cfa -16 + ^
STACK CFI 9740 x26: .cfa -8 + ^
STACK CFI 9744 x25: x25 x26: x26
STACK CFI 9748 x25: .cfa -16 + ^
STACK CFI 974c x26: .cfa -8 + ^
STACK CFI INIT 9790 40 .cfa: sp 0 + .ra: x30
STACK CFI 97a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 97d0 11c .cfa: sp 0 + .ra: x30
STACK CFI 97d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 97e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9868 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 98f0 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 98f8 .cfa: sp 240 +
STACK CFI 98fc .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 9910 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 9b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9b30 .cfa: sp 240 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9cc0 294 .cfa: sp 0 + .ra: x30
STACK CFI 9cc8 .cfa: sp 224 +
STACK CFI 9cd4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ce4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9dd4 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9f54 10c .cfa: sp 0 + .ra: x30
STACK CFI 9f5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f68 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9fdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a060 334 .cfa: sp 0 + .ra: x30
STACK CFI a068 .cfa: sp 208 +
STACK CFI a06c .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI a084 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI a23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a244 .cfa: sp 208 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x29: .cfa -176 + ^
STACK CFI INIT a394 40 .cfa: sp 0 + .ra: x30
STACK CFI a3ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a3d4 298 .cfa: sp 0 + .ra: x30
STACK CFI a3dc .cfa: sp 224 +
STACK CFI a3e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a3f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI a4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a4e8 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a670 40 .cfa: sp 0 + .ra: x30
STACK CFI a688 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a6b0 104 .cfa: sp 0 + .ra: x30
STACK CFI a6b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a6c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI a728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a730 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a7b4 378 .cfa: sp 0 + .ra: x30
STACK CFI a7bc .cfa: sp 224 +
STACK CFI a7c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a7d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a7e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a9a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a9a8 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT ab30 264 .cfa: sp 0 + .ra: x30
STACK CFI ab38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ab48 .cfa: sp 6224 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ab6c x21: .cfa -32 + ^
STACK CFI ab70 x22: .cfa -24 + ^
STACK CFI abb4 x23: .cfa -16 + ^
STACK CFI abb8 x24: .cfa -8 + ^
STACK CFI ac58 x23: x23
STACK CFI ac5c x24: x24
STACK CFI ac7c x21: x21
STACK CFI ac84 x22: x22
STACK CFI ac88 .cfa: sp 64 +
STACK CFI ac94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac9c .cfa: sp 6224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI aca4 x24: .cfa -8 + ^
STACK CFI acc4 x23: .cfa -16 + ^
STACK CFI accc x23: x23
STACK CFI acd0 x24: x24
STACK CFI ad2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI ad54 x23: x23
STACK CFI ad5c x24: x24
STACK CFI ad80 x23: .cfa -16 + ^
STACK CFI ad84 x24: .cfa -8 + ^
STACK CFI ad88 x23: x23 x24: x24
STACK CFI ad8c x23: .cfa -16 + ^
STACK CFI ad90 x24: .cfa -8 + ^
STACK CFI INIT ada0 50 .cfa: sp 0 + .ra: x30
STACK CFI ada8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI adb8 x19: .cfa -16 + ^
STACK CFI ade8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT adf0 e8 .cfa: sp 0 + .ra: x30
STACK CFI adf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae04 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI ae50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ae58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ae94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ae9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI aeb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aeb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT aee0 80 .cfa: sp 0 + .ra: x30
STACK CFI aee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI af0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI af18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT af60 ac .cfa: sp 0 + .ra: x30
STACK CFI af68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI af70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI afa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI afac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b010 d4 .cfa: sp 0 + .ra: x30
STACK CFI b018 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b020 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b09c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT b0e4 13c .cfa: sp 0 + .ra: x30
STACK CFI b0ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b0f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b15c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b164 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b220 a8 .cfa: sp 0 + .ra: x30
STACK CFI b228 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b230 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b270 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b280 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b2d0 14c .cfa: sp 0 + .ra: x30
STACK CFI b2d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b2e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b36c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b374 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b3d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT b420 b8 .cfa: sp 0 + .ra: x30
STACK CFI b428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b430 x19: .cfa -16 + ^
STACK CFI b44c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b45c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b47c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b490 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b4e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI b4e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b4f0 x19: .cfa -16 + ^
STACK CFI b530 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b540 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b548 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b590 a8 .cfa: sp 0 + .ra: x30
STACK CFI b598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b5a0 x19: .cfa -16 + ^
STACK CFI b5d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b5e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b5e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b5f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b640 6c .cfa: sp 0 + .ra: x30
STACK CFI b648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b65c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b664 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b6b0 74 .cfa: sp 0 + .ra: x30
STACK CFI b6b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b6c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b6dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b724 74 .cfa: sp 0 + .ra: x30
STACK CFI b72c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b73c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b750 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b7a0 88 .cfa: sp 0 + .ra: x30
STACK CFI b7a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b7b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b7e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b830 c8 .cfa: sp 0 + .ra: x30
STACK CFI b838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b840 x19: .cfa -16 + ^
STACK CFI b884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b88c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b900 a0 .cfa: sp 0 + .ra: x30
STACK CFI b914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b91c x19: .cfa -16 + ^
STACK CFI b944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b9a0 104 .cfa: sp 0 + .ra: x30
STACK CFI b9a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b9b0 x19: .cfa -16 + ^
STACK CFI ba04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ba0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ba30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ba38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT baa4 1e0 .cfa: sp 0 + .ra: x30
STACK CFI baac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bab8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI bbe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI bbe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
