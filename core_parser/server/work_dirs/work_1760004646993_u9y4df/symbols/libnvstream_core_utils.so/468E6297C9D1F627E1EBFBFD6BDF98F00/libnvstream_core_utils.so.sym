MODULE Linux arm64 468E6297C9D1F627E1EBFBFD6BDF98F00 libnvstream_core_utils.so
INFO CODE_ID 97628E46D1C927F6E1EBFBFD6BDF98F0
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 3b00 24 0 init_have_lse_atomics
3b00 4 45 0
3b04 4 46 0
3b08 4 45 0
3b0c 4 46 0
3b10 4 47 0
3b14 4 47 0
3b18 4 48 0
3b1c 4 47 0
3b20 4 48 0
PUBLIC 3840 0 _init
PUBLIC 3b24 0 call_weak_fn
PUBLIC 3b40 0 deregister_tm_clones
PUBLIC 3b70 0 register_tm_clones
PUBLIC 3bb0 0 __do_global_dtors_aux
PUBLIC 3c00 0 frame_dummy
PUBLIC 3c10 0 linvs::utils::CallbackWatchDogImpl::Start()
PUBLIC 3c30 0 linvs::utils::CallbackWatchDogImpl::BeforeFeed()
PUBLIC 3c80 0 linvs::utils::CallbackWatchDogImpl::Stop()
PUBLIC 3cc0 0 linvs::utils::CallbackWatchDogImpl::AfterFeed()
PUBLIC 3d60 0 std::_Function_handler<void (), linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (), linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}> const&, std::_Manager_operation)
PUBLIC 3da0 0 linvs::utils::CallbackWatchDogImpl::~CallbackWatchDogImpl()
PUBLIC 3e30 0 linvs::utils::CallbackWatchDogImpl::~CallbackWatchDogImpl()
PUBLIC 3e60 0 linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)
PUBLIC 4050 0 linvs::utils::CallbackWatchDogImpl::OnTimer()
PUBLIC 4120 0 std::_Function_handler<void (), linvs::utils::CallbackWatchDogImpl::CallbackWatchDogImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)::{lambda()#1}>::_M_invoke(std::_Any_data const&)
PUBLIC 4130 0 linvs::utils::CallbackWatchDog::CreateUnique(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long, long, long)
PUBLIC 41b0 0 linvs::utils::FrameAlignImpl::flush_frames(std::function<void (int, unsigned long, long)>&&)
PUBLIC 4290 0 std::_Deque_base<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> >::_M_initialize_map(unsigned long) [clone .constprop.0]
PUBLIC 4340 0 linvs::utils::FrameAlignImpl::push_frame_into_queue(int, unsigned long, long)
PUBLIC 4650 0 linvs::utils::FrameAlignImpl::FrameAlignImpl(long, int, int, int, std::unordered_set<int, std::hash<int>, std::equal_to<int>, std::allocator<int> > const&)
PUBLIC 48e0 0 linvs::utils::FrameAlign::create_unique_instance(long, int, int, int, std::unordered_set<int, std::hash<int>, std::equal_to<int>, std::allocator<int> > const&)
PUBLIC 4970 0 linvs::utils::FrameAlignImpl::get_pacesetter_frame_info()
PUBLIC 4a70 0 linvs::utils::FrameAlignImpl::pop_pacesetter_queue()
PUBLIC 4d10 0 linvs::utils::FrameAlignImpl::align_frames(int, unsigned long, long)
PUBLIC 5100 0 linvs::utils::FrameAlignImpl::~FrameAlignImpl()
PUBLIC 5210 0 linvs::utils::FrameAlignImpl::~FrameAlignImpl()
PUBLIC 5310 0 std::_Deque_base<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> >::~_Deque_base()
PUBLIC 5380 0 std::_Hashtable<int, std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > >, std::allocator<std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > > >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 54b0 0 std::__detail::_Map_base<int, std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > >, std::allocator<std::pair<int const, std::queue<linvs::utils::FrameAlignImpl::FrameInfo, std::deque<linvs::utils::FrameAlignImpl::FrameInfo, std::allocator<linvs::utils::FrameAlignImpl::FrameInfo> > > > >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&)
PUBLIC 56f0 0 std::_Hashtable<int, std::pair<int const, unsigned long>, std::allocator<std::pair<int const, unsigned long> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 5820 0 std::__detail::_Map_base<int, std::pair<int const, unsigned long>, std::allocator<std::pair<int const, unsigned long> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](int const&)
PUBLIC 59e0 0 linvs::utils::FrameSelectorImpl::expect_frame_id() const
PUBLIC 59f0 0 linvs::utils::FrameSelectorImpl::fetch_expect_frame_id()
PUBLIC 5a20 0 linvs::utils::FrameSelectorImpl::expect_frame(unsigned long)
PUBLIC 5ac0 0 linvs::utils::FrameSelectorImpl::FrameSelectorImpl(double, double)
PUBLIC 5af0 0 linvs::utils::FrameSelector::create_unique_instance(double, double)
PUBLIC 5b60 0 linvs::utils::FrameSelectorImpl::~FrameSelectorImpl()
PUBLIC 5b70 0 linvs::utils::FrameSelectorImpl::~FrameSelectorImpl()
PUBLIC 5b80 0 linvs::utils::FrequencyCal::CreateUnique(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)
PUBLIC 5d90 0 linvs::utils::FrequencyCalImpl::FetchAdd(unsigned long)
PUBLIC 5db0 0 linvs::utils::FrequencyCalImpl::Reset()
PUBLIC 5e00 0 linvs::utils::FrequencyCalImpl::CurrentFrequency()
PUBLIC 5e70 0 std::_Function_handler<void (), linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (), linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}> const&, std::_Manager_operation)
PUBLIC 5eb0 0 linvs::utils::FrequencyCalImpl::~FrequencyCalImpl()
PUBLIC 5f20 0 std::_Function_handler<void (), linvs::utils::FrequencyCalImpl::FrequencyCalImpl(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, long)::{lambda()#1}>::_M_invoke(std::_Any_data const&)
PUBLIC 5fd0 0 linvs::utils::FrequencyCalImpl::~FrequencyCalImpl()
PUBLIC 6040 0 linvs::utils::GetSocId()
PUBLIC 6050 0 __aarch64_ldadd8_acq_rel
PUBLIC 6080 0 _fini
STACK CFI INIT 3b40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bbc x19: .cfa -16 + ^
STACK CFI 3bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c10 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c30 4c .cfa: sp 0 + .ra: x30
STACK CFI 3c34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c3c x19: .cfa -16 + ^
STACK CFI 3c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c80 3c .cfa: sp 0 + .ra: x30
STACK CFI 3c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c8c x19: .cfa -16 + ^
STACK CFI 3cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3cc0 98 .cfa: sp 0 + .ra: x30
STACK CFI 3cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ccc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3da0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3da4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3db4 x19: .cfa -16 + ^
STACK CFI 3e18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e30 28 .cfa: sp 0 + .ra: x30
STACK CFI 3e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e3c x19: .cfa -16 + ^
STACK CFI 3e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e60 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 3e64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3e74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3e84 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3e98 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3ffc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4050 c4 .cfa: sp 0 + .ra: x30
STACK CFI 4054 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 405c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4064 x21: .cfa -16 + ^
STACK CFI 40bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 40c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4120 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4130 7c .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 413c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4154 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4194 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 41b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 41c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 41e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 41ec x23: .cfa -48 + ^
STACK CFI 4230 x21: x21 x22: x22
STACK CFI 4234 x23: x23
STACK CFI 4258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 425c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 427c x21: x21 x22: x22 x23: x23
STACK CFI 4280 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4284 x23: .cfa -48 + ^
STACK CFI INIT 4290 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4294 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5100 108 .cfa: sp 0 + .ra: x30
STACK CFI 5104 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 510c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5114 x25: .cfa -16 + ^
STACK CFI 512c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5138 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5198 x19: x19 x20: x20
STACK CFI 51e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 51f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5204 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 5210 fc .cfa: sp 0 + .ra: x30
STACK CFI 5214 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 521c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5224 x25: .cfa -16 + ^
STACK CFI 523c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5248 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 52a8 x19: x19 x20: x20
STACK CFI 5308 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 4340 308 .cfa: sp 0 + .ra: x30
STACK CFI 4344 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4350 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 435c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 437c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4380 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 43c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 444c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4458 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4464 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 44c0 x25: x25 x26: x26
STACK CFI 44c8 x27: x27 x28: x28
STACK CFI 44ec x23: x23 x24: x24
STACK CFI 44f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5310 6c .cfa: sp 0 + .ra: x30
STACK CFI 5314 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 531c x21: .cfa -16 + ^
STACK CFI 532c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5360 x19: x19 x20: x20
STACK CFI 536c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5370 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5378 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 5380 12c .cfa: sp 0 + .ra: x30
STACK CFI 5384 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5390 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5398 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 543c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5440 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 54b0 238 .cfa: sp 0 + .ra: x30
STACK CFI 54b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 54bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 54dc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 5580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5584 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4650 290 .cfa: sp 0 + .ra: x30
STACK CFI 4654 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 466c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 46a4 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 46ec x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 46f4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 47bc x23: x23 x24: x24
STACK CFI 47c0 x27: x27 x28: x28
STACK CFI 47ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 47f0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI 47f4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 47f8 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 48e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 48e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 48ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 48f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4904 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4910 x25: .cfa -16 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4954 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4970 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4980 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4990 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 49dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 49e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4a70 298 .cfa: sp 0 + .ra: x30
STACK CFI 4a74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a7c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4a84 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4a94 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4b60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 56f0 12c .cfa: sp 0 + .ra: x30
STACK CFI 56f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5700 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5708 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 57ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5820 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 582c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 58e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4d10 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 4d14 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 4d30 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 4d38 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 4d48 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 4da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4dac .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 59e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 59f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 59f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a00 x19: .cfa -16 + ^
STACK CFI 5a1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a20 98 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ac0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5af0 64 .cfa: sp 0 + .ra: x30
STACK CFI 5af4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b00 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 5b0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b38 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 5b3c .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d90 20 .cfa: sp 0 + .ra: x30
STACK CFI 5d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5dac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5db0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5dbc x19: .cfa -16 + ^
STACK CFI 5df4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e00 68 .cfa: sp 0 + .ra: x30
STACK CFI 5e04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e0c x19: .cfa -16 + ^
STACK CFI 5e60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5eb0 68 .cfa: sp 0 + .ra: x30
STACK CFI 5eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ec4 x19: .cfa -16 + ^
STACK CFI 5f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5f14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f20 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5fd0 64 .cfa: sp 0 + .ra: x30
STACK CFI 5fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fe4 x19: .cfa -16 + ^
STACK CFI 6030 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b80 210 .cfa: sp 0 + .ra: x30
STACK CFI 5b84 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5b98 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5ba4 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5bac x25: .cfa -80 + ^
STACK CFI 5c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5c78 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 6040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6050 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b00 24 .cfa: sp 0 + .ra: x30
STACK CFI 3b04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x29: x29
