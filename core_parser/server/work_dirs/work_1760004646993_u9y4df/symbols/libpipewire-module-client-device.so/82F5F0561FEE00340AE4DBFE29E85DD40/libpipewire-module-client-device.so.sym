MODULE Linux arm64 82F5F0561FEE00340AE4DBFE29E85DD40 libpipewire-module-client-device.so
INFO CODE_ID 56F0F582EE1F34000AE4DBFE29E85DD4B49A691B
PUBLIC 9660 0 pipewire__module_init
STACK CFI INIT 1ce0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d10 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d50 48 .cfa: sp 0 + .ra: x30
STACK CFI 1d54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d5c x19: .cfa -16 + ^
STACK CFI 1d94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1da0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1db0 ac .cfa: sp 0 + .ra: x30
STACK CFI 1db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dc0 x19: .cfa -16 + ^
STACK CFI 1e44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e60 1c .cfa: sp 0 + .ra: x30
STACK CFI 1e68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e80 20 .cfa: sp 0 + .ra: x30
STACK CFI 1e88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ea0 414 .cfa: sp 0 + .ra: x30
STACK CFI 1ea8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1eb8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1ed8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 200c x23: x23 x24: x24
STACK CFI 2014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 201c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2094 x23: x23 x24: x24
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 20c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2138 x23: x23 x24: x24
STACK CFI 213c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 21ac x23: x23 x24: x24
STACK CFI 21b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 22b4 68 .cfa: sp 0 + .ra: x30
STACK CFI 22bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22c4 x19: .cfa -16 + ^
STACK CFI 2304 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 230c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2314 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2320 78 .cfa: sp 0 + .ra: x30
STACK CFI 2328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2330 x19: .cfa -16 + ^
STACK CFI 2380 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2390 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23a0 13c .cfa: sp 0 + .ra: x30
STACK CFI 23a8 .cfa: sp 96 +
STACK CFI 23b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 246c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 24e0 100 .cfa: sp 0 + .ra: x30
STACK CFI 24e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2504 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2598 x21: x21 x22: x22
STACK CFI 25a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 25b0 x21: x21 x22: x22
STACK CFI 25c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 25cc x21: x21 x22: x22
STACK CFI 25d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 25e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25f0 x19: .cfa -16 + ^
STACK CFI 2604 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 260c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2638 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2640 10c .cfa: sp 0 + .ra: x30
STACK CFI 2648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2658 x19: .cfa -16 + ^
STACK CFI 2708 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2710 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2750 10c .cfa: sp 0 + .ra: x30
STACK CFI 2758 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2768 x19: .cfa -16 + ^
STACK CFI 2818 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2820 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2860 bc .cfa: sp 0 + .ra: x30
STACK CFI 2868 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2870 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 28c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2920 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 2928 .cfa: sp 480 +
STACK CFI 293c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 2954 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 295c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2974 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 3120 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3128 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 55b0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 55b8 .cfa: sp 64 +
STACK CFI 55bc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 55c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55d8 x21: .cfa -16 + ^
STACK CFI 5674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 567c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5770 de8 .cfa: sp 0 + .ra: x30
STACK CFI 5778 .cfa: sp 352 +
STACK CFI 578c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5794 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 57a4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 57b0 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 57b8 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5b88 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6560 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 6568 .cfa: sp 96 +
STACK CFI 6574 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6580 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 671c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6724 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6814 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 681c .cfa: sp 160 +
STACK CFI 6828 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6830 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6838 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6844 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6850 x25: .cfa -16 + ^
STACK CFI 6a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6a1c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6b10 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 6b18 .cfa: sp 112 +
STACK CFI 6b24 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b40 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ce0 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6de4 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6dec .cfa: sp 128 +
STACK CFI 6df8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fb8 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6fc4 218 .cfa: sp 0 + .ra: x30
STACK CFI 6fcc .cfa: sp 176 +
STACK CFI 6fd8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fe0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 71c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71d0 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71e0 200 .cfa: sp 0 + .ra: x30
STACK CFI 71e8 .cfa: sp 144 +
STACK CFI 71f4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 73cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 73d4 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 73e0 31c .cfa: sp 0 + .ra: x30
STACK CFI 73e8 .cfa: sp 176 +
STACK CFI 73f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 73fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7404 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7410 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7420 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 75d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 75e0 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7700 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 7708 .cfa: sp 96 +
STACK CFI 7714 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7720 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 78b4 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 79a4 850 .cfa: sp 0 + .ra: x30
STACK CFI 79ac .cfa: sp 224 +
STACK CFI 79b8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 79c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 79dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7a9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7aa4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7ba8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7ef4 x27: x27 x28: x28
STACK CFI 7f70 x21: x21 x22: x22
STACK CFI 7f74 x23: x23 x24: x24
STACK CFI 7fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 7fd8 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8198 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 81b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 81d8 x27: x27 x28: x28
STACK CFI 81e4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 81e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 81ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 81f0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 81f4 7f8 .cfa: sp 0 + .ra: x30
STACK CFI 81fc .cfa: sp 240 +
STACK CFI 8208 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8210 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 821c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8234 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 830c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8420 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 876c x27: x27 x28: x28
STACK CFI 877c x23: x23 x24: x24
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 87e4 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 89a0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 89bc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 89e0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 89e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 89e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 89f0 258 .cfa: sp 0 + .ra: x30
STACK CFI 89f8 .cfa: sp 240 +
STACK CFI 8a04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8a0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8a4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8a54 x23: .cfa -16 + ^
STACK CFI 8bbc x19: x19 x20: x20
STACK CFI 8bc0 x23: x23
STACK CFI 8bc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 8c04 x19: x19 x20: x20
STACK CFI 8c08 x23: x23
STACK CFI 8c34 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8c3c .cfa: sp 240 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8c40 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c44 x23: .cfa -16 + ^
STACK CFI INIT 8c50 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 8c58 .cfa: sp 128 +
STACK CFI 8c64 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e28 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8e34 470 .cfa: sp 0 + .ra: x30
STACK CFI 8e3c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8e40 .cfa: x29 96 +
STACK CFI 8e44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8e64 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 90fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 9104 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 92a4 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 92ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 92b0 .cfa: x29 80 +
STACK CFI 92b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 92d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 9570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9578 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9660 238 .cfa: sp 0 + .ra: x30
STACK CFI 9668 .cfa: sp 112 +
STACK CFI 9674 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9684 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9714 x25: .cfa -16 + ^
STACK CFI 97f4 x25: x25
STACK CFI 9828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9830 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9870 x25: x25
STACK CFI 9880 x25: .cfa -16 + ^
STACK CFI 988c x25: x25
STACK CFI 9894 x25: .cfa -16 + ^
