MODULE Linux arm64 70C7347EBDB4259E35FD54013BAD8EA40 libnvstream_core_nvipc.so
INFO CODE_ID 7E34C770B4BD9E2535FD54013BAD8EA4
PUBLIC 1420 0 _init
PUBLIC 1590 0 call_weak_fn
PUBLIC 15b0 0 deregister_tm_clones
PUBLIC 15e0 0 register_tm_clones
PUBLIC 1620 0 __do_global_dtors_aux
PUBLIC 1670 0 frame_dummy
PUBLIC 1680 0 std::once_flag::_Prepare_execution::_Prepare_execution<std::call_once<linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)::{lambda()#1}>(std::once_flag&, linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)::{lambda()#1}&&)::{lambda()#1}>(std::once_flag&)::{lambda()#1}::_FUN()
PUBLIC 16c0 0 linvs::nvipc::Endpoint::Endpoint(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 1830 0 linvs::nvipc::Endpoint::~Endpoint()
PUBLIC 1860 0 linvs::nvipc::Endpoint::operator bool() const
PUBLIC 1870 0 linvs::nvipc::Endpoint::operator*() const
PUBLIC 1880 0 linvs::nvipc::Endpoint::GetEndpointInfo(linvs::nvipc::EndpointInfo&) const
PUBLIC 18f0 0 linvs::nvipc::Endpoint::GetEvent(unsigned int&) const
PUBLIC 1940 0 linvs::nvipc::Endpoint::GetLinuxFd() const
PUBLIC 19e0 0 linvs::nvipc::Endpoint::Read(void*, unsigned long) const
PUBLIC 1a70 0 linvs::nvipc::Endpoint::Write(void const*, unsigned long) const
PUBLIC 1b00 0 linvs::nvipc::EndpointInfo::operator*()
PUBLIC 1b04 0 _fini
STACK CFI INIT 15b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1620 48 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 162c x19: .cfa -16 + ^
STACK CFI 1664 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1680 40 .cfa: sp 0 + .ra: x30
STACK CFI 1684 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16c0 16c .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 16d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 16e0 x21: .cfa -48 + ^
STACK CFI 17b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1830 24 .cfa: sp 0 + .ra: x30
STACK CFI 1840 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1850 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1860 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1880 68 .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1894 x19: .cfa -16 + ^
STACK CFI 18b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18f0 4c .cfa: sp 0 + .ra: x30
STACK CFI 18f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 190c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1910 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1940 94 .cfa: sp 0 + .ra: x30
STACK CFI 1944 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1958 x19: .cfa -32 + ^
STACK CFI 19a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 19e0 88 .cfa: sp 0 + .ra: x30
STACK CFI 19e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a70 88 .cfa: sp 0 + .ra: x30
STACK CFI 1a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b00 4 .cfa: sp 0 + .ra: x30
