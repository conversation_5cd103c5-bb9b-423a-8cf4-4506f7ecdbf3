MODULE Linux arm64 5FB7A11360E643B2F3FBEBE7A11F0B920 libpulse-mainloop-glib.so.0
INFO CODE_ID 13A1B75FE660B243F3FBEBE7A11F0B92AFB05B0E
PUBLIC 2640 0 pa_glib_mainloop_new
PUBLIC 26f4 0 pa_glib_mainloop_free
PUBLIC 2774 0 pa_glib_mainloop_get_api
STACK CFI INIT f00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT f70 48 .cfa: sp 0 + .ra: x30
STACK CFI f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f7c x19: .cfa -16 + ^
STACK CFI fb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd0 90 .cfa: sp 0 + .ra: x30
STACK CFI fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 101c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1060 78 .cfa: sp 0 + .ra: x30
STACK CFI 1068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 108c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 10e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 10e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 112c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1154 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 115c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 11b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1230 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1278 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1280 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 129c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1320 d0 .cfa: sp 0 + .ra: x30
STACK CFI 1328 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1374 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 137c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 13f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 13f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1414 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 141c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1470 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1478 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1480 x19: .cfa -16 + ^
STACK CFI 14b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 14d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1530 1cc .cfa: sp 0 + .ra: x30
STACK CFI 1538 .cfa: sp 80 +
STACK CFI 153c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1544 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 154c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 155c x23: .cfa -16 + ^
STACK CFI 15dc x23: x23
STACK CFI 15f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15fc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1658 x23: x23
STACK CFI 165c x23: .cfa -16 + ^
STACK CFI 16a0 x23: x23
STACK CFI 16a8 x23: .cfa -16 + ^
STACK CFI 16d0 x23: x23
STACK CFI 16f8 x23: .cfa -16 + ^
STACK CFI INIT 1700 1fc .cfa: sp 0 + .ra: x30
STACK CFI 1708 .cfa: sp 80 +
STACK CFI 170c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1714 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 171c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 172c x23: .cfa -16 + ^
STACK CFI 17ac x23: x23
STACK CFI 17c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17cc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1830 x23: x23
STACK CFI 1834 x23: .cfa -16 + ^
STACK CFI 1878 x23: x23
STACK CFI 1880 x23: .cfa -16 + ^
STACK CFI 18d0 x23: x23
STACK CFI 18f8 x23: .cfa -16 + ^
STACK CFI INIT 1900 1fc .cfa: sp 0 + .ra: x30
STACK CFI 1908 .cfa: sp 80 +
STACK CFI 190c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1914 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 191c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 192c x23: .cfa -16 + ^
STACK CFI 19ac x23: x23
STACK CFI 19c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19cc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1a30 x23: x23
STACK CFI 1a34 x23: .cfa -16 + ^
STACK CFI 1a78 x23: x23
STACK CFI 1a80 x23: .cfa -16 + ^
STACK CFI 1ad0 x23: x23
STACK CFI 1af8 x23: .cfa -16 + ^
STACK CFI INIT 1b00 164 .cfa: sp 0 + .ra: x30
STACK CFI 1b08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b18 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1bc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1c64 f8 .cfa: sp 0 + .ra: x30
STACK CFI 1c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c78 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1ce8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d60 168 .cfa: sp 0 + .ra: x30
STACK CFI 1d68 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1d78 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1e24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1ed0 190 .cfa: sp 0 + .ra: x30
STACK CFI 1ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2060 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2068 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2074 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 209c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 20f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2100 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2124 28 .cfa: sp 0 + .ra: x30
STACK CFI 212c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2138 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2150 1bc .cfa: sp 0 + .ra: x30
STACK CFI 2158 .cfa: sp 80 +
STACK CFI 2164 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 216c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2174 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 21ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 21f4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2310 12c .cfa: sp 0 + .ra: x30
STACK CFI 2318 .cfa: sp 80 +
STACK CFI 2324 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2338 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2398 x19: x19 x20: x20
STACK CFI 239c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23a4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 23a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 23d8 x21: x21 x22: x22
STACK CFI 23ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 23f0 x21: x21 x22: x22
STACK CFI 2414 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 2440 200 .cfa: sp 0 + .ra: x30
STACK CFI 2448 .cfa: sp 96 +
STACK CFI 2454 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2468 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 246c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 24d0 x19: x19 x20: x20
STACK CFI 24d4 x21: x21 x22: x22
STACK CFI 24d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24e0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 253c x23: .cfa -16 + ^
STACK CFI 2540 x23: x23
STACK CFI 2544 x23: .cfa -16 + ^
STACK CFI 2580 x23: x23
STACK CFI 25c0 x23: .cfa -16 + ^
STACK CFI 25e8 x23: x23
STACK CFI 2610 x23: .cfa -16 + ^
STACK CFI 2614 x23: x23
STACK CFI 2618 x23: .cfa -16 + ^
STACK CFI INIT 2640 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2654 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26f4 80 .cfa: sp 0 + .ra: x30
STACK CFI 26fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2704 x19: .cfa -16 + ^
STACK CFI 2748 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2750 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2774 40 .cfa: sp 0 + .ra: x30
STACK CFI 278c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
