MODULE Linux arm64 4B36FA1C64EAC9C038572CF57C2F27D20 libboost_coroutine.so.1.77.0
INFO CODE_ID 1CFA364BEA64C0C938572CF57C2F27D2
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 52e0 24 0 init_have_lse_atomics
52e0 4 45 0
52e4 4 46 0
52e8 4 45 0
52ec 4 46 0
52f0 4 47 0
52f4 4 47 0
52f8 4 48 0
52fc 4 47 0
5300 4 48 0
PUBLIC 4aa8 0 _init
PUBLIC 4ef0 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::rethrow() const
PUBLIC 4fa4 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::rethrow() const
PUBLIC 5058 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::rethrow() const
PUBLIC 5118 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::rethrow() const
PUBLIC 51e0 0 _GLOBAL__sub_I_stack_traits.cpp
PUBLIC 5304 0 call_weak_fn
PUBLIC 5320 0 deregister_tm_clones
PUBLIC 5350 0 register_tm_clones
PUBLIC 5390 0 __do_global_dtors_aux
PUBLIC 53e0 0 frame_dummy
PUBLIC 53f0 0 boost::coroutines::detail::coroutine_context::coroutine_context()
PUBLIC 5410 0 boost::coroutines::detail::coroutine_context::coroutine_context(void (*)(boost::context::detail::transfer_t), boost::coroutines::detail::preallocated const&)
PUBLIC 5450 0 boost::coroutines::detail::coroutine_context::coroutine_context(boost::coroutines::detail::coroutine_context const&)
PUBLIC 5470 0 boost::coroutines::detail::coroutine_context::operator=(boost::coroutines::detail::coroutine_context const&)
PUBLIC 5490 0 boost::coroutines::detail::coroutine_context::jump(boost::coroutines::detail::coroutine_context&, void*)
PUBLIC 5500 0 boost::coroutines::coroutine_category()
PUBLIC 5510 0 boost::system::error_category::failed(int) const
PUBLIC 5520 0 boost::system::detail::generic_error_category::name() const
PUBLIC 5530 0 boost::system::detail::system_error_category::name() const
PUBLIC 5540 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 5560 0 boost::system::detail::interop_error_category::name() const
PUBLIC 5570 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 5650 0 boost::system::detail::std_category::name() const
PUBLIC 5670 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 56e0 0 boost::coroutines::coroutine_error_category::name() const
PUBLIC 56f0 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 5700 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 5710 0 boost::system::detail::std_category::~std_category()
PUBLIC 5730 0 boost::system::detail::std_category::~std_category()
PUBLIC 5770 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 5800 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 58a0 0 boost::coroutines::coroutine_error_category::message[abi:cxx11](int) const
PUBLIC 59b0 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 5ad0 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 5bf0 0 boost::system::system_error::~system_error()
PUBLIC 5c40 0 boost::system::system_error::~system_error()
PUBLIC 5ca0 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 5e60 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 6390 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 6970 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 6a20 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 6a60 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 6b60 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 6cc0 0 boost::system::system_error::what() const
PUBLIC 6ef0 0 boost::exception_detail::operator<(boost::exception_detail::type_info_ const&, boost::exception_detail::type_info_ const&) [clone .isra.0]
PUBLIC 6f40 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_erase(std::_Rb_tree_node<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >*) [clone .isra.0]
PUBLIC 71e0 0 boost::coroutines::stack_traits::is_unbounded()
PUBLIC 7230 0 boost::coroutines::stack_traits::page_size()
PUBLIC 7290 0 boost::coroutines::stack_traits::minimum_size()
PUBLIC 72a0 0 boost::coroutines::stack_traits::maximum_size()
PUBLIC 72f0 0 boost::coroutines::stack_traits::default_size()
PUBLIC 7350 0 boost::detail::sp_counted_base::destroy()
PUBLIC 7360 0 boost::exception_detail::error_info_container_impl::add_ref() const
PUBLIC 7370 0 boost::error_info<boost::tag_original_exception_type, std::type_info const*>::~error_info()
PUBLIC 7380 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::~sp_counted_impl_p()
PUBLIC 7390 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::~sp_counted_impl_p()
PUBLIC 73a0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 73b0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::get_deleter(std::type_info const&)
PUBLIC 73c0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::get_local_deleter(std::type_info const&)
PUBLIC 73d0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::get_untyped_deleter()
PUBLIC 73e0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::get_deleter(std::type_info const&)
PUBLIC 73f0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::get_local_deleter(std::type_info const&)
PUBLIC 7400 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::get_untyped_deleter()
PUBLIC 7410 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_deleter(std::type_info const&)
PUBLIC 7420 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_local_deleter(std::type_info const&)
PUBLIC 7430 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_untyped_deleter()
PUBLIC 7440 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::~sp_counted_impl_p()
PUBLIC 7450 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::~sp_counted_impl_p()
PUBLIC 7460 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 7470 0 boost::error_info<boost::tag_original_exception_type, std::type_info const*>::~error_info()
PUBLIC 7480 0 boost::error_info<boost::tag_original_exception_type, std::type_info const*>::clone() const
PUBLIC 74c0 0 boost::error_info<boost::tag_original_exception_type, std::type_info const*>::name_value_string[abi:cxx11]() const
PUBLIC 7630 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::dispose()
PUBLIC 7670 0 boost::exception_ptr::~exception_ptr()
PUBLIC 7700 0 boost::exception_detail::error_info_container_impl::get(boost::exception_detail::type_info_ const&) const
PUBLIC 77f0 0 boost::system::error_category::operator std::_V2::error_category const&() const
PUBLIC 7970 0 boost::exception_detail::copy_boost_exception(boost::exception*, boost::exception const*)
PUBLIC 7b30 0 boost::detail::sp_counted_base::release()
PUBLIC 7bd0 0 boost::exception_detail::error_info_container_impl::release() const
PUBLIC 7cb0 0 boost::exception_detail::bad_alloc_::~bad_alloc_()
PUBLIC 7de0 0 non-virtual thunk to boost::exception_detail::bad_alloc_::~bad_alloc_()
PUBLIC 7df0 0 boost::exception_detail::bad_alloc_::~bad_alloc_()
PUBLIC 7e20 0 non-virtual thunk to boost::exception_detail::bad_alloc_::~bad_alloc_()
PUBLIC 7e50 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 7e80 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 7ec0 0 non-virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 7ef0 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 7f40 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 7fa0 0 non-virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::~clone_impl()
PUBLIC 8000 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::clone() const
PUBLIC 80f0 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_>::clone() const
PUBLIC 81f0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_alloc_> >::dispose()
PUBLIC 8280 0 boost::exception_detail::bad_exception_::~bad_exception_()
PUBLIC 83b0 0 non-virtual thunk to boost::exception_detail::bad_exception_::~bad_exception_()
PUBLIC 83c0 0 boost::exception_detail::bad_exception_::~bad_exception_()
PUBLIC 83f0 0 non-virtual thunk to boost::exception_detail::bad_exception_::~bad_exception_()
PUBLIC 8420 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 8450 0 non-virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 8480 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 84c0 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 8510 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 8570 0 non-virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::~clone_impl()
PUBLIC 85d0 0 boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::clone() const
PUBLIC 86c0 0 virtual thunk to boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_>::clone() const
PUBLIC 87c0 0 boost::detail::sp_counted_impl_p<boost::exception_detail::clone_impl<boost::exception_detail::bad_exception_> >::dispose()
PUBLIC 8850 0 boost::exception_detail::error_info_container_impl::diagnostic_information(char const*) const
PUBLIC 8cf0 0 boost::exception_ptr boost::exception_detail::get_static_exception_object<boost::exception_detail::bad_alloc_>()
PUBLIC 9020 0 boost::exception_ptr boost::exception_detail::get_static_exception_object<boost::exception_detail::bad_exception_>()
PUBLIC 9350 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_unique_pos(boost::exception_detail::type_info_ const&)
PUBLIC 9460 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, boost::exception_detail::type_info_ const&)
PUBLIC 95d0 0 boost::exception_detail::error_info_container_impl::clone() const
PUBLIC 9980 0 boost::exception_detail::error_info_container_impl::set(boost::shared_ptr<boost::exception_detail::error_info_base> const&, boost::exception_detail::type_info_ const&)
PUBLIC 9b20 0 __aarch64_cas8_acq_rel
PUBLIC 9b60 0 __aarch64_ldadd4_relax
PUBLIC 9b90 0 __aarch64_ldadd4_acq_rel
PUBLIC 9bc0 0 _fini
STACK CFI INIT 5320 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5350 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5390 48 .cfa: sp 0 + .ra: x30
STACK CFI 5394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 539c x19: .cfa -16 + ^
STACK CFI 53d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5410 38 .cfa: sp 0 + .ra: x30
STACK CFI 5418 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5428 x19: .cfa -16 + ^
STACK CFI 5444 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5450 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5470 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5490 64 .cfa: sp 0 + .ra: x30
STACK CFI 5494 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 54f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5510 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5520 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5530 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5560 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5570 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5650 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5670 64 .cfa: sp 0 + .ra: x30
STACK CFI 5674 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5680 x19: .cfa -32 + ^
STACK CFI 56cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 56d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 56e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 56f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5700 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5710 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5730 38 .cfa: sp 0 + .ra: x30
STACK CFI 5734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5744 x19: .cfa -16 + ^
STACK CFI 5764 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5770 88 .cfa: sp 0 + .ra: x30
STACK CFI 5774 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 577c x19: .cfa -16 + ^
STACK CFI 57a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5800 94 .cfa: sp 0 + .ra: x30
STACK CFI 5804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5818 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5878 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 58a0 104 .cfa: sp 0 + .ra: x30
STACK CFI 58a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58b8 x19: .cfa -32 + ^
STACK CFI 5948 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 594c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 59b0 114 .cfa: sp 0 + .ra: x30
STACK CFI 59b4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 59c8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 59d0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a54 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5ad0 114 .cfa: sp 0 + .ra: x30
STACK CFI 5ad4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5ae8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5af0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b74 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 5bf0 50 .cfa: sp 0 + .ra: x30
STACK CFI 5bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c0c x19: .cfa -16 + ^
STACK CFI 5c3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c40 5c .cfa: sp 0 + .ra: x30
STACK CFI 5c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c5c x19: .cfa -16 + ^
STACK CFI 5c98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ca0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cbc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5da0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e60 530 .cfa: sp 0 + .ra: x30
STACK CFI 5e64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5e6c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5e84 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5ea4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5fd0 x25: x25 x26: x26
STACK CFI 5fd8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 602c x25: x25 x26: x26
STACK CFI 6034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6038 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 60b0 x25: x25 x26: x26
STACK CFI 60e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 60ec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 6100 x25: x25 x26: x26
STACK CFI 6104 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6190 x25: x25 x26: x26
STACK CFI 623c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6250 x25: x25 x26: x26
STACK CFI 6264 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6318 x25: x25 x26: x26
STACK CFI 632c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6340 x25: x25 x26: x26
STACK CFI 635c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6370 x25: x25 x26: x26
STACK CFI 6374 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 6390 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 6394 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 639c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 63b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6678 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 6738 x25: .cfa -48 + ^
STACK CFI 676c x25: x25
STACK CFI 692c x25: .cfa -48 + ^
STACK CFI 6948 x25: x25
STACK CFI INIT 6970 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6974 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 6a0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a10 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 6a20 3c .cfa: sp 0 + .ra: x30
STACK CFI 6a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a3c x19: .cfa -16 + ^
STACK CFI 6a58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6a60 f4 .cfa: sp 0 + .ra: x30
STACK CFI 6a64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6a74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6aa4 x21: .cfa -64 + ^
STACK CFI 6ae8 x21: x21
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 6b18 x21: x21
STACK CFI 6b28 x21: .cfa -64 + ^
STACK CFI 6b50 x21: x21
STACK CFI INIT 6b60 158 .cfa: sp 0 + .ra: x30
STACK CFI 6b64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6b78 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6b80 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c20 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5500 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cc0 224 .cfa: sp 0 + .ra: x30
STACK CFI 6cc4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6ccc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6cdc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7350 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7360 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7390 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 73f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7400 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7410 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7420 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7430 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7480 34 .cfa: sp 0 + .ra: x30
STACK CFI 7484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 748c x19: .cfa -16 + ^
STACK CFI 74b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ef0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4efc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 4fa4 b4 .cfa: sp 0 + .ra: x30
STACK CFI 4fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 74c0 168 .cfa: sp 0 + .ra: x30
STACK CFI 74c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 74d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 74e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 759c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6ef0 48 .cfa: sp 0 + .ra: x30
STACK CFI 6efc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6f2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7630 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5058 c0 .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5064 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 5118 c0 .cfa: sp 0 + .ra: x30
STACK CFI 511c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5124 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 7670 90 .cfa: sp 0 + .ra: x30
STACK CFI 7674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 767c x19: .cfa -16 + ^
STACK CFI 76e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 76fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7700 e4 .cfa: sp 0 + .ra: x30
STACK CFI 7704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 770c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7714 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7720 x23: .cfa -16 + ^
STACK CFI 77a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 77a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 77d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 77d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 77f0 17c .cfa: sp 0 + .ra: x30
STACK CFI 77f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7804 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 784c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7850 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7970 1bc .cfa: sp 0 + .ra: x30
STACK CFI 7974 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 797c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7990 x21: .cfa -32 + ^
STACK CFI 7a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7b30 98 .cfa: sp 0 + .ra: x30
STACK CFI 7b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b3c x19: .cfa -16 + ^
STACK CFI 7b9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ba8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6f40 294 .cfa: sp 0 + .ra: x30
STACK CFI 6f48 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6f50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6f5c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6f68 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6f6c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7180 x21: x21 x22: x22
STACK CFI 7184 x27: x27 x28: x28
STACK CFI 71cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 7bd0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7c3c x21: .cfa -16 + ^
STACK CFI 7c8c x21: x21
STACK CFI 7ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7cb0 12c .cfa: sp 0 + .ra: x30
STACK CFI 7cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7d64 x21: .cfa -16 + ^
STACK CFI 7db4 x21: x21
STACK CFI 7dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7dc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7de0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7df0 28 .cfa: sp 0 + .ra: x30
STACK CFI 7df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7dfc x19: .cfa -16 + ^
STACK CFI 7e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e20 2c .cfa: sp 0 + .ra: x30
STACK CFI 7e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e2c x19: .cfa -16 + ^
STACK CFI 7e48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e50 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ef0 48 .cfa: sp 0 + .ra: x30
STACK CFI 7ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f14 x19: .cfa -16 + ^
STACK CFI 7f34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8000 ec .cfa: sp 0 + .ra: x30
STACK CFI 8004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 800c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 80b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7e80 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ec0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7f40 58 .cfa: sp 0 + .ra: x30
STACK CFI 7f44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f70 x19: .cfa -16 + ^
STACK CFI 7f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7fa0 54 .cfa: sp 0 + .ra: x30
STACK CFI 7fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fc0 x19: .cfa -16 + ^
STACK CFI 7ff0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 81f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 81f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 81fc x19: .cfa -16 + ^
STACK CFI 8254 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8258 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8264 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 80f0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 80f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 80fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 81b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 81b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8280 12c .cfa: sp 0 + .ra: x30
STACK CFI 8284 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 82f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 8334 x21: .cfa -16 + ^
STACK CFI 8384 x21: x21
STACK CFI 8394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8398 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 83a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 83b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 83c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 83c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83cc x19: .cfa -16 + ^
STACK CFI 83e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 83f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 83f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83fc x19: .cfa -16 + ^
STACK CFI 8418 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8420 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 84c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 84c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84e4 x19: .cfa -16 + ^
STACK CFI 8504 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 85d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 85d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 868c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8450 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8480 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8510 58 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8540 x19: .cfa -16 + ^
STACK CFI 8564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8570 54 .cfa: sp 0 + .ra: x30
STACK CFI 8574 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8590 x19: .cfa -16 + ^
STACK CFI 85c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 87c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 87c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87cc x19: .cfa -16 + ^
STACK CFI 8824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8828 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8834 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 86c0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 86c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8788 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71e0 50 .cfa: sp 0 + .ra: x30
STACK CFI 71e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 722c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7230 58 .cfa: sp 0 + .ra: x30
STACK CFI 7234 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 723c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7248 x21: .cfa -16 + ^
STACK CFI 7284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 72a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 72a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 72f0 54 .cfa: sp 0 + .ra: x30
STACK CFI 72f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72fc x19: .cfa -16 + ^
STACK CFI 7318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 731c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7340 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8850 498 .cfa: sp 0 + .ra: x30
STACK CFI 8854 .cfa: sp 624 +
STACK CFI 8860 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 8868 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 8880 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 8890 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 8894 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 889c x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 8b94 x19: x19 x20: x20
STACK CFI 8b98 x21: x21 x22: x22
STACK CFI 8b9c x23: x23 x24: x24
STACK CFI 8ba0 x25: x25 x26: x26
STACK CFI 8bcc .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 8bd0 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 8c04 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8c08 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 8c0c x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 8c10 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 8c14 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI INIT 8cf0 324 .cfa: sp 0 + .ra: x30
STACK CFI 8cf4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8d14 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8d28 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 8d38 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 8d40 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 8e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8e2c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 8e3c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8f30 x27: x27 x28: x28
STACK CFI 8f34 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8f3c x27: x27 x28: x28
STACK CFI 8f40 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8f78 x27: x27 x28: x28
STACK CFI 8f94 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8fa0 x27: x27 x28: x28
STACK CFI 8fb8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 9020 324 .cfa: sp 0 + .ra: x30
STACK CFI 9024 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9044 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 9058 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 9068 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 9070 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 9158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 915c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x29: .cfa -224 + ^
STACK CFI 916c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 9260 x27: x27 x28: x28
STACK CFI 9264 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 926c x27: x27 x28: x28
STACK CFI 9270 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 92a8 x27: x27 x28: x28
STACK CFI 92c4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 92d0 x27: x27 x28: x28
STACK CFI 92e8 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 9350 108 .cfa: sp 0 + .ra: x30
STACK CFI 9354 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 935c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9364 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 936c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9408 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9460 168 .cfa: sp 0 + .ra: x30
STACK CFI 9464 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 946c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 947c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 948c x23: .cfa -16 + ^
STACK CFI 94d4 x23: x23
STACK CFI 94ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 94f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9534 x23: x23
STACK CFI 956c x23: .cfa -16 + ^
STACK CFI 9570 x23: x23
STACK CFI 9584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9588 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 958c x23: x23
STACK CFI 95a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 95b4 x23: x23
STACK CFI 95b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 95c0 x23: x23
STACK CFI INIT 95d0 3ac .cfa: sp 0 + .ra: x30
STACK CFI 95d4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 95dc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 95f8 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 983c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9840 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9980 194 .cfa: sp 0 + .ra: x30
STACK CFI 9984 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 998c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9998 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 99a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 99a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9a6c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 51e0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 51e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5238 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 523c x19: .cfa -32 + ^
STACK CFI 5270 x19: x19
STACK CFI 527c x19: .cfa -32 + ^
STACK CFI 52b8 x19: x19
STACK CFI 52c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52d0 x19: .cfa -32 + ^
STACK CFI INIT 9b20 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 52e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 52e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52fc .cfa: sp 0 + .ra: .ra x29: x29
