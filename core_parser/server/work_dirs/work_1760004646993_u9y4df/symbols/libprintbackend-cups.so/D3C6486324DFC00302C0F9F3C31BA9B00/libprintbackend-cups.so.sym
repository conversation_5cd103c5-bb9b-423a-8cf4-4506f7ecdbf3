MODULE Linux arm64 D3C6486324DFC00302C0F9F3C31BA9B00 libprintbackend-cups.so
INFO CODE_ID 6348C6D3DF2403C002C0F9F3C31BA9B08FD969FA
PUBLIC 11b74 0 g_io_module_load
PUBLIC 11cb0 0 g_io_module_unload
PUBLIC 11cd0 0 g_io_module_query
STACK CFI INIT b200 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT b230 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b270 48 .cfa: sp 0 + .ra: x30
STACK CFI b274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b27c x19: .cfa -16 + ^
STACK CFI b2b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b2c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b2d0 18 .cfa: sp 0 + .ra: x30
STACK CFI b2d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b2e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b2f0 1c .cfa: sp 0 + .ra: x30
STACK CFI b2f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b310 20 .cfa: sp 0 + .ra: x30
STACK CFI b318 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b328 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b330 1c .cfa: sp 0 + .ra: x30
STACK CFI b338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b350 20 .cfa: sp 0 + .ra: x30
STACK CFI b358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b364 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b370 b0 .cfa: sp 0 + .ra: x30
STACK CFI b37c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b388 x21: .cfa -16 + ^
STACK CFI b3bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3f4 x19: x19 x20: x20
STACK CFI b3fc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI b40c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b418 x19: x19 x20: x20
STACK CFI INIT b420 58 .cfa: sp 0 + .ra: x30
STACK CFI b428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b46c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b480 d4 .cfa: sp 0 + .ra: x30
STACK CFI b488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b4d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b4d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT b554 f4 .cfa: sp 0 + .ra: x30
STACK CFI b55c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b564 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b638 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b650 3c .cfa: sp 0 + .ra: x30
STACK CFI b658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b660 x19: .cfa -16 + ^
STACK CFI b684 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b690 368 .cfa: sp 0 + .ra: x30
STACK CFI b698 .cfa: sp 128 +
STACK CFI b6a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b6ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b6b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b6c0 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI b714 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b71c x25: .cfa -32 + ^
STACK CFI b7e0 x23: x23 x24: x24
STACK CFI b7e4 x25: x25
STACK CFI b840 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b848 .cfa: sp 128 + .ra: .cfa -88 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI b880 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI b928 x23: x23 x24: x24
STACK CFI b92c x25: x25
STACK CFI b930 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI b9e4 x23: x23 x24: x24
STACK CFI b9e8 x25: x25
STACK CFI b9f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b9f4 x25: .cfa -32 + ^
STACK CFI INIT ba00 24 .cfa: sp 0 + .ra: x30
STACK CFI ba08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ba14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ba24 128 .cfa: sp 0 + .ra: x30
STACK CFI ba2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ba34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ba48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ba50 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba5c x27: .cfa -16 + ^
STACK CFI ba7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI baf4 x19: x19 x20: x20
STACK CFI bafc x23: x23 x24: x24
STACK CFI bb00 x25: x25 x26: x26
STACK CFI bb04 x27: x27
STACK CFI bb0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI bb14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI bb18 x19: x19 x20: x20
STACK CFI INIT bb50 4ac .cfa: sp 0 + .ra: x30
STACK CFI bb58 .cfa: sp 176 +
STACK CFI bb5c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bb64 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bb6c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bb74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI bdb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bdb8 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI bf08 x25: .cfa -16 + ^
STACK CFI bf80 x25: x25
STACK CFI bff8 x25: .cfa -16 + ^
STACK CFI INIT c000 34c .cfa: sp 0 + .ra: x30
STACK CFI c018 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c020 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c02c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c05c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c068 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI c084 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c08c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI c0f0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c1d8 x23: x23 x24: x24
STACK CFI c1dc x25: x25 x26: x26
STACK CFI c1e0 x27: x27 x28: x28
STACK CFI c1e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c1ec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI c244 x27: x27 x28: x28
STACK CFI c25c x23: x23 x24: x24
STACK CFI c260 x25: x25 x26: x26
STACK CFI c264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c26c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI c290 x23: x23 x24: x24
STACK CFI c294 x25: x25 x26: x26
STACK CFI c298 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c2a4 v8: .cfa -32 + ^
STACK CFI c2e0 v8: v8
STACK CFI c304 x27: x27 x28: x28
STACK CFI c308 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c324 x27: x27 x28: x28
STACK CFI c328 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c348 x27: x27 x28: x28
STACK CFI INIT c350 48 .cfa: sp 0 + .ra: x30
STACK CFI c360 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c368 x19: .cfa -16 + ^
STACK CFI c38c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c3a0 38 .cfa: sp 0 + .ra: x30
STACK CFI c3c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c3e0 130 .cfa: sp 0 + .ra: x30
STACK CFI c3e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c3f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c40c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c414 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c470 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c4c0 x19: x19 x20: x20
STACK CFI c4dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c4e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT c510 12c .cfa: sp 0 + .ra: x30
STACK CFI c518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c524 x19: .cfa -16 + ^
STACK CFI c5b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c5c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c608 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c640 2c .cfa: sp 0 + .ra: x30
STACK CFI c648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c650 x19: .cfa -16 + ^
STACK CFI c664 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c670 8c .cfa: sp 0 + .ra: x30
STACK CFI c678 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c688 x19: .cfa -16 + ^
STACK CFI c6b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c6c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c6cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c6d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c6f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c700 114 .cfa: sp 0 + .ra: x30
STACK CFI c708 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c710 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c71c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c728 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c734 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c740 x27: .cfa -16 + ^
STACK CFI c7cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI c7d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT c814 49c .cfa: sp 0 + .ra: x30
STACK CFI c81c .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI c824 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI c830 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI c83c x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI cc74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cc7c .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI cca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cca8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT ccb0 2d8 .cfa: sp 0 + .ra: x30
STACK CFI ccb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ccc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cd34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cd3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cd90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cd98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cf80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT cf90 7c .cfa: sp 0 + .ra: x30
STACK CFI cf98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cfa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cffc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d010 52c .cfa: sp 0 + .ra: x30
STACK CFI d018 .cfa: sp 224 +
STACK CFI d024 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d030 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d098 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d0a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d2f0 x21: x21 x22: x22
STACK CFI d2f4 x25: x25 x26: x26
STACK CFI d320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI d328 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI d410 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI d438 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d4a8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI d4e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d530 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI d534 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d538 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT d540 298 .cfa: sp 0 + .ra: x30
STACK CFI d548 .cfa: sp 128 +
STACK CFI d558 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d560 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d56c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d680 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d7e0 19c .cfa: sp 0 + .ra: x30
STACK CFI d7e8 .cfa: sp 80 +
STACK CFI d7f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d7fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d820 x21: .cfa -16 + ^
STACK CFI d8ac x21: x21
STACK CFI d8d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d8dc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d904 x21: .cfa -16 + ^
STACK CFI d91c x21: x21
STACK CFI d978 x21: .cfa -16 + ^
STACK CFI INIT d980 254 .cfa: sp 0 + .ra: x30
STACK CFI d988 .cfa: sp 160 +
STACK CFI d998 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d9a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d9d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d9dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d9e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d9f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI db20 x21: x21 x22: x22
STACK CFI db24 x23: x23 x24: x24
STACK CFI db28 x25: x25 x26: x26
STACK CFI db2c x27: x27 x28: x28
STACK CFI db54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db5c .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI dbc4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI dbc8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI dbcc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI dbd0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT dbd4 40 .cfa: sp 0 + .ra: x30
STACK CFI dbfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dc08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT dc14 b0 .cfa: sp 0 + .ra: x30
STACK CFI dc1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dc24 x19: .cfa -16 + ^
STACK CFI dc40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dc48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI dcac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI dcb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT dcc4 104 .cfa: sp 0 + .ra: x30
STACK CFI dccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dcd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI dd5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dd64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ddb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ddbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ddd0 194 .cfa: sp 0 + .ra: x30
STACK CFI ddd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI dde4 .cfa: sp 1104 + x23: .cfa -16 + ^
STACK CFI de14 x20: .cfa -40 + ^
STACK CFI de1c x21: .cfa -32 + ^
STACK CFI de24 x22: .cfa -24 + ^
STACK CFI de30 x19: .cfa -48 + ^
STACK CFI df14 x19: x19
STACK CFI df18 x20: x20
STACK CFI df1c x21: x21
STACK CFI df20 x22: x22
STACK CFI df40 .cfa: sp 64 +
STACK CFI df48 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI df50 .cfa: sp 1104 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI df54 x19: .cfa -48 + ^
STACK CFI df58 x20: .cfa -40 + ^
STACK CFI df5c x21: .cfa -32 + ^
STACK CFI df60 x22: .cfa -24 + ^
STACK CFI INIT df64 220 .cfa: sp 0 + .ra: x30
STACK CFI df6c .cfa: sp 80 +
STACK CFI df7c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI df88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e09c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI e0d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e0d8 x23: .cfa -16 + ^
STACK CFI e12c x21: x21 x22: x22
STACK CFI e130 x23: x23
STACK CFI e134 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI e160 x21: x21 x22: x22
STACK CFI e164 x23: x23
STACK CFI e170 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e174 x23: .cfa -16 + ^
STACK CFI e178 x21: x21 x22: x22 x23: x23
STACK CFI e17c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e180 x23: .cfa -16 + ^
STACK CFI INIT e184 1b8 .cfa: sp 0 + .ra: x30
STACK CFI e18c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e19c .cfa: sp 1120 + x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e1d4 x19: .cfa -64 + ^
STACK CFI e1dc x20: .cfa -56 + ^
STACK CFI e1e4 x21: .cfa -48 + ^
STACK CFI e1f0 x22: .cfa -40 + ^
STACK CFI e2dc x19: x19
STACK CFI e2e0 x20: x20
STACK CFI e2e4 x21: x21
STACK CFI e2e8 x22: x22
STACK CFI e308 .cfa: sp 80 +
STACK CFI e318 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e320 .cfa: sp 1120 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI e328 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI e32c x19: .cfa -64 + ^
STACK CFI e330 x20: .cfa -56 + ^
STACK CFI e334 x21: .cfa -48 + ^
STACK CFI e338 x22: .cfa -40 + ^
STACK CFI INIT e340 408 .cfa: sp 0 + .ra: x30
STACK CFI e348 .cfa: sp 192 +
STACK CFI e354 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e368 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e370 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e378 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e3e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e3f0 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI e400 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e5f0 x21: x21 x22: x22
STACK CFI e5f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e6a0 x21: x21 x22: x22
STACK CFI e6b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e724 x21: x21 x22: x22
STACK CFI e734 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e73c x21: x21 x22: x22
STACK CFI e744 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT e750 654 .cfa: sp 0 + .ra: x30
STACK CFI e758 .cfa: sp 96 +
STACK CFI e768 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e770 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e784 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI e910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e918 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI e994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e99c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI e9dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ea04 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI eabc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI eae4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI ec70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ec78 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT eda4 154 .cfa: sp 0 + .ra: x30
STACK CFI edac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI edb4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI edc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI edd4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI eddc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ee2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ee34 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI eef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT ef00 4b8 .cfa: sp 0 + .ra: x30
STACK CFI ef0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ef14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ef20 x21: .cfa -16 + ^
STACK CFI f06c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f074 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f0ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f0b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f0d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f0f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f168 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f18c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f248 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT f3c0 410 .cfa: sp 0 + .ra: x30
STACK CFI f3c8 .cfa: sp 368 +
STACK CFI f3d8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f3e0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f42c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f4a0 x21: x21 x22: x22
STACK CFI f4c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f4d0 .cfa: sp 368 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI f4e0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f52c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI f538 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f540 v8: .cfa -16 + ^
STACK CFI f5fc x23: x23 x24: x24
STACK CFI f600 x27: x27 x28: x28
STACK CFI f604 v8: v8
STACK CFI f61c x21: x21 x22: x22
STACK CFI f620 x25: x25 x26: x26
STACK CFI f624 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f65c v8: .cfa -16 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f764 x21: x21 x22: x22
STACK CFI f768 x23: x23 x24: x24
STACK CFI f76c x25: x25 x26: x26
STACK CFI f770 x27: x27 x28: x28
STACK CFI f774 v8: v8
STACK CFI f778 v8: .cfa -16 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f790 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f7ac x21: x21 x22: x22
STACK CFI f7b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f7b4 x25: x25 x26: x26
STACK CFI f7b8 x21: x21 x22: x22
STACK CFI f7bc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI f7c0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI f7c4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI f7c8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f7cc v8: .cfa -16 + ^
STACK CFI INIT f7d0 114 .cfa: sp 0 + .ra: x30
STACK CFI f7d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f7e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f7ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f7f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI f804 x25: .cfa -16 + ^
STACK CFI f87c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI f884 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI f8dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT f8e4 ec .cfa: sp 0 + .ra: x30
STACK CFI f8ec .cfa: sp 304 +
STACK CFI f8fc .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI f90c x19: .cfa -208 + ^
STACK CFI f9c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f9cc .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT f9d0 128 .cfa: sp 0 + .ra: x30
STACK CFI f9d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f9e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT fb00 17c .cfa: sp 0 + .ra: x30
STACK CFI fb08 .cfa: sp 112 +
STACK CFI fb0c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fb14 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI fb24 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI fc0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI fc14 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT fc80 4ac .cfa: sp 0 + .ra: x30
STACK CFI fc88 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fca4 .cfa: sp 1168 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ff50 .cfa: sp 96 +
STACK CFI ff68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ff70 .cfa: sp 1168 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10130 140 .cfa: sp 0 + .ra: x30
STACK CFI 10138 .cfa: sp 96 +
STACK CFI 10144 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1014c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10158 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10180 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 101e8 x23: x23 x24: x24
STACK CFI 10218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10220 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 10234 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10248 x23: x23 x24: x24
STACK CFI 10250 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10268 x23: x23 x24: x24
STACK CFI 1026c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 10270 70 .cfa: sp 0 + .ra: x30
STACK CFI 10278 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10280 x19: .cfa -16 + ^
STACK CFI 102bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 102c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 102e0 15c .cfa: sp 0 + .ra: x30
STACK CFI 102e8 .cfa: sp 64 +
STACK CFI 102f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10300 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10308 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 103c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 103c8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10440 dc .cfa: sp 0 + .ra: x30
STACK CFI 10448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10450 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 104f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 104fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10520 128 .cfa: sp 0 + .ra: x30
STACK CFI 10528 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10530 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1053c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 105a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 105b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 10620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10650 d4 .cfa: sp 0 + .ra: x30
STACK CFI 10658 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10664 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 106ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 106b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10724 284 .cfa: sp 0 + .ra: x30
STACK CFI 1072c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1073c .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 10864 .cfa: sp 48 +
STACK CFI 10870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10878 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 109b0 334 .cfa: sp 0 + .ra: x30
STACK CFI 109b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 109d8 .cfa: sp 5264 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10bec .cfa: sp 96 +
STACK CFI 10c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10c10 .cfa: sp 5264 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10ce4 5c .cfa: sp 0 + .ra: x30
STACK CFI 10cec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10cf4 x19: .cfa -16 + ^
STACK CFI 10d1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10d40 32c .cfa: sp 0 + .ra: x30
STACK CFI 10d48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10d50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10d58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10d60 x23: .cfa -16 + ^
STACK CFI 10fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10fe4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11070 104 .cfa: sp 0 + .ra: x30
STACK CFI 11078 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11080 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11088 x21: .cfa -16 + ^
STACK CFI 11128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11130 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11174 440 .cfa: sp 0 + .ra: x30
STACK CFI 1117c .cfa: sp 96 +
STACK CFI 11188 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11190 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11198 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11278 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1135c x25: .cfa -16 + ^
STACK CFI 11368 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 113e8 x23: x23 x24: x24 x25: x25
STACK CFI 11404 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1140c x23: x23 x24: x24
STACK CFI 1150c x25: x25
STACK CFI 1151c x25: .cfa -16 + ^
STACK CFI 1157c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11590 x23: x23 x24: x24
STACK CFI 11594 x25: x25
STACK CFI 1159c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 115a0 x25: .cfa -16 + ^
STACK CFI 115b0 x23: x23 x24: x24
STACK CFI INIT 115b4 64 .cfa: sp 0 + .ra: x30
STACK CFI 115bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 115c4 x19: .cfa -16 + ^
STACK CFI 115f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 115fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11620 158 .cfa: sp 0 + .ra: x30
STACK CFI 11628 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1163c x21: .cfa -16 + ^
STACK CFI 11660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11668 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 116b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 116b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11780 bc .cfa: sp 0 + .ra: x30
STACK CFI 11788 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11790 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11798 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 117ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 117fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11804 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 11840 114 .cfa: sp 0 + .ra: x30
STACK CFI 11848 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11850 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11864 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1194c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 11954 114 .cfa: sp 0 + .ra: x30
STACK CFI 1195c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11964 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11980 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 119c0 x21: x21 x22: x22
STACK CFI 119cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 119d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 119d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 119f0 x21: x21 x22: x22
STACK CFI 11a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11a70 104 .cfa: sp 0 + .ra: x30
STACK CFI 11a78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11a80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11abc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 11ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11ac8 x19: x19 x20: x20
STACK CFI 11ad8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 11ae0 .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11ae4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11af4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11b50 x19: x19 x20: x20
STACK CFI 11b58 x21: x21 x22: x22
STACK CFI 11b60 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 11b68 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11b6c x19: x19 x20: x20
STACK CFI 11b70 x21: x21 x22: x22
STACK CFI INIT 11b74 134 .cfa: sp 0 + .ra: x30
STACK CFI 11b7c .cfa: sp 128 +
STACK CFI 11b88 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11b90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11b9c x21: .cfa -16 + ^
STACK CFI 11c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11ca4 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11cb0 18 .cfa: sp 0 + .ra: x30
STACK CFI 11cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11cc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11cd0 68 .cfa: sp 0 + .ra: x30
STACK CFI 11cd8 .cfa: sp 48 +
STACK CFI 11cec .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11d2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11d34 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11d40 264 .cfa: sp 0 + .ra: x30
STACK CFI 11d48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11d50 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11d60 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11d70 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11e00 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11e28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11eb0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 11f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 11fa4 404 .cfa: sp 0 + .ra: x30
STACK CFI 11fac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11fc8 .cfa: sp 1168 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12288 .cfa: sp 96 +
STACK CFI 122a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 122a8 .cfa: sp 1168 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 123b0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 123b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 123c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 123d0 x21: .cfa -16 + ^
STACK CFI 12448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 12460 180 .cfa: sp 0 + .ra: x30
STACK CFI 12468 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12470 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12478 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 124c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 124d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1251c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12524 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 12588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12590 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 125d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 125e0 3c .cfa: sp 0 + .ra: x30
STACK CFI 125e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 125fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12608 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12620 140 .cfa: sp 0 + .ra: x30
STACK CFI 12628 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12630 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12638 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12644 x23: .cfa -16 + ^
STACK CFI 1266c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12674 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 12738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1274c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12760 3e0 .cfa: sp 0 + .ra: x30
STACK CFI 12768 .cfa: sp 96 +
STACK CFI 12774 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1277c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12794 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 127b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12888 x23: x23 x24: x24
STACK CFI 128b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 128c0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 129b4 x23: x23 x24: x24
STACK CFI 129d4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 129fc x23: x23 x24: x24
STACK CFI 12a60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12a90 x23: x23 x24: x24
STACK CFI 12ad8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12ae0 x23: x23 x24: x24
STACK CFI 12b3c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 12b40 d8 .cfa: sp 0 + .ra: x30
STACK CFI 12b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12b50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 12c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12c20 37c .cfa: sp 0 + .ra: x30
STACK CFI 12c28 .cfa: sp 192 +
STACK CFI 12c34 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12c3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12c44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12dcc .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 12fa0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 12fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12fb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1303c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13044 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 13068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13070 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13090 714 .cfa: sp 0 + .ra: x30
STACK CFI 13098 .cfa: sp 128 +
STACK CFI 130a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 130ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 130b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 130ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 130f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 130fc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 133a0 x23: x23 x24: x24
STACK CFI 133a4 x25: x25 x26: x26
STACK CFI 133a8 x27: x27 x28: x28
STACK CFI 133d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 133dc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 13448 x23: x23 x24: x24
STACK CFI 1344c x25: x25 x26: x26
STACK CFI 13450 x27: x27 x28: x28
STACK CFI 13468 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 134d8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13524 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13580 x23: x23 x24: x24
STACK CFI 13588 x25: x25 x26: x26
STACK CFI 1358c x27: x27 x28: x28
STACK CFI 13590 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13600 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1361c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13680 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 136a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1377c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13784 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13794 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13798 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1379c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 137a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 137a4 88 .cfa: sp 0 + .ra: x30
STACK CFI 137ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 137b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 137bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1380c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 13824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 13830 978 .cfa: sp 0 + .ra: x30
STACK CFI 13838 .cfa: sp 256 +
STACK CFI 13844 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1385c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 13868 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 13890 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 13898 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 138a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 139c0 x19: x19 x20: x20
STACK CFI 139c4 x21: x21 x22: x22
STACK CFI 139c8 x27: x27 x28: x28
STACK CFI 139f4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 139fc .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 14188 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 1418c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 14190 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14194 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 141b0 d10 .cfa: sp 0 + .ra: x30
STACK CFI 141b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 141e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 141ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 141f8 .cfa: sp 544 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 147d8 .cfa: sp 96 +
STACK CFI 147f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 147fc .cfa: sp 544 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 14ec0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 14ec8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14ed4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14ee0 x25: .cfa -16 + ^
STACK CFI 14ef4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14f5c x21: x21 x22: x22
STACK CFI 14fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 14fb0 dc .cfa: sp 0 + .ra: x30
STACK CFI 14fb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14fc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14fd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14fdc x23: .cfa -16 + ^
STACK CFI 15070 x21: x21 x22: x22
STACK CFI 15078 x23: x23
STACK CFI 15084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15090 90 .cfa: sp 0 + .ra: x30
STACK CFI 150a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15120 7c .cfa: sp 0 + .ra: x30
STACK CFI 15128 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15148 x19: .cfa -16 + ^
STACK CFI 15194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 151a0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 151a8 .cfa: sp 96 +
STACK CFI 151ac .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 151b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 151c0 x21: .cfa -16 + ^
STACK CFI 15238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 15240 e8 .cfa: sp 0 + .ra: x30
STACK CFI 15248 .cfa: sp 96 +
STACK CFI 1524c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15254 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15280 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15284 x21: .cfa -16 + ^
STACK CFI 15300 x21: x21
STACK CFI 15304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1530c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15330 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 15340 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15348 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 154b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 154c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 154d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 154f0 100 .cfa: sp 0 + .ra: x30
STACK CFI 154f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15500 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1555c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1558c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 155f0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 155f8 .cfa: sp 48 +
STACK CFI 15604 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1560c x19: .cfa -16 + ^
STACK CFI 15664 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1566c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 156b4 10c .cfa: sp 0 + .ra: x30
STACK CFI 156bc .cfa: sp 64 +
STACK CFI 156c8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 156d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 156fc x21: .cfa -16 + ^
STACK CFI 15728 x21: x21
STACK CFI 15760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15768 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 157bc x21: .cfa -16 + ^
STACK CFI INIT 157c0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 157c8 .cfa: sp 48 +
STACK CFI 157d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 157dc x19: .cfa -16 + ^
STACK CFI 15834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1583c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15884 104 .cfa: sp 0 + .ra: x30
STACK CFI 1588c .cfa: sp 64 +
STACK CFI 15898 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 158a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 158cc x21: .cfa -16 + ^
STACK CFI 15900 x21: x21
STACK CFI 15938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15940 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15984 x21: .cfa -16 + ^
STACK CFI INIT 15990 c0 .cfa: sp 0 + .ra: x30
STACK CFI 15998 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 159a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 159c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15a34 x21: x21 x22: x22
STACK CFI 15a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15a50 2c .cfa: sp 0 + .ra: x30
STACK CFI 15a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15a68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15a70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15a80 dc .cfa: sp 0 + .ra: x30
STACK CFI 15a88 .cfa: sp 48 +
STACK CFI 15a94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15a9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15b14 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15b60 e0 .cfa: sp 0 + .ra: x30
STACK CFI 15b68 .cfa: sp 272 +
STACK CFI 15b78 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 15b88 x19: .cfa -176 + ^
STACK CFI 15c34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 15c3c .cfa: sp 272 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x29: .cfa -192 + ^
STACK CFI INIT 15c40 70 .cfa: sp 0 + .ra: x30
STACK CFI 15c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15c60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15c7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15c84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15c98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15cb0 6c .cfa: sp 0 + .ra: x30
STACK CFI 15cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15cd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15ce8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15cf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15d04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15d20 c0 .cfa: sp 0 + .ra: x30
STACK CFI 15d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15d30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15d68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15d6c x21: .cfa -16 + ^
STACK CFI 15dc4 x21: x21
STACK CFI 15dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15dd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 15de0 38 .cfa: sp 0 + .ra: x30
STACK CFI 15de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15df8 x19: .cfa -16 + ^
STACK CFI 15e10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15e20 170 .cfa: sp 0 + .ra: x30
STACK CFI 15e28 .cfa: sp 80 +
STACK CFI 15e2c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15e34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15e3c x21: .cfa -16 + ^
STACK CFI 15f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 15f58 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 15f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 15f90 e0 .cfa: sp 0 + .ra: x30
STACK CFI 15f98 .cfa: sp 80 +
STACK CFI 15fa4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15fac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15fb4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1605c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16070 130 .cfa: sp 0 + .ra: x30
STACK CFI 16078 .cfa: sp 96 +
STACK CFI 16088 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16090 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1609c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1618c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 161a0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 161a8 .cfa: sp 64 +
STACK CFI 161bc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 161ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1620c x21: .cfa -16 + ^
STACK CFI 1624c x21: x21
STACK CFI 16288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 16290 1fc .cfa: sp 0 + .ra: x30
STACK CFI 16298 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 162a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 162ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 162b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 162c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 16400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 16408 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 16490 100 .cfa: sp 0 + .ra: x30
STACK CFI 16498 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 164a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 164bc x21: .cfa -16 + ^
STACK CFI 1652c x21: x21
STACK CFI 16530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16538 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 16560 x21: x21
STACK CFI 1656c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16574 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16590 7c .cfa: sp 0 + .ra: x30
STACK CFI 165ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 165bc x19: .cfa -16 + ^
STACK CFI 165e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 165f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 165fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16610 34c .cfa: sp 0 + .ra: x30
STACK CFI 16618 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 16624 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 16638 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 16644 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 16898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 168a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 16960 5c8 .cfa: sp 0 + .ra: x30
STACK CFI 16968 .cfa: sp 128 +
STACK CFI 16974 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1698c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 169a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 169b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 169bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16ab4 x19: x19 x20: x20
STACK CFI 16ab8 x21: x21 x22: x22
STACK CFI 16abc x23: x23 x24: x24
STACK CFI 16ac0 x25: x25 x26: x26
STACK CFI 16ac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16acc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 16b24 x27: .cfa -16 + ^
STACK CFI 16bac x27: x27
STACK CFI 16bd4 x19: x19 x20: x20
STACK CFI 16bd8 x21: x21 x22: x22
STACK CFI 16bdc x23: x23 x24: x24
STACK CFI 16be0 x25: x25 x26: x26
STACK CFI 16be4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16bec .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 16c44 x27: .cfa -16 + ^
STACK CFI 16c88 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 16ca8 x21: x21 x22: x22
STACK CFI 16cc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16cd0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 16cec x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 16d24 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16d48 x27: x27
STACK CFI 16d88 x21: x21 x22: x22
STACK CFI 16d90 x23: x23 x24: x24
STACK CFI 16d98 x19: x19 x20: x20
STACK CFI 16da0 x25: x25 x26: x26
STACK CFI 16da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16dac .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 16e2c x19: x19 x20: x20
STACK CFI 16e30 x21: x21 x22: x22
STACK CFI 16e34 x23: x23 x24: x24
STACK CFI 16e38 x25: x25 x26: x26
STACK CFI 16e3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16e44 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 16e54 x27: x27
STACK CFI 16e80 x27: .cfa -16 + ^
STACK CFI 16e84 x27: x27
STACK CFI 16ea4 x27: .cfa -16 + ^
STACK CFI 16eb8 x27: x27
STACK CFI 16ef4 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16ef8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16efc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16f00 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16f04 x27: .cfa -16 + ^
STACK CFI 16f08 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 16f0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16f10 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 16f14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16f18 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16f1c x27: .cfa -16 + ^
STACK CFI 16f20 x27: x27
STACK CFI 16f24 x27: .cfa -16 + ^
STACK CFI INIT 16f30 14c .cfa: sp 0 + .ra: x30
STACK CFI 16f38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16f40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16f48 x21: .cfa -16 + ^
STACK CFI 16fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16fb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 16fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 17074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 17080 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 17088 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17090 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 17194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 171a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 17234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1723c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 17264 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1726c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17274 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1727c x21: .cfa -16 + ^
STACK CFI 172fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17304 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17330 190 .cfa: sp 0 + .ra: x30
STACK CFI 17338 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17340 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1734c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1737c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17384 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 17414 x23: .cfa -16 + ^
STACK CFI 17468 x23: x23
STACK CFI 17480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17488 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 174c0 80 .cfa: sp 0 + .ra: x30
STACK CFI 174dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 174e4 x19: .cfa -16 + ^
STACK CFI 17504 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1750c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 17520 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17540 e8 .cfa: sp 0 + .ra: x30
STACK CFI 17548 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17554 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1755c x23: .cfa -16 + ^
STACK CFI 17620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 17630 1cc .cfa: sp 0 + .ra: x30
STACK CFI 17638 .cfa: sp 448 +
STACK CFI 17644 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1764c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17654 x21: .cfa -16 + ^
STACK CFI 17750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 17758 .cfa: sp 448 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17800 104 .cfa: sp 0 + .ra: x30
STACK CFI 17808 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17810 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1784c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17854 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17890 x21: .cfa -16 + ^
STACK CFI 178c0 x21: x21
STACK CFI 178c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 178d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 178ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 178f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17904 208 .cfa: sp 0 + .ra: x30
STACK CFI 1790c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1791c .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 179a4 .cfa: sp 48 +
STACK CFI 179ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 179b4 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17a04 x21: .cfa -16 + ^
STACK CFI 17a54 x21: x21
STACK CFI 17a6c x21: .cfa -16 + ^
STACK CFI 17aa4 x21: x21
STACK CFI 17aa8 x21: .cfa -16 + ^
STACK CFI 17ae8 x21: x21
STACK CFI 17aec x21: .cfa -16 + ^
STACK CFI 17b00 x21: x21
STACK CFI 17b08 x21: .cfa -16 + ^
STACK CFI INIT 17b10 380 .cfa: sp 0 + .ra: x30
STACK CFI 17b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17b20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17b64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17bb8 x21: x21 x22: x22
STACK CFI 17bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17bc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17c54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17c78 x21: x21 x22: x22
STACK CFI 17d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17d38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17dd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17e08 x21: x21 x22: x22
STACK CFI 17e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17e48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17e64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17e70 x21: x21 x22: x22
STACK CFI INIT 17e90 118 .cfa: sp 0 + .ra: x30
STACK CFI 17e98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17ea0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17f48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17f9c x21: x21 x22: x22
STACK CFI 17fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 17fb0 164 .cfa: sp 0 + .ra: x30
STACK CFI 17fb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17fc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17fe0 x21: .cfa -16 + ^
STACK CFI 18040 x21: x21
STACK CFI 18044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1804c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 180a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 180a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 180d0 x21: x21
STACK CFI 180d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 180dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 18114 33c .cfa: sp 0 + .ra: x30
STACK CFI 1811c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18124 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18160 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 181b4 x21: x21 x22: x22
STACK CFI 181b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 181c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 18208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18210 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 18220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18228 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 182ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 182e4 x21: x21 x22: x22
STACK CFI 18310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18324 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1834c x21: x21 x22: x22
STACK CFI 18350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18358 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 18408 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 18414 x21: x21 x22: x22
STACK CFI INIT 18450 158 .cfa: sp 0 + .ra: x30
STACK CFI 18458 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1846c .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 18528 .cfa: sp 48 +
STACK CFI 18534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1853c .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 185b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 185b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 185c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 185d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 185e4 x23: .cfa -16 + ^
STACK CFI 1864c x21: x21 x22: x22
STACK CFI 18650 x23: x23
STACK CFI 1865c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18664 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 18690 448 .cfa: sp 0 + .ra: x30
STACK CFI 18698 .cfa: sp 192 +
STACK CFI 186a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 186b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 186d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 18718 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1871c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 18720 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1890c x21: x21 x22: x22
STACK CFI 18910 x23: x23 x24: x24
STACK CFI 18914 x25: x25 x26: x26
STACK CFI 18940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 18948 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 18980 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 189b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 18a78 x21: x21 x22: x22
STACK CFI 18a7c x23: x23 x24: x24
STACK CFI 18a80 x25: x25 x26: x26
STACK CFI 18a94 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 18ac8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 18acc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 18ad0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 18ad4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 18ae0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 18ae8 .cfa: sp 64 +
STACK CFI 18af4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18b80 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18bc0 33c .cfa: sp 0 + .ra: x30
STACK CFI 18bc8 .cfa: sp 144 +
STACK CFI 18bcc .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 18bdc x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 18c24 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 18c38 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 18c48 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 18e2c x23: x23 x24: x24
STACK CFI 18e30 x25: x25 x26: x26
STACK CFI 18e34 x27: x27 x28: x28
STACK CFI 18e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18e40 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 18e64 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 18eb8 .cfa: sp 144 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 18ebc x23: x23 x24: x24
STACK CFI 18ec0 x25: x25 x26: x26
STACK CFI 18ec4 x27: x27 x28: x28
STACK CFI 18edc x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 18ee0 x23: x23 x24: x24
STACK CFI 18ee4 x25: x25 x26: x26
STACK CFI 18ee8 x27: x27 x28: x28
STACK CFI INIT 18f00 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 18f08 .cfa: sp 128 +
STACK CFI 18f14 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18f28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18fac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1902c x23: x23 x24: x24
STACK CFI 19060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19068 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 190cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 190d0 170 .cfa: sp 0 + .ra: x30
STACK CFI 190d8 .cfa: sp 64 +
STACK CFI 190e8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 190f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 190f8 x21: .cfa -16 + ^
STACK CFI 191a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 191a8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 19240 198 .cfa: sp 0 + .ra: x30
STACK CFI 19248 .cfa: sp 144 +
STACK CFI 19254 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1925c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 19268 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 192c8 x23: .cfa -16 + ^
STACK CFI 19360 x23: x23
STACK CFI 1938c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19394 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 193d4 x23: .cfa -16 + ^
STACK CFI INIT 193e0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 193e8 .cfa: sp 112 +
STACK CFI 193f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19400 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1940c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 194ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 194b4 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 195d0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 195d8 .cfa: sp 112 +
STACK CFI 195e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 195ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 195f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 19614 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 19638 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 196cc x23: x23 x24: x24
STACK CFI 196fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 19704 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1973c x23: x23 x24: x24
STACK CFI 19750 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 19784 x23: x23 x24: x24
STACK CFI 19788 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 19790 268 .cfa: sp 0 + .ra: x30
STACK CFI 19798 .cfa: sp 96 +
STACK CFI 197a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 197ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 197b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1985c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19864 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 19a00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 19a08 .cfa: sp 48 +
STACK CFI 19a14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19a1c x19: .cfa -16 + ^
STACK CFI 19a8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 19a94 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 19ac0 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 19ac8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19ae4 .cfa: sp 1136 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19e10 .cfa: sp 96 +
STACK CFI 19e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19e30 .cfa: sp 1136 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1a064 eb0 .cfa: sp 0 + .ra: x30
STACK CFI 1a06c .cfa: sp 112 +
STACK CFI 1a078 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a080 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1a088 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1a164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1a16c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 1a238 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a24c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a250 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a30c x21: x21 x22: x22
STACK CFI 1a310 x25: x25 x26: x26
STACK CFI 1a314 x27: x27 x28: x28
STACK CFI 1a318 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a324 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a350 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a404 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a430 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a464 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a498 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a4d8 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a524 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a558 x21: x21 x22: x22
STACK CFI 1a564 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a584 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a668 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a6c0 x21: x21 x22: x22
STACK CFI 1a6c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a6cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a6d0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1a6d4 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a720 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a72c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a7ec x21: x21 x22: x22
STACK CFI 1a7f0 x25: x25 x26: x26
STACK CFI 1a7f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a85c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 1a8f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a938 x21: x21 x22: x22
STACK CFI 1a990 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a9c0 x21: x21 x22: x22
STACK CFI 1aa3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1aa60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1aacc x21: x21 x22: x22
STACK CFI 1aad0 x25: x25 x26: x26
STACK CFI 1aad4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1ab40 x21: x21 x22: x22
STACK CFI 1ab44 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1abcc x25: x25 x26: x26
STACK CFI 1abe8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1abf4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1ac88 x27: x27 x28: x28
STACK CFI 1accc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1ad38 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ad6c x21: x21 x22: x22
STACK CFI 1ad70 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1adb4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1ade0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ae4c x21: x21 x22: x22
STACK CFI 1ae50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1ae84 x21: x21 x22: x22
STACK CFI 1ae88 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1aebc x21: x21 x22: x22
STACK CFI 1aec0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1af04 x21: x21 x22: x22
STACK CFI 1af0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
