MODULE Linux arm64 BAA6BDD4ED88565C8C1E30838CCD64B30 libcolord_sensor_scanner.so
INFO CODE_ID D4BDA6BA88ED5C568C1E30838CCD64B3A6D4715C
PUBLIC 16b4 0 cd_plugin_get_description
PUBLIC 16d4 0 cd_plugin_config_enabled
PUBLIC 16f0 0 cd_plugin_coldplug
PUBLIC 1790 0 cd_plugin_initialize
PUBLIC 1850 0 cd_plugin_destroy
STACK CFI INIT 1200 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1230 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1270 48 .cfa: sp 0 + .ra: x30
STACK CFI 1274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 127c x19: .cfa -16 + ^
STACK CFI 12b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d0 2fc .cfa: sp 0 + .ra: x30
STACK CFI 12d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1320 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 1348 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1358 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 135c x25: .cfa -16 + ^
STACK CFI 1598 x21: x21 x22: x22
STACK CFI 159c x23: x23 x24: x24
STACK CFI 15a0 x25: x25
STACK CFI 15a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15ac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 15d0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 15d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15f8 x21: .cfa -16 + ^
STACK CFI 1664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1670 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1698 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 16ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 16b4 20 .cfa: sp 0 + .ra: x30
STACK CFI 16bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16d4 1c .cfa: sp 0 + .ra: x30
STACK CFI 16dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 16f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1704 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1718 x21: .cfa -16 + ^
STACK CFI 1774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1790 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1798 .cfa: sp 64 +
STACK CFI 17a8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 183c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1844 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1850 38 .cfa: sp 0 + .ra: x30
STACK CFI 1858 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1860 x19: .cfa -16 + ^
STACK CFI 187c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
