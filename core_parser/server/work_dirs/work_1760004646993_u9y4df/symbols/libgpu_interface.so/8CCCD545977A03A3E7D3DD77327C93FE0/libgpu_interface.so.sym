MODULE Linux arm64 8CCCD545977A03A3E7D3DD77327C93FE0 libgpu_interface.so
INFO CODE_ID 45D5CC8C7A97A303E7D3DD77327C93FE
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 6d80 24 0 init_have_lse_atomics
6d80 4 45 0
6d84 4 46 0
6d88 4 45 0
6d8c 4 46 0
6d90 4 47 0
6d94 4 47 0
6d98 4 48 0
6d9c 4 47 0
6da0 4 48 0
PUBLIC 6730 0 _init
PUBLIC 6da4 0 call_weak_fn
PUBLIC 6dc0 0 deregister_tm_clones
PUBLIC 6df0 0 register_tm_clones
PUBLIC 6e30 0 __do_global_dtors_aux
PUBLIC 6e80 0 frame_dummy
PUBLIC 6e90 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::find(CUexternalMemory_st* const&) [clone .isra.0]
PUBLIC 6f20 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::find(CUexternalMemory_st* const&) [clone .isra.0]
PUBLIC 6fb0 0 lios::gpu::GpuCuda::GpuCuda()
PUBLIC 7000 0 lios::gpu::GpuCuda::GetBufAttrList(int, linvs::buf::BufAttrList&)
PUBLIC 7290 0 lios::gpu::GpuCuda::GetSignalAttrList(int, linvs::sync::SyncAttrList&)
PUBLIC 7300 0 lios::gpu::GpuCuda::GetWaitAttrList(int, linvs::sync::SyncAttrList&)
PUBLIC 7370 0 lios::gpu::GpuCuda::ImportMem(NvSciBufObjRefRec* const&, unsigned long)
PUBLIC 7440 0 lios::gpu::GpuCuda::ImportMem(linvs::buf::BufObj const&)
PUBLIC 76a0 0 lios::gpu::GpuCuda::GetGpuRawPtr(lios::gpu::GpuExternalMemory&, unsigned long)
PUBLIC 7770 0 lios::gpu::GpuCuda::RegisterSyncObj(linvs::sync::SyncObj const&, lios::gpu::GpuExternalSemaphore&)
PUBLIC 7840 0 lios::gpu::GpuCuda::InsertPerfence(linvs::sync::SyncFence&, lios::gpu::GpuExternalSemaphore&, lios::gpu::GpuStream&)
PUBLIC 7930 0 lios::gpu::GpuCuda::Instance(int)
PUBLIC 79c0 0 lios::gpu::GpuCuda::GpuCuda(int)
PUBLIC 7ac0 0 lios::gpu::GpuCuda::MapBuf(linvs::buf::BufObj const&, lios::camera::camera_nv::CudaMapInfo&)
PUBLIC 8070 0 lios::gpu::GpuCuda::MapBuf(linvs::buf::BufObj const&, lios::camera::camera_nv::CudaMapInfo*)
PUBLIC 8080 0 lios::gpu::GpuCuda::GetGpuRawPtr(linvs::buf::BufObj&)
PUBLIC 8310 0 lios::gpu::GpuCuda::DeepCopyAsync(linvs::buf::BufObj&, linvs::buf::BufObj&, lios::gpu::GpuStream&)
PUBLIC 8670 0 lios::gpu::GpuCuda::DeepCopySync(linvs::buf::BufObj&, linvs::buf::BufObj&, lios::gpu::GpuStream&)
PUBLIC 86b0 0 lios::gpu::GpuCuda::InitMipmap(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&)
PUBLIC 8ef0 0 lios::gpu::GpuCuda::InitLevel(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&)
PUBLIC 93b0 0 lios::gpu::GpuCuda::Bl2PlAsync(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 95a0 0 lios::gpu::GpuCuda::Bl2PlAsync(lios::camera::camera_nv::CudaMapInfo&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 95f0 0 lios::gpu::GpuCuda::Bl2PlSync(lios::gpu::GpuExternalMemory&, unsigned int, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > const&, std::vector<unsigned long, std::allocator<unsigned long> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, std::vector<unsigned int, std::allocator<unsigned int> > const&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 9650 0 lios::gpu::GpuCuda::Bl2PlSync(lios::camera::camera_nv::CudaMapInfo&, lios::gpu::GpuPtr&, lios::gpu::GpuStream&)
PUBLIC 96d0 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 96e0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 96f0 0 linvs::buf::BufAttrValues::~BufAttrValues()
PUBLIC 9860 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 9920 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 99f0 0 lios::camera::camera_nv::CudaMapInfo::~CudaMapInfo()
PUBLIC 9bb0 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::~_Hashtable()
PUBLIC 9c80 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::clear()
PUBLIC 9e10 0 lios::gpu::GpuCuda::~GpuCuda()
PUBLIC 9f00 0 std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> >::_M_default_append(unsigned long)
PUBLIC a230 0 std::vector<cudaArray*, std::allocator<cudaArray*> >::_M_default_append(unsigned long)
PUBLIC a3b0 0 void std::vector<unsigned int, std::allocator<unsigned int> >::_M_realloc_insert<unsigned int const&>(__gnu_cxx::__normal_iterator<unsigned int*, std::vector<unsigned int, std::allocator<unsigned int> > >, unsigned int const&)
PUBLIC a530 0 void std::vector<unsigned long, std::allocator<unsigned long> >::_M_realloc_insert<unsigned long const&>(__gnu_cxx::__normal_iterator<unsigned long*, std::vector<unsigned long, std::allocator<unsigned long> > >, unsigned long const&)
PUBLIC a6b0 0 void std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> >::_M_realloc_insert<NvSciBufAttrValColorFmt const&>(__gnu_cxx::__normal_iterator<NvSciBufAttrValColorFmt*, std::vector<NvSciBufAttrValColorFmt, std::allocator<NvSciBufAttrValColorFmt> > >, NvSciBufAttrValColorFmt const&)
PUBLIC a830 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC a960 0 std::__detail::_Map_base<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<lios::gpu::GpuMipmappedArray, std::allocator<lios::gpu::GpuMipmappedArray> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true>, true>::operator[](CUexternalMemory_st* const&)
PUBLIC ab60 0 std::_Hashtable<CUexternalMemory_st*, std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > >, std::allocator<std::pair<CUexternalMemory_st* const, std::vector<cudaArray*, std::allocator<cudaArray*> > > >, std::__detail::_Select1st, std::equal_to<CUexternalMemory_st*>, std::hash<CUexternalMemory_st*>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC ac90 0 lios::gpu::GpuExternalMemory::GpuExternalMemory()
PUBLIC ace0 0 lios::gpu::GpuExternalMemory::GpuExternalMemory(CUexternalMemory_st*&)
PUBLIC ad40 0 lios::gpu::GpuExternalMemory::operator bool() const
PUBLIC ad60 0 lios::gpu::GpuExternalMemory::operator*()
PUBLIC ad70 0 lios::gpu::GpuPtr::operator bool() const
PUBLIC ad90 0 lios::gpu::GpuPtr::operator*()
PUBLIC ada0 0 lios::gpu::GpuPtr::GetSize() const
PUBLIC adb0 0 lios::gpu::GpuExternalSemaphore::GpuExternalSemaphore(CUexternalSemaphore_st*&)
PUBLIC adc0 0 lios::gpu::GpuExternalSemaphore::~GpuExternalSemaphore()
PUBLIC ade0 0 lios::gpu::GpuStream::GpuStream()
PUBLIC ae30 0 lios::gpu::GpuStream::GpuStream(int)
PUBLIC aef0 0 lios::gpu::GpuStream::GpuStream(CUstream_st*&)
PUBLIC af50 0 lios::gpu::GpuStream::operator bool() const
PUBLIC af70 0 lios::gpu::GpuStream::operator*()
PUBLIC af80 0 lios::gpu::GpuStream::Sync()
PUBLIC afd0 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray()
PUBLIC b020 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray(cudaMipmappedArray*&)
PUBLIC b080 0 lios::gpu::GpuMipmappedArray::operator bool() const
PUBLIC b0a0 0 lios::gpu::GpuMipmappedArray::operator*()
PUBLIC b0b0 0 lios::gpu::GpuArray::GpuArray(cudaArray*&)
PUBLIC b0c0 0 lios::gpu::GpuArray::~GpuArray()
PUBLIC b0e0 0 lios::gpu::GpuPtr::GpuPtr(unsigned long)
PUBLIC b1d0 0 lios::gpu::GpuPtr::operator=(lios::gpu::GpuPtr const&)
PUBLIC b2d0 0 lios::gpu::GpuMipmappedArray::operator=(lios::gpu::GpuMipmappedArray const&)
PUBLIC b3d0 0 lios::gpu::GpuExternalMemory::operator=(lios::gpu::GpuExternalMemory const&)
PUBLIC b4d0 0 lios::gpu::GpuStream::operator=(lios::gpu::GpuStream const&)
PUBLIC b5d0 0 lios::gpu::GpuPtr::GpuPtr(lios::gpu::GpuPtr const&)
PUBLIC b6b0 0 lios::gpu::GpuMipmappedArray::GpuMipmappedArray(lios::gpu::GpuMipmappedArray const&)
PUBLIC b790 0 lios::gpu::GpuStream::GpuStream(lios::gpu::GpuStream const&)
PUBLIC b870 0 lios::gpu::GpuExternalMemory::GpuExternalMemory(lios::gpu::GpuExternalMemory const&)
PUBLIC b950 0 lios::gpu::GpuPtr::GpuPtr(void*, unsigned long, lios::gpu::GpuExternalMemory&)
PUBLIC b9f0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba00 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba10 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba20 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba30 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ba50 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba60 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba70 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba80 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ba90 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC bab0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC bad0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC bae0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC baf0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC bb00 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC bb70 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC bb80 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuStream::GpuStreamImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC bbf0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuExternalMemory::GpuExternalMemoryImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC bc60 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuMipmappedArray::GpuMipmappedArrayImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC bcd0 0 std::_Sp_counted_ptr_inplace<lios::gpu::GpuPtr::GpuPtrImpl, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC bd90 0 __aarch64_ldadd4_acq_rel
PUBLIC bdc0 0 _fini
STACK CFI INIT 6dc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6df0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e30 48 .cfa: sp 0 + .ra: x30
STACK CFI 6e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e3c x19: .cfa -16 + ^
STACK CFI 6e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e90 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f20 84 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fb0 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7000 284 .cfa: sp 0 + .ra: x30
STACK CFI 7004 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 7014 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 701c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7118 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI INIT 7290 68 .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 729c x19: .cfa -16 + ^
STACK CFI 72c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 72cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7300 68 .cfa: sp 0 + .ra: x30
STACK CFI 7304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 730c x19: .cfa -16 + ^
STACK CFI 7338 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 733c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7370 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7374 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7388 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7424 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI INIT 96f0 168 .cfa: sp 0 + .ra: x30
STACK CFI 96f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 96fc x19: .cfa -16 + ^
STACK CFI 9848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 984c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7440 258 .cfa: sp 0 + .ra: x30
STACK CFI 7444 .cfa: sp 528 +
STACK CFI 7454 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 745c x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 7468 x21: .cfa -496 + ^
STACK CFI 7654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7658 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x29: .cfa -528 + ^
STACK CFI INIT 76a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 76a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 76b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7748 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7770 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7774 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7788 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7804 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7840 e4 .cfa: sp 0 + .ra: x30
STACK CFI 7844 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 7858 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7868 x21: .cfa -176 + ^
STACK CFI 78f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 78f4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x29: .cfa -208 + ^
STACK CFI INIT 7930 90 .cfa: sp 0 + .ra: x30
STACK CFI 7934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 793c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7964 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 79a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9860 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 987c x19: .cfa -16 + ^
STACK CFI 98b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 98b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 98dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 98e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9910 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9920 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9930 x19: .cfa -16 + ^
STACK CFI 9970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9974 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 99c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 99c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 99d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 99d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 99f0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 99f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 99fc x19: .cfa -16 + ^
STACK CFI 9b24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9b64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9b70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9b88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9b9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ba0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9bb0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9bbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9bc4 x21: .cfa -16 + ^
STACK CFI 9c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9c80 188 .cfa: sp 0 + .ra: x30
STACK CFI 9c84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9c8c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9c98 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9ca0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9cac x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9cb8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9d8c x23: x23 x24: x24
STACK CFI 9d90 x25: x25 x26: x26
STACK CFI 9d94 x27: x27 x28: x28
STACK CFI 9db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9dbc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 79c0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 79c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9e10 ec .cfa: sp 0 + .ra: x30
STACK CFI 9e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9e1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9e28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9eec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9f00 330 .cfa: sp 0 + .ra: x30
STACK CFI 9f08 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9f10 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 9f18 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 9f20 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9f64 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 9f68 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9f74 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI a07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a080 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI a0a0 x27: x27 x28: x28
STACK CFI a0bc x25: x25 x26: x26
STACK CFI a0c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a0c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI a18c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a190 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI a194 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT a230 178 .cfa: sp 0 + .ra: x30
STACK CFI a238 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a240 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a250 x25: .cfa -16 + ^
STACK CFI a264 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a274 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a2ec x21: x21 x22: x22
STACK CFI a2f0 x23: x23 x24: x24
STACK CFI a2f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI a2fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a33c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI a344 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT a3b0 180 .cfa: sp 0 + .ra: x30
STACK CFI a3b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a3bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a3cc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a3d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a464 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT a530 180 .cfa: sp 0 + .ra: x30
STACK CFI a534 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a53c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a54c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a558 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a5e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a5e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT a6b0 180 .cfa: sp 0 + .ra: x30
STACK CFI a6b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a6bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a6cc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a6d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI a760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a764 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7ac0 5a8 .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 592 +
STACK CFI 7ad4 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 7adc x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 7af0 x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 7f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7f84 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^ x29: .cfa -592 + ^
STACK CFI INIT 8070 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8080 290 .cfa: sp 0 + .ra: x30
STACK CFI 8084 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 8094 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 80a0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 8250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8254 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x29: .cfa -368 + ^
STACK CFI INIT 8310 35c .cfa: sp 0 + .ra: x30
STACK CFI 8314 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8324 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8330 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8494 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8670 38 .cfa: sp 0 + .ra: x30
STACK CFI 8674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 867c x19: .cfa -16 + ^
STACK CFI 869c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 86a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a830 12c .cfa: sp 0 + .ra: x30
STACK CFI a834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a840 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a848 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a8ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a8f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a960 1fc .cfa: sp 0 + .ra: x30
STACK CFI a964 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a96c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a97c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI aa18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aa1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI aa24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aa8c x23: x23 x24: x24
STACK CFI aa98 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aae8 x23: x23 x24: x24
STACK CFI aaec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 86b0 834 .cfa: sp 0 + .ra: x30
STACK CFI 86b4 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 86cc x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 86d8 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 86e0 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 8948 v8: .cfa -208 + ^
STACK CFI 8aa4 v8: v8
STACK CFI 8ac8 v8: .cfa -208 + ^
STACK CFI 8b14 v8: v8
STACK CFI 8b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8b78 .cfa: sp 304 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI 8bbc v8: v8
STACK CFI 8bc4 v8: .cfa -208 + ^
STACK CFI 8d50 v8: v8
STACK CFI 8dac v8: .cfa -208 + ^
STACK CFI 8e04 v8: v8
STACK CFI 8e3c v8: .cfa -208 + ^
STACK CFI 8e48 v8: v8
STACK CFI 8e4c v8: .cfa -208 + ^
STACK CFI 8e50 v8: v8
STACK CFI 8e90 v8: .cfa -208 + ^
STACK CFI 8e9c v8: v8
STACK CFI 8ed4 v8: .cfa -208 + ^
STACK CFI 8ed8 v8: v8
STACK CFI 8ee0 v8: .cfa -208 + ^
STACK CFI INIT ab60 12c .cfa: sp 0 + .ra: x30
STACK CFI ab64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ab78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ac1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ac20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8ef0 4bc .cfa: sp 0 + .ra: x30
STACK CFI 8ef4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8f04 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8f10 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8f1c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8f2c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8f34 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 913c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9140 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 93b0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 93b4 .cfa: sp 128 +
STACK CFI 93b8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 93c0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 93d0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 93d8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 93e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 93ec x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 953c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9540 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 95a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 95a4 .cfa: sp 48 +
STACK CFI 95bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95f0 5c .cfa: sp 0 + .ra: x30
STACK CFI 95f4 .cfa: sp 64 +
STACK CFI 95f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9614 x19: .cfa -16 + ^
STACK CFI 9640 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9644 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9650 74 .cfa: sp 0 + .ra: x30
STACK CFI 9654 .cfa: sp 64 +
STACK CFI 966c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9678 x19: .cfa -16 + ^
STACK CFI 96b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 96bc .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b9f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba30 20 .cfa: sp 0 + .ra: x30
STACK CFI ba3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ba48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ba50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba90 20 .cfa: sp 0 + .ra: x30
STACK CFI ba9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI baa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bab0 20 .cfa: sp 0 + .ra: x30
STACK CFI babc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bac8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bad0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bae0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT baf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb00 70 .cfa: sp 0 + .ra: x30
STACK CFI bb04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb14 x19: .cfa -16 + ^
STACK CFI bb58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bb5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bb6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bb70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb80 70 .cfa: sp 0 + .ra: x30
STACK CFI bb84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb94 x19: .cfa -16 + ^
STACK CFI bbd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bbdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bbec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bbf0 70 .cfa: sp 0 + .ra: x30
STACK CFI bbf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc04 x19: .cfa -16 + ^
STACK CFI bc48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bc4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bc5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bc60 70 .cfa: sp 0 + .ra: x30
STACK CFI bc64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc74 x19: .cfa -16 + ^
STACK CFI bcb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bcbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac90 50 .cfa: sp 0 + .ra: x30
STACK CFI ac94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac9c x19: .cfa -16 + ^
STACK CFI acdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ace0 54 .cfa: sp 0 + .ra: x30
STACK CFI ace4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ad30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ad40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad70 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ada0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT adb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT adc0 20 .cfa: sp 0 + .ra: x30
STACK CFI adcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI add8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ade0 50 .cfa: sp 0 + .ra: x30
STACK CFI ade4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI adec x19: .cfa -16 + ^
STACK CFI ae2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae30 bc .cfa: sp 0 + .ra: x30
STACK CFI ae34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ae48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ae9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aed4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT aef0 54 .cfa: sp 0 + .ra: x30
STACK CFI aef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aefc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI af40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT af50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT af70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT af80 50 .cfa: sp 0 + .ra: x30
STACK CFI af84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI afa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI afa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT afd0 50 .cfa: sp 0 + .ra: x30
STACK CFI afd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI afdc x19: .cfa -16 + ^
STACK CFI b01c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b020 54 .cfa: sp 0 + .ra: x30
STACK CFI b024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b02c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b080 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b0a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b0b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b0c0 20 .cfa: sp 0 + .ra: x30
STACK CFI b0cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b0d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b0e0 e8 .cfa: sp 0 + .ra: x30
STACK CFI b0e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b0ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b0f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b15c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b198 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT bcd0 b8 .cfa: sp 0 + .ra: x30
STACK CFI bcd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bcdc x19: .cfa -16 + ^
STACK CFI bd38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bd3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bd60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bd6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bd84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b1d0 f4 .cfa: sp 0 + .ra: x30
STACK CFI b1d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b1dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b1e8 x21: .cfa -16 + ^
STACK CFI b268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b26c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b2d0 f4 .cfa: sp 0 + .ra: x30
STACK CFI b2d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b2dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b2e8 x21: .cfa -16 + ^
STACK CFI b368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b36c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b3d0 f4 .cfa: sp 0 + .ra: x30
STACK CFI b3d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b3dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3e8 x21: .cfa -16 + ^
STACK CFI b468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b46c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b4d0 f4 .cfa: sp 0 + .ra: x30
STACK CFI b4d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b4dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b4e8 x21: .cfa -16 + ^
STACK CFI b568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b56c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b5d0 d8 .cfa: sp 0 + .ra: x30
STACK CFI b5d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b5f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b618 x21: x21 x22: x22
STACK CFI b624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b628 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b6b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI b6b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b6bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b6d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b6f8 x21: x21 x22: x22
STACK CFI b704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b708 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b790 d8 .cfa: sp 0 + .ra: x30
STACK CFI b794 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b79c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b7b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b7d8 x21: x21 x22: x22
STACK CFI b7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b7e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b870 d8 .cfa: sp 0 + .ra: x30
STACK CFI b874 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b87c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b894 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b8b8 x21: x21 x22: x22
STACK CFI b8c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b8c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b950 94 .cfa: sp 0 + .ra: x30
STACK CFI b954 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b95c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b970 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b9c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b9cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT bd90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d80 24 .cfa: sp 0 + .ra: x30
STACK CFI 6d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d9c .cfa: sp 0 + .ra: .ra x29: x29
