MODULE Linux arm64 E061226A004D40F9ECAB795690DF5D1C0 libslam.so
INFO CODE_ID 6A2261E04D00F940ECAB795690DF5D1C
PUBLIC c98 0 _init
PUBLIC cf0 0 call_weak_fn
PUBLIC d10 0 deregister_tm_clones
PUBLIC d40 0 register_tm_clones
PUBLIC d80 0 __do_global_dtors_aux
PUBLIC dd0 0 frame_dummy
PUBLIC de0 0 fsd::slam::CoreAlgorithm::generateLocalMap()
PUBLIC df0 0 fsd::slam::CoreAlgorithm::appendToGlobalMap(fsd::slam::LocalMap const&)
PUBLIC e00 0 fsd::slam::CoreAlgorithm::largeScaleOptimize()
PUBLIC e10 0 fsd::slam::CoreAlgorithm::storeGlobalMap(fsd::slam::HdMap const&) const
PUBLIC e14 0 _fini
STACK CFI INIT d10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d80 48 .cfa: sp 0 + .ra: x30
STACK CFI d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d8c x19: .cfa -16 + ^
STACK CFI dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT df0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e10 4 .cfa: sp 0 + .ra: x30
