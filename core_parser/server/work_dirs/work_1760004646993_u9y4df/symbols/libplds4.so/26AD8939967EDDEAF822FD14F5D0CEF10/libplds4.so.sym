MODULE Linux arm64 26AD8939967EDDEAF822FD14F5D0CEF10 libplds4.so
INFO CODE_ID 3989AD267E96EADDF822FD14F5D0CEF11151A55F
PUBLIC ed0 0 PL_InitArenaPool
PUBLIC f74 0 PL_ArenaAllocate
PUBLIC 1080 0 PL_ArenaGrow
PUBLIC 1140 0 PL_ClearArenaPool
PUBLIC 1190 0 PL_ArenaRelease
PUBLIC 11e0 0 PL_FreeArenaPool
PUBLIC 11f0 0 PL_FinishArenaPool
PUBLIC 1200 0 PL_CompactArenaPool
PUBLIC 1210 0 PL_ArenaFinish
PUBLIC 1220 0 PL_SizeOfArenaPoolExcludingPool
PUBLIC 12e0 0 PL_NewHashTable
PUBLIC 1404 0 PL_HashTableDestroy
PUBLIC 14b0 0 PL_HashTableRawLookup
PUBLIC 1570 0 PL_HashTableRawLookupConst
PUBLIC 1604 0 PL_HashTableRawAdd
PUBLIC 1770 0 PL_HashTableAdd
PUBLIC 1840 0 PL_HashTableRawRemove
PUBLIC 1964 0 PL_HashTableRemove
PUBLIC 19d4 0 PL_HashTableLookup
PUBLIC 1a24 0 PL_HashTableLookupConst
PUBLIC 1a74 0 PL_HashTableEnumerateEntries
PUBLIC 1bb0 0 PL_HashTableDump
PUBLIC 1bc0 0 PL_HashString
PUBLIC 1bf0 0 PL_CompareStrings
PUBLIC 1c14 0 PL_CompareValues
PUBLIC 1c30 0 libVersionPoint
STACK CFI INIT db0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT de0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT e20 48 .cfa: sp 0 + .ra: x30
STACK CFI e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e2c x19: .cfa -16 + ^
STACK CFI e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e80 50 .cfa: sp 0 + .ra: x30
STACK CFI e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ea0 x21: .cfa -16 + ^
STACK CFI ec0 x21: x21
STACK CFI ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ed0 a4 .cfa: sp 0 + .ra: x30
STACK CFI ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f74 104 .cfa: sp 0 + .ra: x30
STACK CFI f7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f88 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fe0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1004 x21: .cfa -16 + ^
STACK CFI 1018 x21: x21
STACK CFI 1024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 102c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1068 x21: x21
STACK CFI 1070 x21: .cfa -16 + ^
STACK CFI INIT 1080 c0 .cfa: sp 0 + .ra: x30
STACK CFI 1094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 109c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 110c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1124 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1140 4c .cfa: sp 0 + .ra: x30
STACK CFI 1148 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1150 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1190 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1200 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1210 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1220 6c .cfa: sp 0 + .ra: x30
STACK CFI 1228 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1230 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 123c x21: .cfa -16 + ^
STACK CFI 1260 x21: x21
STACK CFI 126c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1274 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1290 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12e0 124 .cfa: sp 0 + .ra: x30
STACK CFI 12e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1300 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 130c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 13b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 13e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1404 ac .cfa: sp 0 + .ra: x30
STACK CFI 140c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1414 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 142c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 14b0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 14b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14dc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1570 94 .cfa: sp 0 + .ra: x30
STACK CFI 1578 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1580 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 158c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15b4 x23: .cfa -16 + ^
STACK CFI 15ec x23: x23
STACK CFI 15fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1604 164 .cfa: sp 0 + .ra: x30
STACK CFI 160c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1618 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1624 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1630 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1648 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1654 x27: .cfa -16 + ^
STACK CFI 1758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 1760 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1770 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1778 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1780 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1790 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 179c x23: .cfa -16 + ^
STACK CFI 1804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 180c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1840 124 .cfa: sp 0 + .ra: x30
STACK CFI 1848 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 192c x19: x19 x20: x20
STACK CFI 1934 x23: x23 x24: x24
STACK CFI 1938 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1948 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 194c x19: x19 x20: x20
STACK CFI 1954 x23: x23 x24: x24
STACK CFI 195c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 1964 70 .cfa: sp 0 + .ra: x30
STACK CFI 196c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1974 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 19c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 19cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 19d4 50 .cfa: sp 0 + .ra: x30
STACK CFI 19dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a24 50 .cfa: sp 0 + .ra: x30
STACK CFI 1a2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a74 138 .cfa: sp 0 + .ra: x30
STACK CFI 1a7c .cfa: sp 96 +
STACK CFI 1a88 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1a94 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a9c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1aa8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1b84 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bc0 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bf0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1bf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c14 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c30 10 .cfa: sp 0 + .ra: x30
