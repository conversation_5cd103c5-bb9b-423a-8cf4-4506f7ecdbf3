MODULE Linux arm64 5CEEB3D6FF21CD7E73ABA7B64405D2A50 libpipewire-module-echo-cancel.so
INFO CODE_ID D6B3EE5C21FF7ECD73ABA7B64405D2A5FA70AF5D
PUBLIC c000 0 pipewire__module_init
STACK CFI INIT 24b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 48 .cfa: sp 0 + .ra: x30
STACK CFI 2524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 252c x19: .cfa -16 + ^
STACK CFI 2564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2580 340 .cfa: sp 0 + .ra: x30
STACK CFI 2588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2674 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2680 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 27c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 281c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 289c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 28c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28d0 x19: .cfa -16 + ^
STACK CFI 2908 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2910 50 .cfa: sp 0 + .ra: x30
STACK CFI 2918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2920 x19: .cfa -16 + ^
STACK CFI 2958 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2960 50 .cfa: sp 0 + .ra: x30
STACK CFI 2968 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2970 x19: .cfa -16 + ^
STACK CFI 29a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29b0 58 .cfa: sp 0 + .ra: x30
STACK CFI 29b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29c0 x19: .cfa -16 + ^
STACK CFI 2a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a10 de8 .cfa: sp 0 + .ra: x30
STACK CFI 2a18 .cfa: sp 352 +
STACK CFI 2a2c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 2a34 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 2a44 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 2a50 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 2a58 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 2e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e28 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 3800 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 3808 .cfa: sp 480 +
STACK CFI 381c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3834 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 383c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3854 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4000 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4008 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6490 a38 .cfa: sp 0 + .ra: x30
STACK CFI 6498 .cfa: sp 160 +
STACK CFI 649c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 64a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 64bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 6b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6b14 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6ed0 328 .cfa: sp 0 + .ra: x30
STACK CFI 6ed8 .cfa: sp 96 +
STACK CFI 6edc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ee4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ef8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 707c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7084 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7200 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 7208 .cfa: sp 224 +
STACK CFI 7214 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 721c x19: .cfa -16 + ^
STACK CFI 7390 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7398 .cfa: sp 224 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 73a4 c8 .cfa: sp 0 + .ra: x30
STACK CFI 73ac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 73b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 73c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7430 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7470 108 .cfa: sp 0 + .ra: x30
STACK CFI 7478 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7488 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7490 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7580 54 .cfa: sp 0 + .ra: x30
STACK CFI 7588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7590 x19: .cfa -16 + ^
STACK CFI 75cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75d4 198 .cfa: sp 0 + .ra: x30
STACK CFI 75dc .cfa: sp 96 +
STACK CFI 75e0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 764c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 76e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76f0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7770 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 7778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7780 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 77c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 77f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7944 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b40 154 .cfa: sp 0 + .ra: x30
STACK CFI 7b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b50 x19: .cfa -16 + ^
STACK CFI 7b9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7be4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bfc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c94 160 .cfa: sp 0 + .ra: x30
STACK CFI 7c9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7df4 3d8 .cfa: sp 0 + .ra: x30
STACK CFI 7dfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7fd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 80b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 81d0 308 .cfa: sp 0 + .ra: x30
STACK CFI 81d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 81e4 .cfa: sp 1360 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8224 x19: .cfa -64 + ^
STACK CFI 822c x20: .cfa -56 + ^
STACK CFI 823c x23: .cfa -32 + ^
STACK CFI 8244 x24: .cfa -24 + ^
STACK CFI 8390 x19: x19
STACK CFI 8394 x20: x20
STACK CFI 8398 x23: x23
STACK CFI 839c x24: x24
STACK CFI 83bc .cfa: sp 80 +
STACK CFI 83c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 83cc .cfa: sp 1360 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 83d8 x25: .cfa -16 + ^
STACK CFI 8430 x25: x25
STACK CFI 8434 x25: .cfa -16 + ^
STACK CFI 844c x25: x25
STACK CFI 84c0 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 84c4 x19: .cfa -64 + ^
STACK CFI 84c8 x20: .cfa -56 + ^
STACK CFI 84cc x23: .cfa -32 + ^
STACK CFI 84d0 x24: .cfa -24 + ^
STACK CFI 84d4 x25: .cfa -16 + ^
STACK CFI INIT 84e0 33c .cfa: sp 0 + .ra: x30
STACK CFI 84e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 84f4 .cfa: sp 1360 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8534 x19: .cfa -64 + ^
STACK CFI 853c x20: .cfa -56 + ^
STACK CFI 854c x23: .cfa -32 + ^
STACK CFI 8554 x24: .cfa -24 + ^
STACK CFI 86a4 x19: x19
STACK CFI 86a8 x20: x20
STACK CFI 86ac x23: x23
STACK CFI 86b0 x24: x24
STACK CFI 86d0 .cfa: sp 80 +
STACK CFI 86d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 86e0 .cfa: sp 1360 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 86e4 x19: x19
STACK CFI 86e8 x20: x20
STACK CFI 86ec x23: x23
STACK CFI 86f0 x24: x24
STACK CFI 86f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 877c x19: x19
STACK CFI 8780 x20: x20
STACK CFI 8784 x23: x23
STACK CFI 8788 x24: x24
STACK CFI 878c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8790 x25: .cfa -16 + ^
STACK CFI 87e8 x25: x25
STACK CFI 87ec x25: .cfa -16 + ^
STACK CFI 8804 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 8808 x19: .cfa -64 + ^
STACK CFI 880c x20: .cfa -56 + ^
STACK CFI 8810 x23: .cfa -32 + ^
STACK CFI 8814 x24: .cfa -24 + ^
STACK CFI 8818 x25: .cfa -16 + ^
STACK CFI INIT 8820 18c .cfa: sp 0 + .ra: x30
STACK CFI 8828 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8838 .cfa: sp 1152 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 895c .cfa: sp 48 +
STACK CFI 8968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8970 .cfa: sp 1152 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 89b0 64 .cfa: sp 0 + .ra: x30
STACK CFI 89b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 89dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 89e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 89ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 89f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a14 13c .cfa: sp 0 + .ra: x30
STACK CFI 8a1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a2c x21: .cfa -16 + ^
STACK CFI 8b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8b48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8b50 50 .cfa: sp 0 + .ra: x30
STACK CFI 8b58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b60 x19: .cfa -16 + ^
STACK CFI 8b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ba0 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 8ba8 .cfa: sp 448 +
STACK CFI 8bb8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8bcc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8bd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8be4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8d50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d58 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9060 e0 .cfa: sp 0 + .ra: x30
STACK CFI 9068 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9070 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 908c x21: .cfa -16 + ^
STACK CFI 90d4 x21: x21
STACK CFI 90dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 90f0 x21: x21
STACK CFI 90fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9104 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9140 4c .cfa: sp 0 + .ra: x30
STACK CFI 9148 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9150 x19: .cfa -16 + ^
STACK CFI 9184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9190 17c .cfa: sp 0 + .ra: x30
STACK CFI 9198 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 91b4 .cfa: sp 4208 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 91f0 x22: .cfa -56 + ^
STACK CFI 9204 x21: .cfa -64 + ^
STACK CFI 9294 x21: x21
STACK CFI 9298 x22: x22
STACK CFI 92bc .cfa: sp 96 +
STACK CFI 92d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 92dc .cfa: sp 4208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 92f8 x21: x21 x22: x22
STACK CFI 9304 x21: .cfa -64 + ^
STACK CFI 9308 x22: .cfa -56 + ^
STACK CFI INIT 9310 190 .cfa: sp 0 + .ra: x30
STACK CFI 9318 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9334 .cfa: sp 4208 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9378 x22: .cfa -56 + ^
STACK CFI 9390 x21: .cfa -64 + ^
STACK CFI 9428 x21: x21
STACK CFI 942c x22: x22
STACK CFI 9450 .cfa: sp 96 +
STACK CFI 9468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9470 .cfa: sp 4208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 948c x21: x21 x22: x22
STACK CFI 9498 x21: .cfa -64 + ^
STACK CFI 949c x22: .cfa -56 + ^
STACK CFI INIT 94a0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 94a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 94c4 .cfa: sp 4208 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 950c x19: .cfa -80 + ^
STACK CFI 951c x20: .cfa -72 + ^
STACK CFI 95cc x19: x19
STACK CFI 95d0 x20: x20
STACK CFI 95f4 .cfa: sp 96 +
STACK CFI 960c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9614 .cfa: sp 4208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9630 x19: x19 x20: x20
STACK CFI 963c x19: .cfa -80 + ^
STACK CFI 9640 x20: .cfa -72 + ^
STACK CFI INIT 9644 18c .cfa: sp 0 + .ra: x30
STACK CFI 964c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9668 .cfa: sp 4208 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96ac x22: .cfa -56 + ^
STACK CFI 96c4 x21: .cfa -64 + ^
STACK CFI 9758 x21: x21
STACK CFI 975c x22: x22
STACK CFI 9780 .cfa: sp 96 +
STACK CFI 9798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 97a0 .cfa: sp 4208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 97bc x21: x21 x22: x22
STACK CFI 97c8 x21: .cfa -64 + ^
STACK CFI 97cc x22: .cfa -56 + ^
STACK CFI INIT 97d0 188 .cfa: sp 0 + .ra: x30
STACK CFI 97d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 97f4 .cfa: sp 4208 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9830 x22: .cfa -56 + ^
STACK CFI 984c x21: .cfa -64 + ^
STACK CFI 98e0 x21: x21
STACK CFI 98e4 x22: x22
STACK CFI 9908 .cfa: sp 96 +
STACK CFI 9920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9928 .cfa: sp 4208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9944 x21: x21 x22: x22
STACK CFI 9950 x21: .cfa -64 + ^
STACK CFI 9954 x22: .cfa -56 + ^
STACK CFI INIT 9960 344 .cfa: sp 0 + .ra: x30
STACK CFI 9968 .cfa: sp 96 +
STACK CFI 9978 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9984 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9994 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9a68 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9ca4 284 .cfa: sp 0 + .ra: x30
STACK CFI 9cac .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9cb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9cc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9de4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9de8 x23: .cfa -16 + ^
STACK CFI 9e60 x23: x23
STACK CFI 9e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9e80 x23: .cfa -16 + ^
STACK CFI 9e98 x23: x23
STACK CFI 9e9c x23: .cfa -16 + ^
STACK CFI 9ea4 x23: x23
STACK CFI 9ea8 x23: .cfa -16 + ^
STACK CFI 9eb0 x23: x23
STACK CFI 9eb4 x23: .cfa -16 + ^
STACK CFI 9ebc x23: x23
STACK CFI 9ec0 x23: .cfa -16 + ^
STACK CFI 9ec8 x23: x23
STACK CFI 9ecc x23: .cfa -16 + ^
STACK CFI 9ed4 x23: x23
STACK CFI 9ed8 x23: .cfa -16 + ^
STACK CFI 9ee0 x23: x23
STACK CFI 9ee4 x23: .cfa -16 + ^
STACK CFI 9eec x23: x23
STACK CFI 9ef0 x23: .cfa -16 + ^
STACK CFI 9ef8 x23: x23
STACK CFI 9efc x23: .cfa -16 + ^
STACK CFI 9f04 x23: x23
STACK CFI 9f08 x23: .cfa -16 + ^
STACK CFI 9f10 x23: x23
STACK CFI 9f14 x23: .cfa -16 + ^
STACK CFI 9f1c x23: x23
STACK CFI 9f20 x23: .cfa -16 + ^
STACK CFI INIT 9f30 274 .cfa: sp 0 + .ra: x30
STACK CFI 9f38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9f3c .cfa: x29 96 +
STACK CFI 9f40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9f50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9f58 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9f64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9f70 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a09c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a0a4 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a1a4 be0 .cfa: sp 0 + .ra: x30
STACK CFI a1ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a1b0 .cfa: x29 96 +
STACK CFI a1b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a1cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a984 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT ad84 338 .cfa: sp 0 + .ra: x30
STACK CFI ad8c .cfa: sp 144 +
STACK CFI ad90 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ad98 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI adac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI adb4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI adbc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI adc0 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI af38 x19: x19 x20: x20
STACK CFI af3c x21: x21 x22: x22
STACK CFI af40 x23: x23 x24: x24
STACK CFI af44 x27: x27 x28: x28
STACK CFI af50 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI af58 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI affc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b024 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b02c .cfa: sp 144 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI b054 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b07c .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT b0c0 34c .cfa: sp 0 + .ra: x30
STACK CFI b0c8 .cfa: sp 144 +
STACK CFI b0cc .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b0d4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b0e8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b0f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b0f8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b0fc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b288 x19: x19 x20: x20
STACK CFI b28c x21: x21 x22: x22
STACK CFI b290 x23: x23 x24: x24
STACK CFI b294 x27: x27 x28: x28
STACK CFI b2a0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b2a8 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI b34c x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b374 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b37c .cfa: sp 144 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI b3a4 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b3cc .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT b410 f4 .cfa: sp 0 + .ra: x30
STACK CFI b418 .cfa: sp 352 +
STACK CFI b428 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI b438 x19: .cfa -192 + ^
STACK CFI b4f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b4f8 .cfa: sp 352 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT b504 4a4 .cfa: sp 0 + .ra: x30
STACK CFI b50c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b518 .cfa: sp 1744 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b540 x23: .cfa -48 + ^
STACK CFI b544 x24: .cfa -40 + ^
STACK CFI b560 x27: .cfa -16 + ^
STACK CFI b568 x28: .cfa -8 + ^
STACK CFI b5b4 x27: x27
STACK CFI b5b8 x28: x28
STACK CFI b640 x23: x23
STACK CFI b644 x24: x24
STACK CFI b664 .cfa: sp 96 +
STACK CFI b66c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b674 .cfa: sp 1744 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b678 x23: x23
STACK CFI b67c x24: x24
STACK CFI b6c0 .cfa: sp 96 +
STACK CFI b6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b6f8 .cfa: sp 1744 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI b710 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b720 x21: .cfa -64 + ^
STACK CFI b724 x22: .cfa -56 + ^
STACK CFI b738 x25: .cfa -32 + ^
STACK CFI b740 x26: .cfa -24 + ^
STACK CFI b7f0 x21: x21
STACK CFI b7f4 x22: x22
STACK CFI b7f8 x25: x25
STACK CFI b7fc x26: x26
STACK CFI b808 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b968 x21: x21
STACK CFI b96c x22: x22
STACK CFI b970 x25: x25
STACK CFI b974 x26: x26
STACK CFI b978 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b97c x21: x21
STACK CFI b980 x22: x22
STACK CFI b984 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b988 x21: .cfa -64 + ^
STACK CFI b98c x22: .cfa -56 + ^
STACK CFI b990 x23: .cfa -48 + ^
STACK CFI b994 x24: .cfa -40 + ^
STACK CFI b998 x25: .cfa -32 + ^
STACK CFI b99c x26: .cfa -24 + ^
STACK CFI b9a0 x27: .cfa -16 + ^
STACK CFI b9a4 x28: .cfa -8 + ^
STACK CFI INIT b9b0 60 .cfa: sp 0 + .ra: x30
STACK CFI b9b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b9e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b9e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b9ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b9f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b9fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ba04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ba08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ba10 5f0 .cfa: sp 0 + .ra: x30
STACK CFI ba18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba28 .cfa: sp 6288 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba6c x21: .cfa -32 + ^
STACK CFI ba80 x22: .cfa -24 + ^
STACK CFI baf4 x23: .cfa -16 + ^
STACK CFI bafc x24: .cfa -8 + ^
STACK CFI bf20 x21: x21
STACK CFI bf24 x22: x22
STACK CFI bf28 x23: x23
STACK CFI bf2c x24: x24
STACK CFI bf54 .cfa: sp 64 +
STACK CFI bf5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf64 .cfa: sp 6288 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI bf68 x21: x21
STACK CFI bf6c x22: x22
STACK CFI bf70 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bf94 x21: x21
STACK CFI bf98 x22: x22
STACK CFI bf9c x23: x23
STACK CFI bfa0 x24: x24
STACK CFI bfa4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bfd8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bfdc x21: x21
STACK CFI bfe0 x22: x22
STACK CFI bfe4 x23: x23
STACK CFI bfe8 x24: x24
STACK CFI bff0 x21: .cfa -32 + ^
STACK CFI bff4 x22: .cfa -24 + ^
STACK CFI bff8 x23: .cfa -16 + ^
STACK CFI bffc x24: .cfa -8 + ^
STACK CFI INIT c000 1530 .cfa: sp 0 + .ra: x30
STACK CFI c008 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c018 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c020 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c034 .cfa: sp 512 + x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c660 .cfa: sp 96 +
STACK CFI c67c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c684 .cfa: sp 512 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
