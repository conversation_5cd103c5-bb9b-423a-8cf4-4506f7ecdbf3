MODULE Linux arm64 82118CB30C3239DEB8F2AC3CEF76891E0 libboost_prg_exec_monitor.so.1.77.0
INFO CODE_ID B38C1182320CDE39B8F2AC3CEF76891E
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 50c0 24 0 init_have_lse_atomics
50c0 4 45 0
50c4 4 46 0
50c8 4 45 0
50cc 4 46 0
50d0 4 47 0
50d4 4 47 0
50d8 4 48 0
50dc 4 47 0
50e0 4 48 0
PUBLIC 4160 0 _init
PUBLIC 47c0 0 boost::detail::signal_action::~signal_action() [clone .part.0]
PUBLIC 47d0 0 boost::detail::report_error(boost::execution_exception::error_code, boost::exception const*, char const*, ...) [clone .constprop.0]
PUBLIC 4860 0 boost::wrapexcept<boost::bad_function_call>::rethrow() const
PUBLIC 495c 0 boost::exception const* boost::current_exception_cast<boost::exception const>()
PUBLIC 49a0 0 void boost::unit_test::ut_detail::throw_exception<boost::system_error>(boost::system_error const&)
PUBLIC 49d0 0 void boost::throw_exception<boost::bad_function_call>(boost::bad_function_call const&)
PUBLIC 4a48 0 std::basic_ostream<char, std::char_traits<char> >& std::endl<char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&) [clone .isra.0]
PUBLIC 4ad0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 4be0 0 boost::enable_if_<!boost::is_integral<void (*)(boost::debug::dbg_startup_info const&)>::value, boost::function<void (boost::debug::dbg_startup_info const&)>&>::type boost::function<void (boost::debug::dbg_startup_info const&)>::operator=<void (*)(boost::debug::dbg_startup_info const&)>(void (*)(boost::debug::dbg_startup_info const&)) [clone .isra.0]
PUBLIC 4ce0 0 boost::debug::(anonymous namespace)::info_t::info_t() [clone .constprop.0]
PUBLIC 5090 0 _GLOBAL__sub_I_debug.cpp
PUBLIC 50e4 0 call_weak_fn
PUBLIC 5100 0 deregister_tm_clones
PUBLIC 5130 0 register_tm_clones
PUBLIC 5170 0 __do_global_dtors_aux
PUBLIC 51c0 0 frame_dummy
PUBLIC 51d0 0 boost_execution_monitor_jumping_signal_handler
PUBLIC 5200 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_erase(std::_Rb_tree_node<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >*) [clone .isra.0]
PUBLIC 52f0 0 boost::detail::signal_action::signal_action()
PUBLIC 5300 0 boost::detail::signal_action::~signal_action()
PUBLIC 5320 0 boost::detail::signal_handler::~signal_handler()
PUBLIC 56c0 0 boost::execution_monitor::execution_monitor()
PUBLIC 56e0 0 boost::system_error::system_error(char const*)
PUBLIC 5710 0 boost::execution_exception::execution_exception(boost::execution_exception::error_code, boost::unit_test::basic_cstring<char const>, boost::execution_exception::location const&)
PUBLIC 5780 0 boost::execution_exception::location::location(char const*, unsigned long, char const*)
PUBLIC 57f0 0 boost::detail::report_error(boost::execution_exception::error_code, boost::exception const*, char const*, std::__va_list*)
PUBLIC 5920 0 boost::detail::report_error(boost::execution_exception::error_code, char const*, ...)
PUBLIC 59c0 0 boost::detail::system_signal_exception::report() const
PUBLIC 5e30 0 boost::execution_exception::location::location(boost::unit_test::basic_cstring<char const>, unsigned long, char const*)
PUBLIC 5e80 0 boost::fpe::enable(unsigned int)
PUBLIC 5ec0 0 boost::fpe::disable(unsigned int)
PUBLIC 5f00 0 boost::detail::signal_action::signal_action(int, bool, bool, char*) [clone .part.0]
PUBLIC 6040 0 boost_execution_monitor_attaching_signal_handler
PUBLIC 6110 0 boost::detail::signal_handler::signal_handler(bool, bool, unsigned long, bool, char*)
PUBLIC 6590 0 boost::detail::signal_action::signal_action(int, bool, bool, char*)
PUBLIC 66f0 0 boost::execution_monitor::catch_signals(boost::function<int ()> const&)
PUBLIC 6980 0 boost::execution_monitor::execute(boost::function<int ()> const&)
PUBLIC 7500 0 boost::execution_monitor::vexecute(boost::function<void ()> const&)
PUBLIC 75e0 0 boost::detail::sp_counted_base::destroy()
PUBLIC 75f0 0 std::ctype<char>::do_widen(char) const
PUBLIC 7600 0 boost::exception_detail::error_info_container_impl::add_ref() const
PUBLIC 7610 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 7620 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::dispose()
PUBLIC 7640 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_deleter(std::type_info const&)
PUBLIC 7650 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_local_deleter(std::type_info const&)
PUBLIC 7660 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::get_untyped_deleter()
PUBLIC 7670 0 boost::detail::sp_counted_impl_p<boost::exception_detail::error_info_base>::~sp_counted_impl_p()
PUBLIC 7680 0 boost::bad_function_call::~bad_function_call()
PUBLIC 76a0 0 boost::bad_function_call::~bad_function_call()
PUBLIC 76e0 0 boost::detail::function::functor_manager<boost::detail::forward>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC 7780 0 boost::exception_detail::error_info_container_impl::release() const
PUBLIC 7800 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 79e0 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7bc0 0 boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7cd0 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 7e90 0 non-virtual thunk to boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 8050 0 boost::wrapexcept<boost::bad_function_call>::~wrapexcept()
PUBLIC 8130 0 boost::exception_detail::error_info_container_impl::get(boost::exception_detail::type_info_ const&) const
PUBLIC 8240 0 boost::wrapexcept<boost::bad_function_call>::clone() const
PUBLIC 86e0 0 boost::detail::sp_counted_base::release()
PUBLIC 87a0 0 boost::core::demangle[abi:cxx11](char const*)
PUBLIC 8920 0 boost::exception_detail::error_info_container_impl::diagnostic_information(char const*) const
PUBLIC 8cc0 0 boost::exception_detail::diagnostic_information_impl[abi:cxx11](boost::exception const*, std::exception const*, bool, bool)
PUBLIC 9920 0 boost::detail::function::function_obj_invoker0<boost::detail::forward, int>::invoke(boost::detail::function::function_buffer&)
PUBLIC 9a10 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_unique_pos(boost::exception_detail::type_info_ const&)
PUBLIC 9b60 0 std::_Rb_tree<boost::exception_detail::type_info_, std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> >, std::_Select1st<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, std::less<boost::exception_detail::type_info_>, std::allocator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<boost::exception_detail::type_info_ const, boost::shared_ptr<boost::exception_detail::error_info_base> > >, boost::exception_detail::type_info_ const&)
PUBLIC 9d80 0 boost::exception_detail::error_info_container_impl::clone() const
PUBLIC a2c0 0 boost::exception_detail::error_info_container_impl::set(boost::shared_ptr<boost::exception_detail::error_info_base> const&, boost::exception_detail::type_info_ const&)
PUBLIC a530 0 boost::debug::(anonymous namespace)::start_gdb_in_xemacs(boost::debug::dbg_startup_info const&)
PUBLIC a540 0 boost::debug::(anonymous namespace)::process_info::process_info(int)
PUBLIC a720 0 boost::debug::(anonymous namespace)::prepare_gdb_cmnd_file(boost::debug::dbg_startup_info const&)
PUBLIC a9a0 0 boost::debug::(anonymous namespace)::start_dbx_in_xemacs(boost::debug::dbg_startup_info const&)
PUBLIC a9b0 0 char const* std::__find_if<char const*, __gnu_cxx::__ops::_Iter_equals_val<char const> >(char const*, char const*, __gnu_cxx::__ops::_Iter_equals_val<char const>, std::random_access_iterator_tag) [clone .isra.0]
PUBLIC aaa0 0 boost::debug::(anonymous namespace)::prepare_window_title(boost::debug::dbg_startup_info const&)
PUBLIC ab60 0 boost::debug::(anonymous namespace)::start_dbx_in_emacs(boost::debug::dbg_startup_info const&)
PUBLIC ab70 0 boost::debug::(anonymous namespace)::safe_execlp(char const*, ...)
PUBLIC aed0 0 boost::debug::(anonymous namespace)::start_dbx_in_console(boost::debug::dbg_startup_info const&)
PUBLIC afc0 0 boost::debug::(anonymous namespace)::start_gdb_in_console(boost::debug::dbg_startup_info const&)
PUBLIC b000 0 boost::debug::(anonymous namespace)::start_dbx_in_ddd(boost::debug::dbg_startup_info const&)
PUBLIC b120 0 boost::debug::(anonymous namespace)::start_dbx_in_xterm(boost::debug::dbg_startup_info const&)
PUBLIC b2b0 0 boost::debug::(anonymous namespace)::start_gdb_in_xterm(boost::debug::dbg_startup_info const&)
PUBLIC b390 0 boost::debug::(anonymous namespace)::start_gdb_in_emacs(boost::debug::dbg_startup_info const&)
PUBLIC b480 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >*) [clone .isra.0]
PUBLIC b520 0 boost::debug::(anonymous namespace)::info_t::~info_t()
PUBLIC b5f0 0 boost::debug::under_debugger()
PUBLIC b7f0 0 boost::debug::debugger_break()
PUBLIC b810 0 boost::debug::detect_memory_leaks(bool, boost::unit_test::basic_cstring<char const>)
PUBLIC b820 0 boost::debug::break_memory_alloc(long)
PUBLIC b830 0 std::_Rb_tree_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::piecewise_construct_t const&, std::tuple<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>&&, std::tuple<>&&) [clone .isra.0]
PUBLIC ba60 0 boost::debug::attach_debugger(bool)
PUBLIC be20 0 boost::debug::set_debugger[abi:cxx11](boost::unit_test::basic_cstring<char const>, boost::function<void (boost::debug::dbg_startup_info const&)>)
PUBLIC c2e0 0 boost::detail::function::void_function_invoker1<void (*)(boost::debug::dbg_startup_info const&), void, boost::debug::dbg_startup_info const&>::invoke(boost::detail::function::function_buffer&, boost::debug::dbg_startup_info const&)
PUBLIC c300 0 boost::detail::function::functor_manager<void (*)(boost::debug::dbg_startup_info const&)>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC c3c0 0 boost::function1<void, boost::debug::dbg_startup_info const&>::swap(boost::function1<void, boost::debug::dbg_startup_info const&>&)
PUBLIC c5f0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC c750 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC c9d0 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, boost::function<void (boost::debug::dbg_startup_info const&)>, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, boost::function<void (boost::debug::dbg_startup_info const&)> > > >::operator[](std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC cc40 0 boost::detail::function::function_obj_invoker0<(anonymous namespace)::cpp_main_caller, int>::invoke(boost::detail::function::function_buffer&)
PUBLIC cc60 0 boost::detail::function::functor_manager<(anonymous namespace)::cpp_main_caller>::manage(boost::detail::function::function_buffer const&, boost::detail::function::function_buffer&, boost::detail::function::functor_manager_operation_type)
PUBLIC cd10 0 boost::prg_exec_monitor_main(int (*)(int, char**), int, char**)
PUBLIC d480 0 boost::execution_monitor::~execution_monitor()
PUBLIC d520 0 __aarch64_ldadd4_relax
PUBLIC d550 0 __aarch64_ldadd4_acq_rel
PUBLIC d580 0 _fini
STACK CFI INIT 5100 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5130 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5170 48 .cfa: sp 0 + .ra: x30
STACK CFI 5174 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 517c x19: .cfa -16 + ^
STACK CFI 51b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75e0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 75f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7600 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7610 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7620 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7650 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 51d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 51d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7680 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76a0 38 .cfa: sp 0 + .ra: x30
STACK CFI 76a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76b4 x19: .cfa -16 + ^
STACK CFI 76d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 76e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 76e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7714 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7778 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4860 fc .cfa: sp 0 + .ra: x30
STACK CFI 4864 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 486c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4878 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 5200 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5208 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5210 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5218 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 52c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7780 80 .cfa: sp 0 + .ra: x30
STACK CFI 7784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 778c x19: .cfa -16 + ^
STACK CFI 77ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 77fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7800 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 7804 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 780c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7884 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 78a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 78b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 798c x23: x23 x24: x24
STACK CFI 79a8 x21: x21 x22: x22
STACK CFI 79b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 79e0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 79e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 79ec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 7a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 7a94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7ac8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7b6c x23: x23 x24: x24
STACK CFI 7b88 x21: x21 x22: x22
STACK CFI 7b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7cd0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 7cd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7cdc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7cfc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7d70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7db4 x25: .cfa -16 + ^
STACK CFI 7dc0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7e5c x23: x23 x24: x24
STACK CFI 7e60 x25: x25
STACK CFI 7e70 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 7e90 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 7e94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7e9c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7ebc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7f74 x25: .cfa -16 + ^
STACK CFI 7f80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 801c x23: x23 x24: x24
STACK CFI 8020 x25: x25
STACK CFI 8030 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 8130 108 .cfa: sp 0 + .ra: x30
STACK CFI 8134 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 813c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8150 x23: .cfa -16 + ^
STACK CFI 820c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8210 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7bc0 10c .cfa: sp 0 + .ra: x30
STACK CFI 7bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7bcc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8050 e0 .cfa: sp 0 + .ra: x30
STACK CFI 8054 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 805c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 80dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8240 4a0 .cfa: sp 0 + .ra: x30
STACK CFI 8244 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8258 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8270 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 82d8 x25: .cfa -32 + ^
STACK CFI 8390 x25: x25
STACK CFI 8444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8448 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 84d0 x25: .cfa -32 + ^
STACK CFI 8548 x25: x25
STACK CFI 85b0 x25: .cfa -32 + ^
STACK CFI 85f4 x25: x25
STACK CFI 860c x25: .cfa -32 + ^
STACK CFI 8654 x25: x25
STACK CFI 8694 x25: .cfa -32 + ^
STACK CFI 86bc x25: x25
STACK CFI 86cc x25: .cfa -32 + ^
STACK CFI 86d0 x25: x25
STACK CFI 86d8 x25: .cfa -32 + ^
STACK CFI 86dc x25: x25
STACK CFI INIT 86e0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 86e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86ec x19: .cfa -16 + ^
STACK CFI 8768 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8774 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 877c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8780 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 879c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 87a0 178 .cfa: sp 0 + .ra: x30
STACK CFI 87a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 87b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 87c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 87c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8864 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 52f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5300 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5320 398 .cfa: sp 0 + .ra: x30
STACK CFI 5324 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 532c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53fc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 5438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 543c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 54d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 55d4 x21: x21 x22: x22
STACK CFI 55d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5690 x21: x21 x22: x22
STACK CFI 5694 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 56c0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 56e0 2c .cfa: sp 0 + .ra: x30
STACK CFI 56e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5710 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5780 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT 57f0 124 .cfa: sp 0 + .ra: x30
STACK CFI 57f4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 5808 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 5810 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 5920 94 .cfa: sp 0 + .ra: x30
STACK CFI 5924 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI INIT 59c0 468 .cfa: sp 0 + .ra: x30
STACK CFI 59cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5aec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5af0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 47d0 90 .cfa: sp 0 + .ra: x30
STACK CFI 47d4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI INIT 5e30 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e80 38 .cfa: sp 0 + .ra: x30
STACK CFI 5e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e8c x19: .cfa -16 + ^
STACK CFI 5eb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ec0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ecc x19: .cfa -16 + ^
STACK CFI 5ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8920 394 .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 560 +
STACK CFI 8930 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 8938 x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI 8950 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 895c x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 8964 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 896c x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 8b98 x19: x19 x20: x20
STACK CFI 8b9c x21: x21 x22: x22
STACK CFI 8ba0 x23: x23 x24: x24
STACK CFI 8ba4 x27: x27 x28: x28
STACK CFI 8bd0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 8bd4 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI 8be4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 8be8 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 8bec x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 8bf0 x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 8bf4 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT 8cc0 c60 .cfa: sp 0 + .ra: x30
STACK CFI 8cc4 .cfa: sp 672 +
STACK CFI 8cd0 .ra: .cfa -664 + ^ x29: .cfa -672 + ^
STACK CFI 8cd8 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 8ce0 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 8cf8 x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 8d00 x25: .cfa -608 + ^ x26: .cfa -600 + ^
STACK CFI 8d0c x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 8dd0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8e5c .cfa: sp 672 + .ra: .cfa -664 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^ x29: .cfa -672 + ^
STACK CFI 9330 x25: x25 x26: x26
STACK CFI 9334 x27: x27 x28: x28
STACK CFI 9338 x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 94e0 x25: x25 x26: x26
STACK CFI 94e4 x27: x27 x28: x28
STACK CFI 94e8 x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 977c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9780 x25: .cfa -608 + ^ x26: .cfa -600 + ^
STACK CFI 9784 x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI INIT 495c 44 .cfa: sp 0 + .ra: x30
STACK CFI 4960 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4968 x19: .cfa -16 + ^
STACK CFI 499c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 49a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 49a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49ac x19: .cfa -16 + ^
STACK CFI INIT 5f00 13c .cfa: sp 0 + .ra: x30
STACK CFI 5f08 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5f10 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5f20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5fa0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6040 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6044 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6054 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6060 x21: .cfa -48 + ^
STACK CFI 60b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6110 474 .cfa: sp 0 + .ra: x30
STACK CFI 6114 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 611c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 6128 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 613c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 6144 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 6150 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 628c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6290 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 6590 158 .cfa: sp 0 + .ra: x30
STACK CFI 6594 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 65a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 65ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 65f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6638 x21: x21 x22: x22
STACK CFI 6640 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 66a4 x21: x21 x22: x22
STACK CFI 66ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 49d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 49d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49dc x19: .cfa -16 + ^
STACK CFI INIT 66f0 288 .cfa: sp 0 + .ra: x30
STACK CFI 66f4 .cfa: sp 2944 +
STACK CFI 66fc .ra: .cfa -2936 + ^ x29: .cfa -2944 + ^
STACK CFI 6718 x19: .cfa -2928 + ^ x20: .cfa -2920 + ^ x21: .cfa -2912 + ^
STACK CFI 67d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 67d8 .cfa: sp 2944 + .ra: .cfa -2936 + ^ x19: .cfa -2928 + ^ x20: .cfa -2920 + ^ x21: .cfa -2912 + ^ x29: .cfa -2944 + ^
STACK CFI INIT 6980 b80 .cfa: sp 0 + .ra: x30
STACK CFI 6984 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6994 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 699c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a1c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7500 e0 .cfa: sp 0 + .ra: x30
STACK CFI 7514 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7580 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7584 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7588 x19: .cfa -64 + ^
STACK CFI 758c x19: x19
STACK CFI 7594 x19: .cfa -64 + ^
STACK CFI INIT 9920 e8 .cfa: sp 0 + .ra: x30
STACK CFI 9924 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 997c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9980 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9984 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9998 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 99d0 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 99d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 99d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 9a10 148 .cfa: sp 0 + .ra: x30
STACK CFI 9a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9a24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a2c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9ae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9b38 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9b60 218 .cfa: sp 0 + .ra: x30
STACK CFI 9b64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9b74 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9b88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9b90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9c24 x19: x19 x20: x20
STACK CFI 9c28 x21: x21 x22: x22
STACK CFI 9c2c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9c30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9c58 x21: x21 x22: x22
STACK CFI 9c64 x19: x19 x20: x20
STACK CFI 9c6c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9c70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9cb4 x19: x19 x20: x20
STACK CFI 9cb8 x21: x21 x22: x22
STACK CFI 9cc8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9cd8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9d18 x19: x19 x20: x20
STACK CFI 9d28 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9d2c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9d38 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9d4c x21: x21 x22: x22
STACK CFI 9d50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9d54 x21: x21 x22: x22
STACK CFI 9d5c x19: x19 x20: x20
STACK CFI 9d64 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 9d68 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9d6c x21: x21 x22: x22
STACK CFI INIT 9d80 534 .cfa: sp 0 + .ra: x30
STACK CFI 9d84 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9d8c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9da8 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a04c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT a2c0 268 .cfa: sp 0 + .ra: x30
STACK CFI a2c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a2cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a2d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a2e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a2e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a444 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT a530 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c2e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT a540 1d8 .cfa: sp 0 + .ra: x30
STACK CFI a544 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a54c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a554 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI a560 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI a570 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a6d8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT a720 27c .cfa: sp 0 + .ra: x30
STACK CFI a724 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a734 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a750 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a958 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT a9a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a9b0 f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT aaa0 c0 .cfa: sp 0 + .ra: x30
STACK CFI aaa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aab8 x19: .cfa -16 + ^
STACK CFI ab38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ab3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ad0 104 .cfa: sp 0 + .ra: x30
STACK CFI 4ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4ae4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4aec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b6c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT c300 b4 .cfa: sp 0 + .ra: x30
STACK CFI c304 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c310 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c334 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c34c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c350 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ab60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ab70 360 .cfa: sp 0 + .ra: x30
STACK CFI ab74 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI ab84 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI ab8c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI abd4 x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ac5c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI addc x21: x21 x22: x22
STACK CFI ae0c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI ae34 x21: x21 x22: x22
STACK CFI ae68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ae6c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI ae74 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI ae94 x21: x21 x22: x22
STACK CFI aeb0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI aec0 x21: x21 x22: x22
STACK CFI aecc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT aed0 e8 .cfa: sp 0 + .ra: x30
STACK CFI aed4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aeec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aef8 x21: .cfa -48 + ^
STACK CFI afb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI afb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT afc0 40 .cfa: sp 0 + .ra: x30
STACK CFI afc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI afd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI aff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI affc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b000 114 .cfa: sp 0 + .ra: x30
STACK CFI b004 .cfa: sp 96 +
STACK CFI b010 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b018 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b038 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b0d0 x21: x21 x22: x22
STACK CFI b0f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b0fc .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI b10c x21: x21 x22: x22
STACK CFI b110 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT b120 18c .cfa: sp 0 + .ra: x30
STACK CFI b124 .cfa: sp 208 +
STACK CFI b130 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b138 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b158 x23: .cfa -48 + ^
STACK CFI b16c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b270 x21: x21 x22: x22
STACK CFI b274 x23: x23
STACK CFI b29c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b2a0 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI b2a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b2a8 x23: .cfa -48 + ^
STACK CFI INIT b2b0 dc .cfa: sp 0 + .ra: x30
STACK CFI b2b4 .cfa: sp 128 +
STACK CFI b2b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b390 ec .cfa: sp 0 + .ra: x30
STACK CFI b394 .cfa: sp 544 +
STACK CFI b3a0 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI b3a8 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI b44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b450 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x29: .cfa -544 + ^
STACK CFI INIT b480 9c .cfa: sp 0 + .ra: x30
STACK CFI b488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b490 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b520 d0 .cfa: sp 0 + .ra: x30
STACK CFI b524 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b52c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b534 x21: .cfa -16 + ^
STACK CFI b5dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b5e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b5ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b5f0 1f4 .cfa: sp 0 + .ra: x30
STACK CFI b5f4 .cfa: sp 1120 +
STACK CFI b600 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI b61c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI b62c x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI b63c x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI b77c x19: x19 x20: x20
STACK CFI b780 x21: x21 x22: x22
STACK CFI b784 x23: x23 x24: x24
STACK CFI b7ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b7b0 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x29: .cfa -1120 + ^
STACK CFI b7c4 x19: x19 x20: x20
STACK CFI b7cc x21: x21 x22: x22
STACK CFI b7d0 x23: x23 x24: x24
STACK CFI b7d8 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI b7dc x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI b7e0 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI INIT b7f0 18 .cfa: sp 0 + .ra: x30
STACK CFI b7f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b800 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b820 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3c0 224 .cfa: sp 0 + .ra: x30
STACK CFI c3c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c3d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c3fc x21: .cfa -64 + ^
STACK CFI c480 x21: x21
STACK CFI c4a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c4a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI c4fc x21: x21
STACK CFI c504 x21: .cfa -64 + ^
STACK CFI INIT 4be0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4bec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c70 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4c7c x19: .cfa -64 + ^
STACK CFI 4c80 x19: x19
STACK CFI 4c88 x19: .cfa -64 + ^
STACK CFI INIT c5f0 154 .cfa: sp 0 + .ra: x30
STACK CFI c5f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c5fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c608 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c610 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c618 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c6d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c750 27c .cfa: sp 0 + .ra: x30
STACK CFI c754 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c764 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c76c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI c778 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c784 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c810 x19: x19 x20: x20
STACK CFI c814 x21: x21 x22: x22
STACK CFI c820 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c824 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI c8b0 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI c8bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c8c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c904 x21: x21 x22: x22
STACK CFI c90c x19: x19 x20: x20
STACK CFI c91c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c920 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI c97c x19: x19 x20: x20
STACK CFI c980 x21: x21 x22: x22
STACK CFI c994 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c998 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT b830 230 .cfa: sp 0 + .ra: x30
STACK CFI b834 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b844 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI b84c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI b854 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI b85c x25: .cfa -48 + ^
STACK CFI b930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI b934 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI INIT ba60 3b4 .cfa: sp 0 + .ra: x30
STACK CFI ba64 .cfa: sp 1296 +
STACK CFI ba70 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI ba7c x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI ba90 x27: .cfa -1216 + ^
STACK CFI bad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x29: x29
STACK CFI bad4 .cfa: sp 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x27: .cfa -1216 + ^ x29: .cfa -1296 + ^
STACK CFI bae4 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI bbd8 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bc6c x25: x25 x26: x26
STACK CFI bcf4 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bcf8 x25: x25 x26: x26
STACK CFI bd3c x23: x23 x24: x24
STACK CFI bd40 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI bd48 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bd50 x25: x25 x26: x26
STACK CFI bd54 x23: x23 x24: x24
STACK CFI bd58 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI bd80 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bd84 x25: x25 x26: x26
STACK CFI bd98 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bd9c x25: x25 x26: x26
STACK CFI bda0 x23: x23 x24: x24
STACK CFI bda4 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^
STACK CFI bda8 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI bdac x25: x25 x26: x26
STACK CFI bdfc x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI be08 x25: x25 x26: x26
STACK CFI be10 x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI INIT be20 4b4 .cfa: sp 0 + .ra: x30
STACK CFI be24 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI be38 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI be4c x19: .cfa -160 + ^ x20: .cfa -152 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI be5c x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI c088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c08c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT c9d0 270 .cfa: sp 0 + .ra: x30
STACK CFI c9d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c9dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c9e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c9f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c9f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI cac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cac8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI cb90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cb94 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI cbd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cbdc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4ce0 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 4ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4cf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d00 x21: .cfa -64 + ^
STACK CFI 4fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4fac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5090 30 .cfa: sp 0 + .ra: x30
STACK CFI 5094 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc40 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc60 ac .cfa: sp 0 + .ra: x30
STACK CFI cc64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cc70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI cc90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cc94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI cd00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cd04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a48 84 .cfa: sp 0 + .ra: x30
STACK CFI 4a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d480 a0 .cfa: sp 0 + .ra: x30
STACK CFI d484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d48c x19: .cfa -16 + ^
STACK CFI d500 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d50c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI d51c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cd10 764 .cfa: sp 0 + .ra: x30
STACK CFI cd14 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI cd28 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI cd30 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI cd3c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI cefc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI cf00 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI INIT d520 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d550 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 50c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50dc .cfa: sp 0 + .ra: .ra x29: x29
