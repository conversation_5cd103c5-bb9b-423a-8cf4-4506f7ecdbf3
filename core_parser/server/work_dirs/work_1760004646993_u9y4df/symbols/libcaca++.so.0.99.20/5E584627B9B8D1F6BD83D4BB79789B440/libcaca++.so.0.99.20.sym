MODULE Linux arm64 5E584627B9B8D1F6BD83D4BB79789B440 libcaca++.so.0
INFO CODE_ID 2746585EB8B9F6D1BD83D4BB79789B443B62B9E9
PUBLIC 3c60 0 Charset::utf8ToUtf32(char const*, unsigned long*)
PUBLIC 3c80 0 Charset::utf32ToUtf8(char*, unsigned int)
PUBLIC 3ca0 0 Charset::utf32ToCp437(unsigned int)
PUBLIC 3cc0 0 Charset::cp437ToUtf32(unsigned char)
PUBLIC 3ce0 0 Canvas::Canvas()
PUBLIC 3d20 0 Canvas::Canvas(int, int)
PUBLIC 3d60 0 Canvas::~Canvas()
PUBLIC 3d90 0 Canvas::get_caca_canvas_t()
PUBLIC 3db0 0 Canvas::setSize(unsigned int, unsigned int)
PUBLIC 3dd0 0 Canvas::getWidth()
PUBLIC 3df0 0 Canvas::getHeight()
PUBLIC 3e10 0 Canvas::setColorANSI(unsigned char, unsigned char)
PUBLIC 3e30 0 Canvas::setColorARGB(unsigned int, unsigned int)
PUBLIC 3e50 0 Canvas::putChar(int, int, unsigned int)
PUBLIC 3e70 0 Canvas::getChar(int, int)
PUBLIC 3e90 0 Canvas::putStr(int, int, char*)
PUBLIC 3eb0 0 Canvas::Printf(int, int, char const*, ...)
PUBLIC 4000 0 Canvas::Clear()
PUBLIC 4020 0 Canvas::Blit(int, int, Canvas*, Canvas*)
PUBLIC 4090 0 Canvas::Invert()
PUBLIC 40b0 0 Canvas::Flip()
PUBLIC 40d0 0 Canvas::Flop()
PUBLIC 40f0 0 Canvas::Rotate180()
PUBLIC 4110 0 Canvas::RotateLeft()
PUBLIC 4130 0 Canvas::RotateRight()
PUBLIC 4150 0 Canvas::drawLine(int, int, int, int, unsigned int)
PUBLIC 4170 0 Canvas::drawPolyline(int const*, int const*, int, unsigned int)
PUBLIC 4190 0 Canvas::drawThinLine(int, int, int, int)
PUBLIC 41b0 0 Canvas::drawThinPolyline(int const*, int const*, int)
PUBLIC 41d0 0 Canvas::drawCircle(int, int, int, unsigned int)
PUBLIC 41f0 0 Canvas::drawEllipse(int, int, int, int, unsigned int)
PUBLIC 4210 0 Canvas::drawThinEllipse(int, int, int, int)
PUBLIC 4230 0 Canvas::fillEllipse(int, int, int, int, unsigned int)
PUBLIC 4250 0 Canvas::drawBox(int, int, int, int, unsigned int)
PUBLIC 4270 0 Canvas::drawThinBox(int, int, int, int)
PUBLIC 4290 0 Canvas::drawCP437Box(int, int, int, int)
PUBLIC 42b0 0 Canvas::fillBox(int, int, int, int, unsigned int)
PUBLIC 42d0 0 Canvas::drawTriangle(int, int, int, int, int, int, unsigned int)
PUBLIC 42f0 0 Canvas::drawThinTriangle(int, int, int, int, int, int)
PUBLIC 4310 0 Canvas::fillTriangle(int, int, int, int, int, int, unsigned int)
PUBLIC 4330 0 Canvas::fillTriangleTextured(int*, Canvas*, float*)
PUBLIC 4350 0 Canvas::Rand(int, int)
PUBLIC 4370 0 Canvas::getVersion()
PUBLIC 4390 0 Canvas::setAttr(unsigned int)
PUBLIC 43b0 0 Canvas::getAttr(int, int)
PUBLIC 43d0 0 Canvas::setBoundaries(caca_canvas*, int, int, unsigned int, unsigned int)
PUBLIC 4400 0 Canvas::getFrameCount()
PUBLIC 4420 0 Canvas::setFrame(unsigned int)
PUBLIC 4440 0 Canvas::createFrame(unsigned int)
PUBLIC 4460 0 Canvas::freeFrame(unsigned int)
PUBLIC 4480 0 Canvas::getImportList()
PUBLIC 44a0 0 Canvas::importFromMemory(void const*, unsigned long, char const*)
PUBLIC 44c0 0 Canvas::importFromFile(char const*, char const*)
PUBLIC 44e0 0 Canvas::getExportList()
PUBLIC 4500 0 Canvas::exportToMemory(char const*, unsigned long*)
PUBLIC 4520 0 Dither::Dither(unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int)
PUBLIC 4570 0 Dither::~Dither()
PUBLIC 4590 0 Dither::setPalette(unsigned int*, unsigned int*, unsigned int*, unsigned int*)
PUBLIC 45b0 0 Dither::setBrightness(float)
PUBLIC 45d0 0 Dither::setGamma(float)
PUBLIC 45f0 0 Dither::setContrast(float)
PUBLIC 4610 0 Dither::setAntialias(char const*)
PUBLIC 4630 0 Dither::getAntialiasList()
PUBLIC 4650 0 Dither::setColor(char const*)
PUBLIC 4670 0 Dither::getColorList()
PUBLIC 4690 0 Dither::setCharset(char const*)
PUBLIC 46b0 0 Dither::getCharsetList()
PUBLIC 46d0 0 Dither::setMode(char const*)
PUBLIC 46f0 0 Dither::getModeList()
PUBLIC 4710 0 Dither::Bitmap(Canvas*, int, int, int, int, void*)
PUBLIC 4780 0 Font::Font(void const*, unsigned int)
PUBLIC 47c0 0 Font::getList()
PUBLIC 47e0 0 Font::getWidth()
PUBLIC 4800 0 Font::getHeight()
PUBLIC 4820 0 Font::renderCanvas(Canvas*, unsigned char*, unsigned int, unsigned int, unsigned int)
PUBLIC 4880 0 Font::getBlocks()
PUBLIC 48a0 0 Font::~Font()
PUBLIC 48c0 0 Caca::Caca(Canvas*)
PUBLIC 4900 0 Caca::~Caca()
PUBLIC 4920 0 Caca::Attach(Canvas*)
PUBLIC 4980 0 Caca::Detach()
PUBLIC 49a0 0 Caca::setDisplayTime(unsigned int)
PUBLIC 49c0 0 Caca::Display()
PUBLIC 49e0 0 Caca::getDisplayTime()
PUBLIC 4a00 0 Caca::getWidth()
PUBLIC 4a20 0 Caca::getHeight()
PUBLIC 4a40 0 Caca::setTitle(char const*)
PUBLIC 4a60 0 Caca::getEvent(unsigned int, Event*, int)
PUBLIC 4a90 0 Caca::getMouseX()
PUBLIC 4ab0 0 Caca::getMouseY()
PUBLIC 4ad0 0 Caca::setMouse(int)
PUBLIC 4af0 0 Caca::getVersion()
STACK CFI INIT 3b90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3bc0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3c00 48 .cfa: sp 0 + .ra: x30
STACK CFI 3c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c0c x19: .cfa -16 + ^
STACK CFI 3c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3c50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b50 2c .cfa: sp 0 + .ra: x30
STACK CFI 3b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c60 20 .cfa: sp 0 + .ra: x30
STACK CFI 3c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c80 20 .cfa: sp 0 + .ra: x30
STACK CFI 3c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ca0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cc0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3cc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ce0 3c .cfa: sp 0 + .ra: x30
STACK CFI 3ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cf4 x19: .cfa -16 + ^
STACK CFI 3d10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d20 3c .cfa: sp 0 + .ra: x30
STACK CFI 3d28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d30 x19: .cfa -16 + ^
STACK CFI 3d50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d60 2c .cfa: sp 0 + .ra: x30
STACK CFI 3d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d90 1c .cfa: sp 0 + .ra: x30
STACK CFI 3d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3db0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3db8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3dd0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3dd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3df0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e10 1c .cfa: sp 0 + .ra: x30
STACK CFI 3e18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e30 1c .cfa: sp 0 + .ra: x30
STACK CFI 3e38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e50 1c .cfa: sp 0 + .ra: x30
STACK CFI 3e58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e70 1c .cfa: sp 0 + .ra: x30
STACK CFI 3e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e90 1c .cfa: sp 0 + .ra: x30
STACK CFI 3e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ea0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3eb0 150 .cfa: sp 0 + .ra: x30
STACK CFI 3eb8 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 3ed0 .cfa: sp 8528 + x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^
STACK CFI 3fe4 .cfa: sp 224 +
STACK CFI 3ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3ffc .cfa: sp 8528 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 4000 1c .cfa: sp 0 + .ra: x30
STACK CFI 4008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4020 70 .cfa: sp 0 + .ra: x30
STACK CFI 4028 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4030 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 403c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4044 x23: .cfa -16 + ^
STACK CFI 4088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4090 1c .cfa: sp 0 + .ra: x30
STACK CFI 4098 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 40b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 40d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 40f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4100 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4110 1c .cfa: sp 0 + .ra: x30
STACK CFI 4118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4120 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4130 1c .cfa: sp 0 + .ra: x30
STACK CFI 4138 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4150 1c .cfa: sp 0 + .ra: x30
STACK CFI 4158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4160 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4170 1c .cfa: sp 0 + .ra: x30
STACK CFI 4178 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4180 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4190 1c .cfa: sp 0 + .ra: x30
STACK CFI 4198 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 41b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 41f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4200 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4210 1c .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4230 1c .cfa: sp 0 + .ra: x30
STACK CFI 4238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4250 1c .cfa: sp 0 + .ra: x30
STACK CFI 4258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4270 1c .cfa: sp 0 + .ra: x30
STACK CFI 4278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4280 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4290 1c .cfa: sp 0 + .ra: x30
STACK CFI 4298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 42b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 42d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 42f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4300 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4310 1c .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4320 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4330 20 .cfa: sp 0 + .ra: x30
STACK CFI 4338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4340 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4350 18 .cfa: sp 0 + .ra: x30
STACK CFI 4358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4370 18 .cfa: sp 0 + .ra: x30
STACK CFI 4378 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4380 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4390 1c .cfa: sp 0 + .ra: x30
STACK CFI 4398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 43b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 43d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4400 1c .cfa: sp 0 + .ra: x30
STACK CFI 4408 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4420 1c .cfa: sp 0 + .ra: x30
STACK CFI 4428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4430 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4440 1c .cfa: sp 0 + .ra: x30
STACK CFI 4448 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4460 1c .cfa: sp 0 + .ra: x30
STACK CFI 4468 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4480 18 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4490 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 44a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 44c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 44e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4500 1c .cfa: sp 0 + .ra: x30
STACK CFI 4508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4510 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4520 50 .cfa: sp 0 + .ra: x30
STACK CFI 4528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4538 x19: .cfa -16 + ^
STACK CFI 4568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4570 20 .cfa: sp 0 + .ra: x30
STACK CFI 4578 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4590 1c .cfa: sp 0 + .ra: x30
STACK CFI 4598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 45b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 45d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 45f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 45f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4610 1c .cfa: sp 0 + .ra: x30
STACK CFI 4618 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4620 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4630 1c .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4650 1c .cfa: sp 0 + .ra: x30
STACK CFI 4658 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4660 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4670 1c .cfa: sp 0 + .ra: x30
STACK CFI 4678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4680 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4690 1c .cfa: sp 0 + .ra: x30
STACK CFI 4698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 46b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 46d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 46f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4700 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4710 68 .cfa: sp 0 + .ra: x30
STACK CFI 4718 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4720 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4730 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 473c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4780 3c .cfa: sp 0 + .ra: x30
STACK CFI 4788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4790 x19: .cfa -16 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 47c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 47c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 47d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 47e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 47e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 47f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4800 1c .cfa: sp 0 + .ra: x30
STACK CFI 4808 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4820 60 .cfa: sp 0 + .ra: x30
STACK CFI 4828 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4830 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 484c x23: .cfa -16 + ^
STACK CFI 4878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4880 1c .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48a0 20 .cfa: sp 0 + .ra: x30
STACK CFI 48a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 48c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48d0 x19: .cfa -16 + ^
STACK CFI 48f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4900 20 .cfa: sp 0 + .ra: x30
STACK CFI 4908 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4918 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4920 58 .cfa: sp 0 + .ra: x30
STACK CFI 4928 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4930 x19: .cfa -16 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4980 1c .cfa: sp 0 + .ra: x30
STACK CFI 4988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4990 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 49a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 49c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 49e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a00 1c .cfa: sp 0 + .ra: x30
STACK CFI 4a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a20 1c .cfa: sp 0 + .ra: x30
STACK CFI 4a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a40 1c .cfa: sp 0 + .ra: x30
STACK CFI 4a48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a60 28 .cfa: sp 0 + .ra: x30
STACK CFI 4a68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a90 1c .cfa: sp 0 + .ra: x30
STACK CFI 4a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4aa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ab0 1c .cfa: sp 0 + .ra: x30
STACK CFI 4ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ac0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ad0 1c .cfa: sp 0 + .ra: x30
STACK CFI 4ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4af0 18 .cfa: sp 0 + .ra: x30
STACK CFI 4af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b00 .cfa: sp 0 + .ra: .ra x29: x29
