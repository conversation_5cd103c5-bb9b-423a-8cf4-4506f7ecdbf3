MODULE Linux arm64 E536A2070DEC626D372517475FCD13430 libpixbufloader-svg.so
INFO CODE_ID 07A236E5EC0D6D62372517475FCD1343017E3462
PUBLIC ec0 0 fill_vtable
PUBLIC f00 0 fill_info
STACK CFI INIT aa0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b10 48 .cfa: sp 0 + .ra: x30
STACK CFI b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b1c x19: .cfa -16 + ^
STACK CFI b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b70 68 .cfa: sp 0 + .ra: x30
STACK CFI b78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b9c x23: .cfa -16 + ^
STACK CFI bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT be0 180 .cfa: sp 0 + .ra: x30
STACK CFI be8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bfc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d60 160 .cfa: sp 0 + .ra: x30
STACK CFI d68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e14 x21: x21 x22: x22
STACK CFI e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI e8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT ec0 38 .cfa: sp 0 + .ra: x30
STACK CFI ec8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI edc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f00 5c .cfa: sp 0 + .ra: x30
STACK CFI f08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f1c .cfa: sp 0 + .ra: .ra x29: x29
