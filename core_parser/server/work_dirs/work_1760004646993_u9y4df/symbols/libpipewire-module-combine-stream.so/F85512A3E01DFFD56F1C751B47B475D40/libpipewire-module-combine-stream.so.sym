MODULE Linux arm64 F85512A3E01DFFD56F1C751B47B475D40 libpipewire-module-combine-stream.so
INFO CODE_ID A31255F81DE0D5FF6F1C751B47B475D42EEC90F6
PUBLIC a500 0 pipewire__module_init
STACK CFI INIT 2230 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2260 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 22a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 22a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22ac x19: .cfa -16 + ^
STACK CFI 22e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2300 340 .cfa: sp 0 + .ra: x30
STACK CFI 2308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2400 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2538 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2544 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 259c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2614 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 261c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2640 60 .cfa: sp 0 + .ra: x30
STACK CFI 2648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 26a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 26a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 26f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 26f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2700 x19: .cfa -16 + ^
STACK CFI 2738 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2740 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 2748 .cfa: sp 480 +
STACK CFI 275c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 2774 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 277c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2794 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2f40 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2f48 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 53d0 190 .cfa: sp 0 + .ra: x30
STACK CFI 53d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 53e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 53f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5438 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5440 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5494 x25: x25 x26: x26
STACK CFI 54ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 54f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5524 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5528 x25: x25 x26: x26
STACK CFI 5534 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 554c x25: x25 x26: x26
STACK CFI INIT 5560 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5568 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5570 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5578 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5580 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5620 de8 .cfa: sp 0 + .ra: x30
STACK CFI 5628 .cfa: sp 352 +
STACK CFI 563c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5644 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5654 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5660 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5668 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5a38 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6410 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6418 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6424 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6494 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 64a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 64b0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 64b8 .cfa: sp 448 +
STACK CFI 64c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 64dc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 64e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 64f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6668 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6974 5dc .cfa: sp 0 + .ra: x30
STACK CFI 697c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6988 .cfa: sp 1408 + x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 69b0 x19: .cfa -96 + ^
STACK CFI 69b4 x20: .cfa -88 + ^
STACK CFI 69b8 x21: .cfa -80 + ^
STACK CFI 69bc x22: .cfa -72 + ^
STACK CFI 69c0 x25: .cfa -48 + ^
STACK CFI 69c4 x26: .cfa -40 + ^
STACK CFI 69c8 v8: .cfa -16 + ^
STACK CFI 69cc v9: .cfa -8 + ^
STACK CFI 69f0 x23: .cfa -64 + ^
STACK CFI 69f4 x24: .cfa -56 + ^
STACK CFI 6ac4 x23: x23
STACK CFI 6acc x24: x24
STACK CFI 6b24 x23: .cfa -64 + ^
STACK CFI 6b2c x24: .cfa -56 + ^
STACK CFI 6c7c x19: x19
STACK CFI 6c80 x20: x20
STACK CFI 6c84 x21: x21
STACK CFI 6c88 x22: x22
STACK CFI 6c8c x23: x23
STACK CFI 6c90 x24: x24
STACK CFI 6c94 x25: x25
STACK CFI 6c98 x26: x26
STACK CFI 6c9c v8: v8
STACK CFI 6ca0 v9: v9
STACK CFI 6cc0 .cfa: sp 112 +
STACK CFI 6cc8 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 6cd0 .cfa: sp 1408 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6dfc x19: x19
STACK CFI 6e00 x20: x20
STACK CFI 6e04 x21: x21
STACK CFI 6e08 x22: x22
STACK CFI 6e0c x25: x25
STACK CFI 6e10 x26: x26
STACK CFI 6e14 v8: v8
STACK CFI 6e18 v9: v9
STACK CFI 6e1c v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6efc x23: x23
STACK CFI 6f00 x24: x24
STACK CFI 6f24 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 6f28 x19: .cfa -96 + ^
STACK CFI 6f2c x20: .cfa -88 + ^
STACK CFI 6f30 x21: .cfa -80 + ^
STACK CFI 6f34 x22: .cfa -72 + ^
STACK CFI 6f38 x23: .cfa -64 + ^
STACK CFI 6f3c x24: .cfa -56 + ^
STACK CFI 6f40 x25: .cfa -48 + ^
STACK CFI 6f44 x26: .cfa -40 + ^
STACK CFI 6f48 v8: .cfa -16 + ^
STACK CFI 6f4c v9: .cfa -8 + ^
STACK CFI INIT 6f50 35c .cfa: sp 0 + .ra: x30
STACK CFI 6f58 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6f64 .cfa: sp 1200 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7060 .cfa: sp 80 +
STACK CFI 7068 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7070 .cfa: sp 1200 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7098 x19: .cfa -64 + ^
STACK CFI 70b0 x20: .cfa -56 + ^
STACK CFI 70b8 x23: .cfa -32 + ^
STACK CFI 70c0 x24: .cfa -24 + ^
STACK CFI 70c8 x25: .cfa -16 + ^
STACK CFI 70d0 x26: .cfa -8 + ^
STACK CFI 7198 x19: x19
STACK CFI 719c x20: x20
STACK CFI 71a0 x23: x23
STACK CFI 71a4 x24: x24
STACK CFI 71a8 x25: x25
STACK CFI 71ac x26: x26
STACK CFI 71b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7290 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7294 x19: .cfa -64 + ^
STACK CFI 7298 x20: .cfa -56 + ^
STACK CFI 729c x23: .cfa -32 + ^
STACK CFI 72a0 x24: .cfa -24 + ^
STACK CFI 72a4 x25: .cfa -16 + ^
STACK CFI 72a8 x26: .cfa -8 + ^
STACK CFI INIT 72b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 72b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 72e0 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 72e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 72fc .cfa: sp 1120 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 7424 .cfa: sp 64 +
STACK CFI 7434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 743c .cfa: sp 1120 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 74b0 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 74b8 .cfa: sp 176 +
STACK CFI 74bc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 74c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 74cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7500 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7508 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7514 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7658 x19: x19 x20: x20
STACK CFI 765c x25: x25 x26: x26
STACK CFI 7660 x27: x27 x28: x28
STACK CFI 7690 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7698 .cfa: sp 176 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 769c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 76a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 76b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 76b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 76e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 76e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 76f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7708 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7758 x19: x19 x20: x20
STACK CFI 7764 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 7770 104 .cfa: sp 0 + .ra: x30
STACK CFI 7778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7788 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 782c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7874 54 .cfa: sp 0 + .ra: x30
STACK CFI 787c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7884 x19: .cfa -16 + ^
STACK CFI 78c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 78ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78f4 x19: .cfa -16 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7940 144 .cfa: sp 0 + .ra: x30
STACK CFI 7948 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7950 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7a84 4d4 .cfa: sp 0 + .ra: x30
STACK CFI 7a8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7aa8 .cfa: sp 576 + x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b00 .cfa: sp 48 +
STACK CFI 7b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b14 .cfa: sp 576 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7f60 874 .cfa: sp 0 + .ra: x30
STACK CFI 7f68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f74 .cfa: sp 1312 + x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7fb8 x19: .cfa -80 + ^
STACK CFI 7fc0 x20: .cfa -72 + ^
STACK CFI 7fc8 x21: .cfa -64 + ^
STACK CFI 7fd0 x22: .cfa -56 + ^
STACK CFI 7fd8 x25: .cfa -32 + ^
STACK CFI 7fe0 x26: .cfa -24 + ^
STACK CFI 7fe8 x27: .cfa -16 + ^
STACK CFI 7ff0 x28: .cfa -8 + ^
STACK CFI 8388 x19: x19
STACK CFI 8390 x20: x20
STACK CFI 8398 x21: x21
STACK CFI 839c x22: x22
STACK CFI 83a0 x25: x25
STACK CFI 83a4 x26: x26
STACK CFI 83a8 x27: x27
STACK CFI 83ac x28: x28
STACK CFI 83cc .cfa: sp 96 +
STACK CFI 83d4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 83dc .cfa: sp 1312 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 87a0 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 87b4 x19: .cfa -80 + ^
STACK CFI 87b8 x20: .cfa -72 + ^
STACK CFI 87bc x21: .cfa -64 + ^
STACK CFI 87c0 x22: .cfa -56 + ^
STACK CFI 87c4 x25: .cfa -32 + ^
STACK CFI 87c8 x26: .cfa -24 + ^
STACK CFI 87cc x27: .cfa -16 + ^
STACK CFI 87d0 x28: .cfa -8 + ^
STACK CFI INIT 87d4 9a4 .cfa: sp 0 + .ra: x30
STACK CFI 87dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 87f8 .cfa: sp 1808 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8d48 .cfa: sp 96 +
STACK CFI 8d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d6c .cfa: sp 1808 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9180 b4 .cfa: sp 0 + .ra: x30
STACK CFI 9188 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9190 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 91a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 91c0 x19: x19 x20: x20
STACK CFI 91cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 91d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9208 x19: x19 x20: x20
STACK CFI 9214 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 921c .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 922c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 9234 194 .cfa: sp 0 + .ra: x30
STACK CFI 923c .cfa: sp 96 +
STACK CFI 9248 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9250 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9270 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9328 x21: x21 x22: x22
STACK CFI 9354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 935c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9360 x21: x21 x22: x22
STACK CFI 9364 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 93c0 x21: x21 x22: x22
STACK CFI 93c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 93d0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 93d8 .cfa: sp 144 +
STACK CFI 93dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 93e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 93f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 94d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 94d8 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 94f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9560 x23: x23 x24: x24
STACK CFI 9580 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 95e4 x23: x23 x24: x24
STACK CFI 95f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 95fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 960c x27: .cfa -16 + ^
STACK CFI 9650 x23: x23 x24: x24
STACK CFI 9654 x25: x25 x26: x26
STACK CFI 9658 x27: x27
STACK CFI 965c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9668 x25: x25 x26: x26
STACK CFI 9670 x27: x27
STACK CFI 9674 x23: x23 x24: x24
STACK CFI 9678 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 967c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9680 x27: .cfa -16 + ^
STACK CFI 9684 x25: x25 x26: x26 x27: x27
STACK CFI 9688 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9690 x27: .cfa -16 + ^
STACK CFI INIT 96a0 120 .cfa: sp 0 + .ra: x30
STACK CFI 96a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 96b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 971c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9724 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9728 x21: .cfa -16 + ^
STACK CFI 97b0 x21: x21
STACK CFI 97b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 97c0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 97c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 97d0 x19: .cfa -16 + ^
STACK CFI 988c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9894 ec .cfa: sp 0 + .ra: x30
STACK CFI 989c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 98a4 x19: .cfa -16 + ^
STACK CFI 990c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9914 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9964 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9974 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9980 45c .cfa: sp 0 + .ra: x30
STACK CFI 9988 .cfa: sp 224 +
STACK CFI 9994 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 99b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 99c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9a30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9a34 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9a38 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9bd4 x19: x19 x20: x20
STACK CFI 9bd8 x21: x21 x22: x22
STACK CFI 9bdc x23: x23 x24: x24
STACK CFI 9be0 x25: x25 x26: x26
STACK CFI 9be4 x27: x27 x28: x28
STACK CFI 9c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c10 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9cd0 x19: x19 x20: x20
STACK CFI 9cd4 x21: x21 x22: x22
STACK CFI 9cd8 x23: x23 x24: x24
STACK CFI 9cdc x25: x25 x26: x26
STACK CFI 9ce0 x27: x27 x28: x28
STACK CFI 9ce8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9cf0 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 9cf4 x19: x19 x20: x20
STACK CFI 9cf8 x21: x21 x22: x22
STACK CFI 9d44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9d6c .cfa: sp 224 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9d84 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9db0 x19: x19 x20: x20
STACK CFI 9db4 x21: x21 x22: x22
STACK CFI 9dbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9dc4 .cfa: sp 224 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9dc8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9dcc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9dd0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9dd4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9dd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9de0 464 .cfa: sp 0 + .ra: x30
STACK CFI 9de8 .cfa: sp 208 +
STACK CFI 9df4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9e1c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9e20 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9e50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9e54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9e58 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a03c x19: x19 x20: x20
STACK CFI a040 x21: x21 x22: x22
STACK CFI a044 x23: x23 x24: x24
STACK CFI a048 x25: x25 x26: x26
STACK CFI a04c x27: x27 x28: x28
STACK CFI a070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a078 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a13c x19: x19 x20: x20
STACK CFI a140 x21: x21 x22: x22
STACK CFI a144 x23: x23 x24: x24
STACK CFI a148 x25: x25 x26: x26
STACK CFI a14c x27: x27 x28: x28
STACK CFI a154 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a15c .cfa: sp 208 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a1ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a1d4 .cfa: sp 208 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a1ec x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a218 x19: x19 x20: x20
STACK CFI a21c x21: x21 x22: x22
STACK CFI a224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a22c .cfa: sp 208 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a230 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a234 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a238 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a23c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a240 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT a244 104 .cfa: sp 0 + .ra: x30
STACK CFI a24c .cfa: sp 96 +
STACK CFI a250 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a258 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a264 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a2a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2a8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a350 160 .cfa: sp 0 + .ra: x30
STACK CFI a358 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a360 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a4a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a4b0 50 .cfa: sp 0 + .ra: x30
STACK CFI a4b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a4c0 x19: .cfa -16 + ^
STACK CFI a4f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a500 de8 .cfa: sp 0 + .ra: x30
STACK CFI a508 .cfa: sp 144 +
STACK CFI a514 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a51c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a524 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a538 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a5a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a608 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ad08 x23: x23 x24: x24
STACK CFI ad0c x25: x25 x26: x26
STACK CFI ad10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ad5c x25: x25 x26: x26
STACK CFI ad90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI ad98 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI adb0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ae50 x23: x23 x24: x24
STACK CFI ae54 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aeac x23: x23 x24: x24
STACK CFI aeb0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI afa0 x23: x23 x24: x24
STACK CFI afdc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b154 x23: x23 x24: x24
STACK CFI b158 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b25c x23: x23 x24: x24
STACK CFI b28c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b2a0 x23: x23 x24: x24
STACK CFI b2a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b2bc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b2d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b2d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
