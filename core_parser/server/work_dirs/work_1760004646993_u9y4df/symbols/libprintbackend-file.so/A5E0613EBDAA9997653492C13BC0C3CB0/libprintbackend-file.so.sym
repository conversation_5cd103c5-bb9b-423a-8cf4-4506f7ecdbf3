MODULE Linux arm64 A5E0613EBDAA9997653492C13BC0C3CB0 libprintbackend-file.so
INFO CODE_ID 3E61E0A5AABD9799653492C13BC0C3CB8C49FD32
PUBLIC 3bd0 0 pb_module_init
PUBLIC 3c94 0 pb_module_exit
PUBLIC 3cb0 0 gtk_print_backend_file_get_type
PUBLIC 3cd0 0 gtk_print_backend_file_new
PUBLIC 3cf0 0 pb_module_create
STACK CFI INIT 26e0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2710 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2750 48 .cfa: sp 0 + .ra: x30
STACK CFI 2754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 275c x19: .cfa -16 + ^
STACK CFI 2794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 27b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e0 x19: .cfa -16 + ^
STACK CFI 2838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2840 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2848 .cfa: sp 64 +
STACK CFI 284c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2854 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 285c x21: .cfa -16 + ^
STACK CFI 2908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2910 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2918 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2924 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2938 x21: .cfa -16 + ^
STACK CFI 29d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 29e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 29f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 29f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a08 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 2a20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a58 x21: x21 x22: x22
STACK CFI 2a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 2a78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 2aa0 bc .cfa: sp 0 + .ra: x30
STACK CFI 2aac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2abc x19: .cfa -16 + ^
STACK CFI 2b28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2b60 128 .cfa: sp 0 + .ra: x30
STACK CFI 2b68 .cfa: sp 80 +
STACK CFI 2b78 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b8c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2c2c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 2c34 .cfa: sp 80 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2c90 180 .cfa: sp 0 + .ra: x30
STACK CFI 2c98 .cfa: sp 64 +
STACK CFI 2ca8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cbc x21: .cfa -16 + ^
STACK CFI 2dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2dd4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e10 254 .cfa: sp 0 + .ra: x30
STACK CFI 2e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3064 e8 .cfa: sp 0 + .ra: x30
STACK CFI 306c .cfa: sp 96 +
STACK CFI 3078 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3094 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30ac x23: .cfa -16 + ^
STACK CFI 30f0 x19: x19 x20: x20
STACK CFI 30f8 x21: x21 x22: x22
STACK CFI 30fc x23: x23
STACK CFI 3100 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3104 x19: x19 x20: x20
STACK CFI 3108 x21: x21 x22: x22
STACK CFI 310c x23: x23
STACK CFI 3134 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 313c .cfa: sp 96 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3140 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3144 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3148 x23: .cfa -16 + ^
STACK CFI INIT 3150 11c .cfa: sp 0 + .ra: x30
STACK CFI 3158 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3160 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 321c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3224 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3248 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3270 4dc .cfa: sp 0 + .ra: x30
STACK CFI 3278 .cfa: sp 240 +
STACK CFI 328c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3298 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 32ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 32c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 32cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 357c .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3750 170 .cfa: sp 0 + .ra: x30
STACK CFI 3758 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3760 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3770 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3790 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3800 x23: x23 x24: x24
STACK CFI 380c x21: x21 x22: x22
STACK CFI 3810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3818 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3828 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3898 x21: x21 x22: x22
STACK CFI 389c x23: x23 x24: x24
STACK CFI 38a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 38c0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 38c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38e0 x21: .cfa -16 + ^
STACK CFI 3978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3980 148 .cfa: sp 0 + .ra: x30
STACK CFI 3988 .cfa: sp 96 +
STACK CFI 3994 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 399c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 39a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 39b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39bc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3aa8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3ad0 fc .cfa: sp 0 + .ra: x30
STACK CFI 3ad8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3aec .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3b5c .cfa: sp 48 +
STACK CFI 3b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b70 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3bd0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3bd8 .cfa: sp 112 +
STACK CFI 3be8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bf8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c90 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c94 18 .cfa: sp 0 + .ra: x30
STACK CFI 3c9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ca4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cb0 20 .cfa: sp 0 + .ra: x30
STACK CFI 3cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cd0 20 .cfa: sp 0 + .ra: x30
STACK CFI 3cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cf0 18 .cfa: sp 0 + .ra: x30
STACK CFI 3cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d00 .cfa: sp 0 + .ra: .ra x29: x29
