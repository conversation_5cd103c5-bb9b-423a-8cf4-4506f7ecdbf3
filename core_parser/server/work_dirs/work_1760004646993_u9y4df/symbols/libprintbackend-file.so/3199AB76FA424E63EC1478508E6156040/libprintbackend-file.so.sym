MODULE Linux arm64 3199AB76FA424E63EC1478508E6156040 libprintbackend-file.so
INFO CODE_ID 76AB993142FA634EEC1478508E6156044EEBD50B
PUBLIC 3f44 0 g_io_module_load
PUBLIC 4010 0 g_io_module_unload
PUBLIC 4030 0 g_io_module_query
STACK CFI INIT 2880 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 28f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28fc x19: .cfa -16 + ^
STACK CFI 2934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2940 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2950 1c .cfa: sp 0 + .ra: x30
STACK CFI 2958 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2970 18 .cfa: sp 0 + .ra: x30
STACK CFI 2978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2990 ec .cfa: sp 0 + .ra: x30
STACK CFI 2998 .cfa: sp 304 +
STACK CFI 29a8 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 29b8 x19: .cfa -208 + ^
STACK CFI 2a70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a78 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2a80 94 .cfa: sp 0 + .ra: x30
STACK CFI 2a88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a90 x19: .cfa -16 + ^
STACK CFI 2b00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2b14 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2b1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b3c x21: .cfa -16 + ^
STACK CFI 2bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2be0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2bf4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2c0c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 2c24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c60 x21: x21 x22: x22
STACK CFI 2c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 2c80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 2ca4 bc .cfa: sp 0 + .ra: x30
STACK CFI 2cb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cc0 x19: .cfa -16 + ^
STACK CFI 2d2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2d50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d60 128 .cfa: sp 0 + .ra: x30
STACK CFI 2d68 .cfa: sp 80 +
STACK CFI 2d78 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d8c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2e2c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 2e34 .cfa: sp 80 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e90 188 .cfa: sp 0 + .ra: x30
STACK CFI 2e98 .cfa: sp 64 +
STACK CFI 2ea8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2eb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ebc x21: .cfa -16 + ^
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fdc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3020 254 .cfa: sp 0 + .ra: x30
STACK CFI 3028 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3034 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3070 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 311c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3274 144 .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 96 +
STACK CFI 3288 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3290 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 329c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 32c4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3328 x23: x23 x24: x24
STACK CFI 3358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3360 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3374 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3390 x23: x23 x24: x24
STACK CFI 3398 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 33b0 x23: x23 x24: x24
STACK CFI 33b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 33c0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 33c8 .cfa: sp 64 +
STACK CFI 33cc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33dc x21: .cfa -16 + ^
STACK CFI 3488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3490 11c .cfa: sp 0 + .ra: x30
STACK CFI 3498 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 355c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35b0 4dc .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 240 +
STACK CFI 35cc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 35d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 35ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3600 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 360c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 38b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 38bc .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3a90 170 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3aa0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ab0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ad0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3b40 x23: x23 x24: x24
STACK CFI 3b4c x21: x21 x22: x22
STACK CFI 3b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b68 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3bd8 x21: x21 x22: x22
STACK CFI 3bdc x23: x23 x24: x24
STACK CFI 3be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3be8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 3c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c1c x21: .cfa -16 + ^
STACK CFI 3cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3cc0 148 .cfa: sp 0 + .ra: x30
STACK CFI 3cc8 .cfa: sp 96 +
STACK CFI 3cd4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3cdc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3cf0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3cfc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3de8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3e10 134 .cfa: sp 0 + .ra: x30
STACK CFI 3e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e2c .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3efc .cfa: sp 48 +
STACK CFI 3f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f10 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f44 cc .cfa: sp 0 + .ra: x30
STACK CFI 3f4c .cfa: sp 112 +
STACK CFI 3f58 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 400c .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4010 18 .cfa: sp 0 + .ra: x30
STACK CFI 4018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4030 68 .cfa: sp 0 + .ra: x30
STACK CFI 4038 .cfa: sp 48 +
STACK CFI 404c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 408c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4094 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
