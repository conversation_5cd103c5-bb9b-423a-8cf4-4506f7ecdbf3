MODULE Linux arm64 B6053ABC5D45182294C76879276713DE0 libprintbackend-file.so
INFO CODE_ID BC3A05B6455D221894C76879276713DEEBE03E1C
PUBLIC 34b0 0 pb_module_init
PUBLIC 3574 0 pb_module_exit
PUBLIC 3590 0 gtk_print_backend_file_get_type
PUBLIC 35b0 0 gtk_print_backend_file_new
PUBLIC 35d0 0 pb_module_create
STACK CFI INIT 21a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2210 48 .cfa: sp 0 + .ra: x30
STACK CFI 2214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 221c x19: .cfa -16 + ^
STACK CFI 2254 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2260 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2270 1c .cfa: sp 0 + .ra: x30
STACK CFI 2278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2290 70 .cfa: sp 0 + .ra: x30
STACK CFI 2298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22a0 x19: .cfa -16 + ^
STACK CFI 22f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2300 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2308 .cfa: sp 64 +
STACK CFI 230c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2314 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 231c x21: .cfa -16 + ^
STACK CFI 23c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 23d0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 23d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23f8 x21: .cfa -16 + ^
STACK CFI 2494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 249c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 24b0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 24b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2580 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2594 b0 .cfa: sp 0 + .ra: x30
STACK CFI 259c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25ac x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 25c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2600 x21: x21 x22: x22
STACK CFI 2618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 2620 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 263c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 2644 bc .cfa: sp 0 + .ra: x30
STACK CFI 2650 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2660 x19: .cfa -16 + ^
STACK CFI 26cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 26f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 26f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2700 128 .cfa: sp 0 + .ra: x30
STACK CFI 2708 .cfa: sp 80 +
STACK CFI 2718 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2720 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 272c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 27cc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 27d4 .cfa: sp 80 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2830 12c .cfa: sp 0 + .ra: x30
STACK CFI 2838 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2840 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2848 x21: .cfa -16 + ^
STACK CFI 2878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2880 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 291c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2924 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2960 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2968 .cfa: sp 96 +
STACK CFI 2974 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2990 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 299c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29a8 x23: .cfa -16 + ^
STACK CFI 29e8 x19: x19 x20: x20
STACK CFI 29f0 x21: x21 x22: x22
STACK CFI 29f4 x23: x23
STACK CFI 29f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 29fc x19: x19 x20: x20
STACK CFI 2a00 x21: x21 x22: x22
STACK CFI 2a04 x23: x23
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a34 .cfa: sp 96 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a38 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a40 x23: .cfa -16 + ^
STACK CFI INIT 2a44 11c .cfa: sp 0 + .ra: x30
STACK CFI 2a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b60 4dc .cfa: sp 0 + .ra: x30
STACK CFI 2b68 .cfa: sp 240 +
STACK CFI 2b7c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b88 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2bb0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2bbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e6c .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3040 170 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3050 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3060 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3080 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 30f0 x23: x23 x24: x24
STACK CFI 30fc x21: x21 x22: x22
STACK CFI 3100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3118 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3188 x21: x21 x22: x22
STACK CFI 318c x23: x23 x24: x24
STACK CFI 3190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3198 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 31b0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 31c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31c8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 325c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3264 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 326c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3274 148 .cfa: sp 0 + .ra: x30
STACK CFI 327c .cfa: sp 96 +
STACK CFI 3288 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3290 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3298 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 32b0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 339c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 33c0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 33c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33dc .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 344c .cfa: sp 48 +
STACK CFI 3458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3460 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 34b0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 34b8 .cfa: sp 112 +
STACK CFI 34c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3570 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3574 18 .cfa: sp 0 + .ra: x30
STACK CFI 357c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3590 20 .cfa: sp 0 + .ra: x30
STACK CFI 3598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35d0 18 .cfa: sp 0 + .ra: x30
STACK CFI 35d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35e0 .cfa: sp 0 + .ra: .ra x29: x29
