MODULE Linux arm64 4DF6FA3598B4D8A747FFEABDEFDF34C10 libpipewire-module-roc-sink.so
INFO CODE_ID 35FAF64DB498A7D847FFEABDEFDF34C12956BA8D
PUBLIC 53c0 0 pipewire__module_init
STACK CFI INIT 16d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1740 48 .cfa: sp 0 + .ra: x30
STACK CFI 1744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 174c x19: .cfa -16 + ^
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 17a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17b0 x19: .cfa -16 + ^
STACK CFI 17e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17f0 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 17f8 .cfa: sp 480 +
STACK CFI 180c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 1824 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 182c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 1844 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 1ff0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ff8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 4480 248 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 128 +
STACK CFI 4494 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 449c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 44bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 44c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 44dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 44f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4594 x25: x25 x26: x26
STACK CFI 4598 x27: x27 x28: x28
STACK CFI 45a8 x19: x19 x20: x20
STACK CFI 45ac x23: x23 x24: x24
STACK CFI 45d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 45dc .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4624 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4674 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 469c .cfa: sp 128 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 46b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 46bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 46c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 46c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 46d0 110 .cfa: sp 0 + .ra: x30
STACK CFI 46ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46f8 x19: .cfa -16 + ^
STACK CFI 4724 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 472c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 47b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 47e0 54 .cfa: sp 0 + .ra: x30
STACK CFI 47e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47f0 x19: .cfa -16 + ^
STACK CFI 482c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4834 104 .cfa: sp 0 + .ra: x30
STACK CFI 483c .cfa: sp 96 +
STACK CFI 4840 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4848 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4898 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4940 ac .cfa: sp 0 + .ra: x30
STACK CFI 4948 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4950 x19: .cfa -16 + ^
STACK CFI 49dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 49f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a00 x19: .cfa -16 + ^
STACK CFI 4a38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a40 980 .cfa: sp 0 + .ra: x30
STACK CFI 4a48 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a54 .cfa: sp 1552 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4b14 x21: .cfa -48 + ^
STACK CFI 4b18 x22: .cfa -40 + ^
STACK CFI 4b1c x23: .cfa -32 + ^
STACK CFI 4b20 x24: .cfa -24 + ^
STACK CFI 4dc8 x21: x21
STACK CFI 4dd0 x22: x22
STACK CFI 4dd8 x23: x23
STACK CFI 4ddc x24: x24
STACK CFI 4dfc .cfa: sp 80 +
STACK CFI 4e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e10 .cfa: sp 1552 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e38 x21: x21
STACK CFI 4e40 x22: x22
STACK CFI 4e44 x23: x23
STACK CFI 4e48 x24: x24
STACK CFI 4e4c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4f44 x25: .cfa -16 + ^
STACK CFI 4fb0 x25: x25
STACK CFI 4fe4 x21: x21
STACK CFI 4fe8 x22: x22
STACK CFI 4fec x23: x23
STACK CFI 4ff0 x24: x24
STACK CFI 4ff4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5048 x21: x21
STACK CFI 504c x22: x22
STACK CFI 5050 x23: x23
STACK CFI 5054 x24: x24
STACK CFI 5058 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 50dc x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 513c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 51b8 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 51d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 51dc x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 524c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5278 x21: x21
STACK CFI 5280 x22: x22
STACK CFI 5284 x23: x23
STACK CFI 5288 x24: x24
STACK CFI 528c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 52f8 x21: x21
STACK CFI 52fc x22: x22
STACK CFI 5300 x23: x23
STACK CFI 5304 x24: x24
STACK CFI 5308 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 53a8 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 53ac x21: .cfa -48 + ^
STACK CFI 53b0 x22: .cfa -40 + ^
STACK CFI 53b4 x23: .cfa -32 + ^
STACK CFI 53b8 x24: .cfa -24 + ^
STACK CFI 53bc x25: .cfa -16 + ^
STACK CFI INIT 53c0 8c0 .cfa: sp 0 + .ra: x30
STACK CFI 53c8 .cfa: sp 112 +
STACK CFI 53d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 53dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 53e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 53f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56d4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5734 x25: x25 x26: x26
STACK CFI 57fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5804 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5900 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5940 x25: x25 x26: x26
STACK CFI 5b18 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5b48 x25: x25 x26: x26
STACK CFI 5bb8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5bf0 x25: x25 x26: x26
STACK CFI 5bf4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5c2c x25: x25 x26: x26
STACK CFI 5c6c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5c70 x25: x25 x26: x26
