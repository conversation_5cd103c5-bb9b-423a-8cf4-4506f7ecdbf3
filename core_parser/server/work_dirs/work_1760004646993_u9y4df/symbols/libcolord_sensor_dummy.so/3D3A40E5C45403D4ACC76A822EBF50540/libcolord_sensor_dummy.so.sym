MODULE Linux arm64 3D3A40E5C45403D4ACC76A822EBF50540 libcolord_sensor_dummy.so
INFO CODE_ID E5403A3D54C4D403ACC76A822EBF505494D64751
PUBLIC 1410 0 cd_sensor_get_sample_async
PUBLIC 1510 0 cd_sensor_get_sample_finish
PUBLIC 1584 0 cd_sensor_set_options_finish
PUBLIC 1600 0 cd_sensor_set_options_async
PUBLIC 1860 0 cd_sensor_coldplug
STACK CFI INIT 11f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1220 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1260 48 .cfa: sp 0 + .ra: x30
STACK CFI 1264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 126c x19: .cfa -16 + ^
STACK CFI 12a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 12c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12d0 x19: .cfa -16 + ^
STACK CFI 1318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1320 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1328 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1330 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1338 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 13d4 34 .cfa: sp 0 + .ra: x30
STACK CFI 13dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13e4 x19: .cfa -16 + ^
STACK CFI 1400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1410 100 .cfa: sp 0 + .ra: x30
STACK CFI 1418 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1420 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 142c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1438 x23: .cfa -16 + ^
STACK CFI 14b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 14bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 14d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 14e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 14fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1510 74 .cfa: sp 0 + .ra: x30
STACK CFI 1518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1520 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1550 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 157c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1584 74 .cfa: sp 0 + .ra: x30
STACK CFI 158c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1594 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 15f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1600 25c .cfa: sp 0 + .ra: x30
STACK CFI 1608 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1610 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1624 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1630 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 167c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 16a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1744 x25: x25 x26: x26
STACK CFI 175c x27: x27 x28: x28
STACK CFI 1760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1768 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1790 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 17c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1800 x25: x25 x26: x26
STACK CFI 1814 x27: x27 x28: x28
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1820 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1848 x25: x25 x26: x26
STACK CFI INIT 1860 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 1868 .cfa: sp 128 +
STACK CFI 187c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1890 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 189c x23: .cfa -16 + ^
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 19e4 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
