MODULE Linux arm64 8772F9B377228080CE486B50E61431BE0 libshmmem.so
INFO CODE_ID B3F9728722778080CE486B50E61431BE
PUBLIC 1090 0 shm_heap_fd
PUBLIC 1160 0 shm_heap_close
PUBLIC 11a0 0 shm_buf_open
PUBLIC 12c0 0 shm_buf_close
PUBLIC 1330 0 shm_heap_get_event
PUBLIC 14c0 0 shm_heap_query
PUBLIC 16c0 0 shm_get_uuid
PUBLIC 17a0 0 shm_buf_open_with_uuid
PUBLIC 18a0 0 shm_mmap
PUBLIC 1930 0 shm_reader_mmap
PUBLIC 19d0 0 shm_unmap
STACK CFI INIT fc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1030 48 .cfa: sp 0 + .ra: x30
STACK CFI 1034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 103c x19: .cfa -16 + ^
STACK CFI 1074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1080 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1090 d0 .cfa: sp 0 + .ra: x30
STACK CFI 1094 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 10a4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 10f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10fc .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x29: .cfa -192 + ^
STACK CFI 110c x21: .cfa -160 + ^
STACK CFI 1144 x21: x21
STACK CFI 1150 x21: .cfa -160 + ^
STACK CFI 1154 x21: x21
STACK CFI 115c x21: .cfa -160 + ^
STACK CFI INIT 1160 34 .cfa: sp 0 + .ra: x30
STACK CFI 1164 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 116c x19: .cfa -16 + ^
STACK CFI 1190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11a0 120 .cfa: sp 0 + .ra: x30
STACK CFI 11a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 124c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1250 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 12c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 12c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1300 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1314 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 131c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1330 188 .cfa: sp 0 + .ra: x30
STACK CFI 1334 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1348 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1370 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1374 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1394 x25: .cfa -48 + ^
STACK CFI 1410 x19: x19 x20: x20
STACK CFI 1418 x23: x23 x24: x24
STACK CFI 141c x25: x25
STACK CFI 1444 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1448 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 145c x19: x19 x20: x20
STACK CFI 1464 x23: x23 x24: x24
STACK CFI 1468 x25: x25
STACK CFI 146c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI 1490 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 1494 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1498 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 149c x25: .cfa -48 + ^
STACK CFI 14a0 x25: x25
STACK CFI 14ac x19: x19 x20: x20
STACK CFI 14b4 x23: x23 x24: x24
STACK CFI INIT 14c0 200 .cfa: sp 0 + .ra: x30
STACK CFI 14c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14d4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14e0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 14e8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1540 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1544 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 15b4 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 15bc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 161c x21: x21 x22: x22
STACK CFI 1624 x27: x27 x28: x28
STACK CFI 1650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1654 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 1670 x27: x27 x28: x28
STACK CFI 1678 x21: x21 x22: x22
STACK CFI 16a4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 16a8 x21: x21 x22: x22
STACK CFI 16b0 x27: x27 x28: x28
STACK CFI 16b8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 16bc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 16c0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 16c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 175c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1760 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 17a0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 17a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1878 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18a0 8c .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 18e8 x21: .cfa -16 + ^
STACK CFI 1924 x21: x21
STACK CFI INIT 1930 98 .cfa: sp 0 + .ra: x30
STACK CFI 1934 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1944 x19: .cfa -160 + ^
STACK CFI 1990 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1994 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 19d0 84 .cfa: sp 0 + .ra: x30
STACK CFI 19d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
