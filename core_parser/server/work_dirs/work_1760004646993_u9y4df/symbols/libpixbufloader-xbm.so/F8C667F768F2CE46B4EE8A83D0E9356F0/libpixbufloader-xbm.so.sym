MODULE Linux arm64 F8C667F768F2CE46B4EE8A83D0E9356F0 libpixbufloader-xbm.so
INFO CODE_ID F767C6F8F26846CEB4EE8A83D0E9356F48BCC582
PUBLIC 1c50 0 fill_vtable
PUBLIC 1c94 0 fill_info
STACK CFI INIT 1010 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1040 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1080 48 .cfa: sp 0 + .ra: x30
STACK CFI 1084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 108c x19: .cfa -16 + ^
STACK CFI 10c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 10e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1104 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1110 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 116c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1174 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 11a0 140 .cfa: sp 0 + .ra: x30
STACK CFI 11a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1238 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1268 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12e0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 12e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 132c x21: x21 x22: x22
STACK CFI 1330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1384 x21: x21 x22: x22
STACK CFI 1390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1398 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 13c0 4b4 .cfa: sp 0 + .ra: x30
STACK CFI 13c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 13e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 13f8 .cfa: sp 688 + x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 17f0 .cfa: sp 96 +
STACK CFI 1808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1810 .cfa: sp 688 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1874 2ec .cfa: sp 0 + .ra: x30
STACK CFI 187c .cfa: sp 128 +
STACK CFI 188c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1898 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 18d8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 19e0 x23: x23 x24: x24
STACK CFI 19e4 x25: x25 x26: x26
STACK CFI 1a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a1c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1ab4 x23: x23 x24: x24
STACK CFI 1ab8 x25: x25 x26: x26
STACK CFI 1abc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1ac0 x23: x23 x24: x24
STACK CFI 1ac4 x25: x25 x26: x26
STACK CFI 1ac8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1ad4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1b0c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1b4c x23: x23 x24: x24
STACK CFI 1b50 x25: x25 x26: x26
STACK CFI 1b58 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1b5c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 1b60 20 .cfa: sp 0 + .ra: x30
STACK CFI 1b68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b80 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c50 44 .cfa: sp 0 + .ra: x30
STACK CFI 1c58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c94 5c .cfa: sp 0 + .ra: x30
STACK CFI 1c9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cb0 .cfa: sp 0 + .ra: .ra x29: x29
