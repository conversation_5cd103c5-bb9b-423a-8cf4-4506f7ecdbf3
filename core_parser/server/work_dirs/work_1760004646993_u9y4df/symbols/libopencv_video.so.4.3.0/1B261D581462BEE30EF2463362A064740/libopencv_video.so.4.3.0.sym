MODULE Linux arm64 1B261D581462BEE30EF2463362A064740 libopencv_video.so.4.3
INFO CODE_ID 581D261B6214E3BE0EF2463362A064743E8641A9
PUBLIC a3b0 0 _init
PUBLIC b1e0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.36]
PUBLIC b280 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.40]
PUBLIC b320 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.21]
PUBLIC b3c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.18]
PUBLIC b460 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::checkParam()
PUBLIC b540 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.99]
PUBLIC b5e0 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::polynomialExpansionOcl(cv::UMat const&, cv::UMat&)
PUBLIC b940 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.86]
PUBLIC b9e0 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::updateMatricesOcl(cv::UMat const&, cv::UMat const&, cv::UMat const&, cv::UMat const&, cv::UMat&) [clone .isra.87]
PUBLIC bdb8 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::updateFlowOcl(cv::UMat const&, cv::UMat&, cv::UMat&) [clone .isra.88]
PUBLIC c090 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setGaussianBlurKernel(int, double)
PUBLIC c160 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::allocMatFromBuf(int, int, int, cv::UMat&) [clone .constprop.115]
PUBLIC c350 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::operator()(cv::UMat const&, cv::UMat const&, cv::UMat&, cv::UMat&)
PUBLIC dba0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.50]
PUBLIC dc40 0 _GLOBAL__sub_I_optical_flow_io.cpp
PUBLIC dc70 0 call_weak_fn
PUBLIC dc88 0 deregister_tm_clones
PUBLIC dcc0 0 register_tm_clones
PUBLIC dd00 0 __do_global_dtors_aux
PUBLIC dd48 0 frame_dummy
PUBLIC dd80 0 cv::Algorithm::clear()
PUBLIC dd88 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC dd90 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC dd98 0 cv::Algorithm::empty() const
PUBLIC dda0 0 cv::BackgroundSubtractorKNNImpl::getHistory() const
PUBLIC dda8 0 cv::BackgroundSubtractorKNNImpl::setHistory(int)
PUBLIC ddb0 0 cv::BackgroundSubtractorKNNImpl::getNSamples() const
PUBLIC ddb8 0 cv::BackgroundSubtractorKNNImpl::setNSamples(int)
PUBLIC ddc0 0 cv::BackgroundSubtractorKNNImpl::getkNNSamples() const
PUBLIC ddc8 0 cv::BackgroundSubtractorKNNImpl::setkNNSamples(int)
PUBLIC ddd0 0 cv::BackgroundSubtractorKNNImpl::getDist2Threshold() const
PUBLIC dde0 0 cv::BackgroundSubtractorKNNImpl::setDist2Threshold(double)
PUBLIC ddf0 0 cv::BackgroundSubtractorKNNImpl::getDetectShadows() const
PUBLIC ddf8 0 cv::BackgroundSubtractorKNNImpl::getShadowValue() const
PUBLIC de00 0 cv::BackgroundSubtractorKNNImpl::setShadowValue(int)
PUBLIC de08 0 cv::BackgroundSubtractorKNNImpl::getShadowThreshold() const
PUBLIC de18 0 cv::BackgroundSubtractorKNNImpl::setShadowThreshold(double)
PUBLIC de28 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorKNNImpl, std::allocator<cv::BackgroundSubtractorKNNImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC de30 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorKNNImpl, std::allocator<cv::BackgroundSubtractorKNNImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC de38 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorKNNImpl, std::allocator<cv::BackgroundSubtractorKNNImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC de40 0 cv::KNNInvoker::~KNNInvoker()
PUBLIC de50 0 cv::KNNInvoker::~KNNInvoker()
PUBLIC de78 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorKNNImpl, std::allocator<cv::BackgroundSubtractorKNNImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC dec8 0 cv::BackgroundSubtractorKNNImpl::read(cv::FileNode const&)
PUBLIC e0d0 0 cv::KNNInvoker::operator()(cv::Range const&) const
PUBLIC e5f0 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorKNNImpl, std::allocator<cv::BackgroundSubtractorKNNImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ea30 0 cv::BackgroundSubtractorKNNImpl::~BackgroundSubtractorKNNImpl()
PUBLIC ee70 0 cv::BackgroundSubtractorKNNImpl::~BackgroundSubtractorKNNImpl()
PUBLIC f2a8 0 cv::BackgroundSubtractorKNNImpl::write(cv::FileStorage&) const
PUBLIC f8b8 0 cv::BackgroundSubtractorKNNImpl::setDetectShadows(bool)
PUBLIC fa08 0 cv::Mat::~Mat()
PUBLIC fa98 0 cv::BackgroundSubtractorKNNImpl::initialize(cv::Size_<int>, int)
PUBLIC 104f8 0 cv::BackgroundSubtractorKNNImpl::ocl_apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 10c58 0 cv::BackgroundSubtractorKNNImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 114d0 0 cv::BackgroundSubtractorKNNImpl::ocl_getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 11640 0 cv::createBackgroundSubtractorKNN(int, double, bool)
PUBLIC 11b60 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 11c20 0 cv::BackgroundSubtractorKNNImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 12070 0 cv::BackgroundSubtractorMOG2Impl::getHistory() const
PUBLIC 12078 0 cv::BackgroundSubtractorMOG2Impl::setHistory(int)
PUBLIC 12080 0 cv::BackgroundSubtractorMOG2Impl::getNMixtures() const
PUBLIC 12088 0 cv::BackgroundSubtractorMOG2Impl::setNMixtures(int)
PUBLIC 12090 0 cv::BackgroundSubtractorMOG2Impl::getBackgroundRatio() const
PUBLIC 120a0 0 cv::BackgroundSubtractorMOG2Impl::setBackgroundRatio(double)
PUBLIC 120b0 0 cv::BackgroundSubtractorMOG2Impl::getVarThreshold() const
PUBLIC 120b8 0 cv::BackgroundSubtractorMOG2Impl::setVarThreshold(double)
PUBLIC 120c0 0 cv::BackgroundSubtractorMOG2Impl::getVarThresholdGen() const
PUBLIC 120d0 0 cv::BackgroundSubtractorMOG2Impl::setVarThresholdGen(double)
PUBLIC 120e0 0 cv::BackgroundSubtractorMOG2Impl::getVarInit() const
PUBLIC 120f0 0 cv::BackgroundSubtractorMOG2Impl::setVarInit(double)
PUBLIC 12100 0 cv::BackgroundSubtractorMOG2Impl::getVarMin() const
PUBLIC 12110 0 cv::BackgroundSubtractorMOG2Impl::setVarMin(double)
PUBLIC 12120 0 cv::BackgroundSubtractorMOG2Impl::getVarMax() const
PUBLIC 12130 0 cv::BackgroundSubtractorMOG2Impl::setVarMax(double)
PUBLIC 12140 0 cv::BackgroundSubtractorMOG2Impl::getComplexityReductionThreshold() const
PUBLIC 12150 0 cv::BackgroundSubtractorMOG2Impl::setComplexityReductionThreshold(double)
PUBLIC 12160 0 cv::BackgroundSubtractorMOG2Impl::getDetectShadows() const
PUBLIC 12168 0 cv::BackgroundSubtractorMOG2Impl::getShadowValue() const
PUBLIC 12170 0 cv::BackgroundSubtractorMOG2Impl::setShadowValue(int)
PUBLIC 12178 0 cv::BackgroundSubtractorMOG2Impl::getShadowThreshold() const
PUBLIC 12188 0 cv::BackgroundSubtractorMOG2Impl::setShadowThreshold(double)
PUBLIC 12198 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorMOG2Impl, std::allocator<cv::BackgroundSubtractorMOG2Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 121a0 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorMOG2Impl, std::allocator<cv::BackgroundSubtractorMOG2Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 121a8 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorMOG2Impl, std::allocator<cv::BackgroundSubtractorMOG2Impl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 121b0 0 cv::MOG2Invoker::~MOG2Invoker()
PUBLIC 121c0 0 cv::MOG2Invoker::~MOG2Invoker()
PUBLIC 121e8 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorMOG2Impl, std::allocator<cv::BackgroundSubtractorMOG2Impl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 12238 0 cv::BackgroundSubtractorMOG2Impl::read(cv::FileNode const&)
PUBLIC 124e0 0 std::_Sp_counted_ptr_inplace<cv::BackgroundSubtractorMOG2Impl, std::allocator<cv::BackgroundSubtractorMOG2Impl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 12668 0 cv::BackgroundSubtractorMOG2Impl::~BackgroundSubtractorMOG2Impl()
PUBLIC 127f8 0 cv::BackgroundSubtractorMOG2Impl::~BackgroundSubtractorMOG2Impl()
PUBLIC 12980 0 cv::MOG2Invoker::operator()(cv::Range const&) const
PUBLIC 13c90 0 cv::BackgroundSubtractorMOG2Impl::write(cv::FileStorage&) const
PUBLIC 146c8 0 cv::BackgroundSubtractorMOG2Impl::setDetectShadows(bool)
PUBLIC 14828 0 cv::BackgroundSubtractorMOG2Impl::ocl_apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 15280 0 cv::BackgroundSubtractorMOG2Impl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 15e50 0 cv::BackgroundSubtractorMOG2Impl::ocl_getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 16010 0 cv::createBackgroundSubtractorMOG2(int, double, bool)
PUBLIC 16360 0 void cv::BackgroundSubtractorMOG2Impl::getBackgroundImage_intern<unsigned char, 1>(cv::_OutputArray const&) const
PUBLIC 16680 0 void cv::BackgroundSubtractorMOG2Impl::getBackgroundImage_intern<unsigned char, 3>(cv::_OutputArray const&) const
PUBLIC 16a30 0 void cv::BackgroundSubtractorMOG2Impl::getBackgroundImage_intern<float, 1>(cv::_OutputArray const&) const
PUBLIC 16d30 0 void cv::BackgroundSubtractorMOG2Impl::getBackgroundImage_intern<float, 3>(cv::_OutputArray const&) const
PUBLIC 17048 0 cv::BackgroundSubtractorMOG2Impl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 171b0 0 cv::meanShift(cv::_InputArray const&, cv::Rect_<int>&, cv::TermCriteria)
PUBLIC 17ad0 0 cv::CamShift(cv::_InputArray const&, cv::Rect_<int>&, cv::TermCriteria)
PUBLIC 18448 0 cv::DISOpticalFlowImpl::getFinestScale() const
PUBLIC 18450 0 cv::DISOpticalFlowImpl::setFinestScale(int)
PUBLIC 18458 0 cv::DISOpticalFlowImpl::getPatchSize() const
PUBLIC 18460 0 cv::DISOpticalFlowImpl::setPatchSize(int)
PUBLIC 18468 0 cv::DISOpticalFlowImpl::getPatchStride() const
PUBLIC 18470 0 cv::DISOpticalFlowImpl::setPatchStride(int)
PUBLIC 18478 0 cv::DISOpticalFlowImpl::getGradientDescentIterations() const
PUBLIC 18480 0 cv::DISOpticalFlowImpl::setGradientDescentIterations(int)
PUBLIC 18488 0 cv::DISOpticalFlowImpl::getVariationalRefinementIterations() const
PUBLIC 18490 0 cv::DISOpticalFlowImpl::setVariationalRefinementIterations(int)
PUBLIC 18498 0 cv::DISOpticalFlowImpl::getVariationalRefinementAlpha() const
PUBLIC 184a0 0 cv::DISOpticalFlowImpl::setVariationalRefinementAlpha(float)
PUBLIC 184a8 0 cv::DISOpticalFlowImpl::getVariationalRefinementDelta() const
PUBLIC 184b0 0 cv::DISOpticalFlowImpl::setVariationalRefinementDelta(float)
PUBLIC 184b8 0 cv::DISOpticalFlowImpl::getVariationalRefinementGamma() const
PUBLIC 184c0 0 cv::DISOpticalFlowImpl::setVariationalRefinementGamma(float)
PUBLIC 184c8 0 cv::DISOpticalFlowImpl::getUseMeanNormalization() const
PUBLIC 184d0 0 cv::DISOpticalFlowImpl::setUseMeanNormalization(bool)
PUBLIC 184d8 0 cv::DISOpticalFlowImpl::getUseSpatialPropagation() const
PUBLIC 184e0 0 cv::DISOpticalFlowImpl::setUseSpatialPropagation(bool)
PUBLIC 184e8 0 std::_Sp_counted_ptr_inplace<cv::DISOpticalFlowImpl, std::allocator<cv::DISOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 184f0 0 std::_Sp_counted_ptr_inplace<cv::DISOpticalFlowImpl, std::allocator<cv::DISOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 184f8 0 std::_Sp_counted_ptr_inplace<cv::DISOpticalFlowImpl, std::allocator<cv::DISOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 18500 0 std::_Sp_counted_ptr_inplace<cv::DISOpticalFlowImpl, std::allocator<cv::DISOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 18550 0 cv::DISOpticalFlowImpl::Densification_ParBody::operator()(cv::Range const&) const
PUBLIC 188f8 0 cv::DISOpticalFlowImpl::PatchInverseSearch_ParBody::~PatchInverseSearch_ParBody()
PUBLIC 18908 0 cv::DISOpticalFlowImpl::PatchInverseSearch_ParBody::~PatchInverseSearch_ParBody()
PUBLIC 18930 0 cv::DISOpticalFlowImpl::Densification_ParBody::~Densification_ParBody()
PUBLIC 18940 0 cv::DISOpticalFlowImpl::Densification_ParBody::~Densification_ParBody()
PUBLIC 18968 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.79]
PUBLIC 18a48 0 cv::Mat::Mat(cv::Mat const&)
PUBLIC 18ac8 0 cv::DISOpticalFlowImpl::~DISOpticalFlowImpl()
PUBLIC 193c0 0 cv::DISOpticalFlowImpl::~DISOpticalFlowImpl()
PUBLIC 193d8 0 std::_Sp_counted_ptr_inplace<cv::DISOpticalFlowImpl, std::allocator<cv::DISOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 193e0 0 cv::Mat::release()
PUBLIC 19458 0 cv::DISOpticalFlowImpl::collectGarbage()
PUBLIC 19e60 0 cv::DISOpticalFlowImpl::precomputeStructureTensor(cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC 1a8a8 0 cv::DISOpticalFlowImpl::autoSelectPatchSizeAndScales(int)
PUBLIC 1a9a0 0 cv::computeSSDMeanNorm(unsigned char*, unsigned char*, int, int, float, float, float, float, int)
PUBLIC 1aba8 0 cv::DISOpticalFlowImpl::PatchInverseSearch_ParBody::operator()(cv::Range const&) const
PUBLIC 1c820 0 cv::DISOpticalFlowImpl::ocl_Densification(cv::UMat&, cv::UMat&, cv::UMat&, cv::UMat&)
PUBLIC 1caf0 0 cv::DISOpticalFlowImpl::ocl_precomputeStructureTensor(cv::UMat&, cv::UMat&, cv::UMat&, cv::UMat&, cv::UMat&, cv::UMat&, cv::UMat&)
PUBLIC 1d190 0 std::vector<cv::Mat_<unsigned char>, std::allocator<cv::Mat_<unsigned char> > >::~vector()
PUBLIC 1d250 0 std::vector<cv::Mat_<short>, std::allocator<cv::Mat_<short> > >::~vector()
PUBLIC 1d310 0 std::vector<cv::Mat_<float>, std::allocator<cv::Mat_<float> > >::~vector()
PUBLIC 1d3d0 0 std::vector<cv::Ptr<cv::VariationalRefinement>, std::allocator<cv::Ptr<cv::VariationalRefinement> > >::~vector()
PUBLIC 1d510 0 std::vector<cv::UMat, std::allocator<cv::UMat> >::~vector()
PUBLIC 1d568 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 1d620 0 std::vector<cv::Mat_<unsigned char>, std::allocator<cv::Mat_<unsigned char> > >::_M_default_append(unsigned long)
PUBLIC 1d9a8 0 std::vector<cv::Mat_<short>, std::allocator<cv::Mat_<short> > >::_M_default_append(unsigned long)
PUBLIC 1dd38 0 std::vector<cv::Mat_<float>, std::allocator<cv::Mat_<float> > >::_M_default_append(unsigned long)
PUBLIC 1e0c8 0 std::vector<cv::Mat_<float>, std::allocator<cv::Mat_<float> > >::resize(unsigned long)
PUBLIC 1e1c0 0 cv::DISOpticalFlowImpl::prepareBuffers(cv::Mat&, cv::Mat&, cv::Mat&, bool)
PUBLIC 1f670 0 std::vector<cv::UMat, std::allocator<cv::UMat> >::_M_default_append(unsigned long)
PUBLIC 1f980 0 cv::DISOpticalFlowImpl::ocl_prepareBuffers(cv::UMat&, cv::UMat&, cv::_InputArray const&, bool)
PUBLIC 209d0 0 cv::DISOpticalFlowImpl::ocl_calc(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&)
PUBLIC 21fc0 0 cv::DISOpticalFlowImpl::calc(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&)
PUBLIC 22cb0 0 void std::vector<cv::Ptr<cv::VariationalRefinement>, std::allocator<cv::Ptr<cv::VariationalRefinement> > >::_M_emplace_back_aux<cv::Ptr<cv::VariationalRefinement> >(cv::Ptr<cv::VariationalRefinement>&&)
PUBLIC 22ed0 0 cv::DISOpticalFlowImpl::DISOpticalFlowImpl()
PUBLIC 23848 0 cv::DISOpticalFlow::create(int)
PUBLIC 23bb8 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.24]
PUBLIC 23c98 0 cv::Mat::create(int, int, int) [clone .constprop.25]
PUBLIC 23d00 0 project_onto_jacobian_ECC(cv::Mat const&, cv::Mat const&, cv::Mat&)
PUBLIC 24360 0 cv::MatExpr::operator cv::Mat() const
PUBLIC 243f0 0 cv::MatExpr::~MatExpr()
PUBLIC 245a0 0 image_jacobian_homo_ECC(cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat&)
PUBLIC 27280 0 cv::computeECC(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 27b20 0 cv::findTransformECC(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, int, cv::TermCriteria, cv::_InputArray const&, int)
PUBLIC 2b7e8 0 cv::findTransformECC(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, int, cv::TermCriteria, cv::_InputArray const&)
PUBLIC 2b7f0 0 cv::KalmanFilter::KalmanFilter()
PUBLIC 2ba20 0 cv::KalmanFilter::init(int, int, int, int)
PUBLIC 2ce50 0 cv::KalmanFilter::KalmanFilter(int, int, int, int)
PUBLIC 2d110 0 cv::KalmanFilter::predict(cv::Mat const&)
PUBLIC 2d7a0 0 cv::KalmanFilter::correct(cv::Mat const&)
PUBLIC 2e658 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::getWinSize() const
PUBLIC 2e668 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::setWinSize(cv::Size_<int>)
PUBLIC 2e678 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::getMaxLevel() const
PUBLIC 2e680 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::setMaxLevel(int)
PUBLIC 2e688 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::getTermCriteria() const
PUBLIC 2e690 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::setTermCriteria(cv::TermCriteria&)
PUBLIC 2e6a0 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::getFlags() const
PUBLIC 2e6a8 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::setFlags(int)
PUBLIC 2e6b0 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::getMinEigThreshold() const
PUBLIC 2e6b8 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::setMinEigThreshold(double)
PUBLIC 2e6c0 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 2e6c8 0 cv::detail::SharrDerivInvoker::~SharrDerivInvoker()
PUBLIC 2e6d8 0 cv::detail::LKTrackerInvoker::~LKTrackerInvoker()
PUBLIC 2e6e8 0 cv::detail::SharrDerivInvoker::~SharrDerivInvoker()
PUBLIC 2e710 0 cv::detail::LKTrackerInvoker::~LKTrackerInvoker()
PUBLIC 2e738 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 2e740 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 2e748 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 2e798 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::~SparsePyrLKOpticalFlowImpl()
PUBLIC 2e7b0 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 2e7d0 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::~SparsePyrLKOpticalFlowImpl()
PUBLIC 2e7f8 0 cv::detail::SharrDerivInvoker::operator()(cv::Range const&) const
PUBLIC 2f588 0 (anonymous namespace)::calcSharrDeriv(cv::Mat const&, cv::Mat&)
PUBLIC 2f6e0 0 cv::detail::LKTrackerInvoker::operator()(cv::Range const&) const
PUBLIC 30a28 0 cv::UMat::create(int, int, int, cv::UMatUsageFlags)
PUBLIC 30a90 0 cv::UMat::operator=(cv::UMat&&)
PUBLIC 30bb0 0 cv::buildOpticalFlowPyramid(cv::_InputArray const&, cv::_OutputArray const&, cv::Size_<int>, int, bool, int, int, bool)
PUBLIC 31cb0 0 cv::SparsePyrLKOpticalFlow::create(cv::Size_<int>, int, cv::TermCriteria, int, double)
PUBLIC 31db0 0 cv::calcOpticalFlowPyrLK(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Size_<int>, int, cv::TermCriteria, int, double)
PUBLIC 31f00 0 std::vector<cv::UMat, std::allocator<cv::UMat> >::resize(unsigned long)
PUBLIC 31f90 0 cv::(anonymous namespace)::SparsePyrLKOpticalFlowImpl::calc(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 34a80 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_default_append(unsigned long)
PUBLIC 34bd0 0 std::vector<unsigned char, std::allocator<unsigned char> >::_M_default_append(unsigned long)
PUBLIC 34d10 0 cv::estimateRigidTransform(cv::_InputArray const&, cv::_InputArray const&, bool)
PUBLIC 35f00 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getNumLevels() const
PUBLIC 35f08 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setNumLevels(int)
PUBLIC 35f10 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getPyrScale() const
PUBLIC 35f18 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setPyrScale(double)
PUBLIC 35f20 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getFastPyramids() const
PUBLIC 35f28 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setFastPyramids(bool)
PUBLIC 35f30 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getWinSize() const
PUBLIC 35f38 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setWinSize(int)
PUBLIC 35f40 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getNumIters() const
PUBLIC 35f48 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setNumIters(int)
PUBLIC 35f50 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getPolyN() const
PUBLIC 35f58 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setPolyN(int)
PUBLIC 35f60 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getPolySigma() const
PUBLIC 35f68 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setPolySigma(double)
PUBLIC 35f70 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::getFlags() const
PUBLIC 35f78 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::setFlags(int)
PUBLIC 35f80 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::FarnebackOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::FarnebackOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 35f88 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::FarnebackOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::FarnebackOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 35fd8 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::FarnebackOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::FarnebackOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 35fe0 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::FarnebackOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::FarnebackOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 35fe8 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::~FarnebackOpticalFlowImpl()
PUBLIC 36130 0 cv::FarnebackUpdateMatrices(cv::Mat const&, cv::Mat const&, cv::Mat const&, cv::Mat&, int, int)
PUBLIC 36620 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::collectGarbage()
PUBLIC 36a80 0 std::_Sp_counted_ptr_inplace<cv::(anonymous namespace)::FarnebackOpticalFlowImpl, std::allocator<cv::(anonymous namespace)::FarnebackOpticalFlowImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 36bc8 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::~FarnebackOpticalFlowImpl()
PUBLIC 36d18 0 cv::Mat::Mat(int, int, int, void*, unsigned long)
PUBLIC 36e90 0 cv::UMat::UMat(cv::UMat const&)
PUBLIC 36f10 0 cv::UMat::operator=(cv::UMat const&)
PUBLIC 37040 0 cv::UMat::empty() const
PUBLIC 370c0 0 cv::FarnebackPrepareGaussian(int, double, float*, float*, float*, double&, double&, double&, double&)
PUBLIC 37dd0 0 cv::FarnebackOpticalFlow::create(int, double, bool, int, int, int, double, int)
PUBLIC 38180 0 cv::calcOpticalFlowFarneback(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, double, int, int, int, int, double, int)
PUBLIC 386a0 0 void std::vector<cv::UMat, std::allocator<cv::UMat> >::_M_emplace_back_aux<cv::UMat>(cv::UMat&&)
PUBLIC 38960 0 void std::vector<cv::UMat, std::allocator<cv::UMat> >::emplace_back<cv::UMat>(cv::UMat&&)
PUBLIC 38a30 0 cv::(anonymous namespace)::FarnebackOpticalFlowImpl::calc(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&)
PUBLIC 3c180 0 cv::readOpticalFlow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3c808 0 cv::writeOpticalFlow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::_InputArray const&)
PUBLIC 3cc20 0 cv::VariationalRefinementImpl::getFixedPointIterations() const
PUBLIC 3cc28 0 cv::VariationalRefinementImpl::setFixedPointIterations(int)
PUBLIC 3cc30 0 cv::VariationalRefinementImpl::getSorIterations() const
PUBLIC 3cc38 0 cv::VariationalRefinementImpl::setSorIterations(int)
PUBLIC 3cc40 0 cv::VariationalRefinementImpl::getOmega() const
PUBLIC 3cc48 0 cv::VariationalRefinementImpl::setOmega(float)
PUBLIC 3cc50 0 cv::VariationalRefinementImpl::getAlpha() const
PUBLIC 3cc58 0 cv::VariationalRefinementImpl::setAlpha(float)
PUBLIC 3cc60 0 cv::VariationalRefinementImpl::getDelta() const
PUBLIC 3cc68 0 cv::VariationalRefinementImpl::setDelta(float)
PUBLIC 3cc70 0 cv::VariationalRefinementImpl::getGamma() const
PUBLIC 3cc78 0 cv::VariationalRefinementImpl::setGamma(float)
PUBLIC 3cc80 0 std::_Sp_counted_ptr_inplace<cv::VariationalRefinementImpl, std::allocator<cv::VariationalRefinementImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 3cc88 0 cv::VariationalRefinementImpl::ParallelOp_ParBody::operator()(cv::Range const&) const
PUBLIC 3cd78 0 std::_Sp_counted_ptr_inplace<cv::VariationalRefinementImpl, std::allocator<cv::VariationalRefinementImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 3cd80 0 std::_Sp_counted_ptr_inplace<cv::VariationalRefinementImpl, std::allocator<cv::VariationalRefinementImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 3cd88 0 cv::VariationalRefinementImpl::ComputeDataTerm_ParBody::~ComputeDataTerm_ParBody()
PUBLIC 3cd98 0 cv::VariationalRefinementImpl::ComputeDataTerm_ParBody::~ComputeDataTerm_ParBody()
PUBLIC 3cdc0 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermHorPass_ParBody::~ComputeSmoothnessTermHorPass_ParBody()
PUBLIC 3cdd0 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermHorPass_ParBody::~ComputeSmoothnessTermHorPass_ParBody()
PUBLIC 3cdf8 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermVertPass_ParBody::~ComputeSmoothnessTermVertPass_ParBody()
PUBLIC 3ce08 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermVertPass_ParBody::~ComputeSmoothnessTermVertPass_ParBody()
PUBLIC 3ce30 0 cv::VariationalRefinementImpl::RedBlackSOR_ParBody::~RedBlackSOR_ParBody()
PUBLIC 3ce40 0 cv::VariationalRefinementImpl::RedBlackSOR_ParBody::~RedBlackSOR_ParBody()
PUBLIC 3ce68 0 cv::VariationalRefinementImpl::ParallelOp_ParBody::~ParallelOp_ParBody()
PUBLIC 3ceb8 0 cv::VariationalRefinementImpl::subtractOp(void*, void*, void*)
PUBLIC 3cf70 0 cv::VariationalRefinementImpl::averageOp(void*, void*, void*)
PUBLIC 3d028 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermVertPass_ParBody::operator()(cv::Range const&) const
PUBLIC 3d4f0 0 cv::VariationalRefinementImpl::ComputeDataTerm_ParBody::operator()(cv::Range const&) const
PUBLIC 3dd98 0 cv::VariationalRefinementImpl::ComputeSmoothnessTermHorPass_ParBody::operator()(cv::Range const&) const
PUBLIC 3e798 0 cv::VariationalRefinementImpl::RedBlackSOR_ParBody::operator()(cv::Range const&) const
PUBLIC 3ecc8 0 std::_Sp_counted_ptr_inplace<cv::VariationalRefinementImpl, std::allocator<cv::VariationalRefinementImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 3ed18 0 cv::VariationalRefinementImpl::updateRepeatedBorders(cv::VariationalRefinementImpl::RedBlackBuffer&) [clone .constprop.87]
PUBLIC 3eed0 0 cv::VariationalRefinementImpl::ParallelOp_ParBody::~ParallelOp_ParBody()
PUBLIC 3ef28 0 cv::VariationalRefinementImpl::~VariationalRefinementImpl()
PUBLIC 409d0 0 cv::VariationalRefinementImpl::~VariationalRefinementImpl()
PUBLIC 409e8 0 std::_Sp_counted_ptr_inplace<cv::VariationalRefinementImpl, std::allocator<cv::VariationalRefinementImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 409f0 0 cv::VariationalRefinementImpl::splitCheckerboard(cv::VariationalRefinementImpl::RedBlackBuffer&, cv::Mat&)
PUBLIC 40f18 0 cv::VariationalRefinementImpl::gradVertAndSplitOp(void*, void*, void*)
PUBLIC 40fe0 0 cv::VariationalRefinementImpl::gradHorizAndSplitOp(void*, void*, void*)
PUBLIC 410a8 0 cv::VariationalRefinementImpl::mergeCheckerboard(cv::Mat&, cv::VariationalRefinementImpl::RedBlackBuffer&)
PUBLIC 414b8 0 cv::VariationalRefinementImpl::RedBlackBuffer::create(cv::Size_<int>)
PUBLIC 41618 0 cv::VariationalRefinementImpl::RedBlackBuffer::release()
PUBLIC 41750 0 cv::VariationalRefinementImpl::collectGarbage()
PUBLIC 41cd0 0 cv::VariationalRefinementImpl::VariationalRefinementImpl()
PUBLIC 43078 0 cv::VariationalRefinementImpl::ParallelOp_ParBody::ParallelOp_ParBody(cv::VariationalRefinementImpl&, std::vector<void (cv::VariationalRefinementImpl::*)(void*, void*, void*), std::allocator<void (cv::VariationalRefinementImpl::*)(void*, void*, void*)> >, std::vector<void*, std::allocator<void*> >&, std::vector<void*, std::allocator<void*> >&, std::vector<void*, std::allocator<void*> >&)
PUBLIC 432f0 0 cv::VariationalRefinementImpl::warpImage(cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC 43710 0 cv::VariationalRefinement::create()
PUBLIC 437b8 0 void std::vector<void*, std::allocator<void*> >::_M_emplace_back_aux<void*>(void*&&)
PUBLIC 438a0 0 void std::vector<void (cv::VariationalRefinementImpl::*)(void*, void*, void*), std::allocator<void (cv::VariationalRefinementImpl::*)(void*, void*, void*)> >::_M_emplace_back_aux<void (cv::VariationalRefinementImpl::*)(void*, void*, void*)>(void (cv::VariationalRefinementImpl::*&&)(void*, void*, void*))
PUBLIC 43990 0 cv::VariationalRefinementImpl::prepareBuffers(cv::Mat&, cv::Mat&, cv::Mat&, cv::Mat&)
PUBLIC 44e18 0 cv::VariationalRefinementImpl::calcUV(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&)
PUBLIC 46200 0 cv::VariationalRefinementImpl::calc(cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&)
PUBLIC 46650 0 _fini
STACK CFI INIT dd80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dda0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT dda8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddb8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddc8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dde0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ddf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ddf8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT de00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT de08 c .cfa: sp 0 + .ra: x30
STACK CFI INIT de18 c .cfa: sp 0 + .ra: x30
STACK CFI INIT de28 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT de50 24 .cfa: sp 0 + .ra: x30
STACK CFI de54 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI de70 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT de78 50 .cfa: sp 0 + .ra: x30
STACK CFI de7c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI de88 .ra: .cfa -16 + ^
STACK CFI dec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b1e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI b1e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b1f0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b274 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT dec8 208 .cfa: sp 0 + .ra: x30
STACK CFI decc .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ded4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI dee8 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI e03c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI e040 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT e0d0 51c .cfa: sp 0 + .ra: x30
STACK CFI e0d4 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI e0e8 .ra: .cfa -176 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI e5e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT e5f0 43c .cfa: sp 0 + .ra: x30
STACK CFI e5f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e604 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI e9b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e9c0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT ea30 43c .cfa: sp 0 + .ra: x30
STACK CFI ea34 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ea44 .ra: .cfa -16 + ^
STACK CFI edfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI ee00 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT ee70 434 .cfa: sp 0 + .ra: x30
STACK CFI ee74 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ee84 .ra: .cfa -16 + ^
STACK CFI f234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI f238 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT f2a8 60c .cfa: sp 0 + .ra: x30
STACK CFI f2ac .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f2b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f2bc .ra: .cfa -48 + ^
STACK CFI f704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI f708 .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT f8b8 14c .cfa: sp 0 + .ra: x30
STACK CFI f8cc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f8d8 .ra: .cfa -48 + ^
STACK CFI f8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI f8f8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f9b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI f9bc .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT fa08 90 .cfa: sp 0 + .ra: x30
STACK CFI fa0c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI fa80 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI fa88 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI fa94 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT fa98 a5c .cfa: sp 0 + .ra: x30
STACK CFI fa9c .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI faa4 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI fab4 .ra: .cfa -88 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI fe24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI fe28 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI 10298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 102a0 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^
STACK CFI INIT 104f8 748 .cfa: sp 0 + .ra: x30
STACK CFI 104fc .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 10508 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 10520 .ra: .cfa -384 + ^ v8: .cfa -368 + ^ v9: .cfa -360 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 10b58 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10b60 .cfa: sp 464 + .ra: .cfa -384 + ^ v8: .cfa -368 + ^ v9: .cfa -360 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI INIT 10c58 854 .cfa: sp 0 + .ra: x30
STACK CFI 10c5c .cfa: sp 544 +
STACK CFI 10c60 v8: .cfa -456 + ^
STACK CFI 10c68 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 10c80 x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI 10c98 .ra: .cfa -464 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 111dc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 111e0 .cfa: sp 544 + .ra: .cfa -464 + ^ v8: .cfa -456 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT 114d0 168 .cfa: sp 0 + .ra: x30
STACK CFI 114d4 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 114e4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 114ec .ra: .cfa -136 + ^ x23: .cfa -144 + ^
STACK CFI 11610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 11614 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^
STACK CFI INIT 11640 4f0 .cfa: sp 0 + .ra: x30
STACK CFI 11644 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 11648 v8: .cfa -72 + ^
STACK CFI 11650 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 11674 .ra: .cfa -80 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 11a28 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11a30 .cfa: sp 160 + .ra: .cfa -80 + ^ v8: .cfa -72 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 11b60 bc .cfa: sp 0 + .ra: x30
STACK CFI 11b64 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11b68 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 11c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 11c10 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 11c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 11c20 438 .cfa: sp 0 + .ra: x30
STACK CFI 11c24 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 11c2c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 11c3c x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 11c44 .ra: .cfa -208 + ^
STACK CFI 11e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 11e48 .cfa: sp 272 + .ra: .cfa -208 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI INIT 12070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12078 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12088 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12090 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 120a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 120b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 120b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 120c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 120d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 120e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 120f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12100 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12110 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12120 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12130 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12140 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12150 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12160 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12168 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12170 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12178 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12188 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12198 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 121c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 121e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 121e8 50 .cfa: sp 0 + .ra: x30
STACK CFI 121ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 121f8 .ra: .cfa -16 + ^
STACK CFI 12234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b280 a0 .cfa: sp 0 + .ra: x30
STACK CFI b284 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b290 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b314 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 12238 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 1223c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 12244 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 12258 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 1244c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 12450 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 124e0 184 .cfa: sp 0 + .ra: x30
STACK CFI 124e4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 124f4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 12644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 12648 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 12668 18c .cfa: sp 0 + .ra: x30
STACK CFI 1266c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1267c .ra: .cfa -16 + ^
STACK CFI 127d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 127d8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 127f8 184 .cfa: sp 0 + .ra: x30
STACK CFI 127fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1280c .ra: .cfa -16 + ^
STACK CFI 12958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12960 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 12980 1300 .cfa: sp 0 + .ra: x30
STACK CFI 12984 .cfa: sp 3664 +
STACK CFI 1298c x27: .cfa -3600 + ^ x28: .cfa -3592 + ^
STACK CFI 12994 x19: .cfa -3664 + ^ x20: .cfa -3656 + ^
STACK CFI 129ac .ra: .cfa -3584 + ^ v8: .cfa -3568 + ^ v9: .cfa -3560 + ^ x21: .cfa -3648 + ^ x22: .cfa -3640 + ^ x23: .cfa -3632 + ^ x24: .cfa -3624 + ^ x25: .cfa -3616 + ^ x26: .cfa -3608 + ^
STACK CFI 13bd0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13bd4 .cfa: sp 3664 + .ra: .cfa -3584 + ^ v8: .cfa -3568 + ^ v9: .cfa -3560 + ^ x19: .cfa -3664 + ^ x20: .cfa -3656 + ^ x21: .cfa -3648 + ^ x22: .cfa -3640 + ^ x23: .cfa -3632 + ^ x24: .cfa -3624 + ^ x25: .cfa -3616 + ^ x26: .cfa -3608 + ^ x27: .cfa -3600 + ^ x28: .cfa -3592 + ^
STACK CFI INIT 13c90 a38 .cfa: sp 0 + .ra: x30
STACK CFI 13c94 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 13c9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 13ca4 .ra: .cfa -64 + ^
STACK CFI 14400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 14404 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 146c8 15c .cfa: sp 0 + .ra: x30
STACK CFI 146dc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 146e8 .ra: .cfa -48 + ^
STACK CFI 14704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14708 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 147d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 147dc .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 14828 a58 .cfa: sp 0 + .ra: x30
STACK CFI 1482c .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 14838 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 14850 .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 14df8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14e00 .cfa: sp 352 + .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 15280 bc8 .cfa: sp 0 + .ra: x30
STACK CFI 15284 .cfa: sp 656 +
STACK CFI 15288 v8: .cfa -568 + ^
STACK CFI 15290 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 15298 x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 152a8 x21: .cfa -640 + ^ x22: .cfa -632 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^
STACK CFI 152bc .ra: .cfa -576 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 157f8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15800 .cfa: sp 656 + .ra: .cfa -576 + ^ v8: .cfa -568 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI INIT 15e50 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 15e54 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 15e6c .ra: .cfa -136 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^
STACK CFI 15fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 15fe0 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^
STACK CFI INIT 16010 318 .cfa: sp 0 + .ra: x30
STACK CFI 16014 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 16018 v8: .cfa -40 + ^
STACK CFI 16020 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 16030 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 16044 .ra: .cfa -48 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1626c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16270 .cfa: sp 128 + .ra: .cfa -48 + ^ v8: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 16360 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 16364 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 16398 .ra: .cfa -224 + ^ v10: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 16600 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16608 .cfa: sp 304 + .ra: .cfa -224 + ^ v10: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 16680 390 .cfa: sp 0 + .ra: x30
STACK CFI 16684 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 16690 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 166bc .ra: .cfa -240 + ^ v10: .cfa -232 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 169a0 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 169a8 .cfa: sp 320 + .ra: .cfa -240 + ^ v10: .cfa -232 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI INIT 16a30 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 16a34 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 16a3c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 16a64 .ra: .cfa -192 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 16cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16cc8 .cfa: sp 272 + .ra: .cfa -192 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 16d30 300 .cfa: sp 0 + .ra: x30
STACK CFI 16d34 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 16d3c x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 16d64 .ra: .cfa -192 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 16fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16fe0 .cfa: sp 272 + .ra: .cfa -192 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT 17048 160 .cfa: sp 0 + .ra: x30
STACK CFI 1704c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17058 .ra: .cfa -48 + ^
STACK CFI 170a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 170a8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 170c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 170c8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17108 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1711c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17120 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT b320 a0 .cfa: sp 0 + .ra: x30
STACK CFI b324 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b330 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b3b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b3b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 171b0 8f8 .cfa: sp 0 + .ra: x30
STACK CFI 171b4 .cfa: sp 800 +
STACK CFI 171bc x21: .cfa -784 + ^ x22: .cfa -776 + ^
STACK CFI 171cc x19: .cfa -800 + ^ x20: .cfa -792 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^
STACK CFI 171f0 v10: .cfa -688 + ^ v11: .cfa -680 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI 171f8 .ra: .cfa -720 + ^
STACK CFI 1773c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17740 .cfa: sp 800 + .ra: .cfa -720 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI INIT 17ad0 934 .cfa: sp 0 + .ra: x30
STACK CFI 17ad4 .cfa: sp 800 +
STACK CFI 17adc x19: .cfa -800 + ^ x20: .cfa -792 + ^
STACK CFI 17aec x21: .cfa -784 + ^ x22: .cfa -776 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI 17b24 .ra: .cfa -720 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ v14: .cfa -656 + ^ v15: .cfa -648 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^
STACK CFI 17e7c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17e80 .cfa: sp 800 + .ra: .cfa -720 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ v14: .cfa -656 + ^ v15: .cfa -648 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI INIT 18448 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18458 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18468 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18478 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18488 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18498 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 184f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18500 50 .cfa: sp 0 + .ra: x30
STACK CFI 18504 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18510 .ra: .cfa -16 + ^
STACK CFI 1854c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 18550 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 18554 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 18580 .ra: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 188bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 188c0 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 188ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 188f8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18908 24 .cfa: sp 0 + .ra: x30
STACK CFI 1890c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 18928 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 18930 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18940 24 .cfa: sp 0 + .ra: x30
STACK CFI 18944 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 18960 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 18968 dc .cfa: sp 0 + .ra: x30
STACK CFI 1896c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18970 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18978 .ra: .cfa -32 + ^
STACK CFI 189c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 189c8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 18a10 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 18a38 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 18a48 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18ac8 8f8 .cfa: sp 0 + .ra: x30
STACK CFI 18acc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18ae4 .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 19358 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 193c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 193c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 193d4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 193d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 193e0 78 .cfa: sp 0 + .ra: x30
STACK CFI 193e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 19448 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 19450 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 19458 a08 .cfa: sp 0 + .ra: x30
STACK CFI 1945c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1946c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1947c .ra: .cfa -32 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 19d80 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 19e60 a48 .cfa: sp 0 + .ra: x30
STACK CFI 19e68 .cfa: sp 5536 +
STACK CFI 19e6c x23: .cfa -5504 + ^ x24: .cfa -5496 + ^
STACK CFI 19e78 x21: .cfa -5520 + ^ x22: .cfa -5512 + ^
STACK CFI 19e8c x19: .cfa -5536 + ^ x20: .cfa -5528 + ^ x25: .cfa -5488 + ^ x26: .cfa -5480 + ^
STACK CFI 19eb0 .ra: .cfa -5456 + ^ x27: .cfa -5472 + ^ x28: .cfa -5464 + ^
STACK CFI 1a6dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a6e0 .cfa: sp 5536 + .ra: .cfa -5456 + ^ x19: .cfa -5536 + ^ x20: .cfa -5528 + ^ x21: .cfa -5520 + ^ x22: .cfa -5512 + ^ x23: .cfa -5504 + ^ x24: .cfa -5496 + ^ x25: .cfa -5488 + ^ x26: .cfa -5480 + ^ x27: .cfa -5472 + ^ x28: .cfa -5464 + ^
STACK CFI INIT 1a8a8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1a8ac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1a900 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1a908 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1a93c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1a940 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1a978 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1a980 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1a994 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1a9a0 204 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1aba8 1c70 .cfa: sp 0 + .ra: x30
STACK CFI 1abac .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 1abb0 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 1abc0 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 1abec .ra: .cfa -368 + ^ v10: .cfa -336 + ^ v11: .cfa -328 + ^ v12: .cfa -320 + ^ v13: .cfa -312 + ^ v14: .cfa -304 + ^ v15: .cfa -296 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 1c2bc .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1c2c0 .cfa: sp 448 + .ra: .cfa -368 + ^ v10: .cfa -336 + ^ v11: .cfa -328 + ^ v12: .cfa -320 + ^ v13: .cfa -312 + ^ v14: .cfa -304 + ^ v15: .cfa -296 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI INIT 1c820 2cc .cfa: sp 0 + .ra: x30
STACK CFI 1c824 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 1c82c x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 1c83c x21: .cfa -368 + ^ x22: .cfa -360 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 1c848 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 1c860 .ra: .cfa -304 + ^
STACK CFI 1ca64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ca68 .cfa: sp 384 + .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 1caf0 69c .cfa: sp 0 + .ra: x30
STACK CFI 1caf4 .cfa: sp 768 +
STACK CFI 1cafc x19: .cfa -768 + ^ x20: .cfa -760 + ^
STACK CFI 1cb0c x21: .cfa -752 + ^ x22: .cfa -744 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 1cb2c .ra: .cfa -688 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^ x27: .cfa -704 + ^ x28: .cfa -696 + ^
STACK CFI 1d0e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d0f0 .cfa: sp 768 + .ra: .cfa -688 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^ x27: .cfa -704 + ^ x28: .cfa -696 + ^
STACK CFI INIT 1d190 bc .cfa: sp 0 + .ra: x30
STACK CFI 1d194 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d198 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1d23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1d240 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1d248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1d250 bc .cfa: sp 0 + .ra: x30
STACK CFI 1d254 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d258 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1d2fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1d300 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1d308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1d310 bc .cfa: sp 0 + .ra: x30
STACK CFI 1d314 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d318 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1d3bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1d3c0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1d3c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1d3d0 140 .cfa: sp 0 + .ra: x30
STACK CFI 1d3d4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d3dc .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1d498 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d50c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 1d510 54 .cfa: sp 0 + .ra: x30
STACK CFI 1d514 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d518 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1d550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1d558 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1d560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1d568 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1d570 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d57c .ra: .cfa -16 + ^
STACK CFI 1d5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d5a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d5ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1d5f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 1d620 384 .cfa: sp 0 + .ra: x30
STACK CFI 1d628 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d640 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1d878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1d87c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1d8dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1d8e0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1d8f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1d8fc .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 1d9a8 38c .cfa: sp 0 + .ra: x30
STACK CFI 1d9b0 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d9c8 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1dc04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1dc08 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1dc6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1dc70 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1dc88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1dc8c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 1dd38 38c .cfa: sp 0 + .ra: x30
STACK CFI 1dd40 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1dd58 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1df94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1df98 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1dffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1e000 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1e018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1e01c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 1e0c8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1e0cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1e0dc .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e1a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1e1a4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1e1b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 1e1c0 1494 .cfa: sp 0 + .ra: x30
STACK CFI 1e1c4 .cfa: sp 624 +
STACK CFI 1e1c8 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 1e204 .ra: .cfa -544 + ^ v8: .cfa -536 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 1eb8c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1eb90 .cfa: sp 624 + .ra: .cfa -544 + ^ v8: .cfa -536 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 1f670 304 .cfa: sp 0 + .ra: x30
STACK CFI 1f678 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1f690 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1f708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1f718 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1f8b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1f8c0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 1f980 103c .cfa: sp 0 + .ra: x30
STACK CFI 1f984 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 1f9bc .ra: .cfa -384 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 1fff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1fff8 .cfa: sp 464 + .ra: .cfa -384 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI INIT 209d0 15cc .cfa: sp 0 + .ra: x30
STACK CFI 209d4 .cfa: sp 1232 +
STACK CFI 209dc x21: .cfa -1216 + ^ x22: .cfa -1208 + ^
STACK CFI 209ec x19: .cfa -1232 + ^ x20: .cfa -1224 + ^ x23: .cfa -1200 + ^ x24: .cfa -1192 + ^
STACK CFI 20a18 .ra: .cfa -1152 + ^ v8: .cfa -1144 + ^ x25: .cfa -1184 + ^ x26: .cfa -1176 + ^ x27: .cfa -1168 + ^ x28: .cfa -1160 + ^
STACK CFI 21bd4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21bd8 .cfa: sp 1232 + .ra: .cfa -1152 + ^ v8: .cfa -1144 + ^ x19: .cfa -1232 + ^ x20: .cfa -1224 + ^ x21: .cfa -1216 + ^ x22: .cfa -1208 + ^ x23: .cfa -1200 + ^ x24: .cfa -1192 + ^ x25: .cfa -1184 + ^ x26: .cfa -1176 + ^ x27: .cfa -1168 + ^ x28: .cfa -1160 + ^
STACK CFI INIT 21fc0 ce4 .cfa: sp 0 + .ra: x30
STACK CFI 21fc4 .cfa: sp 880 +
STACK CFI 21fcc x19: .cfa -880 + ^ x20: .cfa -872 + ^
STACK CFI 21fec x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^
STACK CFI 22000 .ra: .cfa -800 + ^ v8: .cfa -792 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI 22890 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 22898 .cfa: sp 880 + .ra: .cfa -800 + ^ v8: .cfa -792 + ^ x19: .cfa -880 + ^ x20: .cfa -872 + ^ x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI INIT 22cb0 218 .cfa: sp 0 + .ra: x30
STACK CFI 22cb4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 22cbc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 22cc8 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 22e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 22e28 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 22ed0 950 .cfa: sp 0 + .ra: x30
STACK CFI 22ed4 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 22ee0 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 22eec x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 22ef8 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 22f04 .ra: .cfa -352 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 2362c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 23630 .cfa: sp 432 + .ra: .cfa -352 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 23848 370 .cfa: sp 0 + .ra: x30
STACK CFI 2384c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23858 .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 23860 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2390c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 23910 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI INIT 23bb8 dc .cfa: sp 0 + .ra: x30
STACK CFI 23bbc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23bc0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23bc8 .ra: .cfa -32 + ^
STACK CFI 23c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 23c18 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 23c60 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 23c88 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 23c98 60 .cfa: sp 0 + .ra: x30
STACK CFI 23cb4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 23ccc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 23d00 63c .cfa: sp 0 + .ra: x30
STACK CFI 23d04 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 23d20 .ra: .cfa -336 + ^ v8: .cfa -320 + ^ v9: .cfa -312 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 23e80 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 23e84 .cfa: sp 416 + .ra: .cfa -336 + ^ v8: .cfa -320 + ^ v9: .cfa -312 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 24218 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 24220 .cfa: sp 416 + .ra: .cfa -336 + ^ v8: .cfa -320 + ^ v9: .cfa -312 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI INIT 24360 80 .cfa: sp 0 + .ra: x30
STACK CFI 24370 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2437c .ra: .cfa -16 + ^
STACK CFI 243c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 243cc .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 243f0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 243f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24400 .ra: .cfa -16 + ^
STACK CFI 2455c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 24560 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 24598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 245a0 2cb0 .cfa: sp 0 + .ra: x30
STACK CFI 245a4 .cfa: sp 2496 +
STACK CFI 245a8 x27: .cfa -2432 + ^ x28: .cfa -2424 + ^
STACK CFI 245d0 .ra: .cfa -2416 + ^ v10: .cfa -2384 + ^ v11: .cfa -2376 + ^ v12: .cfa -2368 + ^ v13: .cfa -2360 + ^ v14: .cfa -2408 + ^ v8: .cfa -2400 + ^ v9: .cfa -2392 + ^ x19: .cfa -2496 + ^ x20: .cfa -2488 + ^ x21: .cfa -2480 + ^ x22: .cfa -2472 + ^ x23: .cfa -2464 + ^ x24: .cfa -2456 + ^ x25: .cfa -2448 + ^ x26: .cfa -2440 + ^
STACK CFI 26b18 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 26b20 .cfa: sp 2496 + .ra: .cfa -2416 + ^ v10: .cfa -2384 + ^ v11: .cfa -2376 + ^ v12: .cfa -2368 + ^ v13: .cfa -2360 + ^ v14: .cfa -2408 + ^ v8: .cfa -2400 + ^ v9: .cfa -2392 + ^ x19: .cfa -2496 + ^ x20: .cfa -2488 + ^ x21: .cfa -2480 + ^ x22: .cfa -2472 + ^ x23: .cfa -2464 + ^ x24: .cfa -2456 + ^ x25: .cfa -2448 + ^ x26: .cfa -2440 + ^ x27: .cfa -2432 + ^ x28: .cfa -2424 + ^
STACK CFI INIT 27280 888 .cfa: sp 0 + .ra: x30
STACK CFI 27284 .cfa: sp 816 +
STACK CFI 27288 x21: .cfa -800 + ^ x22: .cfa -792 + ^
STACK CFI 27290 x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 27298 x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI 272ac .ra: .cfa -752 + ^ v10: .cfa -720 + ^ v11: .cfa -712 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 2793c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 27940 .cfa: sp 816 + .ra: .cfa -752 + ^ v10: .cfa -720 + ^ v11: .cfa -712 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI INIT 27b20 3c80 .cfa: sp 0 + .ra: x30
STACK CFI 27b28 .cfa: sp 5552 +
STACK CFI 27b2c v10: .cfa -5440 + ^ v11: .cfa -5432 + ^
STACK CFI 27b34 x19: .cfa -5552 + ^ x20: .cfa -5544 + ^
STACK CFI 27b44 x21: .cfa -5536 + ^ x22: .cfa -5528 + ^ x23: .cfa -5520 + ^ x24: .cfa -5512 + ^
STACK CFI 27b54 x25: .cfa -5504 + ^ x26: .cfa -5496 + ^ x27: .cfa -5488 + ^ x28: .cfa -5480 + ^
STACK CFI 27b6c .ra: .cfa -5472 + ^ v12: .cfa -5424 + ^ v13: .cfa -5416 + ^ v8: .cfa -5456 + ^ v9: .cfa -5448 + ^
STACK CFI 29ff4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 29ff8 .cfa: sp 5552 + .ra: .cfa -5472 + ^ v10: .cfa -5440 + ^ v11: .cfa -5432 + ^ v12: .cfa -5424 + ^ v13: .cfa -5416 + ^ v8: .cfa -5456 + ^ v9: .cfa -5448 + ^ x19: .cfa -5552 + ^ x20: .cfa -5544 + ^ x21: .cfa -5536 + ^ x22: .cfa -5528 + ^ x23: .cfa -5520 + ^ x24: .cfa -5512 + ^ x25: .cfa -5504 + ^ x26: .cfa -5496 + ^ x27: .cfa -5488 + ^ x28: .cfa -5480 + ^
STACK CFI INIT 2b7e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b3c0 a0 .cfa: sp 0 + .ra: x30
STACK CFI b3c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b3d0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b454 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2b7f0 214 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ba20 142c .cfa: sp 0 + .ra: x30
STACK CFI 2ba24 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 2ba38 .ra: .cfa -360 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^
STACK CFI 2ca70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2ca78 .cfa: sp 416 + .ra: .cfa -360 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^
STACK CFI INIT 2ce50 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 2ce58 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ce60 .ra: .cfa -16 + ^
STACK CFI 2d078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2d07c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 2d110 68c .cfa: sp 0 + .ra: x30
STACK CFI 2d114 .cfa: sp 528 +
STACK CFI 2d118 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 2d120 x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI 2d134 x21: .cfa -512 + ^ x22: .cfa -504 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 2d14c .ra: .cfa -464 + ^
STACK CFI 2d6f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2d6f8 .cfa: sp 528 + .ra: .cfa -464 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI INIT 2d7a0 eb4 .cfa: sp 0 + .ra: x30
STACK CFI 2d7a4 .cfa: sp 864 +
STACK CFI 2d7a8 x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 2d7b0 x23: .cfa -832 + ^ x24: .cfa -824 + ^
STACK CFI 2d7c8 x19: .cfa -864 + ^ x20: .cfa -856 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^ x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI 2d7dc .ra: .cfa -784 + ^
STACK CFI 2e46c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2e470 .cfa: sp 864 + .ra: .cfa -784 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^ x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI INIT 2e658 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e668 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e678 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e688 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e690 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6c8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e6e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 2e6ec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2e708 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2e710 24 .cfa: sp 0 + .ra: x30
STACK CFI 2e714 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2e730 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2e738 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e740 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e748 50 .cfa: sp 0 + .ra: x30
STACK CFI 2e74c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e758 .ra: .cfa -16 + ^
STACK CFI 2e794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2e798 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e7b0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e7d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 2e7d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2e7f4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT b460 e0 .cfa: sp 0 + .ra: x30
STACK CFI b464 .cfa: sp 16 +
STACK CFI b53c .cfa: sp 0 +
STACK CFI INIT b540 a0 .cfa: sp 0 + .ra: x30
STACK CFI b544 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b550 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b5d4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 2e7f8 d8c .cfa: sp 0 + .ra: x30
STACK CFI 2e7fc .cfa: sp 1328 +
STACK CFI 2e81c .ra: .cfa -1248 + ^ x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x23: .cfa -1296 + ^ x24: .cfa -1288 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^ x27: .cfa -1264 + ^ x28: .cfa -1256 + ^
STACK CFI 2f49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2f4a0 .cfa: sp 1328 + .ra: .cfa -1248 + ^ x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x23: .cfa -1296 + ^ x24: .cfa -1288 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^ x27: .cfa -1264 + ^ x28: .cfa -1256 + ^
STACK CFI INIT 2f588 154 .cfa: sp 0 + .ra: x30
STACK CFI 2f58c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2f594 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2f59c .ra: .cfa -56 + ^ x23: .cfa -64 + ^
STACK CFI 2f628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f630 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI INIT 2f6e0 1334 .cfa: sp 0 + .ra: x30
STACK CFI 2f6e4 .cfa: sp 1696 +
STACK CFI 2f6e8 x27: .cfa -1632 + ^ x28: .cfa -1624 + ^
STACK CFI 2f724 .ra: .cfa -1616 + ^ v10: .cfa -1584 + ^ v11: .cfa -1576 + ^ v12: .cfa -1568 + ^ v13: .cfa -1560 + ^ v14: .cfa -1552 + ^ v15: .cfa -1544 + ^ v8: .cfa -1600 + ^ v9: .cfa -1592 + ^ x19: .cfa -1696 + ^ x20: .cfa -1688 + ^ x21: .cfa -1680 + ^ x22: .cfa -1672 + ^ x23: .cfa -1664 + ^ x24: .cfa -1656 + ^ x25: .cfa -1648 + ^ x26: .cfa -1640 + ^
STACK CFI 2fbc0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2fbc4 .cfa: sp 1696 + .ra: .cfa -1616 + ^ v10: .cfa -1584 + ^ v11: .cfa -1576 + ^ v12: .cfa -1568 + ^ v13: .cfa -1560 + ^ v14: .cfa -1552 + ^ v15: .cfa -1544 + ^ v8: .cfa -1600 + ^ v9: .cfa -1592 + ^ x19: .cfa -1696 + ^ x20: .cfa -1688 + ^ x21: .cfa -1680 + ^ x22: .cfa -1672 + ^ x23: .cfa -1664 + ^ x24: .cfa -1656 + ^ x25: .cfa -1648 + ^ x26: .cfa -1640 + ^ x27: .cfa -1632 + ^ x28: .cfa -1624 + ^
STACK CFI INIT 30a28 60 .cfa: sp 0 + .ra: x30
STACK CFI 30a48 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 30a5c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 30a90 110 .cfa: sp 0 + .ra: x30
STACK CFI 30a94 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30aa0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 30b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 30b80 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 30bb0 10e4 .cfa: sp 0 + .ra: x30
STACK CFI 30bb4 .cfa: sp 688 +
STACK CFI 30bc8 x21: .cfa -672 + ^ x22: .cfa -664 + ^
STACK CFI 30bd4 x19: .cfa -688 + ^ x20: .cfa -680 + ^
STACK CFI 30be4 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 30bf8 .ra: .cfa -608 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^ x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI 31a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 31a70 .cfa: sp 688 + .ra: .cfa -608 + ^ x19: .cfa -688 + ^ x20: .cfa -680 + ^ x21: .cfa -672 + ^ x22: .cfa -664 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^ x25: .cfa -640 + ^ x26: .cfa -632 + ^ x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI INIT 31cb0 fc .cfa: sp 0 + .ra: x30
STACK CFI 31cb4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 31cb8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 31cc4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 31cd4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 31cdc .ra: .cfa -24 + ^ x27: .cfa -32 + ^
STACK CFI 31d94 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 31d98 .cfa: sp 96 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI INIT 31db0 14c .cfa: sp 0 + .ra: x30
STACK CFI 31db4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 31dcc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 31dd4 .ra: .cfa -48 + ^
STACK CFI 31e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 31e68 .cfa: sp 96 + .ra: .cfa -48 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 31f00 84 .cfa: sp 0 + .ra: x30
STACK CFI 31f04 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 31f14 .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 31f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 31f70 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 31f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 31f90 2aa8 .cfa: sp 0 + .ra: x30
STACK CFI 31f94 .cfa: sp 1968 +
STACK CFI 31f9c x19: .cfa -1968 + ^ x20: .cfa -1960 + ^
STACK CFI 31fb4 x21: .cfa -1952 + ^ x22: .cfa -1944 + ^
STACK CFI 31fbc x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI 31fd0 .ra: .cfa -1888 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x23: .cfa -1936 + ^ x24: .cfa -1928 + ^ x25: .cfa -1920 + ^ x26: .cfa -1912 + ^
STACK CFI 3366c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33670 .cfa: sp 1968 + .ra: .cfa -1888 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x19: .cfa -1968 + ^ x20: .cfa -1960 + ^ x21: .cfa -1952 + ^ x22: .cfa -1944 + ^ x23: .cfa -1936 + ^ x24: .cfa -1928 + ^ x25: .cfa -1920 + ^ x26: .cfa -1912 + ^ x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI INIT 34a80 150 .cfa: sp 0 + .ra: x30
STACK CFI 34acc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34ad8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 34ae4 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 34ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 34bb0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 34bd0 138 .cfa: sp 0 + .ra: x30
STACK CFI 34bd8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34be0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 34bec .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 34c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 34c88 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 34cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 34cc0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 34d10 119c .cfa: sp 0 + .ra: x30
STACK CFI 34d14 .cfa: sp 848 +
STACK CFI 34d18 x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 34d2c x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 34d38 x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI 34d54 .ra: .cfa -736 + ^ v8: .cfa -720 + ^ v9: .cfa -712 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 35158 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 35160 .cfa: sp 848 + .ra: .cfa -736 + ^ v8: .cfa -720 + ^ v9: .cfa -712 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI INIT 35f00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35f88 50 .cfa: sp 0 + .ra: x30
STACK CFI 35f8c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 35f98 .ra: .cfa -16 + ^
STACK CFI 35fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 35fd8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35fe0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b5e0 344 .cfa: sp 0 + .ra: x30
STACK CFI b5e4 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI b5ec x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI b600 .ra: .cfa -144 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI b920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT b940 a0 .cfa: sp 0 + .ra: x30
STACK CFI b944 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b950 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI b9d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI b9d4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT b9e0 3d8 .cfa: sp 0 + .ra: x30
STACK CFI b9e4 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI b9e8 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI b9f0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI ba00 .ra: .cfa -96 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI bdb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT bdb8 2d8 .cfa: sp 0 + .ra: x30
STACK CFI bdbc .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI bdc0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI bdd0 .ra: .cfa -96 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI c08c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 35fe8 144 .cfa: sp 0 + .ra: x30
STACK CFI 35fec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 35ff0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 35ff8 .ra: .cfa -16 + ^
STACK CFI 36128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 36130 4f0 .cfa: sp 0 + .ra: x30
STACK CFI 36134 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 3613c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 36144 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 36158 .ra: .cfa -160 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 365e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 365ec .cfa: sp 240 + .ra: .cfa -160 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 36620 45c .cfa: sp 0 + .ra: x30
STACK CFI 36624 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3662c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36634 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 369dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 369e0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 36a80 148 .cfa: sp 0 + .ra: x30
STACK CFI 36a84 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36a88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36a90 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 36bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 36bc8 14c .cfa: sp 0 + .ra: x30
STACK CFI 36bcc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36bd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36bd8 .ra: .cfa -16 + ^
STACK CFI 36d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 36d18 178 .cfa: sp 0 + .ra: x30
STACK CFI 36d20 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36d2c .ra: .cfa -48 + ^
STACK CFI 36dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 36dd0 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 36e00 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT c090 c8 .cfa: sp 0 + .ra: x30
STACK CFI c094 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI c0a0 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI c0b4 .ra: .cfa -240 + ^
STACK CFI c130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI c134 .cfa: sp 272 + .ra: .cfa -240 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI INIT 36e90 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 36f10 12c .cfa: sp 0 + .ra: x30
STACK CFI 36f14 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36f20 .ra: .cfa -16 + ^
STACK CFI 36fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 36fe8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 37040 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT c160 1d4 .cfa: sp 0 + .ra: x30
STACK CFI c164 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c174 .ra: .cfa -88 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI c330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 370c0 cd8 .cfa: sp 0 + .ra: x30
STACK CFI 370c8 .cfa: sp 896 +
STACK CFI 370cc x19: .cfa -896 + ^ x20: .cfa -888 + ^
STACK CFI 370e4 x21: .cfa -880 + ^ x22: .cfa -872 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^ x27: .cfa -832 + ^ x28: .cfa -824 + ^
STACK CFI 37104 .ra: .cfa -816 + ^ v8: .cfa -800 + ^ v9: .cfa -792 + ^ x23: .cfa -864 + ^ x24: .cfa -856 + ^
STACK CFI 379f8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37a00 .cfa: sp 896 + .ra: .cfa -816 + ^ v8: .cfa -800 + ^ v9: .cfa -792 + ^ x19: .cfa -896 + ^ x20: .cfa -888 + ^ x21: .cfa -880 + ^ x22: .cfa -872 + ^ x23: .cfa -864 + ^ x24: .cfa -856 + ^ x25: .cfa -848 + ^ x26: .cfa -840 + ^ x27: .cfa -832 + ^ x28: .cfa -824 + ^
STACK CFI INIT 37dd0 394 .cfa: sp 0 + .ra: x30
STACK CFI 37dd4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 37dd8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 37de4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 37df4 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 37e00 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 37e14 .ra: .cfa -32 + ^
STACK CFI 38130 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38138 .cfa: sp 112 + .ra: .cfa -32 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 38180 504 .cfa: sp 0 + .ra: x30
STACK CFI 38184 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 38188 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI 381a0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 381b4 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 381cc .ra: .cfa -80 + ^
STACK CFI 38540 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38548 .cfa: sp 160 + .ra: .cfa -80 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT c350 1828 .cfa: sp 0 + .ra: x30
STACK CFI c354 .cfa: sp 1600 +
STACK CFI c358 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI c378 .ra: .cfa -1520 + ^ v10: .cfa -1488 + ^ v11: .cfa -1480 + ^ v8: .cfa -1504 + ^ v9: .cfa -1496 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI db74 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 386a0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 386a4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 386b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 386c0 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 38888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 38890 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 38960 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38a30 3718 .cfa: sp 0 + .ra: x30
STACK CFI 38a38 .cfa: sp 9216 +
STACK CFI 38a40 x21: .cfa -9200 + ^ x22: .cfa -9192 + ^
STACK CFI 38a60 x19: .cfa -9216 + ^ x20: .cfa -9208 + ^ x23: .cfa -9184 + ^ x24: .cfa -9176 + ^
STACK CFI 38a7c .ra: .cfa -9136 + ^ v10: .cfa -9104 + ^ v11: .cfa -9096 + ^ v12: .cfa -9088 + ^ v13: .cfa -9080 + ^ v8: .cfa -9120 + ^ v9: .cfa -9112 + ^ x25: .cfa -9168 + ^ x26: .cfa -9160 + ^ x27: .cfa -9152 + ^ x28: .cfa -9144 + ^
STACK CFI 3b3f4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3b3f8 .cfa: sp 9216 + .ra: .cfa -9136 + ^ v10: .cfa -9104 + ^ v11: .cfa -9096 + ^ v12: .cfa -9088 + ^ v13: .cfa -9080 + ^ v8: .cfa -9120 + ^ v9: .cfa -9112 + ^ x19: .cfa -9216 + ^ x20: .cfa -9208 + ^ x21: .cfa -9200 + ^ x22: .cfa -9192 + ^ x23: .cfa -9184 + ^ x24: .cfa -9176 + ^ x25: .cfa -9168 + ^ x26: .cfa -9160 + ^ x27: .cfa -9152 + ^ x28: .cfa -9144 + ^
STACK CFI INIT 3c180 664 .cfa: sp 0 + .ra: x30
STACK CFI 3c188 .cfa: sp 768 +
STACK CFI 3c18c x27: .cfa -704 + ^ x28: .cfa -696 + ^
STACK CFI 3c1ac x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI 3c1b4 x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 3c1dc .ra: .cfa -688 + ^ v8: .cfa -680 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 3c620 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c628 .cfa: sp 768 + .ra: .cfa -688 + ^ v8: .cfa -680 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^ x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^ x27: .cfa -704 + ^ x28: .cfa -696 + ^
STACK CFI INIT 3c808 418 .cfa: sp 0 + .ra: x30
STACK CFI 3c80c .cfa: sp 720 +
STACK CFI 3c810 x19: .cfa -720 + ^ x20: .cfa -712 + ^
STACK CFI 3c818 x21: .cfa -704 + ^ x22: .cfa -696 + ^
STACK CFI 3c82c .ra: .cfa -648 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^
STACK CFI 3c8ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3c8f0 .cfa: sp 720 + .ra: .cfa -648 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^
STACK CFI INIT dc40 30 .cfa: sp 0 + .ra: x30
STACK CFI dc44 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI dc60 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3cc20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc48 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cc88 f0 .cfa: sp 0 + .ra: x30
STACK CFI 3cc8c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3cc94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3ccac .ra: .cfa -32 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3cd58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3cd5c .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 3cd78 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd88 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cd98 24 .cfa: sp 0 + .ra: x30
STACK CFI 3cd9c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3cdb8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3cdc0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3cdd0 24 .cfa: sp 0 + .ra: x30
STACK CFI 3cdd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3cdf0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3cdf8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ce08 24 .cfa: sp 0 + .ra: x30
STACK CFI 3ce0c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3ce28 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3ce30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ce40 24 .cfa: sp 0 + .ra: x30
STACK CFI 3ce44 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3ce60 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3ce68 50 .cfa: sp 0 + .ra: x30
STACK CFI 3ce6c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3ceb4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3ceb8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3cebc .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3cecc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3cedc .ra: .cfa -112 + ^
STACK CFI 3cf4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3cf50 .cfa: sp 144 + .ra: .cfa -112 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 3cf70 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3cf74 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3cf84 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3cf90 .ra: .cfa -112 + ^
STACK CFI 3d008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3d00c .cfa: sp 144 + .ra: .cfa -112 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 3d028 4c8 .cfa: sp 0 + .ra: x30
STACK CFI 3d02c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3d03c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3d044 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3d058 .ra: .cfa -32 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3d374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3d378 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3d4f0 8a4 .cfa: sp 0 + .ra: x30
STACK CFI 3d4f4 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 3d504 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI 3d530 .ra: .cfa -304 + ^ v10: .cfa -272 + ^ v11: .cfa -264 + ^ v12: .cfa -256 + ^ v13: .cfa -248 + ^ v14: .cfa -240 + ^ v15: .cfa -232 + ^ v8: .cfa -288 + ^ v9: .cfa -280 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 3dd90 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 3dd98 a00 .cfa: sp 0 + .ra: x30
STACK CFI 3dd9c .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 3ddd0 .ra: .cfa -240 + ^ v10: .cfa -232 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 3e56c .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3e570 .cfa: sp 320 + .ra: .cfa -240 + ^ v10: .cfa -232 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI INIT 3e798 52c .cfa: sp 0 + .ra: x30
STACK CFI 3e79c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3e7a4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3e7c4 .ra: .cfa -32 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3eb48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3eb50 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3ecc8 50 .cfa: sp 0 + .ra: x30
STACK CFI 3eccc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ecd8 .ra: .cfa -16 + ^
STACK CFI 3ed14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT dba0 a0 .cfa: sp 0 + .ra: x30
STACK CFI dba4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI dbb0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI dc30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI dc34 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 3ed18 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 3ed1c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3ed40 .ra: .cfa -32 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3ee68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3ee70 .cfa: sp 96 + .ra: .cfa -32 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 3eed0 58 .cfa: sp 0 + .ra: x30
STACK CFI 3eed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3ef24 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3ef28 1aa4 .cfa: sp 0 + .ra: x30
STACK CFI 3ef2c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ef44 .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 406ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 406b0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 409d0 18 .cfa: sp 0 + .ra: x30
STACK CFI 409d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 409e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 409e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 409f0 524 .cfa: sp 0 + .ra: x30
STACK CFI 409f4 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 40a04 x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 40a1c .ra: .cfa -80 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 40ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 40ef4 .cfa: sp 160 + .ra: .cfa -80 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 40f18 c8 .cfa: sp 0 + .ra: x30
STACK CFI 40f1c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 40f2c .ra: .cfa -72 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 40fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 40fc4 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 40fe0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 40fe4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 40ff4 .ra: .cfa -72 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 41088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 4108c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 410a8 40c .cfa: sp 0 + .ra: x30
STACK CFI 410ac .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 410c4 .ra: .cfa -88 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 412b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 412b8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI INIT 414b8 15c .cfa: sp 0 + .ra: x30
STACK CFI 414bc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 414c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 414cc .ra: .cfa -40 + ^ x23: .cfa -48 + ^
STACK CFI 41590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 41598 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT 41618 138 .cfa: sp 0 + .ra: x30
STACK CFI 4161c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4162c .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 41714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 41718 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 41750 580 .cfa: sp 0 + .ra: x30
STACK CFI 41754 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 41764 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 41770 .ra: .cfa -32 + ^
STACK CFI 41c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 41c18 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 41cd0 1390 .cfa: sp 0 + .ra: x30
STACK CFI 41cd4 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 41ce0 x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 41ce8 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 41cfc .ra: .cfa -240 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 42c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 42c14 .cfa: sp 320 + .ra: .cfa -240 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI INIT 43078 274 .cfa: sp 0 + .ra: x30
STACK CFI 4307c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 43084 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4308c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4309c .ra: .cfa -16 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 43284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 43288 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 432f0 40c .cfa: sp 0 + .ra: x30
STACK CFI 432f4 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 43324 .ra: .cfa -208 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 436dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 436e0 .cfa: sp 288 + .ra: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 43710 a4 .cfa: sp 0 + .ra: x30
STACK CFI 43714 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4371c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 43770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 43778 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4379c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 437a0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 437b8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 437bc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 437c4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 437d0 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 43858 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 438a0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 438a4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 438ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 438b8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 43940 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 43990 1454 .cfa: sp 0 + .ra: x30
STACK CFI 43994 .cfa: sp 832 +
STACK CFI 43998 x25: .cfa -784 + ^ x26: .cfa -776 + ^
STACK CFI 439a8 x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^
STACK CFI 439c0 x23: .cfa -800 + ^ x24: .cfa -792 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 439c8 .ra: .cfa -752 + ^
STACK CFI 44968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 44970 .cfa: sp 832 + .ra: .cfa -752 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI INIT 44e18 13e0 .cfa: sp 0 + .ra: x30
STACK CFI 44e1c .cfa: sp 880 +
STACK CFI 44e24 x19: .cfa -880 + ^ x20: .cfa -872 + ^
STACK CFI 44e34 x21: .cfa -864 + ^ x22: .cfa -856 + ^
STACK CFI 44e4c x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI 44e5c .ra: .cfa -800 + ^ v8: .cfa -792 + ^
STACK CFI 45f08 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 45f0c .cfa: sp 880 + .ra: .cfa -800 + ^ v8: .cfa -792 + ^ x19: .cfa -880 + ^ x20: .cfa -872 + ^ x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI INIT 46200 43c .cfa: sp 0 + .ra: x30
STACK CFI 46204 .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 46214 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 46224 .ra: .cfa -264 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^
STACK CFI 46568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4656c .cfa: sp 320 + .ra: .cfa -264 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^
