MODULE Linux arm64 27B7C534C5E3EE1C94C7F7317E6AA3970 libpipewire-module-spa-node.so
INFO CODE_ID 34C5B727E3C51CEE94C7F7317E6AA397C448A39C
PUBLIC 6394 0 pipewire__module_init
STACK CFI INIT 5370 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 53a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 53e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 53e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53ec x19: .cfa -16 + ^
STACK CFI 5424 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5430 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5440 50 .cfa: sp 0 + .ra: x30
STACK CFI 5448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5450 x19: .cfa -16 + ^
STACK CFI 5488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5490 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54a8 x19: .cfa -16 + ^
STACK CFI 5508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5510 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5550 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5560 114 .cfa: sp 0 + .ra: x30
STACK CFI 5568 .cfa: sp 48 +
STACK CFI 556c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5574 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5598 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 55e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 55f0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 565c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5674 250 .cfa: sp 0 + .ra: x30
STACK CFI 567c .cfa: sp 96 +
STACK CFI 5680 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5688 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5694 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 569c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 57a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 57ac .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 58c4 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 58cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 58d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 58dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 58f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 58f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 58f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5a80 x19: x19 x20: x20
STACK CFI 5a90 x25: x25 x26: x26
STACK CFI 5a94 x27: x27 x28: x28
STACK CFI 5a98 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5aa0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5ab0 x19: x19 x20: x20
STACK CFI 5ab4 x25: x25 x26: x26
STACK CFI 5ab8 x27: x27 x28: x28
STACK CFI 5ac4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5acc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5b60 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5b78 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5b7c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5b80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5b8c x19: x19 x20: x20
STACK CFI 5b94 x25: x25 x26: x26
STACK CFI 5b98 x27: x27 x28: x28
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5bac .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5bb4 7e0 .cfa: sp 0 + .ra: x30
STACK CFI 5bbc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5bd4 .cfa: sp 4384 + x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5cc8 x28: .cfa -24 + ^
STACK CFI 5ce8 x25: .cfa -48 + ^
STACK CFI 5cf4 x26: .cfa -40 + ^
STACK CFI 5cfc x27: .cfa -32 + ^
STACK CFI 5dac x25: x25
STACK CFI 5db0 x26: x26
STACK CFI 5db4 x27: x27
STACK CFI 5db8 x28: x28
STACK CFI 5de0 .cfa: sp 112 +
STACK CFI 5df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5dfc .cfa: sp 4384 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5f0c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5f58 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6000 v8: .cfa -16 + ^
STACK CFI 6070 v8: v8
STACK CFI 6074 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6090 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 60c0 x25: x25
STACK CFI 60c4 x26: x26
STACK CFI 60c8 x27: x27
STACK CFI 60cc x28: x28
STACK CFI 60d0 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 61a4 v8: .cfa -16 + ^
STACK CFI 6210 v8: v8
STACK CFI 6268 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 62d8 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 631c x25: x25
STACK CFI 6320 x26: x26
STACK CFI 6324 x27: x27
STACK CFI 6328 x28: x28
STACK CFI 632c v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6374 v8: v8
STACK CFI 637c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6380 x25: .cfa -48 + ^
STACK CFI 6384 x26: .cfa -40 + ^
STACK CFI 6388 x27: .cfa -32 + ^
STACK CFI 638c x28: .cfa -24 + ^
STACK CFI 6390 v8: .cfa -16 + ^
STACK CFI INIT 6394 538 .cfa: sp 0 + .ra: x30
STACK CFI 639c .cfa: sp 128 +
STACK CFI 63a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 63b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 63bc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 641c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6448 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6594 x21: x21 x22: x22
STACK CFI 6598 x27: x27 x28: x28
STACK CFI 65d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 65e0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 65fc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6644 x27: x27 x28: x28
STACK CFI 6688 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6754 x27: x27 x28: x28
STACK CFI 6768 x21: x21 x22: x22
STACK CFI 676c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 67f4 x27: x27 x28: x28
STACK CFI 6804 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 680c x27: x27 x28: x28
STACK CFI 6818 x21: x21 x22: x22
STACK CFI 6820 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6848 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 68c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 68c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
