MODULE Linux arm64 E189F4E9E60509861C3A71F2073637520 libpanelw.so.6
INFO CODE_ID E9F489E105E686091C3A71F207363752C2584611
PUBLIC 1730 0 ground_panel
PUBLIC 1794 0 panel_above
PUBLIC 17d0 0 ceiling_panel
PUBLIC 1834 0 panel_below
PUBLIC 1894 0 bottom_panel
PUBLIC 1b80 0 del_panel
PUBLIC 1e30 0 hide_panel
PUBLIC 20d4 0 panel_hidden
PUBLIC 2140 0 move_panel
PUBLIC 2390 0 replace_panel
PUBLIC 25b0 0 show_panel
PUBLIC 2840 0 new_panel
PUBLIC 28f0 0 top_panel
PUBLIC 2910 0 update_panels_sp
PUBLIC 2af0 0 update_panels
PUBLIC 2b14 0 set_panel_userptr
PUBLIC 2b44 0 panel_userptr
PUBLIC 2b74 0 panel_window
STACK CFI INIT 1660 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1690 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 16d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16dc x19: .cfa -16 + ^
STACK CFI 1714 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1730 64 .cfa: sp 0 + .ra: x30
STACK CFI 1754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 176c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1774 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1778 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1794 38 .cfa: sp 0 + .ra: x30
STACK CFI 179c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17d0 64 .cfa: sp 0 + .ra: x30
STACK CFI 17f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 180c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1814 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1834 60 .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 184c x19: .cfa -16 + ^
STACK CFI 187c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1894 2ec .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 19e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1a48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1a64 x25: .cfa -16 + ^
STACK CFI 1b10 x23: x23 x24: x24
STACK CFI 1b14 x25: x25
STACK CFI 1b1c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1b24 x23: x23 x24: x24 x25: x25
STACK CFI 1b34 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1b40 x23: x23 x24: x24 x25: x25
STACK CFI 1b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1b64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1b80 2ac .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1b90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1b9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1c9c x21: x21 x22: x22
STACK CFI 1ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1d00 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d1c x25: .cfa -16 + ^
STACK CFI 1dc8 x23: x23 x24: x24
STACK CFI 1dcc x25: x25
STACK CFI 1dd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1ddc x23: x23 x24: x24 x25: x25
STACK CFI 1dec x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1df8 x23: x23 x24: x24 x25: x25
STACK CFI 1e1c x21: x21 x22: x22
STACK CFI 1e24 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 1e30 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1e40 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1e48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1fa8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1fc4 x25: .cfa -16 + ^
STACK CFI 2070 x23: x23 x24: x24
STACK CFI 2074 x25: x25
STACK CFI 207c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2084 x23: x23 x24: x24 x25: x25
STACK CFI 2094 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 20a0 x23: x23 x24: x24 x25: x25
STACK CFI 20c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 20d4 68 .cfa: sp 0 + .ra: x30
STACK CFI 20e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20ec x19: .cfa -16 + ^
STACK CFI 2114 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2124 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2140 24c .cfa: sp 0 + .ra: x30
STACK CFI 2150 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2158 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2164 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 216c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2250 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2288 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 229c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2350 x25: x25 x26: x26
STACK CFI 2358 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2390 220 .cfa: sp 0 + .ra: x30
STACK CFI 23a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 248c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2494 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 24c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24dc x25: .cfa -16 + ^
STACK CFI 2588 x23: x23 x24: x24
STACK CFI 258c x25: x25
STACK CFI 2594 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 25a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 25b0 288 .cfa: sp 0 + .ra: x30
STACK CFI 25c0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 25c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 25d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 273c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2758 x25: .cfa -16 + ^
STACK CFI 2800 x23: x23 x24: x24
STACK CFI 2804 x25: x25
STACK CFI 280c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2814 x23: x23 x24: x24 x25: x25
STACK CFI 2824 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2840 ac .cfa: sp 0 + .ra: x30
STACK CFI 2848 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2850 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 289c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 28a4 x21: .cfa -16 + ^
STACK CFI 28b8 x21: x21
STACK CFI 28c4 x21: .cfa -16 + ^
STACK CFI 28e8 x21: x21
STACK CFI INIT 28f0 18 .cfa: sp 0 + .ra: x30
STACK CFI 28f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2900 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2910 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2920 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2928 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 293c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2940 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2944 x25: .cfa -16 + ^
STACK CFI 29f4 x19: x19 x20: x20
STACK CFI 29f8 x23: x23 x24: x24
STACK CFI 29fc x25: x25
STACK CFI 2a04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2a0c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2af0 24 .cfa: sp 0 + .ra: x30
STACK CFI 2af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b14 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b44 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b74 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b98 .cfa: sp 0 + .ra: .ra x29: x29
