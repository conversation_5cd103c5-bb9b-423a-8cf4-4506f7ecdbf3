MODULE Linux arm64 22C8521C3ACD93B3FEB7E1F8AF9F4D4F0 libopencv_dnn_objdetect.so.4.3
INFO CODE_ID 1C52C822CD3AB393FEB7E1F8AF9F4D4F54E3BBD9
PUBLIC 1768 0 _init
PUBLIC 1a10 0 _GLOBAL__sub_I_core_detect.cpp
PUBLIC 1a40 0 call_weak_fn
PUBLIC 1a58 0 deregister_tm_clones
PUBLIC 1a90 0 register_tm_clones
PUBLIC 1ad0 0 __do_global_dtors_aux
PUBLIC 1b18 0 frame_dummy
PUBLIC 1b50 0 cv::dnn_objdetect::InferBbox::comparator(std::pair<double, unsigned long>, std::pair<double, unsigned long>)
PUBLIC 1b68 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, std::pair<double, unsigned long>, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)> >(__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, long, std::pair<double, unsigned long>, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)>) [clone .constprop.201]
PUBLIC 1d08 0 void std::__insertion_sort<__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)> >(__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, __gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)>) [clone .constprop.206]
PUBLIC 1e08 0 cv::Mat::~Mat()
PUBLIC 1e98 0 cv::dnn_objdetect::InferBbox::transform_bboxes(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*)
PUBLIC 2068 0 cv::dnn_objdetect::InferBbox::final_probability_dist(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*)
PUBLIC 2160 0 cv::dnn_objdetect::InferBbox::transform_bboxes_inv(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*, std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*)
PUBLIC 21b8 0 cv::dnn_objdetect::InferBbox::assert_predictions(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*)
PUBLIC 2250 0 cv::dnn_objdetect::InferBbox::intersection_over_union(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*, std::vector<double, std::allocator<double> >*, std::vector<double, std::allocator<double> >*)
PUBLIC 2320 0 std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >::~vector()
PUBLIC 2380 0 std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >::_M_default_append(unsigned long)
PUBLIC 2540 0 std::vector<double, std::allocator<double> >::_M_default_append(unsigned long)
PUBLIC 2690 0 void std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::_M_emplace_back_aux<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 28c8 0 std::_Bvector_base<std::allocator<bool> >::_M_deallocate()
PUBLIC 28e0 0 void std::vector<cv::dnn_objdetect::object, std::allocator<cv::dnn_objdetect::object> >::_M_emplace_back_aux<cv::dnn_objdetect::object const&>(cv::dnn_objdetect::object const&)
PUBLIC 2b58 0 void std::vector<std::pair<double, double>, std::allocator<std::pair<double, double> > >::_M_emplace_back_aux<std::pair<double, double> >(std::pair<double, double>&&)
PUBLIC 2c50 0 cv::dnn_objdetect::InferBbox::InferBbox(cv::Mat, cv::Mat, cv::Mat)
PUBLIC 3998 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, std::pair<double, unsigned long>, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)> >(__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, long, std::pair<double, unsigned long>, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)>)
PUBLIC 3b40 0 void std::__introsort_loop<__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)> >(__gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, __gnu_cxx::__normal_iterator<std::pair<double, unsigned long>*, std::vector<std::pair<double, unsigned long>, std::allocator<std::pair<double, unsigned long> > > >, long, __gnu_cxx::__ops::_Iter_comp_iter<bool (*)(std::pair<double, unsigned long>, std::pair<double, unsigned long>)>) [clone .constprop.196]
PUBLIC 3d60 0 cv::dnn_objdetect::InferBbox::non_maximal_suppression(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*, std::vector<double, std::allocator<double> >*)
PUBLIC 46c0 0 cv::dnn_objdetect::InferBbox::nms_wrapper(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >&, std::vector<unsigned long, std::allocator<unsigned long> >&, std::vector<double, std::allocator<double> >&)
PUBLIC 50f0 0 cv::dnn_objdetect::InferBbox::filter_top_n(std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*, std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >*, std::vector<std::vector<double, std::allocator<double> >, std::allocator<std::vector<double, std::allocator<double> > > >&, std::vector<unsigned long, std::allocator<unsigned long> >&, std::vector<double, std::allocator<double> >&)
PUBLIC 5770 0 cv::dnn_objdetect::InferBbox::filter(double)
PUBLIC 5d7c 0 _fini
STACK CFI INIT 1b50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b68 19c .cfa: sp 0 + .ra: x30
STACK CFI 1b6c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b84 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b94 .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x25: .cfa -32 + ^
STACK CFI 1c2c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 1c30 .cfa: sp 80 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1cbc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 1cc0 .cfa: sp 80 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 1d08 fc .cfa: sp 0 + .ra: x30
STACK CFI 1d20 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1d34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1d44 .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x25: .cfa -32 + ^
STACK CFI 1db0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 1db8 .cfa: sp 80 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 1e00 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 1e08 90 .cfa: sp 0 + .ra: x30
STACK CFI 1e0c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1e80 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1e88 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1e94 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1e98 1cc .cfa: sp 0 + .ra: x30
STACK CFI 1ea4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1ea8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1eb0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1eb8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1ec8 .ra: .cfa -32 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2014 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2018 .cfa: sp 112 + .ra: .cfa -32 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 205c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 2068 f8 .cfa: sp 0 + .ra: x30
STACK CFI 206c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2074 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 215c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 2160 58 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b8 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2268 .cfa: sp 32 +
STACK CFI 231c .cfa: sp 0 +
STACK CFI INIT 2320 5c .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2328 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2370 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2380 1bc .cfa: sp 0 + .ra: x30
STACK CFI 23e4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23f8 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2518 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 2540 14c .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2560 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 25a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 25b0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 264c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2650 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 2690 234 .cfa: sp 0 + .ra: x30
STACK CFI 2694 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 269c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 26a8 .ra: .cfa -32 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 27f8 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 28c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e0 278 .cfa: sp 0 + .ra: x30
STACK CFI 28e4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 28e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 28f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 28f8 .ra: .cfa -24 + ^ x25: .cfa -32 + ^
STACK CFI 2a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2a90 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 2b58 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b64 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2b6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2c20 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2c50 d0c .cfa: sp 0 + .ra: x30
STACK CFI 2c54 .cfa: sp 944 +
STACK CFI 2c64 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 2c78 x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^
STACK CFI 2c94 .ra: .cfa -864 + ^ v8: .cfa -848 + ^ v9: .cfa -840 + ^ x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI 36e0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 36e4 .cfa: sp 944 + .ra: .cfa -864 + ^ v8: .cfa -848 + ^ v9: .cfa -840 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^ x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI INIT 3998 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 399c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 39a0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 39b8 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 39d0 .ra: .cfa -32 + ^ v8: .cfa -24 + ^
STACK CFI 3a80 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3a88 .cfa: sp 112 + .ra: .cfa -32 + ^ v8: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3b40 218 .cfa: sp 0 + .ra: x30
STACK CFI 3b44 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b58 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3cf4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3d60 93c .cfa: sp 0 + .ra: x30
STACK CFI 3d64 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 3d68 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 3d70 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 3d84 .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4524 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4528 .cfa: sp 256 + .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 46c0 a30 .cfa: sp 0 + .ra: x30
STACK CFI 46cc .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 46d0 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 46e0 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 46e8 x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 4700 .ra: .cfa -256 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 47b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47c0 .cfa: sp 336 + .ra: .cfa -256 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 5008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 500c .cfa: sp 336 + .ra: .cfa -256 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 50f0 654 .cfa: sp 0 + .ra: x30
STACK CFI 50f4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 50f8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5100 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5108 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 5118 .ra: .cfa -48 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 5120 v8: .cfa -40 + ^
STACK CFI 553c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5540 .cfa: sp 128 + .ra: .cfa -48 + ^ v8: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 56d0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 56d4 .cfa: sp 128 + .ra: .cfa -48 + ^ v8: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 5770 60c .cfa: sp 0 + .ra: x30
STACK CFI 5774 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 5778 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 578c .ra: .cfa -160 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 5c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5c98 .cfa: sp 240 + .ra: .cfa -160 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 1a10 30 .cfa: sp 0 + .ra: x30
STACK CFI 1a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1a30 .cfa: sp 0 + .ra: .ra x19: x19
