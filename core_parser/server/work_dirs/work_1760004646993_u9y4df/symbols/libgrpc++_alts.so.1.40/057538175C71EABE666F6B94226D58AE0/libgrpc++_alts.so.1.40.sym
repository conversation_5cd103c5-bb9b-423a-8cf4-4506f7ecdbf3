MODULE Linux arm64 057538175C71EABE666F6B94226D58AE0 libgrpc++_alts.so.1.40
INFO CODE_ID 17387505715CBEEA666F6B94226D58AE
PUBLIC 1b30 0 _init
PUBLIC 1da0 0 call_weak_fn
PUBLIC 1dc0 0 deregister_tm_clones
PUBLIC 1df0 0 register_tm_clones
PUBLIC 1e30 0 __do_global_dtors_aux
PUBLIC 1e80 0 frame_dummy
PUBLIC 1e90 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC 22b0 0 grpc::experimental::AltsContext::application_protocol[abi:cxx11]() const
PUBLIC 2380 0 grpc::experimental::AltsContext::record_protocol[abi:cxx11]() const
PUBLIC 2450 0 grpc::experimental::AltsContext::peer_service_account[abi:cxx11]() const
PUBLIC 2520 0 grpc::experimental::AltsContext::local_service_account[abi:cxx11]() const
PUBLIC 25f0 0 grpc::experimental::AltsContext::security_level() const
PUBLIC 2600 0 grpc::experimental::AltsContext::peer_rpc_versions() const
PUBLIC 2610 0 grpc::experimental::AltsContext::peer_attributes[abi:cxx11]() const
PUBLIC 2620 0 grpc::experimental::AltsContext::AltsContext(grpc_gcp_AltsContext const*)
PUBLIC 3360 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 34c0 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3740 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC 3b60 0 std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >* std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_copy<false, std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_Alloc_node>(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*, std::_Rb_tree_node_base*, std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_Alloc_node&) [clone .isra.0]
PUBLIC 3f40 0 grpc::experimental::GetAltsContextFromAuthContext(std::shared_ptr<grpc::AuthContext const> const&)
PUBLIC 4580 0 grpc::experimental::AltsClientAuthzCheck(std::shared_ptr<grpc::AuthContext const> const&, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 4e00 0 grpc::experimental::AltsContext::~AltsContext()
PUBLIC 4f10 0 std::unique_ptr<grpc::experimental::AltsContext, std::default_delete<grpc::experimental::AltsContext> >::~unique_ptr()
PUBLIC 5034 0 _fini
STACK CFI INIT 1dc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e30 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e3c x19: .cfa -16 + ^
STACK CFI 1e74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e90 418 .cfa: sp 0 + .ra: x30
STACK CFI 1e98 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1ea0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1eac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1eb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1ebc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2228 x21: x21 x22: x22
STACK CFI 222c x27: x27 x28: x28
STACK CFI 22a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 22b0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 22b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 22cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 22d8 x21: .cfa -32 + ^
STACK CFI 233c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2340 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2380 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2384 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 239c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 23a8 x21: .cfa -32 + ^
STACK CFI 240c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2410 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2450 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2454 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 246c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2478 x21: .cfa -32 + ^
STACK CFI 24dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 24e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2520 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2524 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 253c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2548 x21: .cfa -32 + ^
STACK CFI 25ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25b0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 25f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2600 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3360 154 .cfa: sp 0 + .ra: x30
STACK CFI 3364 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 336c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3378 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3380 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3388 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3448 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 34c0 27c .cfa: sp 0 + .ra: x30
STACK CFI 34c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 34d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 34dc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 34e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3580 x19: x19 x20: x20
STACK CFI 3584 x21: x21 x22: x22
STACK CFI 3590 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3594 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3620 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 362c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3634 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3674 x21: x21 x22: x22
STACK CFI 367c x19: x19 x20: x20
STACK CFI 368c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3690 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 36ec x19: x19 x20: x20
STACK CFI 36f0 x21: x21 x22: x22
STACK CFI 3704 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3708 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2620 d3c .cfa: sp 0 + .ra: x30
STACK CFI 2624 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 262c x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 2648 x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2658 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 2660 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2ae8 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 3740 418 .cfa: sp 0 + .ra: x30
STACK CFI 3748 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3750 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 375c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3768 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 376c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3ad8 x21: x21 x22: x22
STACK CFI 3adc x27: x27 x28: x28
STACK CFI 3b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 3b60 3dc .cfa: sp 0 + .ra: x30
STACK CFI 3b64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3b74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3b7c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3b88 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3d20 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4e00 110 .cfa: sp 0 + .ra: x30
STACK CFI 4e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e1c x21: .cfa -16 + ^
STACK CFI 4e8c x21: x21
STACK CFI 4f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f40 634 .cfa: sp 0 + .ra: x30
STACK CFI 3f44 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 3f54 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 3f6c x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 3f88 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 4014 x21: x21 x22: x22
STACK CFI 4040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 4044 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^ x29: .cfa -336 + ^
STACK CFI 4070 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 4074 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 40e8 x23: x23 x24: x24
STACK CFI 40ec x25: x25 x26: x26
STACK CFI 40f4 x21: x21 x22: x22
STACK CFI 411c x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 4144 x23: x23 x24: x24
STACK CFI 4148 x25: x25 x26: x26
STACK CFI 414c x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 4484 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4488 x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 448c x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 4490 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 4494 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 44b0 x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 44c0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 44e8 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 44ec x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 4550 x23: x23 x24: x24
STACK CFI 4554 x25: x25 x26: x26
STACK CFI 4558 x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 4564 x23: x23 x24: x24
STACK CFI 4568 x25: x25 x26: x26
STACK CFI 456c x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI INIT 4f10 124 .cfa: sp 0 + .ra: x30
STACK CFI 4f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f30 x21: .cfa -16 + ^
STACK CFI 4fa4 x21: x21
STACK CFI 5024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5028 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4580 878 .cfa: sp 0 + .ra: x30
STACK CFI 4584 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 459c x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 45ac x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 45b8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4a7c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
