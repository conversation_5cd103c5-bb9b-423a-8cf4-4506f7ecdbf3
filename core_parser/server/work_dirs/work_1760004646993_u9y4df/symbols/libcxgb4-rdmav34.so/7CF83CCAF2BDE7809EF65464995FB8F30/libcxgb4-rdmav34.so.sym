MODULE Linux arm64 7CF83CCAF2BDE7809EF65464995FB8F30 libcxgb4-rdmav34.so
INFO CODE_ID CA3CF87CBDF280E79EF65464995FB8F3D62317E3
STACK CFI INIT 16a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1710 48 .cfa: sp 0 + .ra: x30
STACK CFI 1714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 171c x19: .cfa -16 + ^
STACK CFI 1754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1770 178 .cfa: sp 0 + .ra: x30
STACK CFI 1778 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1784 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 178c x25: .cfa -16 + ^
STACK CFI 17b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18a0 x21: x21 x22: x22
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 18b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 18dc x21: x21 x22: x22
STACK CFI INIT 18f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 18f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1900 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 190c x21: .cfa -16 + ^
STACK CFI 1994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 19a0 710 .cfa: sp 0 + .ra: x30
STACK CFI 19a8 .cfa: sp 192 +
STACK CFI 19ac .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 19c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1a78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1a7c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1a80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1e58 x21: x21 x22: x22
STACK CFI 1e5c x23: x23 x24: x24
STACK CFI 1e60 x27: x27 x28: x28
STACK CFI 1e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1e94 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2064 x21: x21 x22: x22
STACK CFI 2068 x23: x23 x24: x24
STACK CFI 2070 x27: x27 x28: x28
STACK CFI 2074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 207c .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 20a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 20a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 20ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 20b0 128 .cfa: sp 0 + .ra: x30
STACK CFI 20b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 20c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 20ec x25: .cfa -16 + ^
STACK CFI 2164 x21: x21 x22: x22
STACK CFI 2168 x25: x25
STACK CFI 2188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2190 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 219c x21: x21 x22: x22
STACK CFI 21a4 x25: x25
STACK CFI 21a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 21b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 21b4 x21: x21 x22: x22
STACK CFI 21bc x25: x25
STACK CFI INIT 21e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 21e8 .cfa: sp 80 +
STACK CFI 21f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 21fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2224 x21: .cfa -16 + ^
STACK CFI 224c x21: x21
STACK CFI 227c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2284 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2288 x21: x21
STACK CFI 2290 x21: .cfa -16 + ^
STACK CFI INIT 2294 18 .cfa: sp 0 + .ra: x30
STACK CFI 229c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 22a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22b0 60 .cfa: sp 0 + .ra: x30
STACK CFI 22b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22c0 x19: .cfa -16 + ^
STACK CFI 22e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 22e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2310 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2318 .cfa: sp 368 +
STACK CFI 2328 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2330 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23c0 .cfa: sp 368 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23c4 298 .cfa: sp 0 + .ra: x30
STACK CFI 23cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 23e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 23ec .cfa: sp 672 + x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2470 x23: .cfa -16 + ^
STACK CFI 24b0 x23: x23
STACK CFI 24ec x23: .cfa -16 + ^
STACK CFI 2554 x23: x23
STACK CFI 2574 .cfa: sp 64 +
STACK CFI 2584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 258c .cfa: sp 672 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2654 x23: x23
STACK CFI 2658 x23: .cfa -16 + ^
STACK CFI INIT 2660 114 .cfa: sp 0 + .ra: x30
STACK CFI 2668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 267c x21: .cfa -16 + ^
STACK CFI 2750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2758 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 276c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2774 100c .cfa: sp 0 + .ra: x30
STACK CFI 277c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 278c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2794 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 279c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27ac .cfa: sp 752 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2808 .cfa: sp 96 +
STACK CFI 2820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 2828 .cfa: sp 752 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 282c x25: .cfa -32 + ^
STACK CFI 2830 x26: .cfa -24 + ^
STACK CFI 2e60 x25: x25
STACK CFI 2e64 x26: x26
STACK CFI 2e68 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2fcc x25: x25
STACK CFI 2fd0 x26: x26
STACK CFI 2fd4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3660 x25: x25
STACK CFI 3664 x26: x26
STACK CFI 3668 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3678 x25: x25 x26: x26
STACK CFI 367c x25: .cfa -32 + ^
STACK CFI 3680 x26: .cfa -24 + ^
STACK CFI INIT 3780 440 .cfa: sp 0 + .ra: x30
STACK CFI 3788 .cfa: sp 256 +
STACK CFI 3794 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 37a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 37b0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 37dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 37f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3a90 x21: x21 x22: x22
STACK CFI 3a9c x23: x23 x24: x24
STACK CFI 3ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3ad8 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3b2c x21: x21 x22: x22
STACK CFI 3b30 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3b9c x21: x21 x22: x22
STACK CFI 3ba4 x23: x23 x24: x24
STACK CFI 3bb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3bbc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 3bc0 490 .cfa: sp 0 + .ra: x30
STACK CFI 3bc8 .cfa: sp 480 +
STACK CFI 3bd4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3bdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3be8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3bf4 x23: .cfa -16 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3f14 .cfa: sp 480 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4050 75c .cfa: sp 0 + .ra: x30
STACK CFI 4058 .cfa: sp 224 +
STACK CFI 405c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4064 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 406c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 408c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 40e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 42e0 x23: x23 x24: x24
STACK CFI 42e4 x25: x25 x26: x26
STACK CFI 42f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 42f8 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 43cc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 43ec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4610 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 465c .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4664 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 479c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 47a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 47a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 47b0 11ec .cfa: sp 0 + .ra: x30
STACK CFI 47b8 .cfa: sp 256 +
STACK CFI 47bc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 47c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 47d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 47d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4858 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 48c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4bd4 x27: x27 x28: x28
STACK CFI 4bd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5174 x27: x27 x28: x28
STACK CFI 517c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5774 x27: x27 x28: x28
STACK CFI 57e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5854 x27: x27 x28: x28
STACK CFI 585c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5880 x27: x27 x28: x28
STACK CFI 5894 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5920 x27: x27 x28: x28
STACK CFI 5930 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5994 x27: x27 x28: x28
STACK CFI 5998 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 59a0 148 .cfa: sp 0 + .ra: x30
STACK CFI 59ac .cfa: sp 192 +
STACK CFI 59b0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 59b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 59c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 59d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5a84 .cfa: sp 192 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5af0 40 .cfa: sp 0 + .ra: x30
STACK CFI 5af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b30 60 .cfa: sp 0 + .ra: x30
STACK CFI 5b38 .cfa: sp 48 +
STACK CFI 5b48 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b8c .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5b90 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5b98 .cfa: sp 64 +
STACK CFI 5ba4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c24 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5c40 3c .cfa: sp 0 + .ra: x30
STACK CFI 5c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5c80 118 .cfa: sp 0 + .ra: x30
STACK CFI 5c88 .cfa: sp 176 +
STACK CFI 5c8c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5c94 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ca4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5cac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5cb8 x25: .cfa -16 + ^
STACK CFI 5d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5d84 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5da0 74 .cfa: sp 0 + .ra: x30
STACK CFI 5da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5db0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5db8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5e14 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5e1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5eb4 254 .cfa: sp 0 + .ra: x30
STACK CFI 5ebc .cfa: sp 192 +
STACK CFI 5ecc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ed4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ee0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 60a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 60ac .cfa: sp 192 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6110 98 .cfa: sp 0 + .ra: x30
STACK CFI 6118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6120 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 61a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 61b0 24c .cfa: sp 0 + .ra: x30
STACK CFI 61b8 .cfa: sp 192 +
STACK CFI 61bc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 61c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 61d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6230 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6298 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6358 x23: x23 x24: x24
STACK CFI 635c x25: x25 x26: x26
STACK CFI 638c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6394 .cfa: sp 192 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 63d4 x25: x25 x26: x26
STACK CFI 63e0 x23: x23 x24: x24
STACK CFI 63f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 63f8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 6400 60 .cfa: sp 0 + .ra: x30
STACK CFI 6408 .cfa: sp 48 +
STACK CFI 6418 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 645c .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6460 11c .cfa: sp 0 + .ra: x30
STACK CFI 6468 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6470 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 647c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6498 x23: .cfa -16 + ^
STACK CFI 6514 x23: x23
STACK CFI 6524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 652c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6580 704 .cfa: sp 0 + .ra: x30
STACK CFI 6588 .cfa: sp 288 +
STACK CFI 658c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6594 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 659c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 65a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6608 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 679c x25: x25 x26: x26
STACK CFI 67d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 67d8 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 689c x25: x25 x26: x26
STACK CFI 68ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 68f8 x27: .cfa -16 + ^
STACK CFI 6a4c x25: x25 x26: x26
STACK CFI 6a50 x27: x27
STACK CFI 6a54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6b34 x27: .cfa -16 + ^
STACK CFI 6b54 x25: x25 x26: x26
STACK CFI 6b5c x27: x27
STACK CFI 6b64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6bd4 x27: .cfa -16 + ^
STACK CFI 6c1c x27: x27
STACK CFI 6c78 x25: x25 x26: x26
STACK CFI 6c7c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6c80 x27: .cfa -16 + ^
STACK CFI INIT 6c84 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6c8c .cfa: sp 96 +
STACK CFI 6c90 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6c98 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6cac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6cb4 x23: .cfa -16 + ^
STACK CFI 6d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6d48 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6d60 7c .cfa: sp 0 + .ra: x30
STACK CFI 6d68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6de0 7c .cfa: sp 0 + .ra: x30
STACK CFI 6de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6df0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6dfc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1660 24 .cfa: sp 0 + .ra: x30
STACK CFI 1668 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1674 .cfa: sp 0 + .ra: .ra x29: x29
