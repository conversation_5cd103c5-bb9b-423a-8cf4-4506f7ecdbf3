MODULE Linux arm64 3646E582BF5DBED3B76781FC92FF11460 libshm_proxy.so
INFO CODE_ID 82E546365DBFD3BEB76781FC92FF1146
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 2a40 24 0 init_have_lse_atomics
2a40 4 45 0
2a44 4 46 0
2a48 4 45 0
2a4c 4 46 0
2a50 4 47 0
2a54 4 47 0
2a58 4 48 0
2a5c 4 47 0
2a60 4 48 0
PUBLIC 27e0 0 _init
PUBLIC 2a64 0 call_weak_fn
PUBLIC 2a80 0 deregister_tm_clones
PUBLIC 2ab0 0 register_tm_clones
PUBLIC 2af0 0 __do_global_dtors_aux
PUBLIC 2b40 0 frame_dummy
PUBLIC 2b50 0 lios::utils::ShmMgr::~ShmMgr()
PUBLIC 2bc0 0 lios::utils::ShmMgr::~ShmMgr() [clone .localalias]
PUBLIC 2bf0 0 lios::utils::SharedRingBufferSim::Start(std::function<void (void*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>&&)
PUBLIC 2cb0 0 lios::utils::SharedRingBufferSim::Stop()
PUBLIC 2cd0 0 lios::utils::SharedRingBufferSim::IsEnabled()
PUBLIC 2ce0 0 lios::utils::ShmMgr::GetSize()
PUBLIC 2d20 0 lios::utils::ShmMgr::GetBuffer() const
PUBLIC 2d30 0 lios::utils::ShmMgr::GetSharedMemory(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, bool)
PUBLIC 3130 0 lios::utils::SharedRingBufferBase::WriteData(char*, unsigned long)
PUBLIC 31f0 0 lios::utils::SharedRingBufferBase::ReadData(char*, unsigned long, int)
PUBLIC 32d0 0 lios::utils::SharedRingBufferBase::~SharedRingBufferBase()
PUBLIC 33c0 0 lios::utils::SharedRingBufferBase::~SharedRingBufferBase()
PUBLIC 33f0 0 lios::utils::SharedRingBufferBase::CheckMeta()
PUBLIC 35d0 0 lios::utils::SharedRingBufferBase::SharedRingBufferBase(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, bool)
PUBLIC 3720 0 std::_Sp_counted_ptr<lios::utils::ShmMgr*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 3730 0 std::_Sp_counted_ptr<lios::utils::ShmMgr*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 3740 0 std::_Sp_counted_ptr<lios::utils::ShmMgr*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 3750 0 std::_Sp_counted_ptr<lios::utils::ShmMgr*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 3760 0 std::_Sp_counted_ptr<lios::utils::ShmMgr*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 37d0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 3850 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 38f0 0 __aarch64_ldadd4_relax
PUBLIC 3920 0 __aarch64_ldadd8_acq
PUBLIC 3950 0 __aarch64_ldadd4_acq_rel
PUBLIC 3980 0 _fini
STACK CFI INIT 2a80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ab0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2afc x19: .cfa -16 + ^
STACK CFI 2b34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3730 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b50 64 .cfa: sp 0 + .ra: x30
STACK CFI 2b54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2bc0 28 .cfa: sp 0 + .ra: x30
STACK CFI 2bc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bcc x19: .cfa -16 + ^
STACK CFI 2be4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3740 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3750 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3760 64 .cfa: sp 0 + .ra: x30
STACK CFI 3764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 376c x19: .cfa -16 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 37b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 37c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bf0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 2bf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c00 x19: .cfa -64 + ^
STACK CFI 2c94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ca4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2cb0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ce0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2cf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d30 400 .cfa: sp 0 + .ra: x30
STACK CFI 2d34 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2d50 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2d60 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2d6c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2f58 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3130 bc .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 313c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3148 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3150 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 31e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 31e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 31f0 dc .cfa: sp 0 + .ra: x30
STACK CFI 31f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3208 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3254 x21: .cfa -16 + ^
STACK CFI 3280 x21: x21
STACK CFI 3294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3298 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 32a0 x21: x21
STACK CFI 32ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 32bc x21: x21
STACK CFI 32c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 37d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 37d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37e4 x19: .cfa -16 + ^
STACK CFI 3818 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 381c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 382c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3850 9c .cfa: sp 0 + .ra: x30
STACK CFI 3854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3860 x19: .cfa -16 + ^
STACK CFI 38a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 38a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 38d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 38dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 38e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32d0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 32d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 339c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 33c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 33c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33cc x19: .cfa -16 + ^
STACK CFI 33e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33f0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 33f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 33fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3448 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 35d0 148 .cfa: sp 0 + .ra: x30
STACK CFI 35d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 35ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3600 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3698 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 38f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3920 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3950 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a40 24 .cfa: sp 0 + .ra: x30
STACK CFI 2a44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a5c .cfa: sp 0 + .ra: .ra x29: x29
