MODULE Linux arm64 8103A994A49DAEF2AFBC90642CE280FE0 libcmdline-samba4.so.0
INFO CODE_ID 94A903819DA4F2AEAFBC90642CE280FE86A20831
PUBLIC 3670 0 samba_cmdline_set_talloc_ctx
PUBLIC 36c0 0 samba_cmdline_get_talloc_ctx
PUBLIC 36e0 0 samba_cmdline_init_common
PUBLIC 3744 0 samba_cmdline_set_load_config_fn
PUBLIC 3770 0 samba_cmdline_set_lp_ctx
PUBLIC 37b0 0 samba_cmdline_get_lp_ctx
PUBLIC 3af4 0 samba_cmdline_set_creds
PUBLIC 3b50 0 samba_cmdline_get_creds
PUBLIC 3d20 0 samba_cmdline_get_daemon_cfg
PUBLIC 3d40 0 samba_cmdline_set_machine_account_fn
PUBLIC 3d60 0 samba_cmdline_burn
PUBLIC 3f50 0 samba_cmdline_sanity_check
PUBLIC 3f70 0 samba_popt_get_context
PUBLIC 3fd0 0 samba_cmdline_get_popt
PUBLIC 40f0 0 closefrom_except
PUBLIC 41e0 0 closefrom_except_fd_params
PUBLIC 43c0 0 cli_credentials_set_cmdline_callbacks
STACK CFI INIT 2df0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e20 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e60 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e6c x19: .cfa -16 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ec0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f70 6c .cfa: sp 0 + .ra: x30
STACK CFI 2f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f80 x19: .cfa -16 + ^
STACK CFI 2f98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2fe0 138 .cfa: sp 0 + .ra: x30
STACK CFI 2fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ff0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ffc x21: .cfa -16 + ^
STACK CFI 30a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 30b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3120 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 3128 .cfa: sp 64 +
STACK CFI 3134 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 313c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3148 x21: .cfa -16 + ^
STACK CFI 3214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 321c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 32f0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 32f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3300 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3310 x21: .cfa -16 + ^
STACK CFI 3368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 33c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 33d0 128 .cfa: sp 0 + .ra: x30
STACK CFI 34cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3500 40 .cfa: sp 0 + .ra: x30
STACK CFI 3520 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3540 130 .cfa: sp 0 + .ra: x30
STACK CFI 3548 .cfa: sp 320 +
STACK CFI 3554 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 355c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3564 x21: .cfa -16 + ^
STACK CFI 365c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3664 .cfa: sp 320 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3670 4c .cfa: sp 0 + .ra: x30
STACK CFI 3684 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 36c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 36e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36f0 x19: .cfa -16 + ^
STACK CFI 373c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3744 28 .cfa: sp 0 + .ra: x30
STACK CFI 374c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3760 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3770 3c .cfa: sp 0 + .ra: x30
STACK CFI 3778 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 378c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 379c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 37b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37d0 324 .cfa: sp 0 + .ra: x30
STACK CFI 37d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 37e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 37e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 37f0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 384c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3938 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 396c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3974 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 39b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 39bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 39cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 39d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a58 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3af4 5c .cfa: sp 0 + .ra: x30
STACK CFI 3b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b50 20 .cfa: sp 0 + .ra: x30
STACK CFI 3b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b70 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ba8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3bac x21: .cfa -16 + ^
STACK CFI 3bf8 x21: x21
STACK CFI 3bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3c24 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3c60 x21: .cfa -16 + ^
STACK CFI 3cb8 x21: x21
STACK CFI 3cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3d20 20 .cfa: sp 0 + .ra: x30
STACK CFI 3d28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d40 20 .cfa: sp 0 + .ra: x30
STACK CFI 3d48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d60 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 3d68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3d74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3d88 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3d90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3d9c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3dac x27: .cfa -16 + ^
STACK CFI 3ee8 x19: x19 x20: x20
STACK CFI 3ef0 x21: x21 x22: x22
STACK CFI 3ef4 x23: x23 x24: x24
STACK CFI 3efc x27: x27
STACK CFI 3f00 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 3f08 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3f0c x19: x19 x20: x20
STACK CFI 3f10 x21: x21 x22: x22
STACK CFI 3f14 x23: x23 x24: x24
STACK CFI 3f18 x27: x27
STACK CFI 3f28 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 3f30 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3f50 1c .cfa: sp 0 + .ra: x30
STACK CFI 3f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f70 5c .cfa: sp 0 + .ra: x30
STACK CFI 3f78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f98 x23: .cfa -16 + ^
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3fd0 118 .cfa: sp 0 + .ra: x30
STACK CFI 3fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 40f0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 4100 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4108 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4110 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 411c x23: .cfa -16 + ^
STACK CFI 41a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 41a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 41d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 41e0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 41e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 41f0 .cfa: x29 80 +
STACK CFI 41fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4214 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4228 x25: .cfa -16 + ^
STACK CFI 439c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 43a4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 43c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 43c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43e0 5b8 .cfa: sp 0 + .ra: x30
STACK CFI 43e8 .cfa: sp 96 +
STACK CFI 43f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4404 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 440c x23: .cfa -16 + ^
STACK CFI 4464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 446c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 46f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 46f8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4790 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
