MODULE Linux arm64 CA7E4EAB63E65C390F6416A539574F310 libunwind.so.8
INFO CODE_ID AB4E7ECAE663395C0F6416A539574F311A15716A
PUBLIC 43c0 0 _Uaarch64_get_elf_image
PUBLIC 4ac0 0 _Uaarch64_get_exe_image_path
PUBLIC 4c30 0 _Uaarch64_flush_cache
PUBLIC 5ef0 0 _Uaarch64_strerror
PUBLIC 5fb0 0 _Uaarch64_is_fpreg
PUBLIC 6100 0 _Uaarch64_regname
PUBLIC 6144 0 _U_dyn_cancel
PUBLIC 61d0 0 _U_dyn_info_list_addr
PUBLIC 6220 0 _U_dyn_register
PUBLIC 62b0 0 _Uaarch64_get_accessors
PUBLIC 6300 0 _ULaarch64_destroy_addr_space
PUBLIC 6310 0 _ULaarch64_get_reg
PUBLIC 73e0 0 _ULaarch64_set_reg
PUBLIC 7474 0 _ULaarch64_get_fpreg
PUBLIC 74a4 0 _ULaarch64_set_fpreg
PUBLIC 74d0 0 _ULaarch64_set_caching_policy
PUBLIC 7540 0 _ULaarch64_set_cache_size
PUBLIC 75f4 0 _ULaarch64_apply_reg_state
PUBLIC 7604 0 _ULaarch64_create_addr_space
PUBLIC 7620 0 _ULaarch64_get_save_loc
PUBLIC 7670 0 _ULaarch64_init_local
PUBLIC 76d0 0 _ULaarch64_init_local2
PUBLIC 7790 0 _ULaarch64_init_remote
PUBLIC 77a4 0 _ULaarch64_is_signal_frame
PUBLIC 7820 0 _ULaarch64_resume
PUBLIC 7974 0 _ULaarch64_dwarf_search_unwind_table
PUBLIC 7da0 0 _ULaarch64_get_proc_info_by_ip
PUBLIC 7e40 0 _ULaarch64_get_proc_name
PUBLIC 8050 0 _ULaarch64_reg_states_iterate
PUBLIC 8724 0 _ULaarch64_step
PUBLIC 8a20 0 backtrace
PUBLIC 9230 0 _ULaarch64_get_proc_info
PUBLIC 9270 0 _ULaarch64_dwarf_find_debug_frame
PUBLIC 9840 0 _ULaarch64_dwarf_find_unwind_table
STACK CFI INIT 1fe0 10 .cfa: sp 0 + .ra: x30
STACK CFI 1fe4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ff0 234 .cfa: sp 0 + .ra: x30
STACK CFI 1ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2224 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2300 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2338 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 233c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 23d0 218 .cfa: sp 0 + .ra: x30
STACK CFI 23d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2584 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 25f0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 25f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 260c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2618 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2638 x25: x25 x26: x26
STACK CFI 263c x27: x27 x28: x28
STACK CFI 2640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2644 .cfa: sp 128 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 2654 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2658 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 265c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 266c x19: x19 x20: x20
STACK CFI 2670 x21: x21 x22: x22
STACK CFI 2674 x23: x23 x24: x24
STACK CFI 2678 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2680 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2684 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 27e0 10c .cfa: sp 0 + .ra: x30
STACK CFI 27e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 286c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2880 x21: .cfa -16 + ^
STACK CFI 28c0 x21: x21
STACK CFI 28c4 x21: .cfa -16 + ^
STACK CFI 28e4 x21: x21
STACK CFI INIT 28f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 28f4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2904 x21: .cfa -144 + ^
STACK CFI 2914 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 298c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x29: .cfa -176 + ^
STACK CFI INIT 29a0 160 .cfa: sp 0 + .ra: x30
STACK CFI 29a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 29ac x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 29cc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2a44 x21: x21 x22: x22
STACK CFI 2a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a78 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI 2a7c x21: x21 x22: x22
STACK CFI 2a84 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2afc x21: x21 x22: x22
STACK CFI INIT 2b00 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2b04 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2b0c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2b3c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2bd8 x21: x21 x22: x22
STACK CFI 2bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2be0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI 2bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c00 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2c04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2c18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2cb0 44 .cfa: sp 0 + .ra: x30
STACK CFI 2cb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cc4 x19: .cfa -16 + ^
STACK CFI 2cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2cf4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2cf8 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2d04 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2d10 x21: .cfa -144 + ^
STACK CFI 2d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d80 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2da0 488 .cfa: sp 0 + .ra: x30
STACK CFI 2da4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2db8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2dc4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2fdc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3050 x23: x23 x24: x24
STACK CFI 3060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3064 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 318c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3190 x23: x23 x24: x24
STACK CFI 3194 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3208 x23: x23 x24: x24
STACK CFI 321c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 3220 x23: x23 x24: x24
STACK CFI INIT 3230 138 .cfa: sp 0 + .ra: x30
STACK CFI 3250 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 32c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3370 dc .cfa: sp 0 + .ra: x30
STACK CFI 3374 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3380 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3394 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 33a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 33f8 x19: x19 x20: x20
STACK CFI 33fc x23: x23 x24: x24
STACK CFI 3404 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3408 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3444 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 3450 dc .cfa: sp 0 + .ra: x30
STACK CFI 3454 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3460 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3474 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3480 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34d8 x19: x19 x20: x20
STACK CFI 34dc x23: x23 x24: x24
STACK CFI 34e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 34e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3524 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 3530 154 .cfa: sp 0 + .ra: x30
STACK CFI 3534 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 353c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 356c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 357c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 35bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 35c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3608 x21: x21 x22: x22
STACK CFI 360c x23: x23 x24: x24
STACK CFI 3610 x25: x25 x26: x26
STACK CFI 3614 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3618 x21: x21 x22: x22
STACK CFI 361c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3620 x23: x23 x24: x24
STACK CFI 3624 x25: x25 x26: x26
STACK CFI 362c x21: x21 x22: x22
STACK CFI 3630 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3678 x21: x21 x22: x22
STACK CFI 367c x23: x23 x24: x24
STACK CFI 3680 x25: x25 x26: x26
STACK CFI INIT 3684 164 .cfa: sp 0 + .ra: x30
STACK CFI 3688 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3690 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3698 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 36cc x21: x21 x22: x22
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 36e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 36f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3700 x21: x21 x22: x22
STACK CFI 370c x23: x23 x24: x24
STACK CFI 3714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 3718 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 37a4 x21: x21 x22: x22
STACK CFI 37a8 x23: x23 x24: x24
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 37c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 37d8 x21: x21 x22: x22
STACK CFI 37dc x23: x23 x24: x24
STACK CFI 37e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 37f0 314 .cfa: sp 0 + .ra: x30
STACK CFI 37f4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 37f8 .cfa: x29 240 +
STACK CFI 37fc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 3808 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 381c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 3880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3884 .cfa: x29 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 3b04 4d4 .cfa: sp 0 + .ra: x30
STACK CFI 3b08 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3b10 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3b20 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3b2c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3b3c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3b40 x27: .cfa -32 + ^
STACK CFI 3ed0 x25: x25 x26: x26
STACK CFI 3ed4 x27: x27
STACK CFI 3ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3edc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 3f98 x25: x25 x26: x26
STACK CFI 3f9c x27: x27
STACK CFI 3fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3fb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3fe0 3dc .cfa: sp 0 + .ra: x30
STACK CFI 3fe8 .cfa: sp 4288 +
STACK CFI 3fec .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI 3ff4 x21: .cfa -4256 + ^ x22: .cfa -4248 + ^
STACK CFI 4000 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI 4010 x23: .cfa -4240 + ^ x24: .cfa -4232 + ^
STACK CFI 4044 x25: .cfa -4224 + ^ x26: .cfa -4216 + ^
STACK CFI 4158 x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI 41dc x27: x27 x28: x28
STACK CFI 41e4 x25: x25 x26: x26
STACK CFI 41fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4200 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x23: .cfa -4240 + ^ x24: .cfa -4232 + ^ x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x27: .cfa -4208 + ^ x28: .cfa -4200 + ^ x29: .cfa -4288 + ^
STACK CFI 4224 x27: x27 x28: x28
STACK CFI 4358 x25: x25 x26: x26
STACK CFI 4370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4374 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x23: .cfa -4240 + ^ x24: .cfa -4232 + ^ x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x29: .cfa -4288 + ^
STACK CFI 4380 x25: x25 x26: x26
STACK CFI 4388 x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI 4390 x27: x27 x28: x28
STACK CFI 43a8 x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI INIT 43c0 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 43c4 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 43cc x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 43d8 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 43ec x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 440c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 4418 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 46ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 46f0 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT 4790 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 4798 .cfa: sp 4368 +
STACK CFI 47a0 .ra: .cfa -4344 + ^ x29: .cfa -4352 + ^
STACK CFI 47a8 x19: .cfa -4336 + ^ x20: .cfa -4328 + ^
STACK CFI 47b4 x23: .cfa -4304 + ^ x24: .cfa -4296 + ^
STACK CFI 47c0 x25: .cfa -4288 + ^ x26: .cfa -4280 + ^
STACK CFI 47d4 x21: .cfa -4320 + ^ x22: .cfa -4312 + ^
STACK CFI 47e8 x27: .cfa -4272 + ^ x28: .cfa -4264 + ^
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4958 .cfa: sp 4368 + .ra: .cfa -4344 + ^ x19: .cfa -4336 + ^ x20: .cfa -4328 + ^ x21: .cfa -4320 + ^ x22: .cfa -4312 + ^ x23: .cfa -4304 + ^ x24: .cfa -4296 + ^ x25: .cfa -4288 + ^ x26: .cfa -4280 + ^ x27: .cfa -4272 + ^ x28: .cfa -4264 + ^ x29: .cfa -4352 + ^
STACK CFI INIT 4a74 44 .cfa: sp 0 + .ra: x30
STACK CFI 4a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4ac0 28 .cfa: sp 0 + .ra: x30
STACK CFI 4acc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4af0 13c .cfa: sp 0 + .ra: x30
STACK CFI 4af8 .cfa: sp 4288 +
STACK CFI 4afc .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI 4b04 x21: .cfa -4256 + ^ x22: .cfa -4248 + ^
STACK CFI 4b10 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c14 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x29: .cfa -4288 + ^
STACK CFI INIT 4c30 74 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4ca4 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 4ca8 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4cb0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 4cc4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 4d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d44 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 4d48 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4ec4 x23: x23 x24: x24
STACK CFI 4ec8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 4f50 98c .cfa: sp 0 + .ra: x30
STACK CFI 4f54 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 4f5c x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 4f68 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 4f78 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 4f84 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 4f8c x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 5048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 504c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 5168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 516c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 58c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 58c8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 58e0 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 58e4 .cfa: sp 2128 +
STACK CFI 58e8 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 58f0 x25: .cfa -2064 + ^ x26: .cfa -2056 + ^
STACK CFI 58fc x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 590c x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x27: .cfa -2048 + ^ x28: .cfa -2040 + ^
STACK CFI 5b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5b74 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x25: .cfa -2064 + ^ x26: .cfa -2056 + ^ x27: .cfa -2048 + ^ x28: .cfa -2040 + ^ x29: .cfa -2128 + ^
STACK CFI INIT 5ba0 230 .cfa: sp 0 + .ra: x30
STACK CFI 5ba4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5bb4 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 5bc0 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 5dd0 11c .cfa: sp 0 + .ra: x30
STACK CFI 5dd4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 5de4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 5df4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 5e0c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 5e18 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 5eac x19: x19 x20: x20
STACK CFI 5eb0 x23: x23 x24: x24
STACK CFI 5ec0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 5ec4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x29: .cfa -160 + ^
STACK CFI 5edc x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI INIT 5ef0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5ef4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5fb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5fb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5fd0 9c .cfa: sp 0 + .ra: x30
STACK CFI 5fd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5fe0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6034 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 604c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6070 90 .cfa: sp 0 + .ra: x30
STACK CFI 6074 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6080 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 608c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 60d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 60dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6100 44 .cfa: sp 0 + .ra: x30
STACK CFI 6104 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6124 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6138 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6144 8c .cfa: sp 0 + .ra: x30
STACK CFI 6148 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6158 x19: .cfa -16 + ^
STACK CFI 61c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 61c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 61d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 61d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 61f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 61f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61fc x19: .cfa -16 + ^
STACK CFI 6218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6220 88 .cfa: sp 0 + .ra: x30
STACK CFI 6224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6234 x19: .cfa -16 + ^
STACK CFI 6290 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 629c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 62a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 62b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 62b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62c4 x19: .cfa -16 + ^
STACK CFI 62e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 62e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 62f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6300 10 .cfa: sp 0 + .ra: x30
STACK CFI 6304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 630c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6310 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6314 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6354 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 63d0 afc .cfa: sp 0 + .ra: x30
STACK CFI 63d4 .cfa: sp 624 +
STACK CFI 63d8 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 63e0 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 63ec x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 6400 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 6408 x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 6434 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 6480 x23: x23 x24: x24
STACK CFI 649c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 64a0 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 6588 x23: x23 x24: x24
STACK CFI 6590 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 65e8 x23: x23 x24: x24
STACK CFI 6610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6614 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI 6cdc x23: x23 x24: x24
STACK CFI 6ce8 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 6d74 x23: x23 x24: x24
STACK CFI 6d84 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 6ec8 x23: x23 x24: x24
STACK CFI INIT 6ed0 508 .cfa: sp 0 + .ra: x30
STACK CFI 6ed4 .cfa: sp 944 +
STACK CFI 6ed8 .ra: .cfa -936 + ^ x29: .cfa -944 + ^
STACK CFI 6ee0 x19: .cfa -928 + ^ x20: .cfa -920 + ^
STACK CFI 6f10 x23: .cfa -896 + ^ x24: .cfa -888 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^
STACK CFI 6f54 x21: .cfa -912 + ^ x22: .cfa -904 + ^
STACK CFI 6f58 x25: .cfa -880 + ^ x26: .cfa -872 + ^
STACK CFI 70a4 x21: x21 x22: x22
STACK CFI 70a8 x25: x25 x26: x26
STACK CFI 70bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 70c0 .cfa: sp 944 + .ra: .cfa -936 + ^ x19: .cfa -928 + ^ x20: .cfa -920 + ^ x21: .cfa -912 + ^ x22: .cfa -904 + ^ x23: .cfa -896 + ^ x24: .cfa -888 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^ x29: .cfa -944 + ^
STACK CFI 716c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 71bc x21: .cfa -912 + ^ x22: .cfa -904 + ^
STACK CFI 71c0 x25: .cfa -880 + ^ x26: .cfa -872 + ^
STACK CFI 71f0 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 7214 x21: .cfa -912 + ^ x22: .cfa -904 + ^
STACK CFI 7218 x25: .cfa -880 + ^ x26: .cfa -872 + ^
STACK CFI 73a0 x21: x21 x22: x22
STACK CFI 73a4 x25: x25 x26: x26
STACK CFI 73a8 x21: .cfa -912 + ^ x22: .cfa -904 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^
STACK CFI 73cc x21: x21 x22: x22
STACK CFI 73d4 x25: x25 x26: x26
STACK CFI INIT 73e0 94 .cfa: sp 0 + .ra: x30
STACK CFI 73e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 742c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7430 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7474 30 .cfa: sp 0 + .ra: x30
STACK CFI 747c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7498 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 749c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 74a4 2c .cfa: sp 0 + .ra: x30
STACK CFI 74ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 74c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 74c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 74d0 68 .cfa: sp 0 + .ra: x30
STACK CFI 74d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 752c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7530 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7540 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7544 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 754c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7560 x21: .cfa -16 + ^
STACK CFI 75cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 75d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 75e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 75ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 75f4 10 .cfa: sp 0 + .ra: x30
STACK CFI 75f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7604 14 .cfa: sp 0 + .ra: x30
STACK CFI 7608 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7614 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7620 48 .cfa: sp 0 + .ra: x30
STACK CFI 7624 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 763c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7640 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 765c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7670 5c .cfa: sp 0 + .ra: x30
STACK CFI 7674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 767c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 76c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 76d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 76d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 772c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7778 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 778c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7790 14 .cfa: sp 0 + .ra: x30
STACK CFI 7794 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 77a4 78 .cfa: sp 0 + .ra: x30
STACK CFI 77a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7814 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7820 154 .cfa: sp 0 + .ra: x30
STACK CFI 7824 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 782c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 783c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 784c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7854 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7918 x19: x19 x20: x20
STACK CFI 791c x21: x21 x22: x22
STACK CFI 7920 x23: x23 x24: x24
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 7934 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 7964 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7970 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI INIT 7974 1cc .cfa: sp 0 + .ra: x30
STACK CFI 7978 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7980 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7998 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 79a4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 79ac x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7adc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 7b40 14c .cfa: sp 0 + .ra: x30
STACK CFI 7b44 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 7b5c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 7b68 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 7b80 x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^
STACK CFI 7c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7c48 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x29: .cfa -416 + ^
STACK CFI 7c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7c84 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x29: .cfa -416 + ^
STACK CFI INIT 7c90 110 .cfa: sp 0 + .ra: x30
STACK CFI 7c9c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ca4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7cbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7cc8 x23: .cfa -16 + ^
STACK CFI 7cfc x19: x19 x20: x20
STACK CFI 7d00 x23: x23
STACK CFI 7d0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7d10 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7d40 x23: x23
STACK CFI 7d50 x19: x19 x20: x20
STACK CFI 7d5c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7d60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7d74 x23: x23
STACK CFI 7d8c x19: x19 x20: x20
STACK CFI 7d90 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 7d94 x19: x19 x20: x20
STACK CFI 7d9c x23: x23
STACK CFI INIT 7da0 98 .cfa: sp 0 + .ra: x30
STACK CFI 7da4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7db4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7dc0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7e30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e40 160 .cfa: sp 0 + .ra: x30
STACK CFI 7e44 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 7e4c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 7e58 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 7e6c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 7e74 x25: .cfa -80 + ^
STACK CFI 7f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7f14 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 7f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7f5c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7fa0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7fa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7fb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7fc8 x21: .cfa -16 + ^
STACK CFI 8020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8024 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8050 140 .cfa: sp 0 + .ra: x30
STACK CFI 8054 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 805c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 8068 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 8070 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 810c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8110 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI 8118 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 8188 x25: x25 x26: x26
STACK CFI INIT 8190 594 .cfa: sp 0 + .ra: x30
STACK CFI 8194 .cfa: sp 2192 +
STACK CFI 8198 .ra: .cfa -2184 + ^ x29: .cfa -2192 + ^
STACK CFI 81a0 x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI 81a8 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI 81b4 x23: .cfa -2144 + ^ x24: .cfa -2136 + ^
STACK CFI 82e4 x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI 8300 x25: x25 x26: x26
STACK CFI 83c0 x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI 83c4 x25: x25 x26: x26
STACK CFI 84b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84bc .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x29: .cfa -2192 + ^
STACK CFI 84f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84f8 .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x29: .cfa -2192 + ^
STACK CFI 8634 x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI 863c x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 86b8 x27: x27 x28: x28
STACK CFI 86c0 x25: x25 x26: x26
STACK CFI INIT 8724 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 8728 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8730 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 873c x21: .cfa -32 + ^
STACK CFI 8784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8788 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 8900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8904 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8a20 65c .cfa: sp 0 + .ra: x30
STACK CFI 8a28 .cfa: sp 5168 +
STACK CFI 8a2c .ra: .cfa -5160 + ^ x29: .cfa -5168 + ^
STACK CFI 8a34 x21: .cfa -5136 + ^ x22: .cfa -5128 + ^
STACK CFI 8a44 x19: .cfa -5152 + ^ x20: .cfa -5144 + ^
STACK CFI 8a4c x23: .cfa -5120 + ^ x24: .cfa -5112 + ^
STACK CFI 8a88 x25: .cfa -5104 + ^ x26: .cfa -5096 + ^
STACK CFI 8a9c x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI 8b90 x25: x25 x26: x26
STACK CFI 8b94 x27: x27 x28: x28
STACK CFI 8c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c60 .cfa: sp 5168 + .ra: .cfa -5160 + ^ x19: .cfa -5152 + ^ x20: .cfa -5144 + ^ x21: .cfa -5136 + ^ x22: .cfa -5128 + ^ x23: .cfa -5120 + ^ x24: .cfa -5112 + ^ x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^ x29: .cfa -5168 + ^
STACK CFI 8c74 x25: x25 x26: x26
STACK CFI 8c7c x27: x27 x28: x28
STACK CFI 8c84 x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI 8d5c x25: x25 x26: x26
STACK CFI 8d60 x27: x27 x28: x28
STACK CFI 8d64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8d68 .cfa: sp 5168 + .ra: .cfa -5160 + ^ x19: .cfa -5152 + ^ x20: .cfa -5144 + ^ x21: .cfa -5136 + ^ x22: .cfa -5128 + ^ x23: .cfa -5120 + ^ x24: .cfa -5112 + ^ x29: .cfa -5168 + ^
STACK CFI 8d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8d8c .cfa: sp 5168 + .ra: .cfa -5160 + ^ x19: .cfa -5152 + ^ x20: .cfa -5144 + ^ x21: .cfa -5136 + ^ x22: .cfa -5128 + ^ x23: .cfa -5120 + ^ x24: .cfa -5112 + ^ x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^ x29: .cfa -5168 + ^
STACK CFI 8eb0 x25: x25 x26: x26
STACK CFI 8eb8 x27: x27 x28: x28
STACK CFI 8ec0 x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI 903c x25: x25 x26: x26
STACK CFI 9044 x27: x27 x28: x28
STACK CFI 904c x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI 9050 x27: x27 x28: x28
STACK CFI 9058 x25: x25 x26: x26
STACK CFI 905c x25: .cfa -5104 + ^ x26: .cfa -5096 + ^ x27: .cfa -5088 + ^ x28: .cfa -5080 + ^
STACK CFI INIT 9080 1ac .cfa: sp 0 + .ra: x30
STACK CFI 9084 .cfa: sp 2032 +
STACK CFI 9088 .ra: .cfa -2024 + ^ x29: .cfa -2032 + ^
STACK CFI 9094 x19: .cfa -2016 + ^ x20: .cfa -2008 + ^
STACK CFI 90b0 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 90cc x23: .cfa -1984 + ^ x24: .cfa -1976 + ^
STACK CFI 90d8 x25: .cfa -1968 + ^
STACK CFI 9144 x23: x23 x24: x24
STACK CFI 9148 x25: x25
STACK CFI 9150 x21: x21 x22: x22
STACK CFI 917c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9180 .cfa: sp 2032 + .ra: .cfa -2024 + ^ x19: .cfa -2016 + ^ x20: .cfa -2008 + ^ x29: .cfa -2032 + ^
STACK CFI 91a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 91ac .cfa: sp 2032 + .ra: .cfa -2024 + ^ x19: .cfa -2016 + ^ x20: .cfa -2008 + ^ x29: .cfa -2032 + ^
STACK CFI 91e0 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 91f0 x21: x21 x22: x22
STACK CFI 9204 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 920c x21: x21 x22: x22
STACK CFI 9218 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI 9228 x21: x21 x22: x22
STACK CFI INIT 9230 40 .cfa: sp 0 + .ra: x30
STACK CFI 9234 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 923c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 926c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9270 19c .cfa: sp 0 + .ra: x30
STACK CFI 927c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9284 x23: .cfa -16 + ^
STACK CFI 9290 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 92a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 930c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9410 42c .cfa: sp 0 + .ra: x30
STACK CFI 9414 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 941c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 9424 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 943c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 9440 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 9474 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 960c x27: x27 x28: x28
STACK CFI 9614 x21: x21 x22: x22
STACK CFI 9618 x23: x23 x24: x24
STACK CFI 9624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 9628 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 9694 x21: x21 x22: x22
STACK CFI 9698 x23: x23 x24: x24
STACK CFI 96a0 x27: x27 x28: x28
STACK CFI 96a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 96a8 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 973c x21: x21 x22: x22
STACK CFI 9740 x23: x23 x24: x24
STACK CFI 9748 x27: x27 x28: x28
STACK CFI 974c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 9750 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 97f0 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 97f8 x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 9840 298 .cfa: sp 0 + .ra: x30
STACK CFI 9844 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 984c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 9880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9884 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI 9894 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 9898 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 989c x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 98ac x21: x21 x22: x22
STACK CFI 98b0 x23: x23 x24: x24
STACK CFI 98b4 x25: x25 x26: x26
STACK CFI 98b8 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 9a6c x25: x25 x26: x26
STACK CFI 9a7c x21: x21 x22: x22
STACK CFI 9a84 x23: x23 x24: x24
STACK CFI 9a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9a98 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI 9abc x21: x21 x22: x22
STACK CFI 9ac0 x23: x23 x24: x24
STACK CFI 9ac4 x25: x25 x26: x26
STACK CFI 9ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9acc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x29: .cfa -176 + ^
STACK CFI INIT 9ad8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9af0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fb0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1fb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fcc .cfa: sp 0 + .ra: .ra x29: x29
