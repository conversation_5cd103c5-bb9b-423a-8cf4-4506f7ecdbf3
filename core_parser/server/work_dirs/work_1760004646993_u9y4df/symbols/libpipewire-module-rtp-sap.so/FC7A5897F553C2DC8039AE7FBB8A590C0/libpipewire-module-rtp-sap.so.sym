MODULE Linux arm64 FC7A5897F553C2DC8039AE7FBB8A590C0 libpipewire-module-rtp-sap.so
INFO CODE_ID 97587AFC53F5DCC28039AE7FBB8A590C64322EF9
PUBLIC 4d60 0 pipewire__module_init
STACK CFI INIT 21b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2220 48 .cfa: sp 0 + .ra: x30
STACK CFI 2224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 222c x19: .cfa -16 + ^
STACK CFI 2264 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2270 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2280 108 .cfa: sp 0 + .ra: x30
STACK CFI 2288 .cfa: sp 320 +
STACK CFI 2298 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 22a8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 237c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2384 .cfa: sp 320 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2390 58 .cfa: sp 0 + .ra: x30
STACK CFI 2398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23a0 x19: .cfa -16 + ^
STACK CFI 23e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23f0 158 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 96 +
STACK CFI 23fc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2404 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2414 x21: .cfa -16 + ^
STACK CFI 2460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2468 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2550 9c .cfa: sp 0 + .ra: x30
STACK CFI 2558 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2568 x19: .cfa -16 + ^
STACK CFI 258c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2594 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 25e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25f0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2600 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2608 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2610 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2664 x23: .cfa -16 + ^
STACK CFI 26a8 x23: x23
STACK CFI 26b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 26c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 26d4 54 .cfa: sp 0 + .ra: x30
STACK CFI 26dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26e4 x19: .cfa -16 + ^
STACK CFI 2720 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2730 104 .cfa: sp 0 + .ra: x30
STACK CFI 2738 .cfa: sp 96 +
STACK CFI 273c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2744 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2750 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 278c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2794 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 282c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2834 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 283c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2848 .cfa: sp 2496 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 287c x23: .cfa -48 + ^
STACK CFI 2884 x24: .cfa -40 + ^
STACK CFI 28cc x25: .cfa -32 + ^
STACK CFI 28d0 x26: .cfa -24 + ^
STACK CFI 2930 x21: .cfa -64 + ^
STACK CFI 2934 x22: .cfa -56 + ^
STACK CFI 2938 x27: .cfa -16 + ^
STACK CFI 2ad0 x21: x21
STACK CFI 2ad4 x22: x22
STACK CFI 2ad8 x23: x23
STACK CFI 2adc x24: x24
STACK CFI 2ae0 x25: x25
STACK CFI 2ae4 x26: x26
STACK CFI 2ae8 x27: x27
STACK CFI 2b08 .cfa: sp 96 +
STACK CFI 2b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b18 .cfa: sp 2496 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2b54 x21: .cfa -64 + ^
STACK CFI 2b58 x22: .cfa -56 + ^
STACK CFI 2b5c x27: .cfa -16 + ^
STACK CFI 2bb4 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 2bc8 x25: .cfa -32 + ^
STACK CFI 2bcc x26: .cfa -24 + ^
STACK CFI 2bd4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 2ca4 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 2ca8 x23: x23
STACK CFI 2cac x24: x24
STACK CFI 2cb0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2cb4 x23: x23
STACK CFI 2cb8 x24: x24
STACK CFI 2cbc x25: x25
STACK CFI 2cc0 x26: x26
STACK CFI 2cc8 x21: .cfa -64 + ^
STACK CFI 2ccc x22: .cfa -56 + ^
STACK CFI 2cd0 x23: .cfa -48 + ^
STACK CFI 2cd4 x24: .cfa -40 + ^
STACK CFI 2cd8 x25: .cfa -32 + ^
STACK CFI 2cdc x26: .cfa -24 + ^
STACK CFI 2ce0 x27: .cfa -16 + ^
STACK CFI INIT 2ce4 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2cec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cf4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2db0 54 .cfa: sp 0 + .ra: x30
STACK CFI 2db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2dc0 x19: .cfa -16 + ^
STACK CFI 2dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e04 fc .cfa: sp 0 + .ra: x30
STACK CFI 2e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e1c x19: .cfa -16 + ^
STACK CFI 2eb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2eb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f00 110 .cfa: sp 0 + .ra: x30
STACK CFI 2f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3008 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3010 50 .cfa: sp 0 + .ra: x30
STACK CFI 3018 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3020 x19: .cfa -16 + ^
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3060 f50 .cfa: sp 0 + .ra: x30
STACK CFI 3068 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3078 .cfa: sp 3040 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 30d8 .cfa: sp 96 +
STACK CFI 30e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30ec .cfa: sp 3040 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 30f0 x25: .cfa -32 + ^
STACK CFI 30fc x26: .cfa -24 + ^
STACK CFI 3150 x23: .cfa -48 + ^
STACK CFI 3154 x24: .cfa -40 + ^
STACK CFI 3158 x27: .cfa -16 + ^
STACK CFI 315c x28: .cfa -8 + ^
STACK CFI 3314 x23: x23
STACK CFI 3318 x24: x24
STACK CFI 331c x25: x25
STACK CFI 3320 x26: x26
STACK CFI 3324 x27: x27
STACK CFI 3328 x28: x28
STACK CFI 332c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3450 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3454 x25: .cfa -32 + ^
STACK CFI 3460 x26: .cfa -24 + ^
STACK CFI 347c x25: x25
STACK CFI 3480 x26: x26
STACK CFI 3484 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 349c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 35c0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 361c x25: x25
STACK CFI 3620 x26: x26
STACK CFI 3624 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 36e8 x23: x23
STACK CFI 36ec x24: x24
STACK CFI 36f0 x27: x27
STACK CFI 36f4 x28: x28
STACK CFI 3708 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3794 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 37c4 x25: x25
STACK CFI 37c8 x26: x26
STACK CFI 37cc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 37e8 x23: x23
STACK CFI 37f0 x24: x24
STACK CFI 37f4 x27: x27
STACK CFI 37f8 x28: x28
STACK CFI 37fc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3928 x23: x23
STACK CFI 3930 x24: x24
STACK CFI 3934 x27: x27
STACK CFI 3938 x28: x28
STACK CFI 393c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 39b4 x23: x23
STACK CFI 39bc x24: x24
STACK CFI 39c0 x27: x27
STACK CFI 39c4 x28: x28
STACK CFI 39c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3a08 x23: x23
STACK CFI 3a0c x24: x24
STACK CFI 3a10 x27: x27
STACK CFI 3a14 x28: x28
STACK CFI 3a18 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3af0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 3af8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3b38 x23: x23
STACK CFI 3b40 x24: x24
STACK CFI 3b44 x27: x27
STACK CFI 3b48 x28: x28
STACK CFI 3b4c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3e64 x23: x23
STACK CFI 3e68 x24: x24
STACK CFI 3e6c x27: x27
STACK CFI 3e70 x28: x28
STACK CFI 3e7c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3f94 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3f98 x23: .cfa -48 + ^
STACK CFI 3f9c x24: .cfa -40 + ^
STACK CFI 3fa0 x25: .cfa -32 + ^
STACK CFI 3fa4 x26: .cfa -24 + ^
STACK CFI 3fa8 x27: .cfa -16 + ^
STACK CFI 3fac x28: .cfa -8 + ^
STACK CFI INIT 3fb0 28 .cfa: sp 0 + .ra: x30
STACK CFI 3fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3fcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fe0 17c .cfa: sp 0 + .ra: x30
STACK CFI 3fe8 .cfa: sp 128 +
STACK CFI 3ff8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4004 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4020 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4050 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 405c x27: .cfa -16 + ^
STACK CFI 40d4 x25: x25 x26: x26
STACK CFI 40d8 x27: x27
STACK CFI 4108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4110 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 4150 x25: x25 x26: x26 x27: x27
STACK CFI 4154 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4158 x27: .cfa -16 + ^
STACK CFI INIT 4160 c00 .cfa: sp 0 + .ra: x30
STACK CFI 4168 .cfa: sp 160 +
STACK CFI 4170 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4178 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 418c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 41bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 422c .cfa: sp 160 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 43f4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 44a4 x25: x25 x26: x26
STACK CFI 44e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4544 x25: x25 x26: x26
STACK CFI 4548 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 46a0 x25: x25 x26: x26
STACK CFI 46b4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 46d0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 47d8 v8: .cfa -16 + ^
STACK CFI 4824 v8: v8
STACK CFI 4978 x25: x25 x26: x26
STACK CFI 497c x27: x27 x28: x28
STACK CFI 4980 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4984 x27: x27 x28: x28
STACK CFI 49a0 x25: x25 x26: x26
STACK CFI 49e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4a00 x27: x27 x28: x28
STACK CFI 4a20 x25: x25 x26: x26
STACK CFI 4a24 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4a44 x25: x25 x26: x26
STACK CFI 4aa0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4ab8 x25: x25 x26: x26
STACK CFI 4aec v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4af0 v8: v8
STACK CFI 4b40 x27: x27 x28: x28
STACK CFI 4b44 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4b94 x27: x27 x28: x28
STACK CFI 4bf8 x25: x25 x26: x26
STACK CFI 4c2c x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4c60 x27: x27 x28: x28
STACK CFI 4c94 x25: x25 x26: x26
STACK CFI 4c98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4cc8 x25: x25 x26: x26
STACK CFI 4ccc v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4ce8 v8: v8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4cec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4cf0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4cf4 v8: .cfa -16 + ^
STACK CFI 4cf8 v8: v8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 4d60 1134 .cfa: sp 0 + .ra: x30
STACK CFI 4d68 .cfa: sp 320 +
STACK CFI 4d74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4d7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d84 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d94 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 51fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5204 .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
