MODULE Linux arm64 863C20D6D076F45927770D9143EB1DFB0 libzvbi-chains.so.0
INFO CODE_ID D6203C8676D059F427770D9143EB1DFB7B977D3C
PUBLIC 30d0 0 fcntl
PUBLIC 3290 0 write
PUBLIC 3374 0 vbi_capture_read_raw
PUBLIC 3470 0 vbi_capture_read_sliced
PUBLIC 35b0 0 vbi_capture_read
PUBLIC 3700 0 vbi_capture_pull_raw
PUBLIC 37a4 0 vbi_capture_pull_sliced
PUBLIC 3850 0 vbi_capture_pull
PUBLIC 38d0 0 vbi_capture_parameters
PUBLIC 3914 0 vbi_capture_update_services
PUBLIC 3960 0 vbi_capture_fd
PUBLIC 39a0 0 vbi_capture_set_log_fp
PUBLIC 39e0 0 vbi_capture_get_scanning
PUBLIC 3a20 0 vbi_capture_flush
PUBLIC 3a70 0 vbi_capture_set_video_path
PUBLIC 3ac0 0 vbi_capture_get_fd_flags
PUBLIC 3b10 0 vbi_capture_delete
PUBLIC 3b40 0 vbi_capture_io_update_timeout
PUBLIC 3f24 0 vbi_capture_io_select
PUBLIC 4040 0 fprint_symbolic
PUBLIC 4310 0 fprint_unknown_ioctl
PUBLIC 4360 0 device_mmap
PUBLIC 4510 0 device_munmap
PUBLIC 45d0 0 _vbi_popcnt
PUBLIC 4600 0 _vbi_strlcpy
PUBLIC 4720 0 _vbi_strndup
PUBLIC 4790 0 _vbi_vasprintf
PUBLIC 48c0 0 _vbi_asprintf
PUBLIC 4ab4 0 _vbi_keyword_lookup
PUBLIC 4c50 0 _vbi_shrink_vector_capacity
PUBLIC 4cc0 0 _vbi_grow_vector_capacity
PUBLIC 4e10 0 vbi_log_on_stderr
PUBLIC 4ed0 0 _vbi_log_vprintf
PUBLIC 5014 0 _vbi_log_printf
PUBLIC 50c0 0 vbi_proxy_client_channel_suspend
PUBLIC 50e0 0 vbi_proxy_client_get_channel_desc
PUBLIC 5120 0 vbi_proxy_client_has_channel_control
PUBLIC 5150 0 vbi_proxy_client_get_driver_api
PUBLIC 5180 0 vbi_proxy_client_set_callback
PUBLIC 51b4 0 vbi_proxy_client_get_capture_if
PUBLIC 51e0 0 read
PUBLIC 5440 0 vbi_proxy_msg_set_logging
PUBLIC 5540 0 vbi_proxy_msg_set_debug_level
PUBLIC 5560 0 vbi_proxy_msg_debug_get_type_str
PUBLIC 5b60 0 vbi_proxy_msg_read_idle
PUBLIC 5bb4 0 vbi_proxy_msg_write_idle
PUBLIC 5be0 0 vbi_proxy_msg_is_idle
PUBLIC 5c40 0 vbi_proxy_msg_close_read
PUBLIC 5c90 0 vbi_proxy_msg_check_timeout
PUBLIC 5cd0 0 vbi_proxy_msg_handle_write
PUBLIC 5e50 0 vbi_proxy_msg_handle_read
PUBLIC 6520 0 vbi_proxy_msg_close_io
PUBLIC 68f0 0 vbi_proxy_client_destroy
PUBLIC 6970 0 close
PUBLIC 6a60 0 device_close
PUBLIC 6b80 0 vbi_proxy_msg_fill_magics
PUBLIC 6bc0 0 vbi_proxy_msg_write
PUBLIC 6d10 0 vbi_proxy_client_channel_request
PUBLIC 6e90 0 vbi_proxy_client_channel_notify
PUBLIC 7210 0 vbi_proxy_msg_stop_listen
PUBLIC 7270 0 vbi_proxy_msg_get_socket_name
PUBLIC 75a0 0 vbi_proxy_client_create
PUBLIC 76e0 0 vbi_proxy_msg_check_connect
PUBLIC 77f4 0 vbi_proxy_msg_connect_to_server
PUBLIC 7c00 0 vbi_proxy_msg_finish_connect
PUBLIC 7db4 0 vbi_capture_proxy_new
PUBLIC 8294 0 open
PUBLIC 8620 0 device_open
PUBLIC 87b0 0 vbi_proxy_msg_logger
PUBLIC 8c24 0 vbi_proxy_msg_listen_socket
PUBLIC 8fe0 0 vbi_proxy_msg_accept_connection
PUBLIC 92e0 0 vbi_proxy_msg_check_ioctl
PUBLIC 98e0 0 vbi_proxy_client_device_ioctl
PUBLIC 9b84 0 ioctl
PUBLIC a050 0 device_ioctl
STACK CFI INIT 2ad0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b00 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b40 48 .cfa: sp 0 + .ra: x30
STACK CFI 2b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b4c x19: .cfa -16 + ^
STACK CFI 2b84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ba0 24 .cfa: sp 0 + .ra: x30
STACK CFI 2ba8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2bc4 28 .cfa: sp 0 + .ra: x30
STACK CFI 2bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2be4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2bf0 1c .cfa: sp 0 + .ra: x30
STACK CFI 2bf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c10 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 2c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c3c x19: .cfa -16 + ^
STACK CFI 2cac x19: x19
STACK CFI 2cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2cbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d08 x19: .cfa -16 + ^
STACK CFI 2d44 x19: x19
STACK CFI 2d64 x19: .cfa -16 + ^
STACK CFI 2de4 x19: x19
STACK CFI 2e3c x19: .cfa -16 + ^
STACK CFI 2e40 x19: x19
STACK CFI INIT 2ec0 208 .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 48 +
STACK CFI 2ed8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3048 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30d0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 30d8 .cfa: sp 128 +
STACK CFI 30e8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 30f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3104 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3110 x23: .cfa -32 + ^
STACK CFI 3218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3220 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3290 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3298 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 32fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3304 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3330 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3374 f8 .cfa: sp 0 + .ra: x30
STACK CFI 337c .cfa: sp 80 +
STACK CFI 338c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3394 x19: .cfa -16 + ^
STACK CFI 3400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3408 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3470 140 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 80 +
STACK CFI 3488 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3490 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 351c .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 35b0 148 .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 112 +
STACK CFI 35c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 365c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3664 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3700 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3708 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3738 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 37a4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 37e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3850 80 .cfa: sp 0 + .ra: x30
STACK CFI 3858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3878 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 38d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 38ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3914 44 .cfa: sp 0 + .ra: x30
STACK CFI 3930 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3960 38 .cfa: sp 0 + .ra: x30
STACK CFI 3968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 397c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 398c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 39a0 40 .cfa: sp 0 + .ra: x30
STACK CFI 39b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 39e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 39e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3a20 4c .cfa: sp 0 + .ra: x30
STACK CFI 3a44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3a70 50 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3ac0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3b10 30 .cfa: sp 0 + .ra: x30
STACK CFI 3b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b40 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3b48 .cfa: sp 80 +
STACK CFI 3b54 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3b64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3bf4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3c34 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 3c3c .cfa: sp 400 +
STACK CFI 3c40 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3c48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3c5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3c7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3c88 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3c94 x27: .cfa -16 + ^
STACK CFI 3e1c x19: x19 x20: x20
STACK CFI 3e20 x25: x25 x26: x26
STACK CFI 3e24 x27: x27
STACK CFI 3e28 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 3e34 x19: x19 x20: x20
STACK CFI 3e38 x25: x25 x26: x26
STACK CFI 3e3c x27: x27
STACK CFI 3e6c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e74 .cfa: sp 400 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 3e9c x19: x19 x20: x20
STACK CFI 3ea0 x25: x25 x26: x26
STACK CFI 3ea4 x27: x27
STACK CFI 3ea8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 3edc x19: x19 x20: x20
STACK CFI 3ee0 x25: x25 x26: x26
STACK CFI 3ee4 x27: x27
STACK CFI 3f18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3f1c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3f20 x27: .cfa -16 + ^
STACK CFI INIT 3f24 118 .cfa: sp 0 + .ra: x30
STACK CFI 3f2c .cfa: sp 256 +
STACK CFI 3f3c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f44 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f4c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f58 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3f64 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4038 .cfa: sp 256 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4040 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 4048 .cfa: sp 176 +
STACK CFI 4054 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4060 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4068 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4070 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 41c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 41cc .cfa: sp 176 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4310 4c .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4324 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4360 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 4368 .cfa: sp 128 +
STACK CFI 436c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4374 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4380 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 438c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 43ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 43c4 x27: .cfa -16 + ^
STACK CFI 44c4 x27: x27
STACK CFI 44e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 44e8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4510 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4518 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4520 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4530 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4548 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4584 x23: x23 x24: x24
STACK CFI 4594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 459c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 45d0 2c .cfa: sp 0 + .ra: x30
STACK CFI 45e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4600 7c .cfa: sp 0 + .ra: x30
STACK CFI 4608 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 464c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4660 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 466c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4680 a0 .cfa: sp 0 + .ra: x30
STACK CFI 468c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4694 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 46a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 46b0 x23: .cfa -16 + ^
STACK CFI 4710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4720 68 .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4730 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4738 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4790 130 .cfa: sp 0 + .ra: x30
STACK CFI 4798 .cfa: sp 224 +
STACK CFI 47a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 47b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 47bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 47c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 47d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 47d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 48bc .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 48c0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 48c8 .cfa: sp 272 +
STACK CFI 48d8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4968 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4970 .cfa: sp 272 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4974 140 .cfa: sp 0 + .ra: x30
STACK CFI 497c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4984 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4990 x21: .cfa -16 + ^
STACK CFI 49e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 49f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ab4 19c .cfa: sp 0 + .ra: x30
STACK CFI 4abc .cfa: sp 96 +
STACK CFI 4ac0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ac8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4ae8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4aec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4b24 x25: .cfa -16 + ^
STACK CFI 4b74 x25: x25
STACK CFI 4b9c x19: x19 x20: x20
STACK CFI 4ba4 x23: x23 x24: x24
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4bb0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4c08 x25: .cfa -16 + ^
STACK CFI 4c10 x25: x25
STACK CFI 4c24 x25: .cfa -16 + ^
STACK CFI 4c28 x25: x25
STACK CFI 4c4c x25: .cfa -16 + ^
STACK CFI INIT 4c50 70 .cfa: sp 0 + .ra: x30
STACK CFI 4c58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c60 x21: .cfa -16 + ^
STACK CFI 4c78 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4c80 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4c88 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ca8 x19: x19 x20: x20
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4cb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4cbc x19: x19 x20: x20
STACK CFI INIT 4cc0 150 .cfa: sp 0 + .ra: x30
STACK CFI 4cc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4cd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4cd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d04 x23: .cfa -16 + ^
STACK CFI 4d48 x19: x19 x20: x20
STACK CFI 4d4c x21: x21 x22: x22
STACK CFI 4d50 x23: x23
STACK CFI 4d54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4d5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4d78 x23: x23
STACK CFI 4d88 x19: x19 x20: x20
STACK CFI 4d94 x21: x21 x22: x22
STACK CFI 4d98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4db8 x23: x23
STACK CFI 4ddc x23: .cfa -16 + ^
STACK CFI 4de0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 4e04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e0c x23: .cfa -16 + ^
STACK CFI INIT 4e10 bc .cfa: sp 0 + .ra: x30
STACK CFI 4e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4ed0 144 .cfa: sp 0 + .ra: x30
STACK CFI 4ed8 .cfa: sp 304 +
STACK CFI 4ee4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4eec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4ef8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4f04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4f1c x27: .cfa -16 + ^
STACK CFI 4ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5004 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5014 ac .cfa: sp 0 + .ra: x30
STACK CFI 501c .cfa: sp 240 +
STACK CFI 502c .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 50b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 50bc .cfa: sp 240 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI INIT 50c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 50c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50e0 40 .cfa: sp 0 + .ra: x30
STACK CFI 50e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5110 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5120 28 .cfa: sp 0 + .ra: x30
STACK CFI 5128 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5134 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5140 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5150 30 .cfa: sp 0 + .ra: x30
STACK CFI 5158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5170 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5174 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5180 34 .cfa: sp 0 + .ra: x30
STACK CFI 5188 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 51b4 24 .cfa: sp 0 + .ra: x30
STACK CFI 51bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51e0 258 .cfa: sp 0 + .ra: x30
STACK CFI 51e8 .cfa: sp 96 +
STACK CFI 51f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5208 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 532c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5378 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5440 100 .cfa: sp 0 + .ra: x30
STACK CFI 5448 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5450 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 545c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5468 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5478 x25: .cfa -16 + ^
STACK CFI 5518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5520 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5540 20 .cfa: sp 0 + .ra: x30
STACK CFI 5548 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5558 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5560 6c .cfa: sp 0 + .ra: x30
STACK CFI 55a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 55d0 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 55d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 55e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5608 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 563c x23: x23 x24: x24
STACK CFI 5670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5678 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 56fc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5728 x23: x23 x24: x24
STACK CFI INIT 58c4 294 .cfa: sp 0 + .ra: x30
STACK CFI 58cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58d4 x21: .cfa -16 + ^
STACK CFI 58dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5940 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 59c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 59d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b60 54 .cfa: sp 0 + .ra: x30
STACK CFI 5b8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5bb4 24 .cfa: sp 0 + .ra: x30
STACK CFI 5bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5be0 60 .cfa: sp 0 + .ra: x30
STACK CFI 5c18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5c40 50 .cfa: sp 0 + .ra: x30
STACK CFI 5c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5c90 40 .cfa: sp 0 + .ra: x30
STACK CFI 5cb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5cd0 17c .cfa: sp 0 + .ra: x30
STACK CFI 5cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5cec x21: .cfa -16 + ^
STACK CFI 5d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e50 348 .cfa: sp 0 + .ra: x30
STACK CFI 5e58 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5e60 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5e6c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5e78 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5e8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5ea8 x27: .cfa -16 + ^
STACK CFI 5f0c x27: x27
STACK CFI 5f78 x25: x25 x26: x26
STACK CFI 5f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5f84 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5f9c x25: x25 x26: x26
STACK CFI 5fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5fa8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5fec x27: x27
STACK CFI 5ff0 x27: .cfa -16 + ^
STACK CFI 5ff4 x27: x27
STACK CFI 6070 x25: x25 x26: x26
STACK CFI 6074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 607c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 60d4 x27: .cfa -16 + ^
STACK CFI 6100 x27: x27
STACK CFI 6124 x27: .cfa -16 + ^
STACK CFI 612c x27: x27
STACK CFI 615c x27: .cfa -16 + ^
STACK CFI 6188 x27: x27
STACK CFI INIT 61a0 218 .cfa: sp 0 + .ra: x30
STACK CFI 61a8 .cfa: sp 112 +
STACK CFI 61ac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 61b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 61c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 629c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 63c0 158 .cfa: sp 0 + .ra: x30
STACK CFI 63c8 .cfa: sp 96 +
STACK CFI 63cc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6408 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64a4 x21: x21 x22: x22
STACK CFI 64b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64b4 x21: x21 x22: x22
STACK CFI 64e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64e8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 650c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6510 x21: x21 x22: x22
STACK CFI 6514 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 6520 58 .cfa: sp 0 + .ra: x30
STACK CFI 6528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6530 x19: .cfa -16 + ^
STACK CFI 6568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6580 84 .cfa: sp 0 + .ra: x30
STACK CFI 6588 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6598 x21: .cfa -16 + ^
STACK CFI 65f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 65fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6604 2ec .cfa: sp 0 + .ra: x30
STACK CFI 660c .cfa: sp 112 +
STACK CFI 6618 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 662c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6638 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6640 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6660 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6714 x21: x21 x22: x22
STACK CFI 6718 x23: x23 x24: x24
STACK CFI 671c x25: x25 x26: x26
STACK CFI 674c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6754 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6758 x23: x23 x24: x24
STACK CFI 675c x25: x25 x26: x26
STACK CFI 676c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6770 x21: x21 x22: x22
STACK CFI 6774 x23: x23 x24: x24
STACK CFI 6778 x25: x25 x26: x26
STACK CFI 677c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6848 x23: x23 x24: x24
STACK CFI 6850 x21: x21 x22: x22
STACK CFI 6854 x25: x25 x26: x26
STACK CFI 6858 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 68e0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 68e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 68e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 68ec x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 68f0 80 .cfa: sp 0 + .ra: x30
STACK CFI 6900 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 690c x19: .cfa -16 + ^
STACK CFI 6950 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 696c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6970 f0 .cfa: sp 0 + .ra: x30
STACK CFI 6978 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6980 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69a8 x21: .cfa -16 + ^
STACK CFI 69f0 x21: x21
STACK CFI 69f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 69fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6a00 x21: x21
STACK CFI 6a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6a20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6a28 x21: .cfa -16 + ^
STACK CFI INIT 6a60 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6a68 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6a70 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6a7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a90 x23: .cfa -16 + ^
STACK CFI 6ac8 x23: x23
STACK CFI 6ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ae0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6b10 68 .cfa: sp 0 + .ra: x30
STACK CFI 6b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b80 40 .cfa: sp 0 + .ra: x30
STACK CFI 6b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6bb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6bc0 148 .cfa: sp 0 + .ra: x30
STACK CFI 6bc8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6bd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6be0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6c9c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6d10 17c .cfa: sp 0 + .ra: x30
STACK CFI 6d18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e90 140 .cfa: sp 0 + .ra: x30
STACK CFI 6e98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ea0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ebc x21: .cfa -16 + ^
STACK CFI 6f30 x21: x21
STACK CFI 6f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6fa4 x21: x21
STACK CFI 6fac x21: .cfa -16 + ^
STACK CFI INIT 6fd0 34 .cfa: sp 0 + .ra: x30
STACK CFI 6fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6fe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7004 208 .cfa: sp 0 + .ra: x30
STACK CFI 700c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7014 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7030 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7034 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7038 x25: .cfa -16 + ^
STACK CFI 7080 x21: x21 x22: x22
STACK CFI 7084 x23: x23 x24: x24
STACK CFI 7088 x25: x25
STACK CFI 7094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 709c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 711c x25: x25
STACK CFI 7124 x21: x21 x22: x22
STACK CFI 712c x23: x23 x24: x24
STACK CFI 7134 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 71f8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 7200 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7204 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7208 x25: .cfa -16 + ^
STACK CFI INIT 7210 58 .cfa: sp 0 + .ra: x30
STACK CFI 7224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 722c x19: .cfa -16 + ^
STACK CFI 7240 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7248 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 725c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7270 330 .cfa: sp 0 + .ra: x30
STACK CFI 7278 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7284 .cfa: x29 96 +
STACK CFI 7288 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 72a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 74dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 74e4 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 75a0 140 .cfa: sp 0 + .ra: x30
STACK CFI 75a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75c4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 767c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7684 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 76e0 114 .cfa: sp 0 + .ra: x30
STACK CFI 76e8 .cfa: sp 176 +
STACK CFI 76f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7700 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7740 x21: .cfa -16 + ^
STACK CFI 776c x21: x21
STACK CFI 77a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77a8 .cfa: sp 176 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 77d8 x21: x21
STACK CFI 77dc x21: .cfa -16 + ^
STACK CFI 77e4 x21: x21
STACK CFI 77f0 x21: .cfa -16 + ^
STACK CFI INIT 77f4 404 .cfa: sp 0 + .ra: x30
STACK CFI 77fc .cfa: sp 144 +
STACK CFI 780c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7814 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 781c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7824 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 78f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7900 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7994 x25: .cfa -16 + ^
STACK CFI 79e8 x25: x25
STACK CFI 79ec x25: .cfa -16 + ^
STACK CFI 7a38 x25: x25
STACK CFI 7b38 x25: .cfa -16 + ^
STACK CFI 7b48 x25: x25
STACK CFI 7bf4 x25: .cfa -16 + ^
STACK CFI INIT 7c00 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 7c08 .cfa: sp 48 +
STACK CFI 7c18 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cdc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7db4 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 7dbc .cfa: sp 128 +
STACK CFI 7dc8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7dd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7de4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7eb4 x21: x21 x22: x22
STACK CFI 7eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ec0 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7ecc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7f3c x23: x23 x24: x24
STACK CFI 7fe8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7fec x25: .cfa -16 + ^
STACK CFI 7ff0 x25: x25
STACK CFI 7ff4 x25: .cfa -16 + ^
STACK CFI 80dc x23: x23 x24: x24
STACK CFI 80e0 x25: x25
STACK CFI 80e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 80e8 x23: x23 x24: x24
STACK CFI 80ec x25: x25
STACK CFI 80f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 8144 x23: x23 x24: x24
STACK CFI 8148 x25: x25
STACK CFI 8150 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 819c x23: x23 x24: x24
STACK CFI 81a4 x25: x25
STACK CFI 81a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 8230 x23: x23 x24: x24
STACK CFI 8234 x25: x25
STACK CFI 823c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8240 x25: .cfa -16 + ^
STACK CFI 8244 x23: x23 x24: x24 x25: x25
STACK CFI 8268 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 826c x25: .cfa -16 + ^
STACK CFI INIT 8294 384 .cfa: sp 0 + .ra: x30
STACK CFI 829c .cfa: sp 160 +
STACK CFI 82a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 82b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 82c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 82dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8418 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 842c x25: .cfa -32 + ^
STACK CFI 84e8 x25: x25
STACK CFI 84ec x25: .cfa -32 + ^
STACK CFI 8580 x25: x25
STACK CFI 8584 x25: .cfa -32 + ^
STACK CFI 85d0 x25: x25
STACK CFI 85d8 x25: .cfa -32 + ^
STACK CFI 85ec x25: x25
STACK CFI 85f0 x25: .cfa -32 + ^
STACK CFI 8610 x25: x25
STACK CFI 8614 x25: .cfa -32 + ^
STACK CFI INIT 8620 18c .cfa: sp 0 + .ra: x30
STACK CFI 8628 .cfa: sp 176 +
STACK CFI 862c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8634 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8640 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8650 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8668 x25: .cfa -16 + ^
STACK CFI 8764 x25: x25
STACK CFI 877c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8784 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 87b0 474 .cfa: sp 0 + .ra: x30
STACK CFI 87b8 .cfa: sp 336 +
STACK CFI 87c4 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 87cc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 87d4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 87dc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 87e4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8814 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8984 x19: x19 x20: x20
STACK CFI 89bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 89c4 .cfa: sp 336 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 8ae8 x19: x19 x20: x20
STACK CFI 8aec x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8b90 x19: x19 x20: x20
STACK CFI 8b94 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8bdc x19: x19 x20: x20
STACK CFI 8be0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 8c1c x19: x19 x20: x20
STACK CFI 8c20 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI INIT 8c24 3bc .cfa: sp 0 + .ra: x30
STACK CFI 8c2c .cfa: sp 144 +
STACK CFI 8c40 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8c4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8c54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8d04 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8db0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8e04 x23: x23 x24: x24
STACK CFI 8e6c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8eb8 x23: x23 x24: x24
STACK CFI 8f50 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8f90 x23: x23 x24: x24
STACK CFI 8fdc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 8fe0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 8fe8 .cfa: sp 288 +
STACK CFI 8ff4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9000 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 90b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 90bc .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 92e0 5f8 .cfa: sp 0 + .ra: x30
STACK CFI 92e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92f0 x19: .cfa -16 + ^
STACK CFI 93c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 93d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 93dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 93e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 98e0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 98e8 .cfa: sp 80 +
STACK CFI 98f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 98fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9978 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 99a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 99e0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9a54 x21: x21 x22: x22
STACK CFI 9a5c x23: x23 x24: x24
STACK CFI 9a64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a74 x21: x21 x22: x22
STACK CFI 9a80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a98 x21: x21 x22: x22
STACK CFI 9aa4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9af8 x23: x23 x24: x24
STACK CFI 9b1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9b34 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 9b3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9b40 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9b44 x23: x23 x24: x24
STACK CFI 9b68 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9b6c x23: x23 x24: x24
STACK CFI 9b74 x21: x21 x22: x22
STACK CFI 9b7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9b80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 9b84 4c8 .cfa: sp 0 + .ra: x30
STACK CFI 9b8c .cfa: sp 176 +
STACK CFI 9b9c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9ba4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9bb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9bc4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9c1c x25: .cfa -32 + ^
STACK CFI 9c94 x25: x25
STACK CFI 9d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9d44 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9d98 x25: .cfa -32 + ^
STACK CFI 9dc0 x25: x25
STACK CFI 9e60 x25: .cfa -32 + ^
STACK CFI 9e78 x25: x25
STACK CFI 9e98 x25: .cfa -32 + ^
STACK CFI 9ea0 x25: x25
STACK CFI 9ed8 x25: .cfa -32 + ^
STACK CFI 9edc x25: x25
STACK CFI 9f48 x25: .cfa -32 + ^
STACK CFI 9f4c x25: x25
STACK CFI 9f88 x25: .cfa -32 + ^
STACK CFI 9f8c x25: x25
STACK CFI 9fcc x25: .cfa -32 + ^
STACK CFI 9ff4 x25: x25
STACK CFI a020 x25: .cfa -32 + ^
STACK CFI a024 x25: x25
STACK CFI a028 x25: .cfa -32 + ^
STACK CFI a02c x25: x25
STACK CFI a048 x25: .cfa -32 + ^
STACK CFI INIT a050 214 .cfa: sp 0 + .ra: x30
STACK CFI a058 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a070 .cfa: sp 1120 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a104 .cfa: sp 80 +
STACK CFI a11c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a124 .cfa: sp 1120 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
