MODULE Linux arm64 69031950E44B02BAA9AEDB529973C3830 libliodom_common.so
INFO CODE_ID 501903694BE4BA02A9AEDB529973C383
FILE 0 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/common/base.cpp
FILE 1 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/common/common.cpp
FILE 2 /opt/aarch64--glibc--bleeding-edge-2024.02-1/lib/gcc/aarch64-buildroot-linux-gnu/13.2.0/include/arm_neon.h
FILE 3 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/DenseStorage.h
FILE 4 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/Matrix.h
FILE 5 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/PlainObjectBase.h
FILE 6 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h
FILE 7 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h
FILE 8 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h
FILE 9 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h
FILE 10 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/OrthoMethods.h
FILE 11 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Geometry/Quaternion.h
FUNC 1000 4 0 _GLOBAL__sub_I_base.cpp
1000 4 380 0
FUNC 1010 4 0 _GLOBAL__sub_I_common.cpp
1010 4 5 1
FUNC 1100 20 0 li::distance2d(Eigen::Matrix<float, 2, 1, 0, 2, 1> const&, Eigen::Matrix<float, 2, 1, 0, 2, 1> const&)
1100 c 7 0
110c 4 7 0
1110 4 7 0
1114 4 7 0
1118 8 8 0
FUNC 1120 30 0 li::distance3d(Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&)
1120 8 11 0
1128 4 11 0
112c 4 11 0
1130 4 11 0
1134 4 11 0
1138 4 11 0
113c 4 11 0
1140 4 11 0
1144 4 11 0
1148 8 12 0
FUNC 1150 14c 0 li::df_rotatePoint(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, float*, float*, float*)
1150 4 27 0
1154 4 26 0
1158 4 30 0
115c 4 31 0
1160 4 40 0
1164 4 39 0
1168 4 34 0
116c 4 39 0
1170 4 41 0
1174 4 32 0
1178 4 41 0
117c 4 40 0
1180 4 40 0
1184 4 39 0
1188 4 39 0
118c 4 40 0
1190 4 41 0
1194 4 37 0
1198 4 37 0
119c 4 40 0
11a0 4 39 0
11a4 4 39 0
11a8 4 41 0
11ac 4 41 0
11b0 4 40 0
11b4 4 41 0
11b8 4 39 0
11bc 4 39 0
11c0 4 40 0
11c4 4 41 0
11c8 4 40 0
11cc 4 41 0
11d0 4 39 0
11d4 4 40 0
11d8 4 41 0
11dc 4 39 0
11e0 4 40 0
11e4 4 41 0
11e8 4 40 0
11ec 4 43 0
11f0 4 44 0
11f4 4 44 0
11f8 4 48 0
11fc 4 47 0
1200 4 48 0
1204 4 47 0
1208 4 46 0
120c 4 49 0
1210 4 46 0
1214 4 49 0
1218 4 53 0
121c c 52 0
1228 4 47 0
122c 4 48 0
1230 4 46 0
1234 4 49 0
1238 4 58 0
123c 4 51 0
1240 4 53 0
1244 4 52 0
1248 4 47 0
124c 4 48 0
1250 4 46 0
1254 4 49 0
1258 4 51 0
125c 4 58 0
1260 4 53 0
1264 4 52 0
1268 4 47 0
126c 4 49 0
1270 4 52 0
1274 4 54 0
1278 4 57 0
127c 4 59 0
1280 4 63 0
1284 4 66 0
1288 4 69 0
128c 4 71 0
1290 4 74 0
1294 4 75 0
1298 4 78 0
FUNC 12a0 50 0 li::toGlobal(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1>&)
12a0 4 14 0
12a4 8 15 0
12ac 8 14 0
12b4 4 14 0
12b8 4 14 0
12bc 4 15 0
12c0 4 15 0
12c4 8 12213 2
12cc 4 49 7
12d0 4 261 2
12d4 4 21862 2
12d8 c 49 7
12e4 4 17 0
12e8 8 17 0
FUNC 12f0 98 0 li::toLocal(Eigen::Quaternion<float, 0> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1> const&, Eigen::Matrix<float, 3, 1, 0, 3, 1>&)
12f0 4 19 0
12f4 4 20 0
12f8 8 19 0
1300 8 1360 2
1308 4 19 0
130c 4 12531 2
1310 c 19 0
131c 4 20 0
1320 4 1360 2
1324 c 19 0
1330 4 20 0
1334 4 21962 2
1338 4 20 0
133c 8 12213 2
1344 4 70 7
1348 8 22 0
1350 4 1619 2
1354 4 21862 2
1358 c 70 7
1364 18 22 0
137c 8 22 0
1384 4 22 0
FUNC 1390 114 0 li::df_rotate(Eigen::Quaternion<float, 0> const&, Eigen::Quaternion<float, 0> const&, float*, float*, float*)
1390 4 83 0
1394 4 87 0
1398 4 81 0
139c 4 85 0
13a0 4 99 0
13a4 4 91 0
13a8 4 92 0
13ac 4 91 0
13b0 4 92 0
13b4 4 93 0
13b8 4 90 0
13bc 4 93 0
13c0 4 90 0
13c4 4 101 0
13c8 4 91 0
13cc 4 92 0
13d0 4 93 0
13d4 4 90 0
13d8 4 92 0
13dc 4 91 0
13e0 4 93 0
13e4 4 90 0
13e8 4 95 0
13ec 4 95 0
13f0 4 95 0
13f4 4 106 0
13f8 4 101 0
13fc 4 96 0
1400 4 97 0
1404 4 96 0
1408 4 103 0
140c 4 102 0
1410 4 109 0
1414 4 105 0
1418 4 106 0
141c 4 101 0
1420 4 111 0
1424 4 107 0
1428 4 110 0
142c 4 105 0
1430 4 102 0
1434 4 107 0
1438 4 110 0
143c 4 111 0
1440 4 115 0
1444 4 118 0
1448 4 120 0
144c 4 119 0
1450 4 122 0
1454 4 128 0
1458 4 118 0
145c 4 120 0
1460 4 124 0
1464 4 126 0
1468 4 130 0
146c 4 132 0
1470 4 136 0
1474 4 140 0
1478 4 141 0
147c 4 139 0
1480 4 143 0
1484 4 147 0
1488 4 139 0
148c 4 141 0
1490 4 145 0
1494 4 149 0
1498 4 151 0
149c 4 153 0
14a0 4 156 0
FUNC 14b0 17c 0 li::eulerangle2quaternion(Eigen::Matrix<double, 3, 1, 0, 3, 1> const&, Eigen::Quaternion<double, 0>&)
14b0 10 160 0
14c0 4 561 11
14c4 4 160 0
14c8 8 565 11
14d0 14 160 0
14e4 4 160 0
14e8 c 160 0
14f4 4 1003 2
14f8 c 565 11
1504 4 10812 2
1508 4 1003 2
150c 8 1003 2
1514 4 10812 2
1518 4 561 11
151c 8 1003 2
1524 10 1367 2
1534 4 1003 2
1538 4 345 2
153c 4 80 8
1540 4 1003 2
1544 8 565 11
154c 4 1003 2
1550 8 1003 2
1558 4 1703 2
155c 4 345 2
1560 8 1703 2
1568 4 345 2
156c 4 1703 2
1570 4 1367 2
1574 8 3146 2
157c 8 3301 2
1584 4 345 2
1588 4 1367 2
158c 4 3146 2
1590 4 345 2
1594 4 3146 2
1598 4 3146 2
159c 8 1003 2
15a4 c 1367 2
15b0 4 1003 2
15b4 4 80 8
15b8 4 1003 2
15bc 8 80 8
15c4 4 80 8
15c8 8 1003 2
15d0 4 165 0
15d4 4 1003 2
15d8 4 1703 2
15dc 4 1003 2
15e0 4 345 2
15e4 4 1703 2
15e8 4 165 0
15ec 4 165 0
15f0 4 345 2
15f4 4 3146 2
15f8 4 3146 2
15fc 4 1367 2
1600 8 3301 2
1608 4 345 2
160c 4 1367 2
1610 4 345 2
1614 4 3146 2
1618 8 504 5
1620 4 165 0
1624 8 165 0
FUNC 1630 b4 0 li::df_rotateinv(Eigen::Quaternion<float, 0> const&, float*, float*)
1630 4 183 0
1634 4 170 0
1638 4 168 0
163c 4 177 0
1640 4 178 0
1644 4 185 0
1648 4 190 0
164c 4 179 0
1650 4 181 0
1654 4 186 0
1658 4 187 0
165c 4 189 0
1660 4 190 0
1664 4 185 0
1668 4 195 0
166c 4 193 0
1670 4 191 0
1674 4 194 0
1678 4 185 0
167c 4 190 0
1680 4 195 0
1684 4 186 0
1688 4 189 0
168c 4 187 0
1690 4 193 0
1694 4 191 0
1698 4 194 0
169c 4 186 0
16a0 4 189 0
16a4 4 191 0
16a8 4 194 0
16ac 4 195 0
16b0 4 198 0
16b4 4 200 0
16b8 28 200 0
16e0 4 218 0
FUNC 16f0 5ac 0 li::df_delta_pqv(float, float const*, float const*, float const*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*, float*)
16f0 14 223 0
1704 4 405 4
1708 4 223 0
170c 4 393 4
1710 8 223 0
1718 4 395 4
171c 4 223 0
1720 4 408 4
1724 4 393 4
1728 c 223 0
1734 c 223 0
1740 4 933 2
1744 4 405 4
1748 4 223 0
174c 4 405 4
1750 4 223 0
1754 4 407 4
1758 4 223 0
175c c 223 0
1768 4 393 4
176c 4 395 4
1770 4 393 4
1774 4 223 0
1778 4 393 4
177c 4 395 4
1780 4 933 2
1784 4 393 4
1788 4 1619 2
178c 4 395 4
1790 4 80 8
1794 4 393 4
1798 4 395 4
179c 8 933 2
17a4 4 359 8
17a8 4 933 2
17ac 4 395 4
17b0 4 80 8
17b4 4 393 4
17b8 4 1619 2
17bc 4 80 8
17c0 4 261 2
17c4 4 80 8
17c8 4 393 4
17cc 4 395 4
17d0 4 42 8
17d4 4 359 8
17d8 4 261 2
17dc 4 408 4
17e0 4 1619 2
17e4 4 408 4
17e8 4 359 8
17ec 4 35 10
17f0 8 42 8
17f8 8 47 10
1800 4 405 4
1804 4 46 10
1808 4 46 10
180c 4 46 10
1810 4 45 10
1814 4 47 10
1818 4 45 10
181c 4 45 10
1820 4 1360 2
1824 4 46 10
1828 4 45 10
182c 4 47 10
1830 4 1360 2
1834 4 1360 2
1838 4 394 4
183c 4 223 0
1840 4 393 4
1844 4 12213 2
1848 4 49 7
184c 4 1360 2
1850 8 223 0
1858 4 261 2
185c 4 223 0
1860 4 393 4
1864 4 223 0
1868 4 394 4
186c 4 12213 2
1870 10 996 2
1880 8 46 10
1888 8 46 10
1890 8 261 2
1898 4 47 10
189c 4 996 2
18a0 4 42 8
18a4 4 996 2
18a8 4 47 10
18ac 4 261 2
18b0 4 49 7
18b4 8 996 2
18bc 4 394 4
18c0 4 45 10
18c4 4 42 8
18c8 4 45 10
18cc c 261 2
18d8 8 42 8
18e0 8 267 3
18e8 4 46 10
18ec 8 46 10
18f4 4 393 4
18f8 c 1696 2
1904 4 45 10
1908 4 24 7
190c 8 1696 2
1914 4 45 10
1918 4 47 10
191c 4 1696 2
1920 4 47 10
1924 8 1360 2
192c 4 1696 2
1930 4 338 2
1934 4 261 2
1938 4 394 4
193c 4 1696 2
1940 4 393 4
1944 4 1360 2
1948 4 261 2
194c 4 42 8
1950 8 261 2
1958 4 338 2
195c 4 21862 2
1960 4 24 7
1964 4 21862 2
1968 4 21962 2
196c 4 237 0
1970 4 239 0
1974 c 238 0
1980 4 241 0
1984 4 239 0
1988 4 241 0
198c 4 238 0
1990 4 240 0
1994 4 241 0
1998 4 239 0
199c 8 240 0
19a4 4 241 0
19a8 4 239 0
19ac 4 240 0
19b0 4 244 0
19b4 4 17409 2
19b8 4 24 9
19bc 4 245 0
19c0 4 245 0
19c4 8 245 0
19cc 4 245 0
19d0 4 24 9
19d4 8 17409 2
19dc 4 21862 2
19e0 4 24 7
19e4 4 245 0
19e8 c 245 0
19f4 4 248 0
19f8 4 1619 2
19fc 4 249 0
1a00 4 359 8
1a04 4 249 0
1a08 8 249 0
1a10 4 1619 2
1a14 4 249 0
1a18 8 359 8
1a20 4 261 2
1a24 4 42 8
1a28 4 1619 2
1a2c 4 359 8
1a30 4 21862 2
1a34 4 24 7
1a38 4 249 0
1a3c 4 249 0
1a40 4 252 0
1a44 4 21862 2
1a48 4 24 7
1a4c 8 253 0
1a54 c 253 0
1a60 4 21862 2
1a64 4 24 7
1a68 4 253 0
1a6c 4 256 0
1a70 18 257 0
1a88 4 260 0
1a8c c 261 0
1a98 4 264 0
1a9c c 265 0
1aa8 4 269 0
1aac c 1360 2
1ab8 8 272 0
1ac0 c 272 0
1acc 4 1360 2
1ad0 4 272 0
1ad4 4 21962 2
1ad8 4 272 0
1adc c 273 0
1ae8 4 273 0
1aec 8 695 6
1af4 4 11874 2
1af8 10 695 6
1b08 10 996 2
1b18 4 11874 2
1b1c 4 695 6
1b20 4 11874 2
1b24 4 695 6
1b28 4 11874 2
1b2c 4 695 6
1b30 4 11874 2
1b34 4 695 6
1b38 8 11874 2
1b40 4 695 6
1b44 4 11874 2
1b48 4 695 6
1b4c 4 11874 2
1b50 4 695 6
1b54 4 11874 2
1b58 4 695 6
1b5c 4 11874 2
1b60 c 11874 2
1b6c 4 21962 2
1b70 4 275 0
1b74 4 21962 2
1b78 4 275 0
1b7c 8 275 0
1b84 4 278 0
1b88 c 1360 2
1b94 c 279 0
1ba0 8 279 0
1ba8 4 1360 2
1bac 4 21962 2
1bb0 4 279 0
1bb4 4 283 0
1bb8 4 17409 2
1bbc 4 24 9
1bc0 8 284 0
1bc8 c 284 0
1bd4 4 21862 2
1bd8 4 24 7
1bdc 4 284 0
1be0 4 287 0
1be4 4 1619 2
1be8 4 288 0
1bec 4 359 8
1bf0 4 288 0
1bf4 8 288 0
1bfc 4 1619 2
1c00 4 288 0
1c04 4 359 8
1c08 4 261 2
1c0c 4 42 8
1c10 4 21862 2
1c14 4 24 7
1c18 4 288 0
1c1c 8 291 0
1c24 18 292 0
1c3c 4 295 0
1c40 c 296 0
1c4c 24 299 0
1c70 18 299 0
1c88 c 299 0
1c94 4 299 0
1c98 4 299 0
FUNC 1ca0 1e8 0 li::df_delta_speed(float, float, float const*, float*, float*, float*, float*, float*)
1ca0 8 302 0
1ca8 4 405 4
1cac 8 1360 2
1cb4 4 302 0
1cb8 4 395 4
1cbc 4 408 4
1cc0 4 302 0
1cc4 4 394 4
1cc8 c 302 0
1cd4 4 1360 2
1cd8 c 302 0
1ce4 4 405 4
1ce8 4 394 4
1cec 8 46 10
1cf4 4 395 4
1cf8 4 35 10
1cfc 4 12213 2
1d00 4 45 10
1d04 4 46 10
1d08 4 45 10
1d0c 4 47 10
1d10 4 47 10
1d14 4 394 4
1d18 4 393 4
1d1c 4 12213 2
1d20 4 49 7
1d24 4 261 2
1d28 4 46 10
1d2c 4 45 10
1d30 4 46 10
1d34 4 45 10
1d38 4 45 10
1d3c 4 394 4
1d40 4 12213 2
1d44 4 306 0
1d48 8 261 2
1d50 4 306 0
1d54 4 306 0
1d58 4 308 0
1d5c 4 309 0
1d60 4 311 0
1d64 4 312 0
1d68 4 314 0
1d6c 4 316 0
1d70 4 316 0
1d74 8 316 0
1d7c 8 316 0
1d84 4 316 0
1d88 4 21962 2
1d8c 4 316 0
1d90 10 318 0
1da0 8 694 6
1da8 4 933 2
1dac 4 694 6
1db0 4 12213 2
1db4 4 933 2
1db8 8 933 2
1dc0 8 694 6
1dc8 4 322 0
1dcc 4 11867 2
1dd0 c 11867 2
1ddc 8 694 6
1de4 4 11867 2
1de8 4 12213 2
1dec 4 11867 2
1df0 4 694 6
1df4 4 11867 2
1df8 8 694 6
1e00 4 11867 2
1e04 4 11867 2
1e08 4 11867 2
1e0c 4 321 0
1e10 4 322 0
1e14 4 327 0
1e18 c 1360 2
1e24 4 329 0
1e28 4 329 0
1e2c 4 329 0
1e30 8 329 0
1e38 4 1360 2
1e3c 4 21962 2
1e40 4 329 0
1e44 4 330 0
1e48 4 331 0
1e4c 4 332 0
1e50 4 331 0
1e54 4 332 0
1e58 20 335 0
1e78 4 335 0
1e7c 4 335 0
1e80 4 335 0
1e84 4 335 0
FUNC 1e90 54 0 li::df_delta_xy(float const*, float const*, float*, float*, float*, float*)
1e90 4 341 0
1e94 4 394 4
1e98 8 342 0
1ea0 4 342 0
1ea4 8 343 0
1eac 4 343 0
1eb0 4 346 0
1eb4 4 348 0
1eb8 8 348 0
1ec0 4 355 0
1ec4 8 357 0
1ecc 4 362 0
1ed0 4 357 0
1ed4 4 366 0
1ed8 4 368 0
1edc 4 374 0
1ee0 4 379 0
PUBLIC f50 0 _init
PUBLIC 1014 0 call_weak_fn
PUBLIC 1030 0 deregister_tm_clones
PUBLIC 1060 0 register_tm_clones
PUBLIC 10a0 0 __do_global_dtors_aux
PUBLIC 10f0 0 frame_dummy
PUBLIC 1ee4 0 _fini
STACK CFI INIT 1030 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1060 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 10a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10ac x19: .cfa -16 + ^
STACK CFI 10e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1100 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1150 14c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 12a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12f0 98 .cfa: sp 0 + .ra: x30
STACK CFI 12f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1300 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1384 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1390 114 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14b0 17c .cfa: sp 0 + .ra: x30
STACK CFI 14b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14bc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14c8 v8: .cfa -80 + ^ v9: .cfa -72 + ^
STACK CFI 14d4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14ec v10: .cfa -64 + ^
STACK CFI 1628 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1630 b4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16f0 5ac .cfa: sp 0 + .ra: x30
STACK CFI 16f4 .cfa: sp 512 +
STACK CFI 1700 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 1714 x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 1740 v12: .cfa -384 + ^ v13: .cfa -376 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 175c v10: .cfa -400 + ^ v11: .cfa -392 + ^ v14: .cfa -368 + ^ v15: .cfa -360 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^
STACK CFI 1c94 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1c98 .cfa: sp 512 + .ra: .cfa -504 + ^ v10: .cfa -400 + ^ v11: .cfa -392 + ^ v12: .cfa -384 + ^ v13: .cfa -376 + ^ v14: .cfa -368 + ^ v15: .cfa -360 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI INIT 1ca0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 1ca4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 1cc0 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 1d78 x21: .cfa -176 + ^
STACK CFI 1dcc x21: x21
STACK CFI 1e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e80 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI 1e84 x21: .cfa -176 + ^
STACK CFI INIT 1e90 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1000 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1010 4 .cfa: sp 0 + .ra: x30
