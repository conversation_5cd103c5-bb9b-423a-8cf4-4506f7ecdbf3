MODULE Linux arm64 CE9BD1B59091A023FD2DF9019DDA40B30 libopencv_xphoto.so.4.3
INFO CODE_ID B5D19BCE919023A0FD2DF9019DDA40B386FA5AFE
PUBLIC 7440 0 _init
PUBLIC 7f50 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.34]
PUBLIC 7ff0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.30]
PUBLIC 8090 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.81]
PUBLIC 8130 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.33]
PUBLIC 81d0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.56]
PUBLIC 8270 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.30]
PUBLIC 8310 0 _GLOBAL__sub_I_dct_image_denoising.cpp
PUBLIC 8340 0 _GLOBAL__sub_I_inpainting.cpp
PUBLIC 8370 0 _GLOBAL__sub_I_simple_color_balance.cpp
PUBLIC 83a0 0 call_weak_fn
PUBLIC 83b8 0 deregister_tm_clones
PUBLIC 83f0 0 register_tm_clones
PUBLIC 8430 0 __do_global_dtors_aux
PUBLIC 8478 0 frame_dummy
PUBLIC 84b0 0 void cv::xphoto::HaarTransform2D::ForwardTransform2x2<unsigned char, short>(unsigned char const*, short*, int const&, int)
PUBLIC 8520 0 void cv::xphoto::HaarTransform2D::InverseTransform2x2<short>(short*, int)
PUBLIC 85a0 0 void cv::xphoto::HaarTransform2D::InverseTransform4x4<short>(short*, int)
PUBLIC 8750 0 void cv::xphoto::HaarTransform2D::ForwardTransform2x2<unsigned short, int>(unsigned short const*, int*, int const&, int)
PUBLIC 87b8 0 void cv::xphoto::HaarTransform2D::InverseTransform2x2<int>(int*, int)
PUBLIC 8818 0 void cv::xphoto::HaarTransform2D::InverseTransform4x4<int>(int*, int)
PUBLIC 88b0 0 void cv::xphoto::HaarTransform2D::InverseTransform8x8<short>(short*, int)
PUBLIC 8e80 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<short, 16>(short*, int)
PUBLIC 91c8 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<short, 32>(short*, int)
PUBLIC 9d60 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<short, 64>(short*, int)
PUBLIC 9fb0 0 void cv::xphoto::HaarTransform2D::InverseTransform8x8<int>(int*, int)
PUBLIC a478 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<int, 16>(int*, int)
PUBLIC a710 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<int, 32>(int*, int)
PUBLIC b2e8 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<int, 64>(int*, int)
PUBLIC b530 0 void cv::xphoto::HaarTransform1D::ForwardTransform16<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC b7a0 0 void cv::xphoto::HaarTransform1D::InverseTransform16<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC b9b8 0 void cv::xphoto::HaarTransform1D::ForwardTransform8<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bab0 0 void cv::xphoto::HaarTransform1D::InverseTransform8<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bb90 0 void cv::xphoto::HaarTransform1D::ForwardTransform4<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bc08 0 void cv::xphoto::HaarTransform1D::InverseTransform4<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bc78 0 void cv::xphoto::HaarTransform1D::ForwardTransform2<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bcb0 0 void cv::xphoto::HaarTransform1D::InverseTransform2<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&)
PUBLIC bce8 0 void cv::xphoto::HaarTransform1D::ForwardTransform16<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC bf10 0 void cv::xphoto::HaarTransform1D::InverseTransform16<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c120 0 void cv::xphoto::HaarTransform1D::ForwardTransform8<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c1f8 0 void cv::xphoto::HaarTransform1D::InverseTransform8<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c2d8 0 void cv::xphoto::HaarTransform1D::ForwardTransform4<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c340 0 void cv::xphoto::HaarTransform1D::InverseTransform4<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c3b0 0 void cv::xphoto::HaarTransform1D::ForwardTransform2<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c3e0 0 void cv::xphoto::HaarTransform1D::InverseTransform2<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&)
PUBLIC c420 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned char, short>(unsigned char const*, short*, int const&, int)
PUBLIC d5e0 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned short, int>(unsigned short const*, int*, int const&, int)
PUBLIC e330 0 void cv::xphoto::HaarTransform1D::InverseTransformN<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&, unsigned int const&)
PUBLIC e510 0 void cv::xphoto::HaarTransform1D::ForwardTransformN<int, int, int>(cv::xphoto::BlockMatch<int, int, int>*, int const&, unsigned int const&)
PUBLIC e710 0 void cv::xphoto::HaarTransform1D::InverseTransformN<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&, unsigned int const&)
PUBLIC e8c8 0 void cv::xphoto::HaarTransform1D::ForwardTransformN<short, int, short>(cv::xphoto::BlockMatch<short, int, short>*, int const&, unsigned int const&)
PUBLIC eb08 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.32]
PUBLIC ebe8 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned short, int, 64>(unsigned short const*, int*, int const&, int)
PUBLIC eec8 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned short, int, 32>(unsigned short const*, int*, int const&, int)
PUBLIC fd60 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned short, int, 16>(unsigned short const*, int*, int const&, int)
PUBLIC 10520 0 void cv::xphoto::HaarTransform2D::ForwardTransform8x8<unsigned short, int>(unsigned short const*, int*, int const&, int)
PUBLIC 10ac8 0 void cv::xphoto::HaarTransform2D::ForwardTransform4x4<unsigned short, int>(unsigned short const*, int*, int const&, int)
PUBLIC 10c70 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned char, short, 64>(unsigned char const*, short*, int const&, int)
PUBLIC 10f58 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned char, short, 32>(unsigned char const*, short*, int const&, int)
PUBLIC 11dc0 0 void cv::xphoto::HaarTransform2D::ForwardTransformXxX<unsigned char, short, 16>(unsigned char const*, short*, int const&, int)
PUBLIC 12538 0 void cv::xphoto::HaarTransform2D::ForwardTransform8x8<unsigned char, short>(unsigned char const*, short*, int const&, int)
PUBLIC 12860 0 void cv::xphoto::HaarTransform2D::ForwardTransform4x4<unsigned char, short>(unsigned char const*, short*, int const&, int)
PUBLIC 12ac8 0 void std::__insertion_sort<cv::xphoto::BlockMatch<int, int, int>*, __gnu_cxx::__ops::_Iter_less_iter>(cv::xphoto::BlockMatch<int, int, int>*, cv::xphoto::BlockMatch<int, int, int>*, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.93]
PUBLIC 12c20 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<int>(int*, int)
PUBLIC 13130 0 void cv::xphoto::HaarTransform2D::InverseTransformXxX<short>(short*, int)
PUBLIC 13788 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 138d8 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 13a28 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 13af8 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 13bc8 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 13c98 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 13de8 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 13eb0 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 13f78 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep1()
PUBLIC 14040 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 14188 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 142d0 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::~Bm3dDenoisingInvokerStep2()
PUBLIC 14418 0 cv::Mat::~Mat()
PUBLIC 144a8 0 cv::MatExpr::~MatExpr()
PUBLIC 14660 0 cv::xphoto::calcKaiserWindow2D(float*&, int, float)
PUBLIC 14f88 0 cv::xphoto::HaarTransform<unsigned char, short>::RegisterTransforms2D(int const&)
PUBLIC 15148 0 cv::xphoto::HaarTransform<unsigned short, int>::RegisterTransforms2D(int const&)
PUBLIC 15310 0 cv::xphoto::HaarTransform<unsigned char, short>::calcCoefficients1D(cv::Mat&, int const&)
PUBLIC 15ad0 0 cv::xphoto::HaarTransform<unsigned char, short>::fillHaarCoefficients2D(float*, int const&)
PUBLIC 15fb0 0 cv::xphoto::HaarTransform<unsigned char, short>::calcThresholdMap3D(short*&, float const&, int const&, int const&)
PUBLIC 163b0 0 cv::xphoto::HaarTransform<unsigned short, int>::calcCoefficients1D(cv::Mat&, int const&)
PUBLIC 16b70 0 cv::xphoto::HaarTransform<unsigned short, int>::fillHaarCoefficients2D(float*, int const&)
PUBLIC 17050 0 cv::xphoto::HaarTransform<unsigned short, int>::calcThresholdMap3D(int*&, float const&, int const&, int const&)
PUBLIC 17410 0 cv::xphoto::bm3dDenoising(cv::_InputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, float, int, int, int, int, int, int, float, int, int, int)
PUBLIC 19080 0 cv::xphoto::bm3dDenoising(cv::_InputArray const&, cv::_OutputArray const&, float, int, int, int, int, int, int, float, int, int, int)
PUBLIC 19240 0 void std::__adjust_heap<cv::xphoto::BlockMatch<int, int, int>*, long, cv::xphoto::BlockMatch<int, int, int>, __gnu_cxx::__ops::_Iter_less_iter>(cv::xphoto::BlockMatch<int, int, int>*, long, long, cv::xphoto::BlockMatch<int, int, int>, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 193e0 0 void std::__introsort_loop<cv::xphoto::BlockMatch<int, int, int>*, long, __gnu_cxx::__ops::_Iter_less_iter>(cv::xphoto::BlockMatch<int, int, int>*, cv::xphoto::BlockMatch<int, int, int>*, long, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.104]
PUBLIC 19660 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::operator()(cv::Range const&) const
PUBLIC 1b400 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned short, cv::xphoto::DistAbs, float, int, cv::xphoto::HaarTransform<unsigned short, int> >::operator()(cv::Range const&) const
PUBLIC 1d1b0 0 void std::__adjust_heap<cv::xphoto::BlockMatch<short, int, short>*, long, cv::xphoto::BlockMatch<short, int, short>, __gnu_cxx::__ops::_Iter_less_iter>(cv::xphoto::BlockMatch<short, int, short>*, long, long, cv::xphoto::BlockMatch<short, int, short>, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 1d2f0 0 void std::__introsort_loop<cv::xphoto::BlockMatch<short, int, short>*, long, __gnu_cxx::__ops::_Iter_less_iter>(cv::xphoto::BlockMatch<short, int, short>*, cv::xphoto::BlockMatch<short, int, short>*, long, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.113]
PUBLIC 1d4f0 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::operator()(cv::Range const&) const
PUBLIC 1ee00 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::operator()(cv::Range const&) const
PUBLIC 20770 0 cv::xphoto::Bm3dDenoisingInvokerStep1<unsigned char, cv::xphoto::DistSquared, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::operator()(cv::Range const&) const
PUBLIC 220f0 0 cv::xphoto::Bm3dDenoisingInvokerStep2<unsigned char, cv::xphoto::DistAbs, float, short, cv::xphoto::HaarTransform<unsigned char, short> >::operator()(cv::Range const&) const
PUBLIC 23a00 0 cv::xphoto::grayDctDenoisingInvoker::~grayDctDenoisingInvoker()
PUBLIC 23a10 0 cv::xphoto::grayDctDenoisingInvoker::~grayDctDenoisingInvoker()
PUBLIC 23a40 0 cv::xphoto::grayDctDenoisingInvoker::operator()(cv::Range const&) const
PUBLIC 23d40 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 23e00 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC 24150 0 cv::xphoto::grayDctDenoising(cv::Mat const&, cv::Mat&, double, int)
PUBLIC 24b70 0 cv::xphoto::rgbDctDenoising(cv::Mat const&, cv::Mat&, double, int)
PUBLIC 24f70 0 cv::xphoto::dctDenoising(cv::Mat const&, cv::Mat&, double, int)
PUBLIC 25210 0 cv::Algorithm::clear()
PUBLIC 25218 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 25220 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 25228 0 cv::Algorithm::empty() const
PUBLIC 25230 0 cv::xphoto::GrayworldWBImpl::getSaturationThreshold() const
PUBLIC 25238 0 cv::xphoto::GrayworldWBImpl::setSaturationThreshold(float)
PUBLIC 25240 0 std::_Sp_counted_ptr_inplace<cv::xphoto::GrayworldWBImpl, std::allocator<cv::xphoto::GrayworldWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 25248 0 std::_Sp_counted_ptr_inplace<cv::xphoto::GrayworldWBImpl, std::allocator<cv::xphoto::GrayworldWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 25250 0 std::_Sp_counted_ptr_inplace<cv::xphoto::GrayworldWBImpl, std::allocator<cv::xphoto::GrayworldWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 25258 0 std::_Sp_counted_ptr_inplace<cv::xphoto::GrayworldWBImpl, std::allocator<cv::xphoto::GrayworldWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 252a8 0 cv::xphoto::GrayworldWBImpl::~GrayworldWBImpl()
PUBLIC 252c0 0 cv::xphoto::GrayworldWBImpl::~GrayworldWBImpl()
PUBLIC 252e8 0 std::_Sp_counted_ptr_inplace<cv::xphoto::GrayworldWBImpl, std::allocator<cv::xphoto::GrayworldWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 25308 0 cv::xphoto::calculateChannelSums(unsigned int&, unsigned int&, unsigned int&, unsigned char*, int, float)
PUBLIC 25570 0 cv::xphoto::calculateChannelSums(unsigned long&, unsigned long&, unsigned long&, unsigned short*, int, float)
PUBLIC 257d0 0 cv::xphoto::applyChannelGains(cv::_InputArray const&, cv::_OutputArray const&, float, float, float)
PUBLIC 26470 0 cv::xphoto::GrayworldWBImpl::balanceWhite(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 26848 0 cv::xphoto::createGrayworldWB()
PUBLIC 26918 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::dist(cv::Vec<float, 1> const&, cv::Vec<float, 1> const&, cv::Vec<float, 1> const&, cv::Vec<float, 1> const&)
PUBLIC 26948 0 cv::Point_<int> cv::operator*<int>(float, cv::Point_<int> const&)
PUBLIC 269a0 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 269b0 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 269d8 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 269e8 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 26a10 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 26a20 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 26a48 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 26a58 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::ParallelExpansion::~ParallelExpansion()
PUBLIC 26a80 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.172]
PUBLIC 26b60 0 void std::vector<unsigned char, std::allocator<unsigned char> >::emplace_back<unsigned char>(unsigned char&&) [clone .constprop.603]
PUBLIC 26c40 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::dist(cv::Vec<float, 4> const&, cv::Vec<float, 4> const&, cv::Vec<float, 4> const&, cv::Vec<float, 4> const&)
PUBLIC 26cc8 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::dist(cv::Vec<float, 3> const&, cv::Vec<float, 3> const&, cv::Vec<float, 3> const&, cv::Vec<float, 3> const&)
PUBLIC 26d70 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::dist(cv::Vec<float, 2> const&, cv::Vec<float, 2> const&, cv::Vec<float, 2> const&, cv::Vec<float, 2> const&)
PUBLIC 26db0 0 std::vector<int, std::allocator<int> >::vector(unsigned long, std::allocator<int> const&) [clone .constprop.606]
PUBLIC 26e28 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::~Photomontage()
PUBLIC 26ea8 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::~Photomontage()
PUBLIC 26f28 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::~Photomontage()
PUBLIC 26fa8 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::~Photomontage()
PUBLIC 27028 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::~Photomontage()
PUBLIC 270b0 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::~Photomontage()
PUBLIC 27138 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::~Photomontage()
PUBLIC 271c0 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::~Photomontage()
PUBLIC 27248 0 cv::Mat::Mat(cv::Mat const&)
PUBLIC 272d0 0 cv::xphoto::icvStandardDeviation(cv::Mat const&, cv::Mat const&)
PUBLIC 27480 0 cv::Mat::create(int, int, int)
PUBLIC 274e0 0 cv::MatExpr::operator cv::Mat() const
PUBLIC 27570 0 cv::xphoto::icvExtrapolateBlock(cv::Mat&, cv::Mat&, cv::xphoto::fsr_parameters&, double, double, cv::Mat&) [clone .constprop.608]
PUBLIC 28e98 0 KDTree<float, 24>::updateDist(int, int const&, int&, double&)
PUBLIC 29098 0 std::vector<std::tuple<int, int>, std::allocator<std::tuple<int, int> > >::operator=(std::vector<std::tuple<int, int>, std::allocator<std::tuple<int, int> > > const&)
PUBLIC 292e0 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 29628 0 KDTree<float, 24>::getMaxSpreadN(int, int) const
PUBLIC 29818 0 void std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::emplace_back<cv::Point_<int> >(cv::Point_<int>&&)
PUBLIC 29950 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::_M_default_append(unsigned long)
PUBLIC 29aa0 0 void std::vector<std::tuple<int, int>, std::allocator<std::tuple<int, int> > >::_M_emplace_back_aux<int&, int&>(int&, int&)
PUBLIC 29bb0 0 void std::vector<std::tuple<int, int>, std::allocator<std::tuple<int, int> > >::_M_emplace_back_aux<int, int>(int&&, int&&)
PUBLIC 29cc0 0 cv::xphoto::icvDetermineProcessingOrder(cv::Mat const&, cv::Mat const&, int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::Mat&)
PUBLIC 2c9b0 0 cv::xphoto::inpaint_fsr(cv::Mat const&, cv::Mat const&, cv::Mat&, int)
PUBLIC 2d420 0 void std::vector<cv::Vec<float, 24>, std::allocator<cv::Vec<float, 24> > >::_M_emplace_back_aux<cv::Vec<float, 24> const&>(cv::Vec<float, 24> const&)
PUBLIC 2d6c0 0 std::_Deque_base<int, std::allocator<int> >::~_Deque_base()
PUBLIC 2d718 0 void std::vector<std::pair<double, int>, std::allocator<std::pair<double, int> > >::_M_emplace_back_aux<std::pair<double, int> >(std::pair<double, int>&&)
PUBLIC 2d810 0 cv::Mat_<int>::Mat_(cv::Size_<int>, int const&)
PUBLIC 2d8b0 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::~vector()
PUBLIC 2d910 0 std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >::~vector()
PUBLIC 2d970 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::vector(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC 2da10 0 std::vector<int, std::allocator<int> >::vector(std::vector<int, std::allocator<int> > const&)
PUBLIC 2da98 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::vector(std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > > const&)
PUBLIC 2dbf0 0 std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >::vector(std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > > const&)
PUBLIC 2dd40 0 void std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::_M_emplace_back_aux<cv::Point_<int> const&>(cv::Point_<int> const&)
PUBLIC 2de40 0 std::_Deque_base<int, std::allocator<int> >::_M_initialize_map(unsigned long)
PUBLIC 2df50 0 void std::__insertion_sort<__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator> >(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, __gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator>)
PUBLIC 2e150 0 void std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > >::_M_emplace_back_aux<cv::Vec<float, 1> const&>(cv::Vec<float, 1> const&)
PUBLIC 2e248 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int const&>(int const&)
PUBLIC 2e330 0 std::vector<int, std::allocator<int> >::push_back(int const&)
PUBLIC 2e360 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::gradientDescent()
PUBLIC 2e500 0 void std::vector<std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > >, std::allocator<std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > > > >::_M_emplace_back_aux<std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > > const&>(std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > > const&)
PUBLIC 2e740 0 void std::vector<std::vector<unsigned char, std::allocator<unsigned char> >, std::allocator<std::vector<unsigned char, std::allocator<unsigned char> > > >::_M_emplace_back_aux<std::vector<unsigned char, std::allocator<unsigned char> > const&>(std::vector<unsigned char, std::allocator<unsigned char> > const&)
PUBLIC 2e950 0 void std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::_M_emplace_back_aux<std::vector<int, std::allocator<int> > const&>(std::vector<int, std::allocator<int> > const&)
PUBLIC 2eb78 0 void std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > >::_M_emplace_back_aux<cv::Vec<float, 2> const&>(cv::Vec<float, 2> const&)
PUBLIC 2ec78 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::gradientDescent()
PUBLIC 2ee18 0 void std::vector<std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > >, std::allocator<std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > > > >::_M_emplace_back_aux<std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > > const&>(std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > > const&)
PUBLIC 2f060 0 void std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_emplace_back_aux<cv::Vec<float, 3> const&>(cv::Vec<float, 3> const&)
PUBLIC 2f1a0 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::gradientDescent()
PUBLIC 2f348 0 void std::vector<std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >, std::allocator<std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > > > >::_M_emplace_back_aux<std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > > const&>(std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > > const&)
PUBLIC 2f5c8 0 void std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > >::_M_emplace_back_aux<cv::Vec<float, 4> const&>(cv::Vec<float, 4> const&)
PUBLIC 2f6d8 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::gradientDescent()
PUBLIC 2f878 0 void std::vector<std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > >, std::allocator<std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > > > >::_M_emplace_back_aux<std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > > const&>(std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > > const&)
PUBLIC 2fad0 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<std::pair<double, int>*, std::vector<std::pair<double, int>, std::allocator<std::pair<double, int> > > >, long, std::pair<double, int>, __gnu_cxx::__ops::_Iter_comp_iter<std::greater<std::pair<double, int> > > >(__gnu_cxx::__normal_iterator<std::pair<double, int>*, std::vector<std::pair<double, int>, std::allocator<std::pair<double, int> > > >, long, long, std::pair<double, int>, __gnu_cxx::__ops::_Iter_comp_iter<std::greater<std::pair<double, int> > >)
PUBLIC 2fc60 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int>(int&&)
PUBLIC 2fd48 0 void std::vector<int, std::allocator<int> >::emplace_back<int>(int&&) [clone .constprop.605]
PUBLIC 2fd78 0 void std::vector<cv::Vec<float, 1>, std::allocator<cv::Vec<float, 1> > >::_M_emplace_back_aux<cv::Vec<float, 1> >(cv::Vec<float, 1>&&)
PUBLIC 2fe70 0 void std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > >::_M_emplace_back_aux<cv::Vec<float, 2> >(cv::Vec<float, 2>&&)
PUBLIC 2ff70 0 void std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_emplace_back_aux<cv::Vec<float, 3> >(cv::Vec<float, 3>&&)
PUBLIC 300b0 0 void std::vector<cv::Vec<float, 4>, std::allocator<cv::Vec<float, 4> > >::_M_emplace_back_aux<cv::Vec<float, 4> >(cv::Vec<float, 4>&&)
PUBLIC 301c0 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, long, int, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator> >(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, long, long, int, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator>)
PUBLIC 30420 0 void std::__introselect<__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, long, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator> >(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, __gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, __gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, long, __gnu_cxx::__ops::_Iter_comp_iter<KDTree<float, 24>::KDTreeComparator>)
PUBLIC 30b00 0 std::deque<int, std::allocator<int> >::_M_reallocate_map(unsigned long, bool)
PUBLIC 30c70 0 KDTree<float, 24>::KDTree(cv::Mat const&, int, int)
PUBLIC 31840 0 dominantTransforms(cv::Mat const&, std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >&, int, int) [clone .constprop.612]
PUBLIC 33580 0 std::vector<int, std::allocator<int> >::operator=(std::vector<int, std::allocator<int> > const&)
PUBLIC 336d0 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::_M_fill_assign(unsigned long, std::vector<int, std::allocator<int> > const&)
PUBLIC 33b20 0 void cv::xphoto::shiftMapInpaint<double, 1u>(cv::Mat const&, cv::Mat const&, cv::Mat&, int, int, cv::Point_<int>) [clone .constprop.595]
PUBLIC 35930 0 void cv::xphoto::shiftMapInpaint<double, 2u>(cv::Mat const&, cv::Mat const&, cv::Mat&, int, int, cv::Point_<int>) [clone .constprop.597]
PUBLIC 37670 0 void cv::xphoto::shiftMapInpaint<double, 4u>(cv::Mat const&, cv::Mat const&, cv::Mat&, int, int, cv::Point_<int>) [clone .constprop.601]
PUBLIC 39400 0 void cv::xphoto::shiftMapInpaint<double, 3u>(cv::Mat const&, cv::Mat const&, cv::Mat&, int, int, cv::Point_<int>) [clone .constprop.599]
PUBLIC 3b240 0 cv::xphoto::inpaint(cv::Mat const&, cv::Mat const&, cv::Mat&, int)
PUBLIC 3b728 0 gcoptimization::GCGraph<float>::addTermWeights(int, float, float)
PUBLIC 3b800 0 gcoptimization::GCGraph<float>::GCGraph(unsigned int, unsigned int)
PUBLIC 3b978 0 gcoptimization::GCGraph<float>::~GCGraph()
PUBLIC 3b9a8 0 std::vector<gcoptimization::GCGraph<float>::Edge, std::allocator<gcoptimization::GCGraph<float>::Edge> >::_M_default_append(unsigned long)
PUBLIC 3bb20 0 void std::vector<gcoptimization::GCGraph<float>::Edge, std::allocator<gcoptimization::GCGraph<float>::Edge> >::_M_emplace_back_aux<gcoptimization::GCGraph<float>::Edge const&>(gcoptimization::GCGraph<float>::Edge const&)
PUBLIC 3bc30 0 gcoptimization::GCGraph<float>::addEdges(int, int, float, float)
PUBLIC 3beb8 0 void std::vector<gcoptimization::GCGraph<float>::Vtx, std::allocator<gcoptimization::GCGraph<float>::Vtx> >::_M_emplace_back_aux<gcoptimization::GCGraph<float>::Vtx const&>(gcoptimization::GCGraph<float>::Vtx const&)
PUBLIC 3bfa8 0 gcoptimization::GCGraph<float>::addVtx()
PUBLIC 3c008 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::setWeights(gcoptimization::GCGraph<float>&, int, int, int, int, int)
PUBLIC 3c218 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::setWeights(gcoptimization::GCGraph<float>&, int, int, int, int, int)
PUBLIC 3c3c0 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::setWeights(gcoptimization::GCGraph<float>&, int, int, int, int, int)
PUBLIC 3c570 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::setWeights(gcoptimization::GCGraph<float>&, int, int, int, int, int)
PUBLIC 3c718 0 void std::vector<gcoptimization::GCGraph<float>::Vtx*, std::allocator<gcoptimization::GCGraph<float>::Vtx*> >::_M_emplace_back_aux<gcoptimization::GCGraph<float>::Vtx* const&>(gcoptimization::GCGraph<float>::Vtx* const&)
PUBLIC 3c800 0 gcoptimization::GCGraph<float>::maxFlow()
PUBLIC 3cfa8 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::singleExpansion(int)
PUBLIC 3d368 0 gcoptimization::Photomontage<cv::Vec<float, 1> >::ParallelExpansion::operator()(cv::Range const&) const
PUBLIC 3d3c8 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::singleExpansion(int)
PUBLIC 3d788 0 gcoptimization::Photomontage<cv::Vec<float, 2> >::ParallelExpansion::operator()(cv::Range const&) const
PUBLIC 3d7e8 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::singleExpansion(int)
PUBLIC 3dba8 0 gcoptimization::Photomontage<cv::Vec<float, 3> >::ParallelExpansion::operator()(cv::Range const&) const
PUBLIC 3dc08 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::singleExpansion(int)
PUBLIC 3e440 0 gcoptimization::Photomontage<cv::Vec<float, 4> >::ParallelExpansion::operator()(cv::Range const&) const
PUBLIC 3e4a0 0 cv::xphoto::LearningBasedWBImpl::getRangeMaxVal() const
PUBLIC 3e4a8 0 cv::xphoto::LearningBasedWBImpl::setRangeMaxVal(int)
PUBLIC 3e4b0 0 cv::xphoto::LearningBasedWBImpl::getSaturationThreshold() const
PUBLIC 3e4b8 0 cv::xphoto::LearningBasedWBImpl::setSaturationThreshold(float)
PUBLIC 3e4c0 0 cv::xphoto::LearningBasedWBImpl::getHistBinNum() const
PUBLIC 3e4c8 0 cv::xphoto::LearningBasedWBImpl::setHistBinNum(int)
PUBLIC 3e4d0 0 std::_Sp_counted_ptr_inplace<cv::xphoto::LearningBasedWBImpl, std::allocator<cv::xphoto::LearningBasedWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 3e4d8 0 std::_Sp_counted_ptr_inplace<cv::xphoto::LearningBasedWBImpl, std::allocator<cv::xphoto::LearningBasedWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 3e4f0 0 std::_Sp_counted_ptr_inplace<cv::xphoto::LearningBasedWBImpl, std::allocator<cv::xphoto::LearningBasedWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 3e4f8 0 std::_Sp_counted_ptr_inplace<cv::xphoto::LearningBasedWBImpl, std::allocator<cv::xphoto::LearningBasedWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 3e500 0 std::_Sp_counted_ptr_inplace<cv::xphoto::LearningBasedWBImpl, std::allocator<cv::xphoto::LearningBasedWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 3e550 0 cv::xphoto::LearningBasedWBImpl::~LearningBasedWBImpl()
PUBLIC 3e7a8 0 cv::xphoto::LearningBasedWBImpl::~LearningBasedWBImpl()
PUBLIC 3e9f8 0 cv::xphoto::LearningBasedWBImpl::preprocessing(cv::Mat&)
PUBLIC 3eec0 0 cv::xphoto::LearningBasedWBImpl::getAverageAndBrightestColorChromaticity(cv::Vec<float, 2>&, cv::Vec<float, 2>&, cv::Mat&)
PUBLIC 3f648 0 void std::vector<cv::xphoto::hist_elem, std::allocator<cv::xphoto::hist_elem> >::_M_emplace_back_aux<cv::xphoto::hist_elem const&>(cv::xphoto::hist_elem const&)
PUBLIC 3f780 0 void std::vector<float, std::allocator<float> >::_M_emplace_back_aux<float const&>(float const&)
PUBLIC 3f868 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<cv::xphoto::hist_elem*, std::vector<cv::xphoto::hist_elem, std::allocator<cv::xphoto::hist_elem> > >, long, cv::xphoto::hist_elem, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<cv::xphoto::hist_elem*, std::vector<cv::xphoto::hist_elem, std::allocator<cv::xphoto::hist_elem> > >, long, long, cv::xphoto::hist_elem, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 3f9d0 0 cv::xphoto::LearningBasedWBImpl::getHistogramBasedFeatures(cv::Vec<float, 2>&, cv::Vec<float, 2>&, cv::Mat&)
PUBLIC 40198 0 cv::xphoto::LearningBasedWBImpl::extractSimpleFeatures(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 405b0 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, long, float, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, long, long, float, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 406a0 0 void std::__introselect<__gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, long, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, __gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, __gnu_cxx::__normal_iterator<float*, std::vector<float, std::allocator<float> > >, long, __gnu_cxx::__ops::_Iter_less_iter)
PUBLIC 40958 0 cv::xphoto::LearningBasedWBImpl::predictIlluminant(std::vector<cv::Vec<float, 2>, std::allocator<cv::Vec<float, 2> > >)
PUBLIC 41180 0 cv::xphoto::LearningBasedWBImpl::balanceWhite(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 41980 0 cv::xphoto::createLearningBasedWB(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 42038 0 cv::Mat::forEach_impl<unsigned char, cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1}>(cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1} const&)::PixelOperationWrapper::~PixelOperationWrapper()
PUBLIC 42050 0 cv::Mat::forEach_impl<unsigned char, cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1}>(cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1} const&)::PixelOperationWrapper::~PixelOperationWrapper()
PUBLIC 42078 0 cv::xphoto::ParallelOilPainting<unsigned char>::~ParallelOilPainting()
PUBLIC 42088 0 cv::xphoto::ParallelOilPainting<unsigned char>::~ParallelOilPainting()
PUBLIC 420b0 0 cv::xphoto::ParallelOilPainting<cv::Vec<unsigned char, 3> >::~ParallelOilPainting()
PUBLIC 420c0 0 cv::xphoto::ParallelOilPainting<cv::Vec<unsigned char, 3> >::~ParallelOilPainting()
PUBLIC 420e8 0 std::vector<cv::Vec<float, 3>, std::allocator<cv::Vec<float, 3> > >::_M_fill_assign(unsigned long, cv::Vec<float, 3> const&) [clone .constprop.75]
PUBLIC 42220 0 cv::Mat::forEach_impl<unsigned char, cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1}>(cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)::{lambda(unsigned char&, int const*)#1} const&)::PixelOperationWrapper::operator()(cv::Range const&) const
PUBLIC 42490 0 cv::xphoto::ParallelOilPainting<cv::Vec<unsigned char, 3> >::operator()(cv::Range const&) const
PUBLIC 42ac8 0 cv::xphoto::ParallelOilPainting<unsigned char>::operator()(cv::Range const&) const
PUBLIC 43010 0 cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int, int)
PUBLIC 43bf0 0 cv::xphoto::oilPainting(cv::_InputArray const&, cv::_OutputArray const&, int, int)
PUBLIC 43bf8 0 cv::xphoto::SimpleWBImpl::getInputMin() const
PUBLIC 43c00 0 cv::xphoto::SimpleWBImpl::setInputMin(float)
PUBLIC 43c08 0 cv::xphoto::SimpleWBImpl::getInputMax() const
PUBLIC 43c10 0 cv::xphoto::SimpleWBImpl::setInputMax(float)
PUBLIC 43c18 0 cv::xphoto::SimpleWBImpl::getOutputMin() const
PUBLIC 43c20 0 cv::xphoto::SimpleWBImpl::setOutputMin(float)
PUBLIC 43c28 0 cv::xphoto::SimpleWBImpl::getOutputMax() const
PUBLIC 43c30 0 cv::xphoto::SimpleWBImpl::setOutputMax(float)
PUBLIC 43c38 0 cv::xphoto::SimpleWBImpl::getP() const
PUBLIC 43c40 0 cv::xphoto::SimpleWBImpl::setP(float)
PUBLIC 43c48 0 std::_Sp_counted_ptr_inplace<cv::xphoto::SimpleWBImpl, std::allocator<cv::xphoto::SimpleWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 43c50 0 std::_Sp_counted_ptr_inplace<cv::xphoto::SimpleWBImpl, std::allocator<cv::xphoto::SimpleWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 43ca0 0 std::_Sp_counted_ptr_inplace<cv::xphoto::SimpleWBImpl, std::allocator<cv::xphoto::SimpleWBImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 43ca8 0 std::_Sp_counted_ptr_inplace<cv::xphoto::SimpleWBImpl, std::allocator<cv::xphoto::SimpleWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 43cb0 0 cv::xphoto::SimpleWBImpl::~SimpleWBImpl()
PUBLIC 43cc8 0 cv::xphoto::SimpleWBImpl::~SimpleWBImpl()
PUBLIC 43cf0 0 std::_Sp_counted_ptr_inplace<cv::xphoto::SimpleWBImpl, std::allocator<cv::xphoto::SimpleWBImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 43d10 0 cv::xphoto::createSimpleWB()
PUBLIC 43df0 0 std::vector<cv::Mat_<unsigned char>, std::allocator<cv::Mat_<unsigned char> > >::~vector()
PUBLIC 43eb0 0 void cv::xphoto::balanceWhiteSimple<unsigned char>(std::vector<cv::Mat_<unsigned char>, std::allocator<cv::Mat_<unsigned char> > >&, cv::Mat&, float, float, float, float, float)
PUBLIC 44b58 0 std::vector<cv::Mat_<short>, std::allocator<cv::Mat_<short> > >::~vector()
PUBLIC 44c20 0 void cv::xphoto::balanceWhiteSimple<short>(std::vector<cv::Mat_<short>, std::allocator<cv::Mat_<short> > >&, cv::Mat&, float, float, float, float, float)
PUBLIC 458c8 0 std::vector<cv::Mat_<int>, std::allocator<cv::Mat_<int> > >::~vector()
PUBLIC 45990 0 void cv::xphoto::balanceWhiteSimple<int>(std::vector<cv::Mat_<int>, std::allocator<cv::Mat_<int> > >&, cv::Mat&, float, float, float, float, float)
PUBLIC 46638 0 std::vector<cv::Mat_<float>, std::allocator<cv::Mat_<float> > >::~vector()
PUBLIC 46700 0 void cv::xphoto::balanceWhiteSimple<float>(std::vector<cv::Mat_<float>, std::allocator<cv::Mat_<float> > >&, cv::Mat&, float, float, float, float, float)
PUBLIC 473c8 0 cv::xphoto::SimpleWBImpl::balanceWhite(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 47a60 0 cv::xphoto::TonemapDurandImpl::getGamma() const
PUBLIC 47a68 0 cv::xphoto::TonemapDurandImpl::setGamma(float)
PUBLIC 47a70 0 cv::xphoto::TonemapDurandImpl::getSaturation() const
PUBLIC 47a78 0 cv::xphoto::TonemapDurandImpl::setSaturation(float)
PUBLIC 47a80 0 cv::xphoto::TonemapDurandImpl::getContrast() const
PUBLIC 47a88 0 cv::xphoto::TonemapDurandImpl::setContrast(float)
PUBLIC 47a90 0 cv::xphoto::TonemapDurandImpl::getSigmaColor() const
PUBLIC 47a98 0 cv::xphoto::TonemapDurandImpl::setSigmaColor(float)
PUBLIC 47aa0 0 cv::xphoto::TonemapDurandImpl::getSigmaSpace() const
PUBLIC 47aa8 0 cv::xphoto::TonemapDurandImpl::setSigmaSpace(float)
PUBLIC 47ab0 0 std::_Sp_counted_ptr_inplace<cv::xphoto::TonemapDurandImpl, std::allocator<cv::xphoto::TonemapDurandImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 47ab8 0 std::_Sp_counted_ptr_inplace<cv::xphoto::TonemapDurandImpl, std::allocator<cv::xphoto::TonemapDurandImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 47b08 0 std::_Sp_counted_ptr_inplace<cv::xphoto::TonemapDurandImpl, std::allocator<cv::xphoto::TonemapDurandImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 47b10 0 std::_Sp_counted_ptr_inplace<cv::xphoto::TonemapDurandImpl, std::allocator<cv::xphoto::TonemapDurandImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 47b18 0 cv::xphoto::TonemapDurandImpl::~TonemapDurandImpl()
PUBLIC 47b60 0 cv::xphoto::TonemapDurandImpl::read(cv::FileNode const&)
PUBLIC 47d08 0 std::_Sp_counted_ptr_inplace<cv::xphoto::TonemapDurandImpl, std::allocator<cv::xphoto::TonemapDurandImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 47d58 0 cv::xphoto::TonemapDurandImpl::~TonemapDurandImpl()
PUBLIC 47da8 0 cv::xphoto::TonemapDurandImpl::write(cv::FileStorage&) const
PUBLIC 48220 0 cv::xphoto::createTonemapDurand(float, float, float, float, float)
PUBLIC 48330 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 483f0 0 cv::xphoto::TonemapDurandImpl::process(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 49c70 0 _fini
STACK CFI INIT 84b0 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8520 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT 85a0 150 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8750 64 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87b8 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8818 8c .cfa: sp 0 + .ra: x30
STACK CFI 8820 .cfa: sp 64 +
STACK CFI 88a0 .cfa: sp 0 +
STACK CFI INIT 88b0 598 .cfa: sp 0 + .ra: x30
STACK CFI 88b8 .cfa: sp 96 + v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 88c4 v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 88cc v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI 8e44 .cfa: sp 0 + v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9
STACK CFI INIT 8e80 344 .cfa: sp 0 + .ra: x30
STACK CFI 8e84 .cfa: sp 560 +
STACK CFI 8ea4 .ra: .cfa -520 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^
STACK CFI 91c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 91c8 b98 .cfa: sp 0 + .ra: x30
STACK CFI 91cc .cfa: sp 2352 +
STACK CFI 91e8 x21: .cfa -2336 + ^ x22: .cfa -2328 + ^
STACK CFI 9204 .ra: .cfa -2272 + ^ x19: .cfa -2352 + ^ x20: .cfa -2344 + ^ x23: .cfa -2320 + ^ x24: .cfa -2312 + ^ x25: .cfa -2304 + ^ x26: .cfa -2296 + ^ x27: .cfa -2288 + ^ x28: .cfa -2280 + ^
STACK CFI 9d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 9d60 250 .cfa: sp 0 + .ra: x30
STACK CFI 9d68 .cfa: sp 8640 +
STACK CFI 9d70 x23: .cfa -8608 + ^ x24: .cfa -8600 + ^
STACK CFI 9d80 x19: .cfa -8640 + ^ x20: .cfa -8632 + ^ x21: .cfa -8624 + ^ x22: .cfa -8616 + ^
STACK CFI 9d94 .ra: .cfa -8584 + ^ x25: .cfa -8592 + ^
STACK CFI 9fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 9fb0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 9fb8 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 9fd8 .ra: .cfa -264 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^
STACK CFI a470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI INIT a478 294 .cfa: sp 0 + .ra: x30
STACK CFI a47c .cfa: sp 1072 +
STACK CFI a49c .ra: .cfa -1032 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^
STACK CFI a708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT a710 bd8 .cfa: sp 0 + .ra: x30
STACK CFI a718 .cfa: sp 4720 +
STACK CFI a724 x21: .cfa -4704 + ^ x22: .cfa -4696 + ^
STACK CFI a738 x19: .cfa -4720 + ^ x20: .cfa -4712 + ^
STACK CFI a750 .ra: .cfa -4640 + ^ x23: .cfa -4688 + ^ x24: .cfa -4680 + ^ x25: .cfa -4672 + ^ x26: .cfa -4664 + ^ x27: .cfa -4656 + ^ x28: .cfa -4648 + ^
STACK CFI b2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT b2e8 248 .cfa: sp 0 + .ra: x30
STACK CFI b2f0 .cfa: sp 17216 +
STACK CFI b2f8 x23: .cfa -17184 + ^ x24: .cfa -17176 + ^
STACK CFI b308 x19: .cfa -17216 + ^ x20: .cfa -17208 + ^ x21: .cfa -17200 + ^ x22: .cfa -17192 + ^
STACK CFI b31c .ra: .cfa -17160 + ^ x25: .cfa -17168 + ^
STACK CFI b52c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT b530 26c .cfa: sp 0 + .ra: x30
STACK CFI b534 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b54c .ra: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI b798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT b7a0 214 .cfa: sp 0 + .ra: x30
STACK CFI b7a4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b7bc .ra: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI b9b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT b9b8 f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT bab0 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT bb90 74 .cfa: sp 0 + .ra: x30
STACK CFI INIT bc08 70 .cfa: sp 0 + .ra: x30
STACK CFI INIT bc78 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT bcb0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT bce8 224 .cfa: sp 0 + .ra: x30
STACK CFI bcec .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI bd00 .ra: .cfa -64 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI bf08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT bf10 20c .cfa: sp 0 + .ra: x30
STACK CFI bf14 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI bf28 .ra: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT c120 d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c1f8 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT c2d8 68 .cfa: sp 0 + .ra: x30
STACK CFI INIT c340 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT c3b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3e0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT c420 11b0 .cfa: sp 0 + .ra: x30
STACK CFI c424 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI c428 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI c438 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI c450 .ra: .cfa -112 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI cf44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI cf48 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT d5e0 d40 .cfa: sp 0 + .ra: x30
STACK CFI d5e4 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI d5e8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI d5f8 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI d610 .ra: .cfa -96 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI de14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI de18 .cfa: sp 176 + .ra: .cfa -96 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT e330 1dc .cfa: sp 0 + .ra: x30
STACK CFI e334 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e33c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e344 .ra: .cfa -16 + ^
STACK CFI e4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI e4ec .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT e510 200 .cfa: sp 0 + .ra: x30
STACK CFI e514 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e518 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e528 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e6f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI e6f4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT e710 1b8 .cfa: sp 0 + .ra: x30
STACK CFI e714 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e71c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e724 .ra: .cfa -16 + ^
STACK CFI e8a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI e8a4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT e8c8 240 .cfa: sp 0 + .ra: x30
STACK CFI e8cc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e8d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e8dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e8e4 .ra: .cfa -16 + ^
STACK CFI eaa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI eaac .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI eaf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI eaf4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT eb08 dc .cfa: sp 0 + .ra: x30
STACK CFI eb0c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI eb10 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI eb18 .ra: .cfa -32 + ^
STACK CFI eb64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI eb68 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ebac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI ebb0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ebd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI ebd8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT ebe8 2e0 .cfa: sp 0 + .ra: x30
STACK CFI ebf0 .cfa: sp 17456 +
STACK CFI ec04 x19: .cfa -17456 + ^ x20: .cfa -17448 + ^
STACK CFI ec14 .ra: .cfa -17416 + ^ x21: .cfa -17440 + ^ x22: .cfa -17432 + ^ x23: .cfa -17424 + ^
STACK CFI eec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT eec8 e98 .cfa: sp 0 + .ra: x30
STACK CFI eed0 .cfa: sp 4672 +
STACK CFI eee4 x19: .cfa -4672 + ^ x20: .cfa -4664 + ^
STACK CFI eeec x21: .cfa -4656 + ^ x22: .cfa -4648 + ^
STACK CFI eefc .ra: .cfa -4624 + ^ x23: .cfa -4640 + ^ x24: .cfa -4632 + ^
STACK CFI fd5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT fd60 7c0 .cfa: sp 0 + .ra: x30
STACK CFI fd64 .cfa: sp 1344 +
STACK CFI fd78 x19: .cfa -1344 + ^ x20: .cfa -1336 + ^ x21: .cfa -1328 + ^ x22: .cfa -1320 + ^
STACK CFI fd90 .ra: .cfa -1264 + ^ x23: .cfa -1312 + ^ x24: .cfa -1304 + ^ x25: .cfa -1296 + ^ x26: .cfa -1288 + ^ x27: .cfa -1280 + ^ x28: .cfa -1272 + ^
STACK CFI 1051c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 10520 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 10528 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 1052c .ra: .cfa -272 + ^
STACK CFI 10ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 10ac8 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 10ad0 .cfa: sp 80 + .ra: .cfa -80 + ^
STACK CFI 10c68 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 10c70 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 10c78 .cfa: sp 8880 +
STACK CFI 10c8c x19: .cfa -8880 + ^ x20: .cfa -8872 + ^
STACK CFI 10c9c .ra: .cfa -8840 + ^ x21: .cfa -8864 + ^ x22: .cfa -8856 + ^ x23: .cfa -8848 + ^
STACK CFI 10f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 10f3c .cfa: sp 8880 + .ra: .cfa -8840 + ^ x19: .cfa -8880 + ^ x20: .cfa -8872 + ^ x21: .cfa -8864 + ^ x22: .cfa -8856 + ^ x23: .cfa -8848 + ^
STACK CFI INIT 10f58 e64 .cfa: sp 0 + .ra: x30
STACK CFI 10f5c .cfa: sp 2384 +
STACK CFI 10f74 .ra: .cfa -2376 + ^ x19: .cfa -2384 + ^
STACK CFI 11db8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 11dc0 774 .cfa: sp 0 + .ra: x30
STACK CFI 11dc4 .cfa: sp 720 +
STACK CFI 11dd0 x19: .cfa -720 + ^ x20: .cfa -712 + ^
STACK CFI 11dd8 x21: .cfa -704 + ^ x22: .cfa -696 + ^
STACK CFI 11df0 .ra: .cfa -640 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^ x26: .cfa -664 + ^ x27: .cfa -656 + ^ x28: .cfa -648 + ^
STACK CFI 12530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 12538 328 .cfa: sp 0 + .ra: x30
STACK CFI 1253c .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 12544 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 1254c .ra: .cfa -144 + ^
STACK CFI 1285c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 12860 264 .cfa: sp 0 + .ra: x30
STACK CFI 12864 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12ac0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12ac8 154 .cfa: sp 0 + .ra: x30
STACK CFI 12ad4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 12aec .ra: .cfa -40 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 12c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 12c0c .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 12c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI INIT 12c20 510 .cfa: sp 0 + .ra: x30
STACK CFI 12c24 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 12c30 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 12c4c .ra: .cfa -160 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 130f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 130f8 .cfa: sp 240 + .ra: .cfa -160 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT 13130 654 .cfa: sp 0 + .ra: x30
STACK CFI 13134 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 13138 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 13148 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 1315c .ra: .cfa -208 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 13708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1370c .cfa: sp 288 + .ra: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 13788 14c .cfa: sp 0 + .ra: x30
STACK CFI 1378c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1379c .ra: .cfa -16 + ^
STACK CFI 138b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 138b8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 138d8 14c .cfa: sp 0 + .ra: x30
STACK CFI 138dc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 138ec .ra: .cfa -16 + ^
STACK CFI 13a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13a08 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13a28 cc .cfa: sp 0 + .ra: x30
STACK CFI 13a2c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13a3c .ra: .cfa -16 + ^
STACK CFI 13ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13ae8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13af8 cc .cfa: sp 0 + .ra: x30
STACK CFI 13afc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13b0c .ra: .cfa -16 + ^
STACK CFI 13bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13bb8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13bc8 cc .cfa: sp 0 + .ra: x30
STACK CFI 13bcc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13bdc .ra: .cfa -16 + ^
STACK CFI 13c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13c88 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13c98 14c .cfa: sp 0 + .ra: x30
STACK CFI 13c9c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13cac .ra: .cfa -16 + ^
STACK CFI 13dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13dc8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13de8 c4 .cfa: sp 0 + .ra: x30
STACK CFI 13dec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13dfc .ra: .cfa -16 + ^
STACK CFI 13e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13ea0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13eb0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 13eb4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13ec4 .ra: .cfa -16 + ^
STACK CFI 13f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13f68 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13f78 c4 .cfa: sp 0 + .ra: x30
STACK CFI 13f7c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13f8c .ra: .cfa -16 + ^
STACK CFI 14028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14030 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 14040 144 .cfa: sp 0 + .ra: x30
STACK CFI 14044 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14054 .ra: .cfa -16 + ^
STACK CFI 14164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14168 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 14188 144 .cfa: sp 0 + .ra: x30
STACK CFI 1418c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1419c .ra: .cfa -16 + ^
STACK CFI 142ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 142b0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 142d0 144 .cfa: sp 0 + .ra: x30
STACK CFI 142d4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 142e4 .ra: .cfa -16 + ^
STACK CFI 143f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 143f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 14418 90 .cfa: sp 0 + .ra: x30
STACK CFI 1441c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 14490 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 14498 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 144a4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 144a8 1ac .cfa: sp 0 + .ra: x30
STACK CFI 144ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 144b8 .ra: .cfa -16 + ^
STACK CFI 14614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14618 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 14660 8f4 .cfa: sp 0 + .ra: x30
STACK CFI 14664 .cfa: sp 784 +
STACK CFI 14668 x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI 14670 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 14684 .ra: .cfa -712 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x27: .cfa -720 + ^
STACK CFI 14694 v10: .cfa -688 + ^ v11: .cfa -680 + ^ v14: .cfa -656 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^
STACK CFI 147b8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 147bc .cfa: sp 784 + .ra: .cfa -712 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ v14: .cfa -656 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^
STACK CFI INIT 14f88 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 14fec .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14ffc .ra: .cfa -48 + ^
STACK CFI 15028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15124 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 15148 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 151ac .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 151bc .ra: .cfa -48 + ^
STACK CFI 151e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 152e4 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 15310 7b0 .cfa: sp 0 + .ra: x30
STACK CFI 15314 .cfa: sp 624 +
STACK CFI 1531c x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 15324 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 15334 .ra: .cfa -592 + ^
STACK CFI 15a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 15a50 .cfa: sp 624 + .ra: .cfa -592 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI INIT 15ad0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 15ad8 .cfa: sp 720 +
STACK CFI 15ae0 x19: .cfa -720 + ^ x20: .cfa -712 + ^
STACK CFI 15b1c .ra: .cfa -664 + ^ v8: .cfa -656 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^
STACK CFI 15f38 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 15f3c .cfa: sp 720 + .ra: .cfa -664 + ^ v8: .cfa -656 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^
STACK CFI INIT 15fb0 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 15fb4 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 15fb8 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 15fc0 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 15fd4 .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 162bc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 162c0 .cfa: sp 256 + .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 163b0 7b0 .cfa: sp 0 + .ra: x30
STACK CFI 163b4 .cfa: sp 624 +
STACK CFI 163bc x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 163c4 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 163d4 .ra: .cfa -592 + ^
STACK CFI 16aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 16af0 .cfa: sp 624 + .ra: .cfa -592 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI INIT 16b70 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 16b78 .cfa: sp 720 +
STACK CFI 16b80 x19: .cfa -720 + ^ x20: .cfa -712 + ^
STACK CFI 16bbc .ra: .cfa -664 + ^ v8: .cfa -656 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^
STACK CFI 16fd8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 16fdc .cfa: sp 720 + .ra: .cfa -664 + ^ v8: .cfa -656 + ^ x19: .cfa -720 + ^ x20: .cfa -712 + ^ x21: .cfa -704 + ^ x22: .cfa -696 + ^ x23: .cfa -688 + ^ x24: .cfa -680 + ^ x25: .cfa -672 + ^
STACK CFI INIT 17050 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 17054 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 17058 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 17060 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 17074 .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 17334 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17338 .cfa: sp 256 + .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 17410 1c4c .cfa: sp 0 + .ra: x30
STACK CFI 17414 .cfa: sp 1920 +
STACK CFI 17418 v8: .cfa -1824 + ^ v9: .cfa -1816 + ^
STACK CFI 17420 x21: .cfa -1904 + ^ x22: .cfa -1896 + ^
STACK CFI 17430 x23: .cfa -1888 + ^ x24: .cfa -1880 + ^ x25: .cfa -1872 + ^ x26: .cfa -1864 + ^
STACK CFI 17444 x19: .cfa -1920 + ^ x20: .cfa -1912 + ^ x27: .cfa -1856 + ^ x28: .cfa -1848 + ^
STACK CFI 17458 .ra: .cfa -1840 + ^ v10: .cfa -1832 + ^
STACK CFI 17784 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17788 .cfa: sp 1920 + .ra: .cfa -1840 + ^ v10: .cfa -1832 + ^ v8: .cfa -1824 + ^ v9: .cfa -1816 + ^ x19: .cfa -1920 + ^ x20: .cfa -1912 + ^ x21: .cfa -1904 + ^ x22: .cfa -1896 + ^ x23: .cfa -1888 + ^ x24: .cfa -1880 + ^ x25: .cfa -1872 + ^ x26: .cfa -1864 + ^ x27: .cfa -1856 + ^ x28: .cfa -1848 + ^
STACK CFI INIT 19080 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 19084 .cfa: sp 192 +
STACK CFI 1908c .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^
STACK CFI 19198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 191a0 .cfa: sp 192 + .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^
STACK CFI INIT 19240 19c .cfa: sp 0 + .ra: x30
STACK CFI 19248 .cfa: sp 32 +
STACK CFI 19374 .cfa: sp 0 +
STACK CFI 19378 .cfa: sp 32 +
STACK CFI INIT 193e0 27c .cfa: sp 0 + .ra: x30
STACK CFI 193e4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 193e8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 193f8 .ra: .cfa -72 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^
STACK CFI 195e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 195e8 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI INIT 19660 1da0 .cfa: sp 0 + .ra: x30
STACK CFI 19664 .cfa: sp 528 +
STACK CFI 19668 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 19680 .ra: .cfa -448 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 1b36c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1b370 .cfa: sp 528 + .ra: .cfa -448 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI INIT 1b400 1dac .cfa: sp 0 + .ra: x30
STACK CFI 1b404 .cfa: sp 512 +
STACK CFI 1b408 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 1b420 .ra: .cfa -432 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 1cc58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1cc5c .cfa: sp 512 + .ra: .cfa -432 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI INIT 1d1b0 13c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d2f0 200 .cfa: sp 0 + .ra: x30
STACK CFI 1d2f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d2f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d300 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1d474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1d478 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1d4f0 1910 .cfa: sp 0 + .ra: x30
STACK CFI 1d4f4 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1d50c .ra: .cfa -256 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 1eda8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1edac .cfa: sp 336 + .ra: .cfa -256 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 1ee00 196c .cfa: sp 0 + .ra: x30
STACK CFI 1ee04 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 1ee1c .ra: .cfa -256 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 204b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 204b8 .cfa: sp 336 + .ra: .cfa -256 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 20770 197c .cfa: sp 0 + .ra: x30
STACK CFI 20774 .cfa: sp 336 + x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 2078c .ra: .cfa -256 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 21e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21e40 .cfa: sp 336 + .ra: .cfa -256 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 220f0 1910 .cfa: sp 0 + .ra: x30
STACK CFI 220f4 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 2210c .ra: .cfa -272 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 239a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 239ac .cfa: sp 352 + .ra: .cfa -272 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 23a00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23a10 24 .cfa: sp 0 + .ra: x30
STACK CFI 23a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 23a30 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7f50 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7f54 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7f60 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7fe4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 23a40 2ec .cfa: sp 0 + .ra: x30
STACK CFI 23a44 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 23a58 .ra: .cfa -272 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 23d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 23d04 .cfa: sp 352 + .ra: .cfa -272 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 23d40 bc .cfa: sp 0 + .ra: x30
STACK CFI 23d44 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23d48 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 23dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 23df0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 23df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 23e00 334 .cfa: sp 0 + .ra: x30
STACK CFI 23e04 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23e10 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23e20 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 24078 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 24150 a08 .cfa: sp 0 + .ra: x30
STACK CFI 24154 .cfa: sp 960 +
STACK CFI 24158 x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 24174 .ra: .cfa -880 + ^ v8: .cfa -872 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 249e4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 249e8 .cfa: sp 960 + .ra: .cfa -880 + ^ v8: .cfa -872 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI INIT 24b70 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 24b74 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 24b8c .ra: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^
STACK CFI 24ed8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 24edc .cfa: sp 288 + .ra: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^
STACK CFI INIT 24f70 288 .cfa: sp 0 + .ra: x30
STACK CFI 24f74 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 24f7c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 24f8c .ra: .cfa -152 + ^ v8: .cfa -144 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI 25140 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 25148 .cfa: sp 208 + .ra: .cfa -152 + ^ v8: .cfa -144 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI INIT 8310 30 .cfa: sp 0 + .ra: x30
STACK CFI 8314 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8330 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 25210 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25218 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25228 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25238 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25248 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25250 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25258 50 .cfa: sp 0 + .ra: x30
STACK CFI 2525c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25268 .ra: .cfa -16 + ^
STACK CFI 252a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 252a8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 252c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 252e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 252e8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7ff0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7ff4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8000 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 8080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8084 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 25308 260 .cfa: sp 0 + .ra: x30
STACK CFI 25310 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25320 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2532c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 25550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 25554 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 25570 25c .cfa: sp 0 + .ra: x30
STACK CFI 25578 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 25584 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2558c .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 257b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 257b8 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI INIT 257d0 c94 .cfa: sp 0 + .ra: x30
STACK CFI 257d4 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 257dc v8: .cfa -224 + ^ v9: .cfa -216 + ^
STACK CFI 257e8 v10: .cfa -208 + ^ v11: .cfa -200 + ^
STACK CFI 257f0 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 257fc .ra: .cfa -232 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI 25a94 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 25a98 .cfa: sp 288 + .ra: .cfa -232 + ^ v10: .cfa -208 + ^ v11: .cfa -200 + ^ v8: .cfa -224 + ^ v9: .cfa -216 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^
STACK CFI INIT 26470 3cc .cfa: sp 0 + .ra: x30
STACK CFI 26474 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2647c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2648c .ra: .cfa -160 + ^
STACK CFI 2662c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26630 .cfa: sp 192 + .ra: .cfa -160 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI INIT 26848 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2684c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26854 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2685c .ra: .cfa -16 + ^
STACK CFI 268c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 268d0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 268f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 268fc .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 26918 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26948 54 .cfa: sp 0 + .ra: x30
STACK CFI 2694c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2695c .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x21: .cfa -32 + ^
STACK CFI 26998 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI INIT 269a0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 269b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 269b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 269d0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 269d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 269e8 24 .cfa: sp 0 + .ra: x30
STACK CFI 269ec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 26a08 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 26a10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a20 24 .cfa: sp 0 + .ra: x30
STACK CFI 26a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 26a40 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 26a48 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26a58 24 .cfa: sp 0 + .ra: x30
STACK CFI 26a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 26a78 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 26a80 dc .cfa: sp 0 + .ra: x30
STACK CFI 26a84 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 26a88 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26a90 .ra: .cfa -32 + ^
STACK CFI 26adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26ae0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26b28 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26b50 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 26b60 e0 .cfa: sp 0 + .ra: x30
STACK CFI 26b8c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 26b9c .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 26c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 26c08 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 26c40 84 .cfa: sp 0 + .ra: x30
STACK CFI 26c48 .cfa: sp 96 +
STACK CFI 26cb0 .cfa: sp 0 +
STACK CFI INIT 26cc8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 26cd0 .cfa: sp 96 +
STACK CFI 26d60 .cfa: sp 0 +
STACK CFI INIT 26d70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26db0 74 .cfa: sp 0 + .ra: x30
STACK CFI 26db4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26dbc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 26e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 26e08 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 26e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 26e20 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 26e28 7c .cfa: sp 0 + .ra: x30
STACK CFI 26e2c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26e3c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 26ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 26ea8 7c .cfa: sp 0 + .ra: x30
STACK CFI 26eac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26ebc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 26f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 26f28 7c .cfa: sp 0 + .ra: x30
STACK CFI 26f2c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26f3c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 26fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 26fa8 7c .cfa: sp 0 + .ra: x30
STACK CFI 26fac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26fbc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 27020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 27028 84 .cfa: sp 0 + .ra: x30
STACK CFI 2702c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2703c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 27098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 270a0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 270a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 270b0 84 .cfa: sp 0 + .ra: x30
STACK CFI 270b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 270c4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 27120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 27128 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 27130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 27138 84 .cfa: sp 0 + .ra: x30
STACK CFI 2713c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2714c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 271a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 271b0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 271b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 271c0 84 .cfa: sp 0 + .ra: x30
STACK CFI 271c4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 271d4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 27230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 27238 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 27240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 27248 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272d0 180 .cfa: sp 0 + .ra: x30
STACK CFI 272d8 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 272e4 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 272fc .ra: .cfa -272 + ^ v8: .cfa -264 + ^
STACK CFI 27414 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 27418 .cfa: sp 304 + .ra: .cfa -272 + ^ v8: .cfa -264 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 27438 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2743c .cfa: sp 304 + .ra: .cfa -272 + ^ v8: .cfa -264 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI INIT 27480 60 .cfa: sp 0 + .ra: x30
STACK CFI 274a0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 274b4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 274e0 80 .cfa: sp 0 + .ra: x30
STACK CFI 274f0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 274fc .ra: .cfa -16 + ^
STACK CFI 27548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2754c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 27570 18f8 .cfa: sp 0 + .ra: x30
STACK CFI 27574 .cfa: sp 2336 +
STACK CFI 2757c x27: .cfa -2272 + ^ x28: .cfa -2264 + ^
STACK CFI 2758c x19: .cfa -2336 + ^ x20: .cfa -2328 + ^ x21: .cfa -2320 + ^ x22: .cfa -2312 + ^
STACK CFI 2759c x23: .cfa -2304 + ^ x24: .cfa -2296 + ^ x25: .cfa -2288 + ^ x26: .cfa -2280 + ^
STACK CFI 275ac .ra: .cfa -2256 + ^ v10: .cfa -2224 + ^ v11: .cfa -2216 + ^ v8: .cfa -2240 + ^ v9: .cfa -2232 + ^
STACK CFI 275b4 v14: .cfa -2192 + ^ v15: .cfa -2184 + ^
STACK CFI 275bc v12: .cfa -2208 + ^ v13: .cfa -2200 + ^
STACK CFI 287f4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 287f8 .cfa: sp 2336 + .ra: .cfa -2256 + ^ v10: .cfa -2224 + ^ v11: .cfa -2216 + ^ v12: .cfa -2208 + ^ v13: .cfa -2200 + ^ v14: .cfa -2192 + ^ v15: .cfa -2184 + ^ v8: .cfa -2240 + ^ v9: .cfa -2232 + ^ x19: .cfa -2336 + ^ x20: .cfa -2328 + ^ x21: .cfa -2320 + ^ x22: .cfa -2312 + ^ x23: .cfa -2304 + ^ x24: .cfa -2296 + ^ x25: .cfa -2288 + ^ x26: .cfa -2280 + ^ x27: .cfa -2272 + ^ x28: .cfa -2264 + ^
STACK CFI INIT 28e98 200 .cfa: sp 0 + .ra: x30
STACK CFI 28e9c .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 28ea8 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 28eb4 .ra: .cfa -392 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x25: .cfa -400 + ^
STACK CFI 29094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 29098 244 .cfa: sp 0 + .ra: x30
STACK CFI 2909c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 290a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 290ac .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 29170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 29178 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 292e0 344 .cfa: sp 0 + .ra: x30
STACK CFI 292e4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 292f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29300 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2955c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 29560 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 29628 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2962c .cfa: sp 336 + v8: .cfa -336 + ^ v9: .cfa -328 + ^
STACK CFI 29638 v10: .cfa -320 + ^ v11: .cfa -312 + ^ v12: .cfa -304 + ^ v13: .cfa -296 + ^
STACK CFI 29800 .cfa: sp 0 + v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI 29804 .cfa: sp 336 + v10: .cfa -320 + ^ v11: .cfa -312 + ^ v12: .cfa -304 + ^ v13: .cfa -296 + ^ v8: .cfa -336 + ^ v9: .cfa -328 + ^
STACK CFI INIT 29818 134 .cfa: sp 0 + .ra: x30
STACK CFI 2984c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2985c .ra: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 29918 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 29950 150 .cfa: sp 0 + .ra: x30
STACK CFI 2999c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 299a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 299b4 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 29a80 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 29aa0 110 .cfa: sp 0 + .ra: x30
STACK CFI 29aa4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29aa8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29ab0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29ab8 .ra: .cfa -16 + ^
STACK CFI 29b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 29b80 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 29bb0 110 .cfa: sp 0 + .ra: x30
STACK CFI 29bb4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29bb8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29bc0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29bc8 .ra: .cfa -16 + ^
STACK CFI 29c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 29c90 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 29cc0 2c98 .cfa: sp 0 + .ra: x30
STACK CFI 29cd0 .cfa: sp 3088 +
STACK CFI 29cec x23: .cfa -3056 + ^ x24: .cfa -3048 + ^
STACK CFI 29cfc x19: .cfa -3088 + ^ x20: .cfa -3080 + ^ x21: .cfa -3072 + ^ x22: .cfa -3064 + ^
STACK CFI 29d28 .ra: .cfa -3008 + ^ v10: .cfa -2976 + ^ v11: .cfa -2968 + ^ v12: .cfa -2960 + ^ v13: .cfa -2952 + ^ v14: .cfa -2944 + ^ v15: .cfa -2936 + ^ v8: .cfa -2992 + ^ v9: .cfa -2984 + ^ x25: .cfa -3040 + ^ x26: .cfa -3032 + ^ x27: .cfa -3024 + ^ x28: .cfa -3016 + ^
STACK CFI 2c0e8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2c0f0 .cfa: sp 3088 + .ra: .cfa -3008 + ^ v10: .cfa -2976 + ^ v11: .cfa -2968 + ^ v12: .cfa -2960 + ^ v13: .cfa -2952 + ^ v14: .cfa -2944 + ^ v15: .cfa -2936 + ^ v8: .cfa -2992 + ^ v9: .cfa -2984 + ^ x19: .cfa -3088 + ^ x20: .cfa -3080 + ^ x21: .cfa -3072 + ^ x22: .cfa -3064 + ^ x23: .cfa -3056 + ^ x24: .cfa -3048 + ^ x25: .cfa -3040 + ^ x26: .cfa -3032 + ^ x27: .cfa -3024 + ^ x28: .cfa -3016 + ^
STACK CFI INIT 2c9b0 a30 .cfa: sp 0 + .ra: x30
STACK CFI 2c9b4 .cfa: sp 1328 +
STACK CFI 2c9b8 x23: .cfa -1296 + ^ x24: .cfa -1288 + ^
STACK CFI 2c9dc .ra: .cfa -1248 + ^ v8: .cfa -1240 + ^ x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^ x27: .cfa -1264 + ^ x28: .cfa -1256 + ^
STACK CFI 2cb78 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2cb80 .cfa: sp 1328 + .ra: .cfa -1248 + ^ v8: .cfa -1240 + ^ x19: .cfa -1328 + ^ x20: .cfa -1320 + ^ x21: .cfa -1312 + ^ x22: .cfa -1304 + ^ x23: .cfa -1296 + ^ x24: .cfa -1288 + ^ x25: .cfa -1280 + ^ x26: .cfa -1272 + ^ x27: .cfa -1264 + ^ x28: .cfa -1256 + ^
STACK CFI INIT 2d420 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 2d424 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d438 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2d620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2d628 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2d6c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 2d6c4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d6c8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2d704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2d708 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2d710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2d718 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2d71c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d724 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2d72c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d7d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2d7e0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2d810 8c .cfa: sp 0 + .ra: x30
STACK CFI 2d814 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d824 .ra: .cfa -48 + ^
STACK CFI 2d884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2d888 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 2d8b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2d8b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d8b8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2d8f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2d900 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2d908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2d910 5c .cfa: sp 0 + .ra: x30
STACK CFI 2d914 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d918 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2d958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2d960 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2d968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2d970 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2d974 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d97c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2da08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2da0c .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 2da10 88 .cfa: sp 0 + .ra: x30
STACK CFI 2da14 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2da20 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2da90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2da94 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 2da98 158 .cfa: sp 0 + .ra: x30
STACK CFI 2da9c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2daa0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2daa8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2dab0 .ra: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 2dba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2dba4 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 2dbf0 150 .cfa: sp 0 + .ra: x30
STACK CFI 2dbf4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2dbf8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2dc00 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2dc08 .ra: .cfa -16 + ^
STACK CFI 2dce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2dce8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2dd40 100 .cfa: sp 0 + .ra: x30
STACK CFI 2dd44 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2dd4c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2dd54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2de08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2de10 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2de40 10c .cfa: sp 0 + .ra: x30
STACK CFI 2de44 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2de4c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2de5c .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2def8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2defc .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 2df50 1fc .cfa: sp 0 + .ra: x30
STACK CFI 2df5c .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 2df60 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 2df78 .ra: .cfa -240 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 2e07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2e080 .cfa: sp 320 + .ra: .cfa -240 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x23: .cfa -288 + ^ x24: .cfa -280 + ^ x25: .cfa -272 + ^ x26: .cfa -264 + ^ x27: .cfa -256 + ^ x28: .cfa -248 + ^
STACK CFI 2e148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 2e150 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2e154 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e15c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2e164 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2e218 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2e248 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2e24c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e254 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e260 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e2e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2e2e8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2e330 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e360 194 .cfa: sp 0 + .ra: x30
STACK CFI 2e364 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2e36c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2e378 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2e380 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2e390 .ra: .cfa -48 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2e4ec .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2e4f0 .cfa: sp 112 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 2e500 23c .cfa: sp 0 + .ra: x30
STACK CFI 2e504 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e510 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e518 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e520 .ra: .cfa -16 + ^
STACK CFI 2e6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2e6c0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2e740 210 .cfa: sp 0 + .ra: x30
STACK CFI 2e744 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e750 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e758 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e760 .ra: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 2e8bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2e8c0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 2e950 228 .cfa: sp 0 + .ra: x30
STACK CFI 2e954 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2e960 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e968 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e970 .ra: .cfa -16 + ^
STACK CFI 2eaec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2eaf0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2eb78 100 .cfa: sp 0 + .ra: x30
STACK CFI 2eb7c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2eb84 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2eb8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ec40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2ec48 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2ec78 194 .cfa: sp 0 + .ra: x30
STACK CFI 2ec7c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2ec84 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2ec90 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2ec98 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2eca8 .ra: .cfa -48 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2ee04 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2ee08 .cfa: sp 112 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 2ee18 244 .cfa: sp 0 + .ra: x30
STACK CFI 2ee1c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2ee28 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2ee30 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2ee38 .ra: .cfa -16 + ^
STACK CFI 2efdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2efe0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2f060 140 .cfa: sp 0 + .ra: x30
STACK CFI 2f064 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f070 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f078 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2f164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f168 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2f1a0 19c .cfa: sp 0 + .ra: x30
STACK CFI 2f1a4 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2f1b0 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2f1bc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2f1c8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f1d4 .ra: .cfa -40 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI 2f334 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2f338 .cfa: sp 112 + .ra: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^
STACK CFI INIT 2f348 27c .cfa: sp 0 + .ra: x30
STACK CFI 2f34c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f358 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f368 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2f548 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2f5c8 110 .cfa: sp 0 + .ra: x30
STACK CFI 2f5cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f5d4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2f5dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f6a8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2f6d8 194 .cfa: sp 0 + .ra: x30
STACK CFI 2f6dc .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2f6e4 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 2f6f0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2f6f8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2f708 .ra: .cfa -48 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2f864 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2f868 .cfa: sp 112 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 2f878 254 .cfa: sp 0 + .ra: x30
STACK CFI 2f87c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2f888 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f890 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f898 .ra: .cfa -16 + ^
STACK CFI 2fa4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2fa50 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2fad0 18c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fc60 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2fc64 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2fc6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2fc78 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2fcf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2fd00 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 2fd48 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd78 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2fd7c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2fd84 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2fd8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2fe38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2fe40 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2fe70 100 .cfa: sp 0 + .ra: x30
STACK CFI 2fe74 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2fe7c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 2fe84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ff38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2ff40 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 2ff70 140 .cfa: sp 0 + .ra: x30
STACK CFI 2ff74 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ff80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ff88 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 30074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 30078 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 300b0 110 .cfa: sp 0 + .ra: x30
STACK CFI 300b4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 300bc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 300c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 30190 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 301c0 260 .cfa: sp 0 + .ra: x30
STACK CFI 301c4 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 301d0 x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 301e4 .ra: .cfa -208 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 302f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 302f8 .cfa: sp 272 + .ra: .cfa -208 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 303f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 303f8 .cfa: sp 272 + .ra: .cfa -208 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI INIT 30420 6dc .cfa: sp 0 + .ra: x30
STACK CFI 30424 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 30428 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 30438 x21: .cfa -480 + ^ x22: .cfa -472 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 30448 .ra: .cfa -416 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 30ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 30ab8 .cfa: sp 496 + .ra: .cfa -416 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 30af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 30b00 170 .cfa: sp 0 + .ra: x30
STACK CFI 30b04 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30b0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 30b14 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 30b1c .ra: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 30c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 30c30 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 30c70 bc8 .cfa: sp 0 + .ra: x30
STACK CFI 30c74 .cfa: sp 560 +
STACK CFI 30c7c x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 30c8c x19: .cfa -560 + ^ x20: .cfa -552 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 30ca4 .ra: .cfa -480 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 31288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 31290 .cfa: sp 560 + .ra: .cfa -480 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI INIT 31840 1d0c .cfa: sp 0 + .ra: x30
STACK CFI 31844 .cfa: sp 1568 +
STACK CFI 31864 x19: .cfa -1568 + ^ x20: .cfa -1560 + ^
STACK CFI 31898 .ra: .cfa -1488 + ^ v8: .cfa -1480 + ^ x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI 333d8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 333dc .cfa: sp 1568 + .ra: .cfa -1488 + ^ v8: .cfa -1480 + ^ x19: .cfa -1568 + ^ x20: .cfa -1560 + ^ x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI INIT 33580 14c .cfa: sp 0 + .ra: x30
STACK CFI 33584 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 33598 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 33604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 33608 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 336d0 448 .cfa: sp 0 + .ra: x30
STACK CFI 336d4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 336e4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 336ec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 336fc .ra: .cfa -48 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3384c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33850 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 338c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 338c8 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 33a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 33a88 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 33b20 1de4 .cfa: sp 0 + .ra: x30
STACK CFI 33b24 .cfa: sp 1952 +
STACK CFI 33bcc .ra: .cfa -1872 + ^ v8: .cfa -1856 + ^ v9: .cfa -1848 + ^ x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x21: .cfa -1936 + ^ x22: .cfa -1928 + ^ x23: .cfa -1920 + ^ x24: .cfa -1912 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^ x27: .cfa -1888 + ^ x28: .cfa -1880 + ^
STACK CFI 34844 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 34848 .cfa: sp 1952 + .ra: .cfa -1872 + ^ v8: .cfa -1856 + ^ v9: .cfa -1848 + ^ x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x21: .cfa -1936 + ^ x22: .cfa -1928 + ^ x23: .cfa -1920 + ^ x24: .cfa -1912 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^ x27: .cfa -1888 + ^ x28: .cfa -1880 + ^
STACK CFI INIT 35930 1d14 .cfa: sp 0 + .ra: x30
STACK CFI 35934 .cfa: sp 1952 +
STACK CFI 359d8 .ra: .cfa -1872 + ^ v8: .cfa -1856 + ^ v9: .cfa -1848 + ^ x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x21: .cfa -1936 + ^ x22: .cfa -1928 + ^ x23: .cfa -1920 + ^ x24: .cfa -1912 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^ x27: .cfa -1888 + ^ x28: .cfa -1880 + ^
STACK CFI 3702c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37030 .cfa: sp 1952 + .ra: .cfa -1872 + ^ v8: .cfa -1856 + ^ v9: .cfa -1848 + ^ x19: .cfa -1952 + ^ x20: .cfa -1944 + ^ x21: .cfa -1936 + ^ x22: .cfa -1928 + ^ x23: .cfa -1920 + ^ x24: .cfa -1912 + ^ x25: .cfa -1904 + ^ x26: .cfa -1896 + ^ x27: .cfa -1888 + ^ x28: .cfa -1880 + ^
STACK CFI INIT 37670 1d6c .cfa: sp 0 + .ra: x30
STACK CFI 37674 .cfa: sp 1936 +
STACK CFI 3772c .ra: .cfa -1856 + ^ v8: .cfa -1848 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 38484 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 38488 .cfa: sp 1936 + .ra: .cfa -1856 + ^ v8: .cfa -1848 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 39400 1e20 .cfa: sp 0 + .ra: x30
STACK CFI 39404 .cfa: sp 1968 +
STACK CFI 394a8 .ra: .cfa -1888 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x19: .cfa -1968 + ^ x20: .cfa -1960 + ^ x21: .cfa -1952 + ^ x22: .cfa -1944 + ^ x23: .cfa -1936 + ^ x24: .cfa -1928 + ^ x25: .cfa -1920 + ^ x26: .cfa -1912 + ^ x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI 3a234 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3a238 .cfa: sp 1968 + .ra: .cfa -1888 + ^ v8: .cfa -1872 + ^ v9: .cfa -1864 + ^ x19: .cfa -1968 + ^ x20: .cfa -1960 + ^ x21: .cfa -1952 + ^ x22: .cfa -1944 + ^ x23: .cfa -1936 + ^ x24: .cfa -1928 + ^ x25: .cfa -1920 + ^ x26: .cfa -1912 + ^ x27: .cfa -1904 + ^ x28: .cfa -1896 + ^
STACK CFI INIT 3b240 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 3b244 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b24c .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 3b380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3b388 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 3b728 d8 .cfa: sp 0 + .ra: x30
STACK CFI 3b77c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b78c .ra: .cfa -48 + ^
STACK CFI 3b7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3b7dc .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 3b800 174 .cfa: sp 0 + .ra: x30
STACK CFI 3b804 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b814 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b824 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3b85c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 3b860 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3b920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 3b928 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 3b978 30 .cfa: sp 0 + .ra: x30
STACK CFI 3b97c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3b998 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3b9a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3b9a4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3b9a8 178 .cfa: sp 0 + .ra: x30
STACK CFI 3b9b0 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b9b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b9c4 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3ba14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 3ba20 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3bae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 3bae8 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 3bb20 110 .cfa: sp 0 + .ra: x30
STACK CFI 3bb24 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3bb30 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3bb40 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3bbd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3bbe0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3bc30 284 .cfa: sp 0 + .ra: x30
STACK CFI 3bc34 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3bc3c .ra: .cfa -72 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI 3bd70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3bd78 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 3beb8 f0 .cfa: sp 0 + .ra: x30
STACK CFI 3bebc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3bec4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3bed0 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3bf58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3bf60 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3bfa8 5c .cfa: sp 0 + .ra: x30
STACK CFI 3bfac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI 3bfe8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3bff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI INIT 3c008 210 .cfa: sp 0 + .ra: x30
STACK CFI 3c00c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3c01c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3c02c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3c038 .ra: .cfa -32 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3c168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c170 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3c208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c210 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 3c218 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 3c21c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c22c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3c23c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3c24c .ra: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c358 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c3bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 3c3c0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 3c3c4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c3d4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3c3e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3c3f4 .ra: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c500 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 3c570 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 3c574 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3c584 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3c594 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3c5a4 .ra: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c6a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3c6b0 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 3c718 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3c71c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3c724 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c730 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3c7b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3c7b8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3c800 7a8 .cfa: sp 0 + .ra: x30
STACK CFI 3c804 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 3c808 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 3c820 .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 3cdf4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3cdf8 .cfa: sp 256 + .ra: .cfa -176 + ^ v8: .cfa -168 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 3cfa8 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 3cfac .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3cfb4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3cfcc .ra: .cfa -128 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3cfd4 v8: .cfa -120 + ^
STACK CFI 3d304 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3d308 .cfa: sp 192 + .ra: .cfa -128 + ^ v8: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 3d368 60 .cfa: sp 0 + .ra: x30
STACK CFI 3d36c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d370 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3d378 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d3c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 3d3c8 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 3d3cc .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3d3d4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3d3ec .ra: .cfa -128 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3d3f4 v8: .cfa -120 + ^
STACK CFI 3d724 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3d728 .cfa: sp 192 + .ra: .cfa -128 + ^ v8: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 3d788 60 .cfa: sp 0 + .ra: x30
STACK CFI 3d78c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d790 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3d798 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 3d7e8 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 3d7ec .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3d7f4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3d80c .ra: .cfa -128 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3d814 v8: .cfa -120 + ^
STACK CFI 3db44 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3db48 .cfa: sp 192 + .ra: .cfa -128 + ^ v8: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT 3dba8 60 .cfa: sp 0 + .ra: x30
STACK CFI 3dbac .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3dbb0 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3dbb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3dc04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 3dc08 830 .cfa: sp 0 + .ra: x30
STACK CFI 3dc0c .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 3dc10 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3dc18 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3dc34 .ra: .cfa -208 + ^ v8: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 3e39c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3e3a0 .cfa: sp 288 + .ra: .cfa -208 + ^ v8: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 3e440 60 .cfa: sp 0 + .ra: x30
STACK CFI 3e444 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e448 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3e450 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 8340 30 .cfa: sp 0 + .ra: x30
STACK CFI 8344 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8360 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3e4a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4d8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e4f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e500 50 .cfa: sp 0 + .ra: x30
STACK CFI 3e504 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e510 .ra: .cfa -16 + ^
STACK CFI 3e54c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8090 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8094 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 80a0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 8120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8124 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 3e550 254 .cfa: sp 0 + .ra: x30
STACK CFI 3e554 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e564 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 3e760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3e768 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 3e7a8 24c .cfa: sp 0 + .ra: x30
STACK CFI 3e7ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e7bc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 3e9b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3e9b8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 3e9f8 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 3e9fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ea0c .ra: .cfa -32 + ^
STACK CFI 3ea8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3ea90 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 3eec0 784 .cfa: sp 0 + .ra: x30
STACK CFI 3eec8 .cfa: sp 112 +
STACK CFI 3eeec .cfa: sp 0 +
STACK CFI 3eef0 .cfa: sp 112 +
STACK CFI 3f5bc .cfa: sp 0 +
STACK CFI 3f5c0 .cfa: sp 112 +
STACK CFI INIT 3f648 138 .cfa: sp 0 + .ra: x30
STACK CFI 3f64c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f658 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3f660 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 3f744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3f748 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 3f780 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3f784 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f78c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3f798 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3f820 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3f868 168 .cfa: sp 0 + .ra: x30
STACK CFI 3f86c .cfa: sp 16 +
STACK CFI 3f98c .cfa: sp 0 +
STACK CFI 3f990 .cfa: sp 16 +
STACK CFI INIT 3f9d0 7a4 .cfa: sp 0 + .ra: x30
STACK CFI 3f9d4 .cfa: sp 352 +
STACK CFI 3f9e4 x25: .cfa -288 + ^ x26: .cfa -280 + ^
STACK CFI 3f9ec x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 3fa04 x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 3fa1c .ra: .cfa -256 + ^ v8: .cfa -248 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 3fe2c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3fe30 .cfa: sp 352 + .ra: .cfa -256 + ^ v8: .cfa -248 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI INIT 40198 414 .cfa: sp 0 + .ra: x30
STACK CFI 4019c .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 401a4 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 401ac .ra: .cfa -200 + ^ x23: .cfa -208 + ^
STACK CFI 40418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 40420 .cfa: sp 240 + .ra: .cfa -200 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^
STACK CFI INIT 405b0 f0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 406a0 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 406a4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 406bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 406cc .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x25: .cfa -32 + ^
STACK CFI 40854 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 40858 .cfa: sp 80 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 40958 820 .cfa: sp 0 + .ra: x30
STACK CFI 4095c .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 40960 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 40968 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 4097c .ra: .cfa -272 + ^ v8: .cfa -264 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 40fa4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 40fa8 .cfa: sp 352 + .ra: .cfa -272 + ^ v8: .cfa -264 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 41180 7f4 .cfa: sp 0 + .ra: x30
STACK CFI 41184 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 41190 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 411a0 .ra: .cfa -376 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^
STACK CFI 41674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 41678 .cfa: sp 432 + .ra: .cfa -376 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^
STACK CFI INIT 41980 688 .cfa: sp 0 + .ra: x30
STACK CFI 41984 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 41988 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 41990 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 419a8 .ra: .cfa -416 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 41b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 41b90 .cfa: sp 496 + .ra: .cfa -416 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 42038 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42050 28 .cfa: sp 0 + .ra: x30
STACK CFI 4205c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 42074 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 42078 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42088 24 .cfa: sp 0 + .ra: x30
STACK CFI 4208c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 420a8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 420b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 420c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 420c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 420e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8130 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8134 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8140 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 81c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 81c4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 420e8 134 .cfa: sp 0 + .ra: x30
STACK CFI 421b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 421c4 .ra: .cfa -16 + ^
STACK CFI 4220c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 42210 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 42220 270 .cfa: sp 0 + .ra: x30
STACK CFI 42224 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 42228 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 42230 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 42240 .ra: .cfa -32 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4233c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 42340 .cfa: sp 112 + .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4248c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 42490 634 .cfa: sp 0 + .ra: x30
STACK CFI 42494 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 424b4 .ra: .cfa -96 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 42880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 42888 .cfa: sp 176 + .ra: .cfa -96 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 42ac8 544 .cfa: sp 0 + .ra: x30
STACK CFI 42acc .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 42af0 .ra: .cfa -80 + ^ v8: .cfa -72 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 42df8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 42e00 .cfa: sp 160 + .ra: .cfa -80 + ^ v8: .cfa -72 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 43010 bcc .cfa: sp 0 + .ra: x30
STACK CFI 43014 .cfa: sp 1104 +
STACK CFI 43018 x25: .cfa -1056 + ^ x26: .cfa -1048 + ^
STACK CFI 43028 x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 43038 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI 43048 .ra: .cfa -1024 + ^ v8: .cfa -1016 + ^
STACK CFI 437d0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 437d8 .cfa: sp 1104 + .ra: .cfa -1024 + ^ v8: .cfa -1016 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x26: .cfa -1048 + ^ x27: .cfa -1040 + ^ x28: .cfa -1032 + ^
STACK CFI INIT 43bf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43bf8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c08 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c18 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c38 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43c50 50 .cfa: sp 0 + .ra: x30
STACK CFI 43c54 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43c60 .ra: .cfa -16 + ^
STACK CFI 43c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 43ca0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43ca8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43cb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43cc8 28 .cfa: sp 0 + .ra: x30
STACK CFI 43cd0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 43cec .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 43cf0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 81d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 81d4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 81e0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 8260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8264 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 43d10 d8 .cfa: sp 0 + .ra: x30
STACK CFI 43d14 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43d1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43d24 .ra: .cfa -16 + ^
STACK CFI 43da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 43da8 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 43dd4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 43df0 bc .cfa: sp 0 + .ra: x30
STACK CFI 43df4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43df8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 43e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 43ea0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 43ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 43eb0 c84 .cfa: sp 0 + .ra: x30
STACK CFI 43eb4 .cfa: sp 1952 +
STACK CFI 43ec0 x25: .cfa -1888 + ^ x26: .cfa -1880 + ^
STACK CFI 43edc .ra: .cfa -1856 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 43eec v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^
STACK CFI 43ef8 v8: .cfa -1840 + ^ v9: .cfa -1832 + ^
STACK CFI 44a40 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 44a48 .cfa: sp 1952 + .ra: .cfa -1856 + ^ v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^ v8: .cfa -1840 + ^ v9: .cfa -1832 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 44b58 bc .cfa: sp 0 + .ra: x30
STACK CFI 44b5c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44b60 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 44c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 44c08 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 44c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 44c20 c8c .cfa: sp 0 + .ra: x30
STACK CFI 44c24 .cfa: sp 1952 +
STACK CFI 44c30 x25: .cfa -1888 + ^ x26: .cfa -1880 + ^
STACK CFI 44c4c .ra: .cfa -1856 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 44c5c v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^
STACK CFI 44c68 v8: .cfa -1840 + ^ v9: .cfa -1832 + ^
STACK CFI 457b8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 457c0 .cfa: sp 1952 + .ra: .cfa -1856 + ^ v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^ v8: .cfa -1840 + ^ v9: .cfa -1832 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 458c8 bc .cfa: sp 0 + .ra: x30
STACK CFI 458cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 458d0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 45974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 45978 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 45980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 45990 c8c .cfa: sp 0 + .ra: x30
STACK CFI 45994 .cfa: sp 1952 +
STACK CFI 459a0 x25: .cfa -1888 + ^ x26: .cfa -1880 + ^
STACK CFI 459bc .ra: .cfa -1856 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 459cc v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^
STACK CFI 459d8 v8: .cfa -1840 + ^ v9: .cfa -1832 + ^
STACK CFI 46528 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 46530 .cfa: sp 1952 + .ra: .cfa -1856 + ^ v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^ v8: .cfa -1840 + ^ v9: .cfa -1832 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 46638 bc .cfa: sp 0 + .ra: x30
STACK CFI 4663c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 46640 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 466e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 466e8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 466f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 46700 ca4 .cfa: sp 0 + .ra: x30
STACK CFI 46704 .cfa: sp 1952 +
STACK CFI 4670c v14: .cfa -1792 + ^ v15: .cfa -1784 + ^
STACK CFI 4671c v12: .cfa -1808 + ^ v13: .cfa -1800 + ^
STACK CFI 46728 x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^
STACK CFI 4674c .ra: .cfa -1856 + ^ v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v8: .cfa -1840 + ^ v9: .cfa -1832 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI 472b0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 472b8 .cfa: sp 1952 + .ra: .cfa -1856 + ^ v10: .cfa -1824 + ^ v11: .cfa -1816 + ^ v12: .cfa -1808 + ^ v13: .cfa -1800 + ^ v14: .cfa -1792 + ^ v15: .cfa -1784 + ^ v8: .cfa -1840 + ^ v9: .cfa -1832 + ^ x19: .cfa -1936 + ^ x20: .cfa -1928 + ^ x21: .cfa -1920 + ^ x22: .cfa -1912 + ^ x23: .cfa -1904 + ^ x24: .cfa -1896 + ^ x25: .cfa -1888 + ^ x26: .cfa -1880 + ^ x27: .cfa -1872 + ^ x28: .cfa -1864 + ^
STACK CFI INIT 473c8 694 .cfa: sp 0 + .ra: x30
STACK CFI 473cc .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 473d4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 473e4 .ra: .cfa -192 + ^
STACK CFI 475d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 475d4 .cfa: sp 224 + .ra: .cfa -192 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI INIT 8370 30 .cfa: sp 0 + .ra: x30
STACK CFI 8374 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 8390 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 47a60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47aa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47aa8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47ab8 50 .cfa: sp 0 + .ra: x30
STACK CFI 47abc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47ac8 .ra: .cfa -16 + ^
STACK CFI 47b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 47b08 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47b18 44 .cfa: sp 0 + .ra: x30
STACK CFI 47b1c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 47b58 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8270 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8274 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8280 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 8300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8304 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 47b60 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 47b64 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 47b74 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 47b80 .ra: .cfa -80 + ^
STACK CFI 47cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 47cd8 .cfa: sp 112 + .ra: .cfa -80 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI INIT 47d08 50 .cfa: sp 0 + .ra: x30
STACK CFI 47d0c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47d1c .ra: .cfa -16 + ^
STACK CFI 47d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 47d58 4c .cfa: sp 0 + .ra: x30
STACK CFI 47d5c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 47da0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 47da8 474 .cfa: sp 0 + .ra: x30
STACK CFI 47dac .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 47db4 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 480dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 480e0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 48220 10c .cfa: sp 0 + .ra: x30
STACK CFI 48224 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4822c v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 48238 v10: .cfa -16 + ^ v11: .cfa -8 + ^
STACK CFI 48244 v12: .cfa -40 + ^
STACK CFI 4824c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 48254 .ra: .cfa -48 + ^
STACK CFI 482f8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 48300 .cfa: sp 80 + .ra: .cfa -48 + ^ v10: .cfa -16 + ^ v11: .cfa -8 + ^ v12: .cfa -40 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 48330 b4 .cfa: sp 0 + .ra: x30
STACK CFI 48338 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48344 .ra: .cfa -16 + ^
STACK CFI 4836c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 48370 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 483b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 483c0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 483f0 185c .cfa: sp 0 + .ra: x30
STACK CFI 483f4 .cfa: sp 1840 +
STACK CFI 483fc x19: .cfa -1840 + ^ x20: .cfa -1832 + ^
STACK CFI 48414 x21: .cfa -1824 + ^ x22: .cfa -1816 + ^ x27: .cfa -1776 + ^ x28: .cfa -1768 + ^
STACK CFI 4842c .ra: .cfa -1760 + ^ v8: .cfa -1744 + ^ v9: .cfa -1736 + ^ x23: .cfa -1808 + ^ x24: .cfa -1800 + ^ x25: .cfa -1792 + ^ x26: .cfa -1784 + ^
STACK CFI 498b8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 498bc .cfa: sp 1840 + .ra: .cfa -1760 + ^ v8: .cfa -1744 + ^ v9: .cfa -1736 + ^ x19: .cfa -1840 + ^ x20: .cfa -1832 + ^ x21: .cfa -1824 + ^ x22: .cfa -1816 + ^ x23: .cfa -1808 + ^ x24: .cfa -1800 + ^ x25: .cfa -1792 + ^ x26: .cfa -1784 + ^ x27: .cfa -1776 + ^ x28: .cfa -1768 + ^
