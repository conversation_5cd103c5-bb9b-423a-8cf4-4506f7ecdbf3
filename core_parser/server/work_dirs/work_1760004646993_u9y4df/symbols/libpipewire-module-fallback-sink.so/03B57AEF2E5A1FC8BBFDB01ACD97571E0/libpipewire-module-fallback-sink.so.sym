MODULE Linux arm64 03B57AEF2E5A1FC8BBFDB01ACD97571E0 libpipewire-module-fallback-sink.so
INFO CODE_ID EF7AB5035A2EC81FBBFDB01ACD97571E3A525A35
PUBLIC 1b20 0 pipewire__module_init
STACK CFI INIT fc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ff0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1030 48 .cfa: sp 0 + .ra: x30
STACK CFI 1034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 103c x19: .cfa -16 + ^
STACK CFI 1074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1080 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1090 78 .cfa: sp 0 + .ra: x30
STACK CFI 1098 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10a0 x19: .cfa -16 + ^
STACK CFI 1100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1110 64 .cfa: sp 0 + .ra: x30
STACK CFI 1118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1120 x19: .cfa -16 + ^
STACK CFI 1164 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 116c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1174 ec .cfa: sp 0 + .ra: x30
STACK CFI 117c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1184 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1248 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1258 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1260 1c .cfa: sp 0 + .ra: x30
STACK CFI 1268 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1270 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1280 60 .cfa: sp 0 + .ra: x30
STACK CFI 1288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1290 x19: .cfa -16 + ^
STACK CFI 12d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12e0 ac .cfa: sp 0 + .ra: x30
STACK CFI 12e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12f8 x19: .cfa -16 + ^
STACK CFI 1344 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 134c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1390 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13a0 x19: .cfa -16 + ^
STACK CFI 13d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 13d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1428 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1430 21c .cfa: sp 0 + .ra: x30
STACK CFI 1438 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1440 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1460 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 14a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 153c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1548 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1634 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1650 f4 .cfa: sp 0 + .ra: x30
STACK CFI 1658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1660 x19: .cfa -16 + ^
STACK CFI 1730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1738 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1744 50 .cfa: sp 0 + .ra: x30
STACK CFI 174c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1754 x19: .cfa -16 + ^
STACK CFI 178c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1794 15c .cfa: sp 0 + .ra: x30
STACK CFI 17a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 180c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1810 x23: .cfa -16 + ^
STACK CFI 1850 x23: x23
STACK CFI 1854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1858 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 188c x23: x23
STACK CFI 1890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1898 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 18c4 x23: x23
STACK CFI 18cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 18f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 18f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1900 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1960 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1970 1ac .cfa: sp 0 + .ra: x30
STACK CFI 1978 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1980 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 198c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1994 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a90 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1a94 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1ae8 x25: x25 x26: x26
STACK CFI 1af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1b00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1b08 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1b18 x25: x25 x26: x26
STACK CFI INIT 1b20 47c .cfa: sp 0 + .ra: x30
STACK CFI 1b28 .cfa: sp 96 +
STACK CFI 1b34 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1b44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ba4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1e00 x23: x23 x24: x24
STACK CFI 1e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e38 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1ea4 x23: x23 x24: x24
STACK CFI 1ea8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1f84 x23: x23 x24: x24
STACK CFI 1f88 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1f8c x23: x23 x24: x24
