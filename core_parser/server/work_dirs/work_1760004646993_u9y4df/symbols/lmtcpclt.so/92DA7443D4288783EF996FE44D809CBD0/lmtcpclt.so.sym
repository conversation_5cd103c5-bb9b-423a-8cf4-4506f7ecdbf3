MODULE Linux arm64 92DA7443D4288783EF996FE44D809CBD0 lmtcpclt.so
INFO CODE_ID 4374DA9228D48387EF996FE44D809CBD5505AC7F
PUBLIC d20 0 tcpcltQueryInterface
PUBLIC f70 0 tcpcltDestruct
PUBLIC 12b0 0 tcpcltConstruct
PUBLIC 13f4 0 tcpcltClassExit
PUBLIC 1440 0 tcpcltClassInit
PUBLIC 14f0 0 modInit
STACK CFI INIT ac0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT af0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT b30 48 .cfa: sp 0 + .ra: x30
STACK CFI b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b3c x19: .cfa -16 + ^
STACK CFI b74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b90 28 .cfa: sp 0 + .ra: x30
STACK CFI b9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bc0 24 .cfa: sp 0 + .ra: x30
STACK CFI bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be4 24 .cfa: sp 0 + .ra: x30
STACK CFI bf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c10 24 .cfa: sp 0 + .ra: x30
STACK CFI c1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c34 24 .cfa: sp 0 + .ra: x30
STACK CFI c40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c60 24 .cfa: sp 0 + .ra: x30
STACK CFI c6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c84 24 .cfa: sp 0 + .ra: x30
STACK CFI c90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cb0 24 .cfa: sp 0 + .ra: x30
STACK CFI cbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ccc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cd4 24 .cfa: sp 0 + .ra: x30
STACK CFI ce0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d00 1c .cfa: sp 0 + .ra: x30
STACK CFI d08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d20 ac .cfa: sp 0 + .ra: x30
STACK CFI d28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT dd0 2c .cfa: sp 0 + .ra: x30
STACK CFI ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e00 168 .cfa: sp 0 + .ra: x30
STACK CFI e08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e18 .cfa: sp 1104 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e44 x21: .cfa -32 + ^
STACK CFI e50 x22: .cfa -24 + ^
STACK CFI ef8 x21: x21
STACK CFI efc x22: x22
STACK CFI f14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f18 x21: x21
STACK CFI f1c x22: x22
STACK CFI f3c .cfa: sp 64 +
STACK CFI f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI f54 .cfa: sp 1104 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI f60 x21: .cfa -32 + ^
STACK CFI f64 x22: .cfa -24 + ^
STACK CFI INIT f70 54 .cfa: sp 0 + .ra: x30
STACK CFI f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT fc4 2ec .cfa: sp 0 + .ra: x30
STACK CFI fcc .cfa: sp 112 +
STACK CFI fd0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fd8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI fe8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ff8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 113c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 12b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 12b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12c4 x19: .cfa -16 + ^
STACK CFI 12fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1304 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1310 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1330 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 136c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 13ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13f4 2c .cfa: sp 0 + .ra: x30
STACK CFI 13fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1420 20 .cfa: sp 0 + .ra: x30
STACK CFI 1428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1434 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1440 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1448 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1454 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1460 x21: .cfa -16 + ^
STACK CFI 14d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 14dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 14e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 14f0 dc .cfa: sp 0 + .ra: x30
STACK CFI 14f8 .cfa: sp 64 +
STACK CFI 1504 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 150c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1518 x21: .cfa -16 + ^
STACK CFI 15a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 15b0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
