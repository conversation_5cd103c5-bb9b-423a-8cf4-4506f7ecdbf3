MODULE Linux arm64 477BDB103587EC8A9080DDE9EC85B3C60 libusb-1.0.so.0
INFO CODE_ID 10DB7B4787358AEC9080DDE9EC85B3C682E937FC
PUBLIC 5140 0 libusb_get_bus_number
PUBLIC 5160 0 libusb_get_port_number
PUBLIC 5180 0 libusb_get_parent
PUBLIC 51a0 0 libusb_get_device_address
PUBLIC 51c0 0 libusb_get_device_speed
PUBLIC 51e0 0 libusb_ref_device
PUBLIC 5244 0 libusb_get_device
PUBLIC 5260 0 libusb_dev_mem_alloc
PUBLIC 52a0 0 libusb_dev_mem_free
PUBLIC 52c0 0 libusb_set_auto_detach_kernel_driver
PUBLIC 52e4 0 libusb_has_capability
PUBLIC 54a0 0 libusb_get_port_numbers
PUBLIC 5574 0 libusb_get_port_path
PUBLIC 55a0 0 libusb_set_configuration
PUBLIC 5620 0 libusb_clear_halt
PUBLIC 56a4 0 libusb_reset_device
PUBLIC 5720 0 libusb_alloc_streams
PUBLIC 57e0 0 libusb_free_streams
PUBLIC 5890 0 libusb_kernel_driver_active
PUBLIC 5924 0 libusb_detach_kernel_driver
PUBLIC 59c0 0 libusb_attach_kernel_driver
PUBLIC 5a54 0 libusb_set_log_cb
PUBLIC 5b00 0 libusb_set_option
PUBLIC 5d60 0 libusb_set_debug
PUBLIC 96b0 0 libusb_error_name
PUBLIC 9a50 0 libusb_get_version
PUBLIC 9c90 0 libusb_unref_device
PUBLIC 9d60 0 libusb_free_device_list
PUBLIC 9f90 0 libusb_init_context
PUBLIC a7b0 0 libusb_init
PUBLIC a7d0 0 libusb_exit
PUBLIC ad10 0 libusb_get_device_list
PUBLIC b050 0 libusb_wrap_sys_device
PUBLIC b1e0 0 libusb_open
PUBLIC b350 0 libusb_claim_interface
PUBLIC b440 0 libusb_release_interface
PUBLIC b514 0 libusb_set_interface_alt_setting
PUBLIC b610 0 libusb_get_device_descriptor
PUBLIC b670 0 libusb_open_device_with_vid_pid
PUBLIC b780 0 libusb_get_active_config_descriptor
PUBLIC b860 0 libusb_get_config_descriptor
PUBLIC b990 0 libusb_get_config_descriptor_by_value
PUBLIC ba34 0 libusb_free_config_descriptor
PUBLIC bab0 0 libusb_get_max_packet_size
PUBLIC bb74 0 libusb_get_ss_endpoint_companion_descriptor
PUBLIC bca0 0 libusb_free_ss_endpoint_companion_descriptor
PUBLIC bd84 0 libusb_get_max_iso_packet_size
PUBLIC be54 0 libusb_get_max_alt_packet_size
PUBLIC bf90 0 libusb_free_bos_descriptor
PUBLIC bff4 0 libusb_get_usb_2_0_extension_descriptor
PUBLIC c0c0 0 libusb_free_usb_2_0_extension_descriptor
PUBLIC c0e0 0 libusb_get_ss_usb_device_capability_descriptor
PUBLIC c1b0 0 libusb_free_ss_usb_device_capability_descriptor
PUBLIC c1d0 0 libusb_get_container_id_descriptor
PUBLIC c2a0 0 libusb_free_container_id_descriptor
PUBLIC c2c0 0 libusb_get_platform_descriptor
PUBLIC c3c0 0 libusb_free_platform_descriptor
PUBLIC c3e0 0 libusb_get_interface_association_descriptors
PUBLIC c534 0 libusb_get_active_interface_association_descriptors
PUBLIC c630 0 libusb_free_interface_association_descriptors
PUBLIC c670 0 libusb_hotplug_deregister_callback
PUBLIC c854 0 libusb_hotplug_register_callback
PUBLIC cb60 0 libusb_hotplug_get_user_data
PUBLIC cc90 0 libusb_alloc_transfer
PUBLIC cd20 0 libusb_free_transfer
PUBLIC cdf0 0 libusb_submit_transfer
PUBLIC d154 0 libusb_cancel_transfer
PUBLIC df10 0 libusb_transfer_set_stream_id
PUBLIC df30 0 libusb_transfer_get_stream_id
PUBLIC df50 0 libusb_try_lock_events
PUBLIC e044 0 libusb_lock_events
PUBLIC e0d0 0 libusb_unlock_events
PUBLIC e1b0 0 libusb_close
PUBLIC e570 0 libusb_event_handling_ok
PUBLIC e660 0 libusb_event_handler_active
PUBLIC e744 0 libusb_interrupt_event_handler
PUBLIC e884 0 libusb_lock_event_waiters
PUBLIC e914 0 libusb_unlock_event_waiters
PUBLIC fc60 0 libusb_wait_for_event
PUBLIC fe34 0 libusb_pollfds_handle_timeouts
PUBLIC fed0 0 libusb_get_next_timeout
PUBLIC 10220 0 libusb_handle_events_timeout_completed
PUBLIC 10450 0 libusb_handle_events_timeout
PUBLIC 10470 0 libusb_handle_events
PUBLIC 104e0 0 libusb_handle_events_completed
PUBLIC 10790 0 libusb_handle_events_locked
PUBLIC 108b0 0 libusb_set_pollfd_notifiers
PUBLIC 10950 0 libusb_get_pollfds
PUBLIC 10a70 0 libusb_free_pollfds
PUBLIC 10a90 0 libusb_setlocale
PUBLIC 10b90 0 libusb_strerror
PUBLIC 10bc4 0 libusb_control_transfer
PUBLIC 10df4 0 libusb_get_configuration
PUBLIC 10f80 0 libusb_get_bos_descriptor
PUBLIC 11424 0 libusb_get_string_descriptor_ascii
PUBLIC 11640 0 libusb_bulk_transfer
PUBLIC 11660 0 libusb_interrupt_transfer
STACK CFI INIT 4480 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 44b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 44f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 44f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44fc x19: .cfa -16 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4540 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4550 ac .cfa: sp 0 + .ra: x30
STACK CFI 4558 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4600 9c .cfa: sp 0 + .ra: x30
STACK CFI 460c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 464c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4660 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 466c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4684 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46a0 64 .cfa: sp 0 + .ra: x30
STACK CFI 46a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 46f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4704 d8 .cfa: sp 0 + .ra: x30
STACK CFI 470c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4714 x23: .cfa -16 + ^
STACK CFI 4730 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4734 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47c0 x19: x19 x20: x20
STACK CFI 47c4 x21: x21 x22: x22
STACK CFI 47d4 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI INIT 47e0 38 .cfa: sp 0 + .ra: x30
STACK CFI 47e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4820 2c .cfa: sp 0 + .ra: x30
STACK CFI 4828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4850 30 .cfa: sp 0 + .ra: x30
STACK CFI 4858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4880 30 .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 48b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 48b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 48e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 48e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4910 30 .cfa: sp 0 + .ra: x30
STACK CFI 4918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4940 30 .cfa: sp 0 + .ra: x30
STACK CFI 4948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4970 30 .cfa: sp 0 + .ra: x30
STACK CFI 4978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 49a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 49a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 49d0 30 .cfa: sp 0 + .ra: x30
STACK CFI 49d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a00 30 .cfa: sp 0 + .ra: x30
STACK CFI 4a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a30 30 .cfa: sp 0 + .ra: x30
STACK CFI 4a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a60 30 .cfa: sp 0 + .ra: x30
STACK CFI 4a68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a90 30 .cfa: sp 0 + .ra: x30
STACK CFI 4a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4ac0 30 .cfa: sp 0 + .ra: x30
STACK CFI 4ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4af0 30 .cfa: sp 0 + .ra: x30
STACK CFI 4af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b20 30 .cfa: sp 0 + .ra: x30
STACK CFI 4b28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b50 30 .cfa: sp 0 + .ra: x30
STACK CFI 4b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b80 30 .cfa: sp 0 + .ra: x30
STACK CFI 4b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4bb0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4c54 6c .cfa: sp 0 + .ra: x30
STACK CFI 4c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c90 x19: .cfa -16 + ^
STACK CFI 4cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4cc0 420 .cfa: sp 0 + .ra: x30
STACK CFI 4cc8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4cdc .cfa: sp 1232 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4d1c x23: .cfa -48 + ^
STACK CFI 4d20 x24: .cfa -40 + ^
STACK CFI 4d24 x25: .cfa -32 + ^
STACK CFI 4d28 x26: .cfa -24 + ^
STACK CFI 4ebc x23: x23
STACK CFI 4ec0 x24: x24
STACK CFI 4ec4 x25: x25
STACK CFI 4ec8 x26: x26
STACK CFI 4ee8 .cfa: sp 96 +
STACK CFI 4ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 4f00 .cfa: sp 1232 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4f04 x23: x23
STACK CFI 4f08 x24: x24
STACK CFI 4f0c x25: x25
STACK CFI 4f10 x26: x26
STACK CFI 4f14 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5014 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 505c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 50c8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 50cc x23: .cfa -48 + ^
STACK CFI 50d0 x24: .cfa -40 + ^
STACK CFI 50d4 x25: .cfa -32 + ^
STACK CFI 50d8 x26: .cfa -24 + ^
STACK CFI INIT 50e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 5118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5140 1c .cfa: sp 0 + .ra: x30
STACK CFI 5148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5150 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5160 1c .cfa: sp 0 + .ra: x30
STACK CFI 5168 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5170 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5180 1c .cfa: sp 0 + .ra: x30
STACK CFI 5188 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5194 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 51a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 51c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 51e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 51f0 x19: .cfa -16 + ^
STACK CFI 5218 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5220 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5244 1c .cfa: sp 0 + .ra: x30
STACK CFI 524c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5260 38 .cfa: sp 0 + .ra: x30
STACK CFI 5268 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5280 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5288 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 528c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 52a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 52a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 52c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 52cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 52e4 54 .cfa: sp 0 + .ra: x30
STACK CFI 52ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5310 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 531c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5328 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 532c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5340 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5348 .cfa: sp 256 +
STACK CFI 5358 .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 53e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 53ec .cfa: sp 256 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI INIT 53f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 53f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5420 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 54a0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 54a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54b8 x19: .cfa -16 + ^
STACK CFI 550c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5514 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5530 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 556c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5574 24 .cfa: sp 0 + .ra: x30
STACK CFI 557c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55a0 80 .cfa: sp 0 + .ra: x30
STACK CFI 55a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 560c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5620 84 .cfa: sp 0 + .ra: x30
STACK CFI 5628 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5630 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5690 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 569c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 56a4 78 .cfa: sp 0 + .ra: x30
STACK CFI 56ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56b4 x19: .cfa -16 + ^
STACK CFI 5700 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5708 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5714 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5720 bc .cfa: sp 0 + .ra: x30
STACK CFI 5728 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5730 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 573c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 57d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57e0 ac .cfa: sp 0 + .ra: x30
STACK CFI 57e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 57fc x21: .cfa -16 + ^
STACK CFI 5864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 586c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 587c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5884 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5890 94 .cfa: sp 0 + .ra: x30
STACK CFI 5898 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5908 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 591c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5924 94 .cfa: sp 0 + .ra: x30
STACK CFI 592c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5934 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 599c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 59a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 59b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 59c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 59c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a54 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5a5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5aa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5b00 25c .cfa: sp 0 + .ra: x30
STACK CFI 5b08 .cfa: sp 320 +
STACK CFI 5b18 .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 5b28 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 5b7c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 5bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bcc .cfa: sp 320 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x29: .cfa -240 + ^
STACK CFI 5c28 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5c3c x23: x23 x24: x24
STACK CFI 5c40 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5c94 x23: x23 x24: x24
STACK CFI 5cac x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5ce8 x23: x23 x24: x24
STACK CFI 5cec x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5d20 x23: x23 x24: x24
STACK CFI 5d28 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5d30 x23: x23 x24: x24
STACK CFI 5d3c x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5d4c x23: x23 x24: x24
STACK CFI 5d54 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 5d58 x23: x23 x24: x24
STACK CFI INIT 5d60 20 .cfa: sp 0 + .ra: x30
STACK CFI 5d68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5d80 bc0 .cfa: sp 0 + .ra: x30
STACK CFI 5d88 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 5d90 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 5d9c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 5da4 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 5e08 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 5e54 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 60d8 x27: x27 x28: x28
STACK CFI 60e8 x23: x23 x24: x24
STACK CFI 6100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6108 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 61b8 x23: x23 x24: x24
STACK CFI 61bc x27: x27 x28: x28
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 6200 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 6238 x27: x27 x28: x28
STACK CFI 625c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 679c x27: x27 x28: x28
STACK CFI 67c8 x23: x23 x24: x24
STACK CFI 6884 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 68e0 x23: x23 x24: x24
STACK CFI 68e8 x27: x27 x28: x28
STACK CFI 68ec x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 690c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 6914 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 691c x27: x27 x28: x28
STACK CFI 6924 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 6930 x27: x27 x28: x28
STACK CFI 6934 x23: x23 x24: x24
STACK CFI INIT 6940 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6948 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6950 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6958 x21: .cfa -16 + ^
STACK CFI 6988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6990 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 69c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 69d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6a00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 6a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a18 x21: .cfa -16 + ^
STACK CFI 6a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6a50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6a90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6ac0 224 .cfa: sp 0 + .ra: x30
STACK CFI 6ac8 .cfa: sp 96 +
STACK CFI 6ad4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6adc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ae4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6af0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6af8 x25: .cfa -16 + ^
STACK CFI 6c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6c24 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6ce4 180 .cfa: sp 0 + .ra: x30
STACK CFI 6cec .cfa: sp 80 +
STACK CFI 6cf8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d34 x23: .cfa -16 + ^
STACK CFI 6db0 x23: x23
STACK CFI 6db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6dbc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6e08 x23: x23
STACK CFI 6e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6e58 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6e5c x23: x23
STACK CFI 6e60 x23: .cfa -16 + ^
STACK CFI INIT 6e64 188 .cfa: sp 0 + .ra: x30
STACK CFI 6e6c .cfa: sp 80 +
STACK CFI 6e70 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6e78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f38 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6fb8 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6ff0 78 .cfa: sp 0 + .ra: x30
STACK CFI 6ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7044 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 704c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7070 168 .cfa: sp 0 + .ra: x30
STACK CFI 7078 .cfa: sp 80 +
STACK CFI 7084 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 708c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7094 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 709c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7188 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 71e0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 71e8 .cfa: sp 64 +
STACK CFI 71f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7200 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 720c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7308 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7368 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 73b4 15c .cfa: sp 0 + .ra: x30
STACK CFI 73bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 73c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 73d0 x21: .cfa -16 + ^
STACK CFI 7490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7498 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7510 60 .cfa: sp 0 + .ra: x30
STACK CFI 7518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7520 x19: .cfa -16 + ^
STACK CFI 753c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7544 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7570 5c .cfa: sp 0 + .ra: x30
STACK CFI 7578 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7580 x19: .cfa -16 + ^
STACK CFI 75c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75d0 60 .cfa: sp 0 + .ra: x30
STACK CFI 75e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75f0 x19: .cfa -16 + ^
STACK CFI 7620 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7630 dc .cfa: sp 0 + .ra: x30
STACK CFI 7638 .cfa: sp 304 +
STACK CFI 7648 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7658 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 76bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76c4 .cfa: sp 304 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7710 278 .cfa: sp 0 + .ra: x30
STACK CFI 7718 .cfa: sp 128 +
STACK CFI 7724 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7730 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 773c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7760 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7824 x25: x25 x26: x26
STACK CFI 785c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7864 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7888 x25: x25 x26: x26
STACK CFI 788c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7894 x25: x25 x26: x26
STACK CFI 78a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 78cc x25: x25 x26: x26
STACK CFI 78d4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 794c x25: x25 x26: x26
STACK CFI 7958 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7980 x25: x25 x26: x26
STACK CFI 7984 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 7990 134 .cfa: sp 0 + .ra: x30
STACK CFI 7998 .cfa: sp 64 +
STACK CFI 79a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a8c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ac4 13c .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 80 +
STACK CFI 7adc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7aec x19: .cfa -16 + ^
STACK CFI 7b68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b70 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c00 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c10 x19: .cfa -16 + ^
STACK CFI 7c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7cac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7cc0 10c .cfa: sp 0 + .ra: x30
STACK CFI 7cc8 .cfa: sp 64 +
STACK CFI 7cd8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d68 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7d70 x21: .cfa -16 + ^
STACK CFI 7d84 x21: x21
STACK CFI 7db4 x21: .cfa -16 + ^
STACK CFI 7dbc x21: x21
STACK CFI 7dc8 x21: .cfa -16 + ^
STACK CFI INIT 7dd0 114 .cfa: sp 0 + .ra: x30
STACK CFI 7dd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7de0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ee4 dc .cfa: sp 0 + .ra: x30
STACK CFI 7eec .cfa: sp 48 +
STACK CFI 7efc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f0c x19: .cfa -16 + ^
STACK CFI 7f64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f6c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7fc0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7fc8 .cfa: sp 48 +
STACK CFI 7fd8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fe8 x19: .cfa -16 + ^
STACK CFI 803c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8044 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 80a0 170 .cfa: sp 0 + .ra: x30
STACK CFI 80a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 80b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 80c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 80dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 80e8 x27: .cfa -16 + ^
STACK CFI 8144 x21: x21 x22: x22
STACK CFI 8148 x23: x23 x24: x24
STACK CFI 814c x27: x27
STACK CFI 815c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 8164 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 81b4 x21: x21 x22: x22
STACK CFI 81c0 x23: x23 x24: x24
STACK CFI 81c8 x27: x27
STACK CFI 81cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 81d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 81d8 x21: x21 x22: x22
STACK CFI 81dc x23: x23 x24: x24
STACK CFI 81e0 x27: x27
STACK CFI 81e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 81ec x21: x21 x22: x22
STACK CFI 81f4 x23: x23 x24: x24
STACK CFI 81f8 x27: x27
STACK CFI 81fc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 8200 x21: x21 x22: x22
STACK CFI 8208 x23: x23 x24: x24
STACK CFI 820c x27: x27
STACK CFI INIT 8210 34 .cfa: sp 0 + .ra: x30
STACK CFI 8218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8244 2c .cfa: sp 0 + .ra: x30
STACK CFI 824c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8270 80 .cfa: sp 0 + .ra: x30
STACK CFI 8278 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8284 x19: .cfa -16 + ^
STACK CFI 82ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 82b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 82f0 74 .cfa: sp 0 + .ra: x30
STACK CFI 82f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8300 x19: .cfa -16 + ^
STACK CFI 831c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8364 fc .cfa: sp 0 + .ra: x30
STACK CFI 836c .cfa: sp 304 +
STACK CFI 837c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 838c x19: .cfa -16 + ^
STACK CFI 83f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 83f8 .cfa: sp 304 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8460 130 .cfa: sp 0 + .ra: x30
STACK CFI 8468 .cfa: sp 320 +
STACK CFI 8478 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8484 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8520 .cfa: sp 320 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8590 104 .cfa: sp 0 + .ra: x30
STACK CFI 8598 .cfa: sp 64 +
STACK CFI 85a8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85b8 x19: .cfa -16 + ^
STACK CFI 8618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8620 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8694 dc .cfa: sp 0 + .ra: x30
STACK CFI 869c .cfa: sp 48 +
STACK CFI 86ac .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 871c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8770 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 8778 .cfa: sp 336 +
STACK CFI 878c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 87a0 x21: .cfa -16 + ^
STACK CFI 8864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 886c .cfa: sp 336 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8920 120 .cfa: sp 0 + .ra: x30
STACK CFI 8928 .cfa: sp 48 +
STACK CFI 8938 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8940 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8988 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 89d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 89dc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8a40 188 .cfa: sp 0 + .ra: x30
STACK CFI 8a48 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8a58 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8a74 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8a94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8b60 x19: x19 x20: x20
STACK CFI 8b68 x21: x21 x22: x22
STACK CFI 8b78 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8b80 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 8ba8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8bc0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 8bd0 80 .cfa: sp 0 + .ra: x30
STACK CFI 8bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8be0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8c48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8c50 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 8c58 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8c68 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 8c74 x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 8ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8eb0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9040 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 9048 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9054 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9070 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9088 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9100 x23: x23 x24: x24
STACK CFI 9104 x25: x25 x26: x26
STACK CFI 9118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 9120 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 912c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 9134 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9138 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 91b4 x21: x21 x22: x22
STACK CFI 91b8 x23: x23 x24: x24
STACK CFI 91c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 91c8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 91f4 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9260 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 941c x21: x21 x22: x22
STACK CFI 9420 x23: x23 x24: x24
STACK CFI 9424 x25: x25 x26: x26
STACK CFI 942c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9484 x25: x25 x26: x26
STACK CFI 9488 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 94b4 x21: x21 x22: x22
STACK CFI 94b8 x23: x23 x24: x24
STACK CFI 94bc x25: x25 x26: x26
STACK CFI 94c0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9510 x21: x21 x22: x22
STACK CFI 9514 x23: x23 x24: x24
STACK CFI 9518 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 952c x25: x25 x26: x26
STACK CFI 9530 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 953c x25: x25 x26: x26
STACK CFI 9540 x21: x21 x22: x22
STACK CFI 9544 x23: x23 x24: x24
STACK CFI 9548 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9574 x25: x25 x26: x26
STACK CFI 9578 x21: x21 x22: x22
STACK CFI 957c x23: x23 x24: x24
STACK CFI 9580 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9584 x21: x21 x22: x22
STACK CFI 958c x23: x23 x24: x24
STACK CFI 9590 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9594 x25: x25 x26: x26
STACK CFI 9598 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 95d8 x21: x21 x22: x22
STACK CFI 95dc x25: x25 x26: x26
STACK CFI INIT 95e4 cc .cfa: sp 0 + .ra: x30
STACK CFI 95ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 95f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 964c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 966c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 96b0 188 .cfa: sp 0 + .ra: x30
STACK CFI 96b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 96d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 96e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9840 20c .cfa: sp 0 + .ra: x30
STACK CFI 9848 .cfa: sp 80 +
STACK CFI 9854 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 985c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9870 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9878 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 99f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9a00 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9a50 20 .cfa: sp 0 + .ra: x30
STACK CFI 9a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9a68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a70 30 .cfa: sp 0 + .ra: x30
STACK CFI 9a78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9aa0 30 .cfa: sp 0 + .ra: x30
STACK CFI 9aa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9ad0 9c .cfa: sp 0 + .ra: x30
STACK CFI 9ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ae0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9b70 84 .cfa: sp 0 + .ra: x30
STACK CFI 9b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9b80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9b8c x21: .cfa -16 + ^
STACK CFI 9bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9bf4 98 .cfa: sp 0 + .ra: x30
STACK CFI 9bfc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9c04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9c10 x21: .cfa -16 + ^
STACK CFI 9c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9c90 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9ca0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9cac x19: .cfa -16 + ^
STACK CFI 9ccc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 9d24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9d60 54 .cfa: sp 0 + .ra: x30
STACK CFI 9d70 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9db4 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 9dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9dc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9dd0 x21: .cfa -16 + ^
STACK CFI 9f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9f90 818 .cfa: sp 0 + .ra: x30
STACK CFI 9f98 .cfa: sp 160 +
STACK CFI 9fa4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9fac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9fb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9fc4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9fd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9fd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a420 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a7b0 20 .cfa: sp 0 + .ra: x30
STACK CFI a7b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a7c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a7d0 284 .cfa: sp 0 + .ra: x30
STACK CFI a7d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a7e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a7e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a7f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a958 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI aa18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI aa20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT aa54 1a4 .cfa: sp 0 + .ra: x30
STACK CFI aa5c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aa70 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aa78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI aab0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aabc x25: .cfa -16 + ^
STACK CFI ab48 x23: x23 x24: x24
STACK CFI ab4c x25: x25
STACK CFI ab68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI ab98 x23: x23 x24: x24 x25: x25
STACK CFI ab9c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aba0 x25: .cfa -16 + ^
STACK CFI aba4 x23: x23 x24: x24 x25: x25
STACK CFI aba8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI abac x25: .cfa -16 + ^
STACK CFI INIT ac00 110 .cfa: sp 0 + .ra: x30
STACK CFI ac08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ac18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ac5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ac78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ac94 x21: .cfa -16 + ^
STACK CFI accc x21: x21
STACK CFI acd0 x21: .cfa -16 + ^
STACK CFI INIT ad10 338 .cfa: sp 0 + .ra: x30
STACK CFI ad18 .cfa: sp 112 +
STACK CFI ad24 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ad30 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ad38 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ada4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI adac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI aea0 x25: x25 x26: x26
STACK CFI aea4 x27: x27 x28: x28
STACK CFI af5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI af64 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI afdc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b024 x25: x25 x26: x26
STACK CFI b028 x27: x27 x28: x28
STACK CFI b030 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b034 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b03c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b044 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT b050 188 .cfa: sp 0 + .ra: x30
STACK CFI b058 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b068 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b070 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b088 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b128 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI b1b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b1c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT b1e0 170 .cfa: sp 0 + .ra: x30
STACK CFI b1e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b1f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b200 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b20c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI b2cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b2d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI b328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b330 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT b350 e8 .cfa: sp 0 + .ra: x30
STACK CFI b358 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b360 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b36c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b410 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b440 d4 .cfa: sp 0 + .ra: x30
STACK CFI b448 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b450 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b45c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b4ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b4f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b514 f8 .cfa: sp 0 + .ra: x30
STACK CFI b51c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b530 x21: .cfa -16 + ^
STACK CFI b5cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b5d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI b5e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b5ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b610 5c .cfa: sp 0 + .ra: x30
STACK CFI b618 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b624 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b670 10c .cfa: sp 0 + .ra: x30
STACK CFI b678 .cfa: sp 112 +
STACK CFI b684 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b68c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b698 x23: .cfa -16 + ^
STACK CFI b6bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b728 x19: x19 x20: x20
STACK CFI b758 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b760 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b76c x19: x19 x20: x20
STACK CFI b778 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT b780 dc .cfa: sp 0 + .ra: x30
STACK CFI b788 .cfa: sp 80 +
STACK CFI b794 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b79c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b7a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b82c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b834 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b860 12c .cfa: sp 0 + .ra: x30
STACK CFI b868 .cfa: sp 96 +
STACK CFI b874 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b87c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b888 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b898 x23: .cfa -16 + ^
STACK CFI b954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b95c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b990 a4 .cfa: sp 0 + .ra: x30
STACK CFI b998 .cfa: sp 32 +
STACK CFI b9a8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b9ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b9f4 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ba28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ba30 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT ba34 74 .cfa: sp 0 + .ra: x30
STACK CFI ba44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ba9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bab0 c4 .cfa: sp 0 + .ra: x30
STACK CFI bab8 .cfa: sp 48 +
STACK CFI bac4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bacc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bb38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bb40 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bb74 12c .cfa: sp 0 + .ra: x30
STACK CFI bb7c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bb88 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bc08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bc10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bc1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bc24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bca0 18 .cfa: sp 0 + .ra: x30
STACK CFI bca8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bcb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bcc0 c4 .cfa: sp 0 + .ra: x30
STACK CFI bcc8 .cfa: sp 48 +
STACK CFI bcd4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bcdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bd38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bd40 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT bd84 d0 .cfa: sp 0 + .ra: x30
STACK CFI bd8c .cfa: sp 48 +
STACK CFI bd98 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bda0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI be20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be28 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT be54 134 .cfa: sp 0 + .ra: x30
STACK CFI be5c .cfa: sp 64 +
STACK CFI be68 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI be70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI be7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI bf4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bf54 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT bf90 64 .cfa: sp 0 + .ra: x30
STACK CFI bfa0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bfa8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bfb8 x21: .cfa -16 + ^
STACK CFI bfdc x21: x21
STACK CFI bfe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bff4 cc .cfa: sp 0 + .ra: x30
STACK CFI bffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c010 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c050 x19: x19 x20: x20
STACK CFI c058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c060 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c084 x19: x19 x20: x20
STACK CFI c0b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c0b8 x19: x19 x20: x20
STACK CFI INIT c0c0 18 .cfa: sp 0 + .ra: x30
STACK CFI c0c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c0d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c0e0 cc .cfa: sp 0 + .ra: x30
STACK CFI c0e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c0fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c13c x19: x19 x20: x20
STACK CFI c144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c14c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c170 x19: x19 x20: x20
STACK CFI c1a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c1a4 x19: x19 x20: x20
STACK CFI INIT c1b0 18 .cfa: sp 0 + .ra: x30
STACK CFI c1b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c1c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c1d0 cc .cfa: sp 0 + .ra: x30
STACK CFI c1d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c22c x19: x19 x20: x20
STACK CFI c234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c23c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c260 x19: x19 x20: x20
STACK CFI c290 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c294 x19: x19 x20: x20
STACK CFI INIT c2a0 18 .cfa: sp 0 + .ra: x30
STACK CFI c2a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c2b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c2c0 fc .cfa: sp 0 + .ra: x30
STACK CFI c2c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c2dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c2e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c340 x19: x19 x20: x20
STACK CFI c344 x21: x21 x22: x22
STACK CFI c348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c350 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c378 x19: x19 x20: x20
STACK CFI c380 x21: x21 x22: x22
STACK CFI c3ac x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c3b0 x19: x19 x20: x20
STACK CFI c3b8 x21: x21 x22: x22
STACK CFI INIT c3c0 18 .cfa: sp 0 + .ra: x30
STACK CFI c3c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c3e0 154 .cfa: sp 0 + .ra: x30
STACK CFI c3e8 .cfa: sp 96 +
STACK CFI c3f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c3fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c418 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c470 x23: .cfa -16 + ^
STACK CFI c4a8 x21: x21 x22: x22
STACK CFI c4ac x23: x23
STACK CFI c4d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c4e0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c4fc x23: x23
STACK CFI c500 x21: x21 x22: x22
STACK CFI c504 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c508 x21: x21 x22: x22
STACK CFI c51c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c520 x23: .cfa -16 + ^
STACK CFI c528 x21: x21 x22: x22
STACK CFI c530 x23: x23
STACK CFI INIT c534 f8 .cfa: sp 0 + .ra: x30
STACK CFI c53c .cfa: sp 80 +
STACK CFI c548 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c550 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c570 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c5b8 x21: x21 x22: x22
STACK CFI c5e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c5ec .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c60c x21: x21 x22: x22
STACK CFI c61c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c624 x21: x21 x22: x22
STACK CFI INIT c630 40 .cfa: sp 0 + .ra: x30
STACK CFI c640 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c648 x19: .cfa -16 + ^
STACK CFI c664 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c670 1e4 .cfa: sp 0 + .ra: x30
STACK CFI c678 .cfa: sp 80 +
STACK CFI c684 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c68c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c6dc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI c6e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c794 x21: x21 x22: x22
STACK CFI c798 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c7a8 x23: .cfa -16 + ^
STACK CFI c7ac x23: x23
STACK CFI c7b0 x23: .cfa -16 + ^
STACK CFI c7c4 x23: x23
STACK CFI c808 x23: .cfa -16 + ^
STACK CFI c83c x23: x23
STACK CFI c840 x21: x21 x22: x22
STACK CFI c844 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c848 x23: .cfa -16 + ^
STACK CFI c84c x23: x23
STACK CFI c850 x23: .cfa -16 + ^
STACK CFI INIT c854 30c .cfa: sp 0 + .ra: x30
STACK CFI c85c .cfa: sp 112 +
STACK CFI c868 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c874 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c88c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c89c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c8ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c9f0 x19: x19 x20: x20
STACK CFI c9f4 x25: x25 x26: x26
STACK CFI ca2c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI ca34 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ca8c x19: x19 x20: x20
STACK CFI ca90 x25: x25 x26: x26
STACK CFI ca94 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI caf0 x19: x19 x20: x20
STACK CFI caf8 x25: x25 x26: x26
STACK CFI cafc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb10 x19: x19 x20: x20
STACK CFI cb14 x25: x25 x26: x26
STACK CFI cb18 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb1c x19: x19 x20: x20
STACK CFI cb24 x25: x25 x26: x26
STACK CFI cb28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb2c x25: x25 x26: x26
STACK CFI cb3c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb44 x19: x19 x20: x20
STACK CFI cb4c x25: x25 x26: x26
STACK CFI cb50 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb54 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI cb58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cb5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT cb60 128 .cfa: sp 0 + .ra: x30
STACK CFI cb68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cb70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cb88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cc08 x21: x21 x22: x22
STACK CFI cc14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cc1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI cc68 x21: x21 x22: x22
STACK CFI cc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cc80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT cc90 8c .cfa: sp 0 + .ra: x30
STACK CFI cc98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cca0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ccec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ccf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT cd20 c8 .cfa: sp 0 + .ra: x30
STACK CFI cd30 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cd38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cd48 x21: .cfa -16 + ^
STACK CFI cdac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cdb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI cdc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cdc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT cdf0 364 .cfa: sp 0 + .ra: x30
STACK CFI cdf8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ce04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ce08 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ce0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ce98 x25: .cfa -16 + ^
STACK CFI cfb4 x25: x25
STACK CFI cfbc x19: x19 x20: x20
STACK CFI cfc0 x21: x21 x22: x22
STACK CFI cfc4 x23: x23 x24: x24
STACK CFI cfc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cfd0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d0fc x25: x25
STACK CFI d11c x25: .cfa -16 + ^
STACK CFI d120 x25: x25
STACK CFI d124 x25: .cfa -16 + ^
STACK CFI d128 x25: x25
STACK CFI d14c x25: .cfa -16 + ^
STACK CFI INIT d154 120 .cfa: sp 0 + .ra: x30
STACK CFI d15c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d164 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d16c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d174 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d20c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT d274 15c .cfa: sp 0 + .ra: x30
STACK CFI d27c .cfa: sp 80 +
STACK CFI d280 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d288 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d2d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d37c x21: x21 x22: x22
STACK CFI d3a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d3ac .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI d3c0 x21: x21 x22: x22
STACK CFI d3c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d3c8 x21: x21 x22: x22
STACK CFI d3cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT d3d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI d3d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d3e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d41c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT d470 aa0 .cfa: sp 0 + .ra: x30
STACK CFI d478 .cfa: sp 208 +
STACK CFI d47c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d484 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d494 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d4b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d4c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d5a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d878 x27: x27 x28: x28
STACK CFI d9d4 x21: x21 x22: x22
STACK CFI d9d8 x25: x25 x26: x26
STACK CFI da08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI da10 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI db08 x27: x27 x28: x28
STACK CFI db14 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI db5c x27: x27 x28: x28
STACK CFI db60 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dcd4 x27: x27 x28: x28
STACK CFI dd00 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dd34 x27: x27 x28: x28
STACK CFI dd58 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dd74 x27: x27 x28: x28
STACK CFI dd7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ddfc x27: x27 x28: x28
STACK CFI de00 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI de08 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI de70 x27: x27 x28: x28
STACK CFI de74 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI de78 x27: x27 x28: x28
STACK CFI de7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI de80 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI de84 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI de88 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI de8c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT df10 1c .cfa: sp 0 + .ra: x30
STACK CFI df18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT df30 1c .cfa: sp 0 + .ra: x30
STACK CFI df38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT df50 f4 .cfa: sp 0 + .ra: x30
STACK CFI df58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI df60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI df68 x21: .cfa -16 + ^
STACK CFI dfb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI dfb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI dfe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI dff0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT e044 8c .cfa: sp 0 + .ra: x30
STACK CFI e04c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e054 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e080 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e0d0 e0 .cfa: sp 0 + .ra: x30
STACK CFI e0d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e0e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e130 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e1b0 3bc .cfa: sp 0 + .ra: x30
STACK CFI e1b8 .cfa: sp 144 +
STACK CFI e1c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e1cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e1e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e1f0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e210 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e254 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e308 x23: x23 x24: x24
STACK CFI e36c x21: x21 x22: x22
STACK CFI e370 x25: x25 x26: x26
STACK CFI e374 x27: x27 x28: x28
STACK CFI e39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e3a4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI e3fc x23: x23 x24: x24
STACK CFI e4ac x21: x21 x22: x22
STACK CFI e4b0 x25: x25 x26: x26
STACK CFI e4b4 x27: x27 x28: x28
STACK CFI e4b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e4c0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI e53c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e540 x23: x23 x24: x24
STACK CFI e544 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e548 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e54c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e550 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e554 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e558 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e55c x23: x23 x24: x24
STACK CFI e560 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e564 x23: x23 x24: x24
STACK CFI e568 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT e570 ec .cfa: sp 0 + .ra: x30
STACK CFI e578 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e588 x21: .cfa -16 + ^
STACK CFI e5c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e5d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e64c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT e660 e4 .cfa: sp 0 + .ra: x30
STACK CFI e668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e678 x21: .cfa -16 + ^
STACK CFI e6b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e6b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI e734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e73c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT e744 140 .cfa: sp 0 + .ra: x30
STACK CFI e74c .cfa: sp 64 +
STACK CFI e758 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e760 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e768 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e7ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e7f4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT e884 90 .cfa: sp 0 + .ra: x30
STACK CFI e88c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e8a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e8ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e8b0 x19: .cfa -32 + ^
STACK CFI e8c4 x19: x19
STACK CFI e8c8 x19: .cfa -32 + ^
STACK CFI e908 x19: x19
STACK CFI e910 x19: .cfa -32 + ^
STACK CFI INIT e914 90 .cfa: sp 0 + .ra: x30
STACK CFI e91c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e93c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e940 x19: .cfa -32 + ^
STACK CFI e954 x19: x19
STACK CFI e958 x19: .cfa -32 + ^
STACK CFI e998 x19: x19
STACK CFI e9a0 x19: .cfa -32 + ^
STACK CFI INIT e9a4 1b0 .cfa: sp 0 + .ra: x30
STACK CFI e9ac .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e9b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e9bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e9c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e9d0 x25: .cfa -16 + ^
STACK CFI eacc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ead4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI eaf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI eafc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT eb54 98 .cfa: sp 0 + .ra: x30
STACK CFI eb5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI eb64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eb70 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ebdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ebe4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ebf0 cc4 .cfa: sp 0 + .ra: x30
STACK CFI ebf8 .cfa: sp 128 +
STACK CFI ec08 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ec18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ecac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ecb4 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI ecbc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ecc8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ed10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ede8 x21: x21 x22: x22
STACK CFI edec x23: x23 x24: x24
STACK CFI edf0 x25: x25 x26: x26
STACK CFI edf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ee0c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ef64 x21: x21 x22: x22
STACK CFI ef6c x23: x23 x24: x24
STACK CFI ef70 x25: x25 x26: x26
STACK CFI ef74 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ef94 x21: x21 x22: x22
STACK CFI ef9c x25: x25 x26: x26
STACK CFI efa0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI efa4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI efb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f190 x21: x21 x22: x22
STACK CFI f194 x23: x23 x24: x24
STACK CFI f198 x25: x25 x26: x26
STACK CFI f19c x27: x27 x28: x28
STACK CFI f1a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f240 x27: x27 x28: x28
STACK CFI f244 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f348 x27: x27 x28: x28
STACK CFI f524 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f5a0 x27: x27 x28: x28
STACK CFI f5f8 x21: x21 x22: x22
STACK CFI f5fc x23: x23 x24: x24
STACK CFI f600 x25: x25 x26: x26
STACK CFI f604 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f648 x27: x27 x28: x28
STACK CFI f730 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f744 x21: x21 x22: x22
STACK CFI f748 x23: x23 x24: x24
STACK CFI f74c x25: x25 x26: x26
STACK CFI f750 x27: x27 x28: x28
STACK CFI f754 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f804 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f80c x27: x27 x28: x28
STACK CFI f844 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f880 x21: x21 x22: x22
STACK CFI f884 x23: x23 x24: x24
STACK CFI f888 x25: x25 x26: x26
STACK CFI f88c x27: x27 x28: x28
STACK CFI f894 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f898 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f89c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f8a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f8a4 x27: x27 x28: x28
STACK CFI f8a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f8ac x27: x27 x28: x28
STACK CFI f8b0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT f8b4 3a8 .cfa: sp 0 + .ra: x30
STACK CFI f8bc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI f8c4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI f8cc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI f8d4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI f914 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI f918 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI f9dc x23: x23 x24: x24
STACK CFI f9e0 x27: x27 x28: x28
STACK CFI fa00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI fa08 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI fc40 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI fc44 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI fc48 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI fc50 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI fc54 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI fc58 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT fc60 1d4 .cfa: sp 0 + .ra: x30
STACK CFI fc68 .cfa: sp 96 +
STACK CFI fc74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fc7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fcb4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI fd1c x21: x21 x22: x22
STACK CFI fd50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd58 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI fd70 x21: x21 x22: x22
STACK CFI fde4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI fe0c x21: x21 x22: x22
STACK CFI fe30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT fe34 9c .cfa: sp 0 + .ra: x30
STACK CFI fe54 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fe5c x19: .cfa -32 + ^
STACK CFI fe7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fe88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT fed0 288 .cfa: sp 0 + .ra: x30
STACK CFI fed8 .cfa: sp 96 +
STACK CFI fee4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI feec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fef4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ff1c x23: .cfa -16 + ^
STACK CFI ffec x23: x23
STACK CFI 1001c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10024 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 10070 x23: .cfa -16 + ^
STACK CFI 100a4 x23: x23
STACK CFI 100ac x23: .cfa -16 + ^
STACK CFI 10130 x23: x23
STACK CFI 1013c x23: .cfa -16 + ^
STACK CFI 1014c x23: x23
STACK CFI 10154 x23: .cfa -16 + ^
STACK CFI INIT 10160 bc .cfa: sp 0 + .ra: x30
STACK CFI 10168 .cfa: sp 64 +
STACK CFI 10174 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1017c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 101f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 101f8 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10220 230 .cfa: sp 0 + .ra: x30
STACK CFI 10228 .cfa: sp 96 +
STACK CFI 1022c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10234 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10270 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10278 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10310 x21: x21 x22: x22
STACK CFI 10314 x23: x23 x24: x24
STACK CFI 10340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10348 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 103e4 x21: x21 x22: x22
STACK CFI 103ec x23: x23 x24: x24
STACK CFI 103f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 10400 x21: x21 x22: x22
STACK CFI 10404 x23: x23 x24: x24
STACK CFI 10408 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1043c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 10448 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1044c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 10450 1c .cfa: sp 0 + .ra: x30
STACK CFI 10458 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10464 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10470 6c .cfa: sp 0 + .ra: x30
STACK CFI 10478 .cfa: sp 48 +
STACK CFI 10488 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 104d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 104d8 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 104e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 104e8 .cfa: sp 48 +
STACK CFI 104f8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 10548 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 10550 d0 .cfa: sp 0 + .ra: x30
STACK CFI 10558 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10560 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10568 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10594 x23: .cfa -16 + ^
STACK CFI 105f4 x23: x23
STACK CFI 10610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1061c x23: x23
STACK CFI INIT 10620 170 .cfa: sp 0 + .ra: x30
STACK CFI 10628 .cfa: sp 96 +
STACK CFI 10634 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1063c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1064c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1073c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10744 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 10790 11c .cfa: sp 0 + .ra: x30
STACK CFI 10798 .cfa: sp 80 +
STACK CFI 1079c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 107a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 107e0 x21: .cfa -16 + ^
STACK CFI 1080c x21: x21
STACK CFI 10834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1083c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10848 x21: x21
STACK CFI 10850 x21: .cfa -16 + ^
STACK CFI 1089c x21: x21
STACK CFI 108a8 x21: .cfa -16 + ^
STACK CFI INIT 108b0 98 .cfa: sp 0 + .ra: x30
STACK CFI 108b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 108c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 108cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 108e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 108f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10950 118 .cfa: sp 0 + .ra: x30
STACK CFI 10958 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10960 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10968 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 109f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10a00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10a70 18 .cfa: sp 0 + .ra: x30
STACK CFI 10a78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10a90 fc .cfa: sp 0 + .ra: x30
STACK CFI 10aa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10aa8 x19: .cfa -16 + ^
STACK CFI 10b60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10b74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10b7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10b84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10b90 34 .cfa: sp 0 + .ra: x30
STACK CFI 10b9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10bc4 230 .cfa: sp 0 + .ra: x30
STACK CFI 10bcc .cfa: sp 128 +
STACK CFI 10bd8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10be0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10bf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10d5c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10df4 188 .cfa: sp 0 + .ra: x30
STACK CFI 10dfc .cfa: sp 96 +
STACK CFI 10e08 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10e10 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10e18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10e20 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10e28 x25: .cfa -16 + ^
STACK CFI 10eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 10ef4 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 10f80 4a4 .cfa: sp 0 + .ra: x30
STACK CFI 10f88 .cfa: sp 128 +
STACK CFI 10f94 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10fa0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10fa8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 110ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 110f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1119c x27: x27 x28: x28
STACK CFI 111b0 x25: x25 x26: x26
STACK CFI 111ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 111f4 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 1126c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 112a0 x27: x27 x28: x28
STACK CFI 112a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 112d8 x25: x25 x26: x26
STACK CFI 112dc x27: x27 x28: x28
STACK CFI 112e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11310 x27: x27 x28: x28
STACK CFI 11314 x25: x25 x26: x26
STACK CFI 113f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11404 x27: x27 x28: x28
STACK CFI 1140c x25: x25 x26: x26
STACK CFI 1141c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11420 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 11424 214 .cfa: sp 0 + .ra: x30
STACK CFI 1142c .cfa: sp 336 +
STACK CFI 11438 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11440 x23: .cfa -16 + ^
STACK CFI 11458 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1147c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 115cc x19: x19 x20: x20
STACK CFI 115d0 x21: x21 x22: x22
STACK CFI 115f8 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 11600 .cfa: sp 336 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 11604 x19: x19 x20: x20
STACK CFI 11608 x21: x21 x22: x22
STACK CFI 1160c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11610 x19: x19 x20: x20
STACK CFI 11618 x21: x21 x22: x22
STACK CFI 1161c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11624 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 11630 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11634 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 11640 1c .cfa: sp 0 + .ra: x30
STACK CFI 11648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11654 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11660 1c .cfa: sp 0 + .ra: x30
STACK CFI 11668 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11674 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11680 208 .cfa: sp 0 + .ra: x30
STACK CFI 11688 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11694 .cfa: x29 80 +
STACK CFI 11698 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 116a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 116c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 11790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 11798 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11890 228 .cfa: sp 0 + .ra: x30
STACK CFI 11898 .cfa: sp 96 +
STACK CFI 118a4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 118ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 118e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 118e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1193c x21: x21 x22: x22
STACK CFI 11940 x23: x23 x24: x24
STACK CFI 11970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11978 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 119b8 x25: .cfa -16 + ^
STACK CFI 11a30 x21: x21 x22: x22
STACK CFI 11a34 x23: x23 x24: x24
STACK CFI 11a38 x25: x25
STACK CFI 11a3c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 11a40 x25: x25
STACK CFI 11a80 x21: x21 x22: x22
STACK CFI 11a84 x23: x23 x24: x24
STACK CFI 11a88 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11a98 x21: x21 x22: x22
STACK CFI 11a9c x23: x23 x24: x24
STACK CFI 11aa4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11aa8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11aac x25: .cfa -16 + ^
STACK CFI INIT 11ac0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 11ac8 .cfa: sp 80 +
STACK CFI 11adc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11ae8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11b74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11bac x21: x21 x22: x22
STACK CFI 11c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11c40 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 11c64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11c68 x21: x21 x22: x22
STACK CFI 11c6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 11c74 b0 .cfa: sp 0 + .ra: x30
STACK CFI 11c7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11c84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11c98 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 11d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11d1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11d24 964 .cfa: sp 0 + .ra: x30
STACK CFI 11d2c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11d3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11d48 .cfa: sp 544 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11d90 x27: .cfa -16 + ^
STACK CFI 11d94 x28: .cfa -8 + ^
STACK CFI 11e00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11e04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 120e4 x21: x21 x22: x22
STACK CFI 120ec x23: x23 x24: x24
STACK CFI 120f0 x27: x27
STACK CFI 120f4 x28: x28
STACK CFI 12114 .cfa: sp 96 +
STACK CFI 12120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 12128 .cfa: sp 544 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 12158 x27: x27
STACK CFI 12160 x28: x28
STACK CFI 12164 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12400 x21: x21 x22: x22
STACK CFI 12408 x23: x23 x24: x24
STACK CFI 1240c x27: x27
STACK CFI 12410 x28: x28
STACK CFI 12414 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12434 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 12468 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12488 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 124b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1254c x21: x21 x22: x22
STACK CFI 12554 x23: x23 x24: x24
STACK CFI 12558 x27: x27
STACK CFI 1255c x28: x28
STACK CFI 12560 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12568 x21: x21 x22: x22
STACK CFI 12570 x23: x23 x24: x24
STACK CFI 12574 x27: x27
STACK CFI 12578 x28: x28
STACK CFI 1257c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12604 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 12608 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1260c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12610 x27: .cfa -16 + ^
STACK CFI 12614 x28: .cfa -8 + ^
STACK CFI INIT 12690 88c .cfa: sp 0 + .ra: x30
STACK CFI 12698 .cfa: sp 160 +
STACK CFI 126a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 126b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 126bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 126dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12768 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1276c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12840 x25: x25 x26: x26
STACK CFI 12848 x27: x27 x28: x28
STACK CFI 1287c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12884 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 128a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12918 x25: x25 x26: x26
STACK CFI 1291c x27: x27 x28: x28
STACK CFI 12920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12928 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 12938 x25: x25 x26: x26
STACK CFI 1293c x27: x27 x28: x28
STACK CFI 12940 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12a70 x25: x25 x26: x26
STACK CFI 12a74 x27: x27 x28: x28
STACK CFI 12a78 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12adc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12af8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12b4c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12bb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 12bb4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12bc4 x25: x25 x26: x26
STACK CFI 12bc8 x27: x27 x28: x28
STACK CFI 12c14 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12c38 x25: x25 x26: x26
STACK CFI 12c40 x27: x27 x28: x28
STACK CFI 12c88 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12cc4 x25: x25 x26: x26
STACK CFI 12cc8 x27: x27 x28: x28
STACK CFI 12ccc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12cf4 x25: x25 x26: x26
STACK CFI 12cf8 x27: x27 x28: x28
STACK CFI 12cfc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12d28 x25: x25 x26: x26
STACK CFI 12d2c x27: x27 x28: x28
STACK CFI 12d30 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12d5c x25: x25 x26: x26
STACK CFI 12d60 x27: x27 x28: x28
STACK CFI 12d64 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12d90 x25: x25 x26: x26
STACK CFI 12d94 x27: x27 x28: x28
STACK CFI 12d98 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12dc4 x25: x25 x26: x26
STACK CFI 12dc8 x27: x27 x28: x28
STACK CFI 12dcc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12df4 x25: x25 x26: x26
STACK CFI 12df8 x27: x27 x28: x28
STACK CFI 12dfc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12e24 x25: x25 x26: x26
STACK CFI 12e28 x27: x27 x28: x28
STACK CFI 12e2c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12e90 x25: x25 x26: x26
STACK CFI 12e94 x27: x27 x28: x28
STACK CFI 12e98 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12eb0 x25: x25 x26: x26
STACK CFI 12eb8 x27: x27 x28: x28
STACK CFI 12ebc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12ec8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12ecc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 12ed0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12ed4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12f08 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12f14 x25: x25 x26: x26
STACK CFI 12f18 x27: x27 x28: x28
STACK CFI INIT 12f20 190 .cfa: sp 0 + .ra: x30
STACK CFI 12f28 .cfa: sp 80 +
STACK CFI 12f38 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12f48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 12f54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13048 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 130b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 130b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 130e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 130e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 13110 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 13118 .cfa: sp 80 +
STACK CFI 13124 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1312c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13138 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 13228 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 132f4 20c .cfa: sp 0 + .ra: x30
STACK CFI 132fc .cfa: sp 128 +
STACK CFI 1330c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1331c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13328 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13334 x25: .cfa -16 + ^
STACK CFI 133dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 133e4 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13500 b4 .cfa: sp 0 + .ra: x30
STACK CFI 13508 .cfa: sp 64 +
STACK CFI 1351c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13578 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 13580 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 135b4 4ec .cfa: sp 0 + .ra: x30
STACK CFI 135bc .cfa: sp 128 +
STACK CFI 135c8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 135d0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 135dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 135e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 135f4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 136e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 136ec .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13944 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 13a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 13a5c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13aa0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4440 24 .cfa: sp 0 + .ra: x30
STACK CFI 4444 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 445c .cfa: sp 0 + .ra: .ra x29: x29
