MODULE Linux arm64 509ADA22957162595C1D6B8A30B555740 libayatana-appindicator3.so.1
INFO CODE_ID 22DA9A50719559625C1D6B8A30B55574D0411539
PUBLIC 47f4 0 _application_service_marshal_VOID__STRING_STRING
PUBLIC 48a0 0 _application_service_marshal_VOID__INT_UINT
PUBLIC 5100 0 app_indicator_get_type
PUBLIC 5d74 0 app_indicator_set_attention_icon_full
PUBLIC 5fe0 0 app_indicator_set_attention_icon
PUBLIC 6000 0 app_indicator_set_icon_full
PUBLIC 6270 0 app_indicator_set_icon
PUBLIC 6290 0 app_indicator_set_label
PUBLIC 6354 0 app_indicator_set_icon_theme_path
PUBLIC 6594 0 app_indicator_set_menu
PUBLIC 6770 0 app_indicator_set_ordering_index
PUBLIC 6800 0 app_indicator_set_secondary_activate_target
PUBLIC 6974 0 app_indicator_set_title
PUBLIC 6a14 0 app_indicator_get_id
PUBLIC 6aa4 0 app_indicator_get_category
PUBLIC 6b40 0 app_indicator_get_status
PUBLIC 6bd4 0 app_indicator_get_icon
PUBLIC 6c70 0 app_indicator_get_icon_desc
PUBLIC 6d04 0 app_indicator_get_icon_theme_path
PUBLIC 6da0 0 app_indicator_get_attention_icon
PUBLIC 70f0 0 app_indicator_get_attention_icon_desc
PUBLIC 7184 0 app_indicator_get_title
PUBLIC 7390 0 app_indicator_get_menu
PUBLIC 7500 0 app_indicator_get_label
PUBLIC 7594 0 app_indicator_get_label_guide
PUBLIC 7630 0 app_indicator_get_secondary_activate_target
PUBLIC 76c4 0 app_indicator_build_menu_from_desktop
PUBLIC 78c4 0 app_indicator_category_get_type
PUBLIC 7924 0 app_indicator_new_with_path
PUBLIC 79c0 0 app_indicator_new
PUBLIC 7a40 0 app_indicator_status_get_type
PUBLIC 8104 0 app_indicator_set_status
PUBLIC 8a80 0 _application_service_marshal_VOID__STRING_INT_STRING_STRING_STRING_STRING_STRING
PUBLIC 8b50 0 _application_service_marshal_VOID__INT_STRING_STRING
PUBLIC 8c00 0 _application_service_marshal_VOID__INT_STRING
PUBLIC 8cb0 0 _application_service_marshal_VOID__BOOLEAN_STRING_OBJECT
PUBLIC 8d60 0 _application_service_marshal_VOID__INT_INT
PUBLIC 8e10 0 _generate_id
PUBLIC 8ec0 0 app_indicator_get_ordering_index
STACK CFI INIT 3d00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d70 48 .cfa: sp 0 + .ra: x30
STACK CFI 3d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d7c x19: .cfa -16 + ^
STACK CFI 3db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3dc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3dd0 6b0 .cfa: sp 0 + .ra: x30
STACK CFI 3dd8 .cfa: sp 128 +
STACK CFI 3de4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3dec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3df8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3e00 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4360 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4480 150 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4490 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 449c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 45d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 45d8 .cfa: sp 48 +
STACK CFI 45e8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 46b8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 46c0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 46c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 477c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4784 70 .cfa: sp 0 + .ra: x30
STACK CFI 478c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 479c x19: .cfa -16 + ^
STACK CFI 47ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47f4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4808 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4810 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 481c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4860 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 48a0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 48b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 48c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 490c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4950 7c .cfa: sp 0 + .ra: x30
STACK CFI 4958 .cfa: sp 48 +
STACK CFI 4968 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4974 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 49d0 144 .cfa: sp 0 + .ra: x30
STACK CFI 49d8 .cfa: sp 80 +
STACK CFI 49ec .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4a0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a54 x23: .cfa -16 + ^
STACK CFI 4ac8 x23: x23
STACK CFI 4afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b04 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4b08 x23: x23
STACK CFI 4b10 x23: .cfa -16 + ^
STACK CFI INIT 4b14 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 4b1c .cfa: sp 64 +
STACK CFI 4b20 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c18 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cac .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4cd0 cc .cfa: sp 0 + .ra: x30
STACK CFI 4cd8 .cfa: sp 48 +
STACK CFI 4ce8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cf0 x19: .cfa -16 + ^
STACK CFI 4d68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d70 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4da0 210 .cfa: sp 0 + .ra: x30
STACK CFI 4da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4db4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4e3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4e90 x21: .cfa -16 + ^
STACK CFI 4ee8 x21: x21
STACK CFI 4f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f84 x21: x21
STACK CFI 4f88 x21: .cfa -16 + ^
STACK CFI 4f94 x21: x21
STACK CFI INIT 4fb0 148 .cfa: sp 0 + .ra: x30
STACK CFI 4fbc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4fc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4fd8 x21: .cfa -16 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5100 70 .cfa: sp 0 + .ra: x30
STACK CFI 5108 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5110 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 513c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5170 140 .cfa: sp 0 + .ra: x30
STACK CFI 5178 .cfa: sp 64 +
STACK CFI 5184 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 518c x21: .cfa -16 + ^
STACK CFI 5198 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5258 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52b0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 52b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 52d0 x21: .cfa -16 + ^
STACK CFI 5320 x21: x21
STACK CFI 5330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 533c x21: x21
STACK CFI 5368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 53a4 e8 .cfa: sp 0 + .ra: x30
STACK CFI 53ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5460 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 546c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5490 274 .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 96 +
STACK CFI 54a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 54ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 54b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 54fc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 557c x23: x23 x24: x24
STACK CFI 5580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5588 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 55e8 x23: x23 x24: x24
STACK CFI 5620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5638 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 56ec x23: x23 x24: x24
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 56f8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 56fc x23: x23 x24: x24
STACK CFI 5700 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5704 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 570c .cfa: sp 48 +
STACK CFI 5718 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 57ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57f4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 58b0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 58b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 591c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5924 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5960 174 .cfa: sp 0 + .ra: x30
STACK CFI 5968 .cfa: sp 80 +
STACK CFI 5974 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 597c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5988 x21: .cfa -16 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5a08 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5a44 v8: .cfa -8 + ^
STACK CFI 5a80 v8: v8
STACK CFI 5a84 v8: .cfa -8 + ^
STACK CFI 5ac4 v8: v8
STACK CFI 5ad0 v8: .cfa -8 + ^
STACK CFI INIT 5ad4 110 .cfa: sp 0 + .ra: x30
STACK CFI 5adc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ae4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5be4 9c .cfa: sp 0 + .ra: x30
STACK CFI 5bec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bf4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5c80 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5c88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5d74 268 .cfa: sp 0 + .ra: x30
STACK CFI 5d7c .cfa: sp 80 +
STACK CFI 5d88 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5d90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d9c x23: .cfa -16 + ^
STACK CFI 5de0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5e38 x21: x21 x22: x22
STACK CFI 5e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 5e48 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 5e98 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5f70 x21: x21 x22: x22
STACK CFI 5fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 5fc0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5fd4 x21: x21 x22: x22
STACK CFI 5fd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 5fe0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5fe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ff4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6000 270 .cfa: sp 0 + .ra: x30
STACK CFI 6008 .cfa: sp 80 +
STACK CFI 6014 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 601c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6024 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 606c x23: .cfa -16 + ^
STACK CFI 618c x23: x23
STACK CFI 6190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6198 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 61b4 x23: x23
STACK CFI 61ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6204 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 623c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6254 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6268 x23: x23
STACK CFI 626c x23: .cfa -16 + ^
STACK CFI INIT 6270 1c .cfa: sp 0 + .ra: x30
STACK CFI 6278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6290 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6298 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62ac x21: .cfa -16 + ^
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6324 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6338 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6354 240 .cfa: sp 0 + .ra: x30
STACK CFI 635c .cfa: sp 64 +
STACK CFI 6368 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6370 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6378 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6408 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6538 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6594 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 659c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 65a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6608 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6684 x21: x21 x22: x22
STACK CFI 6690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6698 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 66a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 66d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6700 x23: .cfa -16 + ^
STACK CFI 671c x23: x23
STACK CFI 6748 x21: x21 x22: x22
STACK CFI 6750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6770 90 .cfa: sp 0 + .ra: x30
STACK CFI 6778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6780 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 67cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 67d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 67e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6800 174 .cfa: sp 0 + .ra: x30
STACK CFI 6808 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6810 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 684c x21: .cfa -16 + ^
STACK CFI 68d4 x21: x21
STACK CFI 68ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6904 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6930 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6938 x21: x21
STACK CFI 693c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6944 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6950 x21: x21
STACK CFI 6958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6974 a0 .cfa: sp 0 + .ra: x30
STACK CFI 697c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6984 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 69dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 69e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 69f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6a14 90 .cfa: sp 0 + .ra: x30
STACK CFI 6a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a24 x19: .cfa -16 + ^
STACK CFI 6a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6a70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6aa4 94 .cfa: sp 0 + .ra: x30
STACK CFI 6aac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ab4 x19: .cfa -16 + ^
STACK CFI 6afc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6b30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b40 94 .cfa: sp 0 + .ra: x30
STACK CFI 6b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b50 x19: .cfa -16 + ^
STACK CFI 6b98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6ba0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6bcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6bd4 94 .cfa: sp 0 + .ra: x30
STACK CFI 6bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6be4 x19: .cfa -16 + ^
STACK CFI 6c2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6c34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6c60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6c70 94 .cfa: sp 0 + .ra: x30
STACK CFI 6c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c80 x19: .cfa -16 + ^
STACK CFI 6cc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6cfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6d04 94 .cfa: sp 0 + .ra: x30
STACK CFI 6d0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d14 x19: .cfa -16 + ^
STACK CFI 6d5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6d90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6da0 94 .cfa: sp 0 + .ra: x30
STACK CFI 6da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6db0 x19: .cfa -16 + ^
STACK CFI 6df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6e2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e34 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 6e3c .cfa: sp 96 +
STACK CFI 6e4c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e68 x23: .cfa -16 + ^
STACK CFI 6f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6f84 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6fd8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 70d4 1c .cfa: sp 0 + .ra: x30
STACK CFI 70dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 70e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 70f0 94 .cfa: sp 0 + .ra: x30
STACK CFI 70f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7100 x19: .cfa -16 + ^
STACK CFI 7148 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7150 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 717c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7184 94 .cfa: sp 0 + .ra: x30
STACK CFI 718c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7194 x19: .cfa -16 + ^
STACK CFI 71dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 71e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7210 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7220 16c .cfa: sp 0 + .ra: x30
STACK CFI 7228 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7230 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7238 x21: .cfa -16 + ^
STACK CFI 7384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7390 94 .cfa: sp 0 + .ra: x30
STACK CFI 7398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73a0 x19: .cfa -16 + ^
STACK CFI 73e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 741c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7424 68 .cfa: sp 0 + .ra: x30
STACK CFI 742c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7434 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 747c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7490 68 .cfa: sp 0 + .ra: x30
STACK CFI 7498 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 74f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7500 94 .cfa: sp 0 + .ra: x30
STACK CFI 7508 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7510 x19: .cfa -16 + ^
STACK CFI 7558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7560 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 758c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7594 94 .cfa: sp 0 + .ra: x30
STACK CFI 759c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75a4 x19: .cfa -16 + ^
STACK CFI 75ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 75f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7620 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7630 94 .cfa: sp 0 + .ra: x30
STACK CFI 7638 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7640 x19: .cfa -16 + ^
STACK CFI 7688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7690 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 76bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 76c4 200 .cfa: sp 0 + .ra: x30
STACK CFI 76cc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 76d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7718 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7760 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7774 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 77f8 x25: x25 x26: x26
STACK CFI 77fc x27: x27 x28: x28
STACK CFI 7828 x23: x23 x24: x24
STACK CFI 782c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7834 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 7848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7864 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 78a4 x23: x23 x24: x24
STACK CFI 78ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 78c4 60 .cfa: sp 0 + .ra: x30
STACK CFI 78cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 78d4 x19: .cfa -16 + ^
STACK CFI 78ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 78f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 791c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7924 9c .cfa: sp 0 + .ra: x30
STACK CFI 792c .cfa: sp 80 +
STACK CFI 7930 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7938 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7940 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 794c x23: .cfa -16 + ^
STACK CFI 79b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 79c0 7c .cfa: sp 0 + .ra: x30
STACK CFI 79c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7a40 60 .cfa: sp 0 + .ra: x30
STACK CFI 7a48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a50 x19: .cfa -16 + ^
STACK CFI 7a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7a70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7a98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7aa0 218 .cfa: sp 0 + .ra: x30
STACK CFI 7aa8 .cfa: sp 64 +
STACK CFI 7ab0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7abc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ac8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b44 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b84 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7bac .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7c74 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7cb0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7cc0 444 .cfa: sp 0 + .ra: x30
STACK CFI 7cc8 .cfa: sp 96 +
STACK CFI 7cd4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7ce8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7d3c x23: .cfa -16 + ^
STACK CFI 7d88 x23: x23
STACK CFI 7d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7d94 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7e48 x23: x23
STACK CFI 7e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7e54 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 7ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7eb0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7fb4 x23: x23
STACK CFI 7fb8 x23: .cfa -16 + ^
STACK CFI 8018 x23: x23
STACK CFI 8020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8028 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 807c x23: x23
STACK CFI 8080 x23: .cfa -16 + ^
STACK CFI 80d4 x23: x23
STACK CFI 80d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80e0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 80fc x23: x23
STACK CFI INIT 8104 1cc .cfa: sp 0 + .ra: x30
STACK CFI 810c .cfa: sp 80 +
STACK CFI 8118 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8120 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8164 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 81a8 x21: x21 x22: x22
STACK CFI 81ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 81b4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 81b8 x23: .cfa -16 + ^
STACK CFI 8264 x23: x23
STACK CFI 8268 x21: x21 x22: x22
STACK CFI 8298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82b4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 82b8 x23: x23
STACK CFI 82c0 x23: .cfa -16 + ^
STACK CFI 82c4 x21: x21 x22: x22 x23: x23
STACK CFI 82c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 82cc x23: .cfa -16 + ^
STACK CFI INIT 82d0 194 .cfa: sp 0 + .ra: x30
STACK CFI 82d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82e0 x21: .cfa -16 + ^
STACK CFI 82ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8420 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8464 614 .cfa: sp 0 + .ra: x30
STACK CFI 846c .cfa: sp 96 +
STACK CFI 8478 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8480 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 848c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8498 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8540 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8548 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8650 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 869c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 86a8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 870c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 875c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8764 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8830 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8910 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8a80 c8 .cfa: sp 0 + .ra: x30
STACK CFI 8a94 .cfa: sp 64 +
STACK CFI 8a98 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8aa0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8aac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b0c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8b50 ac .cfa: sp 0 + .ra: x30
STACK CFI 8b64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8b6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8c00 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8c1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8c28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8cb0 ac .cfa: sp 0 + .ra: x30
STACK CFI 8cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8ccc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8cd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8d20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8d3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8d60 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8d88 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8e10 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8e18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e20 x19: .cfa -16 + ^
STACK CFI 8e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8ec0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ed0 x19: .cfa -16 + ^
STACK CFI 8f1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8f50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8f64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
