MODULE Linux arm64 BA9CCEC46506119B346AFBCBD9F869D50 libauthkrb5-samba4.so.0
INFO CODE_ID C4CE9CBA06659B11346AFBCBD9F869D507C655BC
PUBLIC 5d50 0 packet_full_request_u32
PUBLIC 75a0 0 kerberos_encode_pac
PUBLIC 7ba0 0 kerberos_create_pac
PUBLIC 7f04 0 kerberos_pac_to_user_info_dc
PUBLIC 8540 0 kerberos_pac_blob_to_user_info_dc
PUBLIC 8620 0 tls_cert_generate
PUBLIC a230 0 tls_verify_peer_string
PUBLIC ac14 0 tstream_tls_params_enabled
PUBLIC ac34 0 tstream_tls_params_client
PUBLIC b080 0 _tstream_tls_connect_send
PUBLIC b4f0 0 tstream_tls_connect_recv
PUBLIC b580 0 tstream_tls_params_server
PUBLIC bc44 0 _tstream_tls_accept_send
PUBLIC c080 0 tstream_tls_accept_recv
PUBLIC c110 0 smb_krb5_set_send_to_kdc_func
PUBLIC c370 0 smb_krb5_init_context_basic
PUBLIC c6b0 0 smb_krb5_init_context
PUBLIC c954 0 smb_krb5_context_set_event_ctx
PUBLIC caa0 0 smb_krb5_context_remove_event_ctx
PUBLIC cb94 0 packet_init
PUBLIC cbe0 0 packet_set_callback
PUBLIC cc00 0 packet_set_error_handler
PUBLIC cc20 0 packet_set_private
PUBLIC cc40 0 packet_set_full_request
PUBLIC cc60 0 packet_set_socket
PUBLIC cc80 0 packet_set_event_context
PUBLIC cca0 0 packet_set_fde
PUBLIC ccc0 0 packet_set_serialise
PUBLIC cce0 0 packet_set_initial_read
PUBLIC cd00 0 packet_set_nofree
PUBLIC cd20 0 packet_set_unreliable_select
PUBLIC cd40 0 packet_recv
PUBLIC d350 0 packet_recv_disable
PUBLIC d370 0 packet_recv_enable
PUBLIC d420 0 packet_queue_run
PUBLIC d904 0 packet_send_callback
PUBLIC da50 0 packet_send
PUBLIC e020 0 smb_krb5_send_and_recv_func
PUBLIC e190 0 smb_krb5_send_and_recv_func_forced_tcp
PUBLIC e2a0 0 packet_full_request_nbt
PUBLIC e2f0 0 packet_full_request_u16
PUBLIC e340 0 gssapi_error_string
PUBLIC e494 0 gssapi_obtain_pac_blob
PUBLIC e674 0 gssapi_get_session_key
PUBLIC e944 0 check_pac_checksum
PUBLIC eae0 0 kerberos_decode_pac
PUBLIC fb64 0 kerberos_pac_logon_info
PUBLIC fc30 0 auth4_context_for_PAC_DATA_CTR
PUBLIC fc70 0 auth4_context_get_PAC_DATA_CTR
PUBLIC fd80 0 gssapi_get_sig_size
PUBLIC ff60 0 gssapi_seal_packet
PUBLIC 10244 0 gssapi_unseal_packet
PUBLIC 10570 0 gssapi_sign_packet
PUBLIC 106f0 0 gssapi_check_packet
STACK CFI INIT 5c60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cdc x19: .cfa -16 + ^
STACK CFI 5d14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d30 18 .cfa: sp 0 + .ra: x30
STACK CFI 5d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5d50 4c .cfa: sp 0 + .ra: x30
STACK CFI 5d58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5da0 20 .cfa: sp 0 + .ra: x30
STACK CFI 5da8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5db4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5dc0 18 .cfa: sp 0 + .ra: x30
STACK CFI 5dc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5de0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5e20 70 .cfa: sp 0 + .ra: x30
STACK CFI 5e28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e34 x19: .cfa -16 + ^
STACK CFI 5e4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5e88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e90 16c .cfa: sp 0 + .ra: x30
STACK CFI 5e98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ea0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ec0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 5f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5f74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6000 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 6008 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6010 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6020 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6030 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6090 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 60f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6100 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 61b4 38 .cfa: sp 0 + .ra: x30
STACK CFI 61bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61c4 x19: .cfa -16 + ^
STACK CFI 61e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 61f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6200 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 623c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6244 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6260 168 .cfa: sp 0 + .ra: x30
STACK CFI 6268 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6270 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6298 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62e8 x19: x19 x20: x20
STACK CFI 62ec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 62f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 62f8 x19: x19 x20: x20
STACK CFI 6300 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6308 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 631c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6328 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6344 x19: x19 x20: x20
STACK CFI 634c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6354 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 63d0 14c .cfa: sp 0 + .ra: x30
STACK CFI 63d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 63e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6434 x23: .cfa -16 + ^
STACK CFI 64d4 x23: x23
STACK CFI 64ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 64f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6514 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6520 19c .cfa: sp 0 + .ra: x30
STACK CFI 6528 .cfa: sp 96 +
STACK CFI 6534 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 653c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6548 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6554 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 655c x25: .cfa -16 + ^
STACK CFI 6678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6680 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 66c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 66c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66dc x21: .cfa -16 + ^
STACK CFI 674c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6754 94 .cfa: sp 0 + .ra: x30
STACK CFI 675c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6770 x21: .cfa -16 + ^
STACK CFI 67e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 67f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 67f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6800 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6830 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 6838 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6844 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6850 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 68ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 68f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6910 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6950 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6980 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 699c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 69e4 134 .cfa: sp 0 + .ra: x30
STACK CFI 69ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 69f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6a00 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6af4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 6b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6b20 19c .cfa: sp 0 + .ra: x30
STACK CFI 6b28 .cfa: sp 96 +
STACK CFI 6b34 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6b3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6b54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6b5c x25: .cfa -16 + ^
STACK CFI 6c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6c80 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6cc0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 6cc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6cf8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6d2c x19: x19 x20: x20
STACK CFI 6d3c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6d48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d4c x19: x19 x20: x20
STACK CFI 6d54 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6d5c .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d70 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6da8 x19: x19 x20: x20
STACK CFI 6db0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6db8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e74 128 .cfa: sp 0 + .ra: x30
STACK CFI 6e7c .cfa: sp 64 +
STACK CFI 6e88 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6e9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6f60 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6fa0 48 .cfa: sp 0 + .ra: x30
STACK CFI 6fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fb0 x19: .cfa -16 + ^
STACK CFI 6fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ff0 4c .cfa: sp 0 + .ra: x30
STACK CFI 6ff8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7000 x19: .cfa -16 + ^
STACK CFI 7034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7040 5c .cfa: sp 0 + .ra: x30
STACK CFI 7048 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7050 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7064 x21: .cfa -16 + ^
STACK CFI 7094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 70a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 70a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70bc x19: .cfa -16 + ^
STACK CFI 70e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 70f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7124 34 .cfa: sp 0 + .ra: x30
STACK CFI 712c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7138 x19: .cfa -16 + ^
STACK CFI 7150 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7160 84 .cfa: sp 0 + .ra: x30
STACK CFI 7168 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7174 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 71a0 x21: .cfa -16 + ^
STACK CFI 71c0 x21: x21
STACK CFI 71d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 71dc x21: x21
STACK CFI INIT 71e4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 71ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71f4 x19: .cfa -16 + ^
STACK CFI 7224 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 722c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7290 118 .cfa: sp 0 + .ra: x30
STACK CFI 7298 .cfa: sp 80 +
STACK CFI 72a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 72ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 72b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7368 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 739c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73a4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 73b0 118 .cfa: sp 0 + .ra: x30
STACK CFI 73b8 .cfa: sp 80 +
STACK CFI 73c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 73cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 73d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7488 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 74bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 74c4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 74d0 cc .cfa: sp 0 + .ra: x30
STACK CFI 74d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 74fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7508 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 753c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7544 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7570 x21: .cfa -16 + ^
STACK CFI 7598 x21: x21
STACK CFI INIT 75a0 5f8 .cfa: sp 0 + .ra: x30
STACK CFI 75a8 .cfa: sp 160 +
STACK CFI 75b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 75c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 75c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 75d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 75e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7638 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 77cc x25: x25 x26: x26
STACK CFI 77d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 77f8 x25: x25 x26: x26
STACK CFI 7830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7838 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 785c x25: x25 x26: x26
STACK CFI 7860 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7878 x25: x25 x26: x26
STACK CFI 7880 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78a4 x25: x25 x26: x26
STACK CFI 78a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78cc x25: x25 x26: x26
STACK CFI 78d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7900 x25: x25 x26: x26
STACK CFI 7904 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7908 x25: x25 x26: x26
STACK CFI 7950 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7988 x25: x25 x26: x26
STACK CFI 798c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7a04 x25: x25 x26: x26
STACK CFI 7a08 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b90 x25: x25 x26: x26
STACK CFI 7b94 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 7ba0 364 .cfa: sp 0 + .ra: x30
STACK CFI 7ba8 .cfa: sp 160 +
STACK CFI 7bb4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bc8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7bd8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7c40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7d38 x21: x21 x22: x22
STACK CFI 7d3c x27: x27 x28: x28
STACK CFI 7d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7d78 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7dcc x21: x21 x22: x22
STACK CFI 7dd0 x27: x27 x28: x28
STACK CFI 7dd4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7de8 x21: x21 x22: x22
STACK CFI 7dec x27: x27 x28: x28
STACK CFI 7e0c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7e24 x21: x21 x22: x22
STACK CFI 7e28 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e40 x21: x21 x22: x22
STACK CFI 7e44 x27: x27 x28: x28
STACK CFI 7e48 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e60 x21: x21 x22: x22
STACK CFI 7e64 x27: x27 x28: x28
STACK CFI 7e68 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e80 x21: x21 x22: x22
STACK CFI 7e84 x27: x27 x28: x28
STACK CFI 7e88 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7eb0 x21: x21 x22: x22
STACK CFI 7eb4 x27: x27 x28: x28
STACK CFI 7eb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7ef8 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 7efc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f00 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7f04 63c .cfa: sp 0 + .ra: x30
STACK CFI 7f0c .cfa: sp 416 +
STACK CFI 7f18 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f24 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f3c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7f48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7f54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 81c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 81d0 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8540 dc .cfa: sp 0 + .ra: x30
STACK CFI 8548 .cfa: sp 80 +
STACK CFI 8554 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 855c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 856c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8574 x23: .cfa -16 + ^
STACK CFI 8604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 860c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8620 1c0c .cfa: sp 0 + .ra: x30
STACK CFI 8628 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8640 .cfa: sp 4400 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 86d4 .cfa: sp 96 +
STACK CFI 86e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 86ec .cfa: sp 4400 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 87dc x25: .cfa -32 + ^
STACK CFI 87e8 x26: .cfa -24 + ^
STACK CFI 8814 x27: .cfa -16 + ^
STACK CFI 8818 x28: .cfa -8 + ^
STACK CFI 8bcc x25: x25
STACK CFI 8bd0 x26: x26
STACK CFI 8bd4 x27: x27
STACK CFI 8bd8 x28: x28
STACK CFI 8e10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8e20 x25: x25
STACK CFI 8e24 x26: x26
STACK CFI 8e5c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8e6c x25: x25
STACK CFI 8e70 x26: x26
STACK CFI 8e74 x27: x27
STACK CFI 8e78 x28: x28
STACK CFI 8e7c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8e8c x25: x25
STACK CFI 8e90 x26: x26
STACK CFI 8e94 x27: x27
STACK CFI 8e98 x28: x28
STACK CFI 8ed0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8ee0 x25: x25
STACK CFI 8ee4 x26: x26
STACK CFI 8ee8 x27: x27
STACK CFI 8eec x28: x28
STACK CFI 8ef0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8f08 x25: x25
STACK CFI 8f0c x26: x26
STACK CFI 8f10 x27: x27
STACK CFI 8f14 x28: x28
STACK CFI 8f18 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8f30 x25: x25
STACK CFI 8f34 x26: x26
STACK CFI 8f38 x27: x27
STACK CFI 8f3c x28: x28
STACK CFI 8f88 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8fa0 x25: x25
STACK CFI 8fa4 x26: x26
STACK CFI 8fa8 x27: x27
STACK CFI 8fac x28: x28
STACK CFI 8fb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8fc8 x25: x25
STACK CFI 8fcc x26: x26
STACK CFI 8fd0 x27: x27
STACK CFI 8fd4 x28: x28
STACK CFI 8fd8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9000 x25: x25
STACK CFI 9004 x26: x26
STACK CFI 9008 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9020 x25: x25
STACK CFI 9024 x26: x26
STACK CFI 9028 x27: x27
STACK CFI 902c x28: x28
STACK CFI 9030 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9058 x25: x25
STACK CFI 905c x26: x26
STACK CFI 9060 x27: x27
STACK CFI 9064 x28: x28
STACK CFI 9068 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9080 x25: x25
STACK CFI 9084 x26: x26
STACK CFI 9088 x27: x27
STACK CFI 908c x28: x28
STACK CFI 9090 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 90b8 x25: x25
STACK CFI 90bc x26: x26
STACK CFI 90c0 x27: x27
STACK CFI 90c4 x28: x28
STACK CFI 90c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 90f0 x25: x25
STACK CFI 90f4 x26: x26
STACK CFI 90f8 x27: x27
STACK CFI 90fc x28: x28
STACK CFI 9104 x25: .cfa -32 + ^
STACK CFI 9108 x26: .cfa -24 + ^
STACK CFI 910c x27: .cfa -16 + ^
STACK CFI 9110 x28: .cfa -8 + ^
STACK CFI 9124 x25: x25
STACK CFI 9128 x26: x26
STACK CFI 912c x27: x27
STACK CFI 9130 x28: x28
STACK CFI 9134 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9164 x25: x25
STACK CFI 9168 x26: x26
STACK CFI 916c x27: x27
STACK CFI 9170 x28: x28
STACK CFI 9174 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9184 x25: x25
STACK CFI 9188 x26: x26
STACK CFI 918c x27: x27
STACK CFI 9190 x28: x28
STACK CFI 9194 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 91b8 x25: x25
STACK CFI 91bc x26: x26
STACK CFI 91c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 91e4 x25: x25
STACK CFI 91e8 x26: x26
STACK CFI 91ec x27: x27
STACK CFI 91f0 x28: x28
STACK CFI 91f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9204 x25: x25
STACK CFI 9208 x26: x26
STACK CFI 920c x27: x27
STACK CFI 9210 x28: x28
STACK CFI 9214 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9224 x25: x25
STACK CFI 9228 x26: x26
STACK CFI 922c x27: x27
STACK CFI 9230 x28: x28
STACK CFI 9234 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9264 x25: x25
STACK CFI 9268 x26: x26
STACK CFI 926c x27: x27
STACK CFI 9270 x28: x28
STACK CFI 9274 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9298 x25: x25
STACK CFI 929c x26: x26
STACK CFI 92a0 x27: x27
STACK CFI 92a4 x28: x28
STACK CFI 92a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 92d8 x25: x25
STACK CFI 92dc x26: x26
STACK CFI 92e0 x27: x27
STACK CFI 92e4 x28: x28
STACK CFI 92e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9318 x25: x25
STACK CFI 931c x26: x26
STACK CFI 9320 x27: x27
STACK CFI 9324 x28: x28
STACK CFI 9328 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9338 x25: x25
STACK CFI 933c x26: x26
STACK CFI 9340 x27: x27
STACK CFI 9344 x28: x28
STACK CFI 9348 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 936c x25: x25
STACK CFI 9370 x26: x26
STACK CFI 9374 x27: x27
STACK CFI 9378 x28: x28
STACK CFI 937c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 938c x25: x25
STACK CFI 9390 x26: x26
STACK CFI 9394 x27: x27
STACK CFI 9398 x28: x28
STACK CFI 939c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 93ac x25: x25
STACK CFI 93b0 x26: x26
STACK CFI 93b4 x27: x27
STACK CFI 93b8 x28: x28
STACK CFI 93bc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 93ec x25: x25
STACK CFI 93f0 x26: x26
STACK CFI 93f4 x27: x27
STACK CFI 93f8 x28: x28
STACK CFI 93fc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 942c x25: x25
STACK CFI 9430 x26: x26
STACK CFI 9434 x27: x27
STACK CFI 9438 x28: x28
STACK CFI 943c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9494 x25: x25
STACK CFI 9498 x26: x26
STACK CFI 949c x27: x27
STACK CFI 94a0 x28: x28
STACK CFI 94a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 94c8 x25: x25
STACK CFI 94cc x26: x26
STACK CFI 94d0 x27: x27
STACK CFI 94d4 x28: x28
STACK CFI 94d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9500 x25: x25
STACK CFI 9504 x26: x26
STACK CFI 9508 x27: x27
STACK CFI 950c x28: x28
STACK CFI 9510 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9534 x25: x25
STACK CFI 9538 x26: x26
STACK CFI 953c x27: x27
STACK CFI 9540 x28: x28
STACK CFI 9544 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9568 x25: x25
STACK CFI 956c x26: x26
STACK CFI 9570 x27: x27
STACK CFI 9574 x28: x28
STACK CFI 9578 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 95a0 x25: x25
STACK CFI 95a4 x26: x26
STACK CFI 95a8 x27: x27
STACK CFI 95ac x28: x28
STACK CFI 95b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 95d8 x25: x25
STACK CFI 95dc x26: x26
STACK CFI 95e0 x27: x27
STACK CFI 95e4 x28: x28
STACK CFI 95e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 960c x25: x25
STACK CFI 9610 x26: x26
STACK CFI 9614 x27: x27
STACK CFI 9618 x28: x28
STACK CFI 961c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9640 x25: x25
STACK CFI 9644 x26: x26
STACK CFI 9648 x27: x27
STACK CFI 964c x28: x28
STACK CFI 9650 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9678 x25: x25
STACK CFI 967c x26: x26
STACK CFI 9680 x27: x27
STACK CFI 9684 x28: x28
STACK CFI 9688 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96ac x25: x25
STACK CFI 96b0 x26: x26
STACK CFI 96b4 x27: x27
STACK CFI 96b8 x28: x28
STACK CFI 96bc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96e0 x25: x25
STACK CFI 96e4 x26: x26
STACK CFI 96e8 x27: x27
STACK CFI 96ec x28: x28
STACK CFI 96f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9700 x25: x25
STACK CFI 9704 x26: x26
STACK CFI 9708 x27: x27
STACK CFI 970c x28: x28
STACK CFI 9710 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9720 x25: x25
STACK CFI 9724 x26: x26
STACK CFI 9728 x27: x27
STACK CFI 972c x28: x28
STACK CFI 9730 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9758 x25: x25
STACK CFI 975c x26: x26
STACK CFI 9760 x27: x27
STACK CFI 9764 x28: x28
STACK CFI 9768 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9790 x25: x25
STACK CFI 9794 x26: x26
STACK CFI 9798 x27: x27
STACK CFI 979c x28: x28
STACK CFI 97a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 97b4 x25: x25
STACK CFI 97b8 x26: x26
STACK CFI 97bc x27: x27
STACK CFI 97c0 x28: x28
STACK CFI 97c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 97e8 x25: x25
STACK CFI 97ec x26: x26
STACK CFI 97f0 x27: x27
STACK CFI 97f4 x28: x28
STACK CFI 97f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9808 x25: x25
STACK CFI 980c x26: x26
STACK CFI 9810 x27: x27
STACK CFI 9814 x28: x28
STACK CFI 9818 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9828 x25: x25
STACK CFI 982c x26: x26
STACK CFI 9830 x27: x27
STACK CFI 9834 x28: x28
STACK CFI 9838 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9860 x25: x25
STACK CFI 9864 x26: x26
STACK CFI 9868 x27: x27
STACK CFI 986c x28: x28
STACK CFI 9870 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9898 x25: x25
STACK CFI 989c x26: x26
STACK CFI 98a0 x27: x27
STACK CFI 98a4 x28: x28
STACK CFI 98a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 98bc x25: x25
STACK CFI 98c0 x26: x26
STACK CFI 98c4 x27: x27
STACK CFI 98c8 x28: x28
STACK CFI 98cc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 98f0 x25: x25
STACK CFI 98f4 x26: x26
STACK CFI 98f8 x27: x27
STACK CFI 98fc x28: x28
STACK CFI 9900 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9910 x25: x25
STACK CFI 9914 x26: x26
STACK CFI 9918 x27: x27
STACK CFI 991c x28: x28
STACK CFI 9920 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9930 x25: x25
STACK CFI 9934 x26: x26
STACK CFI 9938 x27: x27
STACK CFI 993c x28: x28
STACK CFI 9940 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9968 x25: x25
STACK CFI 996c x26: x26
STACK CFI 9970 x27: x27
STACK CFI 9974 x28: x28
STACK CFI 9978 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 99a0 x25: x25
STACK CFI 99a4 x26: x26
STACK CFI 99a8 x27: x27
STACK CFI 99ac x28: x28
STACK CFI 99b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 99c4 x25: x25
STACK CFI 99c8 x26: x26
STACK CFI 99cc x27: x27
STACK CFI 99d0 x28: x28
STACK CFI 99d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 99f8 x25: x25
STACK CFI 99fc x26: x26
STACK CFI 9a00 x27: x27
STACK CFI 9a04 x28: x28
STACK CFI 9a08 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9a3c x25: x25
STACK CFI 9a40 x26: x26
STACK CFI 9a44 x27: x27
STACK CFI 9a48 x28: x28
STACK CFI 9a4c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9a74 x25: x25
STACK CFI 9a78 x26: x26
STACK CFI 9a7c x27: x27
STACK CFI 9a80 x28: x28
STACK CFI 9a84 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ac8 x25: x25
STACK CFI 9acc x26: x26
STACK CFI 9ad0 x27: x27
STACK CFI 9ad4 x28: x28
STACK CFI 9ad8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ae8 x25: x25
STACK CFI 9aec x26: x26
STACK CFI 9af0 x27: x27
STACK CFI 9af4 x28: x28
STACK CFI 9af8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b20 x25: x25
STACK CFI 9b24 x26: x26
STACK CFI 9b28 x27: x27
STACK CFI 9b2c x28: x28
STACK CFI 9b30 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b58 x25: x25
STACK CFI 9b5c x26: x26
STACK CFI 9b60 x27: x27
STACK CFI 9b64 x28: x28
STACK CFI 9b68 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b8c x25: x25
STACK CFI 9b90 x26: x26
STACK CFI 9b94 x27: x27
STACK CFI 9b98 x28: x28
STACK CFI 9b9c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9bc0 x25: x25
STACK CFI 9bc4 x26: x26
STACK CFI 9bc8 x27: x27
STACK CFI 9bcc x28: x28
STACK CFI 9bd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9be0 x25: x25
STACK CFI 9be4 x26: x26
STACK CFI 9be8 x27: x27
STACK CFI 9bec x28: x28
STACK CFI 9bf0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9c00 x25: x25
STACK CFI 9c04 x26: x26
STACK CFI 9c08 x27: x27
STACK CFI 9c0c x28: x28
STACK CFI 9c10 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9c38 x25: x25
STACK CFI 9c3c x26: x26
STACK CFI 9c40 x27: x27
STACK CFI 9c44 x28: x28
STACK CFI 9c48 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9c70 x25: x25
STACK CFI 9c74 x26: x26
STACK CFI 9c78 x27: x27
STACK CFI 9c7c x28: x28
STACK CFI 9c80 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ca4 x25: x25
STACK CFI 9ca8 x26: x26
STACK CFI 9cac x27: x27
STACK CFI 9cb0 x28: x28
STACK CFI 9cb4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9cd8 x25: x25
STACK CFI 9cdc x26: x26
STACK CFI 9ce0 x27: x27
STACK CFI 9ce4 x28: x28
STACK CFI 9ce8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9d10 x25: x25
STACK CFI 9d14 x26: x26
STACK CFI 9d18 x27: x27
STACK CFI 9d1c x28: x28
STACK CFI 9d20 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9d44 x25: x25
STACK CFI 9d48 x26: x26
STACK CFI 9d4c x27: x27
STACK CFI 9d50 x28: x28
STACK CFI 9d54 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9d78 x25: x25
STACK CFI 9d7c x26: x26
STACK CFI 9d80 x27: x27
STACK CFI 9d84 x28: x28
STACK CFI 9d88 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9dac x25: x25
STACK CFI 9db0 x26: x26
STACK CFI 9db4 x27: x27
STACK CFI 9db8 x28: x28
STACK CFI 9dbc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9de4 x25: x25
STACK CFI 9de8 x26: x26
STACK CFI 9dec x27: x27
STACK CFI 9df0 x28: x28
STACK CFI 9df4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e04 x25: x25
STACK CFI 9e08 x26: x26
STACK CFI 9e0c x27: x27
STACK CFI 9e10 x28: x28
STACK CFI 9e14 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e24 x25: x25
STACK CFI 9e28 x26: x26
STACK CFI 9e2c x27: x27
STACK CFI 9e30 x28: x28
STACK CFI 9e34 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e44 x25: x25
STACK CFI 9e48 x26: x26
STACK CFI 9e4c x27: x27
STACK CFI 9e50 x28: x28
STACK CFI 9e54 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e60 x25: x25
STACK CFI 9e64 x26: x26
STACK CFI 9e68 x27: x27
STACK CFI 9e6c x28: x28
STACK CFI 9e70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e80 x25: x25
STACK CFI 9e84 x26: x26
STACK CFI 9e88 x27: x27
STACK CFI 9e8c x28: x28
STACK CFI 9e90 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ea0 x25: x25
STACK CFI 9ea4 x26: x26
STACK CFI 9ea8 x27: x27
STACK CFI 9eac x28: x28
STACK CFI 9eb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ec0 x25: x25
STACK CFI 9ec4 x26: x26
STACK CFI 9ec8 x27: x27
STACK CFI 9ecc x28: x28
STACK CFI 9ed0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ef4 x25: x25
STACK CFI 9ef8 x26: x26
STACK CFI 9efc x27: x27
STACK CFI 9f00 x28: x28
STACK CFI 9f04 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9f28 x25: x25
STACK CFI 9f2c x26: x26
STACK CFI 9f30 x27: x27
STACK CFI 9f34 x28: x28
STACK CFI 9f38 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9f60 x25: x25
STACK CFI 9f64 x26: x26
STACK CFI 9f68 x27: x27
STACK CFI 9f6c x28: x28
STACK CFI 9f70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9f98 x25: x25
STACK CFI 9f9c x26: x26
STACK CFI 9fa0 x27: x27
STACK CFI 9fa4 x28: x28
STACK CFI 9fa8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9fd0 x25: x25
STACK CFI 9fd4 x26: x26
STACK CFI 9fd8 x27: x27
STACK CFI 9fdc x28: x28
STACK CFI 9fe0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a004 x25: x25
STACK CFI a008 x26: x26
STACK CFI a00c x27: x27
STACK CFI a010 x28: x28
STACK CFI a014 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a038 x25: x25
STACK CFI a03c x26: x26
STACK CFI a040 x27: x27
STACK CFI a044 x28: x28
STACK CFI a048 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a06c x25: x25
STACK CFI a070 x26: x26
STACK CFI a074 x27: x27
STACK CFI a078 x28: x28
STACK CFI a07c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a0a4 x25: x25
STACK CFI a0a8 x26: x26
STACK CFI a0ac x27: x27
STACK CFI a0b0 x28: x28
STACK CFI a0b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a0dc x25: x25
STACK CFI a0e0 x26: x26
STACK CFI a0e4 x27: x27
STACK CFI a0e8 x28: x28
STACK CFI a0ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a110 x25: x25
STACK CFI a114 x26: x26
STACK CFI a118 x27: x27
STACK CFI a11c x28: x28
STACK CFI a120 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a144 x25: x25
STACK CFI a148 x26: x26
STACK CFI a14c x27: x27
STACK CFI a150 x28: x28
STACK CFI a154 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a17c x25: x25
STACK CFI a180 x26: x26
STACK CFI a184 x27: x27
STACK CFI a188 x28: x28
STACK CFI a18c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a1b4 x25: x25
STACK CFI a1b8 x26: x26
STACK CFI a1bc x27: x27
STACK CFI a1c0 x28: x28
STACK CFI a1c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a1e8 x25: x25
STACK CFI a1ec x26: x26
STACK CFI a1f0 x27: x27
STACK CFI a1f4 x28: x28
STACK CFI a1f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a21c x25: x25
STACK CFI a220 x26: x26
STACK CFI a224 x27: x27
STACK CFI a228 x28: x28
STACK CFI INIT a230 ac .cfa: sp 0 + .ra: x30
STACK CFI a238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a26c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a294 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a2a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a2b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a2c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2e0 4f4 .cfa: sp 0 + .ra: x30
STACK CFI a2e8 .cfa: sp 112 +
STACK CFI a2f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a308 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a330 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a3f4 x19: x19 x20: x20
STACK CFI a3fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a404 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a458 x19: x19 x20: x20
STACK CFI a468 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a470 .cfa: sp 112 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a4a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a4b0 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a4e8 x19: x19 x20: x20
STACK CFI a504 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a50c .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a57c x19: x19 x20: x20
STACK CFI a590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a7cc x19: x19 x20: x20
STACK CFI a7d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT a7d4 108 .cfa: sp 0 + .ra: x30
STACK CFI a7dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a7e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a7ec x21: .cfa -16 + ^
STACK CFI a844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a84c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a85c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a864 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a87c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a8bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a8c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a8d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a8e0 34 .cfa: sp 0 + .ra: x30
STACK CFI a8e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a908 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a914 e4 .cfa: sp 0 + .ra: x30
STACK CFI a920 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a938 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a970 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a988 x21: .cfa -16 + ^
STACK CFI a9c8 x21: x21
STACK CFI a9cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a9d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a9d8 x21: x21
STACK CFI a9f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aa00 120 .cfa: sp 0 + .ra: x30
STACK CFI aa08 .cfa: sp 64 +
STACK CFI aa14 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aa1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aa24 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aafc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab04 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ab20 f4 .cfa: sp 0 + .ra: x30
STACK CFI ab28 .cfa: sp 64 +
STACK CFI ab34 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ab44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI abf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI abf8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ac14 20 .cfa: sp 0 + .ra: x30
STACK CFI ac1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ac28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ac34 44c .cfa: sp 0 + .ra: x30
STACK CFI ac3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ac44 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ac50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ac68 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ac88 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ad38 x19: x19 x20: x20
STACK CFI ad40 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ad90 x19: x19 x20: x20
STACK CFI ada0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ada8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI adbc x19: x19 x20: x20
STACK CFI add4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI addc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI ae30 x19: x19 x20: x20
STACK CFI ae34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ae90 x19: x19 x20: x20
STACK CFI ae98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aeec x19: x19 x20: x20
STACK CFI aef0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI af04 x19: x19 x20: x20
STACK CFI af08 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT b080 470 .cfa: sp 0 + .ra: x30
STACK CFI b088 .cfa: sp 112 +
STACK CFI b094 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b09c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b0a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b0b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b0f4 x25: .cfa -16 + ^
STACK CFI b2d4 x25: x25
STACK CFI b30c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b314 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI b34c x25: x25
STACK CFI b350 x25: .cfa -16 + ^
STACK CFI b4e8 x25: x25
STACK CFI b4ec x25: .cfa -16 + ^
STACK CFI INIT b4f0 90 .cfa: sp 0 + .ra: x30
STACK CFI b4f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b500 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b50c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b570 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b580 6c4 .cfa: sp 0 + .ra: x30
STACK CFI b588 .cfa: sp 256 +
STACK CFI b598 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b5a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b5c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b668 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI b680 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b68c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b790 x21: x21 x22: x22
STACK CFI b79c x27: x27 x28: x28
STACK CFI b7bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b7c0 x21: x21 x22: x22
STACK CFI b7c4 x27: x27 x28: x28
STACK CFI b7c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b824 x21: x21 x22: x22
STACK CFI b82c x27: x27 x28: x28
STACK CFI b830 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b854 x21: x21 x22: x22
STACK CFI b85c x27: x27 x28: x28
STACK CFI b860 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b940 x21: x21 x22: x22
STACK CFI b948 x27: x27 x28: x28
STACK CFI b94c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bb60 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI bb64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bb68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT bc44 434 .cfa: sp 0 + .ra: x30
STACK CFI bc4c .cfa: sp 112 +
STACK CFI bc58 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bc60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bc68 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bc78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI bc80 x25: .cfa -16 + ^
STACK CFI beb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI bec0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT c080 90 .cfa: sp 0 + .ra: x30
STACK CFI c088 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c090 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c09c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c0f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c100 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT c110 25c .cfa: sp 0 + .ra: x30
STACK CFI c118 .cfa: sp 96 +
STACK CFI c11c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c124 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c134 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c140 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI c25c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c264 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT c370 340 .cfa: sp 0 + .ra: x30
STACK CFI c378 .cfa: sp 96 +
STACK CFI c384 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c38c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c394 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c3e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c3f0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI c510 x23: .cfa -16 + ^
STACK CFI c534 x23: x23
STACK CFI c55c x23: .cfa -16 + ^
STACK CFI c578 x23: x23
STACK CFI c60c x23: .cfa -16 + ^
STACK CFI c628 x23: x23
STACK CFI c62c x23: .cfa -16 + ^
STACK CFI c674 x23: x23
STACK CFI c678 x23: .cfa -16 + ^
STACK CFI INIT c6b0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI c6b8 .cfa: sp 80 +
STACK CFI c6c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c6cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c6d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c7f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c7fc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT c954 148 .cfa: sp 0 + .ra: x30
STACK CFI c95c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c964 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c9b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c9c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c9d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c9d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c9e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c9f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ca04 x21: .cfa -16 + ^
STACK CFI ca3c x21: x21
STACK CFI ca4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT caa0 f4 .cfa: sp 0 + .ra: x30
STACK CFI caa8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cab0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI caf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI caf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cb0c x21: .cfa -16 + ^
STACK CFI cb38 x21: x21
STACK CFI cb44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cb4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT cb94 48 .cfa: sp 0 + .ra: x30
STACK CFI cb9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cbb0 x19: .cfa -16 + ^
STACK CFI cbd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT cbe0 1c .cfa: sp 0 + .ra: x30
STACK CFI cbe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cbf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc00 1c .cfa: sp 0 + .ra: x30
STACK CFI cc08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc20 1c .cfa: sp 0 + .ra: x30
STACK CFI cc28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc40 1c .cfa: sp 0 + .ra: x30
STACK CFI cc48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc60 1c .cfa: sp 0 + .ra: x30
STACK CFI cc68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cc80 1c .cfa: sp 0 + .ra: x30
STACK CFI cc88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cc94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cca0 1c .cfa: sp 0 + .ra: x30
STACK CFI cca8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ccb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ccc0 20 .cfa: sp 0 + .ra: x30
STACK CFI ccc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ccd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cce0 1c .cfa: sp 0 + .ra: x30
STACK CFI cce8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ccf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cd00 20 .cfa: sp 0 + .ra: x30
STACK CFI cd08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cd18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cd20 20 .cfa: sp 0 + .ra: x30
STACK CFI cd28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cd38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cd40 5b8 .cfa: sp 0 + .ra: x30
STACK CFI cd48 .cfa: sp 128 +
STACK CFI cd4c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI cd54 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI cd80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cd98 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ce48 x25: .cfa -16 + ^
STACK CFI ce4c x21: x21 x22: x22 x25: x25
STACK CFI ce90 x23: x23 x24: x24
STACK CFI cee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ceec .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI cefc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI cf28 x21: x21 x22: x22
STACK CFI cf2c x23: x23 x24: x24
STACK CFI cf30 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cf70 x21: x21 x22: x22
STACK CFI cf74 x23: x23 x24: x24
STACK CFI cf78 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cf9c x23: x23 x24: x24
STACK CFI cfa0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI cfa4 x25: .cfa -16 + ^
STACK CFI d128 x25: x25
STACK CFI d138 x21: x21 x22: x22
STACK CFI d13c x23: x23 x24: x24
STACK CFI d140 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d150 x23: x23 x24: x24
STACK CFI d154 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d16c x23: x23 x24: x24
STACK CFI d170 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d17c x25: .cfa -16 + ^
STACK CFI d184 x21: x21 x22: x22
STACK CFI d188 x23: x23 x24: x24
STACK CFI d18c x25: x25
STACK CFI d190 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d1a0 x21: x21 x22: x22
STACK CFI d1a4 x23: x23 x24: x24
STACK CFI d1a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d1c8 x21: x21 x22: x22
STACK CFI d1cc x23: x23 x24: x24
STACK CFI d1d0 x25: x25
STACK CFI d1d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d220 x21: x21 x22: x22
STACK CFI d224 x23: x23 x24: x24
STACK CFI d228 x25: x25
STACK CFI d22c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d240 x21: x21 x22: x22
STACK CFI d244 x23: x23 x24: x24
STACK CFI d248 x25: x25
STACK CFI d24c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d2c4 x21: x21 x22: x22
STACK CFI d2c8 x23: x23 x24: x24
STACK CFI d2cc x25: x25
STACK CFI d2d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d2d8 x25: x25
STACK CFI d2e0 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI d2e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d2e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d2ec x25: .cfa -16 + ^
STACK CFI d2f0 x21: x21 x22: x22 x25: x25
STACK CFI d2f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT d300 4c .cfa: sp 0 + .ra: x30
STACK CFI d308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d338 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d340 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d350 20 .cfa: sp 0 + .ra: x30
STACK CFI d358 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d368 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d370 ac .cfa: sp 0 + .ra: x30
STACK CFI d378 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d380 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d3ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d3b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d420 21c .cfa: sp 0 + .ra: x30
STACK CFI d428 .cfa: sp 112 +
STACK CFI d42c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d434 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d458 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d464 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d474 x25: .cfa -16 + ^
STACK CFI d4e0 x21: x21 x22: x22
STACK CFI d4e4 x23: x23 x24: x24
STACK CFI d4e8 x25: x25
STACK CFI d528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d530 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d5cc x21: x21 x22: x22
STACK CFI d5d0 x23: x23 x24: x24
STACK CFI d5d4 x25: x25
STACK CFI d5d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d5e0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d62c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI d630 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d634 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d638 x25: .cfa -16 + ^
STACK CFI INIT d640 2c4 .cfa: sp 0 + .ra: x30
STACK CFI d648 .cfa: sp 80 +
STACK CFI d658 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d660 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d6c4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d700 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI d770 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d80c x21: x21 x22: x22
STACK CFI d83c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d844 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI d86c x21: x21 x22: x22
STACK CFI d870 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d884 x21: x21 x22: x22
STACK CFI d888 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d8a8 x21: x21 x22: x22
STACK CFI d8ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d8c0 x21: x21 x22: x22
STACK CFI d8c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d8fc x21: x21 x22: x22
STACK CFI d900 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT d904 144 .cfa: sp 0 + .ra: x30
STACK CFI d90c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d914 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d91c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d92c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI d9ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d9f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI da0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI da14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT da50 20 .cfa: sp 0 + .ra: x30
STACK CFI da58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI da64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT da70 5a8 .cfa: sp 0 + .ra: x30
STACK CFI da78 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI da80 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI da88 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI da94 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI daa0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI dadc x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI db48 x27: x27 x28: x28
STACK CFI db78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI db80 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI dbac x27: x27 x28: x28
STACK CFI dbb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI dbb8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI de04 x27: x27 x28: x28
STACK CFI de08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI de10 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI df20 x27: x27 x28: x28
STACK CFI df28 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI df6c x27: x27 x28: x28
STACK CFI df74 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI dfb0 x27: x27 x28: x28
STACK CFI dfb8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI e010 x27: x27 x28: x28
STACK CFI INIT e020 168 .cfa: sp 0 + .ra: x30
STACK CFI e028 .cfa: sp 128 +
STACK CFI e034 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e03c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e044 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e04c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e058 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e080 x27: .cfa -16 + ^
STACK CFI e100 x27: x27
STACK CFI e138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e140 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI e154 x27: x27
STACK CFI e158 x27: .cfa -16 + ^
STACK CFI e178 x27: x27
STACK CFI e184 x27: .cfa -16 + ^
STACK CFI INIT e190 110 .cfa: sp 0 + .ra: x30
STACK CFI e198 .cfa: sp 128 +
STACK CFI e1a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e1ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e1b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e1c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e27c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e284 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT e2a0 50 .cfa: sp 0 + .ra: x30
STACK CFI e2a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e2d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e2e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e2e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e2f0 50 .cfa: sp 0 + .ra: x30
STACK CFI e2f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e324 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e330 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e334 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e340 154 .cfa: sp 0 + .ra: x30
STACK CFI e348 .cfa: sp 128 +
STACK CFI e354 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e35c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e368 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e374 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e380 x25: .cfa -16 + ^
STACK CFI e488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI e490 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT e494 1e0 .cfa: sp 0 + .ra: x30
STACK CFI e49c .cfa: sp 112 +
STACK CFI e4ac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e4b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e4c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e4cc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e58c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT e674 2d0 .cfa: sp 0 + .ra: x30
STACK CFI e67c .cfa: sp 112 +
STACK CFI e688 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e690 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e6a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e6ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e6b8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e780 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT e944 194 .cfa: sp 0 + .ra: x30
STACK CFI e94c .cfa: sp 96 +
STACK CFI e960 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e968 x19: .cfa -16 + ^
STACK CFI ea24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ea2c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT eae0 ed0 .cfa: sp 0 + .ra: x30
STACK CFI eae8 .cfa: sp 224 +
STACK CFI eaf4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI eb0c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI eb14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI eb20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI eb28 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI eb48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ebe0 x19: x19 x20: x20
STACK CFI ec1c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ec24 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ed3c x19: x19 x20: x20
STACK CFI ed50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI eda0 x19: x19 x20: x20
STACK CFI eda4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI edc8 x19: x19 x20: x20
STACK CFI edd0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ee00 x19: x19 x20: x20
STACK CFI ee08 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ee34 x19: x19 x20: x20
STACK CFI ee38 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f074 x19: x19 x20: x20
STACK CFI f07c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f0fc x19: x19 x20: x20
STACK CFI f104 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f128 x19: x19 x20: x20
STACK CFI f130 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f188 x19: x19 x20: x20
STACK CFI f190 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f1f4 x19: x19 x20: x20
STACK CFI f1fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f228 x19: x19 x20: x20
STACK CFI f22c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f250 x19: x19 x20: x20
STACK CFI f258 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f2d0 x19: x19 x20: x20
STACK CFI f2d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f374 x19: x19 x20: x20
STACK CFI f37c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f3dc x19: x19 x20: x20
STACK CFI f3e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f40c x19: x19 x20: x20
STACK CFI f410 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f4bc x19: x19 x20: x20
STACK CFI f4c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f4f0 x19: x19 x20: x20
STACK CFI f4f4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f568 x19: x19 x20: x20
STACK CFI f56c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f6a4 x19: x19 x20: x20
STACK CFI f6ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f6d8 x19: x19 x20: x20
STACK CFI f6e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT f9b0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI f9b8 .cfa: sp 96 +
STACK CFI f9c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f9dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f9e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fa04 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fa94 x19: x19 x20: x20
STACK CFI fa98 x23: x23 x24: x24
STACK CFI fac4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI facc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI fad4 x19: x19 x20: x20
STACK CFI fadc x23: x23 x24: x24
STACK CFI faec x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fb30 x19: x19 x20: x20
STACK CFI fb34 x23: x23 x24: x24
STACK CFI fb3c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI fb50 x19: x19 x20: x20
STACK CFI fb54 x23: x23 x24: x24
STACK CFI fb5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fb60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT fb64 cc .cfa: sp 0 + .ra: x30
STACK CFI fb6c .cfa: sp 64 +
STACK CFI fb7c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fb84 x19: .cfa -16 + ^
STACK CFI fc18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fc20 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT fc30 38 .cfa: sp 0 + .ra: x30
STACK CFI fc38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fc60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fc70 10c .cfa: sp 0 + .ra: x30
STACK CFI fc78 .cfa: sp 48 +
STACK CFI fc88 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fc94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fd10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd18 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT fd80 1dc .cfa: sp 0 + .ra: x30
STACK CFI fd88 .cfa: sp 128 +
STACK CFI fd94 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI fd9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI fda4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI fdb0 x23: .cfa -16 + ^
STACK CFI fe78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI fe80 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT ff60 2e4 .cfa: sp 0 + .ra: x30
STACK CFI ff68 .cfa: sp 208 +
STACK CFI ff74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ff80 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ff88 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ff98 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1002c .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10244 324 .cfa: sp 0 + .ra: x30
STACK CFI 1024c .cfa: sp 224 +
STACK CFI 10258 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10264 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10270 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10278 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10280 x27: .cfa -16 + ^
STACK CFI 103ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 103b4 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10570 17c .cfa: sp 0 + .ra: x30
STACK CFI 10578 .cfa: sp 112 +
STACK CFI 10588 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 105a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 105ac x23: .cfa -16 + ^
STACK CFI 10658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10660 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 106f0 160 .cfa: sp 0 + .ra: x30
STACK CFI 106f8 .cfa: sp 128 +
STACK CFI 106fc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10704 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10710 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1071c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1072c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 107cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 107d4 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
