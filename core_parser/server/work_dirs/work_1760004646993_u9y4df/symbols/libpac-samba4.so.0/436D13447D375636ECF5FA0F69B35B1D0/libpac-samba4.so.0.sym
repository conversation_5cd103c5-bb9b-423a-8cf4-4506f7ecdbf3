MODULE Linux arm64 436D13447D375636ECF5FA0F69B35B1D0 libpac-samba4.so.0
INFO CODE_ID 44136D43377D3656ECF5FA0F69B35B1D528442B7
PUBLIC 3990 0 samba_kdc_encrypt_pac_credentials
PUBLIC 3d60 0 samba_make_krb5_pac
PUBLIC 4000 0 samba_princ_needs_pac
PUBLIC 4034 0 samba_client_requested_pac
PUBLIC 4190 0 samba_krbtgt_is_in_db
PUBLIC 43a0 0 samba_kdc_get_user_info_from_db
PUBLIC 4480 0 samba_kdc_get_logon_info_blob
PUBLIC 4580 0 samba_kdc_get_cred_ndr_blob
PUBLIC 4ae0 0 samba_kdc_get_upn_info_blob
PUBLIC 4ce0 0 samba_kdc_get_pac_attrs_blob
PUBLIC 4ef4 0 samba_kdc_get_requester_sid_blob
PUBLIC 5144 0 samba_kdc_get_claims_blob
PUBLIC 5320 0 samba_kdc_get_user_info_dc
PUBLIC 5950 0 samba_kdc_map_policy_err
PUBLIC 59d4 0 samba_kdc_check_client_access
PUBLIC 5aa0 0 samba_kdc_validate_pac_blob
PUBLIC 5da4 0 samba_rodc_confirm_user_is_allowed
PUBLIC 6100 0 samba_kdc_allowed_to_authenticate_to
PUBLIC 62a4 0 samba_kdc_check_device
PUBLIC 66e0 0 pac_blobs_init
PUBLIC 6720 0 pac_blobs_destroy
PUBLIC 6760 0 pac_blobs_from_krb5_pac
PUBLIC 69c0 0 _pac_blobs_ensure_exists
PUBLIC 6b30 0 samba_kdc_verify_pac
PUBLIC 6ef0 0 _pac_blobs_replace_existing
PUBLIC 70a0 0 pac_blobs_add_blob
PUBLIC 72e0 0 pac_blobs_remove_blob
PUBLIC 7590 0 samba_kdc_update_pac
STACK CFI INIT 3080 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 30f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30fc x19: .cfa -16 + ^
STACK CFI 3134 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3140 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3150 15c .cfa: sp 0 + .ra: x30
STACK CFI 3158 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3160 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3174 x21: .cfa -16 + ^
STACK CFI 31b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 31ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 32b0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 32b8 .cfa: sp 160 +
STACK CFI 32c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 32cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 32d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 32e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3424 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 342c .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3554 bc .cfa: sp 0 + .ra: x30
STACK CFI 355c .cfa: sp 112 +
STACK CFI 3568 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35b8 .cfa: sp 112 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35e4 x19: x19 x20: x20
STACK CFI 35e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3604 x19: x19 x20: x20
STACK CFI 360c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 3610 198 .cfa: sp 0 + .ra: x30
STACK CFI 3618 .cfa: sp 80 +
STACK CFI 361c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3624 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3638 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3654 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36a4 x19: x19 x20: x20
STACK CFI 3728 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3730 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3734 x19: x19 x20: x20
STACK CFI 37a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 37b0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 37b8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 37c0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 37c8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 37d4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 380c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3864 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 391c x21: x21 x22: x22
STACK CFI 3924 x19: x19 x20: x20
STACK CFI 3938 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3940 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3948 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 394c x19: x19 x20: x20
STACK CFI 3950 x21: x21 x22: x22
STACK CFI 3964 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 396c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 3970 x19: x19 x20: x20
STACK CFI 3974 x21: x21 x22: x22
STACK CFI 3984 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 3990 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 3998 .cfa: sp 128 +
STACK CFI 39a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 39c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3b10 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d60 298 .cfa: sp 0 + .ra: x30
STACK CFI 3d68 .cfa: sp 176 +
STACK CFI 3d74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3d80 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3d8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3d98 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3da4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3db0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3fe0 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4000 34 .cfa: sp 0 + .ra: x30
STACK CFI 4008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4028 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4034 158 .cfa: sp 0 + .ra: x30
STACK CFI 403c .cfa: sp 160 +
STACK CFI 4044 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 404c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4060 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 40bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 40c4 .cfa: sp 160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4190 208 .cfa: sp 0 + .ra: x30
STACK CFI 4198 .cfa: sp 64 +
STACK CFI 41a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 433c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4344 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 43a0 dc .cfa: sp 0 + .ra: x30
STACK CFI 43a8 .cfa: sp 128 +
STACK CFI 43ac .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43e8 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 43f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43fc x25: .cfa -16 + ^
STACK CFI 4468 x23: x23 x24: x24
STACK CFI 446c x25: x25
STACK CFI 4470 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4474 x23: x23 x24: x24
STACK CFI 4478 x25: x25
STACK CFI INIT 4480 100 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4490 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 449c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 44ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4510 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4580 560 .cfa: sp 0 + .ra: x30
STACK CFI 4588 .cfa: sp 208 +
STACK CFI 4594 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 459c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 45b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 45e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 45f4 x25: .cfa -16 + ^
STACK CFI 467c x23: x23 x24: x24
STACK CFI 4684 x25: x25
STACK CFI 46b0 x19: x19 x20: x20
STACK CFI 46b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 46c0 .cfa: sp 208 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4794 x23: x23 x24: x24 x25: x25
STACK CFI 47a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4890 x23: x23 x24: x24
STACK CFI 4894 x25: x25
STACK CFI 4898 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 48f4 x23: x23 x24: x24 x25: x25
STACK CFI 48fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4900 x25: .cfa -16 + ^
STACK CFI 4acc x23: x23 x24: x24
STACK CFI 4ad0 x25: x25
STACK CFI 4ad8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4adc x25: .cfa -16 + ^
STACK CFI INIT 4ae0 200 .cfa: sp 0 + .ra: x30
STACK CFI 4ae8 .cfa: sp 128 +
STACK CFI 4af4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4afc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c18 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ce0 214 .cfa: sp 0 + .ra: x30
STACK CFI 4ce8 .cfa: sp 128 +
STACK CFI 4cf4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d00 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4dc0 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ef4 250 .cfa: sp 0 + .ra: x30
STACK CFI 4efc .cfa: sp 144 +
STACK CFI 4f08 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4f94 x19: x19 x20: x20
STACK CFI 4f98 x21: x21 x22: x22
STACK CFI 4f9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4fa4 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4fb4 x23: .cfa -16 + ^
STACK CFI 5008 x23: x23
STACK CFI 5014 x23: .cfa -16 + ^
STACK CFI 5054 x23: x23
STACK CFI 5058 x23: .cfa -16 + ^
STACK CFI 50b0 x23: x23
STACK CFI 50b4 x23: .cfa -16 + ^
STACK CFI 513c x23: x23
STACK CFI 5140 x23: .cfa -16 + ^
STACK CFI INIT 5144 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 514c .cfa: sp 144 +
STACK CFI 5158 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 516c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5170 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51ac x23: .cfa -16 + ^
STACK CFI 51e8 x23: x23
STACK CFI 5218 x19: x19 x20: x20
STACK CFI 521c x21: x21 x22: x22
STACK CFI 5220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5228 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5234 x23: .cfa -16 + ^
STACK CFI 5250 x23: x23
STACK CFI 525c x23: .cfa -16 + ^
STACK CFI 52c8 x23: x23
STACK CFI 52cc x23: .cfa -16 + ^
STACK CFI 5314 x23: x23
STACK CFI 5318 x23: .cfa -16 + ^
STACK CFI INIT 5320 384 .cfa: sp 0 + .ra: x30
STACK CFI 5328 .cfa: sp 160 +
STACK CFI 5334 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 533c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5354 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 548c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5494 .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 56a4 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 56ac .cfa: sp 80 +
STACK CFI 56bc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 56c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 56d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 56d8 x23: .cfa -16 + ^
STACK CFI 57cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 57d4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5950 84 .cfa: sp 0 + .ra: x30
STACK CFI 5958 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 597c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 59c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 59d4 c8 .cfa: sp 0 + .ra: x30
STACK CFI 59dc .cfa: sp 80 +
STACK CFI 59e0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 59e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5a0c x23: .cfa -16 + ^
STACK CFI 5a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5a78 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5aa0 304 .cfa: sp 0 + .ra: x30
STACK CFI 5aa8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5ab8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ac0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5acc .cfa: sp 592 + x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5b0c x25: .cfa -16 + ^
STACK CFI 5bd8 x25: x25
STACK CFI 5c0c .cfa: sp 80 +
STACK CFI 5c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c28 .cfa: sp 592 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5cc8 x25: x25
STACK CFI 5cd0 x25: .cfa -16 + ^
STACK CFI 5ce4 x25: x25
STACK CFI 5cec x25: .cfa -16 + ^
STACK CFI 5d2c x25: x25
STACK CFI 5d30 x25: .cfa -16 + ^
STACK CFI INIT 5da4 354 .cfa: sp 0 + .ra: x30
STACK CFI 5dac .cfa: sp 144 +
STACK CFI 5db8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5dc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5dc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5dd0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5ddc x25: .cfa -16 + ^
STACK CFI 5f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5f38 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6100 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 6108 .cfa: sp 176 +
STACK CFI 6118 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6120 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6128 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6134 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6140 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 614c x27: .cfa -16 + ^
STACK CFI 61fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 6204 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 62a4 434 .cfa: sp 0 + .ra: x30
STACK CFI 62ac .cfa: sp 256 +
STACK CFI 62b8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 62c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 62c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 62d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 62e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 62e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 63bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 63c4 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 66e0 3c .cfa: sp 0 + .ra: x30
STACK CFI 66f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 66f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6720 3c .cfa: sp 0 + .ra: x30
STACK CFI 6728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6730 x19: .cfa -16 + ^
STACK CFI 6754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6760 260 .cfa: sp 0 + .ra: x30
STACK CFI 6768 .cfa: sp 64 +
STACK CFI 6774 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 677c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6788 x21: .cfa -16 + ^
STACK CFI 6878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6880 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 69c0 168 .cfa: sp 0 + .ra: x30
STACK CFI 69c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 69f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a00 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a08 x19: .cfa -32 + ^
STACK CFI 6a1c x19: x19
STACK CFI 6a24 x19: .cfa -32 + ^
STACK CFI 6a38 x19: x19
STACK CFI 6a40 x19: .cfa -32 + ^
STACK CFI 6a58 x19: x19
STACK CFI 6a60 x19: .cfa -32 + ^
STACK CFI INIT 6b30 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 6b38 .cfa: sp 304 +
STACK CFI 6b44 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6b50 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6b5c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6b68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6b74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6b7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6cf4 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6ef0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 6ef8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6f00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6f0c x21: .cfa -16 + ^
STACK CFI 6f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6f34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6f78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 70a0 240 .cfa: sp 0 + .ra: x30
STACK CFI 70b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 70b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 70c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 711c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7124 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7160 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 72e0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 72e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72f0 x19: .cfa -16 + ^
STACK CFI 7370 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7378 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7590 1390 .cfa: sp 0 + .ra: x30
STACK CFI 7598 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 75a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 75a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 75c0 .cfa: sp 720 + x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7d04 .cfa: sp 96 +
STACK CFI 7d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7d28 .cfa: sp 720 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
