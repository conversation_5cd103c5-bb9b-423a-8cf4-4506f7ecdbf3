MODULE Linux arm64 BD315CAF7D0E31D620BE8808E6A79E470 libnvstream_core_image.so
INFO CODE_ID AF5C31BD0E7DD63120BE8808E6A79E47
PUBLIC ca0 0 _init
PUBLIC d40 0 call_weak_fn
PUBLIC d60 0 deregister_tm_clones
PUBLIC d90 0 register_tm_clones
PUBLIC dd0 0 __do_global_dtors_aux
PUBLIC e20 0 frame_dummy
PUBLIC e30 0 linvs::image::GetImageBufAttrList(linvs::buf::BufAttrList&, linvs::image::ImageType, int, int)
PUBLIC 1260 0 _fini
STACK CFI INIT d60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 48 .cfa: sp 0 + .ra: x30
STACK CFI dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ddc x19: .cfa -16 + ^
STACK CFI e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e30 430 .cfa: sp 0 + .ra: x30
STACK CFI e34 .cfa: sp 560 +
STACK CFI e48 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI e54 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI e5c x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI e6c x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI ee8 x25: .cfa -496 + ^
STACK CFI 106c x25: x25
STACK CFI 10a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10a4 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x29: .cfa -560 + ^
STACK CFI 10ac x25: .cfa -496 + ^
STACK CFI 1244 x25: x25
STACK CFI 1254 x25: .cfa -496 + ^
STACK CFI 1258 x25: x25
STACK CFI 125c x25: .cfa -496 + ^
