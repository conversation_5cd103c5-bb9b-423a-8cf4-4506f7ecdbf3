MODULE Linux arm64 C8F9877EF35C945387AE9585592D15B00 libcairo-gobject.so.2
INFO CODE_ID 7E87F9C85CF3539487AE9585592D15B0FD6A2981
PUBLIC 35c0 0 cairo_gobject_status_get_type
PUBLIC 3640 0 cairo_gobject_content_get_type
PUBLIC 36d0 0 cairo_gobject_operator_get_type
PUBLIC 3760 0 cairo_gobject_antialias_get_type
PUBLIC 37f0 0 cairo_gobject_fill_rule_get_type
PUBLIC 3880 0 cairo_gobject_line_cap_get_type
PUBLIC 3910 0 cairo_gobject_line_join_get_type
PUBLIC 39a0 0 cairo_gobject_text_cluster_flags_get_type
PUBLIC 3a30 0 cairo_gobject_font_slant_get_type
PUBLIC 3ac0 0 cairo_gobject_font_weight_get_type
PUBLIC 3b50 0 cairo_gobject_subpixel_order_get_type
PUBLIC 3be0 0 cairo_gobject_hint_style_get_type
PUBLIC 3c70 0 cairo_gobject_hint_metrics_get_type
PUBLIC 3d00 0 cairo_gobject_font_type_get_type
PUBLIC 3d90 0 cairo_gobject_path_data_type_get_type
PUBLIC 3e20 0 cairo_gobject_device_type_get_type
PUBLIC 3eb0 0 cairo_gobject_surface_type_get_type
PUBLIC 3f40 0 cairo_gobject_format_get_type
PUBLIC 3fd0 0 cairo_gobject_pattern_type_get_type
PUBLIC 4060 0 cairo_gobject_extend_get_type
PUBLIC 40f0 0 cairo_gobject_filter_get_type
PUBLIC 4180 0 cairo_gobject_region_overlap_get_type
PUBLIC 42b0 0 cairo_gobject_context_get_type
PUBLIC 4330 0 cairo_gobject_device_get_type
PUBLIC 43c0 0 cairo_gobject_pattern_get_type
PUBLIC 4450 0 cairo_gobject_surface_get_type
PUBLIC 44e0 0 cairo_gobject_scaled_font_get_type
PUBLIC 4570 0 cairo_gobject_font_face_get_type
PUBLIC 4600 0 cairo_gobject_font_options_get_type
PUBLIC 4690 0 cairo_gobject_region_get_type
PUBLIC 4720 0 cairo_gobject_matrix_get_type
PUBLIC 47b0 0 cairo_gobject_rectangle_get_type
PUBLIC 4840 0 cairo_gobject_rectangle_int_get_type
PUBLIC 48d0 0 cairo_gobject_glyph_get_type
PUBLIC 4960 0 cairo_gobject_text_cluster_get_type
STACK CFI INIT 34f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3520 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3560 48 .cfa: sp 0 + .ra: x30
STACK CFI 3564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 356c x19: .cfa -16 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 35b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35c0 78 .cfa: sp 0 + .ra: x30
STACK CFI 35c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3640 88 .cfa: sp 0 + .ra: x30
STACK CFI 3648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3650 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 367c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 36c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 36d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 36d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 370c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3760 88 .cfa: sp 0 + .ra: x30
STACK CFI 3768 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3770 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 379c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 37f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3800 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 382c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3880 88 .cfa: sp 0 + .ra: x30
STACK CFI 3888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 38b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3910 88 .cfa: sp 0 + .ra: x30
STACK CFI 3918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3920 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 394c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 39a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 39a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a30 88 .cfa: sp 0 + .ra: x30
STACK CFI 3a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ac0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ad0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3afc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b50 88 .cfa: sp 0 + .ra: x30
STACK CFI 3b58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3be0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3be8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bf0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3c70 88 .cfa: sp 0 + .ra: x30
STACK CFI 3c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d00 88 .cfa: sp 0 + .ra: x30
STACK CFI 3d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d90 88 .cfa: sp 0 + .ra: x30
STACK CFI 3d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3da0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3e20 88 .cfa: sp 0 + .ra: x30
STACK CFI 3e28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3eb0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3eb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ec0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3eec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f40 88 .cfa: sp 0 + .ra: x30
STACK CFI 3f48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3fd0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3fd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fe0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 400c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4060 88 .cfa: sp 0 + .ra: x30
STACK CFI 4068 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4070 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 409c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 40e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 40f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 40f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4100 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4124 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 412c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4180 88 .cfa: sp 0 + .ra: x30
STACK CFI 4188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4190 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 41bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4210 1c .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4230 1c .cfa: sp 0 + .ra: x30
STACK CFI 4238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4244 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4250 1c .cfa: sp 0 + .ra: x30
STACK CFI 4258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4264 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4270 1c .cfa: sp 0 + .ra: x30
STACK CFI 4278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4290 1c .cfa: sp 0 + .ra: x30
STACK CFI 4298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42b0 80 .cfa: sp 0 + .ra: x30
STACK CFI 42b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 42dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 42e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4330 8c .cfa: sp 0 + .ra: x30
STACK CFI 4338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4340 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 436c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 43b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 43c0 8c .cfa: sp 0 + .ra: x30
STACK CFI 43c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4450 8c .cfa: sp 0 + .ra: x30
STACK CFI 4458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4460 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 448c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 44d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44e0 8c .cfa: sp 0 + .ra: x30
STACK CFI 44e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 451c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4570 8c .cfa: sp 0 + .ra: x30
STACK CFI 4578 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4580 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4600 8c .cfa: sp 0 + .ra: x30
STACK CFI 4608 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4610 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 463c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4690 8c .cfa: sp 0 + .ra: x30
STACK CFI 4698 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 46cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4720 8c .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4730 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 475c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 47a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 47b0 8c .cfa: sp 0 + .ra: x30
STACK CFI 47b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 47e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4840 8c .cfa: sp 0 + .ra: x30
STACK CFI 4848 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4850 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 487c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 48c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 48d0 8c .cfa: sp 0 + .ra: x30
STACK CFI 48d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 490c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4960 8c .cfa: sp 0 + .ra: x30
STACK CFI 4968 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4970 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 499c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 49e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
